<template>
  <div class="messagePushBox">
    <component
      :is="$route.name == 'messagePushBox' ? 'messagePush' : $route.name"
    ></component>
  </div>
</template>

<script>
import messagePush from "./messagePush"
export default {
  //import引入的组件
  components: {
    messagePush
  },
  data() {
    return {
      componentStr: "",
    };
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
.structure {
  margin-top: 15px;
}
</style>