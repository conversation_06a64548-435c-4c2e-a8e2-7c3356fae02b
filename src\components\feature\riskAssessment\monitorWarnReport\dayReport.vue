<template>
  <div class="dayReport">
    <div>
      <div class="seach-part">
        <div class="l">
          <el-date-picker
            v-model="dateTime"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            unlink-panels
            clearable
          >
          </el-date-picker>

          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>日报列表</h2>
          <!-- <el-button
            type="primary"
            size="mini"
            @click="generateBox()"
            :disabled="$store.state.login.user.isDanger!=1 || $store.state.login.userDistCode!='420000'"
            >生成
          </el-button> -->
        </div>

        <div>
          <div class="table">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
            >
              <!-- <el-table-column prop="i"
                               type="selection"
                               width="45"
                               fixed="left"
                               align="center">
              </el-table-column> -->
              <el-table-column
                prop="i"
                type="index"
                label="序号"
                width="60"
                align="center"
              >
              </el-table-column>

              <el-table-column prop="reportDate" label="日期" align="center">
              </el-table-column>

              <el-table-column
                prop="reportName"
                label="报告名称"
                align="center"
                width="300"
                :show-overflow-tooltip="true"
              >
                <!-- <template slot-scope="scope">
                  <el-button
                    @click="goEnt(scope.row)"
                    type="text"
                    class="enterpName"
                  >
                    {{ scope.row.orgName }}
                  </el-button>
                </template> -->
              </el-table-column>
              <el-table-column prop="issueFlagName" label="状态" align="center">
              </el-table-column>

              <el-table-column prop="最终报告" label="最终报告" align="center">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="openSee(scope.row)"
                    v-if="scope.row.issueFlag == 1"
                    >查看
                  </el-button>
                </template>
              </el-table-column>

              <!-- <el-table-column
                prop="signAttachList"
                label="专家签字报告"
                align="center"
                width="110"
              >
                <template slot-scope="{ row }">
                  <el-button
                    type="text"
                    v-if="row.signAttachList && row.signAttachList.length > 0"
                    @click="openImg(row)"
                    >查看附件</el-button
                  >
                </template>
              </el-table-column> -->

              <el-table-column prop="i" label="操作" align="center">
                <template slot-scope="scope">
                  <!-- <el-button type="text" @click="downSee(scope.row)"
                    >生成
                  </el-button> -->
                  <!-- :disabled="scope.row.issueFlag==1 || $store.state.login.user.isDanger!=1 || $store.state.login.userDistCode!='420000'"  -->
                  <el-button
                    :disabled="
                      $store.state.login.user.isDanger != 1 ||
                      $store.state.login.userDistCode != '420000'
                    "
                    type="text"
                    @click="lastReport(scope.row)"
                    >发布
                  </el-button>
                  <!-- dailyReportDetele -->
                  <el-button
                    type="text"
                    @click="dailyReportDetele(scope.row)"
                    :disabled="
                      $store.state.login.user.isDanger != 1 ||
                      $store.state.login.userDistCode != '420000'
                    "
                    >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      title="查看图片"
      :visible.sync="dialogImg"
      width="600px"
      top="5vh"
      @close="dialogFileSrc = []"
      :close-on-click-modal="false"
    >
      <AttachmentUpload
        :attachmentlist="dialogFileSrc"
        :limit="5"
        type="all"
        :editabled="true"
      ></AttachmentUpload>
    </el-dialog>

    <el-dialog
      title="生成"
      v-if="dialogVisible1"
      :visible.sync="dialogVisible1"
      width="1000px"
      top="5vh"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogInfo">
        <el-form ref="ruleForm1" :model="form1" size="medium" :rules="rules1">
          <el-descriptions :column="2" border :labelStyle="labelStyle">
            <el-descriptions-item>
              <template slot="label">日报名称：</template>
              <el-form-item prop="reportName">
                <el-input
                  v-model.trim="form1.reportName"
                  disabled
                  placeholder="请输入日报名称"
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label"> 期号： </template>
              <el-form-item prop="issueNum">
                <el-input
                  v-model.trim="form1.issueNum"
                  placeholder="请输入期号"
                  clearable
                  disabled
                  maxlength="9"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label"> 发布人： </template>
              <el-form-item prop="issuePerson">
                <el-input
                  v-model.trim="form1.issuePerson"
                  disabled
                  placeholder="请输入发布人"
                  clearable
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label"> 发布单位： </template>
              <el-form-item prop="issueOrg">
                <el-input
                  v-model.trim="form1.issueOrg"
                  placeholder="请输入发布单位"
                  clearable
                  maxlength="20"
                  disabled
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>

      <div slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" size="mini" @click="submitSave()"
          >保 存</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="发布"
      v-if="dialogVisible2"
      :visible.sync="dialogVisible2"
      width="1000px"
      top="5vh"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogInfo">
        <el-form ref="ruleForm2" :model="form2" size="medium" :rules="rules2">
          <el-descriptions :column="2" border :labelStyle="labelStyle">
            <el-descriptions-item>
              <template slot="label">日报名称：</template>
              <el-form-item prop="reportName">
                <el-input
                  v-model.trim="form2.reportName"
                  placeholder="请输入日报名称"
                  disabled
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label"> 期号： </template>
              <el-form-item prop="issueNum">
                <el-input
                  v-model.trim="form2.issueNum"
                  placeholder="请输入期号"
                  clearable
                  disabled
                  maxlength="9"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label"> 发布人： </template>
              <el-form-item prop="issuePerson">
                <el-input
                  v-model.trim="form2.issuePerson"
                  placeholder="请输入发布人"
                  clearable
                  disabled
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label"> 发布单位： </template>
              <el-form-item prop="issueOrg">
                <el-input
                  v-model.trim="form2.issueOrg"
                  placeholder="请输入发布单位"
                  disabled
                  clearable
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label"
                ><span class="redDot">*</span>上传最终报告：</template
              >
              <el-form-item prop="reportList" class="uploadBox">
                <AttachmentUpload
                  :attachmentlist="form2.reportList"
                  :limit="1"
                  type="docx"
                  v-bind="{}"
                  :messageTip="messageTip1"
                  @resBack="resBackReportList"
                ></AttachmentUpload>
                <!-- <span class="tipInfo"
                  >请上传word、pdf等其他补充材料，最多上传5个，单个文件大小不超过30M</span
                > -->
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>

      <div slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" size="mini" @click="submitSaveLast()"
          >保 存</el-button
        >
      </div>
    </el-dialog>

    <el-dialog
      title="查看"
      v-if="dialogVisible3"
      :visible.sync="dialogVisible3"
      width="80%"
      top="0"
      :close-on-click-modal="false"
    >
      <!-- <div class="dialog dialogInfo" style="height: calc(100vh - 100px)">
        <div v-loading="loading">
          <iframe
            id="pdfRef"
            ref="pdf"
            :src="pdfUrl"
            width="100%"
            frameborder="no"
            border="0"
            marginwidth="0"
            marginheight="0"
            style="height: calc(100vh - 100px)"
          ></iframe>
        </div>
      </div> -->
      <!-- word 显示-->
      <div
        style="height: calc(100vh - 100px); overflow: auto"
        class="wordReview"
      >
        <el-button
          class="reviewDownStyle"
          type="primary"
          size="mini"
          @click="reviewDown()"
          >下 载</el-button
        >

        <div ref="word"></div>
        <div class="nodata" v-if="showNodata">
          <img
            style="margin: 0 auto; display: block; margin-top: 30px"
            src="/static/img/assets/img/noData.png"
          />
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js";
import {
  dailyReportFindPage,
  dailyReportExport,
  dailyReportGenerate, //生成日报记录（所有字段可以不传值）
  cimEarlyWarningExportExcel, //测试
  dailyReportDelete, //删除
  dailyReportIssue, //发布日报记录
  dailyReportissueNum, //查询本年最新期号
} from "@/api/riskAssessment";

import axios from "axios";
const docx = require("docx-preview");
window.JSZip = require("jszip");
export default {
  name: "dayReport",
  components: {
    AttachmentUpload,
  },
  data() {
    return {
      attachId: "",
      showNodata: false,
      dateTime: [], //查询时间
      tableData: [], //列表
      currentPage: 1, //列表分页
      size: 10, //列表一页显示
      total: 0, //列表总数
      loading: false, //进度
      btnLoading: false, //按钮加载进度
      selection: [], //多谢
      dialogImg: false, //弹窗下载
      dialogFileSrc: [], //弹窗下载
      reviseAttachList: [], //上传
      dialogVisible3: false, //查看弹窗
      pdfUrl: "", //查看弹窗pdf
      isDown: "",
      //弹框表单
      dialogVisible1: false,
      form1: {
        issueNum: "",
        issueOrg: this.$store.state.login.user.org_name,
        issuePerson: "",
        reportId: "",
        reportName: "武汉市危险化学品重大危险源安全生产风险监测预警日报", ////日报名称
      },
      dialogVisible2: false,
      form2: {
        issueNum: "",
        issueOrg: this.$store.state.login.user.org_name, //发布单位
        issuePerson: "", //发布人
        reportId: "",
        reportName: "", //日报名称
        reportList: [],
      },
      messageTip1: "请上传1个.docx文件",
      labelStyle: {
        textAlign: "center",
        backgroundColor: "rgb(242, 246, 255)",
      },
      rules1: {
        issureportNameNum: [
          { required: true, message: "日报名称不能为空", trigger: "blur" },
        ],
        issueNum: [
          { required: true, message: "期号不能为空", trigger: "blur" },
        ],
        issueOrg: [
          { required: true, message: "发布单位不能为空", trigger: "blur" },
        ],
      },
      rules2: {
        issureportNameNum: [
          { required: true, message: "日报名称不能为空", trigger: "blur" },
        ],
        issueNum: [
          { required: true, message: "期号不能为空", trigger: "blur" },
        ],

        issueOrg: [
          { required: true, message: "发布单位不能为空", trigger: "blur" },
        ],
        reportList: [
          {
            type: "array",
            required: true,
            message: "上传最终报告不能为空",
            trigger: "change",
          },
        ],
      },
    };
  },
  methods: {
    resetForm1() {
      this.form1 = {
        issueNum: "",
        issueOrg: this.$store.state.login.user.org_name,
        issuePerson: "",
        reportId: "",
        reportName: "武汉市危险化学品重大危险源安全生产风险监测预警日报", ////日报名称
      };
    },
    resetForm2() {
      this.form2 = {
        issueNum: "",
        issueOrg: this.$store.state.login.user.org_name, //发布单位
        issuePerson: "", //发布人
        reportId: "",
        reportName: "", //日报名称
        reportList: [],
      };
    },
    resBackReportList() {
      this.$refs["ruleForm2"].clearValidate(["reportList"]);
    },
    exportAll(issueNumT) {
      dailyReportExport({ issueNum: issueNumT }).then((response) => {
        if (response.status == 200) {
          this.$message({
            message: "文件下载成功",
            type: "success",
          });
        } else {
          this.$message.error("文件下载失败");
        }
        downloadFuc(response);
      });
    },
    //

    //生成
    generateBox() {
      this.dialogVisible1 = true;
      dailyReportissueNum({}).then((res) => {
        if (res.data.data) {
          this.form1.issueNum = res.data.data;
        }
      });
    },
    //生成-submitSave
    submitSave() {
      // consoel.log(date)
      this.$refs["ruleForm1"].validate((valid) => {
        if (valid) {
          dailyReportGenerate(this.form1).then((res) => {
            if (res.data.status === 200) {
              this.exportAll(this.form1.issueNum);
              // this.$message.success("数据生成");
              this.dialogVisible1 = false;
              this.resetForm1();
              this.getData();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    //发布-打开弹框
    lastReport(val) {
      this.dialogVisible2 = true;
      this.form2.issueNum = val.issueNum; //期号
      this.form2.reportId = val.reportId;
      this.form2.reportName = val.reportName; //日报名称
      this.form2.issuePerson = val.issuePerson; //发布人
      this.form2.reportList = val.reportList || [];
    },
    //弹框保存
    submit() {},
    //查看
    async openSee(item) {
      this.dialogVisible3 = true;
      this.attachId = item.reportList[0].attachId;
      this.showNodata = false;
      //pdf预览
      // var axiosRes = await axios({
      //   method: "post",
      //   url: "/gemp-file/api/attachment/findById/v1",
      //   data: { attachId: item.reportList[0].attachId },
      // });
      // this.loading = false;
      //  var filePathPdf = axiosRes.data.data[1];
      //  this.pdfUrl = "/upload/" + filePathPdf;

      //word预览
      axios({
        method: "post",
        url: "/gemp-file/api/attachment/download/v1",
        data: { fileId: item.reportList[0].attachId },
        responseType: "blob",
      }).then((res) => {
        // 对后端返回二进制流做处理
        const blob = new Blob([res.data]);
        docx
          .renderAsync(blob, this.$refs.word)
          .then((res) => {
            this.showNodata = false;
          })
          .catch((err) => {
            this.showNodata = true;
            // this.$message.error("暂无数据");
          });
        // docx.renderAsync(res, this.$refs.word); // 渲染到页面
      });
    },
    //发布-submitSaveLast

    submitSaveLast() {
      this.$refs["ruleForm2"].validate((valid) => {
        if (valid) {
          dailyReportIssue(this.form2).then((res) => {
            if (res.data.status === 200) {
              this.$message.success("提交成功");
              this.dialogVisible2 = false;
              this.getData();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },

    //下载最终报告
    reviewDown() {
      let par = {
        fileId: this.attachId,
      };
      Attachmentdownload(par).then((res) => {
        downloadFuc(res);
      });
    },

    //删除
    dailyReportDetele(row) {
      this.$confirm("确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          dailyReportDelete({ reportId: row.reportId }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success(res.data.data);
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getData();
            } else {
              this.$message.error(res.data.data);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {},
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData();
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    handleSelectionChange(val) {},
    handleClick() {},
    //列表查询
    getData() {
      // this.loading = true;
      const dto = {
        startDate: this.dateTime ? this.dateTime[0] : "",
        endDate: this.dateTime ? this.dateTime[1] : "",
        nowPage: this.currentPage,
        // reportId:'reportId',
        keyword: "",
        pageSize: 10,
      };
      dailyReportFindPage(dto).then((res) => {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
        // this.loading = false;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    console.log(this.$store.state, "this.$store.state");
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
      district: (state) => state.controler.district,
      enterData: (state) => state.login.enterData,
    }),
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-descriptions .is-bordered .el-descriptions-item__cell {
  padding: 0 0 25px 0 !important;
  background-color: #fff !important;
  text-align: right !important;
  color: #252525;
}
/deep/ .el-form-item {
  margin-bottom: 0;
  margin-top: 0;
}

/deep/ .el-form-item__error {
  padding-top: 2px;
}
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
/deep/ .l .el-input {
  width: 150px;
}
/deep/ .el-image__preview {
  cursor: zoom-in;
}
/deep/ .el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 0;
  padding: 12px 10px;
}
/deep/ .el-form-item.uploadBox.el-form-item--medium .el-form-item__content {
  text-align: left !important;
}

.dayReport {
  .reviewDownStyle {
    position: absolute;
    right: 26px;
    top: 56px;
  }
  .redDot {
    color: red;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .table-main {
    background: #fff;

    .table-top {
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
        float: left;
      }

      button {
        float: right;
        margin-top: 10px;
      }
    }

    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
/deep/ .uploadBox .el-form-item__content > div {
  display: inline-block;
}
/deep/ .uploadBox .el-form-item__content > div .tipInfo {
  color: #ccc;
}
/deep/ .is-disabled.el-button--text {
  color: #ccc;
}
</style>
<style></style>
