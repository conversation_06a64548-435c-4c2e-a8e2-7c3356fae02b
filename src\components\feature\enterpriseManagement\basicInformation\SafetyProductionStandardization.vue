<template>
  <div class="SafetyProductionStandardization">
    <div class="header">
      <div style="display: flex; justify-content: flex-start">
        <div class="title">安全生产标准化列表</div>
        <!-- <div class="legend">
          <div class="red">
            <div class="point"></div>
            逾期
          </div>
          <div class="yellow">
            <div class="point"></div>
            即将逾期（三个月）
          </div>
        </div> -->
      </div>
      <!-- <div>
         <el-input
            style="width:260px"
            v-model.trim="certCode"
            placeholder="请输入查询证书编号"
            class="input"
            size="mini"
            clearable
            @clear="clearDangerName(event)"
            @keyup.enter.native="search"
          ></el-input>
           <el-button
        icon="el-icon-plus"
        size="mini"
        type="success"
        @click="openDialog('新增安全生产标准化证')"
        v-if="user.user_type === 'ent'"
        >新增
      </el-button>
      </div> -->
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="listNew"
        :header-cell-style="{
          textAlign: 'center',
          backgroundColor: 'rgb(242, 246, 255)',
        }"
        border
        style="width: 100%;"
        height="50vh"
      >
        <!-- <el-table-column label="序号" type="index" width="60"></el-table-column> -->

        <el-table-column
          label="标准化自评分数"
          prop="evaluationScore"
           width="200"
        >

         <template slot-scope="{ row }">
            <span>{{row.evaluationScore || "无"}}</span>
          </template>
        
        
        </el-table-column>
        <el-table-column
          label="标准化评审"
          prop="auditInfo"
         
        >

         <template slot-scope="{ row }">
            <span>{{row.auditInfo || "无"}}</span>
          </template>
        
        
        </el-table-column>

        <el-table-column label="操作" prop="go"  width="200">
          <template>
            <span v-if="$store.state.login.user.user_type == 'ent'"
              ><a href="http://zwfw.hubei.gov.cn/" target="_blank">更多</a
            ></span>
            <span v-else
              ><a
                href="https://yyzc.hbsis.gov.cn:30004/Secures/index"
                target="_blank"
              >更多</a
            ></span>
          </template>
          <!-- v-if="$store.state.login.user.user_type !== 'ent'" -->
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="table" v-loading="loading">
      <el-table
        :data="list"
        :header-cell-style="{
          textAlign: 'center',
          backgroundColor: 'rgb(242, 246, 255)',
        }"
        border
        style="width: 100%"
        height="50vh"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column
          label="更新时间"
          prop="updateTime"
          width="150px"
        ></el-table-column>
        <el-table-column label="证书编号" prop="certCode"></el-table-column>
        <el-table-column label="发证机关" prop="issueOrg"></el-table-column>
        <el-table-column
          label="证书生效时间"
          prop="startTime"
          width="150px"
        ></el-table-column>
        <el-table-column
          label="证书失效时间"
          prop="endTime"
          width="120px"
        ></el-table-column>
        <el-table-column label="证书附件" prop="attachmentList"
          ><template slot-scope="{ row }"
            ><el-button type="text" @click="openAttachment(row.attachmentList)"
              >附件
            </el-button></template
          ></el-table-column
        >
        <el-table-column label="状态" prop="certStatusName" width="110px">
          <template slot-scope="scope">
            <span slot="reference" v-if="scope.row.certStatus == 2">
              <i class="dotClass" style="background-color: red"></i>
            </span>
            <span slot="reference" v-if="scope.row.certStatus == 3">
              <i class="dotClass" style="background-color: yellow"></i>
            </span>
            {{ scope.row.certStatusName }}
          </template>
        </el-table-column>
        <el-table-column
          label="倒计时天数"
          prop="expDays"
          width="110px"
        ></el-table-column>
        <el-table-column label="操作" width="200px">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              @click="openDialog('安全生产标准化证详情', row.id)"
              >详情
            </el-button>
            <el-button
              type="text"
              v-if="user.user_type === 'ent'"
              @click="openDialog('编辑安全生产标准化证', row.id)"
              >编辑
            </el-button>
            <el-button
              type="text"
              v-if="user.user_type === 'ent'"
              @click="deleteFun(row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size="size"
        :total="total"
        background
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div> -->
    <el-dialog
      :title="title"
      :visible.sync="newAddVisible"
      append-to-body
      top="5vh"
      width="800px"
      @close="closeDialog()"
      :close-on-click-modal="false"
    >
      <div class="newAddVisible" v-loading="dialogLoading">
        <div class="box">
          <el-form
            label-position="right"
            label-width="180px"
            :model="form"
            ref="form"
          >
            <el-form-item label="证书附件：" prop="attachmentList">
              <div>
                <AttachmentUpload
                  :attachmentlist="form.attachmentList"
                  :limit="1"
                  type="img"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入证书编号', trigger: 'blur' },
              ]"
              label="证书编号："
              prop="certCode"
            >
              <el-input
                placeholder="请输入证书编号"
                :disabled="disabled"
                maxlength="50"
                type="text"
                v-model.trim="form.certCode"
              ></el-input>
            </el-form-item>
            <el-form-item label="企业名称：" prop="orgName">
              <el-input
                disabled
                type="text"
                maxlength="50"
                placeholder="请输入单位名称"
                v-model.trim="form.orgName"
              ></el-input>
            </el-form-item>
            <el-form-item label="安全生产标准化等级：" prop="standardLevel">
              <el-select
                placeholder="请选择安全生产标准化等级"
                :disabled="disabled"
                style="width: 100%"
                v-model="form.standardLevel"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :rules="[
                {
                  required: true,
                  message: '请选择证书生效时间',
                  trigger: 'blur',
                },
              ]"
              label="证书生效时间："
              prop="startTime"
            >
              <el-date-picker
                placeholder="请选择证书生效时间"
                :disabled="disabled"
                style="width: 100%"
                type="date"
                v-model="form.startTime"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              :rules="[
                {
                  required: true,
                  message: '请选择证件失效时间',
                  trigger: 'blur',
                },
              ]"
              label="证件失效时间："
              prop="endTime"
            >
              <el-date-picker
                placeholder="请选择证件失效时间"
                style="width: 100%"
                type="date"
                :disabled="disabled"
                v-model="form.endTime"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入发证机关', trigger: 'blur' },
              ]"
              label="发证机关："
              prop="issueOrg"
            >
              <el-input
                type="text"
                :disabled="disabled"
                maxlength="30"
                placeholder="请输入发证机关"
                v-model.trim="form.issueOrg"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div
        slot="footer"
        style="display: flex; justify-content: center"
        v-if="!disabled"
      >
        <el-button type="primary" @click="submit(form.id)" :loading="btnLoading"
          >确定</el-button
        >
        <el-button @click="closeDialog()">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="证书附件"
      :visible.sync="imgVisible"
      append-to-body
      top="5vh"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="this.imgSrc">
        <el-image
          style="width: 100%; height: 60vh"
          :src="[imgSrc]"
          fit="scale-down"
          :preview-src-list="[imgSrc]"
        ></el-image>
      </div>
      <div v-else style="width: 100%; height: 60vh" class="noData">
        暂无附件
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import {
  getCompanyCertDelete,
  getCompanyCertFindOne,
  getCompanyCertList,
  standardEvaluation,
  getCompanyCertSave,
  getCompanyCertType,
  getCompanyCertUpdate,
} from "../../../../api/BasicDataManagement";
export default {
  name: "SafetyProductionStandardization",
  components: {
    AttachmentUpload,
  },
  props: ["orgCode", "orgName"],
  data() {
    return {
      list: [],
      listNew: [],
      certCode: "",
      currentPage: 1,
      size: 10,
      total: 0,
      tableData: {},
      newAddVisible: false,
      disabled: true,
      iconLoading: false,
      title: "",
      options: [
        { label: "无", value: "0" },
        { label: "一级", value: "1" },
        { label: "二级", value: "2" },
        { label: "三级", value: "3" },
      ],
      value: "",
      loading: false,
      btnLoading: false,
      dialogLoading: false,
      imgVisible: false,
      imgSrc: "",
      form: {
        address: "", //地址/住所
        addressWorksite: "", //生产地址
        attachmentList: [],
        businessType: "", //业务类型
        certCode: "", //证件编号
        certName: "", //证件名称
        companyType: "", //单位类型
        economicType: "", //经济类型/经济方式
        endTime: "", //证件有效期(结束)
        id: "", //主键
        issueDate: "", //发证日期
        issueOrg: "", //发证机构
        maillistPersonId: "", //负责人/法定代表人
        orgCode: "", //企业机构
        permitScope: "", //许可范围
        standardLevel: "", //安全生产标准化等级
        startTime: "", //证件有效期(开始)
        type: "4", //证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-安全生产标准化证书)
      },
    };
  },

  mounted() {},

  methods: {
    search() {
      this.getData();
    },
    clearDangerName(e) {
      this.certCode = "";
      this.getData();
    },
    deleteFun(id) {
      this.$confirm("确认删除该数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        getCompanyCertDelete({ id: id }).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg);
            if (this.list.length === 1 && this.currentPage !== 1) {
              this.currentPage--;
            }
            this.getData();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    getData(id) {
      this.loading = true;
      this.listNew = []
      standardEvaluation({ enterpId: id }).then((res) => {
        this.loading = false;
        this.listNew.push(res.data.data);
      });
      // getCompanyCertList({
      //   certCode: this.certCode,
      //   certName: "",
      //   issueOrg: "",
      //   nowPage: this.currentPage,
      //   orgCode: this.orgCode, //this.$store.state.login.enterData.enterpId  this.orgCode
      //   pageSize: 10,
      //   types: ["4"],
      //   //   证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-安全生产标准化证书 )
      // }).then((res) => {
      //   this.loading = false;
      //   this.list = res.data.data.list;
      //   this.total = res.data.data.total;
      // });
    },
    submit(id) {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form.issueDate = this.form.startTime;
          this.form.address = this.form.addressWorksite;
          this.btnLoading = true;
          if (id) {
            getCompanyCertUpdate(this.form).then((res) => {
              this.btnLoading = false;
              if (res.data.status === 200) {
                this.$message.success(res.data.msg);
                this.getData();
                this.closeDialog();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {
            getCompanyCertSave(this.form).then((res) => {
              this.btnLoading = false;
              if (res.data.status === 200) {
                this.$message.success(res.data.msg);
                this.getData();
                this.closeDialog();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    getOption() {
      this.iconLoading = true;
      getCompanyCertType().then((res) => {
        this.options = res.data.data;
        this.iconLoading = false;
        // console.log(res.data);
      });
    },
    handleCurrentChange() {
      this.getData();
    },
    closeDialog() {
      this.newAddVisible = false;
      this.form = {
        address: "", //地址/住所
        addressWorksite: "", //生产地址
        attachmentList: [],
        businessType: "", //业务类型
        certCode: "", //证件编号
        certName: "", //证件名称
        companyType: "", //单位类型
        economicType: "", //经济类型/经济方式
        endTime: "", //证件有效期(结束)
        id: "", //主键
        issueDate: "", //发证日期
        issueOrg: "", //发证机构
        maillistPersonId: "", //负责人/法定代表人
        orgCode: "", //企业机构
        permitScope: "", //许可范围
        standardLevel: "", //安全生产标准化等级
        startTime: "", //证件有效期(开始)
        type: "4", //
      };
      this.$refs.form.resetFields();
    },
    openDialog(title, id) {
      this.title = title;
      switch (title) {
        case "新增安全生产标准化证":
          this.disabled = false;
          this.form.orgName = this.user.org_name;
          this.form.orgCode = this.orgCode;
          break;
        case "安全生产标准化证详情":
          this.disabled = true;
          this.getCompanyCertFindOneFun(id);
          break;
        case "编辑安全生产标准化证":
          this.disabled = false;
          this.getCompanyCertFindOneFun(id);
          break;
        default:
          break;
      }
      this.newAddVisible = true;
    },
    openAttachment(img) {
      this.imgVisible = true;
      this.imgSrc = "";
      if (img.length > 0) {
        this.imgSrc = img[0].urlOuterNet;
      } else {
        this.imgSrc = "";
      }
    },
    getCompanyCertFindOneFun(id) {
      this.dialogLoading = true;
      getCompanyCertFindOne({ id: id }).then((res) => {
        this.dialogLoading = false;
        this.form = res.data.data;
        // this.form.orgCode = this.orgCode;
        // this.form.orgName = this.user.org_name;
        if (this.form.attachmentList === null) {
          this.form.attachmentList = [];
        }
      });
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {},
};
</script>

<style lang="scss" scoped>
.noData {
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
}
/deep/ .el-image__preview {
  cursor: -moz-zoom-in;
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
.dotClass {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 10px; //这个用于圆点居中
}
.SafetyProductionStandardization {
  height: 60vh;
  overflow: auto;
  .header {
    display: flex;
    justify-content: space-between;

    .title {
      font-weight: 600;
      font-size: 18px;
      margin-right: 50px;
    }

    .legend {
      display: flex;
      align-items: center;
      margin-right: 5px;

      .red {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .point {
          background: red;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }

      .yellow {
        display: flex;
        align-items: center;

        .point {
          background: yellow;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }
    }
  }

  .table {
    margin-top: 15px;
  }

  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}

.newAddVisible {
  width: 100%;
  overflow-y: scroll;
  height: 60vh;

  .box {
    width: 70%;
    margin: 0 auto;
  }
}
</style>
