<template>
  <div>
    <h2>动态风险指标项</h2>
    <el-tabs
      v-model="activeName"
      :tab-position="tabPosition"
      style="height: 200px"
      @tab-click="changeTab"
    >
      <el-tab-pane label="特殊时期修正" name="specialData">
        <specialData ref="specialDataBox"></specialData>
      </el-tab-pane>
      <el-tab-pane label="物联网指标修正" name="InternetData">
        <InternetData ref="InternetDataBox"></InternetData>
      </el-tab-pane>
      <el-tab-pane label="自然灾害修正" name="naturalData">
        <naturalData ref="naturalDataBox"></naturalData>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import specialData from "@/components/feature/riskAssessment/riskDynamic/configure/specialData.vue";
import naturalData from "@/components/feature/riskAssessment/riskDynamic/configure/naturalData.vue";
import InternetData from "@/components/feature/riskAssessment/riskDynamic/configure/InternetData.vue";
export default {
  components: {
    specialData,
    naturalData,
    InternetData,
  },
  data() {
    return {
      tabPosition: "left",
      activeName: "specialData",
    };
  },
  methods: {
    changeTab(tab, event) {
      if (this.activeName == "specialData") {
        this.$nextTick(() => {
          this.$refs["specialDataBox"].getAccidentList(1); //活动
          this.$refs["specialDataBox"].getAccidentList(0); //节假日
          this.$refs["specialDataBox"].getFindPeriodList(1);
          this.$refs["specialDataBox"].getFindPeriodList(0);
        });
      } else if (this.activeName == "InternetData") {
        this.$nextTick(() => {
          this.$refs["InternetDataBox"].getAccidentList();
          this.$refs["InternetDataBox"].getaccidentTypeList();
        });
      } else if (this.activeName == "naturalData") {
        this.$nextTick(() => {
          this.$refs["naturalDataBox"].getAccidentList();
          this.$refs["naturalDataBox"].getaccidentTypeList();
        });
      }
    },
  },
};
</script>
<style scoped lang="scss">
/deep/ .el-tabs.el-tabs--left {
  height: auto !important;
}
.boxRight {
  height: calc(100% - 50px);
  display: block;
}
</style>