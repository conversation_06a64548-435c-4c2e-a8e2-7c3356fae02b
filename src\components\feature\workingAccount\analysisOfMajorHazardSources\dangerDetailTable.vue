<template>
  <div class="historyListes">
    <el-dialog
      :title="titleName"
      :visible.sync="showDetail"
      @close="closeBooleanTwo(false)"
      width="1350px"
      top="5vh"
      :destroy-on-close="true"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="seach-part">
        <div class="l">
          <!-- <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist"
          ></el-cascader>
          <el-select
            v-model="majorHazardLevel"
            placeholder="请选择重大危险源等级"
            size="mini"
            clearable
            @clear="clearMa"
             @change="getEntData(distCode, type, areaName)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <!-- <el-input
            placeholder="请输入企业名称"
            v-model="enterName"
            size="mini"
            clearable
            @clear="clearEntName"
          >
          </el-input> -->
          <!-- <el-button type="primary" size="mini"  class="btn" @click="search">查询</el-button> -->
          <CA-button type="primary" size="mini" class="btn" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="distName" label="行政区划" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          prop="enterName"
          label="企业名称"
          width="300"
          align="center"
          :show-overflow-tooltip="true"
        >
          <!-- <template slot-scope="scope">
            <span @click="goEnt(scope.row)" style="color: #3977ea; cursor: pointer">{{
              scope.row.enterName
            }}</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="dangerName" label="重大危险源名称" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          prop="dangerLevelCode"
          label="重大危险源等级"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="deviceType"
          label="设备类型"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
      </el-table>
      <div class="paginationes">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import History from "./history"
import {
  queryDangerList,
  exportCqz,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      showDetail: false,
      type: "",
      currentPage: 1,
      size:10,
      dangerType:'',
      dangerId:'',
      enterName:'',
      loading: true,
      tableData: [],
      total: 0,
      areaName: "",
      titleName: "",
      selection: [],
      enterpName:'',
      dangerName:'',
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist
    }),
  },
  //方法集合
  methods: {
    closeBooleanTwo(val) {
        this.currentPage = 1;
       this.enterName="";
      this.showDetail = val;
    },
    handleChange(value) {
      // if (value && value.length > 0) {
      //   this.distCode = value[value.length - 1];
      // }
    },
    goEnt(row) {
      // console.log(row);
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.entId);
    },
    getEntData(enterpName, type, dangerName,dangerType,dangerId) {
      this.enterpName = enterpName;
      this.loading = true;
      this.type = type;
      this.dangerName = dangerName;
      this.dangerType = dangerType;
      this.dangerId = dangerId;
      if (type == 0) {
        this.titleName = this.enterpName + "-" + this.dangerName+"  储罐清单";
      } else if (type == 1) {
        this.titleName = this.enterpName + "-" + this.dangerName+"  气体泄漏点清单";
      } else if (type == 2) {
        this.titleName = this.enterpName + "-" + this.dangerName+"  装置清单";
      } else if (type === 3) {
        this.titleName = this.enterpName + "-" + this.dangerName+"  仓库清单";
      }
      queryDangerList({
        // enterName: this.enterName,
        dangerType:this.dangerType,
        dangerId: this.dangerId,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    exportExcel() {
      let list = [...this.selection];
      if(list.length<=0){
        this.$message({
            message: "请选择导出内容",
            type: "error",
          });
        return
      }
      
      exportCqz({
        // distCode: this.distCode,
        idList: list,
        dangerType:this.dangerType,
        dangerId: this.dangerId,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
          let filename = '';
        if (this.dangerType == 'G') {
            filename = this.enterpName + "-" + this.dangerName+"  储罐清单"+ timestamp + ".xls";
        } else if (this.dangerType == 'Q') {
            filename = this.enterpName + "-" + this.dangerName+"  气体泄漏点清单"+ timestamp + ".xls";
        } else if (this.dangerType == 'P') {
            filename = this.enterpName + "-" + this.dangerName+"  装置清单" + timestamp + ".xls";
        }
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getEntData(this.enterpName, this.type, this.dangerName,this.dangerType,this.dangerId);
    },
    search() {
      this.currentPage = 1;
      this.getEntData(this.enterpName, this.type, this.dangerName,this.dangerType,this.dangerId);
    },
    clearEntName() {
      this.enterName = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
    //   width: 350px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      >.btn{
        margin-right: 15px;
      }
    }
  }
  .paginationes {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
</style>
<style>
</style>