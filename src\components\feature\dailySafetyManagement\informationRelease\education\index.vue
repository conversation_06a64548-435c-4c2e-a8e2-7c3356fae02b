<template>
  <div class="app-container">
    <div class="specification-from-box">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px !important"
      >
        <el-form-item prop="earlyWarnTitle">
          <el-input
            v-model.trim="queryParams.earlyWarnTitle"
            placeholder="请输入宣传教育标题"
            clearable
            maxlength="100"
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="earlyWarnStatus"  v-if="roleInfo.user_type !== 'ent'">

          <el-select
            v-model="queryParams.earlyWarnStatus"
            placeholder="请选择状态"
            clearable
            size="small"
          >
            <el-option
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleQuery"
            >查询</el-button
          >
        </el-form-item>
        <el-form-item>
          <el-button size="small" type="primary" plain @click="openTemplate(1)" v-if="roleInfo.user_type !== 'ent'"
            >选择模板</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <div class="specification-table-head" id="abc" >
      <div class="left">宣传教育列表</div>
      <!-- 测试 -->
      <div>
        <el-button
          type="primary"
          v-if="roleInfo.user_type !== 'ent'"
          plain
          icon="el-icon-plus"
          size="small"
          @click="handleAdd"
          >新增</el-button
        >
      </div>
    </div>

    <el-table v-loading="loading" :data="safetyList">
      <el-table-column label="序号" align="center" type="index" width="70" />
      <el-table-column
        label="宣传教育标题"
        align="center"
        prop="earlyWarnTitle"
      >
        <template slot-scope="scope">
          <div class="title-hover">{{ scope.row.earlyWarnTitle }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="releaseTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        prop="earlyWarnStatus"
        width="180"
      >
        <template slot-scope="scope">
          <span>
            {{ scope.row.earlyWarnStatus == 0 ? "待发布" : "已发布" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <!-- 已发布：1 没有编辑和删除 -->
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            v-if="scope.row.earlyWarnStatus != 1"
            >编辑</el-button
          >

          <!--          <el-button round size="mini" @click="cancelPublishFun(scope.row)"-->
          <!--            >取消发布</el-button-->
          <!--          >-->

          <el-button
            type="text"
            @click="handleDelete(scope.row)"
            v-if="scope.row.earlyWarnStatus != 1"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        :total="total"
        :page.sync="queryParams.nowPage"
        :limit.sync="queryParams.pageSize"
        @current-change="handleGetList"
        background
        layout="total, prev, pager, next"
      ></el-pagination>
    </div>

    <!-- 添加或修改宣传教育对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
      top="10vh"
      :close-on-click-modal="false"
    >
      <div style="height: 600px" v-loading="dialogLoading" class="dialogH">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px !important"
        >
          <el-form-item label="宣传教育标题" prop="earlyWarnTitle">
            <el-input
              v-model.trim="form.earlyWarnTitle"
              maxlength="30"
              placeholder="请输入标题"
            />
          </el-form-item>

          <el-form-item label="宣传教育内容" prop="earlyWarnContent">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="1000"
              show-word-limit
              placeholder="请输入宣传教育内容"
              v-model.trim="form.earlyWarnContent"
            ></el-input>
          </el-form-item>
        </el-form>

        <div class="btnbox">
          <el-button
            v-if="roleInfo.user_type !== 'ent'"
            type="primary"
            @click="chooseTemplate()"
            icon="el-icon-tickets"
            size="small"
            >选择模板</el-button
          >
        </div>
        <channel-list
          ref="channelList"
          :chanList="chanList"
          :checkLists="checkLists"
          @selectList="selectList"
        ></channel-list>
      </div>

      <div slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" @click="submitForm(1)">发布</el-button>
        <el-button type="primary" @click="submitForm(0)">保存草稿</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <TemplateChoose
      ref="templateChoose"
      @choosedTemplateInfo="choosedTemplateInfo"
      :temType="1"
      :temStatus="1"
    ></TemplateChoose>
    <!-- 新增模板 -->
    <el-dialog
      title="模板列表"
      :close-on-click-modal="false"
      :visible.sync="isTemplateOpen"
      width="1200px"
      top="10vh"
      append-to-body
    >
      <Template></Template>
    </el-dialog>
    <!-- 详情弹窗 -->
    <SafetyDetails ref="safetyDetails" :closable="closable"></SafetyDetails>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { addSafety, listSafetys } from "../../../../../api/informationRelease";
import { delSafety, getSafety } from "../../../../../api/BasicDataManagement";

const TemplateChoose = () => import("../component/TemplateChoose");
const Template = () => import("../component/Template");
const ChannelList = () => import("../component/ChannelList");
const SafetyDetails = () => import("../component/SafetyDetails");
const checkConIsEmpty = (rule, value, callback) => {
  if (!value.trim()) {
    return callback(new Error("内容不能为空"));
  } else {
    callback();
  }
};

export default {
  name: "education",
  components: {
    TemplateChoose,
    ChannelList,
    SafetyDetails,
    Template,
  },
  data() {
    return {
      checkLists: [],
      listSelect: "",
      roleInfo: {},
      dialogLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全预警表格数据
      safetyList: [],
      // 弹出层标题
      title: "",
      titles: "",
      // 是否显示弹出层
      open: false,
      isTemplateOpen: false,
      // 查询参数
      queryParams: {
        nowPage: 1,
        pageSize: 10,
        earlyWarnTitle: null,
        earlyWarnStatus: "",
        typeOfEarlyWarn: "1",
      },
      // 表单参数
      form: {
        orgDTOs: [],
        rcveUnit: "",
      },
      // 表单校验
      rules: {
        earlyWarnTitle: [
          {
            required: true,
            message: "请输入宣传教育标题",
            trigger: ["blur", "change"],
          },
          {
            validator: checkConIsEmpty,
            trigger: ["blur", "change"],
          },
        ],
        earlyWarnContent: [
          {
            required: true,
            message: "请输入宣传教育内容",
            trigger: ["blur", "change"],
          },
          {
            validator: checkConIsEmpty,
            trigger: ["blur", "change"],
          },
        ],
      },
      statusList: [
        { label: "已发布", value: "1" },
        { label: "待发布", value: "0" },
      ],
      chanList: [],
      closable: true,
    };
  },

  created() {
    this.roleInfo =
      JSON.parse(sessionStorage.getItem("VueX_local")).root.login.user || {};
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
    }),
  },
  methods: {
    handleGetList(val) {
      this.queryParams.nowPage = val;
      this.getList();
    },
    openTemplate() {
      this.isTemplateOpen = true;
      // this.$nextTick(() => {
      //   console.log(this.$refs.Template, "this.$refs.Template.queryParams");
      //   this.$refs.Template.queryParams.tmpltType = val;
      //   this.$refs.Template.form.tmpltType = val;
      // });
    },

    selectList(list) {
      this.form.instCode = list;
      this.listSelect = list;
      console.log(list, "发布对象");
    },

    chooseTemplate() {
      this.$refs.templateChoose.open = true;
      this.$nextTick(() => {
        this.$refs.templateChoose.getList();
      });
    },

    choosedTemplateInfo(e) {
      this.form.earlyWarnTitle = e.tmpltName;
      this.form.earlyWarnContent = e.tmpltContent;
    },

    /** 查询安全预警列表 */
    getList() {
      this.queryParams.issueUnitCode = this.user.org_code;
      this.queryParams.queryOrgCode = this.user.org_code;
      // this.queryParams.earlyWarnStatus = "";
      this.loading = true;
      listSafetys(this.queryParams).then((response) => {
        this.safetyList = response.data.data.list;
        this.total = response.data.data.total || 0;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        warnCode: null,
        earlyWarnTitle: null,
        releaseTime: null,
        earlyWarnContent: null,
        issueUnitCode: null,
        earlyWarnStatus: null,
        instCode: null,
        typeOfEarlyWarn: "1",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.nowPage = 1;
      this.queryParams.earlyWarnTitle = this.queryParams.earlyWarnTitle
        ? this.queryParams.earlyWarnTitle.trim()
        : "";
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.warnCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.resetForm("form");
      this.resetForm("queryForm");
      this.$set(this.form, "", "");
      this.chanList = [];
      this.checkLists = [];
      this.$nextTick(() => {
        this.open = true;
        this.title = "添加宣传教育";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // this.reset();
      // this.resetForm("form");
      // this.resetForm("queryForm");
      this.open = true;
      this.title = "修改宣传教育";
      this.dialogLoading = true;
      const warnCode = row.warnCode || this.ids;
      getSafety({ warnCode: warnCode }).then((response) => {
        this.dialogLoading = false;
        this.form = response.data.data;
        this.checkLists = response.data.data.orgDTOs || [];
      });
    },
    // 某个单元格被点击事件
    handleView(row, col) {
      this.$refs.safetyDetails.open = true;
      this.$refs.safetyDetails.title = "宣传教育详情";
      this.closable = false;
      this.$refs.safetyDetails.titles = "宣传教育";
      // this.$refs.safetyDetails.handleUpdate();
      this.$refs.safetyDetails.orgType = "0";

      getSafety({ warnCode: row.warnCode }).then((response) => {
        this.$nextTick(() => {
          if (response.data.status === 200) {
            this.$refs.safetyDetails.form = response.data.data;
            if (response.data.data.orgDTOs.length > 0) {
              var ary=[]
              response.data.data.orgDTOs.forEach((item) => {
                ary.push(item.orgName);
              });
              this.$refs.safetyDetails.orgDTOsAry=ary.join(' , ')
            }
          } else {
            this.$message.error(response.data.msg || "请求失败");
          }
        });
      });
    },
    debounce(func, delay) {
      const context = this; // this指向发生变化，需要提出来
      const args = arguments;
      return (function () {
        if (context.timeout) {
          clearTimeout(context.timeout);
        }
        context.timeout = setTimeout(() => {
          func.apply(context, args);
        }, delay);
      })();
    },

    /** 提交按钮 */
    submitForm(type) {
      var that = this;
      let orgAry = [];
      if (this.listSelect.length == 0) {
        //表示回填时候，没有做任何修改
        if (this.checkLists.length > 0) {
          this.checkLists.forEach((item, index) => {
            orgAry[index] = item.orgCode;
          });
        }
      }

      let orgDTOsAry = [];
      if (this.listSelect.length > 0) {
        this.listSelect.forEach((item, index) => {
          orgDTOsAry[index] = item.orgCode;
        });
      }

      let orgDTOs = orgAry.concat(orgDTOsAry);

      if (orgDTOs.length == 0) {
        this.$message.error("请添加发布对象");
        return false;
      }
      that.debounce(() => {
        this.form.orgDTOs = orgDTOs;
        this.form.rcveUnit = orgDTOs.toString();
        this.form.typeOfEarlyWarn = "1";
        this.form.earlyWarnStatus = type;
        // this.form.geReleaseChnList = _geReleaseChnList;
        // this.form.receiveInstCode = _geReleaseChnList[0].publishingobjects || "";
        this.form.issueUnitCode = this.user.org_code;
        this.form.instCode = this.user.org_name;
        console.log(this.form, "宣传教育列表");
        this.$refs["form"].validate((valid) => {
          if (valid) {
            addSafety(this.form).then((response) => {
              if (response.data.status === 200) {
                if(this.form.warnCode){
                  this.$message.success("编辑成功");
                } else {
                  this.$message.success("新增成功");
                }
                this.$refs.channelList.channelList.forEach((item, index) => {
                  item.isApply = false;
                });
                this.open = false;
                this.getList();
              } else {
                this.$message.error("新增失败");
              }
            });
          }
        });
      }, 500);
    },

    // 取消发布的操作
    // cancelPublishFun(row) {
    //   updateStatus({
    //     warnCode: row.warnCode,
    //     earlyWarnStatus: "0",
    //   }).then((response) => {
    //     this.msgSuccess("取消成功");
    //     this.getList();
    //   });
    // },

    /** 删除按钮操作 */
    handleDelete(row) {
      const warnCodes = row.warnCode || this.ids;
      this.$confirm("确认删除该数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delSafety({ warnCode: warnCodes });
        })
        .then((res) => {
          if (res.data.status === 200) {
              if (this.safetyList.length === 1 && this.queryParams.nowPage !== 1) {
                this.queryParams.nowPage--
              }
            this.getList();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有安全预警数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportSafety(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0;
}
.dialogH {
  overflow: auto;
  padding: 20px;
}
.btnbox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
/deep/ .el-form-item__content {
  line-height: 0 !important;
}
.el-form-item {
  // margin-bottom: 0;
  margin-top: 10px;
}
.specification-table-head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  // margin-top: 15px;
  .left {
    font-size: 18px;
    text-align: left;
    font-weight: 900;
  }
}
.pagination {
  display: flex;
  padding: 20px 0;
  justify-content: flex-end;
}
.abc {
  color: red;
  font-size: 20px;
}
</style>
