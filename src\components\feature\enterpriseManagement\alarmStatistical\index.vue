<template>
  <div class="lot">
    <div class="header">
      <div class="operation">
        <div class="inputBox">
          <el-date-picker
            v-model="value1"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            :clearable="false"
          >
          </el-date-picker>

          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <CA-button
            type="primary"
            class="export"
            plain
            size="mini"
            @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
    </div>
    <div class="alarm-summary">
      <h2>企业汇总</h2>
      <ul>
        <li @click="openDialog(enterpriseId, false, starTime, endTime)">
          <span>{{
            this.totalData.warningNum
              ? this.totalData.warningNum
              : this.totalData.warningNum == 0
              ? this.totalData.warningNum
              : "--"
          }}</span>
          <p><i class="colorPoint c1"></i>报警次数<i class="look-icon"></i></p>
        </li>
        <li>
          <span>{{
            this.totalData.repeatWarningPointsNum
              ? this.totalData.repeatWarningPointsNum
              : this.totalData.repeatWarningPointsNum == 0
              ? this.totalData.repeatWarningPointsNum
              : "--"
          }}</span>
          <p><i class="colorPoint c2"></i>重复报警点位数</p>
        </li>
        <li>
          <span>{{
            this.totalData.repeatWarningNum
              ? this.totalData.repeatWarningNum
              : this.totalData.repeatWarningNum == 0
              ? this.totalData.repeatWarningNum
              : "--"
          }}</span>
          <p><i class="colorPoint c3"></i>重复报警次数</p>
        </li>
        <li>
          <span>{{
            this.totalData.averageEliminationDuration
              ? this.totalData.averageEliminationDuration.toFixed(2)
              : this.totalData.averageEliminationDuration == 0
              ? this.totalData.averageEliminationDuration.toFixed(2)
              : "--"
          }}</span>
          <p><i class="colorPoint c4"></i>平均消警时长(h)</p>
        </li>
        <li>
          <span>{{
            this.totalData.pointAverageWarningNum
              ? this.totalData.pointAverageWarningNum
              : this.totalData.pointAverageWarningNum == 0
              ? this.totalData.pointAverageWarningNum
              : "--"
          }}</span>
          <p><i class="colorPoint c5"></i>点位平均报警次数</p>
        </li>
        <li>
          <span>{{
            this.totalData.maxWarningDuration
              ? this.totalData.maxWarningDuration.toFixed(2)
              : this.totalData.maxWarningDuration == 0
              ? this.totalData.maxWarningDuration.toFixed(2)
              : "--"
          }}</span>
          <p><i class="colorPoint c6"></i>最大报警时长(h)</p>
        </li>
        <li>
          <span>{{
            this.totalData.averageDisturbanceRatio
              ? this.totalData.averageDisturbanceRatio
              : this.totalData.averageDisturbanceRatio == 0
              ? this.totalData.averageDisturbanceRatio
              : "--"
          }}</span>
          <p><i class="colorPoint c7"></i>平均扰动率</p>
        </li>
        <li>
          <span>{{
            this.totalData.eliminationTimelyRatio
              ? this.totalData.eliminationTimelyRatio
              : this.totalData.eliminationTimelyRatio == 0
              ? this.totalData.eliminationTimelyRatio
              : "--"
          }}</span>
          <p><i class="colorPoint c8"></i>消警处置及时率</p>
        </li>
      </ul>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        v-loading="loading"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        @row-click="goEnt"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="55"
          fixed="left"
          align="center"
        >
        </el-table-column>
        <el-table-column type="index" label="序号" width="50" align="center">
        </el-table-column>
        <el-table-column
          prop="dangerName"
          label="重大危险源名称"
          align="center"
          width="230"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="warningNum"
          label="报警次数"
          align="center"
          class-name="cursor"
          width="80"
        >
          <template slot-scope="scope">
            <span
              style="color: #3977ea"
              @click="openDialog(scope.row.dangerId, true, starTime, endTime)"
              >{{ scope.row.warningNum }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="repeatWarningPointsNum"
          label="重复报警点位数"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="repeatWarningNum"
          label="重复报警次数"
          align="center"
          width="110"
        >
        </el-table-column>
        <el-table-column
          prop="averageEliminationDuration"
          label="平均消警时长(h)"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.averageEliminationDuration.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="pointAverageWarningNum"
          label="点位平均报警次数"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="maxWarningDuration"
          label="最大报警时长(h)"
          align="center"
          width="125"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.maxWarningDuration.toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="averageDisturbanceRatio"
          label="平均扰动率"
          align="center"
          width="100"
        >
        </el-table-column>
        <el-table-column
          prop="eliminationTimelyRatio"
          label="消警处置及时率"
          align="center"
        >
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>
    <StatisticDetail ref="statisticDetail"></StatisticDetail>
  </div>
</template>
<script>
import { getAlarm, getTotalAlarm, alarmExportExcel } from "@/api/entList";
import { parseTime } from "@/utils/index";
import StatisticDetail from "./statisticDetail";
export default {
  //import引入的组件
  name: "Alarm",
  components: {
    StatisticDetail,
  },
  data() {
    return {
      currentPage: 1,
      value1: "",
      starTime: "",
      endTime: "",
      total: "",
      tableData: [],
      enterpriseId: "",
      totalData: {},
      loading: true,
      selection: [],
    };
  },
  //方法集合
  methods: {
    goEnt(val) {
      console.log(val);
      let bool = true;
      this.$emit("entBool", bool);
    },
    getData(id) {
      this.loading = true;
      this.enterpriseId = id;
      getAlarm({
        enterpriseId: this.enterpriseId,
        //  enterpriseId:'17acd1e0e',
        current: this.currentPage,
        startTime: this.starTime,
        endTime: this.endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    getTotalData(id) {
      this.enterpriseId = id;
      getTotalAlarm({
        enterpriseId: this.enterpriseId,
        //  enterpriseId:'17acd1e0e',
        startTime: this.starTime,
        endTime: this.endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.totalData = res.data.data;
        }
      });
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    search() {
      this.currentPage = 1;
      this.getData(this.enterpriseId);
      this.getTotalData(this.enterpriseId);
    },
    openDialog(dangerId, flag, starTime, endTime) {
      this.$refs.statisticDetail.closeBoolean(true);
      this.$nextTick(function () {
        if (flag) {
          this.$refs.statisticDetail.getDetail(dangerId, starTime, endTime);
        } else {
          this.$refs.statisticDetail.getTotalDetail(
            dangerId,
            starTime,
            endTime
          );
        }
        this.$refs.statisticDetail.drawLine();
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].dangerId;
      }
    },
    // 导出
    exportExcel() {
      if (this.selection.length == 0) {
        this.$message.error("请勾选导出的列表");
        return;
      }
      alarmExportExcel({
        enterpId: this.enterpriseId,
        current: this.currentPage,
        startTime: this.starTime,
        endTime: this.endTime,
        ids: this.selection,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "报警统计分析" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData(this.enterpriseId);
    },
    getTime() {
      this.value1 = [this.get_date(-6), this.get_date(0)];
      this.starTime = this.value1[0];
      this.endTime = this.value1[1];
    },
    get_date(num) {
      var date = new Date(); //获取今天的时间
      var today =
        date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
      var new_date = new Date(date);
      new_date.setDate(date.getDate() + num);
      // num为正数时，获取后num天 ，为负数时，获取前num天，0表示今天。
      var new_day =
        new_date.getFullYear() +
        "-" +
        (new_date.getMonth() + 1) +
        "-" +
        new_date.getDate();
      return new_day;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.lot {
  background-color: #fff;
  //   padding-top: 20px;
  .header {
    .title {
      margin-left: 20px;
      font-weight: 600;
    }
    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        width: 520px;
        display: flex;
        justify-content: space-between;
        .input {
          width: 180px;
        }
      }
    }
    .export {
      margin-right: 20px;
    }
  }
  .alarm-summary {
    width: 100%;
    margin-bottom: 12px;
    height: 70px;
    background: rgba(238, 243, 249, 0);
    border: 1px solid #d8e0ee;
    box-shadow: 0px 0px 8px 0px rgba(121, 163, 241, 0.3);
    border-radius: 4px 4px 0px 0px;
    position: relative;
    h2 {
      width: 100px;
      height: 29px;
      background: url("/static/img/assets/img/xingzhuang.png") no-repeat center;
      background-size: contain;
      color: #000;
      font-weight: bold;
      font-size: 13px;
      text-align: center;
      line-height: 27px;
      position: absolute;
      top: -3px;
      left: 0;
    }
    ul {
      display: flex;
      justify-content: space-around;
      overflow: hidden;
      padding-left: 60px;
      li {
        list-style: none;
        text-align: center;
        span {
          display: block;
          margin-top: 12px;
          font-size: 16px;
          font-weight: bold;
          color: #414548;
        }
        p {
          font-size: 12px;
          color: #545c65;
          i {
            display: inline-block;
          }
          .colorPoint {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            margin-right: 5px;
          }
          .c1 {
            background: #3977ea;
            cursor: pointer;
          }
          .c2 {
            background: #ea3939;
          }
          .c3 {
            background: #fa8733;
          }
          .c4 {
            background: #6092bd;
          }
          .c5 {
            background: #fad233;
          }
          .c6 {
            background: #85c655;
          }
          .c7 {
            background: #936bd5;
          }
          .c8 {
            background: #ff9ff7;
          }
          .look-icon {
            width: 20px;
            height: 20px;
            background: url("/static/img/assets/img/look-icon.png") no-repeat
              center;
            background-size: cover;
            margin-left: 8px;
            vertical-align: top;
            position: relative;
            top: -1px;
          }
        }
      }
      li:first-child {
        cursor: pointer;
      }
    }
  }
  .table {
    width: 100%;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
/deep/   .cursor{
  cursor: pointer;
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
