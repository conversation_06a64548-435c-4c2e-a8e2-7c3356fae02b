import axios from "axios";
import qs from "qs";

//安全承诺分析列表
export const getSafetyListData = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/safetyPromiseAnalysis/safetyPromiseAnalysis?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//安全承诺详情列表
export const getentArr = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/safetyPromiseAnalysis/safetyPromiseDetailsList?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//安全承诺导出

export const getSafetyPromiseExportExcel = (table) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/exportExcel",
    data: { ...table },
    responseType: "arraybuffer",
  });
};
//安全承诺详情导出
export const getSafetyPromiseExportPromiseDetailsToExcel = (table) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/exportPromiseDetailsToExcel",
    data: { ...table },
    responseType: "arraybuffer",
  });
};

// 特殊作业统计列表
export const getSpecialWorkStatistics = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/specialJob/districtCount/list",
    data: { ...data },
  });
};

// 特殊作业导出
export const exportSpecialWorkStatistics = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/specialJob/districtCount/export",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

//企业风险分析列表
export const getEnRiskListData = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/riskAnalysis/districtAnalysis/v1",
    data: { ...data },
  });
};
//企业风险分析列表导出
export const getCimRiskAnalysisExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/riskAnalysis/districtAnalysis/export/v1",
    data: { ...data },
    responseType: "arraybuffer",
  });
};
//企业风险研判统计
export const getEnRiskStatistic = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/riskMonitorWorkorder/statistics/v1",
    data: { ...data },
  });
};
// 查询不同风险等级的分页列表
export const getRiskDetailsList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/riskMonitorWorkorder/statistics/page/v1",
    data: { ...data },
  });
}
//企业风险分析企业详情列表
export const getCimRiskAnalysisCimRiskDetailsList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/companyRiskModel/queryPageRiskPage/v1",
    data: data,
  });
};
//导出企业详情列表
export const getCimRiskAnalysisExportPromiseDetailsToExcel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/companyRiskModel/enterpRiskList/export/v1",
    data: { ...data },
    responseType: "arraybuffer",
  });
};
//企业风险趋势变化
export const getCimRiskAnalysisCimRiskGroupDate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/riskAnalysis/trendAnalysis/v1",
    data: data,
  });
};
//企业重大危险源
export const areaTrendV1 = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/danger/areaTrend/v1",
    data: data,
  });
};
//企业重大危险源列表
export const getDangerList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/danger/videoDangerList/v1",
    data: data,
  });
};
//导出企业风险趋势变化
export const getCimRiskAnalysisExportCimRiskGroupDateToExcel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/riskAnalysis/trendAnalysis/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//重点关注企业列表 废弃！！！
export const getFocuscompanyPageList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/focuscompany/pageList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: { ...data },
  });
};
//重点关注企业列表2 废弃！！！
export const getFocuscompanyFocusTypePageList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/focuscompany/focusTypePageList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: { ...data },
  });
};
//重点关注企业列表3
export const getFocuscompanyFocusOn = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/focuscompany/focusOn",
    params: data,
  });
};
//重点关注企业列表导出3
export const getFocuscompanyExport = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/focuscompany/export",
    data: { ...data },
    responseType: "arraybuffer",
  });
};
//重点关注企业列表导出 废弃！！！
export const getFocuscompanyExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/focuscompany/exportExcel",
    data: { ...data },
    responseType: "arraybuffer",
  });
};
//报警统计分析
export const getAlarmAnalysis = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/alarmAnalysis/alarmAnalysis?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//报警统计分析导出
export const getAlarmAnalysisExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/alarmAnalysis/exportExcel",
    data: { ...data },
    responseType: "arraybuffer",
  });
};
//未消警企业列表
export const getAlarmAnalysisCompanyNotClearedAlarm = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/alarmAnalysis/companyNotClearedAlarm?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//未消警企业列表导出
export const getAlarmAnalysisCompanyNotClearedAlarmExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/alarmAnalysis/companyNotClearedAlarmExportExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//报警统计分析-报警企业数
export const getAlarmAnalysisCompanyAlarmDetailsList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/alarmAnalysis/companyAlarmDetailsList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//报警统计分析-报警企业数导出
export const getAlarmAnalysisCompanyAlarmDetailsListExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/alarmAnalysis/companyAlarmDetailsListExportExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//报警统计分析 –趋势分析
export const getWarningAllEliminateList = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/warning/allEliminate/list",
    params: data,
  });
};
//  报警分析 新接口
export const getWarningAllEliminateList2 = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/warning/allEliminate/list/v2?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//报警统计分析 –趋势分析导出
export const getWarningAllEliminateListExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/warning/allEliminate/list/exportExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//运行状态分析视频运行分析
export const getVideoRunningData = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/video/videoAnalysis?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//安全承诺趋势分析echart数据
export const getSaftyEchartData = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/promise/trend?distCode=" +
      data.distCode +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//安全承诺风险变化echart数据
export const getCommitRiskEchartData = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/promise/riskChange?distCode=" +
      data.distCode +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//安全承诺趋势图列表
export const getSaftyTrendList = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/promise/promiseTrendList?distCode=" +
      data.distCode +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//在线联网分析列表
export const getOnlineNetworking = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/OnlineNetworking?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
export const selTargetCount = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/selTargetCount?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//在线联网分析列表导出
export const getOnlineNetworkingAnalysisExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
export const exportPromiseDetailsToExcelA = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportPromiseDetailsToExcel",
    data: data,
    responseType: "arraybuffer",
  });
};

export const onlineExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/online/analysis/iot/site/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

export const videoExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/online/analysis/video/site/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

export const exportVideoDetailsToExcelB = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/video/exportVideoDetailsToExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//在线联网分析列表详情
export const getOnlineNetworkingAnalysisDetail = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/onlineNetworkingDetailsList?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//在线联网分析详情导出
export const getOnlineNetworkingAnalysisDetailExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportPromiseDetailsToExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//视频监控在线企业列表
export const getVideoworkingAnalysisDetail = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/video/videoDetailsList?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//视频在线分析列表导出
export const getVideoAnalysisExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/video/exportExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//视频在线分析详情导出
export const getVideoAnalysisDetailExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/video/exportVideoDetailsToExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//安全承诺趋势导出
export const getSafetyTrendExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/promise/exportExcel",
    data: data,
    responseType: "arraybuffer",
  });
};
//企业运行分析趋势列表-物联
export const getRunningStateTrendList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/onlineNetworkingGroupDate?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//企业运行分析趋势列表-物联系统平均在线率
export const getRunningStateTrendOnlineLv = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/averageOnlineRate?current=1&size=100",
    data: data,
  });
};
//企业运行分析趋势列表-视频
export const getRunningStateVideoTrendList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/video/onlineVideoGroupDate?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//企业运行分析趋势列表-视频导出
export const getExportVideoGroupDate = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/video/exportVideoGroupDate",
    data: data,
    responseType: "arraybuffer",
  });
};

//企业运行分析趋势列表-物联导出
export const getExportCimRiskGroupDate = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportCimRiskGroupDate",
    data: data,
    responseType: "arraybuffer",
  });
};
//企业运行分析趋势列表-视频平均在线率
export const getAverageVideoOnlineRate = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/video/averageVideoOnlineRate?current=1&size=100",
    data: data,
  });
};

//企业运行分析趋势列表-超量程趋势
export const integrityDataSearch = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/enterprise/integrity/dataSearch/v1",
    data: data,
  });
};

//安全承诺分析-系统平均在线率
export const getSafetyPromiseAverageOnlineRate = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/averageOnlineRate?current=1&size=100",
    data: data,
  });
};
//重大危险源分析列表
export const queryMajors = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/major/danger/queryMajors?distCode=" +
      data.distCode +
      "&size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//重大危险源分析详情
export const queryMajorDetails = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/major/danger/queryMajorDetails?distParentCode=" +
      data.distParentCode +
      "&distCode=" +
      data.distCode +
      "&dangerLevelCode=" +
      data.dangerLevelCode +
      "&dangerName=" +
      data.dangerName +
      "&current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//查询 储罐/气体泄漏点/装置 列表
export const queryDangerList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/major/danger/queryDangerList?dangerType=" +
      data.dangerType +
      "&dangerId=" +
      data.dangerId +
      "&current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//重大危险源分析导出
export const exportMajor = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/major/danger/exportMajor",
    data: data,
    responseType: "arraybuffer",
  });
};
//危险源清单导出
export const exportMajorDetails = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/major/danger/exportMajorDetails",
    data: data,
    responseType: "arraybuffer",
  });
};
//储罐/气体泄漏点/装置导出
export const exportCqz = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/major/danger/export",
    data: data,
    responseType: "arraybuffer",
  });
};

//视频分析报警地区统计
export const alarmDistrictCount = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/alarmDistrictCount/v1",
    data: { ...data },
  });
};

//视频分析报警企业统计
export const alarmEnterpCount = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/videoAlarm/alarmEnterpCount/v1?size=" +
      data.size +
      "&current=" +
      data.current,
    data: { ...data },
  });
};

//
export const alarmDistrictExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/alarmDistrictExport/v1",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

export const alarmEnterpExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/alarmEnterpExport/v1",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

//安全承诺分析-督促
export const addUrge = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/addUrge",
    data: data,
  });
};

//超量程企业统计
export const clcEnterCount = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/clcEnterCount?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//超量程企业列表
export const clcEnterListt = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/clcEnterList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//超量程指标列表
export const clcTargetList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/clcTargetList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//导出超量程企业统计
export const exportClcEnterCount = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportClcEnterCount",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

//导出超量程企业列表
export const exportEnterList = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportEnterList",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

//导出超量程指标列表
export const exportClcTargetList = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlineNetworkingAnalysis/exportClcTargetList",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

// export const selTargetCount = data => {
//   return axios({
//     method: "post",
//     url: '/enterprise/onlineNetworkingAnalysis/selTargetCount?size='+data.size+'&current='+data.current,
//     data: data
//   });
// };

// 物联监测状态统计
export const itoSatistics = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/online/analysis/iot/statistics/v1?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
// 视频监测状态统计
export const videoSatistics = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/online/analysis/video/statistics/v1?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//视频监控在线企业列表
export const onlineIotSite = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/online/analysis/iot/site/page/v1?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};

//在线联网分析列表详情
export const videoSitePage = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/online/analysis/video/site/page/v1?nowPage=" +
      data.nowPage +
      "&pageSize=" +
      data.pageSize,
    data: data,
  });
};

//物联监测状态主体分页列表查询
export const iotMainPage = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/online/analysis/iot/main/page/v1?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};
//视频监测状态主体分页列表查询
export const videoMainPage = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/online/analysis/video/main/page/v1?current=" +
      data.current +
      "&size=" +
      data.size,
    data: data,
  });
};

//安全承诺详情列表
export const notPromiseEnterprises = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/promise/notPromiseEnterprises?nowPage=" +
      data.nowPage +
      "&pageSize=" +
      data.pageSize,
    data: data,
  });
};

export const queryCompanyRiskLevel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/companyRiskModel/queryCompanyRiskLevel/v1",
    data: data,
  });
};

// 依据企业画像获取企业等级评分
export const getCompanyRiskLevel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/enterprisePortraitScore/v1",
    data: data,
  });
}
// 依据企业画像等级点击获取详情
export const getCompanyRiskLevelDetail = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/enterprisePortraitScoreDetails/v1",
    data: data,
  });
}
// 获取工作台承诺风险等级分布
export const getCommitmentRiskLevel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/safetyPromiseAnalysis/safetyPromiseAnalysis/v1",
    data: data,
  });
}
