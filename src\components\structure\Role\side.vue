<template>
  <div class="side" v-loading="loading">
    <a-collapse
      :bordered="false"
      :expand-icon-position="expandIconPosition"
      :destroyInactivePanel="true"
      :accordion="true"
      @change="getMenuList"
      :defaultActiveKey="defaultActiveKey"
      :activeKey="activeKey"
    >
      <!-- <template #expandIcon="props">
        <a-icon type="right" :rotate="props.isActive ? 90 : 0" />
      </template> -->
      <a-collapse-panel
        v-for="item in systemAll"
        :key="item.systemCode + ',' + item.id"
        :header="item.systemName"
        class="customStyle"
      >
        <!-- <el-input
          style="margin-bottom: 8px"
          placeholder="请输入搜索内容"
          v-model="searchVal"
          @input="search"
        /> -->
        <a-directory-tree multiple @select="onSelect" :autoExpandParent="false">
          <a-tree-node
            :title="item_menuList.name"
            is-leaf
            v-for="item_menuList in item.children"
            :key="
              item_menuList.id +
              ',' +
              item_menuList.systemCode +
              ',' +
              item_menuList.name +
              ',' +
              item_menuList.orderNum
            "
          >
            <!-- <a-tree-node
              :title="item_menuList_children.name"
              v-for="item_menuList_children in item_menuList.children"
              :key="
                item_menuList_children.id + ',' + item_menuList_children.systemCode + ','+ item_menuList_children.name + ','+ item_menuList_children.orderNum"
            >
                <a-tree-node
              :title="item_menuList_childrens.name"
              is-leaf
              v-for="item_menuList_childrens in item_menuList_children.children"
              :key="
                item_menuList_childrens.id + ',' + item_menuList_childrens.systemCode + ','+ item_menuList_childrens.name + ','+ item_menuList_childrens.orderNum"
            />
            </a-tree-node> -->
          </a-tree-node>
        </a-directory-tree>

        <!-- <a-directory-tree
          multiple
          default-expand-all
          @select="onSelect"
          v-loading="loadingTree"
          :autoExpandParent="false"
          v-else
        >
          <a-tree-node
            :title="item_menuList.menuName"
            v-for="item_menuList in menuListBySysCodeBak"
            :key="item_menuList.menuId + ',' + item_menuList.systemCode + ','"
          >
            <a-tree-node
              :title="item_menuList_children.menuName"
              is-leaf
              v-for="item_menuList_children in item_menuList.children"
              :key="
                item_menuList_children.menuId +
                ',' +
                item_menuList_children.systemCode +
                ','+','+','
              "
            />
          </a-tree-node>
        </a-directory-tree> -->
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
import { getRoleGroupData } from "../../../api/role";
import { mapState, mapGetters, mapActions, mapMutations } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  name: "Side",
  components: {},
  data() {
    return {
      expandIconPosition: "right",
      systemAll: [],
      menuListBySysCode: [],
      loading: false,
      defaultActiveKey: "1",
      activeKey: "1",
      searchVal: "",
      searchData: [],
      // menuListBool: true,
      menuListBySysCodeBak: [],
    };
  },
  //方法集合
  methods: {
    onSelect(keys, event) {
      console.log(event);
      // console.log(keys[0].split(","));
      let systemCode = keys[0].split(",")[1];
      let menuId = keys[0].split(",")[0];
      let tabCon = true;
      let groupName = keys[0].split(",")[2];
      let orderNum = keys[0].split(",")[3];
      this.$store.commit("sa/updateSARoleListData", {
        menuId,
        systemCode,
        tabCon,
        groupName,
        orderNum,
      });
      Bus.$emit("SARoleListData", {
        menuId,
        systemCode,
        tabCon,
        groupName,
        orderNum,
      });
      // console.log({ menuId, systemCode,tabCon,groupName,orderNum});
      console.log(this.$store.state.sa);
    },
    // onExpand(keys, event){
    //   let systemCode = keys[0].split(",")[1];
    //   let menuId = keys[0].split(",")[0]
    //   let tabCon = true;
    //   let groupName = keys[0].split(",")[2];
    //   let orderNum = keys[0].split(",")[3];
    //   this.$store.commit("updateSARoleListData", { menuId, systemCode,tabCon,groupName,orderNum });
    //   Bus.$emit('SARoleListData', { menuId, systemCode,tabCon,groupName,orderNum})
    // },
    getList() {
      this.loading = true;
      getRoleGroupData().then((res) => {
        this.systemAll = res.data.data;
        this.activeKey = [
          res.data.data[0].systemCode + "," + res.data.data[0].id,
        ];
        this.getMenuList(
          res.data.data[0].systemCode + "," + res.data.data[0].id
        );
        this.loading = false;
      });
    },
    getMenuList(keys) {
      if (keys != undefined) {
        this.menuListBySysCode = [];
        // this.menuListBySysCodeBak = [];
        // this.loadingTree = true;
        let systemCode = keys.split(",")[0];
        let menuId = "";
        let parentId = "-1";
        let tabCon = false;
        // console.log(keys);
        // let systemCode = keys;
        this.$store.commit("sa/updateSARoleListData", {
          menuId,
          systemCode,
          parentId,
          tabCon,
        });
        Bus.$emit("SARoleListData", { menuId, systemCode, parentId, tabCon });
        // getRoleGroupData({ systemCode: systemCode })
        // .then((res) => {
        //     // this.searchData = res.data.data; //搜索用的
        //     this.dataTOTree(res.data.data, this.menuListBySysCode);

        //     this.loadingTree = false;
        // })
        // .catch((e) => {
        //     console.log(e, "请求错误");
        // });
      }
    },
    dataTOTree(data, val) {
      let map = {};
      data.forEach((item) => {
        map[item.groupId] = item;
      });
      data.forEach((item) => {
        let parent = map[item.parentId];
        if (parent) {
          (parent.children || (parent.children = [])).push(item);
        } else {
          val.push(item);
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getList();
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
.side {
  width: 280px;
  background-color: #fff;
  overflow: auto;
  height: 89vh;
  .customStyle {
    font-size: 16px;
    background-color: #fff;
  }
}
</style>

<style>
.ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
  min-height: 200px;
}
</style>
