"use strict";

process.env.NODE_ENV = "production";
const HappyPack = require("happypack");
const os = require("os");
const vueLoaderConfig = require("./vue-loader.conf");
const VueLoaderPlugin = require("vue-loader/lib/plugin");
const ora = require("ora");
const rm = require("rimraf");
const path = require("path");
const chalk = require("chalk");
const webpack = require("webpack");
const config = require("../config");
const happyThreadPool = HappyPack.ThreadPool({ size: os.cpus().length });
const spinner = ora("构建H5轻量版本...");
spinner.start();

function resolve(dir) {
  return path.join(__dirname, "..", dir);
}

// 独立的H5配置
const h5Config = {
  mode: "production",
  entry: {
    h5: "./src/h5-main.js", // 显式指定入口名称为 "h5"
  },

  output: {
    path: config.build.assetsRoot,
    filename: "h5.[chunkhash:8].js",
    publicPath: "/report-h5/",
  },

  resolve: {
    extensions: [".js", ".vue", ".json", ".ts"],
    alias: {
      vue$: "vue/dist/vue.esm.js",
      "@": resolve("src"),
    },
  },

  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: "vue-loader?chaheDirectory=true",
        options: {
          vueLoaderConfig,
          loaders: {
            css: "vue-style-loader!css-loader",
            scss: "vue-style-loader!css-loader!sass-loader",
            ts: [
              {
                loader: "ts-loader",
                options: {
                  appendTsSuffixTo: [/\.vue$/],
                },
              },
            ],
          },
          cacheDirectory: path.resolve(
            __dirname,
            "../node_modules/.cache/vue-loader"
          ),
          cacheIdentifier: "cache-loader:{version} {process.env.NODE_ENV}",
        },
        exclude: /node_modules/,
      },
      {
        test: /\.js$/,
        // 把对 .js 文件的处理转交给 id 为 babel 的 HappyPack 实例
        use: ["happypack/loader?id=js", "babel-loader?cacheDirectory=true"],
        exclude: /node_modules/,
      },
      {
        test: /\.ts$/,
        loader: "ts-loader",
        options: {
          appendTsSuffixTo: [/\.vue$/],
        },
        exclude: /node_modules/,
      },
      {
        test: /\.css$/,
        use: ["vue-style-loader", "css-loader"],
      },
      {
        test: /\.scss$/,
        use: ["vue-style-loader", "css-loader", "sass-loader"],
      },
      {
        test: /\.(png|jpe?g|gif|svg)(\?.*)?$/,
        loader: "url-loader",
        options: {
          limit: 10000,
          name: "static/img/[name].[hash:7].[ext]",
        },
      },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: "url-loader",
        options: {
          limit: 10000,
          name: "static/fonts/[name].[hash:7].[ext]",
        },
      },
    ],
  },

  plugins: [
    new HappyPack({
      // 用唯一的标识符id来代表当前的HappyPack 处理一类特定的文件
      id: "js",
      // 如何处理.js文件，用法和Loader配置是一样的
      loaders: [
        {
          loader: "babel-loader",
        },
      ],
      //共享进程池
      threadPool: happyThreadPool,
      //允许 HappyPack 输出日志
      verbose: true,
    }),
    new VueLoaderPlugin(), //vue-loader的伴生插件
    new (require("html-webpack-plugin"))({
      filename: "h5.html",
      template: "h5.html",
      inject: true,
      chunks: ["h5"], // 确保只包含 h5 chunk
    }),
    new webpack.DefinePlugin({
      "process.env.NODE_ENV": '"production"',
    }),
  ],
};

// 删除之前的H5构建文件
rm(path.join(config.build.assetsRoot, 'h5*'), err => {
  if (err) throw err;

  webpack(h5Config, (err, stats) => {
    spinner.stop();
  if (err) throw err;

  console.log(
    stats.toString({
      colors: true,
      modules: false,
      children: false,
      chunks: false,
      chunkModules: false,
    })
  );

  if (stats.hasErrors()) {
    console.log(chalk.red("构建失败"));
    process.exit(1);
  }

  console.log(chalk.cyan("H5构建完成！"));
  });
});
