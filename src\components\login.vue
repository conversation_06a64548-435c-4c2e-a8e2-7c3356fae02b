<template>
  <div class="login-bg">
    <div class="logo">
      <div class="login">
        <div>
          <a href="javascript:void()"></a>
        </div>
        <p class="loginTitle">用户登录</p>
        <div class="login-cont">
          <ul class="user">
            <li class="user-name">
              <i class="user-icon"></i
              ><input
                id="loginName"
                name="loginName"
                type="text"
                placeholder="请输入用户名"
                v-model="loginName"
              />
            </li>
            <li class="passinput">
              <i class="pass-icon"></i>

              <input
                id="password"
                name="password"
                :type="isPass == true ? 'password' : 'text'"
                placeholder="请输入密码"
                v-model="password"
              />

              <span
                class="passPic"
                :class="{ passPics: isPass == true }"
                @click="showPass"
              ></span>
            </li>
          </ul>
          <div
            :class="loginStatus ? 'login-btn' : 'login-btnActive'"
            @click="loginStatus ? saveLogin() : null"
          >
            <span v-if="loginStatus">登录</span>
            <a-icon v-else style="fontsize: 20px" type="loading" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import AES from "@/utils/AES.js";
import {
  login,
  districtByUser,
  adminUserPark,
  adminUserParkCode,
} from "../api/login";
import { getEnt } from "@/api/dailySafety";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  data() {
    return {
      isPass: true,
      loginName: "",
      password: "",
      loginStatus: true,
      entLevel: false,
    };
  },
  created() {},
  mounted() {
    //  this.$router.beforeEach( (to,from)=>{   //路由守卫。使用ES6的箭头函数做一些前处理。2个参数分别封装了目标页面、上一个页面的信息
    //   console.log(to,from);
    //  });
    this.keyUp();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      user: (state) => state.user,
      enterData: (state) => state.enterData,
    }),
  },
  beforeRouteUpdate(to, from, next) {
    // 如果有token 说明该用户已登陆
    // console.log(store.state.login.user.access_token);
    // debugger;
    if (this.$store.state.login.user.access_token) {
      // 在已登陆的情况下访问登陆页会重定向到首页
      if (to.path === "/login") {
        next({ path: "/" });
      } else {
        next({ path: to.path || "/" });
      }
    } else {
      // 没有登陆则访问任何页面都重定向到登陆页
      if (to.path === "/login") {
        next();
      } else {
        next(`/login?redirect=${to.path}`);
      }
    }
  },
  methods: {
    keyUp() {
      const This = this;
      document.onkeydown = function (e) {
        // console.log(e);
        //事件对象兼容
        let e1 =
          e || event || window.event || arguments.callee.caller.arguments[0];
        //键盘按键判断:左箭头-37;上箭头-38；右箭头-39;下箭头-40
        //左
        if (e1 && e1.keyCode == 13) {
          This.saveLogin();
        }
      };
    },
    showPass() {
      this.isPass = !this.isPass;
    },
    //获取监管用户账号所在地区的code
    getbyUser() {
      return new Promise((resolve, reject) => {
        districtByUser()
          .then((res) => {
            console.log(res.data.data);
            resolve(res.data.data[0]);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //获取园区用户账号所在地区的code
    getAdminUserPark() {
      return new Promise((resolve, reject) => {
        adminUserPark()
          .then((res) => {
            resolve(res.data.data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //获取园区用户账号所在地区的code
    getAdminUserParkCode() {
      return new Promise((resolve, reject) => {
        adminUserParkCode()
          .then((res) => {
            resolve(res.data.data.distCode);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getEntId() {
      return new Promise((resolve, reject) => {
        getEnt({})
          .then((res) => {
            if (res.data.code == 0) {
              resolve(res.data.data);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    saveLogin() {
      const This = this;
      this.loginStatus = false;
      if (this.loginName == "" || this.password == "") {
        this.$message({
          type: "info",
          message: "用户名或密码不能为空！",
        });
        return;
      }
      login({
        flag: "111",
        loginName: this.loginName,
        password: this.password,
      })
        .then(async (res) => {
          this.loginStatus = true;
          //保存用户信息
          this.$store.commit("login/updataIsShowDist", true);
          this.$store.commit("login/updataUser", {
            ...res.data.data,
          });
          //获取用户账号所在地区的code
          // if (res.data.user_type === "sup") {
          if (res.data.data.userType === "sup") {
            //超级管理员
            this.$store.commit("login/updataPark", {});
            this.$store.commit("login/updataEnter", null);
            this.$router.push({ name: "user" });
            // } else if (res.data.user_type === "gov") {
          } else if (res.data.data.userType === "gov") {
            //监管
            console.log(123);
            let userCode = await this.getbyUser();
            console.log(userCode);
            this.$store.commit("login/updataUserDistCode", userCode.distCode);
            this.$store.commit(
              "login/updataIsXiaZuan",
              userCode.distCode == "420000" ? true : false
            );
            this.$store.commit("login/updataPark", {});
            this.$store.commit("login/updataEnter", null);

            This.$router.push({ name: "entManagement" });
            this.$router.push({ path: `/workbench/superviseWorkbench` });
            // } else if (res.data.user_type === "ent") {
          } else if (res.data.data.userType === "ent") {
            //企业
            this.$store.commit("login/updataPark", {});
            let getEntId = await this.getEntId();
            this.$store.commit("login/updataEnter", res.data.data);
            this.$store.commit(
              "login/updataUserDistCode",
              getEntId.districtCode
            );
            //如果不是重大危险源企业只保留企业管理
            this.entLevel =
              getEntId.level == null || getEntId.level == "" ? false : true;
            // console.log(this.menu);
            if (getEntId.level == null || getEntId.level == "") {
              this.$router.push({ name: "entManagement" });
            } else {
              this.$router.push({ path: `/workbench/enterWorkbench` });
            }
            // } else if (res.data.user_type === "park") {
          } else if (res.data.data.userType === "park") {
            let userPark = await this.getAdminUserPark();
            let userparkCode = await this.getAdminUserParkCode();
            this.$store.commit("login/updataPark", userPark);
            this.$store.commit("login/updataIsShowDist", false);
            // this.$store.commit("login/updataUserDistCode", null);
            this.$store.commit("login/updataUserDistCode", userparkCode);
            this.$store.commit("login/updataEnter", null);
            //园区
            this.$router.push({ name: "entManagement" });
          }
        })
        .catch((e) => {
          this.loginStatus = true;
          // console.log(e, "请求错误");
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.login-bg {
  // position: fixed;
  // top: 0;
  // left: 0;
  width: 100vw;
  height: 100vh;
  // min-width: 1000px;
  // z-index:-10;
  // zoom: 1;
  background: url("/static/img/assets/img/login-bg.png") no-repeat;
  background-size: cover;
  -webkit-background-size: 100% 100%;
  -o-background-size: 100% 100%;
  background-position: center 0;

  .logo {
    width: 100%;
    height: 100%;
    position: relative;
    .login {
      width: 336px;
      height: 384px;
      background: url("/static/img/assets/img/login-boxbg.png") no-repeat;
      -webkit-background-size: 100% 100%;
      background-size: 100% 100%;
      opacity: 0.8;
      position: absolute;
      right: 10%;
      top: calc(50% - 192px);
      z-index: 999;
      .loginTitle {
        text-align: center;
        font-size: 2rem;
        font-weight: bold;
        color: #ffffff;
        position: absolute;
        top: 70px;
        // background: url("/static/img/assets/img/loginTitle.png") no-repeat;
        background-size: 100% 100%;
        left: 50%;
        transform: translate(-50%);
      }
      .login-cont {
        .user {
          position: relative;
          top: 140px;
          li {
            height: 45px;
            width: 260px;
            padding: 1px 1px 1px 49px;
            border-radius: 5px;
            margin: 0 auto;
            list-style-type: none;
            position: relative;
            border: 1px rgb(65, 145, 211) solid;
            background-color: rgb(38, 67, 142);
            margin-bottom: 14px;
            i {
              display: inline-block;
              width: 16px;
              height: 17px;
              position: absolute;
              left: 14px;
              top: 12px;
            }
            .user-icon {
              background: url("/static/img/assets/img/uesr-icon.png") no-repeat;
              background-size: cover;
            }
            .pass-icon {
              background: url("/static/img/assets/img/pass-icon.png") no-repeat;
              background-size: cover;
            }
            input[type="text"] {
              float: left;
              width: 94%;
              height: 56%;
              background: transparent;
              color: #fff;
              text-indent: 0.3rem;
              border: none;
            }
            input[type="text"]:focus {
              border: none;
            }
            input[type="text"]::-webkit-input-placeholder {
              color: #ccc;
            }
            #password {
              width: 83%;
              height: 56%;
              text-indent: 0.3rem;
              border: none;
              background: transparent;
              color: #fff;
            }
            #password:focus {
              border: none;
            }
            #password::-webkit-input-placeholder {
              color: #ccc;
            }
          }
          .user-name {
            // background: url("/static/img/assets/img/userInput.png") no-repeat;
            // background-size: 100% 100%;
            display: flex;
            align-items: center;
          }
          .passinput {
            // background: url("/static/img/assets/img/poseeInput.png") no-repeat;
            // background-size: 100% 100%;
            display: flex;
            align-items: center;
            .passPic {
              width: 20px;
              height: 14px;
              background: url("/static/img/assets/img/passHied.png") no-repeat;
              background-size: 100%;
              display: inline-block;
              cursor: pointer;
              margin-left: 7px;
            }
            .passPics {
              width: 20px;
              height: 14px;
              background: url("/static/img/assets/img/passShow.png") no-repeat;
              background-size: 100%;
              display: inline-block;
              cursor: pointer;
              margin-left: 7px;
            }
          }
        }
        .login-btn {
          margin-bottom: 0px;
          margin-top: 0.25rem;
          background: url("/static/img/assets/img/login-input.png") no-repeat;
          background-size: 100% 100%;
          position: relative;
          top: 174px;
          left: 43px;
          cursor: pointer;
          width: 76%;
          height: 45px;
          display: flex;
          justify-content: center;
          align-items: center;
          > * {
            // display: inline-block;
            // width: 100%;
            // text-align: center;
            // line-height: 45px;
            color: #fff;
          }
        }
        .login-btnActive {
          margin-bottom: 0px;
          margin-top: 0.25rem;
          width: 76%;
          height: 45px;
          background: url("/static/img/assets/img/login-inputHover.png")
            no-repeat;
          background-size: 100% 100%;
          position: relative;
          top: 174px;
          left: 43px;
          display: flex;
          justify-content: center;
          align-items: center;
          > * {
            // display: inline-block;
            // width: 100%;
            // text-align: center;
            // line-height: 45px;
            color: #fff;
          }
        }
        .login-btn:hover {
          width: 76%;
          height: 45px;
          background: url("/static/img/assets/img/login-inputHover.png")
            no-repeat;
          background-size: 100% 100%;
        }
      }
    }
    .logo-tp-bg {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 5.62rem;
      background-size: cover;
    }
    .logo-bt-bg {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: calc(100% - 5.62rem);
      background-size: cover;
      .bt-explain {
        width: 750px;
        height: 172px;
        background: url("/static/img/assets/img/qh-bg-title2.png") no-repeat 0 0;
        background-size: 100% 100%;
        position: absolute;
        z-index: 999;
        top: 17%;
        left: 5%;
      }
    }
  }
}
</style>
