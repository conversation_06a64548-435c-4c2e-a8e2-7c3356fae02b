<template>
  <div class="enterpriseList">
    <div class="header">
      <div class="title">预案列表</div>
      <div class="operation">
        <div class="inputBox">
          <el-input v-model.trim="keyWord"
                    size="small"
                    placeholder="请输入预案名称"
                    class="input"
                    clearable></el-input>
          <el-select v-model="planLevel"
                     size="small"
                     placeholder="请选择预案级别"
                     :clearable="true">
            <el-option v-for="item in planLevelData"
                       :key="item.value"
                       :label="item.name"
                       :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-select
            v-model="eventTypeCode"
            size="mini"
            placeholder="请选择事件类型"
            :clearable="true"
          >
            <el-option
              v-for="item in eventTypeCodeData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <el-cascader placeholder="请选择事件类型"
                        v-model="eventTypeCode"
                         size="small"
                         clearable=""
                       :options="eventTypeCodeData"
                       @change="handleChangeEventTypeCode">
          </el-cascader>
          <!-- <el-select
            v-model="establishOrgCode"
            size="mini"
            placeholder="请选择编制单位"
            :clearable="true"
          >
            <el-option
              v-for="item in entType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->

<!--           
          <el-cascader placeholder="请选择编制单位s's" v-if="roleInfo.user_type !== 'ent'"
                       v-model="establishOrgCode"
                       size="small"
                       :options="establishOrgCodeData"
                       @change="handleChange">
          </el-cascader>           
            <el-cascader v-if="roleInfo.user_type !== 'ent'" placeholder="请选择编制单位" style="width:400px"
            clearable
            size="small"
            @clear='clearEstablishOrgCode'
                    v-model="establishOrgCode"
                    :options="establishOrgCodeData"
                    @change="handleChangePublishOrgCode" 
                    :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                  </el-cascader> -->           
            <el-cascader v-if="roleInfo.user_type !== 'ent'" placeholder="请选择编制单位" style="width:400px"
            clearable
            size="small"
            @clear='clearEstablishOrgCode'
                    v-model="establishOrgCode"
                    :options="establishOrgCodeData"
                    @change="handleChangePublishOrgCode" 
                    :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                  </el-cascader>






          <el-button type="primary"
                     size="small"
                     @click="searches()">查询</el-button>
         <el-button type="reset"

                    style="margin-left:-10px"
                     size="small"
                     @click="resetList">重置</el-button>
          <div class="gorupradio">
            <el-radio-group v-model="radio1"
                            @change="changeshow"
                            size="small">
              <el-radio-button label="0">
                <i class="el-icon-notebook-1"></i>
              </el-radio-button>
              <el-radio-button label="1">
                <i class="el-icon-s-operation"></i>
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <el-button type="primary"
                   size="small"
                  
                   @click="addEdit">新增</el-button>
      </div>
    </div>
    <div class="contentFelx">
      <div class="left">
        <div class="search_tree">
          <div class="search_slide_input">
            <div class="search_slide_searchKey">
              <el-input v-model.trim="filterText"
                        placeholder="请输入预案类型"
                        clearable
                        maxlength="10"
                        show-word-limit>
              </el-input>
            </div>
            <div class="search_slide">
              <el-scrollbar id="mainheight">
                <div class="headertitle">
                  <slot name="header"></slot>
                </div>
                <div class="navtitle">
                  <slot name="nav"></slot>
                </div>
                <el-tree class="filter-tree"
                         :data="data"
                         :highlight-current="true"
                         :filter-node-method="filterNode"
                         @node-click="handleNodeClick"
                         node-key="id"
                         ref="tree">
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="table"
             v-loading="loading"
             v-if="changeContent == 1">
          <el-table :data="tableData"
                    :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                    border
                    style="width: 100%"
                    ref="multipleTable"
                    :highlight-current-row="true">
            <el-table-column type="selection"
                             width="55"
                             fixed="left"
                             align="center">
            </el-table-column>
            <el-table-column type="index"
                             label="序号"
                             width="55"
                             align="center">
            </el-table-column>
            <el-table-column prop="planName"
                             label="预案名称"
                             align="center"
                             :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="planLevelName"
                             label="预案级别"
                             align="center"
                             :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="planTypeCodeName"
                             label="预案类型"
                             align="center"
                             :show-overflow-tooltip="true">
            </el-table-column>
            <el-table-column prop="establishOrgName"
                             label="编制单位"
                             align="center">
            </el-table-column>
            <el-table-column prop="publishTime"
                             label="发布时间"
                             align="center"
                             width="150">
            </el-table-column>
            <el-table-column prop="address"
                             label="操作"
                             align="center"
                             width="240">
              <template slot-scope="scope">
                <span @click="view(scope.row.planId)"
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">查看</span>
                <span @click="operation(scope.row.planId,scope.row.sourceDeptFlag)"
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">工作手册</span>
                <span @click="digital(scope.row.planId)"
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">数字化</span>
                <span v-if="roleInfo.user_type == 'gov'">
                     <span @click="edit(scope.row.planId)"  v-if='scope.row.sourceDeptFlag!=1'
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">编辑</span>
                </span>
                <span v-else  @click="edit(scope.row.planId)" 
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">编辑</span>



                  <span v-if="roleInfo.user_type == 'gov'">
                     <span @click="deleter(scope.row.planId)" v-if='scope.row.sourceDeptFlag!=1'
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">删除</span>
                  </span>               
                <span @click="deleter(scope.row.planId)" v-else
                      style="
                    color: rgb(57, 119, 234);
                    margin-right: 10px;
                    cursor: pointer;
                  ">删除</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-if="changeContent == 2 && bookData.length"
             class="dictionary">
          <div class="bookDatBox">
            <el-scrollbar>
              <div v-for="(item, index) in bookData"
                   :key="index"
                   class="bookDataItem">
                <el-row class="bookshelf">
                  <el-col :span="5"
                          v-for="team in item"
                          :key="team.planId"
                          class="book">
                    <!-- <div :class="'_'+team.eventType"> -->
                    <div class="book_item_0"
                         :class="'_' + team.eventType">
                      <!-- :style="'background:'+bgstyle[team.eventType]+';backgroundSize:100%'" -->
                      <div class="book_more">
                        <div class="book_ico">
                          <i class="el-icon-view"
                             @click="view(team.planId)"
                             title="详情"></i>
                          <i class="el-icon-s-data"
                             @click="digital(team.planId)"
                             title="数字化"></i>

                          <span v-if="roleInfo.user_type == 'gov'" style="color:#fff;cursor: pointer;">
                          <i class="el-icon-edit"   v-if='team.sourceDeptFlag!=1'
                             @click="edit(team.planId)"
                             title="编辑"></i></span>
                          
                          <span v-else style="color:#fff;cursor: pointer;">
                          <i class="el-icon-edit"  
                             @click="edit(team.planId)"
                             title="编辑"></i></span>



                          <span v-if="roleInfo.user_type == 'gov'" style="color:#fff;cursor: pointer;">
                             <i class="el-icon-delete"   v-if='team.sourceDeptFlag!=1'
                             @click="deleter(team.planId)"
                             title="删除"></i>
                          </span>
                           <span v-else style="color:#fff;cursor: pointer;">
                             <i class="el-icon-delete"   
                             @click="deleter(team.planId)"
                             title="删除"></i>
                          </span>


                          <i class="el-icon-s-order"
                             @click="operation(team.planId,team.sourceDeptFlag)"
                             title="工作手册"></i>
                        </div>
                      </div>
                      <div class="book_top">
                        <p>{{ team.planName }}</p>
                      </div>

                      <div class="book_bottom">
                        <span>{{ team.publishOrgName }}</span>
                        <span>{{ team.publishTime }} 发布</span>
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <el-row class="line"></el-row>
              </div>
            </el-scrollbar>
          </div>
        </div>

<div class="pagination">
          <el-pagination @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="size"
            layout="total, prev, pager, next"
            background
            :total="total">
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- 新增编辑查看 -->
    <el-dialog :title="title"
               :visible.sync="open"
               width="1100px"
               :before-close="close"
               :close-on-click-modal="false"
               :append-to-body="true">
      <el-form ref="form"
               :model="form"
               :rules="rules"
               :disabled="isDisabled"
               label-width="150px"
               :hide-required-asterisk="isDisabled">
        <div class="form_item"
             style="max-height: 50vh;overflow: auto;overflow-x: hidden;">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="planName"
                              label="预案名称">
                  <el-input v-model.trim="form.planName"
                            maxlength="20"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="planVersion"
                              label="预案文号">
                  <el-input v-model.trim="form.planVersion"
                            maxlength="30"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="planTypeCode"
                              label="预案类型">
                  <el-cascader placeholder="请选择预案类型"
                               v-model="form.planTypeCode"
                               :options="datas"
                               @change="handleChangePlanTypeSuperCode">
                  </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="planLevel"
                              label="预案级别">
                  <el-select v-model="form.planLevel">
                    <el-option v-for="options in planLevelData"
                               :label="options.name"
                               :value="options.value"
                               :key="options.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="establishOrgCode" label="编制单位">
                  <el-input v-model.trim="form.establishOrgName" v-if="roleInfo.user_type === 'ent'" disabled></el-input>
                  <!-- <el-cascader v-else placeholder="请选择编制单位"
                    v-model="form.establishOrgCode"
                    :options="establishOrgCodeData"
                    @change="handleChangeEstablishOrgCode">
                  </el-cascader> -->
                  <el-cascader v-else placeholder="请选择编制单位"
                    v-model="form.establishOrgCode"
                    :options="establishOrgCodeData"
                    @change="handleChangeEstablishOrgCode" 
                    :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                  </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="publishOrgCode" label="发布单位">
                  <el-input v-model.trim="form.publishOrgName" v-if="roleInfo.user_type === 'ent'" disabled></el-input>
                  <!-- <el-cascader v-else placeholder="请选择发布单位"
                    v-model="form.publishOrgCode"
                    :options="establishOrgCodeData"
                    @change="handleChangePublishOrgCode">
                  </el-cascader> -->
                  <el-cascader v-else placeholder="请选择发布单位"
                    v-model="form.publishOrgCode"
                    :options="establishOrgCodeData"
                    @change="handleChangePublishOrgCode" 
                    :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                  </el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="eventTypeCode" label="事件类型:">
                  <el-cascader placeholder="请选择事件类型"
                    v-model="form.eventTypeCode"
                    :options="eventTypeCodeData"
                    @change="handleChangeEventTypeCodes">
                  </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="classCode"
                              label="预案密级:">
                  <el-select v-model="form.classCode">
                    <el-option v-for="options in classCodeList"
                               :label="options.name"
                               :value="options.value"
                               :key="options.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="contactName"
                              label="联系人:">
                  <el-input v-model.trim="form.contactName"
                            maxlength="16"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="contactPhone"
                              label="联系人移动电话:">
                  <el-input v-model.trim="form.contactPhone"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="publishTime"
                              label="发布日期:">
                  <el-date-picker type="date"
                                  v-model="form.publishTime"
                                  :editable="false"
                                  style="width: 100%"
                                  placeholder="请选择日期"
                                  format="yyyy-MM-dd"
                                  value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="endTime"
                              label="下次修订日期:">
                  <el-date-picker type="date"
                                  v-model="form.endTime"
                                  :editable="false"
                                  style="width: 100%"
                                  placeholder="请选择日期"
                                  format="yyyy-MM-dd"
                                  value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="涉及危化品:">
                  <el-autocomplete popper-class="my-autocomplete"
                    v-model="form.msdsName"
                    :fetch-suggestions="querySearch"
                    placeholder="请选择/输入涉及危化品"
                    clearable
                    @clear="clearSensororgCode()"
                    @select="handleSelect"
                    style="width: 150px">
                    <template slot-scope="{ item }">
                      <div class="name">{{ item.msdstitle }}</div>
                    </template>
                  </el-autocomplete>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item prop="attachmentList" label="附件:">
                  <AttachmentUpload :attachmentlist="form.attachmentList"
                    :limit="1"
                    type="office"
                    v-bind="{}"
                    :editabled="isDisabled"></AttachmentUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="!isDisabled"
           slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <DetailPlan ref="detailPlan" :eventTypeCodeData="eventTypeCodeData"></DetailPlan>
    <EditPlan ref="editPlan" :rules="rules" :establishOrgCodeData="establishOrgCodeData" :eventTypeCodeData="eventTypeCodeData" :planTypeData="datas" @updatePlan="updatePlan" :roleInfo="roleInfo"></EditPlan>
    <Manual ref="manual"></Manual>
    <Digitization ref="digitization"
                  :planId="planId"
                  :edVisible='edVisible'
                  ></Digitization>
  </div>
</template>

<script>
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import {
  getPlanType,
  getPlanList,
  getEstablishOrgCodeTreeData,
  getEventTypeCodData,
  getExpertTypeData,
  addPlanData,
  editPlanData,
  deletePlanData,
  getOrgTree
} from '@/api/mergencyResources'
import { getDangerIdIsNotNullListData } from '@/api/equipmentAndFacilities'
import DetailPlan from './emergencyPlan/detailPlan'
import EditPlan from './emergencyPlan/editPlan'
import Manual from './emergencyPlan/manual'
import Digitization from './emergencyPlan/digitization'
import iamsCombobox from '@/components/common/packages/iamsCombobox'
// import FileUpload from '@/components/common/packages/FileUpload';
import { createNamespacedHelpers } from 'vuex'
import { size } from 'lodash-es'
// const { mapState: mapStateLogin } = createNamespacedHelpers("login");
// const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  name: 'emergencyPlan',
  components: {
    DetailPlan,
    EditPlan,
    Manual,
    Digitization,
    AttachmentUpload,
    iamsCombobox
  },
  data() {
    var checkPhone = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/
      if (!value) {
        return callback(new Error('联系人移动电话不能为空'))
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error('联系人移动电话格式不正确'))
        } else {
          if (phoneReg.test(value)) {
            callback()
          } else {
            callback(new Error('联系人移动电话格式不正确'))
          }
        }
      }, 100)
    }
    return {
      edVisible:true,
      keyWord: '',
      planLevel: '',
      eventTypeCode: '',
      establishOrgCode: '',

      establishOrgCodeData: [],
      filterText: '',
      planLevelData: [],
      eventTypeCodeData: [],
      classCodeList: [],
      planId: '',

      currentPage: 1,
      enterpId: '',
      title: '新增预案信息',
      open: false,
      radio1: '0',
      changeContent: 2,
      form: {
        attachmentList: [],
        checkStatus: '', //备案状态，建字典项（2不通过0未审核1审核通过）
        classCode: '', //预案密级，建字典项（CONFIDENTIAL、SECRET、LIMIT、OPEN、OTHE）
        comments: '', //备注
        contactName: '', //联系人
        contactPhone: '', //联系人电话
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        dutiesMeasures: '', //职责与措施
        endTime: '', //($date-time)适用结束时间
        establishOrgCode: '', //编制单位编码
        establishOrgName: '', //编制单位名称
        eventTypeCodeList: [], //事件类型
        eventTypeCode: '', //事件类型
        eventTypeCodeListNew:[],
        notes: '', //编制或修订说明 （0,1）
        planCategory: '', //预案种类，预案大类GENERAL，SPECIAL，DEPARTMENT，COM
        planFlag: '1', //预案标志（0：备案，1：预案）
        planId: '', //预案id
        planLevel: '', //预案级别，COUNTRY、PROVINCE、CITY、ARER、INDUSTRY、COM
        planName: '', //预案名称
        planResponseValue: '', //可选响应级别(1、2、3、4级)，给默认值4级
        planSubCategory: '', //预案子类，GENERAL，SPECIAL，DEPARTMENT，COM
        planTypeCode: [], //预案类型
        planTypeSuperCode: '', //父节点预案类型
        planVersion: '', //预案版本
        publishOrgCode: '', //发布单位编码
        publishOrgName: '', //发布单位名称
        publishTime: '', //($date-time)发布时间
        reportOrgCode: '', //上报单位编码
        reportOrgName: '', //上报单位名称
        startTime: '', //($date-time)适用开始时间
        updateBy: '', //更新人
        updateTime: '', //($date-time)更新时间
        versionFlag: '', //发布还是修订(0发布,1修订)
        planStatus: '', //备案状态
        msdsId: '',
        msdsName: ''
      },
      isDisabled: false,
      rules: {
        planName: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'blur'
          }
        ],
        planTypeCode: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'change'
          }
        ],
        planLevel: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'change'
          }
        ],
        establishOrgCode: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'change'
          }
        ],
        publishOrgCode: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'change'
          }
        ],
        eventTypeCode: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'change'
          }
        ],
        contactName: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'blur'
          }
        ],
        contactPhone: [
          {
            required: true,
            validator: checkPhone,
            trigger: 'blur'
          }
        ],
        publishTime: [
          {
            required: true,
            message: '该字段不能为空',
            trigger: 'blur'
          }
        ]
      },
      bookData: [],
      tableData: [],
      entType: [
        {
          label: '生产',
          value: '01'
        },
        {
          label: '经营',
          value: '02'
        },
        {
          label: '使用',
          value: '03'
        },
        {
          label: '第一类非药品类易制毒',
          value: '04'
        }
      ],
      data: [],
      datas: [],
      loading: false,
      selection: [],
      total: 0,
      size: 10,
      enterpName: '',
      entTypeVal: '',
      roleInfo: {}
    }
  },
  //方法集合
  methods: {
   
    resetList(){
      this.eventTypeCode = '';
      this.keyWord = '';
      this.planLevel = '';
      this.establishOrgCode = [];
      this.planTypeCode = '';
      this.$refs.tree.setCurrentKey(null);
      this.currentPage = 1;
      this.getList();
    },
    close(){
      this.open = false;
      this.$refs['form'].resetFields()
      this.$refs['form'].clearValidate();
    },
    clearEstablishOrgCode(){
      this.establishOrgCode=[]
    },
    getPlanTypeList() {
      //预案类型
      getPlanType({}).then(res => {
        if (res.data.status == 200) {
          this.data = res.data.data.treeData
        }
        this.datas = this.data
        this.datas.forEach(item => {
          item.value = item.id
          if (item.children && item.children.length > 0) {
            item.children.forEach(items => {
              items.value = items.id
            })
          }
        })
      })
      //编制单位
      getOrgTree({}).then(res => {
        if (res.data.status == 200) {
          
          res.data.data.forEach((item,i)=>{
            if(item.orgName=='政务侧'){
              this.establishOrgCodeData=res.data.data.splice(i,1);
              //  this.establishOrgCodeData = res.data.data
          this.establishOrgCodeData.forEach(item => {
            item.value = item.id
            if (item.children && item.children.length > 0) {
              item.children.forEach(items => {
                items.value = items.id
                if (items.children && items.children.length > 0) {
                  items.children.forEach(itemed => {
                    itemed.value = itemed.id
                  })
                }
              })
            }
          })
        
            }
          })
         
        }
      })
      //预案级别
      getExpertTypeData({ dicCode: 'PLAN_LEVEL' }).then(res => {
        if (res.data.status == 200) {
          this.planLevelData = res.data.data
        }
      })
      //预案密级
      getExpertTypeData({ dicCode: 'PLAN_CLASS_CODE' }).then(res => {
        if (res.data.status == 200) {
          this.classCodeList = res.data.data
        }
      })
      //事件类型
      getEventTypeCodData().then(res => {
        if (res.data.status == 200) {
          this.eventTypeCodeData = res.data.data.treeData
          this.eventTypeCodeData.forEach(item => {
            item.value = item.id
            if (item.children && item.children.length > 0) {
              item.children.forEach(items => {
                items.value = items.id
                if (items.children && items.children.length > 0) {
                  items.children.forEach(itemed => {
                    itemed.value = itemed.id
                  })
                }
              })
            }
          })
        }
      })
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || '', cb)
    },
    getSeachData(keyWord, cb) {
      getDangerIdIsNotNullListData({ msdstitle: keyWord })
        .then(res => {
          if (res.data.status == 200) {
            if (res.data.data.list.length > 0) {
              cb(res.data.data.list)
            } else {
              cb([])
            }
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    //选择化学品
    handleSelect(value) {
      this.form.msdsId = value.msdsid
      this.form.msdsName = value.msdstitle
    },
    clearSensororgCode(item) {
      this.form.msdsId = ''
      this.form.msdsName = ''
    },
    handleChange(value) {    
      if (value.length > 0) {
        this.establishOrgCode = value[value.length - 1]
      } else {
        this.establishOrgCode = ''
      }
    },
    handleChangeEventTypeCode(value) {
      if (value.length > 0) {
        this.eventTypeCode = value[value.length - 1]
      } else {
        this.eventTypeCode = ''
      }
    },
    handleChangeEventTypeCodes(value) {
      console.log(value,'value')
      if (value.length > 0) {
        this.form.eventTypeCode = value;
      } else {
        this.form.eventTypeCode = ''
      }
    },
    handleChangePlanTypeSuperCode(value) {
      if (value.length > 0) {
        this.form.planTypeCode = value[value.length - 1]
      } else {
        this.form.planTypeCode = ''
      }
    },
    handleChangeEstablishOrgCode(value) {
      if (value.length > 0) {
        this.form.establishOrgCode = value[value.length - 1]
      } else {
        this.form.establishOrgCode = ''
      }
    },
    handleChangePublishOrgCode(value) {
      
      if (value.length > 0) {
        this.form.publishOrgCode = value[value.length - 1]
      } else {
        this.form.publishOrgCode = ''
      }
    },
    handleNodeClick(data) {
      // console.log(data);
      this.planTypeCode = data.id
      this.getList()
    },
    updatePlan() { // 编辑更新
      this.getList();
    },
    getList() {
      this.loading = true
      var splitCode=this.establishOrgCode?((this.establishOrgCode).slice(-1)).toString():''
      getPlanList({
        establishOrgCode: splitCode, //编制单位编码
        establishOrgName: this.establishOrgName, //编制单位名称
        eventTypeCode: this.eventTypeCode, //事件类型
        eventTypeName: this.eventTypeName, //事件类型名称
        keyWord: this.keyWord, //关键字
        //  listOrder: {
        //     prop: '',
        //     sort: '',
        // },
        nowPage: this.currentPage,
        planFlag: 1,
        pageSize: this.size,
        publishOrgCode: '', //发布单位
        planTypeCode: this.planTypeCode, //预案类型
        planLevel: this.planLevel //预案级别
      }).then(res => {
        if (res.data.status == 200) {
          this.loading = false
          this.bookData = res.data.data.list
          this.tableData = res.data.data.list
          this.bookData = this.splitArr(this.bookData, 5)
          this.total = res.data.data.total
        }
      })
    },
    searches() {
      this.currentPage = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    },
    changeshow(val) {
      // val == '0' ? this.changeMain() : this.changeList();
      if (val == '0') {
        // this.bookData = this.splitArr(this.bookData, 4);
        this.changeContent = 2
      } else if (val == '1') {
        // this.bookData = this.splitArr(this.bookData, 4);
        this.changeContent = 1
      }
    },
    splitArr(data, senArrLen) {
      let data_len = data.length
      let arrOuter_len =
        data_len % senArrLen === 0
          ? data_len / senArrLen
          : Math.ceil(data_len / senArrLen)
      let arrSec_len = data_len > senArrLen ? senArrLen : data_len
      let arrOuter = new Array(arrOuter_len)
      let arrOuter_index = 0
      for (let i = 0; i < data_len; i++) {
        if (i % senArrLen === 0) {
          arrOuter_index++
          let len = arrSec_len * arrOuter_index
          arrOuter[arrOuter_index - 1] = new Array(data_len % senArrLen)
          if (arrOuter_index === arrOuter_len)
            data_len % senArrLen === 0
              ? (len = data_len % senArrLen + senArrLen * arrOuter_index)
              : (len = data_len % senArrLen + senArrLen * (arrOuter_index - 1))
          let arrSec_index = 0
          for (let k = i; k < len; k++) {
            arrOuter[arrOuter_index - 1][arrSec_index] = data[k]
            arrSec_index++
          }
        }
      }
      return arrOuter
    },
    addEdit() {
      this.open = true
      // this.roleInfo = JSON.parse(sessionStorage.getItem('VueX_local')).root.login.user || {}
      // if (this.roleInfo.user_type === 'ent') { // 企业用户编制单位、发布单位为自己单位
      //   this.form.establishOrgCode = this.roleInfo.org_code;
      //   this.form.publishOrgCode = this.roleInfo.org_code;
      //   this.form.establishOrgName = this.roleInfo.org_name;
      //   this.form.publishOrgName = this.roleInfo.org_name;
      // }
    },
    cancel() {
      this.open = false;
      this.reset()
      // this.$refs['form'].resetFields()
    },
    reset() {
      this.form = {
        attachmentList: [],
        checkStatus: '', //备案状态，建字典项（2不通过0未审核1审核通过）
        classCode: '', //预案密级，建字典项（CONFIDENTIAL、SECRET、LIMIT、OPEN、OTHE）
        comments: '', //备注
        contactName: '', //联系人
        contactPhone: '', //联系人电话
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        dutiesMeasures: '', //职责与措施
        endTime: '', //($date-time)适用结束时间
        establishOrgCode: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_code : '', //编制单位编码
        establishOrgName: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_name : '', //编制单位名称
        eventTypeCodeList: [], //事件类型
        eventTypeCodeListNew:[],
        eventTypeCode: '', //事件类型
        notes: '', //编制或修订说明 （0,1）
        planCategory: '', //预案种类，预案大类GENERAL，SPECIAL，DEPARTMENT，COM
        planFlag: '1', //预案标志（0：备案，1：预案）
        planId: '', //预案id
        planLevel: '', //预案级别，COUNTRY、PROVINCE、CITY、ARER、INDUSTRY、COM
        planName: '', //预案名称
        planResponseValue: '', //可选响应级别(1、2、3、4级)，给默认值4级
        planSubCategory: '', //预案子类，GENERAL，SPECIAL，DEPARTMENT，COM
        planTypeCode: [], //预案类型
        planTypeSuperCode: '', //父节点预案类型
        planVersion: '', //预案版本
        publishOrgCode: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_code : '', //发布单位编码
        publishOrgName: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_name : '', //发布单位名称
        publishTime: '', //($date-time)发布时间
        reportOrgCode: '', //上报单位编码
        reportOrgName: '', //上报单位名称
        startTime: '', //($date-time)适用开始时间
        updateBy: '', //更新人
        updateTime: '', //($date-time)更新时间
        versionFlag: '', //发布还是修订(0发布,1修订)
        planStatus: '', //备案状态
        msdsId: '',
        msdsName: ''
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          addPlanData({
            ...this.form,
            eventTypeCode:this.form.eventTypeCode[this.form.eventTypeCode.length-1],
           eventTypeCodeList:this.form.eventTypeCode,
          }).then(response => {
            if (response.data.status == 200) {
              this.open = false
              this.$message({
                type: 'success',
                message: '新增成功'
              })
              this.reset()
              this.getList()
            }
          })
        } else {
          return false
        }
      })
    },
    view(id) {
      this.$refs.detailPlan.closeBoolean(true)
      this.$refs.detailPlan.getPlanType()
      //  this.$refs.detailPlan.getPlanLevel();
      //  this.$refs.detailPlan.getclassCode();
      this.$refs.detailPlan.stepList(id)
      this.$refs.detailPlan.planGetById(id)
    },
    edit(id) {
      this.$refs.editPlan.getPlanId(id);
      this.$refs.editPlan.closeBoolean(true)
      this.$refs.editPlan.getPlanTypeList()
      //  this.$refs.editPlan.getPlanLevel();
      //  this.$refs.editPlan.getclassCode();
      this.$refs.editPlan.stepList(id)
      this.$refs.editPlan.planGetById(id)
      this.$refs.editPlan.getEmergencyTree()
      this.$refs.editPlan.getOrgCode()
    },
    digital(id) {
      this.planId = id;
      this.edVisible = false;
      this.$refs.digitization.changecheckPage(1, id)
      this.$refs.digitization.closeBoolean(true)
    },
    operation(id,sourceDeptFlag) {
      this.$refs.manual.closeBoolean(true)
      this.$refs.manual.getList(id)
      this.$refs.manual.getDetaildata(id,sourceDeptFlag)
    },
    deleter(id) {
      this.$confirm('是否确认删除该预案信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletePlanData({
            planId: id
          }).then(res => {
            if (res.data.status == 200) {
              this.$message.success('删除成功')
               if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getList()
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    }
  },
  mounted() {   
    // debugger
    // console.log(this.enterData)
    this.roleInfo = JSON.parse(sessionStorage.getItem('VueX_local')).root.login.user || {}
    if (this.roleInfo.user_type === 'ent') { // 企业用户编制单位、发布单位为自己单位
      // this.form.establishOrgCode = this.roleInfo.org_code;
      // this.form.publishOrgCode = this.roleInfo.org_code;
      // this.form.establishOrgName = this.roleInfo.org_name;
      // this.form.publishOrgName = this.roleInfo.org_name;   
       this.form.establishOrgCode = this.enterData.enterpId;
      this.form.publishOrgCode = this.enterData.enterpId;
      this.form.establishOrgName = this.enterData.enterpName;
      this.form.publishOrgName = this.enterData.enterpName;   
    }
  },
  computed: {
    ...mapStateLogin({
      enterData: (state) => state.enterData,
    }),
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    }
  }
}
</script>
<style lang="scss" scoped>
.enterpriseList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 5px;
        }
      }
    }
  }
  .contentFelx {
    width: 100%;
    height: calc(100% - 50px);
    display: flex;
    justify-content: flex-start;
  }
  .gorupradio {
    height: 28px;
    line-height: 28px;
  }
  .radioicon {
    font-size: 22px;
  }
  .search_tree {
    position: relative;
    height: 90%;
    box-sizing: border-box;
  }
  .search_slide_searchKey {
    position: relative;
    height: 3rem;
    line-height: 3rem;
    background: #f5f5f7;
    border: 1px solid #e3e3e5;
    padding: 0 0.5rem;
    border-bottom: none;
  }

  .search_slide_input {
    // padding: 0 0.625rem;
    height: 100%;
    // padding-top: 0.5rem;
    // padding-left:28px;
    padding-right: 20px;
  }

  .search_slide {
    position: relative;
    transition: all 0.5s;
    // top: 0.5rem;
    width: 100%;
    // height: calc(100% - 2.7rem);
    // height: 93.5%;
    // height: calc(100% - 80px);
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #e3e3e5;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    box-sizing: border-box;
    z-index: 11;
    padding-left: 0.2rem;
    padding-bottom: 0.2rem;
    border-top: none;
    .controllab {
      border-bottom: 1px solid #e4e7ed;
      display: flex;
      justify-content: space-between;
    }
  }
  .contentFelx .left {
    width: 300px;
    min-height: 60vh;
  }
  .contentFelx .right {
    width: calc(100% - 300px);
    height: 100%;
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form_item {
    .form_title {
      font-size: 20px;
      color: #2f85de;
      margin-left: 20px;
    }
  }
  .dictionary {
    height: calc(100% - 3.857rem);
    padding: 0 28px;
    width: 100%;
    .bookDatBox {
      height: 100%;
      width: 100%;
    }
    .bookshelf {
      display: flex;
      align-items: center;
      // height: 9.5rem;
      .book {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 2.4%;
        .book_item_0 {
          position: relative;
          width: 166px;
          height: 200px;
          padding: 1rem 1rem;
          background: url(/static/img/assets/img/book/book1_noline.png) center center no-repeat;
          text-align: center;
          background-size: 100% 100%;
          .book_top {
            //    width: 100%;
            height: 58px;
            // border-bottom: 3px solid #e1c6c6;
            //    position: relative;
            //    top:-0.9rem;
            > p {
              width: 100%;
              // position: absolute;
              top: 0;
              // transform: translateY(-50%-7px);
              font-size: 0.9rem;
              line-height: 1.15rem;
              color: #fff;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 6;
              word-wrap: break-word;
              word-break: break-all;
            }
          }

          &:hover .book_more {
            display: block;
          }
          .book_more {
            position: absolute;
            top: 0.1rem;
            left: 0;
            display: none;
            width: 97%;
            height: 194px;
            background-color: rgba(0, 0, 0, 0.5);
            .book_ico {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 100%;
              transform: translate(-50%, -50%);
              > i {
                display: inline-block;
                padding: 0 0.3rem;
                font-size: 0.95rem;
                color: #cccccc;
                cursor: pointer;
              }
            }
          }
          .book_bottom {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            right: 0;
            text-align: center;
            color: #edeff3;
            padding: 0 0.5rem;
            > span {
              display: block;
              font-size: 0.9rem;
              line-height: 1.2rem;
              color: #edeff3;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }
        .book_item_1 {
          position: relative;
          width: 166px;
          height: 200px;
          padding: 1rem 1rem;
          background: url(/static/img/assets/img/book/book1_noline.png) center
            center no-repeat;
          text-align: center;
          background-size: 100% 100%;
          .book_top {
            //    width: 100%;
            height: 58px;
            // border-bottom: 3px solid #e1c6c6;
            //    position: relative;
            //    top:-0.9rem;
            > p {
              width: 100%;
              // position: absolute;
              top: 0;
              // transform: translateY(-50%-7px);
              font-size: 0.9rem;
              line-height: 1.15rem;
              color: #fff;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 6;
              word-wrap: break-word;
              word-break: break-all;
            }
          }

          &:hover .book_more {
            display: block;
          }
          .book_more {
            position: absolute;
            top: 0.1rem;
            left: 0;
            display: none;
            width: 97%;
            height: 194px;
            background-color: rgba(0, 0, 0, 0.5);
            .book_ico {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 100%;
              transform: translate(-50%, -50%);
              > i {
                display: inline-block;
                padding: 0 0.3rem;
                font-size: 0.95rem;
                color: #cccccc;
                cursor: pointer;
              }
            }
          }
          .book_bottom {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            right: 0;
            text-align: center;
            color: #edeff3;
            padding: 0 0.5rem;
            > span {
              display: block;
              font-size: 0.9rem;
              line-height: 1.2rem;
              color: #edeff3;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }
        .book_item_2 {
          position: relative;
          width: 166px;
          height: 200px;
          padding: 1rem 1rem;
          background: url(/static/img/assets/img/book/book2_noline.png) center
            center no-repeat;
          text-align: center;
          background-size: 100% 100%;
          .book_top {
            //    width: 100%;
            height: 58px;
            // border-bottom: 3px solid #e1c6c6;
            //    position: relative;
            //    top:-0.9rem;
            > p {
              width: 100%;
              // position: absolute;
              top: 0;
              // transform: translateY(-50%-7px);
              font-size: 0.9rem;
              line-height: 1.15rem;
              color: #fff;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 6;
              word-wrap: break-word;
              word-break: break-all;
            }
          }

          &:hover .book_more {
            display: block;
          }
          .book_more {
            position: absolute;
            top: 0.1rem;
            left: 0;
            display: none;
            width: 97%;
            height: 194px;
            background-color: rgba(0, 0, 0, 0.5);
            .book_ico {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 100%;
              transform: translate(-50%, -50%);
              > i {
                display: inline-block;
                padding: 0 0.3rem;
                font-size: 0.95rem;
                color: #cccccc;
                cursor: pointer;
              }
            }
          }
          .book_bottom {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            right: 0;
            text-align: center;
            color: #edeff3;
            padding: 0 0.5rem;
            > span {
              display: block;
              font-size: 0.9rem;
              line-height: 1.2rem;
              color: #edeff3;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }
        .book_item_3 {
          position: relative;
          width: 166px;
          height: 200px;
          padding: 1rem 1rem;
          background: url(/static/img/assets/img/book/book3_noline.png) center
            center no-repeat;
          text-align: center;
          background-size: 100% 100%;
          .book_top {
            //    width: 100%;
            height: 58px;
            // border-bottom: 3px solid #e1c6c6;
            //    position: relative;
            //    top:-0.9rem;
            > p {
              width: 100%;
              // position: absolute;
              top: 0;
              // transform: translateY(-50%-7px);
              font-size: 0.9rem;
              line-height: 1.15rem;
              color: #fff;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 6;
              word-wrap: break-word;
              word-break: break-all;
            }
          }

          &:hover .book_more {
            display: block;
          }
          .book_more {
            position: absolute;
            top: 0.1rem;
            left: 0;
            display: none;
            width: 97%;
            height: 194px;
            background-color: rgba(0, 0, 0, 0.5);
            .book_ico {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 100%;
              transform: translate(-50%, -50%);
              > i {
                display: inline-block;
                padding: 0 0.3rem;
                font-size: 0.95rem;
                color: #cccccc;
                cursor: pointer;
              }
            }
          }
          .book_bottom {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            right: 0;
            text-align: center;
            color: #edeff3;
            padding: 0 0.5rem;
            > span {
              display: block;
              font-size: 0.9rem;
              line-height: 1.2rem;
              color: #edeff3;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }
        .book_item_4 {
          position: relative;
          width: 166px;
          height: 200px;
          padding: 1rem 1rem;
          background: url(/static/img/assets/img/book/book4_noline.png) center
            center no-repeat;
          text-align: center;
          background-size: 100% 100%;
          .book_top {
            //    width: 100%;
            height: 58px;
            // border-bottom: 3px solid #e1c6c6;
            //    position: relative;
            //    top:-0.9rem;
            > p {
              width: 100%;
              // position: absolute;
              top: 0;
              // transform: translateY(-50%-7px);
              font-size: 0.9rem;
              line-height: 1.15rem;
              color: #fff;
              text-align: center;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 6;
              word-wrap: break-word;
              word-break: break-all;
            }
          }

          &:hover .book_more {
            display: block;
          }
          .book_more {
            position: absolute;
            top: 0.1rem;
            left: 0;
            display: none;
            width: 97%;
            height: 194px;
            background-color: rgba(0, 0, 0, 0.5);
            .book_ico {
              position: absolute;
              top: 50%;
              left: 50%;
              width: 100%;
              transform: translate(-50%, -50%);
              > i {
                display: inline-block;
                padding: 0 0.3rem;
                font-size: 0.95rem;
                color: #cccccc;
                cursor: pointer;
              }
            }
          }
          .book_bottom {
            position: absolute;
            bottom: 1.5rem;
            left: 0;
            right: 0;
            text-align: center;
            color: #edeff3;
            padding: 0 0.5rem;
            > span {
              display: block;
              font-size: 0.9rem;
              line-height: 1.2rem;
              color: #edeff3;
              overflow: hidden;
              text-overflow: ellipsis;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              word-wrap: break-word;
              word-break: break-all;
            }
          }
        }
      }
    }
    .line {
      width: 100%;
      height: 1rem;
      background-color: #fff;
      // border: 1rem solid #fff;
      // border-width: 0 0 1rem 0;
      box-shadow: 0 0 20px #b9b4b4;
    }
  }
  ._1801 {
    background: url(/static/img/assets/img/book/book0_noline.png) center center !important;
    background-size: 100% 100%;
  }
  ._1802 {
    background: url(/static/img/assets/img/book/book1_noline.png) center center !important;
    background-size: 100% 100%;
  }
  ._1803 {
    background: url(/static/img/assets/img/book/book2_noline.png) center center !important;
    background-size: 100% 100%;
  }
  ._1804 {
    background: url(/static/img/assets/img/book/book3_noline.png) center center !important;
    background-size: 100% 100%;
  }
  ._1899 {
    background: url(/static/img/assets/img/book/book4_noline.png) center center !important;
    background-size: 100% 100%;
  }
  //   .el-scrollbar {
  //     height: 94%!important;
  //   }
}
/deep/ .el-input.is-disabled .el-input__inner{
  color: #000;
}
</style>
