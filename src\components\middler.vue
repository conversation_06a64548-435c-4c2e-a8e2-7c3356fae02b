<template>
  <div class="middler">
    <p class="head">请选择对应的场所</p>
    <div class="middler-box">
      <div
        class="side-box"
        v-for="(item, index) in list"
        :key="index"
        @click.stop="login(item)"
      >
        <p class="p1">
          <span class="qh-rescueUnit"></span>
          {{ item.enterpname }}
        </p>
        <p class="p2">法人:{{ item.legalrepper }}</p>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from "vue-property-decorator";
import { getenterpriseByOrg } from "@/api/mailList";
import { adminEnteroginInfo } from "@/api/login";

@Component({})
export default class Middler extends Vue {
  private list = [];
  private mounted() {
    let token = this.$route.query.token;

    getenterpriseByOrg({
      token: "bearer " + token,
    }).then((res) => {
      if (res.data.status == 200) this.list = res.data.data;
    });
  }
  private login(item) {
    let token = this.$route.query.token;
    adminEnteroginInfo(
      {
        token: "bearer " + token,
        Authorization: "bearer " + token,
      },
      {
        enterpId: item.enterpid,
      }
    ).then((res) => {
      if (res.data.code == 0) {
        // window.location.href = `/?token=${token}`
        this.$router.push({
          path: `/?token=${token}`,
        });
      } else {
        this.$router.push({
          path: `/error`,
        });
      }
    });
  }
}
</script>

<style lang="scss" scoped>
.middler {
  width: 1200px;
  margin: 0 auto;
}
.head {
  font-size: 24px;
  font-family: Source Han Sans CN-Medium, Source Han Sans CN;
  font-weight: 500;
  color: #000;
  // padding: 20px 0;
  // margin: 0 20px;
  margin-top: 20px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
}
.middler-box {
  display: flex;
  flex-wrap: wrap;
  .side-box {
    padding: 20px;
    width: 280px;
    height: 180px;
    box-sizing: border-box;
    box-shadow: 1px 1px 3px 1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    cursor: pointer;
    margin-right: 20px;
    margin-bottom: 20px;
    &:hover {
      transition: all 0.5s;
      perspective: 300px;
      transform: scale(1.06);
    }

    .p1 {
      margin-top: 30px;
      font-size: 18px;
      padding-left: 10px;
      font-weight: bold;
      color: rgb(69, 69, 69);
    }
    .p2 {
      margin-top: 10px;
      padding-left: 10px;
      color: #333;
    }
  }
}
</style>
