<template>
  <div class="handleProcedures">
    <div class="header">
      <div class="breadcrumb breadcrumbDetails">
        <a-breadcrumb separator=" - ">
          <a-breadcrumb-item>
            <span class="icon-box" @click="goToRunning"> 三同时资料复查 </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <span class="icon-box box-con"> 办理</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
        <div>
          <el-button type="primary" size="mini" @click="closeCurrent()"
            >返 回</el-button
          >
        </div>
      </div>
    </div>
    <div class="activeBody">
      <div class="activeHeader">
        <el-tabs v-model="activeTabClass" @tab-click="handleClickActiveTab()">
          <el-tab-pane label="基本信息" name="baseInfo">
            <baseInfo :baseInfoData='baseInfoData' ref="baseInfo" @updateMsg="paneName"></baseInfo>
          </el-tab-pane>
          <el-tab-pane label="专家复查" name="expertReview">
            <expertReview ref="expertReview"></expertReview>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import baseInfo from "./baseInfo";
import expertReview from "./expertReview";
import { mapState } from "vuex";
import {
  getCompanyProjectFindById, //查询详情 
} from "@/api/companyParticularJob";
export default {
  //import引入的组件
  components: {
    baseInfo: baseInfo,
    expertReview: expertReview,
  },
  data() {
    return { 
      baseInfoData:{},
      activeTabClass: "baseInfo" };
  },
  mounted() {
    this.getCompanyParticularJobFun()
  },
  methods: {
    paneName(name) {
      this.activeTabClass = name;  
      // this.baseInfoData.checkFlag=1    
       this.handleClickActiveTab()
    },
    getCompanyParticularJobFun(row) {
      this.loading = true;
      getCompanyProjectFindById({ infoId: this.$route.query.infoId }).then(
        (res) => {
          this.baseInfoData = res.data.data;
          // this.form.infoId = res.data.data.infoId;
          // this.form2.infoId = res.data.data.infoId;
          // this.reportAttachList = res.data.data.reportAttachList; //电子报告
          // this.commentAttachList = res.data.data.commentAttachList; //专家意见附件信息
          // this.reviseAttachList = res.data.data.reviseAttachList; //修订对照附件信息
          // this.signAttachList = res.data.data.signAttachList; //签字报告附件信息
          // this.otherAttachList = res.data.data.otherAttachList; //其他附件信息
          // console.log(res, "这是查看详情数据infoId");
          this.loading = false;
        }
      );
    },

    goToRunning() {
      this.$router.push({
        path: `/dailySafetyManagement/threeDataReview`,
      });
    },
    closeCurrent() {
      this.$router.push({
        path: `/dailySafetyManagement/threeDataReview`,
      });
    },
    handleClickActiveTab() {
      // this.$store.commit('controler/updateTableBar', this.activeTabClass)
      this.$nextTick(() => {    
        this.$refs['expertReview'].getCompanyParticularJobFun()   
        this.$refs['expertReview'].reviewResultData();    
        this.$refs['expertReview'].getList();
        // if (this.$route.query.checkFlag == 1) {
        //   this.$refs['expertReview'].reviewResultData();          
        // }else{
        //   this.$refs['expertReview'].getList();
        // }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.handleProcedures {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumbDetails {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // .icon-box{
      //   border:1px solid #d9d9d9;
      //   padding:0 10px;
      //   height: 30px;
      //   display: inline-block;
      //   text-align: center;
      //   line-height: 30px;
      //   border-radius: 5px;
      //   color:#252525;
      //   margin: 0 15px 0 0;
      //  }
      // .icon-box.box-con {
      //   background-color: #326eff;
      //   border:1px solid #326eff;
      //   color: #fff;
      //   i.el-icon-close {
      //     display: inline-block;
      //     margin: 0 0 0 10px;
      //   }
      // }
    }
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }
  .activeBody {
    //  height:calc(100% - 150px);
    border: 1px solid #d9d9d9;
    padding: 0 15px 0 0;
    margin: 10px 0 0 0;
    .activeHeader {
      width: 100%;
      position: relative;
    }
  }

  /deep/ .el-tabs__header {
    margin: 0 0 0 15px;
  }
  /deep/ .el-tabs__nav-scroll {
    overflow: inherit;
  }
  /deep/ .el-tabs__content {
    overflow: inherit;
  }
}
</style>
