<template>
  <!-- 特殊时期修正 -->
  <div class="boxCon">
    <div class="boxLeft">
      <!-- {{calendarData}} -->
      <el-calendar v-model="calendarData"> </el-calendar>

      <h2>法定节假日：{{ statutoryData.length > 0 ? "有" : "无" }}</h2>
      <div class="table">
        <el-table
          :data="statutoryData"
          v-loading="loading"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          border
          style="width: 100%"
          ref="multipleTable"
        >
          <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
          <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column>
          <el-table-column
            prop="periodName"
            label="节日名称"
            align="center"
            min-width="100"
          >
          </el-table-column>
          <el-table-column
            prop="startTime"
            label="起始时间"
            align="center"
            min-width="100"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="endTime"
            label="结束时间"
            align="center"
            min-width="100"
            :show-overflow-tooltip="true"
          >
          </el-table-column>

          <el-table-column
            prop="districtName"
            label="生效区域"
            align="center"
            width="100"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table>
      </div>
      <br /><br />
      <h2>重要活动状态：{{ tableDataQuery.length > 0 ? "有" : "无" }}</h2>
      <div class="table">
        <el-table
          :data="tableDataQuery"
          v-loading="loading"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          border
          style="width: 100%"
          ref="multipleTable"
        >
          <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
          <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column>
          <el-table-column
            prop="periodName"
            label="活动名称"
            align="center"
            min-width="100"
          >
          </el-table-column>
          <el-table-column
            prop="startTime"
            label="起始时间"
            align="center"
            min-width="100"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="endTime"
            label="结束时间"
            align="center"
            min-width="100"
            :show-overflow-tooltip="true"
          >
          </el-table-column>

          <el-table-column
            prop="districtName"
            label="生效区域"
            align="center"
            width="100"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="boxRight">
      <div class="legalBox">
        <h2>法定节假日</h2>
        <div class="serchBox">
          <div class="searchItem">
            <div>
              <el-input
                v-model="searchObj.keywords2"
                size="mini"
                placeholder="节假日名称"
                clearable
              >
              </el-input>
            </div>
            <div>
              <el-date-picker
                v-model="dateValue2"
                size="mini"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                unlink-panels
                @change="searchTime2"
                clearable
              >
              </el-date-picker>
            </div>
          </div>
          <div>
            <el-button type="primary" size="mini" @click="search2"
              >查询</el-button
            >
          </div>
        </div>
        <div class="table-main">
          <!-- <div class="table-top">
          <h2>特殊时期列表</h2>
        </div> -->
          <div>
            <!-- legalData -->
            <div class="table">
              <el-table
                :data="legalData"
                v-loading="loading"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
                ref="multipleTable"
              >
                <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
                <el-table-column
                  type="index"
                  label="序号"
                  width="50"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="periodName"
                  label="节日名称"
                  align="center"
                  width="100"
                >
                </el-table-column>
                <el-table-column
                  prop="startTime"
                  label="起始时间"
                  align="center"
                  min-width="100"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column
                  prop="endTime"
                  label="结束时间"
                  align="center"
                  min-width="100"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>

                <el-table-column
                  prop="districtName"
                  label="生效区域"
                  align="center"
                  width="100"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>

                <el-table-column
                  prop="draftFlagName"
                  label="策略状态"
                  align="center"
                  width="100"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>

                <el-table-column label="操作" width="180" align="center">
                  <template slot-scope="{ row }">
                    <div class="tabButton">
                      <el-button
                        type="text"
                        @click="openDialog('read', row)"
                        v-if="
                          row.handleFlag == 2 ||
                          row.handleFlag == 0 ||
                          row.handleFlag == 1
                        "
                        >查看</el-button
                      >
                      <el-button
                        type="text"
                        @click="openDialog('edit', row)"
                        v-if="row.handleFlag == 2 || row.handleFlag == 1"
                        >编辑</el-button
                      ><el-button
                        type="text"
                        @click="deleteAccident(row.id, row)"
                        v-if="row.handleFlag == 2"
                        >删除</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="pagination">
              <el-pagination
                @current-change="handleCurrentChange2"
                :current-page.sync="searchObj.nowPage2"
                background
                layout="total, prev, pager, next"
                :total="searchObj.total2"
                v-if="searchObj.total2 != 0"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
      <div class="importantBox">
        <h2>重要活动</h2>
        <div>
          <el-button @click="openDialog('add')" type="primary" size="mini">
            <!----><!----><span>添加</span>
          </el-button>
        </div>
      </div>

      <div class="serchBox">
        <div class="searchItem">
          <div>
            <el-input
              v-model="searchObj.keywords"
              size="mini"
              placeholder="活动名称"
              clearable
            >
            </el-input>
          </div>
          <div>
            <el-date-picker
              v-model="dateValue"
              size="mini"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="searchTime"
              value-format="yyyy-MM-dd"
              unlink-panels
              clearable
            >
            </el-date-picker>
          </div>
        </div>
        <div>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
        </div>
      </div>

      <div class="table-main">
        <!-- <div class="table-top">
          <h2>特殊时期列表</h2>
        </div> -->
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
            >
              <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="periodName"
                label="活动名称"
                align="center"
                width="100"
              >
              </el-table-column>
              <el-table-column
                prop="startTime"
                label="起始时间"
                align="center"
                min-width="100"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="endTime"
                label="结束时间"
                align="center"
                min-width="100"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="districtName"
                label="生效区域"
                align="center"
                width="100"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="draftFlagName"
                label="策略状态"
                align="center"
                width="100"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column label="操作" width="180" align="center">
                <template slot-scope="{ row }">
                  <div class="tabButton">
                    <el-button
                      type="text"
                      @click="openDialog('read', row)"
                      v-if="
                        row.handleFlag == 2 ||
                        row.handleFlag == 0 ||
                        row.handleFlag == 1
                      "
                      >查看</el-button
                    >
                    <el-button
                      type="text"
                      @click="openDialog('edit', row)"
                      v-if="row.handleFlag == 2 || row.handleFlag == 1"
                      >编辑</el-button
                    ><el-button
                      type="text"
                      @click="deleteAccident(row.id,row)"
                      v-if="row.handleFlag == 2"
                      >删除</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="searchObj.nowPage"
              background
              layout="total, prev, pager, next"
              :total="searchObj.total"
              v-if="searchObj.total != 0"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <el-dialog
        :title="dialogInfo.title"
        :visible.sync="dialogInfo.visible"
        width="1100px"
        @close="closeDialog"
        v-if="dialogInfo.visible"
        :close-on-click-modal="false"
      >
        <div class="dialog">
          <el-form
            :model="accidentForm"
            :rules="rules"
            ref="ruleForm"
            label-width="150px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="行政区划:" prop="districtCode">
                  <!-- {{district}} -->
                  <el-cascader
                    placeholder="请选择行政区划"
                    :options="district"
                    :disabled="dialogInfo.disable"
                    v-model="accidentForm.districtCode"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    :show-all-levels="true"
                    style="width: 100%"
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="日期类型:" prop="periodType">
                  <el-select
                    v-model="accidentForm.periodType"
                    placeholder="请输入日期类型"
                    style="width: 100%"
                    :disabled="dialogInfo.disable"
                  >
                    <el-option
                      v-for="item in typeList"
                      :key="item.code"
                      :value="item.code"
                      :label="item.name"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="活动名称:" prop="periodName">
                  <el-input
                    v-model.trim="accidentForm.periodName"
                    maxlength="50"
                    placeholder="请输入活动名称"
                    :disabled="dialogInfo.disable"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <!-- <div style="display:flex">
                      <el-form-item label="活动日期:" prop="startTime">
                         <el-date-picker v-model="accidentForm.startTime"
                                clearable                               
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择开始时间"
                                :picker-options="pickerOptions0" />
                         </el-form-item>
                
                      <el-form-item label="" prop="endTime" class="labelWidth">
                     <el-date-picker v-model="accidentForm.endTime" style="margin-left:10px!important"
                                clearable                               
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择结束开始时间"
                                :picker-options="pickerOptions0" />
                      </el-form-item>
                    </div> -->
                <!-- {{value1}} -->
                <!-- {{accidentForm.value1}} -->
                <!-- @change="searchTimeAdd" -->
                <el-form-item label="活动日期" prop="value1">
                  <el-date-picker
                    v-model="value1"
                    type="daterange"
                    style="width: 100%"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @input="searchTimeAdd"
                    value-format="yyyy-MM-dd"
                    :disabled="dialogInfo.disable"
                    unlink-panels
                    :clearable="false"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否启用:" prop="draftFlag">
                  <el-select
                    v-model="accidentForm.draftFlag"
                    placeholder="请选择"
                    style="width: 100%"
                    :disabled="draftFlagDisabled"
                  >
                    <el-option
                      v-for="item in draftFlagOption"
                      :key="item.code"
                      :value="item.code"
                      :label="item.name"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12"> </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="活动描述:" prop="periodNote">
                  <el-input
                    v-model.trim="accidentForm.periodNote"
                    type="textarea"
                    :rows="5"
                    placeholder="2000字以内"
                    maxlength="2000"
                    :disabled="dialogInfo.disable"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="closeDialog" v-if="!draftFlagDisabled"
              >取 消</el-button
            >
            <!-- <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button> -->
            <el-button
              type="primary"
              @click="saveData()"
              v-if="!draftFlagDisabled"
              >提 交</el-button
            >
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { mapState, createNamespacedHelpers } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import {
  getAccidentListData,
  getAccidentTypeListData,
  deleteAccidentById,
  addAccident,
  updateAccident,
} from "@/api/accidentManagement";
import { parseTime } from "@/utils/index";
import { MessageBox } from "element-ui";
const mapConfig = require("@/assets/json/map.json");
import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");
// import {
//   getSelectData,
//   getInformationInfoDanger,
//   getHazarchemList,
// } from "@/api/entList";
import {
  findPeriod,
  addPeriod,
  deletePeriod,
  updatePeriod,
  findPeriodList,
} from "@/api/riskAssessment";
export default {
  data() {
    //  pickerOptions0: { // 禁止选择今天以前的日期
    //     disabledDate(time) {
    //       return time.getTime() < Date.now() - 8.64e7
    //     }
    //   },
    return {
      dateValue: [],
      dateValue2: [],
      handleFlag: "",
      statutoryData: [],
      calendarData: new Date(),
      tableDataQuery: [], //重要活动
      legalData: [], //法定节假日
      draftFlagDisabled: false,
      value1: [],
      header: {
        token: this["$store"].state.login.token,
      },
      tableData: [], // 表格数据
      loading: false, // 加载状态
      searchObj: {
        // 表格查询参数
        nowPage: 1,
        nowPage2: 1,
        total: 0,
        total2: 0,
        pageSize: 10,
        pageSize2: 10,
        keyword: "",
        keyword2: "",
        startTime: "",
        endTime: "",
        startTime2: "",
        endTime2: "",
      },

      typeList: [
        { name: "节假日", code: "0" },
        { name: "重大活动", code: "1" },
      ], // 特殊时期类型数据
      draftFlagOption: [
        { name: "未启用", code: "0" },
        { name: "启用", code: "1" },
      ],
      levelList: [], // 特殊时期等级数据
      dialogInfo: {
        // 弹窗控制信息
        visible: false,
        title: "新增特殊时期信息",
        disable: false,
      },
      accidentForm: {
        value1: [],
        startTime: "",
        endTime: "",
        districtCode: this.$store.state.login.userDistCode, //行政区划代
        periodType: "", // 日期类型
        periodName: "", // 活动名称
        draftFlag: "", //  是否启用
        periodNote: "", //活动描述
      },
      rules: {
        // 表单校验规则
        districtCode: [
          {
            required: true,
            message: "请选择行政区划",
            trigger: "change",
          },
        ],
        periodType: [
          {
            type: "string",
            required: true,
            message: "请选择日期类型",
            trigger: "change",
          },
        ],
        periodName: [
          { required: true, message: "请填写活动名称", trigger: "blur" },
          { max: 50, message: "长度 50 个字符一下", trigger: "blur" },
        ],
        value1: [
          {
            required: true,
            message: "请选择活动日期",
            trigger: "change",
          },
        ],
        //  endTime: [
        //   {
        //     type: "string",
        //     required: true,
        //     message: "请选择结束日期",
        //     trigger: "change",
        //   },
        // ],
        draftFlag: [
          {
            type: "string",
            required: true,
            message: "请选择是否启用",
            trigger: "change",
          },
        ],
      },
      district: this.$store.state.controler.district, // 行政区划
    };
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
  created() {},
  methods: {
    searchTimeAdd(value1) {
      if (value1) {
        this.accidentForm.startTime = value1[0];
        this.accidentForm.endTime = value1[1];
        // this.accidentForm.value1=[value1[0],value1[1]]

        this.$set(this.accidentForm.value1, 0, value1[0]);
        this.$set(this.accidentForm.value1, 1, value1[1]);
      } else {
      }
    },
    searchTime(value1) {
      if (value1) {
        // let date1 = new Date(value1[0]);
        // let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        // let date2 = new Date(value1[1]);
        // let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.searchObj.startTime = value1[0];
        this.searchObj.endTime = value1[1];
      } else {
        this.value1 = [];
        this.searchObj.startTime = "";
        this.searchObj.endTime = "";
      }
    },

    searchTime2(value1) {
      if (value1) {
        // let date1 = new Date(value1[0]);
        // let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        // let date2 = new Date(value1[1]);
        // let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.searchObj.startTime2 = value1[0];
        this.searchObj.endTime2 = value1[1];
      } else {
        this.value1 = [];
        this.searchObj.startTime2 = "";
        this.searchObj.endTime2 = "";
      }
    },

    search() {
      this.searchObj.nowPage = 1;
      this.getAccidentList(1);
    },
    search2() {
      this.searchObj.nowPage2 = 1;
      this.getAccidentList(0);
    },
    handleSelectionChange() {},

    // 分页查询
    handleCurrentChange(val) {
      this.searchObj.nowPage = val;
      this.getAccidentList(1);
    },
    handleCurrentChange2(val) {
      this.searchObj.nowPage = val;
      this.getAccidentList(0);
    },
    // 获取特殊时期列表数据
    getAccidentList(type) {
      this.loading = true;
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        draftFlag: "",
        endTime: type == 1 ? this.searchObj.endTime : this.searchObj.endTime2,
        id: "",
        keywords:
          type == 1 ? this.searchObj.keywords : this.searchObj.keywords2,
        nowPage: this.searchObj.nowPage,
        pageSize: this.searchObj.pageSize,
        periodType: type,
        startTime:
          type == 1 ? this.searchObj.startTime : this.searchObj.startTime2,
      };
      findPeriod(params).then((res) => {
        if (res.data.status === 200) {
          if (type == 1) {
            this.tableData = res.data.data.list;
            this.searchObj.nowPage = res.data.data.nowPage + 1;
            this.searchObj.total = res.data.data.total;
          } else {
            this.legalData = res.data.data.list;
            this.searchObj.nowPage2 = res.data.data.nowPage + 1;
            this.searchObj.total2 = res.data.data.total;
          }

          this.loading = false;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
    //无分页
    getFindPeriodList(type) {
      this.loading = true;
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        draftFlag: "1",
        endTime: dayjs(this.calendarData).format("YYYY-MM-DD"),
        id: "",
        keywords: "",
        nowPage: 0,
        pageSize: 0,
        periodType: type,
        startTime: dayjs(this.calendarData).format("YYYY-MM-DD"),
      };

      findPeriodList(params).then((res) => {
        if (res.data.status === 200) {
          if (type == 1) {
            this.tableDataQuery = res.data.data;
          } else {
            this.statutoryData = res.data.data;
          }
          this.loading = false;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },

    // 删除特殊时期列
    deleteAccident(id, row) {
      MessageBox.confirm("确定要删除选择的数据吗?", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(() => {
          deletePeriod({ id })
            .then((res) => {
              this.$message.success("删除成功");
              if (this.tableData.length === 1 && this.searchObj.nowPage !== 1) {
                this.searchObj.nowPage--;
              }
              if (row.periodType == 1) {
                this.getAccidentList(1);
                this.getFindPeriodList(1);
              } else {
                this.getAccidentList(0);
                this.getFindPeriodList(0);
              }
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {});
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogInfo.visible = false;
      this.empty();
    },
    empty() {
      this.value1 = [];
      this.accidentForm = {
        districtCode: this.$store.state.login.userDistCode, //行政区划代
        periodType: "", // 日期类型
        periodName: "", // 活动名称
        startTime: "", //活动日期
        endTime: "",
        draftFlag: "", //  是否启用
        periodNote: "", //活动描述
        // value1:[]
      };
      this.resetForm("ruleForm");
    },
    // 打开弹窗
    openDialog(type, data) {
      const dataList = Object.assign({}, data);
      if (type === "edit") {
        this.handleFlag = data.handleFlag;
        this.dialogInfo.title = "编辑特殊时期信息";
        this.accidentForm = dataList;
        this.accidentForm.value1 = [];
        this.value1.push(dataList.startTime, dataList.endTime);
        this.$set(this.accidentForm.value1, 0, dataList.startTime);
        this.$set(this.accidentForm.value1, 1, dataList.endTime);

        if (data.handleFlag == 1) {
          //handleFlag为1的时候只能编辑启用
          this.dialogInfo.disable = true;
          this.draftFlagDisabled = false;
        } else if (data.handleFlag == 2) {
          //handleFlag是2的时候编辑的时候可以全部编辑
          this.dialogInfo.disable = false;
          this.draftFlagDisabled = false;
        }
      } else if (type === "add") {
        this.accidentForm.value1 = [];
        this.dialogInfo.title = "新增特殊时期信息";
        this.dialogInfo.disable = false;
        this.draftFlagDisabled = false;
      } else {
        this.handleFlag = data.handleFlag;
        this.accidentForm = dataList;
        this.accidentForm.value1 = [];
        this.value1.push(dataList.startTime, dataList.endTime);
        this.$set(this.accidentForm.value1, 0, dataList.startTime);
        this.$set(this.accidentForm.value1, 1, dataList.endTime);
        this.dialogInfo.title = "特殊时期信息详情";
        this.dialogInfo.disable = true;
        this.draftFlagDisabled = true;
      }
      this.dialogInfo.visible = true;
    },
    // 新增/编辑
    saveData() {
      // debugger
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.dialogInfo.title === "新增特殊时期信息"
            ? this.addData()
            : this.updateData();
        }
      });
    },
    // 新增特殊时期数据
    addData() {
      addPeriod(this.accidentForm).then((res) => {
        if (res.data.status === 200) {
          this.$message.success("操作成功");
          this.dialogInfo.visible = false;
          if (this.accidentForm.periodType == 1) {
            this.getAccidentList(1);
            this.getFindPeriodList(1);
          } else {
            this.getAccidentList(0);
            this.getFindPeriodList(0);
          }
          this.empty();
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },
    // 更新特殊时期数据
    updateData() {
      // debugger
      updatePeriod(this.accidentForm).then((res) => {
        if (res.data.status === 200) {
          this.$message.success("操作成功");
          this.dialogInfo.visible = false;
         
          if (this.accidentForm.periodType == 1) {
            this.getAccidentList(1);
            this.getFindPeriodList(1);
          } else {
            this.getAccidentList(0);
            this.getFindPeriodList(0);
          }

          this.empty();
        } else {
          this.$message.warning(res.data.mssg);
        }
      });
    },

    // 企业数据
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    // 清楚建议框
    clearSensororgCode() {
      this.accidentForm.companyId = "";
      this.accidentForm.companyName = "";
    },
    //选择企业
    handleSelect(item) {
      // consle.log(this)
      this.accidentForm.companyId = item.enterpId;
      this.accidentForm.companyName = item.enterpName;
      this.accidentForm.dangerId = "";
      this.getSelectDataById(item.enterpId);
      this.accidentForm.chemicalsId = "";
      this.getHazarchemData(item.enterpId);

      this.accidentForm.longitude = item.longitude;
      this.accidentForm.latitude = item.latitude;
      this.accidentForm.districtCode = item.districtCode;
      this.accidentForm.address = item.address;
      var x = {
        x: item.longitude,
        y: item.latitude,
      };
      //
      this.$refs["detailMap"].setMapDiot2(x);
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  mounted() {
    this.getAccidentList(1); //活动
    this.getAccidentList(0); //节假日
    this.getFindPeriodList(1);
    this.getFindPeriodList(0);
  },
};
</script>
<style scoped lang="scss">
.serchBox {
  display: flex;
  margin: 0 0 10px 0;
  .searchItem {
    display: flex;
    div {
      margin: 0 10px 0 0;
    }
  }
}
/deep/ .el-tabs__item {
  font-size: 16px;
}
/deep/ .el-calendar-table .el-calendar-day {
  height: 30px;
}
/deep/ .el-calendar__header .el-calendar__button-group {
  display: none !important;
}
/deep/ .el-calendar-table td.is-today {
  background-color: #409eff !important;
  color: #ccc !important;
}
/deep/ .el-table td.el-table__cell div {
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/ .el-tabs--left .el-tabs__header.is-left {
  height: calc(100% - 50px);
}
/deep/ .cell .tabButton .el-button {
  padding: 5px;
}
/deep/ .labelWidth .el-form-item__content {
  margin-left: 0 !important;
}
.boxRight {
  .legalBox {
    margin: 0 0 20px 0;
  }
}
.table {
  // height: 300px;
}
.dialog-footer {
  text-align: center;
}
.boxCon {
  display: flex;
  justify-content: space-between;
  > div {
    width: 49%;
  }
}
.importantBox {
  display: flex;
  justify-content: space-between;
}
.table-main {
  background: #fff;
  .table-top {
    display: flex;
    justify-content: space-between;
    // padding: 10px 0;
    h2 {
      font-size: 18px;
      line-height: 45px;
      margin-bottom: 0;
    }
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>