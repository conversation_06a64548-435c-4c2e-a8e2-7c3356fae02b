<template>
  <div class="riskDy">
    <div class="topTit">
      风险单元和风险点：<span>{{ risktopData.riskUnintCount }}</span
      >个风险单元， <span>{{ risktopData.riskUnitPointCount }}</span
      >项风险点
    </div>
    <div class="riskBox" v-loading>
      <div
        :class="['riskItem', 'riskItem' + item.riskUnitRank]"
        v-for="(item, index) in riskData"
        :key="index"
      >
        <div class="riskItemTop">
          <div class="title">
            {{ item.riskUnitName }}</div>
          <div :class="['riskNnum', 'status' + item.riskUnitRank]">
            <div><span>{{ item.riskUnitTypeName}}</span></div>
            <span>{{ item.riskLevelName }}</span>
            <p>{{ item.riskUnitValue }}</p>
          </div>
        </div>
        <ul>
          <li
            v-for="(el, k1) of item.cimRiskUnitPointDTOList"
            :key="k1"
            @click="
              clickItem(
                el,
                item.cimRiskUnitPointDTOList,
                k1,
                item.riskUnitName,
                item
              )
            "
          >
            <span :class="['iconSquer', 'iconSquer' + item.riskUnitRank]"></span
            >{{ el.riskPointName }}<span class="iconArrow"></span>
          </li>
        </ul>
      </div>
    </div>

    <!-- <DialogTwo ref="DialogTwo"></DialogTwo>
    <DialogOne ref="DialogOne"></DialogOne> -->
    <DialogThree ref="DialogThree"></DialogThree>
  </div>
</template>

<script>
// const echarts = require('echarts');
import {
  queryRiskCompanyInfo, //
} from "@/api/riskAssessment";
// import DialogTwo from "./DialogTwo.vue";
// import DialogOne from "./DialogOne.vue";
import DialogThree from "./DialogThree.vue";
export default {
  //import引入的组件
  name: "riskDynamics",
  components: { DialogThree },
  data() {
    return {
      loading: false,
      dialogRisk: false, //弹窗
      curDistrictCode:"",
      risktopData: {},
      getIndex123:"0",
      riskData: [
        {
          title: "氯化工艺",
          level: "1",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "2",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "3",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "4",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "1",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "2",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "3",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "4",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
      ],
    };
  },
  filters: {
    filterLevel(val) {
      var str = "";
      if (val == 1) {
        str = "较大风险";
      } else if (val == 2) {
        str = "一般风险";
      } else if (val == 3) {
        str = "重大风险";
      } else if (val == 4) {
        str = "低风险";
      }
      return str;
    },
  },

  //方法集合
  methods: {
    clickItem(el, numLen, clickIndex, title, bigObjData) {
      // if(numLen.length==2){
      //    this.$refs['DialogTwo'].isShow(true);//火灾弹框
      // }else if(numLen.length==1){
      //   this.$refs['DialogOne'].isShow(true);
      // }else if(numLen.length==3){
      //   el.preRiskUnitName=title
      //   this.$refs['DialogThree'].isShow(true);
      //   this.$refs['DialogThree'].parentData=el;
      //   this.$refs['DialogThree'].allRiskPoinData=numLen
      //   this.$refs['DialogThree'].clickActive=clickIndex,//默认是点击的那个风险点
      //   this.$refs['DialogThree'].getHS();
      //   this.$refs.DialogThree.getK1()
      //   this.$refs.DialogThree.getE();
      //   this.$refs.DialogThree.getM()
      // }
      this.$nextTick(() => {
        this.$refs["DialogThree"].bigObj.risktopData=this.risktopData
        this.$refs["DialogThree"].bigObj = bigObjData;
        this.$refs["DialogThree"].title = title;
        this.$refs["DialogThree"].isShow(true);
        this.$refs["DialogThree"].parentData = el;
        this.$refs["DialogThree"].allRiskPoinData = numLen;
        this.$refs["DialogThree"].clickActive = clickIndex; //默认是点击的那个风险点
        this.$refs["DialogThree"].curDistrictCode=this.curDistrictCode;
        this.$refs["DialogThree"].getIndex123=this.getIndex123
        this.$refs["DialogThree"].getHS();
        this.$refs.DialogThree.getK1();
        this.$refs.DialogThree.getE();
        this.$refs.DialogThree.getM();
        this.$refs.DialogThree.getK2();    
        this.$refs.DialogThree.getK3();    
        this.$refs.DialogThree.getModel();
        this.$refs.DialogThree.enterSpecialFn(); //特殊日期
        this.$refs.DialogThree.getNaturalData(); //特殊日期
        this.$refs.DialogThree.internetDataList(); //特殊日期
      });
    },
    getData(enterpriseId) {
      this.loading = true;
      queryRiskCompanyInfo({
        enterId: enterpriseId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          this.riskData = res.data.data.cimRiskUnitDTOList;
          this.risktopData = res.data.data;
          this.curDistrictCode= res.data.data.districtCode;
          this.getIndex123=Number(res.data.data.indexI1) + Number(res.data.data.indexI2) + Number(res.data.data.indexI3)
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0;
}
.riskDialogBox {
  background: url("../../../../../static/img/riskDialog_bg.png") no-repeat
    center #0c1534;
  width: 100%;
  padding: 0 20px;
  .riskDialogTop {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #fff;
    padding: 10px 0 10px 0;
    .dialogL {
      background: url("../../../../../static/img/riskTopL.png") no-repeat bottom
        left;
      padding: 0 0 8px;
      width: 48%;
    }
    .dialogR {
      background: url("../../../../../static/img/riskTopR.png") no-repeat bottom
        right;
      padding: 0 0 8px;
      width: 48%;
      text-align: right;
    }
  }
  .dialogCon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialogConL {
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitL.png") no-repeat
          bottom left;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #808089;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
    .dialogConC {
      background: url("../../../../../static/img/riskDialog.png") no-repeat
        center center;
      width: 960px;
      height: 535px;
      background-size: 100% 100%;
      position: relative;
    }
    .dialogConR {
      text-align: right;
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitR.png") no-repeat
          bottom right;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #808089;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
  }
  .dialogConC {
  }
  .centerL {
    font-size: 12px;
    text-align: center;
    color: #fff;
    .center1 {
      //高风险设备设施
      position: absolute;
      top: 31px;
      left: 75px;
    }
    .center2 {
      // 高风险作业K2
      position: absolute;
      left: 218px;
      top: 31px;
    }
    .center3 {
      //高风险物品（能量）M
      position: absolute;
      left: 19px;
      top: 144px;
    }
    .center4 {
      //高风险场所E
      position: absolute;
      left: 96px;
      top: 239px;
    }
    .center5 {
      left: 212px;
      top: 239px;
      position: absolute;
    }
    .center6 {
      //初始风险H
      left: 284px;
      top: 147px;
      position: absolute;
    }
    .center7 {
      //指标管控率G
      position: absolute;
      left: 226px;
      top: 116px;
      transform: rotate(-56deg);
    }
    .center71 {
      //  固有风险H
      position: absolute;
      left: 167px;
      top: 184px;
      color: #03d2da;
      b {
        display: block;
        color: #fff;
      }
    }
    .clickCen {
      background: #03d2da;
      position: absolute;
      left: 113px;
      top: 89px;
      font-size: 16px;
      width: 158px;
      line-height: 16px;
      padding: 3px 0;
      color: #000;
    }
    .clickStyle {
      background: url("../../../../../static/img/img-两个-常态.png") no-repeat
        center;
      position: absolute;
      width: 60px;
      background-size: 100% 100%;
      height: 60px;
      left: 161px;
      top: 119px;
      line-height: 60px;
      span {
        display: inline-block;
        width: 28px;
        cursor: pointer;
      }
    }
  }

  .centerC {
    font-size: 12px;
    text-align: center;
    color: #fff;
    position: absolute;
    width: 240px;
    /* border: 1px solid red; */
    left: 347px;
    top: 5px;
    height: 242px;
    > p {
      padding: 21px 0 0 0;
    }
  }
  .center10 {
    position: absolute;
    /* border: 1px solid red; */
    width: 69px;
    left: 83px;
    top: 92px;
    /* height: 64px; */
    text-align: center;
    font-weight: bold;
  }
  .center101 {
    position: absolute;
    left: 27px;
    top: 176px;
    font-weight: bold;
    > div {
      color: rgba(255, 255, 255, 0.6);
      font-weight: normal;
    }
  }
  .center102 {
    position: absolute;
    top: 176px;
    left: 116px;
    font-weight: bold;
    > div {
      color: rgba(255, 255, 255, 0.6);
      font-weight: normal;
    }
  }
  .centerR {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 300px;
    /* border: 1px solid red; */
    height: 123px;
    right: 18px;
    top: 133px;
    color: #33bbff;
    > p {
      position: absolute;
      left: 122px;
      bottom: 11px;
      margin: 0;
    }
    .center12 {
      //特殊时期
      position: absolute;
      left: 178px;
      top: 16px;
      span {
        font-weight: bold;
      }
    }
    .center13 {
      position: absolute;
      left: 32px;
      top: 25px;
      width: 61px;
      height: 46px;
      /* border: 1px solid red; */
      line-height: 46px;
      font-weight: bold;
      color: #fff;
    }
    .center14 {
      //自然环境
      position: absolute;
      left: 178px;
      top: 41px;
      span {
        font-weight: bold;
      }
    }
    .center15 {
      position: absolute;
      left: 178px;
      top: 65px;
      span {
        font-weight: bold;
      }
    }
  }
  .k4 {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 146px;
    height: 90px;
    // border: 1px solid red;
    height: 72px;
    right: 18px;
    top: 44px;
    color: #33bbff;
    > p {
      text-align: right;
      padding: 5px 10px 0 0;
      font-weight: bold;
      margin: 0;
      line-height: 12px;
    }
    .con {
      line-height: 15px;
    }
    .con > p {
      margin: 0;
      color: rgba(51, 187, 255, 0.6);
    }
  }
  .k3 {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 140px;
    height: 90px;
    /* border: 1px solid red; */
    height: 69px;
    right: 180px;
    top: 44px;
    color: #00ffff;
    > p {
      text-align: right;
      padding: 5px 10px 0 0;
      font-weight: bold;
      margin: 0;
      line-height: 12px;
    }
    .con {
      line-height: 15px;
    }
    .con > p {
      margin: 0;
      color: rgba(0, 255, 255, 0.6);
    }
  }
  .major {
    position: absolute;
    right: 87px;
    top: 375px;
    background: url("../../../../../static/img/重大隐患.png") no-repeat center;
    width: 150px;
    background-size: 100%;
    height: 123px;
    /* padding: 20px 0 0 0; */
    color: #ea6544;
    font-size: 12px;
    > p {
      text-align: center;
      margin-top: -10px;
      padding: 0 0 0 27px;
    }
    > div.num {
      width: 67px;
      margin: 0 0 0 59px;
      height: 61px;
      position: absolute;
      top: 30px;
      line-height: 61px;
      text-align: center;
      font-weight: bold;
    }
  }

  .centerBottomItem2 {
    position: absolute;
    bottom: 50px;
    left: 362px;
    text-align: center;
    color: #fff;
    width: 218px;
    .name {
      color: #ffdcc3;
    }
    > p {
      font-size: 22px;
      margin: 0px 0 5px 0;
      span {
        font-weight: bold;
        display: inline-block;
        margin-left: 5px;
      }
    }
  }
}
.riskDy {
  .topTit {
    height: 40px;
    line-height: 40px;
    background: #fff7f7;
    border: 1px solid red;
    margin: 0 0 12px 0;
    // font-size: 16px;
    color: #252525;
    width: 100%;
    padding: 0 12px 0 20px;
    span {
      color: #409eff;
    }
  }
  .riskBox {
    display: flex;
    flex-wrap: wrap;

    .riskItem {
      width: 265px;
      height: 300px;
      //   border: 1px solid #d9d9d9;
      margin: 0 12px 12px 0;
      padding: 0 15px;
      background: url("../../../../../static/img/kapian.png") no-repeat top left;
      .riskItemTop {
        display: flex;
        height: 110px;
        // width:120px;
        align-items: center;
        margin: 5px 0 0 5px;
        overflow: hidden;
        justify-content: space-between;
        box-sizing: content-box;
        .title {
          color: #333333;
          font-size: 22px;
          width: 120px;
          height: 100px;
          line-height: 27px;
          // display: flex;
          // flex-wrap: wrap;
          // align-items: flex-end;
          p{
          }
          span{
            
          }
        }
        .riskNnum {
          text-align: right;
          font-size: 14px;
          // font-size: 30px;
          // font-weight: bold;
        }
      }
      ul {
        margin: 0;
        padding: 0;
        li {
          list-style: none;
          position: relative;
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin: 25px 0 0 0;
          padding: 0 0 0 12px;
          cursor: pointer;
          .iconArrow {
            background: url("../../../../../static/img/箭头.png") no-repeat
              center center;
            width: 12px;
            height: 10px;
            display: inline-block;
            position: absolute;
            top: 3px;
            right: 0;
          }
        }
      }
    }
  }

  //@at-root
  .riskNnum {
    >span {
      font-size: 16px;
      display: inline-block;
      margin: 5px 0 0 0;
    }
    >p {
      font-size: 30px;
      margin: 0;
      font-weight: bold;
    }
  }

  .riskNnum.status1 {
    span,
    p {
      color: #f06844;
    }
  }
  .iconSquer.iconSquer1 {
    background: #f06844;
  }
  .riskNnum.status2 {   //较大风险  橙色
    span,
    p {
      color: #ff9d47;
    }
  }
  .iconSquer.iconSquer2 {
    background: #ff9d47;
  }

  .riskNnum.status3 {  //一般风险 黄色
    span,
    p {
      color: #feb60f;
    }
  }
  .iconSquer.iconSquer3 {
    background: #feb60f;
  }

  .riskNnum.status4 {  //蓝色
    span,
    p {
      color: #5d93fd;
    }
  }
  .iconSquer.iconSquer4 {
    background: #5d93fd;
  }

  .iconSquer {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ccc;
    top: 50%;
    margin-top: -3px;
    left: 0;
  }
}
</style>