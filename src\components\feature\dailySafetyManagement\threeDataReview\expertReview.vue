<template>
  <div :class="['expertReview', baseInfoData.checkFlag == 0 ? 'zhuStyle' : '']">
    <!-- {{ isReview+'------1:显示0:不显示' }}-- {{ isReview == 1 }} -->
    <!-- 默认false,发起复查后-成功后是true -->
    <!-- v-if="$route.query.approvalStatus==3" -->
    <!-- {{localStorage.getItem('localIsReview') +'---------------------localStorage.getItem(key)'}} -->
    <div class="reviewInfo" v-if="baseInfoData.checkFlag == 1">
      <div class="titStatus">
        <span>专家组组长复查意见</span>
      </div>
      <div class="reviewInfoCon">
        <div
          class="reviewInfoItem"
          v-for="(item, index) in reviewInfoData"
          :key="index"
        >
          <div class="itemT">
            <!-- {{ item.headStatus | filterStatus }} -->
            <div class="itemT_left">
              <span class="name">{{ item.expertName }}</span>
              <span class="line"></span>
              <span class="phone">{{ item.expertTelephone }}</span>
              <span :class="['status', 'pass', 'status' + item.expertApproval]"
                ><i class="el-icon-check" v-if="item.expertApproval == 0"></i>
                {{ item.expertApprovalName }}</span
              >
            </div>

            <div>
              {{ item.expertMessage.split("，")[2] }}
            </div>

            <div class="time">
              <i class="el-icon-timer"></i>{{ item.updateTime }}
            </div>
          </div>

          <div class="con">
            <div class="exprertWenj">
              <div class="exprertWenjBox">
                <span> 附件： </span>
                <div class="experComment">
                  <AttachmentUpload
                    :attachmentlist="item.commentAttachments"
                    :limit="5"
                    type="pdf"
                    v-bind="{}"
                    :editabled="true"
                  ></AttachmentUpload>
                </div>
              </div>
            </div>

            <div class="exprertWenj commentStly">
              <div class="exprertWenjBox">
                <span>意见：</span>
                <div class="experComment">
                  {{ item.expertComment || "无" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="titStatus">
        <span>专家组成员复查意见</span>
      </div>
      <div class="reviewInfoCon">
        <div
          class="reviewInfoItem"
          v-for="(item, index) in memberApprovals"
          :key="index"
        >
          <div class="itemT">
            <div class="itemT_left">
              <span class="name">{{ item.expertName }}</span>
              <span class="line"></span>
              <span class="phone">{{ item.expertTelephone }}</span>
              <span :class="['status', 'pass', 'status' + item.expertApproval]"
                ><i class="el-icon-check" v-if="item.expertApproval == 0"></i>
                {{ item.expertApprovalName }}</span
              >
            </div>

            <div>
              {{ item.expertMessage.split("，")[2] }}
            </div>

            <div class="time">
              <i class="el-icon-timer"></i>{{ item.updateTime }}
            </div>
          </div>

          <div class="con">
            <div class="exprertWenj">
              <div class="exprertWenjBox">
                <span> 附件： </span>
                <AttachmentUpload
                  :attachmentlist="item.commentAttachments"
                  :limit="5"
                  type="pdf"
                  v-bind="{}"
                  :editabled="true"
                ></AttachmentUpload>
              </div>
            </div>
            <div class="exprertWenj">
              <div class="exprertWenjBox commentStly">
                <span>意见：</span>
                <div class="experComment">
                  {{ item.expertComment || "无" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- passFlag：0的时候不显示按钮；1显示生成报告，生成以后再显示下载报告；2显示驳回 -->
      <div class="btnBoxLast" v-if="passFlag != 0">
        <div v-if="passFlag == 1">
          <div v-if="$route.query.approvalStatus != 5">
            <el-button
              type="primary"
              :disabled="$store.state.login.user.isDanger != 1"
              size="mini"
              @click="clickReport"
              >生成报告</el-button
            >
          </div>
        </div>
        <!-- 监管端的专家复查页面，生成报告，弹窗进行对照表和签名合稿，保存后才可以下载签字报告 -->
        <!-- <div v-if="passFlag == 1">
          <div v-if='$route.query.approvalStatus==5'>
             <el-button :disabled="isDown" type="primary" size="mini"
            >下载报告</el-button
          >          
          </div>         
        </div> -->
        <div v-if="passFlag == 2" @click="bacKInfoBtn">
          <el-button
            :disabled="$store.state.login.user.isDanger != 1"
            type="primary"
            size="mini"
            >驳回</el-button
          >
        </div>
      </div>
    </div>
    <div v-if="baseInfoData.checkFlag == 0">
      <div>
        <div class="expertReviewCon">
          <div class="expertReviewL">
            <div class="searchBox">
              <div class="search_slide_searchKey">
                <el-input
                  placeholder="请输入专家名称"
                  v-model.trim="filterText"
                >
                  <i
                    slot="append"
                    class="el-icon-search"
                    @click="serchClick"
                  ></i>
                </el-input>
              </div>
            </div>

            <div class="groupBox" v-loading="loading">
              <el-checkbox-group v-model="checkedCities">
                <el-checkbox
                  v-for="city in cities"
                  :label="city.expertId"
                  :key="city.expertId"
                >
                  {{ city.expertName }}<i></i>{{ city.mobileTel }}
                </el-checkbox>
              </el-checkbox-group>
            </div>

            <div class="pagination">
              <el-pagination
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="size"
                :pager-count="3"
                layout="total, prev, pager, next"
                background
                :total="total"
              >
              </el-pagination>
            </div>
            <!--         
          <br />
          <br />
          {{ checkedCities }}<br /> -->
            <!-- {{ rightData }}---这是选择的对象 -->
          </div>
          <div class="expertReviewR">
            <div class="rightTop">
              <el-form
                ref="ruleForm"
                class="ruleFormStly"
                :rules="rules"
                :model="form"
                label-width="100px"
              >
                <el-form-item label="添加名单 ：" prop="expertName">
                  <el-input
                    v-model.trim="form.expertName"
                    placeholder="请输入姓名"
                    size="small"
                    style="width: 200px"
                    maxlength="6"
                    clearable
                  ></el-input>
                </el-form-item>
                <el-form-item label="手机号码" prop="mobileTel">
                  <el-input
                    v-model.trim="form.mobileTel"
                    placeholder="请输入手机号码"
                    clearable
                    size="small"
                    style="width: 200px"
                  ></el-input>
                </el-form-item>
              </el-form>
              <div class="btnAdd" @click="clickAdd">
                <el-button type="primary" style="" size="mini"
                  ><i class="el-icon-plus"></i>添加</el-button
                >
              </div>
            </div>
            <div class="rightBottom">
              <span>专家名单 ：</span>
              <div class="itemHeight">
                <div class="itemBox">
                  <div
                    class="itemCon"
                    v-for="(el, index) of rightData"
                    :key="index"
                  >
                    <!-- {{rightData}} -->
                    <div class="addClassStyle">
                      <!-- {{el}} -->
                      <div>
                        <span class="name">{{ el.expertName }}</span>
                        <span class="line"></span>
                        <span class="phone">{{
                          el.expertTelephone || el.mobileTel
                        }}</span>
                      </div>
                      <i class="el-icon-close" @click="removePerson(el)"></i>
                    </div>

                    <!-- <el-radio-group v-model="rightData.radio" style="margin-bottom:0!important">
                  <el-radio :label="1">组长</el-radio>
                  <el-radio :label="2">专家组成员</el-radio>               
                </el-radio-group> -->
                    <!-- {{ formRadio.radioArray[index] }} -->
                    <el-radio-group
                      v-model="formRadio.radioArray[index]"
                      @change="clickRadio(el, index)"
                    >
                      <el-radio
                        v-for="it of el.list"
                        :key="it.id"
                        :label="it.id"
                      >
                        {{ it.anames }}
                      </el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
            </div>

            <div class="rightBtn">
              <el-button
                type="primary"
                style=""
                size="mini"
                @click="sendReview"
                v-if="true"
                :disabled="
                  baseInfoData.approvalStatus == 2 || user.isAdmin != 1
                "
                ><i class="el-icon-s-promotion"></i>发起复查</el-button
              >
              <!--  -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- v-if="isReview"  -->
    <!-- {{$route.query.approvalStatus + '状态----' }} -->
    <!-- v-if="$route.query.approvalStatus==3 || $route.query.approvalStatus==4 || $route.query.approvalStatus==5" -->

    <!-- <iframe style="width:500px;height:500px" src="../../../pdf-signature-master/pdf-signature-master/index.html"></iframe> -->

    <el-dialog
      title="生成报告"
      :visible.sync="dialogVisibleReport"
      width="1000px"
      top="5vh"
      @close="closeDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogLink" v-loading="loading">内容</div>
      <div slot="footer" style="display: flex; justify-content: center">
        <!-- <el-button @click="closeDialog()">取 消</el-button> -->
        <el-button type="primary" size="mini">保 存</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="发起复查"
      :visible.sync="dialogVisible"
      width="1000px"
      top="5vh"
      @close="closeDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogLink">
        <p>访问链接和授权</p>
        <!-- {{linkData}} -->
        <div v-for="(item, index) of linkData" :key="index" class="linkInfoBox">
          <div class="linkInfo">
            <span class="name">{{ item.expertName }}</span> /
            <span class="phone">{{ item.expertTelephone }}</span>
          </div>

          <div class="linkCon">
            <div class="copyCon">
              {{ item.expertMessage }}
            </div>
            <span class="copyLink" @click="clickLink(item)">复制链接</span>
          </div>
        </div>
      </div>
      <!-- <div slot="footer" style="display: flex; justify-content: center">
        <el-button @click="closeDialog()">取 消</el-button>
        <el-button :loading="btnLoading" type="primary">提 交</el-button>
      </div> -->
    </el-dialog>

    <el-dialog
      title="驳回"
      :visible.sync="dialogVisible3"
      width="500px"
      top="5vh"
      @close="closeDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogInfo">
        <el-form ref="ruleForm" :model="form2" size="medium" :rules="rules2">
          <el-descriptions :column="2" border :labelStyle="labelStyle">
            <el-descriptions-item :span="2">
              <template slot="label"
                ><span class="redDot" style="color: red">*</span> 驳回：
              </template>
              <el-form-item prop="rejectInfo">
                <el-input
                  type="textarea"
                  placeholder="请输入驳回原因"
                  v-model.trim="form2.rejectInfo"
                  :rows="4"
                  maxlength="500"
                  show-word-limit
                >
                </el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
      <div slot="footer" style="display: flex; justify-content: center">
        <el-button size="mini" type="primary" @click="backInfo()"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { getExpertListData } from "@/api/mergencyResources";
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
import {
  projectApprovalStart, //开始复核
  projectApprovalReviewResult,
  getCompanyProjectFindById,
  getCompanyProjectReject, //驳回
} from "@/api/companyParticularJob";
const cityOptions = ["上海", "北京", "广州", "深圳"];
export default {
  components: {
    AttachmentUpload,
  },
  data() {
    // 手机号验证
    var checkPhone = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
      if (!value) {
        return callback(new Error("联系电话不能为空"));
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error("请输入数字值"));
        } else {
          if (phoneReg.test(value)) {
            callback();
          } else {
            callback(new Error("联系电话格式不正确"));
          }
        }
      }, 100);
    };
    return {
      currentPage: 1,
      total: 0,
      size: 10,
      formRadio: {
        radioArray: [],
      },
      radio: "1",
      baseInfoData: {},
      passFlag: "",
      reviewResultAllData: {},
      loading: false,
      isDown: false, //默认不能下载报告
      dialogVisible: false,
      dialogVisible3: false,
      localIsReview: 0,
      isReview: 0, //不显示
      dialogVisibleReport: false,
      checkedCities: [],
      rightData: [],
      reviewInfoData: [],
      memberApprovals: [],
      objPerson: [], //
      filter0bjPerson: [],
      form: {
        expertName: "",
        mobileTel: "",
      },
      cities: [],
      citiesAll: [],
      linkData: [],
      filterText: "",
      form2: {
        rejectInfo: "",
        infoId: "",
      },
      rules2: {
        rejectInfo: [
          { required: true, message: "驳回原因不能为空", trigger: "blur" },
        ],
      },
      labelStyle: {
        textAlign: "center",
        backgroundColor: "rgb(242, 246, 255)",
      },
      // 表单校验
      rules: {
        expertName: [
          { required: true, message: "姓名不能为空", trigger: "change" },
        ],
        mobileTel: [{ required: true, validator: checkPhone, trigger: "blur" }],
      },
      // 手机号验证
    };
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
      district: (state) => state.controler.district,
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {
    checkedCities: {
      handler: function (val) {
        this.getSelectFn(val);
      },
      deep: true,
    },
  },
  filters: {
    filterStatus(val) {
      let str = "";
      switch (val) {
        case "0":
          str = "不通过";
          break;
        case "1":
          str = "通过";
          break;
        default:
          break;
      }
      return str;
    },
  },
  methods: {
    //查看详情
    getCompanyParticularJobFun(row) {
      getCompanyProjectFindById({ infoId: this.$route.query.infoId }).then(
        (res) => {
          this.baseInfoData = res.data.data;
        }
      );
    },
    //获取单选框
    clickRadio(val, index) {
      // console.log(val);
      val.isMenber = this.formRadio.radioArray[index];
    },
    //驳回按钮
    bacKInfoBtn() {
      this.dialogVisible3 = true;
    },
    //驳回
    backInfo() {
      this.$refs["ruleForm"].validate((valid) => {
        this.form2.infoId = this.$route.query.infoId;
        if (valid) {
          getCompanyProjectReject(this.form2).then((res) => {
            if (res.data.status === 200) {
              this.$message.success("驳回成功");
              this.dialogVisible3 = false;
              this.$router.push({
                path: `/dailySafetyManagement/threeDataReview`,
              });
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    //复制
    clickLink(item) {
      // console.log(item.expertMessage, "复制内容");
      let oInput = document.createElement("input");
      //这边为链接地址this.liveLink='www.baidu.com'
      var splitQuer = "";
      if (item.expertMessage) {
        var splitAry = item.expertMessage.split("，");
        splitQuer = splitAry[2].split("：")[1];
      }
      oInput.value = splitQuer;
      document.body.appendChild(oInput);
      oInput.select();
      // console.log(oInput.value);
      document.execCommand("Copy");
      oInput.remove();
      this.$message({
        message: "复制成功",
        type: "success",
      });
    },
    //查询
    serchClick() {
      this.currentPage = 1;
      this.getList();
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getList();
    },
    //获取专家电话
    getList() {
      this.loading = true;
      getExpertListData({
        keywords: this.filterText,
        nowPage: this.currentPage,
        orgCode: "",
        pageSize: 10,
        range: 0,
        startTime: "",
        tenantId: "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          var aaa = [];
          if (res.data.data.list.length > 0) {
            res.data.data.list.forEach((item, index) => {
              var listRadio = [
                { id: "11", anames: "专家组组长", pcStatus: "11" },
                { id: "12", anames: "专家组成员", pcStatus: "12" },
              ];
              item.list = listRadio;
              item.isMenber = "12";

              item.list.forEach((it) => {
                if (it.pcStatus == 12) {
                  aaa.push(it.id);
                }
              });
              // if (aaa.length !== index + 1) {
              //   aaa.push(null);
              // }
            });
            this.formRadio.radioArray = aaa;
            this.cities = res.data.data.list;
            this.total = res.data.data.total;
            // let pageNow=this.currentPage
            // this.objPerson.push({
            //   [pageNow]:this.cities
            // })
            // this.objPerson.push(res.data.data.list)
            const abc = [...res.data.data.list, ...this.filter0bjPerson];
            // console.log(this.objPerson);
            abc.forEach((item, index) => {
              if (
                !this.filter0bjPerson.some((e) => e.expertId == item.expertId)
              ) {
                this.filter0bjPerson.push(item);
              }
            });

            // console.log(
            //   this.filter0bjPerson,
            //   "记录每次点击的数据并赋值到右侧解决翻页"
            // );
          }
        }
      });
    },
    //获取专家电话-不分页
    getListAll() {
      this.loading = true;
      getExpertListData({
        keywords: this.filterText,
        nowPage: 1,
        orgCode: "",
        pageSize: 9999,
        range: 0,
        startTime: "",
        tenantId: "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          var aaa = [];
          if (res.data.data.list.length > 0) {
            this.citiesAll = res.data.data.list;
          }
        }
      });
    },
    clickReport() {
      // this.dialogVisibleReport = true;
      this.$router.push({
        path: "/dailySafetyManagement/pdfSignature",
        query: {
          infoId: this.$route.query.infoId,
          approvalStatus: this.$route.query.approvalStatus,
        },
      });
    },
    getSelectFn(val) {
      this.rightData = this.toData(this.filter0bjPerson, val);
    },

    toData(arr1, arr2) {
      // console.log(1);
      var ret = [];
      arr2.forEach((ele) => {
        let findEle = arr1.find((x) => x.expertId === ele); // 如果在arr1中找到,添加到arr1中
        findEle ? ret.push(findEle) : "";
      });
      return ret;
    },
    //删除
    removePerson(el) {
      if (el.expertId != "") {
        this.checkedCities = this.checkedCities.filter((item) => {
          return item != el.expertId;
        });
        //删除右边
        this.rightData = this.rightData.filter((index) => {
          return index.expertId != el.expertId;
        });
      } else {
        //删除右边
        this.rightData = this.rightData.filter((index) => {
          return index.addId != el.addId;
        });
      }
    },
    //添加
    clickAdd() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          //把数据追加到 cities { id: "1", name: "王党海", phone: "18569335649" },
          // <span class="name">{{ item.expertName }}</span> /
          // <span class="phone">{{ item.expertTelephone }}</span>
          //  var listRadio = [
          //     { id: "11", anames: "专家组组长", pcStatus: null },
          //     { id: "12", anames: "专家组成员", pcStatus: null },
          //   ];
          //   item.list = listRadio;
          //   item.isMenber=''
          var isPhone = this.rightData.filter((item) => {
            return item.mobileTel == this.form.mobileTel;
          });
          if (isPhone.length > 0) {
            this.$message.info("不能添加重复的手机号");
            return false;
          }

          this.rightData.push({
            id: "",
            addId: this.generateId(),
            expertId: "",
            expertName: this.form.expertName,
            mobileTel: this.form.mobileTel,
            list: [
              { id: "11", anames: "专家组组长", pcStatus: "11" },
              { id: "12", anames: "专家组成员", pcStatus: "12" },
            ],
            isMenber: "12",
          });
          // console.log(this.rightData);
        }
      });
    },
    generateId() {
      const s = [];
      const hexDigits = "0123456789abcdef";
      for (let i = 0; i < 36; i++) {
        s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
      }
      // bits 12-15 of the time_hi_and_version field to 0010
      s[14] = "4";
      // bits 6-7 of the clock_seq_hi_and_reserved to 01
      s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1);
      s[8] = s[13] = s[18] = s[23] = "-";

      const uuid = s.join("");
      return uuid;
    },

    getProjectApprovalStart() {
      if (this.rightData.length == 0) {
        this.$message.info("请选择或添加复查数据");
        return false;
      }
      //  var leadersNoAll = this.rightData.filter((itemNO) => {
      //     return itemNO.isMenber == "";
      //   });
      //   if (leadersNoAll.length > 0) {
      //     this.$message.info("必须选择专家或组员");
      //     return false;
      //   }
      var leaders = this.rightData.filter((item) => {
        return item.isMenber == 11;
      });
      var members = this.rightData.filter((item2) => {
        return item2.isMenber == 12;
      });

      if (leaders.length == 0 || leaders.length > 1) {
        this.$message.info("必须有且只能选择一个专家组组长");
        return false;
      }
      if (members.length == 0) {
        this.$message.info("必须至少选择一个专家组成员");
        return false;
      }

      this.$confirm("确认发起复查？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let param = {
            infoId: this.$route.query.infoId,
            leaders: leaders, //专家组组长,组长
            members: members, //专家组成员
          };
          projectApprovalStart(param).then((res) => {
            debugger;
            if (res.data.status === 200) {
              this.linkData = res.data.data.expertListDTOs;
              this.dialogVisible = true;
              this.getCompanyParticularJobFun();
              this.reviewResultData();
            } else {
              this.$message.error(res.data.msg);
            }
            // console.log(res, "发起复查弹框数据", res.data.expertListDTOs);
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    //发起复查
    sendReview() {
      // console.log(this.rightData);
      this.getProjectApprovalStart();
    },
    closeDialog() {
      this.dialogVisible = false;
    },
    //专家复核结果
    reviewResultData() {
      projectApprovalReviewResult({
        infoId: this.$route.query.infoId,
      }).then((res) => {
        if (res.data.status == 200) {
          this.reviewInfoData = res.data.data.headApprovals || []; //专家组组长
          this.memberApprovals = res.data.data.memberApprovals || []; //专家组成员
          this.reviewResultAllData = res.data.data;
          this.passFlag = res.data.data.passFlag;
        } else {
          this.isShow = false;
        }
      });
    },
    mounted() {
      this.getCompanyParticularJobFun();
      this.getList();
      // this.reviewResultData();
    },
  },
  async created() {},
};
</script>

<style lang="scss" scoped>
/deep/ .experComment div {
  text-align: left !important;
  align-items: self-start !important;
}
.expertReview {
  .commentStly {
    padding: 10px 0 0 0;
  }
  .exprertWenj {
    .exprertWenjBox {
      display: flex;
      align-items: center;
      > span {
        width: 42px;
      }
      .experComment {
        width: calc(100% - 42px);
        div {
          text-align: left !important;
        }
      }
    }
  }
  height: 72vh;
  div.expertReviewCon {
    display: flex;
    width: 100%;
  }
  .searchBox {
    padding: 0 15px;
  }
  .expertReviewL {
    // border-right: 1px solid #d9d9d9;
    width: 353px;
    padding: 15px 0 0 0;
    overflow: auto;
  }
  .expertReviewR {
    width: calc(100% - 353px);
    margin-top: 10px;
  }
  .groupBox {
    margin: 10px 0 0 0;
    overflow-y: auto;
    height: 59vh;
  }
  .rightTop {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    .el-form > div {
      display: flex;
    }
    .btnAdd {
      margin: 0 0 0 10px;
    }
  }
  .rightBottom {
    // display: flex;
    > span {
      width: 100px;
      display: inline-block;
      text-align: right;
      padding: 0 12px 0 0;
      position: absolute;
    }
    .itemHeight {
      height: 58vh;
      overflow-y: auto;
      border: 1px solid #d9d9d9;
      padding: 10px 0 0 10px;
      margin: 0 0 0 100px;
    }
    .itemBox {
      display: flex;
      flex-wrap: wrap;
      // border: 1px solid #d9d9d9;
      // padding: 10px 0 0 10px;
      // margin:0 0 0 100px;
      .itemCon {
        width: 280px;
        border: 1px solid #d9d9d9;
        // height: 38px;
        align-items: center;
        box-sizing: border-box;
        padding: 15px 15px 0 15px;
        justify-content: space-between;
        border-radius: 5px;
        margin: 0 10px 10px 0;
        .addClassStyle {
          display: flex;
          justify-content: space-between;
          padding: 0 0 5px 0;
        }
        .line {
          border-left: 1px solid #d9d9d9;
          margin: 0 15px;
        }
        i.el-icon-close {
          cursor: pointer;
        }
      }
    }
  }
  .rightBtn {
    margin-left: 100px;
    margin-top: 6px;
  }
  .reviewInfo {
    height: 67vh;
    overflow: auto;
    .titStatus {
      display: flex;
      height: 50px;
      align-items: center;
      padding: 0 0 0 15px;
    }
    .titStatus span {
      border-left: 4px solid #409eff;
      padding: 0 0 0 10px;
      color: #252525;
    }
    .reviewInfoCon {
      position: relative;
    }
    .reviewInfoItem {
      background: #f8f9fb;
      margin-bottom: 20px;
      margin-left: 40px;
      padding: 10px 0 10px 15px;
      position: relative;
      .itemT {
        display: flex;
        justify-content: space-between;
        color: #252525;
      }
      .name {
      }
      .line {
        border-left: 1px solid #d9d9d9;
        margin: 0 15px;
      }
      .phone {
      }
      .con {
        color: #707070;
        padding: 10px 0 0 0;
      }
      .time {
        color: #707070;
        padding: 0 15px 0 0;
      }
      .status {
        height: 30px;
        line-height: 30px;
        width: 80px;
        text-align: center;
        display: inline-block;
        border-radius: 5px;
        margin: 0 0 0 20px;
        background: #fef7e7;
        color: #faad14;
      }
      .status.status0 {
        background: #e7f4e4;
        color: #52c41a;
      }
      .status.status2 {
        background: #fef7e7;
        color: #faad14;
      }
      .status.status1 {
        background: #f8e9e8;
        color: #bf241f;
      }
    }
    .reviewInfoItem:before {
      content: "";
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #3470ff;
      left: -29px;
      top: 0;
      display: block;
      position: absolute;
      top: 19px;
    }
    .reviewInfoCon:before {
      content: "";
      position: absolute;
      left: 0;
      border-left: 1px solid #d9d9d9;
      display: block;
      height: 100%;
      left: 15px;
    }
    .btnBoxLast {
      position: fixed;
      bottom: 48px;
      left: 50%;
      display: flex;
      > div {
        margin: 0 10px;
      }
      /* margin-left: -90px; */
      /* width: 100%; */
      /* text-align: center; */
      /* left: 0; */
    }
  }
}
.zhuStyle.expertReview:before {
  border-left: 1px solid #d9d9d9;
  display: inline-block;
  content: "";
  position: absolute;
  top: 0;
  height: 100%;
  left: 350px;
}
/deep/ .searchBox .el-input__inner {
  height: 30px !important;
  line-height: 30px !important;
  border-right: 0 !important;
}
/deep/ .el-input-group__append,
/deep/ .el-input-group__prepend {
  background-color: #fff;
  color: #909399;
  cursor: pointer;
}
/deep/ .el-input-group__append:focus {
  border-color: #409eff;
}
/deep/ .el-checkbox {
  margin-right: 0;
  display: block;
  padding: 10px 0 10px 15px;
}
/deep/ .el-checkbox.is-checked {
  background: #eaf0ff;
}
/deep/ .el-form-item {
  margin: 0;
}

.el-checkbox span i {
  border-left: 1px solid #d9d9d9;
  margin: 0 20px;
}
.dialogLink {
  > p {
    color: #707070;
  }
  .linkInfoBox {
    margin: 0 0 20px 0;
    .linkCon {
      display: flex;
      align-items: center;
      .copyCon {
        border: 1px solid #d9d9d9;
        background: #f7f8fa;
        padding: 5px;
        color: #707070;
        width: 90%;
      }
      span.copyLink {
        margin: 0 0 0 15px;
        color: #3470ff;
        cursor: pointer;
      }
    }
  }
}
/deep/ .el-form-item__error {
  padding-top: 0;
}
/deep/ .el-form-item__content {
  margin-left: 0 !important;
}
/deep/ .el-form.ruleFormStly {
  display: flex;
}
</style>
