<template>
  <!-- <div v-if="detailAlarmDialog"> -->
    <!-- <div class="warn" ref="warn"> -->
      <!-- {{ detailAlarmDialog }} -->
      <el-dialog
        title="报警详情"
        :visible.sync="detailAlarmDialog"
        width="1000px"
        @close="closeBoolean()"
        destroy-on-close='true'
        :close-on-click-modal="false"
      >
        <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="监管通报" name="1"></el-tab-pane>
        <el-tab-pane label="企业反馈" name="0"></el-tab-pane>
      </el-tabs> -->
        <div class="warn">
          <!-- <div class="null" v-if="tableData == null"></div> -->
          <div class="div1">
            <div class="table">
              <ul class="container">
                <li class="lang">
                  <div class="l">企业名称</div>
                  <div class="r">{{ detailAlarmDialog }}</div>
                </li>
                <li class="lang">
                  <div class="l">企业编码</div>
                  <div class="r">{{ " --" }}</div>
                </li>
                <li class="lang">
                  <div class="l">企业负责人</div>
                  <div class="r">{{ " --" }}</div>
                </li>
                <li class="lang">
                  <div class="l">负责人联系电话</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
                <li class="lang">
                  <div class="l">当前状态</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
                <li class="lang">
                  <div class="l">电力户号</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
                <li class="lang">
                  <div class="l">报警类型</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
                <li class="lang">
                  <div class="l">规则说明</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
                <li class="lang bottom">
                  <div class="l">报警时间</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
                <li class="lang bottom">
                  <div class="l">持续天数</div>
                  <div class="r">
                    {{ "--" }}
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div
          class="echarts"
          id="detailEchart"
          style="width: 1000px; height: 250px"
        ></div>
      </el-dialog>
    <!-- </div> -->
  <!-- </div> -->
</template>
<script>
import {
  postCimEarlyWarningWarningDetails,
  getWarningNoticeList,
  getCimEarlyWarningFeedBackInfo,
} from "@/api/riskAssessment";
export default {
  //import引入的组件
  name: "warn111",
  components: {},
  props: {
    detailAlarmDialog: Boolean,
  },
  data() {
    return {
      show: false,
      tableData: {},
      activeName: "1",
      currentPage: 1,
      loading: false,
      height: "",
      total: 0,
      companyName: "",
    };
  },
  //方法集合
  methods: {
    chartInt() {
      var chartDom = document.getElementById("detailEchart");
      var myChart = this.$echarts.init(chartDom);
      var option;

      let base = +new Date(1968, 9, 3);
      let oneDay = 24 * 3600 * 1000;
      let date = [];
      let data = [Math.random() * 300];
      for (let i = 1; i < 20000; i++) {
        var now = new Date((base += oneDay));
        date.push(
          [now.getFullYear(), now.getMonth() + 1, now.getDate()].join("/")
        );
        data.push(Math.round((Math.random() - 0.5) * 20 + data[i - 1]));
      }
      option = {
        tooltip: {
          trigger: "axis",
          position: function (pt) {
            return [pt[0], "10%"];
          },
        },
        // title: {
        //   left: "center",
        //   text: "Large Area Chart",
        // },
        toolbox: {
          show: false,
          feature: {
            dataZoom: {
              yAxisIndex: "none",
            },
            restore: {},
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: date,
        },
        yAxis: {
          type: "value",
          boundaryGap: [0, "100%"],
        },
        dataZoom: [
          {
            type: "inside",
            start: 0,
            end: 10,
          },
          {
            start: 0,
            end: 10,
          },
        ],
        series: [
          {
            name: "Fake Data",
            type: "line",
            symbol: "none",
            sampling: "lttb",
            itemStyle: {
              color: "rgb(255, 70, 131)",
            },
            areaStyle: {
              color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "rgb(255, 158, 68)",
                },
                {
                  offset: 1,
                  color: "rgb(255, 70, 131)",
                },
              ]),
            },
            data: data,
          },
        ],
      };

      option && myChart.setOption(option);
    },
    closeBoolean(val) {
      this.$emit("closeCenterDialog", false);
      // this.show = val;
      // this.activeName = "1";
      // this.tableData = {};
      // this.currentPage = 1;
    },
    // submit() {},
    getData(id, companyCode, companyName) {
      this.warningId = id;
      this.companyCode = companyCode;
      this.companyName = companyName;
      this.loading = true;
      getWarningNoticeList({
        warningId: this.warningId,
        size: 1,
        current: this.currentPage,
      }).then((res) => {
        // console.log(res);
        if (res.data.data.records.length > 0) {
          this.tableData = res.data.data.records[0];
          this.total = res.data.data.total;
          this.height = "auto";
        } else {
          this.tableData = null;
          this.height = "350px";
        }
        // console.log(this.tableData);
        this.loading = false;
      });
    },

    handleClick() {
      this.loading = true;
      if (this.activeName == 0) {
        getCimEarlyWarningFeedBackInfo(this.warningId).then((res) => {
          if (res.data.data) {
            this.tableData = res.data.data;
            this.total = 0;
            this.height = "auto";
          } else {
            this.tableData = null;
            this.height = "350px";
          }
          // console.log(this.tableData);
          this.loading = false;
        });
      } else {
        this.getData(this.warningId, this.companyCode);
      }
    },
  },
  activated () {
      // debugger
　　　console.log('实例被激活时使用，用于重复激活一个实例的时候')

　　},
  //生命周期 - 挂载完成（可以访问DOM元素）
  created() {
    // debugger;
    // this.handleClick();
    this.chartInt();
  },
  mounted() {
    // debugger;
    // this.handleClick();
    this.chartInt();
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 10px;
}
.warn {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .null {
    width: 467px;
    height: 285px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 50%;
          display: flex;
          // border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 30%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 70%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }

}
</style>