<template>
  <div class="enterpriseManagement" v-loading="loading">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item v-if="vuexUser.user_type == 'gov'">
            <span  @click="goEnt">
              <a-icon type="home" theme="filled" class="icon"  /> 园区管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            园区详情</a-breadcrumb-item
          >
        </a-breadcrumb>
      </div>
     
      <div class="newEnterprise">
        <div class="newEnterpriseL">
          <div class="titH2">
            <span>{{ topInfo.parkName }}</span>
          </div>
          <!-- <div class="titCon">
            <span>法人：</span>           
            &nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp; 
            <span>地址：</span>
          </div> -->
        </div>
        <!-- <div class="newEnterpriseR">
          <div :class="['safeStatus' + riskGrade]"></div>
        </div> -->
      </div>
    </div>
    <div class="tabs">
      <!-- {{this.$store.state.login.parkActiveName}}---------111111
      {{this.activeName}}---------activeName
      {{this.active}}-----------active -->
     <el-tabs v-model="activeName" @tab-click="handleClick" :key="key">
        <el-tab-pane label="园区信息" name="basicInformationTab">
          <BasicInformation            
            ref="basicInformation"
          ></BasicInformation>
        </el-tab-pane>

        <el-tab-pane label="在园企业" name="parkEnterList">
          <ParkEnterList  ref="parkEnterList" :parkId='parkId'></ParkEnterList>
        </el-tab-pane>

        <el-tab-pane  label="园区视频" name="videoInspection" >
          <VideoInspection
            ref="videoInspection"
            :showVideo="showVideo"
            :key="videoInspectionKey"
          ></VideoInspection>
        </el-tab-pane>
        <!-- <el-tab-pane v-if="level" label="物联监测" name="realTimeMonitoring">
          <RealTimeMonitoring ref="realTimeMonitoring"></RealTimeMonitoring>
        </el-tab-pane> -->
        
        <!-- :disabled="MapData.length>0" -->
        <el-tab-pane  label="三维视图" name="threeDimensionalView"  :disabled="MapData.length > 0?false:true"
          ><ThreeDimensionalView ref="threeDimensionalView" ></ThreeDimensionalView
        ></el-tab-pane>
        <!-- <el-tab-pane v-if="level" label="安全承诺" name="safetyCommitment">
          <SafetyCommitment ref="safetyCommitment" :name="name" />
        </el-tab-pane> -->

        <!-- <el-tab-pane v-if="level" label="物联报警" name="IotDetection">
          <Lot ref="IotDetection"></Lot>
        </el-tab-pane> -->
        <!-- <el-tab-pane v-if="level" label="报警分析" name="alarmStatistical">
          <Alarm ref="alarmStatistical"></Alarm>
        </el-tab-pane> -->
        <!-- <el-tab-pane
          v-if="level"
          label="设备设施"
          name="equipmentAndFacilities"
        >
          <EquipmentAndFacilities
            ref="equipmentAndFacilities"
          ></EquipmentAndFacilities>
        </el-tab-pane> -->
        <!-- <el-tab-pane v-if="level" label="监测设备" name="monitoringEquipment">
          <MonitoringEquipment ref="monitoringEquipment"></MonitoringEquipment>
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>
<script>
import BasicInformation from "./parkManagement/basicInformation.vue";
import VideoInspection from "./parkManagement/videoInspection/index";
import ParkEnterList from "./parkManagement/parkEnterList"; //在园企业
import ThreeDimensionalView from "@/components/feature/enterpriseManagement/threeDimensionalView/index.vue";



import { getInformationInfo,getParkView } from "@/api/entList";
import { createNamespacedHelpers, mapState } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import { getCimRiskAnalysisCimRiskDetailsList } from "@/api/workingAcc";

export default {
  name: "entManagement",
  components: {
    BasicInformation,
    VideoInspection,
    ParkEnterList, 
    ThreeDimensionalView
  },
  props: {
    parkId:String,
    // MapData:Array
  },
  // props:['parkId'],
  
  data() {
    return {
      MapDataDisable:false,
      MapData:[],
      riskGrade: "",
      key: "",
      activeName:this.$store.state.login.parkActiveName,
      enterpriseName: "",
      isEnterprise: "",
      showVideo: false,
      enterpriseId: this.parkId,
      ent: "",
      address: "",
      name: "",
      tel: "",
      level: 1,
      loading: false,
      active: this.$store.state.login.parkActiveName, //this.activeF  父组件没有传值？？？
      safeStatus: "",
      showTab: true,
      videoInspectionKey: "",
      routerName: "",
      ///ht/bgmesh/fiddler/3608/service/76a4352c-d267-40a4-821c-24b093411f6b/0/tileset.json?apaasToken=TElKSUFTSFVBSV8zNjA4XzE4MzhfYTRiYjYyNTNiYzA1NDE5Nzg3NmE3MjE2YzZlNDUyYzE%3D
      // MapData:[],
      topInfo:{}
    };
  },
  methods: {   
    
    handleClick(tab, event) {    
      this.showVideo = tab.name === "videoInspection";
      switch (tab.name) {
        case "parkEnterList": //园区企业
          this.$nextTick(() => {
            this.$refs.parkEnterList.getData();
          });
          break;
        case "basicInformationTab": //基本信息
          this.$nextTick(() => {
            this.$refs.basicInformation.getData(this.enterpriseId); //this.enterpriseId
          });
          break;

        case "threeDimensionalView": //基本信息
          this.$nextTick(() => {
            this.$refs.threeDimensionalView.loadMapToCenter(this.MapData); //this.enterpriseId
          });
          break;

        case "videoInspection": //视频
          this.videoInspectionKey = new Date() + "key";
          this.$nextTick(() => {
            this.$refs.videoInspection.getData(this.enterpriseId);
            this.$refs.videoInspection.getVideoNum(this.enterpriseId);
            this.$refs.videoInspection.initVideoNew();
            // this.$refs.videoInspection.initVideo()
            // this.$refs.videoInspection.initializationVideo();
          });
          break;
        default:
          break;
      }
    },
    goEnt() {
      this.enterpriseName = "";
      this.tel = "";
      this.address = "";
      this.ent = "";
      this.name = "";
      this.level = "";
      this.safeStatus = "";
      this.key = new Date().getTime() + "_key";
      this.$emit("type", "gov");
    },
    getData() { 
      this.active = this.$store.state.login.parkActiveName;    
      if (this.vuexUser.user_type === "park") {
        const res = this.parkData;
        this.setTab(res);
      } else {
        this.getInformationInfoFun();
      }
    },
    setTab(res) {
      this.topInfo=res
      this.$nextTick(() => {      
         this.showVideo = this.active === "videoInspection";        
          switch (this.active) {
            case "parkEnterList": //园区企业
              this.$nextTick(() => {
                this.$refs.parkEnterList.getData(this.enterpriseId);
              });
              break;
            case "basicInformationTab":
              this.activeName = this.active;
              this.$refs.basicInformation.getData(this.enterpriseId);
              break;
             case "videoInspection":
              console.log(this.active);
              this.videoInspectionKey = new Date() + "key";
              this.activeName = this.active;
              this.$nextTick(() => {
                this.$refs.videoInspection.getData(this.enterpriseId);
                this.$refs.videoInspection.getVideoNum(this.enterpriseId);
                this.$refs.videoInspection.initVideoNew();
                // this.$refs.videoInspection.initializationVideo();
              });
              break;          
          }
        setTimeout(() => {
          this.loading = false;
        }, 500);
      });
    },
    getInformationInfoFun() {    
      // this.loading = true;    
      getParkView(this.enterpriseId).then((res) => {
        this.setTab(res.data.data);
      });
     
    },
  },
  computed: {
    ...mapStateControler({
      entModelName: (state) => state.entModelName,
    }),
    ...mapStateLogin({
      vuexUser: (state) => state.user,
    }),
    ...mapState({
      enterData: (state) => state.login.enterData,
      parkData:(state) => state.login.park,
    }),
  },
  watch: {
    parkData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.enterpriseId = newVal.parkId;
        }
      },
      deep: true,
      immediate: true,
    },
    parkId: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.enterpriseId = newVal;
        }
      },
      deep: true,
      immediate: true,
    },
    entModelName: {
      handler(newVal, from) {
        this.showTab = !newVal;
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    console.log(this.$store.state, "获取tab状态");
    this.activeName = this.$store.state.login.parkActiveName;
  },
  destroyed() {
    // this.$store.commit("login/updataActiveName", "basicInformationTab");
    // this.$store.commit("login/updataActiveName", this.activeName);
    this.$store.commit("login/updataParkActiveName", 'parkEnterList');
  },
};
</script>
<style lang="scss" scoped>
.newEnterprise {
  background: url("../../../../static/img/riskbg.png") no-repeat top;
  background-size: 100% 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 20px;
  color: #fff;
  min-height: 100px;
  .titH2 span {
    color: #fff;
    text-align: left;
    font-size: 20px;
    font-weight: bold;
    display: inline-block;
    padding: 0 0 5px;
  }
  button.el-button.el-tooltip.botton.item.el-button--text {
    text-align: left;
  }
  .safeStatusnull,
  .safeStatus {
    background: url("../../../../static/img/蓝-低风险.png") no-repeat top right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus1 {
    background: url("../../../../static/img/红-重大风险.png") no-repeat top
      right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus2 {
    background: url("../../../../static/img/橙-较大风险.png") no-repeat top
      right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus3 {
    background: url("../../../../static/img/黄-一般风险.png") no-repeat top
      right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus4 {
    background: url("../../../../static/img/蓝-低风险.png") no-repeat top right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
}
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
    .enterprise {
      display: flex;
      justify-content: space-between;
    }
    .enterpriseL {
      // width: 500px;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-left: 10px;
      .Grade {
        color: #fff;
        padding: 0px 5px;
        margin-right: 10px;
        font-size: 14px;
        float: right;
        width: 93px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 4px;
      }
      .name {
        .botton {
          height: 42px;
          font-size: 20px;
          flex-wrap: 600;
          color: #000;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          text-align: left;
        }
      }
    }
    .enterpriseR {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 650px;
      height: 40px;
      margin-right: 15px;
      background: #f3f8fd;
      border: 1px solid #f1f5fa;
      border-radius: 20px;
      & > div {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25%;
        border-right: 1px #cfdee9 solid;
        // height: 20px;
      }
      & > div:nth-last-of-type(1) {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25%;
        border-right: 0px #cfdee9 solid;
      }
      .ent {
        background: url("../../../../static/icon/jy.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .address {
        background: url("../../../../static/icon/address.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .name {
        background: url("../../../../static/icon/name.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .tel {
        background: url("../../../../static/icon/tel.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
    }
  }
  .tabs {
    padding-top: 10px;
    background-color: #fff;
  }
}
</style>
