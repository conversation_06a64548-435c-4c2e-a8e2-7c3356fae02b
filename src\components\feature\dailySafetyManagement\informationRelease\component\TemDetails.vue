<template>
  <div class="app-container">
    <!-- 添加或修改模板配置对话框 -->
    <el-dialog
      title="模板详情"
      :visible.sync="open"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-scrollbar style="height: 60vh" class="auto-scrollbar">
        <div class="cborder mrb-20">
          <div class="flex cborder-bottom">
            <div class="cleftBox width-20 cborder-right">模板名称：</div>
            <div class="crightBox width-80">{{ form.tmpltName }}</div>
          </div>

          <div class="flex cborder-bottom">
            <div class="cleftBox width-20 cborder-right">类型：</div>
            <div class="crightBox width-80">
              {{ form.tmpltType == 0 ? "安全预警" : "宣传教育" }}
            </div>
          </div>

          <div class="flex">
            <div class="cleftBox width-20 cborder-right">模板内容：</div>
            <div class="crightBox width-80">{{ form.tmpltContent }}</div>
          </div>
        </div>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script>
// import {
//   listTemple,
//   getTemple,
//   delTemple,
//   addTemple,
//   updateTemple,
//   exportTemple,
// } from "@/api/temple/temple";
// import Editor from "@/components/Editor";

import { getTempleList } from "../../../../../api/informationRelease";

export default {
  name: "TemDetails",
  components: {},
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板配置表格数据
      templeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tmpltName: null,
        tmpltType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        tmpltName: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: ["blur", "change"],
          },
        ],
        tmpltType: [
          {
            required: true,
            message: "请选择模板类型",
            trigger: ["blur", "change"],
          },
        ],
        tmpltContent: [
          {
            required: true,
            message: "请输入模板内容",
            trigger: ["blur", "change"],
          },
        ],
      },
      tempalteKindList: [
        { label: "安全预警", value: "0" },
        { label: "宣传教育", value: "1" },
      ],
    };
  },
  created() {},
  mounted() {
    this.getList();
  },
  methods: {
    /** 查询模板配置列表 */
    getList() {
      this.loading = true;
      getTempleList(this.queryParams).then((response) => {
        this.templeList = response.data.data;
        this.total = response.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        tmpltCode: null,
        tmpltName: null,
        tmpltType: null,
        tmpltStatus: "0",
        tmpltContent: null,
        issueUnitCode: null,
        createUser: null,
        createTime: null,
        updateUser: null,
        updateTime: null,
        instCode: null,
        deleteFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.tmpltCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加模板配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const tmpltCode = row.tmpltCode || this.ids;
      getTemple(tmpltCode).then((response) => {
        this.form = response.data;
        this.open = true;
        this.title = "修改模板配置";
      });
    },
    /** 提交按钮 */
    submitForm(type) {
      this.form.flag = type;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.tmpltCode != null) {
            updateTemple(this.form).then((response) => {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTemple(this.form).then((response) => {
              this.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tmpltCodes = row.tmpltCode || this.ids;
      this.$confirm("是否确认删除选中的数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return delTemple(tmpltCodes);
        })
        .then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有模板配置数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportTemple(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
};
</script>
