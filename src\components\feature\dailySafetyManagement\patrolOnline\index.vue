<template>
  <div class="patrolOnline">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              排查整治分析（巡查）
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.name"
      >
        <keep-alive :include="item.name">
          <component :is="item.name" :ref="item.name"></component>
        </keep-alive>
      </el-tab-pane>
    </el-tabs>
    <!-- <iframe src="http://*************:36080/freelogin/guizhou/" seamless align="center" frameborder="0" width="100%" height="100%"></iframe> -->
  </div>
</template>

<script>
import PatrolOnline from "./patrolOnline";
import HistoryPatrolCheck from "./historyPatrolCheck";
import InspectFeedback from "./inspectFeedback";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  name: "patrolOnline",
  components: {
    PatrolOnline,
    HistoryPatrolCheck,
    InspectFeedback,
  },
  data() {
    return {
      activeName: "PatrolOnline",
      tabs: [],
    };
  },
  created() {
    //distRole:  省级：0/地市：1/区县：2
    switch (this.user.distRole) {
      case "0":
        this.tabs = [
          {
            label: "在线巡查",
            name: "PatrolOnline",
          },
          {
            label: "历史巡查记录",
            name: "HistoryPatrolCheck",
          },
          {
            label: "部巡查反馈",
            name: "InspectFeedback",
          },
        ];
        break;
      case "1":
        this.tabs = [
          {
            label: "在线巡查",
            name: "PatrolOnline",
          },
          {
            label: "历史巡查记录",
            name: "HistoryPatrolCheck",
          },
          {
            label: "政府巡查反馈",
            name: "InspectFeedback",
          },
        ];
        break;
      case "2":
        this.tabs = [
          {
            label: "政府巡查反馈",
            name: "InspectFeedback",
          },
        ];
        break;
      default:
        break;
    }
  },
  mounted() {
    this.initalize();
    // this.activeName = this.$store.state.controler.entPatroName || 'PatrolOnline'
    if (this.user.distRole == "0" || this.user.distRole == "1") {
      this.activeName =
        this.$store.state.controler.entPatroName || "PatrolOnline";
    } else {
      this.activeName =
        this.$store.state.controler.entPatroName || "InspectFeedback";
    }
  },
  computed: {
    ...mapStateLogin({
      user: (state) => state.user,
    }),
    ...mapStateControler({
      entPatroName: (state) => state.entPatroName,
    }),
  },
  //   watch: {
  //     entPatroName: {
  //       handler(newVal, oldVal) {
  //         this.activeName = this.entPatroName;
  //       },
  //       deep: true,
  //       // immediate: true,
  //     },
  //   },
  methods: {
    initalize() {
      if (this.user.distRole != "2") {
        this.activeName = "PatrolOnline";
      } else {
        this.activeName = "InspectFeedback";
      }
      this.$nextTick(() => {
        this.handleClick();
      });
    },
    fatherMethod() {
      // debugger
      this.activeName = "HistoryPatrolCheck";
      this.$refs.HistoryPatrolCheck[0].onceShowSpotCheck();
    },
    // handleClick(tab, event) {
    //   return new Promise((resolve, reject) => {
    //     console.log(this.$refs[this.activeName]);
    //     resolve(this.$refs[this.activeName][0].getList());
    //   });
    // },
    handleClick(tab, event) {
      //   if(this.$refs[this.activeName]){
      //     this.$refs[this.activeName][0].getList();
      //   }else{
      //       console.log(this.$refs.InspectFeedback[0])
      //       console.log(this.activeName)
      //      return new Promise((resolve, reject) => {
      //         console.log(this.$refs[this.activeName]);
      //         resolve(this.$refs.InspectFeedback[0].getList());
      //     });
      //   }
      console.log(this.activeName);
      this.$refs[this.activeName][0].getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.patrolOnline {
  height: 100%;
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }
}
</style>
