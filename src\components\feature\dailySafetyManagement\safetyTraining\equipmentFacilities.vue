<template>
  <div class="equipmentFacilities">
    <div>
      <div class="seach-part">
        <div class="l"
             style="display: inline-block;">
          <el-autocomplete popper-class="my-autocomplete"
                           v-model="queryParams.orgname"
                           :fetch-suggestions="querySearch"
                           placeholder="请选择/输入承办单位"
                           clearable
                           @clear="clearSensororgCode()"
                           @select="handleSelect"
                           size="mini"
                           v-if="$store.state.login.user.user_type == 'gov'">
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete>
        </div>
        <el-input v-model.trim="queryParams.trainingName"
                  placeholder="请输入培训名称"
                  clearable
                  size="mini"
                  @keyup.enter.native="handleQuery"
                  style="width: 200px" />
        <el-date-picker v-model="queryParams.startTime"
                        clearable
                        type="date"
                        size="mini"
                        value-format="yyyy-MM-dd"
                        placeholder="选择培训开始时间"
                        style="width: 200px" />

        <el-button type="primary"
                   size="mini"
                   @click="handleQuery">查询</el-button>
        <CA-button type="primary"
                   size="mini"
                   plain
                   @click="handleExport">导出</CA-button>
        <el-button type="primary"
                   @click="handleAdd"
                   style="float:right"
                   v-if="$store.state.login.user.user_type == 'ent'"
                   size="mini">新增</el-button>
      </div>
    </div>
    <div class="table-main">
      <div class="table-top">
        <h2>安全培训列表</h2>
      </div>
      <div>
        <div class="table">
          <el-table :data="safetyTrainingList"
                    v-loading="loading"
                    :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                    border
                    style="width: 100%"
                    ref="multipleTable"
                    @selection-change="handleSelectionChange">
            <el-table-column label="培训名称"
                             align="center"
                             prop="trainingName" />
            <el-table-column label="承办单位"
                             align="center"
                             prop="organizer" />
            <el-table-column label="培训形式"
                             align="center"
                             prop="trainingForm">
              <template slot-scope="scope">
                <span v-if="scope.row.trainingForm == '1'">线下培训</span>
                <span v-else>线上培训</span>
              </template>
            </el-table-column>
            <el-table-column label="参训人数"
                             align="center"
                             prop="noOfPtcpt" />
            <el-table-column label="培训开始时间"
                             align="center"
                             prop="trainingStTime"
                             width="180">
              <!-- <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.startTime, "{y}-{m}-{d}") }}</span>
                    </template> -->
            </el-table-column>
            <el-table-column label="培训结束时间"
                             align="center"
                             prop="trainingEndTime"
                             width="180">
              <!-- <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.endTime, "{y}-{m}-{d}") }}</span>
                    </template> -->
            </el-table-column>
            <el-table-column label="操作"
                             align="center"
                             class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button round
                           size="mini"
                           @click="handleUpdate(scope.row)"
                           v-if="$store.state.login.user.user_type == 'ent'">编辑</el-button>
                <el-button round
                           size="mini"
                           @click="handleDetail(scope.row)">查看</el-button>
                <el-button round
                           size="mini"
                           class="del"
                           @click="handleDelete(scope.row)"
                           v-if="$store.state.login.user.user_type == 'ent'">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="queryParams.nowPage"
                         background
                         layout="total, prev, pager, next"
                         :total="total"
                         v-if="total != 0">
          </el-pagination>
        </div>

        <!-- 添加或修改安全培训对话框 -->
        <el-dialog :title="title"
                   :visible.sync="open"
                   width="750px"
                   :close-on-click-modal="false"
                   :append-to-body="true"
                   top="5vh">
          <div class="container">
            <el-form ref="form"
                     :model="form"
                     :rules="rules"
                     :disabled="disabled"
                     :hide-required-asterisk="disabled">
              <!-- <el-form-item label="承办单位"
                            prop="corpCode"> -->
                <!-- <el-input v-model="form.organizer" placeholder="请输入承办单位" /> -->
                <!-- <el-select v-model="form.organizer" placeholder="请选择承办单位">
                    <el-option
                    v-for="dict in whpEntList"
                    :key="dict.orgCode"
                    :label="dict.orgName"
                    :value="dict.orgCode"
                    />
                </el-select> -->
                <!-- <el-autocomplete popper-class="my-autocomplete"
                                 v-model="form.orgName"
                                 :fetch-suggestions="querySearch"
                                 placeholder="请选择/输入承办单位"
                                 clearable
                                 @clear="clearSensororgCodes()"
                                 @select="handleSelects"
                                 style="width: 100%">
                  <template slot-scope="{ item }">
                    <div class="name">{{ item.enterpName }}</div>
                  </template>
                </el-autocomplete> -->
              <!-- </el-form-item> -->
               <el-form-item label="承办单位"
                            prop="organizer">
                <el-input v-model="form.organizer"
                disabled
                          
                           />
              </el-form-item>
              <el-form-item label="培训名称"
                            prop="trainingName">
                <el-input v-model.trim="form.trainingName"
                          placeholder="请输入培训名称"
                          maxlength="20" />
              </el-form-item>
              <el-form-item label="培训形式"
                            prop="trainingForm">
                <el-select v-model="form.trainingForm"
                           placeholder="请选择培训形式"
                           style="width: 100%">
                  <el-option v-for="dict in trainingFormOptions"
                             :key="dict.dictValue"
                             :label="dict.dictLabel"
                             :value="dict.dictValue" />
                </el-select>
              </el-form-item>
              <el-form-item label="参训人数"
                            prop="noOfPtcpt">
                <el-input v-model.trim="form.noOfPtcpt"
                          placeholder="请输入参训人数"
                          maxlength="5" />
              </el-form-item>
              <el-form-item label="培训开始时间"
                            prop="trainingStTime">
                <el-date-picker v-model="form.trainingStTime"
                                clearable
                                style="width: 100%"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择培训开始时间"
                                :picker-options="pickerOptions0" />
              </el-form-item>
              <el-form-item label="培训结束时间"
                            prop="trainingEndTime">
                <el-date-picker v-model="form.trainingEndTime"
                                clearable
                                style="width: 100%"
                                type="datetime"
                                value-format="yyyy-MM-dd HH:mm:ss"
                                placeholder="选择培训结束时间"
                                :picker-options="pickerOptions0" />
              </el-form-item>
              <el-form-item label="培训费用"
                            prop="trainingExps">
                <el-input v-model.trim="form.trainingExps"
                          placeholder="请输入培训费用"
                          maxlength="10">
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
              <el-form-item label="培训导师 "
                            prop="trainer">
                <el-input v-model.trim="form.trainer"
                          placeholder="请输入培训导师 "
                          maxlength="10" />
              </el-form-item>
              <el-form-item label="负责人"
                            prop="pinCharge">
                <el-input v-model.trim="form.pinCharge"
                          placeholder="请输入负责人"
                          maxlength="10" />
              </el-form-item>
              <el-form-item label="联系电话"
                            prop="phone">
                <el-input v-model.trim="form.phone"
                          placeholder="请输入联系电话" />
              </el-form-item>
              <!-- <el-form-item label="创建用户" prop="createUser">
                <el-input v-model="form.createUser" placeholder="请输入创建用户" />
                </el-form-item>
                <el-form-item label="更新用户" prop="updateUser">
                <el-input v-model="form.updateUser" placeholder="请输入更新用户" />
                </el-form-item>-->
              <!-- <el-form-item label="机构代码" prop="instCode">
                <el-input v-model="form.instCode" placeholder="请输入机构代码" />
                </el-form-item>-->
              <el-form-item label="参训人员"
                            prop="ptcpt">
                <el-input v-model.trim="form.ptcpt"
                          placeholder="请输入参训人员"
                          maxlength="10" />
              </el-form-item>
              <el-form-item label="培训总结"
                            prop="trainingSummary">
                <el-input v-model.trim="form.trainingSummary"
                          placeholder="请输入培训总结"
                          type="textarea"
                          maxlength="70" />
              </el-form-item>
              <el-form-item v-if="!disabled || form.picAttachmentList"
                            label="培训照片">
                <!-- <imageUpload v-model="form.trainingPhoto"
                             accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.JPG,.JPEG,.PNG,.GIF,.BMP,.WEBP"
                             :disabled="disabled" /> -->
                <AttachmentUpload :attachmentlist="form.picAttachmentList"
                                  :limit="5"
                                  type="img"
                                  v-bind="{}"
                                  :editabled="disabled"></AttachmentUpload>
              </el-form-item>
              <el-form-item v-else
                            label="培训照片">暂无照片</el-form-item>
              <el-form-item label="附件">
                <!-- <fileUpload v-model="form.attachmentCode" :disabled="disabled" /> -->
                <AttachmentUpload :attachmentlist="form.list"
                                  :limit="1"
                                  type="office"
                                  v-bind="{}"
                                  :editabled="disabled"></AttachmentUpload>
              </el-form-item>
            </el-form>
          </div>
          <div v-if="!disabled"
               slot="footer"
               class="dialog-footer">
            <el-button type="primary"
                       @click="submitForm">确 定</el-button>
            <el-button @click="cancel">取 消</el-button>
          </div>
        </el-dialog>
      </div>
    </div>
  </div>
  </div>
</template>
<script>
import ImageUpload from '@/components/common/packages/ImageUpload'
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
import {
  getSafetyTrainingData,
  addSafetyTrainingData,
  editSafetyTrainingData,
  detailSafetyTrainingData,
  deleteSafetyTrainingData,
  exportSafetyTraining
} from '@/api/safetyTraining'
import { getSearchArr } from '@/api/entList.js'
import { parseTime } from '@/utils'
import { createNamespacedHelpers, mapState } from 'vuex'
import { debug } from 'util'
const { mapState: mapStateLogin } = createNamespacedHelpers('login')
// import Audit from "./audit";
// import AddEdit from "./addEdit";
// import Detail from "./detail";
// import LookImg from "./lookImg";

export default {
  name: 'equipmentFacilities',
  components: {
    ImageUpload,
    AttachmentUpload
    // Audit,
    // AddEdit,
    // Detail,
    // LookImg,
  },
  props: {
    entInfoData: {
      type: Object
    }
  },
  data() {
    var valiNumberPass1 = (rule, value, callback) => {
      // 包含小数的数字
      const reg = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g
      if (value === '') {
        callback(new Error('请输入内容'))
      } else if (!reg.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    var valiNumberPass2 = (rule, value, callback) => {
      // 正整数
      const reg = /^[+]{0,1}(\d+)$/g
      if (value === '') {
        callback(new Error('请输入内容'))
      } else if (!reg.test(value)) {
        callback(new Error('请输入0及0以上的整数'))
      } else {
        callback()
      }
    }
    // 手机号验证
    var checkPhone = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/
      if (!value) {
        return callback(new Error('联系电话不能为空'))
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error('请输入数字值'))
        } else {
          if (phoneReg.test(value)) {
            callback()
          } else {
            callback(new Error('联系电话格式不正确'))
          }
        }
      }, 100)
    }
    return {
      pickerOptions0: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      // 表单禁用
      disabled: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全培训表格数据
      safetyTrainingList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 培训形式字典
      trainingFormOptions: [
        {
          dictValue: '0',
          dictLabel: '线上培训'
        },
        {
          dictValue: '1',
          dictLabel: '线下培训'
        }
      ],
      // 培训形式字典
      whpEntList: [],
      // 查询参数
      queryParams: {
        nowPage: 1,
        pageSize: 10,
        corpCode: '',
        orgname: '',
        // trainingName: null,
        // startTime: null,
        // endTime: null,
        // instCode: null
        startTime: '',
        trainingName: ''
      },
      // 表单参数
      form: {
        list: [],
        picAttachmentList: []
      },
      // 表单校验
      rules: {
        trainingName: [
          {
            required: true,
            message: '培训名称不能为空',
            pattern: '[^ \x22]+',
            trigger: 'blur'
          }
        ],
        trainingForm: [
          { required: true, message: '培训形式不能为空', trigger: 'change' }
        ],
        trainingExps: [
          { required: true, trigger: 'blur', validator: valiNumberPass1 }
        ],
        noOfPtcpt: [
          { required: true, trigger: 'blur', validator: valiNumberPass2 }
        ],
        pinCharge: [
          { required: true, message: '负责人不能为空', trigger: 'blur' }
        ],
        phone: [{ required: true, validator: checkPhone, trigger: 'blur' }],
        ptcpt: [
          {
            required: true,
            message: '参训人员不能为空',
            pattern: '[^ \x22]+',
            trigger: 'blur'
          }
        ],
        trainingSummary: [
          {
            required: true,
            message: '培训总结不能为空',
            pattern: '[^ \x22]+',
            trigger: 'blur'
          }
        ],
        trainingStTime: [
          { required: true, message: '请选择培训开始时间', trigger: 'change' }
        ],
        trainingEndTime: [
          { required: true, message: '请选择培训结束时间', trigger: 'change' }
        ]
      },
      // 用户信息
      role: {},
      //   btnPermissList: [], // 按钮权限
      orgType: '2',
    }
  },
  computed: {
    ...mapState({
      user: state => state.login.user
    })
  },
  created() {
    // 判断是企业还是政务登录
    // this.queryParams.id = this.$route.query.id
    // this.role = JSON.parse(sessionStorage.getItem('role') || '')
    // this.orgType = this.role.orgType
    // if (this.orgType == '2') {
    //   // 当前用户是企业
    //   this.queryParams.organizer = this.role.orgCode
    // }
    if (this.$store.state.login.user.user_type == 'ent') {
      this.queryParams.corpCode = this.$store.state.login.user.org_code
      this.queryParams.orgname = this.$store.state.login.user.org_name
    }
    // this.btnPermissionsFun()
    this.getList()
    // this.getDicts('trainingForm').then(response => {
    //   this.trainingFormOptions = response.data
    // })
    // getEnterprise({ enterpriseName: '' }).then(res => {
    //   if (res.status === 200) {
    //     this.whpEntList = res.data
    //   }
    // })
  },
  methods: {
    // 获取按钮权限
    // btnPermissionsFun() {
    //   btnPer({
    //     menuId: JSON.parse(sessionStorage.getItem('currentMenu')).id,
    //     userId: JSON.parse(sessionStorage.getItem('role')).userId
    //   })
    //     .then(res => {
    //       this.btnPermissList = res.data
    //     })
    //     .catch(err => {
    //       console.log(err)
    //     })
    // },
    querySearch(queryString, cb) {
      let roleInfo = JSON.parse(sessionStorage.getItem('VueX_local')).root.login.user || {}
      if (roleInfo.user_type === 'ent') {
        queryString = roleInfo.org_name;
      }
      this.getSeachData(queryString || '', cb)
    },
    getSeachData(keyWord, cb) {
      console.log('keyWord--------->', keyWord, typeof keyWord)
      getSearchArr(keyWord)
        .then(res => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data)
            } else {
              cb([])
            }
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.corpCode = item.enterpId
      this.queryParams.orgname = item.enterpName
    },
    handleSelects(item) {
      this.form.corpCode = item.enterpId
      this.form.orgName = item.enterpName
    },
    /** 查询安全培训列表 */
    getList() {
      // if (this.$store.state.login.user.user_type == 'ent') {
      //   this.queryParams.orgCode = this.$store.state.login.user.org_code
      // }
      let roleInfo = JSON.parse(sessionStorage.getItem('VueX_local')).root.login.user || {}
      let params = null;
      if (roleInfo.user_type === 'ent') {
        params = {
          nowPage: this.queryParams.nowPage,
          pageSize: this.queryParams.pageSize,
          startTime: this.queryParams.startTime,
          trainingName: this.queryParams.trainingName
        }
      } else {
        params = this.queryParams
      }
      this.loading = true
      getSafetyTrainingData(params).then(response => {
        this.safetyTrainingList = response.data.data.list
        this.total = response.data.data.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        organizer:null,
        safetyTrainingCode: null,
        corpCode: null,
        orgName: null,
        trainingName: null,
        trainingForm: null,
        noOfPtcpt: null,
        startTime: null,
        trainingEndTime: null,
        trainingStTime: null,
        endTime: null,
        trainingExps: null,
        trainer: null,
        ptcpt: null,
        trainingSummary: null,
        pinCharge: null,
        phone: null,
        picAttachmentList: [],
        createUser: null,
        createTime: null,
        updateUser: null,
        updateTime: null,
        instCode: null,
        trainingPhoto: null,
        list: []
      }
      if (this.$refs['form'] != undefined) {
        this.$refs['form'].resetFields()
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.nowPage = 1
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.safetyTrainingCode)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.disabled = false
      this.reset()
      this.open = true
      this.form.organizer=this.$store.state.login.user.org_name
      this.title = '添加安全培训'
    },
    /**
     * 查看按钮操作
     */
    handleDetail(row) {
      this.reset()
      this.disabled = true
      const safetyTrainingCode = row.safetyTrainingCode || this.ids
      detailSafetyTrainingData({ id: safetyTrainingCode }).then(response => {
        this.form = response.data.data
        // this.form.startTime = response.data.data.trainingStTime;
        // this.form.endTime = response.data.data.trainingEndTime;
        this.open = true
        this.title = '查看安全培训'
      })
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      this.disabled = false
      const safetyTrainingCode = row.safetyTrainingCode || this.ids
      detailSafetyTrainingData({ id: safetyTrainingCode }).then(response => {
        this.form = response.data.data
        // this.form.startTime = response.data.data.trainingStTime;
        // this.form.endTime = response.data.data.trainingEndTime;
        this.open = true
        this.title = '修改安全培训'
      })
    },
    debounce(func, delay) {
      const context = this // this指向发生变化，需要提出来
      const args = arguments
      return (function() {
        if (context.timeout) {
          clearTimeout(context.timeout)
        }
        context.timeout = setTimeout(() => {
          func.apply(context, args)
        }, delay)
      })()
    },
    submitForm() {
      const that = this
      that.debounce(() => {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.form.safetyTrainingCode != null) {
              editSafetyTrainingData(this.form).then(response => {
                this.open = false
                this.$message({
                  type: 'success',
                  message: '修改成功'
                })
                this.getList()
              })
            } else {
              addSafetyTrainingData(this.form).then(response => {
                this.open = false
                this.$message({
                  type: 'success',
                  message: '新增成功'
                })
                this.getList()
              })
            }
          } else {
            this.$scrollToError()
          }
        })
      }, 500)
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const safetyTrainingCodes = row.safetyTrainingCode || this.ids
      this.$confirm('确认删除该数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function() {
          return deleteSafetyTrainingData({
            safetyTrainingCode: safetyTrainingCodes
          })
        })
        .then(() => {
          this.getList()
          //   this.msgSuccess('删除成功')
          this.$message({
            type: 'success',
            message: '删除成功'
          })
        })
    },
    /** 导出按钮操作 */
    handleExport() {
      let queryParams = null;
      let roleInfo = JSON.parse(sessionStorage.getItem('VueX_local')).root.login.user || {}
      if (roleInfo.user_type === 'ent') {
        queryParams = {
          nowPage: this.queryParams.nowPage,
          pageSize: this.queryParams.pageSize,
          startTime: this.queryParams.startTime,
          trainingName: this.queryParams.trainingName
        }
      } else {
        queryParams = this.queryParams;
      }      
      exportSafetyTraining(queryParams).then(response => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        const blob = new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '安全培训' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    },
    clearSensororgCode() {
      this.queryParams.corpCode = ''
      this.queryParams.orgname = ''
    },
    clearSensororgCodes() {
      this.form.corpCode = ''
      this.form.orgName = ''
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.queryParams.nowPage = val
      this.getList()
    }
  }
}
</script>
<style lang="scss" scoped>
.equipmentFacilities {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .my-autocomplete input {
    width: 200px;
  }
  .seach-part {
    width: 100%;
    font-weight: 600;
    padding-bottom: 10px;
    margin-bottom: 0px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
/deep/.el-dialog {
  .container {
    height: 60vh;
    overflow: auto;
    // 表单样式
    .el-form {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .el-form-item {
        width: 100%;
        display: flex;
        flex-wrap: wrap;

        .el-form-item__label {
          width: 25%;
        }

        .el-form-item__content {
          width: 65%;
          margin-left: 30px;

          .mapWrap {
            width: 100%;
            height: 300px;
          }
        }
      }

      .el-image {
        position: relative;
        display: inline-block;
        overflow: hidden;
        line-height: 9rem !important;
      }

      .content {
        width: 100%;

        .el-form-item__label {
          width: 17%;
        }

        .el-form-item__content {
          width: 75%;
          margin-left: 30px;
        }
      }

      .facilityTypeStyle {
        width: 100%;

        .el-form-item__label {
          width: 17%;
        }

        .el-form-item__content {
          width: 78.5%;

          .el-select {
            width: 100%;
          }

          .el-textarea__inner {
            font-size: 13px;
            font-family: sans-serif;
          }
        }
      }
    }
  }

  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
</style>
