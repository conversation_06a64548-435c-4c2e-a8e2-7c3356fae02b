<template>
  <div class="focuscompany">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span @click="goToSafety"
                ><a-icon type="home" theme="filled" class="icon" /> 重点关注企业
              </span>
            </a-breadcrumb-item>
            <!-- <a-breadcrumb-item >{{
              areaName
            }}</a-breadcrumb-item> -->
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            v-if="isShowDist"
          ></el-cascader>
          <el-input
            size="mini"
            class="input"
            v-model.trim="companyName"
            placeholder="请输入企业名称"
            clearable
          ></el-input>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
        <!-- <el-button type="primary" size="mini" @click="gotoTrendAnalysis"
          >趋势分析</el-button
        > -->
      </div>
      <div class="table-main">
        <div class="table-top">
          <el-tabs
            v-model="activeName"
            type="card"
            size="mini"
            @tab-click="handleClick"
          >
            <el-tab-pane label="全部" name="0"></el-tab-pane>
            <el-tab-pane label="较大风险" name="1"></el-tab-pane>
            <el-tab-pane label="重大风险" name="2"></el-tab-pane>
            <el-tab-pane label="未消警次数Top" name="3"></el-tab-pane>
            <el-tab-pane
              v-if="mode == '1'"
              label="最大报警时长"
              name="4"
            ></el-tab-pane>
          </el-tabs>
          <div class="radio">
            <el-radio-group size="small" @change="setMode" v-model="mode">
              <el-radio-button label="0">今日</el-radio-button>
              <el-radio-button label="1">本周</el-radio-button>
              <!-- <el-radio-button label="3">本月</el-radio-button> -->
            </el-radio-group>
          </div>
        </div>
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              :mode="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              :default-sort="{ prop: 'date', order: 'descending' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @select="select"
              @select-all="select"
            >
              <!-- @selection-change="handleSelectionChange" -->
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                label="单位名称"
                width="320"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div
                    style="color: #3977ea; cursor: pointer"
                    class="enterpName"
                    @click="goEnt(row)"
                  >
                    {{ row.companyName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :label="isShowDist == true ? '行政区划' : '归属园区'"
                width="150"
                prop="areaName"
                align="center"
              >
                <template slot-scope="scope">
                  <span v-if="isShowDist == true">{{
                    scope.row.areaName
                  }}</span>
                  <span v-else>{{ park.parkName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="企业类型"
                width="180"
                prop="companyType"
                align="center"
              >
              </el-table-column>
              <el-table-column
                label="重点关注原因"
                prop="focusReason"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template>
                  <template slot-scope="{ row, column, $index, store }">
                    <div style="cursor: pointer" class="enterpName">
                      {{ row.focusReason }}
                    </div>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <!-- <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="total"
            >
            </el-pagination> -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getFocuscompanyPageList,
  getFocuscompanyFocusTypePageList,
  getFocuscompanyFocusOn,
  getFocuscompanyExport,
  getFocuscompanyExportExcel,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  name: "focuscompany",
  data() {
    return {
      TrendChartBool: false,

      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      level: ["1", "2", "3", "4"],
      tableData: [],
      mode: "0",
      showtable: true,
      district: this.$store.state.controler.district,
      distCode: this.$store.state.login.userDistCode,
      areaName: "",
      loading: true,
      selection: [],
      companyName: "",
      activeName: "0",
      greaterRisk:[],
      majorRisks:[],
      maxWarningTimeTop5:[],
      nopoliceEliminationTop5:[],
      tableDataAll:[]
    };
  },
  watch: {},
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
  ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch:{
    vuexDistrict:{
      handler(newVal, oldVal) {
        this.district = newVal;
      }
    },
  },
  methods: {
    handleClick(){
        switch (this.activeName) {
          case "0":
            // 全部
            
            this.tableData = this.tableDataAll;
            break;
          case "1":
            // 一般风险
            this.tableData = this.greaterRisk;
            break;
          case "2":
             // 重大风险
            this.tableData = this.majorRisks;
            break;
          case "4":
             // 最大报警时长top5
            this.tableData = this.maxWarningTimeTop5;
            break;
          case "3":
             // 未消警次数top5
            this.tableData = this.nopoliceEliminationTop5;
            break;
          default:
            break;
        }
    },
    setMode(val) {
      this.mode = val;
      this.activeName = "0";
      this.majorRisks= [];
      this.greaterRisk=[];
      this.maxWarningTimeTop5=[];
      this.nopoliceEliminationTop5=[];
      this.tableDataAll =[];
      this.getData();
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    getData() {
      this.loading = true;
      //如果是数组就取区级行政区划代码
      getFocuscompanyFocusOn({
        enterpName: this.companyName,
        distCode: this.distCode,
        sign: this.mode,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          // this.tableData = res.data.data;
          
          this.majorRisks = res.data.data.majorRisks;
          this.greaterRisk = res.data.data.greaterRisk;
          this.maxWarningTimeTop5 = res.data.data.maxWarningTimeTop5;
          this.nopoliceEliminationTop5 = res.data.data.nopoliceEliminationTop5;
          this.tableDataAll= [];
           if(this.mode == 1){
              this.tableDataAll = this.tableDataAll.concat(res.data.data.majorRisks).concat(res.data.data.greaterRisk).concat(res.data.data.maxWarningTimeTop5).concat(res.data.data.nopoliceEliminationTop5);
           }else{
              this.tableDataAll = this.tableDataAll.concat(res.data.data.majorRisks).concat(res.data.data.greaterRisk).concat(res.data.data.nopoliceEliminationTop5);
           }
          this.handleClick();
        }
      });
    },
    // getDataOther() {
    //   this.loading = true;
    //   getFocuscompanyFocusTypePageList({
    //     companyName: this.companyName,
    //     distCode: this.distCode,
    //     dateType: this.mode,
    //     current: this.currentPage,
    //     focusType: this.activeName,
    //     size: 10,
    //   }).then((res) => {
    //     if (res.data.code == 0) {
    //       this.loading = false;
    //       this.tableData = res.data.data.records;
    //       this.total = res.data.data.total;
    //     }
    //   });
    // },
    // 导出
    exportExcel() {
      let exportExcelType = null;
      this.activeName == "0"
        ? (exportExcelType = "1")
        : (exportExcelType = "2");
      getFocuscompanyExport({
        distCode: this.distCode,
        enterpName:
          [...this.selection].length <= 1 ? null : [...this.selection],
        sign: this.mode,
        //全部传1 //其他传0
        type: this.activeName
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "重点关注企业" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },
    xiaZuan(areaCode, areaName) {
      this.areaName = areaName;
      this.distCode = areaCode;
      this.getData();
    },
    goToSafety() {
      this.distCode = this.$store.state.login.userDistCode;
      this.getData();
    },
    search() {
      this.getData();
    },
    gotoTrendAnalysis() {
      this.TrendChartBool = true;
    },
    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData();
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data;
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tabs__nav-wrap::after {
  background-color: #fff;
}
.focuscompany {
  .input {
    width: 200px;
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
  .seach-part {
    font-weight: 600;
    // padding: 0 10px;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      // width: 550px;
      display: flex;
      justify-content: space-between;
      > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      // height: 50px;
    }

    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  .tabs {
    //   margin-top:30px;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
