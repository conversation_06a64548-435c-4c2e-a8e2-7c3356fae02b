<template>
  <div class="app-container" style="height: 65vh; overflow: auto;padding:20px">
    <div class="specification-from-box">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px !important"
      >
        <el-form-item prop="tmpltName">
          <el-input
            v-model.trim="queryParams.tmpltName"
            placeholder="请输入模板名称"
            clearable
            maxlength="100"
            size="mini"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <!-- {{queryParams.tmpltType}} -->
        <el-form-item prop="tmpltType">
          <el-select
            v-model="queryParams.tmpltType"
            placeholder="请选择类型"
            clearable
            size="mini"
          >
            <el-option
              v-for="(item, index) in tempalteKindList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-form-item>
          <CA-button type="primary" size="mini" @click="handleQuery"
            >查询</CA-button
          >
        </el-form-item>
          <el-button type="primary" size="mini" plain @click="resetQuery"
            >重置</el-button
          >
        </el-form-item>
     
      </el-form>
    </div>

    <div class="specification-table-head">
      <div class="left">模板列表</div>

      <div>
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          style="margin-right: 10px"
          >新增</el-button
        >
      </div>
    </div>

    <el-table
      v-loading="loading"
      stripe
      :data="templeList"
      @selection-change="handleSelectionChange"
    >
      <!--      <el-table-column type="selection" width="55" align="center" />-->
      <el-table-column label="序号" type="index" width="70" />
      <el-table-column label="模板名称" align="center" prop="tmpltName">
        <template slot-scope="scope">
          <div class="title-hover">{{ scope.row.tmpltName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="模板类型" align="center" prop="tmpltType">
        <template slot-scope="scope">
          <span>{{ scope.row.tmpltType == 0 ? "安全预警" : "宣传教育" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="最近修改时间"
        align="center"
        prop="updateTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.updateTime
                ? parseTime(scope.row.updateTime, "{y}-{m}-{d} {h}:{i}:{s}")
                : "--"
            }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="修改人" align="center" prop="updateUser">
        <template slot-scope="scope">
          <span>{{ scope.row.updateUser ? scope.row.updateUser : "--" }}</span>
        </template>
      </el-table-column>

      <el-table-column label="所属单位" align="center" prop="issueUnitName">
        <template slot-scope="scope">
          <span>{{
            scope.row.issueUnitName ? scope.row.issueUnitName : "admin"
          }}</span>
        </template>
      </el-table-column>

      <el-table-column label="状态" align="center" prop="tmpltStatus">
        <template slot-scope="scope">
          <span>{{ scope.row.tmpltStatus == 0 ? "待发布" : "已发布" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            @click="handleUpdate(scope.row)"
            :disabled="scope.row.tmpltStatus != 0"
            >编辑</el-button
          >

          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            @click="cancelPublishFun(scope.row)"-->
          <!--            v-if="scope.row.tmpltStatus == 1"-->
          <!--            type="text"-->
          <!--            >取消发布</el-button-->
          <!--          >-->

          <el-button
            type="text"
            size="mini"
            @click="handleDelete(scope.row)"
           :disabled="scope.row.tmpltStatus != 0"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
         v-show="total > 0"
         :total="total"
         :page.sync="queryParams.nowPage"
         :limit.sync="queryParams.pageSize"
         @pagination="getList"
       /> -->
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>

    <!-- 添加或修改模板配置对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="50%"
      append-to-body
      :close-on-click-modal="false"
    >
      <!--      <el-scrollbar style="height: 50vh" class="auto-scrollbar">-->
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="100px !important"
        v-loading="openLoading"
      >
        <el-form-item label="模板名称" prop="tmpltName">
          <el-input
            v-model.trim="form.tmpltName"
            placeholder="请输入模板名称"
            maxlength="20"
          />
        </el-form-item>
        <!-- {{queryParams.tmpltType}} ------ -->
        <el-form-item label="类型" prop="tmpltType">
          <el-select v-model.trim="form.tmpltType" placeholder="请选择类型">
            <el-option
              v-for="(item, index) in tempalteKindList"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="模板内容" prop="tmpltContent">
          <el-input
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 10 }"
            resize="none"
            placeholder="请输入模板内容"
            v-model.trim="form.tmpltContent"
            maxlength="200"
          ></el-input>
        </el-form-item>
      </el-form>
      <!--      </el-scrollbar>-->

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm(1)">确认发布</el-button>
        <el-button type="primary" @click="submitForm(0)">保存草稿</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情弹窗 -->
    <!--    <TemDetails ref="temDetails"></TemDetails>-->
  </div>
</template>

<script>
import TemDetails from "./TemDetails";
import {
  getTempleAdd,
  getTempleListAll,
  getTempleList,
  getTempleUpdate,
  getTemple,
  delTemple,
} from "../../../../../api/informationRelease";
import { mapState } from "vuex";
// import { btnPermissions } from "@/api/meetings/addressbook";

var checkConIsEmpty = (rule, value, callback) => {
  if (!value.trim()) {
    return callback(new Error("内容不能为空"));
  } else {
    callback();
  }
};

export default {
  name: "Template",
  components: {
    TemDetails,
  },
  data() {
    return {
      preTmpltType: "",
      currentPage: 1,
      size: 10,
      total: 3,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 模板配置表格数据
      templeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        // nowPage: 1,
        // pageSize: 10,
        tmpltName: "",
        tmpltType: "",
        // deleteFlag: "0",
      },
      // 表单参数
      form: {
        //tmpltType:0,
      },
      // 表单校验
      rules: {
        tmpltName: [
          {
            required: true,
            message: "请输入模板名称",
            trigger: ["blur", "change"],
          },
          {
            validator: checkConIsEmpty,
            trigger: ["blur", "change"],
          },
        ],
        tmpltType: [
          {
            required: true,
            message: "请选择模板类型",
            trigger: ["blur", "change"],
          },
        ],
        tmpltContent: [
          {
            required: true,
            message: "请输入模板内容",
            trigger: ["blur", "change"],
          },
          {
            validator: checkConIsEmpty,
            trigger: ["blur", "change"],
          },
        ],
      },
      tempalteKindList: [
        { label: "安全预警", value: "0" },
        { label: "宣传教育", value: "1" },
      ],
      btnPermissList: [
        { privName: "新增" },
        { privName: "审核" },
        { privName: "编辑" },
        { privName: "删除" },
      ], //按钮权限
      openLoading: false,
    };
  },
  created() {},
  mounted() {
    this.getList();
    this.btnPermissionsFun();
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(val, "我执行了弹框的翻页吗");
      this.currentPage = val;
      this.getList();
    },
    // 按钮权限
    btnPermissionsFun() {},

    /** 查询模板配置列表 */
    getList() {
      //debugger
      // 当前登录用户如果是政府侧，查询的时候不用传orgId,否则看不了全部
      this.loading = true;
      // if (!this.queryParams.tmpltName && !this.queryParams.tmpltType) {
      //   getTempleListAll(this.queryParams).then((response) => {
      //     this.templeList = response.data.data;
      //     // this.total = response.data.data;
      //     this.loading = false;
      //   });
      // } else {
      //   this.queryParams.pageSize=this.size, //条数
      //   this.queryParams.nowPage=this.currentPage //页数
      //   getTempleList(this.queryParams).then((response) => {
      //     this.templeList = response.data.data;
      //     this.total = response.data.data;
      //     this.loading = false;
      //   });
      // }
      (this.queryParams.pageSize = this.size), //条数
        (this.queryParams.nowPage = this.currentPage); //页数
      getTempleList(this.queryParams).then((response) => {
        console.log(response, "模板数据");
        this.templeList = response.data.data.list;
        this.total = response.data.data.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        // tmpltCode: null,
        tmpltName: null,
        tmpltType: null,
        tmpltStatus: "0",
        tmpltContent: null,
        issueUnitCode: this.user.org_name,
        // createUser: null,
        // createTime: null,
        // updateUser: null,
        // updateTime: null,
        instCode: this.user.org_name,
        // deleteFlag: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // this.queryParams.nowPage = 1;
      this.queryParams.tmpltName = this.queryParams.tmpltName
        ? this.queryParams.tmpltName.trim()
        : "";
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      // this.ids = selection.map((item) => item.tmpltCode);
      // this.single = selection.length !== 1;
      // this.multiple = !selection.length;
    },
    // 某个单元格被点击事件
    handleView(row, col) {
      if (col.label == "模板名称") {
        this.$refs.temDetails.open = true;
        getTemple(row.tmpltCode).then((response) => {
          this.$refs.temDetails.form = response.data;
        });
      }
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加模板配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      this.open = true;
      this.openLoading = true;
      getTemple({ tmpltCode: row.tmpltCode || this.ids }).then((response) => {
        this.form = response.data.data;
        this.title = "修改模板配置";
        this.openLoading = false;
      });
    },

    // 取消发布的操作
    cancelPublishFun(row) {
      updateStatus({
        tmpltCode: row.tmpltCode,
        tmpltStatus: "0",
      }).then((response) => {
        this.msgSuccess("取消成功");
        this.getList();
      });
    },

    /** 提交按钮 */
    submitForm(type) {
      this.form.tmpltStatus = type;
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.tmpltCode != null) {
            getTempleUpdate(this.form).then((response) => {
              if (response.data.status === 200) {
                this["$message"].success("修改成功");
                this.open = false;
                this.getList();
              } else {
                this["$message"].error(response.data.msg);
              }
            });
          } else {
            getTempleAdd(this.form).then((response) => {
              if (response.data.status === 200) {
                this["$message"].success("修改成功");
                this.open = false;
                this.getList();
              } else {
                this["$message"].error(response.data.msg);
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      this.$confirm("是否确认删除选中的数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        delTemple({ tmpltCodes: row.tmpltCode || this.ids }).then((res) => {
          console.log(res);
          if (res.data.status === 200) {
             if (this.templeList.length === 1 && this.currentPage !== 1) {
                this.currentPage--
              }
            this.getList();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        });
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有模板配置数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportTemple(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
    }),
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
.specification-table-head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  margin-top: 15px;
  .left {
    font-size: 18px;
    text-align: left;
    font-weight: 900;
  }
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
</style>
