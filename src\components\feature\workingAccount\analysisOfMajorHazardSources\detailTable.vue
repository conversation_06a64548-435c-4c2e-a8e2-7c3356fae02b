<template>
  <div class="historyList">
    <el-dialog
      :title="titleName"
      :visible.sync="show"
      @close="closeBooleanOne(false)"
      width="1350px"
      top="5vh"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist"
          ></el-cascader>
          <el-select
            v-model="majorHazardLevel"
            placeholder="请选择重大危险源等级"
            size="mini"
            clearable
            @clear="clearMa"
             @change="getEntDatas(distCode, type, areaName)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            placeholder="请输入重大危险源名称"
            v-model.trim="dangerName"
            size="mini"
            clearable
            @clear="clearEntName"
          >
          </el-input>
          <el-button type="primary" size="mini"  class="btn" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" class="btn" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="distName" label="行政区划" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          prop="enterpName"
          label="企业名称"
          width="300"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span @click="goEnt(scope.row)" style="color: #3977ea; cursor: pointer">{{
              scope.row.enterpName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="dangerName" label="重大危险源名称" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column
          prop="dangerLevelCode"
          label="危险源等级"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column prop="tank" label="储罐" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span
            v-if="row.tank != 0"
            @click="openDialog(row.enterpName, 0, row.dangerName,'G',row.dangerId)"
            style="color: #3977ea; cursor: pointer"
            >{{ row.tank }}</span
            >
            <span v-else>{{ row.tank }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="leak" label="气体泄露点" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span
            v-if="row.leak != 0"
            @click="openDialog(row.enterpName, 1, row.dangerName,'Q',row.dangerId)"
            style="color: #3977ea; cursor: pointer"
            >{{ row.leak }}</span
            >
            <span v-else>{{ row.leak }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="device" label="装置" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span
            v-if="row.device != 0"
            @click="openDialog(row.enterpName, 2, row.dangerName,'P',row.dangerId)"
            style="color: #3977ea; cursor: pointer"
            >{{ row.device }}</span
            >
            <span v-else>{{ row.device }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="warehouse" label="仓库" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span
            v-if="row.warehouse != 0"
            @click="openDialog(row.enterpName, 3, row.dangerName,'K',row.dangerId)"
            style="color: #3977ea; cursor: pointer"
            >{{ row.warehouse }}</span
            >
            <span v-else>{{ row.warehouse }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
      <DangerDetailTable ref="DangerDetailTable"></DangerDetailTable>
    </el-dialog>
  </div>
</template>

<script>
// import History from "./history"
import {
  queryMajorDetails,
  exportMajorDetails,
} from "@/api/workingAcc";
import { getDistrictUser } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
import DangerDetailTable from './dangerDetailTable'
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {
    DangerDetailTable
    // DangerDetailTable: (resolve) => {
    //   require(["./dangerDetailTable.vue"], resolve);
    // },
  },
  data() {
    return {
      show: false,
      //  districtLoading: false,
      type: "",
      currentPage: 1,
      dangerName: "",
      loading: true,
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      majorHazardLevel: "",
      district: [],
      distCode: "",
      tableData: [],
      total: 0,
      areaName: "",
      titleName: "",
      selection: [],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist
    }),
  },
  //方法集合
  methods: {
    openDialog(enterpName, type, dangerName,dangerType,dangerId) {
      this.$nextTick(()=>{
        this.$refs.DangerDetailTable.closeBooleanTwo(true);
        this.$refs.DangerDetailTable.getEntData(enterpName, type, dangerName,dangerType,dangerId);
      })      
    },
    closeBooleanOne(val) {
        this.currentPage = 1;
       this.majorHazardLevel="";
      this.show = val;
    },
    handleChange(value) {
      // if (value && value.length > 0) {
      //   this.distCode = value[value.length - 1];
      // }
    },
    goEnt(row) {
      // console.log(row);
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterPid);
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then((res) => {
               let child = res.data.data;
        // debugger;
        if (child.children.length > 0) {
          for (let j = 0; j < child.children.length; j++) {
            if (child.children[j].children.length > 0) {
              for (let z = 0; z < child.children[j].children.length; z++) {
                if (child.children[j].children[z].children.length < 1) {
                  //判断children的数组长度
                  child.children[j].children[z].children = undefined;
                }
              }
            } else {
              child.children[j].children = undefined;
            }
          }
        } else {
          child.children = undefined;
        }
        this.district = [child];
      });
    },
    getEntDatas(distCode, type, areaName) {
      this.distCode = distCode;
      this.loading = true;
      this.type = type;
      this.areaName = areaName;
      if (this.majorHazardLevel == 0) {
        this.titleName = this.areaName + "-危险源清单-企业合计列表";
        this.majorHazardLevel = '';
      } else if (this.majorHazardLevel == 1) {
        this.titleName = this.areaName + "-危险源清单-一级重大危险源企业列表";
        // this.majorHazardLevel = '1';
      } else if (this.majorHazardLevel == 2) {
        this.titleName = this.areaName + "-危险源清单-二级重大危险源企业列表";
        // this.majorHazardLevel = '2';
      } else if (this.majorHazardLevel == 3) {
        this.titleName = this.areaName + "-危险源清单-三级重大危险源企业列表";
        // this.majorHazardLevel = '3';
      } else if (this.majorHazardLevel == 4) {
        this.titleName = this.areaName + "-危险源清单-四级重大危险源企业列表";
        // this.majorHazardLevel = '4';
      }
      // this.majorHazardLevel = String(this.type);
      queryMajorDetails({
        distCode: this.distCode,
        distParentCode:distCode,
        dangerName: this.dangerName,
        dangerLevelCode: this.majorHazardLevel,
        // type: type,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    getEntData(distCode, type, areaName) {
      this.distCode = distCode;
      this.loading = true;
      this.type = type;
      this.majorHazardLevel = String(type);
      this.areaName = areaName;
      if (this.majorHazardLevel == 0) {
        this.titleName = this.areaName + "-危险源清单-企业合计列表";
        this.majorHazardLevel = '';
      } else if (this.majorHazardLevel == 1) {
        this.titleName = this.areaName + "-危险源清单-一级重大危险源企业列表";
        // this.majorHazardLevel = '1';
      } else if (this.majorHazardLevel == 2) {
        this.titleName = this.areaName + "-危险源清单-二级重大危险源企业列表";
        // this.majorHazardLevel = '2';
      } else if (this.majorHazardLevel == 3) {
        this.titleName = this.areaName + "-危险源清单-三级重大危险源企业列表";
        // this.majorHazardLevel = '3';
      } else if (this.majorHazardLevel == 4) {
        this.titleName = this.areaName + "-危险源清单-四级重大危险源企业列表";
        // this.majorHazardLevel = '4';
      }
      queryMajorDetails({
        distCode: this.distCode,
        distParentCode:distCode,
        dangerName: this.dangerName,
        dangerLevelCode: this.majorHazardLevel,
        // type: type,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].dangerId;
      }
    },
    exportExcel() {
      let list = [...this.selection];
      exportMajorDetails({
        distCode: this.distCode,
        idList: list.length<=0?null:list,
        dangerLevelCode: this.majorHazardLevel,
        distParentCode:this.distCode,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "危险源清单" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    showDialog() {
      this.$refs.History.closeBoolean(true);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getEntData(this.distCode, this.type, this.areaName);
    },
    search() {
      this.currentPage = 1;
      // @change="getEntDatas(distCode, type, areaName)"
      this.getEntData(this.distCode, this.majorHazardLevel, this.areaName);
    },
    clearDis() {
      this.distCode = "";
    },
    clearMa() {
      this.majorHazardLevel = "";
    },
    clearEntName() {
      this.dangerName = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.historyList {
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 700px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      >.btn{
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>