<template>
  <div class="multi-video-container">
    <!-- 视频播放区域 -->
    <div class="video-grid" :class="gridClass">
      <div
        v-for="(video, index) in videoChannels"
        :key="index"
        class="video-item"
        :class="{
          active: activeVideoIndex === index,
        }"
        @click="selectVideo(index)"
      >
        <div class="video-wrapper">
          <flvPlayer
            v-if="video.url"
            :url="video.url"
            :ref="`player_${index}`"
            :key="`player_${index}_${video.key}`"
          />
          <div v-else class="empty-video">
            <div class="empty-content">
              <i class="el-icon-video-camera"></i>
            </div>
          </div>

          <!-- 视频信息覆盖层 -->
          <div class="video-overlay" v-if="video.url">
            <div class="video-info">
              <span class="channel-name">{{ video.name || `` }}</span>
              <!-- <span class="video-status" :class="video.status">{{
                getStatusText(video.status)
              }}</span> -->
            </div>
            <div class="video-controls">
              <el-button
                type="text"
                icon="el-icon-close"
                @click.stop="closeVideo(index)"
                title="关闭"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel" v-show="showControlPanel">
      <div class="layout-controls">
        <span class="label">布局:</span>
        <el-button-group>
          <el-button
            :type="layout === '1x1' ? 'primary' : 'default'"
            size="mini"
            @click="changeLayout('1x1')"
            >1画面</el-button
          >
          <el-button
            :type="layout === '2x2' ? 'primary' : 'default'"
            size="mini"
            @click="changeLayout('2x2')"
            >4画面</el-button
          >
          <el-button
            :type="layout === '1x3' ? 'primary' : 'default'"
            size="mini"
            @click="changeLayout('1x3')"
            >1+3画面</el-button
          >
        </el-button-group>
      </div>

      <div class="video-controls-panel">
        <el-button size="mini" @click="closeAllVideos">全部关闭</el-button>
        <el-button size="mini" @click="toggleControlPanel"
          >隐藏控制栏</el-button
        >
      </div>
    </div>

    <!-- 控制面板隐藏时的显示按钮 -->
    <div
      class="control-toggle"
      v-show="!showControlPanel"
      @click="toggleControlPanel"
    >
      <i class="el-icon-setting"></i>
      <span>显示控制栏</span>
    </div>
  </div>
</template>

<script>
import flvPlayer from "./flvPlayer-v2.vue";

export default {
  name: "MultiVideoPlayer",
  components: {
    flvPlayer,
  },
  props: {
    videoSources: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      layout: "2x2", // 布局模式: 1x1, 2x2, 1x3
      activeVideoIndex: 0, // 当前选中的视频索引
      showControlPanel: false, // 控制面板显示状态
      videoChannels: [
        { url: "", name: "", status: "empty", key: 0 },
        { url: "", name: "", status: "empty", key: 0 },
        { url: "", name: "", status: "empty", key: 0 },
        { url: "", name: "", status: "empty", key: 0 },
      ],
    };
  },
  computed: {
    gridClass() {
      return `layout-${this.layout}`;
    },
    visibleChannels() {
      switch (this.layout) {
        case "1x1":
          return 1;
        case "2x2":
          return 4;
        case "1x3":
          return 4;
        default:
          return 4;
      }
    },
  },
  methods: {
    // 选择视频
    selectVideo(index) {
      this.activeVideoIndex = index;
    },

    // 改变布局
    changeLayout(newLayout) {
      this.layout = newLayout;
      if (newLayout === "1x1") {
        this.activeVideoIndex = 0;
      }
    },

    // 切换控制面板显示状态
    toggleControlPanel() {
      this.showControlPanel = !this.showControlPanel;
    },

    // 添加视频源到指定通道（供外部调用）
    addVideoToChannel(videoSource, channelIndex = null) {
      // 确保 videoChannels 已初始化
      if (!this.videoChannels || !Array.isArray(this.videoChannels)) {
        console.error("videoChannels 未初始化");
        return -1;
      }

      console.log("添加视频到通道:", {
        videoSource: videoSource.url,
        requestedChannel: channelIndex,
        activeChannel: this.activeVideoIndex,
        currentChannels: this.videoChannels.map((ch, i) => ({
          index: i,
          hasUrl: !!ch.url,
          url: ch.url ? ch.url.substring(0, 50) + "..." : "empty",
        })),
      });

      // 首先检查是否已经有相同的URL在其他通道
      const existingChannelIndex = this.videoChannels.findIndex(
        (channel) => channel.url === videoSource.url
      );

      if (existingChannelIndex >= 0) {
        console.log(`视频已存在于通道 ${existingChannelIndex + 1}，不重复添加`);
        return existingChannelIndex;
      }

      // 如果没有指定通道，优先使用当前选中的通道
      if (channelIndex === null) {
        // 如果当前选中通道为空，使用它；否则找第一个空闲通道
        if (!this.videoChannels[this.activeVideoIndex].url) {
          channelIndex = this.activeVideoIndex;
        } else {
          channelIndex = this.videoChannels.findIndex(
            (channel) => !channel.url
          );
          if (channelIndex === -1) {
            // 如果没有空闲通道，使用当前活动通道（覆盖现有视频）
            channelIndex = this.activeVideoIndex;
          }
        }
      }

      // 确保通道索引有效
      if (channelIndex < 0 || channelIndex >= this.videoChannels.length) {
        console.error("无效的通道索引:", channelIndex);
        return -1;
      }

      const channel = this.videoChannels[channelIndex];
      if (!channel) {
        console.error("通道不存在:", channelIndex);
        return -1;
      }

      // 只有在URL真正改变时才更新key
      const urlChanged = channel.url !== videoSource.url;

      channel.url = videoSource.url;
      channel.name = videoSource.name;
      channel.status = "loading";

      if (urlChanged) {
        channel.key++; // 只有URL改变时才强制重新渲染播放器
        console.log(`通道 ${channelIndex + 1} URL已更新，重新渲染播放器`);
      }

      // 模拟加载状态
      setTimeout(() => {
        if (channel && channel.url === videoSource.url) {
          // 确保URL没有被改变
          channel.status = "playing";
        }
      }, 1000);

      return channelIndex;
    },

    // 关闭视频
    closeVideo(index) {
      const channel = this.videoChannels[index];
      channel.url = "";
      channel.name = "";
      channel.status = "empty";
      channel.key++;
    },

    // 关闭所有视频
    closeAllVideos() {
      this.videoChannels.forEach((_, index) => {
        this.closeVideo(index);
      });
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        empty: "空闲",
        loading: "加载中",
        playing: "播放中",
        error: "错误",
      };
      return statusMap[status] || "未知";
    },
  },
  mounted() {
    // 确保组件正确初始化
    console.log("多路视频播放器组件已挂载", this.videoChannels);
  },
};
</script>

<style lang="scss" scoped>
.multi-video-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #000;
  position: relative;
}

.video-grid {
  flex: 1;
  display: grid;
  gap: 2px;
  padding: 2px;
  min-height: 0; /* 防止flex子项溢出 */

  &.layout-1x1 {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr;

    .video-item:not(:first-child) {
      display: none;
    }
  }

  &.layout-2x2 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
  }

  &.layout-1x3 {
    grid-template-columns: 2fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;

    .video-item:first-child {
      grid-row: 1 / 4; /* 占据所有行 */
      grid-column: 1; /* 占据第一列 */
    }

    .video-item:nth-child(2) {
      grid-row: 1;
      grid-column: 2;
    }

    .video-item:nth-child(3) {
      grid-row: 2;
      grid-column: 2;
    }

    .video-item:nth-child(4) {
      grid-row: 3;
      grid-column: 2;
    }
  }
}

.video-item {
  position: relative;
  background: #1a1a1a;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 0; /* 防止内容溢出 */
  overflow: hidden; /* 隐藏溢出内容 */

  &.active {
    border-color: #409eff;
  }

  &:hover {
    .video-overlay {
      opacity: 1;
    }
  }
}

.video-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;

  // 确保视频播放器占满容器
  ::v-deep .flv-player-container,
  ::v-deep video {
    width: 100% !important;
    height: 100% !important;
    object-fit: fill;
  }
}

.empty-video {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;

  .empty-content {
    text-align: center;

    i {
      font-size: 48px;
      margin-bottom: 16px;
      display: block;
    }

    p {
      margin: 8px 0;

      &.tip {
        font-size: 12px;
        color: #999;
      }
    }
  }
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    transparent 30%,
    transparent 70%,
    rgba(0, 0, 0, 0.7) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;

  .video-info,
  .video-controls {
    pointer-events: auto;
  }
}

.video-info {
  position: absolute;
  top: 8px;
  left: 8px;
  color: white;

  .channel-name {
    display: block;
    font-weight: bold;
    margin-bottom: 4px;
  }

  .video-status {
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;

    &.loading {
      background: #e6a23c;
    }

    &.playing {
      background: #67c23a;
    }

    &.error {
      background: #f56c6c;
    }
  }
}

.video-controls {
  position: absolute;
  top: 8px;
  right: 8px;

  .el-button {
    color: white;
    background: rgba(0, 0, 0, 0.5);
    border: none;
    padding: 4px;
    border-radius: 3px;

    &:hover {
      color: #f56c6c;
      background: rgba(0, 0, 0, 0.7);
    }
  }
}

.control-panel {
  background: #2c2c2c;
  padding: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* 防止被压缩 */

  .label {
    color: #ccc;
    margin-right: 8px;

    .channel-status {
      color: #67c23a;
      font-weight: normal;

      &.empty {
        color: #909399;
      }
    }
  }

  .layout-controls,
  .video-controls-panel {
    display: flex;
    align-items: center;
  }
}

.control-toggle {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  z-index: 100;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(0, 0, 0, 0.9);
  }

  i {
    font-size: 14px;
  }
}
</style>
