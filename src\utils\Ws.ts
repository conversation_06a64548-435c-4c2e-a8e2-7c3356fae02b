import Bus from "@/utils/bus";

class Ws {
    private serverUrl
    private ws
    private timeoutObj
    private serverTimeoutObj
    private connectTimes;
    constructor(serverUrl: string) {
      this.serverUrl = serverUrl
      this.ws = new WebSocket(this.serverUrl)
      this.message()
      this.sendHeart()
      this.dealError()
      // 监听窗口关闭事件，当窗口关闭时，主动去关闭websocket连接，防止连接还没断开就关闭窗口，server端会抛异常。
    }
    private sendHeart() {
      clearInterval(this.timeoutObj)
        const self = this
        this.timeoutObj = setInterval(() => {
          self.ws.send("heart")
          // console.error("test heart")
        }, 30000)
    }
    private dealError() {
      this.ws.onerror = (msg) => {
        this.reconnect()
      }
    }
    private message() {
      const self = this
      this.ws.onmessage = (msg) => {
        let data;
        this.connectTimes = 0;
        try{
            data = JSON.parse(msg.data);
            if( this.serverUrl.indexOf('webSocket/warning')>0){
                Bus.$emit("warningMessage", data);
            }else {
                Bus.$emit("newMessage", data);
            }   
        } catch (e) {
            // console.log(e, "e")
          }
       
      }
      this.ws.onclose = function (event) {
        console.log(event, "<===============")
        if (event.code !== 1000) {
          self.reconnect()
        }
      }
      this.ws.onerror = function () {
        console.log("ws连接错误")
      }
    }
    private send(data) {
      this.ws.send(data)
    }
    private reconnect() {
      console.log("重新连接<========================");
      this.connectTimes ++;
      if(this.connectTimes<15){
        clearInterval(this.timeoutObj)
        this.ws = new WebSocket(this.serverUrl)
        this.message()
        this.sendHeart()
        this.dealError()
      }
    }
    private close() {
      this.ws.close()
      clearInterval(this.timeoutObj)
    }
  }
  export default Ws
  