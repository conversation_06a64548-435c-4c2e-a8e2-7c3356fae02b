<template>
  <div class="riskRank">
    <!-- {{riskRankData}} -->
    <div class="riskRankBox" v-if="riskRankDataPro.length > 1">
      <div class="rankItem" v-for="(item, index) of riskRankDataPro" :key="index">
        <div class="LevelL">
          <span :class="['icon', 'icon' + item.riskAreaLevel]"></span>
          {{ item.areaName }}
        </div>
        <div class="LevelR">
          {{ item.riskAreaLevelName }}
        </div>
      </div>
    </div>
    <div style="height:288px;line-height:288px;text-align: center;" v-else>暂无数据</div>
  </div>
</template>
<script>
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  props:{
    riskRankDataPro:Array
  },
  data() {
    return {
      riskRankData: [
        // { name: "武汉市", levelName: "重大", level: "1" },
        // { name: "仙桃市", levelName: "重大", level: "1" },
        // { name: "鄂州市", levelName: "较大", level: "2" },
        // { name: "恩施", levelName: "较大", level: "2" },
        // { name: "宜昌市", levelName: "较大", level: "2" },
        // { name: "随州市", levelName: "较大", level: "2" },
        // { name: "十堰市", levelName: "一般", level: "3" },
        // { name: "咸宁市", levelName: "一般", level: "3" },
        // { name: "黄石市", levelName: "低", level: "4" },
        // { name: "黄冈市", levelName: "低", level: "4" },
        // { name: "天门市", levelName: "低", level: "4" },
        // { name: "潜江市", levelName: "低", level: "4" },
        // { name: "荆州市", levelName: "低", level: "4" },
        // { name: "孝感市", levelName: "低", level: "4" },
        // { name: "荆门市", levelName: "低", level: "4" },
        // { name: "襄樊市", levelName: "低", level: "4" },
        // { name: "神农架林区", levelName: "低", level: "4" },
        // { name: "", levelName: "", level: "" },
      ],
    };
  },
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.riskRank {
  height: 288px;
  overflow: auto;
  .riskRankBox {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    .rankItem {
      display: flex;
      justify-content: space-between;
      justify-items: center;
      border:1px solid #d9d9d9;
      margin-bottom: 15px;
      padding:5px 8px;
      width:30%;
      border-radius: 3px;
      align-items: center;
      .LevelL{
        width:60px;
      }
      .icon{
        width:11px;
        height: 11px;
        display: inline-block;
      }
      .icon1{
        background: url('../../../../../static/img/risk1.png');
      }
      .icon2{
        background: url('../../../../../static/img/risk2.png');
      }
      .icon3{
        background: url('../../../../../static/img/risk3.png');
      }
      .icon4{
        background: url('../../../../../static/img/risk4.png');
      }
    }
  }
  .riskRankBox .rankItem:last-child {
    border: 0;
  }
}
</style>