import axios from "axios";
import qs from "qs";

//特殊列表
export const getCompanyParticularJobList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/list/v1",
    // url: "/gemp-chemical/api/gemp/companyParticularJob/list/all/v1",
    data: data,
  });
};
//详情
export const getCompanyParticularJob = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/id/v1",
    data: data,
  });
};
//删除
export const getCompanyParticularJobDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/delete/v1",
    data: data,
  });
};
//更新
export const getCompanyParticularJobUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/update/v1",
    data: data,
  });
};

//导出
export const getCompanyParticularJobExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
//
//
export const getCompanyParticularJobAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/add/v1",
    data: data,
  });
};
export const getCompanyParticularJobType = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/type/all/v1",
    data: data,
  });
};
export const getCompanyParticularJobTypeByParentCode = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyParticularJob/type/byParentCode/v1",
    data: data,
  });
};

//三同时资料复查 - 列表
export const getCompanyProjectList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/page/v1",   
    data: data,
  });
};
//三同时资料复查 - 新增
export const getCompanyProjectSave = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/save/v1",   
    data: data,
  });
};
//三同时资料复查 - 删除
export const getCompanyProjectDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/delete/v1",   
    data: data,
  });
};

//三同时资料复查 - 修改
export const getCompanyProjectUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/update/v1",   
    data: data,
  });
};

//三同时资料复查 - 查看
export const getCompanyProjectFindById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/findById/v1",   
    data: data,
  });
};

//三同时资料复查 - 提交
export const getCompanyProjectApply = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/apply/v1",   
    data: data,
  });
};

//三同时资料复查 - 驳回
export const getCompanyProjectReject = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/reject/v1",   
    data: data,
  });
};

//三同时资料复查 - 信息复核状态列表下拉框
export const getStatusList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/statusList/v1",   
    data: data,
  });
};

//三同时资料复查 - 信息复核通过
export const projectEvaluatePass = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/pass/v1",   
    data: data,
  });
};


//三同时资料复查 - 专家复核提交
export const projectApprovalDecide = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectApproval/decide/v1",   
    data: data,
  });
};

//三同时资料复查 - 专家复核详情
export const projectApprovalReview = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectApproval/review/v1",   
    data: data,
  });
};


//三同时资料复查 - 专家复核结果
export const projectApprovalReviewResult = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectApproval/reviewResult/v1",   
    data: data,
  });
};

//三同时资料复查 - 开始复核
export const projectApprovalStart = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectApproval/start/v1",   
    data: data,
  });
};

//三同时资料复查 - 受理审核信息
export const projectEvaluateRecieve = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectEvaluate/recieve/v1",   
    data: data,
  });
};

//三同时资料复查 - 专家复核草稿
export const projectEvaluateDraft = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/projectApproval/draft/v1",   
    data: data,
  });
};

//视频分析报警控制器 - 分页查看报警信息 ok
export const videoAlarmFindPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/findPage/v1",   
    data: data,
  });
};

export const findSubPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/findSubPage/v1",   
    data: data,
  });
};

//视频分析报警控制器 - 报警类型下拉列表 ok
export const videoAlarmType = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/alarmType/v1",   
    data: data,
  });
};

//视频分析报警控制器 - 反馈状态下拉列表 ok
export const videoAlarmFeedbackStatus = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/feedbackStatus/v1",   
    data: data,
  });
};

//视频分析报警控制器 - 导出报警信息excel ok
export const videoAlarmExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/export/v1",   
    data: data,
    responseType: "arraybuffer",
  });
};

//视频分析报警控制器 - 反馈报警信息
export const videoAlarmFeedback = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/feedback/v1",   
    data: data,
  });
};


//视频分析报警控制器 - 根据alarmId查看报警信息 ok
export const videoAlarmFindById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/videoAlarm/findById/v1",   
    data: data,
  });
};


//第三方系统对接生成一次性token
export const getTokenGenerateLink = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/login/tokenGenerate/v1",   
    data: data,
  });
};

//第三方系统对接校验token返回用户数据
export const getTokenCheck = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/login/tokenCheck/v1",   
    data: data,
  });
};