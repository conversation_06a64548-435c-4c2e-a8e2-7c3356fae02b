<template>
  <div class="container">
    <div class="header">
      <!-- <div class="title">菜单信息</div>
      <el-button type="primary" class="newMenu" @click="newMenu()" icon="plus"
        ><span>添加规则</span></el-button
      > -->
      <div class="search-box">
        <!-- <div class="select-maker">
            <el-input
                placeholder="操作人"
                clearable>
            </el-input>
        </div> -->
        <div class="select-data">
          <el-date-picker
            v-model="value1"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="选择开始时间"
            end-placeholder="选择结束时间"
            @change="searchTime"
          >
          </el-date-picker>
        </div>
        <div class="select-time">
          <el-select
            v-model="type"
            clearable
            placeholder="日志类型"
            @clear="clearSh"
          >
            <el-option
              v-for="item in option1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <div class="seach-btn">
          <!-- <el-button type="primary" icon="el-icon-search" @click="newMenu()">阈值配置</el-button>
            <el-button type="primary" icon="el-icon-search" @click="search">导出</el-button> -->
          <el-button type="primary" icon="el-icon-search" @click="search"
            >查询</el-button
          >
        </div>
      </div>
    </div>
    <div class="body" v-loading="loading">
      <el-table
        :data="tableData.records"
        style="width: 100%; color: rgb(101, 101, 101)"
      >
        <el-table-column type="index" label="序号" width="80">
        </el-table-column>
        <el-table-column label="类型">
          <template slot-scope="scope">
            <span v-if="scope.row.type">{{
              scope.row.type == "0" ? "正常" : "异常"
            }}</span>
            <span v-else> </span>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题"> </el-table-column>
        <el-table-column prop="remoteAddr" label="IP地址"> </el-table-column>
        <el-table-column prop="method" label="请求方式"> </el-table-column>
        <el-table-column prop="serviceId" label="客户端"> </el-table-column>
        <el-table-column prop="time" label="请求时间"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="200">
        </el-table-column>
        <el-table-column align="right" label="操作">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.$index, scope.row)"
              ><i class="el-icon-view"></i
            ></el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              ><i class="el-icon-delete"></i
            ></el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="tableData.size"
          layout="total, prev, pager, next"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
    ></DialogTable>
  </div>
</template>

<script>
import { getLogList, deleteLog } from "../../../api/log";
import { parseTime } from "@/utils/index";
import DialogTable from "./table";
import { mapState, mapGetters } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {
    DialogTable,
  },
  data() {
    return {
      dialogTableVisible: true,
      currentPage: 1,
      tableData: {},
      loading: false,
      type: "",
      //   createTime:[],
      option1: [
        {
          value: "0",
          label: "正常",
        },
        {
          value: "9",
          label: "异常",
        },
      ],
      value1: "",
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 100,
      },
      startTime: "",
      endTime: "",
    };
  },
  //方法集合
  methods: {
    //打开编辑弹框
    handleEdit(index, row) {
      this.dialogTableVisible = true;
      //打开表单填报
      this.$refs.child.parentMsg(this.dialogTableVisible);
      //数据给到编辑表单，关闭系统编码填报
      this.$refs.child.getData(row);
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("确定要删除选择的数据吗？", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteLog(row.id)
            .then((res) => {
              this.$message({
                message: "操作成功",
                type: "success",
              });
              if (this.tableData.total % this.tableData.size == 1) {
                this.page.pageNo = this.page.pageNo - 1;
              }
              this.getMenuData(this.page.pageNo, 1);
              this.$emit("pf");
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    //新建菜单
    // newMenu() {
    //   this.dialogTableVisible = true;
    //   this.$refs.child.parentMsg(this.dialogTableVisible);
    //   this.$refs.child.clearTable();
    //   //数据给到编辑表单,清空表单，并开放系统编码填报
    //   this.$refs.child.getData({});
    // },
    //获取菜单信息列表
    getMenuData(pageNo) {
      // console.log(val);
      this.loading = true;
      getLogList({
        current: this.page.pageNo,
        size: this.page.pageSize,
        type: this.type,
        // createTime:this.createTime,
        startTime: this.startTime,
        endTime: this.endTime,
      })
        .then((res) => {
          // console.log(res.data.data);
          this.tableData = res.data.data;
          this.currentPage = res.data.data.current;
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    fatherMethod() {
      this.getMenuData();
    },
    //切换分页数据
    handleCurrentChange(val) {
      this.page.pageNo = val;
      this.getMenuData();
      this.$refs.multipleTable.clearSelection();
    },
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getCommitment();
      this.$refs.multipleTable.clearSelection();
    },
    //修改密码管理启用状态
    setPwdRuleUpdateStatus(data) {
      let status = null;
      if (data.isSelected == "true") {
        status = "false";
      } else {
        status = "true";
      }
      updatePwdRuleStatus({ id: data.id, status: status })
        .then((res) => {
          this.$message({
            message: "操作成功",
            type: "success",
          });
          this.getMenuData();
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.startTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.startTime = "";
        this.endTime = "";
      }
    },
    search() {
      this.page.pageNo = 1;
      this.page.pageSize = 10;
      this.getMenuData();
    },
    clearSh() {
      this.type = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getMenuData();
  },
};
</script>
<style lang="scss" scoped>
.container {
  overflow: hidden;
  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    padding: 0 20px;
    background-color: #fff;
    margin-bottom: 20px;
    .newMenu {
      width: 100px;
      height: 38px;
      padding: 10px;
      color: #fff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .search-box {
      width: 100%;
      padding-bottom: 10px;
      background: #fff;
      margin-bottom: 20px;
      padding-top: 20px;
      position: relative;
      > div {
        display: inline-block;
      }
      .seach-btn {
        position: absolute;
        right: 0;
      }
    }
  }
  .pagination {
    float: right;
    margin-top: 30px;
    background-color: #fff;
  }
  .body {
    padding: 20px 10px;
    height: 70vh;
    overflow: auto;
    background-color: #fff;
  }
}
</style>
