<template>
  <el-select
    v-model="selectedValue"
    v-loadData="loadData"
    remote
    filterable
    :remote-method="searchMethod"
    class="more-select"
    :popper-class="`more-select-dropdown ${loading && 'loading'}`"
    :clearable="clearable"
    @change="selectChange"
    :loading="loading"
    :disabled="disabled"
  >
    <!-- 只有isShow定义了false，才进行隐藏，否则正常显示 -->
    <el-option
      v-for="(item, index) in list"
      :key="item.msdsid + `'${index}'`"
      :label="item.msdstitle"
      :value="item.msdsid"
    />
  </el-select>
</template>

<script>
import Vue from "vue";
import { getDangerIdIsNotNullListData } from "../../../../api/equipmentAndFacilities";
Vue.directive("loadData", {
  bind(el, binding) {
    // 获取element-ui定义好的scroll盒子
    const SELECTWRAP_DOM = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap"
    );
    SELECTWRAP_DOM.addEventListener("scroll", function () {
      /**
       * scrollHeight 获取元素内容高度(只读)
       * scrollTop 获取或者设置元素的偏移值,常用于, 计算滚动条的位置, 当一个元素的容器没有产生垂直方向的滚动条, 那它的scrollTop的值默认为0.
       * clientHeight 读取元素的可见高度(只读)
       * 如果元素滚动到底, 下面等式返回true, 没有则返回false:
       * ele.scrollHeight - ele.scrollTop === ele.clientHeight;
       */
      const condition =
        this.scrollHeight - this.scrollTop <= this.clientHeight + 1;
      //监听下拉框是否滚动到底部，滚动到底部就加载下一页数据
      if (condition) binding.value();
    });
  },
});
export default {
  name: "CASelect",
  props: {
    // 默认选中值
    selectedValue: {
      default: "",
    },
    // 默认选中值对应的数据，用于回显
    defaultSelectedStr: {},
    // 是否可以多选
    multiple: {
      type: Boolean,
      default: false,
    },
    // 是否可以清空
    clearable: {
      type: Boolean,
      default: false,
    },
    // 远程搜索时，输入框搜索值的键名
    searchKey: {
      type: String,
      default: "searchKey",
    },
    // 获取下拉框值得方法名
    getListMethods: {
      type: Function,
      default: null,
    },
    disabled: {
      type: Boolean,
      default: true,
    },
    // 获取下拉框值时 默认参数
    getListParams: {
      type: Object,
      default: () => ({}),
    },
    // 下拉框值的格式
    getListFormat: {
      type: Function,
      default: (data) => {
        return data.map(({ label, value }) => ({ label, value }));
      },
    },
  },
  data() {
    return {
      list: [],
      activeValue: "",
      searchText: "",
      pageNum: 1,
      pageSize: 10,
      loading: false,
      finished: false, // 是否加载完所有数据
    };
  },
  watch: {
    // 默认选中值
    defaultSelected: {
      handler() {
        this.activeValue = this.defaultSelected;
      },
      immediate: true,
    },

    // 将选中的数据 拼成数组，放在list中，用于解决回显时 非第一页数据无法正常显示的问题
    defaultSelectedStr: {
      handler() {
        this.list = this.list.concat(this.defaultSelectedStr);
      },
      immediate: true,
    },
  },
  // created() {
  //   this.getData();
  // },
  methods: {
    getData(searchText) {
      let isClear = false;
      if (searchText !== this.searchText) {
        isClear = true;
      }
      this.searchText = searchText;
      if (this.finished) return;
      this.loading = true;
      const params = {
        msdstitle: this.searchText,
        nowPage: this.pageNum,
        pageSize: 30,
      };
      getDangerIdIsNotNullListData(params)
        .then((res) => {
          // 对数据进行格式化
          const data = res.data.data.list;
          // data = this.getListFormat(data);
          // 判断是否是最后一页了
          if (data.length < 30) this.finished = true;

          this.list = this.list.concat(data);
          //滚动条回到初始位置
          this.$nextTick(() => {
            if (isClear) {
              document
                .querySelector(".more-select-dropdown")
                .querySelector(".el-scrollbar__wrap")
                .scrollTo(0, 0);
            }
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    searchMethod(searchText) {
      if (searchText === this.searchText) {
        return;
      }

      // 防抖处理
      let timeout = null;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        this.list = [];
        this.pageNum = 1;
        this.finished = false;

        this.getData(searchText);
      }, 500);
    },
    loadData() {
      // 防抖处理
      let timeout = null;
      clearTimeout(timeout);
      timeout = setTimeout(() => {
        if (this.finished) return;
        this.pageNum += 1;
        this.getData(this.searchText);
      }, 500);
    },
    reset() {
      this.activeValue = "";
    },
    selectChange(value) {
      const res = this.list.find((item, index) => item.msdsid === value);
      this.$emit("selectedChange", res);
    },
  },
};
</script>
