<template>
  <div class="workingaccount">
    <component
      :is="$route.name == 'workingAccount' ? 'runningState' : $route.name"
    ></component>
  </div>
</template>

<script>
// import runningState from "./runningState/index";
// import enterpriseRisk from "./enterpriseRisk/index"
// import safetyCommitment from "./safetyCommitment/index"
// import focuscompany from "./focuscompany/index"
// import alarmAnalysis from "./alarmAnalysis"

export default {
  //import引入的组件
  components: {
    runningState: () => import("./runningState"),
    safetyCommitment: () => import("./safetyCommitment"),
    enterpriseRisk: () => import("./enterpriseRisk"),
    focuscompany: () => import("./focuscompany"),
    alarmAnalysis: () => import("./alarmAnalysis"),
    analysisOfMajorHazardSources: () =>
      import("./analysisOfMajorHazardSources"),
    doublePrevention: () => import("./doublePrevention/doublePrevention.vue"),
    videoReport: () => import("./videoReport/videoReport.vue"),
    accidentAnalysis: () => import("./accidentAnalysis/accidentAnalysis.vue"),
    gatherReport: () => import("./smartReport/gatherReport/index.vue"),
  },
  data() {
    return {};
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
