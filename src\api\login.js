import axios from "@/utils/http";
import qs from "qs";

// 登录接口
// export const login = data => {
//     return axios({
//       method: "post",
//       header:{
//         Authorization:  'Basic eXVuOnl1bg=='
//       },
//       url: "/auth/oauth/token?password="+data.password,
//       data: qs.stringify(data)
//     });
// };
export const login = (data) => {
  return axios({
    method: "post",
    //   header:{
    //     Authorization:  'Basic eXVuOnl1bg==',
    //   },
    headers: {
      "Content-Type": "application/json",
    },
    url: "/admin/user/casLogin/mockLogin",
    //   data: qs.stringify(data)
    data: data,
  });
};
export const loginMock = (data) => {
  return axios({
    method: "get",
    url: "/admin/user/casLogin/mockLogin/v3?u=" + data,
  });
};
//修改密码
export const editPsaa = (data) => {
  return axios({
    method: "post",
    url: "/admin/user/changePassword",
    data: qs.stringify(data),
  });
};
//监管权限接口
export const districtByUser = (data) => {
  return axios({
    method: "GET",
    url: "/admin/district/byUser",
  });
};
//园区权限接口
export const adminUserPark = (data) => {
  return axios({
    method: "GET",
    url: "/admin/user/park",
  });
};
//单点登录接口
export const adminUserLoginInfo = (data) => {
  return axios({
    method: "GET",
    url: "/admin/user/login/info",
    data: data,
  });
};
// 企业登录接口
export const adminEnteroginInfo = (headers, data) => {
  return axios({
    method: "GET",
    headers: headers,
    url: `/admin/user/casLogin/enterp/site?enterpId=${data.enterpId}`,
  });
};

export const adminUserParkCode = (data) => {
  return axios({
    method: "GET",
    url: "/admin/district/user",
    data: data,
  });
};
