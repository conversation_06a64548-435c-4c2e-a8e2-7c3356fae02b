<template>
  <div class="enterprise-info-page">
    <div class="enterprise-info-page-content"
         :class="{ 'side-hidden': sideHidden }">
      <!-- 左侧列表 -->
      <el-card class="left-list"
               shadow="never"
               ref="leftBox">
        <div slot="header"
             class="clearfix title">
          <span>目录</span>
          <!--          <i-->
          <!--            class="select-btn"-->
          <!--            :class="sideHidden ? `el-icon-s-unfold` : `el-icon-s-fold`"-->
          <!--            @click="sideHidden = !sideHidden"-->
          <!--          ></i>-->
        </div>
        <div class="tab-list-btn">
          <ul>
            <li v-for="(item, index) in routerList"
                :key="index"
                @click="selectLeftListBtn(item, index)"
                :class="{ active: selectRouterItem == item }"
                :title="item.name">
              <el-tooltip class="item"
                          effect="light"
                          :content="item.name"
                          placement="right">
                <i :class="item.icon"></i>
              </el-tooltip>
              <span>{{ item.name }}</span>
            </li>
            <a class="not-data"
               v-if="!routerList.length">开发中</a>
          </ul>
        </div>
      </el-card>
      <!-- 右侧内容 -->
      <div class="right-page-list-box"
           id="monitoringEquipmentBox">
        <div class="content-box"
             :style="{ position: 'relative' }"
             ref="anchor">
          <!-- 默认企业id入参名称为enterpriseCode -->
          <Sensor ref="sensor"
                  class="div"></Sensor>
          <VideoEquipment ref="videoEquipment"
                          class="div"></VideoEquipment>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// import { getLot, lotExportExcel, getMonitoringMonvalue } from "@/api/entList";
// import { parseTime } from "@/utils/index";
import Sensor from './sensor'
import VideoEquipment from './videoEquipment'
export default {
  //import引入的组件
  name: 'monitoringEquipment',
  components: {
    Sensor,
    VideoEquipment
  },
  data() {
    return {
      sideHidden: false,
      selectRouterItem: {},
      scrollBox: '',
      routerList: [
        {
          name: '传感器', // 二级菜单名称
          icon: 'nav-icon-fyplyzdhxp',
          pageType: 'compontent',
          componentsName: 'sensor'
        },
        {
          name: '视频设备', // 二级菜单名称
          icon: 'nav-icon-zb',
          pageType: 'compontent',
          componentsName: 'videoEquipment'
        }
        // {
        // name: '泄漏点', // 二级菜单名称
        // icon: 'nav-icon-zdhkzxtda',
        // pageType: 'compontent',
        // componentsName: 'leakagePoint',
        // },
        // {
        // name: '仓库', // 二级菜单名称
        // icon: 'nav-icon-ck',
        // pageType: 'compontent',
        // componentsName: 'warehouse',
        // },
      ]
    }
  },
  //方法集合
  methods: {
    // 左侧菜单选中事件
    selectLeftListBtn(item, index) {
      this.selectRouterItem = item
      const jump = jQuery('.div').eq(index)
      let scrollTop = 0
      try {
        if(index== 0){
          scrollTop =0
        }else{
          scrollTop = jQuery('.div').eq(index-1).position().top +jQuery('.div').eq(index-1).height() // 获取需要滚动的距离
        }
        
      } catch (e) {
        scrollTop = jump.top +jump.height() // 获取需要滚动的距离
      };
      // console.warn(scrollTop,'scrollTop')
      // Chrome
      this.scrollBox.scrollTo({
        top: scrollTop,
        behavior: 'smooth' // 平滑滚动
      })
    },
   async getData(id) {
       this.$nextTick(() => {
      this.$refs.sensor.getData(id)
      // this.$refs.sensor.getOrgData();
      this.$refs.sensor.getMonitorEqpmtType()
      // this.$refs.sensor.getDeviceinfoType()
       this.$refs.videoEquipment.getData(id)
      this.$refs.videoEquipment.getPlanLevel()
       document.getElementById('monitoringEquipmentBox').scrollTo({
        top:0
      })
      this.scroll();
      
    })
      // this.$refs.videoEquipment.getOrgData();
    },
    scroll() {
      const that = this
      // 获取滚动dom元素
      this.scrollBox = document.getElementById('monitoringEquipmentBox')
      const jump = jQuery('.div')
      const topArr = []
      for (let i = 0; i < jump.length; i++) {
        topArr.push(jump.eq(i).position().top + jump.eq(i).height()-50 );
          console.log(jump.eq(i).position().top, jump.eq(i).height())
      };
    
      console.log(this.scrollBox)
      // 监听dom元素的scroll事件
      this.scrollBox.addEventListener(
        'scroll',
        () => {
          // debugger;
          const current_offset_top = that.scrollBox.scrollTop
          for (let i = 0; i < topArr.length; i++) {
            if (current_offset_top <= topArr[i]) {
              // 根据滚动距离判断应该滚动到第几个导航的位置
              that.selectRouterItem = that.routerList[i]
              break
            }
          }
        },
        true
      )
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.selectRouterItem = this.routerList[0]
    
  }
}
</script>
<style lang="scss" scoped>
.enterprise-info-page {
  // position: absolute;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .not-data {
    display: block;
    height: 50px;
    line-height: 50px;
    width: 100%;
    text-align: center;
    transform: rotate(-15deg);
    margin-top: 50px;
    //font-size: 18px;
    color: #b1b1b1;
  }
  .enterprise-info-page-content {
    width: 100%;
    height: calc(100vh - 270px);
    display: flex;
    justify-content: space-between;
    /deep/ .el-card__body {
      padding: 0;
    }
    &.side-hidden {
      .left-list {
        width: 60px;
        .title {
          padding: 0;
        }
        span {
          display: none;
        }
        .tab-list-btn {
          i {
            pointer-events: all;
          }
        }
      }
      .right-page-list-box {
        width: 70%;
      }
    }
    .left-list {
      width: 16%;
      height: calc(100% - 40px);

      overflow: hidden;
      /deep/ .el-card__header {
        padding: 0;
      }
      .title {
        height: 47px;
        background: #f5f5f6;
        line-height: 47px;
        overflow: hidden;
        color: #534d6a;
        font-size: 18px;
        font-weight: 700;
        padding: 0 60px 0 19px;
        position: relative;
        .select-btn {
          position: absolute;
          height: 40px;
          width: 40px;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          text-align: center;
          line-height: 40px;
          font-size: 26px;
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
      }
      /deep/.el-card__body {
        height: calc(100%);
      }
      .tab-list-btn {
        list-style: none;
        width: 100%;
        li {
          width: 100%;
          height: 48px;
          line-height: 48px;
          font-size: 14px;
          padding-left: 20px;
          white-space: nowrap;
          overflow: hidden;
          position: relative;
          text-overflow: ellipsis;
          cursor: pointer;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-transition: 0.3s;
          transition: 0.3s;
          color: #534c6a;
          &:hover {
            background: rgba(0, 0, 0, 0.01);
            color: #4a7dff;
          }
          &.active {
            background: #d4e8ff;
            color: #4a7dff;
            i {
              &.nav-icon-zdhkzxtda {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-zdhkzxtda-active.png')
                  no-repeat center center;
              }
              &.nav-icon-csgd {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-csgd-active.png')
                  no-repeat center center;
              }
              &.nav-icon-cg {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-cg-active.png')
                  no-repeat center center;
              }
              &.nav-icon-ck {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-ck-active.png')
                  no-repeat center center;
              }
            }
          }
          i {
            width: 40px;
            height: 40px;
            position: absolute;
            top: 50%;
            line-height: 40px;
            text-align: center;
            transform: translateY(-50%);
            left: 1px;
            font-size: 18px;
            pointer-events: none;
            &.nav-icon-zdhkzxtda {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-zdhkzxtda.png')
                no-repeat center center;
            }
            &.nav-icon-csgd {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-csgd.png')
                no-repeat center center;
            }
            &.nav-icon-cg {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-cg.png')
                no-repeat center center;
            }
            &.nav-icon-ck {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-ck.png')
                no-repeat center center;
            }
          }
        }
      }
    }
    .right-page-list-box {
      transition: 0.3s;
      overflow: auto;
      width: 82%;
      height: calc(100vh - 270px);
      .content-box {
        position: relative;
        // margin-bottom: 3rem;
      }
    }
  }
  .el-menu-demo {
    /deep/ .el-menu-item {
      height: 42px;
      height: 42px;
      line-height: 42px;
    }
  }
}
</style>
<!--<style lang="scss" scoped>-->
<!--.enterprise-info-page {-->
<!--  // position: absolute;-->
<!--  height: 100%;-->
<!--  width: 100%;-->
<!--  overflow: hidden;-->
<!--  .not-data {-->
<!--    display: block;-->
<!--    height: 50px;-->
<!--    line-height: 50px;-->
<!--    width: 100%;-->
<!--    text-align: center;-->
<!--    transform: rotate(-15deg);-->
<!--    margin-top: 50px;-->
<!--    font-size: 18px;-->
<!--    color: #b1b1b1;-->
<!--  }-->
<!--  .enterprise-info-page-content {-->
<!--    position: relative;-->
<!--    width: 100%;-->
<!--    height: calc(100vh - 270px);-->
<!--    /deep/ .el-card__body {-->
<!--      padding: 0;-->
<!--    }-->
<!--    &.side-hidden {-->
<!--      .left-list {-->
<!--        width: 60px;-->
<!--        .title {-->
<!--          padding: 0;-->
<!--        }-->
<!--        span {-->
<!--          display: none;-->
<!--        }-->
<!--        .tab-list-btn {-->
<!--          i {-->
<!--            pointer-events: all;-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--      .right-page-list-box {-->
<!--        width: calc(100% - 60px - 40px - 20px);-->
<!--      }-->
<!--    }-->
<!--    .left-list {-->
<!--      width: 226px;-->
<!--      height: calc(100% - 40px);-->
<!--      position: absolute;-->
<!--      left: 25px;-->
<!--      top: 20px;-->
<!--      overflow: hidden;-->
<!--      /deep/ .el-card__header {-->
<!--        padding: 0;-->
<!--      }-->
<!--      .title {-->
<!--        height: 47px;-->
<!--        background: #f5f5f6;-->
<!--        padding: 0;-->
<!--        line-height: 47px;-->
<!--        padding: 0 19px;-->
<!--        overflow: hidden;-->
<!--        color: #534d6a;-->
<!--        font-size: 18px;-->
<!--        font-weight: 700;-->
<!--        padding-right: 60px;-->
<!--        position: relative;-->
<!--        .select-btn {-->
<!--          position: absolute;-->
<!--          height: 40px;-->
<!--          width: 40px;-->
<!--          right: 10px;-->
<!--          top: 50%;-->
<!--          transform: translateY(-50%);-->
<!--          text-align: center;-->
<!--          line-height: 40px;-->
<!--          font-size: 26px;-->
<!--          cursor: pointer;-->
<!--          &:hover {-->
<!--            opacity: 0.8;-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--      /deep/.el-card__body {-->
<!--        height: calc(100%);-->
<!--      }-->
<!--      .tab-list-btn {-->
<!--        list-style: none;-->
<!--        width: 100%;-->
<!--        height: calc(100% - 50px);-->
<!--        overflow: hidden;-->
<!--        li {-->
<!--          width: 100%;-->
<!--          height: 54px;-->
<!--          line-height: 54px;-->
<!--          font-size: 16px;-->
<!--          padding-left: 40px;-->
<!--          padding-right: 10px;-->
<!--          white-space: nowrap;-->
<!--          overflow: hidden;-->
<!--          position: relative;-->
<!--          text-overflow: ellipsis;-->
<!--          cursor: pointer;-->
<!--          user-select: none;-->
<!--          transition: 0.3s;-->
<!--          color: #534c6a;-->
<!--          &:hover {-->
<!--            background: rgba(0, 0, 0, 0.01);-->
<!--            color: #4a7dff;-->
<!--          }-->
<!--          &.active {-->
<!--            background: #d4e8ff;-->
<!--            color: #4a7dff;-->
<!--            i {-->
<!--              &.nav-icon-zb {-->
<!--                 background: url('/static/img/assets/img/navIcon/nav-icon-zb-active.png') no-repeat;-->
<!--                background-size: 50% 50%;-->
<!--                background-position: center center;-->
<!--              }-->
<!--              &.nav-icon-fyplyzdhxp {-->
<!--                background: url('/static/img/assets/img/navIcon/nav-icon-fyplyzdhxp-active.png') no-repeat;-->
<!--                background-size: 50% 50%;-->
<!--                background-position: center center;-->
<!--              }-->
<!--            }-->
<!--          }-->
<!--          i {-->
<!--            width: 40px;-->
<!--            height: 40px;-->
<!--            position: absolute;-->
<!--            top: 50%;-->
<!--            line-height: 40px;-->
<!--            text-align: center;-->
<!--            transform: translateY(-50%);-->
<!--            left: 1px;-->
<!--            font-size: 18px;-->
<!--            pointer-events: none;-->
<!--            &.nav-icon-zb {-->
<!--              background: url('/static/img/assets/img/navIcon/nav-icon-zb.png') no-repeat;-->
<!--              background-size: 50% 50%;-->
<!--              background-position: center center;-->
<!--            }-->
<!--            &.nav-icon-fyplyzdhxp {-->
<!--              background: url('/static/img/assets/img/navIcon/nav-icon-fyplyzdhxp.png') no-repeat;-->
<!--              background-size: 50% 50%;-->
<!--              background-position: center center;-->
<!--            }-->
<!--          }-->
<!--        }-->
<!--      }-->
<!--    }-->
<!--    .right-page-list-box {-->
<!--      transition: 0.3s;-->
<!--      width: calc(100% - 226px - 40px - 20px);-->
<!--      height: calc(100% - 40px);-->
<!--      min-height: calc(100% - 40px);-->
<!--      position: absolute;-->
<!--      right: 25px;-->
<!--      top: 20px;-->
<!--      overflow: hidden;-->
<!--      iframe {-->
<!--        min-height: 900px;-->
<!--        width: 100%;-->
<!--      }-->
<!--      .content-box {-->
<!--        position: relative;-->
<!--        // margin-bottom: 3rem;-->
<!--      }-->
<!--    }-->
<!--  }-->
<!--  .el-menu-demo {-->
<!--    /deep/ .el-menu-item {-->
<!--      height: 42px;-->
<!--      line-height: 42px;-->
<!--    }-->
<!--  }-->
<!--}-->
<!--</style>-->
