<template>
  <div>
    <erpriseList
      v-show="isEnterprise === 'gov'"
      v-on:entBool="entBool"
      v-on:entId="entId"
      v-on:goTag="goTag"
      ref="gov"
    ></erpriseList>
    <Management
      v-show="isEnterprise === 'ent'"
      ref="ent"
      :activeF="active"
      :enterpId="enterpriseId"
      @type="type"
    >
    </Management>
  </div>
</template>

<script>
import { mapState } from "vuex";
import entManagement from "./entManagement.vue";
import enterpriseList from "./enterpriseList/index";
export default {
  name: "entAndGov",
  components: {
    Management: entManagement,
    erpriseList: enterpriseList,
  },
  data() {
    return {
      isEnterprise: "",
      active: "",
      enterpriseId: "",
    };
  },
  methods: {
    type(type) {
      this.isEnterprise = type;
    },
    entBool(type, name) {
      // debugger;
      this.isEnterprise = type;
      this.active = name;
      this.$store.commit("login/updataActiveName", "basicInformationTab");
    },
    goTag(enterpId, name) {
      //视频跳转
      this.active = name; //废弃，不通过这个设置选项卡
      if (name == "videoInspection") {
        this.$store.commit("login/updataActiveName", "videoInspection");
      } else if (name == "realTimeMonitoring") {
        this.$store.commit("login/updataActiveName", "realTimeMonitoring");
      }

      this.enterpriseId = enterpId;
      console.log(enterpId);
      this.isEnterprise = "ent";
      this.$nextTick(() => {
        this.$refs.ent.getData();
      });
    },
    entId(id) {
      this.enterpriseId = id;
      this.isEnterprise = "ent";
      this.$nextTick(() => {
        this.$refs.ent.getData();
      });
    },
  },

  computed: {
    ...mapState({
      vuexUser: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
      enterpId: (state) => state.controler.vuexEntId,
    }),
  },
  watch: {
    vuexUser: {
      handler(newVal, oldVal) {
        if (newVal.user_type === "ent") {
          this.isEnterprise = "ent";
          this.$nextTick(() => {
            this.$refs.ent.getData();
          });
        } else {
          this.isEnterprise = "gov";
          this.$nextTick(() => {
            this.$refs.gov.getData();
          });
        }
      },
      deep: true,
      immediate: true,
    },
    // enterData: {
    //   handler(newVal, oldVal) {
    //     if (newVal) {
    //       this.enterpriseId = newVal.enterpId;
    //     }
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    enterpId: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.entId(newVal);
          // this.enterpriseId = newVal;
        }
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>

<style scoped></style>
