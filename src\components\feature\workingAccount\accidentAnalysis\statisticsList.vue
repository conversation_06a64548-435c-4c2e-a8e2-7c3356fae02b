<template>
  <div class="statisticsList">
    <div>
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            v-if="isShowDist"
          ></el-cascader>

          <div>
            <el-date-picker
              v-model="dateTime"
              size="mini"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd"
              unlink-panels
              clearable
            >
            </el-date-picker>
          </div>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <!-- <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          > -->
        </div>
      </div>

      <!-- {{distCode + 'distCode-----------'}}
      {{dateTime + 'dateTime-----------'}} -->
      <div class="table-main">
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              :default-sort="{ prop: 'date', order: 'descending' }"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="districtCodeName"
                label="行政区划"
                min-width="150"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.districtCodeName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="事故总数"
                width="150"
                align="center"
                prop="total"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.total }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="危化品火灾事故"
                min-width="150"
                align="center"
                prop="type01"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type01 }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="危化品爆炸事故"
                min-width="150"
                align="center"
                prop="type02"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type02 }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="危化品中毒和窒息事故"
                min-width="150"
                align="center"
                prop="type03"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type03 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="危化品灼伤事故"
                min-width="150"
                align="center"
                prop="type04"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type04 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="危化品泄露事故"
                min-width="150"
                align="center"
                prop="type05"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type05 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="其它危化品事故"
                min-width="150"
                align="center"
                prop="type06"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type06 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="其它类型事故"
                min-width="150"
                align="center"
                prop="type99"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.type99 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="特别重大"
                min-width="100"
                align="center"
                prop="level10000"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.level10000 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="重大"
                min-width="100"
                align="center"
                prop="level20000"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.level20000 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="较大"
                min-width="100"
                align="center"
                prop="level30000"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.level30000 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="一般"
                min-width="100"
                align="center"
                prop="level40000"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.level40000 }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="未定级"
                min-width="100"
                align="center"
                prop="level90000"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.level90000 }}
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <!-- <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.pageSize"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div> -->
        </div>
      </div>
    </div>
    <!-- <alarmDeatil :detailAlarmDialog='detailAlarmDialog' @closeCenterDialog='closeCenterDialog'  ref="alarmDeatil"></alarmDeatil> -->
  </div>
</template>
<script>
import { eventStatisticsList } from "@/api/riskAssessment";
import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  // props: ["distCode", "dateTime"],
  data() {
    return {
      loading: false,
      tableData: {},
      currentPage: 1,
      selection: [],
      pardistCode: this.$store.state.login.userDistCode,
      pardateTime: [],
      dateTime: [],
      district: this.$store.state.controler.district,
      distCode: this.$store.state.login.userDistCode,
    };
  },

  methods: {
    search(){
      this.getData();
    },
    getData() {
      this.getDataes();
    },
    getDataes() {
      console.log(this.dateTime)
      this.loading = true;
      var param = {
        districtCode: this.distCode,
        startDate: this.dateTime ? this.dateTime[0] : "",
        endDate: this.dateTime ? this.dateTime[1] : "",
      };
      eventStatisticsList(param).then((res) => {
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    distCode: {
      handler(newVal, oldVal) {
        if (newVal != "") {
          this.pardateTime = this.pardateTime;
          this.distCode = newVal;
          this.getDataes();
        }
      },
    },
    dateTime: {
      handler(newVal, oldVal) {
        if (newVal != "") {
          this.pardistCode = this.distCode;
          this.pardateTime = newVal;
          this.getDataes();
        }
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.statisticsList {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;
    margin-bottom: 15px;
    // margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
//弹框样式
.warn {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .null {
    width: 467px;
    height: 285px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 50%;
          display: flex;
          // border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 30%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 70%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
.echartTit {
  text-align: right;
  padding: 0 20px 0 0;
  position: absolute;
  right: 0;
  top: 0;
}
</style>

