<template>
  <el-dialog
    title="字段设定"
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="field-settings">
      <!-- 左侧区域 -->
      <div class="left-panel">
        <!-- 报表名称输入框 -->
        <div class="report-name" v-if="isNewReport">
          <div class="panel-title">报表名称</div>
          <el-input
            v-model="reportName"
            placeholder="请输入报表名称"
            size="small"
            :maxlength="50"
            show-word-limit
          ></el-input>
        </div>

        <!-- 数据表选择 -->
        <div class="table-select">
          <div class="panel-title">数据表</div>
          <el-select
            v-model="selectedTable"
            placeholder="请选择数据表"
            size="small"
            @change="handleTableChange"
          >
            <el-option
              v-for="item in tableList"
              :key="item.reportNameCn"
              :label="item.reportNameCn"
              :value="item.reportNameEn"
            >
            </el-option>
          </el-select>
        </div>
        <!-- 可选字段列表 -->
        <div class="field-list">
          <div class="panel-title">可选字段</div>
          <el-transfer
            v-model="transferValue"
            :data="transferData"
            :titles="['可选字段', '已选字段']"
            :button-texts="['移除', '添加']"
            filterable
            filter-placeholder="请输入字段名称"
            @change="handleFieldChange"
          >
          </el-transfer>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "FieldSettingsDialog",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    allFields: {
      type: Array,
      default: () => [],
    },
    tableList: {
      type: Array,
      default: () => [],
    },
    currentFields: {
      type: Array,
      default: () => [],
    },
    isNewReport: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      reportName: "",
      selectedTable: "",
      selectedTableLabel: "",
      transferData: [],
      transferValue: [],
      selectedFields: [],
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.initData();
      }
    },
    allFields: {
      handler() {
        this.convertToTransferData();
      },
      immediate: true,
    },
    selectedFields: {
      handler(newVal) {
        this.transferValue = this.allFields
          .filter((field) => newVal.includes(field[0]))
          .map((field) => field[0]);
      },
      immediate: true,
    },
  },
  methods: {
    handleTableChange(value) {
      const selectedTable = this.tableList.find(
        (item) => item.reportNameEn === value
      );
      this.selectedTableLabel = selectedTable ? selectedTable.reportNameCn : "";
      this.$emit("update:tableList", value);

      // 重新转换数据以应用新的禁用规则
      this.convertToTransferData();
    },
    initData() {
      this.convertToTransferData();
      this.reportName = "";

      if (this.isNewReport) {
        this.transferValue = [];
        this.selectedFields = [];
      } else if (this.currentFields && this.currentFields.length > 0) {
        const selectedKeys = this.allFields
          .filter((field) => this.currentFields.includes(field[0]))
          .map((field) => field[0]);

        this.transferValue = selectedKeys;
        this.selectedFields = this.currentFields;
      } else {
        const initialSelected = this.allFields.map((field) => field[0]);
        this.handleFieldChange(initialSelected);
      }
      if (!this.isNewReport) {
        this.selectedTable = this.tableList[0].reportNameEn;
      }
    },
    convertToTransferData() {
      const result = [];
      this.allFields.forEach((field) => {
        const fieldKey = field[0];
        const fieldLabel = field[1];

        // 对于行政处罚表，eventName字段默认必选且禁用
        const isAdminPunishmentTable =
          this.selectedTable === "admin_punishment_info";
        const isEventNameField = fieldKey === "event_name";
        const shouldDisable = isAdminPunishmentTable && isEventNameField;

        result.push({
          key: fieldKey,
          label: fieldLabel,
          disabled: shouldDisable,
        });
      });
      this.transferData = result;

      // 如果是行政处罚表，确保eventName字段被默认选中
      if (this.selectedTable === "admin_punishment_info") {
        this.ensureEventNameSelected();
      }
    },
    ensureEventNameSelected() {
      // 确保eventName字段被选中
      if (!this.transferValue.includes("event_name")) {
        this.transferValue.push("event_name");
      }

      // 确保selectedFields中也包含eventName
      if (!this.selectedFields.includes("event_name")) {
        this.selectedFields.push("event_name");
        this.$emit("change", this.selectedFields);
      }
    },
    handleFieldChange(value) {
      const newSelectedFields = [];

      value.forEach((key) => {
        const field = this.allFields.find((field) => field[0] === key);
        if (field) {
          newSelectedFields.push(field[0]);
        }
      });

      // 对于行政处罚表，确保eventName字段始终被包含
      if (
        this.selectedTable === "admin_punishment_info" &&
        !newSelectedFields.includes("event_name")
      ) {
        newSelectedFields.push("event_name");
        // 同时更新transferValue
        if (!this.transferValue.includes("event_name")) {
          this.transferValue.push("event_name");
        }
      }

      this.selectedFields = newSelectedFields;
      this.$emit("change", newSelectedFields);
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
    handleSave() {
      if (this.isNewReport && !this.reportName.trim()) {
        this.$message.warning("请输入报表名称");
        return;
      }

      const selectedLabels = this.selectedFields.map((key) => {
        const field = this.allFields.find((field) => field[0] === key);
        return field[1];
      });

      this.$emit("save", {
        selectedFields: this.selectedFields,
        selectedLabels,
        transferValue: this.transferValue,
        selectedTable: this.selectedTable,
        selectedTableLabel: this.selectedTableLabel,
        reportName: this.reportName.trim(),
      });
      this.handleClose();
    },
  },
};
</script>

<style lang="scss" scoped>
.field-settings {
  display: flex;
  height: 500px;
  gap: 20px;

  .left-panel {
    .report-name {
      margin-bottom: 20px;

      .panel-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .el-input {
        width: 100%;
      }
    }

    .table-select {
      margin-bottom: 20px;

      .panel-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
      }

      .el-select {
        width: 100%;
      }
    }

    .field-list {
      .panel-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 10px;
      }
    }
  }
}

::v-deep .el-transfer {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-transfer-panel {
    width: 300px;

    &__header {
      background: #f5f7fa;
    }
  }

  .el-transfer__buttons {
    padding: 0 20px;

    .el-button {
      display: block;
      margin: 10px 0;
    }
  }
}

::v-deep .el-select {
  .el-select__tags {
    max-width: calc(100% - 30px);
  }

  .el-tag {
    margin: 2px;
  }
}
</style>
