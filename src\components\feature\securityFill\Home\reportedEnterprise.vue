<template>
  <div>
    <div class="search-box" v-if="status ===1">
        <div class="select-data">
            <el-select v-model="cityCode1" clearable placeholder="请选择地市" @clear="clearSh" @change="selectCode">
                <el-option
                    v-for="item in option3"
                    :key="item.value"
                    :disabled="item.disabled"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="select-data">
            <el-select v-model="cityCode2" clearable placeholder="请选择区县" @clear="clearSh" :disabled="isdisabled">
                <el-option
                    v-for="item in option4"
                    :key="item.value"
                    :disabled="item.disabled"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="select-data">
            <el-input
                placeholder="请输入企业名称搜索"
                v-model.trim="keyWords"
                clearable>
            </el-input>
        </div>
         <div class="select-time">
            <el-select v-model="auditStatus" clearable placeholder="风险等级" @clear="clearSh" >
                <el-option
                    v-for="item in option1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="select-time">
            <el-select v-model="onTime" clearable placeholder="是否准时上报" @clear="clearSh" >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div>
         <div class="select-time">
            <el-select v-model="dangerLevel" clearable placeholder="重大危险源级别" @clear="clearSh" >
                <el-option
                    v-for="item in options2"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div>
        <div class="seach-btn">
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
        </div>

    </div>
    <div class="table-box" v-if="status ===1">
        <div class="tab-header">
            <el-row>
                <h2>安全承诺</h2>
                <div class="btn-group">
                    <el-button type="primary" icon="el-icon-download">导出</el-button>
                </div>
            </el-row>
        </div>
        <el-table
            :row-key="getRowKeys"
            v-loading="loading"
            style="width: 100%;min-height:300px"
            :default-sort = "{prop: 'date', order: 'descending'}"
            ref="multipleTable" 
            :data="tableData" 
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                :reserve-selection="true"
                width="50">
            </el-table-column>
            <el-table-column
                label="企业名称"
            >
                <template slot-scope="{row, column, $index, store}">
                    <el-tooltip :content="row.enterpName" placement="bottom" effect="light">
                        <span class="linkBtn" @click="entDetail(row.id)">{{row.enterpName}}</span>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                prop="distParentName"
                label="所属地市"
                >
            </el-table-column>
            <el-table-column
                prop="distName"
                label="所属区县"
                >
            </el-table-column>
            <el-table-column
                prop="parkName"
                label="所属园区"
                >
            </el-table-column>
            <el-table-column
                prop="levelRiskName"
                label="风险等级"
                >
            </el-table-column>
            <el-table-column
                prop="levelRiskName2"
                label="企业危险等级"
                >
            </el-table-column>
            <el-table-column
                prop="updateTimeStr"
                label="上报时间"
                >
            </el-table-column>
        </el-table>
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page.pageNo"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            v-if="page.total != 0">
        </el-pagination>
    </div>
    <div class="commitBox" v-if="status ===1">
        <el-button type="primary" icon="el-icon-back" @click="back">返回</el-button>
    </div>
    <!-- 企业详情 -->
    <EnterpriseDetail v-if="status===0" :enterpriseId="enterpriseId"></EnterpriseDetail>
  </div>
</template>

<script>
import EnterpriseDetail from './enterpriseDetail';
import {getSecurityCommitmentList,getRiskList,getDiShi,getEnterprise} from "@/api/reportedList";
import { parseTime} from '@/utils/index';
export default {
    name: 'reportedEnterprise',
    components: {
        EnterpriseDetail
    },
    data() {
      return {
        isdisabled:true,
        activeName: 'reported',
        tableData:[],
        loading: false,
        enterpriseId:'',
        options: [{
          value: '1',
          label: '准时上报'
        }, {
          value: '2',
          label: '未准时上报'
        }],
        option1:[{
            value:'1',
            label:'高风险'
        }, {
            value:'2',
            label:'较高风险'
        }, {
            value:'3',
            label:'一般风险'
        }, {
            value:'4',
            label:'低风险'
        }],
        options2:[{
            value:'1',
            label:'一级',
        },{
            value:'2',
            label:'二级',
        },{
            value:'3',
            label:'三级',
        },{
            value:'4',
            label:'四级',
        }],
        option3:[],
        option4:[],
        auditStatus: '',
        dangerLevel: '',
        cityCode1: '',
        cityCode2: '',
        keyWords:'',
        onTime: '',
        page: {
            pageNo:1,
            pageSize:10,
            total: 100
        },
        newCommiteDate:'',
        status:0,
        dataTime:''
      }
    },
     props: {
        commiteDate:{
            type:String
        }
    },
    created() {
        if (this.$route.name == "enterpriseDetail") {
            this.status = 0;
        } else {
            this.status = 1;
        }  
    },
    watch: {
        $route(newVal, oldVal) {
            if (newVal.name == "enterpriseDetail") {
                this.status = 0;
            } else {
                this.status = 1;
            }
        },
    },
    mounted() {
        //获取地市
        this.getDiShiData();
        //获取上报企业
        this.getCommitment();
        this.dataTime=this.$route.params.id;
    },
    methods: {
        selectCode(values, items){
          this.isdisabled = false;
          this.option4 = [];
          this.cityCode2 = '';
          //获取区县
          getDiShi({
            distCode: values
          })
          .then(data => {
            if(data.data.code=="success"){
                this.option4 = data.data.data;
            }
          }).catch(e => {
            console.log(e, "请求错误");
          });
        },
      // 获取地市
      getDiShiData(){
        getDiShi({
            distCode: this.$store.state.login.userDistCode
        })
        .then(data => {
            if(data.data.code=="success"){
                this.option3 = data.data.data;
            }
        })
        .catch(e => {
          console.log(e, "请求错误");
        });
      },
      // 获取上报企业
      getCommitment(){
        this.loading = true;
        getEnterprise({
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            cityCode1: this.cityCode1,
            cityCode2: this.cityCode2,
            keyWords: this.keyWords,
            auditStatus: this.auditStatus,
            onTime: this.onTime,
            dangerLevel:this.dangerLevel
        },{
            id: this.commiteDate || this.$route.params.id,
            type: 0
        })
        .then(data => {
            if(data.data.code=="success"){
                this.loading = false;
                let listData = data.data.data.rows;
                this.page.total = data.data.data.total;
                this.tableData = listData;
            }
        })
        .catch(e => {
          console.log(e, "请求错误");
        });
      },
      checkList(val){
          this.getCommitment();
      },
      clearSh(){
        this.onTime = '';
      },
      search(){
        this.page.pageNo = 1
        this.page.pageSize = 10
        this.getCommitment();   
      },
      /**
      * 切换分页尺寸
      * @param val
      */
      handleSizeChange(val) {
        this.page.pageSize = val;
        this.getCommitment();
        this.$refs.multipleTable.clearSelection();
      },
      /**
      * 切换当前页
      * @param val
      */
      handleCurrentChange(val) {
        this.page.pageNo = val;
        this.getCommitment();
        this.$refs.multipleTable.clearSelection();
      },
      handleClick(tab, event) {
        console.log(tab, event);
      },
      getRowKeys(row) {
        return row.id
      },
      handleSelectionChange(val) {
        let vlength = val.length
        this.multipleSelection = val
        this.piLiangIds = [];
        this.multipleSelection.forEach(item => {
            this.piLiangIds.push(item.id)
        });
        this.checkAll = vlength === this.page.size
        this.isIndeterminate = vlength > 0 && vlength < this.page.size
     },
     back(){
        this.$router.push({ path: '/Home'}); 
     },
     entDetail(val){
       this.status = 0;
       this.enterpriseId = val;
     }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.search-box{
    padding-bottom:10px;
    background: #fff;
    /* padding-top: 20px; */
    margin-bottom: 20px;
        padding: 0 20px 20px 20px;
}
.select-data{
   display: inline-block;
   /* margin-right: 5px; */
}
.select-time{
    display: inline-block;
}
.seach-btn{
    display: inline-block;
    float: right;
}
.tab-header{
    padding: 15px 19px 15px 30px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
}
.tab-header h2{
    display: inline-block;
    margin-top: 10px;
    font-size: 18px;
    color: #000;
}
.btn-group{
    float: right;
    display: inline-block;
    vertical-align: middle;
}
.table-box{
    padding-bottom: 70px;
}
.oneColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: orangered;
    color: white;
    border-radius: 4em;
}
.twoColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: orange;
    color: white;
    border-radius: 4em;
}
.threeColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: yellowgreen;
    color: white;
    border-radius: 4em;
}
.fourColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: blue;
    color: white;
    border-radius: 4em;
}
.linkBtn{
    color: #2892e2;
    text-decoration: underline;
    cursor: pointer;
    white-space: nowrap
}
.commitBox{
    width: calc(100% - 216px);
    margin-left: 216px;
    height: 70px;
    justify-content: center;
    align-items: center;
    display: flex;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
    background-color: #fff;
    border-top: 1px solid #dfe0e0;
}
</style>

<style scoped>
.el-tabs__nav-wrap::after{
    height: 0px!important;
}

.el-tabs__nav-wrap {
    padding: 20px;
    background: #fff;
}
.el-tabs__header {
    margin: 0;
}

.el-table th > .cell {
    text-align: center;
}
 
.el-table .cell {
    text-align: center;
}  
.el-pagination {
    background: #fff;
    text-align: right;
    padding: 20px;
}
.el-pagination .el-select .el-input ,.el-pagination__sizes{
    display: none!important;
} 

</style>
