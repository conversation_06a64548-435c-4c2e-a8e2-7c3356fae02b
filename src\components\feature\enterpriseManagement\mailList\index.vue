<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" /> 通讯录
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="duty-list padding_28">
      <div class="duty-list-left">
        <el-row class="tab">
          <el-col :span="8">
            <el-button
              @click="typeClick(1, true)"
              size="small"
              :type="treeType == 1 ? 'primary' : ''"
              style="width: 100%"
              v-if="$store.state.login.user.user_type == 'gov'"
              >政务侧</el-button
            >
          </el-col>
          <el-col :span="8">
            <el-button
              @click="typeClick(2, true)"
              size="small"
              :type="treeType == 2 ? 'primary' : ''"
              style="width: 100%"
              >企业侧
            </el-button>
          </el-col>
          <el-col :span="8">
            <el-button
              @click="typeClick(3, true)"
              size="small"
              :type="treeType == 3 ? 'primary' : ''"
              style="width: 100%"
              >通讯组
            </el-button>
          </el-col>
        </el-row>
        <el-row class="tab" v-if="treeType == 3">
          <el-col :span="8">
            <el-popover
              placement="right"
              width="350"
              v-model="orgGroupInfo.addVisible"
              @show="openAddOrgGroupFun"
            >
              <el-form
                :model="orgGroupInfo"
                :rules="orgGroupInfo.addGroupNameRules"
                label-width="60px"
                ref="orgGroupAddFormRef"
              >
                <el-form-item label="名称" prop="addGroupName">
                  <el-input
                    v-model.trim="orgGroupInfo.addGroupName"
                    :maxlength="20"
                    :autofocus="true"
                    placeholder="请输入通讯组名称"
                  ></el-input>
                </el-form-item>
              </el-form>
              <div style="text-align: center">
                <el-button
                  size="mini"
                  @click="closeAddOrgGroupFun"
                  icon="el-icon-close"
                  >关闭</el-button
                >
                <el-button
                  type="primary"
                  size="mini"
                  @click="addOrgGroupFun"
                  icon="el-icon-check"
                  >确定</el-button
                >
              </div>
              <el-button
                slot="reference"
                size="small"
                type="success"
                plain
                icon="el-icon-plus"
                >新增</el-button
              >
            </el-popover>
          </el-col>
          <el-col :span="8">
            <el-popover
              placement="right"
              width="350"
              v-model="orgGroupInfo.editVisible"
              @show="openOrgGroupEditFun"
            >
              <el-form
                :model="orgGroupInfo"
                :rules="orgGroupInfo.groupNameRules"
                label-width="60px"
                ref="orgGroupEditFormRef"
              >
                <el-form-item label="名称" prop="groupName">
                  <el-input
                    v-model.trim="orgGroupInfo.groupName"
                    :autofocus="true"
                    placeholder="请输入通讯组名称"
                    :maxlength="20"
                  >
                  </el-input>
                </el-form-item>
              </el-form>
              <div style="text-align: center">
                <el-button
                  size="mini"
                  @click="closeAddOrgGroupFun"
                  icon="el-icon-close"
                  >关闭</el-button
                >
                <el-button
                  type="primary"
                  size="mini"
                  @click="editOrgGroupFun"
                  icon="el-icon-check"
                  >确定</el-button
                >
              </div>
              <el-button
                slot="reference"
                size="small"
                type="primary"
                plain
                icon="el-icon-edit"
                >编辑</el-button
              >
            </el-popover>
          </el-col>
          <el-col :span="8">
            <el-button
              @click="deleteFun($event)"
              size="small"
              type="warning"
              plain
              icon="el-icon-delete"
              >删除</el-button
            >
          </el-col>
        </el-row>
        <!-- <search-tree :defaultprops="defaultprops" ref="searchtree" nodekey="orgCode" :lazyload="false" :treedata="treeData"
            @change="getOrg" :defaultchecked="defaultchecked">
            </search-tree> -->

        <div class="search_tree">
          <div class="search_slide_input">
            <div class="search_slide_searchKey">
              <!-- 与严雨露商议暂时隐藏搜索图标 Modify-程云 2019-12-2 -->
              <!-- <el-input @focus="searchKey = false" @input="changeSearchStatus" ref="input" v-model.trim="slideValue" type="text" suffix-icon="el-icon--right el-icon-search" placeholder="请输入机构名称"> -->
              <el-input
                v-model.trim="filterText"
                placeholder="请输入需要查询的机构名称"
                clearable
                maxlength="10"
                show-word-limit
              >
              </el-input>
              <!-- <div id="out_contain" class="tabs_list" v-show="searchKey">
                        <el-scrollbar>
                        <ul id="filter_list" class="filter_list">
                            <div v-if="searchList.length == 0" style="text-align:center;height:100%;padding:12px 0">
                            <span>
                                未搜索到数据
                            </span>
                            </div>
                            <li v-for="(item,index) in searchList" @click.stop="handleSelect(item,index)">
                            <span class="filter_list_content">{{item.fullOrgName || item.orgName}}</span>
                            </li>
                        </ul>
                        </el-scrollbar>
                    </div>
                    <div class="popper__arrow" v-show="searchKey" style="left: 35px;"></div> -->
            </div>
            <div class="search_slide" style="opacity:1'">
              <!-- <div v-if="configtree ? configtree.buttonShow || false : false" class="controllab">
                        <el-button v-if="buttonItem.expression" size="mini" :title="buttonItem.title" :icon="buttonItem.icon"
                        :type="buttonItem.type || text" v-for="buttonItem,btnInd in configtree.buttonConfig"
                        @click="operateFun(buttonItem.emit,buttonItem)" :key="btnInd" plain>{{buttonItem.name}}</el-button>
                    </div> -->

              <el-scrollbar id="mainheight">
                <div class="headertitle">
                  <slot name="header"></slot>
                </div>
                <div class="navtitle">
                  <slot name="nav"></slot>
                </div>
                <el-tree
                  class="filter-tree"
                  :data="treeData"
                  v-loading="loadingTree"
                  :props="defaultProps"
                  node-key="id"
                  :default-expanded-keys="defaultShowNodes"
                  :filter-node-method="filterNode"
                  @node-click="handleNodeClick"
                  ref="tree"
                >
                </el-tree>
                <!-- <el-tree :class="!!lazyload?'lazy_tree':''" :empty-text="placeholderText" ref="tree" :data="treeData"
                        highlight-current :node-key="nodekey || 'id'" :default-expanded-keys="[defaultcheckeds]"
                        :current-node-key="defaultcheckeds" @node-expand="treeLoad" :expand-on-click-node="false"
                        :props="defaultprops || getDefaultProps" @node-click="handleNodeClick">
                        <span :title="scope.node.label" v-if="!!lazyload" slot-scope="scope" class="custom-tree-node">
                            <i v-if="scope.data.leaf" class="header-drops-i headerdrops"></i>
                            <i v-if="!scope.data.leaf" @click.stop="treeLoad(scope.data,scope.node)"
                            :class="[!!scope.node.expanded && scope.data.children.length >0 ?'el-icon-caret-bottom':'el-icon-caret-right',temp.style.tree_icon]"></i>
                            <span>{{scope.node.label}}</span>
                        </span>
                        <span v-else class="custom-tree-node" :title="scope.node.label">
                            <span>{{scope.node.label}}</span>
                        </span>
                        </el-tree> -->
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
      <div class="duty-list-right flex-full">
        <div class="search_filter padding_0">
          <div class="maillist-title">人员信息</div>
          <div class="right_filter">
            <!-- <el-button type="primary" icon="el-icon-mobile-phone" @click="addressCall(null)">自定义拨号
                    </el-button> -->
            <el-button type="primary" @click="openOrgUserOperateDialog"
              >数据维护</el-button
            >
            <el-input
              placeholder="可按姓名、职务、办公电话过滤"
              v-model.trim="searchData.key"
              class="search_input"
              clearable
              @keyup.enter.native="searchByName"
              style="width: 300px"
              maxlength="11"
            >
              <el-button style="cursor: pointer;" slot="append" @click="searchByName" class="el-icon-search"></el-button>
            </el-input>
          </div>
        </div>
        <div class="list_contain">
          <!-- 表 -->
          <div class="list_top">
            <div class="list_left flex-full">
              <!-- <list-table :propdata="propData" @tablecallback="tablecallback" unsortable="true"
                    @handleselection="handleSelectionData" :defaultselectfalse="defaultselect" rowkey="personId"></list-table> -->
              <el-scrollbar>
                <div class="table">
                  <el-table
                    :data="personData"
                    :header-cell-style="{
                      background: '#F1F6FF',
                      color: '#333',
                    }"
                    border
                    v-loading="loadingTable"
                    style="width: 100%"
                    ref="multipleTable"
                    @selection-change="handleSelectionChange"
                  >
                    <el-table-column
                      type="selection"
                      width="55"
                      fixed="left"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      label="姓名"
                      align="center"
                      :show-overflow-tooltip="true"
                      width="200px"
                    >
                      <template slot-scope="scope">
                        <i
                          class="iconfont quanhui qh-gaojing2"
                          v-if="treeType == 2 && scope.row.pushFlag == 1"
                          style="color: red"
                        ></i>
                        {{ scope.row.personName }}
                      </template>
                    </el-table-column>
                    <el-table-column
                      width="100px"
                      prop="post"
                      label="职务"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="company"
                      label="人员单位"
                      align="center"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="phone"
                      label="办公电话"
                      align="center"
                      min-width="100"
                    >
                    </el-table-column>
                    <el-table-column
                      prop="telephone"
                      label="手机"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      v-if="treeType == 3"
                      label="操作"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <span
                          style="
                            color: rgb(57, 119, 234);
                            margin-right: 10px;
                            cursor: pointer;
                          "
                          @click="deletePersonFromOrgGroup(scope.row)"
                          >删除</span
                        >
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-if="treeType == 2"
                      label="操作"
                      align="center"
                    >
                      <template slot-scope="scope">
                        <span
                          v-if="scope.row.pushFlag == 1"
                          style="
                            color: rgb(57, 119, 234);
                            margin-right: 10px;
                            cursor: pointer;
                          "
                          @click.stop="pushMessage(scope.row)"
                          >移除消息推送</span
                        >
                        <span
                          v-else
                          style="
                            color: rgb(57, 119, 234);
                            margin-right: 10px;
                            cursor: pointer;
                          "
                          @click.stop="removeMessage(scope.row)"
                          >指定消息推送</span
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-scrollbar>
            </div>
            <div class="pagination">
              <el-pagination
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="size"
                v-show="total > 0"
                layout="total, prev, pager, next"
                background
                :total="total"
              >
              </el-pagination>
            </div>
          </div>
          <!-- 已选联系人 -->
          <div class="list_bottom_box">
            <div class="bottom_list">
              <div class="bottom_title">已选择联系人</div>
              <div class="bottom_concant_list">
                <el-scrollbar>
                  <div class="bottom_flex_group">
                    <div v-for="(item, index) in activePhone" :key="index">
                      <div class="concat_inner">
                        <div class="cancat_info">
                          <el-tooltip
                            effect="light"
                            :content="
                              item.personName +
                              ' ' +
                              item.telephone +
                              ' ' +
                              (item.posttxt || '')
                            "
                            placement="top"
                          >
                            <div class="ellipsis person_title">
                              <p>{{ item.personName }}</p>
                            </div>
                          </el-tooltip>
                          <div class="person_info">
                            <el-tooltip
                              effect="light"
                              :content="item.telephone"
                              placement="top"
                            >
                              <span class="ellipsis person_info_span">{{
                                item.telephone
                              }}</span>
                            </el-tooltip>
                            <el-tooltip
                              effect="light"
                              :content="item.posttxt || ''"
                              placement=" top "
                            >
                              <span class="ellipsis person_info_span">{{
                                item.posttxt || ""
                              }}</span>
                            </el-tooltip>
                          </div>
                        </div>
                      </div>
                      <i
                        class="el-icon-close"
                        @click="cancelSelectPhone(item, index)"
                      ></i>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
            <div class="bottom_btn_group" v-if="treeType != 3">
              <!-- <div @click="sendmessage(null) ">
                <i class="el-icon-s-comment "
                   style="margin-top: 3px;margin-right: 5px;color:#5588fe "> </i> 短信群发
              </div> -->
              <div @click="openaddPersonToOrgToGroup">
                <i
                  class="el-icon-plus"
                  style="margin-top: 3px; margin-right: 5px; color: #5588fe"
                ></i>
                新增到组
              </div>
            </div>
          </div>
        </div>
        <!-- 发送短信/电话会议 机构树新增和编辑弹框-->
        <!-- <el-dialog top='4vh' width="80% " :title="dialogConfig.tilteName " :visible.sync="dialogConfig.viewDialog "
            :close-on-click-modal="false" :close-on-press-escape="dialogConfig.escap " :show-close="dialogConfig.close "
            v-if="dialogConfig.viewDialog " @close='closeDialog' modal-append-to-body>
            <component :is="dialogConfig.templateName " :propdata="parentData " @dialogcallback="closeDialogCall ">
            </component>
            </el-dialog> -->

        <!-- 云会议-->
        <!-- <el-dialog top='4vh' width="80% " title="云会议 " :visible.sync="cloudConfig.viewDialog " :close-on-click-modal="false"
            :close-on-press-escape="false " :show-close="false " v-if="cloudConfig.viewDialog ">
            <cloudference-dialog :propdata="cloudConfig.data " :people="activePhone " @dialogcallback="closeDialogCall ">
            </cloudference-dialog>
            </el-dialog> -->

        <!-- 拨号盘 -->
        <!-- <el-dialog title="拨打电话 " width="388px " :visible.sync="phonePanelVisible " :before-close="closePhone "
            :close-on-click-modal="false" :close-on-press-escape="false " v-if="phonePanelVisible ">
            <phone-panel :callnumber="callnumber "></phone-panel>
            </el-dialog>
            <add-user ref="addUserRef " @ok="addUserToOrgCall "></add-user>
            <sms-modal ref="smsModalRef " :allowchange="false " :disabledperson="true "></sms-modal> -->
        <el-dialog
          title="选择通讯录组 "
          :visible.sync="popoverOrgGroup.visible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          v-if="popoverOrgGroup.visible"
        >
          <el-form
            :model="popoverOrgGroup"
            :rules="popoverOrgGroup.groupIdRules"
          >
            <el-form-item label="通讯录组：" prop="groupId">
              <el-select
                v-model="popoverOrgGroup.groupId"
                placeholder="请选择通讯录组"
                :popper-append-to-body="false"
              >
                <el-option
                  :label="item.groupName"
                  :value="item.groupId"
                  v-for="(item, index) in addressList"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" style="text-align: center">
            <el-button
              size="mini "
              icon="el-icon-close "
              @click="popoverOrgGroup.visible = false"
              >关闭</el-button
            >
            <el-button
              type="primary "
              size="mini "
              icon="el-icon-check "
              @click="addPersonToOrgToGroup"
              >确定</el-button
            >
          </div>
        </el-dialog>
      </div>
    </div>
    <DataMaintenance
      @refreshPreInfo="refreshPreInfoFn"
      ref="dataMaintenance"
    ></DataMaintenance>
  </div>
</template>
<script>
import DataMaintenance from "./dataMaintenance";
import {
  getMailListTreeData,
  getMailListTreeDataForPerson,
  personlitv1,
  getGroupListData,
  getSelPersonByOrgGroupData,
  addOrgGroup,
  deleteOrgGroup,
  addToAddressList,
  movePersonsFromGroup,
  pushMessageFn,
} from "@/api/mailList";
export default {
  name: "mailList",
  components: {
    DataMaintenance,
  },
  data() {
    return {
      defaultShowNodes: [],
      orgGroupInfo: {
        addVisible: false,
        addGroupName: "",
        groupName: "",
        sort: 0,
        editVisible: false,
        addGroupNameRules: {
          addGroupName: [
            { required: true, message: "请输入通讯组名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "通讯组名称最大长度为20",
              trigger: "blur",
            },
          ],
        },
        groupNameRules: {
          groupName: [
            { required: true, message: "请输入通讯组名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "通讯组名称最大长度为20",
              trigger: "blur",
            },
          ],
        },
      },
      defaultprops: {
        children: "children",
        label: "orgName",
      },
      searchData: {
        key: "",
        orgCode: "",
        groupId: "",
        nowPage: 1,
        pageSize: 5,
      },
      treeData: [],
      dialogConfig: {
        viewDialog: false, //弹框是否显示
        templateName: "", //弹框组件名
        tilteName: "", //标题头
        model: true,
        escap: true,
        close: true,
      },
      treeType: null,
      filterText: "",
      defaultProps: {
        children: "children",
        label: "label",
      },
      total: 0,
      orgType: "",
      currentPage: 1,
      size: 10,
      personData: [],
      loadingTable: false,
      loadingTree: true,
      selOrgGroupInfo: {
        groupId: "",
        groupName: "",
        sort: 0, // 排序
      },
      activePhone: [],
      popoverOrgGroup: {
        visible: false,
        groupId: "",
        groupIdRules: {
          groupId: [
            {
              required: true,
              message: "请选择通讯录组",
              trigger: "blur",
            },
          ],
        },
      },
      addressList: [], // 通讯录
    };
  },

  mounted() {
    this.getOrgGroupList();
    // this.orgType = JSON.parse(sessionStorage.getItem("role")).orgType;
    this.orgType = this.$store.state.login.user.user_type == "gov" ? 1 : 0;
    this.treeType = this.$store.state.login.user.user_type == "gov" ? 1 : 2;
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    treeData: {
      handler() {
        // 我这里默认展开一级, 指定几级就往里遍历几层取到 id 就可以了
        this.treeData.forEach((item) => {
          if (item.children) {
            if (item.children[0].children) {
              this.defaultShowNodes.push(item.children[0].children[0].id);
            }
          }
        });
      },
      deep: true,
    },
    "searchData.key"(val) {
      if (!val || val == "") {
        this.searchByName();
      }
    },
  },

  methods: {
    pushMessage(row) {
      pushMessageFn({
        personId: row.personId,
      }).then((res) => {
        if (res.data.status == "200") {
          this.searchByName();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    removeMessage(row) {
      this.$confirm("消息推送接收人会默认置顶，最多指定10位！", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          pushMessageFn({
            personId: row.personId,
          }).then((res) => {
            if (res.data.status == "200") {
              this.searchByName();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    getOrgGroupList() {
      this.loadingTree = true;
      getMailListTreeData({
        type: this.$store.state.login.user.user_type == "gov" ? 1 : 2,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTree = false;
          this.treeData = res.data.data;
          this.personInfo(res.data.data[0].orgCode);
        }
      });
    },
    getAddressList() {
      getGroupListData().then((res) => {
        if (res.data.status == 200) {
          res.data.data = res.data.data.map((it) => {
            return {
              ...it,
              orgName: it.groupName,
              groupId: it.groupId,
              groupName: it.groupName,
              orgCode: it.orgCode, //groupId
              label: it.groupName,
            };
          });
          this.addressList = res.data.data;
          if (this.addressList.length > 0) {
            this.popoverOrgGroup.groupId = this.addressList[0].groupId;
          }
        }
      });
    },
    handleNodeClick(item) {
      debugger;
      this.selectData = item;
      this.currentPage = 1;
      this.searchData.key = "";
      if (this.treeType == 3) {
        this.getSelPersonByOrgGroup(item.groupId);
        this.selOrgGroupInfo = item;
      } else {
        this.personInfo(item.orgCode);
      }
    },
    personInfo(orgCode) {
      this.searchData.orgCode = orgCode;
      this.loadingTable = true;

      debugger;
      console.log(this.treeType, "aboooooooooooooooooooooooooooo");
      //personlitv1  getMailListTreeDataForPerson
      if (this.treeType == 3) {
        personlitv1({
          key: this.searchData.key,
          nowPage: this.currentPage,
          groupId: this.searchData.groupId,
          pageSize: this.size,
        }).then((res) => {
          if (res.data.status == 200) {
            this.loadingTable = false;
            this.personData = res.data.data.list;
            this.total = res.data.data.total;
          }
        });
      } else {
        getMailListTreeDataForPerson({
          key: this.searchData.key,
          nowPage: this.currentPage,
          orgCode: orgCode,
          pageSize: this.size,
        }).then((res) => {
          if (res.data.status == 200) {
            this.loadingTable = false;
            this.personData = res.data.data.list;
            this.total = res.data.data.total;
          }
        });
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleSelectionChange(val) {
      this.activePhone = val;
    },
    openaddPersonToOrgToGroup() {
      console.log("++++++++选择人员", this.activePhone);
      if (!this.activePhone.length) {
        this.$message.warning("请选择人员");
        return;
      }
      this.popoverOrgGroup.visible = true;
      this.getAddressList();
    },
    addPersonToOrgToGroup() {
      let personIds = [];
      this.activePhone.forEach((item) => {
        personIds.push(item.personId);
      });
      addToAddressList({
        groupId: this.popoverOrgGroup.groupId,
        groupType: "1",
        ids: personIds,
      }).then((res) => {
        if (res.data.status == 200) {
          console.log("++++++++addToAddressList=====>", res);
          this.$message.success(res.data.msg || "关联成功~");
          this.popoverOrgGroup.visible = false;
        } else {
          this.$message.error(res.data.msg || "关联失败~");
        }
      });
    },
    sendmessage(val) {
      // if (this.activePhone.length === 0) {
      //   this.$message.warning('请选择人员');
      //   return;
      // }
      // // 发送短信sms-modal组件
      // (this.$refs as any).smsModalRef.open(this.activePhone);
      // return;
      // let datar = []
      // if (!!val)
      // datar.push(val.rowVal)
      // else {
      // if (!this.activePhone.length) {
      //     if (this.messageDom)
      //     this.messageDom.close()
      //     return this.messageDom = this.$message.warning('请选择人员')
      // }
      // datar = this.activePhone
      // }
      // let data = {
      // viewDialog: true,
      // templateName: 'duty-address-book-send',
      // tilteName: '发送短信',
      // model: true,
      // escap: true,
      // close: true
      // }
      // // console.log(datar)
      // this.$set(this.parentData, 'data', datar)
      // this.$set(this, 'dialogConfig', data)
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.personInfo(this.searchData.orgCode);
      // this.search();
    },
    refreshPreInfoFn() {
      this.personInfo(this.searchData.orgCode);
    },
    // 树类型切换
    typeClick(type, isFirst) {
      this.filterText = "";
      this.searchData.key = "";
      this.treeType = type;
      // if (this.$refs.searchtree) {
      // this.$refs.searchtree.slideValue = '';
      // }
      this.treeData = [];
      this.personData = [];
      this.total = 0;
      if (type != 3) {
        this.loadingTree = true;
        getMailListTreeData({
          type: this.treeType,
        }).then((res) => {
          if (res.data.status == 200) {
            this.loadingTree = false;
            if (this.orgType != 1) {
              this.treeData = res.data.data;
            } else {
              this.treeData = res.data.data[0].children;
            }
            // this.treeData = res.data.data;
            this.defaultchecked = this.defaultchecked;
            this.personInfo(res.data.data[0].orgCode);
            // if (isFirst) {
            //     this.defaultchecked = res.data[0].orgCode;
            //     this.getOrg({ prop: res.data[0] });
            // }
          } else {
            this.$message.error(res.msg);
          }
        });
        return;
      }
      this.getOrgGroupListes(isFirst);
    },
    // 获取通讯录组数据
    getOrgGroupListes(isFirst) {
      this.loadingTree = true;
      getGroupListData().then((res) => {
        if (res.data.status == 200) {
          this.loadingTree = false;
          res.data.data = res.data.data.map((it) => {
            return {
              ...it,
              orgName: it.groupName,
              groupId: it.groupId,
              groupName: it.groupName,
              orgCode: it.orgCode, //
              label: it.groupName,
            };
          });
          if (this.treeType == 3) {
            this.treeData = res.data.data;
            // this.defaultchecked = this.selOrgGroupInfo.groupId;
          }
          if (res.data.data[0]) {
            this.getSelPersonByOrgGroup(res.data.data[0].groupId);
            this.selOrgGroupInfo = res.data.data[0];
          }
          // this.popoverOrgGroup.groupList = res.data.data;
        } else {
          // this.popoverOrgGroup.groupList = [];
          // this.$message.error(res.msg);
        }
      });
    },
    getSelPersonByOrgGroup(groupId) {
      this.searchData.groupId = groupId;
      this.loadingTable = true;
      personlitv1({
        key: this.searchData.key,
        groupId: groupId,
        nowPage: this.currentPage,
        pageSize: this.size,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTable = false;
          this.personData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    searchByName() {
      this.currentPage = 1;
      this.personInfo(this.searchData.orgCode);
    },
    openAddOrgGroupFun() {
      // this.orgGroupInfo.groupName = '';
      this.$refs.orgGroupAddFormRef["resetFields"]();
    },
    // 打开机构树和人员的增删改页面
    openOrgUserOperateDialog() {
      // this.dialogConfig = {
      //     viewDialog: true, //弹框是否显示
      //     templateName: 'org-and-user-operate', //弹框组件名
      //     tilteName: '组织及人员信息维护', //标题头
      //     model: true,
      //     escap: true,
      //     close: true
      // }
      this.$refs.dataMaintenance.closeBoolean(true);
      this.$refs.dataMaintenance.getZidian();
    },
    // 根据通讯组-添加-关闭按钮点击事件
    closeAddOrgGroupFun() {
      this.orgGroupInfo.addGroupName = "";
      this.orgGroupInfo.sort = 0;
      this.orgGroupInfo.groupName = "";
      this.orgGroupInfo.addVisible = false;
      this.orgGroupInfo.editVisible = false;
      this.$refs.orgGroupAddFormRef.resetFields();
      this.$refs.orgGroupEditFormRef.resetFields();
    },
    // 根据通讯组-添加
    addOrgGroupFun() {
      this.$refs.orgGroupAddFormRef.validate((flag) => {
        if (flag) {
          let params = {
            groupId: "",
            groupName: this.orgGroupInfo.addGroupName,
            sort: this.orgGroupInfo.sort,
            groupType: "1",
          };
          addOrgGroup(params).then((res) => {
            if (res.data.status == 200) {
              this.$refs.orgGroupAddFormRef.resetFields();
              this.orgGroupInfo.addVisible = false;
              this.getOrgGroupListes();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    openOrgGroupEditFun() {
      this.$refs.orgGroupEditFormRef["resetFields"]();
      if (!this.selOrgGroupInfo.groupId) {
        this.orgGroupInfo.editVisible = false;
        this.$message.warning("请先选择通讯录组");
        return;
      }
      this.orgGroupInfo = {
        ...this.orgGroupInfo,
        ...this.selOrgGroupInfo,
      };
      this.orgGroupInfo.editVisible = true;
    },
    // 根据通讯组-编辑
    editOrgGroupFun() {
      if (!this.selOrgGroupInfo.groupId) {
        this.$message.warning("请先选择通讯录组");
        return;
      }
      this.$refs.orgGroupEditFormRef.validate((flag) => {
        if (!flag) {
          return;
        }
        this.selOrgGroupInfo.groupName = this.orgGroupInfo.groupName;
        this.selOrgGroupInfo.sort = this.orgGroupInfo.sort;
        let params = {
          groupId: "",
          groupName: "",
          groupType: "1", // 要求前端写死
          orgCode: "", // 不传
          sort: 0,
          ...this.selOrgGroupInfo,
        };
        addOrgGroup(params).then((res) => {
          if (res.data.status == 200) {
            this.orgGroupInfo.editVisible = false;
            this.getOrgGroupListes();
            return;
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    // 根据通讯组-删除
    deleteFun($event) {
      if (!this.selOrgGroupInfo.groupId) {
        this.$message.warning("请先选择通讯录组");
        return;
      }
      this.$confirm("确认删除该数据？", "提示", { type: "warning" })
        .then(() => {
          let params = {
            id: this.selOrgGroupInfo.groupId,
          };
          deleteOrgGroup(params).then((res) => {
            if (res.data.status == 200) {
              this.getOrgGroupListes();
              return;
            }
            this.$message.error(res.msg);
          });
        })
        .catch(() => {})
        .finally(() => {
          this.$nextTick(() => {
            document.getElementsByClassName("el-button--warning")[0]["blur"]();
          });
        });
    },
    /**
     * 删除已选人员
     * author by liuwenlei
     */
    cancelSelectPhone(val, index) {
      let data = {
        groupId: val.groupId,
        personId: val.personId,
      };
      this.$confirm("是否取消当前已选联系人?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.activePhone.splice(index, 1);
          // this.defaultselect = val
          this.$refs.multipleTable.toggleRowSelection(this.personData[index]);
        })
        .catch(() => {});
    },
    // 添加人员到通讯录机构树中
    addPersonToOrg() {
      if (!this.searchData.orgCode) {
        this.$message.warning("请先选择机构！");
        return;
      }
      this.$refs.addUserRef["open"]({ ...this.selOrgInfo, id: "" });
    },
    // 编辑人员-通讯录机构树中
    editPerson(data) {
      this.$refs.addUserRef["open"]({ ...data.rowVal });
    },
    // 删除人员-通讯录机构树中
    deletePerson(data) {
      this.$confirm("确认删除该数据？", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmButtonClass",
        cancelButtonClass: "confirmButtonClass",
      }).then(() => {
        this.http.AddressBookRequest.deletePersonNewtoOrg(data.rowVal.id).then(
          (res) => {
            if (res.status == 200) {
              this.getListData();
            } else {
              this.$message({
                type: "error",
                message: res.msg ? res.msg : "删除失败",
              });
            }
          }
        );
      });
    },
    // 从通讯录组中删除人员
    deletePersonFromOrgGroup(data) {
      console.log("从通讯录组中删除人员------->", data);
      this.$confirm("确认删除该数据？", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmButtonClass",
        cancelButtonClass: "confirmButtonClass",
      }).then(() => {
        let params = {
          groupId: this.selOrgGroupInfo.groupId,
          id: data.personId,
          groupType: "1", // 1：人员分组：2：机构分组
        };
        movePersonsFromGroup(params).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg || "删除成功");
            this.getSelPersonByOrgGroup(this.selOrgGroupInfo.groupId);
          } else {
            this.$message({
              type: "error",
              message: res.data.msg ? res.data.msg : "删除失败",
            });
          }
        });
      });
    },

    // 添加人员到通讯录机构树中回调
    addUserToOrgCall(callData) {
      this.getListData();
    },
  },
};
</script>
<style lang="scss" scoped>
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  /deep/.el-input-group__append {
    cursor: pointer !important;
  }
}
.duty-list {
  display: flex;
  justify-content: start;
  width: 100%;
  height: 77vh;
  padding-top: 0.1rem;
}

.duty-list-left {
  width: 30%;
  height: 100%;
}

.duty-list-right {
  width: 70%;
  height: 100%;
  /* background: rgba(233, 233, 233, 1) */
}
.tab {
  padding-left: 28px;
  padding-right: 20px;
  padding-top: 0.5rem;
  button {
    span {
      font-size: 16px !important;
    }
  }
}
.list_contain {
  // display: flex;
  // justify-content: space-between;
  //   height: 72vh;
  height: 100%;
  width: 100%;
  flex-direction: column;
}
.list_top {
  height: calc(100% - 198px);
  // display: flex;
}

.list_left {
  padding-top: 0.5rem;
  width: 100%;
  height: calc(100% - 50px);
}
.list_bottom_box {
  height: 198px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // justify-content: space-between;
  .bottom_list {
    height: calc(100% - 50px);
    border: 1px solid #dfdfe3;

    .bottom_concant_list {
      width: 100%;
      height: calc(100% - 2rem);

      .bottom_flex_group {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding-left: 1rem;

        > div {
          width: 25%;
          height: 80px;
          border-radius: 4px;
          margin-top: 0.875rem;
          padding-right: 0.75rem;
          position: relative;
          overflow: hidden;
          .concat_inner {
            width: 100%;
            height: 100%;
            padding: 14px;
            display: flex;
            justify-content: space-between;
            border: 1px solid #dfdfe3;
            background: #fafafa;
            > img {
              // width:66px;
              display: inline-block;
            }

            > .cancat_info {
              width: calc(100% - 52px);
              box-sizing: border-box;
              padding-left: 14px;
              font-size: 16px;
              display: flex;
              flex-direction: column;
              justify-content: space-around;

              .person_title {
                color: #49445f;
                width: 100%;
                line-height: 1rem;
                margin-top: 0.5rem;

                > p {
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  // display: -webkit-box;
                  // -webkit-line-clamp: 2;
                  // -webkit-box-orient: vertical;
                }
              }

              .person_info {
                margin-top: 4px;
                margin-bottom: 6px;
                color: #9999b0;
                font-size: 12px;
                word-break: keep-all;
              }
            }
          }

          i {
            position: absolute;
            right: 0.75rem;
            top: 5px;
            cursor: pointer;

            &::before {
              color: #4a7dff !important;
            }
          }
        }
      }
    }

    .bottom_title {
      height: 2rem;
      line-height: 2rem;
      background: #f3f3f3;
      padding: 0 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .bottom_title_add_button {
      cursor: pointer;
    }

    .bottom_contain {
      height: calc(100% - 2rem);
    }
  }

  .bottom_btn_group {
    height: 38px;
    display: flex;
    justify-content: space-between;
    background: #f3f3f5;
    border: 1px solid #dfdfe3;
    border-radius: 4px;
    align-items: center;

    > div {
      flex: 1;
      text-align: center;
      cursor: pointer;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      &:not(:last-child) {
        border-right: 1px solid #dfdfe3;
      }

      &:hover {
        color: #66b1ff;
      }
    }
  }
}
.person_info_span {
  width: 85px;
  //   margin-bottom: 4px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.maillist-title {
  display: inline-block;
  line-height: 40px;
  font-size: 22px;
}
.right_filter {
  float: right;
}

.el-scrollbar {
  height: 100% !important;
}
.search_tree {
  position: relative;
  height: 91.5%;
  box-sizing: border-box;
}

.tabs_list {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 122;
  padding: 0.625rem 4px;
  padding-top: 0;
  margin-top: 0.5rem;
  position: absolute;
  height: 16.5rem;
  width: 100%;

  .filter_list {
    li {
      cursor: pointer;
      color: #000;
      font-size: 16px;
      padding: 0.625rem 1rem;
      line-height: 1rem;
      &:hover {
        background: #66b1ff;
        color: #fff;
      }

      &.active {
        background: #66b1ff;
        color: #fff;
      }
    }
  }
  .filter_list_content {
    display: inline-block;
    // width: 100%;
    line-height: 18px;
    // font-size: 1rem;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
}

.search_slide_searchKey {
  position: relative;
  height: 3rem;
  line-height: 3rem;
  background: #f5f5f7;
  border: 1px solid #e3e3e5;
  padding: 0 0.5rem;
  border-bottom: none;
}

.search_slide_input {
  // padding: 0 0.625rem;
  height: 100%;
  padding-top: 0.5rem;
  padding-left: 28px;
  padding-right: 20px;
}

.search_slide {
  position: relative;
  transition: all 0.5s;
  // top: 0.5rem;
  width: 100%;
  // height: calc(100% - 2.7rem);
  // height: 93.5%;
  // height: calc(100% - 80px);
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e3e3e5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 11;
  padding-left: 0.2rem;
  padding-bottom: 0.2rem;
  border-top: none;
  .controllab {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
  }
}

.popper__arrow {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
  -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: 2.5rem;
  left: 50%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #ebeef5;
  z-index: 100;

  &:after {
    content: "";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
  }
}

.headertitle {
  background: #f5f5f6;
  padding-left: 30px;
  line-height: 2.1rem;
  font-size: 1rem;
  cursor: pointer;
}
.navtitle {
  background: #fff;
  // border-bottom: 1px solid rgb(228, 231, 237)
}

.tree_icon {
  color: #c0c4cc;
  font-size: 18px;
  padding: 6px;
}
.headerdrops {
  margin-left: 1.6rem;
  margin-bottom: 3px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #c0c4cc;
}
.pagination {
  margin-top: 10px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #cee5ff;
  color: #4a7dff;
}
</style>

<style scoped>
/deep/ .el-input .el-input__clear {
  font-size: 20px !important;
}
</style>
