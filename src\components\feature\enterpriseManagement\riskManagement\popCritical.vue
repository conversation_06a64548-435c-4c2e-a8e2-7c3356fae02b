<template>
  <el-dialog
    title="临界量匹配表"
    :visible.sync="iscritical"
    width="800px"
    top="20vh"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    :destroy-on-close='false'
  >
    <div>
      <div class="hs_box">
        <h2>当前危险化学品：{{ preData.title }}</h2>
      </div>

      <div class="M_box">
        <div class="m_tit">
          <div class="m_tit_l">
            <span>注：若一种危险化学品具有多种危险性，请添加多种危险类别</span>
          </div>
          <div class="m_tit_r">
            <button
              @click="addFn"
              type="button"
              class="el-button el-button--primary el-button--small"
            >
              <!----><!----><span>添加</span>
            </button>
          </div>
        </div>
        <el-scrollbar class="linShiHeight">
          <el-table
            :data="storageTankData"
            style="width: 100%"
            :highlight-current-row="false"
            border
          >
            <!-- <el-table-column prop="storageTankNUM" label=" " width="90">
                  <template slot-scope="scope">
                    储罐{{ scope.$index + 1 }}
                  </template>
                </el-table-column> -->
            <el-table-column prop="type" label="危险类别" width="140">
              <template slot-scope="scope">
                <el-select
                  @input="MatchFn(scope.row.chemicalId, scope.row)"
                  v-model="scope.row.chemicalId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in eOption"
                    :key="item.chemicalId"
                    :label="item.title"
                    :value="item.chemicalId"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="casNo" label="符号" width="100">
              <!-- <template slot-scope="scope">
                <div class="tableInputFelxBox">
                  <div class="tableInputFelxBox_left">
                    <el-input type="text" v-model.trim="scope.row.casNo" />
                  </div>
                </div>
              </template> -->
            </el-table-column>
            <el-table-column prop="note" label="说明描述" width="/">
              <template slot-scope="scope">
                <div class="tableInputFelxBox">
                  <div class="tableInputFelxBox_left">
                    <span
                      ><el-input
                        type="textarea"
                        :rows="2"
                        v-model.trim="scope.row.note"
                    /></span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="criticalValue" label="临界量/t" width="100">
              <!-- <template slot-scope="scope">
                <div class="tableInputFelxBox">
                  <div class="tableInputFelxBox_left">
                    <el-input type="text" v-model.trim="scope.row.criticalValue" />
                  </div>
                </div>
              </template> -->
            </el-table-column>
            <el-table-column label="操作" width="100">
              <template slot-scope="scope">
                <div class="deteleStyle" @click="deteleFn(scope.$index)">删除</div>
              </template>
            </el-table-column>
          </el-table>
        </el-scrollbar>
      </div>
    </div>

    <div slot="footer" style="display: flex; justify-content: center">
      <!-- <el-button size="mini">取 消</el-button> -->

      <el-button size="mini" type="primary" @click="subCritical()"
        >提 交</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { generalChemicalsIndex } from "@/api/riskAssessment";
export default {
  data() {
    return {
      eOption: [],
      preData: {},
      criticalValue: "",

      iscritical: false, //临界量匹配表弹框
      storageTankData: [
        {
          tittle: "",
          casNo: "",
          note: "",          
          criticalValue:"",

          alias:"",
          chemicalId:"",
          createTime:"",
          dataType:"",
          deleteFlag:"",
        },
      ],
    };
  },
  watch: {
    radioTop(newVal, oldVal) {
      if (newVal == "fixed") {
        this.isTab = true;
      } else {
        this.isTab = false;
      }
    },
  },
  methods: {
    //无对应数据弹框
    popNoData(val) {
      this.iscritical = val;
    },
    subCritical() {
      if(this.storageTankData.length > 0){
        var ary=[]
        this.storageTankData.forEach(item=>{
          ary.push(item.criticalValue)
        })
        ary.sort(function(a,b){return a-b;});       
        this.storageTankData.minN=ary[0]
      }
      this.$emit("subCritical2",this.storageTankData);
      this.iscritical = false;     
    },
    MatchFn(id, row) {
      if (this.eOption.length > 0) {
        this.eOption.forEach((item) => {
          if (item.chemicalId == id) {
            console.log()
            row.note = item.note;
            row.criticalValue = item.criticalValue;
            this.criticalValue = item.criticalValue;
            row.casNo = item.casNo;
          }
        });
      }
    },
    initData(row, index) {
      console.log(row.correctionValueList,'这是回填')
      this.preData = row;
      this.rowIndex = index;
      this.storageTankData = row.criticalValueList || []; 
      this.storageTankData.rowIndex = index;
     
      generalChemicalsIndex().then((res) => {
        if (res.data.status === 200) {
          this.eOption = res.data.data || [];
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    addFn() {
      let params = {
          tittle: "",
          casNo: "",
          note: "",          
          criticalValue:"",

          alias:"",
          chemicalId:"",
          createTime:"",
          dataType:"",
          deleteFlag:"",
      };
      this.storageTankData.push(params);
    },
    deteleFn(val) {
      this.storageTankData.splice(val, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tab-pane {
  height: 500px;
  overflow: auto;
  // height: calc(100%- 200px);
}
/deep/ .el-date-editor.el-input {
  width: auto;
}
.linShiHeight {
  height: 400px;
}
.M_box {
  .m_tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 10px 0;
  }
}
</style>