<template>
  <div class="data-entry-detail">
    <!-- 添加头部标题 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box" @click="goBack">
              <a-icon type="home" theme="filled" class="icon" />
              数据填报管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <span>{{ tableName }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 内容区域包裹 -->
    <div class="content">
      <!-- 头部工具栏 -->
      <div class="tool-bar">
        <div class="left">
          <el-button-group>
            <el-button
              type="success"
              size="mini"
              icon="el-icon-plus"
              @click="handleAdd"
              >新增</el-button
            >

            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              :disabled="selectedRows.length === 0"
              @click="handleDelete"
              >删除</el-button
            >
          </el-button-group>

          <el-divider direction="vertical"></el-divider>

          <!-- <el-button-group>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-download"
              @click="handleExport"
              >导出</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-refresh"
              @click="handleRefresh"
              >刷新</el-button
            >
          </el-button-group> -->
        </div>
        <div class="right">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-date-picker
                v-model="timeRange"
                size="mini"
                type="daterange"
                value-format="yyyy-MM-dd"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                unlink-panels
                @change="handleDateChange"
                style="width: 300px"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model.trim="searchForm.keyword"
                size="mini"
                placeholder="请输入关键字"
                clearable
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-search"
                @click="handleSearch"
                >检索</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 表格内容 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        height="550px"
        v-loading="tableLoading"
        @selection-change="handleSelectionChange"
        ref="dataTable"
        :span-method="
          tableInfo && tableInfo.reportNameEn === 'admin_punishment_info'
            ? spanMethod
            : null
        "
      >
        <!-- 选择列 -->
        <el-table-column
          type="selection"
          width="55"
          align="center"
        ></el-table-column>

        <!-- 序号列 -->
        <el-table-column
          type="index"
          label="序号"
          width="60"
          align="center"
          :index="getTableIndex"
        ></el-table-column>

        <template v-for="column in displayColumns">
          <el-table-column
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
            show-overflow-tooltip
          >
          </el-table-column>
        </template>

        <!-- 操作列 -->
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="shouldShowOperationButton(scope.$index)"
              type="text"
              size="mini"
              @click="handleEditRow(scope.row)"
              style="color: #409eff"
            >
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
        >
        </el-pagination>
      </div>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="editDialogTitle"
      :visible.sync="editDialogVisible"
      width="1200px"
      top="5vh"
      :close-on-click-modal="false"
      append-to-body
      custom-class="edit-dialog"
    >
      <div class="dialog-content">
        <el-form
          :model="editForm"
          :rules="editFormRules"
          ref="editForm"
          label-width="140px"
          v-loading="editFormLoading"
        >
          <el-row :gutter="20">
            <template v-for="field in editableFields">
              <el-col
                :span="field.type === 'textarea' || field.fullWidth ? 24 : 8"
                :key="field.prop"
              >
                <el-form-item :label="field.label" :prop="field.prop">
                  <!-- 根据字段类型渲染不同的输入组件 -->
                  <el-input
                    v-if="field.type === 'text' || !field.type"
                    v-model="editForm[field.prop]"
                    :placeholder="`请输入${field.label}`"
                    clearable
                  ></el-input>

                  <el-input
                    v-else-if="field.type === 'textarea'"
                    v-model="editForm[field.prop]"
                    type="textarea"
                    :rows="3"
                    :placeholder="`请输入${field.label}`"
                    clearable
                  ></el-input>

                  <el-input-number
                    v-else-if="field.type === 'number'"
                    v-model="editForm[field.prop]"
                    :placeholder="`请输入${field.label}`"
                    :min="0"
                    :controls-position="'right'"
                    style="width: 100%"
                  ></el-input-number>

                  <el-date-picker
                    v-else-if="field.type === 'date'"
                    v-model="editForm[field.prop]"
                    type="date"
                    :placeholder="`请选择${field.label}`"
                    value-format="yyyy-MM-dd"
                    style="width: 100%"
                  ></el-date-picker>

                  <el-date-picker
                    v-else-if="field.type === 'datetime'"
                    v-model="editForm[field.prop]"
                    type="datetime"
                    :placeholder="`请选择${field.label}`"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                  ></el-date-picker>

                  <el-select
                    v-else-if="field.type === 'select'"
                    v-model="editForm[field.prop]"
                    :placeholder="`请选择${field.label}`"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="option in field.options || []"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    ></el-option>
                  </el-select>

                  <el-cascader
                    v-else-if="field.type === 'cascader'"
                    v-model="editForm[field.prop]"
                    :placeholder="`请选择${field.label}`"
                    :options="field.options || []"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    :show-all-levels="true"
                    style="width: 100%"
                  ></el-cascader>

                  <!-- Tab页形式的动态列表 -->
                  <div
                    v-else-if="field.type === 'dynamic-list-tabs'"
                    class="dynamic-list-tabs-container"
                  >
                    <el-tabs v-model="activeTabName" type="border-card">
                      <!-- 机构处罚列表 -->
                      <el-tab-pane label="机构处罚列表" name="orgList">
                        <div class="tab-content">
                          <div class="tab-header">
                            <span class="tab-title">机构处罚信息</span>
                            <el-button
                              type="primary"
                              size="small"
                              @click="addOrgItem"
                              icon="el-icon-plus"
                            >
                              添加机构处罚
                            </el-button>
                          </div>

                          <div
                            v-for="(item, index) in editForm.orgList || []"
                            :key="index"
                            class="list-item"
                          >
                            <div class="item-header">
                              <span class="item-title"
                                >机构处罚 {{ index + 1 }}</span
                              >
                              <el-button
                                type="danger"
                                size="mini"
                                @click="removeOrgItem(index)"
                                icon="el-icon-delete"
                              >
                                删除
                              </el-button>
                            </div>

                            <el-row :gutter="15">
                              <el-col :span="12">
                                <el-form-item label="处罚主体" size="small">
                                  <el-input
                                    v-model="item.punishTarget"
                                    placeholder="请输入处罚主体"
                                    size="small"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="处罚类型" size="small">
                                  <el-input
                                    v-model="item.type"
                                    placeholder="请输入处罚类型"
                                    size="small"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                            </el-row>

                            <el-row :gutter="15">
                              <el-col :span="12">
                                <el-form-item label="金额(万元)" size="small">
                                  <el-input-number
                                    v-model="item.amount"
                                    placeholder="请输入金额"
                                    :min="0"
                                    :precision="2"
                                    size="small"
                                    style="width: 100%"
                                  ></el-input-number>
                                </el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="文书编号" size="small">
                                  <el-input
                                    v-model="item.code"
                                    placeholder="请输入文书编号"
                                    size="small"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                            </el-row>

                            <el-row :gutter="15">
                              <el-col :span="12">
                                <el-form-item label="处罚决定日期" size="small">
                                  <el-date-picker
                                    v-model="item.punishDate"
                                    type="date"
                                    placeholder="请选择处罚决定日期"
                                    value-format="yyyy-MM-dd"
                                    size="small"
                                    style="width: 100%"
                                  ></el-date-picker>
                                </el-form-item>
                              </el-col>
                            </el-row>
                          </div>

                          <div
                            v-if="
                              !editForm.orgList || editForm.orgList.length === 0
                            "
                            class="empty-list"
                          >
                            <i class="el-icon-info"></i>
                            <span
                              >暂无机构处罚数据，点击"添加机构处罚"按钮添加数据</span
                            >
                          </div>
                        </div>
                      </el-tab-pane>

                      <!-- 人员处罚列表 -->
                      <el-tab-pane label="人员处罚列表" name="personList">
                        <div class="tab-content">
                          <div class="tab-header">
                            <span class="tab-title">人员处罚信息</span>
                            <el-button
                              type="primary"
                              size="small"
                              @click="addPersonItem"
                              icon="el-icon-plus"
                            >
                              添加人员处罚
                            </el-button>
                          </div>

                          <div
                            v-for="(item, index) in editForm.personList || []"
                            :key="index"
                            class="list-item"
                          >
                            <div class="item-header">
                              <span class="item-title"
                                >人员处罚 {{ index + 1 }}</span
                              >
                              <el-button
                                type="danger"
                                size="mini"
                                @click="removePersonItem(index)"
                                icon="el-icon-delete"
                              >
                                删除
                              </el-button>
                            </div>

                            <el-row :gutter="15">
                              <el-col :span="12">
                                <el-form-item label="处罚主体" size="small">
                                  <el-input
                                    v-model="item.punishTarget"
                                    placeholder="请输入处罚主体"
                                    size="small"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="处罚类型" size="small">
                                  <el-input
                                    v-model="item.type"
                                    placeholder="请输入处罚类型"
                                    size="small"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                            </el-row>

                            <el-row :gutter="15">
                              <el-col :span="12">
                                <el-form-item label="金额(万元)" size="small">
                                  <el-input-number
                                    v-model="item.amount"
                                    placeholder="请输入金额"
                                    :min="0"
                                    :precision="2"
                                    size="small"
                                    style="width: 100%"
                                  ></el-input-number>
                                </el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="文书编号" size="small">
                                  <el-input
                                    v-model="item.code"
                                    placeholder="请输入文书编号"
                                    size="small"
                                  ></el-input>
                                </el-form-item>
                              </el-col>
                            </el-row>

                            <el-row :gutter="15">
                              <el-col :span="12">
                                <el-form-item label="处罚决定日期" size="small">
                                  <el-date-picker
                                    v-model="item.punishDate"
                                    type="date"
                                    placeholder="请选择处罚决定日期"
                                    value-format="yyyy-MM-dd"
                                    size="small"
                                    style="width: 100%"
                                  ></el-date-picker>
                                </el-form-item>
                              </el-col>
                            </el-row>
                          </div>

                          <div
                            v-if="
                              !editForm.personList ||
                              editForm.personList.length === 0
                            "
                            class="empty-list"
                          >
                            <i class="el-icon-info"></i>
                            <span
                              >暂无人员处罚数据，点击"添加人员处罚"按钮添加数据</span
                            >
                          </div>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </div>

                  <el-input
                    v-else
                    v-model="editForm[field.prop]"
                    :placeholder="`请输入${field.label}`"
                    clearable
                  ></el-input>
                </el-form-item>
              </el-col>
            </template>
          </el-row>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleSaveData"
          :loading="editFormLoading"
        >
          {{ editMode === "add" ? "新增" : "保存" }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getEventPunishmentPage,
  addEventPunishment,
  updateEventPunishment,
  deleteEventPunishment,
  getAdminPunishmentPage,
  addAdminPunishment,
  updateAdminPunishment,
  deleteAdminPunishment,
  getAdminPunishmentById,
} from "@/api/smartReport";
import { getDistrictUser } from "@/api/entList";

export default {
  name: "DataEntryDetail",
  props: {
    tableId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      tableName: "",
      tableInfo: null,
      searchForm: {
        startTime: "",
        endTime: "",
        keyword: "",
      },
      timeRange: [],
      tableData: [],
      tableLoading: false,
      selectedRows: [],
      // 合并单元格信息
      mergeInfo: {},
      // Tab页激活状态
      activeTabName: "orgList",
      // 原始行政处罚数据缓存
      originalAdminPunishmentData: [],
      displayColumns: [],
      // 编辑相关数据
      editDialogVisible: false,
      editMode: "add", // 'add' 或 'edit'
      editForm: {},
      editFormLoading: false,
      editFormRules: {},
      editableFields: [], // 可编辑的字段配置
      district: [], // 行政区划数据
      // 分页相关数据
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
    };
  },
  computed: {
    // 编辑弹窗标题
    editDialogTitle() {
      return this.editMode === "add" ? "新增数据" : "编辑数据";
    },
  },
  methods: {
    // 初始化数据
    async initData(tableInfo) {
      this.tableInfo = tableInfo;
      this.tableName = tableInfo.reportNameCn || "数据填报";

      // 显示加载状态
      this.tableLoading = true;

      try {
        // 先获取字段配置
        this.initColumns();
        // 获取行政区划数据
        await this.getDistrictData();
        // 再获取表格数据
        await this.getTableData();
      } catch (error) {
        console.error("初始化数据失败:", error);
        this.$message.error("初始化数据失败，请稍后重试");
      } finally {
        this.tableLoading = false;
      }
    },

    // 初始化列配置
    initColumns() {
      if (!this.tableInfo || !this.tableInfo.reportNameEn) {
        console.warn("表信息不完整，无法获取字段");
        this.useFallbackColumns();
        return;
      }

      // 根据表名定制化配置字段
      if (this.tableInfo.reportNameEn === "event_punishment_info") {
        // 事故查处表字段配置
        this.displayColumns = [
          { prop: "eventName", label: "事故名称", width: 200, show: true },
          { prop: "eventInfo", label: "事故简况", width: 250, show: true },
          {
            prop: "respOrgName",
            label: "主责单位名称",
            width: 180,
            show: true,
          },
          { prop: "punishNum", label: "处理人数/次", width: 120, show: true },
          {
            prop: "penaltyOrg",
            label: "罚款金额-单位(万元)",
            width: 150,
            show: true,
          },
          {
            prop: "penaltyPerson",
            label: "罚款金额-个人(万元)",
            width: 150,
            show: true,
          },
          { prop: "approvalTime", label: "批复时间", width: 160, show: true },
          {
            prop: "enterpHandle",
            label: "公司内部处理",
            width: 120,
            show: true,
          },
          {
            prop: "judicalHandle",
            label: "移送司法机关处理",
            width: 140,
            show: true,
          },
          { prop: "orgAdvise", label: "组织处理-诫勉", width: 120, show: true },
          {
            prop: "orgTeach",
            label: "组织处理-批评教育",
            width: 140,
            show: true,
          },
          {
            prop: "govWarning",
            label: "政务处分-警告",
            width: 120,
            show: true,
          },
          {
            prop: "govMistake",
            label: "政务处分-记过",
            width: 120,
            show: true,
          },
          {
            prop: "govMajorMistake",
            label: "政务处分-记大过",
            width: 140,
            show: true,
          },
          {
            prop: "govDemotion",
            label: "政务处分-降级",
            width: 120,
            show: true,
          },
          {
            prop: "govDismiss",
            label: "政务处分-撤职",
            width: 120,
            show: true,
          },
          { prop: "govExpel", label: "政务处分-开除", width: 120, show: true },
          {
            prop: "partyWarning",
            label: "党纪处分-警告",
            width: 120,
            show: true,
          },
          {
            prop: "partyMajorWarning",
            label: "党纪处分-严重警告",
            width: 140,
            show: true,
          },
          {
            prop: "partyDismiss",
            label: "党纪处分-撤销党内职务",
            width: 160,
            show: true,
          },
          {
            prop: "partyObserve",
            label: "党纪处分-留党察看",
            width: 140,
            show: true,
          },
          {
            prop: "partyExpel",
            label: "党纪处分-开除党籍",
            width: 140,
            show: true,
          },
          { prop: "remark", label: "备注", width: 200, show: true },
        ];

        // 设置可编辑字段配置
        this.editableFields = [
          {
            prop: "eventName",
            label: "事故名称",
            type: "text",
            required: true,
            fullWidth: true, // 标记为全宽字段
          },
          {
            prop: "eventInfo",
            label: "事故简况",
            type: "textarea",
            required: false,
          },
          {
            prop: "respOrgName",
            label: "主责单位名称",
            type: "text",
            required: false,
          },
          {
            prop: "districtCode",
            label: "行政区划",
            type: "cascader",
            required: false,
            options: [], // 初始为空，会在getDistrictData后更新
          },
          {
            prop: "punishNum",
            label: "处理人数/次",
            type: "number",
            required: false,
          },
          {
            prop: "penaltyOrg",
            label: "罚款金额-单位(万元)",
            type: "text",
            required: false,
          },
          {
            prop: "penaltyPerson",
            label: "罚款金额-个人(万元)",
            type: "text",
            required: false,
          },
          {
            prop: "approvalTime",
            label: "批复时间",
            type: "date",
            required: false,
          },
          {
            prop: "enterpHandle",
            label: "公司内部处理",
            type: "number",
            required: false,
          },
          {
            prop: "judicalHandle",
            label: "移送司法机关处理",
            type: "number",
            required: false,
          },
          {
            prop: "orgAdvise",
            label: "组织处理-诫勉",
            type: "number",
            required: false,
          },
          {
            prop: "orgTeach",
            label: "组织处理-批评教育",
            type: "number",
            required: false,
          },
          {
            prop: "govWarning",
            label: "政务处分-警告",
            type: "number",
            required: false,
          },
          {
            prop: "govMistake",
            label: "政务处分-记过",
            type: "number",
            required: false,
          },
          {
            prop: "govMajorMistake",
            label: "政务处分-记大过",
            type: "number",
            required: false,
          },
          {
            prop: "govDemotion",
            label: "政务处分-降级",
            type: "number",
            required: false,
          },
          {
            prop: "govDismiss",
            label: "政务处分-撤职",
            type: "number",
            required: false,
          },
          {
            prop: "govExpel",
            label: "政务处分-开除",
            type: "number",
            required: false,
          },
          {
            prop: "partyWarning",
            label: "党纪处分-警告",
            type: "number",
            required: false,
          },
          {
            prop: "partyMajorWarning",
            label: "党纪处分-严重警告",
            type: "number",
            required: false,
          },
          {
            prop: "partyDismiss",
            label: "党纪处分-撤销党内职务",
            type: "number",
            required: false,
          },
          {
            prop: "partyObserve",
            label: "党纪处分-留党察看",
            type: "number",
            required: false,
          },
          {
            prop: "partyExpel",
            label: "党纪处分-开除党籍",
            type: "number",
            required: false,
          },
          {
            prop: "remark",
            label: "备注",
            type: "textarea",
            required: false,
          },
        ];
      } else if (this.tableInfo.reportNameEn === "admin_punishment_info") {
        // 行政处罚表字段配置
        this.displayColumns = [
          { prop: "eventName", label: "事故案件名称", width: 600, show: true },
          { prop: "punishTarget", label: "处罚主体", width: 150, show: true },
          { prop: "type", label: "处罚类型", width: 150, show: true },
          { prop: "amount", label: "金额(万元)", width: 150, show: true },
          { prop: "code", label: "文书编号", width: 150, show: true },
          { prop: "punishDate", label: "处罚决定日期", width: 150, show: true },
          { prop: "orgNum", label: "处罚单位数", width: 140, show: true },
          { prop: "personNum", label: "处罚人数", width: 140, show: true },
        ];

        // 设置可编辑字段配置
        this.editableFields = [
          {
            prop: "eventName",
            label: "事故案件名称",
            type: "text",
            required: true,
            fullWidth: true,
          },
          {
            prop: "districtCode",
            label: "行政区划",
            type: "cascader",
            required: true,
            options: this.district,
            props: {
              value: "value",
              label: "label",
              children: "children",
            },
          },
          {
            prop: "punishmentLists",
            label: "处罚信息",
            type: "dynamic-list-tabs",
            required: false,
            fullWidth: true,
          },
        ];

        // 设置表单验证规则
        this.editFormRules = {};
        this.editableFields.forEach((field) => {
          if (field.required) {
            this.editFormRules[field.prop] = [
              {
                required: true,
                message: `请输入${field.label}`,
                trigger: "blur",
              },
            ];
          }
        });
      }
    },

    // 根据字段名获取合适的列宽
    getColumnWidth(fieldName) {
      if (!fieldName) return 120;

      // 根据字段名推断合适的宽度
      if (fieldName.includes("id") || fieldName.includes("Id")) return 80;
      if (
        fieldName.includes("time") ||
        fieldName.includes("Time") ||
        fieldName.includes("date") ||
        fieldName.includes("Date")
      )
        return 160;
      if (
        fieldName.includes("name") ||
        fieldName.includes("Name") ||
        fieldName.includes("title") ||
        fieldName.includes("Title")
      )
        return 150;
      if (fieldName.includes("code") || fieldName.includes("Code")) return 120;
      if (
        fieldName.includes("status") ||
        fieldName.includes("Status") ||
        fieldName.includes("type") ||
        fieldName.includes("Type")
      )
        return 100;
      if (
        fieldName.includes("desc") ||
        fieldName.includes("Desc") ||
        fieldName.includes("remark") ||
        fieldName.includes("Remark")
      )
        return 200;

      return 120; // 默认宽度
    },

    // 生成表单验证规则
    generateFormRules() {
      this.editFormRules = {};
      if (this.editableFields) {
        this.editableFields.forEach((field) => {
          if (field.required) {
            this.editFormRules[field.prop] = [
              {
                required: true,
                message: `请输入${field.label}`,
                trigger: "blur",
              },
            ];
          }
        });
      }
    },

    // 备用列配置（当表名无法识别时使用）
    useFallbackColumns() {
      this.displayColumns = [
        { prop: "id", label: "ID", width: 80, show: true },
        { prop: "name", label: "名称", width: 150, show: true },
        { prop: "type", label: "类型", width: 100, show: true },
        { prop: "status", label: "状态", width: 100, show: true },
        { prop: "createTime", label: "创建时间", width: 160, show: true },
        { prop: "updateTime", label: "更新时间", width: 160, show: true },
      ];

      // 设置可编辑字段配置
      this.editableFields = [
        { prop: "name", label: "名称", type: "text", required: true },
        { prop: "type", label: "类型", type: "text", required: true },
        { prop: "status", label: "状态", type: "text", required: true },
      ];
    },

    // 获取表格数据
    async getTableData() {
      // 如果不是初始化调用，则显示加载状态
      const isInitialCall = arguments[0] !== false;
      if (!isInitialCall) {
        this.tableLoading = true;
      }
      try {
        // 根据表名调用不同的API接口
        if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "event_punishment_info"
        ) {
          // 使用事故查处表的专用接口
          const res = await getEventPunishmentPage({
            nowPage: this.pagination.currentPage,
            pageSize: this.pagination.pageSize,
            startTime: this.searchForm.startTime,
            endTime: this.searchForm.endTime,
            keyword: this.searchForm.keyword,
          });

          if (res.status === 200 && res.data && res.data.data) {
            this.tableData = res.data.data.list || [];
            this.pagination.total = res.data.data.total || 0;
          } else {
            console.warn("获取事故查处数据失败:", res);
            this.tableData = [];
            this.pagination.total = 0;
          }
        } else if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "admin_punishment_info"
        ) {
          // 使用行政处罚表的专用接口
          const res = await getAdminPunishmentPage({
            nowPage: this.pagination.currentPage,
            pageSize: this.pagination.pageSize,
            startTime: this.searchForm.startTime,
            endTime: this.searchForm.endTime,
            keyword: this.searchForm.keyword,
          });

          if (res.status === 200 && res.data && res.data.data) {
            // 处理行政处罚表数据，将orgList和personList展开为行
            const rawData = res.data.data.list || [];
            // 缓存原始数据用于编辑回填
            this.originalAdminPunishmentData = rawData;
            this.tableData = this.processAdminPunishmentData(rawData);
            this.pagination.total = res.data.data.total || 0;

            // 计算合并单元格信息
            this.calculateMergeInfo(this.tableData);
          } else {
            console.warn("获取行政处罚数据失败:", res);
            this.tableData = [];
            this.pagination.total = 0;
          }
        } else {
          // 其他表使用通用接口或模拟数据
          // console.log("使用模拟数据，表名:", this.tableInfo?.reportNameEn);
          // await new Promise((resolve) => setTimeout(resolve, 500));
          // this.tableData = this.generateMockData();
          // this.pagination.total = 100;
        }
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        if (!isInitialCall) {
          this.tableLoading = false;
        }
      }
    },

    // 处理行政处罚表数据，将orgList和personList展开为行
    processAdminPunishmentData(rawData) {
      const processedData = [];

      rawData.forEach((item) => {
        const baseData = {
          id: item.id,
          eventName: item.eventName,
          orgNum: item.orgNum,
          personNum: item.personNum,
          districtCode: item.districtCode,
          createTime: item.createTime,
          updateTime: item.updateTime,
        };

        // 处理机构处罚列表
        if (item.orgList && item.orgList.length > 0) {
          item.orgList.forEach((org, index) => {
            processedData.push({
              ...baseData,
              ...org,
              targetType: "机构",
              rowType: "org",
              parentId: item.id,
              sortIndex: index,
            });
          });
        }

        // 处理人员处罚列表
        if (item.personList && item.personList.length > 0) {
          item.personList.forEach((person, index) => {
            processedData.push({
              ...baseData,
              ...person,
              targetType: "个人",
              rowType: "person",
              parentId: item.id,
              sortIndex: index,
            });
          });
        }

        // 如果没有处罚列表，至少显示一行基础信息
        if (
          (!item.orgList || item.orgList.length === 0) &&
          (!item.personList || item.personList.length === 0)
        ) {
          processedData.push({
            ...baseData,
            targetType: "",
            rowType: "empty",
            parentId: item.id,
            sortIndex: 0,
          });
        }
      });

      return processedData;
    },

    // 表格选择改变
    handleSelectionChange(selection) {
      // 对于行政处罚表，需要以事件为单位进行选择
      if (
        this.tableInfo &&
        this.tableInfo.reportNameEn === "admin_punishment_info"
      ) {
        this.selectedRows = this.getUniqueEventRows(selection);
      } else {
        this.selectedRows = selection;
      }
    },

    // 获取唯一的事件行（去重）
    getUniqueEventRows(selection) {
      const uniqueEvents = new Map();

      selection.forEach((row) => {
        const eventName = row.eventName;
        if (eventName && !uniqueEvents.has(eventName)) {
          // 存储事件的第一行数据作为代表
          uniqueEvents.set(eventName, row);
        }
      });

      return Array.from(uniqueEvents.values());
    },

    // 合并单元格方法
    spanMethod({ row, column, rowIndex, columnIndex }) {
      // 只对行政处罚表进行合并处理
      if (
        !this.tableInfo ||
        this.tableInfo.reportNameEn !== "admin_punishment_info"
      ) {
        return [1, 1];
      }

      const prop = column.property;

      // 需要合并的字段：eventName, orgNum, personNum
      const mergeFields = ["eventName", "orgNum", "personNum"];

      // 需要合并的特殊列：选择列、序号列、操作列
      const specialColumns = ["selection", "index", "operation"];

      // 处理数据字段的合并
      if (mergeFields.includes(prop)) {
        if (this.mergeInfo[prop] && this.mergeInfo[prop][rowIndex]) {
          const spanInfo = this.mergeInfo[prop][rowIndex];
          return [spanInfo.rowspan, spanInfo.colspan];
        }
      }

      // 处理特殊列的合并（选择列、序号列、操作列）
      if (
        column.type === "selection" ||
        column.type === "index" ||
        columnIndex === this.getOperationColumnIndex()
      ) {
        // 使用eventName的合并信息来决定特殊列的合并
        if (
          this.mergeInfo["eventName"] &&
          this.mergeInfo["eventName"][rowIndex]
        ) {
          const spanInfo = this.mergeInfo["eventName"][rowIndex];
          return [spanInfo.rowspan, spanInfo.colspan];
        }
      }

      return [1, 1];
    },

    // 获取操作列的索引
    getOperationColumnIndex() {
      // 操作列通常是最后一列
      // 选择列(0) + 序号列(1) + 数据列(displayColumns.length) = 操作列索引
      return 2 + this.displayColumns.length;
    },

    // 判断是否应该显示操作按钮
    shouldShowOperationButton(rowIndex) {
      // 对于非行政处罚表，始终显示操作按钮
      if (
        !this.tableInfo ||
        this.tableInfo.reportNameEn !== "admin_punishment_info"
      ) {
        return true;
      }

      // 对于行政处罚表，只在合并单元格的第一行显示操作按钮
      if (
        this.mergeInfo["eventName"] &&
        this.mergeInfo["eventName"][rowIndex]
      ) {
        const spanInfo = this.mergeInfo["eventName"][rowIndex];
        // 如果rowspan > 0，说明这是合并单元格的第一行
        return spanInfo.rowspan > 0;
      }

      // 如果没有合并信息，默认显示
      return true;
    },

    // 获取事件数据用于编辑
    getEventDataForEdit(row) {
      // 从原始数据中查找对应的事件记录
      const originalData = this.findOriginalEventData(row.eventName);

      if (originalData) {
        // 直接使用原始数据的结构，不需要文字匹配
        return {
          id: originalData.id,
          eventName: originalData.eventName,
          orgNum: originalData.orgNum,
          personNum: originalData.personNum,
          districtCode: originalData.districtCode,
          orgList: originalData.orgList || [],
          personList: originalData.personList || [],
        };
      }

      // 如果找不到原始数据，返回当前行数据
      return { ...row };
    },

    // 查找原始事件数据
    findOriginalEventData(eventName) {
      // 直接从缓存的原始数据中查找
      return this.originalAdminPunishmentData.find(
        (item) => item.eventName === eventName
      );
    },

    // 计算合并单元格信息
    calculateMergeInfo(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return;
      }

      // 重置合并信息
      this.mergeInfo = {};

      // 按eventName分组
      const groupedData = {};
      data.forEach((item, index) => {
        const eventName = item.eventName;
        if (eventName) {
          if (!groupedData[eventName]) {
            groupedData[eventName] = [];
          }
          groupedData[eventName].push({ ...item, originalIndex: index });
        }
      });

      // 计算合并信息
      let currentIndex = 0;
      Object.keys(groupedData).forEach((eventName) => {
        const group = groupedData[eventName];
        const groupLength = group.length;

        if (groupLength > 1) {
          // 需要合并的字段
          const mergeFields = ["eventName", "orgNum", "personNum"];

          mergeFields.forEach((field) => {
            if (!this.mergeInfo[field]) {
              this.mergeInfo[field] = {};
            }

            // 第一行显示合并单元格
            this.mergeInfo[field][currentIndex] = {
              rowspan: groupLength,
              colspan: 1,
            };

            // 其他行隐藏
            for (let i = 1; i < groupLength; i++) {
              this.mergeInfo[field][currentIndex + i] = {
                rowspan: 0,
                colspan: 0,
              };
            }
          });
        }

        currentIndex += groupLength;
      });
    },

    // 获取表格序号
    getTableIndex(index) {
      // 对于行政处罚表，序号以事件为基数
      if (
        this.tableInfo &&
        this.tableInfo.reportNameEn === "admin_punishment_info"
      ) {
        return this.getEventIndex(index);
      }

      // 其他表格使用默认序号
      return (
        (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
      );
    },

    // 获取事件序号
    getEventIndex(rowIndex) {
      if (!this.tableData || rowIndex >= this.tableData.length) {
        return rowIndex + 1;
      }

      const currentRow = this.tableData[rowIndex];
      const currentEventName = currentRow.eventName;

      // 计算当前事件是第几个事件
      let eventIndex = 0;
      const seenEvents = new Set();

      for (let i = 0; i <= rowIndex; i++) {
        const row = this.tableData[i];
        if (row.eventName && !seenEvents.has(row.eventName)) {
          eventIndex++;
          seenEvents.add(row.eventName);

          // 如果是当前事件，返回序号
          if (row.eventName === currentEventName) {
            return (
              (this.pagination.currentPage - 1) * this.pagination.pageSize +
              eventIndex
            );
          }
        }
      }

      return eventIndex;
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1;
      this.getTableData(false);
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.getTableData(false);
    },

    // 处理日期选择
    handleDateChange(value) {
      if (value && value.length === 2) {
        this.searchForm.startTime = value[0];
        this.searchForm.endTime = value[1];
      } else {
        this.searchForm.startTime = "";
        this.searchForm.endTime = "";
      }
    },

    // 处理搜索
    handleSearch() {
      this.pagination.currentPage = 1;
      this.getTableData(false);
    },

    // 刷新数据
    handleRefresh() {
      this.getTableData(false);
    },

    // 新增数据
    handleAdd() {
      this.editMode = "add";
      this.editForm = {};
      this.editDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm && this.$refs.editForm.clearValidate();
      });
    },

    // 编辑数据
    handleEdit() {
      if (this.selectedRows.length !== 1) {
        this.$message.warning("请选择一条数据进行编辑");
        return;
      }

      this.editMode = "edit";
      this.editForm = { ...this.selectedRows[0] };
      this.editDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm && this.$refs.editForm.clearValidate();
      });
    },

    // 编辑行数据（从操作列点击）
    handleEditRow(row) {
      this.editMode = "edit";

      // 对于行政处罚表，需要编辑整个事件的数据
      if (
        this.tableInfo &&
        this.tableInfo.reportNameEn === "admin_punishment_info"
      ) {
        this.editForm = this.getEventDataForEdit(row);
      } else {
        this.editForm = { ...row };
      }

      this.editDialogVisible = true;
      this.$nextTick(() => {
        this.$refs.editForm && this.$refs.editForm.clearValidate();
      });
    },

    // 删除数据
    handleDelete() {
      if (this.selectedRows.length === 0) {
        this.$message.warning("请选择要删除的数据");
        return;
      }

      const count = this.selectedRows.length;
      const message =
        count === 1
          ? "确定要删除选中的这条数据吗？"
          : `确定要删除选中的这${count}条数据吗？`;

      this.$confirm(message, "删除确认", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.performDelete();
        })
        .catch(() => {
          // 用户取消删除
        });
    },

    // 执行删除操作
    async performDelete() {
      try {
        if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "event_punishment_info"
        ) {
          // 使用事故查处表的专用删除接口
          for (const row of this.selectedRows) {
            await deleteEventPunishment({ id: row.id });
          }
        } else if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "admin_punishment_info"
        ) {
          // 使用行政处罚表的专用删除接口
          for (const row of this.selectedRows) {
            // 使用parentId（原始事件ID）进行删除
            const eventId = row.parentId || row.id;
            await deleteAdminPunishment({ id: eventId });
          }
        } else {
          // 其他表的删除逻辑
          console.log("其他表删除功能待实现");
        }

        // 删除成功后刷新数据
        await this.getTableData(false);
        this.selectedRows = [];
        this.$refs.dataTable.clearSelection();

        this.$message.success("删除成功");
      } catch (error) {
        console.error("删除失败:", error);
        this.$message.error("删除失败，请稍后重试");
      }
    },

    // 导出数据
    handleExport() {
      this.$message.info("导出功能开发中...");
      // TODO: 实现导出功能
    },

    // 保存数据
    async handleSaveData() {
      try {
        await this.$refs.editForm.validate();

        this.editFormLoading = true;

        if (this.editMode === "add") {
          await this.performAdd();
        } else {
          await this.performEdit();
        }

        this.editDialogVisible = false;
      } catch (error) {
        console.error("表单验证失败:", error);
      } finally {
        this.editFormLoading = false;
      }
    },

    // 执行新增操作
    async performAdd() {
      try {
        if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "event_punishment_info"
        ) {
          // 使用事故查处表的专用新增接口
          const res = await addEventPunishment(this.editForm);

          if (res.status === 200 && res.data && res.data.status === 200) {
            this.$message.success("新增成功");
            // 刷新数据
            await this.getTableData(false);
          } else {
            this.$message.error(res.data?.msg || "新增失败");
          }
        } else if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "admin_punishment_info"
        ) {
          // 使用行政处罚表的专用新增接口
          const res = await addAdminPunishment(this.editForm);
          debugger;
          if (res.status === 200 && res.data && res.data.status === 200) {
            this.$message.success("新增成功");
            // 刷新数据
            await this.getTableData(false);
          } else {
            this.$message.error(res.data?.msg || "新增失败");
          }
        } else {
        }
      } catch (error) {
        console.error("新增失败:", error);
        this.$message.error("新增失败，请稍后重试");
        throw error;
      }
    },

    // 执行编辑操作
    async performEdit() {
      try {
        if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "event_punishment_info"
        ) {
          // 使用事故查处表的专用编辑接口
          const res = await updateEventPunishment(this.editForm);

          if (res.status === 200 && res.data && res.data.status === 200) {
            this.$message.success("编辑成功");
            // 刷新数据
            await this.getTableData(false);
          } else {
            this.$message.error(res.data?.msg || "编辑失败");
          }
        } else if (
          this.tableInfo &&
          this.tableInfo.reportNameEn === "admin_punishment_info"
        ) {
          // 使用行政处罚表的专用编辑接口
          const res = await updateAdminPunishment(this.editForm);

          if (res.status === 200 && res.data && res.data.status === 200) {
            this.$message.success("编辑成功");
            // 刷新数据
            await this.getTableData(false);
          } else {
            this.$message.error(res.data?.msg || "编辑失败");
          }
        } else {
          // 其他表的编辑逻辑
          console.log("其他表编辑功能待实现");
          this.$message.success("模拟编辑成功");

          // 模拟编辑成功后刷新数据
          await this.getTableData(false);
        }
      } catch (error) {
        console.error("编辑失败:", error);
        this.$message.error("编辑失败，请稍后重试");
        throw error;
      }
    },

    // 添加动态列表项
    addListItem(field) {
      if (!this.editForm[field.prop]) {
        this.$set(this.editForm, field.prop, []);
      }

      // 创建新的列表项，包含所有子字段的默认值
      const newItem = {};
      field.fields.forEach((subField) => {
        newItem[subField.prop] = subField.type === "number" ? 0 : "";
      });

      this.editForm[field.prop].push(newItem);
    },

    // 删除动态列表项
    removeListItem(field, index) {
      if (
        this.editForm[field.prop] &&
        this.editForm[field.prop].length > index
      ) {
        this.editForm[field.prop].splice(index, 1);
      }
    },

    // 获取行政区划数据
    async getDistrictData() {
      try {
        const res = await getDistrictUser();

        if (res.data) {
          let child = res.data.data;
          // 处理行政区划数据结构
          if (child?.children?.length > 0) {
            for (let j = 0; j < child.children.length; j++) {
              if (child.children[j].children?.length > 0) {
                for (let z = 0; z < child.children[j].children.length; z++) {
                  if (child.children[j].children[z].children?.length < 1) {
                    // 判断children的数组长度
                    child.children[j].children[z].children = undefined;
                  }
                }
              } else {
                child.children[j].children = undefined;
              }
            }
          } else {
            child.children = undefined;
          }
          this.district = [child];
          // 更新字段配置中的行政区划选项
          this.updateDistrictOptions();
        } else {
          console.warn("获取行政区划数据失败:", res);
          this.district = [];
        }
      } catch (error) {
        console.error("获取行政区划数据失败:", error);
        this.district = [];
      }
    },

    // 更新行政区划字段选项
    updateDistrictOptions() {
      const districtField = this.editableFields.find(
        (field) => field.prop === "districtCode"
      );
      if (districtField) {
        districtField.options = this.district;
      }
    },

    // 添加机构处罚项
    addOrgItem() {
      if (!this.editForm.orgList) {
        this.$set(this.editForm, "orgList", []);
      }

      const newItem = {
        punishTarget: "",
        type: "",
        amount: 0,
        code: "",
        punishDate: "",
      };

      this.editForm.orgList.push(newItem);
    },

    // 删除机构处罚项
    removeOrgItem(index) {
      if (this.editForm.orgList && this.editForm.orgList.length > index) {
        this.editForm.orgList.splice(index, 1);
      }
    },

    // 添加人员处罚项
    addPersonItem() {
      if (!this.editForm.personList) {
        this.$set(this.editForm, "personList", []);
      }

      const newItem = {
        punishTarget: "",
        type: "",
        amount: 0,
        code: "",
        punishDate: "",
      };

      this.editForm.personList.push(newItem);
    },

    // 删除人员处罚项
    removePersonItem(index) {
      if (this.editForm.personList && this.editForm.personList.length > index) {
        this.editForm.personList.splice(index, 1);
      }
    },

    // 返回列表
    goBack() {
      this.$emit("goBack");
    },
  },
  created() {
    // 组件创建时不自动初始化，等待父组件调用initData
  },
};
</script>

<style lang="scss" scoped>
.data-entry-detail {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }

      .icon-box {
        cursor: pointer;

        &:hover {
          color: #3977ea;

          .icon {
            color: #3977ea;
          }
        }
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;
  }

  .tool-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px 0;
    border-bottom: 1px solid #ebeef5;

    .left {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;

      .el-button-group {
        margin-right: 0;

        .el-button {
          margin-right: 0;

          &:not(:last-child) {
            margin-right: -1px;
          }
        }
      }

      .el-divider--vertical {
        margin: 0 16px;
        height: 24px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .search-form {
        margin: 0;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .el-form-item {
          margin-bottom: 0;
          margin-right: 12px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    padding: 20px 0;
  }
}

// 编辑弹窗样式
::v-deep .edit-dialog {
  .el-dialog {
    max-height: 90vh;
    margin-top: 5vh !important;
    margin-bottom: 5vh !important;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 10px 20px;
  }

  .dialog-content {
    height: 100%;
    max-height: 70vh;
    overflow-y: auto;
    padding-right: 10px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }

  .el-dialog__footer {
    padding: 10px 20px 20px;
    text-align: right;
    border-top: 1px solid #ebeef5;
  }

  .el-form {
    .el-form-item {
      margin-bottom: 16px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
      line-height: 32px;
    }

    .el-input__inner,
    .el-textarea__inner {
      border-radius: 4px;
    }

    .el-input-number {
      .el-input__inner {
        text-align: left;
      }
    }
  }
}

// 响应式调整
@media (max-width: 1200px) {
  ::v-deep .edit-dialog {
    .el-dialog {
      width: 95% !important;
    }

    .dialog-content {
      max-height: 60vh;
    }
  }
}

@media (max-width: 768px) {
  ::v-deep .edit-dialog {
    .el-dialog {
      width: 98% !important;
      margin: 2vh auto !important;
    }

    .dialog-content {
      max-height: 50vh;
    }

    .el-form {
      .el-col {
        // 在小屏幕上所有字段都占用全宽
        width: 100% !important;
        flex: 0 0 100% !important;
        max-width: 100% !important;
      }
    }
  }
}

// Tab页动态列表样式
.dynamic-list-tabs-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fafafa;

  .el-tabs {
    .el-tabs__header {
      margin: 0;
      background-color: #f8f9fa;
      border-radius: 4px 4px 0 0;
    }

    .el-tabs__content {
      padding: 0;
    }

    .el-tab-pane {
      .tab-content {
        padding: 16px;
        min-height: 300px;

        .tab-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 16px;
          padding-bottom: 12px;
          border-bottom: 2px solid #e4e7ed;

          .tab-title {
            font-size: 16px;
            font-weight: 600;
            color: #303133;
          }
        }

        .list-item {
          background-color: #fff;
          border: 1px solid #e4e7ed;
          border-radius: 6px;
          padding: 16px;
          margin-bottom: 16px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;

          &:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border-color: #409eff;
          }

          &:last-child {
            margin-bottom: 0;
          }

          .item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;

            .item-title {
              font-size: 14px;
              font-weight: 600;
              color: #409eff;
            }
          }

          .el-form-item {
            margin-bottom: 12px;
          }

          .el-row {
            margin-bottom: 8px;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .empty-list {
          text-align: center;
          color: #909399;
          font-size: 14px;
          padding: 40px 20px;
          background-color: #fff;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;

          i {
            font-size: 24px;
            margin-bottom: 8px;
            display: block;
            color: #c0c4cc;
          }

          span {
            display: block;
            line-height: 1.5;
          }
        }
      }
    }
  }
}

// 保留原有动态列表样式（用于其他表格）
.dynamic-list-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;

  .list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #e4e7ed;

    span {
      font-weight: 600;
      color: #303133;
    }
  }

  .list-item {
    background-color: #fff;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    .item-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      span {
        font-weight: 500;
        color: #606266;
      }
    }

    .el-form-item {
      margin-bottom: 8px;
    }
  }

  .empty-list {
    text-align: center;
    color: #909399;
    font-size: 14px;
    padding: 20px;
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 4px;
  }
}
</style>
