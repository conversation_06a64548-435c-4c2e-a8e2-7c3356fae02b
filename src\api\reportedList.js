import axios from "@/utils/http";
import qs from "qs";
export const getMenulist = data =>{
  return axios({
    method:"get",
    url:"/admin/menu/tree/user"
  })
}
// 侧导航数据
export const getAsideData = data => {
    return axios({
      method: "get",
      url: "/admin/menu/tree/transformers",
      data: qs.stringify(data)
    });
  };
// 安全承诺重大危险源列表
export const getSecurityCommitmentList = data => {
  return axios({
    method: "post",
    url: "/hg/dybusi/list.mvc",
    data: qs.stringify(data)
  });
};
// 安全承诺风险列表
export const getRiskList = data => {
    return axios({
      method: "post",
      url: "/hg/dybusi/riskList.mvc",
      data: qs.stringify(data)
    });
};
// 地市接口
export const getDiShi = data => {
    return axios({
      method: "post",
      url: "/hg/commumicationOrg/queryDistByParentCode.mvc",
      data: qs.stringify(data)
    });
};
// 上报企业接口
export const getEnterprise = (data,time) => {
    return axios({
      method: "post",
      url: "/hg/dybusi/dataList.mvc?id="+time.id+"&type="+time.type,
      data: qs.stringify(data)
    });
};
// 获取企业详情接口
export const getEnterpriseDetail = data => {
    return axios({
      method: "post",
      url: "/hg/dybusi/findById.mvc",
      data: qs.stringify(data)
    });
};
