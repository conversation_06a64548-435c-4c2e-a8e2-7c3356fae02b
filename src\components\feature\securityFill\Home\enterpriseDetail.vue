<template>
  <div class="enterpriseFill">
    <div>
      <div class="header">企业现状</div>
      <div>
        <form action="">
          <!-- 企业现状开始 -->
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="title">生产装置套数</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.unitsNumber}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">运行套数</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.runNumber}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">停产套数</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.parkNumber}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="title">一级重大危险源</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.dangerLevelOne}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">二级重大危险源</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.dangerLevelTwe}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">三级重大危险源</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.dangerLevelThree}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="title">四级重大危险源</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.dangerLevelFour}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">试生产装置套数</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.tryUnitsNumber}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">重点监管危险工艺</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.dangerMsds}}</div>
                <div class="yellow">/种</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="title">是否有承包商作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.contractor==1? '是':'否'}}</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">是否处于试生产期</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.trialProduction==1? '是':'否'}}</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">是否处于开停产状态</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.openParking==1? '是':'否'}}</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="title">是否开展中（扩）试</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.test==1? '是':'否'}}</div>
              </div>
            </li>

            <li class="list">
              <div class="titleBox">
                <span class="title">有无重大隐患</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.mhazards==1? '有':'无'}}</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">检维修套数</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.fixNum}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">正在开停车装置数</span>
              </div>
              <div class="inputBox">
                 <div class="mainValue">{{entData.onOffDevice}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
            <!-- 占位符开始 -->
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span><span class="title"></span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="input"
                  placeholder=""
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span><span class="title"></span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="input"
                  placeholder="重点监管危险工艺"
                ></el-input>
                <div class="yellow">/种</div>
              </div>
            </li>
            <!-- 占位符结束 -->
          </ul>
          <!-- 企业现状结束 -->
          <!-- 作业风险开始 -->
          <div class="header">作业风险</div>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="title">特殊动火作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.firesNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">一级动火作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.fire1Number}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">二级动火作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.fire2Number}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="title">受限空间作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.spaceworkNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">盲板作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.blindplateNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">高处作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.highworkNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="title">吊装作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.liftingworkNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">临时用电作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.electricityworkNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">动土作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.soilworkNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="title">断路作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.roadworkNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">检维修作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.inspectionNumber}}</div>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list back">
              <div class="titleBox">
                <span class="title">二级动火作业</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.inspectionNumber}}</div>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <!-- 作业风险结束 -->
          <!-- 企业承诺开始 -->
          <div class="header">企业承诺</div>
          <ul v-if='entData.subject'>
            <li class="list textareaLi">
              <div class="titleBox textareaTitle">
                <span class="title">我司今日承诺</span>
              </div>
              <div class="textareaBox">
                <div class="mainValue">{{entData.subject}}</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="title">风险等级</span>
              </div>
              <div class="inputBox">
                <div class="mainValue" v-if="entData.riskGrade == 1">高风险</div>
                <div class="mainValue" v-else-if="entData.riskGrade == 2">较高风险</div>
                <div class="mainValue" v-else-if="entData.riskGrade == 3">一般风险</div>
                <div class="mainValue" v-else>低风险</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">承诺人</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.commitment}}</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="title">承诺时间</span>
              </div>
              <div class="inputBox">
                <div class="mainValue">{{entData.commiteDate}}</div>
              </div>
            </li>
          </ul>
          <!-- 企业承诺结束 -->
          
        </form>
        <div class="commitBox">
          <el-button type="primary" icon="el-icon-back" @click="back">返回</el-button>
        </div>
        <!-- 占位符 -->
        <div class="placeholder"></div>
      </div>
    </div>
  </div>
</template>

<script>
import {getEnterpriseDetail} from "@/api/reportedList";
export default {
  //import引入的组件
  name:"enterpriseDetail",
  components: {},
  data() {
    return {
      loading:false,
      entData:{}
    };
  },
  props: ['enterpriseId'],
  created () {
  },
  mounted() {
   console.log(this.enterpriseId);
   //获取企业详情数据
   this.getEntdata(); 
  },
  //方法集合
  methods: {
      //获取企业详情数据
      getEntdata(){
        this.loading = true;
        getEnterpriseDetail({
            id:this.enterpriseId
        })
        .then(data => {
            if(data.data.code=="success"){
                this.loading = false;
                this.entData = data.data.data;
            }
        })
        .catch(e => {
          console.log(e, "请求错误");
        });
      },
      back(){
        this.$router.push({ path: '/Home'}); 
      },
  }
};
</script>
<style lang="scss" scoped>
.enterpriseFill {
  margin: 15px 0;
  background-color: #fff;
  padding: 15px;
}
.col {
  display: flex;
  justify-content: space-between;
}
.header {
      font-size: 16px;
    line-height: 1;
    color: #656565;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
    width: 100%;
    text-align: left;
    margin-top: 20px;
    margin-bottom: 5px;
}
.titleBox {
  background-color: #f2f7f8;
  width: 158px;
  min-height: 55px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 10px;
  border-right: 1px solid #f0f0f0;
}
ul:nth-last-of-type(1) {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  height: 55px;
  overflow: hidden;
  padding: 0;
}
ul {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  // height: 55px;
  overflow: hidden;
  padding: 0;
}
.list {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  width: 100%;
  .title {
    font-size: 16px;
    color: #222;
  }
}
.listInput {
  width: 167px;
  height: 36px;
}
.red {
  color: red;
  font-weight: 900;
  font-size: 16px;
  margin-right: 3px;
}
.yellow {
  background-color: #f1ac5a;
  color: #fff;
  text-align: center;
  padding: 17px 0;
  display: block;
  width: 55px;
}
.inputBox {
  padding: 0;
  display: flex;
  align-items: center;
  .mainValue{
    position: absolute;
    left: 195px;
  }
  .listInput {
    margin-right: 10px;
    height: 40px;
    width: 205px;
  }
  .select {
    width: 260px;
    padding-right: 10px;
  }
}
.textareaBox {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  .mainValue{
      position: absolute;
    left: 195px;
  }
  .listTextarea {
    width: 95%;
  }
}
.textareaTitle{
  padding-top: 10px;
  padding-bottom: 10px;
}
.back {
  visibility: hidden;
}
.commitBox{
  width: calc(100% - 216px);
  margin-left: 216px;
  height: 70px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #fff;
      border-top: 1px solid #dfe0e0;
  .commit {
      color: #fff;
      background-color: #2892e2;
      border-color: #2892e2;
      text-align: center;
      display: inline-flex;
      align-items: center;
      border-radius: 4px;
      padding: 10px 20px;
      margin-left: 20px;
      font-weight: 900;
    }
}
.placeholder{
  width: 100%;
  height: 70px;
}
</style>
