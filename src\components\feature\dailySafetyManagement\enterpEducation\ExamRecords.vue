<template>
  <div class="education-training">
    <div class="search-container">
      <div class="search-left">
        <el-input
          v-model.trim="searchParams.candidateName"
          size="small"
          placeholder="请输入考生名称"
          class="input"
          clearable
        ></el-input>
        <el-select
          v-model="searchParams.scoreAssessment"
          placeholder="请选择成绩判定"
          size="small"
        >
          <el-option
            v-for="item in examinationTypes"
            :key="item.value"
            :value="item.value"
            :label="item.label"
          ></el-option>
        </el-select>
        <el-button type="primary" size="small" @click="handleSearch"
          >查询</el-button
        >
        <!-- <el-button type="success" size="small" @click="handleBack">导出</el-button> -->
      </div>
      <el-button type="success" size="small" @click="handleBack"
        >返回</el-button
      >
    </div>
    <div class="table-container">
      <el-table
        :data="tableData"
        :header-cell-style="headerCellStyle"
        border
        v-loading="loading"
        style="width: 100%"
        ref="multipleTable"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="examName"
          label="考试名称"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="candidateName"
          label="考生名称"
          width="120"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="unit"
          label="单位"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="examCourse"
          label="考试科目"
          width="120"
          align="center"
        />
        <el-table-column
          prop="singleCount"
          label="单选题总分"
          width="100"
          align="center"
        />
        <el-table-column
          prop="multipleCount"
          label="多选题总分"
          width="100"
          align="center"
        />
        <el-table-column
          prop="fractionCount"
          label="总分值"
          width="80"
          align="center"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="handleEdit('view', scope.row)">{{
              scope.row.fractionCount
            }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="scoreAssessment"
          label="成绩判定"
          width="80"
          align="center"
        >
        </el-table-column>
        <el-table-column label="操作" align="center" width="160" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit('view', scope.row)"
              >查看</el-button
            >
            <el-button type="text" size="small" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="searchParams.nowPage"
          :page-size="searchParams.pageSize"
          layout="total, prev, pager, next"
          background
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <examDetail
      v-if="showDialog"
      :visible="showDialog"
      :educationItem="educationItem"
      :dialogType="dialogType"
      @closeBoolean="showDialog = false"
    />
  </div>
</template>

<script>
import { deleteRecord, recordPage } from "@/api/enterpEducation";
import examDetail from "./components/examDetail.vue";
export default {
  components: {
    examDetail,
  },
  props: {
    recordId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      loading: false,
      searchParams: {
        candidateName: "",
        nowPage: 1,
        pageSize: 10,
        scoreAssessment: "",
      },
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      total: 0,
      tableData: [],
      educationItem: {},
      showDialog: false,
      dialogType: "add",
      examinationTypes: [
        { label: "不及格", value: "1" },
        { label: "及格", value: "2" },
        { label: "良好", value: "3" },
        { label: "优秀", value: "4" },
      ],
    };
  },
  filters: {
    statusFilter(status) {
      const statusMap = {
        1: "不及格",
        2: "及格",
        3: "良好",
        4: "优秀",
      };
      return statusMap[status];
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleBack() {
      this.$emit("back");
    },
    handleSearch() {
      this.searchParams.nowPage = 1;
      this.getData();
    },
    handleCurrentChange(val) {
      this.searchParams.nowPage = val;
      this.getData();
    },
    // 删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteRecord(row.id).then((res) => {
            if (res.data.status == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getData();
            }
          });
        })
        .catch(() => {});
    },
    // 编辑 查看
    handleEdit(type, row) {
      this.dialogType = type;
      this.showDialog = true;
      if (row) {
        this.educationItem = row;
      }
    },
    async getData() {
      this.loading = true;
      const params = { ...this.searchParams, examinationId: this.recordId };
      await recordPage(params).then((res) => {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;  
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.education-training {
  .search-container {
    display: flex;
    justify-content: space-between;
    min-width: 650px;
    margin-bottom: 20px;

    .search-left {
      display: flex;
      justify-content: flex-start;

      .input {
        width: 200px;
      }

      > * {
        margin-right: 15px;
      }
    }
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
