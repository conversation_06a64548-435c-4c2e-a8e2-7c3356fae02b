<template>
  <div class="production">
    <div class="production-titleBox" v-if="false">
      <div style="display: flex; align-items: center">
        <span
          style="
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          "
          >{{ oneList.name }}
        </span>
      </div>
      <div style="display: flex; align-items: center">
        <span
          style="
            flex: 1;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
          "
          >{{ oneList.uuitNo }}</span
        >
      </div>
    </div>
    <div class="fistTable scrl">
      <el-table
        :data="oneList.busiAddrDTOList"
        border
        style="width: 100%"
        :header-cell-style="headerCellStyle"
      >
        <el-table-column label="生产经营场所名称" prop="busiAddrName">
          <template slot-scope="scope">
            <span :title="scope.row.busiAddrName" style="cursor: pointer">{{
              scope.row.busiAddrName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="addr" label="生产经营场所详细地址">
          <template slot-scope="scope">
            <span :title="scope.row.addr">{{ scope.row.addr }}</span>
          </template>
        </el-table-column>
        <el-table-column label="经纬度">
          <template slot-scope="scope">
            <span>{{ scope.row.latitude }} {{ scope.row.longitude }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="90px">
          <template slot-scope="scope">
            <el-button type="text" @click="seeClick(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="secondTable" v-if="otherDataList.length">
      <p class="qtmc">其他名称</p>
      <div v-for="(item, index) in otherDataList" :key="index" class="tableBox">
        <div class="production-titleBox">
          <div>
            <span>{{ item.name }}</span>
          </div>
          <div>
            <span>{{ item.uuitNo }}</span>
          </div>
        </div>
        <el-table
          :data="item.busiAddrDTOList"
          border
          style="width: 100%"
          :header-cell-style="headerCellStyle"
        >
          <el-table-column label="生产经营场所名称" prop="busiAddrName">
            <template slot-scope="scope">
              <span :title="scope.row.busiAddrName" style="cursor: pointer">{{
                scope.row.busiAddrName
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="addr" label="生产经营场所详细地址">
            <template slot-scope="scope">
              <span :title="scope.row.addr">{{ scope.row.addr }}</span>
            </template>
          </el-table-column>
          <el-table-column label="经纬度">
            <template slot-scope="scope">
              <span>{{ scope.row.latitude }} {{ scope.row.longitude }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="90px">
            <template slot-scope="scope">
              <el-button type="text" @click="seeClick(scope.row)"
                >详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog
      title="生产经营产所详情"
      :visible.sync="dialogVisible3"
      width="39%"
      top="6vh"
      :append-to-body="true"
      class="scjycs"
    >
      <div class="detailsBox">
        <div class="map">
          <!-- <egisMap
            :postflag="true"
            :mapinfo="mapInfo"
            :isdetail="true"
            ref="seeMap"
          ></egisMap> -->
        </div>
        <div class="details">
          <div class="detailsBusiAddrName">
            <span class="label">生产经营场所名称：</span>
            <span class="value">{{ currentBusiAddr.busiAddrName }}</span>
          </div>
          <div class="detailsBusiAddrAddress">
            <span class="label">生产场所详细地址：</span>
            <span class="value">{{ currentBusiAddr.addr }}</span>
          </div>
          <div class="detailsPosition">
            <span class="label">经纬度坐标：</span>
            <span class="value">{{
              currentBusiAddr.longitude && currentBusiAddr.latitude
                ? currentBusiAddr.longitude + "，" + currentBusiAddr.latitude
                : ""
            }}</span>
          </div>
        </div>
      </div>
      <div class="footer">
        <el-button
          type="primary"
          size="small"
          plain
          @click="dialogVisible3 = false"
          ><i class="close"></i> 关闭</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
// import egisMap from '@/components/egisMap'
export default {
  components: {
    // egisMap
  },
  data() {
    return {
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      mapInfo: {},
      newRiskItemDTOList: [
        {
          riskInfo: "生产场所名称生产场所名称",
          address: "江苏省南京市鼓楼区湖南路街道车站东巷南京绿地145号#",
          jwd: "32.06187.118.78546",
        },
      ],
      currentBusiAddr: {},
      otherDataList: [], // 其他名称数据
      oneList: [], // 主数据
      dialogVisible3: false,
    };
  },
  computed: {},
  props: {
    previewInfo: {
      type: Object,
    },
  },
  created() {
    if (
      this.previewInfo &&
      this.previewInfo.enterpriseInfoDTO.busiAddrAndNameDTOList.length
    ) {
      this.oneList =
        this.previewInfo.enterpriseInfoDTO.busiAddrAndNameDTOList[0];
      this.otherDataList =
        this.previewInfo.enterpriseInfoDTO.busiAddrAndNameDTOList.slice(1);
    }
  },
  methods: {
    seeClick(item) {
      const that = this;
      this.dialogVisible3 = true;
      this.currentBusiAddr = item;
      this.$nextTick(() => {
        that.$set(that.mapInfo, "data", {
          location: {
            x: that.currentBusiAddr.longitude,
            y: that.currentBusiAddr.latitude,
          },
        });
        // that.$refs.seeMap['getAddressToDiot'](that.mapInfo); // 设置地图点
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.production {
  width: 100%;
  .production-titleBox {
    display: flex;
    align-items: center;
    height: 32px;
    width: 100%;
    div {
      width: 50%;
      font-size: 16px;
      line-height: 32px;
      color: #086dd4;
      font-weight: bold;
      font-family: "Microsoft Ya Hei";
      i {
        display: inline-block;
        width: 6px;
        height: 6px;
        background-color: #415ef0;
        margin-right: 6px;
        vertical-align: 4px;
      }
    }
  }
  .fistTable {
    width: 100%;
  }
  .secondTable {
    width: 100%;
    padding: 12px;
    background-color: #f4f8fb;
    .qtmc {
      font-size: 14px;
      line-height: 24px;
      color: #49445f;
      font-family: "Microsoft Ya Hei";
      margin-top: -10px;
    }
    .tableBox {
      background-color: #ffffff;
      margin-bottom: 16px;
      padding: 0 10px;
    }
    .production-titleBox {
      display: flex;
      align-items: center;
      height: 40px;
      width: 100%;
      div {
        width: 50%;
        font-size: 16px;
        line-height: 40px;
        color: #49445f;
        font-family: "Microsoft Ya Hei";
        i {
          display: inline-block;
          width: 6px;
          height: 6px;
          background-color: #49445f;
          margin-right: 6px;
          vertical-align: 4px;
        }
      }
    }
  }
}
</style>
<style lang="scss">
// @imgUrl: '../../../assets/img/dialog';
.scjycs {
  .detailsBox {
    width: 100%;
    // height: 474px;
    .map {
      height: 261px;
      margin-bottom: 20px;
    }

    .details {
      font-size: 16px;
      line-height: 40px;
      color: #333;
      > div {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #d5d5d5;
        .label {
          width: 126px;
          text-align: right;
          font-size: 14px;
          line-height: 40px;
          color: #534d6a;
          font-family: "Microsoft Ya Hei";
        }
        .value {
          font-size: 14px;
          line-height: 40px;
          color: #534d6a;
          font-family: "Microsoft Ya Hei";
        }
      }
    }
  }
  .footer {
    padding-top: 10px;
    text-align: center;
  }
}
</style>
