<!-- 企业画像管理 -->
<template>
  <div class="enterprisePortrait">
    <component
      :is="
        $route.name === 'enterprisePortraitList'
          ? 'enterprisePortraitList'
          : $route.name
      "
    ></component>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {
    enterprisePortraitList: () => import("./enterprisePortraitList"),
    enterprisePortraitReport: () => import("./enterprisePortraitReport"),
    portaitConfiguration: () => import("./portaitConfiguration"),
    countReport: () =>
      import("../workingAccount/smartReport/countReport/index.vue"),
  },
  data() {
    return {};
  },
  //方法集合
  methods: {},
  computed: {
    ...mapStateLogin({
      user: (state) => state.user,
    }),
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},

  watch: {},
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
