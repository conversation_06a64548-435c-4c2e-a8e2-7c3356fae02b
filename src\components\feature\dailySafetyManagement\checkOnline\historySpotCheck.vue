<template>
  <div class="historySpotCheck">
    <div class="header">
      <el-date-picker v-model="value1"
                      size="mini"
                      type="daterange"
                      value-format="yyyy-MM-dd"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="searchTime"
                      unlink-panels
                      style="width: 370px"
                      :picker-options="{
          disabledDate: (time) => {
            return (
              time.getTime() >
              new Date(new Date().Format('yyyy-MM-dd 23:59:59')).getTime()
            );
          },
        }"></el-date-picker>
      <el-select v-model="feedbackStatus"
                 placeholder="请选择状态"
                 size="mini"
                 style="width: 190px"
                 clearable>
        <el-option v-for="(item, index) in optionses"
                   :key="index"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
      <el-select v-model="selfFlag"
                 placeholder="下发等级"
                 size="mini"
                 style="width: 190px"
                 clearable>
        <el-option v-for="(item, index) in optionsesLevel"
                   :key="index"
                   :label="item.label"
                   :value="item.value">
        </el-option>
          

      </el-select>
      <el-cascader size="mini"
                   placeholder="请选择地市/区县"
                   :options="district"
                   v-model="distCode"
                   v-if="($store.state.login.user.user_type == 'gov' && user.distRole == '0') || ($store.state.login.user.user_type == 'gov' && user.distRole == '1')"
                   :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
                   clearable
                   @change="handleChange"
                   ref="myCascader"
                   :show-all-levels="true"
                   style="width: 190px; margin-top: 10px"></el-cascader>
      <el-select v-model="parkId"
                 v-if="($store.state.login.user.user_type == 'gov' && user.distRole == '0') || ($store.state.login.user.user_type == 'gov' && user.distRole == '1')"
                 placeholder="请选择园区"
                 size="mini"
                 style="width: 190px"
                 clearable>
        <el-option v-for="(item, index) in parkListArr"
                   :key="index"
                   :label="item.parkName"
                   :value="item.parkId">
        </el-option>
      </el-select>
      <el-button type="primary"
                 style="margin-top: 10px"
                 size="mini"
                 @click="search">查询</el-button>
      <CA-button type="primary"
                 style="margin-top: 10px"
                 plain
                 size="mini"
                 @click="exportExcel">导出</CA-button>
      <!-- <el-button type="primary"
                 style="margin-top: 10px;float: right;margin-right: 0;"
                 size="mini"
                 @click="automaticIssue()">一键自动下发</el-button> -->
    </div>
    <div class="container">
      <div class="title">历史抽查记录表</div>
      <div>
        <el-table :data="tableData"
                  border
                  v-loading="loading"
                  style="width: 100%"
                  :header-cell-style="{
            textAlign: 'center',
            color: 'rgb(51, 51, 51)',
            backgroundColor: 'rgb(242, 246, 255)',
          }"
                  @selection-change="handleSelectionChange"
                  @select="select"
                  @select-all="select">
          <el-table-column type="selection"></el-table-column>
          <el-table-column type="index"
                           label="序号"
                           width="60">
          </el-table-column>
          <el-table-column prop="distName"
                           label="行政区划"
                           width="180">
          </el-table-column>
          <el-table-column prop="companyName"
                           label="企业名称"
                           width="160"
                           :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column prop="dangerLevelCode"
                           label="危险源等级">
            <template slot-scope="scope">
              <span v-if="scope.row.dangerLevelCode == '1'">一级</span>
              <span v-else-if="scope.row.dangerLevelCode == '2'">二级</span>
              <span v-else-if="scope.row.dangerLevelCode == '3'">三级</span>
              <span v-else-if="scope.row.dangerLevelCode == '4'">四级</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="online"
                           label="系统在线"
                           width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.online == '0'">离线</span>
              <span v-else-if="scope.row.online == '1'">在线</span>
            </template>
          </el-table-column>
          <el-table-column prop="videoOnline"
                           label="视频在线"
                           width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.videoOnline == '0'">离线</span>
              <span v-else-if="scope.row.videoOnline == '1'">在线</span>
            </template>
          </el-table-column>
          <el-table-column prop="warnRank"
                           label="预警等级"
                           width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.warnRank == '1'">红色预警</span>
              <span v-else-if="scope.row.warnRank == '2'">橙色预警</span>
              <span v-else-if="scope.row.warnRank == '3'">黄色预警</span>
              <span v-else>蓝色预警</span>
            </template>
          </el-table-column>
          <el-table-column prop="commitStatus"
                           label="是否安全承诺"
                           width="110px">
            <template slot-scope="scope">
              <span v-if="scope.row.commitStatus == '0'">否</span>
              <span v-else-if="scope.row.commitStatus == '1'">是</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarmCount"
                           label="未消警次数"
                           width="100px">
          </el-table-column>
          <el-table-column prop="isLastWeekHignWarnRank"
                           label="7日内是否出现过较大及以上安全风险等级"
                           width="150px">
            <!-- <template slot-scope="scope">
              <span v-if="scope.row.lastWeekWarnRank == '1'">重大风险</span>
              <span v-else-if="scope.row.lastWeekWarnRank == '2'"
                >较大风险</span
              >
              <span v-else-if="scope.row.lastWeekWarnRank == '3'"
                >一般风险</span
              >
              <span v-else-if="scope.row.lastWeekWarnRank == '4'">低风险</span>
            </template> -->
          </el-table-column>
          <el-table-column prop="patrolledTime"
                           label="抽查时间"
                           width="160px">
          </el-table-column>
          <el-table-column prop="patrolledBy"
                           label="抽查人"
                           width="90px">
          </el-table-column>
          <el-table-column prop="distNamePatrol"
                           label="抽查行政区划">
          </el-table-column>
          <el-table-column prop="feedbackStatus"
                           label="状态"
                           width="150px">
            <template slot-scope="scope">
              <span v-if="scope.row.feedbackStatus == '0'">未下发</span>
              <span v-else-if="scope.row.feedbackStatus == '1'">已下发,待反馈</span>
              <span v-else-if="scope.row.feedbackStatus == '2'">已反馈</span>
            </template>
          </el-table-column>
          <el-table-column prop="feedbackTime"
                           label="反馈时间"
                           width="160px">
          </el-table-column>
          <el-table-column prop="address"
                           label="操作"
                           width="150px"
                           fixed="right">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="addSpotCheck(scope.row)"
                         v-if="
                  scope.row.feedbackStatus == '0' &&
                  $store.state.login.user.isDanger == '1'
                ">下发</el-button>
              <el-button type="text"
                         @click="handleDelete(scope.row)"
                         v-if="
                  scope.row.feedbackStatus == '0' &&
                  $store.state.login.user.isDanger == '1'
                ">删除</el-button>
              <!-- <el-button type="text"
                         disabled
                         v-if="scope.row.feedbackStatus == '1'">反馈详情</el-button> -->
              <el-button type="text"
                         @click="addSpotCheckDetails(scope.row)"
                         v-if="scope.row.feedbackStatus == '2'">反馈详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="currentPage"
                         background
                         layout="total, prev, pager, next"
                         :total="total"
                         v-if="total != 0">
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="">
      <el-dialog title="下发抽查情况"
                 @close="showSpotCheckFn"
                 :visible.sync="showSpotCheck"
                 :close-on-click-modal="false"
                 width="740px"
                 v-dialog-drag>
        <el-descriptions :column="1"
                         border
                         :labelStyle="labelStyle">
          <el-descriptions-item>
            <template slot="label"> 企业名称 </template>
            {{ spotCheckDetail.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查反馈类型 </template>
            <!-- <el-checkbox-group v-model="checkList" style="width: 570px">
              <el-checkbox label="1" disabled>安全风险等级</el-checkbox>
              <el-checkbox label="2" disabled>未进行安全承诺</el-checkbox>
              <el-checkbox label="3" disabled>未消警数量</el-checkbox>
              <el-checkbox label="4" disabled>系统在线质量低</el-checkbox>
              <el-checkbox label="5" disabled>视频在线质量低</el-checkbox>
              <el-checkbox
                label="6"
                disabled
              >七日内出现过较大及以上安全风险等级</el-checkbox>
              <el-checkbox label="7" disabled>其他异常情况</el-checkbox>
            </el-checkbox-group> -->
            <div v-if="checkList.length > 0">
              <span class="replyTypeList"
                    v-for="(item, index) in checkList"
                    :key="index">
                {{ index + 1 }}、
                <span v-if="item == '1'">安全风险等级</span>
                <span v-else-if="item == '2'">未进行安全承诺</span>
                <span v-else-if="item == '3'">未消警数量</span>
                <span v-else-if="item == '4'">系统在线质量低</span>
                <span v-else-if="item == '5'">视频在线质量低</span>
                <span v-else-if="item == '6'">七日内出现过较大及以上安全风险等级</span>
                <span v-else-if="item == '7'">其他异常情况</span>
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 其他情况说明 </template>
            <el-input v-model.trim="otherContent"
                      type="textarea"
                      placeholder="最多可输入500字"
                      maxlength="500"
                      show-word-limit
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      resize="none">
            </el-input>
          </el-descriptions-item>
          <!--          <el-descriptions-item>-->
          <!--            <template slot="label"> 是否完成处置 </template>-->
          <!--            <el-select v-model="isFinish" placeholder="请选择">-->
          <!--              <el-option-->
          <!--                label="是"-->
          <!--                value="1">-->
          <!--              </el-option>-->
          <!--              <el-option-->
          <!--                label="否"-->
          <!--                value="0">-->
          <!--              </el-option>-->
          <!--            </el-select>-->
          <!--          </el-descriptions-item>-->
          <!--          <el-descriptions-item>-->
          <!--            <template slot="label"> 情况说明 </template>-->
          <!--            <el-input-->
          <!--              v-model="situation"-->
          <!--              type="textarea"-->
          <!--              placeholder="最多可输入500字"-->
          <!--              maxlength="500"-->
          <!--              show-word-limit-->
          <!--              :autosize="{ minRows: 4, maxRows: 8 }"-->
          <!--              resize="none"-->
          <!--            >-->
          <!--            </el-input>-->
          <!--          </el-descriptions-item>-->

          <!--          <el-descriptions-item>-->
          <!--            <template slot="label"> 处置措施 </template>-->
          <!--            <el-input-->
          <!--              v-model="measure"-->
          <!--              type="textarea"-->
          <!--              placeholder="最多可输入500字"-->
          <!--              maxlength="500"-->
          <!--              show-word-limit-->
          <!--              :autosize="{ minRows: 4, maxRows: 8 }"-->
          <!--              resize="none"-->
          <!--            >-->
          <!--            </el-input>-->
          <!--          </el-descriptions-item>-->
        </el-descriptions>
        <span slot="footer"
              class="dialog-footer">
          <el-button @click="showSpotCheckFn"
                     size="mini">取 消</el-button>
          <el-button type="primary"
                     @click="xiafaSub"
                     size="mini">提 交</el-button>
        </span>
      </el-dialog>
      <el-dialog title="企业反馈详情"
                 :visible.sync="showSpotCheckDetails"
                 width="740px"
                 v-dialog-drag
                 top="5vh"                
                 close-on-click-modal>
        <el-descriptions :column="1"
                         border
                         :labelStyle="labelStyle">
          <el-descriptions-item>
            <template slot="label"> 企业名称 </template>
            {{ spotDetail.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查反馈类型 </template>
            {{ spotDetail.replyTypeContent }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 其他情况说明 </template>
            {{ spotDetail.otherContent }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 是否完成处置 </template>
            {{ spotDetail.isFinish == 1 ? "是" : "否" }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label"> 抽查人 </template>
            {{ spotDetail.patrolledBy }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查单位 </template>
            {{ spotDetail.patrolledUnit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 企业情况说明 </template>
            <!-- <div>
              1.因企业为试生产期间，未取得安全生产许可证，无需进行安全承诺上报。
            </div>
            <div>
              2.因企业设备调试，出现大量误报警数据，导致企业出现较大风险，已进行相关处理。
            </div> -->
            {{ spotDetail.situation }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 处置措施 </template>
            {{ spotDetail.measure }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期日期 </template>
            {{ new Date(spotDetail.planDate).Format("yyyy-MM-dd") }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期说明 </template>
            {{ spotDetail.delaySituation }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 计划完成日期 </template>
            {{ new Date(spotDetail.planDate).Format("yyyy-MM-dd") }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 反馈时间 </template>
            {{ spotDetail.feedbackTime }}
          </el-descriptions-item>
        </el-descriptions>
      </el-dialog>
      <el-dialog title="一键下发抽查情况"
                 :visible.sync="oneShowSpotCheck"
                 width="80%"
                 :close-on-click-modal="false"
                 v-dialog-drag>
        <el-table :data="issueData"
                  border
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }">
          <el-table-column prop="companyName"
                           label="企业名称"
                           width="300px">
          </el-table-column>
          <el-table-column label="抽查反馈类型"
                           width="450px">
            <template slot-scope="{ row }">
              <el-checkbox-group v-model="row.replyTypeList.split(',')"
                                 style="width: 450px">
                <el-checkbox label="1"
                             disabled>安全风险等级</el-checkbox>
                <el-checkbox label="2"
                             disabled>未进行安全承诺</el-checkbox>
                <el-checkbox label="3"
                             disabled>未消警数量</el-checkbox>
                <el-checkbox label="4"
                             disabled>系统在线质量低</el-checkbox>
                <el-checkbox label="5"
                             disabled>视频在线质量低</el-checkbox>
                <el-checkbox label="6"
                             disabled>七日内出现过较大及以上安全风险等级</el-checkbox>
                <el-checkbox label="7"
                             disabled>其他异常情况</el-checkbox>
              </el-checkbox-group>
            </template>
          </el-table-column>
          <el-table-column prop="otherContent"
                           label="其他情况说明">
            <template slot-scope="{ row }">
              <el-input type="textarea"
                        placeholder="最多可输入500字"
                        maxlength="500"
                        show-word-limit
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        resize="none"
                        v-model.trim="row.otherContent">
              </el-input>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer"
              class="dialog-footer">
          <el-button @click="oneShowSpotCheck = false"
                     size="mini">取 消</el-button>
          <el-button type="primary"
                     @click="automaticIssue"
                     size="mini">提 交</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { MessageBox } from 'element-ui'
import { Message } from 'element-ui'
import { mapState, createNamespacedHelpers } from 'vuex'
const { mapState: mapStateControler } = createNamespacedHelpers('controler')
import { parseTime } from '@/utils/index'
import {
  querySpotCheckHis,
  issueSpotCheck,
  deleteSpotCheckById,
  spotCheckHisDetail,
  exportSpotCheckHis,
  getParkList
} from '@/api/dailySafety'
export default {
  name: 'historySpotCheck',
  data() {
    return {
      distCode: this.$store.state.login.userDistCode || '',
      district: this.$store.state.controler.district,
      parkListArr: [],
      parkId: '',
      value1: [new Date(), new Date()],
      startTime: this.format(
        new Date(new Date().setHours(0, 0, 0, 0)).getTime()
      ),
      endTime: this.format(new Date().getTime()),
      feedbackStatus: '',
      showSpotCheck: false,
      showSpotCheckDetails: false,
      spotCheckDetail: {},
      spotDetail: {},
      checkList: [],
      otherContent: '',
      currentPage: 1,
      size: 10,
      total: 0,
      selection: [],
      loading: true,
      labelStyle: {
        textAlign: 'center',
        width: '200px',
        backgroundColor: 'rgb(242, 246, 255)'
      },
      multipleSelection: [],
      tableData: [],
      optionsesLevel:[
      {
          value: '0',
          label: '只看本级'
        },
        {
          value: '1',
          label: '全部'
        },
      ],
      optionses: [
        {
          value: '0',
          label: '未下发'
        },
        {
          value: '1',
          label: '已下发，待反馈'
        },
        {
          value: '2',
          label: '已反馈'
        }
      ],
      value: '',
      issueData: [],
      oneShowSpotCheck: false,
      selfFlag:""
    }
  },
  computed: {
    ...mapState({
      user: state => state.login.user
    }),
    ...mapStateControler({
      vuexDistrict: state => state.district,
      automaticSpotCheckData: state => state.automaticSpotCheckData
    })
  },
  methods: {    
    showSpotCheckFn(){
      this.otherContent=''
      this.showSpotCheck = false
    },

    onceShowSpotCheck() {
      this.oneShowSpotCheck = true
      this.issueData = []
      if (this.$store.state.controler.automaticSpotCheckData.length > 0) {
        this.$store.state.controler.automaticSpotCheckData.forEach(item => {
          this.issueData.push({
            inspectId: item.inspectId,
            companyName: item.companyName,
            replyTypeList: item.replyType,
            otherContent: ''
          })
        })
      }
    },
    //一键自动下发
    automaticIssue() {
      issueSpotCheck({
        // inspectIds: this.$store.state.controler.automaticSpotCheckData,
        // otherContent: '',
        issueReqs: this.issueData
      })
        .then(res => {
          if (res.data.code === 0) {
            this.$message({
              message: '下发成功',
              type: 'success'
            })
            this.oneShowSpotCheck = false
            this.getQuerySpotCheckHis()
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    handleChange(value) {
      if (value) {
        this.distCode = value
      } else {
        this.distCode = this.$store.state.login.userDistCode
      }
    },
    //获取园区列表
    getPark() {
      getParkList().then(res => {
        if (res.data.code == 0) {
          this.parkListArr = res.data.data
        }
      })
    },
    //获取历史抽查记录列表
    getQuerySpotCheckHis() {
      this.loading = true
      querySpotCheckHis({
        current: this.currentPage,
        startTime: this.startTime + ' 00:00:00',
        size: this.size,
        systemFlag: 1,
        endTime: this.endTime + ' 23:59:59',
        feedbackStatus: this.feedbackStatus,
        selfFlag:this.selfFlag,
        distCode: this.distCode || this.$store.state.login.userDistCode,
        parkId: this.parkId
      }).then(res => {
        if (res.data.code == 0) {
          this.loading = false
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      })
    },
    gettime() {
      this.value1 = [new Date(), new Date()]
    },
    add0(m) {
      return m < 10 ? '0' + m : m
    },
    format(shijianchuo) {
      var time = new Date(shijianchuo)
      var y = time.getFullYear()
      var m = time.getMonth() + 1
      var d = time.getDate()
      var h = time.getHours()
      var mm = time.getMinutes()
      var s = time.getSeconds()
      return y + '-' + this.add0(m) + '-' + this.add0(d)
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0])
        let dataTime1 = parseTime(date1, '{y}-{m}-{d}')
        let date2 = new Date(value[1])
        let dataTime2 = parseTime(date2, '{y}-{m}-{d}')
        this.startTime = dataTime1
        this.endTime = dataTime2
      } else {
        this.value1 = ''
        this.startTime = ''
        this.endTime = ''
      }
    },
    search() {
      this.currentPage = 1
      this.getQuerySpotCheckHis()
    },
    addSpotCheck(value) {
      this.showSpotCheck = true
      this.spotCheckDetail = value
      if (this.spotCheckDetail.replyType) {
        this.checkList = this.spotCheckDetail.replyType.split(',')
      } else {
        this.checkList = []
      }
    },
    //下发确定
    xiafaSub() {
      issueSpotCheck({
        issueReqs: [
          {
            inspectId: this.spotCheckDetail.id,
            otherContent: this.otherContent
          }
        ]
      }).then(res => {
        if (res.data.code == 0) {
          this.showSpotCheck = false
          this.otherContent = ''
          this.checkList = []
          this.getQuerySpotCheckHis()
        }
      })
    },
    //删除
    handleDelete(row) {
      MessageBox.confirm('确定要删除选择的数据吗?', '通知', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteSpotCheckById({ id: row.id })
            .then(res => {
              this.$message.success('删除成功')
              this.getQuerySpotCheckHis()
            })
            .catch(e => {
              console.log(e, '请求错误')
            })
        })
        .catch(() => {})
    },
    addSpotCheckDetails(value) {
      spotCheckHisDetail({
        id: value.id
      }).then(res => {
        if (res.data.code == 0) {
          this.showSpotCheckDetails = true
          this.spotDetail = res.data.data
        }
      })
    },
    // addSpotCheckFeedback() {
    //   this.showSpotCheckFeedback = true;
    // },
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id
      }
    },
    // 导出
    exportExcel() {
      //   let list = [this.distCode, ...this.selection];
      exportSpotCheckHis({
        inspectId: this.selection.length <= 0 ? '' : this.selection.join(','),
        feedbackStatus: this.feedbackStatus,
        selfFlag:this.selfFlag,
        startTime: this.startTime + ' 00:00:00',
        endTime: this.endTime + ' 23:59:59',
        systemFlag: 1,
        // distCode: this.$store.state.login.userDistCode || "",
        distCode: this.distCode || this.$store.state.login.userDistCode,
        parkId: this.parkId
      }).then(response => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          // this.$message({
          //   message: "导出成功",
          //   type: "success",
          // });
          Message.success('导出成功')
        } else {
          Message.error('导出失败')
        }
        const blob = new Blob([response.data], { type: 'application/xls' })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '抽查记录表' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getQuerySpotCheckHis()
    }
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-select__tags-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
  display: block;
}
/deep/ .cell .el-button {
  padding: 0;
}
.historySpotCheck {
  .header {
    margin-bottom: 20px;
    & > * {
      margin-right: 20px;
    }
  }
  .container {
    .title {
      font-size: 18px;
      width: 100%;
      text-align: left;
      padding-bottom: 10px;
      font-weight: 900;
    }
  }
  .pagination {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
  }
  .replyTypeList {
    padding-right: 20px;
  }
}
</style>
