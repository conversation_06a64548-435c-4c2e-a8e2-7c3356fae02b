<template>
  <div class="app-container">
    <el-dialog
      title="选择模板"
      :visible.sync="open"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-table v-loading="loading" :data="templeList" @row-click="rowClick">
        <el-table-column label="模板名称" align="center" prop="tmpltName">
          <template slot-scope="scope">
            <div class="tableItem">{{ scope.row.tmpltName }}</div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  getTemple,
  getTempleList,
} from "../../../../../api/informationRelease";

export default {
  name: "TemplateChoose",
  components: {},
  props: {
    temStatus: {
      type: Number,
      default: null,
    },
    temType: {
      type: Number,
      default: null,
    },
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      // total: 0,
      // 安全预警表格数据
      templeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tmpltName: null,
        tmpltType: null,
        tmpltStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      statusList: [
        { label: "全部", value: "-1" },
        { label: "已发布", value: "1" },
        { label: "待发布", value: "0" },
      ],
    };
  },
  mounted() {
    // console.log('我先执行')
    // this.getList()
  },
  methods: {
    getList() {      
      this.loading = true;
      this.queryParams.tmpltType = this.temType;
      this.queryParams.tmpltStatus = this.temStatus;
      getTempleList(this.queryParams).then((response) => {
        this.templeList = response.data.data.list;
        console.log(response.data.data,'我啥时候执行')
        this.loading = false;
      });
    },

    /** 修改按钮操作 */
    getTempleDetails(row) {
      getTemple({ tmpltCode: row.tmpltCode }).then((response) => {
        console.log(response);
        this.open = false;
        this.$emit("choosedTemplateInfo", response.data.data);
      });
    },

    rowClick(e) {
      this.getTempleDetails(e);
    },
  },
};
</script>

<style lang="scss" scoped>
.tableItem {
  cursor: pointer;
  &:hover {
    color: #1890ff;
  }
}
</style>
