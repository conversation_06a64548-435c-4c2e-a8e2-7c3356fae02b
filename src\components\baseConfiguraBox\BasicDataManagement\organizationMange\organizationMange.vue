<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" /> 组织机构管理
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="duty-list padding_28">
      <div class="duty-list-left">
        <!-- <search-tree :defaultprops="defaultprops" ref="searchtree" nodekey="orgCode" :lazyload="false" :treedata="treeData"
            @change="getOrg" :defaultchecked="defaultchecked">
            </search-tree> -->

        <div class="search_tree">
          <div class="search_slide_input">
            <div class="search_slide_searchKey">
              <!-- <el-button
                slot="reference"
                size="small"
                type="success"
                @click="addPop"
                plain
                icon="el-icon-plus"
                >新增</el-button
              >
              <el-button
                slot="reference"
                size="small"
                type="primary"
                plain
                icon="el-icon-edit"
                >编辑</el-button
              >

              <el-button size="small" type="warning" plain icon="el-icon-delete"
                >删除</el-button
              > -->

              <!-- 与严雨露商议暂时隐藏搜索图标 Modify-程云 2019-12-2 -->
              <!-- <el-input @focus="searchKey = false" @input="changeSearchStatus" ref="input" v-model.trim="slideValue" type="text" suffix-icon="el-icon--right el-icon-search" placeholder="请输入机构名称"> -->
              <el-input
                v-model.trim="filterText"
                placeholder="请输入需要查询的机构名称"
                clearable
                maxlength="10"
                show-word-limit
              >
              </el-input>
              <!-- <div id="out_contain" class="tabs_list" v-show="searchKey">
                        <el-scrollbar>
                        <ul id="filter_list" class="filter_list">
                            <div v-if="searchList.length == 0" style="text-align:center;height:100%;padding:12px 0">
                            <span>
                                未搜索到数据
                            </span>
                            </div>
                            <li v-for="(item,index) in searchList" @click.stop="handleSelect(item,index)">
                            <span class="filter_list_content">{{item.fullOrgName || item.orgName}}</span>
                            </li>
                        </ul>
                        </el-scrollbar>
                    </div>
                    <div class="popper__arrow" v-show="searchKey" style="left: 35px;"></div> -->
            </div>
            <div class="search_slide" style="opacity:1'">
              <!-- <div v-if="configtree ? configtree.buttonShow || false : false" class="controllab">
                        <el-button v-if="buttonItem.expression" size="mini" :title="buttonItem.title" :icon="buttonItem.icon"
                        :type="buttonItem.type || text" v-for="buttonItem,btnInd in configtree.buttonConfig"
                        @click="operateFun(buttonItem.emit,buttonItem)" :key="btnInd" plain>{{buttonItem.name}}</el-button>
                    </div> -->

              <el-scrollbar id="mainheight">
                <div class="headertitle">
                  <slot name="header"></slot>
                </div>
                <div class="navtitle">
                  <slot name="nav"></slot>
                </div>
                <el-tree
                  class="filter-tree"
                  :data="treeData"
                  v-loading="loadingTree"
                  :props="defaultProps"
                  node-key="id"
                  :default-expanded-keys="defaultShowNodes"
                  :filter-node-method="filterNode"
                  @node-click="handleNodeClick"
                  ref="tree"
                >
                </el-tree>
                <!-- <el-tree :class="!!lazyload?'lazy_tree':''" :empty-text="placeholderText" ref="tree" :data="treeData"
                        highlight-current :node-key="nodekey || 'id'" :default-expanded-keys="[defaultcheckeds]"
                        :current-node-key="defaultcheckeds" @node-expand="treeLoad" :expand-on-click-node="false"
                        :props="defaultprops || getDefaultProps" @node-click="handleNodeClick">
                        <span :title="scope.node.label" v-if="!!lazyload" slot-scope="scope" class="custom-tree-node">
                            <i v-if="scope.data.leaf" class="header-drops-i headerdrops"></i>
                            <i v-if="!scope.data.leaf" @click.stop="treeLoad(scope.data,scope.node)"
                            :class="[!!scope.node.expanded && scope.data.children.length >0 ?'el-icon-caret-bottom':'el-icon-caret-right',temp.style.tree_icon]"></i>
                            <span>{{scope.node.label}}</span>
                        </span>
                        <span v-else class="custom-tree-node" :title="scope.node.label">
                            <span>{{scope.node.label}}</span>
                        </span>
                        </el-tree> -->
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
      <div class="duty-list-right flex-full">
        <div class="table">
          <ul class="container" v-if='curClick'>
            <li>
              <div class="l">机构名称</div>
              <div class="r">{{curClick.orgName}}</div>
            </li>

             <!-- <li>
              <div class="l">机构简称</div>
              <div class="r">{{curClick.label}}</div>
            </li> -->


             <!-- <li>
              <div class="l">下属机构</div>
              <div class="r">{{curClick.children?curClick.children.length:''}}</div>
            </li> -->

             <li>
              <div class="l">区域代码</div>
              <div class="r">{{curClick.orgCode}}</div>
            </li>

              <li>
              <div class="l">电话</div>
              <div class="r">{{curClick.telphone}}</div>
            </li>

              <li class="bottom">
              <div class="l ">传真</div>
              <div class="r " >{{curClick.fax}}</div>
            </li>

             <!-- <li >
              <div class="l">机构职责</div>
              <div class="r">武汉有机实业有限公司</div>
            </li> -->

           
                                      
            
          </ul>
        </div>

        <div class="search_filter padding_0">
          <div class="maillist-title">下属机构</div>
          <div class="right_filter">
            <!-- <el-button type="primary" icon="el-icon-mobile-phone" @click="addressCall(null)">自定义拨号
                    </el-button> -->
            <!-- <el-button type="primary"
                       @click="openOrgUserOperateDialog">数据维护</el-button> -->
            <el-input placeholder="机构名称"
                      v-model.trim="searchData.orgName"
                      class="search_input"
                      clearable
                      style="width: 300px;"
                      maxlength="11">
              <i slot="append"
                 @click="searchByName"
                 class="el-icon-search"></i>
            </el-input>
          </div>
        </div>
        <div class="list_contain">
          <!-- 表 -->
          <div class="list_top">
            <div class="list_left flex-full">
              <!-- <list-table :propdata="propData" @tablecallback="tablecallback" unsortable="true"
                    @handleselection="handleSelectionData" :defaultselectfalse="defaultselect" rowkey="personId"></list-table> -->

              <div class="table">
                <el-table
                  :data="personData"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                  border
                  v-loading="loadingTable"
                  style="width: 100%"
                  ref="multipleTable"
                >
                  <!-- <el-table-column
                    type="selection"
                    width="55"
                    fixed="left"
                    align="center"
                  >
                  </el-table-column> -->
                  <el-table-column
                    prop="orgName"
                    label="机构名称"
                    align="center"
                    :show-overflow-tooltip="true"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="isDanger"
                    label="危化监管端状态"
                    align="center"
                  >
                  <template slot-scope="scope">
                    <span v-if='scope.row.isDanger==1'>是</span>
                    <span v-if='scope.row.isDanger==0'>否</span>
                  </template>


                  </el-table-column>
                  <!-- <el-table-column
                    prop="company"
                    label="人员单位"
                    align="center"
                    :show-overflow-tooltip="true"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="phone"
                    label="办公电话"
                    align="center"
                    min-width="100"
                  >
                  </el-table-column> -->
                  <!-- <el-table-column prop="telephone" label="手机" align="center">
                  </el-table-column> -->
                  <el-table-column label="操作" align="center">
                    <template slot-scope="scope"> </template>
                    <template slot-scope="scope"> </template>

                    <template slot-scope="scope">
                      <span
                        style="
                          color: rgb(57, 119, 234);
                          margin-right: 10px;
                          cursor: pointer;
                        "
                        @click="addPop(scope.row)"
                        >编辑</span
                      >
                      <span
                        style="
                          color: rgb(57, 119, 234);
                          margin-right: 10px;
                          cursor: pointer;
                        "
                        v-if="scope.row.isDanger==0"
                        @click="addJianG(scope.row)"
                        >添加危化监管权限</span
                      >
                      <span
                        style="
                          color: rgb(57, 119, 234);
                          margin-right: 10px;
                          cursor: pointer;
                        "
                        v-if="scope.row.isDanger==1"
                        @click="dangerPrivilegeRemove(scope.row)"
                        >移除危化监管权限</span
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="pagination">
                <el-pagination
                  @current-change="handleCurrentChange"
                  :current-page.sync="currentPage"
                  :page-size="size"
                  v-show="total > 0"
                  layout="total, prev, pager, next"
                  background
                  :total="total"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
        <!-- 发送短信/电话会议 机构树新增和编辑弹框-->
        <!-- <el-dialog top='4vh' width="80% " :title="dialogConfig.tilteName " :visible.sync="dialogConfig.viewDialog "
            :close-on-click-modal="false" :close-on-press-escape="dialogConfig.escap " :show-close="dialogConfig.close "
            v-if="dialogConfig.viewDialog " @close='closeDialog' modal-append-to-body>
            <component :is="dialogConfig.templateName " :propdata="parentData " @dialogcallback="closeDialogCall ">
            </component>
            </el-dialog> -->

        <!-- 云会议-->
        <!-- <el-dialog top='4vh' width="80% " title="云会议 " :visible.sync="cloudConfig.viewDialog " :close-on-click-modal="false"
            :close-on-press-escape="false " :show-close="false " v-if="cloudConfig.viewDialog ">
            <cloudference-dialog :propdata="cloudConfig.data " :people="activePhone " @dialogcallback="closeDialogCall ">
            </cloudference-dialog>
            </el-dialog> -->

        <!-- 拨号盘 -->
        <!-- <el-dialog title="拨打电话 " width="388px " :visible.sync="phonePanelVisible " :before-close="closePhone "
            :close-on-click-modal="false" :close-on-press-escape="false " v-if="phonePanelVisible ">
            <phone-panel :callnumber="callnumber "></phone-panel>
            </el-dialog>
            <add-user ref="addUserRef " @ok="addUserToOrgCall "></add-user>
            <sms-modal ref="smsModalRef " :allowchange="false " :disabledperson="true "></sms-modal> -->
       

        <el-dialog
          title="新增 "
          :visible.sync="popoverAdd.visible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          v-if="popoverAdd.visible"
        >
          <div>
            <el-form
              :model="popoverAdd"
              :rules="popoverAdd.groupIdRules"
              label-width="120px"
            >
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="处室名称：" prop="orgName">
                    <el-input
                      v-model.trim="popoverAdd.form.orgName"
                      placeholder=""
                      disabled
                       maxlength="20"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="值班电话：" prop="dutyTel">
                    <el-input
                      v-model.trim="popoverAdd.form.dutyTel"
                      placeholder=""
                       maxlength="20"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="邮编：">
                    <el-input
                      v-model.trim="popoverAdd.form.postCode"
                      placeholder=""
                       maxlength="20"
                     
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="区域代码：" prop="districtCode">
                    <el-input
                      v-model.trim="popoverAdd.form.districtCode"
                      placeholder=""
                       maxlength="20"
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="排序：" prop="dispOrder">
                    <el-input
                      v-model.trim="popoverAdd.form.dispOrder"
                      placeholder=""
                       maxlength="8"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="传真：">
                    <el-input
                      v-model.trim="popoverAdd.form.fax"
                      placeholder=""
                       maxlength="20"
                     
                    />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="机构简称：" prop="orgShortName">
                    <el-input
                      v-model.trim="popoverAdd.form.orgShortName"
                      placeholder=""
                    />
                  </el-form-item>
                </el-col>
              </el-row> -->

              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="职能：" prop="orgDuty">
                    <el-input
                      v-model.trim="popoverAdd.form.orgDuty"
                      placeholder=""
                      maxlength="80"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <div slot="footer" style="text-align: center">
            <el-button
              size="mini "
              icon="el-icon-close "
              @click="popoverAdd.visible = false"
              >关闭</el-button
            >
            <el-button
              type="primary "
              size="mini "
              icon="el-icon-check "
              @click="addPersonToOrgToGroup"
              >确定</el-button
            >
          </div>
        </el-dialog>
      </div>
    </div>
    <!-- <DataMaintenance @refreshPreInfo='refreshPreInfoFn' ref="dataMaintenance"></DataMaintenance> -->
  </div>
</template>
<script>
// import DataMaintenance from './dataMaintenance'
import {
  getMailListTreeData,
  getMailListTreeDataForPerson,
  getGroupListData,
  getSelPersonByOrgGroupData,
  addOrgGroup,
  deleteOrgGroup,
  addToAddressList,
  movePersonsFromGroup,
  getByParentCode,
  dangerPrivilegeAdd,
  dangerPrivilegeRemove,
  dangerPrivilegeModify,
  userOrgPage
} from "@/api/mailList";
export default {
  name: "mailList",
  components: {
    // DataMaintenance
  },
  data() {
    return {
      handleNodeClickObj:{},
      curClick:{},
      defaultShowNodes: [],
      orgGroupInfo: {
        addVisible: false,
        addGroupName: "",
        groupName: "",
        sort: 0,
        editVisible: false,
        addGroupNameRules: {
          addGroupName: [
            { required: true, message: "请输入通讯组名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "通讯组名称最大长度为20",
              trigger: "blur",
            },
          ],
        },
        groupNameRules: {
          groupName: [
            { required: true, message: "请输入通讯组名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "通讯组名称最大长度为20",
              trigger: "blur",
            },
          ],
        },
      },
      defaultprops: {
        children: "children",
        label: "orgName",
      },
      searchData: {
       
        orgCode: "",
        orgName:"",
        nowPage: 1,
        pageSize: 10,
      },
      treeData: [],
      dialogConfig: {
        viewDialog: false, //弹框是否显示
        templateName: "", //弹框组件名
        tilteName: "", //标题头
        model: true,
        escap: true,
        close: true,
      },
      treeType: null,
      filterText: "",
      defaultProps: {
        children: "children",
        label: "label",
      },
      total: 0,
      orgType: "",
      currentPage: 1,
      size: 10,
      personData: [],
      loadingTable: false,
      loadingTree: true,
      selOrgGroupInfo: {
        groupId: "",
        groupName: "",
        sort: 0, // 排序
      },
      activePhone: [],
      popoverAdd: {
        visible: false,
        form: {
        },
        groupIdRules: {
          noOfPtcpt1: [
            {
              required: true,
              message: "请选择通讯录组",
              trigger: "blur",
            },
          ],
        },
      },
      popoverOrgGroup: {
        visible: false,
        groupId: "",
        groupIdRules: {
          groupId: [
            {
              required: true,
              message: "请选择通讯录组",
              trigger: "blur",
            },
          ],
        },
      },
      addressList: [], // 通讯录
    };
  },

  mounted() {
    this.getOrgGroupList();
    // this.orgType = JSON.parse(sessionStorage.getItem("role")).orgType;
    this.orgType = 1;
    this.treeType = 1;
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    treeData: {
      handler() {
        // 我这里默认展开一级, 指定几级就往里遍历几层取到 id 就可以了
        this.treeData.forEach((item) => {
          // debugger
          if (item.children) {
            if (item.children[0].children) {
              this.defaultShowNodes.push(item.children[0].children[0].id);
            }
          }
          //  this.defaultShowNodes.push(item.id)
        });
      },
      deep: true,
    },
  },

  methods: {
    //编辑
    addPop(row) {
      
      // this.form=row
      this.popoverAdd.form = Object.assign({},row)//获取到row后
      console.log(this.form)
      this.popoverAdd.visible = true;
    },
    //机构树
    getOrgGroupList() {
      
      this.loadingTree = true;
      getMailListTreeData({
        type: 1,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTree = false;
          // this.treeData = res.data.data[0].children;
           this.treeData = res.data.data;
           this.personInfo(res.data.data[0].orgName,res.data.data[0]);
        }
      });
    },
  
    handleNodeClick(item) {
       if(item.virtualNode==true){
        return
      }
      
      this.curClick=item
      this.selectData = item;
      this.currentPage = 1;
      this.searchData.orgName = "";
      if (this.treeType == 3) {
        this.getSelPersonByOrgGroup(item.groupId);
        this.selOrgGroupInfo = item;
      } else {
        this.handleNodeClickObj=item
        this.personInfo(item.orgName, item);
      }
    },
    personInfo(orgName, item) {
     
      this.curClick=item
      // this.searchData.orgName = orgName;
      this.loadingTable = true;
      userOrgPage({
        parentCode:item.orgCode,
        orgCode: "",
        nowPage: this.currentPage,
        pageSize: this.size,
        "orgName": this.searchData.orgName
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTable = false;
          this.personData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleSelectionChange(val) {
      this.activePhone = val;
    },
    // openaddPersonToOrgToGroup() {
    //   console.log("++++++++选择人员", this.activePhone);
    //   if (!this.activePhone.length) {
    //     this.$message.warning("请选择人员");
    //     return;
    //   }
    //   this.popoverOrgGroup.visible = true;
    //   this.getAddressList();
    // },
    //编辑
    addPersonToOrgToGroup() {
       dangerPrivilegeModify(this.popoverAdd.form).then((res) => {
        if (res.data.status == 200) {
          this.$message.success(res.data.msg || "编辑成功~");
          this.popoverAdd.visible = false;
          this.personInfo(this.handleNodeClickObj.orgName, this.handleNodeClickObj)
        }else{
          this.$message.error(res.data.msg || "编辑失败~");
        }
      });
    },
   
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.personInfo(this.searchData.orgName,this.handleNodeClickObj);
      // this.search();
    },
    refreshPreInfoFn() {
      this.personInfo(this.searchData.orgName,this.handleNodeClickObj);
    },
   
    // 获取通讯录组数据
    getOrgGroupListes(isFirst) {
      this.loadingTree = true;
      getGroupListData().then((res) => {
        if (res.data.status == 200) {
          this.loadingTree = false;
          res.data.data = res.data.data.map((it) => {
            return {
              ...it,
              orgName: it.groupName,
              groupId: it.groupId,
              groupName: it.groupName,
              orgCode: it.orgCode, //
              label: it.groupName,
            };
          });
          
          if (this.treeType == 3) {
            this.treeData = res.data.data;
            // this.defaultchecked = this.selOrgGroupInfo.groupId;
          }
          if (res.data.data[0]) {
            this.getSelPersonByOrgGroup(res.data.data[0].groupId);
            this.selOrgGroupInfo = res.data.data[0];
          }
          // this.popoverOrgGroup.groupList = res.data.data;
        } else {
          // this.popoverOrgGroup.groupList = [];
          // this.$message.error(res.msg);
        }
      });
    },
    getSelPersonByOrgGroup(groupId) {
      this.searchData.groupId = groupId;
      this.loadingTable = true;
      getSelPersonByOrgGroupData({
        key: this.searchData.key,
        groupId: groupId,
        nowPage: this.currentPage,
        pageSize: this.size,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTable = false;
          this.personData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    searchByName() {
      this.currentPage = 1;
      this.personInfo(this.searchData.orgName,this.handleNodeClickObj);
    },
  
 
    
  
    // 从通讯录组中删除人员
    addJianG(data) {
      
      this.$confirm("确认加入危化监管？", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmButtonClass",
        cancelButtonClass: "confirmButtonClass",
      }).then(() => {
        let params = {
          orgCode:data.orgCode
         };
        dangerPrivilegeAdd(params).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg || "加入成功");
            this.personInfo(this.handleNodeClickObj.orgName, this.handleNodeClickObj)
          } else {
            this.$message({
              type: "error",
              message: res.data.msg ? res.data.msg : "加入失败",
            });
          }
        });
      });
    },
    // 去掉危化监管
    dangerPrivilegeRemove(data) {
      this.$confirm("确认去掉危化监管？", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmButtonClass",
        cancelButtonClass: "confirmButtonClass",
      }).then(() => {
        let params = {
          orgCode:data.orgCode
        };
        dangerPrivilegeRemove(params).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg || "去掉成功");
            this.personInfo(this.handleNodeClickObj.orgName, this.handleNodeClickObj)
          } else {
            this.$message({
              type: "error",
              message: res.data.msg ? res.data.msg : "去掉失败",
            });
          }
        });
      });
    },

    // 添加人员到通讯录机构树中回调
    addUserToOrgCall(callData) {
      this.getListData();
    },
  },
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    border-right: 1px solid rgb(231, 231, 231);
    border-bottom: 1px solid rgb(231, 231, 231);
    .bottom {
      // border-bottom: 1px solid rgb(231, 231, 231);
       
    }
    li {
      list-style-type: none;
      width: 50%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      // border-right: 1px solid rgb(231, 231, 231);
      border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 48%;
        padding: 1%;
        word-break: break-all;
      }
    }
    li >div:first-child{
      // border-right:1px solid rgb(231, 231, 231);
    }
    li >div{
      // border:1px solid rgb(231, 231, 231);
    }
    li:nth-of-type(3n + 0) {
      list-style-type: none;
      width: 50%;
      display: flex;
      align-items: center;
      // border-top: 1px solid rgb(231, 231, 231);
      // border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .lang {
      list-style-type: none;
      width: 66.6%;
      display: flex;
      border-top: 1px solid #eaedf2;
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 24.9%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        background-color: rgb(242, 242, 242);
      }
      .r {
        width: 73.3%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        padding: 0px 10px;
        word-break: break-all;
      }
    }
    .liLine {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      // border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 50%;
        word-break: break-all;
      }
    }
  }
}
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  /deep/.el-input-group__append {
    cursor: pointer !important;
  }
}
.duty-list {
  display: flex;
  justify-content: start;
  width: 100%;
  // height: calc(100% - 100px);
  height: 84vh;
  padding-top: 0.1rem;
  // border:1px solid red
}

.duty-list-left {
  width: 30%;
  height: 100%;
}

.duty-list-right {
  width: 70%;
  height: 100%;
  /* background: rgba(233, 233, 233, 1) */
}
.tab {
  padding-left: 28px;
  padding-right: 20px;
  padding-top: 0.5rem;
  button {
    span {
      font-size: 16px !important;
    }
  }
}
.list_contain {
  display: flex;
  // justify-content: space-between;
  //   height: 72vh;
  // height: 100%;
  width: 100%;
  flex-direction: column;
}
.list_top {
  height: calc(100% - 198px);
  display: flex;
}
.list_left {
  padding-top: 0.5rem;
  width: 100%;
}
.list_bottom_box {
  height: 198px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // justify-content: space-between;
  .bottom_list {
    height: calc(100% - 50px);
    border: 1px solid #dfdfe3;

    .bottom_concant_list {
      width: 100%;
      height: calc(100% - 2rem);

      .bottom_flex_group {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding-left: 1rem;

        > div {
          width: 25%;
          height: 80px;
          border-radius: 4px;
          margin-top: 0.875rem;
          padding-right: 0.75rem;
          position: relative;
          overflow: hidden;
          .concat_inner {
            width: 100%;
            height: 100%;
            padding: 14px;
            display: flex;
            justify-content: space-between;
            border: 1px solid #dfdfe3;
            background: #fafafa;
            > img {
              // width:66px;
              display: inline-block;
            }

            > .cancat_info {
              width: calc(100% - 52px);
              box-sizing: border-box;
              padding-left: 14px;
              font-size: 16px;
              display: flex;
              flex-direction: column;
              justify-content: space-around;

              .person_title {
                color: #49445f;
                width: 100%;
                line-height: 1rem;
                margin-top: 0.5rem;

                > p {
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  // display: -webkit-box;
                  // -webkit-line-clamp: 2;
                  // -webkit-box-orient: vertical;
                }
              }

              .person_info {
                margin-top: 4px;
                margin-bottom: 6px;
                color: #9999b0;
                font-size: 12px;
                word-break: keep-all;
              }
            }
          }

          i {
            position: absolute;
            right: 0.75rem;
            top: 5px;
            cursor: pointer;

            &::before {
              color: #4a7dff !important;
            }
          }
        }
      }
    }

    .bottom_title {
      height: 2rem;
      line-height: 2rem;
      background: #f3f3f3;
      padding: 0 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .bottom_title_add_button {
      cursor: pointer;
    }

    .bottom_contain {
      height: calc(100% - 2rem);
    }
  }

  .bottom_btn_group {
    height: 38px;
    display: flex;
    justify-content: space-between;
    background: #f3f3f5;
    border: 1px solid #dfdfe3;
    border-radius: 4px;
    align-items: center;

    > div {
      flex: 1;
      text-align: center;
      cursor: pointer;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      &:not(:last-child) {
        border-right: 1px solid #dfdfe3;
      }

      &:hover {
        color: #66b1ff;
      }
    }
  }
}
.person_info_span {
  width: 85px;
  //   margin-bottom: 4px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.maillist-title {
  display: inline-block;
  line-height: 40px;
  font-size: 22px;
}
.right_filter {
  float: right;
}

.el-scrollbar {
  height: 100% !important;
}
.search_tree {
  position: relative;
  height: 91.5%;
  box-sizing: border-box;
}

.tabs_list {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 122;
  padding: 0.625rem 4px;
  padding-top: 0;
  margin-top: 0.5rem;
  position: absolute;
  height: 16.5rem;
  width: 100%;

  .filter_list {
    li {
      cursor: pointer;
      color: #000;
      font-size: 16px;
      padding: 0.625rem 1rem;
      line-height: 1rem;
      &:hover {
        background: #66b1ff;
        color: #fff;
      }

      &.active {
        background: #66b1ff;
        color: #fff;
      }
    }
  }
  .filter_list_content {
    display: inline-block;
    // width: 100%;
    line-height: 18px;
    // font-size: 1rem;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
}

.search_slide_searchKey {
  position: relative;
  height: 3rem;
  line-height: 3rem;
  background: #f5f5f7;
  border: 1px solid #e3e3e5;
  padding: 0 0.5rem;
  border-bottom: none;
}

.search_slide_input {
  // padding: 0 0.625rem;
  height: 100%;
  padding-top: 0.5rem;
  padding-left: 28px;
  padding-right: 20px;
}

.search_slide {
  position: relative;
  transition: all 0.5s;
  // top: 0.5rem;
  width: 100%;
  // height: calc(100% - 2.7rem);
  // height: 93.5%;
  // height: calc(100% - 80px);
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e3e3e5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 11;
  padding-left: 0.2rem;
  padding-bottom: 0.2rem;
  border-top: none;
  .controllab {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
  }
}

.popper__arrow {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
  -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: 2.5rem;
  left: 50%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #ebeef5;
  z-index: 100;

  &:after {
    content: "";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
  }
}

.headertitle {
  background: #f5f5f6;
  padding-left: 30px;
  line-height: 2.1rem;
  font-size: 1rem;
  cursor: pointer;
}
.navtitle {
  background: #fff;
  // border-bottom: 1px solid rgb(228, 231, 237)
}

.tree_icon {
  color: #c0c4cc;
  font-size: 18px;
  padding: 6px;
}
.headerdrops {
  margin-left: 1.6rem;
  margin-bottom: 3px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #c0c4cc;
}
.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #cee5ff;
  color: #4a7dff;
}
</style>
