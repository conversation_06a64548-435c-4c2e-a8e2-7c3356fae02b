<template>
  <div id="leakagePoint"
       class="leakagePoint">
    <div class="header">
      <div class="operation">
        <h2 style="margin-bottom: 0">泄漏点列表</h2>
        <div class="inputBox">
          <el-select v-model="searchParam.hazardId" style="margin-right: 10px"
            size="small"
            clearable
            placeholder="全部重大危险源">
            <el-option v-for="item in dangerIdIsNotNullListDataList"
              :key="item.dangerid"
              :label="item.dangername"
              :value="item.dangerid" />
            </el-option>
          </el-select>
          <el-input v-model.trim="searchParam.keyword"
            placeholder="请输入泄漏点名称"
            class="input"
            size="small"
            clearable
            style="margin-right: 10px"></el-input>
          <el-button size="small"
            type="primary"
            @click="search()">查询</el-button>
        </div>
        <div class="btnBox">
          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            type="primary"-->
          <!--            @click="addEdit"-->
          <!--            v-if="$store.state.login.user.user_type === 'ent'"-->
          <!--            >新增</el-button-->
          <!--          >-->
          <!-- <el-button size="mini" v-if="$store.state.login.user.user_type != 'ent'" style="margin-right: 10px" type="primary"
          >批量删除</el-button
        > -->
          <CA-button class="export"
            plain
            size="small"
            type="primary"
            @click="exportExcel">导出
          </CA-button>
        </div>
      </div>
    </div>
    <div class="table"
         v-loading="loading">
      <el-table ref="multipleTable"
                :data="tableData"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
                height="530px">
        <!-- <el-table-column
          align="center"
          fixed="left"
          type="selection"
        >
        </el-table-column> -->
        <el-table-column align="center"
                         width="55"
                         label="序号"
                         type="index">
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align="center"
                         label="所属重大危险源"
                         prop="hazardIdName">
        </el-table-column>
        <el-table-column prop="leakageName"
                         label="泄漏点名称"
                         align="center"
                         show-overflow-tooltip
                         width="120px">
        </el-table-column>
        <el-table-column prop="deviceTypeName"
                         label="传感器类型">
        </el-table-column>
        <el-table-column prop="monitorMedium"
                         label="监测介质"
                         align="center">
        </el-table-column>
        <el-table-column align="center"
                         label="最近检修日期">
          <template slot-scope="scope">
            <span style="color: rgb(57, 119, 234); cursor: pointer"
                  @click="enterDetail(scope.row)">{{ scope.row.lastMaintenanceTime ? new Date(scope.row.lastMaintenanceTime).Format("yyyy-MM-dd") : "" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center"
                         label="操作"
                         min-width="120">
          <template slot-scope="scope">
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="view(scope.row)">详情</span>
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="edit(scope.row)"
                  v-if="$store.state.login.user.user_type === 'ent'">编辑</span>
            <!--            <span-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              @click="deleter(scope.row.id)"-->
            <!--              v-if="$store.state.login.user.user_type === 'ent'"-->
            <!--              >删除</span-->
            <!--            >-->
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="enterInput(scope.row)"
                  v-if="$store.state.login.user.user_type === 'ent'">录入</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination v-if="total != 0"
                     :current-page.sync="currentPage"
                     :total="total"
                     background
                    :page-size='size'
                     layout="total, prev, pager, next"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增编辑查看 -->
    <div  v-if='open'>
    <el-dialog :title="title"
      :visible.sync='open'
      :before-close="beforeClose"
      width="1100px"
      :close-on-click-modal="false"
      :append-to-body="true">
      <el-form ref="form"
        :model="form"
        :rules="rules"
        label-width="150px">
        <div class="form_item">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="所属重大危险源">
                  <!-- <el-input
                    v-model="form.teamName"
                    placeholder="所属重大危险源"
                  /> -->
                  <el-select v-model="form.hazardId"
                    placeholder="请选择所属重大危险源"
                    disabled>
                    <el-option v-for="item in dangerIdIsNotNullListDataList"
                               :key="item.dangerid"
                               :label="item.dangername"
                               :value="item.dangerid" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="泄漏点编码">
                  <el-input v-model.trim="form.leakageCode"
                    disabled
                    placeholder="泄漏点编码" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="泄漏点名称">
                  <el-input v-model.trim="form.leakageName"
                    disabled
                    placeholder="泄漏点名称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="设备状态">
                  <el-select v-model="form.deviceStatus"
                    placeholder="请选择设备状态"
                    disabled>
                    <el-option label="在线" value="1"></el-option>
                    <el-option label="报警" value="2"></el-option>
                    <el-option label="离线" value="3"></el-option>
                    <el-option label="停用" value="4"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="传感器类型">
                  <el-select v-model="form.deviceType"
                    placeholder="请选择传感器类型"
                    disabled>
                    <!-- <el-option
                        v-for="item in tankTypeData"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id">
                    </el-option> -->
                    <el-option label="可燃气体" value="1"></el-option>
                    <el-option label="有毒气体" value="2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="监测介质">
                  <el-autocomplete popper-class="my-autocomplete"
                    v-model="form.monitorMediumName"
                    :fetch-suggestions="querySearch"
                    placeholder="请选择/输入监测介质"
                    clearable
                    @clear="clearSensororgCode()"
                    @select="handleSelect"
                    style="width: 150px"
                    disabled>
                    <template slot-scope="{ item }">
                      <div class="name">{{ item.msdstitle }}</div>
                    </template>
                  </el-autocomplete>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="介质形态">
                  <el-select v-model="form.mediumType"
                    placeholder="请选择介质形态"
                    disabled>
                    <el-option v-for="item in dangerIdIsNotNullListData"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="地址">
                  <el-input v-model.trim="form.address"
                  maxlength="45"
                    :disabled="dialogType === 'add'"
                    placeholder="请选择地址" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="经度">
                  <el-input v-model.trim="form.longitude"
                    disabled
                    maxlength="10"
                    placeholder="请输入经度" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="纬度">
                  <el-input v-model.trim="form.latitude"
                  maxlength="10"
                    disabled
                    placeholder="请输入纬度" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="高程">
                  <el-input v-model.trim="form.altitude"
                  maxlength="5"
                    :disabled="dialogType === 'add'"
                    placeholder="请输入高程"> 
                   <template slot="append">米</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="map_height">
                  <el-form-item label="地图定位">
                    <egisMap :islistener="false"
                      :isdetail="disabled"
                      ref="detailMap"
                      :datas="form"
                      style="height: 200px"
                      @mapCallback="mapcallback"></egisMap>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="dialogType === 'edit'"
           slot="footer"
           class="dialog-footer">
        <el-button type="primary"
          @click="submitForm"
          :loading="btnLoading">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    </div>

    <!-- 录入 -->
    <el-dialog :append-to-body="true"
      :close-on-click-modal="false"
      :visible.sync="opens"
      title="录入检维修记录"
      width="900px">
      <el-form ref="formData"
        :disabled="disabled"
        :hide-required-asterisk="disabled"
        :model="formData"
        label-width="150px">
        <div class="form_item">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="dangerId"
                  label="所属重大危险源">
                  <el-select v-model="formData.hazardId"
                    placeholder="请选择所属重大危险源"
                    @change="changeDanger"
                    :disabled="true">
                    <el-option v-for="item in dangerIdIsNotNullListDataList"
                      :key="item.dangerid"
                      :label="item.dangername"
                      :value="item.dangerid" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="危险源编码">
                  <el-input v-model.trim="formData.hazardId"
                    :disabled="true"
                    placeholder="请输入危险源编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="泄漏点名称"
                  prop="tankName">
                  <el-input :disabled="true"
                    placeholder="请选择泄漏点名称"
                    v-model.trim="formData.leakageName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="泄漏点编码">
                  <el-input :disabled="true"
                            v-model.trim="formData.leakageCode"
                            placeholder="请输入泄漏点编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上次检维修日期">
                  <el-input :disabled="true"
                            v-model.trim="formData.lastMaintenanceTime"
                            placeholder="请输入泄漏点编码" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检维修日期"
                              prop="maintenanceTime"
                              :rules="{
                    required: true,
                    message: '检维修日期不能为空',
                    trigger: 'blur',
                  }">
                  <el-date-picker v-model="formData.maintenanceTime"
                                  type="date"
                                  placeholder="请输入检维修日期"
                                  value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作负责人"
                              prop="operator"
                              :rules="{
                    required: true,
                    message: '操作负责人不能为空',
                    trigger: 'blur',
                  }">
                  <el-input maxlength="30" v-model.trim="formData.operator"
                            placeholder="请输入操作负责人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式"
                              prop="contact"
                              :rules="[{
                    required: true,
                    message: '联系方式不能为空',
                    trigger: 'blur',
                  },
                   {
                    pattern: /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/,
                    message: '请输入有效的电话号码'
                   }]">
                  <el-input v-model.trim="formData.contact"
                            placeholder="请输入联系方式" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="更换备品清单"
                              prop="replacementContent"
                              :rules="{
                    required: true,
                    message: '更换备品清单不能为空',
                    trigger: 'blur',
                  }">
                  <el-input v-model.trim="formData.replacementContent"
                            :rows="4"
                            maxlength="500"
                            placeholder="请输入更换备品清单"
                            show-word-limit
                            type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="检维修内容"
                              prop="maintenanceContent"
                              :rules="{
                    required: true,
                    message: '检维修内容不能为空',
                    trigger: 'blur',
                  }">
                  <el-input v-model.trim="formData.maintenanceContent"
                            :rows="4"
                            maxlength="500"
                            placeholder="请输入检维修内容"
                            show-word-limit
                            type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submit" :loading="btnLoading">保 存</el-button>
        <el-button @click="cancels">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 录入 -->
    <el-dialog :append-to-body="true"
               :close-on-click-modal="false"
               :visible.sync="openes"
               title="检维修记录"
               width="900px"
               top="10vh">
      <el-form ref="form"
               :disabled="disabled"
               :hide-required-asterisk="disabled"
               :model="formList"
               :rules="rules"
               label-width="150px">
        <div class="form_item">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属重大危险源">
                  <el-input v-model.trim="formList.hazardIdName"
                            :disabled="true"
                            placeholder="请输入所属重大危险源" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="危险源编码">
                  <el-input v-model.trim="formList.hazardId"
                            :disabled="true"
                            placeholder="请输入危险源编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="泄漏点名称">
                    <el-input v-model.trim="formList.leakageName" :disabled="true" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="泄漏点编码">
                  <el-input v-model.trim="formList.leakageCode"
                    :disabled="true"
                    placeholder="请输入泄漏点编码" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="form_item">
          <h2 class="form_title">检维修记录</h2>
          
          <div class="form_main"
               v-loading="loading">
            <el-collapse v-model="activeRepairName"
              accordion
              v-if="formDataRepair.list && formDataRepair.list.length > 0">
              <el-collapse-item :name="item.maintenanceId"
                v-for="(item, index) in formDataRepair.list"
                :key="index">
                <template slot="title">
                  <h3 class="time">
                    {{ item.maintenanceTime ? new Date(item.maintenanceTime).Format("yyyy-MM-dd") : "" }}
                  </h3>
                </template>
                <div class="box">
                  <div>
                    <span class="title">操作负责人：</span>
                    <span>{{ item.operator }}</span>
                  </div>
                  <div>
                    <span class="title">联系方式：</span>
                    <span>{{ item.contact }}</span>
                  </div>
                  <div>
                    <span class="title">更换备品清单：</span>
                    <span>{{ item.replacementContent }}</span>
                  </div>
                  <div>
                    <span class="title">检维修内容：</span>
                    <span>{{ item.maintenanceContent }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-empty v-else
              :image-size="160"
              description="暂无数据"></el-empty>
          </div>
        </div>
      </el-form>
      <!-- <div v-if="!disabled"
           slot="footer"
           class="dialog-footer">
        <el-button @click="cancelRecord">取 消</el-button>
      </div> -->
    </el-dialog>
  </div>
</template>
<script>
import {
  getLeakagePointListData,
  addLeakagePointListData,
  editLeakagePointListData,
  datailLeakagePointListData,
  deleteLeakagePointListData,
  getLeakagePointListAllData,
  getDangerIdIsNotNullListDatas,
  maintenanceSave,
  getmaintenanceSave,
  getDangerIdIsNotNullListData,
  getLeakageDeviceTypeData,
  getDangerIdIsNotNullTypeData,
  exportLeakagePointListData
} from '@/api/equipmentAndFacilities'
import { getmaintenanceList } from '../../../../api/equipmentAndFacilities'

const mapConfig = require('@/assets/json/map.json')
export default {
  //import引入的组件
  name: 'leakagePoint',
  components: {},
  data() {
    var validatelongitude = (rule, value, callback) => {
      // 校验经度
      const reg = /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,9})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,9}|180)$/
      if (value !== '' && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error('经度整数部分为0-180,小数部分为0到9位!'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    var validatelatitude = (rule, value, callback) => {
      // 校验纬度
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,9}|90\.0{0,9}|[0-8]?\d{1}|90)$/
      if (value !== '' && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error('纬度整数部分为0-90,小数部分为0到9位!'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      formDataRepair: {},
      formList: {},
      activeRepairName: '',
      nameOfProductionUnit: '',
      loading: false,
      typeOfProductionUnit: '',
      productionUnitCode: '',
      btnLoading: false,
      open: false,
      opens: false,
      openes: false,
      isDanger: true,
      dangerIdIsNotNullListDataList: [],
      title: '编辑泄漏点信息',
      dialogType: '',
      tankTypeData: [],
      pressuretypeData: [],
      temperaturTypeData: [],
      dangerIdIsNotNullListData: [],
      allShebeiNameData: [],
      currentPage: 1,
      size: 11,
      selection: [],
      total: 0,
      tableData: [],
      form: {
        hazardId: '',
        leakageCode: '',
        leakageName: '',
        deviceStatus: '',
        deviceType: '',
        monitorMedium: '',
        orgCode: '',
        orgName: '',
        mediumType: '',
        address: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: '0'
      },
      formData: {
        contact: '',
        leakageCode: '',
        dangerId: '',
        upmaintenanceTime: '',
        maintenanceContent: '',
        maintenanceId: '',
        maintenanceTime: '',
        operator: '',
        replacementContent: ''
      },
      rules: {
        longitude: [
          {
            required: true,
            validator: validatelongitude,
            trigger: ['change', 'blur']
          }
        ],
        latitude: [
          {
            required: true,
            validator: validatelatitude,
            trigger: ['change', 'blur']
          }
        ],
        altitude: [
          {
            required: true,
            pattern: /^\d{1,6}(\.\d{1,6})?$/,
            message: '请输入正确的高程',
            trigger: ['change', 'blur']
          }
        ]
      },
      enterpriseId: '',
      disabled: false,
      searchParam: { // 搜索
        hazardId: '', // 所属危险源
        keyword: '', // 关键字
      }
    }
  },
  //方法集合
  methods: {
    beforeClose(){
      this.open = false;
    },
    getSelectData(enterpriseId) {
      this.enterpriseId = enterpriseId
      getSelectData({
        enterpId: this.enterpriseId
      }).then(res => {
        if (res.data.code == 0) {
          this.dangerList = res.data.data
        }
      })
    },
    handleMaintenance(row) {
      this.getmaintenanceListFun(this.formList, row.currentPage)
    },
    getmaintenanceListFun(row, nowPage) {
      getmaintenanceList({
        facilityId: row.leakageId,
        maintenanceId: row.leakageId,
        nowPage: nowPage || 1,
        pageSize: 10
      }).then(res => {
        this.$nextTick(() => {
          if (res.data.status === 200) {
            this.formDataRepair = res.data.data
          }
        })
      })
    },
    enterDetail(row) {
      this.openes = true
      this.formList = row
      // this.formList = {
      //   hazardIdName: row.sysGreatHazardName,
      //   hazardId: row.sysGreatHazard,
      //   tankName: row.typeOfProductionUnitName,
      //   tankNum: row.typeOfProductionUnit
      // }
      this.getmaintenanceListFun(row)
    },
    changeDanger(event, item) {
      if (event) {
        this.isDanger = false
        this.formData.dangerId = event
        this.getAllName(event)
      } else {
        this.isDanger = true
      }
    },
    getAllName(danderId) {
      getLeakagePointListAllData({
        hazardId: danderId
      }).then(res => {
        if (res.data.status == 200) {
          this.allShebeiNameData = res.data.data;
        }
      })
    },
    getData(id) {
      this.loading = true
      this.enterpriseId = id
      var orgCode = ''
      if (this.$store.state.login.user.user_type == 'ent') {
        orgCode = this.$store.state.login.user.org_code
      }
      getLeakagePointListData({
        nowPage: this.currentPage,
        // orgCode: orgCode,
        orgCode: id,
        pageSize: this.size,
        hazardId: this.searchParam.hazardId, // 所属危险源
        leakageName: this.searchParam.keyword, // 关键字
      }).then(res => {
        this.loading = false
        if (res.data.status == 200) {
          this.total = res.data.data.total
          this.tableData = res.data.data.list
        }
      })
    },
    //重大危险源
    getDangerIdIsNotNullListDataList(id) {
      getDangerIdIsNotNullListDatas({ enterpid: id }).then(res => {
        if (res.data.status == 200) {
          this.dangerIdIsNotNullListDataList = res.data.data
        }
      })
    },
    getDangerIdIsNotNullListDataType() {
      // getLeakageDeviceTypeData({}).then((res) => {
      //     if (res.data.status == 200) {
      //         this.tankTypeData = res.data.data;
      //     }
      // });
      // getDangerIdIsNotNullTypeData({
      //     dicCode: 'PRESSURE_TYPE',
      // }).then((res) => {
      //     if (res.data.status == 200) {
      //         this.pressuretypeData = res.data.data;
      //     }
      // });
      // getDangerIdIsNotNullTypeData({
      //     dicCode: 'temperaturType',
      // }).then((res) => {
      //     if (res.data.status == 200) {
      //         this.temperaturTypeData = res.data.data;
      //     }
      // });
      getDangerIdIsNotNullTypeData({
        dicCode: 'CHEMICAL_STATUS'
      }).then(res => {
        if (res.data.status === 200) {
          this.dangerIdIsNotNullListData = res.data.data
        }
      })
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || '', cb)
    },
    getSeachData(keyWord, cb) {
      getDangerIdIsNotNullListData({ msdstitle: keyWord })
        .then(res => {
          if (res.data.status === 200) {
            if (res.data.data.list.length > 0) {
              cb(res.data.data.list)
            } else {
              cb([])
            }
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    //选择化学品
    handleSelect(value) {
      this.form.monitorMedium = value.msdsid
      this.form.monitorMediumName = value.msdstitle
      this.form.casCode = value.casno
      this.form.isImportant = value.supervisionflag
    },
    clearSensororgCode(item) {
      this.form.monitorMedium = ''
      this.form.monitorMediumName = ''
      this.form.casCode = ''
      this.form.isImportant = ''
    },
    // searchTime(value) {
    //   if (value) {
    //     let date1 = new Date(value[0]);
    //     let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
    //     let date2 = new Date(value[1]);
    //     let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
    //     this.starTime = dataTime1;
    //     this.endTime = dataTime2;
    //   } else {
    //     this.value1 = "";
    //     this.starTime = "";
    //     this.endTime = "";
    //   }
    // },
    search() {
      this.currentPage = 1
      this.getData(this.enterpriseId)
    },
    // clearSensortypeCode(e) {
    //   this.sensortypeCode = "";
    // },
    // clearState(e) {
    //   this.state = "";
    // },
    // clearDangerName(e) {
    //   this.dangerName = "";
    // },
    handleSelectionChange(val) {
      console.log(val)
    },
    handleClick() {
      console.log(123)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(this.enterpriseId)
    },
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId
      }
    },
    reset() {
      this.form = {
        hazardId: '',
        leakageCode: '',
        orgCode: '',
        orgName: '',
        leakageName: '',
        deviceStatus: '',
        deviceType: '',
        monitorMedium: '',
        mediumType: '',
        address: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: '0'
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
    resetes() {
      this.formData = {
        contact: '',
        leakageCode: '',
        maintenanceContent: '',
        maintenanceId: '',
        maintenanceTime: '',
        operator: '',
        replacementContent: '',
        dangerId: ''
      }
      this.isDanger = true
      if (this.$refs['formData']) {
        this.$refs['formData'].resetFields()
      }
    },
    addEdit() {
      this.reset()
      this.open = true
      this.disabled = false
    },
    view(row) {
      this.reset()
      this.dialogType = 'add';
      this.open = true
      this.disabled = true
      this.title = '查看泄漏点信息'
      //   this.form = row;
      datailLeakagePointListData({ leakageId: row.leakageId }).then(res => {
        if (res.data.status == 200) {
          this.form = res.data.data
          if (!this.form.longitude || !this.form.latitude) {
            this.form.longitude = mapConfig.map.defaultExtent.center[0];
            this.form.latitude = mapConfig.map.defaultExtent.center[1];
          }
        }
      })
    },
    edit(row) {
      this.reset()
      this.dialogType = 'edit';
      this.open = true
      this.disabled = false
      this.title = '编辑泄漏点信息'
      //   this.form = row;
      datailLeakagePointListData({ leakageId: row.leakageId }).then(res => {
        if (res.data.status == 200) {
          this.form = res.data.data
          if (!this.form.longitude || !this.form.latitude) {
            this.form.longitude = mapConfig.map.defaultExtent.center[0];
            this.form.latitude = mapConfig.map.defaultExtent.center[1];
          }
        }
      })
    },
    enterInput(row) {
      this.resetes()
      this.disabled = false
      this.formData = row
      // this.formData.lastMaintenanceTime = new Date(
      //   this.formData.lastMaintenanceTime
      // ).Format('yyyy-MM-dd')
      this.formData.maintenanceId = row.leakageId
      this.opens = true
    },
    deleter(row) {
      const id = row
      this.$confirm('是否确认删除该泄漏点信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteLeakagePointListData({
            ids: [id]
          }).then(res => {
            if (res.data.status == 200) {
              this.$message.success('删除成功')
              this.getData(this.enterpriseId)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    cancel() {
      this.open = false
      this.reset()
    },
    cancelRecord(){
       this.openes = false
      this.reset()
    },
    
    cancels() {
      this.opens = false
      this.resetes()
    },
    canceles() {
      this.openes = false
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          if (this.title === '编辑泄漏点信息') {
            editLeakagePointListData(this.form).then(response => {
              this.btnLoading = false
              if (response.data.status == 200) {
                this.open = false
                this.$message({
                  type: 'success',
                  message: response.data.msg || '修改成功'
                })
                this.reset()
                this.getData(this.enterpriseId)
              } else {
                this.$message.error(response.data.msg || '修改失败')
              }
              this.btnLoading = false
            })
          } else {
            this.form.orgCode = this.$store.state.login.user.org_code
            this.form.orgName = this.$store.state.login.user.org_name
            addLeakagePointListData(this.form).then(response => {
              if (response.data.status == 200) {
                this.open = false
                this.$message({
                  type: 'success',
                  message: response.data.msg || '新增成功'
                })
                this.reset()
                this.getData(this.enterpriseId)
              } else {
                this.$message.error(response.data.msg || '新增失败')
              }
              this.btnLoading = false
            })
          }
        } else {
          return false
        }
      })
    },
    submit() {
      console.log('++++++++++++this.formData', this.formData)
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          const {
            contact,
            id,
            maintenanceContent,
            maintenanceId,
            maintenanceTime,
            operator,
            replacementContent
          } = this.formData
          maintenanceSave({
            contact,
            facilityId: this.formData.leakageId,
            maintenanceContent,
            maintenanceId,
            maintenanceTime,
            operator,
            replacementContent
          }).then(response => {
            this.btnLoading = false
            if (response.data.status === 200) {
              this.$message({
                type: 'success',
                message: '录入成功'
              })
              this.opens = false
              this.resetes()
              this.getData(this.enterpriseId)
            } else {
              this.$message({
                type: 'success',
                message: '录入失败'
              })
            }
          })
        } else {
          return false
        }
      })
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6)
        this.form.latitude = data.location.lat.toFixed(6)
        this.form.altitude = data.location.altitude ? data.location.altitude.toFixed(6) : '0'
        this.form.address = data.formatted_address
      } else {
        this.$message({
          message: '详情页面不可选点！',
          type: 'warning'
        })
      }
    },
    exportExcel() {
      exportLeakagePointListData({
        nowPage: this.currentPage,
        // orgCode: this.$store.state.login.user.org_code,
        orgCode: this.enterpriseId,
        pageSize: this.size,
        hazardId: this.searchParam.hazardId, // 所属危险源
        leakageName: this.searchParam.keyword, // 关键字
      }).then(response => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        const blob = new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '泄漏点' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
  }
}
</script>
<style lang="scss" scoped>
.leakagePoint {
  background-color: #fff;
  padding-bottom: 50px;

  .header {
    .title {
      margin-left: 20px;
      font-weight: 600;
    }

    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      .inputBox {
        // width: 1020px;
        //width: 940px;
        //float: left;
        display: flex;
        align-items: center;
        .input {
          width: 160px;
        }
      }
      .btnBox {
        display: flex;
        & > * {
          margin-left: 10px;
        }
      }
    }

    .export {
      margin-right: 20px;
    }
  }

  .table {
    width: 100%;
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
/deep/.el-dialog__wrapper .tips {
  padding-left: 39px;
  color: #F56C6C;
  font-size: 12px;
  font-style: italic;
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
