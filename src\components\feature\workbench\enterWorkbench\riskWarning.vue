<template>
  <div class="main-top">
    <div class="main-left" v-if="safeStatus == '1'">
      <img src="/static/img/assets/img/zhongdafengxian.png" /><span
        >重大风险</span
      >
    </div>
    <div class="main-left" v-else-if="safeStatus == '2'">
      <img src="/static/img/assets/img/jiaodafengxian.png" /><span
        >较大风险</span
      >
    </div>
    <div class="main-left" v-else-if="safeStatus == '3'">
      <img src="/static/img/assets/img/yibanfengxian.png" /><span
        >一般风险</span
      >
    </div>
    <div class="main-left" v-else-if="safeStatus == '4'">
      <img src="/static/img/assets/img/difengxian.png" /><span>低风险</span>
    </div>
    <div
      class="main-right"
      v-if="recordsArr.length == 0"
      style="background: #fff; line-height: 40px; text-align: center"
    >
      暂无预警
      <span class="more" style="top: 0" @click="more">详细</span>
    </div>
    <div v-else>
      <div
        class="main-right"
        v-if="table.currentRankLevel == '黄色预警'"
        style="backgronund: #fff4db"
      >
        <p>
          当前预警:<span v-if="table.currentRankLevel == '黄色预警'"
            ><span class="yName">黄色预警</span
            ><img src="/static/img/assets/img/huang.gif" /></span
          ><span v-else-if="table.currentRankLevel == '橙色预警'"
            ><span class="cName">橙色预警</span
            ><img src="/static/img/assets/img/cheng.gif" /></span
          ><span v-if="table.currentRankLevel == '红色预警'"
            ><span class="hName">红色预警</span
            ><img src="/static/img/assets/img/hong.gif" /></span
          >最高预警等级:<span
            v-if="table.highestRankLevel == '红色预警'"
            style="color: #ff5656"
            >{{ table.highestRankLevel }}</span
          ><span
            v-else-if="table.highestRankLevel == '橙色预警'"
            style="color: #ff9037"
            >{{ table.highestRankLevel }}</span
          ><span
            v-else-if="table.highestRankLevel == '黄色预警'"
            style="color: #ffb821"
            >{{ table.highestRankLevel }}</span
          >预警触发时间:<i class="huang"></i
          ><span class="huangTime">{{
            table.yellowWarningTime ? table.yellowWarningTime : "--"
          }}</span
          ><i class="cheng"></i
          ><span class="chengTime">{{
            table.orangeWarningTime ? table.orangeWarningTime : "--"
          }}</span
          ><i class="hong"></i
          ><span class="hongTime">{{
            table.redWarningTime ? table.redWarningTime : "--"
          }}</span>
        </p>
        <span class="more" @click="more">详细</span>
      </div>
      <div
        class="main-right"
        v-else-if="table.currentRankLevel == '橙色预警'"
        style="backgronund: #ffe4c6"
      >
        <p>
          当前预警:<span v-if="table.currentRankLevel == '黄色预警'"
            ><span class="yName">黄色预警</span
            ><img src="/static/img/assets/img/huang.gif" /></span
          ><span v-else-if="table.currentRankLevel == '橙色预警'"
            ><span class="cName">橙色预警</span
            ><img src="/static/img/assets/img/cheng.gif" /></span
          ><span v-if="table.currentRankLevel == '红色预警'"
            ><span class="hName">红色预警</span
            ><img src="/static/img/assets/img/hong.gif" /></span
          >最高预警等级:<span
            v-if="table.highestRankLevel == '红色预警'"
            style="color: #ff5656"
            >{{ table.highestRankLevel }}</span
          ><span
            v-else-if="table.highestRankLevel == '橙色预警'"
            style="color: #ff9037"
            >{{ table.highestRankLevel }}</span
          ><span
            v-else-if="table.highestRankLevel == '黄色预警'"
            style="color: #ffb821"
            >{{ table.highestRankLevel }}</span
          >预警触发时间:<i class="huang"></i
          ><span class="huangTime">{{
            table.yellowWarningTime ? table.yellowWarningTime : "--"
          }}</span
          ><i class="cheng"></i
          ><span class="chengTime">{{
            table.orangeWarningTime ? table.orangeWarningTime : "--"
          }}</span
          ><i class="hong"></i
          ><span class="hongTime">{{
            table.redWarningTime ? table.redWarningTime : "--"
          }}</span>
        </p>
        <span class="more" @click="more">详细</span>
      </div>
      <div
        class="main-right"
        v-else-if="table.currentRankLevel == '红色预警'"
        style="backgronund: #ffe3e3"
      >
        <p>
          当前预警:<span v-if="table.currentRankLevel == '黄色预警'"
            ><span class="yName">黄色预警</span
            ><img src="/static/img/assets/img/huang.gif" /></span
          ><span v-else-if="table.currentRankLevel == '橙色预警'"
            ><span class="cName">橙色预警</span
            ><img src="/static/img/assets/img/cheng.gif" /></span
          ><span v-if="table.currentRankLevel == '红色预警'"
            ><span class="hName">红色预警</span
            ><img src="/static/img/assets/img/hong.gif" /></span
          >最高预警等级:<span
            v-if="table.highestRankLevel == '红色预警'"
            style="color: #ff5656"
            >{{ table.highestRankLevel }}</span
          ><span
            v-else-if="table.highestRankLevel == '橙色预警'"
            style="color: #ff9037"
            >{{ table.highestRankLevel }}</span
          ><span
            v-else-if="table.highestRankLevel == '黄色预警'"
            style="color: #ffb821"
            >{{ table.highestRankLevel }}</span
          >预警触发时间:<i class="huang"></i
          ><span class="huangTime">{{
            table.yellowWarningTime ? table.yellowWarningTime : "--"
          }}</span
          ><i class="cheng"></i
          ><span class="chengTime">{{
            table.orangeWarningTime ? table.orangeWarningTime : "--"
          }}</span
          ><i class="hong"></i
          ><span class="hongTime">{{
            table.redWarningTime ? table.redWarningTime : "--"
          }}</span>
        </p>
        <span class="more" @click="more">详细</span>
      </div>
    </div>
  </div>
</template>
<script>
import { getInformationInfo } from "@/api/entList";
import { getCimEarlyWarningPushList } from "@/api/workbench";
import { postCimEarlyWarningPushList } from "@/api/riskAssessment";
import { cimEarlyWarningCount } from "@/api/riskAssessment";
import { parseTime } from "@/utils/index";
export default {
  components: {},
  data() {
    return {
      enterpId: "",
      safeStatus: "4",
      table: {},
      recordsArr: [],
      distCode: this.$store.state.login.userDistCode,
    };
  },
  methods: {
    getData(id) {
      this.enterpId = id;
      getInformationInfo(this.enterpId).then((res) => {
        if (res.data.code == 0) {
          this.safeStatus = res.data.data.safeStatus;
        }
      });
      postCimEarlyWarningPushList({
        companyCode: this.enterpId ? this.enterpId.toString() : "",
        distCode: this.distCode,
        rank: "",
        warnStatus: "0", //warnstatus
        pageSize: 1,
        nowPage: 1,
        startDate: parseTime(
          new Date(new Date(new Date().toLocaleDateString()).getTime()),
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
        //   startDate:"2010-01-01 22:22:22",
        endDate: parseTime(
          new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
              24 * 60 * 60 * 1000 -
              1
          ),
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
        // endDate:"2022-11-16 23:59:59",
        // startDate:"2022-10-17 00:00:00"

        // companyCodeList: [this.enterpId],
        // startDate: parseTime(
        //   new Date(new Date(new Date().toLocaleDateString()).getTime()),
        //   "{y}-{m}-{d} {h}:{i}:{s}"
        // ),
        // //   startDate:"2010-01-01 22:22:22",
        // endDate: parseTime(
        //   new Date(
        //     new Date(new Date().toLocaleDateString()).getTime() +
        //       24 * 60 * 60 * 1000 -
        //       1
        //   ),
        //   "{y}-{m}-{d} {h}:{i}:{s}"
        // ),
      }).then((res) => {
        if (res.data.status == 200) {        
          this.recordsArr = res.data.data.list;
          this.table = res.data.data.list[0];
        }
      });
    },
    more() {
      this.$router.push({
        path: `/riskAssessment/riskEarlyWarningPush`,
        query: { enterpId: this.enterpId },
      });
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {},
};
</script>
<style lang="scss" scoped>
.main-top {
  width: 100%;
  height: 40px;
  overflow: hidden;
  // margin-top: 15px;
  .main-left {
    float: left;
    width: 13%;
    img {
      display: inline-block;
      vertical-align: middle;
      width: 35px;
    }
    span {
      font-size: 18px;
      font-weight: bolder;
      display: inline-block;
      margin-left: 15px;
      line-height: 40px;
    }
  }
  .main-right {
    float: left;
    width: 87%;
    height: 40px;
    background: #fff4db;
    border-radius: 30px;
    position: relative;
    p {
      span:nth-child(1) {
        margin-right: 30px;
        // position: relative;
        // top: -2px;
      }
      .yName {
        position: absolute;
        top: 7px;
        left: 127px;
        color: #ffb821;
      }
      .cName {
        position: absolute;
        top: 7px;
        left: 127px;
        color: #ff9037;
      }
      .hName {
        position: absolute;
        top: 7px;
        left: 127px;
        color: #ff5656;
      }
      img {
        position: relative;
        top: -2px;
      }
      span:nth-child(2) {
        margin-right: 30px;
      }
      height: 30px;
      margin: 5px auto 5px 45px;
      font-size: 14px;
      color: #666;
      line-height: 30px;
      i {
        display: inline-block;
        width: 21px;
        height: 21px;
        vertical-align: middle;
        position: relative;
        top: -1px;
      }
      i.huang {
        background: url("/static/img/assets/img/cfyjhus.png") center center
          no-repeat;
        background-size: cover;
        margin-left: 10px;
      }
      i.cheng {
        background: url("/static/img/assets/img/cfyjcs.png") center center
          no-repeat;
        background-size: cover;
      }
      i.hong {
        background: url("/static/img/assets/img/cfyjhs.png") center center
          no-repeat;
        background-size: cover;
      }
      .huangTime {
        display: inline-block;
        min-width: 140px;
        text-align: center;
        border-right: 1px solid #d8d8d8;
        margin-right: 15px;
      }
      .chengTime {
        display: inline-block;
        min-width: 140px;
        text-align: center;
        border-right: 1px solid #d8d8d8;
        margin-right: 15px;
      }
      .hongTime {
        display: inline-block;
        min-width: 140px;
        text-align: center;
      }
    }
    .more {
      position: absolute;
      right: 30px;
      top: 10px;
      font-size: 14px;
      color: #3977ea;
      cursor: pointer;
    }
  }
}
</style>
