<template>
  <div>
    <div class="work-heard">
      <h2>企业专区</h2>
    </div>
    <ul class="yingji-list">
      <li @click="goToLink(1)">
        <img src="/static/img/assets/img/zcxx.png" />
        <p>隐患管理</p>
      </li>
      <li @click="goToLink(2)">
        <img src="/static/img/assets/img/zzgl.png" />
        <p>组织管理</p>
      </li>
      <li @click="goToLink(3)">
        <img src="/static/img/assets/img/skxt.png" />
        <p>双控系统</p>
      </li>
      <li @click="goToLink(4)">
        <img src="/static/img/assets/img/zncj.png" />
        <p>智能采集</p>
      </li>
      <li @click="goToLink(5)">
        <img src="/static/img/assets/img/ywtb.png" />
        <p>业务填报</p>
      </li>
      <li @click="goToLink(6)">
        <img src="/static/img/assets/img/ywtb.png" />
        <p style="margin-top: -15px">现场核查与行政检查</p>
      </li>
      <!-- <li @click="goToLink" style="overflow: hidden; height: 0px">
        <img src="/static/img/assets/img/yqtb.png" />
        <p>疫情填报</p>
      </li> -->
      <li @click="goToLinkInfo(1)">
        <img src="/static/img/assets/img/信息接收.png" />
        <p style="margin-top: -15px">信息接收<br />事故警示</p>
      </li>
      <li @click="goToLinkInfo(2)">
        <img src="/static/img/assets/img/日报.png" />
        <p>日报</p>
      </li>
      <li @click="goToLinkInfo(3)">
        <img src="/static/img/assets/img/vedio.png" />
        <p>视频智能分析</p>
      </li>
    </ul>
  </div>
</template>
<script>
export default {
  components: {},
  data() {
    return {};
  },
  methods: {
    goToLinkInfo(val) {
      if (val == 1) {
        this.$router.push({
          path: "/dailySafetyManagement/informationRelease",
        });
      } else if (val == 2) {
        //日报
        this.$router.push({
          path: "/riskAssessment/monitorWarnReport",
        });
      } else if (val == 3) {
        //视频智能分析
        this.$router.push({
          path: "/riskAssessment/videoAnalysis",
        });
      }
    },
    goToLink(type) {
      // this.$message({
      //   type: "info",
      //   message: "该功能暂未上线，敬请期待！",
      // });
      console.log(this.$store.state.controler.urlData, "aaaa");
      let urlDataAry = this.$store.state.controler.urlData;
      if (type == 1) {
        //注册信息 11
        // if(urlDataAry.length > 0){
        //  let ary= urlDataAry.filter(el=>el.name=='注册信息')
        //  window.open(ary[0].url);
        // }
        const username = this.$store.state.login.user.username || "";
        window.open(
          `https://zhyj.yjj.wuhan.gov.cn:8991/controlweb/admin/#/nologin?username=${username}&access=0`
        );
      } else if (type == 2) {
        //组织管理 11
        if (urlDataAry.length > 0) {
          let ary = urlDataAry.filter((el) => el.name == "组织管理");
          window.open(ary[0].url);
        }
      } else if (type == 3) {
        //双控系统 11
        if (urlDataAry.length > 0) {
          //  let ary= urlDataAry.filter(el=>el.name=='双控系统')
          //  window.open(ary[0].url);
          window.open("http://223.75.53.53:9999/");
        }
      } else if (type == 4) {
        //智能采集 11
        if (urlDataAry.length > 0) {
          let ary = urlDataAry.filter((el) => el.name == "智能采集");
          window.open(ary[0].url);
        }
      } else if (type == 5) {
        //业务填报
        if (urlDataAry.length > 0) {
          let ary = urlDataAry.filter((el) => el.name == "业务报表");
          window.open(ary[0].url);
        }
      } else if (type == 6) {
        this.$router.push({
          path: "/dailySafetyManagement/otherInspections",
        });
      }
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {},
};
</script>
<style lang="scss" scoped>
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.yingji-list {
  width: 92%;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  li {
    margin-top: 20px;
    width: calc(100% / 5 - 13px);
    height: 146px;
    border: none;
    box-shadow: 0px 0px 8px 0px rgba(121, 163, 241, 0.3);
    border-radius: 4px;
    list-style: none;
    background: #f5f9fe;
    cursor: pointer;
    img {
      width: 68px;
      display: table-cell;
      text-align: center;
      margin: 20px auto;
    }
    p {
      text-align: center;
      color: #545c65;
    }
  }
  li:hover {
    background: #9cc6fa;
  }
}
</style>
