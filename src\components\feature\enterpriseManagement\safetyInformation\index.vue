<template>
  <div class="safety-information">
    <div class="table" v-loading="loading">
      <ul class="container">
        <li class="">
          <div class="l">企业法人</div>
          <div class="r">{{ safetyInfo.legal_person }}</div>
        </li>
        <li class=""></li>
        <!-- <li class="">
          <div class="l">企业负责人</div>
          <div class="r">{{ safetyInfo.responsible }}</div>
        </li> -->
        <!-- <li>
          <div class="l">企业负责人手机号</div>
          <div class="r">{{ safetyInfo.responsible_mobile }}</div>
        </li> -->
        <li>
          <div class="l">企业安管机构负责人</div>
          <div class="r">{{ safetyInfo.safe_responsible }}</div>
        </li>
        <!-- <li>
          <div class="l">企业安管机构负责人身份证号</div>
          <div class="r">{{ safetyInfo.safe_responsible_id_card }}</div>
        </li> -->
        <li>
          <div class="l">企业安管负责人手机号</div>
          <div class="r">{{ safetyInfo.safe_responsible_phone }}</div>
        </li>
        <li>
          <div class="l">企业分管安全负责人</div>
          <div class="r">{{ safetyInfo.safety_people }}</div>
        </li>
        <li>
          <div class="l">企业分管安全负责人手机号</div>
          <div class="r">{{ safetyInfo.safety_people_mobile }}</div>
        </li>
        <li>
          <div class="l">经办人</div>
          <div class="r">{{ safetyInfo.manager_name }}</div>
        </li>
        <li>
          <div class="l">经办人身份证号</div>
          <div class="r">{{ safetyInfo.manager_id_code }}</div>
        </li>
        <li>
          <div class="l">经办人手机号</div>
          <div class="r">{{ safetyInfo.manager_phone }}</div>
        </li>
        <li>
          <div class="l">经办人邮箱</div>
          <div class="r">{{ safetyInfo.email }}</div>
        </li>
        <!-- <li class="">
          <div class="l">企业法人身份证号</div>
          <div class="r">{{ safetyInfo.legal_person_id_card }}</div>
        </li>
        <li class="">
          <div class="l">企业法人手机号</div>
          <div class="r">{{ safetyInfo.legal_person_phone }}</div>
        </li> -->
        <li></li>
        <li class="">
          <div class="l">企业内设的安全管理部门</div>
          <div class="r">{{ safetyInfo.company_security_departm }}</div>
        </li>
        <li>
          <div class="l">注册安全工程师人数</div>
          <div class="r">{{ safetyInfo.safety_engineers_people }}</div>
        </li>
        <li class="">
          <div class="l">专职安全生产管理人员人数</div>
          <div class="r">{{ safetyInfo.security_builder_people_num }}</div>
        </li>
        <li>
          <div class="l">兼职安全生产管理人数</div>
          <div class="r">{{ safetyInfo.safety_produce_mansge_pt_people }}</div>
        </li>
        <li>
          <div class="l">应急救援队伍专职人数</div>
          <div class="r">{{ safetyInfo.rescue_team_full_people }}</div>
        </li>
        <li>
          <div class="l">应急救援队伍兼职人数</div>
          <div class="r">{{ safetyInfo.rescue_team_pt_people }}</div>
        </li>
        <li>
          <div class="l">剧毒化学品作业人员人数</div>
          <div class="r">{{ safetyInfo.highly_toxic_operating_people }}</div>
        </li>
        <li class="bottom">
          <div class="l">危险化学品作业人员人数</div>
          <div class="r">{{ safetyInfo.danger_toxic_operating_people }}</div>
        </li>
        <li class="bottom">
          <div class="l">特种作业人员人数</div>
          <div class="r">{{ safetyInfo.special_operating_people }}</div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getCompanySafetyInformation } from "@/api/safetyInfomation";
import { Loading } from "element-ui";
export default {
  name: "safetyInformation",
  components: {},
  props: { enterpriseId: { type: String, default: "" } },
  data() {
    return {
      safetyInfo: {},
      Loading: null,
    };
  },
  methods: {
    getData(id) {
      return;
      this.loading = true;
      getCompanySafetyInformation({
        enterpriseId: id,
      }).then((res) => {
        if (res.data.status == 200) {
          this.safetyInfo = res.data.data;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-information {
  width: 100%;
  height: 100%;
  .table {
    width: 100%;
    height: 100%;
    overflow: auto;
    .container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      text-align: center;
      .bottom {
        border-bottom: 1px solid rgb(231, 231, 231);
      }

      li {
        list-style-type: none;
        width: 50%;
        display: flex;
        align-items: center;
        border-top: 1px solid rgb(231, 231, 231);
        // border-right: 1px solid rgb(231, 231, 231);
        border-left: 1px solid rgb(231, 231, 231);
        overflow: hidden;
        min-height: 40px;
        .red {
          color: red;
        }
        .l {
          width: 40%;
          min-height: 40px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 5px 10px;
          color: #60627a;
          background: rgb(242, 246, 255);
        }

        .r {
          width: 58%;
          padding: 1%;
          word-break: break-all;
        }
      }
      li:nth-of-type(2n + 0) {
        list-style-type: none;
        width: 50%;
        display: flex;
        align-items: center;
        border-top: 1px solid rgb(231, 231, 231);
        border-right: 1px solid rgb(231, 231, 231);
        overflow: hidden;
        min-height: 40px;
        .red {
          color: red;
        }
        .l {
          width: 40%;
          min-height: 40px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 5px 10px;
          color: #60627a;
          background: rgb(242, 246, 255);
        }

        .r {
          width: 58%;
          padding: 1%;
          word-break: break-all;
        }
      }
      .lang {
        list-style-type: none;
        width: 100%;
        display: flex;
        border-top: 1px solid #eaedf2;
        border-right: 1px solid rgb(231, 231, 231);
        overflow: hidden;
        min-height: 40px;
        .red {
          color: red;
        }
        .l {
          width: 25%;
          min-height: 40px;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 5px 10px;
          color: #60627a;
          background: rgb(242, 246, 255);
        }

        .r {
          width: 58%;
          padding: 1%;
          word-break: break-all;
        }
      }
      .liLine {
        list-style-type: none;
        width: 33.3%;
        display: flex;
        border-top: 1px solid rgb(231, 231, 231);
        border-right: 1px solid rgb(231, 231, 231);
        overflow: hidden;
        min-height: 40px;
        .red {
          color: red;
        }
        .l {
          width: 50%;
          background: rgb(242, 246, 255);
        }
        .r {
          width: 50%;
          word-break: break-all;
        }
      }
      .bottom:nth-of-type(3n + 0) {
        border-right: 1px solid rgb(231, 231, 231);
      }
    }
  }
}
</style>
