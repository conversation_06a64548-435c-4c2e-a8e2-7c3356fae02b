<template>
  <el-dialog
    :title="parentData.preRiskUnitName + '—' + parentData.riskPointName"
    :visible.sync="ishandleClick"
    width="800px"
    top="10vh"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
  >
    <div class="riskH">
      <div class="radioBox">
        <el-radio-group v-model="radioTop">
          <el-radio-button label="fixed">固定风险指标</el-radio-button>
          <el-radio-button label="dynamic">动态风险指标</el-radio-button>
        </el-radio-group>
      </div>

      <div v-if="isTab">
        <el-tabs v-model="activeName" @click="handleClick" class="tab">
          <!---------------------------------------------风险设备设施(hs) -------------------------==-->
          <el-tab-pane label="风险设备设施(hs)" name="1">
            <!-- <span>风险设备设施(hs)</span> -->
            <!-- <span slot="label">
              <span v-if="hsDataObj.indexHs"
                ><el-tooltip placement="top" content="提交成功">
                  <i
                    class="iconTip iconTipGreen el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              <span v-else
                ><el-tooltip placement="top" content="请提交风险设备设施(hs)">
                  <i
                    class="iconTip iconTipRed iconTip el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              风险设备设施(hs)</span
            > -->

            <div class="hs_box">
              <h2>hs——高风险设备固有危险指数：</h2>
              <div class="fenShu">
                <h2>{{ hsDataObj.indexHs || "无" }}</h2>
                <el-progress
                  type="circle"
                  :percentage="hsDataObj.percentageS"
                  >{{ hsDataObj.indexHs }}</el-progress
                >
              </div>
            </div>
            <div class="hs_con">
              <div class="hs_con_tit">
                <div><span>风险要素</span></div>
                <div>本质安全化水平</div>
              </div>

              <div class="hs_con_box hsHeight">
                <div>
                  <div
                    class="hs_con_item"
                    v-for="(item, index) of hsData"
                    :key="index"
                  >
                    <span>{{ item.factorName }}</span>
                    <div>
                      <el-select
                        v-model="item.factorValue"
                        placeholder="请选择"
                        size="mini"
                        clearable
                      >
                        <el-option
                          v-for="item in hsDataOptions"
                          :key="item.id"
                          :label="item.label"
                          :value="item.id"
                        >
                        </el-option>
                      </el-select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="btnBox">
              <el-button size="mini" type="primary" @click="hsSubmitFn()"
                >提 交</el-button
              >
            </div>
          </el-tab-pane>
          <!---------------------------------------------高风险工艺(K1) -------------------------==-->
          <el-tab-pane label="高风险工艺(K1)" name="2">
            <!-- <span>高风险工艺(K1)</span> -->
            <!-- <span slot="label">
              <span v-if="hsDataObj.indexHs != 0"
                ><el-tooltip placement="top" content="提交成功">
                  <i
                    class="iconTip iconTipGreen el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              <span v-else
                ><el-tooltip placement="top" content="请提交高风险工艺(K1)">
                  <i
                    class="iconTip iconTipRed iconTip el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              高风险工艺(K1)</span
            > -->

            <div class="k1_box">
              <!-- <img src="../../../../../static/img/riskManagementIcon/k1.png" /> -->

              <div class="hs_box">
                <!-- {{k1DataObj}} -->
                <h2>高风险工艺修正系数-K1：</h2>
                <div class="fenShu">
                  <h2>{{ k1DataObj.indexK1 || "无" }}</h2>
                  <el-progress
                    type="circle"
                    :percentage="k1DataObj.percentageS"
                    >{{ k1DataObj.indexK1 }}</el-progress
                  >
                </div>
              </div>

              <div class="k1_con">
                <div class="tit_top">
                  <span>风险要素</span>
                  <span>监测监控完好水平</span>
                  <span>失效率</span>
                </div>
                <div>
                  <div class="tit_con">
                    <div v-for="(item, index2) of k1Data" :key="index2">
                      <div class="tit_item">
                        <div class="item_l">
                          <span
                            >{{ item.superFactorName
                            }}<i class="el-icon-arrow-right"></i
                          ></span>
                        </div>
                        <div class="item_r">
                          <div
                            class="item_list"
                            v-for="(el, index3) of item.processInherentDatas"
                            :key="index3"
                          >
                            <div style="width: 50%">
                              {{ el.factorName || "--" }}
                            </div>
                            <!-- <div class="selectTit">
                            <el-select
                              v-model="el.factorValue"
                              placeholder="请选择"
                              size="mini"
                              clearable
                            >
                              <el-option
                                v-for="item in hsDataOptions"
                                :key="item.id"
                                :label="item.label"
                                :value="item.id"
                              >
                              </el-option>
                            </el-select>
                          </div> -->
                            <!-- {{el}} -->
                            <div class="shixiao">
                              <el-input-number
                                @input.native="onInput0_100"
                                placeholder="不涉及的请勿填写"
                                v-model.number="el.factorValue"
                                size="mini"
                                style="width: 100%"
                              ></el-input-number>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="btnBox">
                <el-button size="mini" type="primary" @click="k1SubmitFn()"
                  >提 交</el-button
                >
              </div>
            </div>
          </el-tab-pane>
          <!---------------------------------------------高风险场所(E) -------------------------==-->
          <el-tab-pane label="高风险场所(E)" name="3">
            <span slot="label">
              <span v-if="eDataObj.indexE"
                ><el-tooltip placement="top" content="提交成功">
                  <i
                    class="iconTip iconTipGreen el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              <span v-else
                ><el-tooltip placement="top" content="请提交高风险场所(E)">
                  <i
                    class="iconTip iconTipRed iconTip el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              高风险场所(E)</span
            >

            <div>
              <!-- <img src="../../../../../static/img/riskManagementIcon/e.png" /> -->

              <div class="hs_box">
                <h2>E——高风险场所人员暴露指数：</h2>
                <div class="fenShu">
                  <h2>{{ eModel.indexE || "无" }}</h2>
                  <el-progress
                    type="circle"
                    :percentage="eDataObj.percentageS"
                    >{{ eDataObj.indexE }}</el-progress
                  >
                </div>
              </div>

              <div class="hs_con">
                <div class="e_con_h">
                <div class="hs_con_tit">
                  <div><span>风险要素</span></div>
                  <div>人员风险暴露</div>
                </div>
                <div class="hs_con_box">
                  <div class="hs_con_item">
                    <span>发生装置区域</span>
                    <div class="item_l">
                      <el-select
                        v-model="eDataObj.indexE"
                        placeholder="请输入"
                        size="mini"
                        clearable
                      >
                        <el-option
                          v-for="item in eOption"
                          :key="item.id"
                          :label="item.label"
                          :value="item.id"
                        >
                        </el-option>
                      </el-select>
                      <span>人</span>
                    </div>
                  </div>
                </div>
                <div class="message">
                  注：原则上以事故后果严重度模拟计算结果为依据，确定事故影响范围，进而确定波及人员数量。
                </div>
                </div>
                <!--  -->
                <div class="btnBox">
                  <el-button size="mini" type="primary" @click="eSubmitFn()"
                    >提 交</el-button
                  >
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="高风险物品(M)" name="4">
            <span slot="label">
              <span v-if="mDataObj.indexM"
                ><el-tooltip placement="top" content="提交成功">
                  <i
                    class="iconTip iconTipGreen el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              <span v-else
                ><el-tooltip placement="top" content="请提交高风险物品(M)">
                  <i
                    class="iconTip iconTipRed iconTip el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              高风险物品(M)</span
            >

            <div class="hs_box">
              <h2>M—高风险物品物质危险指数：</h2>
              <div class="fenShu">
                <h2>{{ mDataObj.indexM || "无" }}</h2>
                <el-progress type="circle" :percentage="mDataObj.percentageS">{{
                  mDataObj.indexM
                }}</el-progress>
              </div>
            </div>
         
            <div class="hs_con">
              
              <!-- <div class="hs_con_tit">
                <div><span>风险要素</span></div>
                <div>物质危险性指数</div>
              </div>
              <div class="hs_con_box">
                <div class="hs_con_item">
                  <span>生产、储存物质</span>
                  <div class="item_l">
                    <el-input type="text" v-model.trim="majorHazardLevel" />
                  </div>
                </div>
              </div> -->
              <!-- <div class="message">
                  注：原则上以事故后果严重度模拟计算结果为依据，确定事故影响范围，进而确定波及人员数量。
                </div> -->
            </div>

            <!-- <br /> -->

            <div class="M_box">
              <div>
                <h2>物品相对量值：{{ mDataObj.indexM || "--" }}</h2>
              <div class="m_tit">
                <div class="m_tit_l">
                  物资列表<span
                    >注：临界量来至与《危险化学品重大危险源辨识》</span
                  >
                </div>
                <div class="m_tit_r">
                  <button
                    @click="addFn"
                    type="button"
                    class="el-button el-button--primary el-button--small"
                  >
                    <!----><!----><span>添加</span>
                  </button>
                </div>
              </div>
              </div>
               
              <div class="M_boxtabHeight">
                <el-table
                  :data="StorageTankData"
                  style="width: 100%"
                  :highlight-current-row="false"
                  border
                  class=""
                >
                  <!-- <el-table-column prop="storageTankNUM" label=" " width="90">
                  <template slot-scope="scope">
                    储罐{{ scope.$index + 1 }}
                  </template>
                </el-table-column> -->
                  <el-table-column prop="type" label="物资名称" width="200">
                    <template slot-scope="scope">
                      <!-- <el-select
                        v-model.trim="scope.row.title"
                        :disabled="!scope.row.isDisabled"
                        filterable
                        style="width: 160px"
                        placeholder="请输入物资名称"
                        remote
                        value-key="chemicalId"
                        clearable
                        @change="
                          (item) => {
                            currentSel(item, scope.row);
                          }
                        "
                        reserve-keyword
                        :remote-method="Tolikesearch"
                      >
                        <el-option
                          v-for="item in chemicalOption"
                          :key="item.chemicalId"
                          :title="item"
                          :label="item.title"
                          :value="item"
                        >
                        </el-option>
                      </el-select> -->

                      <el-autocomplete
                        popper-class="my-autocomplete"
                        v-model.trim="scope.row.title"
                        :disabled="!scope.row.isDisabled"
                        :fetch-suggestions="querySearch"
                        placeholder="请输入物资名称"
                        clearable
                        @select="
                          (item) => {
                            handleSelect(item, scope.row);
                          }
                        "
                        size="mini"
                      >
                        <template slot-scope="{ item }">
                          <div class="name">{{ item.title }}</div>
                        </template>
                      </el-autocomplete>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="storage"
                    label="设计存储量(吨)"
                    width="/"
                  >
                    <template slot-scope="scope">
                      <div class="tableInputFelxBox">
                        <div class="tableInputFelxBox_left">
                          <el-input
                            type="text"
                            size="mini"
                            @input.native="onInputNum"
                            v-model.trim="scope.row.storage"
                            maxlength="10"
                          />


                           <!-- <el-input-number
                                :min="0"
                                placeholder=""
                                v-model.number="scope.row.storage"
                                size="mini"
                                style="width: 100%"
                              ></el-input-number> -->






                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="criticalValue"
                    label="临界量(吨)"
                    width="/"
                  >
                    <template slot-scope="scope">
                      <div class="tableInputFelxBox">
                        <div class="tableInputFelxBox_left">
                          <span
                            v-if="
                              scope.row.criticalValue == null ||
                              scope.row.criticalValue == ''
                            "
                            @click="popNoData(scope.row, scope, scope.$index)"
                            >无对应数据</span
                          >

                          <button
                            v-else
                            class="noClickOff"
                            :disabled="!scope.row.criticalValueList"
                            @click="popNoData(scope.row, scope, scope.$index)"
                          >
                            {{ scope.row.criticalValue }}
                          </button>
                          <!-- <span
                            v-if="
                              scope.row.criticalValue == null ||
                              scope.row.criticalValue == ''
                            "
                            @click="popNoData(scope.row, scope, scope.$index)"
                            >无对应数据</span
                          >
                          <span v-else
                            ><el-input
                              type="text"                            
                              v-model.trim="scope.row.criticalValue"
                          /></span> -->
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="校正系数" width="">
                    <template slot-scope="scope">
                      <!-- {{ }} -->
                      <div class="tableInputFelxBox">
                        <div class="tableInputFelxBox_left">
                          <span
                            v-if="
                              scope.row.correction == null ||
                              scope.row.correction == ''
                            "
                            @click="
                              correctionMatchFn(scope.row, scope, scope.$index)
                            "
                            >无对应数据</span
                          >

                          <button
                            v-else
                            class="noClickOff"
                            :disabled="!scope.row.correctionValueList"
                            @click="
                              correctionMatchFn(scope.row, scope, scope.$index)
                            "
                          >
                            {{ scope.row.correction }}
                          </button>
                          <!-- <span
                            v-if="
                              scope.row.correction == null ||
                              scope.row.correction == ''
                            "
                            @click="
                              correctionMatchFn(scope.row, scope, scope.$index)
                            "
                            >无对应数据</span
                          >
                          <span v-else
                            ><el-input
                              type="text"                            
                              v-model.trim="scope.row.correction"
                          /></span> -->
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80">
                    <template slot-scope="scope">
                      <div class="deteleStyle" @click="deteleFn(scope.$index)">
                        删除
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <div class="btnBox">
                <el-button size="mini" type="primary" @click="mSubmitFn()"
                  >提 交</el-button
                >
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane name="5">
            <span slot="label">
                           <span v-if="k2DataObj.indexK2"
                ><el-tooltip placement="top" content="提交成功">
                  <i
                    class="iconTip iconTipGreen el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              <span v-else
                ><el-tooltip placement="top" content="请提交高风险作业K2">
                  <i
                    class="iconTip iconTipRed iconTip el-icon-warning"
                  ></i> </el-tooltip
              ></span>

              高风险作业(k2)</span
            >
            <div>
              <!-- k2DataObj -->
              <div class="hs_box">
                <h2>k2—高风险作业指数：</h2>
                <div class="fenShu">
                  <h2>{{ k2DataObj.indexK2 || "无" }}</h2>
                  <el-progress
                    type="circle"
                    :percentage="k2DataObj.percentageS"
                    >{{ k2DataObj.indexK2 }}</el-progress
                  >
                </div>
              </div>

              <div class="k2_box ">
                 <div class="k2_box_h">
                <el-table
                  :data="k2Data"
                  style="width: 100%"
                  :highlight-current-row="false"
                  border
                >
                  <el-table-column prop="storageTankNUM" label="序号" width="50">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
                  <el-table-column
                    prop="jobTypeName"
                    label="作业类型"
                    width="/"
                  >
                  </el-table-column>
                  <!-- <el-table-column
                    prop="jobSmallTypeName"
                    label="作业小类"
                    width="/"
                  >
                  </el-table-column> -->
                  <el-table-column label="数量" width="/" prop="count">
                  </el-table-column>
                  <el-table-column label="更新时间" width="/" prop="updateTime">
                  </el-table-column>
                </el-table>
                 </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>

      <div v-else>
        <div>
          <div class="hs_box">
            <h2>高危风险监测特征指标修正系数(k3)：</h2>
             <div class="fenShu">
                  <h2>{{ k3DataObj.indexK3 || "无" }}</h2>
                  <el-progress
                    type="circle"
                    :percentage="k3DataObj.percentageS"
                    >{{ k3DataObj.indexK3 }}</el-progress
                  >
                </div>
          </div>

          <div class="fen_dong">
            <div class="fen_tit">
              <span>报警级别</span>
              <span>数量</span>
              <span>系数</span>
              <span>更新时间</span>
            </div>

            <!-- <div class="k3_con_h">
                <div class="feng_con">
              <div class="feng_item">
                <span>一级报警</span>
                <div class="level_num">
                  <el-input
                    size="mini"
                    v-model.trim="levelOneDate.num"
                  ></el-input>
                </div>
                <div class="xiShu">
                  <el-input
                    size="mini"
                    v-model.trim="levelOneDate.xishu"
                  ></el-input>
                </div>
                <div class="updataTime">
                  <el-date-picker
                    v-model="levelOneDate.updata"
                    type="date"
                    size="mini"
                    placeholder="选择日期"
                  >
                  </el-date-picker>
                </div>
              </div>
            </div> -->

            <div class="feng_con">
              <div class="feng_item">
                <span>二级报警</span>
                <div class="level_num">
                  <!-- <el-input
                    size="mini"
                    v-model.trim="levelTwoDate.num"
                  ></el-input> -->
                  {{k3DataObj.alarmLevel2}}
                </div>
                <div class="xiShu">
                  <!-- <el-input
                    size="mini"
                    v-model.trim="levelTwoDate.xishu"
                  ></el-input> -->
                  0.3
                </div>
                <div class="updataTime">
                  {{k3DataObj.updateTime}}
                  <!-- <el-date-picker
                    v-model="levelTwoDate.updata"
                    type="date"
                    size="mini"
                    placeholder="选择日期"
                  >
                  </el-date-picker> -->
                </div>
              </div>
            </div>

            <div class="feng_con">
              <div class="feng_item">
                <span>三级报警</span>
                <div class="level_num">
                   {{k3DataObj.alarmLevel3}}
                </div>
                <div class="xiShu">
                 0.6
                </div>
                <div class="updataTime">
                  {{k3DataObj.updateTime}}
                </div>
              </div>
            </div>
            </div>

          
          </div>
        </div>
      </div>
    </div>
    <!-- 
    <div slot="footer" style="display: flex; justify-content: center">
      <el-button size="mini">取 消</el-button>

      <el-button size="mini" type="primary" @click="submit(2)">提 交</el-button>
    </div> -->

    <popCritical @subCritical2="subCriticalFn" ref="popCritical"></popCritical>
    <correctionMatch
      ref="correctionMatch"
      @submitMatch1="submitMatchFn"
    ></correctionMatch>
  </el-dialog>
</template>

<script>
var dayjs = require("dayjs");
import popCritical from "@/components/feature/enterpriseManagement/riskManagement/popCritical.vue";
import correctionMatch from "@/components/feature/enterpriseManagement/riskManagement/correctionMatch.vue";
//
import {
  getFindDevice,
  getDeviceInherentIndex,
  updateDevice,
  getFindProcess,
  updateProcess,
  exposeIndexOption,
  exposeFind,
  exposeUpdate,
  specificChemicalsIndex,
  updateMaterial,
  findMaterial,
  particularJobPage,
  particularPage,
  particularFindJob,
  alarmFind,
  findPeriodList,
} from "@/api/riskAssessment";
export default {
  components: {
    popCritical,
    correctionMatch,
  },
  data() {
    return {
      mDataObj: {
        indexM: 0,
        percentageS: 0,
      },
      mData: [],
      queryParams: {
        chemicalId: "",
        title: "",
      },
      chemicalOption: [],
      isSucess: false, //高风险作业K2
      hsSucess: false,
      hsData: [],
      hsDataObj: {
        indexHs: 0,
        percentageS: 0,
      },
      k1DataObj: {
        indexK1: 0,
        percentageS: 0,
      },
      k1Data: [],
      k3Data: [],
      k3DataObj: {
        indexK3: 0,
        percentageS: 0,
      },
      hsDataOptions: [],

      eOption: [],

      eDataObj: {
        indexK1: 0,
        percentageS: 0,
      },
      eData: [],
      eModel: {
        indexE: "",
      },
      majorHazardLevel: "",
      radioTop: "fixed",
      ishandleClick: false,
      isTab: true,
      activeName: "1",
      parentData: {}, //父数据
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      //k2—高风险作业指数
      k2Data: [],
      k2DataObj: {
        indexK2: 0,
        percentageS: 0,
      },
      StorageTankData: [
        // {
        //   dataId: "ff80808184ec01240184ec0618e200ff",
        //   enterpId: "420110101",
        //   cimRiskUnitId: "420110101007",
        //   riskPointId: "100032",
        //   chemicalId: "1",
        //   title: "氨",
        //   storage: 1,
        //   criticalValue: 10,
        //   correction: 2,
        //   createTime: "2022-12-07 17:58:53",
        //   creator: "6fa15da6bcc94c3daa9b03af08dc98a2",
        //   updateTime: "2022-12-07 17:58:53",
        //   modifier: "6fa15da6bcc94c3daa9b03af08dc98a2",
        //   deleteFlag: "0",
        //   criticalValueIds: null,
        //   criticalValueList: null,
        //   correctionIds: null,
        //   correctionValueList: null,
        // },
      ],
      levelOneDate: {
        num: 0,
        xishu: 0.1,
        updata: "2022-01-11",
      },
      levelTwoDate: {
        num: 0,
        xishu: 0.1,
        updata: "2022-01-11",
      },
      levelThreeDate: {
        num: 0,
        xishu: 0.1,
        updata: "2022-01-11",
      },
    };
  },
  watch: {
    radioTop(newVal, oldVal) {
      if (newVal == "fixed") {
        this.isTab = true;
      } else {
        this.isTab = false;
      }
    },
    watch: {},
  },
  methods: {
    getK3() {
      alarmFind({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k3DataObj = res.data.data || {};        
          this.k3DataObj.percentageS = res.data.data.indexK3
            ? res.data.data.indexK3 * 10
            : "";
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //k2—高风险作业指数
    getK2() {
      var _this = this;
      console.log(_this.parentData);
      particularFindJob({
        cimRiskUnitId: _this.parentData.cimRiskUnitId,
        cimRiskUnitType: _this.parentData.cimRiskUnitType,
        enterpId: _this.parentData.enterpId,
        riskPointId: _this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k2DataObj = res.data.data || {};
          this.k2Data = res.data.data.dtos || [];
          this.k2DataObj.percentageS = res.data.data.indexK2
            ? res.data.data.indexK2 * 10
            : "";
        } else {
          // this.$message.error(res.msg);
        }
      });
      particularJobPage({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
        jobDate: dayjs().format("YYYY-MM-DD"),
        nowPage: 1,
        pageSize: 1000,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k2Data = res.data.data || [];
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      specificChemicalsIndex({ keywords: keyWord })
        .then((res) => {
          if (res.data.status == 200) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    handleSelect(value, item) {
      console.log(value, "选择的数据", item);
      item.correction = value.correction;
      item.criticalValue = value.criticalValue;
      item.chemicalId = value.chemicalId;
      item.title = value.title;
      item.cimRiskUnitId = this.parentData.cimRiskUnitId;
      item.cimRiskUnitType = this.parentData.cimRiskUnitType;
      item.enterpId = this.parentData.enterpId; //enterpId
      item.riskPointId = this.parentData.riskPointId;
    },

    //自动输入end

    //高风险场所(M)初始数据
    getM() {
      findMaterial({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {         
          this.mDataObj = res.data.data;
          this.StorageTankData = res.data.data.materialDataList || [];
          console.log(this.StorageTankData);
          this.mDataObj.percentageS = res.data.data.indexM
            ? res.data.data.indexM * 10
            : "";
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //提交高风险场所(M)
    mSubmitFn() {
      if (this.StorageTankData.length > 0) {
        if (this.StorageTankData.some((val) => !val.title)) {
          this.$message.error("物资名称不能为空");
          return;
        }
        if (this.StorageTankData.some((val) => !val.storage)) {
          console.log(val.storage);
          this.$message.error("设计存储量(吨)不能为空");
          return;
        }
        if (this.StorageTankData.some((val) => !val.criticalValue)) {
          this.$message.error("临界量(吨)不能为空");
          return;
        }
        if (this.StorageTankData.some((val) => !val.correction)) {
          this.$message.error("校正系数不能为空");
          return;
        }
      }
      //处理第二次没有任何修改是提交保存
      if (this.StorageTankData.length > 0) {
        this.StorageTankData.forEach((item) => {
          if (item.chemicalId == "") {
            item.chemicalId = item.title;
          }
          if (item.correctionValueList == null) {
            item.correctionValueList = [];
          } else {
            if (item.correctionValueList.length > 0) {
              if (item.correctionValueList[0].chemicalId) {
                item.correctionValueList = [
                  item.correctionValueList[0].chemicalId,
                ];
              }
            }
          }
        });

        this.StorageTankData.forEach((item) => {
          if (item.criticalValueList == null) {
            item.criticalValueList = [];
          } else {
            if (item.criticalValueList.length > 0) {
              if (item.criticalValueList[0].chemicalId) {
                var ary = [];
                item.criticalValueList.forEach((el) => {
                  ary.push(el.chemicalId);
                });
                item.criticalValueList = ary;
              }
            }
          }
        });
      }
      var params = {
        cimRiskUnitId: this.mDataObj.cimRiskUnitId,
        enterpId: this.mDataObj.enterpId,
        materialDataList: this.StorageTankData,
        riskPointId: this.mDataObj.riskPointId,
      };
      // console.log(params,'这是提交bbbbbbbbbbbbb')
      updateMaterial(params).then((res) => {
        if (res.data.status === 200) {
          this.getM();
          this.$message.success("保存成功");
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //回填系数校正
    submitMatchFn(val) {
      this.StorageTankData[val.rowIndex].correctionValueList = [
        val[0].chemicalId,
      ];
      this.StorageTankData[val.rowIndex].correction = val[0].correction;
    },
    //回填临界量
    subCriticalFn(val) {
      var ary = [];
      if (val.length > 0) {
        val.forEach((item) => {
          ary.push(item.chemicalId);
        });
      }
      this.StorageTankData[val.rowIndex].criticalValueList = ary;
      this.StorageTankData[val.rowIndex].criticalValue = val.minN;
    },
    Tolikesearch(query) {
      specificChemicalsIndex({ keywords: query }).then((res) => {
        if (res.data.status === 200) {
          this.chemicalOption = res.data.data;
        }
      });
    },
    //选择化学品
    currentSel(value, item) {
      //  value.title=item.title;
      item.correction = value.correction;
      item.criticalValue = value.criticalValue;
      item.chemicalId = value.chemicalId;
      item.title = value.title;
      // item.cimRiskUnitId = this.parentData.cimRiskUnitId;
      // item.cimRiskUnitType = this.parentData.cimRiskUnitType;
      // item.enterpId = this.parentData.enterpId; //enterpId
      // item.riskPointId = this.parentData.riskPointId;
    },
    //获取风险设备设施(hs)初始数据
    getHS() {
      getFindDevice({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.hsDataObj = res.data.data;
          this.hsDataObj.percentageS = res.data.data.indexHs
            ? res.data.data.indexHs * 10
            : "";
          // if(res.data.data.deviceInherentDatas.length > 0){
          //   res.data.data.deviceInherentDatas.forEach(item=>{
          //     if(){item.factorValue=0
          //   })
          // }
          this.hsData = res.data.data.deviceInherentDatas || [];
        } else {
        }
      });
    },
    //获取高风险工艺(K1)初始数据
    getK1() {
      getFindProcess({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k1DataObj = res.data.data;
          this.k1DataObj.percentageS = res.data.data.indexK1
            ? res.data.data.indexK1 * 10
            : "";

          //processInherentDatas
          var lenData = this.deepClone(res.data.data.processInherentLists);
          if (lenData.length > 0) {
            lenData.forEach((item) => {
              if (item.processInherentDatas.length > 0) {
                item.processInherentDatas.forEach((el) => {
                  el.factorValue = el.factorValue ? el.factorValue : undefined;
                });
              }
            });
          }

          this.k1Data = lenData || [];
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //高风险场所(E)初始数据
    getE() {
      exposeFind({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        dataId: this.parentData.dataId,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.eDataObj = res.data.data;
          this.eData = res.data.data;
          this.eDataObj.percentageS = res.data.data.indexE
            ? res.data.data.indexE * 10
            : "";
          this.eModel.indexE = res.data.data.indexE;
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //高风险场所(E)提交
    eSubmitFn() {
      var params = {
        cimRiskUnitId: this.eDataObj.cimRiskUnitId,
        dataId: this.dataId,
        enterpId: this.enterpId,
        indexE: this.indexE,
        riskPointId: this.riskPointId,
      };
      exposeUpdate(this.eDataObj).then((res) => {
        if (res.data.status === 200) {
          this.getE(); //获取计算的分数
          this.$message.success("保存成功");
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },

    //风险设备设施(hs)提交
    hsSubmitFn() {
      updateDevice(this.hsData).then((res) => {
        if (res.data.status === 200) {
          this.getHS(); //获取计算的分数
          this.$message.success("保存成功");
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    //高风险工艺(K1)提交
    k1SubmitFn() {
      var aAry = this.deepClone(this.k1Data);
      if (aAry.length > 0) {
        aAry.forEach((item) => {
          if (item.processInherentDatas.length > 0) {
            item.processInherentDatas.forEach((el) => {
              if (el.factorValue == undefined) {
                el.factorValue = "";
              } else {
                el.factorValue = el.factorValue;
              }
              // el.factorValue==undefined
              //   ? 'aa'
              //   : el.factorValue;
            });
          }
        });
      }

      updateProcess(aAry).then((res) => {
        if (res.data.status === 200) {
          this.getK1(); //获取计算的分数
          this.$message.success("保存成功");
        } else {
          this.$message.res.data(res.data.msg);
        }
      });
    },

    //高风险工艺(K1)数据校验
    onInput0_100(e) {
      this.$message.closeAll();
      // 验证是否是纯数字
      let isNumber = /^\d*$/.test(e.target.value);
      // 过滤非数字
      e.target.value = e.target.value.replace(/\D/g, "");
      if (!isNumber || e.target.value < 0 || e.target.value > 100) {
        this.$message.warning("只能输入[0,100]区间的整数");
      }
      e.target.value =
        (e.target.value > 0 &&
          e.target.value <= 100 &&
          e.target.value.match(/^\d*/g)[0]) ||
        null;
    },

    onInputNum(e) {
      this.$message.closeAll();
      // 验证是否是纯数字
      // let isNumber = /^(?!(0[0-9]{0,}$))[0-9]{1,}[.]{0,}[0-9]{0,}$/;
      // const isNumber = /^[0-9]+(.?[0-9]{1,2})?$/
      let isNumber =/^[0-9]+.?[0-9]*$/;
      // 过滤非数字
      if (isNumber.test(e.target.value) == false) {
        e.target.value = "";
        this.$message.error("请填写数字");
      }
      // e.target.value =
      //   (e.target.value >= 0 &&
      //     e.target.value.match(/^\d*/g)[0]) ||
      //   null;
      // },
    },

    deepClone(obj) {
      let objClone = Array.isArray(obj) ? [] : {};
      if (obj && typeof obj === "object") {
        for (let key in obj) {
          if (obj.hasOwnProperty(key)) {
            //判断ojb子元素是否为对象，如果是，递归复制
            if (obj[key] && typeof obj[key] === "object") {
              objClone[key] = this.deepClone(obj[key]);
            } else {
              //如果不是，简单复制
              objClone[key] = obj[key];
            }
          }
        }
      }
      return objClone;
    },

    //下拉
    initData() {
      getDeviceInherentIndex().then((res) => {
        if (res.data.status === 200) {
          // res.data.data.forEach(item=>{
          //    console.log(item.id==null,'111111111111')
          //    if(item.id==null){
          //      item.id:""
          //    }
          // })

          let result = res.data.data.map((item) => ({
            id: item.id != "" ? Number(item.id) : "",
            label: item.label,
          }));

          result.forEach((item) => {
            if (item.id == 0) {
              item.id = null;
            }
          });

          this.hsDataOptions = result || [];
        } else {
          // this.$message.error(res.msg);
        }
      });

      //发生装置区域
      exposeIndexOption().then((res) => {
        if (res.data.status === 200) {
          let result = res.data.data.map((item) => ({
            id: item.id != "" ? Number(item.id) : "",
            label: item.label,
          }));

          result.forEach((item) => {
            if (item.id == 0) {
              item.id = null;
            }
          });

          this.eOption = result || [];
        } else {
          // this.$message.error(res.msg);
        }
      });
    },

    handleClick() {},
    //无对应数据弹框
    popNoData(row, index) {
      if (row.title == "") {
        this.$message.error("请先填写物资名称");
        return false;
      } //popCritical
      this.$refs["popCritical"].popNoData(true);
      this.$refs["popCritical"].initData(row, index.$index);
    },
    correctionMatchFn(row, index) {
      if (row.title == "") {
        this.$message.error("请先填写物资名称");
        return false;
      }
      this.$refs["correctionMatch"].popNoData(true);
      this.$refs["correctionMatch"].initData(row, index.$index);
    },
    addFn() {
      // item.cimRiskUnitId = this.parentData.cimRiskUnitId;
      // item.cimRiskUnitType = this.parentData.cimRiskUnitType;
      // item.enterpId = this.parentData.enterpId; //enterpId
      // item.riskPointId = this.parentData.riskPointId;
      var _this = this;
      let params = {
        chemicalId: "",
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        correction: "",
        correctionValueList: [],
        criticalValue: "",
        criticalValueList: [],
        dataId: "",
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
        storage: "",
        title: "",
        isDisabled: 1,
      };
      _this.StorageTankData.push(params);
    },
    deteleFn(val) {
      this.StorageTankData.splice(val, 1);
    },
  },
  mounted() {
    // this.getHS()
  },
};
</script>
<style lang="scss" scoped>
.noClickOff {
  padding: 0px 10px;
  cursor: pointer;
}
/deep/ button.noClickOff:disabled {
  border: 1px solid #ccc;
  color: #ccc;
  cursor: inherit;
}
/deep/ .M_box .el-scrollbar__bar.is-horizontal {
  display: none !important;
}
/deep/ .el-tab-pane {
  // height: 500px;
  // overflow: auto;
  // height: calc(100%- 200px);
}
/deep/ .el-progress--circle .el-progress__text,
/deep/ .el-progress--dashboard .el-progress__text {
  display: none !important;
}
.tableInputFelxBox {
  span {
    cursor: pointer;
    color: #409eff;
    text-decoration: underline;
  }
}
.k1_box .tit_con,
.hs_con .hs_con_box.hsHeight {
  height: 300px;
  overflow: auto;
}
.k3_con_h {
  height: calc(300px + 55px);
}

.M_boxtabHeight {
  height: calc(300px - 73.5px + 40px); //title:73.5px  40px
  overflow: auto;
}
.k2_box_h {
  height: calc(300px + 40px + 48px);
  overflow-y: auto; //提交28+20
}
.e_con_h {
  height: calc(300px + 40px);
  overflow-y: auto;
}
.btnBox {
  display: flex;
  align-content: center;
  justify-content: center;
  margin: 20px 0 0 0;
}
.el-tabs__item {
  position: relative;
}
.iconTip {
  position: absolute;
  right: 0px;
}
.iconTipRed {
  color: red;
}
.iconTipGreen {
  color: green;
}
//计算分数
.calculateScore {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 5px solid #e5e9f2;
  .itemDiv {
    width: 10px;
    background: #409eff;
  }
}
.radioBox {
  margin: 0 0 15px 0;
  text-align: center;
  border-bottom: 1px solid #ccc;
  padding: 0 0 10px;
}
h2 {
  margin: 0;
}
.hs_box {
  display: flex;
  align-items: center;
  justify-content: center;
  .fenShu {
    position: relative;
    h2 {
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      text-align: center;
      margin: 0;
      -webkit-transform: translate(0, -50%);
      transform: translate(0, -50%);
      margin: 0;
    }
  }
}
.hs_con {
  width: 400px;
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  margin: 0 auto 0 auto;
  .hs_con_tit {
    background: #ccc;
    height: 40px;
    line-height: 40px;
    display: flex;
    width: 100%;

    > div {
      width: 50%;
      span {
        padding: 0 0 0 20px;
        display: inline-block;
      }
    }
  }
  .hs_con_box {
    width: 100%;
    .hs_con_item {
      display: flex;
      margin: 20px 0 0 0;
      align-items: center;
      .item_l {
        display: flex;
        align-items: center;
        span {
          margin: 0 0 0 10px;
        }
      }
      > span {
        width: 50%;
        padding: 0 0 0 20px;
        box-sizing: border-box;
      }
      > div {
        width: 50%;
      }
    }
  }
  .message {
    color: #ccc;
    margin: 20px 0 0 0;
  }
}

//高风险物品(M)
.M_box {
  .m_tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 10px 0;
    .m_tit_l {
      border-left: 4px solid #409eff;
      padding: 0 0 0 5px;
      span {
        color: #ccc;
        display: inline-block;
        margin: 0 0 0 20px;
      }
    }
    .m_tit_r {
    }
  }
  .deteleStyle {
    cursor: pointer;
    color: #409eff;
  }
}
.k1_con {
  .tit_top {
    display: flex;
    height: 40px;
    align-items: center;
    background: #ccc;
    span {
      width: 33.33%;
      position: relative;
    }

    span:first-child {
      padding: 0 0 0 20px;
    }
  }
  .tit_item {
    display: flex;
    padding: 20px 0;
    .item_l {
      width: 33.33%;
      display: flex;
      padding: 0 40px 0 0;
      box-sizing: border-box;
      align-content: center;
      span {
        display: flex;
        align-items: center;
        // justify-content: center;
        padding: 0 0 0 20px;
        border-right: 1px solid #ccc;
        width: 100%;
        position: relative;
        i {
          position: absolute;
          right: -7px;
          color: #409eff;
        }
      }
      span::before {
        position: absolute;
        right: -6px;
        width: 12px;
        height: 12px;
        background: rgb(186, 236, 245);
        content: "";
      }
      span:after {
        position: absolute;
        right: 0;
        // content:"\e6e0"
      }
    }
    .item_r {
      width: 66.66%;
      .item_list {
        display: flex;
        align-items: center;
        margin: 10px 0;
        .selectTit {
          width: 50%;
        }
        .shixiao .el-input {
          width: 100px;
          /* margin-left: 20px; */
        }
      }
    }
  }
}
.fen_dong {
  .fen_tit {
    display: flex;
    height: 40px;
    background: #ccc;
    align-items: center;
    justify-content: center;
    text-align: center;
    > span {
      width: 25%;
    }
  }
  .feng_item {
    display: flex;
    text-align: center;
    align-items: center;
    margin: 10px 0;
    > span {
      width: 25%;
    }
    > div {
      width: 25%;
      // padding: 0 10px;
      box-sizing: content-box;
    }
  }
}
/deep/ .el-date-editor.el-input {
  width: auto;
}
</style>