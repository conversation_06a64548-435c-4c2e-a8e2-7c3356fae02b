<template>
  <div class="enterpriseFill">
    <div>
      <div class="header">企业现状</div>
      <div>
        <form action="" model="entData">
          <!-- 企业现状开始 -->
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">生产装置套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.unitsNumber"
                  placeholder="生产装置套数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">运行套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.runNumber"
                  placeholder="运行套数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">停产套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.parkNumber"
                  placeholder="停产套数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">一级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.dangerLevelOne"
                  placeholder="一级重大危险源"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">二级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.dangerLevelTwe"
                  placeholder="二级重大危险源"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">三级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.dangerLevelThree"
                  placeholder="二级重大危险源"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">四级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.dangerLevelFour"
                  placeholder="四级重大危险源"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">试生产装置套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.dangerLevelFour"
                  placeholder="试生产装置套数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">重点监管危险工艺</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.dangerMsds"
                  placeholder="重点监管危险工艺"
                ></el-input>
                <div class="yellow">/种</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否有承包商作业</span>
              </div>
              <div class="inputBox">
                <el-select
                  class="select"
                  v-model="entData.contractor"
                  placeholder="是否有承包商作业"
                >
                  <el-option
                    v-for="item in contractor1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否处于试生产期</span>
              </div>
              <div class="inputBox">
                <el-select
                  class="select"
                  v-model="entData.trialProduction"
                  placeholder="是否处于试生产期"
                >
                  <el-option
                    v-for="item in trialProduction1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否处于开停车状态</span>
              </div>
              <div class="inputBox">
                <el-select
                  class="select"
                  v-model="entData.openParking"
                  placeholder="是否处于开停车状态"
                >
                  <el-option
                    v-for="item in openParking1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否开展中（扩）试</span>
              </div>
              <div class="inputBox">
                <el-select
                  class="select"
                  v-model="entData.test"
                  placeholder="是否开展中（扩）试"
                >
                  <el-option
                    v-for="item in test1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>

            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">有无重大隐患</span>
              </div>
              <div class="inputBox">
                <el-select
                  class="select"
                  v-model="entData.mhazards"
                  placeholder="有无重大隐患"
                >
                  <el-option
                    v-for="item in mHazards1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">检维修套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.fixNum"
                  placeholder="检维修套数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">正在开停车装置数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.onOffDevice"
                  placeholder="正在开停车装置数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <!-- 占位符开始 -->
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span><span class="title"></span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="input"
                  placeholder=""
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span><span class="title"></span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="input"
                  placeholder="重点监管危险工艺"
                ></el-input>
                <div class="yellow">/种</div>
              </div>
            </li>
            <!-- 占位符结束 -->
          </ul>
          <!-- 企业现状结束 -->
          <!-- 作业风险开始 -->
          <div class="header">作业风险</div>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">特殊动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.firesNumber"
                  placeholder="特殊动火作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">一级动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.fire1Number"
                  placeholder="运行套数"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">二级动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.fire2Number"
                  placeholder="二级动火作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">受限空间作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.spaceworkNumber"
                  placeholder="受限空间作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">盲板作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.blindplateNumber"
                  placeholder="盲板作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">高处作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.highworkNumber"
                  placeholder="高处作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">吊装作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.liftingworkNumber"
                  placeholder="吊装作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">临时用电作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.electricityworkNumber"
                  placeholder="临时用电作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">动土作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.soilworkNumber"
                  placeholder="动土作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">断路作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.roadworkNumber"
                  placeholder="断路作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">检维修作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.inspectionNumber"
                  placeholder="检维修作业"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">二级动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.inspectionNumber"
                  placeholder="停产套数"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <!-- 作业风险结束 -->
          <!-- 企业承诺开始 -->
          <div class="header">企业承诺</div>
          <ul>
            <li class="list textareaLi">
              <div class="titleBox textareaTitle">
                <span class="red">*</span>
                <span class="title">我司今日承诺</span>
              </div>
              <div class="textareaBox">
                <el-input
                  class="listTextarea"
                  v-model.trim="entData.subject"
                  placeholder="企业承诺"
                  type="textarea"
                  :rows="2"
                  :autosize="{ minRows: 2 }"
                ></el-input>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">风险等级</span>
              </div>
              <div class="inputBox">
                <el-select
                  class="select"
                  v-model="entData.riskGrade"
                  disabled
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in riskGrade1"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">承诺人</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  v-model.trim="entData.commitment"
                  placeholder="承诺人"
                  type="input"
                  disabled
                ></el-input>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">承诺时间</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.commiteDate"
                  disabled
                ></el-input>
              </div>
            </li>
          </ul>
          <!-- 企业承诺结束 -->
        </form>
        <div class="commitBox">
          <div class="commit" @click="upData()"><i></i>保存</div>
          <div class="commit" @click="saveData()"><i></i>保存并上报</div>
          <div class="commit" @click="toPath('enterprise')"><i></i>返回</div>
        </div>
        <!-- 占位符 -->
        <div class="placeholder"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getSecuritySave ,getSecurityUpdate} from "@/api/entList";
import { dateFormat } from "../../../../utils/index";
export default {
  name:"enterpriseFill",
  //import引入的组件
  components: {},
  data() {
    return {
      input: "",
      options: [
        {
          value: "选项1",
          label: "黄金糕"
        },
        {
          value: "选项2",
          label: "双皮奶"
        },
        {
          value: "选项3",
          label: "蚵仔煎"
        },
        {
          value: "选项4",
          label: "龙须面"
        },
        {
          value: "选项5",
          label: "北京烤鸭"
        }
      ],
      value: "",
      entData: {
        blindplateNumber: "",
        commiteDate: dateFormat("YYYY-mm-dd HH:MM:SS", new Date()),
        commiteDateStr: "",
        commitment: "",
        commitmentState: "",
        commitmentStateStr: "",
        companyCode: "",
        contractor: "",
        dangerLevelFour: "",
        dangerLevelOne: "",
        dangerLevelThree: "",
        dangerLevelTwe: "",
        dangerMsds: "",
        electricityworkNumber: "",
        extendstr1: "",
        fire1Number: "",
        fire2Number: "",
        firesNumber: "",
        fixNum: "",
        highworkNumber: "",
        id: "",
        inspectionNumber: "",
        levelRiskName: "",
        liftingworkNumber: "",
        mhazards: "",
        onOffDevice: "",
        openParking: "",
        parkNumber: "",
        riskGrade: "",
        roadworkNumber: "",
        runNumber: "",
        soilworkNumber: "",
        spaceworkNumber: "",
        subject: "",
        test: "",
        trialProduction: "",
        tryUnitsNumber: "",
        unitsNumber: ""
      },
      contractor1: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      trialProduction1: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      openParking1: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      test1: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      mHazards1: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ],
      riskGrade1: [
        {
          value: "1",
          label: "是"
        },
        {
          value: "0",
          label: "否"
        }
      ]
    };
  },
  //方法集合
  methods: {
    toPath(path) {
      this.$router.push({ name: path });
    },
    saveData() {
      getSecuritySave({ dyBusinessCommitment: this.entData }).then(res => {
        this.$router.go(-1);//返回上一层
        if (res.data.code == "error") {
          this.$message({
            message: res.data.message,
            type: "warning"
          });
        }
      });
    },
    upData() {
      getSecurityUpdate({ dyBusinessCommitment: this.entData }).then(res => {
        this.$router.go(-1);//返回上一层
        if (res.data.code == "error") {
          this.$message({
            message: res.data.message,
            type: "warning"
          });
        }
      });
    }

  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  created() {
    this.entData.id=this.$route.params.id
  }

};
</script>
<style lang="scss" scoped>
.enterpriseFill {
  margin: 15px;
  background-color: #fff;
  padding: 15px;
}
.col {
  display: flex;
  justify-content: space-between;
}
.header {
  font-size: 16px;
  line-height: 1;
  color: #656565;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  width: 100%;
  text-align: left;
  margin-top: 20px;
  margin-bottom: 5px;
}
.titleBox {
  background-color: #f2f7f8;
  width: 158px;
  min-height: 55px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 10px;
  border-right: 1px solid #f0f0f0;
}
ul:nth-last-of-type(1) {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  height: 55px;
  overflow: hidden;
  padding: 0;
}
ul {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  // height: 55px;
  overflow: hidden;
  padding: 0;
}
.list {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  .title {
    font-size: 16px;
    color: #222;
  }
}
.listInput {
  flex: 1;
  height: 36px;
}
.red {
  color: red;
  font-weight: 900;
  font-size: 16px;
  margin-right: 3px;
}
.yellow {
  background-color: #f1ac5a;
  color: #fff;
  padding: 14px 0;
  display: block;
  width: 55px;
  text-align: center;
}
.inputBox {
  padding: 0;
  display: flex;
  align-items: center;
  flex: 1;
  .listInput {
    margin-right: 10px;
    margin-left: 10px;
    height: 40px;
    flex: 1;
  }
  .select {
    margin-right: 10px;
    margin-left: 10px;
    height: 40px;
    flex: 1;
  }
}
.textareaBox {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  .listTextarea {
    width: 95%;
  }
}
.textareaTitle {
  padding-top: 10px;
  padding-bottom: 10px;
}
.back {
  visibility: hidden;
}
.commitBox {
  width: 100%;
  height: 70px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #fff;
  border-top: 1px solid #dfe0e0;
  .commit {
    color: #fff;
    background-color: #2892e2;
    border-color: #2892e2;
    text-align: center;
    display: inline-flex;
    align-items: center;
    border-radius: 4px;
    padding: 10px 20px;
    margin-left: 20px;
    font-weight: 900;
  }
}
.placeholder {
  width: 100%;
  height: 70px;
}
</style>
