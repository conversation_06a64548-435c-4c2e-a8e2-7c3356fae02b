<template>
  <div class="home">
    <el-table :data="propsData.list" border :header-cell-style="headerCellStyle">
      <el-table-column label="环保设施名称" prop="epfName"> </el-table-column>
      <el-table-column label="环保设施类型" prop="epfTypeCode">
      </el-table-column>
      <el-table-column prop="epfExecutor" label="运行企业"></el-table-column>
      <el-table-column prop="epfBuilder" label="建设企业"></el-table-column>
      <el-table-column prop="epfDesigner" label="设计企业"></el-table-column>
      <el-table-column
        prop="epfUsePhaseCode"
        label="使用阶段"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  components: {},
  props: {
    propsData: {
      type: Object,
      required: false,
    },
  },
  data() {
    return {
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      disabledTooltip: false,
      options1: [],
      options2: [],
      options3: [],
      form: {
        formTop: { formTopResource: "" },
        formList: [],
      },
    };
  },

  created() {
    //this.dictionaryData(); // 下拉选择数据查询
    this.disposeData(this.propsData);
  },
  methods: {
    dictionaryData() {
      // 下拉选择查询
      const promiseList = [
        this.$api.dictionaryData({ groupCode: "EPF_TYPE", parentCode: "0" }),
        this.$api.dictionaryData({
          groupCode: "EPF_USE_PHASE",
          parentCode: "0",
        }),
        this.$api.dictionaryData({
          groupCode: "EPF_COOP_MODE",
          parentCode: "0",
        }),
      ];
      Promise.all(promiseList).then((res) => {
        if (res.length > 0) {
          this.options1 = res[0].data;
          this.options2 = res[1].data;
          this.options3 = res[2].data;
        }
      });
    },
    disposeData(res) {
      console.log(res);
      this.form.formTop.formTopResource = res.hasEpf;
      if (res.list && res.list.length > 0 && res.hasEpf == "1") {
        this.form.formList = res.list;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
