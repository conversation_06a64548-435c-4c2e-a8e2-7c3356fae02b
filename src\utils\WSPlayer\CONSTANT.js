
const CONSTANT = {
    websocketPorts: {
        realmonitor: "realmonitor-websocket",
        playback: "playback-websocket",
        // realmonitor: {
        //     ws: 9100,
        //     wss: 9102,
        //     nginxProxy: "realmonitor-websocket",
        //     proxyPort: 9800
        // },
        // playback: {
        //     ws: 9320,
        //     wss: 9322,
        //     nginxProxy: "playback-websocket",
        //     proxyPort: 9900
        // }
    },
    errorInfo: {
        101: "播放延时大于8s",
        201: "当前音频无法播放",
        202: "websocket连接错误",
        203: "文件播放完成",
        // 204: "连接失败，请检查设备",
        404: "请求超时或未找到",
        405: "播放超时",
        406: "视频流类型解析失败，请检查通道配置",
        407: "请求超时",
        457: "时间设置错误",
        503: "SETUP服务不可用",
        504: "对讲服务不可用",
        701: "Chrome版本低，请升级到最新的Chrome版本",
        702: "Firefox版本低，请升级到最新的Firefox版本",
        703: "Edge版本低，请升级到最新的Edge版本",
        defaultErrorMsg: "加载中，请稍等"
    }
}

export default CONSTANT
