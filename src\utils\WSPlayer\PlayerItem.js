/**
 * PlayerItem
 */
import moment from "moment"

class PlayerItem {
    /**
     * @param {*} opt.wrapperDomId 父级id
     * @param {*} opt.index 索引
     */
    constructor(opt) {
        // dom
        this.$el = null
        // 播放用元素
        this.canvasElem = null
        this.videoElem = null
        // 每个组件的dom唯一 id
        this.domId = opt.wrapperDomId + '-' + opt.index
        // 所属的wsplayer
        this.wsPlayer = opt.wsPlayer
        // index序号
        this.index = opt.index
        // 第一帧事件
        this.firstTime = 0
        // audioOn
        this.isAudioPlay = false
    }

    /**
     * @param {*} domId 此播放器的id
     */
    initDom() {
        let template = this.getTemplate()
        let player = $(template)
        this.wsPlayer.$wrapper.append(player[0])
        this.$el = $('#' + this.domId)
        this.canvasElem = document.getElementById(this.canvasId)
        this.videoElem = document.getElementById(this.videoId)
    }
    // 添加监听
    initMouseEvent() {
        this.$el.click((evt) => {
            this.wsPlayer.setSelectIndex(this.index)
            this.$el.siblings().removeClass('selected').addClass('unselected')
            this.$el.removeClass('unselected').addClass('selected')
        })
        this.$el.dblclick((evt) => {
            if (this.wsPlayer.$el.hasClass('fullplayer')) {
                this.wsPlayer.setPlayerNum(4)
                this.wsPlayer.sendMessage("windowNumChanged", 4);
            } else {
                this.wsPlayer.setPlayerNum(1)
                this.wsPlayer.sendMessage("windowNumChanged", 1);
            }
            this.wsPlayer.setSelectIndex(this.index)
            this.$el.siblings().removeClass('selected').addClass('unselected')
            this.$el.removeClass('unselected').addClass('selected')
        })
        $('.end-icon', this.$el).on('click', () => {
            this.close()
        })
        $('.audio-icon', this.$el).click((evt) => {
            if (this.isAudioPlay) {
                // 正在播放，关闭声音
                this.player.setAudioVolume(0);
                $(evt.target).removeClass('on').addClass('off')
            } else {
                // 未播放，打开声音
                this.player.setAudioVolume(1);
                $(evt.target).removeClass('off').addClass('on')
            }
            this.isAudioPlay = !this.isAudioPlay
        })
        $('.capture-icon', this.$el).click((evt) => {
            this.player.capture(`${this.options.channelName || "抓图"}-${moment().format("YYYYMMDD-HHmmss")}`, $("canvas", this.$el)[0]);
        })
        $('.close-icon', this.$el).click((evt) => {
            this.close();
        })
        $('.record-icon', this.$el).click((evt) => {
            if (this.isRecording) {
                // 结束录像
                this.isRecording = false
                this.player.stopLocalRecord()
                $(evt.target).removeClass('recording')
            } else if(this.status === "playing") {
                // 正在播放的时候才能开始录像
                // 开始录像
                this.isRecording = true
                // 开始录像，参数是每个录像文件的大小，单位兆
                this.player.startLocalRecord(5);
                $(evt.target).addClass('recording')
            }
        })
    }
    // 设置状态
    setStatus() {}
    /**
     * 播放视频
     */
    play() {
        this.player.play()
        this.setStatus('playing')
    }
    /**
     * 暂停视频
     */
    pause() {
        this.player.pause()
        this.setStatus('pause')
    }
    /**
     * 关闭视频
     */
    close(changeVideoFlag = false) {
        this.player && window.wsPlayerManager.unbindPlayer(this.player.nPlayPort)
        this.wsPlayer.sendMessage("closeVideo", this.index);
        // 关闭视频的时候，也要关闭播放按钮
        this.setDomVisible($('.play-pause-wrapper', this.$el), false)
        // 关闭视频后也需要隐藏播放器
        this.videoElem.style.display = 'none';
        this.canvasElem.style.display = 'none';
        // 关闭回放的时候也需要清空时间轴
        this.wsPlayer.setTimeLine([]);
        if(this.isRecording) {
            // 结束录像
            this.isRecording = false
            this.player.stopLocalRecord()
            $('.record-icon', this.$el).removeClass('recording')
        }
        // 销毁“加载中”样式
        this.spinner && this.spinner.stop()
        this.player && this.player.stop()
        this.player && this.player.close()
        // 关闭视频后清空一些数据
        if(!changeVideoFlag) {
            this.player = null;
            this.options = null;
        }
        this.setStatus('closed')
    }

    // 设置元素是否可见
    setDomVisible(dom, visible) {
        dom && dom.css({
            visibility: visible ? 'visible' : 'hidden'
        })
    }

    copyCanvas() {
        let newCanvas = document.createElement("canvas");
        newCanvas.id = this.canvasElem.id
        newCanvas.className = this.canvasElem.className;
        return newCanvas;
    }
}
export default PlayerItem
