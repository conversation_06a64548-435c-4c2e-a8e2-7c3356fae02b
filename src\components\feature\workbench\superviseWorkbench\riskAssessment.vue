<template>
  <div class="riskAssessment" v-loading="loading">
    <div class="header">
      <div class="title">企业风险研判</div>
      <div
        class="more"
        @click="toUrl('/enterprisePortrait/enterprisePortraitReport')"
      >
        更多
      </div>
    </div>
    <div class="container">
      <div class="red" @click="openDialog(1)">
        <div>重大风险</div>
        <div style="margin-top: 5px">
          <span class="num">{{ enRiskList.eventLevelCount1 || 0 }}</span
          >个
        </div>
      </div>
      <div class="orange" @click="openDialog(2)">
        <div>较大风险</div>
        <div style="margin-top: 5px">
          <span class="num">{{ enRiskList.eventLevelCount2 || 0 }}</span
          >个
        </div>
      </div>
      <div class="yellow" @click="openDialog(3)">
        <div>一般风险</div>
        <div style="margin-top: 5px">
          <span class="num">{{ enRiskList.eventLevelCount3 || 0 }}</span
          >个
        </div>
      </div>
      <div class="blue" @click="openDialog(4)">
        <div>低风险</div>
        <div style="margin-top: 5px">
          <span class="num">{{ enRiskList.eventLevelCount4 || 0 }}</span
          >个
        </div>
      </div>
    </div>
    <div class="dialog">
      <el-dialog
        :title="dialogTitle"
        :visible.sync="show"
        width="1200px"
        top="5vh"
        @close="handleClose"
        :key="demoKey"
        v-dialogDrag
        :close-on-click-modal="false"
      >
        <div class="inputBox">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtVal"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            :show-all-levels="true"
            v-if="isShowDist"
          ></el-cascader>
          <el-select
            v-model="levelVal"
            size="mini"
            placeholder="请选择重大危险源企业"
            :clearable="true"
            multiple
            style="width: 300px"
          >
            <el-option
              v-for="item in level"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="enterName"
            size="mini"
            placeholder="请输入企业名称"
            class="input"
            clearable
          ></el-input>

          <el-button type="primary" size="mini" @click="searches"
            >查询</el-button
          >
        </div>
        <div class="table" v-loading="dialogLoading">
          <el-table
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            :data="tableData.records || tableData.list"
            style="width: 100%"
            border
            @select="select"
            @select-all="select"
          >
            <el-table-column
              type="selection"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column label="序号" width="50" align="center">
              <template slot-scope="{ row, column, $index, store }">
                <span>{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="enterName"
              label="单位名称"
              width="300"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row, column, $index, store }">
                <span
                  @click="goEnt(row)"
                  style="color: #3977ea; cursor: pointer"
                  >{{ row.enterName }}</span
                >
              </template>
            </el-table-column>
            <el-table-column prop="districtName" label="区划" align="center">
              <template slot-scope="{ row, column, $index, store }">
                <span>{{ row.districtName }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="score" label="分数" align="center">
              <template slot-scope="{ row, column, $index, store }">
                <span>{{ row.score }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="rank" label="风险等级" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.rank == 'D'">重大风险</span>
                <span v-if="scope.row.rank == 'C'">较大风险</span>
                <span v-if="scope.row.rank == 'B'">一般风险</span>
                <span v-if="scope.row.rank == 'A'">低风险</span>
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="riskLevelName"
              label="风险等级"
              align="center"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.riskGrade == 1">重大风险</span>
                <span v-if="scope.row.riskGrade == 2">较大风险</span>
                <span v-if="scope.row.riskGrade == 3">一般风险</span>
                <span v-if="scope.row.riskGrade == 4">低风险</span>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="tableData.size"
            layout="total, prev, pager, next"
            background
            :total="tableData.total"
          >
          </el-pagination>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
// import {
//   getEnterpriseList,
//   InformationExportexcel,
// } from "@/api/entList";
import {
  getEnRiskStatistic,
  getRiskDetailsList,
  getCompanyRiskLevel,
  getCompanyRiskLevelDetail,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      dialogTitle: "",
      currentPage: 1,
      show: false,
      level: [
        {
          label: "一级",
          value: "D",
        },
        {
          label: "二级",
          value: "C",
        },
        {
          label: "三级",
          value: "B",
        },
        {
          label: "四级",
          value: "A",
        },
      ],
      levelVal: [],
      input: "",
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      distCode: this.$store.state.login.userDistCode,
      value: "",
      tableData: {},
      districtLoading: false,
      enterName: "",
      loading: false,
      dialogLoading: false,
      selection: [],
      enRiskList: [],
      type: "",
      demoKey: 0,
    };
  },
  //方法集合
  methods: {
    openDialog(type) {
      this.show = true;
      if (type == 1) {
        this.dialogTitle = "重大风险企业清单";
        this.levelVal = ["D"];
      } else if (type == 2) {
        this.dialogTitle = "较大风险企业清单";
        this.levelVal = ["C"];
      } else if (type == 3) {
        this.dialogTitle = "一般风险企业清单";
        this.levelVal = ["B"];
      } else if (type == 4) {
        this.dialogTitle = "低风险企业清单";
        this.levelVal = ["A"];
      }
      this.type = type;
      this.searches();
    },
    toUrl(url) {
      this.$router.push(url);
    },
    handleClose() {
      this.levelVal = ["D", "C", "B", "A"];
      this.enterName = "";
      this.districtVal = this.$store.state.login.userDistCode;
      this.demoKey =
        "demo-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
    },
    searches() {
      this.currentPage = 1;
      this.search();
    },
    search() {
      this.dialogLoading = true;
      getCompanyRiskLevelDetail({
        enterName: "",
        districtCode: this.districtVal,
        enterName: this.enterName,
        eventLevel: this.levelVal,
        nowPage: this.currentPage, //current
        pageSize: 10, //size
      }).then((res) => {
        if (res.data.status == 200) {
          this.dialogLoading = false;
          this.tableData = res.data.data;
        }
      });
    },
    getCompanyRiskLevel() {
      this.loading = true;
      getCompanyRiskLevel({}).then((res) => {
        if (res.status == 200) {
          this.loading = false;
          // this.enRiskList = res.data.data;
          const arr = res.data.data;
          for (let i = 0; i < arr.length; i++) {
            if (arr[i].rank == "A") {
              this.enRiskList.eventLevelCount4 = arr[i].number;
            } else if (arr[i].rank == "B") {
              this.enRiskList.eventLevelCount3 = arr[i].number;
            } else if (arr[i].rank == "C") {
              this.enRiskList.eventLevelCount2 = arr[i].number;
            } else if (arr[i].rank == "D") {
              this.enRiskList.eventLevelCount1 = arr[i].number;
            }
          }
        }
      });
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.search();
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].enterpId;
      }
    },
    goEnt(row) {
      this.$router.push({
        path: '/enterprisePortrait/enterprisePortraitReport',
        query: {
          enterpName: row.enterName,
          openIndexAnalysis: true
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getCompanyRiskLevel();
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    deep: true,
    immediate: true,
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
};
</script>
<style lang="scss" scoped>
.riskAssessment {
  width: 100%;
  border: 1px solid #d8e0ee;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #fff;
  z-index: 1;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  .header {
    width: 95%;
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .container {
    margin-top: 10px;
    color: #3c4043;
    width: 95%;
    padding-bottom: 15px;
    display: flex;
    justify-content: space-between;
    > div {
      width: 23%;
      height: 90px;
      cursor: pointer;
    }
    .red {
      background: url(../../../../../static/img/assets/img/red-BG.png) no-repeat;
      background-size: cover;
      padding-left: 20px;
      padding-bottom: 20px;
      padding-top: 10px;
      .num {
        font-size: 30px;
        color: #ec7878;
        font-weight: 600;
        margin-right: 2px;
      }
    }
    .orange {
      background: url(../../../../../static/img/assets/img/orange-BG.png)
        no-repeat;
      background-size: cover;
      padding-left: 20px;
      padding-bottom: 20px;
      padding-top: 10px;
      .num {
        font-size: 30px;
        color: #ffa656;
        font-weight: 600;
        margin-right: 2px;
      }
    }
    .yellow {
      background: url(../../../../../static/img/assets/img/yellow-BG.png)
        no-repeat;
      background-size: cover;
      padding-left: 20px;
      padding-bottom: 20px;
      padding-top: 10px;
      .num {
        font-size: 30px;
        color: #ffd964;
        font-weight: 600;
        margin-right: 2px;
      }
    }
    .blue {
      background: url(../../../../../static/img/assets/img/blue-BG.png)
        no-repeat;
      background-size: cover;
      padding-left: 20px;
      padding-bottom: 20px;
      padding-top: 10px;
      .num {
        font-size: 30px;
        color: #78afec;
        font-weight: 600;
        margin-right: 2px;
      }
    }
  }
  .dialog {
    .inputBox {
      width: 900px;
      display: flex;
      justify-content: flex-start;

      .input {
        width: 200px;
      }
      > * {
        margin-right: 15px;
      }
    }
    .table {
      margin-top: 10px;
    }
    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
    }
  }
}
</style>
