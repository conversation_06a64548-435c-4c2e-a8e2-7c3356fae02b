<template>
  <div class="user">
    <div class="tree-box">
      <h2>组织机构</h2>
      <!-- <a-tree
        :show-line="true"
        :default-expanded-keys="['']"
        @select="onSelect"
        @expand="onExpand"
        style="padding:0 10px;height: 100%;overflow-x: scroll;"
        >
            <a-icon slot="icon" type="carry-out" />
            <a-tree-node :key="item.orgCode" v-for="item in newAllData">
                <a-icon slot="icon" type="carry-out" />
                <span slot="title">{{item.orgName}}</span>
                <a-tree-node
                :key="subItem.orgCode" v-for="subItem in item.children" :title="subItem.orgName">
                </a-tree-node>
            </a-tree-node>
        </a-tree> -->

      <a-tree
        :multiple="false"
        default-expand-all
        @select="onSelect"
        @expand="onExpand"
        :tree-data="newAllData"
        style="padding: 0 10px; height: 100%; overflow-x: scroll"
      >
        <!-- <a-tree-node
          :key="item.orgCode"
          v-for="item in newAllData"
          :title="item.orgName"
        >
          <a-tree-node
            :key="subItem.orgCode"
            v-for="subItem in item.children"
            :title="subItem.orgName"
          >
            <a-tree-node
              :key="subItems.orgCode"
              v-for="subItems in subItem.children"
              :title="subItems.orgName"
            ></a-tree-node>
          </a-tree-node>
        </a-tree-node> -->
      </a-tree>
    </div>
    <div class="user-table">
      <div class="header">
        <div class="title">用户管理</div>
        <div>
          <el-radio-group size="small" v-model="mode">
            <el-radio-button label="活动用户"></el-radio-button>
            <el-radio-button label="注销用户"></el-radio-button>
          </el-radio-group>
          <a-input-search
            placeholder="请输入姓名查询关键字"
            v-model="keyWords"
            @search="search"
            style="width: 200px; margin-top: 9px"
          />
          <el-button
            type="primary"
            icon="plus"
            :style="{ marginTop: '9px', marginRight: '20px' }"
            @click="addInfo()"
          >
            新增用户
          </el-button>
        </div>
      </div>
      <div class="body" style="padding: 0 10px">
        <el-table
          v-loading="loading"
          style="width: 100%; min-height: 515px"
          :default-sort="{ prop: 'date', order: 'descending' }"
          ref="multipleTable"
          :data="tableData"
        >
          <el-table-column type="index" label="序号" width="80">
          </el-table-column>
          <el-table-column prop="name" label="姓名"> </el-table-column>
          <el-table-column prop="loginName" label="用户名"> </el-table-column>
          <el-table-column prop="belongRoleName" label="角色">
            <template slot-scope="{ row, column, $index, store }">
              <el-tooltip
                class="item"
                effect="dark"
                :content="row.belongRoleName"
                placement="left"
              >
                <span
                  style="
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                  "
                  >{{ row.belongRoleName }}</span
                >
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="状态">
            <template slot-scope="{ row, column, $index, store }">
              <span v-if="row.isLock == 'true'">已锁定</span>
              <span v-else>已激活</span>
            </template>
          </el-table-column>
          <el-table-column width="250" label="操作">
            <template slot-scope="{ row, column, $index, store }">
              <el-tooltip
                class="item"
                effect="dark"
                content="编辑"
                placement="top-start"
              >
                <el-button
                  type="primary"
                  icon="el-icon-edit"
                  @click="editUser(row)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="注销"
                placement="top-start"
              >
                <el-button
                  type="success"
                  icon="el-icon-user"
                  @click="deleteUser(row.userId)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="已锁定"
                placement="top-start"
                v-if="row.isLock == 'true'"
              >
                <el-button
                  type="warning"
                  icon="el-icon-lock"
                  @click="isClock(row.userId, 0)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="已激活"
                placement="top-start"
                v-else
              >
                <el-button
                  type="warning"
                  icon="el-icon-unlock"
                  @click="isClock(row.userId, 1)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="重置"
                placement="top-start"
              >
                <el-button
                  type="danger"
                  icon="el-icon-switch-button"
                  @click="restUser(row.userId)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="角色配置"
                placement="top-start"
              >
                <el-button
                  type="info"
                  icon="el-icon-user-solid"
                  @click="settingRole(row)"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          v-if="page.total > 0"
          style="float: right; padding-top: 15px"
        >
        </el-pagination>
      </div>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
      :orgCode="orgCode"
      @add-callback="addUser"
    ></DialogTable>
    <EditUser
      ref="childs"
      :edVisible="edVisible"
      :rowData="rowData"
      :orgCode="orgCode"
      @ed-callback="edUser"
    ></EditUser>
    <SettingRole
      ref="childes"
      :srVisible="srVisible"
      :rowData="rowData"
      :orgCode="orgCode"
      @ed-callback="srUser"
    ></SettingRole>
  </div>
</template>

<script>
// import Vue from "vue";
// import Antd from "ant-design-vue";
// import "ant-design-vue/dist/antd.css";
import DialogTable from "./add";
import EditUser from "./edit";
import SettingRole from "./settingrole";
import {
  getStructureData,
  getUserData,
  DeleteUser,
  lockandunlockUser,
  restPassword,
} from "@/api/user";
// Vue.config.productionTip = false;

// Vue.use(Antd);
export default {
  name: "User",
  components: {
    DialogTable,
    EditUser,
    SettingRole,
  },
  data() {
    return {
      allData: [],
      newAllData: [],
      tableData: [],
      mode: "活动用户",
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      orgCode: "",
      showFlag: 0,
      keyWords: "",
      dialogTableVisible: false,
      loading: false,
      edVisible: false,
      srVisible: false,
      rowData: {},
      i: 0,
    };
  },
  created() {},
  mounted() {
    this.getStructure();
    // this.getUser();
  },
  watch: {
    mode(newValue, oldValue) {
      if (newValue === "活动用户") {
        this.showFlag = 0;
      } else if (newValue === "注销用户") {
        this.showFlag = 1;
      }
      this.getUser();
    },
  },
  methods: {
    setMenus(arr) {
      /**
       * 利用递归替换key值
       * title替换orgName
       * key替换orgCode
       */
      var keyMap = {
        orgName: "title",
        orgCode: "key",
      };
      for (var i = 0; i < arr.length; i++) {
        delete arr[i].isLeaf;
        var obj = arr[i];
        for (var key in obj) {
          var newKey = keyMap[key];
          //   console.log(newKey);
          if (newKey) {
            obj[newKey] = obj[key];
            if (obj.children.length > 0) {
              this.setMenus(obj.children);
            }
            // delete obj[key];
          }
        }
      }
    },
    onSelect(selectedKeys, info) {
      this.orgCode = selectedKeys[0];
      this.getUser();
    },
    onExpand() {},
    search() {
      this.page.pageNo = 1;
      this.page.pageSize = 10;
      // this.getCommitment();
      this.getUser();
    },
    getStructure() {
      getStructureData()
        .then((data) => {
          // debugger;
          if (data.data.code == 0) {
            this.newAllData = data.data.data;
            // this.dataTOTree(this.allData,this.newAllData)
            this.setMenus(this.newAllData);
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    getUser() {
      this.loading = true;
      getUserData({
        showFlag: this.showFlag,
        name: this.keyWords,
        orgCode: this.orgCode,
        current: this.page.pageNo,
        size: this.page.pageSize,
      })
        .then((data) => {
          if (data.data.code == 0) {
            this.loading = false;
            let listData = data.data.data.records;
            this.page.total = data.data.data.total;
            this.tableData = listData;
          } else {
            this.loading = false;
            this.tableData = [];
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    addInfo() {
      if (this.orgCode) {
        this.dialogTableVisible = true;
        this.$refs.child.parentMsg(this.dialogTableVisible);
        // this.$refs.childs.getStructureTreeData();
        this.$refs.childs.clearTable();
      } else {
        this.$message({
          type: "warning",
          message: "请选择选择组织机构!",
        });
      }
    },
    /**
     * 切换分页尺寸
     * @param val
     */
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getUser();
      this.$refs.multipleTable.clearSelection();
    },
    /**
     * 切换当前页
     * @param val
     */
    handleCurrentChange(val) {
      this.page.pageNo = val;
      this.getUser();
      this.$refs.multipleTable.clearSelection();
    },
    addUser() {
      this.dialogTableVisible = false;
      this.getUser();
    },
    deleteUser(id) {
      this.$confirm("确定要注销该用户吗", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          DeleteUser({
            id: id,
          })
            .then(() => {
              this.getUser();
              this.$message({
                type: "success",
                message: "注销成功!",
              });
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    isClock(id, flag) {
      this.$confirm(flag == 0 ? "确定要激活该用户吗" : "确定要锁定该用户吗", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          lockandunlockUser({
            id: id,
            lockFlag: flag,
          })
            .then((data) => {
              if (data.data.code == 0) {
                this.getUser();
                this.$message({
                  type: "success",
                  message: "操作成功！",
                });
              }
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    restUser(id) {
      this.$confirm("确定要重置该用户密码吗", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          restPassword({
            id: id,
          })
            .then((data) => {
              if (data.data.code == 0) {
                this.getUser();
                this.$message({
                  type: "success",
                  message: data.data.msg,
                });
              }
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    editUser(row) {
      this.edVisible = true;
      this.rowData = row;
      this.$refs.childs.parentMsg(this.edVisible);
    },
    edUser() {
      this.edVisible = false;
      this.timeres = new Date().getTime();
      this.getUser();
    },
    settingRole(row) {
      this.srVisible = true;
      this.rowData = row;
      this.$refs.childes.parentMsg(this.srVisible);
      this.$refs.childes.getRoleIdsData(row.userId);
    },
    srUser() {
      this.seVisible = false;
      this.timeres = new Date().getTime();
      this.getUser();
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.user {
  padding: 20px 0;
  overflow: hidden;
}
.user > div {
  display: inline-block;
}
.tree-box {
  width: 23%;
  min-height: 100%;
  height: 85vh;
  overflow: hidden;
  background: #fff;
  h2 {
    line-height: 34px;
    font-size: 18px;
    padding: 8px 16px;
  }
}
.ant-tree-title {
  width: 167px;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.user-table {
  //   height: 85vh;
  min-height: 85vh;
  width: calc(77% - 16px);
  float: right;
  background-color: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    .newMenu {
      width: 90px;
      height: 38px;
      padding: 10px;
      background: #53a8e8;
      color: #fff;
      text-align: center;
      border-color: #53a8e8;
      border-radius: 4px;
    }
    .title {
      font-weight: 900;
      font-size: 16px;
      color: #000;
      padding: 0 20px;
      line-height: 50px;
    }
  }
}
</style>

<style>
.ant-tree-title {
  width: 187px;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.el-table th > .cell {
  text-align: center;
}

.el-table .cell {
  text-align: center;
}
.cell .el-button {
  padding: 12px;
}
.cell .el-button + .el-button {
  margin-left: 0px;
}
.body .el-table td,
.el-table th {
  padding: 4px 0;
}
</style>
