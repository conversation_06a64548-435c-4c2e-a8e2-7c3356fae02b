<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span @click="gotoRunningState"
              ><a-icon type="home" theme="filled" class="icon" /> 报警统计分析
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>企业分析</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="seach-part">
      <div class="l" :style="[{width:(isShowDist?'1015px':'745px')}]">
        <el-cascader
          size="mini"
          placeholder="请选择行政区划"
          :options="district"
          v-model="districtVal"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          :show-all-levels="true"
          v-if="isShowDist"
          style="width: 250px"
        ></el-cascader>
        <el-input
          size="mini"
          v-model.trim="enterpriseName"
          style="width: 250px"
          placeholder="请输入企业名称"
          clearable
        ></el-input>
        <el-date-picker
          v-model="date"
          size="mini"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getDate"
          clearable
          unlink-panels
        >
        </el-date-picker>
        <el-button type="primary" size="mini" @click="getDataSearch">查询</el-button>
        <CA-button type="primary" size="mini" plain @click="exportExcel"
          >导出</CA-button
        >
      </div>
      <!-- <el-button type="primary" size="mini">趋势分析</el-button>      -->
    </div>
    <div class="table-main">
      <div class="table-top">
        <h2>报警统计分析</h2>
      </div>
      <div>
        <div class="table" v-loading="loading">
          <el-table
            :data="tableData.records"
            style="width: 100%"
            ref="multipleTable"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            :default-sort="{ prop: 'date', order: 'descending' }"
            @select="select"
            @select-all="select"
            @sort-change="changeTableSort"
          >
            <el-table-column type="selection" width="50" align="center">
            </el-table-column>
            <el-table-column
              label="单位名称"
              align="center"
              prop="enterpName"
              width="240"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row, column, $index, store }">
                <span
                  @click="goEnt(row)"
                  style="color: #3977ea; cursor: pointer"
                  >{{ row.enterpName }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              label="行政区划"
              width="100"
              prop="distName"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="报警次数"
              prop="warningNum"
              align="center"
              width="120"
              sortable="custom"
            >
            </el-table-column>
            <el-table-column
              label="重复报警点位数"
              prop="repeatWarningPointsNum"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="重复报警次数"
              prop="repeatWarningNum"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="平均消警时长(h)"
              prop="averageEliminationDuration"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="点位平均报警次数"
              prop="pointAverageWarningNum"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="最大报警时长(h)"
              prop="maxWarningDuration"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="平均扰动率"
              prop="averageDisturbanceRatio"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="消警处置及时率"
              prop="eliminationTimelyRatio"
              align="center"
            >
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @current-change="getPageChangeData"
            :current-page.sync="currentPage"
            :page-size="tableData.size"
            layout="total, prev, pager, next"
            :total="tableData.total"
            background
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getWarningAllEliminateList2,
  getWarningAllEliminateListExportExcel,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  data() {
    return {
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      value1: "",
      value: "",
      tableData: [],
      showtable: false,
      currentPage: 1,
      endDate: "",
      startDate: "",
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (144 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      majorHazardLevel: ["1", "2", "3", "4"],
      districtVal: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      enterpriseName: "",
      loading: false,
      selection: [],
      warningNumSign: "0",
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch:{
    vuexDistrict:{
      handler(newVal, oldVal) {
        this.district = newVal;
      }
    },
  },
  methods: {
    gotoRunningState() {
      let data = false;
      this.$emit("RunningState", data);
      // this.$router.go(0);
    },
    getDataSearch(){
      this.currentPage =1;
      this.getData();
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpId);
    },
    getPageChangeData(val){
        this.currentPage = val;
        this.getData();
    },
    getData() {
      this.loading = true;
      getWarningAllEliminateList2({
        distCode: this.districtVal,
        startTime: this.startDate || new Date(this.date[0]).Format("yy-MM-dd"),
        endTime: this.endDate || new Date(this.date[1]).Format("yy-MM-dd"),
        size: 10,
        current: this.currentPage,
        enterpriseName: this.enterpriseName,
        warningNumSign: this.warningNumSign,
      }).then((res) => {
        // console.log(res);
        this.tableData = res.data.data;
        // this.getEchart();
        this.loading = false;
      });
    },
    getDate() {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd");
    },
    // 导出
    exportExcel() {
      getWarningAllEliminateListExportExcel({
        distCode: this.districtVal,
        ids: this.selection.length <= 1 ? null : this.selection,
        startTime: this.startDate || new Date(this.date[0]).Format("yy-MM-dd"),
        endTime: this.endDate || new Date(this.date[1]).Format("yy-MM-dd"),
        // size: 10,
        // current: this.currentPage,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "报警统计分析_企业分析" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    changeTableSort(column) {
      let sortingType = column.order;
      //按照降序排序
      if (sortingType == "descending") {
        this.warningNumSign = "0";
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        this.warningNumSign = "1";
      } else {
        this.warningNumSign = "0";
      }
      this.getData();
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].enterpId;
      }
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      width: 1015px;
      display: flex;
      justify-content: space-between;
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      // height: 48px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 10px;
      padding-bottom: 10px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
