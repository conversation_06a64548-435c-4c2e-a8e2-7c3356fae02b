/*!
 * 基于MikesWei的CesiumVectorTile代码整理规范修改，并用webpack打包
 * 版本信息：v2.0.0, hash值: 6a3f822c63f0b0504916
 * 编译日期：2022-04-30 17:22:29
 * Github：https://github.com/muyao1987/CesiumVectorTile/
 * 
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e(require("mars3d-cesium"),require("@turf/turf")):"function"==typeof define&&define.amd?define("CesiumVectorTile",["mars3d-cesium","@turf/turf"],e):"object"==typeof exports?exports.CesiumVectorTile=e(require("mars3d-cesium"),require("@turf/turf")):t.CesiumVectorTile=e(t.Cesium,t.turf)}(window,(function(t,e){return function(t){var e={};function i(r){if(e[r])return e[r].exports;var n=e[r]={i:r,l:!1,exports:{}};return t[r].call(n.exports,n,n.exports,i),n.l=!0,n.exports}return i.m=t,i.c=e,i.d=function(t,e,r){i.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},i.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},i.t=function(t,e){if(1&e&&(t=i(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(i.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)i.d(r,n,function(e){return t[e]}.bind(null,n));return r},i.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return i.d(e,"a",e),e},i.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},i.p="",i(i.s=64)}([function(e,i){e.exports=t},function(t,i){t.exports=e},function(t,e,i){"use strict";var r=i(4),n=i(10),s=i(12);function a(t){return t}function o(t,e){for(var i=0;i<t.length;++i)e[i]=255&t.charCodeAt(i);return e}function h(t){var i=65536,r=[],n=t.length,a=e.getTypeOf(t),o=0,h=!0;try{switch(a){case"uint8array":String.fromCharCode.apply(null,new Uint8Array(0));break;case"nodebuffer":String.fromCharCode.apply(null,s(0))}}catch(t){h=!1}if(!h){for(var l="",u=0;u<t.length;u++)l+=String.fromCharCode(t[u]);return l}for(;o<n&&i>1;)try{"array"===a||"nodebuffer"===a?r.push(String.fromCharCode.apply(null,t.slice(o,Math.min(o+i,n)))):r.push(String.fromCharCode.apply(null,t.subarray(o,Math.min(o+i,n)))),o+=i}catch(t){i=Math.floor(i/2)}return r.join("")}function l(t,e){for(var i=0;i<t.length;i++)e[i]=t[i];return e}e.string2binary=function(t){for(var e="",i=0;i<t.length;i++)e+=String.fromCharCode(255&t.charCodeAt(i));return e},e.arrayBuffer2Blob=function(t,i){e.checkSupport("blob"),i=i||"application/zip";try{return new Blob([t],{type:i})}catch(e){try{var r=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder);return r.append(t),r.getBlob(i)}catch(t){throw new Error("Bug : can't construct the Blob.")}}},e.applyFromCharCode=h;var u={};u.string={string:a,array:function(t){return o(t,new Array(t.length))},arraybuffer:function(t){return u.string.uint8array(t).buffer},uint8array:function(t){return o(t,new Uint8Array(t.length))},nodebuffer:function(t){return o(t,s(t.length))}},u.array={string:h,array:a,arraybuffer:function(t){return new Uint8Array(t).buffer},uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s(t)}},u.arraybuffer={string:function(t){return h(new Uint8Array(t))},array:function(t){return l(new Uint8Array(t),new Array(t.byteLength))},arraybuffer:a,uint8array:function(t){return new Uint8Array(t)},nodebuffer:function(t){return s(new Uint8Array(t))}},u.uint8array={string:h,array:function(t){return l(t,new Array(t.length))},arraybuffer:function(t){return t.buffer},uint8array:a,nodebuffer:function(t){return s(t)}},u.nodebuffer={string:h,array:function(t){return l(t,new Array(t.length))},arraybuffer:function(t){return u.nodebuffer.uint8array(t).buffer},uint8array:function(t){return l(t,new Uint8Array(t.length))},nodebuffer:a},e.transformTo=function(t,i){if(i||(i=""),!t)return i;e.checkSupport(t);var r=e.getTypeOf(i);return u[r][t](i)},e.getTypeOf=function(t){return"string"==typeof t?"string":"[object Array]"===Object.prototype.toString.call(t)?"array":r.nodebuffer&&s.test(t)?"nodebuffer":r.uint8array&&t instanceof Uint8Array?"uint8array":r.arraybuffer&&t instanceof ArrayBuffer?"arraybuffer":void 0},e.checkSupport=function(t){if(!r[t.toLowerCase()])throw new Error(t+" is not supported by this browser")},e.MAX_VALUE_16BITS=65535,e.MAX_VALUE_32BITS=-1,e.pretty=function(t){var e,i,r="";for(i=0;i<(t||"").length;i++)r+="\\x"+((e=t.charCodeAt(i))<16?"0":"")+e.toString(16).toUpperCase();return r},e.findCompression=function(t){for(var e in n)if(n.hasOwnProperty(e)&&n[e].magic===t)return n[e];return null},e.isRegExp=function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},e.extend=function(){var t,e,i={};for(t=0;t<arguments.length;t++)for(e in arguments[t])arguments[t].hasOwnProperty(e)&&void 0===i[e]&&(i[e]=arguments[t][e]);return i}},function(t,e,i){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function n(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.assign=function(t){for(var e=Array.prototype.slice.call(arguments,1);e.length;){var i=e.shift();if(i){if("object"!=typeof i)throw new TypeError(i+"must be non-object");for(var r in i)n(i,r)&&(t[r]=i[r])}}return t},e.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var s={arraySet:function(t,e,i,r,n){if(e.subarray&&t.subarray)t.set(e.subarray(i,i+r),n);else for(var s=0;s<r;s++)t[n+s]=e[i+s]},flattenChunks:function(t){var e,i,r,n,s,a;for(r=0,e=0,i=t.length;e<i;e++)r+=t[e].length;for(a=new Uint8Array(r),n=0,e=0,i=t.length;e<i;e++)s=t[e],a.set(s,n),n+=s.length;return a}},a={arraySet:function(t,e,i,r,n){for(var s=0;s<r;s++)t[n+s]=e[i+s]},flattenChunks:function(t){return[].concat.apply([],t)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,s)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,a))},e.setTyped(r)},function(t,e,i){"use strict";(function(t){if(e.base64=!0,e.array=!0,e.string=!0,e.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,e.nodebuffer=void 0!==t,e.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)e.blob=!1;else{var i=new ArrayBuffer(0);try{e.blob=0===new Blob([i],{type:"application/zip"}).size}catch(t){try{var r=new(window.BlobBuilder||window.WebKitBlobBuilder||window.MozBlobBuilder||window.MSBlobBuilder);r.append(i),e.blob=0===r.getBlob("application/zip").size}catch(t){e.blob=!1}}}}).call(this,i(5).Buffer)},function(t,e,i){"use strict";(function(t){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <http://feross.org>
 * @license  MIT
 */
var r=i(30),n=i(31),s=i(32);function a(){return h.TYPED_ARRAY_SUPPORT?**********:**********}function o(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return h.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=h.prototype:(null===t&&(t=new h(e)),t.length=e),t}function h(t,e,i){if(!(h.TYPED_ARRAY_SUPPORT||this instanceof h))return new h(t,e,i);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return f(this,t)}return l(this,t,e,i)}function l(t,e,i,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,i,r){if(e.byteLength,i<0||e.byteLength<i)throw new RangeError("'offset' is out of bounds");if(e.byteLength<i+(r||0))throw new RangeError("'length' is out of bounds");e=void 0===i&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,i):new Uint8Array(e,i,r);h.TYPED_ARRAY_SUPPORT?(t=e).__proto__=h.prototype:t=c(t,e);return t}(t,e,i,r):"string"==typeof e?function(t,e,i){"string"==typeof i&&""!==i||(i="utf8");if(!h.isEncoding(i))throw new TypeError('"encoding" must be a valid string encoding');var r=0|p(e,i),n=(t=o(t,r)).write(e,i);n!==r&&(t=t.slice(0,n));return t}(t,e,i):function(t,e){if(h.isBuffer(e)){var i=0|d(e.length);return 0===(t=o(t,i)).length||e.copy(t,0,0,i),t}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||(r=e.length)!=r?o(t,0):c(t,e);if("Buffer"===e.type&&s(e.data))return c(t,e.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function u(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function f(t,e){if(u(e),t=o(t,e<0?0:0|d(e)),!h.TYPED_ARRAY_SUPPORT)for(var i=0;i<e;++i)t[i]=0;return t}function c(t,e){var i=e.length<0?0:0|d(e.length);t=o(t,i);for(var r=0;r<i;r+=1)t[r]=255&e[r];return t}function d(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function p(t,e){if(h.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var i=t.length;if(0===i)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return i;case"utf8":case"utf-8":case void 0:return U(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*i;case"hex":return i>>>1;case"base64":return F(t).length;default:if(r)return U(t).length;e=(""+e).toLowerCase(),r=!0}}function g(t,e,i){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===i||i>this.length)&&(i=this.length),i<=0)return"";if((i>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return P(this,e,i);case"utf8":case"utf-8":return k(this,e,i);case"ascii":return E(this,e,i);case"latin1":case"binary":return A(this,e,i);case"base64":return S(this,e,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,e,i);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function m(t,e,i){var r=t[e];t[e]=t[i],t[i]=r}function _(t,e,i,r,n){if(0===t.length)return-1;if("string"==typeof i?(r=i,i=0):i>**********?i=**********:i<-2147483648&&(i=-2147483648),i=+i,isNaN(i)&&(i=n?0:t.length-1),i<0&&(i=t.length+i),i>=t.length){if(n)return-1;i=t.length-1}else if(i<0){if(!n)return-1;i=0}if("string"==typeof e&&(e=h.from(e,r)),h.isBuffer(e))return 0===e.length?-1:y(t,e,i,r,n);if("number"==typeof e)return e&=255,h.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?n?Uint8Array.prototype.indexOf.call(t,e,i):Uint8Array.prototype.lastIndexOf.call(t,e,i):y(t,[e],i,r,n);throw new TypeError("val must be string, number or Buffer")}function y(t,e,i,r,n){var s,a=1,o=t.length,h=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;a=2,o/=2,h/=2,i/=2}function l(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(n){var u=-1;for(s=i;s<o;s++)if(l(t,s)===l(e,-1===u?0:s-u)){if(-1===u&&(u=s),s-u+1===h)return u*a}else-1!==u&&(s-=s-u),u=-1}else for(i+h>o&&(i=o-h),s=i;s>=0;s--){for(var f=!0,c=0;c<h;c++)if(l(t,s+c)!==l(e,c)){f=!1;break}if(f)return s}return-1}function v(t,e,i,r){i=Number(i)||0;var n=t.length-i;r?(r=Number(r))>n&&(r=n):r=n;var s=e.length;if(s%2!=0)throw new TypeError("Invalid hex string");r>s/2&&(r=s/2);for(var a=0;a<r;++a){var o=parseInt(e.substr(2*a,2),16);if(isNaN(o))return a;t[i+a]=o}return a}function b(t,e,i,r){return q(U(e,t.length-i),t,i,r)}function M(t,e,i,r){return q(function(t){for(var e=[],i=0;i<t.length;++i)e.push(255&t.charCodeAt(i));return e}(e),t,i,r)}function w(t,e,i,r){return M(t,e,i,r)}function x(t,e,i,r){return q(F(e),t,i,r)}function C(t,e,i,r){return q(function(t,e){for(var i,r,n,s=[],a=0;a<t.length&&!((e-=2)<0);++a)i=t.charCodeAt(a),r=i>>8,n=i%256,s.push(n),s.push(r);return s}(e,t.length-i),t,i,r)}function S(t,e,i){return 0===e&&i===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,i))}function k(t,e,i){i=Math.min(t.length,i);for(var r=[],n=e;n<i;){var s,a,o,h,l=t[n],u=null,f=l>239?4:l>223?3:l>191?2:1;if(n+f<=i)switch(f){case 1:l<128&&(u=l);break;case 2:128==(192&(s=t[n+1]))&&(h=(31&l)<<6|63&s)>127&&(u=h);break;case 3:s=t[n+1],a=t[n+2],128==(192&s)&&128==(192&a)&&(h=(15&l)<<12|(63&s)<<6|63&a)>2047&&(h<55296||h>57343)&&(u=h);break;case 4:s=t[n+1],a=t[n+2],o=t[n+3],128==(192&s)&&128==(192&a)&&128==(192&o)&&(h=(15&l)<<18|(63&s)<<12|(63&a)<<6|63&o)>65535&&h<1114112&&(u=h)}null===u?(u=65533,f=1):u>65535&&(u-=65536,r.push(u>>>10&1023|55296),u=56320|1023&u),r.push(u),n+=f}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var i="",r=0;for(;r<e;)i+=String.fromCharCode.apply(String,t.slice(r,r+=4096));return i}(r)}e.Buffer=h,e.SlowBuffer=function(t){+t!=t&&(t=0);return h.alloc(+t)},e.INSPECT_MAX_BYTES=50,h.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=a(),h.poolSize=8192,h._augment=function(t){return t.__proto__=h.prototype,t},h.from=function(t,e,i){return l(null,t,e,i)},h.TYPED_ARRAY_SUPPORT&&(h.prototype.__proto__=Uint8Array.prototype,h.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&h[Symbol.species]===h&&Object.defineProperty(h,Symbol.species,{value:null,configurable:!0})),h.alloc=function(t,e,i){return function(t,e,i,r){return u(e),e<=0?o(t,e):void 0!==i?"string"==typeof r?o(t,e).fill(i,r):o(t,e).fill(i):o(t,e)}(null,t,e,i)},h.allocUnsafe=function(t){return f(null,t)},h.allocUnsafeSlow=function(t){return f(null,t)},h.isBuffer=function(t){return!(null==t||!t._isBuffer)},h.compare=function(t,e){if(!h.isBuffer(t)||!h.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var i=t.length,r=e.length,n=0,s=Math.min(i,r);n<s;++n)if(t[n]!==e[n]){i=t[n],r=e[n];break}return i<r?-1:r<i?1:0},h.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},h.concat=function(t,e){if(!s(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return h.alloc(0);var i;if(void 0===e)for(e=0,i=0;i<t.length;++i)e+=t[i].length;var r=h.allocUnsafe(e),n=0;for(i=0;i<t.length;++i){var a=t[i];if(!h.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,n),n+=a.length}return r},h.byteLength=p,h.prototype._isBuffer=!0,h.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)m(this,e,e+1);return this},h.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)m(this,e,e+3),m(this,e+1,e+2);return this},h.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)m(this,e,e+7),m(this,e+1,e+6),m(this,e+2,e+5),m(this,e+3,e+4);return this},h.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?k(this,0,t):g.apply(this,arguments)},h.prototype.equals=function(t){if(!h.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===h.compare(this,t)},h.prototype.inspect=function(){var t="",i=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,i).match(/.{2}/g).join(" "),this.length>i&&(t+=" ... ")),"<Buffer "+t+">"},h.prototype.compare=function(t,e,i,r,n){if(!h.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===i&&(i=t?t.length:0),void 0===r&&(r=0),void 0===n&&(n=this.length),e<0||i>t.length||r<0||n>this.length)throw new RangeError("out of range index");if(r>=n&&e>=i)return 0;if(r>=n)return-1;if(e>=i)return 1;if(this===t)return 0;for(var s=(n>>>=0)-(r>>>=0),a=(i>>>=0)-(e>>>=0),o=Math.min(s,a),l=this.slice(r,n),u=t.slice(e,i),f=0;f<o;++f)if(l[f]!==u[f]){s=l[f],a=u[f];break}return s<a?-1:a<s?1:0},h.prototype.includes=function(t,e,i){return-1!==this.indexOf(t,e,i)},h.prototype.indexOf=function(t,e,i){return _(this,t,e,i,!0)},h.prototype.lastIndexOf=function(t,e,i){return _(this,t,e,i,!1)},h.prototype.write=function(t,e,i,r){if(void 0===e)r="utf8",i=this.length,e=0;else if(void 0===i&&"string"==typeof e)r=e,i=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(i)?(i|=0,void 0===r&&(r="utf8")):(r=i,i=void 0)}var n=this.length-e;if((void 0===i||i>n)&&(i=n),t.length>0&&(i<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var s=!1;;)switch(r){case"hex":return v(this,t,e,i);case"utf8":case"utf-8":return b(this,t,e,i);case"ascii":return M(this,t,e,i);case"latin1":case"binary":return w(this,t,e,i);case"base64":return x(this,t,e,i);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,t,e,i);default:if(s)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),s=!0}},h.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function E(t,e,i){var r="";i=Math.min(t.length,i);for(var n=e;n<i;++n)r+=String.fromCharCode(127&t[n]);return r}function A(t,e,i){var r="";i=Math.min(t.length,i);for(var n=e;n<i;++n)r+=String.fromCharCode(t[n]);return r}function P(t,e,i){var r=t.length;(!e||e<0)&&(e=0),(!i||i<0||i>r)&&(i=r);for(var n="",s=e;s<i;++s)n+=j(t[s]);return n}function I(t,e,i){for(var r=t.slice(e,i),n="",s=0;s<r.length;s+=2)n+=String.fromCharCode(r[s]+256*r[s+1]);return n}function O(t,e,i){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>i)throw new RangeError("Trying to access beyond buffer length")}function N(t,e,i,r,n,s){if(!h.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>n||e<s)throw new RangeError('"value" argument is out of bounds');if(i+r>t.length)throw new RangeError("Index out of range")}function L(t,e,i,r){e<0&&(e=65535+e+1);for(var n=0,s=Math.min(t.length-i,2);n<s;++n)t[i+n]=(e&255<<8*(r?n:1-n))>>>8*(r?n:1-n)}function T(t,e,i,r){e<0&&(e=4294967295+e+1);for(var n=0,s=Math.min(t.length-i,4);n<s;++n)t[i+n]=e>>>8*(r?n:3-n)&255}function R(t,e,i,r,n,s){if(i+r>t.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("Index out of range")}function z(t,e,i,r,s){return s||R(t,0,i,4),n.write(t,e,i,r,23,4),i+4}function B(t,e,i,r,s){return s||R(t,0,i,8),n.write(t,e,i,r,52,8),i+8}h.prototype.slice=function(t,e){var i,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t),h.TYPED_ARRAY_SUPPORT)(i=this.subarray(t,e)).__proto__=h.prototype;else{var n=e-t;i=new h(n,void 0);for(var s=0;s<n;++s)i[s]=this[s+t]}return i},h.prototype.readUIntLE=function(t,e,i){t|=0,e|=0,i||O(t,e,this.length);for(var r=this[t],n=1,s=0;++s<e&&(n*=256);)r+=this[t+s]*n;return r},h.prototype.readUIntBE=function(t,e,i){t|=0,e|=0,i||O(t,e,this.length);for(var r=this[t+--e],n=1;e>0&&(n*=256);)r+=this[t+--e]*n;return r},h.prototype.readUInt8=function(t,e){return e||O(t,1,this.length),this[t]},h.prototype.readUInt16LE=function(t,e){return e||O(t,2,this.length),this[t]|this[t+1]<<8},h.prototype.readUInt16BE=function(t,e){return e||O(t,2,this.length),this[t]<<8|this[t+1]},h.prototype.readUInt32LE=function(t,e){return e||O(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},h.prototype.readUInt32BE=function(t,e){return e||O(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},h.prototype.readIntLE=function(t,e,i){t|=0,e|=0,i||O(t,e,this.length);for(var r=this[t],n=1,s=0;++s<e&&(n*=256);)r+=this[t+s]*n;return r>=(n*=128)&&(r-=Math.pow(2,8*e)),r},h.prototype.readIntBE=function(t,e,i){t|=0,e|=0,i||O(t,e,this.length);for(var r=e,n=1,s=this[t+--r];r>0&&(n*=256);)s+=this[t+--r]*n;return s>=(n*=128)&&(s-=Math.pow(2,8*e)),s},h.prototype.readInt8=function(t,e){return e||O(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},h.prototype.readInt16LE=function(t,e){e||O(t,2,this.length);var i=this[t]|this[t+1]<<8;return 32768&i?4294901760|i:i},h.prototype.readInt16BE=function(t,e){e||O(t,2,this.length);var i=this[t+1]|this[t]<<8;return 32768&i?4294901760|i:i},h.prototype.readInt32LE=function(t,e){return e||O(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},h.prototype.readInt32BE=function(t,e){return e||O(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},h.prototype.readFloatLE=function(t,e){return e||O(t,4,this.length),n.read(this,t,!0,23,4)},h.prototype.readFloatBE=function(t,e){return e||O(t,4,this.length),n.read(this,t,!1,23,4)},h.prototype.readDoubleLE=function(t,e){return e||O(t,8,this.length),n.read(this,t,!0,52,8)},h.prototype.readDoubleBE=function(t,e){return e||O(t,8,this.length),n.read(this,t,!1,52,8)},h.prototype.writeUIntLE=function(t,e,i,r){(t=+t,e|=0,i|=0,r)||N(this,t,e,i,Math.pow(2,8*i)-1,0);var n=1,s=0;for(this[e]=255&t;++s<i&&(n*=256);)this[e+s]=t/n&255;return e+i},h.prototype.writeUIntBE=function(t,e,i,r){(t=+t,e|=0,i|=0,r)||N(this,t,e,i,Math.pow(2,8*i)-1,0);var n=i-1,s=1;for(this[e+n]=255&t;--n>=0&&(s*=256);)this[e+n]=t/s&255;return e+i},h.prototype.writeUInt8=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,1,255,0),h.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},h.prototype.writeUInt16LE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,2,65535,0),h.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):L(this,t,e,!0),e+2},h.prototype.writeUInt16BE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,2,65535,0),h.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):L(this,t,e,!1),e+2},h.prototype.writeUInt32LE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,4,4294967295,0),h.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):T(this,t,e,!0),e+4},h.prototype.writeUInt32BE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,4,4294967295,0),h.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):T(this,t,e,!1),e+4},h.prototype.writeIntLE=function(t,e,i,r){if(t=+t,e|=0,!r){var n=Math.pow(2,8*i-1);N(this,t,e,i,n-1,-n)}var s=0,a=1,o=0;for(this[e]=255&t;++s<i&&(a*=256);)t<0&&0===o&&0!==this[e+s-1]&&(o=1),this[e+s]=(t/a>>0)-o&255;return e+i},h.prototype.writeIntBE=function(t,e,i,r){if(t=+t,e|=0,!r){var n=Math.pow(2,8*i-1);N(this,t,e,i,n-1,-n)}var s=i-1,a=1,o=0;for(this[e+s]=255&t;--s>=0&&(a*=256);)t<0&&0===o&&0!==this[e+s+1]&&(o=1),this[e+s]=(t/a>>0)-o&255;return e+i},h.prototype.writeInt8=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,1,127,-128),h.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},h.prototype.writeInt16LE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,2,32767,-32768),h.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):L(this,t,e,!0),e+2},h.prototype.writeInt16BE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,2,32767,-32768),h.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):L(this,t,e,!1),e+2},h.prototype.writeInt32LE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,4,**********,-2147483648),h.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):T(this,t,e,!0),e+4},h.prototype.writeInt32BE=function(t,e,i){return t=+t,e|=0,i||N(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),h.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):T(this,t,e,!1),e+4},h.prototype.writeFloatLE=function(t,e,i){return z(this,t,e,!0,i)},h.prototype.writeFloatBE=function(t,e,i){return z(this,t,e,!1,i)},h.prototype.writeDoubleLE=function(t,e,i){return B(this,t,e,!0,i)},h.prototype.writeDoubleBE=function(t,e,i){return B(this,t,e,!1,i)},h.prototype.copy=function(t,e,i,r){if(i||(i=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<i&&(r=i),r===i)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(i<0||i>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-i&&(r=t.length-e+i);var n,s=r-i;if(this===t&&i<e&&e<r)for(n=s-1;n>=0;--n)t[n+e]=this[n+i];else if(s<1e3||!h.TYPED_ARRAY_SUPPORT)for(n=0;n<s;++n)t[n+e]=this[n+i];else Uint8Array.prototype.set.call(t,this.subarray(i,i+s),e);return s},h.prototype.fill=function(t,e,i,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,i=this.length):"string"==typeof i&&(r=i,i=this.length),1===t.length){var n=t.charCodeAt(0);n<256&&(t=n)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!h.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<i)throw new RangeError("Out of range index");if(i<=e)return this;var s;if(e>>>=0,i=void 0===i?this.length:i>>>0,t||(t=0),"number"==typeof t)for(s=e;s<i;++s)this[s]=t;else{var a=h.isBuffer(t)?t:U(new h(t,r).toString()),o=a.length;for(s=0;s<i-e;++s)this[s+e]=a[s%o]}return this};var D=/[^+\/0-9A-Za-z-_]/g;function j(t){return t<16?"0"+t.toString(16):t.toString(16)}function U(t,e){var i;e=e||1/0;for(var r=t.length,n=null,s=[],a=0;a<r;++a){if((i=t.charCodeAt(a))>55295&&i<57344){if(!n){if(i>56319){(e-=3)>-1&&s.push(239,191,189);continue}if(a+1===r){(e-=3)>-1&&s.push(239,191,189);continue}n=i;continue}if(i<56320){(e-=3)>-1&&s.push(239,191,189),n=i;continue}i=65536+(n-55296<<10|i-56320)}else n&&(e-=3)>-1&&s.push(239,191,189);if(n=null,i<128){if((e-=1)<0)break;s.push(i)}else if(i<2048){if((e-=2)<0)break;s.push(i>>6|192,63&i|128)}else if(i<65536){if((e-=3)<0)break;s.push(i>>12|224,i>>6&63|128,63&i|128)}else{if(!(i<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;s.push(i>>18|240,i>>12&63|128,i>>6&63|128,63&i|128)}}return s}function F(t){return r.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(D,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function q(t,e,i,r){for(var n=0;n<r&&!(n+i>=e.length||n>=t.length);++n)e[n+i]=t[n];return n}}).call(this,i(7))},function(t,e,i){"use strict";(function(e){var r=i(63);r.default&&(r=r.default);var n=i(28),s=i(51),a=i(54),o=i(55),h=i(26),l=i(62),u=i(5).Buffer,f=new l({max:20});function c(t){if(!t)throw new Error("forgot to pass buffer");return u.isBuffer(t)?t:t instanceof e.ArrayBuffer?u.from(t):t.buffer instanceof e.ArrayBuffer?1===t.BYTES_PER_ELEMENT?u.from(t):u.from(t.buffer):void 0}function d(t,e){return"string"==typeof t&&f.has(t)?h.resolve(f.get(t)):d.getShapefile(t,e).then((function(e){return"string"==typeof t&&f.set(t,e),e}))}d.combine=function(t){for(var e={type:"FeatureCollection",features:[]},i=0,r=t[0].length;i<r;)e.features.push({type:"Feature",geometry:t[0][i],properties:t[1][i]}),i++;return e},d.parseZip=function(t,e){var i;t=c(t);var s=n(t),h=[];for(i in e=e||[],s)-1===i.indexOf("__MACOSX")&&("shp"===i.slice(-3).toLowerCase()?(h.push(i.slice(0,-4)),s[i.slice(0,-3)+i.slice(-3).toLowerCase()]=s[i]):"prj"===i.slice(-3).toLowerCase()?s[i.slice(0,-3)+i.slice(-3).toLowerCase()]=r(s[i]):"json"===i.slice(-4).toLowerCase()||e.indexOf(i.split(".").pop())>-1?h.push(i.slice(0,-3)+i.slice(-3).toLowerCase()):"dbf"!==i.slice(-3).toLowerCase()&&"cpg"!==i.slice(-3).toLowerCase()||(s[i.slice(0,-3)+i.slice(-3).toLowerCase()]=s[i]));if(!h.length)throw new Error("no layers founds");var l=h.map((function(t){var i,r,n=t.lastIndexOf(".");return n>-1&&t.slice(n).indexOf("json")>-1?(i=JSON.parse(s[t])).fileName=t.slice(0,n):e.indexOf(t.slice(n+1))>-1?(i=s[t]).fileName=t:(s[t+".dbf"]&&(r=o(s[t+".dbf"],s[t+".cpg"])),(i=d.combine([a(s[t+".shp"],s[t+".prj"]),r])).fileName=t),i}));return 1===l.length?l[0]:l},d.getShapefile=function(t,e){return"string"==typeof t?".zip"===t.slice(-4).toLowerCase()?function(t,e){return s(t).then((function(t){return d.parseZip(t,e)}))}(t,e):h.all([h.all([s(t+".shp"),s(t+".prj")]).then((function(t){var e=!1;try{t[1]&&(e=r(t[1]))}catch(t){e=!1}return a(t[0],e)})),h.all([s(t+".dbf"),s(t+".cpg")]).then((function(t){return o(t[0],t[1])}))]).then(d.combine):new h((function(e){e(d.parseZip(t))}))},d.parseShp=function(t,e){if(t=c(t),u.isBuffer(e)&&(e=e.toString()),"string"==typeof e)try{e=r(e)}catch(t){e=!1}return a(t,e)},d.parseDbf=function(t,e){return t=c(t),o(t,e)},t.exports=d}).call(this,i(7))},function(t,e){var i;i=function(){return this}();try{i=i||new Function("return this")()}catch(t){"object"==typeof window&&(i=window)}t.exports=i},function(t,e,i){"use strict";var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";e.encode=function(t,e){for(var i,n,s,a,o,h,l,u="",f=0;f<t.length;)a=(i=t.charCodeAt(f++))>>2,o=(3&i)<<4|(n=t.charCodeAt(f++))>>4,h=(15&n)<<2|(s=t.charCodeAt(f++))>>6,l=63&s,isNaN(n)?h=l=64:isNaN(s)&&(l=64),u=u+r.charAt(a)+r.charAt(o)+r.charAt(h)+r.charAt(l);return u},e.decode=function(t,e){var i,n,s,a,o,h,l="",u=0;for(t=t.replace(/[^A-Za-z0-9\+\/\=]/g,"");u<t.length;)i=r.indexOf(t.charAt(u++))<<2|(a=r.indexOf(t.charAt(u++)))>>4,n=(15&a)<<4|(o=r.indexOf(t.charAt(u++)))>>2,s=(3&o)<<6|(h=r.indexOf(t.charAt(u++))),l+=String.fromCharCode(i),64!=o&&(l+=String.fromCharCode(n)),64!=h&&(l+=String.fromCharCode(s));return l}},function(t,e,i){"use strict";var r=i(4),n=i(2),s=i(43),a=i(18),o=i(19),h=i(8),l=i(10),u=i(20),f=i(12),c=i(21),d=i(44),p=i(45),g=function(t){if(t._data instanceof u&&(t._data=t._data.getContent(),t.options.binary=!0,t.options.base64=!1,"uint8array"===n.getTypeOf(t._data))){var e=t._data;t._data=new Uint8Array(e.length),0!==e.length&&t._data.set(e,0)}return t._data},m=function(t){var e=g(t);return"string"===n.getTypeOf(e)?!t.options.binary&&r.nodebuffer?f(e,"utf-8"):t.asBinary():e},_=function(t){var e=g(this);return null==e?"":(this.options.base64&&(e=h.decode(e)),e=t&&this.options.binary?k.utf8decode(e):n.transformTo("string",e),t||this.options.binary||(e=n.transformTo("string",k.utf8encode(e))),e)},y=function(t,e,i){this.name=t,this.dir=i.dir,this.date=i.date,this.comment=i.comment,this.unixPermissions=i.unixPermissions,this.dosPermissions=i.dosPermissions,this._data=e,this.options=i,this._initialMetadata={dir:i.dir,date:i.date}};y.prototype={asText:function(){return _.call(this,!0)},asBinary:function(){return _.call(this,!1)},asNodeBuffer:function(){var t=m(this);return n.transformTo("nodebuffer",t)},asUint8Array:function(){var t=m(this);return n.transformTo("uint8array",t)},asArrayBuffer:function(){return this.asUint8Array().buffer}};var v=function(t,e){var i,r="";for(i=0;i<e;i++)r+=String.fromCharCode(255&t),t>>>=8;return r},b=function(t,e,i){var r,s=n.getTypeOf(e);if("string"==typeof(i=function(t){return!0!==(t=t||{}).base64||null!==t.binary&&void 0!==t.binary||(t.binary=!0),(t=n.extend(t,o)).date=t.date||new Date,null!==t.compression&&(t.compression=t.compression.toUpperCase()),t}(i)).unixPermissions&&(i.unixPermissions=parseInt(i.unixPermissions,8)),i.unixPermissions&&16384&i.unixPermissions&&(i.dir=!0),i.dosPermissions&&16&i.dosPermissions&&(i.dir=!0),i.dir&&(t=w(t)),i.createFolders&&(r=M(t))&&x.call(this,r,!0),i.dir||null==e)i.base64=!1,i.binary=!1,e=null,s=null;else if("string"===s)i.binary&&!i.base64&&!0!==i.optimizedBinaryString&&(e=n.string2binary(e));else{if(i.base64=!1,i.binary=!0,!(s||e instanceof u))throw new Error("The data of '"+t+"' is in an unsupported format !");"arraybuffer"===s&&(e=n.transformTo("uint8array",e))}var a=new y(t,e,i);return this.files[t]=a,a},M=function(t){"/"==t.slice(-1)&&(t=t.substring(0,t.length-1));var e=t.lastIndexOf("/");return e>0?t.substring(0,e):""},w=function(t){return"/"!=t.slice(-1)&&(t+="/"),t},x=function(t,e){return e=void 0!==e&&e,t=w(t),this.files[t]||b.call(this,t,null,{dir:!0,createFolders:e}),this.files[t]},C=function(t,e,i){var r,a=new u;return t._data instanceof u?(a.uncompressedSize=t._data.uncompressedSize,a.crc32=t._data.crc32,0===a.uncompressedSize||t.dir?(e=l.STORE,a.compressedContent="",a.crc32=0):t._data.compressionMethod===e.magic?a.compressedContent=t._data.getCompressedContent():(r=t._data.getContent(),a.compressedContent=e.compress(n.transformTo(e.compressInputType,r),i))):((r=m(t))&&0!==r.length&&!t.dir||(e=l.STORE,r=""),a.uncompressedSize=r.length,a.crc32=s(r),a.compressedContent=e.compress(n.transformTo(e.compressInputType,r),i)),a.compressedSize=a.compressedContent.length,a.compressionMethod=e.magic,a},S=function(t,e,i,r,o,h){i.compressedContent;var l,u,f,d,p=h!==c.utf8encode,g=n.transformTo("string",h(e.name)),m=n.transformTo("string",c.utf8encode(e.name)),_=e.comment||"",y=n.transformTo("string",h(_)),b=n.transformTo("string",c.utf8encode(_)),M=m.length!==e.name.length,w=b.length!==_.length,x=e.options,C="",S="",k="";f=e._initialMetadata.dir!==e.dir?e.dir:x.dir,d=e._initialMetadata.date!==e.date?e.date:x.date;var E,A,P,I=0,O=0;f&&(I|=16),"UNIX"===o?(O=798,I|=(E=e.unixPermissions,A=f,P=E,E||(P=A?16893:33204),(65535&P)<<16)):(O=20,I|=63&(e.dosPermissions||0)),l=d.getHours(),l<<=6,l|=d.getMinutes(),l<<=5,l|=d.getSeconds()/2,u=d.getFullYear()-1980,u<<=4,u|=d.getMonth()+1,u<<=5,u|=d.getDate(),M&&(S=v(1,1)+v(s(g),4)+m,C+="up"+v(S.length,2)+S),w&&(k=v(1,1)+v(this.crc32(y),4)+b,C+="uc"+v(k.length,2)+k);var N="";return N+="\n\0",N+=p||!M&&!w?"\0\0":"\0\b",N+=i.compressionMethod,N+=v(l,2),N+=v(u,2),N+=v(i.crc32,4),N+=v(i.compressedSize,4),N+=v(i.uncompressedSize,4),N+=v(g.length,2),N+=v(C.length,2),{fileRecord:a.LOCAL_FILE_HEADER+N+g+C,dirRecord:a.CENTRAL_FILE_HEADER+v(O,2)+N+v(y.length,2)+"\0\0\0\0"+v(I,4)+v(r,4)+g+C+y,compressedObject:i}},k={load:function(t,e){throw new Error("Load method is not defined. Is the file jszip-load.js included ?")},filter:function(t){var e,i,r,s,a=[];for(e in this.files)this.files.hasOwnProperty(e)&&(r=this.files[e],s=new y(r.name,r._data,n.extend(r.options)),i=e.slice(this.root.length,e.length),e.slice(0,this.root.length)===this.root&&t(i,s)&&a.push(s));return a},file:function(t,e,i){if(1===arguments.length){if(n.isRegExp(t)){var r=t;return this.filter((function(t,e){return!e.dir&&r.test(t)}))}return this.filter((function(e,i){return!i.dir&&e===t}))[0]||null}return t=this.root+t,b.call(this,t,e,i),this},folder:function(t){if(!t)return this;if(n.isRegExp(t))return this.filter((function(e,i){return i.dir&&t.test(e)}));var e=this.root+t,i=x.call(this,e),r=this.clone();return r.root=i.name,r},remove:function(t){t=this.root+t;var e=this.files[t];if(e||("/"!=t.slice(-1)&&(t+="/"),e=this.files[t]),e&&!e.dir)delete this.files[t];else for(var i=this.filter((function(e,i){return i.name.slice(0,t.length)===t})),r=0;r<i.length;r++)delete this.files[i[r].name];return this},generate:function(t){t=n.extend(t||{},{base64:!0,compression:"STORE",compressionOptions:null,type:"base64",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:c.utf8encode}),n.checkSupport(t.type),"darwin"!==t.platform&&"freebsd"!==t.platform&&"linux"!==t.platform&&"sunos"!==t.platform||(t.platform="UNIX"),"win32"===t.platform&&(t.platform="DOS");var e,i,r=[],s=0,o=0,u=n.transformTo("string",t.encodeFileName(t.comment||this.comment||""));for(var f in this.files)if(this.files.hasOwnProperty(f)){var g=this.files[f],m=g.options.compression||t.compression.toUpperCase(),_=l[m];if(!_)throw new Error(m+" is not a valid compression method !");var y=g.options.compressionOptions||t.compressionOptions||{},b=C.call(this,g,_,y),M=S.call(this,f,g,b,s,t.platform,t.encodeFileName);s+=M.fileRecord.length+b.compressedSize,o+=M.dirRecord.length,r.push(M)}var w;w=a.CENTRAL_DIRECTORY_END+"\0\0\0\0"+v(r.length,2)+v(r.length,2)+v(o,4)+v(s,4)+v(u.length,2)+u;var x=t.type.toLowerCase();for(e="uint8array"===x||"arraybuffer"===x||"blob"===x||"nodebuffer"===x?new p(s+o+w.length):new d(s+o+w.length),i=0;i<r.length;i++)e.append(r[i].fileRecord),e.append(r[i].compressedObject.compressedContent);for(i=0;i<r.length;i++)e.append(r[i].dirRecord);e.append(w);var k=e.finalize();switch(t.type.toLowerCase()){case"uint8array":case"arraybuffer":case"nodebuffer":return n.transformTo(t.type.toLowerCase(),k);case"blob":return n.arrayBuffer2Blob(n.transformTo("arraybuffer",k),t.mimeType);case"base64":return t.base64?h.encode(k):k;default:return k}},crc32:function(t,e){return s(t,e)},utf8encode:function(t){return n.transformTo("string",c.utf8encode(t))},utf8decode:function(t){return c.utf8decode(t)}};t.exports=k},function(t,e,i){"use strict";e.STORE={magic:"\0\0",compress:function(t,e){return t},uncompress:function(t){return t},compressInputType:null,uncompressInputType:null},e.DEFLATE=i(33)},function(t,e,i){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},function(t,e,i){"use strict";(function(e){t.exports=function(t,i){return new e(t,i)},t.exports.test=function(t){return e.isBuffer(t)}}).call(this,i(5).Buffer)},function(t,e,i){"use strict";t.exports=function(t,e,i,r){for(var n=65535&t|0,s=t>>>16&65535|0,a=0;0!==i;){i-=a=i>2e3?2e3:i;do{s=s+(n=n+e[r++]|0)|0}while(--a);n%=65521,s%=65521}return n|s<<16|0}},function(t,e,i){"use strict";var r=function(){for(var t,e=[],i=0;i<256;i++){t=i;for(var r=0;r<8;r++)t=1&t?3988292384^t>>>1:t>>>1;e[i]=t}return e}();t.exports=function(t,e,i,n){var s=r,a=n+i;t^=-1;for(var o=n;o<a;o++)t=t>>>8^s[255&(t^e[o])];return-1^t}},function(t,e,i){"use strict";var r=i(3),n=!0,s=!0;try{String.fromCharCode.apply(null,[0])}catch(t){n=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(t){s=!1}for(var a=new r.Buf8(256),o=0;o<256;o++)a[o]=o>=252?6:o>=248?5:o>=240?4:o>=224?3:o>=192?2:1;function h(t,e){if(e<65534&&(t.subarray&&s||!t.subarray&&n))return String.fromCharCode.apply(null,r.shrinkBuf(t,e));for(var i="",a=0;a<e;a++)i+=String.fromCharCode(t[a]);return i}a[254]=a[254]=1,e.string2buf=function(t){var e,i,n,s,a,o=t.length,h=0;for(s=0;s<o;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(n-56320),s++),h+=i<128?1:i<2048?2:i<65536?3:4;for(e=new r.Buf8(h),a=0,s=0;a<h;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(n=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(n-56320),s++),i<128?e[a++]=i:i<2048?(e[a++]=192|i>>>6,e[a++]=128|63&i):i<65536?(e[a++]=224|i>>>12,e[a++]=128|i>>>6&63,e[a++]=128|63&i):(e[a++]=240|i>>>18,e[a++]=128|i>>>12&63,e[a++]=128|i>>>6&63,e[a++]=128|63&i);return e},e.buf2binstring=function(t){return h(t,t.length)},e.binstring2buf=function(t){for(var e=new r.Buf8(t.length),i=0,n=e.length;i<n;i++)e[i]=t.charCodeAt(i);return e},e.buf2string=function(t,e){var i,r,n,s,o=e||t.length,l=new Array(2*o);for(r=0,i=0;i<o;)if((n=t[i++])<128)l[r++]=n;else if((s=a[n])>4)l[r++]=65533,i+=s-1;else{for(n&=2===s?31:3===s?15:7;s>1&&i<o;)n=n<<6|63&t[i++],s--;s>1?l[r++]=65533:n<65536?l[r++]=n:(n-=65536,l[r++]=55296|n>>10&1023,l[r++]=56320|1023&n)}return h(l,r)},e.utf8border=function(t,e){var i;for((e=e||t.length)>t.length&&(e=t.length),i=e-1;i>=0&&128==(192&t[i]);)i--;return i<0||0===i?e:i+a[t[i]]>e?i:e}},function(t,e,i){"use strict";t.exports=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}},function(t,e,i){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},function(t,e,i){"use strict";e.LOCAL_FILE_HEADER="PK",e.CENTRAL_FILE_HEADER="PK",e.CENTRAL_DIRECTORY_END="PK",e.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",e.ZIP64_CENTRAL_DIRECTORY_END="PK",e.DATA_DESCRIPTOR="PK\b"},function(t,e,i){"use strict";e.base64=!1,e.binary=!1,e.dir=!1,e.createFolders=!1,e.date=null,e.compression=null,e.compressionOptions=null,e.comment=null,e.unixPermissions=null,e.dosPermissions=null},function(t,e,i){"use strict";function r(){this.compressedSize=0,this.uncompressedSize=0,this.crc32=0,this.compressionMethod=null,this.compressedContent=null}r.prototype={getContent:function(){return null},getCompressedContent:function(){return null}},t.exports=r},function(t,e,i){"use strict";for(var r=i(2),n=i(4),s=i(12),a=new Array(256),o=0;o<256;o++)a[o]=o>=252?6:o>=248?5:o>=240?4:o>=224?3:o>=192?2:1;a[254]=a[254]=1;var h=function(t,e){var i;for((e=e||t.length)>t.length&&(e=t.length),i=e-1;i>=0&&128==(192&t[i]);)i--;return i<0||0===i?e:i+a[t[i]]>e?i:e},l=function(t){var e,i,n,s,o=t.length,h=new Array(2*o);for(i=0,e=0;e<o;)if((n=t[e++])<128)h[i++]=n;else if((s=a[n])>4)h[i++]=65533,e+=s-1;else{for(n&=2===s?31:3===s?15:7;s>1&&e<o;)n=n<<6|63&t[e++],s--;s>1?h[i++]=65533:n<65536?h[i++]=n:(n-=65536,h[i++]=55296|n>>10&1023,h[i++]=56320|1023&n)}return h.length!==i&&(h.subarray?h=h.subarray(0,i):h.length=i),r.applyFromCharCode(h)};e.utf8encode=function(t){return n.nodebuffer?s(t,"utf-8"):function(t){var e,i,r,s,a,o=t.length,h=0;for(s=0;s<o;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(r-56320),s++),h+=i<128?1:i<2048?2:i<65536?3:4;for(e=n.uint8array?new Uint8Array(h):new Array(h),a=0,s=0;a<h;s++)55296==(64512&(i=t.charCodeAt(s)))&&s+1<o&&56320==(64512&(r=t.charCodeAt(s+1)))&&(i=65536+(i-55296<<10)+(r-56320),s++),i<128?e[a++]=i:i<2048?(e[a++]=192|i>>>6,e[a++]=128|63&i):i<65536?(e[a++]=224|i>>>12,e[a++]=128|i>>>6&63,e[a++]=128|63&i):(e[a++]=240|i>>>18,e[a++]=128|i>>>12&63,e[a++]=128|i>>>6&63,e[a++]=128|63&i);return e}(t)},e.utf8decode=function(t){if(n.nodebuffer)return r.transformTo("nodebuffer",t).toString("utf-8");for(var e=[],i=0,s=(t=r.transformTo(n.uint8array?"uint8array":"array",t)).length;i<s;){var a=h(t,Math.min(i+65536,s));n.uint8array?e.push(l(t.subarray(i,a))):e.push(l(t.slice(i,a))),i=a}return e.join("")}},function(t,e,i){"use strict";var r=i(23),n=i(2);function s(t,e){this.data=t,e||(this.data=n.string2binary(this.data)),this.length=this.data.length,this.index=0,this.zero=0}s.prototype=new r,s.prototype.byteAt=function(t){return this.data.charCodeAt(this.zero+t)},s.prototype.lastIndexOfSignature=function(t){return this.data.lastIndexOf(t)-this.zero},s.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},t.exports=s},function(t,e,i){"use strict";var r=i(2);function n(t){this.data=null,this.length=0,this.index=0,this.zero=0}n.prototype={checkOffset:function(t){this.checkIndex(this.index+t)},checkIndex:function(t){if(this.length<this.zero+t||t<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+t+"). Corrupted zip ?")},setIndex:function(t){this.checkIndex(t),this.index=t},skip:function(t){this.setIndex(this.index+t)},byteAt:function(t){},readInt:function(t){var e,i=0;for(this.checkOffset(t),e=this.index+t-1;e>=this.index;e--)i=(i<<8)+this.byteAt(e);return this.index+=t,i},readString:function(t){return r.transformTo("string",this.readData(t))},readData:function(t){},lastIndexOfSignature:function(t){},readDate:function(){var t=this.readInt(4);return new Date(1980+(t>>25&127),(t>>21&15)-1,t>>16&31,t>>11&31,t>>5&63,(31&t)<<1)}},t.exports=n},function(t,e,i){"use strict";var r=i(25);function n(t){t&&(this.data=t,this.length=this.data.length,this.index=0,this.zero=0)}n.prototype=new r,n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return new Uint8Array(0);var e=this.data.subarray(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},t.exports=n},function(t,e,i){"use strict";var r=i(23);function n(t){if(t){this.data=t,this.length=this.data.length,this.index=0,this.zero=0;for(var e=0;e<this.data.length;e++)t[e]=255&t[e]}}n.prototype=new r,n.prototype.byteAt=function(t){return this.data[this.zero+t]},n.prototype.lastIndexOfSignature=function(t){for(var e=t.charCodeAt(0),i=t.charCodeAt(1),r=t.charCodeAt(2),n=t.charCodeAt(3),s=this.length-4;s>=0;--s)if(this.data[s]===e&&this.data[s+1]===i&&this.data[s+2]===r&&this.data[s+3]===n)return s-this.zero;return-1},n.prototype.readData=function(t){if(this.checkOffset(t),0===t)return[];var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},t.exports=n},function(t,e,i){"use strict";var r=i(53);function n(){}var s={},a=["REJECTED"],o=["FULFILLED"],h=["PENDING"];function l(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=h,this.queue=[],this.outcome=void 0,t!==n&&d(this,t)}function u(t,e,i){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof i&&(this.onRejected=i,this.callRejected=this.otherCallRejected)}function f(t,e,i){r((function(){var r;try{r=e(i)}catch(e){return s.reject(t,e)}r===t?s.reject(t,new TypeError("Cannot resolve promise with itself")):s.resolve(t,r)}))}function c(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function d(t,e){var i=!1;function r(e){i||(i=!0,s.reject(t,e))}function n(e){i||(i=!0,s.resolve(t,e))}var a=p((function(){e(n,r)}));"error"===a.status&&r(a.value)}function p(t,e){var i={};try{i.value=t(e),i.status="success"}catch(t){i.status="error",i.value=t}return i}t.exports=l,l.prototype.finally=function(t){if("function"!=typeof t)return this;var e=this.constructor;return this.then((function(i){return e.resolve(t()).then((function(){return i}))}),(function(i){return e.resolve(t()).then((function(){throw i}))}))},l.prototype.catch=function(t){return this.then(null,t)},l.prototype.then=function(t,e){if("function"!=typeof t&&this.state===o||"function"!=typeof e&&this.state===a)return this;var i=new this.constructor(n);this.state!==h?f(i,this.state===o?t:e,this.outcome):this.queue.push(new u(i,t,e));return i},u.prototype.callFulfilled=function(t){s.resolve(this.promise,t)},u.prototype.otherCallFulfilled=function(t){f(this.promise,this.onFulfilled,t)},u.prototype.callRejected=function(t){s.reject(this.promise,t)},u.prototype.otherCallRejected=function(t){f(this.promise,this.onRejected,t)},s.resolve=function(t,e){var i=p(c,e);if("error"===i.status)return s.reject(t,i.value);var r=i.value;if(r)d(t,r);else{t.state=o,t.outcome=e;for(var n=-1,a=t.queue.length;++n<a;)t.queue[n].callFulfilled(e)}return t},s.reject=function(t,e){t.state=a,t.outcome=e;for(var i=-1,r=t.queue.length;++i<r;)t.queue[i].callRejected(e);return t},l.resolve=function(t){if(t instanceof this)return t;return s.resolve(new this(n),t)},l.reject=function(t){var e=new this(n);return s.reject(e,t)},l.all=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var i=t.length,r=!1;if(!i)return this.resolve([]);var a=new Array(i),o=0,h=-1,l=new this(n);for(;++h<i;)u(t[h],h);return l;function u(t,n){e.resolve(t).then((function(t){a[n]=t,++o!==i||r||(r=!0,s.resolve(l,a))}),(function(t){r||(r=!0,s.reject(l,t))}))}},l.race=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var i=t.length,r=!1;if(!i)return this.resolve([]);var a=-1,o=new this(n);for(;++a<i;)h=t[a],e.resolve(h).then((function(t){r||(r=!0,s.resolve(o,t))}),(function(t){r||(r=!0,s.reject(o,t))}));var h;return o}},function(t,e){function i(e){return t.exports=i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,i(e)}t.exports=i,t.exports.__esModule=!0,t.exports.default=t.exports},function(t,e,i){"use strict";var r=i(29);t.exports=function(t){var e=new r(t).file(/.+/),i={};return e.forEach((function(t){"shp"===t.name.slice(-3).toLowerCase()||"dbf"===t.name.slice(-3).toLowerCase()?i[t.name]=t.asNodeBuffer():i[t.name]=t.asText()})),i}},function(t,e,i){"use strict";var r=i(8);function n(t,e){if(!(this instanceof n))return new n(t,e);this.files={},this.comment=null,this.root="",t&&this.load(t,e),this.clone=function(){var t=new n;for(var e in this)"function"!=typeof this[e]&&(t[e]=this[e]);return t}}n.prototype=i(9),n.prototype.load=i(46),n.support=i(4),n.defaults=i(19),n.utils=i(50),n.base64={encode:function(t){return r.encode(t)},decode:function(t){return r.decode(t)}},n.compressions=i(10),t.exports=n},function(t,e,i){"use strict";e.byteLength=function(t){var e=l(t),i=e[0],r=e[1];return 3*(i+r)/4-r},e.toByteArray=function(t){var e,i,r=l(t),a=r[0],o=r[1],h=new s(function(t,e,i){return 3*(e+i)/4-i}(0,a,o)),u=0,f=o>0?a-4:a;for(i=0;i<f;i+=4)e=n[t.charCodeAt(i)]<<18|n[t.charCodeAt(i+1)]<<12|n[t.charCodeAt(i+2)]<<6|n[t.charCodeAt(i+3)],h[u++]=e>>16&255,h[u++]=e>>8&255,h[u++]=255&e;2===o&&(e=n[t.charCodeAt(i)]<<2|n[t.charCodeAt(i+1)]>>4,h[u++]=255&e);1===o&&(e=n[t.charCodeAt(i)]<<10|n[t.charCodeAt(i+1)]<<4|n[t.charCodeAt(i+2)]>>2,h[u++]=e>>8&255,h[u++]=255&e);return h},e.fromByteArray=function(t){for(var e,i=t.length,n=i%3,s=[],a=0,o=i-n;a<o;a+=16383)s.push(u(t,a,a+16383>o?o:a+16383));1===n?(e=t[i-1],s.push(r[e>>2]+r[e<<4&63]+"==")):2===n&&(e=(t[i-2]<<8)+t[i-1],s.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"="));return s.join("")};for(var r=[],n=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,h=a.length;o<h;++o)r[o]=a[o],n[a.charCodeAt(o)]=o;function l(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var i=t.indexOf("=");return-1===i&&(i=e),[i,i===e?0:4-i%4]}function u(t,e,i){for(var n,s,a=[],o=e;o<i;o+=3)n=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(255&t[o+2]),a.push(r[(s=n)>>18&63]+r[s>>12&63]+r[s>>6&63]+r[63&s]);return a.join("")}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},function(t,e){
/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */
e.read=function(t,e,i,r,n){var s,a,o=8*n-r-1,h=(1<<o)-1,l=h>>1,u=-7,f=i?n-1:0,c=i?-1:1,d=t[e+f];for(f+=c,s=d&(1<<-u)-1,d>>=-u,u+=o;u>0;s=256*s+t[e+f],f+=c,u-=8);for(a=s&(1<<-u)-1,s>>=-u,u+=r;u>0;a=256*a+t[e+f],f+=c,u-=8);if(0===s)s=1-l;else{if(s===h)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,r),s-=l}return(d?-1:1)*a*Math.pow(2,s-r)},e.write=function(t,e,i,r,n,s){var a,o,h,l=8*s-n-1,u=(1<<l)-1,f=u>>1,c=23===n?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:s-1,p=r?1:-1,g=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(o=isNaN(e)?1:0,a=u):(a=Math.floor(Math.log(e)/Math.LN2),e*(h=Math.pow(2,-a))<1&&(a--,h*=2),(e+=a+f>=1?c/h:c*Math.pow(2,1-f))*h>=2&&(a++,h/=2),a+f>=u?(o=0,a=u):a+f>=1?(o=(e*h-1)*Math.pow(2,n),a+=f):(o=e*Math.pow(2,f-1)*Math.pow(2,n),a=0));n>=8;t[i+d]=255&o,d+=p,o/=256,n-=8);for(a=a<<n|o,l+=n;l>0;t[i+d]=255&a,d+=p,a/=256,l-=8);t[i+d-p]|=128*g}},function(t,e){var i={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==i.call(t)}},function(t,e,i){"use strict";var r="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,n=i(34);e.uncompressInputType=r?"uint8array":"array",e.compressInputType=r?"uint8array":"array",e.magic="\b\0",e.compress=function(t,e){return n.deflateRaw(t,{level:e.level||-1})},e.uncompress=function(t){return n.inflateRaw(t)}},function(t,e,i){"use strict";var r={};(0,i(3).assign)(r,i(35),i(38),i(17)),t.exports=r},function(t,e,i){"use strict";var r=i(36),n=i(3),s=i(15),a=i(11),o=i(16),h=Object.prototype.toString;function l(t){if(!(this instanceof l))return new l(t);this.options=n.assign({level:-1,method:8,chunkSize:16384,windowBits:15,memLevel:8,strategy:0,to:""},t||{});var e=this.options;e.raw&&e.windowBits>0?e.windowBits=-e.windowBits:e.gzip&&e.windowBits>0&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new o,this.strm.avail_out=0;var i=r.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy);if(0!==i)throw new Error(a[i]);if(e.header&&r.deflateSetHeader(this.strm,e.header),e.dictionary){var u;if(u="string"==typeof e.dictionary?s.string2buf(e.dictionary):"[object ArrayBuffer]"===h.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary,0!==(i=r.deflateSetDictionary(this.strm,u)))throw new Error(a[i]);this._dict_set=!0}}function u(t,e){var i=new l(e);if(i.push(t,!0),i.err)throw i.msg||a[i.err];return i.result}l.prototype.push=function(t,e){var i,a,o=this.strm,l=this.options.chunkSize;if(this.ended)return!1;a=e===~~e?e:!0===e?4:0,"string"==typeof t?o.input=s.string2buf(t):"[object ArrayBuffer]"===h.call(t)?o.input=new Uint8Array(t):o.input=t,o.next_in=0,o.avail_in=o.input.length;do{if(0===o.avail_out&&(o.output=new n.Buf8(l),o.next_out=0,o.avail_out=l),1!==(i=r.deflate(o,a))&&0!==i)return this.onEnd(i),this.ended=!0,!1;0!==o.avail_out&&(0!==o.avail_in||4!==a&&2!==a)||("string"===this.options.to?this.onData(s.buf2binstring(n.shrinkBuf(o.output,o.next_out))):this.onData(n.shrinkBuf(o.output,o.next_out)))}while((o.avail_in>0||0===o.avail_out)&&1!==i);return 4===a?(i=r.deflateEnd(this.strm),this.onEnd(i),this.ended=!0,0===i):2!==a||(this.onEnd(0),o.avail_out=0,!0)},l.prototype.onData=function(t){this.chunks.push(t)},l.prototype.onEnd=function(t){0===t&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},e.Deflate=l,e.deflate=u,e.deflateRaw=function(t,e){return(e=e||{}).raw=!0,u(t,e)},e.gzip=function(t,e){return(e=e||{}).gzip=!0,u(t,e)}},function(t,e,i){"use strict";var r,n=i(3),s=i(37),a=i(13),o=i(14),h=i(11);function l(t,e){return t.msg=h[e],e}function u(t){return(t<<1)-(t>4?9:0)}function f(t){for(var e=t.length;--e>=0;)t[e]=0}function c(t){var e=t.state,i=e.pending;i>t.avail_out&&(i=t.avail_out),0!==i&&(n.arraySet(t.output,e.pending_buf,e.pending_out,i,t.next_out),t.next_out+=i,e.pending_out+=i,t.total_out+=i,t.avail_out-=i,e.pending-=i,0===e.pending&&(e.pending_out=0))}function d(t,e){s._tr_flush_block(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,c(t.strm)}function p(t,e){t.pending_buf[t.pending++]=e}function g(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function m(t,e){var i,r,n=t.max_chain_length,s=t.strstart,a=t.prev_length,o=t.nice_match,h=t.strstart>t.w_size-262?t.strstart-(t.w_size-262):0,l=t.window,u=t.w_mask,f=t.prev,c=t.strstart+258,d=l[s+a-1],p=l[s+a];t.prev_length>=t.good_match&&(n>>=2),o>t.lookahead&&(o=t.lookahead);do{if(l[(i=e)+a]===p&&l[i+a-1]===d&&l[i]===l[s]&&l[++i]===l[s+1]){s+=2,i++;do{}while(l[++s]===l[++i]&&l[++s]===l[++i]&&l[++s]===l[++i]&&l[++s]===l[++i]&&l[++s]===l[++i]&&l[++s]===l[++i]&&l[++s]===l[++i]&&l[++s]===l[++i]&&s<c);if(r=258-(c-s),s=c-258,r>a){if(t.match_start=e,a=r,r>=o)break;d=l[s+a-1],p=l[s+a]}}}while((e=f[e&u])>h&&0!=--n);return a<=t.lookahead?a:t.lookahead}function _(t){var e,i,r,s,h,l,u,f,c,d,p=t.w_size;do{if(s=t.window_size-t.lookahead-t.strstart,t.strstart>=p+(p-262)){n.arraySet(t.window,t.window,p,p,0),t.match_start-=p,t.strstart-=p,t.block_start-=p,e=i=t.hash_size;do{r=t.head[--e],t.head[e]=r>=p?r-p:0}while(--i);e=i=p;do{r=t.prev[--e],t.prev[e]=r>=p?r-p:0}while(--i);s+=p}if(0===t.strm.avail_in)break;if(l=t.strm,u=t.window,f=t.strstart+t.lookahead,c=s,d=void 0,(d=l.avail_in)>c&&(d=c),i=0===d?0:(l.avail_in-=d,n.arraySet(u,l.input,l.next_in,d,f),1===l.state.wrap?l.adler=a(l.adler,u,d,f):2===l.state.wrap&&(l.adler=o(l.adler,u,d,f)),l.next_in+=d,l.total_in+=d,d),t.lookahead+=i,t.lookahead+t.insert>=3)for(h=t.strstart-t.insert,t.ins_h=t.window[h],t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+1])&t.hash_mask;t.insert&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[h+3-1])&t.hash_mask,t.prev[h&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=h,h++,t.insert--,!(t.lookahead+t.insert<3)););}while(t.lookahead<262&&0!==t.strm.avail_in)}function y(t,e){for(var i,r;;){if(t.lookahead<262){if(_(t),t.lookahead<262&&0===e)return 1;if(0===t.lookahead)break}if(i=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==i&&t.strstart-i<=t.w_size-262&&(t.match_length=m(t,i)),t.match_length>=3)if(r=s._tr_tally(t,t.strstart-t.match_start,t.match_length-3),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=3){t.match_length--;do{t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!=--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else r=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(r&&(d(t,!1),0===t.strm.avail_out))return 1}return t.insert=t.strstart<2?t.strstart:2,4===e?(d(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(d(t,!1),0===t.strm.avail_out)?1:2}function v(t,e){for(var i,r,n;;){if(t.lookahead<262){if(_(t),t.lookahead<262&&0===e)return 1;if(0===t.lookahead)break}if(i=0,t.lookahead>=3&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=2,0!==i&&t.prev_length<t.max_lazy_match&&t.strstart-i<=t.w_size-262&&(t.match_length=m(t,i),t.match_length<=5&&(1===t.strategy||3===t.match_length&&t.strstart-t.match_start>4096)&&(t.match_length=2)),t.prev_length>=3&&t.match_length<=t.prev_length){n=t.strstart+t.lookahead-3,r=s._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-3),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=n&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+3-1])&t.hash_mask,i=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!=--t.prev_length);if(t.match_available=0,t.match_length=2,t.strstart++,r&&(d(t,!1),0===t.strm.avail_out))return 1}else if(t.match_available){if((r=s._tr_tally(t,0,t.window[t.strstart-1]))&&d(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return 1}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(r=s._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<2?t.strstart:2,4===e?(d(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(d(t,!1),0===t.strm.avail_out)?1:2}function b(t,e,i,r,n){this.good_length=t,this.max_lazy=e,this.nice_length=i,this.max_chain=r,this.func=n}function M(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=8,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new n.Buf16(1146),this.dyn_dtree=new n.Buf16(122),this.bl_tree=new n.Buf16(78),f(this.dyn_ltree),f(this.dyn_dtree),f(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new n.Buf16(16),this.heap=new n.Buf16(573),f(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new n.Buf16(573),f(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function w(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=2,(e=t.state).pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?42:113,t.adler=2===e.wrap?0:1,e.last_flush=0,s._tr_init(e),0):l(t,-2)}function x(t){var e,i=w(t);return 0===i&&((e=t.state).window_size=2*e.w_size,f(e.head),e.max_lazy_match=r[e.level].max_lazy,e.good_match=r[e.level].good_length,e.nice_match=r[e.level].nice_length,e.max_chain_length=r[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=2,e.match_available=0,e.ins_h=0),i}function C(t,e,i,r,s,a){if(!t)return-2;var o=1;if(-1===e&&(e=6),r<0?(o=0,r=-r):r>15&&(o=2,r-=16),s<1||s>9||8!==i||r<8||r>15||e<0||e>9||a<0||a>4)return l(t,-2);8===r&&(r=9);var h=new M;return t.state=h,h.strm=t,h.wrap=o,h.gzhead=null,h.w_bits=r,h.w_size=1<<h.w_bits,h.w_mask=h.w_size-1,h.hash_bits=s+7,h.hash_size=1<<h.hash_bits,h.hash_mask=h.hash_size-1,h.hash_shift=~~((h.hash_bits+3-1)/3),h.window=new n.Buf8(2*h.w_size),h.head=new n.Buf16(h.hash_size),h.prev=new n.Buf16(h.w_size),h.lit_bufsize=1<<s+6,h.pending_buf_size=4*h.lit_bufsize,h.pending_buf=new n.Buf8(h.pending_buf_size),h.d_buf=1*h.lit_bufsize,h.l_buf=3*h.lit_bufsize,h.level=e,h.strategy=a,h.method=i,x(t)}r=[new b(0,0,0,0,(function(t,e){var i=65535;for(i>t.pending_buf_size-5&&(i=t.pending_buf_size-5);;){if(t.lookahead<=1){if(_(t),0===t.lookahead&&0===e)return 1;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var r=t.block_start+i;if((0===t.strstart||t.strstart>=r)&&(t.lookahead=t.strstart-r,t.strstart=r,d(t,!1),0===t.strm.avail_out))return 1;if(t.strstart-t.block_start>=t.w_size-262&&(d(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(d(t,!0),0===t.strm.avail_out?3:4):(t.strstart>t.block_start&&(d(t,!1),t.strm.avail_out),1)})),new b(4,4,8,4,y),new b(4,5,16,8,y),new b(4,6,32,32,y),new b(4,4,16,16,v),new b(8,16,32,32,v),new b(8,16,128,128,v),new b(8,32,128,256,v),new b(32,128,258,1024,v),new b(32,258,258,4096,v)],e.deflateInit=function(t,e){return C(t,e,8,15,8,0)},e.deflateInit2=C,e.deflateReset=x,e.deflateResetKeep=w,e.deflateSetHeader=function(t,e){return t&&t.state?2!==t.state.wrap?-2:(t.state.gzhead=e,0):-2},e.deflate=function(t,e){var i,n,a,h;if(!t||!t.state||e>5||e<0)return t?l(t,-2):-2;if(n=t.state,!t.output||!t.input&&0!==t.avail_in||666===n.status&&4!==e)return l(t,0===t.avail_out?-5:-2);if(n.strm=t,i=n.last_flush,n.last_flush=e,42===n.status)if(2===n.wrap)t.adler=0,p(n,31),p(n,139),p(n,8),n.gzhead?(p(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),p(n,255&n.gzhead.time),p(n,n.gzhead.time>>8&255),p(n,n.gzhead.time>>16&255),p(n,n.gzhead.time>>24&255),p(n,9===n.level?2:n.strategy>=2||n.level<2?4:0),p(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(p(n,255&n.gzhead.extra.length),p(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(t.adler=o(t.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=69):(p(n,0),p(n,0),p(n,0),p(n,0),p(n,0),p(n,9===n.level?2:n.strategy>=2||n.level<2?4:0),p(n,3),n.status=113);else{var m=8+(n.w_bits-8<<4)<<8;m|=(n.strategy>=2||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(m|=32),m+=31-m%31,n.status=113,g(n,m),0!==n.strstart&&(g(n,t.adler>>>16),g(n,65535&t.adler)),t.adler=1}if(69===n.status)if(n.gzhead.extra){for(a=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),c(t),a=n.pending,n.pending!==n.pending_buf_size));)p(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=73)}else n.status=73;if(73===n.status)if(n.gzhead.name){a=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),c(t),a=n.pending,n.pending===n.pending_buf_size)){h=1;break}h=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,p(n,h)}while(0!==h);n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),0===h&&(n.gzindex=0,n.status=91)}else n.status=91;if(91===n.status)if(n.gzhead.comment){a=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),c(t),a=n.pending,n.pending===n.pending_buf_size)){h=1;break}h=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,p(n,h)}while(0!==h);n.gzhead.hcrc&&n.pending>a&&(t.adler=o(t.adler,n.pending_buf,n.pending-a,a)),0===h&&(n.status=103)}else n.status=103;if(103===n.status&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&c(t),n.pending+2<=n.pending_buf_size&&(p(n,255&t.adler),p(n,t.adler>>8&255),t.adler=0,n.status=113)):n.status=113),0!==n.pending){if(c(t),0===t.avail_out)return n.last_flush=-1,0}else if(0===t.avail_in&&u(e)<=u(i)&&4!==e)return l(t,-5);if(666===n.status&&0!==t.avail_in)return l(t,-5);if(0!==t.avail_in||0!==n.lookahead||0!==e&&666!==n.status){var y=2===n.strategy?function(t,e){for(var i;;){if(0===t.lookahead&&(_(t),0===t.lookahead)){if(0===e)return 1;break}if(t.match_length=0,i=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,i&&(d(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(d(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(d(t,!1),0===t.strm.avail_out)?1:2}(n,e):3===n.strategy?function(t,e){for(var i,r,n,a,o=t.window;;){if(t.lookahead<=258){if(_(t),t.lookahead<=258&&0===e)return 1;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=3&&t.strstart>0&&(r=o[n=t.strstart-1])===o[++n]&&r===o[++n]&&r===o[++n]){a=t.strstart+258;do{}while(r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&r===o[++n]&&n<a);t.match_length=258-(a-n),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=3?(i=s._tr_tally(t,1,t.match_length-3),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(i=s._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),i&&(d(t,!1),0===t.strm.avail_out))return 1}return t.insert=0,4===e?(d(t,!0),0===t.strm.avail_out?3:4):t.last_lit&&(d(t,!1),0===t.strm.avail_out)?1:2}(n,e):r[n.level].func(n,e);if(3!==y&&4!==y||(n.status=666),1===y||3===y)return 0===t.avail_out&&(n.last_flush=-1),0;if(2===y&&(1===e?s._tr_align(n):5!==e&&(s._tr_stored_block(n,0,0,!1),3===e&&(f(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),c(t),0===t.avail_out))return n.last_flush=-1,0}return 4!==e?0:n.wrap<=0?1:(2===n.wrap?(p(n,255&t.adler),p(n,t.adler>>8&255),p(n,t.adler>>16&255),p(n,t.adler>>24&255),p(n,255&t.total_in),p(n,t.total_in>>8&255),p(n,t.total_in>>16&255),p(n,t.total_in>>24&255)):(g(n,t.adler>>>16),g(n,65535&t.adler)),c(t),n.wrap>0&&(n.wrap=-n.wrap),0!==n.pending?0:1)},e.deflateEnd=function(t){var e;return t&&t.state?42!==(e=t.state.status)&&69!==e&&73!==e&&91!==e&&103!==e&&113!==e&&666!==e?l(t,-2):(t.state=null,113===e?l(t,-3):0):-2},e.deflateSetDictionary=function(t,e){var i,r,s,o,h,l,u,c,d=e.length;if(!t||!t.state)return-2;if(2===(o=(i=t.state).wrap)||1===o&&42!==i.status||i.lookahead)return-2;for(1===o&&(t.adler=a(t.adler,e,d,0)),i.wrap=0,d>=i.w_size&&(0===o&&(f(i.head),i.strstart=0,i.block_start=0,i.insert=0),c=new n.Buf8(i.w_size),n.arraySet(c,e,d-i.w_size,i.w_size,0),e=c,d=i.w_size),h=t.avail_in,l=t.next_in,u=t.input,t.avail_in=d,t.next_in=0,t.input=e,_(i);i.lookahead>=3;){r=i.strstart,s=i.lookahead-2;do{i.ins_h=(i.ins_h<<i.hash_shift^i.window[r+3-1])&i.hash_mask,i.prev[r&i.w_mask]=i.head[i.ins_h],i.head[i.ins_h]=r,r++}while(--s);i.strstart=r,i.lookahead=2,_(i)}return i.strstart+=i.lookahead,i.block_start=i.strstart,i.insert=i.lookahead,i.lookahead=0,i.match_length=i.prev_length=2,i.match_available=0,t.next_in=l,t.input=u,t.avail_in=h,i.wrap=o,0},e.deflateInfo="pako deflate (from Nodeca project)"},function(t,e,i){"use strict";var r=i(3);function n(t){for(var e=t.length;--e>=0;)t[e]=0}var s=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],a=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],h=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],l=new Array(576);n(l);var u=new Array(60);n(u);var f=new Array(512);n(f);var c=new Array(256);n(c);var d=new Array(29);n(d);var p,g,m,_=new Array(30);function y(t,e,i,r,n){this.static_tree=t,this.extra_bits=e,this.extra_base=i,this.elems=r,this.max_length=n,this.has_stree=t&&t.length}function v(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function b(t){return t<256?f[t]:f[256+(t>>>7)]}function M(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function w(t,e,i){t.bi_valid>16-i?(t.bi_buf|=e<<t.bi_valid&65535,M(t,t.bi_buf),t.bi_buf=e>>16-t.bi_valid,t.bi_valid+=i-16):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=i)}function x(t,e,i){w(t,i[2*e],i[2*e+1])}function C(t,e){var i=0;do{i|=1&t,t>>>=1,i<<=1}while(--e>0);return i>>>1}function S(t,e,i){var r,n,s=new Array(16),a=0;for(r=1;r<=15;r++)s[r]=a=a+i[r-1]<<1;for(n=0;n<=e;n++){var o=t[2*n+1];0!==o&&(t[2*n]=C(s[o]++,o))}}function k(t){var e;for(e=0;e<286;e++)t.dyn_ltree[2*e]=0;for(e=0;e<30;e++)t.dyn_dtree[2*e]=0;for(e=0;e<19;e++)t.bl_tree[2*e]=0;t.dyn_ltree[512]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function E(t){t.bi_valid>8?M(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function A(t,e,i,r){var n=2*e,s=2*i;return t[n]<t[s]||t[n]===t[s]&&r[e]<=r[i]}function P(t,e,i){for(var r=t.heap[i],n=i<<1;n<=t.heap_len&&(n<t.heap_len&&A(e,t.heap[n+1],t.heap[n],t.depth)&&n++,!A(e,r,t.heap[n],t.depth));)t.heap[i]=t.heap[n],i=n,n<<=1;t.heap[i]=r}function I(t,e,i){var r,n,o,h,l=0;if(0!==t.last_lit)do{r=t.pending_buf[t.d_buf+2*l]<<8|t.pending_buf[t.d_buf+2*l+1],n=t.pending_buf[t.l_buf+l],l++,0===r?x(t,n,e):(x(t,(o=c[n])+256+1,e),0!==(h=s[o])&&w(t,n-=d[o],h),x(t,o=b(--r),i),0!==(h=a[o])&&w(t,r-=_[o],h))}while(l<t.last_lit);x(t,256,e)}function O(t,e){var i,r,n,s=e.dyn_tree,a=e.stat_desc.static_tree,o=e.stat_desc.has_stree,h=e.stat_desc.elems,l=-1;for(t.heap_len=0,t.heap_max=573,i=0;i<h;i++)0!==s[2*i]?(t.heap[++t.heap_len]=l=i,t.depth[i]=0):s[2*i+1]=0;for(;t.heap_len<2;)s[2*(n=t.heap[++t.heap_len]=l<2?++l:0)]=1,t.depth[n]=0,t.opt_len--,o&&(t.static_len-=a[2*n+1]);for(e.max_code=l,i=t.heap_len>>1;i>=1;i--)P(t,s,i);n=h;do{i=t.heap[1],t.heap[1]=t.heap[t.heap_len--],P(t,s,1),r=t.heap[1],t.heap[--t.heap_max]=i,t.heap[--t.heap_max]=r,s[2*n]=s[2*i]+s[2*r],t.depth[n]=(t.depth[i]>=t.depth[r]?t.depth[i]:t.depth[r])+1,s[2*i+1]=s[2*r+1]=n,t.heap[1]=n++,P(t,s,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],function(t,e){var i,r,n,s,a,o,h=e.dyn_tree,l=e.max_code,u=e.stat_desc.static_tree,f=e.stat_desc.has_stree,c=e.stat_desc.extra_bits,d=e.stat_desc.extra_base,p=e.stat_desc.max_length,g=0;for(s=0;s<=15;s++)t.bl_count[s]=0;for(h[2*t.heap[t.heap_max]+1]=0,i=t.heap_max+1;i<573;i++)(s=h[2*h[2*(r=t.heap[i])+1]+1]+1)>p&&(s=p,g++),h[2*r+1]=s,r>l||(t.bl_count[s]++,a=0,r>=d&&(a=c[r-d]),o=h[2*r],t.opt_len+=o*(s+a),f&&(t.static_len+=o*(u[2*r+1]+a)));if(0!==g){do{for(s=p-1;0===t.bl_count[s];)s--;t.bl_count[s]--,t.bl_count[s+1]+=2,t.bl_count[p]--,g-=2}while(g>0);for(s=p;0!==s;s--)for(r=t.bl_count[s];0!==r;)(n=t.heap[--i])>l||(h[2*n+1]!==s&&(t.opt_len+=(s-h[2*n+1])*h[2*n],h[2*n+1]=s),r--)}}(t,e),S(s,l,t.bl_count)}function N(t,e,i){var r,n,s=-1,a=e[1],o=0,h=7,l=4;for(0===a&&(h=138,l=3),e[2*(i+1)+1]=65535,r=0;r<=i;r++)n=a,a=e[2*(r+1)+1],++o<h&&n===a||(o<l?t.bl_tree[2*n]+=o:0!==n?(n!==s&&t.bl_tree[2*n]++,t.bl_tree[32]++):o<=10?t.bl_tree[34]++:t.bl_tree[36]++,o=0,s=n,0===a?(h=138,l=3):n===a?(h=6,l=3):(h=7,l=4))}function L(t,e,i){var r,n,s=-1,a=e[1],o=0,h=7,l=4;for(0===a&&(h=138,l=3),r=0;r<=i;r++)if(n=a,a=e[2*(r+1)+1],!(++o<h&&n===a)){if(o<l)do{x(t,n,t.bl_tree)}while(0!=--o);else 0!==n?(n!==s&&(x(t,n,t.bl_tree),o--),x(t,16,t.bl_tree),w(t,o-3,2)):o<=10?(x(t,17,t.bl_tree),w(t,o-3,3)):(x(t,18,t.bl_tree),w(t,o-11,7));o=0,s=n,0===a?(h=138,l=3):n===a?(h=6,l=3):(h=7,l=4)}}n(_);var T=!1;function R(t,e,i,n){w(t,0+(n?1:0),3),function(t,e,i,n){E(t),n&&(M(t,i),M(t,~i)),r.arraySet(t.pending_buf,t.window,e,i,t.pending),t.pending+=i}(t,e,i,!0)}e._tr_init=function(t){T||(!function(){var t,e,i,r,n,h=new Array(16);for(i=0,r=0;r<28;r++)for(d[r]=i,t=0;t<1<<s[r];t++)c[i++]=r;for(c[i-1]=r,n=0,r=0;r<16;r++)for(_[r]=n,t=0;t<1<<a[r];t++)f[n++]=r;for(n>>=7;r<30;r++)for(_[r]=n<<7,t=0;t<1<<a[r]-7;t++)f[256+n++]=r;for(e=0;e<=15;e++)h[e]=0;for(t=0;t<=143;)l[2*t+1]=8,t++,h[8]++;for(;t<=255;)l[2*t+1]=9,t++,h[9]++;for(;t<=279;)l[2*t+1]=7,t++,h[7]++;for(;t<=287;)l[2*t+1]=8,t++,h[8]++;for(S(l,287,h),t=0;t<30;t++)u[2*t+1]=5,u[2*t]=C(t,5);p=new y(l,s,257,286,15),g=new y(u,a,0,30,15),m=new y(new Array(0),o,0,19,7)}(),T=!0),t.l_desc=new v(t.dyn_ltree,p),t.d_desc=new v(t.dyn_dtree,g),t.bl_desc=new v(t.bl_tree,m),t.bi_buf=0,t.bi_valid=0,k(t)},e._tr_stored_block=R,e._tr_flush_block=function(t,e,i,r){var n,s,a=0;t.level>0?(2===t.strm.data_type&&(t.strm.data_type=function(t){var e,i=4093624447;for(e=0;e<=31;e++,i>>>=1)if(1&i&&0!==t.dyn_ltree[2*e])return 0;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return 1;for(e=32;e<256;e++)if(0!==t.dyn_ltree[2*e])return 1;return 0}(t)),O(t,t.l_desc),O(t,t.d_desc),a=function(t){var e;for(N(t,t.dyn_ltree,t.l_desc.max_code),N(t,t.dyn_dtree,t.d_desc.max_code),O(t,t.bl_desc),e=18;e>=3&&0===t.bl_tree[2*h[e]+1];e--);return t.opt_len+=3*(e+1)+5+5+4,e}(t),n=t.opt_len+3+7>>>3,(s=t.static_len+3+7>>>3)<=n&&(n=s)):n=s=i+5,i+4<=n&&-1!==e?R(t,e,i,r):4===t.strategy||s===n?(w(t,2+(r?1:0),3),I(t,l,u)):(w(t,4+(r?1:0),3),function(t,e,i,r){var n;for(w(t,e-257,5),w(t,i-1,5),w(t,r-4,4),n=0;n<r;n++)w(t,t.bl_tree[2*h[n]+1],3);L(t,t.dyn_ltree,e-1),L(t,t.dyn_dtree,i-1)}(t,t.l_desc.max_code+1,t.d_desc.max_code+1,a+1),I(t,t.dyn_ltree,t.dyn_dtree)),k(t),r&&E(t)},e._tr_tally=function(t,e,i){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&i,t.last_lit++,0===e?t.dyn_ltree[2*i]++:(t.matches++,e--,t.dyn_ltree[2*(c[i]+256+1)]++,t.dyn_dtree[2*b(e)]++),t.last_lit===t.lit_bufsize-1},e._tr_align=function(t){w(t,2,3),x(t,256,l),function(t){16===t.bi_valid?(M(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}(t)}},function(t,e,i){"use strict";var r=i(39),n=i(3),s=i(15),a=i(17),o=i(11),h=i(16),l=i(42),u=Object.prototype.toString;function f(t){if(!(this instanceof f))return new f(t);this.options=n.assign({chunkSize:16384,windowBits:0,to:""},t||{});var e=this.options;e.raw&&e.windowBits>=0&&e.windowBits<16&&(e.windowBits=-e.windowBits,0===e.windowBits&&(e.windowBits=-15)),!(e.windowBits>=0&&e.windowBits<16)||t&&t.windowBits||(e.windowBits+=32),e.windowBits>15&&e.windowBits<48&&0==(15&e.windowBits)&&(e.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new h,this.strm.avail_out=0;var i=r.inflateInit2(this.strm,e.windowBits);if(i!==a.Z_OK)throw new Error(o[i]);if(this.header=new l,r.inflateGetHeader(this.strm,this.header),e.dictionary&&("string"==typeof e.dictionary?e.dictionary=s.string2buf(e.dictionary):"[object ArrayBuffer]"===u.call(e.dictionary)&&(e.dictionary=new Uint8Array(e.dictionary)),e.raw&&(i=r.inflateSetDictionary(this.strm,e.dictionary))!==a.Z_OK))throw new Error(o[i])}function c(t,e){var i=new f(e);if(i.push(t,!0),i.err)throw i.msg||o[i.err];return i.result}f.prototype.push=function(t,e){var i,o,h,l,f,c=this.strm,d=this.options.chunkSize,p=this.options.dictionary,g=!1;if(this.ended)return!1;o=e===~~e?e:!0===e?a.Z_FINISH:a.Z_NO_FLUSH,"string"==typeof t?c.input=s.binstring2buf(t):"[object ArrayBuffer]"===u.call(t)?c.input=new Uint8Array(t):c.input=t,c.next_in=0,c.avail_in=c.input.length;do{if(0===c.avail_out&&(c.output=new n.Buf8(d),c.next_out=0,c.avail_out=d),(i=r.inflate(c,a.Z_NO_FLUSH))===a.Z_NEED_DICT&&p&&(i=r.inflateSetDictionary(this.strm,p)),i===a.Z_BUF_ERROR&&!0===g&&(i=a.Z_OK,g=!1),i!==a.Z_STREAM_END&&i!==a.Z_OK)return this.onEnd(i),this.ended=!0,!1;c.next_out&&(0!==c.avail_out&&i!==a.Z_STREAM_END&&(0!==c.avail_in||o!==a.Z_FINISH&&o!==a.Z_SYNC_FLUSH)||("string"===this.options.to?(h=s.utf8border(c.output,c.next_out),l=c.next_out-h,f=s.buf2string(c.output,h),c.next_out=l,c.avail_out=d-l,l&&n.arraySet(c.output,c.output,h,l,0),this.onData(f)):this.onData(n.shrinkBuf(c.output,c.next_out)))),0===c.avail_in&&0===c.avail_out&&(g=!0)}while((c.avail_in>0||0===c.avail_out)&&i!==a.Z_STREAM_END);return i===a.Z_STREAM_END&&(o=a.Z_FINISH),o===a.Z_FINISH?(i=r.inflateEnd(this.strm),this.onEnd(i),this.ended=!0,i===a.Z_OK):o!==a.Z_SYNC_FLUSH||(this.onEnd(a.Z_OK),c.avail_out=0,!0)},f.prototype.onData=function(t){this.chunks.push(t)},f.prototype.onEnd=function(t){t===a.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=n.flattenChunks(this.chunks)),this.chunks=[],this.err=t,this.msg=this.strm.msg},e.Inflate=f,e.inflate=c,e.inflateRaw=function(t,e){return(e=e||{}).raw=!0,c(t,e)},e.ungzip=c},function(t,e,i){"use strict";var r=i(3),n=i(13),s=i(14),a=i(40),o=i(41);function h(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function l(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function u(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=1,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new r.Buf32(852),e.distcode=e.distdyn=new r.Buf32(592),e.sane=1,e.back=-1,0):-2}function f(t){var e;return t&&t.state?((e=t.state).wsize=0,e.whave=0,e.wnext=0,u(t)):-2}function c(t,e){var i,r;return t&&t.state?(r=t.state,e<0?(i=0,e=-e):(i=1+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?-2:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=i,r.wbits=e,f(t))):-2}function d(t,e){var i,r;return t?(r=new l,t.state=r,r.window=null,0!==(i=c(t,e))&&(t.state=null),i):-2}var p,g,m=!0;function _(t){if(m){var e;for(p=new r.Buf32(512),g=new r.Buf32(32),e=0;e<144;)t.lens[e++]=8;for(;e<256;)t.lens[e++]=9;for(;e<280;)t.lens[e++]=7;for(;e<288;)t.lens[e++]=8;for(o(1,t.lens,0,288,p,0,t.work,{bits:9}),e=0;e<32;)t.lens[e++]=5;o(2,t.lens,0,32,g,0,t.work,{bits:5}),m=!1}t.lencode=p,t.lenbits=9,t.distcode=g,t.distbits=5}function y(t,e,i,n){var s,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new r.Buf8(a.wsize)),n>=a.wsize?(r.arraySet(a.window,e,i-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):((s=a.wsize-a.wnext)>n&&(s=n),r.arraySet(a.window,e,i-n,s,a.wnext),(n-=s)?(r.arraySet(a.window,e,i-n,n,0),a.wnext=n,a.whave=a.wsize):(a.wnext+=s,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=s))),0}e.inflateReset=f,e.inflateReset2=c,e.inflateResetKeep=u,e.inflateInit=function(t){return d(t,15)},e.inflateInit2=d,e.inflate=function(t,e){var i,l,u,f,c,d,p,g,m,v,b,M,w,x,C,S,k,E,A,P,I,O,N,L,T=0,R=new r.Buf8(4),z=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return-2;12===(i=t.state).mode&&(i.mode=13),c=t.next_out,u=t.output,p=t.avail_out,f=t.next_in,l=t.input,d=t.avail_in,g=i.hold,m=i.bits,v=d,b=p,O=0;t:for(;;)switch(i.mode){case 1:if(0===i.wrap){i.mode=13;break}for(;m<16;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(2&i.wrap&&35615===g){i.check=0,R[0]=255&g,R[1]=g>>>8&255,i.check=s(i.check,R,2,0),g=0,m=0,i.mode=2;break}if(i.flags=0,i.head&&(i.head.done=!1),!(1&i.wrap)||(((255&g)<<8)+(g>>8))%31){t.msg="incorrect header check",i.mode=30;break}if(8!=(15&g)){t.msg="unknown compression method",i.mode=30;break}if(m-=4,I=8+(15&(g>>>=4)),0===i.wbits)i.wbits=I;else if(I>i.wbits){t.msg="invalid window size",i.mode=30;break}i.dmax=1<<I,t.adler=i.check=1,i.mode=512&g?10:12,g=0,m=0;break;case 2:for(;m<16;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(i.flags=g,8!=(255&i.flags)){t.msg="unknown compression method",i.mode=30;break}if(57344&i.flags){t.msg="unknown header flags set",i.mode=30;break}i.head&&(i.head.text=g>>8&1),512&i.flags&&(R[0]=255&g,R[1]=g>>>8&255,i.check=s(i.check,R,2,0)),g=0,m=0,i.mode=3;case 3:for(;m<32;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}i.head&&(i.head.time=g),512&i.flags&&(R[0]=255&g,R[1]=g>>>8&255,R[2]=g>>>16&255,R[3]=g>>>24&255,i.check=s(i.check,R,4,0)),g=0,m=0,i.mode=4;case 4:for(;m<16;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}i.head&&(i.head.xflags=255&g,i.head.os=g>>8),512&i.flags&&(R[0]=255&g,R[1]=g>>>8&255,i.check=s(i.check,R,2,0)),g=0,m=0,i.mode=5;case 5:if(1024&i.flags){for(;m<16;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}i.length=g,i.head&&(i.head.extra_len=g),512&i.flags&&(R[0]=255&g,R[1]=g>>>8&255,i.check=s(i.check,R,2,0)),g=0,m=0}else i.head&&(i.head.extra=null);i.mode=6;case 6:if(1024&i.flags&&((M=i.length)>d&&(M=d),M&&(i.head&&(I=i.head.extra_len-i.length,i.head.extra||(i.head.extra=new Array(i.head.extra_len)),r.arraySet(i.head.extra,l,f,M,I)),512&i.flags&&(i.check=s(i.check,l,M,f)),d-=M,f+=M,i.length-=M),i.length))break t;i.length=0,i.mode=7;case 7:if(2048&i.flags){if(0===d)break t;M=0;do{I=l[f+M++],i.head&&I&&i.length<65536&&(i.head.name+=String.fromCharCode(I))}while(I&&M<d);if(512&i.flags&&(i.check=s(i.check,l,M,f)),d-=M,f+=M,I)break t}else i.head&&(i.head.name=null);i.length=0,i.mode=8;case 8:if(4096&i.flags){if(0===d)break t;M=0;do{I=l[f+M++],i.head&&I&&i.length<65536&&(i.head.comment+=String.fromCharCode(I))}while(I&&M<d);if(512&i.flags&&(i.check=s(i.check,l,M,f)),d-=M,f+=M,I)break t}else i.head&&(i.head.comment=null);i.mode=9;case 9:if(512&i.flags){for(;m<16;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(g!==(65535&i.check)){t.msg="header crc mismatch",i.mode=30;break}g=0,m=0}i.head&&(i.head.hcrc=i.flags>>9&1,i.head.done=!0),t.adler=i.check=0,i.mode=12;break;case 10:for(;m<32;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}t.adler=i.check=h(g),g=0,m=0,i.mode=11;case 11:if(0===i.havedict)return t.next_out=c,t.avail_out=p,t.next_in=f,t.avail_in=d,i.hold=g,i.bits=m,2;t.adler=i.check=1,i.mode=12;case 12:if(5===e||6===e)break t;case 13:if(i.last){g>>>=7&m,m-=7&m,i.mode=27;break}for(;m<3;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}switch(i.last=1&g,m-=1,3&(g>>>=1)){case 0:i.mode=14;break;case 1:if(_(i),i.mode=20,6===e){g>>>=2,m-=2;break t}break;case 2:i.mode=17;break;case 3:t.msg="invalid block type",i.mode=30}g>>>=2,m-=2;break;case 14:for(g>>>=7&m,m-=7&m;m<32;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if((65535&g)!=(g>>>16^65535)){t.msg="invalid stored block lengths",i.mode=30;break}if(i.length=65535&g,g=0,m=0,i.mode=15,6===e)break t;case 15:i.mode=16;case 16:if(M=i.length){if(M>d&&(M=d),M>p&&(M=p),0===M)break t;r.arraySet(u,l,f,M,c),d-=M,f+=M,p-=M,c+=M,i.length-=M;break}i.mode=12;break;case 17:for(;m<14;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(i.nlen=257+(31&g),g>>>=5,m-=5,i.ndist=1+(31&g),g>>>=5,m-=5,i.ncode=4+(15&g),g>>>=4,m-=4,i.nlen>286||i.ndist>30){t.msg="too many length or distance symbols",i.mode=30;break}i.have=0,i.mode=18;case 18:for(;i.have<i.ncode;){for(;m<3;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}i.lens[z[i.have++]]=7&g,g>>>=3,m-=3}for(;i.have<19;)i.lens[z[i.have++]]=0;if(i.lencode=i.lendyn,i.lenbits=7,N={bits:i.lenbits},O=o(0,i.lens,0,19,i.lencode,0,i.work,N),i.lenbits=N.bits,O){t.msg="invalid code lengths set",i.mode=30;break}i.have=0,i.mode=19;case 19:for(;i.have<i.nlen+i.ndist;){for(;S=(T=i.lencode[g&(1<<i.lenbits)-1])>>>16&255,k=65535&T,!((C=T>>>24)<=m);){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(k<16)g>>>=C,m-=C,i.lens[i.have++]=k;else{if(16===k){for(L=C+2;m<L;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(g>>>=C,m-=C,0===i.have){t.msg="invalid bit length repeat",i.mode=30;break}I=i.lens[i.have-1],M=3+(3&g),g>>>=2,m-=2}else if(17===k){for(L=C+3;m<L;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}m-=C,I=0,M=3+(7&(g>>>=C)),g>>>=3,m-=3}else{for(L=C+7;m<L;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}m-=C,I=0,M=11+(127&(g>>>=C)),g>>>=7,m-=7}if(i.have+M>i.nlen+i.ndist){t.msg="invalid bit length repeat",i.mode=30;break}for(;M--;)i.lens[i.have++]=I}}if(30===i.mode)break;if(0===i.lens[256]){t.msg="invalid code -- missing end-of-block",i.mode=30;break}if(i.lenbits=9,N={bits:i.lenbits},O=o(1,i.lens,0,i.nlen,i.lencode,0,i.work,N),i.lenbits=N.bits,O){t.msg="invalid literal/lengths set",i.mode=30;break}if(i.distbits=6,i.distcode=i.distdyn,N={bits:i.distbits},O=o(2,i.lens,i.nlen,i.ndist,i.distcode,0,i.work,N),i.distbits=N.bits,O){t.msg="invalid distances set",i.mode=30;break}if(i.mode=20,6===e)break t;case 20:i.mode=21;case 21:if(d>=6&&p>=258){t.next_out=c,t.avail_out=p,t.next_in=f,t.avail_in=d,i.hold=g,i.bits=m,a(t,b),c=t.next_out,u=t.output,p=t.avail_out,f=t.next_in,l=t.input,d=t.avail_in,g=i.hold,m=i.bits,12===i.mode&&(i.back=-1);break}for(i.back=0;S=(T=i.lencode[g&(1<<i.lenbits)-1])>>>16&255,k=65535&T,!((C=T>>>24)<=m);){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(S&&0==(240&S)){for(E=C,A=S,P=k;S=(T=i.lencode[P+((g&(1<<E+A)-1)>>E)])>>>16&255,k=65535&T,!(E+(C=T>>>24)<=m);){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}g>>>=E,m-=E,i.back+=E}if(g>>>=C,m-=C,i.back+=C,i.length=k,0===S){i.mode=26;break}if(32&S){i.back=-1,i.mode=12;break}if(64&S){t.msg="invalid literal/length code",i.mode=30;break}i.extra=15&S,i.mode=22;case 22:if(i.extra){for(L=i.extra;m<L;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}i.length+=g&(1<<i.extra)-1,g>>>=i.extra,m-=i.extra,i.back+=i.extra}i.was=i.length,i.mode=23;case 23:for(;S=(T=i.distcode[g&(1<<i.distbits)-1])>>>16&255,k=65535&T,!((C=T>>>24)<=m);){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(0==(240&S)){for(E=C,A=S,P=k;S=(T=i.distcode[P+((g&(1<<E+A)-1)>>E)])>>>16&255,k=65535&T,!(E+(C=T>>>24)<=m);){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}g>>>=E,m-=E,i.back+=E}if(g>>>=C,m-=C,i.back+=C,64&S){t.msg="invalid distance code",i.mode=30;break}i.offset=k,i.extra=15&S,i.mode=24;case 24:if(i.extra){for(L=i.extra;m<L;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}i.offset+=g&(1<<i.extra)-1,g>>>=i.extra,m-=i.extra,i.back+=i.extra}if(i.offset>i.dmax){t.msg="invalid distance too far back",i.mode=30;break}i.mode=25;case 25:if(0===p)break t;if(M=b-p,i.offset>M){if((M=i.offset-M)>i.whave&&i.sane){t.msg="invalid distance too far back",i.mode=30;break}M>i.wnext?(M-=i.wnext,w=i.wsize-M):w=i.wnext-M,M>i.length&&(M=i.length),x=i.window}else x=u,w=c-i.offset,M=i.length;M>p&&(M=p),p-=M,i.length-=M;do{u[c++]=x[w++]}while(--M);0===i.length&&(i.mode=21);break;case 26:if(0===p)break t;u[c++]=i.length,p--,i.mode=21;break;case 27:if(i.wrap){for(;m<32;){if(0===d)break t;d--,g|=l[f++]<<m,m+=8}if(b-=p,t.total_out+=b,i.total+=b,b&&(t.adler=i.check=i.flags?s(i.check,u,b,c-b):n(i.check,u,b,c-b)),b=p,(i.flags?g:h(g))!==i.check){t.msg="incorrect data check",i.mode=30;break}g=0,m=0}i.mode=28;case 28:if(i.wrap&&i.flags){for(;m<32;){if(0===d)break t;d--,g+=l[f++]<<m,m+=8}if(g!==(4294967295&i.total)){t.msg="incorrect length check",i.mode=30;break}g=0,m=0}i.mode=29;case 29:O=1;break t;case 30:O=-3;break t;case 31:return-4;case 32:default:return-2}return t.next_out=c,t.avail_out=p,t.next_in=f,t.avail_in=d,i.hold=g,i.bits=m,(i.wsize||b!==t.avail_out&&i.mode<30&&(i.mode<27||4!==e))&&y(t,t.output,t.next_out,b-t.avail_out)?(i.mode=31,-4):(v-=t.avail_in,b-=t.avail_out,t.total_in+=v,t.total_out+=b,i.total+=b,i.wrap&&b&&(t.adler=i.check=i.flags?s(i.check,u,b,t.next_out-b):n(i.check,u,b,t.next_out-b)),t.data_type=i.bits+(i.last?64:0)+(12===i.mode?128:0)+(20===i.mode||15===i.mode?256:0),(0===v&&0===b||4===e)&&0===O&&(O=-5),O)},e.inflateEnd=function(t){if(!t||!t.state)return-2;var e=t.state;return e.window&&(e.window=null),t.state=null,0},e.inflateGetHeader=function(t,e){var i;return t&&t.state?0==(2&(i=t.state).wrap)?-2:(i.head=e,e.done=!1,0):-2},e.inflateSetDictionary=function(t,e){var i,r=e.length;return t&&t.state?0!==(i=t.state).wrap&&11!==i.mode?-2:11===i.mode&&n(1,e,r,0)!==i.check?-3:y(t,e,r,r)?(i.mode=31,-4):(i.havedict=1,0):-2},e.inflateInfo="pako inflate (from Nodeca project)"},function(t,e,i){"use strict";t.exports=function(t,e){var i,r,n,s,a,o,h,l,u,f,c,d,p,g,m,_,y,v,b,M,w,x,C,S,k;i=t.state,r=t.next_in,S=t.input,n=r+(t.avail_in-5),s=t.next_out,k=t.output,a=s-(e-t.avail_out),o=s+(t.avail_out-257),h=i.dmax,l=i.wsize,u=i.whave,f=i.wnext,c=i.window,d=i.hold,p=i.bits,g=i.lencode,m=i.distcode,_=(1<<i.lenbits)-1,y=(1<<i.distbits)-1;t:do{p<15&&(d+=S[r++]<<p,p+=8,d+=S[r++]<<p,p+=8),v=g[d&_];e:for(;;){if(d>>>=b=v>>>24,p-=b,0===(b=v>>>16&255))k[s++]=65535&v;else{if(!(16&b)){if(0==(64&b)){v=g[(65535&v)+(d&(1<<b)-1)];continue e}if(32&b){i.mode=12;break t}t.msg="invalid literal/length code",i.mode=30;break t}M=65535&v,(b&=15)&&(p<b&&(d+=S[r++]<<p,p+=8),M+=d&(1<<b)-1,d>>>=b,p-=b),p<15&&(d+=S[r++]<<p,p+=8,d+=S[r++]<<p,p+=8),v=m[d&y];i:for(;;){if(d>>>=b=v>>>24,p-=b,!(16&(b=v>>>16&255))){if(0==(64&b)){v=m[(65535&v)+(d&(1<<b)-1)];continue i}t.msg="invalid distance code",i.mode=30;break t}if(w=65535&v,p<(b&=15)&&(d+=S[r++]<<p,(p+=8)<b&&(d+=S[r++]<<p,p+=8)),(w+=d&(1<<b)-1)>h){t.msg="invalid distance too far back",i.mode=30;break t}if(d>>>=b,p-=b,w>(b=s-a)){if((b=w-b)>u&&i.sane){t.msg="invalid distance too far back",i.mode=30;break t}if(x=0,C=c,0===f){if(x+=l-b,b<M){M-=b;do{k[s++]=c[x++]}while(--b);x=s-w,C=k}}else if(f<b){if(x+=l+f-b,(b-=f)<M){M-=b;do{k[s++]=c[x++]}while(--b);if(x=0,f<M){M-=b=f;do{k[s++]=c[x++]}while(--b);x=s-w,C=k}}}else if(x+=f-b,b<M){M-=b;do{k[s++]=c[x++]}while(--b);x=s-w,C=k}for(;M>2;)k[s++]=C[x++],k[s++]=C[x++],k[s++]=C[x++],M-=3;M&&(k[s++]=C[x++],M>1&&(k[s++]=C[x++]))}else{x=s-w;do{k[s++]=k[x++],k[s++]=k[x++],k[s++]=k[x++],M-=3}while(M>2);M&&(k[s++]=k[x++],M>1&&(k[s++]=k[x++]))}break}}break}}while(r<n&&s<o);r-=M=p>>3,d&=(1<<(p-=M<<3))-1,t.next_in=r,t.next_out=s,t.avail_in=r<n?n-r+5:5-(r-n),t.avail_out=s<o?o-s+257:257-(s-o),i.hold=d,i.bits=p}},function(t,e,i){"use strict";var r=i(3),n=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],s=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],o=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(t,e,i,h,l,u,f,c){var d,p,g,m,_,y,v,b,M,w=c.bits,x=0,C=0,S=0,k=0,E=0,A=0,P=0,I=0,O=0,N=0,L=null,T=0,R=new r.Buf16(16),z=new r.Buf16(16),B=null,D=0;for(x=0;x<=15;x++)R[x]=0;for(C=0;C<h;C++)R[e[i+C]]++;for(E=w,k=15;k>=1&&0===R[k];k--);if(E>k&&(E=k),0===k)return l[u++]=20971520,l[u++]=20971520,c.bits=1,0;for(S=1;S<k&&0===R[S];S++);for(E<S&&(E=S),I=1,x=1;x<=15;x++)if(I<<=1,(I-=R[x])<0)return-1;if(I>0&&(0===t||1!==k))return-1;for(z[1]=0,x=1;x<15;x++)z[x+1]=z[x]+R[x];for(C=0;C<h;C++)0!==e[i+C]&&(f[z[e[i+C]]++]=C);if(0===t?(L=B=f,y=19):1===t?(L=n,T-=257,B=s,D-=257,y=256):(L=a,B=o,y=-1),N=0,C=0,x=S,_=u,A=E,P=0,g=-1,m=(O=1<<E)-1,1===t&&O>852||2===t&&O>592)return 1;for(;;){v=x-P,f[C]<y?(b=0,M=f[C]):f[C]>y?(b=B[D+f[C]],M=L[T+f[C]]):(b=96,M=0),d=1<<x-P,S=p=1<<A;do{l[_+(N>>P)+(p-=d)]=v<<24|b<<16|M|0}while(0!==p);for(d=1<<x-1;N&d;)d>>=1;if(0!==d?(N&=d-1,N+=d):N=0,C++,0==--R[x]){if(x===k)break;x=e[i+f[C]]}if(x>E&&(N&m)!==g){for(0===P&&(P=E),_+=S,I=1<<(A=x-P);A+P<k&&!((I-=R[A+P])<=0);)A++,I<<=1;if(O+=1<<A,1===t&&O>852||2===t&&O>592)return 1;l[g=N&m]=E<<24|A<<16|_-u|0}}return 0!==N&&(l[_+N]=x-P<<24|64<<16|0),c.bits=E,0}},function(t,e,i){"use strict";t.exports=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}},function(t,e,i){"use strict";var r=i(2),n=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];t.exports=function(t,e){if(void 0===t||!t.length)return 0;var i="string"!==r.getTypeOf(t);void 0===e&&(e=0);var s=0;e^=-1;for(var a=0,o=t.length;a<o;a++)s=i?t[a]:t.charCodeAt(a),e=e>>>8^n[255&(e^s)];return-1^e}},function(t,e,i){"use strict";var r=i(2),n=function(){this.data=[]};n.prototype={append:function(t){t=r.transformTo("string",t),this.data.push(t)},finalize:function(){return this.data.join("")}},t.exports=n},function(t,e,i){"use strict";var r=i(2),n=function(t){this.data=new Uint8Array(t),this.index=0};n.prototype={append:function(t){0!==t.length&&(t=r.transformTo("uint8array",t),this.data.set(t,this.index),this.index+=t.length)},finalize:function(){return this.data}},t.exports=n},function(t,e,i){"use strict";var r=i(8),n=i(21),s=i(2),a=i(47);t.exports=function(t,e){var i,o,h,l;for((e=s.extend(e||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:n.utf8decode})).base64&&(t=r.decode(t)),i=(o=new a(t,e)).files,h=0;h<i.length;h++)l=i[h],this.file(l.fileNameStr,l.decompressed,{binary:!0,optimizedBinaryString:!0,date:l.date,dir:l.dir,comment:l.fileCommentStr.length?l.fileCommentStr:null,unixPermissions:l.unixPermissions,dosPermissions:l.dosPermissions,createFolders:e.createFolders});return o.zipComment.length&&(this.comment=o.zipComment),this}},function(t,e,i){"use strict";var r=i(22),n=i(48),s=i(24),a=i(25),o=i(2),h=i(18),l=i(49),u=i(4);i(9);function f(t,e){this.files=[],this.loadOptions=e,t&&this.load(t)}f.prototype={checkSignature:function(t){var e=this.reader.readString(4);if(e!==t)throw new Error("Corrupted zip or bug : unexpected signature ("+o.pretty(e)+", expected "+o.pretty(t)+")")},isSignature:function(t,e){var i=this.reader.index;this.reader.setIndex(t);var r=this.reader.readString(4)===e;return this.reader.setIndex(i),r},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var t=this.reader.readData(this.zipCommentLength),e=u.uint8array?"uint8array":"array",i=o.transformTo(e,t);this.zipComment=this.loadOptions.decodeFileName(i)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.versionMadeBy=this.reader.readString(2),this.versionNeeded=this.reader.readInt(2),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var t,e,i,r=this.zip64EndOfCentralSize-44;0<r;)t=this.reader.readInt(2),e=this.reader.readInt(4),i=this.reader.readString(e),this.zip64ExtensibleData[t]={id:t,length:e,value:i}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var t,e;for(t=0;t<this.files.length;t++)e=this.files[t],this.reader.setIndex(e.localHeaderOffset),this.checkSignature(h.LOCAL_FILE_HEADER),e.readLocalPart(this.reader),e.handleUTF8(),e.processAttributes()},readCentralDir:function(){var t;for(this.reader.setIndex(this.centralDirOffset);this.reader.readString(4)===h.CENTRAL_FILE_HEADER;)(t=new l({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(t);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var t=this.reader.lastIndexOfSignature(h.CENTRAL_DIRECTORY_END);if(t<0)throw!this.isSignature(0,h.LOCAL_FILE_HEADER)?new Error("Can't find end of central directory : is this a zip file ? If it is, see http://stuk.github.io/jszip/documentation/howto/read_zip.html"):new Error("Corrupted zip : can't find end of central directory");this.reader.setIndex(t);var e=t;if(this.checkSignature(h.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===o.MAX_VALUE_16BITS||this.diskWithCentralDirStart===o.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===o.MAX_VALUE_16BITS||this.centralDirRecords===o.MAX_VALUE_16BITS||this.centralDirSize===o.MAX_VALUE_32BITS||this.centralDirOffset===o.MAX_VALUE_32BITS){if(this.zip64=!0,(t=this.reader.lastIndexOfSignature(h.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip : can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(t),this.checkSignature(h.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,h.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(h.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip : can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(h.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var i=this.centralDirOffset+this.centralDirSize;this.zip64&&(i+=20,i+=12+this.zip64EndOfCentralSize);var r=e-i;if(r>0)this.isSignature(e,h.CENTRAL_FILE_HEADER)||(this.reader.zero=r);else if(r<0)throw new Error("Corrupted zip: missing "+Math.abs(r)+" bytes.")},prepareReader:function(t){var e=o.getTypeOf(t);if(o.checkSupport(e),"string"!==e||u.uint8array)if("nodebuffer"===e)this.reader=new n(t);else if(u.uint8array)this.reader=new s(o.transformTo("uint8array",t));else{if(!u.array)throw new Error("Unexpected error: unsupported type '"+e+"'");this.reader=new a(o.transformTo("array",t))}else this.reader=new r(t,this.loadOptions.optimizedBinaryString)},load:function(t){this.prepareReader(t),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},t.exports=f},function(t,e,i){"use strict";var r=i(24);function n(t){this.data=t,this.length=this.data.length,this.index=0,this.zero=0}n.prototype=new r,n.prototype.readData=function(t){this.checkOffset(t);var e=this.data.slice(this.zero+this.index,this.zero+this.index+t);return this.index+=t,e},t.exports=n},function(t,e,i){"use strict";var r=i(22),n=i(2),s=i(20),a=i(9),o=i(4);function h(t,e){this.options=t,this.loadOptions=e}h.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},prepareCompressedContent:function(t,e,i){return function(){var r=t.index;t.setIndex(e);var n=t.readData(i);return t.setIndex(r),n}},prepareContent:function(t,e,i,r,s){return function(){var t=n.transformTo(r.uncompressInputType,this.getCompressedContent()),e=r.uncompress(t);if(e.length!==s)throw new Error("Bug : uncompressed data size mismatch");return e}},readLocalPart:function(t){var e,i;if(t.skip(22),this.fileNameLength=t.readInt(2),i=t.readInt(2),this.fileName=t.readData(this.fileNameLength),t.skip(i),-1==this.compressedSize||-1==this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough informations from the central directory (compressedSize == -1 || uncompressedSize == -1)");if(null===(e=n.findCompression(this.compressionMethod)))throw new Error("Corrupted zip : compression "+n.pretty(this.compressionMethod)+" unknown (inner file : "+n.transformTo("string",this.fileName)+")");if(this.decompressed=new s,this.decompressed.compressedSize=this.compressedSize,this.decompressed.uncompressedSize=this.uncompressedSize,this.decompressed.crc32=this.crc32,this.decompressed.compressionMethod=this.compressionMethod,this.decompressed.getCompressedContent=this.prepareCompressedContent(t,t.index,this.compressedSize,e),this.decompressed.getContent=this.prepareContent(t,t.index,this.compressedSize,e,this.uncompressedSize),this.loadOptions.checkCRC32&&(this.decompressed=n.transformTo("string",this.decompressed.getContent()),a.crc32(this.decompressed)!==this.crc32))throw new Error("Corrupted zip : CRC32 mismatch")},readCentralPart:function(t){if(this.versionMadeBy=t.readInt(2),this.versionNeeded=t.readInt(2),this.bitFlag=t.readInt(2),this.compressionMethod=t.readString(2),this.date=t.readDate(),this.crc32=t.readInt(4),this.compressedSize=t.readInt(4),this.uncompressedSize=t.readInt(4),this.fileNameLength=t.readInt(2),this.extraFieldsLength=t.readInt(2),this.fileCommentLength=t.readInt(2),this.diskNumberStart=t.readInt(2),this.internalFileAttributes=t.readInt(2),this.externalFileAttributes=t.readInt(4),this.localHeaderOffset=t.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");this.fileName=t.readData(this.fileNameLength),this.readExtraFields(t),this.parseZIP64ExtraField(t),this.fileComment=t.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var t=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0===t&&(this.dosPermissions=63&this.externalFileAttributes),3===t&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(t){if(this.extraFields[1]){var e=new r(this.extraFields[1].value);this.uncompressedSize===n.MAX_VALUE_32BITS&&(this.uncompressedSize=e.readInt(8)),this.compressedSize===n.MAX_VALUE_32BITS&&(this.compressedSize=e.readInt(8)),this.localHeaderOffset===n.MAX_VALUE_32BITS&&(this.localHeaderOffset=e.readInt(8)),this.diskNumberStart===n.MAX_VALUE_32BITS&&(this.diskNumberStart=e.readInt(4))}},readExtraFields:function(t){var e,i,r,n=t.index;for(this.extraFields=this.extraFields||{};t.index<n+this.extraFieldsLength;)e=t.readInt(2),i=t.readInt(2),r=t.readString(i),this.extraFields[e]={id:e,length:i,value:r}},handleUTF8:function(){var t=o.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=a.utf8decode(this.fileName),this.fileCommentStr=a.utf8decode(this.fileComment);else{var e=this.findExtraFieldUnicodePath();if(null!==e)this.fileNameStr=e;else{var i=n.transformTo(t,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(i)}var r=this.findExtraFieldUnicodeComment();if(null!==r)this.fileCommentStr=r;else{var s=n.transformTo(t,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(s)}}},findExtraFieldUnicodePath:function(){var t=this.extraFields[28789];if(t){var e=new r(t.value);return 1!==e.readInt(1)||a.crc32(this.fileName)!==e.readInt(4)?null:a.utf8decode(e.readString(t.length-5))}return null},findExtraFieldUnicodeComment:function(){var t=this.extraFields[25461];if(t){var e=new r(t.value);return 1!==e.readInt(1)||a.crc32(this.fileComment)!==e.readInt(4)?null:a.utf8decode(e.readString(t.length-5))}return null}},t.exports=h},function(t,e,i){"use strict";var r=i(2);e.string2binary=function(t){return r.string2binary(t)},e.string2Uint8Array=function(t){return r.transformTo("uint8array",t)},e.uint8Array2String=function(t){return r.transformTo("string",t)},e.string2Blob=function(t){var e=r.transformTo("arraybuffer",t);return r.arrayBuffer2Blob(e)},e.arrayBuffer2Blob=function(t){return r.arrayBuffer2Blob(t)},e.transformTo=function(t,e){return r.transformTo(t,e)},e.getTypeOf=function(t){return r.getTypeOf(t)},e.checkSupport=function(t){return r.checkSupport(t)},e.MAX_VALUE_16BITS=r.MAX_VALUE_16BITS,e.MAX_VALUE_32BITS=r.MAX_VALUE_32BITS,e.pretty=function(t){return r.pretty(t)},e.findCompression=function(t){return r.findCompression(t)},e.isRegExp=function(t){return r.isRegExp(t)}},function(t,e,i){"use strict";(function(e){var r=i(52),n=i(5).Buffer;t.exports=async function(t){if(!e.fetch)return r(t);var i=t.slice(-3).toLowerCase(),s="prj"===i||"cpg"===i;try{if((a=await fetch(t)).status>399)throw new Error(a.statusText);if(s)return a.text();var a=await a.arrayBuffer();return n.from(a)}catch(t){if(s)return!1;throw t}}}).call(this,i(7))},function(t,e,i){"use strict";var r=i(26),n=i(5).Buffer;t.exports=function(t){return new r((function(e,i){var r=t.slice(-3),s=new XMLHttpRequest;s.open("GET",t,!0),"prj"!==r&&"cpg"!==r&&(s.responseType="arraybuffer"),s.addEventListener("load",(function(){return s.status>399?"prj"===r||"cpg"===r?e(!1):i(new Error(s.status)):e("prj"!==r&&"cpg"!==r?n.from(s.response):s.response)}),!1),s.send()}))}},function(t,e,i){"use strict";(function(e){var i,r,n=e.MutationObserver||e.WebKitMutationObserver;if(n){var s=0,a=new n(u),o=e.document.createTextNode("");a.observe(o,{characterData:!0}),i=function(){o.data=s=++s%2}}else if(e.setImmediate||void 0===e.MessageChannel)i="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){u(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(u,0)};else{var h=new e.MessageChannel;h.port1.onmessage=u,i=function(){h.port2.postMessage(0)}}var l=[];function u(){var t,e;r=!0;for(var i=l.length;i;){for(e=l,l=[],t=-1;++t<i;)e[t]();i=l.length}r=!1}t.exports=function(t){1!==l.push(t)||r||i()}}).call(this,i(7))},function(t,e,i){"use strict";function r(t,e){return!function(t){for(var e,i,r=0,n=1,s=t.length;n<s;)e=i||t[0],r+=((i=t[n])[0]-e[0])*(i[1]+e[1]),n++;return r>0}(e)&&t.length?t[t.length-1].push(e):t.push([e]),t}s.prototype.parsePoint=function(t){return{type:"Point",coordinates:this.parseCoord(t,0)}},s.prototype.parseZPoint=function(t){var e=this.parsePoint(t);return e.coordinates.push(t.readDoubleLE(16)),e},s.prototype.parsePointArray=function(t,e,i){for(var r=[],n=0;n<i;)r.push(this.parseCoord(t,e)),e+=16,n++;return r},s.prototype.parseZPointArray=function(t,e,i,r){for(var n=0;n<i;)r[n].push(t.readDoubleLE(e)),n++,e+=8;return r},s.prototype.parseArrayGroup=function(t,e,i,r,n){for(var s,a,o=[],h=0,l=0;h<r;)i+=4,s=l,(a=(l=++h===r?n:t.readInt32LE(i))-s)&&(o.push(this.parsePointArray(t,e,a)),e+=a<<4);return o},s.prototype.parseZArrayGroup=function(t,e,i,r){for(var n=0;n<i;)r[n]=this.parseZPointArray(t,e,r[n].length,r[n]),e+=r[n].length<<3,n++;return r},s.prototype.parseMultiPoint=function(t){var e={},i=this.parseCoord(t,0),r=this.parseCoord(t,16);e.bbox=[i[0],i[1],r[0],r[1]];var n=t.readInt32LE(32,!0);return 1===n?(e.type="Point",e.coordinates=this.parseCoord(t,36)):(e.type="MultiPoint",e.coordinates=this.parsePointArray(t,36,n)),e},s.prototype.parseZMultiPoint=function(t){var e,i=this.parseMultiPoint(t);if("Point"===i.type)return i.coordinates.push(t.readDoubleLE(72)),i;var r=52+((e=i.coordinates.length)<<4);return i.coordinates=this.parseZPointArray(t,r,e,i.coordinates),i},s.prototype.parsePolyline=function(t){var e={},i=this.parseCoord(t,0),r=this.parseCoord(t,16);e.bbox=[i[0],i[1],r[0],r[1]];var n,s=t.readInt32LE(32),a=t.readInt32LE(36);return 1===s?(e.type="LineString",n=44,e.coordinates=this.parsePointArray(t,n,a)):(e.type="MultiLineString",n=40+(s<<2),40,e.coordinates=this.parseArrayGroup(t,n,40,s,a)),e},s.prototype.parseZPolyline=function(t){var e,i=this.parsePolyline(t),r=i.coordinates.length;return"LineString"===i.type?(e=60+(r<<4),i.coordinates=this.parseZPointArray(t,e,r,i.coordinates),i):(e=56+(i.coordinates.reduce((function(t,e){return t+e.length}),0)<<4)+(r<<2),i.coordinates=this.parseZArrayGroup(t,e,r,i.coordinates),i)},s.prototype.polyFuncs=function(t){return"LineString"===t.type?(t.type="Polygon",t.coordinates=[t.coordinates],t):(t.coordinates=t.coordinates.reduce(r,[]),1===t.coordinates.length?(t.type="Polygon",t.coordinates=t.coordinates[0],t):(t.type="MultiPolygon",t))},s.prototype.parsePolygon=function(t){return this.polyFuncs(this.parsePolyline(t))},s.prototype.parseZPolygon=function(t){return this.polyFuncs(this.parseZPolyline(t))};var n={1:"parsePoint",3:"parsePolyline",5:"parsePolygon",8:"parseMultiPoint",11:"parseZPoint",13:"parseZPolyline",15:"parseZPolygon",18:"parseZMultiPoint"};function s(t,e){if(!(this instanceof s))return new s(t,e);this.buffer=t,this.shpFuncs(e),this.rows=this.getRows()}s.prototype.shpFuncs=function(t){var e,i=this.getShpCode();if(i>20&&(i-=20),!(i in n))throw new Error("I don't know that shp type");this.parseFunc=this[n[i]],this.parseCoord=(e=t)?function(t,i){return e.inverse([t.readDoubleLE(i),t.readDoubleLE(i+8)])}:function(t,e){return[t.readDoubleLE(e),t.readDoubleLE(e+8)]}},s.prototype.getShpCode=function(){return this.parseHeader().shpCode},s.prototype.parseHeader=function(){var t=this.buffer.slice(0,100);return{length:t.readInt32BE(24),version:t.readInt32LE(28),shpCode:t.readInt32LE(32),bbox:[t.readDoubleLE(36),t.readDoubleLE(44),t.readDoubleLE(52),t.readDoubleLE(52)]}},s.prototype.getRows=function(){for(var t,e=100,i=this.buffer.byteLength,r=[];e<i;)t=this.getRow(e),e+=8,e+=t.len,t.type?r.push(this.parseFunc(t.data)):r.push(null);return r},s.prototype.getRow=function(t){var e=this.buffer.slice(t,t+12),i=e.readInt32BE(4)<<1,r=this.buffer.slice(t+12,t+i+8);return{id:e.readInt32BE(0),len:i,data:r,type:e.readInt32LE(8)}},t.exports=function(t,e){return new s(t,e).rows}},function(t,e,i){var r=i(56);function n(t,e,i,r,n){var s=n(t.slice(e,e+i));switch(r){case"N":case"F":case"O":return parseFloat(s,10);case"D":return new Date(s.slice(0,4),parseInt(s.slice(4,6),10)-1,s.slice(6,8));case"L":return"y"===s.toLowerCase()||"t"===s.toLowerCase();default:return s}}function s(t,e,i,r){for(var s,a,o={},h=0,l=i.length;h<l;)s=n(t,e,(a=i[h]).len,a.dataType,r),e+=a.len,void 0!==s&&(o[a.name]=s),h++;return o}t.exports=function(t,e){for(var i=r(e),n=function(t){var e={};return e.lastUpdated=new Date(t.readUInt8(1)+1900,t.readUInt8(2),t.readUInt8(3)),e.records=t.readUInt32LE(4),e.headerLen=t.readUInt16LE(8),e.recLen=t.readUInt16LE(10),e}(t),a=function(t,e,i){for(var r=[],n=32;n<e&&(r.push({name:i(t.slice(n,n+11)),dataType:String.fromCharCode(t.readUInt8(n+11)),len:t.readUInt8(n+16),decimal:t.readUInt8(n+17)}),13!==t.readUInt8(n+32));)n+=32;return r}(t,n.headerLen-1,i),o=2+(a.length+1<<5),h=n.recLen,l=n.records,u=[];l;)u.push(s(t,o,a,i)),o+=h,l--;return u}},function(t,e,i){i(57);var r=i(60).StringDecoder;function n(t){var e=new r;return(e.write(t)+e.end()).replace(/\0/g,"").trim()}t.exports=function t(e,i){if(!e)return n;try{new TextDecoder(e.trim())}catch(a){var r=s.exec(e);return r&&!i?t("windows-"+r[1],!0):n}return function(t){var i=new TextDecoder(e);return(i.decode(t,{stream:!0})+i.decode()).replace(/\0/g,"").trim()}};var s=/^(?:ANSI\s)?(\d+)$/m},function(t,e,i){t.exports=i(58)},function(t,e,i){!function(e){"use strict";function r(t,e,i){return e<=t&&t<=i}t.exports&&!e["encoding-indexes"]&&i(59);var n=Math.floor;function s(t){if(void 0===t)return{};if(t===Object(t))return t;throw TypeError("Could not convert argument to dictionary")}function a(t){return 0<=t&&t<=127}var o=a;function h(t){this.tokens=[].slice.call(t),this.tokens.reverse()}h.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.pop():-1},prepend:function(t){if(Array.isArray(t))for(var e=t;e.length;)this.tokens.push(e.pop());else this.tokens.push(t)},push:function(t){if(Array.isArray(t))for(var e=t;e.length;)this.tokens.unshift(e.shift());else this.tokens.unshift(t)}};function l(t,e){if(t)throw TypeError("Decoder error");return e||65533}function u(t){throw TypeError("The code point "+t+" could not be encoded.")}function f(t){return t=String(t).trim().toLowerCase(),Object.prototype.hasOwnProperty.call(d,t)?d[t]:null}var c=[{encodings:[{labels:["unicode-1-1-utf-8","utf-8","utf8"],name:"UTF-8"}],heading:"The Encoding"},{encodings:[{labels:["866","cp866","csibm866","ibm866"],name:"IBM866"},{labels:["csisolatin2","iso-8859-2","iso-ir-101","iso8859-2","iso88592","iso_8859-2","iso_8859-2:1987","l2","latin2"],name:"ISO-8859-2"},{labels:["csisolatin3","iso-8859-3","iso-ir-109","iso8859-3","iso88593","iso_8859-3","iso_8859-3:1988","l3","latin3"],name:"ISO-8859-3"},{labels:["csisolatin4","iso-8859-4","iso-ir-110","iso8859-4","iso88594","iso_8859-4","iso_8859-4:1988","l4","latin4"],name:"ISO-8859-4"},{labels:["csisolatincyrillic","cyrillic","iso-8859-5","iso-ir-144","iso8859-5","iso88595","iso_8859-5","iso_8859-5:1988"],name:"ISO-8859-5"},{labels:["arabic","asmo-708","csiso88596e","csiso88596i","csisolatinarabic","ecma-114","iso-8859-6","iso-8859-6-e","iso-8859-6-i","iso-ir-127","iso8859-6","iso88596","iso_8859-6","iso_8859-6:1987"],name:"ISO-8859-6"},{labels:["csisolatingreek","ecma-118","elot_928","greek","greek8","iso-8859-7","iso-ir-126","iso8859-7","iso88597","iso_8859-7","iso_8859-7:1987","sun_eu_greek"],name:"ISO-8859-7"},{labels:["csiso88598e","csisolatinhebrew","hebrew","iso-8859-8","iso-8859-8-e","iso-ir-138","iso8859-8","iso88598","iso_8859-8","iso_8859-8:1988","visual"],name:"ISO-8859-8"},{labels:["csiso88598i","iso-8859-8-i","logical"],name:"ISO-8859-8-I"},{labels:["csisolatin6","iso-8859-10","iso-ir-157","iso8859-10","iso885910","l6","latin6"],name:"ISO-8859-10"},{labels:["iso-8859-13","iso8859-13","iso885913"],name:"ISO-8859-13"},{labels:["iso-8859-14","iso8859-14","iso885914"],name:"ISO-8859-14"},{labels:["csisolatin9","iso-8859-15","iso8859-15","iso885915","iso_8859-15","l9"],name:"ISO-8859-15"},{labels:["iso-8859-16"],name:"ISO-8859-16"},{labels:["cskoi8r","koi","koi8","koi8-r","koi8_r"],name:"KOI8-R"},{labels:["koi8-ru","koi8-u"],name:"KOI8-U"},{labels:["csmacintosh","mac","macintosh","x-mac-roman"],name:"macintosh"},{labels:["dos-874","iso-8859-11","iso8859-11","iso885911","tis-620","windows-874"],name:"windows-874"},{labels:["cp1250","windows-1250","x-cp1250"],name:"windows-1250"},{labels:["cp1251","windows-1251","x-cp1251"],name:"windows-1251"},{labels:["ansi_x3.4-1968","ascii","cp1252","cp819","csisolatin1","ibm819","iso-8859-1","iso-ir-100","iso8859-1","iso88591","iso_8859-1","iso_8859-1:1987","l1","latin1","us-ascii","windows-1252","x-cp1252"],name:"windows-1252"},{labels:["cp1253","windows-1253","x-cp1253"],name:"windows-1253"},{labels:["cp1254","csisolatin5","iso-8859-9","iso-ir-148","iso8859-9","iso88599","iso_8859-9","iso_8859-9:1989","l5","latin5","windows-1254","x-cp1254"],name:"windows-1254"},{labels:["cp1255","windows-1255","x-cp1255"],name:"windows-1255"},{labels:["cp1256","windows-1256","x-cp1256"],name:"windows-1256"},{labels:["cp1257","windows-1257","x-cp1257"],name:"windows-1257"},{labels:["cp1258","windows-1258","x-cp1258"],name:"windows-1258"},{labels:["x-mac-cyrillic","x-mac-ukrainian"],name:"x-mac-cyrillic"}],heading:"Legacy single-byte encodings"},{encodings:[{labels:["chinese","csgb2312","csiso58gb231280","gb2312","gb_2312","gb_2312-80","gbk","iso-ir-58","x-gbk"],name:"GBK"},{labels:["gb18030"],name:"gb18030"}],heading:"Legacy multi-byte Chinese (simplified) encodings"},{encodings:[{labels:["big5","big5-hkscs","cn-big5","csbig5","x-x-big5"],name:"Big5"}],heading:"Legacy multi-byte Chinese (traditional) encodings"},{encodings:[{labels:["cseucpkdfmtjapanese","euc-jp","x-euc-jp"],name:"EUC-JP"},{labels:["csiso2022jp","iso-2022-jp"],name:"ISO-2022-JP"},{labels:["csshiftjis","ms932","ms_kanji","shift-jis","shift_jis","sjis","windows-31j","x-sjis"],name:"Shift_JIS"}],heading:"Legacy multi-byte Japanese encodings"},{encodings:[{labels:["cseuckr","csksc56011987","euc-kr","iso-ir-149","korean","ks_c_5601-1987","ks_c_5601-1989","ksc5601","ksc_5601","windows-949"],name:"EUC-KR"}],heading:"Legacy multi-byte Korean encodings"},{encodings:[{labels:["csiso2022kr","hz-gb-2312","iso-2022-cn","iso-2022-cn-ext","iso-2022-kr"],name:"replacement"},{labels:["utf-16be"],name:"UTF-16BE"},{labels:["utf-16","utf-16le"],name:"UTF-16LE"},{labels:["x-user-defined"],name:"x-user-defined"}],heading:"Legacy miscellaneous encodings"}],d={};c.forEach((function(t){t.encodings.forEach((function(t){t.labels.forEach((function(e){d[e]=t}))}))}));var p,g,m={},_={};function y(t,e){return e&&e[t]||null}function v(t,e){var i=e.indexOf(t);return-1===i?null:i}function b(t){if(!("encoding-indexes"in e))throw Error("Indexes missing. Did you forget to include encoding-indexes.js first?");return e["encoding-indexes"][t]}function M(t,e){if(!(this instanceof M))throw TypeError("Called as a function. Did you forget 'new'?");t=void 0!==t?String(t):"utf-8",e=s(e),this._encoding=null,this._decoder=null,this._ignoreBOM=!1,this._BOMseen=!1,this._error_mode="replacement",this._do_not_flush=!1;var i=f(t);if(null===i||"replacement"===i.name)throw RangeError("Unknown encoding: "+t);if(!_[i.name])throw Error("Decoder not present. Did you forget to include encoding-indexes.js first?");return this._encoding=i,Boolean(e.fatal)&&(this._error_mode="fatal"),Boolean(e.ignoreBOM)&&(this._ignoreBOM=!0),Object.defineProperty||(this.encoding=this._encoding.name.toLowerCase(),this.fatal="fatal"===this._error_mode,this.ignoreBOM=this._ignoreBOM),this}function w(t,i){if(!(this instanceof w))throw TypeError("Called as a function. Did you forget 'new'?");i=s(i),this._encoding=null,this._encoder=null,this._do_not_flush=!1,this._fatal=Boolean(i.fatal)?"fatal":"replacement";if(Boolean(i.NONSTANDARD_allowLegacyEncoding)){var r=f(t=void 0!==t?String(t):"utf-8");if(null===r||"replacement"===r.name)throw RangeError("Unknown encoding: "+t);if(!m[r.name])throw Error("Encoder not present. Did you forget to include encoding-indexes.js first?");this._encoding=r}else this._encoding=f("utf-8"),void 0!==t&&"console"in e&&console.warn("TextEncoder constructor called with encoding label, which is ignored.");return Object.defineProperty||(this.encoding=this._encoding.name.toLowerCase()),this}function x(t){var e=t.fatal,i=0,n=0,s=0,a=128,o=191;this.handler=function(t,h){if(-1===h&&0!==s)return s=0,l(e);if(-1===h)return-1;if(0===s){if(r(h,0,127))return h;if(r(h,194,223))s=1,i=31&h;else if(r(h,224,239))224===h&&(a=160),237===h&&(o=159),s=2,i=15&h;else{if(!r(h,240,244))return l(e);240===h&&(a=144),244===h&&(o=143),s=3,i=7&h}return null}if(!r(h,a,o))return i=s=n=0,a=128,o=191,t.prepend(h),l(e);if(a=128,o=191,i=i<<6|63&h,(n+=1)!==s)return null;var u=i;return i=s=n=0,u}}function C(t){t.fatal;this.handler=function(t,e){if(-1===e)return-1;if(o(e))return e;var i,n;r(e,128,2047)?(i=1,n=192):r(e,2048,65535)?(i=2,n=224):r(e,65536,1114111)&&(i=3,n=240);for(var s=[(e>>6*i)+n];i>0;){var a=e>>6*(i-1);s.push(128|63&a),i-=1}return s}}function S(t,e){var i=e.fatal;this.handler=function(e,r){if(-1===r)return-1;if(a(r))return r;var n=t[r-128];return null===n?l(i):n}}function k(t,e){e.fatal;this.handler=function(e,i){if(-1===i)return-1;if(o(i))return i;var r=v(i,t);return null===r&&u(i),r+128}}function E(t){var e=t.fatal,i=0,n=0,s=0;this.handler=function(t,o){if(-1===o&&0===i&&0===n&&0===s)return-1;var h;if(-1!==o||0===i&&0===n&&0===s||(i=0,n=0,s=0,l(e)),0!==s){h=null,r(o,48,57)&&(h=function(t){if(t>39419&&t<189e3||t>1237575)return null;if(7457===t)return 59335;var e,i=0,r=0,n=b("gb18030-ranges");for(e=0;e<n.length;++e){var s=n[e];if(!(s[0]<=t))break;i=s[0],r=s[1]}return r+t-i}(10*(126*(10*(i-129)+n-48)+s-129)+o-48));var u=[n,s,o];return i=0,n=0,s=0,null===h?(t.prepend(u),l(e)):h}if(0!==n)return r(o,129,254)?(s=o,null):(t.prepend([n,o]),i=0,n=0,l(e));if(0!==i){if(r(o,48,57))return n=o,null;var f=i,c=null;i=0;var d=o<127?64:65;return(r(o,64,126)||r(o,128,254))&&(c=190*(f-129)+(o-d)),null===(h=null===c?null:y(c,b("gb18030")))&&a(o)&&t.prepend(o),null===h?l(e):h}return a(o)?o:128===o?8364:r(o,129,254)?(i=o,null):l(e)}}function A(t,e){t.fatal;this.handler=function(t,i){if(-1===i)return-1;if(o(i))return i;if(58853===i)return u(i);if(e&&8364===i)return 128;var r=v(i,b("gb18030"));if(null!==r){var s=r%190;return[n(r/190)+129,s+(s<63?64:65)]}if(e)return u(i);r=function(t){if(59335===t)return 7457;var e,i=0,r=0,n=b("gb18030-ranges");for(e=0;e<n.length;++e){var s=n[e];if(!(s[1]<=t))break;i=s[1],r=s[0]}return r+t-i}(i);var a=n(r/10/126/10),h=n((r-=10*a*126*10)/10/126),l=n((r-=10*h*126)/10);return[a+129,h+48,l+129,r-10*l+48]}}function P(t){var e=t.fatal,i=0;this.handler=function(t,n){if(-1===n&&0!==i)return i=0,l(e);if(-1===n&&0===i)return-1;if(0!==i){var s=i,o=null;i=0;var h=n<127?64:98;switch((r(n,64,126)||r(n,161,254))&&(o=157*(s-129)+(n-h)),o){case 1133:return[202,772];case 1135:return[202,780];case 1164:return[234,772];case 1166:return[234,780]}var u=null===o?null:y(o,b("big5"));return null===u&&a(n)&&t.prepend(n),null===u?l(e):u}return a(n)?n:r(n,129,254)?(i=n,null):l(e)}}function I(t){t.fatal;this.handler=function(t,e){if(-1===e)return-1;if(o(e))return e;var i=function(t){var e=g=g||b("big5").map((function(t,e){return e<5024?null:t}));return 9552===t||9566===t||9569===t||9578===t||21313===t||21317===t?e.lastIndexOf(t):v(t,e)}(e);if(null===i)return u(e);var r=n(i/157)+129;if(r<161)return u(e);var s=i%157;return[r,s+(s<63?64:98)]}}function O(t){var e=t.fatal,i=!1,n=0;this.handler=function(t,s){if(-1===s&&0!==n)return n=0,l(e);if(-1===s&&0===n)return-1;if(142===n&&r(s,161,223))return n=0,65216+s;if(143===n&&r(s,161,254))return i=!0,n=s,null;if(0!==n){var o=n;n=0;var h=null;return r(o,161,254)&&r(s,161,254)&&(h=y(94*(o-161)+(s-161),b(i?"jis0212":"jis0208"))),i=!1,r(s,161,254)||t.prepend(s),null===h?l(e):h}return a(s)?s:142===s||143===s||r(s,161,254)?(n=s,null):l(e)}}function N(t){t.fatal;this.handler=function(t,e){if(-1===e)return-1;if(o(e))return e;if(165===e)return 92;if(8254===e)return 126;if(r(e,65377,65439))return[142,e-65377+161];8722===e&&(e=65293);var i=v(e,b("jis0208"));return null===i?u(e):[n(i/94)+161,i%94+161]}}function L(t){var e=t.fatal,i=0,n=1,s=2,a=3,o=4,h=5,u=6,f=i,c=i,d=0,p=!1;this.handler=function(t,g){switch(f){default:case i:return 27===g?(f=h,null):r(g,0,127)&&14!==g&&15!==g&&27!==g?(p=!1,g):-1===g?-1:(p=!1,l(e));case n:return 27===g?(f=h,null):92===g?(p=!1,165):126===g?(p=!1,8254):r(g,0,127)&&14!==g&&15!==g&&27!==g&&92!==g&&126!==g?(p=!1,g):-1===g?-1:(p=!1,l(e));case s:return 27===g?(f=h,null):r(g,33,95)?(p=!1,65344+g):-1===g?-1:(p=!1,l(e));case a:return 27===g?(f=h,null):r(g,33,126)?(p=!1,d=g,f=o,null):-1===g?-1:(p=!1,l(e));case o:if(27===g)return f=h,l(e);if(r(g,33,126)){f=a;var m=y(94*(d-33)+g-33,b("jis0208"));return null===m?l(e):m}return-1===g?(f=a,t.prepend(g),l(e)):(f=a,l(e));case h:return 36===g||40===g?(d=g,f=u,null):(t.prepend(g),p=!1,f=c,l(e));case u:var _=d;d=0;var v=null;if(40===_&&66===g&&(v=i),40===_&&74===g&&(v=n),40===_&&73===g&&(v=s),36!==_||64!==g&&66!==g||(v=a),null!==v){f=f=v;var M=p;return p=!0,M?l(e):null}return t.prepend([_,g]),p=!1,f=c,l(e)}}}function T(t){t.fatal;var e=0,i=1,r=2,s=e;this.handler=function(t,a){if(-1===a&&s!==e)return t.prepend(a),s=e,[27,40,66];if(-1===a&&s===e)return-1;if(!(s!==e&&s!==i||14!==a&&15!==a&&27!==a))return u(65533);if(s===e&&o(a))return a;if(s===i&&(o(a)&&92!==a&&126!==a||165==a||8254==a)){if(o(a))return a;if(165===a)return 92;if(8254===a)return 126}if(o(a)&&s!==e)return t.prepend(a),s=e,[27,40,66];if((165===a||8254===a)&&s!==i)return t.prepend(a),s=i,[27,40,74];8722===a&&(a=65293);var h=v(a,b("jis0208"));return null===h?u(a):s!==r?(t.prepend(a),s=r,[27,36,66]):[n(h/94)+33,h%94+33]}}function R(t){var e=t.fatal,i=0;this.handler=function(t,n){if(-1===n&&0!==i)return i=0,l(e);if(-1===n&&0===i)return-1;if(0!==i){var s=i,o=null;i=0;var h=n<127?64:65,u=s<160?129:193;if((r(n,64,126)||r(n,128,252))&&(o=188*(s-u)+n-h),r(o,8836,10715))return 48508+o;var f=null===o?null:y(o,b("jis0208"));return null===f&&a(n)&&t.prepend(n),null===f?l(e):f}return a(n)||128===n?n:r(n,161,223)?65216+n:r(n,129,159)||r(n,224,252)?(i=n,null):l(e)}}function z(t){t.fatal;this.handler=function(t,e){if(-1===e)return-1;if(o(e)||128===e)return e;if(165===e)return 92;if(8254===e)return 126;if(r(e,65377,65439))return e-65377+161;8722===e&&(e=65293);var i=function(t){return(p=p||b("jis0208").map((function(t,e){return r(e,8272,8835)?null:t}))).indexOf(t)}(e);if(null===i)return u(e);var s=n(i/188),a=i%188;return[s+(s<31?129:193),a+(a<63?64:65)]}}function B(t){var e=t.fatal,i=0;this.handler=function(t,n){if(-1===n&&0!==i)return i=0,l(e);if(-1===n&&0===i)return-1;if(0!==i){var s=i,o=null;i=0,r(n,65,254)&&(o=190*(s-129)+(n-65));var h=null===o?null:y(o,b("euc-kr"));return null===o&&a(n)&&t.prepend(n),null===h?l(e):h}return a(n)?n:r(n,129,254)?(i=n,null):l(e)}}function D(t){t.fatal;this.handler=function(t,e){if(-1===e)return-1;if(o(e))return e;var i=v(e,b("euc-kr"));return null===i?u(e):[n(i/190)+129,i%190+65]}}function j(t,e){var i=t>>8,r=255&t;return e?[i,r]:[r,i]}function U(t,e){var i=e.fatal,n=null,s=null;this.handler=function(e,a){if(-1===a&&(null!==n||null!==s))return l(i);if(-1===a&&null===n&&null===s)return-1;if(null===n)return n=a,null;var o;if(o=t?(n<<8)+a:(a<<8)+n,n=null,null!==s){var h=s;return s=null,r(o,56320,57343)?65536+1024*(h-55296)+(o-56320):(e.prepend(j(o,t)),l(i))}return r(o,55296,56319)?(s=o,null):r(o,56320,57343)?l(i):o}}function F(t,e){e.fatal;this.handler=function(e,i){if(-1===i)return-1;if(r(i,0,65535))return j(i,t);var n=j(55296+(i-65536>>10),t),s=j(56320+(i-65536&1023),t);return n.concat(s)}}function q(t){t.fatal;this.handler=function(t,e){return-1===e?-1:a(e)?e:63360+e-128}}function G(t){t.fatal;this.handler=function(t,e){return-1===e?-1:o(e)?e:r(e,63360,63487)?e-63360+128:u(e)}}Object.defineProperty&&(Object.defineProperty(M.prototype,"encoding",{get:function(){return this._encoding.name.toLowerCase()}}),Object.defineProperty(M.prototype,"fatal",{get:function(){return"fatal"===this._error_mode}}),Object.defineProperty(M.prototype,"ignoreBOM",{get:function(){return this._ignoreBOM}})),M.prototype.decode=function(t,e){var i;i="object"==typeof t&&t instanceof ArrayBuffer?new Uint8Array(t):"object"==typeof t&&"buffer"in t&&t.buffer instanceof ArrayBuffer?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):new Uint8Array(0),e=s(e),this._do_not_flush||(this._decoder=_[this._encoding.name]({fatal:"fatal"===this._error_mode}),this._BOMseen=!1),this._do_not_flush=Boolean(e.stream);for(var r,n=new h(i),a=[];;){var o=n.read();if(-1===o)break;if(-1===(r=this._decoder.handler(n,o)))break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}if(!this._do_not_flush){do{if(-1===(r=this._decoder.handler(n,n.read())))break;null!==r&&(Array.isArray(r)?a.push.apply(a,r):a.push(r))}while(!n.endOfStream());this._decoder=null}return function(t){var e,i;return e=["UTF-8","UTF-16LE","UTF-16BE"],i=this._encoding.name,-1===e.indexOf(i)||this._ignoreBOM||this._BOMseen||(t.length>0&&65279===t[0]?(this._BOMseen=!0,t.shift()):t.length>0&&(this._BOMseen=!0)),function(t){for(var e="",i=0;i<t.length;++i){var r=t[i];r<=65535?e+=String.fromCharCode(r):(r-=65536,e+=String.fromCharCode(55296+(r>>10),56320+(1023&r)))}return e}(t)}.call(this,a)},Object.defineProperty&&Object.defineProperty(w.prototype,"encoding",{get:function(){return this._encoding.name.toLowerCase()}}),w.prototype.encode=function(t,e){t=void 0===t?"":String(t),e=s(e),this._do_not_flush||(this._encoder=m[this._encoding.name]({fatal:"fatal"===this._fatal})),this._do_not_flush=Boolean(e.stream);for(var i,r=new h(function(t){for(var e=String(t),i=e.length,r=0,n=[];r<i;){var s=e.charCodeAt(r);if(s<55296||s>57343)n.push(s);else if(56320<=s&&s<=57343)n.push(65533);else if(55296<=s&&s<=56319)if(r===i-1)n.push(65533);else{var a=e.charCodeAt(r+1);if(56320<=a&&a<=57343){var o=1023&s,h=1023&a;n.push(65536+(o<<10)+h),r+=1}else n.push(65533)}r+=1}return n}(t)),n=[];;){var a=r.read();if(-1===a)break;if(-1===(i=this._encoder.handler(r,a)))break;Array.isArray(i)?n.push.apply(n,i):n.push(i)}if(!this._do_not_flush){for(;-1!==(i=this._encoder.handler(r,r.read()));)Array.isArray(i)?n.push.apply(n,i):n.push(i);this._encoder=null}return new Uint8Array(n)},m["UTF-8"]=function(t){return new C(t)},_["UTF-8"]=function(t){return new x(t)},"encoding-indexes"in e&&c.forEach((function(t){"Legacy single-byte encodings"===t.heading&&t.encodings.forEach((function(t){var e=t.name,i=b(e.toLowerCase());_[e]=function(t){return new S(i,t)},m[e]=function(t){return new k(i,t)}}))})),_.GBK=function(t){return new E(t)},m.GBK=function(t){return new A(t,!0)},m.gb18030=function(t){return new A(t)},_.gb18030=function(t){return new E(t)},m.Big5=function(t){return new I(t)},_.Big5=function(t){return new P(t)},m["EUC-JP"]=function(t){return new N(t)},_["EUC-JP"]=function(t){return new O(t)},m["ISO-2022-JP"]=function(t){return new T(t)},_["ISO-2022-JP"]=function(t){return new L(t)},m.Shift_JIS=function(t){return new z(t)},_.Shift_JIS=function(t){return new R(t)},m["EUC-KR"]=function(t){return new D(t)},_["EUC-KR"]=function(t){return new B(t)},m["UTF-16BE"]=function(t){return new F(!0,t)},_["UTF-16BE"]=function(t){return new U(!0,t)},m["UTF-16LE"]=function(t){return new F(!1,t)},_["UTF-16LE"]=function(t){return new U(!1,t)},m["x-user-defined"]=function(t){return new G(t)},_["x-user-defined"]=function(t){return new q(t)},e.TextEncoder||(e.TextEncoder=w),e.TextDecoder||(e.TextDecoder=M),t.exports&&(t.exports={TextEncoder:e.TextEncoder,TextDecoder:e.TextDecoder,EncodingIndexes:e["encoding-indexes"]})}(this||{})},function(t,e){},function(t,e,i){"use strict";var r=i(61).Buffer,n=r.isEncoding||function(t){switch((t=""+t)&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function s(t){var e;switch(this.encoding=function(t){var e=function(t){if(!t)return"utf8";for(var e;;)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}(t);if("string"!=typeof e&&(r.isEncoding===n||!n(t)))throw new Error("Unknown encoding: "+t);return e||t}(t),this.encoding){case"utf16le":this.text=h,this.end=l,e=4;break;case"utf8":this.fillLast=o,e=4;break;case"base64":this.text=u,this.end=f,e=3;break;default:return this.write=c,void(this.end=d)}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(e)}function a(t){return t<=127?0:t>>5==6?2:t>>4==14?3:t>>3==30?4:t>>6==2?-1:-2}function o(t){var e=this.lastTotal-this.lastNeed,i=function(t,e,i){if(128!=(192&e[0]))return t.lastNeed=0,"�";if(t.lastNeed>1&&e.length>1){if(128!=(192&e[1]))return t.lastNeed=1,"�";if(t.lastNeed>2&&e.length>2&&128!=(192&e[2]))return t.lastNeed=2,"�"}}(this,t);return void 0!==i?i:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function h(t,e){if((t.length-e)%2==0){var i=t.toString("utf16le",e);if(i){var r=i.charCodeAt(i.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],i.slice(0,-1)}return i}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function l(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var i=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,i)}return e}function u(t,e){var i=(t.length-e)%3;return 0===i?t.toString("base64",e):(this.lastNeed=3-i,this.lastTotal=3,1===i?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-i))}function f(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function c(t){return t.toString(this.encoding)}function d(t){return t&&t.length?this.write(t):""}e.StringDecoder=s,s.prototype.write=function(t){if(0===t.length)return"";var e,i;if(this.lastNeed){if(void 0===(e=this.fillLast(t)))return"";i=this.lastNeed,this.lastNeed=0}else i=0;return i<t.length?e?e+this.text(t,i):this.text(t,i):e||""},s.prototype.end=function(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"�":e},s.prototype.text=function(t,e){var i=function(t,e,i){var r=e.length-1;if(r<i)return 0;var n=a(e[r]);if(n>=0)return n>0&&(t.lastNeed=n-1),n;if(--r<i||-2===n)return 0;if((n=a(e[r]))>=0)return n>0&&(t.lastNeed=n-2),n;if(--r<i||-2===n)return 0;if((n=a(e[r]))>=0)return n>0&&(2===n?n=0:t.lastNeed=n-3),n;return 0}(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=i;var r=t.length-(i-this.lastNeed);return t.copy(this.lastChar,0,r),t.toString("utf8",e,r)},s.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},function(t,e,i){
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
var r=i(5),n=r.Buffer;function s(t,e){for(var i in t)e[i]=t[i]}function a(t,e,i){return n(t,e,i)}n.from&&n.alloc&&n.allocUnsafe&&n.allocUnsafeSlow?t.exports=r:(s(r,e),e.Buffer=a),a.prototype=Object.create(n.prototype),s(n,a),a.from=function(t,e,i){if("number"==typeof t)throw new TypeError("Argument must not be a number");return n(t,e,i)},a.alloc=function(t,e,i){if("number"!=typeof t)throw new TypeError("Argument must be a number");var r=n(t);return void 0!==e?"string"==typeof i?r.fill(e,i):r.fill(e):r.fill(0),r},a.allocUnsafe=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return n(t)},a.allocUnsafeSlow=function(t){if("number"!=typeof t)throw new TypeError("Argument must be a number");return r.SlowBuffer(t)}},function(t,e,i){!function(){function e(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function i(){return 1}t.exports?t.exports=s:this.LRUCache=s;var r=!1;function n(t){r||"string"==typeof t||"number"==typeof t||(r=!0,console.error(new TypeError("LRU: key must be a string or number. Almost certainly a bug! "+typeof t).stack))}function s(t){if(!(this instanceof s))return new s(t);"number"==typeof t&&(t={max:t}),t||(t={}),this._max=t.max,(!this._max||"number"!=typeof this._max||this._max<=0)&&(this._max=1/0),this._lengthCalculator=t.length||i,"function"!=typeof this._lengthCalculator&&(this._lengthCalculator=i),this._allowStale=t.stale||!1,this._maxAge=t.maxAge||null,this._dispose=t.dispose,this.reset()}function a(t,e,i){n(e);var r=t._cache[e];return r&&(o(t,r)?(u(t,r),t._allowStale||(r=void 0)):i&&function(t,e){l(t,e),e.lu=t._mru++,t._lruList[e.lu]=e}(t,r),r&&(r=r.value)),r}function o(t,e){if(!e||!e.maxAge&&!t._maxAge)return!1;var i=Date.now()-e.now;return e.maxAge?i>e.maxAge:t._maxAge&&i>t._maxAge}function h(t){for(;t._lru<t._mru&&t._length>t._max;)u(t,t._lruList[t._lru])}function l(t,e){for(delete t._lruList[e.lu];t._lru<t._mru&&!t._lruList[t._lru];)t._lru++}function u(t,e){e&&(t._dispose&&t._dispose(e.key,e.value),t._length-=e.length,t._itemCount--,delete t._cache[e.key],l(t,e))}function f(t,e,i,r,n,s){this.key=t,this.value=e,this.lu=i,this.length=r,this.now=n,s&&(this.maxAge=s)}Object.defineProperty(s.prototype,"max",{set:function(t){(!t||"number"!=typeof t||t<=0)&&(t=1/0),this._max=t,this._length>this._max&&h(this)},get:function(){return this._max},enumerable:!0}),Object.defineProperty(s.prototype,"lengthCalculator",{set:function(t){if("function"!=typeof t)for(var e in this._lengthCalculator=i,this._length=this._itemCount,this._cache)this._cache[e].length=1;else for(var e in this._lengthCalculator=t,this._length=0,this._cache)this._cache[e].length=this._lengthCalculator(this._cache[e].value),this._length+=this._cache[e].length;this._length>this._max&&h(this)},get:function(){return this._lengthCalculator},enumerable:!0}),Object.defineProperty(s.prototype,"length",{get:function(){return this._length},enumerable:!0}),Object.defineProperty(s.prototype,"itemCount",{get:function(){return this._itemCount},enumerable:!0}),s.prototype.forEach=function(t,e){e=e||this;for(var i=0,r=this._itemCount,n=this._mru-1;n>=0&&i<r;n--)if(this._lruList[n]){i++;var s=this._lruList[n];o(this,s)&&(u(this,s),this._allowStale||(s=void 0)),s&&t.call(e,s.value,s.key,this)}},s.prototype.keys=function(){for(var t=new Array(this._itemCount),e=0,i=this._mru-1;i>=0&&e<this._itemCount;i--)if(this._lruList[i]){var r=this._lruList[i];t[e++]=r.key}return t},s.prototype.values=function(){for(var t=new Array(this._itemCount),e=0,i=this._mru-1;i>=0&&e<this._itemCount;i--)if(this._lruList[i]){var r=this._lruList[i];t[e++]=r.value}return t},s.prototype.reset=function(){if(this._dispose&&this._cache)for(var t in this._cache)this._dispose(t,this._cache[t].value);this._cache=Object.create(null),this._lruList=Object.create(null),this._mru=0,this._lru=0,this._length=0,this._itemCount=0},s.prototype.dump=function(){for(var t=[],e=0,i=this._mru-1;i>=0&&e<this._itemCount;i--)if(this._lruList[i]){var r=this._lruList[i];o(this,r)||(++e,t.push({k:r.key,v:r.value,e:r.now+(r.maxAge||0)}))}return t},s.prototype.dumpLru=function(){return this._lruList},s.prototype.set=function(t,i,r){r=r||this._maxAge,n(t);var s=r?Date.now():0,a=this._lengthCalculator(i);if(e(this._cache,t))return a>this._max?(u(this,this._cache[t]),!1):(this._dispose&&this._dispose(t,this._cache[t].value),this._cache[t].now=s,this._cache[t].maxAge=r,this._cache[t].value=i,this._length+=a-this._cache[t].length,this._cache[t].length=a,this.get(t),this._length>this._max&&h(this),!0);var o=new f(t,i,this._mru++,a,s,r);return o.length>this._max?(this._dispose&&this._dispose(t,i),!1):(this._length+=o.length,this._lruList[o.lu]=this._cache[t]=o,this._itemCount++,this._length>this._max&&h(this),!0)},s.prototype.has=function(t){return n(t),!!e(this._cache,t)&&!o(this,this._cache[t])},s.prototype.get=function(t){return n(t),a(this,t,!0)},s.prototype.peek=function(t){return n(t),a(this,t,!1)},s.prototype.pop=function(){var t=this._lruList[this._lru];return u(this,t),t||null},s.prototype.del=function(t){n(t),u(this,this._cache[t])},s.prototype.load=function(t){this.reset();for(var e=Date.now(),i=t.length-1;i>=0;i--){var r=t[i];n(r.k);var s=r.e||0;if(0===s)this.set(r.k,r.v);else{var a=s-e;a>0&&this.set(r.k,r.v,a)}}}}()},function(t,e,i){"use strict";i.r(e);var r=484813681109536e-20,n=Math.PI/2,s=.017453292519943295,a=57.29577951308232,o=Math.PI/4,h=2*Math.PI,l=3.14159265359,u={greenwich:0,lisbon:-9.131906111111,paris:2.337229166667,bogota:-74.080916666667,madrid:-3.687938888889,rome:12.452333333333,bern:7.439583333333,jakarta:106.807719444444,ferro:-17.666666666667,brussels:4.367975,stockholm:18.058277777778,athens:23.7163375,oslo:10.722916666667},f={ft:{to_meter:.3048},"us-ft":{to_meter:1200/3937}},c=/[\s_\-\/\(\)]/g;function d(t,e){if(t[e])return t[e];for(var i,r=Object.keys(t),n=e.toLowerCase().replace(c,""),s=-1;++s<r.length;)if((i=r[s]).toLowerCase().replace(c,"")===n)return t[i]}var p=function(t){var e,i,r,n={},a=t.split("+").map((function(t){return t.trim()})).filter((function(t){return t})).reduce((function(t,e){var i=e.split("=");return i.push(!0),t[i[0].toLowerCase()]=i[1],t}),{}),o={proj:"projName",datum:"datumCode",rf:function(t){n.rf=parseFloat(t)},lat_0:function(t){n.lat0=t*s},lat_1:function(t){n.lat1=t*s},lat_2:function(t){n.lat2=t*s},lat_ts:function(t){n.lat_ts=t*s},lon_0:function(t){n.long0=t*s},lon_1:function(t){n.long1=t*s},lon_2:function(t){n.long2=t*s},alpha:function(t){n.alpha=parseFloat(t)*s},gamma:function(t){n.rectified_grid_angle=parseFloat(t)},lonc:function(t){n.longc=t*s},x_0:function(t){n.x0=parseFloat(t)},y_0:function(t){n.y0=parseFloat(t)},k_0:function(t){n.k0=parseFloat(t)},k:function(t){n.k0=parseFloat(t)},a:function(t){n.a=parseFloat(t)},b:function(t){n.b=parseFloat(t)},r_a:function(){n.R_A=!0},zone:function(t){n.zone=parseInt(t,10)},south:function(){n.utmSouth=!0},towgs84:function(t){n.datum_params=t.split(",").map((function(t){return parseFloat(t)}))},to_meter:function(t){n.to_meter=parseFloat(t)},units:function(t){n.units=t;var e=d(f,t);e&&(n.to_meter=e.to_meter)},from_greenwich:function(t){n.from_greenwich=t*s},pm:function(t){var e=d(u,t);n.from_greenwich=(e||parseFloat(t))*s},nadgrids:function(t){"@null"===t?n.datumCode="none":n.nadgrids=t},axis:function(t){3===t.length&&-1!=="ewnsud".indexOf(t.substr(0,1))&&-1!=="ewnsud".indexOf(t.substr(1,1))&&-1!=="ewnsud".indexOf(t.substr(2,1))&&(n.axis=t)},approx:function(){n.approx=!0}};for(e in a)i=a[e],e in o?"function"==typeof(r=o[e])?r(i):n[r]=i:n[e]=i;return"string"==typeof n.datumCode&&"WGS84"!==n.datumCode&&(n.datumCode=n.datumCode.toLowerCase()),n},g=function(t){return new M(t).output()},m=/\s/,_=/[A-Za-z]/,y=/[A-Za-z84_]/,v=/[,\]]/,b=/[\d\.E\-\+]/;function M(t){if("string"!=typeof t)throw new Error("not a string");this.text=t.trim(),this.level=0,this.place=0,this.root=null,this.stack=[],this.currentObject=null,this.state=1}function w(t,e,i){Array.isArray(e)&&(i.unshift(e),e=null);var r=e?{}:t,n=i.reduce((function(t,e){return x(e,t),t}),r);e&&(t[e]=n)}function x(t,e){if(Array.isArray(t)){var i=t.shift();if("PARAMETER"===i&&(i=t.shift()),1===t.length)return Array.isArray(t[0])?(e[i]={},void x(t[0],e[i])):void(e[i]=t[0]);if(t.length)if("TOWGS84"!==i){if("AXIS"===i)return i in e||(e[i]=[]),void e[i].push(t);var r;switch(Array.isArray(i)||(e[i]={}),i){case"UNIT":case"PRIMEM":case"VERT_DATUM":return e[i]={name:t[0].toLowerCase(),convert:t[1]},void(3===t.length&&x(t[2],e[i]));case"SPHEROID":case"ELLIPSOID":return e[i]={name:t[0],a:t[1],rf:t[2]},void(4===t.length&&x(t[3],e[i]));case"PROJECTEDCRS":case"PROJCRS":case"GEOGCS":case"GEOCCS":case"PROJCS":case"LOCAL_CS":case"GEODCRS":case"GEODETICCRS":case"GEODETICDATUM":case"EDATUM":case"ENGINEERINGDATUM":case"VERT_CS":case"VERTCRS":case"VERTICALCRS":case"COMPD_CS":case"COMPOUNDCRS":case"ENGINEERINGCRS":case"ENGCRS":case"FITTED_CS":case"LOCAL_DATUM":case"DATUM":return t[0]=["name",t[0]],void w(e,i,t);default:for(r=-1;++r<t.length;)if(!Array.isArray(t[r]))return x(t,e[i]);return w(e,i,t)}}else e[i]=t;else e[i]=!0}else e[t]=!0}M.prototype.readCharicter=function(){var t=this.text[this.place++];if(4!==this.state)for(;m.test(t);){if(this.place>=this.text.length)return;t=this.text[this.place++]}switch(this.state){case 1:return this.neutral(t);case 2:return this.keyword(t);case 4:return this.quoted(t);case 5:return this.afterquote(t);case 3:return this.number(t);case-1:return}},M.prototype.afterquote=function(t){if('"'===t)return this.word+='"',void(this.state=4);if(v.test(t))return this.word=this.word.trim(),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in afterquote yet, index '+this.place)},M.prototype.afterItem=function(t){return","===t?(null!==this.word&&this.currentObject.push(this.word),this.word=null,void(this.state=1)):"]"===t?(this.level--,null!==this.word&&(this.currentObject.push(this.word),this.word=null),this.state=1,this.currentObject=this.stack.pop(),void(this.currentObject||(this.state=-1))):void 0},M.prototype.number=function(t){if(!b.test(t)){if(v.test(t))return this.word=parseFloat(this.word),void this.afterItem(t);throw new Error("havn't handled \""+t+'" in number yet, index '+this.place)}this.word+=t},M.prototype.quoted=function(t){'"'!==t?this.word+=t:this.state=5},M.prototype.keyword=function(t){if(y.test(t))this.word+=t;else{if("["===t){var e=[];return e.push(this.word),this.level++,null===this.root?this.root=e:this.currentObject.push(e),this.stack.push(this.currentObject),this.currentObject=e,void(this.state=1)}if(!v.test(t))throw new Error("havn't handled \""+t+'" in keyword yet, index '+this.place);this.afterItem(t)}},M.prototype.neutral=function(t){if(_.test(t))return this.word=t,void(this.state=2);if('"'===t)return this.word="",void(this.state=4);if(b.test(t))return this.word=t,void(this.state=3);if(!v.test(t))throw new Error("havn't handled \""+t+'" in neutral yet, index '+this.place);this.afterItem(t)},M.prototype.output=function(){for(;this.place<this.text.length;)this.readCharicter();if(-1===this.state)return this.root;throw new Error('unable to parse string "'+this.text+'". State is '+this.state)};function C(t){return.017453292519943295*t}var S=function(t){var e=g(t),i=e.shift(),r=e.shift();e.unshift(["name",r]),e.unshift(["type",i]);var n={};return x(e,n),function(t){if("GEOGCS"===t.type?t.projName="longlat":"LOCAL_CS"===t.type?(t.projName="identity",t.local=!0):"object"==typeof t.PROJECTION?t.projName=Object.keys(t.PROJECTION)[0]:t.projName=t.PROJECTION,t.AXIS){for(var e="",i=0,r=t.AXIS.length;i<r;++i){var n=[t.AXIS[i][0].toLowerCase(),t.AXIS[i][1].toLowerCase()];-1!==n[0].indexOf("north")||("y"===n[0]||"lat"===n[0])&&"north"===n[1]?e+="n":-1!==n[0].indexOf("south")||("y"===n[0]||"lat"===n[0])&&"south"===n[1]?e+="s":-1!==n[0].indexOf("east")||("x"===n[0]||"lon"===n[0])&&"east"===n[1]?e+="e":-1===n[0].indexOf("west")&&("x"!==n[0]&&"lon"!==n[0]||"west"!==n[1])||(e+="w")}2===e.length&&(e+="u"),3===e.length&&(t.axis=e)}t.UNIT&&(t.units=t.UNIT.name.toLowerCase(),"metre"===t.units&&(t.units="meter"),t.UNIT.convert&&("GEOGCS"===t.type?t.DATUM&&t.DATUM.SPHEROID&&(t.to_meter=t.UNIT.convert*t.DATUM.SPHEROID.a):t.to_meter=t.UNIT.convert));var s=t.GEOGCS;function a(e){return e*(t.to_meter||1)}"GEOGCS"===t.type&&(s=t),s&&(s.DATUM?t.datumCode=s.DATUM.name.toLowerCase():t.datumCode=s.name.toLowerCase(),"d_"===t.datumCode.slice(0,2)&&(t.datumCode=t.datumCode.slice(2)),"new_zealand_geodetic_datum_1949"!==t.datumCode&&"new_zealand_1949"!==t.datumCode||(t.datumCode="nzgd49"),"wgs_1984"!==t.datumCode&&"world_geodetic_system_1984"!==t.datumCode||("Mercator_Auxiliary_Sphere"===t.PROJECTION&&(t.sphere=!0),t.datumCode="wgs84"),"_ferro"===t.datumCode.slice(-6)&&(t.datumCode=t.datumCode.slice(0,-6)),"_jakarta"===t.datumCode.slice(-8)&&(t.datumCode=t.datumCode.slice(0,-8)),~t.datumCode.indexOf("belge")&&(t.datumCode="rnb72"),s.DATUM&&s.DATUM.SPHEROID&&(t.ellps=s.DATUM.SPHEROID.name.replace("_19","").replace(/[Cc]larke\_18/,"clrk"),"international"===t.ellps.toLowerCase().slice(0,13)&&(t.ellps="intl"),t.a=s.DATUM.SPHEROID.a,t.rf=parseFloat(s.DATUM.SPHEROID.rf,10)),s.DATUM&&s.DATUM.TOWGS84&&(t.datum_params=s.DATUM.TOWGS84),~t.datumCode.indexOf("osgb_1936")&&(t.datumCode="osgb36"),~t.datumCode.indexOf("osni_1952")&&(t.datumCode="osni52"),(~t.datumCode.indexOf("tm65")||~t.datumCode.indexOf("geodetic_datum_of_1965"))&&(t.datumCode="ire65"),"ch1903+"===t.datumCode&&(t.datumCode="ch1903"),~t.datumCode.indexOf("israel")&&(t.datumCode="isr93")),t.b&&!isFinite(t.b)&&(t.b=t.a),[["standard_parallel_1","Standard_Parallel_1"],["standard_parallel_1","Latitude of 1st standard parallel"],["standard_parallel_2","Standard_Parallel_2"],["standard_parallel_2","Latitude of 2nd standard parallel"],["false_easting","False_Easting"],["false_easting","False easting"],["false-easting","Easting at false origin"],["false_northing","False_Northing"],["false_northing","False northing"],["false_northing","Northing at false origin"],["central_meridian","Central_Meridian"],["central_meridian","Longitude of natural origin"],["central_meridian","Longitude of false origin"],["latitude_of_origin","Latitude_Of_Origin"],["latitude_of_origin","Central_Parallel"],["latitude_of_origin","Latitude of natural origin"],["latitude_of_origin","Latitude of false origin"],["scale_factor","Scale_Factor"],["k0","scale_factor"],["latitude_of_center","Latitude_Of_Center"],["latitude_of_center","Latitude_of_center"],["lat0","latitude_of_center",C],["longitude_of_center","Longitude_Of_Center"],["longitude_of_center","Longitude_of_center"],["longc","longitude_of_center",C],["x0","false_easting",a],["y0","false_northing",a],["long0","central_meridian",C],["lat0","latitude_of_origin",C],["lat0","standard_parallel_1",C],["lat1","standard_parallel_1",C],["lat2","standard_parallel_2",C],["azimuth","Azimuth"],["alpha","azimuth",C],["srsCode","name"]].forEach((function(e){return i=t,n=(r=e)[0],s=r[1],void(!(n in i)&&s in i&&(i[n]=i[s],3===r.length&&(i[n]=r[2](i[n]))));var i,r,n,s})),t.long0||!t.longc||"Albers_Conic_Equal_Area"!==t.projName&&"Lambert_Azimuthal_Equal_Area"!==t.projName||(t.long0=t.longc),t.lat_ts||!t.lat1||"Stereographic_South_Pole"!==t.projName&&"Polar Stereographic (variant B)"!==t.projName||(t.lat0=C(t.lat1>0?90:-90),t.lat_ts=t.lat1)}(n),n};function k(t){var e=this;if(2===arguments.length){var i=arguments[1];"string"==typeof i?"+"===i.charAt(0)?k[t]=p(arguments[1]):k[t]=S(arguments[1]):k[t]=i}else if(1===arguments.length){if(Array.isArray(t))return t.map((function(t){Array.isArray(t)?k.apply(e,t):k(t)}));if("string"==typeof t){if(t in k)return k[t]}else"EPSG"in t?k["EPSG:"+t.EPSG]=t:"ESRI"in t?k["ESRI:"+t.ESRI]=t:"IAU2000"in t?k["IAU2000:"+t.IAU2000]=t:console.log(t);return}}!function(t){t("EPSG:4326","+title=WGS 84 (long/lat) +proj=longlat +ellps=WGS84 +datum=WGS84 +units=degrees"),t("EPSG:4269","+title=NAD83 (long/lat) +proj=longlat +a=6378137.0 +b=6356752.31414036 +ellps=GRS80 +datum=NAD83 +units=degrees"),t("EPSG:3857","+title=WGS 84 / Pseudo-Mercator +proj=merc +a=6378137 +b=6378137 +lat_ts=0.0 +lon_0=0.0 +x_0=0.0 +y_0=0 +k=1.0 +units=m +nadgrids=@null +no_defs"),t.WGS84=t["EPSG:4326"],t["EPSG:3785"]=t["EPSG:3857"],t.GOOGLE=t["EPSG:3857"],t["EPSG:900913"]=t["EPSG:3857"],t["EPSG:102113"]=t["EPSG:3857"]}(k);var E=k;var A=["PROJECTEDCRS","PROJCRS","GEOGCS","GEOCCS","PROJCS","LOCAL_CS","GEODCRS","GEODETICCRS","GEODETICDATUM","ENGCRS","ENGINEERINGCRS"];var P=["3857","900913","3785","102113"];var I=function(t){if(!function(t){return"string"==typeof t}(t))return t;if(function(t){return t in E}(t))return E[t];if(function(t){return A.some((function(e){return t.indexOf(e)>-1}))}(t)){var e=S(t);if(function(t){var e=d(t,"authority");if(e){var i=d(e,"epsg");return i&&P.indexOf(i)>-1}}(e))return E["EPSG:3857"];var i=function(t){var e=d(t,"extension");if(e)return d(e,"proj4")}(e);return i?p(i):e}return function(t){return"+"===t[0]}(t)?p(t):void 0},O=function(t,e){var i,r;if(t=t||{},!e)return t;for(r in e)void 0!==(i=e[r])&&(t[r]=i);return t},N=function(t,e,i){var r=t*e;return i/Math.sqrt(1-r*r)},L=function(t){return t<0?-1:1},T=function(t){return Math.abs(t)<=l?t:t-L(t)*h},R=function(t,e,i){var r=t*i,s=.5*t;return r=Math.pow((1-r)/(1+r),s),Math.tan(.5*(n-e))/r},z=function(t,e){for(var i,r,s=.5*t,a=n-2*Math.atan(e),o=0;o<=15;o++)if(i=t*Math.sin(a),a+=r=n-2*Math.atan(e*Math.pow((1-i)/(1+i),s))-a,Math.abs(r)<=1e-10)return a;return-9999};function B(t){return t}var D=[{init:function(){var t=this.b/this.a;this.es=1-t*t,"x0"in this||(this.x0=0),"y0"in this||(this.y0=0),this.e=Math.sqrt(this.es),this.lat_ts?this.sphere?this.k0=Math.cos(this.lat_ts):this.k0=N(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)):this.k0||(this.k?this.k0=this.k:this.k0=1)},forward:function(t){var e,i,r=t.x,s=t.y;if(s*a>90&&s*a<-90&&r*a>180&&r*a<-180)return null;if(Math.abs(Math.abs(s)-n)<=1e-10)return null;if(this.sphere)e=this.x0+this.a*this.k0*T(r-this.long0),i=this.y0+this.a*this.k0*Math.log(Math.tan(o+.5*s));else{var h=Math.sin(s),l=R(this.e,s,h);e=this.x0+this.a*this.k0*T(r-this.long0),i=this.y0-this.a*this.k0*Math.log(l)}return t.x=e,t.y=i,t},inverse:function(t){var e,i,r=t.x-this.x0,s=t.y-this.y0;if(this.sphere)i=n-2*Math.atan(Math.exp(-s/(this.a*this.k0)));else{var a=Math.exp(-s/(this.a*this.k0));if(-9999===(i=z(this.e,a)))return null}return e=T(this.long0+r/(this.a*this.k0)),t.x=e,t.y=i,t},names:["Mercator","Popular Visualisation Pseudo Mercator","Mercator_1SP","Mercator_Auxiliary_Sphere","merc"]},{init:function(){},forward:B,inverse:B,names:["longlat","identity"]}],j={},U=[];function F(t,e){var i=U.length;return t.names?(U[i]=t,t.names.forEach((function(t){j[t.toLowerCase()]=i})),this):(console.log(e),!0)}var q={start:function(){D.forEach(F)},add:F,get:function(t){if(!t)return!1;var e=t.toLowerCase();return void 0!==j[e]&&U[j[e]]?U[j[e]]:void 0}},G={MERIT:{a:6378137,rf:298.257,ellipseName:"MERIT 1983"},SGS85:{a:6378136,rf:298.257,ellipseName:"Soviet Geodetic System 85"},GRS80:{a:6378137,rf:298.*********,ellipseName:"GRS 1980(IUGG, 1980)"},IAU76:{a:6378140,rf:298.257,ellipseName:"IAU 1976"},airy:{a:6377563.396,b:6356256.91,ellipseName:"Airy 1830"},APL4:{a:6378137,rf:298.25,ellipseName:"Appl. Physics. 1965"},NWL9D:{a:6378145,rf:298.25,ellipseName:"Naval Weapons Lab., 1965"},mod_airy:{a:6377340.189,b:6356034.446,ellipseName:"Modified Airy"},andrae:{a:6377104.43,rf:300,ellipseName:"Andrae 1876 (Den., Iclnd.)"},aust_SA:{a:6378160,rf:298.25,ellipseName:"Australian Natl & S. Amer. 1969"},GRS67:{a:6378160,rf:298.*********,ellipseName:"GRS 67(IUGG 1967)"},bessel:{a:6377397.155,rf:299.1528128,ellipseName:"Bessel 1841"},bess_nam:{a:6377483.865,rf:299.1528128,ellipseName:"Bessel 1841 (Namibia)"},clrk66:{a:6378206.4,b:6356583.8,ellipseName:"Clarke 1866"},clrk80:{a:6378249.145,rf:293.4663,ellipseName:"Clarke 1880 mod."},clrk58:{a:6378293.*********,rf:294.2606763692654,ellipseName:"Clarke 1858"},CPM:{a:6375738.7,rf:334.29,ellipseName:"Comm. des Poids et Mesures 1799"},delmbr:{a:6376428,rf:311.5,ellipseName:"Delambre 1810 (Belgium)"},engelis:{a:6378136.05,rf:298.2566,ellipseName:"Engelis 1985"},evrst30:{a:6377276.345,rf:300.8017,ellipseName:"Everest 1830"},evrst48:{a:6377304.063,rf:300.8017,ellipseName:"Everest 1948"},evrst56:{a:6377301.243,rf:300.8017,ellipseName:"Everest 1956"},evrst69:{a:6377295.664,rf:300.8017,ellipseName:"Everest 1969"},evrstSS:{a:6377298.556,rf:300.8017,ellipseName:"Everest (Sabah & Sarawak)"},fschr60:{a:6378166,rf:298.3,ellipseName:"Fischer (Mercury Datum) 1960"},fschr60m:{a:6378155,rf:298.3,ellipseName:"Fischer 1960"},fschr68:{a:6378150,rf:298.3,ellipseName:"Fischer 1968"},helmert:{a:6378200,rf:298.3,ellipseName:"Helmert 1906"},hough:{a:6378270,rf:297,ellipseName:"Hough"},intl:{a:6378388,rf:297,ellipseName:"International 1909 (Hayford)"},kaula:{a:6378163,rf:298.24,ellipseName:"Kaula 1961"},lerch:{a:6378139,rf:298.257,ellipseName:"Lerch 1979"},mprts:{a:6397300,rf:191,ellipseName:"Maupertius 1738"},new_intl:{a:6378157.5,b:6356772.2,ellipseName:"New International 1967"},plessis:{a:6376523,rf:6355863,ellipseName:"Plessis 1817 (France)"},krass:{a:6378245,rf:298.3,ellipseName:"Krassovsky, 1942"},SEasia:{a:6378155,b:6356773.3205,ellipseName:"Southeast Asia"},walbeck:{a:6376896,b:6355834.8467,ellipseName:"Walbeck"},WGS60:{a:6378165,rf:298.3,ellipseName:"WGS 60"},WGS66:{a:6378145,rf:298.25,ellipseName:"WGS 66"},WGS7:{a:6378135,rf:298.26,ellipseName:"WGS 72"}},Z=G.WGS84={a:6378137,rf:298.257223563,ellipseName:"WGS 84"};G.sphere={a:6370997,b:6370997,ellipseName:"Normal Sphere (r=6370997)"};var Y={};Y.wgs84={towgs84:"0,0,0",ellipse:"WGS84",datumName:"WGS84"},Y.ch1903={towgs84:"674.374,15.056,405.346",ellipse:"bessel",datumName:"swiss"},Y.ggrs87={towgs84:"-199.87,74.79,246.62",ellipse:"GRS80",datumName:"Greek_Geodetic_Reference_System_1987"},Y.nad83={towgs84:"0,0,0",ellipse:"GRS80",datumName:"North_American_Datum_1983"},Y.nad27={nadgrids:"@conus,@alaska,@ntv2_0.gsb,@ntv1_can.dat",ellipse:"clrk66",datumName:"North_American_Datum_1927"},Y.potsdam={towgs84:"598.1,73.7,418.2,0.202,0.045,-2.455,6.7",ellipse:"bessel",datumName:"Potsdam Rauenberg 1950 DHDN"},Y.carthage={towgs84:"-263.0,6.0,431.0",ellipse:"clark80",datumName:"Carthage 1934 Tunisia"},Y.hermannskogel={towgs84:"577.326,90.129,463.919,5.137,1.474,5.297,2.4232",ellipse:"bessel",datumName:"Hermannskogel"},Y.osni52={towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"airy",datumName:"Irish National"},Y.ire65={towgs84:"482.530,-130.596,564.557,-1.042,-0.214,-0.631,8.15",ellipse:"mod_airy",datumName:"Ireland 1965"},Y.rassadiran={towgs84:"-133.63,-157.5,-158.62",ellipse:"intl",datumName:"Rassadiran"},Y.nzgd49={towgs84:"59.47,-5.04,187.44,0.47,-0.1,1.024,-4.5993",ellipse:"intl",datumName:"New Zealand Geodetic Datum 1949"},Y.osgb36={towgs84:"446.448,-125.157,542.060,0.1502,0.2470,0.8421,-20.4894",ellipse:"airy",datumName:"Airy 1830"},Y.s_jtsk={towgs84:"589,76,480",ellipse:"bessel",datumName:"S-JTSK (Ferro)"},Y.beduaram={towgs84:"-106,-87,188",ellipse:"clrk80",datumName:"Beduaram"},Y.gunung_segara={towgs84:"-403,684,41",ellipse:"bessel",datumName:"Gunung Segara Jakarta"},Y.rnb72={towgs84:"106.869,-52.2978,103.724,-0.33657,0.456955,-1.84218,1",ellipse:"intl",datumName:"Reseau National Belge 1972"};var V=function(t,e,i,n,s,a,o){var h={};return h.datum_type=void 0===t||"none"===t?5:4,e&&(h.datum_params=e.map(parseFloat),0===h.datum_params[0]&&0===h.datum_params[1]&&0===h.datum_params[2]||(h.datum_type=1),h.datum_params.length>3&&(0===h.datum_params[3]&&0===h.datum_params[4]&&0===h.datum_params[5]&&0===h.datum_params[6]||(h.datum_type=2,h.datum_params[3]*=r,h.datum_params[4]*=r,h.datum_params[5]*=r,h.datum_params[6]=h.datum_params[6]/1e6+1))),o&&(h.datum_type=3,h.grids=o),h.a=i,h.b=n,h.es=s,h.ep2=a,h},W={};function X(t){if(0===t.length)return null;var e="@"===t[0];return e&&(t=t.slice(1)),"null"===t?{name:"null",mandatory:!e,grid:null,isNull:!0}:{name:t,mandatory:!e,grid:W[t]||null,isNull:!1}}function H(t){return t/3600*Math.PI/180}function J(t,e,i){return String.fromCharCode.apply(null,new Uint8Array(t.buffer.slice(e,i)))}function K(t){return t.map((function(t){return[H(t.longitudeShift),H(t.latitudeShift)]}))}function Q(t,e,i){return{name:J(t,e+8,e+16).trim(),parent:J(t,e+24,e+24+8).trim(),lowerLatitude:t.getFloat64(e+72,i),upperLatitude:t.getFloat64(e+88,i),lowerLongitude:t.getFloat64(e+104,i),upperLongitude:t.getFloat64(e+120,i),latitudeInterval:t.getFloat64(e+136,i),longitudeInterval:t.getFloat64(e+152,i),gridNodeCount:t.getInt32(e+168,i)}}function $(t,e,i,r){for(var n=e+176,s=[],a=0;a<i.gridNodeCount;a++){var o={latitudeShift:t.getFloat32(n+16*a,r),longitudeShift:t.getFloat32(n+16*a+4,r),latitudeAccuracy:t.getFloat32(n+16*a+8,r),longitudeAccuracy:t.getFloat32(n+16*a+12,r)};s.push(o)}return s}function tt(t,e){if(!(this instanceof tt))return new tt(t);e=e||function(t){if(t)throw t};var i=I(t);if("object"==typeof i){var r=tt.projections.get(i.projName);if(r){if(i.datumCode&&"none"!==i.datumCode){var n=d(Y,i.datumCode);n&&(i.datum_params=i.datum_params||(n.towgs84?n.towgs84.split(","):null),i.ellps=n.ellipse,i.datumName=n.datumName?n.datumName:i.datumCode)}i.k0=i.k0||1,i.axis=i.axis||"enu",i.ellps=i.ellps||"wgs84",i.lat1=i.lat1||i.lat0;var s,a,o,h,l,u,f,c=function(t,e,i,r,n){if(!t){var s=d(G,r);s||(s=Z),t=s.a,e=s.b,i=s.rf}return i&&!e&&(e=(1-1/i)*t),(0===i||Math.abs(t-e)<1e-10)&&(n=!0,e=t),{a:t,b:e,rf:i,sphere:n}}(i.a,i.b,i.rf,i.ellps,i.sphere),p=(s=c.a,a=c.b,c.rf,o=i.R_A,u=((h=s*s)-(l=a*a))/h,f=0,o?(h=(s*=1-u*(.16666666666666666+u*(.04722222222222222+.022156084656084655*u)))*s,u=0):f=Math.sqrt(u),{es:u,e:f,ep2:(h-l)/l}),g=function(t){return void 0===t?null:t.split(",").map(X)}(i.nadgrids),m=i.datum||V(i.datumCode,i.datum_params,c.a,c.b,p.es,p.ep2,g);O(this,i),O(this,r),this.a=c.a,this.b=c.b,this.rf=c.rf,this.sphere=c.sphere,this.es=p.es,this.e=p.e,this.ep2=p.ep2,this.datum=m,this.init(),e(null,this)}else e(t)}else e(t)}tt.projections=q,tt.projections.start();var et=tt;function it(t,e,i){var r,s,a,o,h=t.x,l=t.y,u=t.z?t.z:0;if(l<-n&&l>-1.001*n)l=-n;else if(l>n&&l<1.001*n)l=n;else{if(l<-n)return{x:-1/0,y:-1/0,z:t.z};if(l>n)return{x:1/0,y:1/0,z:t.z}}return h>Math.PI&&(h-=2*Math.PI),s=Math.sin(l),o=Math.cos(l),a=s*s,{x:((r=i/Math.sqrt(1-e*a))+u)*o*Math.cos(h),y:(r+u)*o*Math.sin(h),z:(r*(1-e)+u)*s}}function rt(t,e,i,r){var s,a,o,h,l,u,f,c,d,p,g,m,_,y,v,b=t.x,M=t.y,w=t.z?t.z:0;if(s=Math.sqrt(b*b+M*M),a=Math.sqrt(b*b+M*M+w*w),s/i<1e-12){if(y=0,a/i<1e-12)return n,v=-r,{x:t.x,y:t.y,z:t.z}}else y=Math.atan2(M,b);o=w/a,c=(h=s/a)*(1-e)*(l=1/Math.sqrt(1-e*(2-e)*h*h)),d=o*l,_=0;do{_++,u=e*(f=i/Math.sqrt(1-e*d*d))/(f+(v=s*c+w*d-f*(1-e*d*d))),m=(g=o*(l=1/Math.sqrt(1-u*(2-u)*h*h)))*c-(p=h*(1-u)*l)*d,c=p,d=g}while(m*m>1e-24&&_<30);return{x:y,y:Math.atan(g/Math.abs(p)),z:v}}function nt(t){return 1===t||2===t}var st=function(t,e,i){if(function(t,e){return t.datum_type===e.datum_type&&(!(t.a!==e.a||Math.abs(t.es-e.es)>5e-11)&&(1===t.datum_type?t.datum_params[0]===e.datum_params[0]&&t.datum_params[1]===e.datum_params[1]&&t.datum_params[2]===e.datum_params[2]:2!==t.datum_type||t.datum_params[0]===e.datum_params[0]&&t.datum_params[1]===e.datum_params[1]&&t.datum_params[2]===e.datum_params[2]&&t.datum_params[3]===e.datum_params[3]&&t.datum_params[4]===e.datum_params[4]&&t.datum_params[5]===e.datum_params[5]&&t.datum_params[6]===e.datum_params[6]))}(t,e))return i;if(5===t.datum_type||5===e.datum_type)return i;var r=t.a,n=t.es;if(3===t.datum_type){if(0!==at(t,!1,i))return;r=6378137,n=.0066943799901413165}var s=e.a,a=e.b,o=e.es;if(3===e.datum_type&&(s=6378137,a=6356752.314,o=.0066943799901413165),n===o&&r===s&&!nt(t.datum_type)&&!nt(e.datum_type))return i;if((i=it(i,n,r),nt(t.datum_type)&&(i=function(t,e,i){if(1===e)return{x:t.x+i[0],y:t.y+i[1],z:t.z+i[2]};if(2===e){var r=i[0],n=i[1],s=i[2],a=i[3],o=i[4],h=i[5],l=i[6];return{x:l*(t.x-h*t.y+o*t.z)+r,y:l*(h*t.x+t.y-a*t.z)+n,z:l*(-o*t.x+a*t.y+t.z)+s}}}(i,t.datum_type,t.datum_params)),nt(e.datum_type)&&(i=function(t,e,i){if(1===e)return{x:t.x-i[0],y:t.y-i[1],z:t.z-i[2]};if(2===e){var r=i[0],n=i[1],s=i[2],a=i[3],o=i[4],h=i[5],l=i[6],u=(t.x-r)/l,f=(t.y-n)/l,c=(t.z-s)/l;return{x:u+h*f-o*c,y:-h*u+f+a*c,z:o*u-a*f+c}}}(i,e.datum_type,e.datum_params)),i=rt(i,o,s,a),3===e.datum_type)&&0!==at(e,!0,i))return;return i};function at(t,e,i){if(null===t.grids||0===t.grids.length)return console.log("Grid shift grids not found"),-1;for(var r={x:-i.x,y:i.y},n={x:Number.NaN,y:Number.NaN},s=[],o=0;o<t.grids.length;o++){var h=t.grids[o];if(s.push(h.name),h.isNull){n=r;break}if(h.mandatory,null!==h.grid){var l=h.grid.subgrids[0],u=(Math.abs(l.del[1])+Math.abs(l.del[0]))/1e4,f=l.ll[0]-u,c=l.ll[1]-u,d=l.ll[0]+(l.lim[0]-1)*l.del[0]+u,p=l.ll[1]+(l.lim[1]-1)*l.del[1]+u;if(!(c>r.y||f>r.x||p<r.y||d<r.x||(n=ot(r,e,l),isNaN(n.x))))break}else if(h.mandatory)return console.log("Unable to find mandatory grid '"+h.name+"'"),-1}return isNaN(n.x)?(console.log("Failed to find a grid shift table for location '"+-r.x*a+" "+r.y*a+" tried: '"+s+"'"),-1):(i.x=-n.x,i.y=n.y,0)}function ot(t,e,i){var r={x:Number.NaN,y:Number.NaN};if(isNaN(t.x))return r;var n={x:t.x,y:t.y};n.x-=i.ll[0],n.y-=i.ll[1],n.x=T(n.x-Math.PI)+Math.PI;var s=ht(n,i);if(e){if(isNaN(s.x))return r;s.x=n.x-s.x,s.y=n.y-s.y;var a,o,h=9;do{if(o=ht(s,i),isNaN(o.x)){console.log("Inverse grid shift iteration failed, presumably at grid edge.  Using first approximation.");break}a={x:n.x-(o.x+s.x),y:n.y-(o.y+s.y)},s.x+=a.x,s.y+=a.y}while(h--&&Math.abs(a.x)>1e-12&&Math.abs(a.y)>1e-12);if(h<0)return console.log("Inverse grid shift iterator failed to converge."),r;r.x=T(s.x+i.ll[0]),r.y=s.y+i.ll[1]}else isNaN(s.x)||(r.x=t.x+s.x,r.y=t.y+s.y);return r}function ht(t,e){var i,r={x:t.x/e.del[0],y:t.y/e.del[1]},n=Math.floor(r.x),s=Math.floor(r.y),a=r.x-1*n,o=r.y-1*s,h={x:Number.NaN,y:Number.NaN};if(n<0||n>=e.lim[0])return h;if(s<0||s>=e.lim[1])return h;i=s*e.lim[0]+n;var l=e.cvs[i][0],u=e.cvs[i][1];i++;var f=e.cvs[i][0],c=e.cvs[i][1];i+=e.lim[0];var d=e.cvs[i][0],p=e.cvs[i][1];i--;var g=e.cvs[i][0],m=e.cvs[i][1],_=a*o,y=a*(1-o),v=(1-a)*(1-o),b=(1-a)*o;return h.x=v*l+y*f+b*g+_*d,h.y=v*u+y*c+b*m+_*p,h}var lt=function(t,e,i){var r,n,s,a=i.x,o=i.y,h=i.z||0,l={};for(s=0;s<3;s++)if(!e||2!==s||void 0!==i.z)switch(0===s?(r=a,n=-1!=="ew".indexOf(t.axis[s])?"x":"y"):1===s?(r=o,n=-1!=="ns".indexOf(t.axis[s])?"y":"x"):(r=h,n="z"),t.axis[s]){case"e":l[n]=r;break;case"w":l[n]=-r;break;case"n":l[n]=r;break;case"s":l[n]=-r;break;case"u":void 0!==i[n]&&(l.z=r);break;case"d":void 0!==i[n]&&(l.z=-r);break;default:return null}return l},ut=function(t){var e={x:t[0],y:t[1]};return t.length>2&&(e.z=t[2]),t.length>3&&(e.m=t[3]),e};function ft(t){if("function"==typeof Number.isFinite){if(Number.isFinite(t))return;throw new TypeError("coordinates must be finite numbers")}if("number"!=typeof t||t!=t||!isFinite(t))throw new TypeError("coordinates must be finite numbers")}function ct(t,e,i,r){var n;if(Array.isArray(i)&&(i=ut(i)),function(t){ft(t.x),ft(t.y)}(i),t.datum&&e.datum&&function(t,e){return(1===t.datum.datum_type||2===t.datum.datum_type)&&"WGS84"!==e.datumCode||(1===e.datum.datum_type||2===e.datum.datum_type)&&"WGS84"!==t.datumCode}(t,e)&&(i=ct(t,n=new et("WGS84"),i,r),t=n),r&&"enu"!==t.axis&&(i=lt(t,!1,i)),"longlat"===t.projName)i={x:i.x*s,y:i.y*s,z:i.z||0};else if(t.to_meter&&(i={x:i.x*t.to_meter,y:i.y*t.to_meter,z:i.z||0}),!(i=t.inverse(i)))return;if(t.from_greenwich&&(i.x+=t.from_greenwich),i=st(t.datum,e.datum,i))return e.from_greenwich&&(i={x:i.x-e.from_greenwich,y:i.y,z:i.z||0}),"longlat"===e.projName?i={x:i.x*a,y:i.y*a,z:i.z||0}:(i=e.forward(i),e.to_meter&&(i={x:i.x/e.to_meter,y:i.y/e.to_meter,z:i.z||0})),r&&"enu"!==e.axis?lt(e,!0,i):i}var dt=et("WGS84");function pt(t,e,i,r){var n,s,a;return Array.isArray(i)?(n=ct(t,e,i,r)||{x:NaN,y:NaN},i.length>2?void 0!==t.name&&"geocent"===t.name||void 0!==e.name&&"geocent"===e.name?"number"==typeof n.z?[n.x,n.y,n.z].concat(i.splice(3)):[n.x,n.y,i[2]].concat(i.splice(3)):[n.x,n.y].concat(i.splice(2)):[n.x,n.y]):(s=ct(t,e,i,r),2===(a=Object.keys(i)).length||a.forEach((function(r){if(void 0!==t.name&&"geocent"===t.name||void 0!==e.name&&"geocent"===e.name){if("x"===r||"y"===r||"z"===r)return}else if("x"===r||"y"===r)return;s[r]=i[r]})),s)}function gt(t){return t instanceof et?t:t.oProj?t.oProj:et(t)}var mt=function(t,e,i){t=gt(t);var r,n=!1;return void 0===e?(e=t,t=dt,n=!0):(void 0!==e.x||Array.isArray(e))&&(i=e,e=t,t=dt,n=!0),e=gt(e),i?pt(t,e,i):(r={forward:function(i,r){return pt(t,e,i,r)},inverse:function(i,r){return pt(e,t,i,r)}},n&&(r.oProj=e),r)},_t=73,yt=79,vt={forward:bt,inverse:function(t){var e=Ct(Et(t.toUpperCase()));if(e.lat&&e.lon)return[e.lon,e.lat,e.lon,e.lat];return[e.left,e.bottom,e.right,e.top]},toPoint:Mt};function bt(t,e){return e=e||5,function(t,e){var i="00000"+t.easting,r="00000"+t.northing;return t.zoneNumber+t.zoneLetter+(d=t.easting,p=t.northing,g=t.zoneNumber,m=kt(g),_=Math.floor(d/1e5),y=Math.floor(p/1e5)%20,n=_,s=y,a=m,o=a-1,h="AJSAJS".charCodeAt(o),l="AFAFAF".charCodeAt(o),u=h+n-1,f=l+s,c=!1,u>90&&(u=u-90+65-1,c=!0),(u===_t||h<_t&&u>_t||(u>_t||h<_t)&&c)&&u++,(u===yt||h<yt&&u>yt||(u>yt||h<yt)&&c)&&++u===_t&&u++,u>90&&(u=u-90+65-1),f>86?(f=f-86+65-1,c=!0):c=!1,(f===_t||l<_t&&f>_t||(f>_t||l<_t)&&c)&&f++,(f===yt||l<yt&&f>yt||(f>yt||l<yt)&&c)&&++f===_t&&f++,f>86&&(f=f-86+65-1),String.fromCharCode(u)+String.fromCharCode(f))+i.substr(i.length-5,e)+r.substr(r.length-5,e);var n,s,a,o,h,l,u,f,c;var d,p,g,m,_,y}(function(t){var e,i,r,n,s,a,o,h=t.lat,l=t.lon,u=6378137,f=wt(h),c=wt(l);o=Math.floor((l+180)/6)+1,180===l&&(o=60);h>=56&&h<64&&l>=3&&l<12&&(o=32);h>=72&&h<84&&(l>=0&&l<9?o=31:l>=9&&l<21?o=33:l>=21&&l<33?o=35:l>=33&&l<42&&(o=37));a=wt(6*(o-1)-180+3),.006739496752268451,e=u/Math.sqrt(1-.00669438*Math.sin(f)*Math.sin(f)),i=Math.tan(f)*Math.tan(f),r=.006739496752268451*Math.cos(f)*Math.cos(f),n=Math.cos(f)*(c-a),s=u*(.9983242984503243*f-.002514607064228144*Math.sin(2*f)+2639046602129982e-21*Math.sin(4*f)-3.418046101696858e-9*Math.sin(6*f));var d=.9996*e*(n+(1-i+r)*n*n*n/6+(5-18*i+i*i+72*r-.39089081163157013)*n*n*n*n*n/120)+5e5,p=.9996*(s+e*Math.tan(f)*(n*n/2+(5-i+9*r+4*r*r)*n*n*n*n/24+(61-58*i+i*i+600*r-2.2240339282485886)*n*n*n*n*n*n/720));h<0&&(p+=1e7);return{northing:Math.round(p),easting:Math.round(d),zoneNumber:o,zoneLetter:St(h)}}({lat:t[1],lon:t[0]}),e)}function Mt(t){var e=Ct(Et(t.toUpperCase()));return e.lat&&e.lon?[e.lon,e.lat]:[(e.left+e.right)/2,(e.top+e.bottom)/2]}function wt(t){return t*(Math.PI/180)}function xt(t){return t/Math.PI*180}function Ct(t){var e=t.northing,i=t.easting,r=t.zoneLetter,n=t.zoneNumber;if(n<0||n>60)return null;var s,a,o,h,l,u,f,c,d=6378137,p=(1-Math.sqrt(.99330562))/(1+Math.sqrt(.99330562)),g=i-5e5,m=e;r<"N"&&(m-=1e7),u=6*(n-1)-180+3,c=(f=m/.9996/6367449.145945056)+(3*p/2-27*p*p*p/32)*Math.sin(2*f)+(21*p*p/16-55*p*p*p*p/32)*Math.sin(4*f)+151*p*p*p/96*Math.sin(6*f),s=d/Math.sqrt(1-.00669438*Math.sin(c)*Math.sin(c)),a=Math.tan(c)*Math.tan(c),o=.006739496752268451*Math.cos(c)*Math.cos(c),h=.99330562*d/Math.pow(1-.00669438*Math.sin(c)*Math.sin(c),1.5),l=g/(.9996*s);var _=c-s*Math.tan(c)/h*(l*l/2-(5+3*a+10*o-4*o*o-.06065547077041606)*l*l*l*l/24+(61+90*a+298*o+45*a*a-1.6983531815716497-3*o*o)*l*l*l*l*l*l/720);_=xt(_);var y,v=(l-(1+2*a+o)*l*l*l/6+(5-2*o+28*a-3*o*o+.05391597401814761+24*a*a)*l*l*l*l*l/120)/Math.cos(c);if(v=u+xt(v),t.accuracy){var b=Ct({northing:t.northing+t.accuracy,easting:t.easting+t.accuracy,zoneLetter:t.zoneLetter,zoneNumber:t.zoneNumber});y={top:b.lat,right:b.lon,bottom:_,left:v}}else y={lat:_,lon:v};return y}function St(t){var e="Z";return 84>=t&&t>=72?e="X":72>t&&t>=64?e="W":64>t&&t>=56?e="V":56>t&&t>=48?e="U":48>t&&t>=40?e="T":40>t&&t>=32?e="S":32>t&&t>=24?e="R":24>t&&t>=16?e="Q":16>t&&t>=8?e="P":8>t&&t>=0?e="N":0>t&&t>=-8?e="M":-8>t&&t>=-16?e="L":-16>t&&t>=-24?e="K":-24>t&&t>=-32?e="J":-32>t&&t>=-40?e="H":-40>t&&t>=-48?e="G":-48>t&&t>=-56?e="F":-56>t&&t>=-64?e="E":-64>t&&t>=-72?e="D":-72>t&&t>=-80&&(e="C"),e}function kt(t){var e=t%6;return 0===e&&(e=6),e}function Et(t){if(t&&0===t.length)throw"MGRSPoint coverting from nothing";for(var e,i=t.length,r=null,n="",s=0;!/[A-Z]/.test(e=t.charAt(s));){if(s>=2)throw"MGRSPoint bad conversion from: "+t;n+=e,s++}var a=parseInt(n,10);if(0===s||s+3>i)throw"MGRSPoint bad conversion from: "+t;var o=t.charAt(s++);if(o<="A"||"B"===o||"Y"===o||o>="Z"||"I"===o||"O"===o)throw"MGRSPoint zone letter "+o+" not handled: "+t;r=t.substring(s,s+=2);for(var h=kt(a),l=function(t,e){var i="AJSAJS".charCodeAt(e-1),r=1e5,n=!1;for(;i!==t.charCodeAt(0);){if(++i===_t&&i++,i===yt&&i++,i>90){if(n)throw"Bad character: "+t;i=65,n=!0}r+=1e5}return r}(r.charAt(0),h),u=function(t,e){if(t>"V")throw"MGRSPoint given invalid Northing "+t;var i="AFAFAF".charCodeAt(e-1),r=0,n=!1;for(;i!==t.charCodeAt(0);){if(++i===_t&&i++,i===yt&&i++,i>86){if(n)throw"Bad character: "+t;i=65,n=!0}r+=1e5}return r}(r.charAt(1),h);u<At(o);)u+=2e6;var f=i-s;if(f%2!=0)throw"MGRSPoint has to have an even number \nof digits after the zone letter and two 100km letters - front \nhalf for easting meters, second half for \nnorthing meters"+t;var c,d,p,g=f/2,m=0,_=0;return g>0&&(c=1e5/Math.pow(10,g),d=t.substring(s,s+g),m=parseFloat(d)*c,p=t.substring(s+g),_=parseFloat(p)*c),{easting:m+l,northing:_+u,zoneLetter:o,zoneNumber:a,accuracy:c}}function At(t){var e;switch(t){case"C":e=11e5;break;case"D":e=2e6;break;case"E":e=28e5;break;case"F":e=37e5;break;case"G":e=46e5;break;case"H":e=55e5;break;case"J":e=64e5;break;case"K":e=73e5;break;case"L":e=82e5;break;case"M":e=91e5;break;case"N":e=0;break;case"P":e=8e5;break;case"Q":e=17e5;break;case"R":e=26e5;break;case"S":e=35e5;break;case"T":e=44e5;break;case"U":e=53e5;break;case"V":e=62e5;break;case"W":e=7e6;break;case"X":e=79e5;break;default:e=-1}if(e>=0)return e;throw"Invalid zone letter: "+t}function Pt(t,e,i){if(!(this instanceof Pt))return new Pt(t,e,i);if(Array.isArray(t))this.x=t[0],this.y=t[1],this.z=t[2]||0;else if("object"==typeof t)this.x=t.x,this.y=t.y,this.z=t.z||0;else if("string"==typeof t&&void 0===e){var r=t.split(",");this.x=parseFloat(r[0],10),this.y=parseFloat(r[1],10),this.z=parseFloat(r[2],10)||0}else this.x=t,this.y=e,this.z=i||0;console.warn("proj4.Point will be removed in version 3, use proj4.toPoint")}Pt.fromMGRS=function(t){return new Pt(Mt(t))},Pt.prototype.toMGRS=function(t){return bt([this.x,this.y],t)};var It=Pt,Ot=.01068115234375,Nt=function(t){var e=[];e[0]=1-t*(.25+t*(.046875+t*(.01953125+t*Ot))),e[1]=t*(.75-t*(.046875+t*(.01953125+t*Ot)));var i=t*t;return e[2]=i*(.46875-t*(.013020833333333334+.007120768229166667*t)),i*=t,e[3]=i*(.3645833333333333-.005696614583333333*t),e[4]=i*t*.3076171875,e},Lt=function(t,e,i,r){return i*=e,e*=e,r[0]*t-i*(r[1]+e*(r[2]+e*(r[3]+e*r[4])))},Tt=function(t,e,i){for(var r=1/(1-e),n=t,s=20;s;--s){var a=Math.sin(n),o=1-e*a*a;if(n-=o=(Lt(n,a,Math.cos(n),i)-t)*(o*Math.sqrt(o))*r,Math.abs(o)<1e-10)return n}return n};var Rt={init:function(){this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.es&&(this.en=Nt(this.es),this.ml0=Lt(this.lat0,Math.sin(this.lat0),Math.cos(this.lat0),this.en))},forward:function(t){var e,i,r,n=t.x,s=t.y,a=T(n-this.long0),o=Math.sin(s),h=Math.cos(s);if(this.es){var l=h*a,u=Math.pow(l,2),f=this.ep2*Math.pow(h,2),c=Math.pow(f,2),d=Math.abs(h)>1e-10?Math.tan(s):0,p=Math.pow(d,2),g=Math.pow(p,2);e=1-this.es*Math.pow(o,2),l/=Math.sqrt(e);var m=Lt(s,o,h,this.en);i=this.a*(this.k0*l*(1+u/6*(1-p+f+u/20*(5-18*p+g+14*f-58*p*f+u/42*(61+179*g-g*p-479*p)))))+this.x0,r=this.a*(this.k0*(m-this.ml0+o*a*l/2*(1+u/12*(5-p+9*f+4*c+u/30*(61+g-58*p+270*f-330*p*f+u/56*(1385+543*g-g*p-3111*p))))))+this.y0}else{var _=h*Math.sin(a);if(Math.abs(Math.abs(_)-1)<1e-10)return 93;if(i=.5*this.a*this.k0*Math.log((1+_)/(1-_))+this.x0,r=h*Math.cos(a)/Math.sqrt(1-Math.pow(_,2)),(_=Math.abs(r))>=1){if(_-1>1e-10)return 93;r=0}else r=Math.acos(r);s<0&&(r=-r),r=this.a*this.k0*(r-this.lat0)+this.y0}return t.x=i,t.y=r,t},inverse:function(t){var e,i,r,s,a=(t.x-this.x0)*(1/this.a),o=(t.y-this.y0)*(1/this.a);if(this.es)if(e=this.ml0+o/this.k0,i=Tt(e,this.es,this.en),Math.abs(i)<n){var h=Math.sin(i),l=Math.cos(i),u=Math.abs(l)>1e-10?Math.tan(i):0,f=this.ep2*Math.pow(l,2),c=Math.pow(f,2),d=Math.pow(u,2),p=Math.pow(d,2);e=1-this.es*Math.pow(h,2);var g=a*Math.sqrt(e)/this.k0,m=Math.pow(g,2);r=i-(e*=u)*m/(1-this.es)*.5*(1-m/12*(5+3*d-9*f*d+f-4*c-m/30*(61+90*d-252*f*d+45*p+46*f-m/56*(1385+3633*d+4095*p+1574*p*d)))),s=T(this.long0+g*(1-m/6*(1+2*d+f-m/20*(5+28*d+24*p+8*f*d+6*f-m/42*(61+662*d+1320*p+720*p*d))))/l)}else r=n*L(o),s=0;else{var _=Math.exp(a/this.k0),y=.5*(_-1/_),v=this.lat0+o/this.k0,b=Math.cos(v);e=Math.sqrt((1-Math.pow(b,2))/(1+Math.pow(y,2))),r=Math.asin(e),o<0&&(r=-r),s=0===y&&0===b?0:T(Math.atan2(y,b)+this.long0)}return t.x=s,t.y=r,t},names:["Fast_Transverse_Mercator","Fast Transverse Mercator"]},zt=function(t){var e=Math.exp(t);return e=(e-1/e)/2},Bt=function(t,e){t=Math.abs(t),e=Math.abs(e);var i=Math.max(t,e),r=Math.min(t,e)/(i||1);return i*Math.sqrt(1+Math.pow(r,2))},Dt=function(t){var e=Math.abs(t);return e=function(t){var e=1+t,i=e-1;return 0===i?t:t*Math.log(e)/i}(e*(1+e/(Bt(1,e)+1))),t<0?-e:e},jt=function(t,e){for(var i,r=2*Math.cos(2*e),n=t.length-1,s=t[n],a=0;--n>=0;)i=r*s-a+t[n],a=s,s=i;return e+i*Math.sin(2*e)},Ut=function(t,e,i){for(var r,n,s=Math.sin(e),a=Math.cos(e),o=zt(i),h=function(t){var e=Math.exp(t);return e=(e+1/e)/2}(i),l=2*a*h,u=-2*s*o,f=t.length-1,c=t[f],d=0,p=0,g=0;--f>=0;)r=p,n=d,c=l*(p=c)-r-u*(d=g)+t[f],g=u*p-n+l*d;return[(l=s*h)*c-(u=a*o)*g,l*g+u*c]};var Ft={init:function(){if(!this.approx&&(isNaN(this.es)||this.es<=0))throw new Error('Incorrect elliptical usage. Try using the +approx option in the proj string, or PROJECTION["Fast_Transverse_Mercator"] in the WKT.');this.approx&&(Rt.init.apply(this),this.forward=Rt.forward,this.inverse=Rt.inverse),this.x0=void 0!==this.x0?this.x0:0,this.y0=void 0!==this.y0?this.y0:0,this.long0=void 0!==this.long0?this.long0:0,this.lat0=void 0!==this.lat0?this.lat0:0,this.cgb=[],this.cbg=[],this.utg=[],this.gtu=[];var t=this.es/(1+Math.sqrt(1-this.es)),e=t/(2-t),i=e;this.cgb[0]=e*(2+e*(-2/3+e*(e*(116/45+e*(26/45+e*(-2854/675)))-2))),this.cbg[0]=e*(e*(2/3+e*(4/3+e*(-82/45+e*(32/45+e*(4642/4725)))))-2),i*=e,this.cgb[1]=i*(7/3+e*(e*(-227/45+e*(2704/315+e*(2323/945)))-1.6)),this.cbg[1]=i*(5/3+e*(-16/15+e*(-13/9+e*(904/315+e*(-1522/945))))),i*=e,this.cgb[2]=i*(56/15+e*(-136/35+e*(-1262/105+e*(73814/2835)))),this.cbg[2]=i*(-26/15+e*(34/21+e*(1.6+e*(-12686/2835)))),i*=e,this.cgb[3]=i*(4279/630+e*(-332/35+e*(-399572/14175))),this.cbg[3]=i*(1237/630+e*(e*(-24832/14175)-2.4)),i*=e,this.cgb[4]=i*(4174/315+e*(-144838/6237)),this.cbg[4]=i*(-734/315+e*(109598/31185)),i*=e,this.cgb[5]=i*(601676/22275),this.cbg[5]=i*(444337/155925),i=Math.pow(e,2),this.Qn=this.k0/(1+e)*(1+i*(1/4+i*(1/64+i/256))),this.utg[0]=e*(e*(2/3+e*(-37/96+e*(1/360+e*(81/512+e*(-96199/604800)))))-.5),this.gtu[0]=e*(.5+e*(-2/3+e*(5/16+e*(41/180+e*(-127/288+e*(7891/37800)))))),this.utg[1]=i*(-1/48+e*(-1/15+e*(437/1440+e*(-46/105+e*(1118711/3870720))))),this.gtu[1]=i*(13/48+e*(e*(557/1440+e*(281/630+e*(-1983433/1935360)))-.6)),i*=e,this.utg[2]=i*(-17/480+e*(37/840+e*(209/4480+e*(-5569/90720)))),this.gtu[2]=i*(61/240+e*(-103/140+e*(15061/26880+e*(167603/181440)))),i*=e,this.utg[3]=i*(-4397/161280+e*(11/504+e*(830251/7257600))),this.gtu[3]=i*(49561/161280+e*(-179/168+e*(6601661/7257600))),i*=e,this.utg[4]=i*(-4583/161280+e*(108847/3991680)),this.gtu[4]=i*(34729/80640+e*(-3418889/1995840)),i*=e,this.utg[5]=i*(-20648693/638668800),this.gtu[5]=.6650675310896665*i;var r=jt(this.cbg,this.lat0);this.Zb=-this.Qn*(r+function(t,e){for(var i,r=2*Math.cos(e),n=t.length-1,s=t[n],a=0;--n>=0;)i=r*s-a+t[n],a=s,s=i;return Math.sin(e)*i}(this.gtu,2*r))},forward:function(t){var e=T(t.x-this.long0),i=t.y;i=jt(this.cbg,i);var r=Math.sin(i),n=Math.cos(i),s=Math.sin(e),a=Math.cos(e);i=Math.atan2(r,a*n),e=Math.atan2(s*n,Bt(r,n*a)),e=Dt(Math.tan(e));var o,h,l=Ut(this.gtu,2*i,2*e);return i+=l[0],e+=l[1],Math.abs(e)<=2.623395162778?(o=this.a*(this.Qn*e)+this.x0,h=this.a*(this.Qn*i+this.Zb)+this.y0):(o=1/0,h=1/0),t.x=o,t.y=h,t},inverse:function(t){var e,i,r=(t.x-this.x0)*(1/this.a),n=(t.y-this.y0)*(1/this.a);if(n=(n-this.Zb)/this.Qn,r/=this.Qn,Math.abs(r)<=2.623395162778){var s=Ut(this.utg,2*n,2*r);n+=s[0],r+=s[1],r=Math.atan(zt(r));var a=Math.sin(n),o=Math.cos(n),h=Math.sin(r),l=Math.cos(r);n=Math.atan2(a*l,Bt(h,l*o)),r=Math.atan2(h,l*o),e=T(r+this.long0),i=jt(this.cgb,n)}else e=1/0,i=1/0;return t.x=e,t.y=i,t},names:["Extended_Transverse_Mercator","Extended Transverse Mercator","etmerc","Transverse_Mercator","Transverse Mercator","tmerc"]};var qt={init:function(){var t=function(t,e){if(void 0===t){if((t=Math.floor(30*(T(e)+Math.PI)/Math.PI)+1)<0)return 0;if(t>60)return 60}return t}(this.zone,this.long0);if(void 0===t)throw new Error("unknown utm zone");this.lat0=0,this.long0=(6*Math.abs(t)-183)*s,this.x0=5e5,this.y0=this.utmSouth?1e7:0,this.k0=.9996,Ft.init.apply(this),this.forward=Ft.forward,this.inverse=Ft.inverse},names:["Universal Transverse Mercator System","utm"],dependsOn:"etmerc"},Gt=function(t,e){return Math.pow((1-t)/(1+t),e)};var Zt={init:function(){var t=Math.sin(this.lat0),e=Math.cos(this.lat0);e*=e,this.rc=Math.sqrt(1-this.es)/(1-this.es*t*t),this.C=Math.sqrt(1+this.es*e*e/(1-this.es)),this.phic0=Math.asin(t/this.C),this.ratexp=.5*this.C*this.e,this.K=Math.tan(.5*this.phic0+o)/(Math.pow(Math.tan(.5*this.lat0+o),this.C)*Gt(this.e*t,this.ratexp))},forward:function(t){var e=t.x,i=t.y;return t.y=2*Math.atan(this.K*Math.pow(Math.tan(.5*i+o),this.C)*Gt(this.e*Math.sin(i),this.ratexp))-n,t.x=this.C*e,t},inverse:function(t){for(var e=t.x/this.C,i=t.y,r=Math.pow(Math.tan(.5*i+o)/this.K,1/this.C),s=20;s>0&&(i=2*Math.atan(r*Gt(this.e*Math.sin(t.y),-.5*this.e))-n,!(Math.abs(i-t.y)<1e-14));--s)t.y=i;return s?(t.x=e,t.y=i,t):null},names:["gauss"]};var Yt={init:function(){Zt.init.apply(this),this.rc&&(this.sinc0=Math.sin(this.phic0),this.cosc0=Math.cos(this.phic0),this.R2=2*this.rc,this.title||(this.title="Oblique Stereographic Alternative"))},forward:function(t){var e,i,r,n;return t.x=T(t.x-this.long0),Zt.forward.apply(this,[t]),e=Math.sin(t.y),i=Math.cos(t.y),r=Math.cos(t.x),n=this.k0*this.R2/(1+this.sinc0*e+this.cosc0*i*r),t.x=n*i*Math.sin(t.x),t.y=n*(this.cosc0*e-this.sinc0*i*r),t.x=this.a*t.x+this.x0,t.y=this.a*t.y+this.y0,t},inverse:function(t){var e,i,r,n,s;if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,s=Math.sqrt(t.x*t.x+t.y*t.y)){var a=2*Math.atan2(s,this.R2);e=Math.sin(a),i=Math.cos(a),n=Math.asin(i*this.sinc0+t.y*e*this.cosc0/s),r=Math.atan2(t.x*e,s*this.cosc0*i-t.y*this.sinc0*e)}else n=this.phic0,r=0;return t.x=r,t.y=n,Zt.inverse.apply(this,[t]),t.x=T(t.x+this.long0),t},names:["Stereographic_North_Pole","Oblique_Stereographic","Polar_Stereographic","sterea","Oblique Stereographic Alternative","Double_Stereographic"]};var Vt={init:function(){this.coslat0=Math.cos(this.lat0),this.sinlat0=Math.sin(this.lat0),this.sphere?1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=1e-10&&(this.k0=.5*(1+L(this.lat0)*Math.sin(this.lat_ts))):(Math.abs(this.coslat0)<=1e-10&&(this.lat0>0?this.con=1:this.con=-1),this.cons=Math.sqrt(Math.pow(1+this.e,1+this.e)*Math.pow(1-this.e,1-this.e)),1===this.k0&&!isNaN(this.lat_ts)&&Math.abs(this.coslat0)<=1e-10&&(this.k0=.5*this.cons*N(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts))/R(this.e,this.con*this.lat_ts,this.con*Math.sin(this.lat_ts))),this.ms1=N(this.e,this.sinlat0,this.coslat0),this.X0=2*Math.atan(this.ssfn_(this.lat0,this.sinlat0,this.e))-n,this.cosX0=Math.cos(this.X0),this.sinX0=Math.sin(this.X0))},forward:function(t){var e,i,r,s,a,o,h=t.x,l=t.y,u=Math.sin(l),f=Math.cos(l),c=T(h-this.long0);return Math.abs(Math.abs(h-this.long0)-Math.PI)<=1e-10&&Math.abs(l+this.lat0)<=1e-10?(t.x=NaN,t.y=NaN,t):this.sphere?(e=2*this.k0/(1+this.sinlat0*u+this.coslat0*f*Math.cos(c)),t.x=this.a*e*f*Math.sin(c)+this.x0,t.y=this.a*e*(this.coslat0*u-this.sinlat0*f*Math.cos(c))+this.y0,t):(i=2*Math.atan(this.ssfn_(l,u,this.e))-n,s=Math.cos(i),r=Math.sin(i),Math.abs(this.coslat0)<=1e-10?(a=R(this.e,l*this.con,this.con*u),o=2*this.a*this.k0*a/this.cons,t.x=this.x0+o*Math.sin(h-this.long0),t.y=this.y0-this.con*o*Math.cos(h-this.long0),t):(Math.abs(this.sinlat0)<1e-10?(e=2*this.a*this.k0/(1+s*Math.cos(c)),t.y=e*r):(e=2*this.a*this.k0*this.ms1/(this.cosX0*(1+this.sinX0*r+this.cosX0*s*Math.cos(c))),t.y=e*(this.cosX0*r-this.sinX0*s*Math.cos(c))+this.y0),t.x=e*s*Math.sin(c)+this.x0,t))},inverse:function(t){var e,i,r,s,a;t.x-=this.x0,t.y-=this.y0;var o=Math.sqrt(t.x*t.x+t.y*t.y);if(this.sphere){var h=2*Math.atan(o/(2*this.a*this.k0));return e=this.long0,i=this.lat0,o<=1e-10?(t.x=e,t.y=i,t):(i=Math.asin(Math.cos(h)*this.sinlat0+t.y*Math.sin(h)*this.coslat0/o),e=Math.abs(this.coslat0)<1e-10?this.lat0>0?T(this.long0+Math.atan2(t.x,-1*t.y)):T(this.long0+Math.atan2(t.x,t.y)):T(this.long0+Math.atan2(t.x*Math.sin(h),o*this.coslat0*Math.cos(h)-t.y*this.sinlat0*Math.sin(h))),t.x=e,t.y=i,t)}if(Math.abs(this.coslat0)<=1e-10){if(o<=1e-10)return i=this.lat0,e=this.long0,t.x=e,t.y=i,t;t.x*=this.con,t.y*=this.con,r=o*this.cons/(2*this.a*this.k0),i=this.con*z(this.e,r),e=this.con*T(this.con*this.long0+Math.atan2(t.x,-1*t.y))}else s=2*Math.atan(o*this.cosX0/(2*this.a*this.k0*this.ms1)),e=this.long0,o<=1e-10?a=this.X0:(a=Math.asin(Math.cos(s)*this.sinX0+t.y*Math.sin(s)*this.cosX0/o),e=T(this.long0+Math.atan2(t.x*Math.sin(s),o*this.cosX0*Math.cos(s)-t.y*this.sinX0*Math.sin(s)))),i=-1*z(this.e,Math.tan(.5*(n+a)));return t.x=e,t.y=i,t},names:["stere","Stereographic_South_Pole","Polar Stereographic (variant B)"],ssfn_:function(t,e,i){return e*=i,Math.tan(.5*(n+t))*Math.pow((1-e)/(1+e),.5*i)}};var Wt={init:function(){var t=this.lat0;this.lambda0=this.long0;var e=Math.sin(t),i=this.a,r=1/this.rf,n=2*r-Math.pow(r,2),s=this.e=Math.sqrt(n);this.R=this.k0*i*Math.sqrt(1-n)/(1-n*Math.pow(e,2)),this.alpha=Math.sqrt(1+n/(1-n)*Math.pow(Math.cos(t),4)),this.b0=Math.asin(e/this.alpha);var a=Math.log(Math.tan(Math.PI/4+this.b0/2)),o=Math.log(Math.tan(Math.PI/4+t/2)),h=Math.log((1+s*e)/(1-s*e));this.K=a-this.alpha*o+this.alpha*s/2*h},forward:function(t){var e=Math.log(Math.tan(Math.PI/4-t.y/2)),i=this.e/2*Math.log((1+this.e*Math.sin(t.y))/(1-this.e*Math.sin(t.y))),r=-this.alpha*(e+i)+this.K,n=2*(Math.atan(Math.exp(r))-Math.PI/4),s=this.alpha*(t.x-this.lambda0),a=Math.atan(Math.sin(s)/(Math.sin(this.b0)*Math.tan(n)+Math.cos(this.b0)*Math.cos(s))),o=Math.asin(Math.cos(this.b0)*Math.sin(n)-Math.sin(this.b0)*Math.cos(n)*Math.cos(s));return t.y=this.R/2*Math.log((1+Math.sin(o))/(1-Math.sin(o)))+this.y0,t.x=this.R*a+this.x0,t},inverse:function(t){for(var e=t.x-this.x0,i=t.y-this.y0,r=e/this.R,n=2*(Math.atan(Math.exp(i/this.R))-Math.PI/4),s=Math.asin(Math.cos(this.b0)*Math.sin(n)+Math.sin(this.b0)*Math.cos(n)*Math.cos(r)),a=Math.atan(Math.sin(r)/(Math.cos(this.b0)*Math.cos(r)-Math.sin(this.b0)*Math.tan(n))),o=this.lambda0+a/this.alpha,h=0,l=s,u=-1e3,f=0;Math.abs(l-u)>1e-7;){if(++f>20)return;h=1/this.alpha*(Math.log(Math.tan(Math.PI/4+s/2))-this.K)+this.e*Math.log(Math.tan(Math.PI/4+Math.asin(this.e*Math.sin(l))/2)),u=l,l=2*Math.atan(Math.exp(h))-Math.PI/2}return t.x=o,t.y=l,t},names:["somerc"]};var Xt={init:function(){var t,e,i,r,a,l,u,f,c,d,p,g,m,_=0,y=0,v=0,b=0,M=0,w=0,x=0;this.no_off=(m="object"==typeof(g=this).PROJECTION?Object.keys(g.PROJECTION)[0]:g.PROJECTION,"no_uoff"in g||"no_off"in g||-1!==["Hotine_Oblique_Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin"].indexOf(m)),this.no_rot="no_rot"in this;var C=!1;"alpha"in this&&(C=!0);var S=!1;if("rectified_grid_angle"in this&&(S=!0),C&&(x=this.alpha),S&&(_=this.rectified_grid_angle*s),C||S)y=this.longc;else if(v=this.long1,M=this.lat1,b=this.long2,w=this.lat2,Math.abs(M-w)<=1e-7||(t=Math.abs(M))<=1e-7||Math.abs(t-n)<=1e-7||Math.abs(Math.abs(this.lat0)-n)<=1e-7||Math.abs(Math.abs(w)-n)<=1e-7)throw new Error;var k=1-this.es;e=Math.sqrt(k),Math.abs(this.lat0)>1e-10?(f=Math.sin(this.lat0),i=Math.cos(this.lat0),t=1-this.es*f*f,this.B=i*i,this.B=Math.sqrt(1+this.es*this.B*this.B/k),this.A=this.B*this.k0*e/t,(a=(r=this.B*e/(i*Math.sqrt(t)))*r-1)<=0?a=0:(a=Math.sqrt(a),this.lat0<0&&(a=-a)),this.E=a+=r,this.E*=Math.pow(R(this.e,this.lat0,f),this.B)):(this.B=1/e,this.A=this.k0,this.E=r=a=1),C||S?(C?(p=Math.asin(Math.sin(x)/r),S||(_=x)):(p=_,x=Math.asin(r*Math.sin(p))),this.lam0=y-Math.asin(.5*(a-1/a)*Math.tan(p))/this.B):(l=Math.pow(R(this.e,M,Math.sin(M)),this.B),u=Math.pow(R(this.e,w,Math.sin(w)),this.B),a=this.E/l,c=(u-l)/(u+l),d=((d=this.E*this.E)-u*l)/(d+u*l),(t=v-b)<-Math.pi?b-=h:t>Math.pi&&(b+=h),this.lam0=T(.5*(v+b)-Math.atan(d*Math.tan(.5*this.B*(v-b))/c)/this.B),p=Math.atan(2*Math.sin(this.B*T(v-this.lam0))/(a-1/a)),_=x=Math.asin(r*Math.sin(p))),this.singam=Math.sin(p),this.cosgam=Math.cos(p),this.sinrot=Math.sin(_),this.cosrot=Math.cos(_),this.rB=1/this.B,this.ArB=this.A*this.rB,this.BrA=1/this.ArB,this.A,this.B,this.no_off?this.u_0=0:(this.u_0=Math.abs(this.ArB*Math.atan(Math.sqrt(r*r-1)/Math.cos(x))),this.lat0<0&&(this.u_0=-this.u_0)),a=.5*p,this.v_pole_n=this.ArB*Math.log(Math.tan(o-a)),this.v_pole_s=this.ArB*Math.log(Math.tan(o+a))},forward:function(t){var e,i,r,s,a,o,h,l,u={};if(t.x=t.x-this.lam0,Math.abs(Math.abs(t.y)-n)>1e-10){if(e=.5*((a=this.E/Math.pow(R(this.e,t.y,Math.sin(t.y)),this.B))-(o=1/a)),i=.5*(a+o),s=Math.sin(this.B*t.x),r=(e*this.singam-s*this.cosgam)/i,Math.abs(Math.abs(r)-1)<1e-10)throw new Error;l=.5*this.ArB*Math.log((1-r)/(1+r)),o=Math.cos(this.B*t.x),h=Math.abs(o)<1e-7?this.A*t.x:this.ArB*Math.atan2(e*this.cosgam+s*this.singam,o)}else l=t.y>0?this.v_pole_n:this.v_pole_s,h=this.ArB*t.y;return this.no_rot?(u.x=h,u.y=l):(h-=this.u_0,u.x=l*this.cosrot+h*this.sinrot,u.y=h*this.cosrot-l*this.sinrot),u.x=this.a*u.x+this.x0,u.y=this.a*u.y+this.y0,u},inverse:function(t){var e,i,r,s,a,o,h,l={};if(t.x=(t.x-this.x0)*(1/this.a),t.y=(t.y-this.y0)*(1/this.a),this.no_rot?(i=t.y,e=t.x):(i=t.x*this.cosrot-t.y*this.sinrot,e=t.y*this.cosrot+t.x*this.sinrot+this.u_0),s=.5*((r=Math.exp(-this.BrA*i))-1/r),a=.5*(r+1/r),h=((o=Math.sin(this.BrA*e))*this.cosgam+s*this.singam)/a,Math.abs(Math.abs(h)-1)<1e-10)l.x=0,l.y=h<0?-n:n;else{if(l.y=this.E/Math.sqrt((1+h)/(1-h)),l.y=z(this.e,Math.pow(l.y,1/this.B)),l.y===1/0)throw new Error;l.x=-this.rB*Math.atan2(s*this.cosgam-o*this.singam,Math.cos(this.BrA*e))}return l.x+=this.lam0,l},names:["Hotine_Oblique_Mercator","Hotine Oblique Mercator","Hotine_Oblique_Mercator_Azimuth_Natural_Origin","Hotine_Oblique_Mercator_Two_Point_Natural_Origin","Hotine_Oblique_Mercator_Azimuth_Center","Oblique_Mercator","omerc"]};var Ht={init:function(){if(this.lat2||(this.lat2=this.lat1),this.k0||(this.k0=1),this.x0=this.x0||0,this.y0=this.y0||0,!(Math.abs(this.lat1+this.lat2)<1e-10)){var t=this.b/this.a;this.e=Math.sqrt(1-t*t);var e=Math.sin(this.lat1),i=Math.cos(this.lat1),r=N(this.e,e,i),n=R(this.e,this.lat1,e),s=Math.sin(this.lat2),a=Math.cos(this.lat2),o=N(this.e,s,a),h=R(this.e,this.lat2,s),l=R(this.e,this.lat0,Math.sin(this.lat0));Math.abs(this.lat1-this.lat2)>1e-10?this.ns=Math.log(r/o)/Math.log(n/h):this.ns=e,isNaN(this.ns)&&(this.ns=e),this.f0=r/(this.ns*Math.pow(n,this.ns)),this.rh=this.a*this.f0*Math.pow(l,this.ns),this.title||(this.title="Lambert Conformal Conic")}},forward:function(t){var e=t.x,i=t.y;Math.abs(2*Math.abs(i)-Math.PI)<=1e-10&&(i=L(i)*(n-2e-10));var r,s,a=Math.abs(Math.abs(i)-n);if(a>1e-10)r=R(this.e,i,Math.sin(i)),s=this.a*this.f0*Math.pow(r,this.ns);else{if((a=i*this.ns)<=0)return null;s=0}var o=this.ns*T(e-this.long0);return t.x=this.k0*(s*Math.sin(o))+this.x0,t.y=this.k0*(this.rh-s*Math.cos(o))+this.y0,t},inverse:function(t){var e,i,r,s,a,o=(t.x-this.x0)/this.k0,h=this.rh-(t.y-this.y0)/this.k0;this.ns>0?(e=Math.sqrt(o*o+h*h),i=1):(e=-Math.sqrt(o*o+h*h),i=-1);var l=0;if(0!==e&&(l=Math.atan2(i*o,i*h)),0!==e||this.ns>0){if(i=1/this.ns,r=Math.pow(e/(this.a*this.f0),i),-9999===(s=z(this.e,r)))return null}else s=-n;return a=T(l/this.ns+this.long0),t.x=a,t.y=s,t},names:["Lambert Tangential Conformal Conic Projection","Lambert_Conformal_Conic","Lambert_Conformal_Conic_1SP","Lambert_Conformal_Conic_2SP","lcc","Lambert Conic Conformal (1SP)","Lambert Conic Conformal (2SP)"]};var Jt={init:function(){this.a=6377397.155,this.es=.006674372230614,this.e=Math.sqrt(this.es),this.lat0||(this.lat0=.863937979737193),this.long0||(this.long0=.4334234309119251),this.k0||(this.k0=.9999),this.s45=.785398163397448,this.s90=2*this.s45,this.fi0=this.lat0,this.e2=this.es,this.e=Math.sqrt(this.e2),this.alfa=Math.sqrt(1+this.e2*Math.pow(Math.cos(this.fi0),4)/(1-this.e2)),this.uq=1.04216856380474,this.u0=Math.asin(Math.sin(this.fi0)/this.alfa),this.g=Math.pow((1+this.e*Math.sin(this.fi0))/(1-this.e*Math.sin(this.fi0)),this.alfa*this.e/2),this.k=Math.tan(this.u0/2+this.s45)/Math.pow(Math.tan(this.fi0/2+this.s45),this.alfa)*this.g,this.k1=this.k0,this.n0=this.a*Math.sqrt(1-this.e2)/(1-this.e2*Math.pow(Math.sin(this.fi0),2)),this.s0=1.37008346281555,this.n=Math.sin(this.s0),this.ro0=this.k1*this.n0/Math.tan(this.s0),this.ad=this.s90-this.uq},forward:function(t){var e,i,r,n,s,a,o,h=t.x,l=t.y,u=T(h-this.long0);return e=Math.pow((1+this.e*Math.sin(l))/(1-this.e*Math.sin(l)),this.alfa*this.e/2),i=2*(Math.atan(this.k*Math.pow(Math.tan(l/2+this.s45),this.alfa)/e)-this.s45),r=-u*this.alfa,n=Math.asin(Math.cos(this.ad)*Math.sin(i)+Math.sin(this.ad)*Math.cos(i)*Math.cos(r)),s=Math.asin(Math.cos(i)*Math.sin(r)/Math.cos(n)),a=this.n*s,o=this.ro0*Math.pow(Math.tan(this.s0/2+this.s45),this.n)/Math.pow(Math.tan(n/2+this.s45),this.n),t.y=o*Math.cos(a)/1,t.x=o*Math.sin(a)/1,this.czech||(t.y*=-1,t.x*=-1),t},inverse:function(t){var e,i,r,n,s,a,o,h=t.x;t.x=t.y,t.y=h,this.czech||(t.y*=-1,t.x*=-1),s=Math.sqrt(t.x*t.x+t.y*t.y),n=Math.atan2(t.y,t.x)/Math.sin(this.s0),r=2*(Math.atan(Math.pow(this.ro0/s,1/this.n)*Math.tan(this.s0/2+this.s45))-this.s45),e=Math.asin(Math.cos(this.ad)*Math.sin(r)-Math.sin(this.ad)*Math.cos(r)*Math.cos(n)),i=Math.asin(Math.cos(r)*Math.sin(n)/Math.cos(e)),t.x=this.long0-i/this.alfa,a=e,o=0;var l=0;do{t.y=2*(Math.atan(Math.pow(this.k,-1/this.alfa)*Math.pow(Math.tan(e/2+this.s45),1/this.alfa)*Math.pow((1+this.e*Math.sin(a))/(1-this.e*Math.sin(a)),this.e/2))-this.s45),Math.abs(a-t.y)<1e-10&&(o=1),a=t.y,l+=1}while(0===o&&l<15);return l>=15?null:t},names:["Krovak","krovak"]},Kt=function(t,e,i,r,n){return t*n-e*Math.sin(2*n)+i*Math.sin(4*n)-r*Math.sin(6*n)},Qt=function(t){return 1-.25*t*(1+t/16*(3+1.25*t))},$t=function(t){return.375*t*(1+.25*t*(1+.46875*t))},te=function(t){return.05859375*t*t*(1+.75*t)},ee=function(t){return t*t*t*(35/3072)},ie=function(t,e,i){var r=e*i;return t/Math.sqrt(1-r*r)},re=function(t){return Math.abs(t)<n?t:t-L(t)*Math.PI},ne=function(t,e,i,r,n){var s,a;s=t/e;for(var o=0;o<15;o++)if(s+=a=(t-(e*s-i*Math.sin(2*s)+r*Math.sin(4*s)-n*Math.sin(6*s)))/(e-2*i*Math.cos(2*s)+4*r*Math.cos(4*s)-6*n*Math.cos(6*s)),Math.abs(a)<=1e-10)return s;return NaN};var se={init:function(){this.sphere||(this.e0=Qt(this.es),this.e1=$t(this.es),this.e2=te(this.es),this.e3=ee(this.es),this.ml0=this.a*Kt(this.e0,this.e1,this.e2,this.e3,this.lat0))},forward:function(t){var e,i,r=t.x,n=t.y;if(r=T(r-this.long0),this.sphere)e=this.a*Math.asin(Math.cos(n)*Math.sin(r)),i=this.a*(Math.atan2(Math.tan(n),Math.cos(r))-this.lat0);else{var s=Math.sin(n),a=Math.cos(n),o=ie(this.a,this.e,s),h=Math.tan(n)*Math.tan(n),l=r*Math.cos(n),u=l*l,f=this.es*a*a/(1-this.es);e=o*l*(1-u*h*(1/6-(8-h+8*f)*u/120)),i=this.a*Kt(this.e0,this.e1,this.e2,this.e3,n)-this.ml0+o*s/a*u*(.5+(5-h+6*f)*u/24)}return t.x=e+this.x0,t.y=i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var e,i,r=t.x/this.a,s=t.y/this.a;if(this.sphere){var a=s+this.lat0;e=Math.asin(Math.sin(a)*Math.cos(r)),i=Math.atan2(Math.tan(r),Math.cos(a))}else{var o=this.ml0/this.a+s,h=ne(o,this.e0,this.e1,this.e2,this.e3);if(Math.abs(Math.abs(h)-n)<=1e-10)return t.x=this.long0,t.y=n,s<0&&(t.y*=-1),t;var l=ie(this.a,this.e,Math.sin(h)),u=l*l*l/this.a/this.a*(1-this.es),f=Math.pow(Math.tan(h),2),c=r*this.a/l,d=c*c;e=h-l*Math.tan(h)/u*c*c*(.5-(1+3*f)*c*c/24),i=c*(1-d*(f/3+(1+3*f)*f*d/15))/Math.cos(h)}return t.x=T(i+this.long0),t.y=re(e),t},names:["Cassini","Cassini_Soldner","cass"]},ae=function(t,e){var i;return t>1e-7?(1-t*t)*(e/(1-(i=t*e)*i)-.5/t*Math.log((1-i)/(1+i))):2*e};var oe={init:function(){var t,e=Math.abs(this.lat0);if(Math.abs(e-n)<1e-10?this.mode=this.lat0<0?this.S_POLE:this.N_POLE:Math.abs(e)<1e-10?this.mode=this.EQUIT:this.mode=this.OBLIQ,this.es>0)switch(this.qp=ae(this.e,1),this.mmf=.5/(1-this.es),this.apa=function(t){var e,i=[];return i[0]=.3333333333333333*t,e=t*t,i[0]+=.17222222222222222*e,i[1]=.06388888888888888*e,e*=t,i[0]+=.10257936507936508*e,i[1]+=.0664021164021164*e,i[2]=.016415012942191543*e,i}(this.es),this.mode){case this.N_POLE:case this.S_POLE:this.dd=1;break;case this.EQUIT:this.rq=Math.sqrt(.5*this.qp),this.dd=1/this.rq,this.xmf=1,this.ymf=.5*this.qp;break;case this.OBLIQ:this.rq=Math.sqrt(.5*this.qp),t=Math.sin(this.lat0),this.sinb1=ae(this.e,t)/this.qp,this.cosb1=Math.sqrt(1-this.sinb1*this.sinb1),this.dd=Math.cos(this.lat0)/(Math.sqrt(1-this.es*t*t)*this.rq*this.cosb1),this.ymf=(this.xmf=this.rq)/this.dd,this.xmf*=this.dd}else this.mode===this.OBLIQ&&(this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0))},forward:function(t){var e,i,r,s,a,h,l,u,f,c,d=t.x,p=t.y;if(d=T(d-this.long0),this.sphere){if(a=Math.sin(p),c=Math.cos(p),r=Math.cos(d),this.mode===this.OBLIQ||this.mode===this.EQUIT){if((i=this.mode===this.EQUIT?1+c*r:1+this.sinph0*a+this.cosph0*c*r)<=1e-10)return null;e=(i=Math.sqrt(2/i))*c*Math.sin(d),i*=this.mode===this.EQUIT?a:this.cosph0*a-this.sinph0*c*r}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(r=-r),Math.abs(p+this.lat0)<1e-10)return null;i=o-.5*p,e=(i=2*(this.mode===this.S_POLE?Math.cos(i):Math.sin(i)))*Math.sin(d),i*=r}}else{switch(l=0,u=0,f=0,r=Math.cos(d),s=Math.sin(d),a=Math.sin(p),h=ae(this.e,a),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(l=h/this.qp,u=Math.sqrt(1-l*l)),this.mode){case this.OBLIQ:f=1+this.sinb1*l+this.cosb1*u*r;break;case this.EQUIT:f=1+u*r;break;case this.N_POLE:f=n+p,h=this.qp-h;break;case this.S_POLE:f=p-n,h=this.qp+h}if(Math.abs(f)<1e-10)return null;switch(this.mode){case this.OBLIQ:case this.EQUIT:f=Math.sqrt(2/f),i=this.mode===this.OBLIQ?this.ymf*f*(this.cosb1*l-this.sinb1*u*r):(f=Math.sqrt(2/(1+u*r)))*l*this.ymf,e=this.xmf*f*u*s;break;case this.N_POLE:case this.S_POLE:h>=0?(e=(f=Math.sqrt(h))*s,i=r*(this.mode===this.S_POLE?f:-f)):e=i=0}}return t.x=this.a*e+this.x0,t.y=this.a*i+this.y0,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var e,i,r,s,a,o,h,l,u,f,c=t.x/this.a,d=t.y/this.a;if(this.sphere){var p,g=0,m=0;if((i=.5*(p=Math.sqrt(c*c+d*d)))>1)return null;switch(i=2*Math.asin(i),this.mode!==this.OBLIQ&&this.mode!==this.EQUIT||(m=Math.sin(i),g=Math.cos(i)),this.mode){case this.EQUIT:i=Math.abs(p)<=1e-10?0:Math.asin(d*m/p),c*=m,d=g*p;break;case this.OBLIQ:i=Math.abs(p)<=1e-10?this.lat0:Math.asin(g*this.sinph0+d*m*this.cosph0/p),c*=m*this.cosph0,d=(g-Math.sin(i)*this.sinph0)*p;break;case this.N_POLE:d=-d,i=n-i;break;case this.S_POLE:i-=n}e=0!==d||this.mode!==this.EQUIT&&this.mode!==this.OBLIQ?Math.atan2(c,d):0}else{if(h=0,this.mode===this.OBLIQ||this.mode===this.EQUIT){if(c/=this.dd,d*=this.dd,(o=Math.sqrt(c*c+d*d))<1e-10)return t.x=this.long0,t.y=this.lat0,t;s=2*Math.asin(.5*o/this.rq),r=Math.cos(s),c*=s=Math.sin(s),this.mode===this.OBLIQ?(h=r*this.sinb1+d*s*this.cosb1/o,a=this.qp*h,d=o*this.cosb1*r-d*this.sinb1*s):(h=d*s/o,a=this.qp*h,d=o*r)}else if(this.mode===this.N_POLE||this.mode===this.S_POLE){if(this.mode===this.N_POLE&&(d=-d),!(a=c*c+d*d))return t.x=this.long0,t.y=this.lat0,t;h=1-a/this.qp,this.mode===this.S_POLE&&(h=-h)}e=Math.atan2(c,d),l=Math.asin(h),u=this.apa,f=l+l,i=l+u[0]*Math.sin(f)+u[1]*Math.sin(f+f)+u[2]*Math.sin(f+f+f)}return t.x=T(this.long0+e),t.y=i,t},names:["Lambert Azimuthal Equal Area","Lambert_Azimuthal_Equal_Area","laea"],S_POLE:1,N_POLE:2,EQUIT:3,OBLIQ:4},he=function(t){return Math.abs(t)>1&&(t=t>1?1:-1),Math.asin(t)};var le={init:function(){Math.abs(this.lat1+this.lat2)<1e-10||(this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e3=Math.sqrt(this.es),this.sin_po=Math.sin(this.lat1),this.cos_po=Math.cos(this.lat1),this.t1=this.sin_po,this.con=this.sin_po,this.ms1=N(this.e3,this.sin_po,this.cos_po),this.qs1=ae(this.e3,this.sin_po,this.cos_po),this.sin_po=Math.sin(this.lat2),this.cos_po=Math.cos(this.lat2),this.t2=this.sin_po,this.ms2=N(this.e3,this.sin_po,this.cos_po),this.qs2=ae(this.e3,this.sin_po,this.cos_po),this.sin_po=Math.sin(this.lat0),this.cos_po=Math.cos(this.lat0),this.t3=this.sin_po,this.qs0=ae(this.e3,this.sin_po,this.cos_po),Math.abs(this.lat1-this.lat2)>1e-10?this.ns0=(this.ms1*this.ms1-this.ms2*this.ms2)/(this.qs2-this.qs1):this.ns0=this.con,this.c=this.ms1*this.ms1+this.ns0*this.qs1,this.rh=this.a*Math.sqrt(this.c-this.ns0*this.qs0)/this.ns0)},forward:function(t){var e=t.x,i=t.y;this.sin_phi=Math.sin(i),this.cos_phi=Math.cos(i);var r=ae(this.e3,this.sin_phi,this.cos_phi),n=this.a*Math.sqrt(this.c-this.ns0*r)/this.ns0,s=this.ns0*T(e-this.long0),a=n*Math.sin(s)+this.x0,o=this.rh-n*Math.cos(s)+this.y0;return t.x=a,t.y=o,t},inverse:function(t){var e,i,r,n,s,a;return t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns0>=0?(e=Math.sqrt(t.x*t.x+t.y*t.y),r=1):(e=-Math.sqrt(t.x*t.x+t.y*t.y),r=-1),n=0,0!==e&&(n=Math.atan2(r*t.x,r*t.y)),r=e*this.ns0/this.a,this.sphere?a=Math.asin((this.c-r*r)/(2*this.ns0)):(i=(this.c-r*r)/this.ns0,a=this.phi1z(this.e3,i)),s=T(n/this.ns0+this.long0),t.x=s,t.y=a,t},names:["Albers_Conic_Equal_Area","Albers","aea"],phi1z:function(t,e){var i,r,n,s,a=he(.5*e);if(t<1e-10)return a;for(var o=t*t,h=1;h<=25;h++)if(a+=s=.5*(n=1-(r=t*(i=Math.sin(a)))*r)*n/Math.cos(a)*(e/(1-o)-i/n+.5/t*Math.log((1-r)/(1+r))),Math.abs(s)<=1e-7)return a;return null}};var ue={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0),this.infinity_dist=1e3*this.a,this.rc=1},forward:function(t){var e,i,r,n,s,a,o,h=t.x,l=t.y;return r=T(h-this.long0),e=Math.sin(l),i=Math.cos(l),n=Math.cos(r),1,(s=this.sin_p14*e+this.cos_p14*i*n)>0||Math.abs(s)<=1e-10?(a=this.x0+1*this.a*i*Math.sin(r)/s,o=this.y0+1*this.a*(this.cos_p14*e-this.sin_p14*i*n)/s):(a=this.x0+this.infinity_dist*i*Math.sin(r),o=this.y0+this.infinity_dist*(this.cos_p14*e-this.sin_p14*i*n)),t.x=a,t.y=o,t},inverse:function(t){var e,i,r,n,s,a;return t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,t.x/=this.k0,t.y/=this.k0,(e=Math.sqrt(t.x*t.x+t.y*t.y))?(n=Math.atan2(e,this.rc),i=Math.sin(n),r=Math.cos(n),a=he(r*this.sin_p14+t.y*i*this.cos_p14/e),s=Math.atan2(t.x*i,e*this.cos_p14*r-t.y*this.sin_p14*i),s=T(this.long0+s)):(a=this.phic0,s=0),t.x=s,t.y=a,t},names:["gnom"]};var fe={init:function(){this.sphere||(this.k0=N(this.e,Math.sin(this.lat_ts),Math.cos(this.lat_ts)))},forward:function(t){var e,i,r=t.x,n=t.y,s=T(r-this.long0);if(this.sphere)e=this.x0+this.a*s*Math.cos(this.lat_ts),i=this.y0+this.a*Math.sin(n)/Math.cos(this.lat_ts);else{var a=ae(this.e,Math.sin(n));e=this.x0+this.a*this.k0*s,i=this.y0+this.a*a*.5/this.k0}return t.x=e,t.y=i,t},inverse:function(t){var e,i;return t.x-=this.x0,t.y-=this.y0,this.sphere?(e=T(this.long0+t.x/this.a/Math.cos(this.lat_ts)),i=Math.asin(t.y/this.a*Math.cos(this.lat_ts))):(i=function(t,e){var i=1-(1-t*t)/(2*t)*Math.log((1-t)/(1+t));if(Math.abs(Math.abs(e)-i)<1e-6)return e<0?-1*n:n;for(var r,s,a,o,h=Math.asin(.5*e),l=0;l<30;l++)if(s=Math.sin(h),a=Math.cos(h),o=t*s,h+=r=Math.pow(1-o*o,2)/(2*a)*(e/(1-t*t)-s/(1-o*o)+.5/t*Math.log((1-o)/(1+o))),Math.abs(r)<=1e-10)return h;return NaN}(this.e,2*t.y*this.k0/this.a),e=T(this.long0+t.x/(this.a*this.k0))),t.x=e,t.y=i,t},names:["cea"]};var ce={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Equidistant Cylindrical (Plate Carre)",this.rc=Math.cos(this.lat_ts)},forward:function(t){var e=t.x,i=t.y,r=T(e-this.long0),n=re(i-this.lat0);return t.x=this.x0+this.a*r*this.rc,t.y=this.y0+this.a*n,t},inverse:function(t){var e=t.x,i=t.y;return t.x=T(this.long0+(e-this.x0)/(this.a*this.rc)),t.y=re(this.lat0+(i-this.y0)/this.a),t},names:["Equirectangular","Equidistant_Cylindrical","eqc"]};var de={init:function(){this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Qt(this.es),this.e1=$t(this.es),this.e2=te(this.es),this.e3=ee(this.es),this.ml0=this.a*Kt(this.e0,this.e1,this.e2,this.e3,this.lat0)},forward:function(t){var e,i,r,n=t.x,s=t.y,a=T(n-this.long0);if(r=a*Math.sin(s),this.sphere)Math.abs(s)<=1e-10?(e=this.a*a,i=-1*this.a*this.lat0):(e=this.a*Math.sin(r)/Math.tan(s),i=this.a*(re(s-this.lat0)+(1-Math.cos(r))/Math.tan(s)));else if(Math.abs(s)<=1e-10)e=this.a*a,i=-1*this.ml0;else{var o=ie(this.a,this.e,Math.sin(s))/Math.tan(s);e=o*Math.sin(r),i=this.a*Kt(this.e0,this.e1,this.e2,this.e3,s)-this.ml0+o*(1-Math.cos(r))}return t.x=e+this.x0,t.y=i+this.y0,t},inverse:function(t){var e,i,r,n,s,a,o,h,l;if(r=t.x-this.x0,n=t.y-this.y0,this.sphere)if(Math.abs(n+this.a*this.lat0)<=1e-10)e=T(r/this.a+this.long0),i=0;else{var u;for(a=this.lat0+n/this.a,o=r*r/this.a/this.a+a*a,h=a,s=20;s;--s)if(h+=l=-1*(a*(h*(u=Math.tan(h))+1)-h-.5*(h*h+o)*u)/((h-a)/u-1),Math.abs(l)<=1e-10){i=h;break}e=T(this.long0+Math.asin(r*Math.tan(h)/this.a)/Math.sin(i))}else if(Math.abs(n+this.ml0)<=1e-10)i=0,e=T(this.long0+r/this.a);else{var f,c,d,p,g;for(a=(this.ml0+n)/this.a,o=r*r/this.a/this.a+a*a,h=a,s=20;s;--s)if(g=this.e*Math.sin(h),f=Math.sqrt(1-g*g)*Math.tan(h),c=this.a*Kt(this.e0,this.e1,this.e2,this.e3,h),d=this.e0-2*this.e1*Math.cos(2*h)+4*this.e2*Math.cos(4*h)-6*this.e3*Math.cos(6*h),h-=l=(a*(f*(p=c/this.a)+1)-p-.5*f*(p*p+o))/(this.es*Math.sin(2*h)*(p*p+o-2*a*p)/(4*f)+(a-p)*(f*d-2/Math.sin(2*h))-d),Math.abs(l)<=1e-10){i=h;break}f=Math.sqrt(1-this.es*Math.pow(Math.sin(i),2))*Math.tan(i),e=T(this.long0+Math.asin(r*f/this.a)/Math.sin(i))}return t.x=e,t.y=i,t},names:["Polyconic","poly"]};var pe={init:function(){this.A=[],this.A[1]=.6399175073,this.A[2]=-.1358797613,this.A[3]=.063294409,this.A[4]=-.02526853,this.A[5]=.0117879,this.A[6]=-.0055161,this.A[7]=.0026906,this.A[8]=-.001333,this.A[9]=67e-5,this.A[10]=-34e-5,this.B_re=[],this.B_im=[],this.B_re[1]=.7557853228,this.B_im[1]=0,this.B_re[2]=.249204646,this.B_im[2]=.003371507,this.B_re[3]=-.001541739,this.B_im[3]=.04105856,this.B_re[4]=-.10162907,this.B_im[4]=.01727609,this.B_re[5]=-.26623489,this.B_im[5]=-.36249218,this.B_re[6]=-.6870983,this.B_im[6]=-1.1651967,this.C_re=[],this.C_im=[],this.C_re[1]=1.3231270439,this.C_im[1]=0,this.C_re[2]=-.577245789,this.C_im[2]=-.007809598,this.C_re[3]=.508307513,this.C_im[3]=-.112208952,this.C_re[4]=-.15094762,this.C_im[4]=.18200602,this.C_re[5]=1.01418179,this.C_im[5]=1.64497696,this.C_re[6]=1.9660549,this.C_im[6]=2.5127645,this.D=[],this.D[1]=1.5627014243,this.D[2]=.5185406398,this.D[3]=-.03333098,this.D[4]=-.1052906,this.D[5]=-.0368594,this.D[6]=.007317,this.D[7]=.0122,this.D[8]=.00394,this.D[9]=-.0013},forward:function(t){var e,i=t.x,n=t.y-this.lat0,s=i-this.long0,a=n/r*1e-5,o=s,h=1,l=0;for(e=1;e<=10;e++)h*=a,l+=this.A[e]*h;var u,f=l,c=o,d=1,p=0,g=0,m=0;for(e=1;e<=6;e++)u=p*f+d*c,d=d*f-p*c,p=u,g=g+this.B_re[e]*d-this.B_im[e]*p,m=m+this.B_im[e]*d+this.B_re[e]*p;return t.x=m*this.a+this.x0,t.y=g*this.a+this.y0,t},inverse:function(t){var e,i,n=t.x,s=t.y,a=n-this.x0,o=(s-this.y0)/this.a,h=a/this.a,l=1,u=0,f=0,c=0;for(e=1;e<=6;e++)i=u*o+l*h,l=l*o-u*h,u=i,f=f+this.C_re[e]*l-this.C_im[e]*u,c=c+this.C_im[e]*l+this.C_re[e]*u;for(var d=0;d<this.iterations;d++){var p,g=f,m=c,_=o,y=h;for(e=2;e<=6;e++)p=m*f+g*c,g=g*f-m*c,m=p,_+=(e-1)*(this.B_re[e]*g-this.B_im[e]*m),y+=(e-1)*(this.B_im[e]*g+this.B_re[e]*m);g=1,m=0;var v=this.B_re[1],b=this.B_im[1];for(e=2;e<=6;e++)p=m*f+g*c,g=g*f-m*c,m=p,v+=e*(this.B_re[e]*g-this.B_im[e]*m),b+=e*(this.B_im[e]*g+this.B_re[e]*m);var M=v*v+b*b;f=(_*v+y*b)/M,c=(y*v-_*b)/M}var w=f,x=c,C=1,S=0;for(e=1;e<=9;e++)C*=w,S+=this.D[e]*C;var k=this.lat0+S*r*1e5,E=this.long0+x;return t.x=E,t.y=k,t},names:["New_Zealand_Map_Grid","nzmg"]};var ge={init:function(){},forward:function(t){var e=t.x,i=t.y,r=T(e-this.long0),n=this.x0+this.a*r,s=this.y0+this.a*Math.log(Math.tan(Math.PI/4+i/2.5))*1.25;return t.x=n,t.y=s,t},inverse:function(t){t.x-=this.x0,t.y-=this.y0;var e=T(this.long0+t.x/this.a),i=2.5*(Math.atan(Math.exp(.8*t.y/this.a))-Math.PI/4);return t.x=e,t.y=i,t},names:["Miller_Cylindrical","mill"]};var me={init:function(){this.sphere?(this.n=1,this.m=0,this.es=0,this.C_y=Math.sqrt((this.m+1)/this.n),this.C_x=this.C_y/(this.m+1)):this.en=Nt(this.es)},forward:function(t){var e,i,r=t.x,n=t.y;if(r=T(r-this.long0),this.sphere){if(this.m)for(var s=this.n*Math.sin(n),a=20;a;--a){var o=(this.m*n+Math.sin(n)-s)/(this.m+Math.cos(n));if(n-=o,Math.abs(o)<1e-10)break}else n=1!==this.n?Math.asin(this.n*Math.sin(n)):n;e=this.a*this.C_x*r*(this.m+Math.cos(n)),i=this.a*this.C_y*n}else{var h=Math.sin(n),l=Math.cos(n);i=this.a*Lt(n,h,l,this.en),e=this.a*r*l/Math.sqrt(1-this.es*h*h)}return t.x=e,t.y=i,t},inverse:function(t){var e,i,r,s;return t.x-=this.x0,r=t.x/this.a,t.y-=this.y0,e=t.y/this.a,this.sphere?(e/=this.C_y,r/=this.C_x*(this.m+Math.cos(e)),this.m?e=he((this.m*e+Math.sin(e))/this.n):1!==this.n&&(e=he(Math.sin(e)/this.n)),r=T(r+this.long0),e=re(e)):(e=Tt(t.y/this.a,this.es,this.en),(s=Math.abs(e))<n?(s=Math.sin(e),i=this.long0+t.x*Math.sqrt(1-this.es*s*s)/(this.a*Math.cos(e)),r=T(i)):s-1e-10<n&&(r=this.long0)),t.x=r,t.y=e,t},names:["Sinusoidal","sinu"]};var _e={init:function(){},forward:function(t){for(var e=t.x,i=t.y,r=T(e-this.long0),n=i,s=Math.PI*Math.sin(i);;){var a=-(n+Math.sin(n)-s)/(1+Math.cos(n));if(n+=a,Math.abs(a)<1e-10)break}n/=2,Math.PI/2-Math.abs(i)<1e-10&&(r=0);var o=.900316316158*this.a*r*Math.cos(n)+this.x0,h=1.4142135623731*this.a*Math.sin(n)+this.y0;return t.x=o,t.y=h,t},inverse:function(t){var e,i;t.x-=this.x0,t.y-=this.y0,i=t.y/(1.4142135623731*this.a),Math.abs(i)>.999999999999&&(i=.999999999999),e=Math.asin(i);var r=T(this.long0+t.x/(.900316316158*this.a*Math.cos(e)));r<-Math.PI&&(r=-Math.PI),r>Math.PI&&(r=Math.PI),i=(2*e+Math.sin(2*e))/Math.PI,Math.abs(i)>1&&(i=1);var n=Math.asin(i);return t.x=r,t.y=n,t},names:["Mollweide","moll"]};var ye={init:function(){Math.abs(this.lat1+this.lat2)<1e-10||(this.lat2=this.lat2||this.lat1,this.temp=this.b/this.a,this.es=1-Math.pow(this.temp,2),this.e=Math.sqrt(this.es),this.e0=Qt(this.es),this.e1=$t(this.es),this.e2=te(this.es),this.e3=ee(this.es),this.sinphi=Math.sin(this.lat1),this.cosphi=Math.cos(this.lat1),this.ms1=N(this.e,this.sinphi,this.cosphi),this.ml1=Kt(this.e0,this.e1,this.e2,this.e3,this.lat1),Math.abs(this.lat1-this.lat2)<1e-10?this.ns=this.sinphi:(this.sinphi=Math.sin(this.lat2),this.cosphi=Math.cos(this.lat2),this.ms2=N(this.e,this.sinphi,this.cosphi),this.ml2=Kt(this.e0,this.e1,this.e2,this.e3,this.lat2),this.ns=(this.ms1-this.ms2)/(this.ml2-this.ml1)),this.g=this.ml1+this.ms1/this.ns,this.ml0=Kt(this.e0,this.e1,this.e2,this.e3,this.lat0),this.rh=this.a*(this.g-this.ml0))},forward:function(t){var e,i=t.x,r=t.y;if(this.sphere)e=this.a*(this.g-r);else{var n=Kt(this.e0,this.e1,this.e2,this.e3,r);e=this.a*(this.g-n)}var s=this.ns*T(i-this.long0),a=this.x0+e*Math.sin(s),o=this.y0+this.rh-e*Math.cos(s);return t.x=a,t.y=o,t},inverse:function(t){var e,i,r,n;t.x-=this.x0,t.y=this.rh-t.y+this.y0,this.ns>=0?(i=Math.sqrt(t.x*t.x+t.y*t.y),e=1):(i=-Math.sqrt(t.x*t.x+t.y*t.y),e=-1);var s=0;if(0!==i&&(s=Math.atan2(e*t.x,e*t.y)),this.sphere)return n=T(this.long0+s/this.ns),r=re(this.g-i/this.a),t.x=n,t.y=r,t;var a=this.g-i/this.a;return r=ne(a,this.e0,this.e1,this.e2,this.e3),n=T(this.long0+s/this.ns),t.x=n,t.y=r,t},names:["Equidistant_Conic","eqdc"]};var ve={init:function(){this.R=this.a},forward:function(t){var e,i,r=t.x,s=t.y,a=T(r-this.long0);Math.abs(s)<=1e-10&&(e=this.x0+this.R*a,i=this.y0);var o=he(2*Math.abs(s/Math.PI));(Math.abs(a)<=1e-10||Math.abs(Math.abs(s)-n)<=1e-10)&&(e=this.x0,i=s>=0?this.y0+Math.PI*this.R*Math.tan(.5*o):this.y0+Math.PI*this.R*-Math.tan(.5*o));var h=.5*Math.abs(Math.PI/a-a/Math.PI),l=h*h,u=Math.sin(o),f=Math.cos(o),c=f/(u+f-1),d=c*c,p=c*(2/u-1),g=p*p,m=Math.PI*this.R*(h*(c-g)+Math.sqrt(l*(c-g)*(c-g)-(g+l)*(d-g)))/(g+l);a<0&&(m=-m),e=this.x0+m;var _=l+c;return m=Math.PI*this.R*(p*_-h*Math.sqrt((g+l)*(l+1)-_*_))/(g+l),i=s>=0?this.y0+m:this.y0-m,t.x=e,t.y=i,t},inverse:function(t){var e,i,r,n,s,a,o,h,l,u,f,c;return t.x-=this.x0,t.y-=this.y0,f=Math.PI*this.R,s=(r=t.x/f)*r+(n=t.y/f)*n,f=3*(n*n/(h=-2*(a=-Math.abs(n)*(1+s))+1+2*n*n+s*s)+(2*(o=a-2*n*n+r*r)*o*o/h/h/h-9*a*o/h/h)/27)/(l=(a-o*o/3/h)/h)/(u=2*Math.sqrt(-l/3)),Math.abs(f)>1&&(f=f>=0?1:-1),c=Math.acos(f)/3,i=t.y>=0?(-u*Math.cos(c+Math.PI/3)-o/3/h)*Math.PI:-(-u*Math.cos(c+Math.PI/3)-o/3/h)*Math.PI,e=Math.abs(r)<1e-10?this.long0:T(this.long0+Math.PI*(s-1+Math.sqrt(1+2*(r*r-n*n)+s*s))/2/r),t.x=e,t.y=i,t},names:["Van_der_Grinten_I","VanDerGrinten","vandg"]};var be={init:function(){this.sin_p12=Math.sin(this.lat0),this.cos_p12=Math.cos(this.lat0)},forward:function(t){var e,i,r,s,a,o,h,l,u,f,c,d,p,g,m,_,y,v,b,M,w,x,C=t.x,S=t.y,k=Math.sin(t.y),E=Math.cos(t.y),A=T(C-this.long0);return this.sphere?Math.abs(this.sin_p12-1)<=1e-10?(t.x=this.x0+this.a*(n-S)*Math.sin(A),t.y=this.y0-this.a*(n-S)*Math.cos(A),t):Math.abs(this.sin_p12+1)<=1e-10?(t.x=this.x0+this.a*(n+S)*Math.sin(A),t.y=this.y0+this.a*(n+S)*Math.cos(A),t):(v=this.sin_p12*k+this.cos_p12*E*Math.cos(A),y=(_=Math.acos(v))?_/Math.sin(_):1,t.x=this.x0+this.a*y*E*Math.sin(A),t.y=this.y0+this.a*y*(this.cos_p12*k-this.sin_p12*E*Math.cos(A)),t):(e=Qt(this.es),i=$t(this.es),r=te(this.es),s=ee(this.es),Math.abs(this.sin_p12-1)<=1e-10?(a=this.a*Kt(e,i,r,s,n),o=this.a*Kt(e,i,r,s,S),t.x=this.x0+(a-o)*Math.sin(A),t.y=this.y0-(a-o)*Math.cos(A),t):Math.abs(this.sin_p12+1)<=1e-10?(a=this.a*Kt(e,i,r,s,n),o=this.a*Kt(e,i,r,s,S),t.x=this.x0+(a+o)*Math.sin(A),t.y=this.y0+(a+o)*Math.cos(A),t):(h=k/E,l=ie(this.a,this.e,this.sin_p12),u=ie(this.a,this.e,k),f=Math.atan((1-this.es)*h+this.es*l*this.sin_p12/(u*E)),b=0===(c=Math.atan2(Math.sin(A),this.cos_p12*Math.tan(f)-this.sin_p12*Math.cos(A)))?Math.asin(this.cos_p12*Math.sin(f)-this.sin_p12*Math.cos(f)):Math.abs(Math.abs(c)-Math.PI)<=1e-10?-Math.asin(this.cos_p12*Math.sin(f)-this.sin_p12*Math.cos(f)):Math.asin(Math.sin(A)*Math.cos(f)/Math.sin(c)),d=this.e*this.sin_p12/Math.sqrt(1-this.es),_=l*b*(1-(M=b*b)*(m=(p=this.e*this.cos_p12*Math.cos(c)/Math.sqrt(1-this.es))*p)*(1-m)/6+(w=M*b)/8*(g=d*p)*(1-2*m)+(x=w*b)/120*(m*(4-7*m)-3*d*d*(1-7*m))-x*b/48*g),t.x=this.x0+_*Math.sin(c),t.y=this.y0+_*Math.cos(c),t))},inverse:function(t){var e,i,r,s,a,o,h,l,u,f,c,d,p,g,m,_,y,v,b,M,w,x,C;if(t.x-=this.x0,t.y-=this.y0,this.sphere){if((e=Math.sqrt(t.x*t.x+t.y*t.y))>2*n*this.a)return;return i=e/this.a,r=Math.sin(i),s=Math.cos(i),a=this.long0,Math.abs(e)<=1e-10?o=this.lat0:(o=he(s*this.sin_p12+t.y*r*this.cos_p12/e),h=Math.abs(this.lat0)-n,a=Math.abs(h)<=1e-10?this.lat0>=0?T(this.long0+Math.atan2(t.x,-t.y)):T(this.long0-Math.atan2(-t.x,t.y)):T(this.long0+Math.atan2(t.x*r,e*this.cos_p12*s-t.y*this.sin_p12*r))),t.x=a,t.y=o,t}return l=Qt(this.es),u=$t(this.es),f=te(this.es),c=ee(this.es),Math.abs(this.sin_p12-1)<=1e-10?(d=this.a*Kt(l,u,f,c,n),e=Math.sqrt(t.x*t.x+t.y*t.y),o=ne((d-e)/this.a,l,u,f,c),a=T(this.long0+Math.atan2(t.x,-1*t.y)),t.x=a,t.y=o,t):Math.abs(this.sin_p12+1)<=1e-10?(d=this.a*Kt(l,u,f,c,n),e=Math.sqrt(t.x*t.x+t.y*t.y),o=ne((e-d)/this.a,l,u,f,c),a=T(this.long0+Math.atan2(t.x,t.y)),t.x=a,t.y=o,t):(e=Math.sqrt(t.x*t.x+t.y*t.y),m=Math.atan2(t.x,t.y),p=ie(this.a,this.e,this.sin_p12),_=Math.cos(m),v=-(y=this.e*this.cos_p12*_)*y/(1-this.es),b=3*this.es*(1-v)*this.sin_p12*this.cos_p12*_/(1-this.es),x=1-v*(w=(M=e/p)-v*(1+v)*Math.pow(M,3)/6-b*(1+3*v)*Math.pow(M,4)/24)*w/2-M*w*w*w/6,g=Math.asin(this.sin_p12*Math.cos(w)+this.cos_p12*Math.sin(w)*_),a=T(this.long0+Math.asin(Math.sin(m)*Math.sin(w)/Math.cos(g))),C=Math.sin(g),o=Math.atan2((C-this.es*x*this.sin_p12)*Math.tan(g),C*(1-this.es)),t.x=a,t.y=o,t)},names:["Azimuthal_Equidistant","aeqd"]};var Me={init:function(){this.sin_p14=Math.sin(this.lat0),this.cos_p14=Math.cos(this.lat0)},forward:function(t){var e,i,r,n,s,a,o,h=t.x,l=t.y;return r=T(h-this.long0),e=Math.sin(l),i=Math.cos(l),n=Math.cos(r),1,((s=this.sin_p14*e+this.cos_p14*i*n)>0||Math.abs(s)<=1e-10)&&(a=1*this.a*i*Math.sin(r),o=this.y0+1*this.a*(this.cos_p14*e-this.sin_p14*i*n)),t.x=a,t.y=o,t},inverse:function(t){var e,i,r,s,a,o,h;return t.x-=this.x0,t.y-=this.y0,e=Math.sqrt(t.x*t.x+t.y*t.y),i=he(e/this.a),r=Math.sin(i),s=Math.cos(i),o=this.long0,Math.abs(e)<=1e-10?(h=this.lat0,t.x=o,t.y=h,t):(h=he(s*this.sin_p14+t.y*r*this.cos_p14/e),a=Math.abs(this.lat0)-n,Math.abs(a)<=1e-10?(o=this.lat0>=0?T(this.long0+Math.atan2(t.x,-t.y)):T(this.long0-Math.atan2(-t.x,t.y)),t.x=o,t.y=h,t):(o=T(this.long0+Math.atan2(t.x*r,e*this.cos_p14*s-t.y*this.sin_p14*r)),t.x=o,t.y=h,t))},names:["ortho"]},we=1,xe=2,Ce=3,Se=4,ke=5,Ee=6,Ae=1,Pe=2,Ie=3,Oe=4;function Ne(t,e,i,r){var s;return t<1e-10?(r.value=Ae,s=0):(s=Math.atan2(e,i),Math.abs(s)<=o?r.value=Ae:s>o&&s<=n+o?(r.value=Pe,s-=n):s>n+o||s<=-(n+o)?(r.value=Ie,s=s>=0?s-l:s+l):(r.value=Oe,s+=n)),s}function Le(t,e){var i=t+e;return i<-l?i+=h:i>+l&&(i-=h),i}var Te={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.lat0=this.lat0||0,this.long0=this.long0||0,this.lat_ts=this.lat_ts||0,this.title=this.title||"Quadrilateralized Spherical Cube",this.lat0>=n-o/2?this.face=ke:this.lat0<=-(n-o/2)?this.face=Ee:Math.abs(this.long0)<=o?this.face=we:Math.abs(this.long0)<=n+o?this.face=this.long0>0?xe:Se:this.face=Ce,0!==this.es&&(this.one_minus_f=1-(this.a-this.b)/this.a,this.one_minus_f_squared=this.one_minus_f*this.one_minus_f)},forward:function(t){var e,i,r,s,a,h,u={x:0,y:0},f={value:0};if(t.x-=this.long0,e=0!==this.es?Math.atan(this.one_minus_f_squared*Math.tan(t.y)):t.y,i=t.x,this.face===ke)s=n-e,i>=o&&i<=n+o?(f.value=Ae,r=i-n):i>n+o||i<=-(n+o)?(f.value=Pe,r=i>0?i-l:i+l):i>-(n+o)&&i<=-o?(f.value=Ie,r=i+n):(f.value=Oe,r=i);else if(this.face===Ee)s=n+e,i>=o&&i<=n+o?(f.value=Ae,r=-i+n):i<o&&i>=-o?(f.value=Pe,r=-i):i<-o&&i>=-(n+o)?(f.value=Ie,r=-i-n):(f.value=Oe,r=i>0?-i+l:-i-l);else{var c,d,p,g,m,_;this.face===xe?i=Le(i,+n):this.face===Ce?i=Le(i,+l):this.face===Se&&(i=Le(i,-n)),g=Math.sin(e),m=Math.cos(e),_=Math.sin(i),c=m*Math.cos(i),d=m*_,p=g,this.face===we?r=Ne(s=Math.acos(c),p,d,f):this.face===xe?r=Ne(s=Math.acos(d),p,-c,f):this.face===Ce?r=Ne(s=Math.acos(-c),p,-d,f):this.face===Se?r=Ne(s=Math.acos(-d),p,c,f):(s=r=0,f.value=Ae)}return h=Math.atan(12/l*(r+Math.acos(Math.sin(r)*Math.cos(o))-n)),a=Math.sqrt((1-Math.cos(s))/(Math.cos(h)*Math.cos(h))/(1-Math.cos(Math.atan(1/Math.cos(r))))),f.value===Pe?h+=n:f.value===Ie?h+=l:f.value===Oe&&(h+=1.5*l),u.x=a*Math.cos(h),u.y=a*Math.sin(h),u.x=u.x*this.a+this.x0,u.y=u.y*this.a+this.y0,t.x=u.x,t.y=u.y,t},inverse:function(t){var e,i,r,s,a,o,h,u,f,c,d,p,g={lam:0,phi:0},m={value:0};if(t.x=(t.x-this.x0)/this.a,t.y=(t.y-this.y0)/this.a,i=Math.atan(Math.sqrt(t.x*t.x+t.y*t.y)),e=Math.atan2(t.y,t.x),t.x>=0&&t.x>=Math.abs(t.y)?m.value=Ae:t.y>=0&&t.y>=Math.abs(t.x)?(m.value=Pe,e-=n):t.x<0&&-t.x>=Math.abs(t.y)?(m.value=Ie,e=e<0?e+l:e-l):(m.value=Oe,e+=n),f=l/12*Math.tan(e),a=Math.sin(f)/(Math.cos(f)-1/Math.sqrt(2)),o=Math.atan(a),(h=1-(r=Math.cos(e))*r*(s=Math.tan(i))*s*(1-Math.cos(Math.atan(1/Math.cos(o)))))<-1?h=-1:h>1&&(h=1),this.face===ke)u=Math.acos(h),g.phi=n-u,m.value===Ae?g.lam=o+n:m.value===Pe?g.lam=o<0?o+l:o-l:m.value===Ie?g.lam=o-n:g.lam=o;else if(this.face===Ee)u=Math.acos(h),g.phi=u-n,m.value===Ae?g.lam=-o+n:m.value===Pe?g.lam=-o:m.value===Ie?g.lam=-o-n:g.lam=o<0?-o-l:-o+l;else{var _,y,v;f=(_=h)*_,y=(f+=(v=f>=1?0:Math.sqrt(1-f)*Math.sin(o))*v)>=1?0:Math.sqrt(1-f),m.value===Pe?(f=y,y=-v,v=f):m.value===Ie?(y=-y,v=-v):m.value===Oe&&(f=y,y=v,v=-f),this.face===xe?(f=_,_=-y,y=f):this.face===Ce?(_=-_,y=-y):this.face===Se&&(f=_,_=y,y=-f),g.phi=Math.acos(-v)-n,g.lam=Math.atan2(y,_),this.face===xe?g.lam=Le(g.lam,-n):this.face===Ce?g.lam=Le(g.lam,-l):this.face===Se&&(g.lam=Le(g.lam,+n))}return 0!==this.es&&(c=g.phi<0?1:0,d=Math.tan(g.phi),p=this.b/Math.sqrt(d*d+this.one_minus_f_squared),g.phi=Math.atan(Math.sqrt(this.a*this.a-p*p)/(this.one_minus_f*p)),c&&(g.phi=-g.phi)),g.lam+=this.long0,t.x=g.lam,t.y=g.phi,t},names:["Quadrilateralized Spherical Cube","Quadrilateralized_Spherical_Cube","qsc"]},Re=[[1,22199e-21,-715515e-10,31103e-10],[.9986,-482243e-9,-24897e-9,-13309e-10],[.9954,-83103e-8,-448605e-10,-9.86701e-7],[.99,-.00135364,-59661e-9,36777e-10],[.9822,-.00167442,-449547e-11,-572411e-11],[.973,-.00214868,-903571e-10,1.8736e-8],[.96,-.00305085,-900761e-10,164917e-11],[.9427,-.00382792,-653386e-10,-26154e-10],[.9216,-.00467746,-10457e-8,481243e-11],[.8962,-.00536223,-323831e-10,-543432e-11],[.8679,-.00609363,-113898e-9,332484e-11],[.835,-.00698325,-640253e-10,9.34959e-7],[.7986,-.00755338,-500009e-10,9.35324e-7],[.7597,-.00798324,-35971e-9,-227626e-11],[.7186,-.00851367,-701149e-10,-86303e-10],[.6732,-.00986209,-199569e-9,191974e-10],[.6213,-.010418,883923e-10,624051e-11],[.5722,-.00906601,182e-6,624051e-11],[.5322,-.00677797,275608e-9,624051e-11]],ze=[[-520417e-23,.0124,121431e-23,-845284e-16],[.062,.0124,-1.26793e-9,4.22642e-10],[.124,.0124,5.07171e-9,-1.60604e-9],[.186,.0123999,-1.90189e-8,6.00152e-9],[.248,.0124002,7.10039e-8,-2.24e-8],[.31,.0123992,-2.64997e-7,8.35986e-8],[.372,.0124029,9.88983e-7,-3.11994e-7],[.434,.0123893,-369093e-11,-4.35621e-7],[.4958,.0123198,-102252e-10,-3.45523e-7],[.5571,.0121916,-154081e-10,-5.82288e-7],[.6176,.0119938,-241424e-10,-5.25327e-7],[.6769,.011713,-320223e-10,-5.16405e-7],[.7346,.0113541,-397684e-10,-6.09052e-7],[.7903,.0109107,-489042e-10,-104739e-11],[.8435,.0103431,-64615e-9,-1.40374e-9],[.8936,.00969686,-64636e-9,-8547e-9],[.9394,.00840947,-192841e-9,-42106e-10],[.9761,.00616527,-256e-6,-42106e-10],[1,.00328947,-319159e-9,-42106e-10]],Be=a/5,De=function(t,e){return t[0]+e*(t[1]+e*(t[2]+e*t[3]))};var je={init:function(){this.x0=this.x0||0,this.y0=this.y0||0,this.long0=this.long0||0,this.es=0,this.title=this.title||"Robinson"},forward:function(t){var e=T(t.x-this.long0),i=Math.abs(t.y),r=Math.floor(i*Be);r<0?r=0:r>=18&&(r=17);var n={x:De(Re[r],i=a*(i-.08726646259971647*r))*e,y:De(ze[r],i)};return t.y<0&&(n.y=-n.y),n.x=n.x*this.a*.8487+this.x0,n.y=n.y*this.a*1.3523+this.y0,n},inverse:function(t){var e={x:(t.x-this.x0)/(.8487*this.a),y:Math.abs(t.y-this.y0)/(1.3523*this.a)};if(e.y>=1)e.x/=Re[18][0],e.y=t.y<0?-n:n;else{var i=Math.floor(18*e.y);for(i<0?i=0:i>=18&&(i=17);;)if(ze[i][0]>e.y)--i;else{if(!(ze[i+1][0]<=e.y))break;++i}var r=ze[i],a=5*(e.y-r[0])/(ze[i+1][0]-r[0]);a=function(t,e,i,r){for(var n=e;r;--r){var s=t(n);if(n-=s,Math.abs(s)<i)break}return n}((function(t){return(De(r,t)-e.y)/function(t,e){return t[1]+e*(2*t[2]+3*e*t[3])}(r,t)}),a,1e-10,100),e.x/=De(Re[i],a),e.y=(5*i+a)*s,t.y<0&&(e.y=-e.y)}return e.x=T(e.x+this.long0),e},names:["Robinson","robin"]};var Ue={init:function(){this.name="geocent"},forward:function(t){return it(t,this.es,this.a)},inverse:function(t){return rt(t,this.es,this.a,this.b)},names:["Geocentric","geocentric","geocent","Geocent"]},Fe=0,qe=1,Ge=2,Ze=3,Ye={h:{def:1e5,num:!0},azi:{def:0,num:!0,degrees:!0},tilt:{def:0,num:!0,degrees:!0},long0:{def:0,num:!0},lat0:{def:0,num:!0}};var Ve={init:function(){if(Object.keys(Ye).forEach(function(t){if(void 0===this[t])this[t]=Ye[t].def;else{if(Ye[t].num&&isNaN(this[t]))throw new Error("Invalid parameter value, must be numeric "+t+" = "+this[t]);Ye[t].num&&(this[t]=parseFloat(this[t]))}Ye[t].degrees&&(this[t]=this[t]*s)}.bind(this)),Math.abs(Math.abs(this.lat0)-n)<1e-10?this.mode=this.lat0<0?qe:Fe:Math.abs(this.lat0)<1e-10?this.mode=Ge:(this.mode=Ze,this.sinph0=Math.sin(this.lat0),this.cosph0=Math.cos(this.lat0)),this.pn1=this.h/this.a,this.pn1<=0||this.pn1>1e10)throw new Error("Invalid height");this.p=1+this.pn1,this.rp=1/this.p,this.h1=1/this.pn1,this.pfact=(this.p+1)*this.h1,this.es=0;var t=this.tilt,e=this.azi;this.cg=Math.cos(e),this.sg=Math.sin(e),this.cw=Math.cos(t),this.sw=Math.sin(t)},forward:function(t){t.x-=this.long0;var e,i,r,n,s=Math.sin(t.y),a=Math.cos(t.y),o=Math.cos(t.x);switch(this.mode){case Ze:i=this.sinph0*s+this.cosph0*a*o;break;case Ge:i=a*o;break;case qe:i=-s;break;case Fe:i=s}switch(e=(i=this.pn1/(this.p-i))*a*Math.sin(t.x),this.mode){case Ze:i*=this.cosph0*s-this.sinph0*a*o;break;case Ge:i*=s;break;case Fe:i*=-a*o;break;case qe:i*=a*o}return n=1/((r=i*this.cg+e*this.sg)*this.sw*this.h1+this.cw),e=(e*this.cg-i*this.sg)*this.cw*n,i=r*n,t.x=e*this.a,t.y=i*this.a,t},inverse:function(t){t.x/=this.a,t.y/=this.a;var e,i,r,n={x:t.x,y:t.y};r=1/(this.pn1-t.y*this.sw),e=this.pn1*t.x*r,i=this.pn1*t.y*this.cw*r,t.x=e*this.cg+i*this.sg,t.y=i*this.cg-e*this.sg;var s=Bt(t.x,t.y);if(Math.abs(s)<1e-10)n.x=0,n.y=t.y;else{var a,o;switch(o=1-s*s*this.pfact,o=(this.p-Math.sqrt(o))/(this.pn1/s+s/this.pn1),a=Math.sqrt(1-o*o),this.mode){case Ze:n.y=Math.asin(a*this.sinph0+t.y*o*this.cosph0/s),t.y=(a-this.sinph0*Math.sin(n.y))*s,t.x*=o*this.cosph0;break;case Ge:n.y=Math.asin(t.y*o/s),t.y=a*s,t.x*=o;break;case Fe:n.y=Math.asin(a),t.y=-t.y;break;case qe:n.y=-Math.asin(a)}n.x=Math.atan2(t.x,t.y)}return t.x=n.x+this.long0,t.y=n.y,t},names:["Tilted_Perspective","tpers"]};var We,Xe={init:function(){if(this.flip_axis="x"===this.sweep?1:0,this.h=Number(this.h),this.radius_g_1=this.h/this.a,this.radius_g_1<=0||this.radius_g_1>1e10)throw new Error;if(this.radius_g=1+this.radius_g_1,this.C=this.radius_g*this.radius_g-1,0!==this.es){var t=1-this.es,e=1/t;this.radius_p=Math.sqrt(t),this.radius_p2=t,this.radius_p_inv2=e,this.shape="ellipse"}else this.radius_p=1,this.radius_p2=1,this.radius_p_inv2=1,this.shape="sphere";this.title||(this.title="Geostationary Satellite View")},forward:function(t){var e,i,r,n,s=t.x,a=t.y;if(s-=this.long0,"ellipse"===this.shape){a=Math.atan(this.radius_p2*Math.tan(a));var o=this.radius_p/Bt(this.radius_p*Math.cos(a),Math.sin(a));if(i=o*Math.cos(s)*Math.cos(a),r=o*Math.sin(s)*Math.cos(a),n=o*Math.sin(a),(this.radius_g-i)*i-r*r-n*n*this.radius_p_inv2<0)return t.x=Number.NaN,t.y=Number.NaN,t;e=this.radius_g-i,this.flip_axis?(t.x=this.radius_g_1*Math.atan(r/Bt(n,e)),t.y=this.radius_g_1*Math.atan(n/e)):(t.x=this.radius_g_1*Math.atan(r/e),t.y=this.radius_g_1*Math.atan(n/Bt(r,e)))}else"sphere"===this.shape&&(e=Math.cos(a),i=Math.cos(s)*e,r=Math.sin(s)*e,n=Math.sin(a),e=this.radius_g-i,this.flip_axis?(t.x=this.radius_g_1*Math.atan(r/Bt(n,e)),t.y=this.radius_g_1*Math.atan(n/e)):(t.x=this.radius_g_1*Math.atan(r/e),t.y=this.radius_g_1*Math.atan(n/Bt(r,e))));return t.x=t.x*this.a,t.y=t.y*this.a,t},inverse:function(t){var e,i,r,n,s=-1,a=0,o=0;if(t.x=t.x/this.a,t.y=t.y/this.a,"ellipse"===this.shape){this.flip_axis?(o=Math.tan(t.y/this.radius_g_1),a=Math.tan(t.x/this.radius_g_1)*Bt(1,o)):(a=Math.tan(t.x/this.radius_g_1),o=Math.tan(t.y/this.radius_g_1)*Bt(1,a));var h=o/this.radius_p;if(e=a*a+h*h+s*s,(r=(i=2*this.radius_g*s)*i-4*e*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;n=(-i-Math.sqrt(r))/(2*e),s=this.radius_g+n*s,a*=n,o*=n,t.x=Math.atan2(a,s),t.y=Math.atan(o*Math.cos(t.x)/s),t.y=Math.atan(this.radius_p_inv2*Math.tan(t.y))}else if("sphere"===this.shape){if(this.flip_axis?(o=Math.tan(t.y/this.radius_g_1),a=Math.tan(t.x/this.radius_g_1)*Math.sqrt(1+o*o)):(a=Math.tan(t.x/this.radius_g_1),o=Math.tan(t.y/this.radius_g_1)*Math.sqrt(1+a*a)),e=a*a+o*o+s*s,(r=(i=2*this.radius_g*s)*i-4*e*this.C)<0)return t.x=Number.NaN,t.y=Number.NaN,t;n=(-i-Math.sqrt(r))/(2*e),s=this.radius_g+n*s,a*=n,o*=n,t.x=Math.atan2(a,s),t.y=Math.atan(o*Math.cos(t.x)/s)}return t.x=t.x+this.long0,t},names:["Geostationary Satellite View","Geostationary_Satellite","geos"]};mt.defaultDatum="WGS84",mt.Proj=et,mt.WGS84=new mt.Proj("WGS84"),mt.Point=It,mt.toPoint=ut,mt.defs=E,mt.nadgrid=function(t,e){var i=new DataView(e),r=function(t){var e=t.getInt32(8,!1);if(11===e)return!1;11!==(e=t.getInt32(8,!0))&&console.warn("Failed to detect nadgrid endian-ness, defaulting to little-endian");return!0}(i),n=function(t,e){return{nFields:t.getInt32(8,e),nSubgridFields:t.getInt32(24,e),nSubgrids:t.getInt32(40,e),shiftType:J(t,56,64).trim(),fromSemiMajorAxis:t.getFloat64(120,e),fromSemiMinorAxis:t.getFloat64(136,e),toSemiMajorAxis:t.getFloat64(152,e),toSemiMinorAxis:t.getFloat64(168,e)}}(i,r);n.nSubgrids>1&&console.log("Only single NTv2 subgrids are currently supported, subsequent sub grids are ignored");var s={header:n,subgrids:function(t,e,i){for(var r=[],n=0;n<e.nSubgrids;n++){var s=Q(t,176,i),a=$(t,176,s,i),o=Math.round(1+(s.upperLongitude-s.lowerLongitude)/s.longitudeInterval),h=Math.round(1+(s.upperLatitude-s.lowerLatitude)/s.latitudeInterval);r.push({ll:[H(s.lowerLongitude),H(s.lowerLatitude)],del:[H(s.longitudeInterval),H(s.latitudeInterval)],lim:[o,h],count:s.gridNodeCount,cvs:K(a)})}return r}(i,n,r)};return W[t]=s,s},mt.transform=ct,mt.mgrs=vt,mt.version="__VERSION__",(We=mt).Proj.projections.add(Rt),We.Proj.projections.add(Ft),We.Proj.projections.add(qt),We.Proj.projections.add(Yt),We.Proj.projections.add(Vt),We.Proj.projections.add(Wt),We.Proj.projections.add(Xt),We.Proj.projections.add(Ht),We.Proj.projections.add(Jt),We.Proj.projections.add(se),We.Proj.projections.add(oe),We.Proj.projections.add(le),We.Proj.projections.add(ue),We.Proj.projections.add(fe),We.Proj.projections.add(ce),We.Proj.projections.add(de),We.Proj.projections.add(pe),We.Proj.projections.add(ge),We.Proj.projections.add(me),We.Proj.projections.add(_e),We.Proj.projections.add(ye),We.Proj.projections.add(ve),We.Proj.projections.add(be),We.Proj.projections.add(Me),We.Proj.projections.add(Te),We.Proj.projections.add(je),We.Proj.projections.add(Ue),We.Proj.projections.add(Ve),We.Proj.projections.add(Xe);e.default=mt},function(t,e,i){"use strict";i.r(e),i.d(e,"VectorStyle",(function(){return s})),i.d(e,"VectorTileImageryProvider",(function(){return x}));var r=i(0);function n(t){return"string"==typeof t?t=r.Color.fromCssColorString(t):Array.isArray(t)&&(t=r.Color.fromBytes(t[0],t[1],t[2],t[3])),t}function s(t){if("undefined"==typeof document)return t;t=r.defaultValue(t,{}),this.fillColor=r.defaultValue(n(t.fillColor),n([0,255,255,30])),this.fill=r.defaultValue(t.fill,!0),this.labelStroke=t.labelStroke,this.labelStrokeWidth=r.defaultValue(t.labelStrokeWidth,1),this.labelStrokeColor=r.defaultValue(n(t.labelStrokeColor),n([160,99,57])),this.outlineColor=r.defaultValue(n(t.outlineColor),n("yellow")),this.backgroundColor=n(t.backgroundColor),this.lineWidth=r.defaultValue(t.lineWidth,1.5),this.outline=r.defaultValue(t.outline,!0),this.fontColor=n(r.defaultValue(t.fontColor,"black")),this.fontSize=r.defaultValue(t.fontSize,16),this.fontFamily=r.defaultValue(t.fontFamily,"宋体"),this.pointSize=r.defaultValue(t.pointSize,4),this.pointColor=n(r.defaultValue(t.pointColor,"yellow")),this.pointStyle=r.defaultValue(t.pointStyle,"Ring"),this.labelPropertyName=r.defaultValue(t.labelPropertyName,"NAME"),this.ringRadius=r.defaultValue(t.ringRadius,2),this.circleLineWidth=r.defaultValue(t.circleLineWidth,2),r.defined(t.showMaker)&&(this.showMarker=r.defaultValue(t.showMaker,!0)),r.defined(t.showMarker)&&(this.showMarker=r.defaultValue(t.showMarker,!0)),this.showLabel=r.defaultValue(t.showLabel,!0),this.showCenterLabel=r.defaultValue(t.showCenterLabel,!1),this.centerLabelPropertyName=t.centerLabelPropertyName,this.labelOffsetX=r.defaultValue(t.labelOffsetX,0),this.labelOffsetY=r.defaultValue(t.labelOffsetY,0),this.markerImage=t.markerImage,this.lineDash=t.lineDash,this.lineCap=r.defaultValue(t.lineCap,"butt"),this.lineJoin=r.defaultValue(t.lineJoin,"miter"),this.shadowColor=n(t.shadowColor),this.shadowBlur=t.shadowBlur,this.shadowOffsetX=t.shadowOffsetX,this.shadowOffsetY=t.shadowOffsetY,this.miterLimit=r.defaultValue(t.miterLimit,10),this.markerImageEl=null;var e=r.defer();this.readyPromise=e.promise;var i=this;if("string"==typeof this.markerImage){var s=new Image;s.onload=function(){i.markerImageEl=this,e.resolve(!0)},s.onerror=function(t){e.reject(t)},s.src=this.markerImage}else(this.markerImage instanceof Image||this.markerImage instanceof HTMLCanvasElement)&&(this.markerImageEl=this.markerImage),setTimeout((function(){e.resolve(!0)}),10)}s.prototype.clone=function(){var t=new s;for(var e in this)this.hasOwnProperty(e)&&(void 0!==r&&this[e]instanceof r.Color?t[e]=r.Color.clone(this[e]):t[e]=this[e]);return t},s.Default=new s;var a=i(27),o=i.n(a),h=i(1);function l(t){var e=t.lastIndexOf(".");return e>=0?t.substring(e,t.length):""}function u(t,e){var i=t,r=e;this.project=function(t,e){var n=e.xMax-e.xMin,s=e.yMin-e.yMax,a=Math.abs(t[0]-e.xMin),o=t[1]-e.yMax;return{x:a/n*i,y:o/s*r}},this.unproject=function(t,e){var n=e.xMax-e.xMin,s=e.yMin-e.yMax;return[t.x/i*n,t.y/r*s]},this.getBoundingRect=function(t){for(var e,i,r=Number.MAX_VALUE,n={xMin:r,yMin:r,xMax:-r,yMax:-r},s=0,a=t.length;s<a;s++){var o=t[s].getBoundingRect();e={x:o.xMin,y:o.yMin},i={x:o.xMax,y:o.yMax},n.xMin=n.xMin<e.x?n.xMin:e.x,n.yMin=n.yMin<e.y?n.yMin:e.y,n.xMax=n.xMax>i.x?n.xMax:i.x,n.yMax=n.yMax>i.y?n.yMax:i.y}return n}}var f=function(t,e){return{x:t,y:e}};function c(t,e){var i=(e=e||{font:"20px sans-serif"}).backgroundColor,n=e.padding?e.padding:0;delete e.backgroundColor,delete e.padding;for(var s=t.split(/[\r]?\n+/),a=[],o=0,h=0,l=0;l<s.length;l++){var u=Object(r.writeTextToCanvas)(s[l],e);u&&(a.push(u),h+=u.height,o=Math.max(o,u.width))}e.backgroundColor=i,e.padding=n;var c=e.canvas;c||(o+=2*n,h+=2.25*n,(c=document.createElement("canvas")).width=o,c.height=h);var d=c.getContext("2d");d.fillStyle=i?i.toCssColorString():void 0,e.border&&(d.lineWidth=e.borderWidth,d.strokeStyle=e.borderColor.toCssColorString()),e.borderRadius?function(t,e,i){var r=f(t.x+e,t.y),n=f(t.x+t.width,t.y),s=f(t.x+t.width,t.y+t.height),a=f(t.x,t.y+t.height),o=f(t.x,t.y);i.beginPath(),i.moveTo(r.x,r.y),i.arcTo(n.x,n.y,s.x,s.y,e),i.arcTo(s.x,s.y,a.x,a.y,e),i.arcTo(a.x,a.y,o.x,o.y,e),i.arcTo(o.x,o.y,r.x,r.y,e),i.fill(),i.stroke()}({x:0,y:0,width:c.width,height:c.height},e.borderRadius,d):(i&&d.fillRect(0,0,c.width,c.height),e.border&&d.strokeRect(0,0,c.width,c.height)),delete d.strokeStyle,delete d.fillStyle;for(var p=0,g=0;g<a.length;g++)d.drawImage(a[g],0+n,p+n),p+=a[g].height;return c}function d(t,e,i){var r=h.polygonToLine(e);if("Feature"==r.type)return"LineString"==r.geometry.type?h.pointToLineDistance(t,r,i):p(t,r,i);if("FeatureCollection"==r.type){var n=Number.MAX_VALUE,s=r;if(h.featureEach(s,(function(e){var s=e.geometry;s&&(n="LineString"==s.type?Math.min(h.pointToLineDistance(t,r,i),n):Math.min(p(t,r,i),n))})),n==Number.MAX_VALUE)return;return n}}function p(t,e,i){var r=h.getCoords(e),n=null,s=Number.MAX_VALUE;if(r.forEach((function(e){n=h.lineString(e);var r=h.pointToLineDistance(t,n,i);s=Math.min(r,s)})),r=[],s!=Number.MAX_VALUE)return s}function g(t,e,i){var r=void 0;switch(e.geometry.type){case"Point":r=h.distance(t,e,i);break;case"MultiPoint":var n=h.getCoords(e),s=[];n.forEach((function(t){s.push(h.point(t))})),s=h.featureCollection(s);var a=h.nearestPoint(t,s);s=[],r=a.properties.distanceToPoint;break;case"LineString":r=h.pointToLineDistance(t,e,i);break;case"MultiLineString":r=p(t,e,i);break;case"Polygon":r=d(t,e,i);break;case"MultiPolygon":r=function(t,e,i){var r=h.getCoords(e),n=Number.MAX_VALUE;if(r.forEach((function(e){var r=h.polygon(e),s=d(t,r,i);n=Math.min(s,n)})),r=[],n!=Number.MAX_VALUE)return n}(t,e,i)}return r}var m,_=i(6),y=i.n(_);function v(t){return new Promise((function(e,i){var r=new FileReader;r.onload=function(t){e(t.target.result)},r.onerror=function(t){i(t.error)},r.readAsArrayBuffer(t)}))}function b(t,e){return y()(t,e)}function M(t,e){if(!t||t.length>0){for(var i,n,s,a=r.defer(),o=a.promise,h=0;h<t.length;h++)t[h].name.toLocaleLowerCase().indexOf(".shp")>0&&(i=t[h]),t[h].name.toLocaleLowerCase().indexOf(".prj")>0&&(s=t[h]),t[h].name.toLocaleLowerCase().indexOf(".dbf")>0&&(n=t[h]);return i&&s&&n?(v(i).then((function(t){(function(t,e){return new Promise((function(i,r){var n=new FileReader;n.onload=function(t){i(t.target.result)},n.onerror=function(t){r(t.error)},n.readAsText(t,e)}))})(s,e).then((function(r){v(n).then((function(n){var s=y.a.combine([y.a.parseShp(t,r),y.a.parseDbf(n,e)]);s.fileName=i.name.toLocaleLowerCase(),a.resolve(s)})).catch((function(t){a.reject(t)}))})).catch((function(t){a.reject(t)}))})).catch((function(t){a.reject(t)})),o):(a.reject(new Error("打开文件失败,请通过ctrl+同时选择shp、prj、dbf三个文件")),o)}throw new Error("文件列表不能为空")}function w(t){var e,i,r;if(!(t.length>=3))return!1;for(var n=0;n<t.length;n++){var s=t[n];if(!(s instanceof File||s instanceof Blob&&s.name))return!1;t[n].name.toLocaleLowerCase().indexOf(".shp")>0&&(e=t[n]),t[n].name.toLocaleLowerCase().indexOf(".prj")>0&&(r=t[n]),t[n].name.toLocaleLowerCase().indexOf(".dbf")>0&&(i=t[n])}return!!(e&&r&&i)}function x(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(r.defined(e.source)){this.options=e;var i=null,n=!1;if("string"==typeof e.source){var a=e.source.toLowerCase();if(".shp"!==(i=l(a))&&".json"!==i&&".geojson"!==i&&".topojson"!==i)throw new Error("The data  options.source provider is not supported.")}else if(e.source.type&&"FeatureCollection"==e.source.type);else{if(!w(e.source))throw new Error("The data  options.source provider is not supported.");n=!0}this._rectangle=e.rectangle,this._tilingScheme=new r.GeographicTilingScheme({ellipsoid:e.ellipsoid}),this._tileWidth=r.defaultValue(e.tileWidth,256),this._tileHeight=r.defaultValue(e.tileHeight,256),this._url=e.source,this._fileExtension=i,this._removeDuplicate=r.defaultValue(e.removeDuplicate,!0),this._allowPick=r.defaultValue(e.allowPick,!1),this._simplifyTolerance=r.defaultValue(e.simplifyTolerance,.01),this._simplify=r.defaultValue(e.simplify,!1),this._maximumLevel=r.defaultValue(e.maximumLevel,22),this._minimumLevel=r.defaultValue(e.minimumLevel,3),this._showMaximumLevel=r.defaultValue(e.showMaximumLevel,!0),this._makerImage=e.markerImage,this._tileCacheSize=r.defaultValue(e.tileCacheSize,200),"object"!=o()(e.defaultStyle)||e.defaultStyle instanceof s||(e.defaultStyle=new s(e.defaultStyle)),this._defaultStyle=r.defaultValue(e.defaultStyle,s.Default.clone()),this._styleFilter="function"==typeof e.styleFilter?e.styleFilter:void 0,this.clustering=e.clustering,this._errorEvent=new r.Event,this._featuresPicked=new r.Event,this._readyPromise=r.defer(),this._ready=!1,this._state=x.State.READY,this._cache={},this._count=0,this.zIndex=e.zIndex,this._bbox=null,this._geoJSON=null;var u=this,f=[];"string"==typeof this._makerImage&&f.push(new Promise((function(e,i){var r=new Image;r.onload=function(){e(this),u._makerImageEl=this},r.onerror=function(t){e(t)},r.src=t._makerImage})));var c=r.defer();if(f.push(c.promise),this._state=x.State.SHPLOADING,i)switch(i){case".shp":var d=this._url,p=l(d);b(d=d.replace(p,"")).then(m,(function(t){console.log("load shp file error："+t),u.readyPromise.reject(t)}));break;case".json":case".geojson":case".topojson":r.Resource.fetchJson(this._url).then((function(t){m(t)})).catch((function(t){console.log(t)}));break;default:throw new Error("The file  options.source provider is not supported.")}else if(n){var g=M(u._url,u.options.encoding||"gbk");g?g.then(m).catch((function(t){this._readyPromise.reject(t)})):this._readyPromise.reject(new Error("The file  options.source provider is not supported."))}else setTimeout((function(){Array.isArray(u._url)?this._readyPromise.reject(new Error("The data  options.source provide is not supported.")):m(u._url)}),10);this._lineGeoJSON=null,this._outlineGeoJSON=null,this._pointGeoJSON=null,this._polygonJSON=null,this._onlyPoint=!1,this._lineOnly=!1,this._polygonOnly=!1,Promise.all(f).then((function(){u._ready=u._state==x.State.LOADED,u._createCanvas(),x.instanceCount++,u._readyPromise.resolve(!0),u._state=x.State.COMPELTED})).catch((function(t){u._readyPromise.reject(t)}))}function m(t){u._allowPick&&(u._geoJSON=t);var e,i=u._simplifyTolerance,n=[],s=[],a=[],o=[],l=!0,f=!0,d=!0;u._defaultStyle.showCenterLabel&&u._defaultStyle.centerLabelPropertyName&&(u._defaultStyle.showLabel=!0,u._defaultStyle.labelPropertyName=u._defaultStyle.centerLabelPropertyName,function(t,e){var i={};h.featureEach(t,(function(t){var r=t.geometry;r||("Polygon"==r.type||"MultiPolygon"==r.type)&&u._defaultStyle.showCenterLabel&&u._defaultStyle.centerLabelPropertyName&&t.properties.hasOwnProperty(e)&&(i[t.properties[e]]||(i[t.properties[e]]=[]),i[t.properties[e]].push(t))}));for(var r=Object.keys(i),n=0;n<r.length;n++){var s=i[r[n]][0],o=h.featureCollection(i[r[n]]),l=h.center(o);a.push(l),l.properties=s.properties,delete i[r[n]]}}(t,u._defaultStyle.centerLabelPropertyName)),h.featureEach(t,(function(e){var i=e.geometry;if(i&&"MultiPolygon"==i.type){var r=h.getCoords(e);r.forEach((function(i){t.features.push(h.polygon(i,e.properties))})),r=[]}}));for(var p=0;p<t.features.length;p++){var g=t.features[p];if(g){var m=g.geometry;m&&"MultiPolygon"==m.type&&t.features.splice(p,1)}}u._removeDuplicate&&(t=h.removeDuplicate(t)),h.featureEach(t,(function(t,r){var c=t.geometry;if(c){if("Point"==c.type||"MultiPoint"==c.type)a.push(t),f=!1,d=!1;else if(u._defaultStyle.showCenterLabel&&u._defaultStyle.centerLabelPropertyName&&"Polygon"!==c.type&&"MultiPolygon"!==c.type){u._defaultStyle.showLabel=!0,u._defaultStyle.labelPropertyName=u._defaultStyle.centerLabelPropertyName;var p=h.centerOfMass(t);a.push(p),p.properties=t.properties}if("Polygon"==c.type||"MultiPolygon"==c.type){var g=h.polygonToLineString(t);g&&("FeatureCollection"==g.type?s=s.concat(g.features):"Feature"==g.type&&(s=s.concat(g))),l=!1,f=!1,u._simplify?(e=h.simplify(t,{tolerance:i,highQuality:!1}),o.push(e),e=null):o.push(t)}"MultiLineString"!=c.type&&"LineString"!=c.type||(u._simplify?(e=h.simplify(t,{tolerance:i,highQuality:!1}),n.push(e),e=null):n.push(t),l=!1,d=!1)}})),n.length>0&&(u._lineGeoJSON=h.featureCollection(n),n=null),s.length>0&&(s.forEach((function(t){t.properties._outline=!0})),u._outlineGeoJSON=h.featureCollection(s),s=null),a.length>0&&(u._pointGeoJSON=h.featureCollection(a),a=null),o.length>0&&(u._polygonJSON=h.featureCollection(o),o=null),u._lineOnly=f,u._polygonOnly=d,u._onlyPoint=l,u._state=x.State.LOADED;var _=h.bbox(t);_[0]==_[2]&&(_[0]=_[0]-.1,_[2]=_[2]+.1),_[1]==_[3]&&(_[1]=_[1]-.1,_[3]=_[3]+.1),u._bbox=r.Rectangle.fromDegrees(_[0],_[1],_[2],_[3]),u._rectangle||(u._rectangle=u._bbox),t=null,c.resolve(u)}}function C(t,e,i,r,n,s,a,o,h){var l=0,u=[];if(s.map((function(s){if(!a||l<=0){var h=0;t.beginPath(),s.map((function(s){var a=e.project(s,i);0==h?t.moveTo(r+a.x,n+a.y):t.lineTo(r+a.x,n+a.y),h++})),a&&(t.closePath(),t.fill()),o&&t.stroke()}else u.push(s);l++})),a)return u;u=null}function S(t){if(m||((m=document.createElement("canvas")).width=256,m.height=256),t){var e=m.getContext("2d");t instanceof r.Color?e.fillStyle=t.toCssColorString():e.fillStyle=t,e.fillRect(0,0,m.width,m.height)}else{m.getContext("2d").clearRect(0,0,m.width,m.height)}return m}x.instanceCount=0,x._currentTaskCount=0,x._maxTaskCount=3,x.State={READY:0,SHPLOADING:1,CLIPPING:3,GEOJSONDRAWING:4,COMPELTED:5},Object.defineProperties(x.prototype,{styleFilter:{get:function(){return this._styleFilter},set:function(t){this._styleFilter=t}},defaultStyle:{get:function(){return this._defaultStyle}},proxy:{get:function(){}},tileWidth:{get:function(){return this._tileWidth}},tileHeight:{get:function(){return this._tileHeight}},maximumLevel:{get:function(){return this._showMaximumLevel?this._maximumLevel:22}},minimumLevel:{get:function(){return 0}},tilingScheme:{get:function(){return this._tilingScheme}},rectangle:{get:function(){return this._rectangle}},tileDiscardPolicy:{get:function(){}},errorEvent:{get:function(){return this._errorEvent}},featuresPicked:{get:function(){return this._featuresPicked}},ready:{get:function(){return this._ready}},readyPromise:{get:function(){return this._readyPromise.promise}},credit:{get:function(){}},hasAlphaChannel:{get:function(){return!0}}}),x.prototype._createCanvas=function(){this._canvas=document.createElement("canvas"),this._canvas.width=this._tileWidth,this._canvas.height=this._tileHeight,this._context=this._canvas.getContext("2d"),this._defaultStyle.backgroundColor&&(this._defaultStyle.backgroundColor instanceof r.Color?this._context.fillStyle=this._defaultStyle.backgroundColor.toCssColorString():this._context.fillStyle=this._defaultStyle.backgroundColor,this._context.fillRect(0,0,this._canvas.width,this._canvas.height)),this._context.lineWidth=this._defaultStyle.lineWidth,this._defaultStyle.outlineColor instanceof r.Color?this._context.strokeStyle=this._defaultStyle.outlineColor.toCssColorString():this._context.strokeStyle=this._defaultStyle.outlineColor,this._defaultStyle.fillColor instanceof r.Color?this._context.fillStyle=this._defaultStyle.fillColor.toCssColorString():this._context.fillStyle=this._defaultStyle.fillColor},x.prototype._clipGeojson=function(t){for(var e=[r.Math.toDegrees(t.west),r.Math.toDegrees(t.south),r.Math.toDegrees(t.east),r.Math.toDegrees(t.north)],i=[[e[0],e[1]],[e[2],e[1]],[e[2],e[3]],[e[0],e[3]],[e[0],e[1]]],n=0;n<i.length;n++){var s=h.point(i[n]);i[n]=s}i=h.featureCollection(i);var a=[];if(this._pointGeoJSON){var o=(e[2]-e[0])/2,l=(e[3]-e[1])/2,u=h.bboxPolygon([e[0]-o,e[1]-l,e[2]+o,e[3]+l]),f=h.featureCollection([u]),c=h.within(this._pointGeoJSON,f);a=a.concat(c.features)}var d,p=[];return this._polygonJSON&&p.push(this._polygonJSON),this._lineGeoJSON&&p.push(this._lineGeoJSON),this._outlineGeoJSON&&p.push(this._outlineGeoJSON),p.forEach((function(t){h.featureEach(t,(function(t,i){if(t.geometry){d=null;try{if(d||(d=h.bboxClip(t,e)),d&&d.geometry.coordinates.length>0){for(var r=!0,n=0;n<d.geometry.coordinates.length;n++)if(d.geometry.coordinates[n].length>0){r=!1;break}r||a.push(d)}}catch(i){var s=[];t.geometry.coordinates.forEach((function(t){t.length>3&&s.push(t)})),t.geometry.coordinates=s;try{(d=h.bboxClip(t,e))&&d.geometry.coordinates.length>0&&a.push(d)}catch(t){console.error(t)}}}}))})),d=null,a.length>0?a=h.featureCollection(a):null},x.prototype._drawGeojson=function(t,e,i,n,s,a,o,l,f,d,p,g){var m=this;if(void 0===l&&(l=!0),void 0===f&&(f=!0),l||f){void 0===a&&(a=t.canvas.width-e),void 0===o&&(o=t.canvas.height-i);var _=new u(a,o),y=this._defaultStyle,v={labelStroke:y.labelStroke,labelStrokeWidth:y.labelStrokeWidth,labelStrokeColor:y.labelStrokeColor,pointSize:y.pointSize,fontSize:y.fontSize,fontFamily:y.fontFamily,color:y.fontColor instanceof r.Color?y.fontColor.toCssColorString():y.fontColor,backgroundColor:y.pointColor instanceof r.Color?y.pointColor.toCssColorString():y.pointColor,pointStyle:y.pointStyle,ringRadius:y.ringRadius,circleLineWidth:y.circleLineWidth,showMarker:y.showMarker,showLabel:y.showLabel,labelOffsetX:y.labelOffsetX,labelOffsetY:y.labelOffsetY,markerSymbol:y.markerImage instanceof Image||y.markerImage instanceof HTMLCanvasElement?y.markerImage:y.markerImageEl},b=[];m._styleFilter&&(h.featureEach(n,(function(t,e){m._styleFilter&&(y=m._defaultStyle.clone(),m._styleFilter(t,y,d,p,g),t.style=y)})),n.features.sort((function(t,e){return t.style&&t.style.lineDash?1:e.style&&e.style.lineDash?-1:0}))),h.featureEach(n,(function(t,e){var i=t.geometry;i&&("Polygon"!=i.type&&"MultiPolygon"!=i.type||x(t))})),b&&b.length&&function(t,e,i,r,n,s){var a=t.canvas,o=t.getImageData(0,0,a.width,a.height),h=document.createElement("canvas");h.width=a.width,h.height=a.height;var l=h.getContext("2d"),u=[];s.map((function(t){l.clearRect(0,0,h.width,h.height),l.beginPath();var s=0;t.map((function(t){var a=e.project(t,i);0==s?l.moveTo(r+a.x,n+a.y):l.lineTo(r+a.x,n+a.y),s++})),l.closePath(),l.fillStyle="rgba(255,255,255,1)",l.fill(),u=l.getImageData(0,0,h.width,h.height).data;for(var a=3;a<u.length;a+=4)u[a]>0&&(o.data[a]=0)})),t.putImageData(o,0,0)}(t,_,s,e,i,b),h.featureEach(n,(function(t,e){var i=t.geometry;i&&("LineString"!=i.type&&"MultiLineString"!=i.type||x(t))}));var M=[];h.featureEach(n,(function(t,e){var i=t.geometry;i&&("Point"!=i.type&&"MultiPoint"!=i.type||("function"!=typeof m.clustering?x(t):M.push(t)))}));var w=[s.xMin,s.yMin,s.xMax,s.yMax];"function"==typeof m.clustering&&(M=m.clustering(M,t,w,d,p,g)),M&&M.length&&M.forEach((function(t,e){x(t)}))}function x(n,a){if(m._styleFilter){if(0==(y=n.style).show)return;v={labelStroke:y.labelStroke,labelStrokeWidth:y.labelStrokeWidth,labelStrokeColor:y.labelStrokeColor,pointSize:y.pointSize,fontSize:y.fontSize,fontFamily:y.fontFamily,color:y.fontColor instanceof r.Color?y.fontColor.toCssColorString():y.fontColor,backgroundColor:y.pointColor instanceof r.Color?y.pointColor.toCssColorString():y.pointColor,pointStyle:y.pointStyle,ringRadius:y.ringRadius,circleLineWidth:y.circleLineWidth,showMarker:y.showMarker,showLabel:y.showLabel,labelOffsetX:y.labelOffsetX,labelOffsetY:y.labelOffsetY,markerSymbol:y.markerImage instanceof Image||y.markerImage instanceof HTMLCanvasElement?y.markerImage:y.markerImageEl}}else y=m._defaultStyle;t.lineWidth=y.lineWidth,y.outlineColor instanceof r.Color?t.strokeStyle=y.outlineColor.toCssColorString():t.strokeStyle=y.outlineColor,y.fillColor instanceof r.Color?t.fillStyle=y.fillColor.toCssColorString():t.fillStyle=y.fillColor,y.lineDash&&t.setLineDash(y.lineDash),t.lineCap=y.lineCap,y.shadowColor&&y.shadowColor instanceof r.Color?t.shadowColor=y.shadowColor.toCssColorString():t.shadowColor=y.shadowColor,t.shadowBlur=y.shadowBlur,t.shadowOffsetX=y.shadowOffsetX,t.shadowOffsetY=y.shadowOffsetY,t.miterLimit=y.miterLimit,t.lineJoin=y.lineJoin;var o=n.geometry;if("Point"==o.type)!function(t,e,i,n,s,a,o,h,l,u){void 0===l&&(l="NAME");var f=Object.assign({pointSize:3,fontSize:9,fontFamily:"courier",color:"rgb(0,0,0)",backgroundColor:"rgb(255,0,0)",pointStyle:"Solid",ringRadius:2,circleLineWidth:1,showMarker:!0,showLabel:!0,labelOffsetX:0,labelOffsetY:0,markerSymbol:void 0},u);t.font=f.fontSize+"px "+f.fontFamily+" bold";var d=a.geometry.coordinates,p=(d[0]-i.xMin)/(i.xMax-i.xMin),g=(d[1]-i.yMax)/(i.yMin-i.yMax),m=p*t.canvas.width,_=g*t.canvas.height;if(f.showMarker||f.showMarker){var y=m+n,v=_+s;if(f.markerSymbol&&(f.markerSymbol instanceof Image||f.markerSymbol instanceof HTMLCanvasElement)){var b=f.markerSymbol.height;y-=f.markerSymbol.width/2,v-=b/2,f.markerSymbol.width&&f.markerSymbol.height&&t.drawImage(f.markerSymbol,y,v)}else y-=f.pointSize,v-=f.pointSize,t.fillStyle=f.backgroundColor,t.beginPath(),t.arc(y,v,f.pointSize,0,2*Math.PI),"Solid"==f.pointStyle?t.fill():"Circle"==f.pointStyle?(t.lineWidth=f.circleLineWidth,t.strokeStyle=f.backgroundColor,t.stroke()):"Ring"==f.pointStyle&&(t.strokeStyle=f.backgroundColor,t.stroke(),t.beginPath(),t.arc(y,v,f.ringRadius,0,2*Math.PI),t.closePath(),t.fill())}if(f.showLabel){var M=a.properties[l];if(M){"string"!=typeof M&&(M=M.toString()),t.fillStyle=f.color;var w=m+n+f.labelOffsetX,x=_+s+f.labelOffsetY,C=c(M=M.trim(),{fill:!0,font:f.fontSize+"px "+f.fontFamily,stroke:f.labelStroke,strokeWidth:f.labelStrokeWidth,strokeColor:"string"==typeof f.labelStrokeColor?r.Color.fromCssColorString(f.labelStrokeColor):f.labelStrokeColor,fillColor:r.Color.fromCssColorString(f.color)}),S=C.height;w-=C.width/2+f.pointSize,x-=S/2+f.pointSize,C.width&&C.height&&t.drawImage(C,w,x)}}t.restore()}(t,0,s,e,i,n,0,0,y.labelPropertyName,v);else if("Polygon"==o.type&&y.fill){var l=h.getCoords(n),u=C(t,_,s,e,i,l,!0,!1);u&&u.map((function(t){t.style=y,b.push(t)}))}else if("MultiPolygon"==o.type&&y.fill){try{h.getCoords(n).map((function(r){var n=C(t,_,s,e,i,r,!0,!1);n&&n.map((function(t){t.style=y,b.push(t)}))}))}catch(t){}}else if("MultiLineString"==o.type)if(n.properties._outline&&!y.outline);else{var f=h.getCoords(n);C(t,_,s,e,i,f,!1,!0),f=null}else if("LineString"==o.type)if(n.properties._outline&&!y.outline);else{var d=h.getCoords(n),p=[d];C(t,_,s,e,i,p,!1,!0),d=null,p=null}}},x.pointIsCrossTile=function(t,e,i,r){if(r&&r.splice(0,r.length),!i.properties)return!0;if(!i.properties.symbol)return!0;var n=i.properties.symbol,s=i.geometry.coordinates,a=(s[0]-e[0])/(e[2]-e[0]),o=(s[1]-e[3])/(e[1]-e[3]),h=a*t.canvas.width,l=o*t.canvas.height,u=h-n.width/2,f=h+n.width/2,c=l-n.height/2,d=l+n.height/2;return u<0||f>t.canvas.width||(c<0||d>t.canvas.height||(r[0]=u,r[1]=c,r[2]=f,r[3]=d,!1))},x.prototype.requestImageSync=function(t,e,i){var n=t+","+e+","+i;if(this.cache&&this.cache[n])return this.cache[n];var s=this._tilingScheme.tileXYToRectangle(t,e,i),a={xMin:r.Math.toDegrees(s.west),yMin:r.Math.toDegrees(s.south),xMax:r.Math.toDegrees(s.east),yMax:r.Math.toDegrees(s.north)},o=this._clipGeojson(s);return o?(this._createCanvas(),this._defaultStyle.backgroundColor||this._context.clearRect(0,0,this._canvas.width,this._canvas.height),this._drawGeojson(this._context,0,0,o,a,this._tileWidth,this._tileHeight,this._fill,this._outline,t,e,i),this._canvas):this._onlyPoint||this._polygonOnly&&this._defaultStyle.fill?S(this._defaultStyle.backgroundColor):void 0},x.prototype._createTileImage=function(t,e,i,n,s){var a=this,o=t+","+e+","+i,h={xMin:r.Math.toDegrees(n.west),yMin:r.Math.toDegrees(n.south),xMax:r.Math.toDegrees(n.east),yMax:r.Math.toDegrees(n.north)};this._state=x.State.CLIPPING,requestAnimationFrame((function(){var l=a._clipGeojson(n);l?r.requestAnimationFrame((function(){a._state=x.State.GEOJSONDRAWING,a._createCanvas(),a._defaultStyle.backgroundColor||a._context.clearRect(0,0,a._canvas.width,a._canvas.height),a._drawGeojson(a._context,0,0,l,h,a._tileWidth,a._tileHeight,a._fill,a._outline,t,e,i),a.cache[o]=a._canvas,a.cache[o].srcJson=l,a.cacheCount++,x._currentTaskCount--,s.resolve(a._canvas),r.requestAnimationFrame((function(){a._state=x.State.COMPELTED}))})):(a._onlyPoint?s.resolve(S(a._defaultStyle.backgroundColor)):s.resolve(void 0),a._state=x.State.COMPELTED,x._currentTaskCount--)}))},x.prototype.clearCache=function(){for(var t in this.cache)this.cache.hasOwnProperty(t)&&(this.cache[t].srcJson=null,delete this.cache[t]);this.cache={},this.cacheCount=0},x.prototype._getTileImage=function(t,e,i,n){var s=r.defer(),a=this,o=t+","+e+","+i;if(a.cacheCount||(a.cacheCount=0),!a.cache||a.cacheCount>a._tileCacheSize){for(var h in a.cache)a.cache.hasOwnProperty(h)&&(a.cache[h].srcJson=null,delete a.cache[h]);a.cache={},a.cacheCount=0}return a.cache[o]?a.cache[o]:x._maxTaskCount<x._currentTaskCount?void 0:(x._currentTaskCount++,a._state=x.State.READY,setTimeout((function(){return a._createTileImage(t,e,i,n,s)}),1),s.promise)},x.prototype.requestImage=function(t,e,i,r){if(this._ready&&this._state==x.State.COMPELTED){if(i<this._minimumLevel)return this._createCanvas(),this._context.clearRect(0,0,this._tileWidth,this._tileHeight),this._canvas;if(i>this._maximumLevel)return S(this._defaultStyle.backgroundColor);var n=this.tilingScheme.tileXYToRectangle(t,e,i);return this._getTileImage(t,e,i,n)}},x.compose=function(t,e,i,n,s){n=n||256,s=s||256,t.sort((function(t,e){return r.defined(t.zIndex)?r.defined(e.zIndex)?t.zIndex-e.zIndex:1:-1}));var a=new r.GeographicTilingScheme,o=r.Rectangle.northwest(e),h=r.Rectangle.southeast(e),l=a.positionToTileXY(o,i),u=a.positionToTileXY(h,i),f=(u.x-l.x+1)*n,c=(u.y-l.y+1)*s,d=document.createElement("canvas");d.width=f,d.height=c;for(var p=d.getContext("2d"),g=0;g<t.length;g++)for(var m=l.x,_=0;m<=u.x;m++,_++)for(var y=l.y,v=0;y<=u.y;y++,v++){var b=_*n,M=v*s,w=t[g];try{var x=w.requestImageSync(m,y,i);x&&x.width&&x.height&&p.drawImage(x,b,M,n,s)}catch(t){console.error(t)}}return d};var k=new r.Rectangle;x.prototype.pickFeatures=function(t,e,i,n,s){var a=this,o=[];if(!this._allowPick||!this._geoJSON)return a._featuresPicked.raiseEvent(a,void 0),o;this.tilingScheme.tileXYToRectangle(t,e,i,k);var l=h.radiansToLength(k.width/256,"kilometers"),u=h.point([r.Math.toDegrees(n),r.Math.toDegrees(s)]),f=this.defaultStyle;if(h.featureEach(this._geoJSON,(function(t){var e=t,i=!1,a=t.geometry;if(a){if(!f.fill||"Polygon"!=a.type&&"MultiPolygon"!=a.type){var c=g(u,t,{units:"kilometers"});if(f.outline&&("Polygon"==a.type||"MultiPolygon"==a.type)||"LineString"==a.type||"MultiLineString"==a.type)i=c<=2*l;else if(f.showMarker&&("Point"==a.type||"MultiPoint"==a.type))switch(f.pointStyle){case"Solid":i=c<=2*f.pointSize;break;case"Ring":case"Circle":i=c<=2*(f.circleLineWidth+f.ringRadius)}}else h.booleanPointInPolygon(u,t)&&(i=!0);if(i){var d=new r.ImageryLayerFeatureInfo;d.data=e,d.description=JSON.stringify(e.properties,null,2),f.labelPropertyName?d.name=e.properties[f.labelPropertyName]:f.centerLabelPropertyName&&(d.name=e.properties[f.centerLabelPropertyName]);var p=e.geometry;if("Point"==p.type||"MultiPoint"==p.type)d.position=new r.Cartographic(n,s);else{var m=h.centroid(e),_=h.getCoords(m);d.position=r.Cartographic.fromDegrees(_[0],_[1])}o.push(d)}}})),o.length){var c=r.defer(),d=new Date;return Promise.all(o).then((function(t){new Date-d<100?setTimeout((function(){a._featuresPicked.raiseEvent(a,t),c.resolve(t)}),100):(a._featuresPicked.raiseEvent(a,t),c.resolve(t))})).catch((function(t){console.error(t),a._featuresPicked.raiseEvent(a,void 0)})),c.promise}return a._featuresPicked.raiseEvent(a,void 0),o},x.prototype.destroy=function(){for(var t in this)this.hasOwnProperty(t)&&delete this[t]}}])}));