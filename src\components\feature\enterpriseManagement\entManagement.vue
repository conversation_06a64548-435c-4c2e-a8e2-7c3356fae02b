<template>
  <div class="enterpriseManagement" v-loading="loading">
    <div class="header">
      <div class="breadcrumb" v-if="showTab && vuexUser.user_type !== 'ent'">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span @click="goEnt()">
              <a-icon type="home" theme="filled" class="icon" /> 企业管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-if="isEnterprise === 'ent'">
            <a-icon
              v-if="!showTab"
              type="home"
              theme="filled"
              class="icon"
              style="margin-right: 10px"
            />企业详情</a-breadcrumb-item
          >
        </a-breadcrumb>
      </div>
      <!-- <div class="enterprise">
        <div class="enterpriseL">
          <div
            class="Grade"
            v-if="safeStatus == '1'"
            style="background: #f65959"
          >
            重大风险
          </div>
          <div
            class="Grade"
            v-if="safeStatus == '2'"
            style="background: #f98e2f"
          >
            较大风险
          </div>
          <div
            class="Grade"
            v-if="safeStatus == '3'"
            style="background: #f9c22f"
          >
            一般风险
          </div>
          <div
            class="Grade"
            v-if="safeStatus == '4'"
            style="background: #2f8ef9"
          >
            低风险
          </div>
          <div class="name">
            <el-tooltip
              class="item"
              effect="dark"
              :content="enterpriseName"
              placement="top-start"
            >
              <el-button type="text" class="botton"
                >{{ enterpriseName }} --{{ safeStatus }}
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="enterpriseR">
          <div>
            <div class="ent"></div>
            <div v-if="ent == '01'">生产</div>
            <div v-else-if="ent == '02'">经营</div>
            <div v-else-if="ent == '03'">使用</div>
            <div v-else-if="ent == '04'">第一类非药品易制毒</div>
            <div v-else></div>
          </div>
          <div>
            <div class="address"></div>
            <div>{{ address ? address : " " }}</div>
          </div>
          <div>
            <div class="name"></div>
            <div>{{ name ? name : " " }}</div>
          </div>
          <div>
            <div class="tel"></div>
            <div>{{ tel ? tel : " " }}</div>
          </div>
        </div>
      </div> -->

      <div class="newEnterprise">
        <div class="newEnterpriseL">
          <div class="titH2">
            <span>{{ enterpriseName }}</span>
          </div>
          <div class="titCon">
            <span>法人：{{ name ? name : " " }}</span>
            &nbsp;&nbsp;&nbsp;&nbsp;<span>{{ tel ? tel : " " }}</span>
            &nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;
            <span>经营：</span>
            <span class="ent"></span>
            <span v-if="ent == '01'">生产</span>
            <span v-else-if="ent == '02'">经营</span>
            <span v-else-if="ent == '03'">使用</span>
            <span v-else-if="ent == '04'">第一类非药品易制毒</span>
            <span v-else></span>
            &nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;
            <span>地址：{{ address ? address : " " }}</span>
          </div>
        </div>
        <div class="newEnterpriseR">
          <div :class="['safeStatus' + riskGrade]"></div>
        </div>
      </div>
    </div>
    <div class="tabs">
      <el-tabs v-model="activeName" @tab-click="handleClick" :key="key">
        <el-tab-pane label="基础信息" name="basicInformationTab">
          <BasicInformation
            :level="level"
            ref="basicInformation"
          ></BasicInformation>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="安全信息" name="safetyInformation">
          <safetyInformation
            :enterpriseId="enterpriseId"
            ref="safetyInformation"
          ></safetyInformation>
        </el-tab-pane>
        <el-tab-pane label="企业风险分析" name="riskAnalysis">
          <riskAnalysis ref="riskAnalysis"></riskAnalysis>
        </el-tab-pane>
        <!-- <el-tab-pane label="风险动态" name="riskDynamics">
          <RiskDynamics ref="riskDynamics"></RiskDynamics>
        </el-tab-pane> -->
        <el-tab-pane v-if="level" label="安全承诺" name="safetyCommitment">
          <SafetyCommitment ref="safetyCommitment" :name="name" />
        </el-tab-pane>
        <el-tab-pane v-if="level" label="视频监控" name="videoInspection">
          <VideoInspection
            ref="videoInspection"
            :showVideo="showVideo"
            :key="videoInspectionKey"
          ></VideoInspection>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="物联监测" name="realTimeMonitoring">
          <RealTimeMonitoring ref="realTimeMonitoring"></RealTimeMonitoring>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="物联报警" name="IotDetection">
          <Lot ref="IotDetection"></Lot>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="报警分析" name="alarmStatistical">
          <Alarm ref="alarmStatistical"></Alarm>
        </el-tab-pane>
        <el-tab-pane
          v-if="level"
          label="设备设施"
          name="equipmentAndFacilities"
        >
          <EquipmentAndFacilities
            ref="equipmentAndFacilities"
          ></EquipmentAndFacilities>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="监测设备" name="monitoringEquipment">
          <MonitoringEquipment ref="monitoringEquipment"></MonitoringEquipment>
        </el-tab-pane>
        <el-tab-pane
          v-if="checkFireFacilitiesVisibility"
          label="消防设施"
          name="fireFacilities"
        >
          <FireFacilities
            ref="fireFacilities"
            :enterpriseId="enterpriseId"
          ></FireFacilities>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="被执法情况" name="lawEnforcement">
          <LawEnforcement ref="lawEnforcement"></LawEnforcement>
        </el-tab-pane>
        <el-tab-pane v-if="level" label="事故案例" name="accidentManagement">
          <AccidentManagement ref="accidentManagement"></AccidentManagement>
        </el-tab-pane>
        <!-- <el-tab-pane v-if="level" label="三维视图" name="threeDimensionalView"
          ><ThreeDimensionalView></ThreeDimensionalView
        ></el-tab-pane> -->
      </el-tabs>
      <div class="to3dMap" @click="to3dMap">
        <el-button type="primary" plain size="small">三维</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import riskAnalysis from "./riskDynamics/riskAnalysis.vue";
import VideoInspection from "./videoInspection/index";
import SafetyCommitment from "./safetyCommitment/index";
import Lot from "./IotDetection/index";
import BasicInformation from "./basicInformation/index";
import RealTimeMonitoring from "./realTimeMonitoring/index";
import ThreeDimensionalView from "./threeDimensionalView/index";
import Alarm from "./alarmStatistical/index";
import EquipmentAndFacilities from "./equipmentAndFacilities/index";
import MonitoringEquipment from "./monitoringEquipment/index";
import LawEnforcement from "./lawEnforcement/index";
import AccidentManagement from "./accidentManagement/index";
import safetyInformation from "./safetyInformation/index";
import { getInformationInfo } from "@/api/entList";
import { createNamespacedHelpers, mapState } from "vuex";
let factoryImage = require("/static/map/factoryImage.json");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import {
  getCimRiskAnalysisCimRiskDetailsList,
  queryCompanyRiskLevel,
} from "@/api/workingAcc";
import FireFacilities from "./fireFacilities/index";
export default {
  name: "entManagement",
  components: {
    riskAnalysis,
    VideoInspection,
    SafetyCommitment,
    Lot,
    BasicInformation,
    RealTimeMonitoring,
    ThreeDimensionalView,
    Alarm,
    EquipmentAndFacilities,
    MonitoringEquipment,
    LawEnforcement,
    AccidentManagement,
    safetyInformation,
    FireFacilities,
  },
  props: ["activeF", "enterpId"],
  data() {
    return {
      riskGrade: "",
      key: "",
      activeName: "videoInspection",
      enterpriseName: "",
      isEnterprise: "",
      showVideo: false,
      enterpriseId: this.enterpId,
      ent: "",
      address: "",
      name: "",
      tel: "",
      level: 1,
      loading: false,
      active: this.$store.state.login.activeName, //this.activeF  父组件没有传值？？？
      safeStatus: "",
      showTab: true,
      videoInspectionKey: "",
      routerName: "",
    };
  },
  methods: {
    to3dMap() {
      console.log(this.BASE_URL, "跳转3d测试");
      let isHttps = location.protocol == "https:"; //互联网环境
      const path = isHttps
        ? "https://" + this.BASE_URL
        : "http://" + this.BASE_URL;
      var tokenBig = this.$store.state.login.user.access_token;
      var enterpId = this.enterpriseId;
      if (this.$store.state.login.user.user_type == "ent") {
        window.open(
          path +
            "/egis/#/enterpriseInfo?type=ent&token=" +
            tokenBig +
            "&enterId=" +
            enterpId
        );
      } else {
        window.open(
          path +
            "/egis/#/enterpriseInfo?token=" +
            tokenBig +
            "&enterId=" +
            enterpId
        );
      }
    },
    handleClick(tab, event) {
      // console.log(tab.name == "IotDetection");
      this.showVideo = tab.name === "videoInspection";
      switch (tab.name) {
        case "basicInformationTab":
          this.$nextTick(() => {
            this.$refs.basicInformation.getData(this.enterpriseId);
          });
          break;
        case "safetyCommitment":
          this.$nextTick(() => {
            // console.log(this.$refs.safetyCommitment);
            this.$refs.safetyCommitment.getData(this.enterpriseId);
          });
          break;
        case "alarmStatistical":
          this.$nextTick(() => {
            this.$refs.alarmStatistical.getData(this.enterpriseId);
            this.$refs.alarmStatistical.getTotalData(this.enterpriseId);
            this.$refs.alarmStatistical.getTime();
          });
          break;
        case "IotDetection":
          this.$nextTick(() => {
            this.$refs.IotDetection.getData(this.enterpriseId);
          });
          break;
        case "realTimeMonitoring":
          this.$nextTick(() => {
            // debugger
            this.$refs.realTimeMonitoring.getData(this.enterpriseId);
            this.$refs.realTimeMonitoring.getSelectData(this.enterpriseId);
            this.$refs.realTimeMonitoring.getStateData(this.enterpriseId);
          });

          break;
        case "videoInspection":
          this.videoInspectionKey = new Date() + "key";
          this.$nextTick(() => {
            this.$refs.videoInspection.getData(this.enterpriseId);
            this.$refs.videoInspection.getVideoNum(this.enterpriseId);
            this.$refs.videoInspection.initVideoNew();
            // this.$refs.videoInspection.initVideo()
            // this.$refs.videoInspection.initializationVideo();
          });
          break;
        case "monitoringEquipment":
          this.$nextTick(() => {
            this.$refs.monitoringEquipment.getData(this.enterpriseId);
          });
          break;
        case "equipmentAndFacilities":
          this.$nextTick(() => {
            this.$refs.equipmentAndFacilities.getData(this.enterpriseId);
          });
          break;
        case "riskAnalysis":
          this.$nextTick(() => {
            this.$refs.riskAnalysis.getData(this.enterpriseId);
          });
          break;
        case "lawEnforcement":
          this.$nextTick(() => {
            console.log(this.enterpriseId);
            this.$refs.lawEnforcement.getData(this.enterpriseId);
          });
          break;
        case "accidentManagement":
          this.$nextTick(() => {
            this.$refs.accidentManagement.getAccidentList(this.enterpriseId);
          });
        case "safetyInformation":
          this.$nextTick(() => {
            this.$refs.safetyInformation.getData(this.enterpriseId);
          });
        default:
          break;
      }
    },
    goEnt() {
      this.enterpriseName = "";
      this.tel = "";
      this.address = "";
      this.ent = "";
      this.name = "";
      this.level = "";
      this.safeStatus = "";
      this.key = new Date().getTime() + "_key";
      this.$emit("type", "gov");
    },
    getData() {
      //this.active = this.activeF;
      this.active = this.$store.state.login.activeName;
      // this.enterpriseId = this.enterpId
      if (this.vuexUser.user_type === "ent") {
        const res = this.enterData;
        this.setTab(res);
      } else {
        this.getInformationInfoFun();
      }
    },
    setTab(res) {
      console.log(res, "这是获取企业基本信息safeStatus");
      this.enterpriseName = res.enterpName;
      this.tel = res.respTel;
      this.address = res.districtName;
      this.ent = res.enterpriseType;
      this.name = res.respper;
      this.level = res.level;
      this.safeStatus = res.safeStatus;
      queryCompanyRiskLevel({
        enterpId: this.enterpriseId,
        // distCode: res.districtCode,
        // entName: res.enterpName,
        // majorHazardLevel: "",
        // type: res.safeStatus,
        // current: 1,
        // size: 10,
      }).then((res) => {
        if (res.data.status == 200) {
          console.log(res.data.data, "这是获取风险动态riskGrade");
          this.riskGrade = res.data.data.riskRank || "";
        }
      });
      this.$nextTick(() => {
        if (this.level === null) {
          this.activeName = "basicInformationTab";
          this.$refs.basicInformation.getData(this.enterpriseId);
        } else {
          this.showVideo = this.active === "videoInspection";

          console.log(this.active, "执行了啥1111111111111111111111111");
          switch (this.active) {
            case "basicInformationTab":
              this.activeName = this.active;
              this.$refs.basicInformation.getData(this.enterpriseId);
              break;
            case "safetyCommitment":
              this.activeName = this.active;
              this.$refs.safetyCommitment.getData(this.enterpriseId);
              break;
            case "alarmStatistical":
              this.activeName = this.active;
              this.$refs.alarmStatistical.getData(this.enterpriseId);
              this.$refs.alarmStatistical.getTotalData(this.enterpriseId);
              break;
            case "IotDetection":
              this.activeName = this.active;
              this.$refs.IotDetection.getData(this.enterpriseId);
              break;
            case "realTimeMonitoring":
              // debugger
              this.activeName = this.active;
              this.$refs.realTimeMonitoring.getData(this.enterpriseId);
              this.$refs.realTimeMonitoring.getSelectData(this.enterpriseId);
              this.$refs.realTimeMonitoring.getStateData(this.enterpriseId);
              break;
            case "monitoringEquipment":
              this.activeName = this.active;
              this.$refs.monitoringEquipment.getData();
              break;
            case "equipmentAndFacilities":
              this.activeName = this.active;
              this.$refs.equipmentAndFacilities.getData();
              break;
            case "videoInspection":
              console.log(this.active);
              this.videoInspectionKey = new Date() + "key";
              this.activeName = this.active;
              this.$nextTick(() => {
                this.$refs.videoInspection.getData(this.enterpriseId);
                this.$refs.videoInspection.getVideoNum(this.enterpriseId);
                this.$refs.videoInspection.initVideoNew();

                // this.$refs.videoInspection.initializationVideo();
              });
              break;
            //riskDynamics
            case "riskAnalysis":
              this.activeName = this.active;
              this.$refs.riskAnalysis.getData(this.enterpriseId);
              break;
            case "":
              this.activeName = this.$store.state.login.activeName;
              this.isEnterprise = "ent";
              this.$refs.basicInformation.getData(this.enterpriseId);
              break;
            default:
              this.activeName = this.active;
              this.isEnterprise = "ent";
              this.$refs.basicInformation.getData(this.enterpriseId);
              break;
          }
        }
        setTimeout(() => {
          this.loading = false;
        }, 500);
      });
    },
    getInformationInfoFun() {
      //正式服一定要放开
      this.loading = true;
      getInformationInfo(this.enterpriseId).then((res) => {
        this.setTab(res.data.data);
      });
    },
  },
  computed: {
    ...mapStateControler({
      entModelName: (state) => state.entModelName,
    }),
    ...mapStateLogin({
      vuexUser: (state) => state.user,
    }),
    ...mapState({
      enterData: (state) => state.login.enterData,
    }),
    // 检查是否显示消防设施tab
    checkFireFacilitiesVisibility() {
      // 检查当前企业ID是否在factoryImage.json中
      const enterpriseExists = Object.prototype.hasOwnProperty.call(
        factoryImage,
        this.enterpriseId
      );

      console.log("检查消防设施显示条件:", {
        enterpriseId: this.enterpriseId,
        enterpriseExists: enterpriseExists,
        factoryImageKeys: Object.keys(factoryImage),
      });

      return enterpriseExists;
    },
  },
  watch: {
    enterData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.enterpriseId = newVal.enterpId;
        }
      },
      deep: true,
      immediate: true,
    },
    enterpId: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.enterpriseId = newVal;
        }
      },
      deep: true,
      immediate: true,
    },
    entModelName: {
      handler(newVal, from) {
        this.showTab = !newVal;
      },
      deep: true,
      immediate: true,
    },
  },
  created() {
    this.activeName = this.$store.state.login.activeName;
  },
  destroyed() {
    // this.$store.commit("login/updataActiveName", "basicInformationTab");
    this.$store.commit("login/updataActiveName", this.activeName);
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tabs__item {
  font-size: 16px;
  font-weight: bold;
}
.newEnterprise {
  background: url("../../../../static/img/riskbg.png") no-repeat top;
  background-size: 100% 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 0 20px;
  color: #fff;
  min-height: 100px;
  .titH2 span {
    color: #fff;
    text-align: left;
    font-size: 20px;
    font-weight: bold;
    display: inline-block;
    padding: 0 0 5px;
  }
  button.el-button.el-tooltip.botton.item.el-button--text {
    text-align: left;
  }
  .safeStatusnull,
  .safeStatus {
    background: url("../../../../static/img/蓝-低风险.png") no-repeat top right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus1 {
    background: url("../../../../static/img/红-重大风险.png") no-repeat top
      right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus2 {
    background: url("../../../../static/img/橙-较大风险.png") no-repeat top
      right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus3 {
    background: url("../../../../static/img/黄-一般风险.png") no-repeat top
      right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
  .safeStatus4 {
    background: url("../../../../static/img/蓝-低风险.png") no-repeat top right;
    height: 100px;
    width: 159px;
    background-size: 100%;
  }
}
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
    .enterprise {
      display: flex;
      justify-content: space-between;
    }
    .enterpriseL {
      // width: 500px;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-left: 10px;
      .Grade {
        color: #fff;
        padding: 0px 5px;
        margin-right: 10px;
        font-size: 14px;
        float: right;
        width: 93px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 4px;
      }
      .name {
        .botton {
          height: 42px;
          font-size: 20px;
          flex-wrap: 600;
          color: #000;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          text-align: left;
        }
      }
    }
    .enterpriseR {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 650px;
      height: 40px;
      margin-right: 15px;
      background: #f3f8fd;
      border: 1px solid #f1f5fa;
      border-radius: 20px;
      & > div {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25%;
        border-right: 1px #cfdee9 solid;
        // height: 20px;
      }
      & > div:nth-last-of-type(1) {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25%;
        border-right: 0px #cfdee9 solid;
      }
      .ent {
        background: url("../../../../static/icon/jy.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .address {
        background: url("../../../../static/icon/address.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .name {
        background: url("../../../../static/icon/name.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .tel {
        background: url("../../../../static/icon/tel.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
    }
  }
  .tabs {
    padding-top: 10px;
    background-color: #fff;
    position: relative;
    .to3dMap {
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      cursor: pointer;
      margin-top: 8px;
    }
  }
}
</style>
