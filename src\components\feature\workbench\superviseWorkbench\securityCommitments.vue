<template>
  <div style="height: 100%">
    <div class="securityCommitments" v-loading="loadingBox">
      <div class="header">
        <div class="title">安全承诺</div>
        <div class="more" @click="toUrl()">更多</div>
      </div>

      <!-- 保持原有的统计信息部分 -->
      <div class="container" v-if="!loadingBox">
        <div class="warning-title">
          <img
            style="height: 20px"
            src="../../../../../static/img/assets/img/title-two.png"
            alt=""
          />
          已承诺企业
          <div class="warning-num" @click="openDialog(6)">
            <span class="num-text">{{ SafetyListData.promiseNum }}</span>
            <span class="unit">家</span>
          </div>
        </div>
        <div class="warning-content">
          <div class="warning-box warning-content-icon"></div>
          <div class="warning-box warning-content1">
            <div>应承诺企业</div>
            <div class="num num1" @click="openDialog(0)">
              {{ SafetyListData.shouldPromiseNum }}
            </div>
          </div>
          <div class="warning-box warning-content2">
            <div>未承诺企业</div>
            <div class="num num2" @click="openDialog(5)">
              {{ SafetyListData.notPromiseNum }}
            </div>
          </div>
          <div class="warning-box warning-content3">
            <div>承诺率</div>
            <div class="num num3">
              {{ SafetyListData.promiseRate + "%" }}
            </div>
          </div>
        </div>
      </div>

      <!-- 添加 tab 切换 -->
      <el-tabs v-model="activeTab" class="commitment-tabs">
        <!-- 承诺风险等级分布 tab -->
        <el-tab-pane label="承诺风险等级分布" name="commitment">
          <div class="chart-container">
            <div id="riskLevelChart" class="risk-chart"></div>
          </div>
        </el-tab-pane>

        <!-- 特殊作业 tab -->
        <el-tab-pane label="特殊作业" name="special">
          <div class="work-list">
            <div
              class="work-item"
              v-for="(item, index) in workTypes"
              :key="index"
            >
              <span class="work-name">{{ item.name }}</span>
              <span class="work-value">{{
                specialWorkData[item.key] || 0
              }}</span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <div class="dialog">
        <el-dialog
          :title="title"
          :visible.sync="show"
          width="1350px"
          top="5vh"
          @close="closeDialog()"
          v-dialogDrag
          :key="demoKey"
          :close-on-click-modal="false"
        >
          <div class="seach-part">
            <div class="l">
              <el-cascader
                size="mini"
                placeholder="请选择行政区划"
                :options="district"
                v-model="districtVal"
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                clearable
                :show-all-levels="true"
                v-if="isShowDist"
              ></el-cascader>
              <el-select
                v-model="majorHazardLevel"
                placeholder="危险源等级"
                size="mini"
                multiple
                clearable
                style="width: 300px"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-input
                placeholder="请输入企业名称"
                v-model.trim="entName"
                size="mini"
                clearable
              >
              </el-input>
              <el-button
                type="primary"
                size="mini"
                class="btn"
                @click="getEntData"
                >查询</el-button
              >
              <CA-button
                type="primary"
                size="mini"
                class="btn"
                plain
                @click="exportExcel"
                >导出</CA-button
              >
            </div>
          </div>

          <el-table
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            v-loading="loading"
            :data="tableData.records"
            style="width: 100%"
            border
            @select="select"
            @select-all="select"
          >
            <el-table-column
              type="selection"
              width="50"
              align="center"
            ></el-table-column>
            <el-table-column label="序号" width="50" align="center">
              <template slot-scope="{ $index }">
                <span>{{ $index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="entName"
              label="单位名称"
              width="300"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span
                  @click="goEnt(scope.row)"
                  style="color: #3977ea; cursor: pointer"
                  >{{ scope.row.entName }}</span
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="areaName"
              label="区划"
              align="center"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="majorHazardLevel"
              label="重大危险源企业等级"
              align="center"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.majorHazardLevel == 1"
                  >一级重大危险源</span
                >
                <span v-if="scope.row.majorHazardLevel == 2"
                  >二级重大危险源</span
                >
                <span v-if="scope.row.majorHazardLevel == 3"
                  >三级重大危险源</span
                >
                <span v-if="scope.row.majorHazardLevel == 4"
                  >四级重大危险源</span
                >
              </template>
            </el-table-column>
            <el-table-column prop="promiseFlag" label="是否承诺" align="center">
              <template slot-scope="scope">
                <span>{{ scope.row.promiseFlag == 1 ? "是" : "否" }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="riskGrade"
              label="承诺风险等级"
              align="center"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.riskGrade == 1">高风险</span>
                <span v-if="scope.row.riskGrade == 2">较大风险</span>
                <span v-if="scope.row.riskGrade == 3">一般风险</span>
                <span v-if="scope.row.riskGrade == 4">低风险</span>
              </template>
            </el-table-column>
            <el-table-column prop="commiteDate" label="承诺时间" align="center">
            </el-table-column>

            <el-table-column prop="urgeStatus" label="督促" align="center">
              <template slot-scope="scope">
                <div v-if="scope.row.urgeStatus == 0">
                  <el-button
                    style="padding: 5px 10px"
                    type="primary"
                    @click="addUrgeFn(scope.row.entId)"
                    >督促</el-button
                  >
                </div>
                <div class="disabledStyle" v-else>
                  <el-button style="padding: 5px 10px" disabled
                    >已督促</el-button
                  >
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </el-dialog>
      </div>

      <el-dialog
        :title="riskDialogTitle"
        :visible.sync="showRiskDialog"
        width="400px"
      >
        <el-table :data="riskEnterpriseList" style="width: 100%">
          <el-table-column prop="enterpName" label="企业名称" />
        </el-table>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getSafetyListData,
  getentArr,
  getSafetyPromiseExportPromiseDetailsToExcel,
  addUrge,
  getSpecialWorkStatistics,
  getCommitmentRiskLevel,
} from "@/api/workingAcc";
import dayjs from "dayjs";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");

export default {
  //import引入的组件
  components: {},
  data() {
    return {
      currentPage: 1,
      show: false,
      options: [
        { value: "1", label: "一级" },
        { value: "2", label: "二级" },
        { value: "3", label: "三级" },
        { value: "4", label: "四级" },
      ],
      majorHazardLevel: ["1", "2", "3", "4"],
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      tableData: {},
      loading: false,
      selection: [],
      SafetyListData: {},
      entName: "",
      title: "",
      loadingBox: false,
      demoKey: 0,
      type: null,
      activeTab: "commitment",
      workTypes: [
        { name: "特级动火作业", key: "firesNumber" },
        { name: "一级动火作业", key: "fire1Number" },
        { name: "二级动火作业", key: "fire2Number" },
        { name: "进入受限空间", key: "spaceworkNumber" },
        { name: "临时用电", key: "electricityworkNumber" },
        { name: "吊装作业", key: "liftingworkNumber" },
        { name: "盲板抽堵", key: "blindplateNumber" },
        { name: "高处作业", key: "highworkNumber" },
        { name: "动土作业", key: "soilworkNumber" },
        { name: "断路作业", key: "roadworkNumber" },
      ],
      specialWorkData: {},
      riskChart: null,
      showRiskDialog: false,
      riskDialogTitle: '',
      riskEnterpriseList: [],
    };
  },
  //方法集合
  methods: {
    // 删除未使用的方法
    toUrl() {
      let url = "";
      if (this.activeTab === "special") {
        url = "/riskAssessment/specialWork";
      } else if (this.activeTab === "commitment") {
        url = "/riskAssessment/safetyCommitment";
      }
      this.$router.push(url);
    },

    closeDialog() {
      this.currentPage = 1;
      this.entName = "";
      this.majorHazardLevel = ["1", "2", "3", "4"];
      this.tableData = {};
      this.districtVal = this.$store.state.login.userDistCode;
      this.demoKey =
        "demo-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
    },

    openDialog(type) {
      this.show = true;
      this.majorHazardLevel = ["1", "2", "3", "4"];
      this.currentPage = 1;
      this.type = type;
      this.getEntData();
    },

    search() {
      this.loadingBox = true;
      getSafetyListData({
        level: this.majorHazardLevel.join(","),
        current: this.currentPage,
        distCode: this.districtVal,
        size: 100,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loadingBox = false;
          this.SafetyListData = res.data.data.records[0];
        }
      });
    },

    handleCurrentChange() {
      this.getEntData();
    },

    select(selection) {
      this.selection = selection.map((item) => item.entId);
    },

    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.entId);
    },

    getEntData() {
      this.loading = true;
      const titles = {
        0: "安全承诺分析-应承诺企业列表",
        1: "安全承诺分析-重大风险企业列表",
        2: "安全承诺分析-较大风险企业列表",
        3: "安全承诺分析-一般风险企业列表",
        4: "安全承诺分析-较低风险企业列表",
        5: "安全承诺分析-未承诺企业列表",
        6: "安全承诺分析-已承诺企业列表",
      };
      this.title = titles[this.type];

      getentArr({
        distCode: this.districtVal,
        entName: this.entName,
        majorHazardLevel: this.majorHazardLevel.join(","),
        type: this.type,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data;
        }
      });
    },

    addUrgeFn(entId) {
      this.$confirm("确认督促该企业？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      }).then(() => {
        addUrge({
          enterpId: entId,
          urgeDate: dayjs().format("YYYY-MM-DD"),
        }).then((res) => {
          if (res) {
            this.$message.success("已发送督促！");
            this.getEntData();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },

    exportExcel() {
      getSafetyPromiseExportPromiseDetailsToExcel({
        distCode: this.districtVal,
        entIdList: this.selection.length <= 1 ? null : this.selection,
        entName: this.entName,
        majorHazardLevel: this.majorHazardLevel.join(","),
        type: this.type,
      }).then((response) => {
        if (response.status == 200) {
          this.$message.success("导出成功");
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        const filename =
          "安全承诺趋势分析" + dayjs().format("YYYY-MM-DD") + ".xls";
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(() => {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    //设置echart大小
    setEchart(eleName) {
      var echartsWarp = document.getElementById(eleName);
      var resizeWorldMapContainer = function () {
        //用于使chart自适应高度和宽度,通过窗体高宽计算容器高宽
        echartsWarp.style.width = "100%";
        echartsWarp.style.height = "60%";
      };
      resizeWorldMapContainer(); //设置容器高宽
      var myChart = this.$echarts.init(echartsWarp);
      window.onresize = function () {
        //用于使chart自适应高度和宽度
        resizeWorldMapContainer(); //重置容器高宽
        myChart.resize();
      };
    },
    // 更新饼图数据
    updateRiskChart() {
      const riskData = [
        {
          value: this.SafetyListData.riskOne || 0,
          name: "一级风险",
          itemStyle: { color: "#FF4444" },
        },
        {
          value: this.SafetyListData.riskTwo || 0,
          name: "二级风险",
          itemStyle: { color: "#FF9A42" },
        },
        {
          value: this.SafetyListData.riskThree || 0,
          name: "三级风险",
          itemStyle: { color: "#FFD159" },
        },
        {
          value: this.SafetyListData.riskFour || 0,
          name: "四级风险",
          itemStyle: { color: "#3B7FEF" },
        },
      ];

      const option = {
        tooltip: {
          trigger: "item",
          formatter: "{b} {c}家 {d}%",
          textStyle: {
            fontSize: 14,
          },
        },
        legend: {
          orient: "vertical",
          right: "5%",
          top: "5%",
          itemGap: 25,
          itemWidth: 12,
          itemHeight: 12,
          formatter: function (name) {
            const item = riskData.find((d) => d.name === name);
            return `${name}  ${item.value}家  ${Math.round(
              (item.value / 14) * 100
            )}%`;
          },
          textStyle: {
            color: "#333",
            fontSize: 16,
            lineHeight: 20,
            rich: {
              value: {
                width: 60,
                align: "right",
              },
            },
          },
        },
        graphic: {
          elements: [
            {
              type: "text",
              left: "22%",
              top: "33%",
              style: {
                text: "风险\n承诺",
                textAlign: "center",
                fill: "#333",
                fontSize: 22,
                lineHeight: 24,
              },
            },
          ],
        },
        series: [
          {
            type: "pie",
            radius: ["45%", "70%"],
            center: ["25%", "40%"],
            avoidLabelOverlap: false,
            label: {
              show: false,
            },
            labelLine: {
              show: false,
            },
            data: riskData,
          },
        ],
      };
      this.riskChart && this.riskChart.setOption(option);
      // legend点击弹窗
      if (this.riskChart) {
        this.riskChart.off('legendselectchanged');
        this.riskChart.on('legendselectchanged', (params) => {
          const riskName = params.name;
          // 强制所有legend项都为true，防止图上模块消失
          const selected = {};
          ['一级风险', '二级风险', '三级风险', '四级风险'].forEach(name => {
            selected[name] = true;
          });
          this.riskChart.setOption({ legend: { selected } });
          this.showRiskEnterpriseDialog(riskName);
        });
      }
    },

    // 初始化饼图
    initRiskChart() {
      if (document.getElementById("riskLevelChart")) {
        this.riskChart = this.$echarts.init(
          document.getElementById("riskLevelChart")
        );
        this.updateRiskChart();
      }
    },

    // 获取特殊作业数据
    getSpecialWorkData() {
      // TODO: 调用接口获取特殊作业数据
      const params = {
        distCode: this.districtVal,
        startDate: dayjs().format("YYYY-MM-DD"),
      };
      getSpecialWorkStatistics(params).then((res) => {
        this.specialWorkData = res.data.data[0];
      });
      //   this.specialWorkData = {
      //     total: 500,
      //     notPromise: 0,
      //     rate: 100,
      //     fireSpecial: 500,
      //     fireFirst: 500,
      //     fireSecond: 500,
      //     limitedSpace: 500,
      //     tempElectric: 500,
      //     lifting: 500,
      //     blindPlate: 500,
      //     highAltitude: 500,
      //     earthwork: 500,
      //     roadwork: 500,
      //   };
    },

    showRiskEnterpriseDialog(riskName) {
      this.riskDialogTitle = `${riskName}`;
      let params;
      if(riskName === '一级风险'){
        params = {
          list: this.SafetyListData.hubeiRiskOneList
        }
      }else if( riskName === '二级风险'){
        params = {
          list: this.SafetyListData.hubeiRiskTwoList
        }
      }else if( riskName === '三级风险'){
        params = {
          list: this.SafetyListData.hubeiRiskThreeList
        }
      }else if( riskName === '四级风险'){
         params = {
          list: this.SafetyListData.hubeiRiskFourList
        }
      }
      if( params.list && params.list.length > 0){
        getCommitmentRiskLevel(params).then((res) => {
          if(res.data.code == 0){
            this.riskEnterpriseList = res.data.data;
            this.showRiskDialog = true;
          }
        });
      }else{
        this.riskEnterpriseList = [];
        this.$message.warning('暂无数据');
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.search();
    this.initRiskChart();
    this.getSpecialWorkData();
  },
  computed: {
    ...mapStateLogin({
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict(newVal) {
      this.district = newVal;
    },
    SafetyListData: {
      handler() {
        this.$nextTick(() => {
          this.updateRiskChart();
        });
      },
      deep: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.securityCommitments {
  width: 100%;
  height: 100%;
  border: 1px solid #d8e0ee;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  position: relative;
  background-color: #fff;

  .moreStyleBox {
    width: 100%;
    height: 60%;

    .echartBox {
      position: relative;
      .bgText {
        text-align: center;
        position: absolute;
        top: 0;
        left: 50%;
        top: 50%;
        margin-top: -30px;
        margin-left: -20px;
        font-size: 20px;
        span {
          display: block;
        }
      }
    }
    .echarts {
      width: 300px !important;
      height: 120px !important;
      margin: 0 auto;
    }
  }
  .header {
    width: 95%;
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .container {
    width: 95%;
    height: 138px;
    display: flex;
    flex-direction: column;
    .warning-title {
      width: 100%;
      height: 52px;
      font-family: PingFang SC;
      font-weight: bold;
      font-size: 16px;
      color: #333333;
      line-height: 54px;
      display: flex;
      align-items: center; /* 垂直居中对齐 */
      .warning-num {
        margin-left: auto; /* 将元素推向右侧 */
        cursor: pointer; /* 鼠标悬停时显示指针手势 */
      }
    }
    .warning-title img {
      margin-right: 8px; /* 图片与文本之间的间距 */
    }
    .warning-content {
      display: flex;
      width: 100%;
      height: 85px;
      justify-content: space-between;
      border-radius: 6px;
      border: 1px solid #d8edff;
      background: url(../../../../../static/img/assets/img/promise.png)
        no-repeat #f7faff;
      background-size: cover;
      position: relative;
      .warning-box {
        margin-top: 5px;
        width: 25%;
        height: 72px;
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        .num {
          font-family: PingFang SC;
          font-weight: bold;
          font-size: 20px;
          color: #333333;
        }
        .num1 {
          position: absolute;
          left: 20%;
          top: 30px;
          cursor: pointer;
        }
        .num2 {
          position: absolute;
          left: 24%;
          top: 30px;
          cursor: pointer;
        }
        .num3 {
          color: #27c68f;
          position: absolute;
          left: 32%;
          top: 30px;
        }
      }
      .warning-content-icon {
        position: absolute;
        left: 20px;
        top: 20px;
      }
      .warning-content1 {
        position: absolute;
        left: 20%;
        top: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
      .warning-content2 {
        position: absolute;
        left: 46%;
        top: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
      .warning-content3 {
        position: absolute;
        left: 72%;
        top: 10px;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #333333;
      }
    }
    .left {
      background: url(../../../../../static/img/entNumBg.png) no-repeat;
      background-size: cover;
      width: 110px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        color: #333333;
        font-size: 14px;
      }
      .num {
        color: #5e86b4;
        font-size: 28px;
        text-decoration: underline;
        cursor: pointer;
        font-weight: 600;
      }
    }
    .right {
      width: calc(100% - 110px);
      height: 100%;
      padding-top: 20px;
      > div {
        width: 50%;
        height: 40%;
        float: left;
        display: flex;
        align-items: center;
        .title {
          color: #666666;
          font-size: 14px;
          margin-left: 10%;
          margin-right: 10%;
        }
        .num {
          font-weight: 600;
          font-size: 18px;
          text-decoration: underline;
          cursor: pointer;
        }
        .num1 {
          font-weight: 600;
          font-size: 18px;
        }
      }
    }
  }

  .dialog {
    .inputBox {
      width: 900px;
      display: flex;
      justify-content: flex-start;

      .input {
        width: 200px;
      }
      > * {
        margin-right: 15px;
      }
    }
    .table {
      margin-top: 10px;
    }
    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      display: flex;
      justify-content: flex-start;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      > .btn {
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
.securityCommitments2 {
  width: 100%;
  height: 100%;
  border: 1px solid #d8e0ee;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  position: relative;
  margin: 15px 0 0 0;
  background-color: #fff;
  .header {
    width: 95%;
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .moreStyleBoxR {
    display: flex;
    flex-wrap: wrap;
    width: 95%;
    margin-top: 13px;
    // justify-content: space-between;
    p {
      font-size: 20px;
      cursor: pointer;
      width: 30%;
      text-align: center;
      margin: 0;
      background: #3977ea;
      margin: 0 10px 10px 0;
      color: #fff;
    }
    p:hover {
      color: #f1d650;
    }
  }
}
.commitment-tabs {
  width: 95%;
  // margin-top: 20px;

  // 新增tab标签字体样式
  /deep/ .el-tabs__item {
    font-size: 16px !important;
    font-weight: bold !important;
    color: #333;
  }

  .chart-container {
    height: 200px;
    position: relative;

    .risk-chart {
      width: 100%;
      height: 100%;
    }
  }

  .work-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0px;
    padding-bottom: 10px;
    max-height: 200px;

    .work-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 4px 25px;
      margin: 0 15px;
      background: #fff;
      border-bottom: 1px solid #e4e7ed;
      border-radius: 4px;
      max-height: 45px;

      .work-name {
        color: #333;
        position: relative;
        &::before {
          content: "";
          display: block;
          width: 5px;
          height: 5px;
          background: #409eff;
          border-radius: 50%;
          position: absolute;
          left: -15px;
          top: 50%;
          transform: translateY(-50%);
        }
      }

      .work-value {
        font-size: 20px;
        font-weight: bold;
        // color: #409eff;
      }
    }
  }
}
</style>
