<template>
  <div class="securityFill">
    <component :is="componentStr"></component>
  </div>
</template>

<script>
import enterprise from "./enterprise/index";
import home from "./Home/index";
export default {
  name: "securityFill",
  //import引入的组件
  components: {
    enterprise,
    home,
  },
  data() {
    return {
      componentStr: "enterprise",
    };
  },
  //方法集合
  methods: {
    url() {
    //   console.log(this.$route.path.indexOf("/enterprise/"));
      if (this.$route.path.indexOf("/enterprise/") == -1) {
        this.componentStr = "home";
      } else {
        this.componentStr = "enterprise";
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.url();
  },
  watch: {
    componentStr: {},
  },
};
</script>
<style lang="scss" scoped>
</style>