<template>
  <div>
    <div class="work-heard">
      <h2>视频监控</h2>
      <span @click="goEnt">更多</span>
    </div>
    <div class="video-select">
      <!-- <el-select
            v-model="cameraIndexCode"
            placeholder="选择设备"
            size="mini"
            style="width: 100%;margin-bottom:15px;"
            placement="bottom"
            @change="changeCode"
          >
            <el-option
              v-for="item in options"
              :key="item.monitornum"
              :label="item.monitorname"
              :value="item.monitornum"
            >
            </el-option>
          </el-select> -->
      <div class="video-box" id="video-box">
        <div id="playWnd" class="playWnd"></div>
        <!-- <video width="600" height="400" controls='controls' preload="auto">
                    <source src="rtsp://**************:554/openUrl/QwH83Di">
                </video> -->
      </div>
    </div>
  </div>
</template>
<script>
import { getVideoList } from "@/api/entList";
export default {
  components: {},
  data() {
    return {
      enterpId: "",
      cameraIndexCode: "",
      sign: "0",
      options: [],

      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
    };
  },
  methods: {
    // getData(id){
    //   this.enterpId = id;
    // },
    goEnt() {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", this.enterpId);
      this.$store.commit("controler/updateEntModelName", "videoInspection");
    },
    changeCode(val) {
      this.yulan(val);
    },
    getData(enterpriseId) {
      this.enterpId = enterpriseId;
      getVideoList({
        enterpId: this.enterpId,
        sign: this.sign,
      }).then((res) => {
        if (res.data.code == 0) {
          this.options = res.data.data;
          this.cameraIndexCode = this.options[0].monitornum;
        }
      });
    },
    // 创建播放实例
    initPlugin() {
      const that = this;
      that.oWebControl = new WebControl({
        szPluginContainer: "playWnd", // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15909,
        szClassId: "23BF3B0A-2C56-4D97-9C03-0CB103AA8F11", // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: function () {
          // 创建WebControl实例成功
          if (that.oWebControl == null) {
            that.initPlugin();
            return;
          }
          that.oWebControl
            .JS_StartService("window", {
              // WebControl实例创建成功后需要启动服务
              dllPath: "./VideoPluginConnect.dll", // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              function () {
                // 启动插件服务成功
                that.oWebControl.JS_SetWindowControlCallback({
                  // 设置消息回调
                  cbIntegrationCallBack: that.cbIntegrationCallBack,
                });

                that.oWebControl
                  .JS_CreateWnd("playWnd", that.videoWidth, that.videoHight)
                  .then(function () {
                    //JS_CreateWnd创建视频播放窗口，宽高可设定
                    that.init(); // 创建播放实例成功后初始化
                  });
              },
              function () {
                // 启动插件服务失败
              }
            );
        },
        cbConnectError: function () {
          // 创建WebControl实例失败
          that.oWebControl = null;
          $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
          window.WebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
          that.initCount++;
          if (that.initCount < 3) {
            setTimeout(function () {
              that.initPlugin();
            }, 3000);
          } else {
            // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
            let host = "http://************:9080/file/image/VideoWebPlugin.exe";
            $("#playWnd").html(
              "插件启动失败，请检查插件是否安装！是否安装:" +
                "<a href=" +
                host +
                ' download="VideoWebPlugin.exe">点击下载</a>'
            );
          }
        },
        cbConnectClose: function (bNormalClose) {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log("cbConnectClose");
          that.oWebControl = null;
        },
      });
    },

    // 设置窗口控制回调
    // setCallbacks() {
    //     oWebControl.JS_SetWindowControlCallback({
    //         cbIntegrationCallBack: cbIntegrationCallBack
    //     });
    // },

    // 推送消息
    cbIntegrationCallBack(oData) {
      // window.showCBInfo(JSON.stringify(oData.responseMsg));
    },

    //初始化
    init() {
      const that = this;
      that.getPubKey(function () {
        ////////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
        var appkey = "29553988"; //综合安防管理平台提供的appkey，必填
        var secret = that.setEncrypt("NtJZ6GjAj5RbHiqaZXmr"); //综合安防管理平台提供的secret，必填
        var ip = "**************"; //综合安防管理平台IP地址，必填
        var playMode = 0; //初始播放模式：0-预览，1-回放
        var port = 1443; //综合安防管理平台端口，若启用HTTPS协议，默认443
        var snapDir = "D:\\SnapDir"; //抓图存储路径
        var videoDir = "D:\\VideoDir"; //紧急录像或录像剪辑存储路径
        var layout = "1x1"; //playMode指定模式的布局
        var enableHTTPS = 1; //是否启用HTTPS协议与综合安防管理平台交互，是为1，否为0
        var encryptedFields = "secret"; //加密字段，默认加密领域为secret
        var showToolbar = 1; //是否显示工具栏，0-不显示，非0-显示
        var showSmart = 1; //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        var buttonIDs =
          "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769"; //自定义工具条按钮
        ////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////

        that.oWebControl
          .JS_RequestInterface({
            funcName: "init",
            argument: JSON.stringify({
              appkey: appkey, //API网关提供的appkey
              secret: secret, //API网关提供的secret
              ip: ip, //API网关IP地址
              playMode: playMode, //播放模式（决定显示预览还是回放界面）
              port: port, //端口
              snapDir: snapDir, //抓图存储路径
              videoDir: videoDir, //紧急录像或录像剪辑存储路径
              layout: layout, //布局
              enableHTTPS: enableHTTPS, //是否启用HTTPS协议
              encryptedFields: encryptedFields, //加密字段
              showToolbar: showToolbar, //是否显示工具栏
              showSmart: showSmart, //是否显示智能信息
              buttonIDs: buttonIDs, //自定义工具条按钮
            }),
          })
          .then(function (oData) {
            // that.oWebControl.JS_Resize(1000, 600);  // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题

            console.log("init成功！");
            that.oWebControl.JS_Resize(
              $("#video-box").width(),
              that.videoHight
            ); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
            // that.yulan(that.videoModuleData.data[0].cameraIndexCode);
            that.yulan(that.cameraIndexCode);
          });
      });
    },

    //获取公钥
    getPubKey(callback) {
      const that = this;
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024,
          }),
        })
        .then(function (oData) {
          console.log(oData);
          if (oData.responseMsg.data) {
            that.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },

    //RSA加密
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },

    // 预览
    yulan(code) {
      const that = this; // 获取输入的监控点编号值，必填
      const streamMode = 0; // 主子码流标识：0-主码流，1-子码流
      const transMode = 1; // 传输协议：0-UDP，1-TCP
      const gpuMode = 0; // 是否启用GPU硬解，0-不启用，1-启用
      const wndId = -1; // 播放窗口序号（在2x2以上布局下可指定播放窗口）
      //      cameraIndexCode = cameraIndexCode.replace(/(^\s*)/g, "");
      //      cameraIndexCode = cameraIndexCode.replace(/(\s*$)/g, "");
      //   let cameraIndexCode = "42011001001320000004";
      let cameraIndexCode = code;
      if (typeof cameraIndexCode === "string") {
        console.log("开始预览！");
        that.oWebControl.JS_RequestInterface({
          funcName: "startPreview",
          argument: JSON.stringify({
            cameraIndexCode, // 监控点编号
            streamMode, // 主子码流标识
            transMode, // 传输协议
            gpuMode, // 是否开启GPU硬解
            wndId, // 可指定播放窗口
          }),
        });
      } else {
        // cameraIndexCode.forEach(function(item, index) {
        //   that.oWebControl.JS_RequestInterface({
        //     funcName: "startPreview",
        //     argument: JSON.stringify({
        //       cameraIndexCode: item, // 监控点编号
        //       streamMode, // 主子码流标识
        //       transMode, // 传输协议
        //       gpuMode, // 是否开启GPU硬解
        //       wndId // 可指定播放窗口
        //     })
        //   });
        // });
      }
    },
    // 销毁视频插件
    unloadVideo(type) {
      if (this.oWebControl != null) {
        this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
        this.oWebControl.JS_Disconnect().then(
          function () {},
          function () {}
        );
        if (type === "updateInit") {
          this.initPlugin();
        }
      }
    },
    setWndCover() {
      console.log("裁剪裁剪");
      var iWidth = $(window).width();
      var iHeight = $(window).height();
      var oDivRect = $("#playWnd").get(0).getBoundingClientRect();

      var iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0;
      var iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0;
      var iCoverRight =
        oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0;
      var iCoverBottom =
        oDivRect.bottom - iHeight > 0
          ? Math.round(oDivRect.bottom - iHeight)
          : 0;

      iCoverLeft = iCoverLeft > 1000 ? 1000 : iCoverLeft;
      iCoverTop = iCoverTop > 600 ? 600 : iCoverTop;
      iCoverRight = iCoverRight > 1000 ? 1000 : iCoverRight;
      iCoverBottom = iCoverBottom > 600 ? 600 : iCoverBottom;

      this.oWebControl.JS_RepairPartWindow(0, 0, 1001, 600); // 多1个像素点防止还原后边界缺失一个像素条
      if (iCoverLeft != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, 600);
      }
      if (iCoverTop != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, 1001, iCoverTop); // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
      }
      if (iCoverRight != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          1000 - iCoverRight,
          0,
          iCoverRight,
          600
        );
      }
      if (iCoverBottom != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          0,
          600 - iCoverBottom,
          1000,
          iCoverBottom
        );
      }
    },
    initVideo() {
      // this.videoWidth = $("#video-box").width();
      // this.videoHight = $("#video-box").height();
      // this.initPlugin();
      // // this.updateVideoCode();
      // console.log("进入视频组件~");
      // const that = this;
      // this.$nextTick(() => {
      //     window.addEventListener("resize", function() {
      //         if (that.oWebControl != null) {
      //             console.log('监听resize事件，使插件窗口尺寸跟随DIV窗口变化')
      //             that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
      //             that.setWndCover();
      //         }
      //     });
      //     window.addEventListener("scroll", function() {
      //         if (that.oWebControl != null) {
      //             console.log('监听滚动条scroll事件')
      //             that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
      //             that.setWndCover();
      //         }
      //     });
      // })
      // debugger;
      // if(this.showVideo){
      this.videoWidth = $("#video-box").width();
      this.videoHight = $("#video-box").height();
      console.log("kkk", this.videoWidth, this.videoHight);
      //    this.videoHight = 400;
      // this.videoModuleData = this.$store.state.login.videoModuleData;
      // if (this.videoModuleData.data.length === 0) {
      // } else {
      //   window.showCBInfo = function() {};
      //   this.initPlugin();
      // }
      this.initPlugin();
      // this.updateVideoCode();
      console.log("进入视频组件~");
      const that = this;
      window.addEventListener("resize", function () {
        that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
        that.setWndCover();
      });
      window.addEventListener("scroll", function () {
        that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
        that.setWndCover();
      });
      // }
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {
    this.videoWidth = $("#video-box").width();
    this.videoHight = $("#video-box").height();
    console.log("lll", this.videoWidth, this.videoHight);
    // this.videoModuleData = this.$store.state.login.videoModuleData;
    // if (this.videoModuleData.data.length === 0) {
    // } else {
    //   window.showCBInfo = function() {};
    //   this.initPlugin();
    // }
    this.initPlugin();
    console.log("进入视频组件~");
    const that = this;
    window.addEventListener("resize", function () {
      that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
      that.setWndCover();
    });
    window.addEventListener("scroll", function () {
      that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
      that.setWndCover();
    });
    // this.getDistrict();
    // this.getIotMontoringDataList();
    // this.updateVideoCode();
    // console.log("进入视频组件111~");
  },
  destroyed() {
    this.unloadVideo();
    // console.log("销毁视频组件~");
  },
};
</script>
<style lang="scss" scoped>
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
    cursor: pointer;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.video-select {
  width: 92%;
  margin: 0 auto;
  .video-box {
    // width:calc(100% - 60px);
    width: 100%;
    height: 275px;
  }
}
//     /deep/.el-select-dropdown{
//     position: absolute!important;
//     top:0!important;
//   }
</style>