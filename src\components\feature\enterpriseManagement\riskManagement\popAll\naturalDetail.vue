<template>
  <el-dialog
    title="自然灾害修正"
    :visible.sync="isamendment"
    width="1000px"
    top="10vh"
    @close="CloseClear"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    :destroy-on-close="false"
  >
    <div class="">
      <el-scrollbar style="height: 600px">
        <!-- <el-calendar v-model="calendarData"> </el-calendar> -->

        <div class="table-main">
          <div>
            <div class="table">
              <el-table
                :data="tableData"
                v-loading="loading"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
                ref="multipleTable"
              >
                <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
                <el-table-column
                  type="index"
                  label="序号"
                  width="50"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  prop="disasterName"
                  label="活动名称"
                  align="center"
                  min-width="150"
                >
                </el-table-column>
                <el-table-column
                  prop="startTime"
                  label="起始时间"
                  align="center"
                  width="150"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column
                  prop="endTime"
                  label="结束时间"
                  align="center"
                  width="150"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>

                <el-table-column
                  prop="districtName"
                  label="生效区域"
                  align="center"
                  width="150"
                  :show-overflow-tooltip="true"
                >
                </el-table-column>

                <el-table-column
                  prop="draftFlag"
                  label="策略状态"
                  align="center"
                  width="100"
                  :show-overflow-tooltip="true"
                >
                  <template slot-scope="{ row }">
                    <div class="tabButton">
                      <!-- {{row}} -->
                      <span>{{ row.draftFlag == 0 ? "未启用" : "启用" }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="180" align="center">
                  <template slot-scope="{ row }">
                    <div class="tabButton">
                      <el-button type="text" @click="openDialog('read', row)"
                        >查看</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="pagination">
              <el-pagination
                @current-change="handleCurrentChange"
                :current-page.sync="searchObj.nowPage"
                background
                layout="total, prev, pager, next"
                :total="searchObj.total"
                v-if="searchObj.total != 0"
              >
              </el-pagination>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>

    <el-dialog
      :title="dialogInfo.title"
      :visible.sync="dialogInfo.visible"
      width="1100px"
      v-if="dialogInfo.visible"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <div class="dialog">
        <el-form :model="accidentForm" ref="ruleForm" label-width="150px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="灾害名称:" prop="disasterName">
                <el-input
                  v-model.trim="accidentForm.disasterName"
                  maxlength="50"
                  placeholder="请输入灾害名称"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <!-- <el-form-item
                  label="灾害发生行政区划:"
                  prop="disasterDistrictCode"
                >
                  
                  <el-cascader
                    placeholder="请选择行政区划"
                    :options="district"
                    :disabled="dialogInfo.disable"
                    v-model="accidentForm.disasterDistrictCode"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    :show-all-levels="true"
                    style="width: 100%"
                  ></el-cascader>
                </el-form-item> -->
              <el-form-item
                label="事故发生行政区划:"
                prop="disasterDistrictName"
              >
                <el-input
                  v-model.trim="accidentForm.disasterDistrictName"
                  maxlength="50"
                  placeholder="请输入行政区划"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <!-- <el-col :span="12">
                <el-form-item label="灾害等级:" prop="disasterLevel">
                  <el-select
                    v-model="accidentForm.disasterLevel"
                    placeholder="请输入事故等级"
                    style="width: 100%"
                    :disabled="dialogInfo.disable"
                  >
                    <el-option
                      v-for="item in levelList"
                      :key="item.levelCode"
                      :value="item.levelCode"
                      :label="item.levelName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->

            <el-col :span="12">
              <el-form-item label="灾害类型:" prop="disasterType">
                <el-select
                  v-model="accidentForm.disasterType"
                  placeholder="请输入事故类型"
                  style="width: 100%"
                  :disabled="dialogInfo.disable"
                >
                  <!-- <el-option
                      v-for="item in typeList"
                      :key="item.code"
                      :value="item.code"
                      :label="item.name"
                    ></el-option> -->
                </el-select>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item label="发生灾害日期" prop="startTime">
                <!-- <el-date-picker
                    v-model="value1"
                    type="daterange"
                    style="width: 100%"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="searchTime"
                    value-format="yyyy-MM-dd"
                    :disabled="dialogInfo.disable"
                  >
                  </el-date-picker> -->
                <el-date-picker
                  v-model="value1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="searchTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  unlink-panels
                  style="width: 100%"
                  :disabled="dialogInfo.disable"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="涉及行政区划:" prop="districtName">
                <el-input
                  v-model.trim="accidentForm.districtName"
                  maxlength="50"
                  placeholder="请输入行政区划"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
              <!-- <el-form-item label="涉及行政区划:" prop="districtCode">                 
                  <el-cascader
                    placeholder="请选择行政区划"
                    :options="district"
                    :disabled="isEditDisabled"
                    v-model="accidentForm.districtCode"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    :show-all-levels="true"
                    style="width: 100%"
                  ></el-cascader>
                </el-form-item> -->
            </el-col>

            <el-col :span="12">
              <el-form-item label="是否启用:" prop="draftFlag">
                <el-select
                  v-model="accidentForm.draftFlag"
                  placeholder="请选择"
                  style="width: 100%"
                   :disabled="dialogInfo.disable"
                >
                  <el-option
                    v-for="item in draftFlagOption"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12"> </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="灾害描述:" prop="disasterInfo">
                <el-input
                  v-model.trim="accidentForm.disasterInfo"
                  type="textarea"
                  :rows="5"
                  placeholder="500字以内"
                  maxlength="500"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  getAccidentRecordList,
  accidentLevel,
  naturalDisasterPageList,
  accidentType,
} from "@/api/accidentManagement";
var dayjs = require("dayjs");
export default {
  data() {
    return {
      calendarData: new Date(),
      statutoryData: [],
      district: this.$store.state.login.userDistCode,
      //  district: this.$store.state.controler.district, // 行政区划
      isamendment: false,
      tableData: [], // 表格数据
      loading: false, // 加载状态
      searchObj: {
        nowPage: 1,
        total: 0,
        pageSize: 10,
        keyword: "",
      },
      dateValue: "", // 时间选择
      typeList: [
        { name: "节假日", code: "0" },
        { name: "重大活动", code: "1" },
      ], // 特殊时期类型数据
      draftFlagOption: [
        { name: "未启用", code: "0" },
        { name: "启用", code: "1" },
      ],
      dialogInfo: {
        // 弹窗控制信息
        visible: false,
        title: "新增特殊时期信息",
        disable: false,
      },
      typeList: [], // 事故类型数据
      levelList: [], // 事故等级数据
      accidentForm: {
        accidentType: "", //事故类型
        accidentLevel: "", //事故等级
        accidentDistrictCode: this.$store.state.login.userDistCode, //行政区划代
        districtCode: "", // 涉及行政区划
        accidentName: "", // 活动名称
        draftFlag: "", //  是否启用
        accidentInfo: "", //事故详情
        endTime: "",
        startTime: "",
      },
    };
  },
  watch: {},
  methods: {
    // 获取事故类型列表
    getaccidentTypeList() {
      accidentLevel().then((res) => {
        if (res.status === 200) {
          this.levelList = res.data.data;
        }
      });
      accidentType().then((res) => {
        if (res.status === 200) {
          this.typeList = res.data.data;
        }
      });
    },
    // 打开弹窗
    openDialog(type, data) {
      const dataList = Object.assign({}, data);
      if (type === "read") {
        this.value1 = [];
        this.value1.push(dataList.startTime, dataList.endTime);
        this.accidentForm = dataList;
        this.dialogInfo.title = "特殊时期信息详情";
        this.dialogInfo.disable = true;
      }
      this.dialogInfo.visible = true;
    },
    CloseClear() {},
    getData(val, type) {
      this.isamendment = val;
      this.getSearch(type);
    },
    // 分页查询
    handleCurrentChange(val) {
      this.searchObj.nowPage = val;
      this.getSearch(1);
    },
    getSearch() {
      this.loading = true;
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        disasterLevel: "",
        disasterType: "",
        draftFlag: "",
        endTime: "",
        id: "",
        keywords: "",
        nowPage: this.searchObj.nowPage,
        pageSize: this.searchObj.pageSize,
        startTime: "",
      };
      naturalDisasterPageList(params).then((res) => {
        if (res.data.status === 200) {
          this.tableData = res.data.data.list;
          this.searchObj.nowPage = res.data.data.nowPage + 1;
          this.searchObj.total = res.data.data.total;
          this.loading = false;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
// /deep/ .el-scrollbar__wrap{
//       overflow-y: scroll;
// }
// .el-scrollbar ::v-deep .is-horizontal{
//   display: none;
// }
/deep/ .el-calendar-table .el-calendar-day {
  height: 30px;
}
/deep/ .el-calendar__header .el-calendar__button-group {
  display: none !important;
}
/deep/ .el-calendar-table td.is-today {
  background-color: #409eff !important;
  color: #ccc !important;
}
/deep/ .el-table td.el-table__cell div {
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pagination {
  text-align: right;
  margin: 10px 0 0 0;
}
/deep/ .el-tab-pane {
  height: 500px;
  overflow: auto;
  // height: calc(100%- 200px);
}
/deep/ .el-date-editor.el-input {
  width: auto;
}
.hs_box {
  display: flex;
  justify-content: space-between;
  margin: 0 0 10px 0;
}
.M_box {
  .m_tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 10px 0;
  }
}
</style>