<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="事故列表" name="事故列表">
        <AccidentCount ref="accidentCount"></AccidentCount>
      </el-tab-pane>
      <el-tab-pane label="事件信息" name="事件信息">
        <AccideInfo ref="accideInfo"></AccideInfo>
      </el-tab-pane>
      <el-tab-pane label="分享给我事故案例" name="分享给我事故案例">
        <ShareAccident ref="ShareAccident"></ShareAccident>
      </el-tab-pane>
      <el-tab-pane label="事故统计分析" name="事故统计分析">
        <accidentAnalysis ref="accidentAnalysis"></accidentAnalysis>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import AccidentCount from "./accidentCount";
import AccideInfo from "./accideInfo";
import ShareAccident from "./shareAccident";
import accidentAnalysis from "../../workingAccount/accidentAnalysis/accidentAnalysis.vue";
export default {
  name: "accidentManagement",
  components: {
    AccidentCount,
    AccideInfo,
    ShareAccident,
    accidentAnalysis,
  },
  data() {
    return {
      activeName: this.$route.query.tab || "事故列表",
    };
  },
};
</script>
