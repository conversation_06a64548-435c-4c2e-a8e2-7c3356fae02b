define(["./defaultValue-69ee94f4","./EllipsoidGeometry-418f414a","./GeometryOffsetAttribute-4d39b441","./RuntimeError-ac440aa5","./Transforms-06c05e21","./Matrix2-e6265fb0","./ComponentDatatype-a9820060","./WebGLConstants-f63312fc","./_commonjsHelpers-3aae1032-15991586","./combine-0259f56f","./GeometryAttribute-b7edcc35","./GeometryAttributes-1b4134a9","./IndexDatatype-1cbc8622","./VertexFormat-e68722dd"],(function(e,t,r,o,a,n,i,f,c,m,s,d,u,b){"use strict";return function(r,o){return e.defined(o)&&(r=t.EllipsoidGeometry.unpack(r,o)),t.EllipsoidGeometry.createGeometry(r)}}));
