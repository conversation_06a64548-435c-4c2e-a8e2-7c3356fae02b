<template>
  <div
    class="enterpriseManagement"
    ref="enterpriseManagement"
    id="enterpriseManagement"
  >
    <div class="more" @click="toUrl()">更多</div>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="tab">
      <el-tab-pane label="视频监控" name="1" class="tab-video-inspection">
        <div class="videoInspection">
          <div class="video-left">
            <div class="videoLeft-top">
              <div class="list-search">
                <el-input
                  v-model.trim="enterpName"
                  size="mini"
                  placeholder="请输入企业名称"
                  class="input"
                  clearable
                  style="width: 220px"
                  @input="changeInput"
                ></el-input>
                <el-button type="primary" size="mini" @click="search"
                  >查询</el-button
                >
              </div>

              <el-tabs
                v-model="activeNameVedio"
                @tab-click="handleClick"
                class="tab"
              >
                <el-tab-pane label="全部企业" name="1">
                  <div class="video-list" v-loading="loading">
                    <el-tree
                      :data="newAllData"
                      accordion
                      @node-click="handleNodeClick"
                      icon-class="el-icon-caret-right"
                    >
                      <span
                        class="custom-tree-node"
                        :title="node.label"
                        slot-scope="{ node, data }"
                      >
                        <span>
                          <i
                            v-if="data.type === '1'"
                            class="el-icon-folder"
                          ></i>
                          <i
                            v-if="data.type === '2'"
                            class="el-icon-office-building"
                          ></i>
                          <i
                            v-if="data.type === '3'"
                            class="el-icon-video-camera"
                          ></i>
                          {{ node.label }}</span
                        >
                      </span>
                    </el-tree>
                  </div>
                </el-tab-pane>
                <el-tab-pane label="仅在线企业" name="2">
                  <div class="video-list" v-loading="loading">
                    <el-tree
                      :data="lineNewAllData"
                      accordion
                      @node-click="handleNodeClick"
                      icon-class="el-icon-caret-right"
                    >
                      <span
                        class="custom-tree-node"
                        :title="node.label"
                        slot-scope="{ node, data }"
                      >
                        <span>
                          <i
                            v-if="data.type === '1'"
                            class="el-icon-folder"
                          ></i>
                          <i
                            v-if="data.type === '2'"
                            class="el-icon-office-building"
                          ></i>
                          <i
                            v-if="data.type === '3'"
                            class="el-icon-video-camera"
                          ></i>
                          {{ node.label }}</span
                        >
                      </span>
                    </el-tree>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
          <div class="video-right">
            <div class="video-box" id="video-box">
              <!-- <div id="playWind"></div> -->
              <multiVideoPlayer ref="multiVideoPlayer" />
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="物联报警" name="2"
        ><div class="table">
          <el-table
            :data="tableData"
            v-loading="loading"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            ref="multipleTable"
            class="elTable"
            height="100%"
            width="1100px"
          >
            <!-- height="100%" -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="enterpName"
              label="单位名称"
              align="center"
              min-width="170"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span
                  @click="goEnt(scope.row)"
                  style="color: rgb(57, 119, 234); cursor: pointer"
                >
                  {{ scope.row.enterpName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="isShowDist == true ? '行政区划' : '归属园区'"
              width="80"
              align="center"
            >
              <template slot-scope="{ row, column, $index, store }">
                <span v-if="isShowDist == true">{{ row.districtName }}</span>
                <span v-else>{{ park.parkName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="dangerName"
              label="重大危险源名称"
              align="center"
              min-width="120"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="monitemkey"
              label="指标类型"
              align="center"
              width="80"
            >
            </el-table-column>
            <el-table-column
              prop="warningType"
              label="报警类型"
              align="center"
              width="80"
            >
            </el-table-column>
            <el-table-column
              prop="warningTime"
              label="报警时间"
              align="center"
              width="155"
            >
            </el-table-column>
            <el-table-column
              prop="alarmHandelTime"
              label="消警时间"
              align="center"
              width="155"
            >
            </el-table-column>
            <el-table-column
              prop="warningDuration"
              label="报警时长"
              align="center"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="state"
              label="当前状态"
              align="center"
              fixed="right"
            >
              <!-- fixed="right" -->
              <template slot-scope="scope">
                <span v-if="scope.row.state == '0'">已消警</span>
                <span v-else-if="scope.row.state == '1'">未消警</span>
                <span v-else></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="视频AI报警" name="3">
        <div class="table">
          <el-table
            :data="tableDataVideo.list"
            v-loading="loading"
            style="width: 100%"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            ref="multipleTable"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="projectName"
              label="单位名称"
              align="center"
              min-width="170"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span
                  @click="goEnt(scope.row)"
                  style="color: rgb(57, 119, 234); cursor: pointer"
                >
                  {{ scope.row.enterpName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="行政区划"
              width="150"
              align="center"
              prop="districtName"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.districtName }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="视频点位名称"
              min-width="80"
              align="center"
              prop="channelName"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.channelName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="设备编码"
              min-width="80"
              align="center"
              prop="channelId"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.channelId }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="报警类型"
              min-width="80"
              align="center"
              prop="alarmType"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.alarmTypeName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="报警时间"
              min-width="110"
              align="center"
              prop="applyTime"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.alarmTime | filterAlarmTime }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="反馈状态"
              min-width="80"
              align="center"
              prop="approvalStatusName"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.feedbackStatusName }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="反馈时间"
              min-width="80"
              align="center"
              prop="feedbackTime"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.feedbackTime | filterFeedbackTime }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="电力监测报警" name="4">
        <div class="table">
          <el-table
            :data="tableDataPown.list"
            v-loading="loading"
            style="width: 100%"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            ref="multipleTable"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="enterpriseName"
              label="单位名称"
              min-width="180"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span
                  @click="goEntPower(scope.row)"
                  style="color: rgb(57, 119, 234); cursor: pointer"
                >
                  {{ scope.row.enterpriseName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              label="行政区划"
              width="150"
              align="center"
              prop="districtName"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.districtName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="企业当前状态"
              min-width="80"
              align="center"
              prop="enterpriseStatusName"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.enterpriseStatusName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="报警类型"
              min-width="80"
              align="center"
              prop="alarmType"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.alarmType }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="报警生成时间"
              min-width="110"
              align="center"
              prop="alarmTime"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.alarmTime }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
      <el-tab-pane label="现场核查和行政检查" name="5">
        <div class="table">
          <el-table
            :data="sceneData"
            v-loading="loading"
            style="width: 100%"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            ref="multipleTable"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>

            <el-table-column
              prop="documentNum"
              label="文件编号"
              align="center"
              min-width="200"
            >
            </el-table-column>
            <!-- enterpName -->
            <el-table-column
              prop="enterpName"
              label="检查对象名称"
              align="center"
              min-width="150"
            >
            </el-table-column>
            <el-table-column
              prop="parentOrgName"
              label="检查机构"
              align="center"
              width="150"
            >
            </el-table-column>
            <el-table-column
              prop="orgName"
              label="检查部门"
              align="center"
              width="150"
            >
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="检查开始时间"
              align="center"
              width="180"
            >
            </el-table-column>
            <el-table-column
              prop="dataSource"
              label="数据来源"
              align="center"
              width="80"
            >
            </el-table-column>

            <el-table-column
              prop="inspectPerson"
              label="检查员"
              align="center"
              width="80"
            >
            </el-table-column>

            <el-table-column label="操作" width="100" align="center">
              <template slot-scope="{ row }">
                <div>
                  <el-button type="text" @click="clickBan(row)">办理</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <el-tab-pane label="安全生产事故警示" name="6">
        <div class="table">
          <el-table
            :data="getAccidentListDataArr"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            style="width: 100%"
            ref="multipleTable"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="unitName"
              label="事故单位名称"
              align="center"
              width="300"
            >
              <template slot-scope="scope">
                <span
                  @click="goEntAccident(scope.row)"
                  style="color: rgb(57, 119, 234); cursor: pointer"
                >
                  {{ scope.row.unitName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              prop="distName"
              label="所属行政区划"
              align="center"
              min-width="120"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column prop="typeName" label="事故类型" align="center">
            </el-table-column>
            <el-table-column prop="typeName" label="状态" align="center">
              <template slot-scope="scope">
                <span>
                  {{ scope.row.status == 0 ? "待发布" : "已发布" }}
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="time" label="事故时间" align="center">
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import {
  getOnlineVideoData,
  getIotMontoringData,
  getRealmonitor,
  getRealmonitorNew,
  getOnlineVideoStatus,
  getElectricityFindPage, //报警数据分页查询
  spotInspectFindPage,
  getVideoPreviewUrl,
} from "@/api/riskAssessment";
import { noticeEvent } from "@/api/informationRelease";
import { getAccidentListData } from "@/api/accidentManagement";
import { videoAlarmFindPage } from "@/api/companyParticularJob";

var dayjs = require("dayjs");
import { getVideoH5VideoH5 } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import WSPlayer from "@/utils/WSPlayer/WSPlayer";
import ICC from "@/utils/icc";
import flvPlayer from "../../riskAssessment/videoOnlineMonitoring/flvPlayer-v2.vue";
import multiVideoPlayer from "../../riskAssessment/videoOnlineMonitoring/multiVideoPlayer.vue";
export default {
  components: { flvPlayer, multiVideoPlayer },
  data() {
    return {
      sceneData: [], //现场核查和行政检查
      tableCheck: true,
      newAllData: [],
      lineNewAllData: [],
      tableDataPown: [],
      enterpName: "",
      loading: true,
      cameraIndexCode: "",
      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
      activeName: "1",
      activeNameVedio: "1",
      districtVal: this.$store.state.login.userDistCode,
      enterpName: "",
      value1: "",
      sensortypeCode: "",
      monitemkey: "",
      warningType: "",
      state: "",
      startTime: "",
      endTime: "",

      loading: true,
      areaName: "",
      options: [
        {
          value: "G0",
          label: "储罐",
        },
        {
          value: "Q0",
          label: "泄漏点",
        },
        {
          value: "P0",
          label: "装置",
        },
      ],
      options1: [
        {
          value: "WD",
          label: "温度",
        },
        {
          value: "YL",
          label: "压力",
        },
        {
          value: "YW",
          label: "液位",
        },
        {
          value: "YDQT",
          label: "有毒气体",
        },
        {
          value: "KRQT",
          label: "可燃气体",
        },
      ],
      options2: [
        {
          value: "1",
          label: "高报",
        },
        {
          value: "2",
          label: "高高报",
        },
        {
          value: "3",
          label: "低报",
        },
        {
          value: "4",
          label: "低低报",
        },
      ],
      options3: [
        {
          value: "1",
          label: "未消警",
        },
        {
          value: "0",
          label: "已消警",
        },
      ],
      getAccidentListDataArr: [],
      tableData: [],
      tableDataVideo: [],
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      size: 10,
      total: "",
      url: "/gardenEnterpriseManagement/videoOnlineMonitoring",
      iWind: 0,
      MaxIWind: 0,
      oPlugin: "",
      previewUrl: "",
      splitScreenValue: "",
      videoCheck: true,
      cameraIndexCode: "",
      resizeObserver: null,
      nowtime: new Date().getTime(),
      selectedKeys: "",
      iWndIndex: [],
      allData: [],
      allDataLine: [],
      realPlayer: null,
      videoInfo: {
        url: "", //视频流地址
        channelId: "",
        expireTime: 0,
        schema: 5,
      },
      isLoadingVideo: false, // 防止重复加载视频
      availableVideoSources: [],
    };
  },
  filters: {
    filterFeedbackTime(val) {
      // debugger
      return val ? dayjs(val).format("YYYY-MM-DD HH:ss") : "";
    },
    filterAlarmTime(val) {
      return val ? dayjs(val).format("YYYY-MM-DD HH:ss") : "";
    },
  },
  methods: {
    clickBan(val) {
      var url = `https://yyzc-hlwzf.hbsis.gov.cn:31443/#v_form/task/check/?id=${val.originId}&menu-id=model%3Atask.check&create_by_me=2`;
      window.open(url);
    },
    handleClick(tab, event) {
      // if (this.activeName == "2") {
      //   this.unloadVideo();
      // } else {
      //   this.initPlugin();
      // }
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpId); //
    },
    goEntPower(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpriseId); //
    },
    goEntAccident(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.unitId); //
    },
    //安全生产事故警示
    spotInspectFindPageFn() {
      var params = {
        keywords: "",
        page: 1,
        pageSize: 20,
        startTime: "",
      };
      spotInspectFindPage(params).then((res) => {
        if (res.data.code == 0) {
          this.sceneData = res.data.data.records;
        } else {
          this.$message({
            message: res.data.msg || "获取数据失败",
            type: "warning",
          });
        }
      });
    },
    //事故警示
    getAccidentListDataFn() {
      var params = {
        nowPage: 1,
        pageSize: 20,
        keyword: "",
        distCode: this.distCode,
        startTime: "",
        endTime: "",
        typeCode: "",
        unitName: "",
      };
      noticeEvent(params).then((res) => {
        if (res.data.status === 200) {
          this.getAccidentListDataArr = res.data.data.list;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
    //物联监测报警列表
    getIotMontoringDataList() {
      this.loading = true;
      getIotMontoringData({
        current: this.currentPage,
        distCode: this.distCode,
        size: 20,
        sensortypeCode: this.sensortypeCode,
        monitemkey: this.monitemkey,
        warningType: this.warningType,
        state: this.state,
        enterpName: this.enterpName,
        startTime: this.startTime,
        endTime: this.endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
        }
      });
    },
    //电力监测报警
    getDataePown() {
      this.loading = true;
      getElectricityFindPage({
        creditCode: "", //统一信用代码
        districtCode: this.distCode, //行政区划代码
        enterpriseId: "", //企业id
        enterpName: "", //企业名称
        enterpriseStatus: "", //监管状态0试生产1停工
        alarmId: "", //报警id
        alarmType: "",
        endTime: "",
        startTime: "",
        pageSize: 20,
        nowPage: 1,
      }).then((res) => {
        this.tableDataPown = res.data.data;
        this.loading = false;
      });
    },
    //视频AI报警 - tableDataVideo
    getDataVideo() {
      this.loading = true;
      const dto = {
        alarmId: "",
        alarmType: "", //告警类型
        districtCode: this.distCode,
        startTime: "",
        endTime: "",
        enterpId: "", //企业id
        feedbackStatus: "", //反馈状态
        nowPage: this.currentPage,
        pageSize: 20,
      };
      videoAlarmFindPage(dto).then((res) => {
        //getCompanyParticularJobList   //getCompanyProjectList
        this.tableDataVideo = res.data.data;
        this.loading = false;
      });
    },

    //视频运行列表(全部)
    getOnlineVideoDataList(type) {
      this.loading = true;
      getOnlineVideoData({
        enterpName: this.enterpName,
      }).then((res) => {
        this.loading = false;
        if (res.data.code == 0) {
          //   this.tableData = res.data.data.records;
          //   this.NetWork = false;
          this.newAllData = res.data.data;
          this.setMenus(this.newAllData);
          if (type !== "search") {
            this.allData = this.newAllData;

            // 数据加载完成后，自动播放前四路视频
            this.$nextTick(() => {
              setTimeout(() => {
                this.autoPlayFirstFourVideos();
              }, 1000); // 延迟1秒确保组件完全渲染
            });
          }
        }
      });
    },
    //统计紧紧在线
    getOnlineVideoChange(type) {
      this.loading = true;
      getOnlineVideoStatus({
        enterpName: this.enterpName,
        onlineStatus: 1,
      }).then((res) => {
        this.loading = false;
        if (res.data.code == 0) {
          //   this.tableData = res.data.data.records;
          //   this.NetWork = false;
          this.lineNewAllData = res.data.data;
          this.setMenus(this.lineNewAllData);
          if (type !== "search") {
            this.allDataLine = this.lineNewAllData;
          }
        }
      });
    },

    changeInput(val) {
      if (this.enterpName == "") {
        this.newAllData = this.allData;
        this.lineNewAllData = this.allDataLine;
        // this.getOnlineVideoDataList();
      }
    },
    setMenus(arr) {
      /**
       * 利用递归替换key值
       * title替换orgName
       * key替换orgCode
       */
      var keyMap = {
        name: "label",
        // id: "key",
      };

      for (var i = 0; i < arr.length; i++) {
        // console.log(arr[i].children);
        if (arr[i].children <= 0) {
          delete arr[i].children;
          // console.log(arr[i]);
        }
        // delete arr[i].isLeaf;
        var obj = arr[i];
        for (var key in obj) {
          var newKey = keyMap[key];
          //   console.log(newKey);
          if (newKey) {
            obj[newKey] = obj[key];
            if (obj.children) {
              this.setMenus(obj.children);
            }
          }
        }
      }
    },
    async handleNodeClick(data, node, el) {
      if (node.childNodes.length !== 0) return;

      // 防止重复点击
      if (this.isLoadingVideo) {
        console.log("视频正在加载中，请稍候...");
        return;
      }

      // 接入高德视频
      if (data.id) {
        this.isLoadingVideo = true;
        try {
          // 获取视频预览地址，使用 gaodeVideoServer 中的函数
          const res = await getVideoPreviewUrl({
            channelId: data.id,
            schema: 5, // 使用默认的流媒体协议
            subType: 1, // 实时预览
          });
          console.log("获取视频流接口:", res, res.status, res.data?.data?.url);
          if (res.status === 200 && res.data?.data?.url) {
            // 将视频源添加到可用列表中
            const videoSource = {
              id: data.id,
              name: data.label || data.name || `摄像头${data.id}`,
              location: node.parent?.data?.label || "未知位置",
              url: res.data.data.url,
              channelId: data.id,
              expireTime: res.data.data.expireTime,
              schema: res.data.data.schema,
              status: "online",
            };

            // 检查视频流URL的有效性
            if (!videoSource.url || !videoSource.url.startsWith("wss://")) {
              console.error("无效的视频流URL:", videoSource.url);
              this.$message.error("视频流URL格式错误，请检查配置");
              return;
            }

            // 检查URL中的token是否存在
            if (!videoSource.url.includes("token=")) {
              console.warn("视频流URL中缺少认证token，可能导致连接失败");
            }

            // 自动将视频源添加到多路播放器
            this.$nextTick(() => {
              if (
                this.$refs.multiVideoPlayer &&
                typeof this.$refs.multiVideoPlayer.addVideoToChannel ===
                  "function"
              ) {
                const channelIndex =
                  this.$refs.multiVideoPlayer.addVideoToChannel(videoSource);
                if (channelIndex >= 0) {
                  console.log(`视频已添加到通道 ${channelIndex + 1}`);
                  console.log("如果连接失败，请检查：");
                  console.log("1. 视频流服务器是否正常运行");
                  console.log("2. 网络连接是否正常");
                  console.log("3. 认证token是否有效");
                } else {
                  console.error("添加视频到多路播放器失败");
                }
              } else {
                console.error("多路播放器组件未准备好");
              }
            });

            // 保持原有的单路播放器逻辑（备用）
            this.videoInfo = Object.assign({}, this.videoInfo, {
              url: res.data.data.url,
              channelId: data.id,
              expireTime: res.data.data.expireTime,
              schema: res.data.data.schema,
            });
          } else {
            this.$message.error(
              "获取视频流失败：" + (res.data?.msg || "未知错误")
            );
          }
        } catch (error) {
          console.error("获取视频流失败:", error);
          this.$message.error(
            "获取视频流失败：" + (error.message || "未知错误")
          );
        } finally {
          this.isLoadingVideo = false; // 重置加载状态
        }
      }
    },
    // 处理视频选择事件

    async refreshVideoStream() {
      try {
        const res = await getVideoPreviewUrl({
          channelId: this.videoInfo.channelId,
          schema: this.videoInfo.schema,
          subType: 1,
        });

        if (res.status === 200 && res.data?.url) {
          this.videoInfo.url = res.data.url;
          this.videoInfo.expireTime = res.data.expireTime;
          // 更新视频源
          if (this.$refs.videoElement) {
            this.$refs.videoElement.src = this.videoInfo.url;
            this.$refs.videoElement.load();
          }
        }
      } catch (error) {
        console.error("刷新视频流失败:", error);
      }
    },
    search() {
      this.newAllData = [];
      this.getOnlineVideoDataList("search");
      this.getOnlineVideoChange("search");
    },

    // 自动播放前四路视频
    async autoPlayFirstFourVideos() {
      console.log("开始自动播放前四路视频...");

      // 获取所有视频节点
      const videoNodes = this.getAllVideoNodes(this.newAllData);
      console.log("找到的视频节点:", videoNodes);

      if (videoNodes.length === 0) {
        console.log("没有找到可用的视频节点");
        return;
      }

      // 取前四个视频节点
      const firstFourVideos = videoNodes.slice(0, 4);
      console.log("准备播放的前四路视频:", firstFourVideos);

      // 依次播放前四路视频
      for (let i = 0; i < firstFourVideos.length; i++) {
        const videoNode = firstFourVideos[i];
        try {
          await this.playVideoById(
            videoNode.id,
            videoNode.label || `摄像头${i + 1}`
          );
          // 添加延迟避免并发请求过多
          await new Promise((resolve) => setTimeout(resolve, 500));
        } catch (error) {
          console.error(`播放第${i + 1}路视频失败:`, error);
        }
      }
    },

    // 递归获取所有视频节点
    getAllVideoNodes(nodes) {
      let videoNodes = [];

      if (!nodes || !Array.isArray(nodes)) {
        return videoNodes;
      }

      for (const node of nodes) {
        // type === '3' 表示视频节点
        if (node.type === "3" && node.id) {
          videoNodes.push({
            id: node.id,
            label: node.label || node.name,
            parentLabel: node.parentLabel,
          });
        }

        // 递归处理子节点
        if (node.children && Array.isArray(node.children)) {
          const childVideoNodes = this.getAllVideoNodes(node.children);
          videoNodes = videoNodes.concat(childVideoNodes);
        }
      }

      return videoNodes;
    },

    // 根据ID播放视频
    async playVideoById(channelId, videoName) {
      try {
        console.log(`正在播放视频: ${videoName} (ID: ${channelId})`);

        const res = await getVideoPreviewUrl({
          channelId: channelId,
          schema: 5,
          subType: 1,
        });

        if (res.status === 200 && res.data?.data?.url) {
          const videoSource = {
            id: channelId,
            name: videoName,
            url: res.data.data.url,
            channelId: channelId,
            expireTime: res.data.data.expireTime,
            schema: res.data.data.schema,
            status: "online",
          };

          // 添加到四路播放器
          this.$nextTick(() => {
            if (
              this.$refs.multiVideoPlayer &&
              typeof this.$refs.multiVideoPlayer.addVideoToChannel ===
                "function"
            ) {
              const channelIndex =
                this.$refs.multiVideoPlayer.addVideoToChannel(videoSource);
              if (channelIndex >= 0) {
                console.log(`${videoName} 已添加到通道 ${channelIndex + 1}`);
              } else {
                console.error(`添加 ${videoName} 到多路播放器失败`);
              }
            }
          });
        } else {
          console.error(`获取 ${videoName} 视频流失败:`, res.data?.msg);
        }
      } catch (error) {
        console.error(`播放 ${videoName} 失败:`, error);
        throw error;
      }
    },
    toUrl() {
      if (this.activeName == "1") {
        this.url = "/gardenEnterpriseManagement/videoOnlineMonitoring";
        this.$router.push(this.url);
      } else if (this.activeName == "2") {
        this.url = "/dailySafetyManagement/iotMontoringAlarm";
        this.$router.push(this.url);
      } else if (this.activeName == "3") {
        this.url = "/riskAssessment/videoAnalysis";
        this.$router.push(this.url);
      } else if (this.activeName == "4") {
        this.url = "/riskAssessment/powerMonitoring";
        this.$router.push(this.url);
      } else if (this.activeName == "5") {
        window.open(
          "https://yyzc-hlwzf.hbsis.gov.cn:31443/#v_list/task/check/?menu-id=model%3Atask.check"
        );
      } else if (this.activeName == "6") {
        this.$router.push({
          path: "/dailySafetyManagement/informationRelease",
        });
        this.$store.commit(
          "controler/updateInformationClickBar",
          "accidentWarning"
        );
      }
    },
    getVideo() {
      this.videoCheck = false;
      getVideoH5VideoH5(this.cameraIndexCode).then((res) => {
        this.previewUrl = res.data.data + "?transcode=1&bitrate=2048";
        this.realplay(this.iWind);
        // console.log(`canvas_animation${this.iWind}`);
        //2021.8.26 根据需求设计，点击播放之后向后选中播放框
        if (this.MaxIWind > this.iWind) {
          this.selectWnd(++this.iWind);
        } else {
          this.selectWnd(this.iWind);
        }
      });
    },
    //加载动画
    canvasAnimation(elName, wd, ht) {
      // console.log(elName, wd, ht);
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(255, 204, 0,1)";
        ctx.lineWidth = 3;
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          70,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        if (deg1 < 360) {
          if (deg1 < 180) {
            index *= 1.08;
          } else {
            index /= 1.08;
          }
          deg1 += index;
        }
        if (deg1 >= 360) {
          deg1 = 0;
        }
        ctx.stroke();
        ctx.beginPath();
        if (flag) {
          opa -= 0.02;
          if (opa < 0.2) {
            flag = false;
          }
        } else {
          opa += 0.02;
          if (opa > 1) {
            flag = true;
          }
        }
        ctx.font = "normal 16px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgba(255, 204, 0," + opa + ")";
        ctx.fillText("正在取流...", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    //错误动画
    canvasError(elName, wd, ht) {
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 16px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("取流失败", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    //初始动画
    canvasDef(elName, wd, ht) {
      var deg1 = 0;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 16px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    // var szWebsocketSessionID = "4cc81824281f214eceb5"; //设备直连取流uuid， 流媒体取流不需要该参数
    // var szToken = "";
    // var iWind = 0; //窗口索引
    initPlugin() {
      const THIS = this;
      this.oPlugin.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {
          //插件选中窗口回调
          THIS.iWind = iWndIndex;
          // console.log(iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
          //插件错误回调
          console.error(
            `window-${iWndIndex}, errorCode: ${iErrorCode}`,
            oError
          );
          let canvasEl = document.getElementById(
            `canvas_animation${iWndIndex}`
          );
          let wd = canvasEl.getAttribute("width");
          let ht = canvasEl.getAttribute("height");
          THIS.canvasError(`canvas_animation${iWndIndex}`, wd, ht);
          THIS.iWndIndex[iWndIndex] = false;
        },
        windowEventOver: function (iWndIndex) {
          //鼠标移过回调
          console.log($("#stopBtn" + iWndIndex));
          if (THIS.iWndIndex[iWndIndex]) {
            $("#stopBtn" + iWndIndex).show();
            $("#stopBtn" + iWndIndex)
              .children()
              .eq(0)
              .click(() => {
                THIS.stopBtn(iWndIndex);
                THIS.iWndIndex[iWndIndex] = null;
              });
          }
        },
        windowEventOut: function (iWndIndex) {
          //鼠标移出回调
          //console.log(iWndIndex);

          $("#stopBtn" + iWndIndex).hide();
        },
        windowEventUp: function (iWndIndex) {
          //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {
          //全屏切换回调
          console.log(bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
          //首帧显示回调
          console.log(iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {
          //性能不足回调
        },
      });
      this.oPlugin
        .JS_SetOptions({
          bSupportSound: false, //是否支持音频，默认支持
          bSupporDoubleClickFull: false, //是否双击窗口全屏，默认支持
          // bOnlySupportMSE: true, //只支持MSE
          // bOnlySupportJSDecoder: true  //只支持JSDecoder
        })
        .then(function () {
          console.log("JS_SetOptions");
        });
    },

    getVersion() {
      this.oPlugin.JS_GetPluginVersion().then(function (szVersion) {
        console.log(szVersion);
      });
    },
    Destroy() {
      this.oPlugin.JS_DestroyWorker().then(function () {
        console.log("destroyWorker success");
      });
    },
    realplay(iWind) {
      this.videoCheck = true;
      var url = this.previewUrl; // + "?token=" + szToken;  //"ws://10.19.141.64:7314/EUrl/ybcwxHO"; 联网共享下该url和playurl是一样的
      // console.log(this.oPlugin);
      const THIS = this;

      this.oPlugin
        .JS_Play(
          url,
          {
            playURL: url,
            mode: 0,
            session: "4cc81824281f214eceb5", //定制设备
            token: "",
          },
          iWind
        )
        .then(
          function () {
            THIS.iWndIndex[iWind] = true;
            console.log("realplay success");
            $(`#canvas_animation${iWind}`).hide();
          },
          function () {
            THIS.iWndIndex[iWind] = false;
            let canvasEl = document.getElementById(`canvas_animation${iWind}`);
            let wd = canvasEl.getAttribute("width");
            let ht = canvasEl.getAttribute("height");
            THIS.canvasError(`canvas_animation${iWind}`, wd, ht);
            console.log("realplay failed");
            THIS.$message.error("取流异常");
          }
        );
    },
    stopBtn(i) {
      this.oPlugin.JS_Stop(i).then(
        () => {
          console.log("stop success");
          $(`#canvas_animation${i}`).show();
          this.canvasDef(
            `canvas_animation${i}`,
            $(`#canvas_animation${i}`).parent().width(),
            $(`#canvas_animation${i}`).parent().height()
          );
          $("#stopBtn" + i).hide();
        },
        function (e) {
          console.error("stop failed", e);
        }
      );
    },
    arrangeWindow(i) {
      this.MaxIWind = i * i - 1;
      this.oPlugin.JS_ArrangeWindow(i).then(() => {
        console.log("JS_ArrangeWindow success");
        // console.log(this.iWndIndex.length);
        for (var i = 0; i < this.iWndIndex.length; i++) {
          // console.log(this.iWndIndex.length);
          $(`#canvas_animation${i}`)[0].width =
            $(`#canvas_animation${i}`).parent().width() * 2;
          $(`#canvas_animation${i}`)[0].height =
            $(`#canvas_animation${i}`).parent().height() * 2;
          if (this.iWndIndex[i] === true) {
            this.canvasAnimation(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width() * 2,
              $(`#canvas_animation${i}`).parent().height() * 2
            );
          } else if (this.iWndIndex[i] === false) {
            this.canvasError(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width() * 2,
              $(`#canvas_animation${i}`).parent().height() * 2
            );
          }
        }
      });
    },
    selectWnd(iWind) {
      this.oPlugin.JS_SelectWnd(iWind).then(
        function () {
          console.log("JS_SelectWnd success");
        },
        function () {
          console.log("JS_SelectWnd failed");
        }
      );
    },
    CapturePicture(szType) {
      // console.log(this.iWind);
      const timestamp = new Date();
      this.oPlugin
        .JS_CapturePicture(this.iWind, `img-${timestamp}`, szType)
        .then(
          function () {
            console.log("CapturePicture success");
          },
          function () {
            console.log("CapturePicture failed");
          }
        );
    },
    //关闭全部预览
    StopRealPlayAll() {
      this.oPlugin.JS_StopRealPlayAll().then(
        () => {
          console.log("JS_StopRealPlayAll success");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        },
        () => {
          console.log("JS_StopRealPlayAll failed");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        }
      );
    },
    dateFormat(oDate, fmt) {
      var o = {
        "M+": oDate.getMonth() + 1, //月份
        "d+": oDate.getDate(), //日
        "h+": oDate.getHours(), //小时
        "m+": oDate.getMinutes(), //分
        "s+": oDate.getSeconds(), //秒
        "q+": Math.floor((oDate.getMonth() + 3) / 3), //季度
        S: oDate.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (oDate.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    fullSreen() {
      this.oPlugin.JS_FullScreenDisplay(true).then(
        function () {
          console.log("JS_FullScreenDisplay success");
        },
        function () {
          console.log("JS_FullScreenDisplay failed");
        }
      );
    },
    getData(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoList({
        enterpId: this.enterpriseId,
        sign: this.sign,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoData = res.data.data;
          this.cameraIndexCode = this.videoData[0].monitornum;
        }
      });
    },
    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
    setVideoSize() {
      var playWind = document.getElementById("playWind_items");
      this.oPlugin.JS_Resize({ iWidth: playWind.scrollWidth }).then(
        () => {
          console.info("JS_Resize success");
          // do you want...
        },
        (err) => {
          console.info("JS_Resize failed");
          // do you want...
        }
      );
    },
    initializationVideo() {
      const THIS = this;
      function getScript(url, fn) {
        if ("string" === typeof url) {
          url = [url]; //如果不是数组带个套
        }
        var ok = 0; //加载成功几个js
        var len = url.length; //一共几个js
        var head = document.getElementsByTagName("head").item(0);
        var js = null;
        var _url;
        var create = function (url) {
          //创建js
          var oScript = null;
          oScript = document.createElement("script");
          oScript.type = "text/javascript";
          oScript.src = url;
          head.appendChild(oScript);
          return oScript;
        };
        for (var i = 0; i < len; i++) {
          _url = url[i];
          js = create(_url); //创建js
          fn &&
            (js.onload = function () {
              if (++ok >= len) {
                //如果加载完所有的js则执行回调
                fn();
              }
            });
        }
      }
      //var szBrowserVersion = "";
      //var iBrowserVersion = -1;
      var aScript = [];
      var szUserAgent = navigator.userAgent.toLowerCase();
      // if (szUserAgent.match(/chrome\/([\d.]+)/) || szUserAgent.match(/Firefox\/([\d.]+)/)) {
      //szBrowserVersion = szUserAgent.match(/chrome\/([\d.]+)/)[1];
      //iBrowserVersion = parseInt(szBrowserVersion.split(".")[0], 10);
      if (
        szUserAgent.indexOf("win64") > -1 ||
        szUserAgent.indexOf("x64") > -1
      ) {
        aScript = ["../../../../../static/h5player.min.js"];
      } else {
        aScript = ["../../../../../static/h5player.min.js"];
      }
      // }
      var playWind = document.getElementById("playWind_items");
      // console.log(playWind.scrollWidth);
      getScript(aScript, function () {
        //初始化插件
        //初始化插件
        THIS.oPlugin = new JSPlugin({
          szId: "playWind",
          iWidth: playWind.scrollWidth,
          // iHeight: 500,
          iMaxSplit: 3,
          iCurrentSplit: 1,
          szBasePath: "../../../../../static/",
          oStyle: {
            border: "#343434",
            borderSelect: "#FFCC00",
            background: "#000",
          },
          openDebug: false,
        });
        THIS.initPlugin();
        //初始化播放器大小
        THIS.setVideoSize();
        THIS.resizeObserver = new ResizeObserver((entries) => {
          // console.log(entries);
          THIS.$nextTick(() => {
            THIS.$refs.enterpriseManagement.style.width =
              entries[0].target.offsetWidth;
            THIS.setVideoSize();
          });
        });

        THIS.resizeObserver.observe(
          document.getElementById("enterpriseManagement")
        );
        //初始化canvas动画
        for (var i = 0; i < $(".parent-wnd").children().length; i++) {
          //初始化动画状态
          THIS.iWndIndex[i] = null;
          //创建canvas
          let canvasWidth = $("#playWind_playCanvas" + i).width();
          let canvasHeight = $("#playWind_playCanvas" + i).height();
          // console.log(canvasHeight);
          $("#playWind_playCanvas" + i).before(
            `<canvas id="canvas_animation${i}" width="${canvasWidth}" height="${canvasHeight}"></canvas>`
          );
          //初始化canvas的css
          $("#canvas_animation" + i).css({
            transform: "scale(0.5) translate(-50%, -50%)",
            "transform-origin": "0 0",
            position: "absolute",
            top: "50%",
            left: "50%",
            // zIndex: 200,
          });
          $("#canvas_draw" + i).css({
            cursor: "default",
          });
          //创建关闭按钮
          $("#playWindow" + i)
            .after(`<div id="stopBtn${i}" style="width:100%;height: 35px;background: rgba(0,0,0,0.4);position: absolute;top:0;left:0;z-index: 9999999;display:none;cursor: default;">
          <i class="el-icon-close"  style="float: right;line-height:35px;margin-right:10px;font-size:18px;color:#eee;cursor: pointer;"></i>
        </div>`);
        }
      });
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId
     */
    // realPlayNew(channelId) {
    //   ICC.getRealmonitor({
    //     optional: "/admin/API/MTS/Video/StartVideo",
    //     channelId: channelId,
    //     dataType: "3", //视频类型：1=视频, 2=音频, 3=音视频
    //     streamType: "2", //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
    //     trackId: "",
    //   }).then((data) => {
    //     this.realPlayer.playReal({
    //       rtspURL: data.rtspUrl, // string | array[string]
    //       decodeMode: "canvas", // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
    //       channelId: channelId, // 可选，用来标记当前视频播放的通道id
    //     });
    //   });
    // },
  },
  async created() {
    await ICC.init();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getOnlineVideoDataList();
    this.getOnlineVideoChange();
    this.getIotMontoringDataList();
    this.getDataVideo(); //视频AI报警
    this.getDataePown(); //电力监测报警
    this.spotInspectFindPageFn(); //安全生产事故警示
    this.getAccidentListDataFn(); //事故警示
    // const THIS = this;
    //初始化播放器
    // this.initializationVideo();
    // window.onresize = () => {
    //   THIS.setVideoSize();
    // };
    // 初始化平台信息获取
    let serverAdress = sessionStorage.getItem("videoApi");

    if (serverAdress) {
      serverAdress = sessionStorage.getItem("videoApi");
    } else {
      if (window.location.port == "30003") {
        //企业端
        serverAdress = "************:9100";
      } else if (window.location.port == "31443") {
        serverAdress = "59.208.147.51:9100";
      }
    }
    console.log(serverAdress, "互联网环境获取不到serverAdress");
    // if (process.env.NODE_ENV === 'development') {
    //     serverAdress = '************:2443';
    // } else {
    //     serverAdress = '***********:9100';
    // }
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // if (!this.realPlayer) {
    //   this.realPlayer = new WSPlayer({
    //     el: "ws-real-player", // 必传
    //     type: "real", // real | record
    //     serverIp: serverAdress,
    //     num: 4,
    //     showControl: true,
    //   });
    // }
  },
  beforeDestroy() {
    // this.StopRealPlayAll();
    // 离开页面删除检测器和所有侦听器
    // this.resizeObserver.disconnect();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
    }),
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tree {
  background: rgba(255, 255, 255, 0) !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/deep/ .el-tree-node__content {
  height: 35px;
}

/deep/ .el-tree-node__content:hover {
  background: #1890ff !important;
  color: #fff !important;
}
/deep/ .el-tree-node__expand-icon {
  color: #606266;
}
/deep/ .el-tree-node__content:hover .el-tree-node__expand-icon {
  color: #fff;
}
/deep/ .el-tree-node__content:hover .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}
/deep/ .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}

/deep/ .el-tree-node:focus,
.is-current:focus,
.is-focusable:focus {
  // color: #fff;
  color: #606266;
  & > .el-tree-node__content {
    background: rgba(255, 255, 255, 0);
  }
}
/deep/ .el-tree-node__content:active {
  background: #1890ff !important;
  color: #fff;
}
/deep/ .el-table {
  flex: none;
  width: none;
  max-width: none;
}
.enterpriseManagement {
  width: 100%;
  height: 100%;

  border: 1px solid #d8e0ee;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  position: relative;
  .more {
    color: #3977ea;
    font-size: 14px;
    cursor: pointer;
    position: absolute;
    z-index: 2000;
    right: 15px;
    top: 10px;
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .tab {
    width: 96.5%;
    margin: 0 auto;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    .video-left {
      float: left;
      width: 33%;
      margin-right: 1%;
      background: #daefff;
      // margin-top: 15px;
      .videoLeft-top {
        // background: #daefff;
        .list-search {
          padding-top: 15px;
          padding-bottom: 5px;
          padding-left: 8px;
          padding-right: 8px;
          display: flex;
          justify-content: space-between;
        }
        .video-list {
          width: 100%;
          height: 238px;
          padding-top: 0;
          padding-bottom: 2px;
          border-radius: 4px;
          overflow-y: auto;
          overflow-y: overlay;
          margin-top: -8px;
        }
      }
    }
    .video-right {
      float: left;
      position: relative;
      width: 66%;
      .video-box {
        width: 100%;
        // margin-top: 40px;
        // border: 1px solid #ddd;
        background-color: #000 !important;
        height: 330px;
        position: relative;
        & > .items {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #2e2e2e;
          padding: 10px;
          height: 40px;
          font-size: 12px;
          .items_button {
            width: 70%;
            height: 25px;
            display: flex;
          }
          .button {
            color: #fff;
            border-radius: 5px;
            padding: 3px 10px;
            margin-right: 20px;
            cursor: pointer;
            background-color: #777;
          }
          .fenping_box {
            width: 60px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .fenping {
              width: 20px;
              height: 20px;
            }
          }
        }
      }
      #playWind {
        height: 290px;
      }
      #ws-real-player {
        height: 331px;
      }
    }
  }
  .table {
    width: 100%;
    height: 330px;
    overflow-y: auto;
    overflow-x: auto;
    .elTable {
      // width: 1100px;
      min-width: 100%;
      overflow-y: auto;
      overflow-x: auto;
    }
  }
}
</style>
