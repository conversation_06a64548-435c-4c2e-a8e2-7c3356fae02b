<template>
  <div class="superviseWorkbench">
    <div class="container">
      <div class="top">
        <div class="div2">
          <RiskWarning></RiskWarning>
        </div>
        <div class="div3"><SecurityCommitments></SecurityCommitments></div>
        <div class="div1">
          <div class="RiskAssessment"><RiskAssessment> </RiskAssessment></div>
          <div class="SystemRunningState">
            <SystemRunningState></SystemRunningState>
          </div>
        </div>
      </div>
      <div>
        <div class="div4"><Video></Video></div>
        <div class="div5">
          <div class="div5-top"><Focuscompany></Focuscompany></div>
          <div class="div5-bottom"><EmergencyEvents></EmergencyEvents></div>
        </div>
        <!-- <div class="div6"><QuickEntrance></QuickEntrance></div> -->
      </div>
    </div>
  </div>
</template>
<script>
import RiskAssessment from "./riskAssessment";
import SystemRunningState from "./systemRunningState";
import RiskWarning from "./riskWarning";
import SecurityCommitments from "./securityCommitments";
import Video from "./video";
import Focuscompany from "./focuscompany";
import EmergencyEvents from "./emergencyEvents.vue";
import QuickEntrance from "./quickEntrance.vue";
export default {
  components: {
    RiskAssessment,
    SystemRunningState,
    RiskWarning,
    SecurityCommitments,
    Video,
    Focuscompany,
    EmergencyEvents,
    QuickEntrance,
  },
  data() {
    return {
      riskAssessmentKey: 0,
      systemRunningStateKey: 0,
      riskWarningKey: 0,
      securityCommitmentsKey: 0,
    };
  },
  methods: {
    // //强制刷新组件
    // forceRerender(eleName) {
    //   switch (eleName) {
    //     case "riskAssessmentKey":
    //       this.riskAssessmentKey =
    //     "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    //       break;
    //       case "systemRunningStateKey":
    //       this.systemRunningStateKey =
    //     "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    //       break;
    //       case "riskWarningKey":
    //       this.riskWarningKey =
    //     "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    //       break;
    //       case "riskAssessmentKey":
    //       this.demoKey =
    //     "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    //       break;
    //       case "securityCommitmentsKey":
    //       this.securityCommitmentsKey =
    //     "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    //       break;

    //     default:
    //       break;
    //   }

    // },
    // closeDialog(eleName){
    //   // console.log(eleName);
    //   this.forceRerender(eleName);
    // },
    close() {
      this.show = false;
    },
    toUrl(url) {
      this.$router.push(url);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.superviseWorkbench {
  min-width: 1366px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  > .container {
    // width: 98%;
    > div {
      margin-left: 3px;
      display: flex;
      justify-content: space-between;
    }
    .div1 {
      width: 38%;
      .RiskAssessment {
        margin-top: 5px;
      }
      .SystemRunningState {
        margin-top: 15px;
      }
    }
    .div2 {
      width: 31%;
      margin-top: 5px;
    }
    .div3 {
      width: 29%;
      margin-top: 5px;
    }
    .div4 {
      width: 59%;
      height: 400px;
      margin-top: 15px;
    }
    .div5 {
      width: 40%;
      height: 400px;
      margin-top: 15px;
      display: flex;
      flex-direction: column;

      .div5-top {
        height: 200px;
        margin-bottom: 15px;
      }

      .div5-bottom {
        height: 185px;
      }
    }
    .div6 {
      width: 10%;
      height: 400px;
      margin-top: 15px;
    }
  }
}
</style>
