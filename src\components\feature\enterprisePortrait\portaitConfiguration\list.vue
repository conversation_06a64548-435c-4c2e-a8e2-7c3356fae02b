<!-- 画像列表 -->
<template>
  <div class="portraitList">
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData"
        :header-cell-style="headerCellStyle"
        show-summary
        :summary-method="getSummaries"
        border
        style="width: 100%"
        ref="multipleTable"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="name"
          label="指数配置方案名称"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="level"
          label="关联企业数"
          width="200"
          align="center"
        >
        </el-table-column>
        <el-table-column prop="percent" label="占比" align="center" width="120">
        </el-table-column>
        <el-table-column
          prop="statusName"
          label="状态"
          align="center"
          width="120"
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="创建时间"
          align="center"
          width="200"
        >
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          width="260"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)" type="text" size="small">
              编辑
            </el-button>
            <el-button @click="handleLink(scope.row)" type="text" size="small">
              关联
            </el-button>
            <el-button
              @click="handleDelete(scope.row)"
              type="text"
              size="small"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- <div class="pagination">
            <el-pagination @current-change="handleCurrentChange" :current-page.sync="searchParams.current"
                :page-size="searchParams.size" layout="total, prev, pager, next" background :total="total">
            </el-pagination>
        </div> -->
    <LinkDialog
      v-if="linkVisible"
      :visible="linkVisible"
      :planId="enterpItem.id"
      @closeBoolean="linkVisible = false"
    />
  </div>
</template>

<script>
import {
  getEnterpriseList,
  getKnowledgeListData,
  enterpriseType,
} from "@/api/entList";
import {
  getPortraitPlanPage,
  getPortraitPlanDelete,
} from "@/api/enterprisePortrait";
import { createNamespacedHelpers } from "vuex";
import LinkDialog from "./components/linkDialog.vue";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: { LinkDialog },
  data() {
    return {
      // district: this.$store.state.controler.district,
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      districtVal: this.$store.state.login.userDistCode,
      loading: false,
      searchParams: {
        //   distCode: this.$store.state.login.userDistCode,
        ids: [],
        name: "",
        nowPage: 1,
        pageSize: 10,
        status: "",
      },
      total: 0,
      tableData: [],
      entType: [],
      processIdsOption: [],
      entTypeOption: [],
      auditStatusList: [
        { label: "审核通过", value: "1" },
        { label: "审核不通过", value: "2" },
        { label: "待审核", value: "3" },
      ],
      levelOptions: [
        { label: "一级", value: "1" },
        { label: "二级", value: "2" },
        { label: "三级", value: "3" },
        { label: "四级", value: "4" },
      ],
      showAnalysis: false,
      enterpItem: {},
      linkVisible: false,
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
    }),
    ...mapStateControler({
      district: (state) => state.district,
    }),
  },
  mounted() {
    // this.getProcess();
    // this.getEnterpriseType();
    this.getData();
  },
  methods: {
    handleSearch() {
      this.searchParams.nowPage = 1;
      this.getData();
    },
    //翻页
    handleCurrentChange(val) {
      this.searchParams.nowPage = val;
      this.getData();
    },
    handleClick(row) {
      this.enterpItem = row;
      this.showAnalysis = true;
      this.$emit("check", row);
    },
    // 关联
    handleLink(row) {
      this.linkVisible = true;
      this.enterpItem = row;
      console.log(row);
    },
    handleDelete(row) {
      this.$confirm("确定删除吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getPortraitPlanDelete({ id: row.id }).then((res) => {
            if (res.data.status == 200) {
              this.getData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    closeBoolean(boolean) {
      this.showAnalysis = boolean;
      this.enterpItem = {};
    },
    // 获取工艺
    getProcess() {
      getKnowledgeListData({
        processid: "",
        processname: "",
      }).then((res) => {
        if (res.data.status === 200) {
          this.processIdsOption = res.data.data;
        }
      });
    },
    // 获取企业类型
    getEnterpriseType() {
      enterpriseType({}).then((res) => {
        this.entType = res.data.data;
      });
    },
    async getData() {
      const params = {
        ...this.searchParams,
      };
      await getPortraitPlanPage(params).then((res) => {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "合计";
          return;
        }
        if (index === 3) {
          sums[index] = "100%";
          return;
        }
        const values = data.map((item) => Number(item[column.property]));
        if (!values.every((value) => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] += " ";
        } else {
          sums[index] = "";
        }
      });
      return sums;
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .subTable.el-table td.el-table__cell,
/deep/ .subTable.el-table th.el-table__cell.is-leaf,
/deep/ .el-table--border .subTable.el-table__cell {
  border-bottom: 0;
  border-right: 0;
}

/deep/ .el-table--border .subTable {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
}

.titTop {
  display: flex;
  justify-content: space-between;

  /deep/ .el-radio-group {
    margin: 0 0 0 0;
  }
}

.operation {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;

  .inputBox {
    min-width: 1150px;
    display: flex;
    justify-content: flex-start;

    .input {
      width: 200px;
    }

    > * {
      margin-right: 15px;
    }
  }
}

.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
