<template>
  <div class="home-tab">
    <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="已上报企业" name="reported">
            <!-- 已上报企业 -->
            <Reported v-if="isChildUpdate1"></Reported>
        </el-tab-pane>
        <el-tab-pane label="未上报企业" name="notSecond">
            <!-- 未上报企业 -->
            <NotReported v-if="isChildUpdate2"></NotReported>
        </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import Reported from './reported';
import NotReported from './notReport';

export default {
    name: 'Home',
    components: {
        Reported,
        NotReported
    },
    data() {
      return {
        activeName: 'reported',
        isChildUpdate1:true,
        isChildUpdate2:false
      }
    },
    methods: {
      handleClick(tab, event) {
         if(tab.name == "reported") {
            this.isChildUpdate1 = true;
            this.isChildUpdate2 = false;
            this.$router.push({ name: `Home`});
            this.activeName = "reported";
        } else if(tab.name == "notSecond") {
            this.isChildUpdate1 = false;
            this.isChildUpdate2 = true;
            this.$router.push({ name: `Home`});
            this.activeName = "notSecond";
        }
      }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style>
.home-tab{
    /* margin-top: 60px; */
}
</style>

<style scoped>
.el-tabs__nav-wrap::after{
    height: 0px!important;
}

.el-tabs__nav-wrap {
    padding: 20px;
    background: #fff;
}
.el-tabs__header {
    margin: 0;
} 
</style>
