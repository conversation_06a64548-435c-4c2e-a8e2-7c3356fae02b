<template>
  <div v-show="visible"  class="showPhoto">
    <div class="el-icon-circle-close closeStyle" @click="closeClick"></div>
    <img class="img" :src="url" alt="图片加载失败" />
  </div>
</template>

<script>
import {
  getEnterpConsumption, //企业用电量查询
} from "@/api/riskAssessment";
import { getSearchArr } from "@/api/entList.js";
import { getInformationBasicInfo } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
var dayjs = require("dayjs");
export default {
  //import引入的组件
  name: "showPhoto",
  components: {},
  props: {
    url: {
      type: String,
      default: "",
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {};
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      userPark: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
  },
  methods: {
    closeClick() {
      //子组件可以使用 $emit 触发父组件的自定义事件
      this.$emit("closeClick");
    },
  },

  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
.closeStyle {
    font-size: 37px;
    position: absolute;
    top: 50px;
    color: #fff;
    right: 50px;
    z-index: 99999;
    cursor: pointer;
}
.closeStyle:hover{
  color:#409EFF
}
.showPhoto {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.showPhoto .img {
  display: block;
  margin: auto 0;
  max-width: 100%;
  text-align: center;
}
</style>