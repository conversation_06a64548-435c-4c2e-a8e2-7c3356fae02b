<template>
  <div style="width: 100%; height: 100%; background-color: #000000">
    <section v-if="!loadStatus" style="width: 100%; height: 100%">
      <video
        autoplay
        controls
        width="100%"
        height="100%"
        :id="id"
        muted
      ></video>
    </section>
    <div
      v-else
      style="
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      "
    >
      <span style="color: wheat; font-size: 14px"
        ><i class="el-icon-warning-outline" style="margin-right: 10px"></i
        >{{ statusMsg }}</span
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "viewVideo",
  data() {
    return {
      src: "", //flv格式的地址
      timerId: null,
      loadStatus: true,
      id: "videoElement" + Math.random(),
      statusMsg: "未选择视频",
      lastDecodedFrame: 0,
      flvPlayer: null,
    };
  },
  created() {
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.setsrc();
    });
  },
  destroyed() {
    console.log("destorying video");
    if (this.timerId) {
      clearInterval(this.timerId);
      this.timerId = null;
    }
    if (this.flvPlayer !== null) {
      this.flvPlayer.pause();
      this.flvPlayer.destroy();
      this.flvPlayer = null;
    }
  },
  methods: {
    createVideo() {
      let that = this;
      console.log(that.flvPlayer);
      if (that.flvPlayer) {
        that.flvPlayer.pause();
        that.flvPlayer.destroy();
        that.flvPlayer = null;
      }
      if (that.timerId !== null) {
        clearInterval(that.timerId);
      }
      that.videoElement = document.getElementById(that.id);
      if (flvjs.isSupported()) {
        that.flvPlayer = flvjs.createPlayer(
          {
            type: "flv", // 指定视频类型，特别注意此处伟flv
            isLive: true, // 开启直播
            hasAudio: false, //需要设置为false不然播放不了视频
            cors: true, // 开启跨域访问
            url: that.src,
          },
          {
            enableWorker: false, //不启用分离线程
            enableStashBuffer: false, //关闭IO隐藏缓冲区
            reuseRedirectedURL: true, //重用301/302重定向url，用于随后的请求，如查找、重新连接等。
            autoCleanupSourceBuffer: true, //自动清除缓存
          }
        );
      }
      that.flvPlayer.on(
        flvjs.Events.ERROR,
        (errorType, errorDetail, errorInfo) => {
          console.log("errorType:", errorType);
          console.log("errorDetail:", errorDetail);
          console.log("errorInfo:", errorInfo);
          // this.loadStatus=true
          // this.statusMsg="正在重连。。。"
          //视频出错后销毁重新创建
          if (that.flvPlayer) {
            that.flvPlayer.pause();
            that.flvPlayer.unload();
            that.flvPlayer.detachMediaElement();
            that.flvPlayer.destroy();
            that.flvPlayer = null;
          }
          that.createVideo();
        }
      );
      that.flvPlayer.on("statistics_info", function (res) {
        if (that.lastDecodedFrame == 0) {
          that.lastDecodedFrame = res.decodedFrames;
          return;
        }
        if (that.lastDecodedFrame != res.decodedFrames) {
          that.lastDecodedFrame = res.decodedFrames;
        } else {
          that.lastDecodedFrame = 0;
          if (that.flvPlayer) {
            that.flvPlayer.pause();
            that.flvPlayer.unload();
            that.flvPlayer.detachMediaElement();
            that.flvPlayer.destroy();
            that.flvPlayer = null;
            that.createVideo();
          }
        }
      });

      that.flvPlayer.attachMediaElement(this.videoElement);
      that.flvPlayer.load();
      that.flvPlayer.play();
      if (that.timerId !== null) {
        clearInterval(that.timerId);
      }
      that.timerId = setInterval(() => {
        if (that.videoElement.buffered.length > 0) {
          const end = that.videoElement.buffered.end(0); // 视频结尾时间
          const current = that.videoElement.currentTime; //  视频当前时间
          const diff = end - current; // 相差时间
          console.log(diff);
          const diffCritical = 4; // 这里设定了超过4秒以上就进行跳转
          const diffSpeedUp = 1; // 这里设置了超过1秒以上则进行视频加速播放
          const maxPlaybackRate = 4; // 自定义设置允许的最大播放速度
          let playbackRate = 1.0; // 播放速度
          if (diff > diffCritical) {
            console.log("相差超过4秒，进行跳转");
            that.videoElement.currentTime = end - 1.5;
            playbackRate = Math.max(1, Math.min(diffCritical, 16));
          } else if (diff > diffSpeedUp) {
            console.log("相差超过1秒，进行加速");
            playbackRate = Math.max(1, Math.min(diff, maxPlaybackRate, 16));
          }
          that.videoElement.playbackRate = playbackRate;
          if (that.videoElement.paused) {
            that.flvPlayer.play();
          }
        }
      }, 1000);
    },
    setsrc() {
      if (this.url) {
        this.src = this.url;
        this.createVideo();
      }
    },
  },
  props: {
    url: {
      default: null,
      required: true,
      type: String | null,
    },
  },
  watch: {
    url: {
      handler(newValue) {
        if (newValue != null) {
          this.loadStatus = false;
          this.src = newValue;
          this.$nextTick(() => {
            this.setsrc();
          });
        }
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
