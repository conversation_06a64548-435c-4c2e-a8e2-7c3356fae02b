import axios from "axios";
import qs from "qs";
import CryptoJS from "crypto-js";
//企业工作台物联监测统计
export const getCompanyAlarminfo = data => {
  return axios({
    method: "post",
    url: "/enterprise/alarmAnalysis/companyAlarminfo",
    data: data
  });
};
//企业工作台视频监测统计
export const getCompanyVideoInfo = data => {
  return axios({
    method: "post",
    url: "/enterprise/video/companyVideoInfo",
    data: data
  });
};
//点位数
export const getCompanyVideoDetails = data => {
  return axios({
    method: "post",
    url:
      "/enterprise/video/companyVideoDetails?current=" +
      data.current +
      "&size=10",
    data: data
  });
};
//企业当前预警
export const getCimEarlyWarningPushList = data => {
  return axios({
    method: "post",
    url: "/enterprise/cimEarlyWarning/cimEarlyWarningPushList?size=1",
    data: data
  });
};
//指标数
export const getSelectCompanyTargetInfo = data => {
  return axios({
    method: "post",
    url:
      "/enterprise/onlineNetworkingAnalysis/selectCompanyTargetInfo?current=" +
      data.current +
      "&size=10",
    data: data
  });
};
export const getMessageList = data => {
  return axios({
    method: "get",
    url: "/file/message/list",
    params: data
  });
};
//物联报警推送
export const getMessageListData = data => {
  return axios({
    method: "get",
    url: "/file/message/list?type=5&current=1",
    data: qs.stringify(data)
  });
};
//初始化消息推送数字
export const getMessageNum = data => {
  return axios({
    method: "get",
    url: "/file/message/num",
    data: qs.stringify(data)
  });
};
//初始化消息推送数字
export const postMessageRead = data => {
  // console.log(data);
  return axios({
    method: "post",
    url: "/file/message/read",
    data: qs.stringify(data)
  });
};
//预警推送规则列表
export const getPushRoleList = data => {
  return axios({
    method: "get",
    url: "/admin/pushRole/list"
  });
};
//预警推送规则编辑
export const getPushRoleUpdate = data => {
  return axios({
    method: "post",
    url: "/admin/pushRole/update",
    data: data
  });
};
//预览视屏
export const postVideoH5SelVideoH5Url = data => {
  return axios({
    method: "post",
    url: `/enterprise/videoH5/selVideoH5Url/${data.cameraId}`,
  });
};