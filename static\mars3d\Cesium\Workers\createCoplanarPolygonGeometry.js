define(["./arrayRemoveDuplicates-fe254feb","./BoundingRectangle-00ff5b29","./Transforms-06c05e21","./Matrix2-e6265fb0","./RuntimeError-ac440aa5","./ComponentDatatype-a9820060","./CoplanarPolygonGeometryLibrary-5f6be5a7","./defaultValue-69ee94f4","./GeometryAttribute-b7edcc35","./GeometryAttributes-1b4134a9","./GeometryInstance-19ac39d5","./GeometryPipeline-311a1f9e","./IndexDatatype-1cbc8622","./PolygonGeometryLibrary-a64b67ab","./PolygonPipeline-1015dc5c","./VertexFormat-e68722dd","./_commonjsHelpers-3aae1032-15991586","./combine-0259f56f","./WebGLConstants-f63312fc","./OrientedBoundingBox-0f36ec8f","./EllipsoidTangentPlane-01395a15","./AxisAlignedBoundingBox-5ba8fa79","./IntersectionTests-94cb8698","./Plane-042297c7","./AttributeCompression-6e71d14f","./EncodedCartesian3-20914bf5","./ArcType-e1641d8d","./EllipsoidRhumbLine-94e77fa4"],(function(e,t,n,o,a,r,i,s,l,c,p,y,u,m,d,g,f,b,h,x,C,P,A,w,F,G,L,E){"use strict";const v=new o.Cartesian3,T=new t.BoundingRectangle,D=new o.Cartesian2,_=new o.Cartesian2,V=new o.Cartesian3,k=new o.Cartesian3,R=new o.Cartesian3,I=new o.Cartesian3,M=new o.Cartesian3,B=new o.Cartesian3,H=new n.Quaternion,O=new o.Matrix3,z=new o.Matrix3,S=new o.Cartesian3;function N(e,t,a,i,s,p,y,m){const g=e.positions;let f=d.PolygonPipeline.triangulate(e.positions2D,e.holes);f.length<3&&(f=[0,1,2]);const b=u.IndexDatatype.createTypedArray(g.length,f.length);b.set(f);let h=O;if(0!==i){let e=n.Quaternion.fromAxisAngle(p,i,H);if(h=o.Matrix3.fromQuaternion(e,h),t.tangent||t.bitangent){e=n.Quaternion.fromAxisAngle(p,-i,H);const a=o.Matrix3.fromQuaternion(e,z);y=o.Cartesian3.normalize(o.Matrix3.multiplyByVector(a,y,y),y),t.bitangent&&(m=o.Cartesian3.normalize(o.Cartesian3.cross(p,y,m),m))}}else h=o.Matrix3.clone(o.Matrix3.IDENTITY,h);const x=_;t.st&&(x.x=a.x,x.y=a.y);const C=g.length,P=3*C,A=new Float64Array(P),w=t.normal?new Float32Array(P):void 0,F=t.tangent?new Float32Array(P):void 0,G=t.bitangent?new Float32Array(P):void 0,L=t.st?new Float32Array(2*C):void 0;let E=0,T=0,V=0,k=0,R=0;for(let e=0;e<C;e++){const n=g[e];if(A[E++]=n.x,A[E++]=n.y,A[E++]=n.z,t.st){const e=s(o.Matrix3.multiplyByVector(h,n,v),D);o.Cartesian2.subtract(e,x,e);const t=r.CesiumMath.clamp(e.x/a.width,0,1),i=r.CesiumMath.clamp(e.y/a.height,0,1);L[R++]=t,L[R++]=i}t.normal&&(w[T++]=p.x,w[T++]=p.y,w[T++]=p.z),t.tangent&&(F[k++]=y.x,F[k++]=y.y,F[k++]=y.z),t.bitangent&&(G[V++]=m.x,G[V++]=m.y,G[V++]=m.z)}const I=new c.GeometryAttributes;return t.position&&(I.position=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.DOUBLE,componentsPerAttribute:3,values:A})),t.normal&&(I.normal=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:w})),t.tangent&&(I.tangent=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:F})),t.bitangent&&(I.bitangent=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:3,values:G})),t.st&&(I.st=new l.GeometryAttribute({componentDatatype:r.ComponentDatatype.FLOAT,componentsPerAttribute:2,values:L})),new l.Geometry({attributes:I,indices:b,primitiveType:l.PrimitiveType.TRIANGLES})}function Q(e){const t=(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).polygonHierarchy,n=s.defaultValue(e.vertexFormat,g.VertexFormat.DEFAULT);this._vertexFormat=g.VertexFormat.clone(n),this._polygonHierarchy=t,this._stRotation=s.defaultValue(e.stRotation,0),this._ellipsoid=o.Ellipsoid.clone(s.defaultValue(e.ellipsoid,o.Ellipsoid.WGS84)),this._workerName="createCoplanarPolygonGeometry",this.packedLength=m.PolygonGeometryLibrary.computeHierarchyPackedLength(t)+g.VertexFormat.packedLength+o.Ellipsoid.packedLength+2}Q.fromPositions=function(e){return new Q({polygonHierarchy:{positions:(e=s.defaultValue(e,s.defaultValue.EMPTY_OBJECT)).positions},vertexFormat:e.vertexFormat,stRotation:e.stRotation,ellipsoid:e.ellipsoid})},Q.pack=function(e,t,n){return n=s.defaultValue(n,0),n=m.PolygonGeometryLibrary.packPolygonHierarchy(e._polygonHierarchy,t,n),o.Ellipsoid.pack(e._ellipsoid,t,n),n+=o.Ellipsoid.packedLength,g.VertexFormat.pack(e._vertexFormat,t,n),n+=g.VertexFormat.packedLength,t[n++]=e._stRotation,t[n]=e.packedLength,t};const j=o.Ellipsoid.clone(o.Ellipsoid.UNIT_SPHERE),U=new g.VertexFormat,Y={polygonHierarchy:{}};return Q.unpack=function(e,t,n){t=s.defaultValue(t,0);const a=m.PolygonGeometryLibrary.unpackPolygonHierarchy(e,t);t=a.startingIndex,delete a.startingIndex;const r=o.Ellipsoid.unpack(e,t,j);t+=o.Ellipsoid.packedLength;const i=g.VertexFormat.unpack(e,t,U);t+=g.VertexFormat.packedLength;const l=e[t++],c=e[t];return s.defined(n)||(n=new Q(Y)),n._polygonHierarchy=a,n._ellipsoid=o.Ellipsoid.clone(r,n._ellipsoid),n._vertexFormat=g.VertexFormat.clone(i,n._vertexFormat),n._stRotation=l,n.packedLength=c,n},Q.createGeometry=function(t){const a=t._vertexFormat,s=t._polygonHierarchy,c=t._stRotation;let d=s.positions;if(d=e.arrayRemoveDuplicates(d,o.Cartesian3.equalsEpsilon,!0),d.length<3)return;let g=V,f=k,b=R,h=M;const x=B;if(!i.CoplanarPolygonGeometryLibrary.computeProjectTo2DArguments(d,I,h,x))return;if(g=o.Cartesian3.cross(h,x,g),g=o.Cartesian3.normalize(g,g),!o.Cartesian3.equalsEpsilon(I,o.Cartesian3.ZERO,r.CesiumMath.EPSILON6)){const e=t._ellipsoid.geodeticSurfaceNormal(I,S);o.Cartesian3.dot(g,e)<0&&(g=o.Cartesian3.negate(g,g),h=o.Cartesian3.negate(h,h))}const C=i.CoplanarPolygonGeometryLibrary.createProjectPointsTo2DFunction(I,h,x),P=i.CoplanarPolygonGeometryLibrary.createProjectPointTo2DFunction(I,h,x);a.tangent&&(f=o.Cartesian3.clone(h,f)),a.bitangent&&(b=o.Cartesian3.clone(x,b));const A=m.PolygonGeometryLibrary.polygonsFromHierarchy(s,C,!1),w=A.hierarchy,F=A.polygons;if(0===w.length)return;d=w[0].outerRing;const G=n.BoundingSphere.fromPoints(d),L=m.PolygonGeometryLibrary.computeBoundingRectangle(g,P,d,c,T),E=[];for(let e=0;e<F.length;e++){const t=new p.GeometryInstance({geometry:N(F[e],a,L,c,P,g,f,b)});E.push(t)}const v=y.GeometryPipeline.combineInstances(E)[0];v.attributes.position.values=new Float64Array(v.attributes.position.values),v.indices=u.IndexDatatype.createTypedArray(v.attributes.position.values.length/3,v.indices);const D=v.attributes;return a.position||delete D.position,new l.Geometry({attributes:D,indices:v.indices,primitiveType:v.primitiveType,boundingSphere:G})},function(e,t){return s.defined(t)&&(e=Q.unpack(e,t)),Q.createGeometry(e)}}));
