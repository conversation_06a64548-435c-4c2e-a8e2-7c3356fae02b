/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.4.0
 * 编译日期：2022-07-14 15:06:04
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2022-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';var _0xee8b26=_0x1102;(function(_0x521bf9,_0x32e0c2){var _0x475284=_0x1102,_0x8e6b86=_0x521bf9();while(!![]){try{var _0x2de07c=-parseInt(_0x475284(0x267))/0x1+parseInt(_0x475284(0x290))/0x2+parseInt(_0x475284(0x1b0))/0x3*(-parseInt(_0x475284(0x309))/0x4)+parseInt(_0x475284(0x276))/0x5+-parseInt(_0x475284(0x2bd))/0x6*(-parseInt(_0x475284(0x2d6))/0x7)+parseInt(_0x475284(0x26f))/0x8+-parseInt(_0x475284(0x1f6))/0x9;if(_0x2de07c===_0x32e0c2)break;else _0x8e6b86['push'](_0x8e6b86['shift']());}catch(_0x4dfe64){_0x8e6b86['push'](_0x8e6b86['shift']());}}}(_0x462a,0xa6390));function _interopNamespace(_0x327a0d){var _0x369af4=_0x1102;if(_0x327a0d&&_0x327a0d[_0x369af4(0x2a0)])return _0x327a0d;var _0x45119b=Object[_0x369af4(0x200)](null);return _0x327a0d&&Object[_0x369af4(0x223)](_0x327a0d)['forEach'](function(_0x2f9c4c){var _0x1aaf2b=_0x369af4;if(_0x2f9c4c!==_0x1aaf2b(0x2fd)){var _0x1af1a4=Object[_0x1aaf2b(0x27a)](_0x327a0d,_0x2f9c4c);Object['defineProperty'](_0x45119b,_0x2f9c4c,_0x1af1a4[_0x1aaf2b(0x266)]?_0x1af1a4:{'enumerable':!![],'get':function(){return _0x327a0d[_0x2f9c4c];}});}}),_0x45119b[_0x369af4(0x2fd)]=_0x327a0d,_0x45119b;}var mars3d__namespace=_interopNamespace(mars3d),Cesium$7=mars3d__namespace[_0xee8b26(0x2f7)];function getU(_0x446f62,_0x3df447){var _0x269eea=_0xee8b26,_0x15a92c=_0x446f62*Math[_0x269eea(0x2a1)](Cesium$7[_0x269eea(0x291)][_0x269eea(0x304)](_0x3df447));return _0x15a92c;}function getV(_0x2b711c,_0x3b6ac8){var _0x146138=_0xee8b26,_0x18bf8b=_0x2b711c*Math[_0x146138(0x2d0)](Cesium$7[_0x146138(0x291)][_0x146138(0x304)](_0x3b6ac8));return _0x18bf8b;}function getSpeed(_0x246f33,_0x5c0fd6){var _0x4ca045=_0xee8b26,_0x35c9f6=Math['sqrt'](Math[_0x4ca045(0x1d2)](_0x246f33,0x2)+Math[_0x4ca045(0x1d2)](_0x5c0fd6,0x2));return _0x35c9f6;}function getDirection(_0x34582a,_0x400dba){var _0x1c48e0=_0xee8b26,_0x1475bb=Cesium$7['Math'][_0x1c48e0(0x225)](Math['atan2'](_0x400dba,_0x34582a));return _0x1475bb+=_0x1475bb<0x0?0x168:0x0,_0x1475bb;}var WindUtil={'__proto__':null,'getU':getU,'getV':getV,'getSpeed':getSpeed,'getDirection':getDirection};function ownKeys(_0x1a991c,_0x396b33){var _0x4cb5b5=_0xee8b26,_0x33600f=Object[_0x4cb5b5(0x223)](_0x1a991c);if(Object[_0x4cb5b5(0x1d0)]){var _0x421436=Object['getOwnPropertySymbols'](_0x1a991c);_0x396b33&&(_0x421436=_0x421436[_0x4cb5b5(0x1b4)](function(_0x30f721){var _0x545ae2=_0x4cb5b5;return Object['getOwnPropertyDescriptor'](_0x1a991c,_0x30f721)[_0x545ae2(0x29b)];})),_0x33600f[_0x4cb5b5(0x19b)][_0x4cb5b5(0x264)](_0x33600f,_0x421436);}return _0x33600f;}function _objectSpread2(_0x10ba92){var _0xddd164=_0xee8b26;for(var _0x40965f=0x1;_0x40965f<arguments['length'];_0x40965f++){var _0x1640ec=null!=arguments[_0x40965f]?arguments[_0x40965f]:{};_0x40965f%0x2?ownKeys(Object(_0x1640ec),!0x0)[_0xddd164(0x30d)](function(_0x809655){_defineProperty(_0x10ba92,_0x809655,_0x1640ec[_0x809655]);}):Object[_0xddd164(0x294)]?Object[_0xddd164(0x27c)](_0x10ba92,Object['getOwnPropertyDescriptors'](_0x1640ec)):ownKeys(Object(_0x1640ec))['forEach'](function(_0x4642b1){var _0xe00f79=_0xddd164;Object[_0xe00f79(0x21a)](_0x10ba92,_0x4642b1,Object[_0xe00f79(0x27a)](_0x1640ec,_0x4642b1));});}return _0x10ba92;}function _classCallCheck(_0x22d669,_0x224777){var _0x59d002=_0xee8b26;if(!(_0x22d669 instanceof _0x224777))throw new TypeError(_0x59d002(0x1f8));}function _defineProperties(_0x3888a2,_0x30505d){var _0x5741dd=_0xee8b26;for(var _0x9d9b89=0x0;_0x9d9b89<_0x30505d[_0x5741dd(0x2e1)];_0x9d9b89++){var _0x2516d5=_0x30505d[_0x9d9b89];_0x2516d5[_0x5741dd(0x29b)]=_0x2516d5[_0x5741dd(0x29b)]||![],_0x2516d5[_0x5741dd(0x268)]=!![];if(_0x5741dd(0x2e9)in _0x2516d5)_0x2516d5['writable']=!![];Object[_0x5741dd(0x21a)](_0x3888a2,_0x2516d5[_0x5741dd(0x230)],_0x2516d5);}}function _createClass(_0x323456,_0x44f5ad,_0xb88473){var _0x16fa4a=_0xee8b26;if(_0x44f5ad)_defineProperties(_0x323456[_0x16fa4a(0x1da)],_0x44f5ad);if(_0xb88473)_defineProperties(_0x323456,_0xb88473);return Object[_0x16fa4a(0x21a)](_0x323456,_0x16fa4a(0x1da),{'writable':![]}),_0x323456;}function _defineProperty(_0x2b748c,_0x488421,_0x51910a){var _0x59d60d=_0xee8b26;return _0x488421 in _0x2b748c?Object[_0x59d60d(0x21a)](_0x2b748c,_0x488421,{'value':_0x51910a,'enumerable':!![],'configurable':!![],'writable':!![]}):_0x2b748c[_0x488421]=_0x51910a,_0x2b748c;}function _inherits(_0x2580ba,_0x3986eb){var _0x34a3f4=_0xee8b26;if(typeof _0x3986eb!==_0x34a3f4(0x2ec)&&_0x3986eb!==null)throw new TypeError(_0x34a3f4(0x2f3));_0x2580ba[_0x34a3f4(0x1da)]=Object[_0x34a3f4(0x200)](_0x3986eb&&_0x3986eb['prototype'],{'constructor':{'value':_0x2580ba,'writable':!![],'configurable':!![]}}),Object[_0x34a3f4(0x21a)](_0x2580ba,'prototype',{'writable':![]});if(_0x3986eb)_setPrototypeOf(_0x2580ba,_0x3986eb);}function _getPrototypeOf(_0x3916a1){var _0x2f347f=_0xee8b26;return _getPrototypeOf=Object[_0x2f347f(0x301)]?Object[_0x2f347f(0x25f)]:function _0x4bbae2(_0x197205){var _0x48fa8b=_0x2f347f;return _0x197205['__proto__']||Object[_0x48fa8b(0x25f)](_0x197205);},_getPrototypeOf(_0x3916a1);}function _setPrototypeOf(_0x4b6ce2,_0x20199f){var _0x49c0bd=_0xee8b26;return _setPrototypeOf=Object[_0x49c0bd(0x301)]||function _0x33103d(_0x56e4b8,_0x2baa84){var _0x3c19b0=_0x49c0bd;return _0x56e4b8[_0x3c19b0(0x27e)]=_0x2baa84,_0x56e4b8;},_setPrototypeOf(_0x4b6ce2,_0x20199f);}function _isNativeReflectConstruct(){var _0x57c0a9=_0xee8b26;if(typeof Reflect===_0x57c0a9(0x2c4)||!Reflect[_0x57c0a9(0x300)])return![];if(Reflect[_0x57c0a9(0x300)][_0x57c0a9(0x283)])return![];if(typeof Proxy===_0x57c0a9(0x2ec))return!![];try{return Boolean[_0x57c0a9(0x1da)][_0x57c0a9(0x26d)]['call'](Reflect[_0x57c0a9(0x300)](Boolean,[],function(){})),!![];}catch(_0x2f7b06){return![];}}function _0x462a(){var _0x469348=['x19LC01VzhvSzq','y29Z','Bwf4','mtaWjq','zNjHBwvsyxrL','C2vNBwvUDhm','uKDcqq','q29TCg9Uzw50rgf0yxr5Cgu','zxHLy3v0zq','zNjVBurLz3jLzxm','CgL4zwXtAxPL','y2fUDMfZsgvPz2H0','Bgv2','BMv4DfrYywLSC0rLChrO','Ew1HEa','Dw5PzM9YBsbZyw1WBgvYmKqGy29SB3juywjSztSkcNzHCNLPBMCGzMXVyxqGC3bLzwroB3jTywXPEMf0Aw9UoWOkDM9PzcbTywLUkcKGEWOGicaGz2XFrNjHz0nVBg9Yid0GDgv4DhvYztjekgnVBg9YvgfIBguSihzLyZiOC3bLzwroB3jTywXPEMf0Aw9UlcaWlJaPktSkFq','y3jLyxrLu2vNBwvUDhnhzw9TzxrYEq','y3jLyxrLrNjHBwvIDwzMzxi','DMLLD3bVCNq','y2XLyxjgCMfTzwj1zMzLCNm','C2vNBwvUDhnezxb0Aa','CgfYDgLJBgvZvgv4DhvYzxm','DgXHDa','Dg9W','CMfUzg9TugfYDgLJBgu','Bw91C2vvCa','CMfUzg9TqMv0D2vLBG','sw52ywXPzcbHDhrLBxb0ihrVihnWCMvHzcbUB24TAxrLCMfIBguGAw5ZDgfUy2uUcKLUig9YzgvYihrVigjLigL0zxjHyMXLlcbUB24TyxjYyxKGB2jQzwn0CYbTDxn0igHHDMuGysbBu3LTyM9SlML0zxjHDg9YxsGPig1LDgHVzc4','qNvMzMvYvxnHz2u','odrKBMvfENC','DMrHDge','z2XVyMu','z2XVyMfSq29TCg9ZAxrLt3bLCMf0Aw9U','x2rHDge','zg9JDw1LBNq','AxnjBKv4DgvUDa','Dw5KzwzPBMvK','u2HHzgvYuhjVz3jHBq','zMLSBfjLy3q','C2v0qxr0CMLIDxrL','Bgf0','CMvK','z2XVyMvcB3vUzgLUz1nWAgvYzq','ugL4zwXeyxrHDhLWzq','CMvTB3zLqwXS','BMv4DfbHCNrPy2XLC1bVC2L0Aw9U','y29UC3rYDwn0B3i','C3rYAw5N','C2LU','DgXUzW','Dw5IAw5KrxzLBNq','CMv2zxjZzq','C2vNBwvUDhndB2XVCG','Dw5PzM9YBsbZyw1WBgvYmKqGDhjHAwXZq29SB3juzxH0DxjLoW0kDw5PzM9YBsbZyw1WBgvYmKqGDhjHAwXZrgvWDgHuzxH0DxjLoW0kdqP2yxj5Aw5NihzLyZiGDgv4DhvYzunVB3jKAw5HDgu7dqOncNzVAwqGBwfPBIGPihSncIaGicb2zwm0ihrYywLSC0nVBg9Yid0GDgv4DhvYztjekhrYywLSC0nVBg9Yvgv4DhvYzsWGDgv4DhvYzunVB3jKAw5HDguPoW0kicaGigzSB2f0ihrYywLSC0rLChrOid0GDgv4DhvYztjekhrYywLSC0rLChrOvgv4DhvYzsWGDgv4DhvYzunVB3jKAw5HDguPlNi7dqOGicaGzMXVyxqGz2XVyMvezxb0Aca9ign6Bv91BNbHy2Tezxb0AcH0zxH0DxjLmKqOy3PTx2DSB2jLrgvWDgHuzxH0DxjLlcb0zxH0DxjLq29VCMrPBMf0zsKPoW0kdqOGicaGAwyGkhrYywLSC0rLChrOidWGz2XVyMvezxb0AcKGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsb0CMfPBhndB2XVCJSncIaGicb9igvSC2uGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsb2zwm0kdaUmcK7dqOGicaGFq0kFq','mJe0mdm5t0jsDgjb','Bw9Kzq','uKDc','zML4zwrizwLNAhq','y3jLyxrLuMvUzgvYAw5NuhjPBwL0AxzLCW','yxv0BW','Dw5PzM9YBu1HCa','q29SB3i','qMfZzuXHEwvY','zhjVCfjHDgvcDw1W','D2LUzezPzwXK','BgvUz3rO','zNjVBq','Bw92zvrV','D2LUza','mhb4','AxnbCNjHEq','EKLUzgv4','ywjZB2X1Dgu','DMfSDwu','C2v0t3b0Aw9UCW','yMLUzev2zw50','zNvUy3rPB24','yxjYyxK','ChjLuMvUzgvY','CgfYDgLJBgvZvgv4DhvYzvnPEMu','x21HEefNzq','zMXVB3i','x29UtwfWx3bYzvjLBMrLCKv2zw50','u3vWzxiGzxHWCMvZC2LVBIbTDxn0igvPDgHLCIbIzsbUDwXSig9YigeGzNvUy3rPB24','q2XLyxjdB21Tyw5K','lY8GDgHLihnPEMuGB2yGvvyGDgv4DhvYzxm6ihDPzhrOid0GBg9UlcbOzwLNAhqGpsbSyxqQBgv2cNvUAwzVCM0GC2fTCgXLCJjeifu7ic8VigvHC3r3yxjKihDPBMqGcNvUAwzVCM0GC2fTCgXLCJjeify7ic8Vig5VCNrOD2fYzcb3Aw5KcGP1BMLMB3jTihnHBxbSzxiYrcbJDxjYzw50ugfYDgLJBgvZug9ZAxrPB247ic8VicHSB24SigXHDcWGBgv2kqOkDw5PzM9YBsb2zwmZigrPBwvUC2LVBJSGlY8GkgXVBIWGBgf0lcbSzxyPcNvUAwzVCM0GDMvJmYbTAw5PBxvToYaVlYbTAw5PBxvTig9MigvHy2GGzgLTzw5ZAw9UcNvUAwzVCM0GDMvJmYbTyxHPBxvToYaVlYbTyxHPBxvTig9MigvHy2GGzgLTzw5ZAw9UcNvUAwzVCM0GDMvJmYbPBNrLCNzHBdSGlY8GAw50zxj2ywWGB2yGzwfJAcbKAw1LBNnPB24kcNzHCNLPBMCGDMvJmIb2x3rLEhr1CMvdB29YzgLUyxrLCZSkcNzLyZiGBwfWug9ZAxrPB25uB05VCM1HBgL6zwrjBMrLEdjekhzLyZmGBg9Utgf0tgv2ksb7cIaGicaVlYbLBNn1CMuGDgHLihjHBMDLig9MigXVBMDPDhvKzsbHBMqGBgf0Axr1zgukicaGigXVBKXHDeXLDI54id0GBw9KkgXVBKXHDeXLDI54lcaZnJaUmcK7cIaGicbSB25myxrmzxyUEsa9ignSyw1WkgXVBKXHDeXLDI55lcaTotaUmcWGotaUmcK7cGOGicaGDMvJmYbPBMrLEdneid0GDMvJmYGWlJaPoWOGicaGAw5KzxGZrc54id0GkgXVBKXHDeXLDI54ic0GBwLUAw11Bs54ksaVigLUDgvYDMfSlNG7cIaGicbPBMrLEdnelNKGpsaOBg9Utgf0tgv2lNKGlsbTAw5PBxvTlNKPic8GAw50zxj2ywWUEtSkicaGigLUzgv4m0qUEIa9icHSB25myxrmzxyUEIaTig1PBMLTDw0UEIKGlYbPBNrLCNzHBc56oWOkicaGic8VihrOzsbZDcb0zxH0DxjLignVB3jKAw5HDguGy29YCMvZCg9UzgLUzYb0BYaOy29SlcbYB3CPigLUzgv4cIaGicaVlYbLEgfTCgXLcIaGicaVlYbKyxrHigfYCMf5igLZifSWlcaXlcaYlcaZlca0lca1xsWGD2LKDgGGpsaZlcbOzwLNAhqGpsaYcIaGicaVlYb0AguGy29UDgvUDcbVzIb0zxH0DxjLihDPBgWGyMukicaGic8VihqGms4WcIaGicaVlYaGicb8icaZidqGnqOGicaGlY8GicaGFaOGicaGlY8GicaGFcaGmcaXidikicaGic8VicaGmc4Wls0Tls0Tms4WihmkcIaGicb2zwmYigLUzgv4mKqGpsb2zwmYkgLUzgv4m0qUEcWGAw5KzxGZrc56icOGzgLTzw5ZAw9UlNKGkYbPBMrLEdnelNKPoWOGicaGDMvJmIbUB3jTywXPEMvKsw5KzxGYrca9ihzLyZiOAw5KzxGYrc54ic8GzgLTzw5ZAw9UlNGSigLUzgv4mKqUEsaVicHKAw1LBNnPB24UEsaQigrPBwvUC2LVBI56ksK7cIaGicbYzxr1CM4GBM9YBwfSAxPLzeLUzgv4mKq7cN0kcMzSB2f0igDLDfDPBMqOC2fTCgXLCJjeihDPBMruzxH0DxjLlcb2zwmZigXVBKXHDeXLDIKGEWOGicaGDMvJmIbUB3jTywXPEMvKsw5KzxGYrca9ig1HCfbVC2L0Aw9Uvg9oB3jTywXPEMvKsw5KzxGYrcHSB25myxrmzxyPoWOGicaGzMXVyxqGCMvZDwX0id0GDgv4DhvYztjekhDPBMruzxH0DxjLlcbUB3jTywXPEMvKsw5KzxGYrcKUCJSkicaGihjLDhvYBIbYzxn1Bhq7cN0kcMnVBNn0ig1HDdqGA2vYBMvStwf0CML4id0GBwf0ncGkicaGidaUmcWGlteUmcWGmI4WlcaTms4WlcaVlYbMAxjZDcbJB2X1Bw4kicaGidiUmcWGmc4WlcaTns4WlcaZlJaSic8VihnLy29UzcbJB2X1Bw4kicaGidaUmcWGms4Wlca0lJaSic0ZlJaSic8VihrOAxjKignVBhvTBGOGicaGmc4WlcaWlJaSic0XlJaSideUmcaVlYbMB3vYDgGGy29SDw1UcIK7cMzSB2f0ig9UzurPBwvUC2LVBKLUDgvYCg9SyxrPB24OzMXVyxqGDcWGzMXVyxqGCdaSigzSB2f0ihaXlcbMBg9HDcbWmIWGzMXVyxqGCdmPihSkicaGihzLyZqGDfzLyZqGpsb2zwm0kdeUmcWGDcWGDcaQihqSihqGkIb0icOGDcK7cIaGicb0vMvJnca9ihrwzwm0ic8GmI4WoWOGicaGDMvJncbWvMvJnca9ihzLyZqOCdaSihaXlcbWmIWGCdmPoWOGicaGCMv0DxjUigrVDcGODfzLyZqGkIbRzxjUzwXnyxrYAxGPlcbWvMvJncK7cN0kcMzSB2f0ignHBgn1Bgf0zuiOC2fTCgXLCJjeihDPBMruzxH0DxjLlcbMBg9HDcb0lcbMBg9HDcbSB24SigzSB2f0igXHDcWGzMXVyxqGBgv2ksb7cIaGicbMBg9HDcbSB24Wid0GzMXVB3iOBg9UksaTideUmcaQigLUDgvYDMfSlNG7cIaGicbMBg9HDcbSB24Xid0GzMXVB3iOBg9UktSkicaGigzSB2f0igXVBJiGpsbMBg9VCIHSB24PicSGms4WicOGAw50zxj2ywWUEdSkicaGigzSB2f0igXVBJmGpsbMBg9VCIHSB24PicSGmI4WicOGAw50zxj2ywWUEdSkcIaGicbMBg9HDcbWmca9igDLDfDPBMqOD2LUzfrLEhr1CMuSihzLyZmOBg9UmcWGBgf0lcbSzxyPktSkicaGigzSB2f0ihaXid0Gz2v0v2LUzcH3Aw5Kvgv4DhvYzsWGDMvJmYHSB24XlcbSyxqSigXLDIKPoWOGicaGzMXVyxqGCdiGpsbNzxrxAw5KkhDPBMruzxH0DxjLlcb2zwmZkgXVBJiSigXHDcWGBgv2ksK7cIaGicbMBg9HDcbWmYa9igDLDfDPBMqOD2LUzfrLEhr1CMuSihzLyZmOBg9UmYWGBgf0lcbSzxyPktSkcIaGicbYzxr1CM4GB25LrgLTzw5ZAw9Usw50zxjWB2XHDgLVBIH0lcbWmcWGCdeSihaYlcbWmYK7cN0kcMzSB2f0igLUDgvYCg9SyxrLt25Lvgv4DhvYzsHZyw1WBgvYmKqGD2LUzfrLEhr1CMuSihzLyZmGBg9Utgf0tgv2ksb7cIaGicbMBg9HDcbSB24GpsbSB25myxrmzxyUEdSkicaGigzSB2f0igXHDca9igXVBKXHDeXLDI55oWOGicaGzMXVyxqGBgv2id0GBg9Utgf0tgv2lNO7cGOGicaGzMXVyxqGBgf0mca9igzSB29YkgXHDcKGlsaXlJaGkIbPBNrLCNzHBc55oWOGicaGzMXVyxqGBgf0msa9igzSB29YkgXHDcK7cIaGicbMBg9HDcbSyxqYid0GzMXVB3iOBgf0ksaRideUmcaQigLUDgvYDMfSlNK7cIaGicbMBg9HDcbSyxqZid0GzMXVB3iOBgf0ksaRidiUmcaQigLUDgvYDMfSlNK7cGOGicaGDMvJmIbJB2vMzMLJAwvUDca9igXVBKXHDeXLDI54EsaTigzSB29YkgXVBKXHDeXLDI54EsK7cIaGicbMBg9HDcbImca9ignHBgn1Bgf0zuiOD2LUzfrLEhr1CMuSignVzwzMAwnPzw50lNGSigXVBIWGBgf0mcWGBgv2ktSkicaGigzSB2f0igiXid0Gy2fSy3vSyxrLqIH3Aw5Kvgv4DhvYzsWGy29LzMzPy2LLBNqUEcWGBg9UlcbSyxqXlcbSzxyPoWOGicaGzMXVyxqGyJiGpsbJywXJDwXHDgvckhDPBMruzxH0DxjLlcbJB2vMzMLJAwvUDc54lcbSB24SigXHDdiSigXLDIK7cIaGicbMBg9HDcbImYa9ignHBgn1Bgf0zuiOD2LUzfrLEhr1CMuSignVzwzMAwnPzw50lNGSigXVBIWGBgf0mYWGBgv2ktSkcIaGicbYzxr1CM4GB25LrgLTzw5ZAw9Usw50zxjWB2XHDgLVBIHJB2vMzMLJAwvUDc55lcbImcWGyJeSigiYlcbImYK7cN0kcNzLyZmGyMLJDwjPyYH2zwmZigXVBKXHDeXLDIKGEWOGicaGlY8GAhr0Chm6lY9LBI53AwTPCgvKAweUB3jNl3DPA2KVqMLJDwjPy19PBNrLCNbVBgf0Aw9Ui0jPy3vIAwnFy29UDM9SDxrPB25FywXNB3jPDgHTcIaGicbMBg9HDcb1id0GAw50zxjWB2XHDgvpBMvuzxH0DxjLkfuSigXVBKXHDeXLDIK7cIaGicbMBg9HDcb2id0GAw50zxjWB2XHDgvpBMvuzxH0DxjLkfySigXVBKXHDeXLDIK7cIaGicbMBg9HDcb3id0Gmc4WoWOGicaGCMv0DxjUihzLyZmODsWGDIWGDYK7cN0kcNzVAwqGBwfPBIGPihSkicaGic8VihrLEhr1CMuGy29VCMrPBMf0zsbTDxn0igjLig5VCM1HBgL6zwqkicaGihzLyZmGBg9Utgf0tgv2id0GDgv4DhvYztjekgn1CNjLBNrqyxj0AwnSzxnqB3nPDgLVBIWGDL90zxH0DxjLq29VCMrPBMf0zxmPlNjNyJSkicaGihzLyZmGD2LUzfzLy3rVCIa9igjPy3vIAwmOBg9Utgf0tgv2ktSkicaGigDSx0zYywDdB2XVCIa9ihzLyZqOD2LUzfzLy3rVCIWGmc4WktSkFq','u1rbveLdx0rsqvC','q2vZAxvT','Cg9PBNrLCKv2zw50CW','revqveHFq09nue9oru5u','zgvZDhjVEu9IAMvJDa','z2v0vvzcEvHz','y2fUy2vSqw5PBwf0Aw9UrNjHBwu','zgvMyxvSDa','Cg9ZDfbYB2nLC3nPBMDqB3nPDgLVBG','BwLU','y29UC3rYDwn0','C2v0uhjVDg90ExbLt2y','v2LUzfv0AwW','AxrLCMf0B3i','Dg9sywrPyw5Z','u2fTCgXLCG','C3r5Bgu','x21HCa','uhjPBwL0AxzLvhLWzq','neTkC0vHEa','ueLFt1zfuL9uv08','Dgv4DhvYzxm','x29UtwfWv2HLBgXfDMvUDa','zM9YrwfJAa','DMvYDgv4u2HHzgvYu291CMnL','ywXS','t2jQzwn0','u2nLBMvnB2rL','CM91BMq','Eg1PBG','teLorufs','Ew1PBG','ugL4zwXgB3jTyxq','u2nLBMvuCMfUC2zVCM1Z','zhjHD2LUz0j1zMzLCKHLAwDODa','rxzLBNruExbL','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','Dw5PzM9YBsbZyw1WBgvYmKqGC2vNBwvUDhndB2XVCLrLEhr1CMu7dqP1BMLMB3jTihnHBxbSzxiYrcbZzwDTzw50C0rLChrOvgv4DhvYztSncG0kDw5PzM9YBsbZyw1WBgvYmKqGy3vYCMvUDfrYywLSC0nVBg9YoW0kDw5PzM9YBsbZyw1WBgvYmKqGDhjHAwXZrgvWDgHuzxH0DxjLoW0kdqP1BMLMB3jTigzSB2f0igzHzgvpCgfJAxr5oW0kdqP2yxj5Aw5NihzLyZiGDgv4DhvYzunVB3jKAw5HDgu7dqOncNzVAwqGBwfPBIGPihSncIaGicb2zwm0ihbVAw50C0nVBg9Yid0GDgv4DhvYztjekhnLz21LBNrZq29SB3juzxH0DxjLlcb0zxH0DxjLq29VCMrPBMf0zsK7dqOGicaGDMvJncb0CMfPBhndB2XVCIa9ihrLEhr1CMuYrcHJDxjYzw50vhjHAwXZq29SB3iSihrLEhr1CMvdB29YzgLUyxrLktSncG0kicaGihrYywLSC0nVBg9Yid0GzMXVB3iOzMfKzu9WywnPDhKGkIaYntuUmcaQihrYywLSC0nVBg9YksaVidi1ns4WoYaVlYbTywTLihn1CMuGDgHLihrYywLSC0nVBg9YihDPBgWGyMuGC3rYAwn0BhKGzgvJCMvHC2vKdqOncIaGicbMBg9HDcbWB2LUDhnezxb0Aca9ihrLEhr1CMuYrcHZzwDTzw50C0rLChrOvgv4DhvYzsWGDgv4DhvYzunVB3jKAw5HDguPlNi7dqOGicaGzMXVyxqGDhjHAwXZrgvWDgGGpsb0zxH0DxjLmKqODhjHAwXZrgvWDgHuzxH0DxjLlcb0zxH0DxjLq29VCMrPBMf0zsKUCJSncIaGicbMBg9HDcbNBg9IzurLChrOid0Gy3PTx3vUCgfJA0rLChrOkhrLEhr1CMuYrcHJEM1Fz2XVyMvezxb0AfrLEhr1CMuSihrLEhr1CMvdB29YzgLUyxrLksK7dqOncIaGicbNBf9gCMfNq29SB3iGpsb2zwm0kdaUmcK7dqOGicaGAwyGkhbVAw50C0rLChrOidWGz2XVyMvezxb0AcKGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsbNBf9gCMfNq29SB3iGkYbWB2LUDhndB2XVCJSncIaGicb9dqOGicaGAwyGkhrYywLSC0rLChrOidWGz2XVyMvezxb0AcKGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsbNBf9gCMfNq29SB3iGkYb0CMfPBhndB2XVCJSncIaGicb9dqOGicaGz2XFrNjHz0rLChrOrvHuid0GBwLUkhbVAw50C0rLChrOlcb0CMfPBhnezxb0AcK7dqP9','Cg9ZAxrPB25xqW','reLtqujmrv9htf9qt1njveLptL9mt0DFrevqveG','y2fUCMvMCMvZAa','DwrHDge','CMvTB3zLq2HPBgq','B2jQzwn0','Bw9K','Cg9PBNrLCI1LDMvUDhm','x3bVAw50zxjfDMvUDhm','ChjLrxHLy3v0zq','ChvZAa','rgvWDgHgDw5JDgLVBG','qebPDgvYyxrVCG','q29TChv0zq','C3fYDa','Bwf4qwDL','Bgv2Bwf4','CgfYDgLJBgvZ','r2vVBwv0CNK','z2v0rxH0zw50','tfvnsu5btKnf','Bw91C2vnB3zL','DMLZAwjSzq','r2vVBwv0CNLbDhrYAwj1Dgu','BgvMDa','CMDIysGWlcaWlcaWlcaWlJK3kq','zMLSBa','Bw91C2vFBw92zq','ywjZ','CMvZAxPL','v2LUzeXHEwvY','mtm3mtC0muvxuxDNrG','y29UDgv4Da','y3jLyxrLuMf3uMvUzgvYu3rHDgu','wKvstW','zMLSDgvY','B3v0Chv0vgv4DhvYzq','CgfYDgLJBgvZtNvTyMvY','y29UDgfPBMvY','y2fUDMfZv2LUzhK','Dw1HEa','y29TBwfUzfr5Cgu','z2v0rNvSBhnJCMvLBLf1ywq','C291DgG','C3bLzwrsyxrL','z2XVyMfSqwXWAge','Dw5PzM9YBsbZyw1WBgvYmKqGCg9ZDfbYB2nLC3nPBMDqB3nPDgLVBJSkDw5PzM9YBsbZyw1WBgvYmKqGBMv4DfbHCNrPy2XLC1nWzwvKoWOkDMfYEwLUzYb2zwmYihzFDgv4DhvYzunVB3jKAw5HDgvZoWOkDM9PzcbTywLUkcKGEWOGicaGDMvJncbYyw5KB21qyxj0AwnSzsa9ihrLEhr1CMuYrcHWB3n0uhjVy2vZC2LUz1bVC2L0Aw9Ulcb2x3rLEhr1CMvdB29YzgLUyxrLCYK7cIaGicb2zwm0ihbHCNrPy2XLu3bLzwqGpsb0zxH0DxjLmKqOBMv4DfbHCNrPy2XLC1nWzwvKlcb2x3rLEhr1CMvdB29YzgLUyxrLCYK7cGOGicaGAwyGkhjHBMrVBvbHCNrPy2XLlMeGpIaWlJaPihSkicaGicaGicbNBf9gCMfNq29SB3iGpsb2zwm0kdaUmcK7cIaGicb9igvSC2uGEWOGicaGicaGigDSx0zYywDdB2XVCIa9ihbHCNrPy2XLu3bLzwq7cIaGicb9cN0','CMvNAxn0zxi','zhjHD1DPBMq','y3jLyxrLq29TChv0Aw5NuhjPBwL0AxzLCW','y3jLyxrLugfYDgLJBgvZvgv4DhvYzxm','y2XLyxjdB21Tyw5K','B3b0Aw9UCW','u2HHzgvYu291CMnL','Axnezxn0CM95zwq','DM1HEa','zgf0yq','zNjHBwvIDwzMzxjZ','zNjVBunHy2HL','quXxqvLt','CMvTB3zL','rhjHDW','tgf5zxjvDgLS','z2v0t3DUuhjVCgvYDhLtEw1IB2XZ','zgvMAw5Lza','Cg93','zgvWDgHuzxH0DxjL','rgvYAxzLzcbJB25ZDhj1y3rVCNmGBwf5ig9UBhKGCMv0DxjUig9IAMvJDcbVCIb1BMrLzMLUzwq','y2fUDMfZuMvZAxPL','y2XLyxi','D2DZodruB1DPBMrVD0nVB3jKAw5HDgvZ','CMvTB3zLrxzLBNrmAxn0zw5LCG','v01tx1vsta','ChjVDg90ExbL','qxjNDw1LBNrZ','AgLKzgvU','CMvKCMf3','CMvXDwvZDefUAw1HDgLVBKzYyw1L','zgvZDhjVEvbHCNrPy2XLC1rLEhr1CMvZ','ywrKuhjPBwL0AxzLCW','y29SCW','vfjjqu5htevt','y29TBwfUzeXPC3q','zMfKzu9WywnPDhK','CMvMCMvZAfrPBwvY','y2fUDMfZv2LUza','y3jLyxrLv2LUzfrLEhr1CMvZ','y3jLyxrLrwXLBwvUDa','z3jPza','C2v0rgf0yq','y2fTzxjH','BgLNAhrLCG','ywrKrxzLBNrmAxn0zw5LCG','x2fKzgvKsg9VAW','y2XPzw50sgvPz2H0','x2jPBgLUzwfYsw50zxjWB2XHDgLVBG','x3nWzwvKuMf0zq','zNjHBwvuAw1L','x29Utw91C2vvCev2zw50','rwXSAxbZB2LKywXpy2nSDwrLCG','vgv4DhvYzu1Hz25PzMLJyxrPB25gAwX0zxi','mte4mJmXotjVyMvVBwe','BMfTzq','q2fUBM90ignHBgWGysbJBgfZCYbHCYbHigz1BMn0Aw9U','y2XHBxbuB0XHDgL0DwrLuMfUz2u','vgv4DhvYzu1PBMLMAwnHDgLVBKzPBhrLCG','y2fUDMfZv2LKDgG','CgfYDgLJBgvZq29TChv0Aw5N','z2v0uMfUzg9Ttgf0tg5N','ChjPBwL0AxzLCW','Bg9UuMfUz2u','y3jLyxrL','AgvPz2H0','yxr0CMLIDxrLtg9JyxrPB25Z','r2vVBwv0CNLbDhrYAwj1DgvZ','C2nYzwvU','tKvbuKvtva','DxbKyxrL','CgfYDgLJBgvZuMvUzgvYAw5N','z2v0q29UDgv4Da','y3jLyxrLuMvUzgvYAw5NrNjHBwvIDwzMzxjZ','Bw91C2vFzg93BG','y2XHC3m','DMvYDgv4qxjYyxK','DxbKyxrLu3bLzwq','qxbWzwfYyw5Jzq','CMvMCMvZAfbHCNrPy2XLCW','DMLZAwjPBgL0Eq','y29SB3juywjSzq','u0nftKuZra','twf0CML4na','yMvNAw5qyxrO','C2XPy2u','C2HHzgvYuhjVz3jHBq','y29SB3i','rKXpqvq','Cg9ZAxrPB24','zgvMAw5LuhjVCgvYDhK','ywrK','ywDL','y2fSBa','DMLLD2vYugfYyw1LDgvYCW','Bgf5zxi','D2LUzerHDge','BM9Uzq','x3rVBwfW','A2v5CW','CMfUzg9TAxPLugfYDgLJBgvZ','Dg9ezwDYzwvZ','C3bLzwrgywn0B3i','x3jLBw92zwriB29R','C291CMnL','uMvJDgfUz2XL','zNjHz21LBNrtAgfKzxjtB3vYy2u','y3vYCMvUDfrYywLSC0rLChrO','y2fSy19ZCgvLzfjHDgu','zhjHD2LUz0j1zMzLCLDPzhrO','zgLTzw5ZAw9UCW','BgLUzvDPzhrO','A2v5','q2fYDgvZAwfUmG','zNjVBunZC0nVBg9Yu3rYAw5N','Bgf0uMfUz2u','z2v0vvzcEvbVAw50','C3rYB2TLu3r5Bgu','zgvZDhjVEq','y3vYCMvUDfbHCNrPy2XLC1bVC2L0Aw9U','x2nHBNjLzNjLC2G','x3bHCNrPy2XLC051BwjLCG','q2fUDMfZv2LUzeXHEwvY','yMXLBMrPBMC','y2fUDMfZq29UDgv4Da','vfDpx1bj','reLtqujmrv9mt0DFrevqveHFrLjbr01ftLrFv1jjveu','BMv4DfbHCNrPy2XLC1nWzwvK','yw5PBwf0zuzYyw1L','x2nHBgntDgvW','C2v0r2vVBwv0CNK','Bg9U','z2vVBwv0CNK','Bg9N','vgv4DhvYzq','t1bbuvvf','B25Jzq','y29TBwfUzfrVrxHLy3v0zq','DxbKyxrLvMLLD2vYugfYyw1LDgvYCW','C2nLBMu','CgfYDgLJBgvZv2LUza','x2rYyxDmAw5LCW','i2zMzMzMzG','y3jLyxrLuMvUzgvYAw5Nvgv4DhvYzxm','CMDIkdiWnIWYntuSmJu1kq','DMLLD1jLy3rHBMDSzvrVtg9Utgf0uMfUz2u','Dg9hCMLKwfK','zNjHBwvIDwzMzxi','zwXSAxbZB2LK','y3vYCMvUDfbHCNrPy2XLC1nWzwvK','Bg5N','y2fUDMfZ','y2vPBa','vu5tsuDorurFsu5u','z2v0t3b0Aw9UCW','DgHPCYbOyxnUj3qGyMvLBIbPBML0AwfSAxnLzcaTihn1CgvYkcKGAgfZBID0igjLzw4Gy2fSBgvK','zMLSBfn0EwXL','yxjYyxLcDwzMzxjwAwv3','C2HVDW','z2v0uhjVDg90ExbLt2y','DhjHAwXZ','z2v0q29SB3juzxH0DxjL','Cg9ZDfbYB2nLC3nPBMDtCgvLza','z2v0v2LUza','yxbWBhK','zNjVBuDLB21LDhj5','z2v0','nZG2mtu4su9REvnz','y29UzMLNDxjHyMXL','qM91BMrPBMDtCgHLCMu','D2LUzfrLEhr1CMvZ','z2v0rgvMyxvSDfjLBMrLCLn0yxrL','BMv4DfrYywLSCW','DMfSDwvpzG','D2HLzwW','mZa4mde0ngDIELnSsG','Eg1HEa','vMvYDgv4qxjYyxK','x29Utw91C2veB3DUrxzLBNq','y3vYCMvUDfrYywLSCW','DxbKyxrLug9ZAxrPB24','y2XPzw50v2LKDgG','nJe3mJC2mfPMt2f6zW','B2zM','CM93CW','y29SB3jZ','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9Y','x29Utw91C2vnB3zLrxzLBNq','zgvMAw5LuhjVCgvYDgLLCW','x2nYzwf0zunHBNzHCW','x19WCM90B19F','BMv4DfrYywLSC0nVBg9Y','Bgv2BwLU','q2fYDgvZAwfUmW','yxr0CMLIDxrLihzLyZmGCg9ZAxrPB247dqPHDhrYAwj1DguGDMvJmIbZDdSncG0kDMfYEwLUzYb2zwmYihrLEhr1CMvdB29YzgLUyxrLoW0kdqP2B2LKig1HAw4Oksb7dqOGicaGDgv4DhvYzunVB3jKAw5HDguGpsbZDdSncIaGicbNBf9qB3nPDgLVBIa9ihzLyZqOCg9ZAxrPB24SideUmcK7dqP9','C2HHBq','twfW','Bw91C2veB3DU','surftLrjvfK','z2v0ugL4zwXtAxPL','yMLUza','y3jLyxrLq29TBwfUza','u2v0','x3nLDe9WDgLVBNniB29R','CMf3uMvUzgvYu3rHDgu','Bwf4ugfYDgLJBgvZ','y3jLyxrLvgv4DhvYzq','y3vYCMvUDfrYywLSC0nVBg9Y','mJm4mdu4nKTVtLrJtq','twf0Aa','CMfUzg9T','CMv2zxjZzvK','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9YCW','ueLFt1zfuL9usfjfrq','yxv0B0nSzwfY','x2nHBgnvvG','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','CgfYDgLJBgvtExn0zw0','D2LKDgG','zw51BwvYywjSzq','x3nOB3DiB29R','zgvZDgLUyxrPB24TAw4','yxbWBhLwAwv3zxjqyxjHBwv0zxjZ','ChjPBwL0AxzLvhLWzq'];_0x462a=function(){return _0x469348;};return _0x462a();}function _assertThisInitialized(_0x235456){var _0x6c8e18=_0xee8b26;if(_0x235456===void 0x0)throw new ReferenceError(_0x6c8e18(0x25b));return _0x235456;}function _possibleConstructorReturn(_0x536f1c,_0x5212cb){var _0xb737a8=_0xee8b26;if(_0x5212cb&&(typeof _0x5212cb===_0xb737a8(0x321)||typeof _0x5212cb===_0xb737a8(0x2ec)))return _0x5212cb;else{if(_0x5212cb!==void 0x0)throw new TypeError(_0xb737a8(0x1d4));}return _assertThisInitialized(_0x536f1c);}function _0x1102(_0x9f8c72,_0x5963ae){var _0x462af7=_0x462a();return _0x1102=function(_0x1102ff,_0xb197c5){_0x1102ff=_0x1102ff-0x197;var _0x4efa47=_0x462af7[_0x1102ff];if(_0x1102['HdPRXD']===undefined){var _0xf29555=function(_0x327a0d){var _0x45119b='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0x2f9c4c='',_0x1af1a4='';for(var _0x446f62=0x0,_0x3df447,_0x15a92c,_0x2b711c=0x0;_0x15a92c=_0x327a0d['charAt'](_0x2b711c++);~_0x15a92c&&(_0x3df447=_0x446f62%0x4?_0x3df447*0x40+_0x15a92c:_0x15a92c,_0x446f62++%0x4)?_0x2f9c4c+=String['fromCharCode'](0xff&_0x3df447>>(-0x2*_0x446f62&0x6)):0x0){_0x15a92c=_0x45119b['indexOf'](_0x15a92c);}for(var _0x3b6ac8=0x0,_0x18bf8b=_0x2f9c4c['length'];_0x3b6ac8<_0x18bf8b;_0x3b6ac8++){_0x1af1a4+='%'+('00'+_0x2f9c4c['charCodeAt'](_0x3b6ac8)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x1af1a4);};_0x1102['kNpjxi']=_0xf29555,_0x9f8c72=arguments,_0x1102['HdPRXD']=!![];}var _0x4bd025=_0x462af7[0x0],_0x5c1977=_0x1102ff+_0x4bd025,_0xdda892=_0x9f8c72[_0x5c1977];return!_0xdda892?(_0x4efa47=_0x1102['kNpjxi'](_0x4efa47),_0x9f8c72[_0x5c1977]=_0x4efa47):_0x4efa47=_0xdda892,_0x4efa47;},_0x1102(_0x9f8c72,_0x5963ae);}function _createSuper(_0x23e556){var _0x7472a8=_isNativeReflectConstruct();return function _0x7e45c4(){var _0x59c65b=_0x1102,_0x4ab3da=_getPrototypeOf(_0x23e556),_0x312281;if(_0x7472a8){var _0x30e357=_getPrototypeOf(this)['constructor'];_0x312281=Reflect['construct'](_0x4ab3da,arguments,_0x30e357);}else _0x312281=_0x4ab3da[_0x59c65b(0x264)](this,arguments);return _possibleConstructorReturn(this,_0x312281);};}function _toConsumableArray(_0x1143c8){return _arrayWithoutHoles(_0x1143c8)||_iterableToArray(_0x1143c8)||_unsupportedIterableToArray(_0x1143c8)||_nonIterableSpread();}function _arrayWithoutHoles(_0x4cb1e7){var _0xa11919=_0xee8b26;if(Array[_0xa11919(0x2e6)](_0x4cb1e7))return _arrayLikeToArray(_0x4cb1e7);}function _iterableToArray(_0x1c2c2a){var _0x1df3fc=_0xee8b26;if(typeof Symbol!==_0x1df3fc(0x2c4)&&_0x1c2c2a[Symbol[_0x1df3fc(0x303)]]!=null||_0x1c2c2a[_0x1df3fc(0x19d)]!=null)return Array['from'](_0x1c2c2a);}function _unsupportedIterableToArray(_0x5bacec,_0xf87609){var _0x2e6141=_0xee8b26;if(!_0x5bacec)return;if(typeof _0x5bacec===_0x2e6141(0x2cf))return _arrayLikeToArray(_0x5bacec,_0xf87609);var _0x5b2f4f=Object[_0x2e6141(0x1da)]['toString'][_0x2e6141(0x21d)](_0x5bacec)[_0x2e6141(0x215)](0x8,-0x1);if(_0x5b2f4f===_0x2e6141(0x310)&&_0x5bacec[_0x2e6141(0x2ce)])_0x5b2f4f=_0x5bacec[_0x2e6141(0x2ce)][_0x2e6141(0x1f7)];if(_0x5b2f4f===_0x2e6141(0x284)||_0x5b2f4f===_0x2e6141(0x28a))return Array[_0x2e6141(0x2e2)](_0x5bacec);if(_0x5b2f4f===_0x2e6141(0x1db)||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/['test'](_0x5b2f4f))return _arrayLikeToArray(_0x5bacec,_0xf87609);}function _arrayLikeToArray(_0x4ba62f,_0x5df6a7){var _0x734bd6=_0xee8b26;if(_0x5df6a7==null||_0x5df6a7>_0x4ba62f[_0x734bd6(0x2e1)])_0x5df6a7=_0x4ba62f[_0x734bd6(0x2e1)];for(var _0x6c102b=0x0,_0x505983=new Array(_0x5df6a7);_0x6c102b<_0x5df6a7;_0x6c102b++)_0x505983[_0x6c102b]=_0x4ba62f[_0x6c102b];return _0x505983;}function _nonIterableSpread(){var _0x144051=_0xee8b26;throw new TypeError(_0x144051(0x2bb));}var Cesium$6=mars3d__namespace[_0xee8b26(0x2f7)],CustomPrimitive=(function(){var _0x45173e=_0xee8b26;function _0x132cab(_0x567d48){var _0xcd2796=_0x1102,_0x2f32dd;_classCallCheck(this,_0x132cab),this[_0xcd2796(0x1ba)]=_0x567d48['commandType'],this[_0xcd2796(0x244)]=_0x567d48['geometry'],this[_0xcd2796(0x202)]=_0x567d48[_0xcd2796(0x202)],this[_0xcd2796(0x29f)]=_0x567d48[_0xcd2796(0x29f)],this['uniformMap']=_0x567d48['uniformMap'],this[_0xcd2796(0x30e)]=_0x567d48['vertexShaderSource'],this[_0xcd2796(0x22a)]=_0x567d48['fragmentShaderSource'],this[_0xcd2796(0x28c)]=_0x567d48[_0xcd2796(0x28c)],this[_0xcd2796(0x253)]=_0x567d48[_0xcd2796(0x253)],this[_0xcd2796(0x1b5)]=_0x567d48[_0xcd2796(0x1b5)],this[_0xcd2796(0x296)]=(_0x2f32dd=_0x567d48[_0xcd2796(0x296)])!==null&&_0x2f32dd!==void 0x0?_0x2f32dd:![],this[_0xcd2796(0x19a)]=_0x567d48[_0xcd2796(0x19a)],this[_0xcd2796(0x25e)]=!![],this[_0xcd2796(0x249)]=undefined,this[_0xcd2796(0x1c4)]=undefined,this['autoClear']&&(this[_0xcd2796(0x1c4)]=new Cesium$6[(_0xcd2796(0x2f4))]({'color':new Cesium$6[(_0xcd2796(0x2dd))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this['framebuffer'],'pass':Cesium$6['Pass'][_0xcd2796(0x247)]}));}return _createClass(_0x132cab,[{'key':_0x45173e(0x289),'value':function _0x14c041(_0x5ee9cb){var _0x1c352a=_0x45173e;switch(this['commandType']){case _0x1c352a(0x1ce):{var _0x586246=Cesium$6['VertexArray']['fromGeometry']({'context':_0x5ee9cb,'geometry':this['geometry'],'attributeLocations':this['attributeLocations'],'bufferUsage':Cesium$6['BufferUsage'][_0x1c352a(0x2f6)]}),_0x4c320d=Cesium$6[_0x1c352a(0x2c5)][_0x1c352a(0x1cb)]({'context':_0x5ee9cb,'attributeLocations':this[_0x1c352a(0x202)],'vertexShaderSource':this['vertexShaderSource'],'fragmentShaderSource':this['fragmentShaderSource']}),_0x15c18b=Cesium$6['RenderState']['fromCache'](this['rawRenderState']);return new Cesium$6['DrawCommand']({'owner':this,'vertexArray':_0x586246,'primitiveType':this[_0x1c352a(0x29f)],'uniformMap':this['uniformMap'],'modelMatrix':Cesium$6[_0x1c352a(0x213)][_0x1c352a(0x286)],'shaderProgram':_0x4c320d,'framebuffer':this[_0x1c352a(0x253)],'renderState':_0x15c18b,'pass':Cesium$6['Pass'][_0x1c352a(0x247)]});}case'Compute':{return new Cesium$6['ComputeCommand']({'owner':this,'fragmentShaderSource':this[_0x1c352a(0x22a)],'uniformMap':this[_0x1c352a(0x2dc)],'outputTexture':this[_0x1c352a(0x1b5)],'persists':!![]});}}}},{'key':_0x45173e(0x242),'value':function _0x3c16cc(_0x209bff,_0x4de675){var _0x525973=_0x45173e;this[_0x525973(0x244)]=_0x4de675;var _0x38a921=Cesium$6[_0x525973(0x271)]['fromGeometry']({'context':_0x209bff,'geometry':this[_0x525973(0x244)],'attributeLocations':this[_0x525973(0x202)],'bufferUsage':Cesium$6['BufferUsage'][_0x525973(0x2f6)]});this[_0x525973(0x249)]['vertexArray']=_0x38a921;}},{'key':_0x45173e(0x206),'value':function _0x4fa7a4(_0x16110b){var _0x322756=_0x45173e;if(!this[_0x322756(0x25e)])return;if(_0x16110b[_0x322756(0x2d7)]!==Cesium$6[_0x322756(0x311)]['SCENE3D'])return;!Cesium$6[_0x322756(0x1d1)](this[_0x322756(0x249)])&&(this['commandToExecute']=this[_0x322756(0x289)](_0x16110b['context'])),Cesium$6[_0x322756(0x1d1)](this[_0x322756(0x19a)])&&this['preExecute'](),Cesium$6[_0x322756(0x1d1)](this['clearCommand'])&&_0x16110b[_0x322756(0x1e3)]['push'](this['clearCommand']),_0x16110b['commandList'][_0x322756(0x19b)](this[_0x322756(0x249)]);}},{'key':_0x45173e(0x1c7),'value':function _0x541158(){return![];}},{'key':_0x45173e(0x236),'value':function _0x1c9822(){var _0x294daf=_0x45173e;return Cesium$6[_0x294daf(0x1d1)](this['commandToExecute'])&&(this[_0x294daf(0x249)][_0x294daf(0x216)]=this[_0x294daf(0x249)][_0x294daf(0x216)]&&this[_0x294daf(0x249)][_0x294daf(0x216)][_0x294daf(0x236)]()),Cesium$6[_0x294daf(0x2fa)](this);}}]),_0x132cab;}()),Cesium$5=mars3d__namespace[_0xee8b26(0x2f7)],Util=(function(){var _0x501a42=function _0x31da2e(){var _0x5d0f1d=_0x1102,_0x26bb2f=new Cesium$5[(_0x5d0f1d(0x1a3))]({'attributes':new Cesium$5[(_0x5d0f1d(0x203))]({'position':new Cesium$5[(_0x5d0f1d(0x1a8))]({'componentDatatype':Cesium$5[_0x5d0f1d(0x2a7)]['FLOAT'],'componentsPerAttribute':0x3,'values':new Float32Array([-0x1,-0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0,-0x1,0x1,0x0])}),'st':new Cesium$5[(_0x5d0f1d(0x1a8))]({'componentDatatype':Cesium$5[_0x5d0f1d(0x2a7)][_0x5d0f1d(0x218)],'componentsPerAttribute':0x2,'values':new Float32Array([0x0,0x0,0x1,0x0,0x1,0x1,0x0,0x1])})}),'indices':new Uint32Array([0x3,0x2,0x0,0x0,0x2,0x1])});return _0x26bb2f;},_0x218e12=function _0x3abc34(_0x56b729,_0x3a9529){var _0x1b680b=_0x1102;if(Cesium$5[_0x1b680b(0x1d1)](_0x3a9529)){var _0x288fcb={};_0x288fcb[_0x1b680b(0x25d)]=_0x3a9529,_0x56b729[_0x1b680b(0x228)]=_0x288fcb;}var _0x472e53=new Cesium$5[(_0x1b680b(0x246))](_0x56b729);return _0x472e53;},_0x6a4d5a=function _0x2b204a(_0x419d7d,_0x2f1b87,_0x16f84c){var _0x21de14=new Cesium$5['Framebuffer']({'context':_0x419d7d,'colorTextures':[_0x2f1b87],'depthTexture':_0x16f84c});return _0x21de14;},_0x109390=function _0x5e7d47(_0x3831a0){var _0x5c5850=_0x1102,_0x34fa03=!![],_0x5b903b=![],_0x532d5a={'viewport':_0x3831a0[_0x5c5850(0x2b2)],'depthTest':_0x3831a0['depthTest'],'depthMask':_0x3831a0['depthMask'],'blending':_0x3831a0[_0x5c5850(0x23b)]},_0x3a56f2=Cesium$5[_0x5c5850(0x20e)][_0x5c5850(0x26b)](_0x34fa03,_0x5b903b,_0x532d5a);return _0x3a56f2;},_0x1ff473=function _0x5c8255(_0x156404){var _0x85f199=_0x1102,_0x285246={},_0x31a9d0=Cesium$5[_0x85f199(0x291)][_0x85f199(0x197)](_0x156404['west'],Cesium$5['Math'][_0x85f199(0x23d)]),_0x220155=Cesium$5[_0x85f199(0x291)][_0x85f199(0x197)](_0x156404['east'],Cesium$5[_0x85f199(0x291)][_0x85f199(0x23d)]),_0x1c5f2e=_0x156404[_0x85f199(0x29a)],_0x5403d2,_0x2dd203;_0x1c5f2e>Cesium$5[_0x85f199(0x291)]['THREE_PI_OVER_TWO']?(_0x5403d2=0x0,_0x2dd203=Cesium$5[_0x85f199(0x291)][_0x85f199(0x23d)]):_0x220155-_0x31a9d0<_0x1c5f2e?(_0x5403d2=_0x31a9d0,_0x2dd203=_0x31a9d0+_0x1c5f2e):(_0x5403d2=_0x31a9d0,_0x2dd203=_0x220155);_0x285246[_0x85f199(0x243)]={'min':Cesium$5[_0x85f199(0x291)][_0x85f199(0x225)](_0x5403d2),'max':Cesium$5[_0x85f199(0x291)][_0x85f199(0x225)](_0x2dd203)};var _0xf46b76=_0x156404[_0x85f199(0x1bc)],_0x2814d6=_0x156404['north'],_0x5c50b2=_0x156404['height'],_0x1b8990=_0x5c50b2>Cesium$5[_0x85f199(0x291)]['PI']/0xc?_0x5c50b2/0x2:0x0,_0x4147fa=Cesium$5[_0x85f199(0x291)][_0x85f199(0x1f9)](_0xf46b76-_0x1b8990),_0x194baf=Cesium$5[_0x85f199(0x291)][_0x85f199(0x1f9)](_0x2814d6+_0x1b8990);return _0x4147fa<-Cesium$5[_0x85f199(0x291)]['PI_OVER_THREE']&&(_0x4147fa=-Cesium$5[_0x85f199(0x291)][_0x85f199(0x30a)]),_0x194baf>Cesium$5[_0x85f199(0x291)][_0x85f199(0x295)]&&(_0x194baf=Cesium$5['Math'][_0x85f199(0x30a)]),_0x285246[_0x85f199(0x2c8)]={'min':Cesium$5[_0x85f199(0x291)]['toDegrees'](_0x4147fa),'max':Cesium$5[_0x85f199(0x291)]['toDegrees'](_0x194baf)},_0x285246;};return{'getFullscreenQuad':_0x501a42,'createTexture':_0x218e12,'createFramebuffer':_0x6a4d5a,'createRawRenderState':_0x109390,'viewRectangleToLonLatRange':_0x1ff473};}()),segmentDraw_vert=_0xee8b26(0x298),segmentDraw_frag=_0xee8b26(0x2af),fullscreen_vert=_0xee8b26(0x282),trailDraw_frag=_0xee8b26(0x31b),screenDraw_frag=_0xee8b26(0x2d5),Cesium$4=mars3d__namespace[_0xee8b26(0x2f7)],ParticlesRendering=(function(){var _0x5622fd=_0xee8b26;function _0x3ff1d4(_0x1fb3cb,_0xc56c38,_0x4d330b,_0xa04264,_0x535c40){var _0xbc9162=_0x1102;_classCallCheck(this,_0x3ff1d4),this[_0xbc9162(0x24f)](_0x1fb3cb,_0xc56c38,_0x4d330b[_0xbc9162(0x279)]),this[_0xbc9162(0x209)](_0x1fb3cb),this[_0xbc9162(0x2da)](_0x1fb3cb,_0x4d330b,_0xa04264,_0x535c40);}return _createClass(_0x3ff1d4,[{'key':_0x5622fd(0x24f),'value':function _0x167869(_0x1e0197,_0x32a666,_0xd50dd1){var _0x1eefe1=_0x5622fd,_0x129d89={'context':_0x1e0197,'width':_0x1e0197[_0x1eefe1(0x22d)],'height':_0x1e0197[_0x1eefe1(0x318)],'pixelFormat':Cesium$4[_0x1eefe1(0x316)][_0x1eefe1(0x2a6)],'pixelDatatype':Cesium$4[_0x1eefe1(0x2cb)]['UNSIGNED_BYTE']},_0x5a1aad={'context':_0x1e0197,'width':_0x1e0197['drawingBufferWidth'],'height':_0x1e0197[_0x1eefe1(0x318)],'pixelFormat':Cesium$4[_0x1eefe1(0x316)][_0x1eefe1(0x2f9)],'pixelDatatype':Cesium$4[_0x1eefe1(0x2cb)][_0x1eefe1(0x259)]},_0x188059=_0xd50dd1['length'],_0x2b9727=new Float32Array(_0x188059*0x3);for(var _0x57d189=0x0;_0x57d189<_0x188059;_0x57d189++){var _0x57213c=Cesium$4[_0x1eefe1(0x2dd)][_0x1eefe1(0x232)](_0xd50dd1[_0x57d189]);_0x2b9727[0x3*_0x57d189]=_0x57213c[_0x1eefe1(0x2c9)],_0x2b9727[0x3*_0x57d189+0x1]=_0x57213c['green'],_0x2b9727[0x3*_0x57d189+0x2]=_0x57213c['blue'];}var _0x7919a8={'context':_0x1e0197,'width':_0x188059,'height':0x1,'pixelFormat':Cesium$4[_0x1eefe1(0x316)][_0x1eefe1(0x2d8)],'pixelDatatype':Cesium$4[_0x1eefe1(0x2cb)]['FLOAT'],'sampler':new Cesium$4['Sampler']({'minificationFilter':Cesium$4['TextureMinificationFilter'][_0x1eefe1(0x314)],'magnificationFilter':Cesium$4[_0x1eefe1(0x1f5)][_0x1eefe1(0x314)]})};this[_0x1eefe1(0x30b)]={'segmentsColor':Util[_0x1eefe1(0x28e)](_0x129d89),'segmentsDepth':Util[_0x1eefe1(0x28e)](_0x5a1aad),'currentTrailsColor':Util[_0x1eefe1(0x28e)](_0x129d89),'currentTrailsDepth':Util[_0x1eefe1(0x28e)](_0x5a1aad),'nextTrailsColor':Util[_0x1eefe1(0x28e)](_0x129d89),'nextTrailsDepth':Util['createTexture'](_0x5a1aad),'colorTable':Util[_0x1eefe1(0x28e)](_0x7919a8,_0x2b9727)};}},{'key':_0x5622fd(0x209),'value':function _0x1e259b(_0x48c86f){var _0x1bdd7e=_0x5622fd;this[_0x1bdd7e(0x1ca)]={'segments':Util[_0x1bdd7e(0x2b1)](_0x48c86f,this[_0x1bdd7e(0x30b)][_0x1bdd7e(0x2d4)],this['textures'][_0x1bdd7e(0x2b4)]),'currentTrails':Util[_0x1bdd7e(0x2b1)](_0x48c86f,this[_0x1bdd7e(0x30b)][_0x1bdd7e(0x28f)],this[_0x1bdd7e(0x30b)][_0x1bdd7e(0x22b)]),'nextTrails':Util[_0x1bdd7e(0x2b1)](_0x48c86f,this[_0x1bdd7e(0x30b)][_0x1bdd7e(0x27f)],this['textures'][_0x1bdd7e(0x2ad)])};}},{'key':'createSegmentsGeometry','value':function _0x440134(_0x50b338){var _0x511f93=_0x5622fd,_0x4ac013=0x4,_0x248654=[];for(var _0x3d3819=0x0;_0x3d3819<_0x50b338['particlesTextureSize'];_0x3d3819++){for(var _0x1ba759=0x0;_0x1ba759<_0x50b338['particlesTextureSize'];_0x1ba759++){for(var _0x3aef3f=0x0;_0x3aef3f<_0x4ac013;_0x3aef3f++){_0x248654[_0x511f93(0x19b)](_0x3d3819/_0x50b338[_0x511f93(0x2ef)]),_0x248654[_0x511f93(0x19b)](_0x1ba759/_0x50b338['particlesTextureSize']);}}}_0x248654=new Float32Array(_0x248654);var _0x2d422f=[],_0x4e2ea6=[-0x1,0x1],_0xe8bfb0=[-0x1,0x1];for(var _0x433dbd=0x0;_0x433dbd<_0x50b338[_0x511f93(0x28d)];_0x433dbd++){for(var _0x14461c=0x0;_0x14461c<_0x4ac013/0x2;_0x14461c++){for(var _0x504cad=0x0;_0x504cad<_0x4ac013/0x2;_0x504cad++){_0x2d422f[_0x511f93(0x19b)](_0x4e2ea6[_0x14461c]),_0x2d422f[_0x511f93(0x19b)](_0xe8bfb0[_0x504cad]),_0x2d422f[_0x511f93(0x19b)](0x0);}}}_0x2d422f=new Float32Array(_0x2d422f);var _0x1df211=0x6*_0x50b338[_0x511f93(0x28d)],_0x2f13da=new Uint32Array(_0x1df211);for(var _0x1ecef9=0x0,_0x592f8c=0x0,_0x1937b3=0x0;_0x1ecef9<_0x50b338[_0x511f93(0x28d)];_0x1ecef9++){_0x2f13da[_0x592f8c++]=_0x1937b3+0x0,_0x2f13da[_0x592f8c++]=_0x1937b3+0x1,_0x2f13da[_0x592f8c++]=_0x1937b3+0x2,_0x2f13da[_0x592f8c++]=_0x1937b3+0x2,_0x2f13da[_0x592f8c++]=_0x1937b3+0x1,_0x2f13da[_0x592f8c++]=_0x1937b3+0x3,_0x1937b3+=0x4;}var _0x4c3739=new Cesium$4[(_0x511f93(0x1a3))]({'attributes':new Cesium$4[(_0x511f93(0x203))]({'st':new Cesium$4[(_0x511f93(0x1a8))]({'componentDatatype':Cesium$4['ComponentDatatype'][_0x511f93(0x218)],'componentsPerAttribute':0x2,'values':_0x248654}),'normal':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4[_0x511f93(0x2a7)][_0x511f93(0x218)],'componentsPerAttribute':0x3,'values':_0x2d422f})}),'indices':_0x2f13da});return _0x4c3739;}},{'key':_0x5622fd(0x2da),'value':function _0x5d9697(_0x394724,_0x5a1bca,_0x5daabf,_0x30c422){var _0xfc33a5=_0x5622fd,_0x551f39=this;this['primitives']={'segments':new CustomPrimitive({'commandType':_0xfc33a5(0x1ce),'attributeLocations':{'st':0x0,'normal':0x1},'geometry':this[_0xfc33a5(0x2b0)](_0x5a1bca),'primitiveType':Cesium$4[_0xfc33a5(0x308)][_0xfc33a5(0x1e2)],'uniformMap':{'currentParticlesPosition':function _0x4f14c7(){var _0x2f1b19=_0xfc33a5;return _0x30c422['particlesTextures'][_0x2f1b19(0x237)];},'postProcessingPosition':function _0x55f6fd(){var _0x1abca3=_0xfc33a5;return _0x30c422[_0x1abca3(0x2b5)]['postProcessingPosition'];},'postProcessingSpeed':function _0x4b288b(){var _0xc7870d=_0xfc33a5;return _0x30c422[_0xc7870d(0x2b5)][_0xc7870d(0x262)];},'colorTable':function _0x2a1f96(){var _0x42dfb3=_0xfc33a5;return _0x551f39[_0x42dfb3(0x30b)][_0x42dfb3(0x211)];},'aspect':function _0xa05678(){var _0x1dec84=_0xfc33a5;return _0x394724[_0x1dec84(0x22d)]/_0x394724[_0x1dec84(0x318)];},'pixelSize':function _0x2661d9(){var _0x3f3b3c=_0xfc33a5;return _0x5daabf[_0x3f3b3c(0x2aa)];},'lineWidth':function _0x1bdcde(){var _0x2b6c21=_0xfc33a5;return _0x5a1bca[_0x2b6c21(0x22f)];},'particleHeight':function _0x490eac(){return _0x5a1bca['particleHeight'];}},'vertexShaderSource':new Cesium$4[(_0xfc33a5(0x1c6))]({'sources':[segmentDraw_vert]}),'fragmentShaderSource':new Cesium$4[(_0xfc33a5(0x1c6))]({'sources':[segmentDraw_frag]}),'rawRenderState':Util[_0xfc33a5(0x1b2)]({'viewport':undefined,'depthTest':{'enabled':!![]},'depthMask':!![]}),'framebuffer':this['framebuffers'][_0xfc33a5(0x2a5)],'autoClear':!![]}),'trails':new CustomPrimitive({'commandType':_0xfc33a5(0x1ce),'attributeLocations':{'position':0x0,'st':0x1},'geometry':Util[_0xfc33a5(0x1bb)](),'primitiveType':Cesium$4['PrimitiveType'][_0xfc33a5(0x1e2)],'uniformMap':{'segmentsColorTexture':function _0x410756(){var _0x6b8c0f=_0xfc33a5;return _0x551f39[_0x6b8c0f(0x30b)]['segmentsColor'];},'segmentsDepthTexture':function _0x530748(){var _0x3fe108=_0xfc33a5;return _0x551f39[_0x3fe108(0x30b)][_0x3fe108(0x2b4)];},'currentTrailsColor':function _0x14936c(){var _0x370277=_0xfc33a5;return _0x551f39['framebuffers'][_0x370277(0x273)][_0x370277(0x261)](0x0);},'trailsDepthTexture':function _0x1708ae(){var _0x1078c2=_0xfc33a5;return _0x551f39[_0x1078c2(0x1ca)][_0x1078c2(0x273)]['depthTexture'];},'fadeOpacity':function _0x5b5def(){var _0x208db4=_0xfc33a5;return _0x5a1bca[_0x208db4(0x1e4)];}},'vertexShaderSource':new Cesium$4['ShaderSource']({'defines':[_0xfc33a5(0x31d)],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4[(_0xfc33a5(0x1c6))]({'defines':[_0xfc33a5(0x23e)],'sources':[trailDraw_frag]}),'rawRenderState':Util[_0xfc33a5(0x1b2)]({'viewport':undefined,'depthTest':{'enabled':!![],'func':Cesium$4[_0xfc33a5(0x19c)][_0xfc33a5(0x1cc)]},'depthMask':!![]}),'framebuffer':this['framebuffers']['nextTrails'],'autoClear':!![],'preExecute':function _0x43605a(){var _0x34d3de=_0xfc33a5,_0x203edc=_0x551f39[_0x34d3de(0x1ca)]['currentTrails'];_0x551f39[_0x34d3de(0x1ca)]['currentTrails']=_0x551f39[_0x34d3de(0x1ca)][_0x34d3de(0x26c)],_0x551f39[_0x34d3de(0x1ca)][_0x34d3de(0x26c)]=_0x203edc,_0x551f39[_0x34d3de(0x1fe)][_0x34d3de(0x260)]['commandToExecute'][_0x34d3de(0x253)]=_0x551f39['framebuffers'][_0x34d3de(0x26c)],_0x551f39[_0x34d3de(0x1fe)][_0x34d3de(0x260)][_0x34d3de(0x1c4)][_0x34d3de(0x253)]=_0x551f39[_0x34d3de(0x1ca)][_0x34d3de(0x26c)];}}),'screen':new CustomPrimitive({'commandType':_0xfc33a5(0x1ce),'attributeLocations':{'position':0x0,'st':0x1},'geometry':Util['getFullscreenQuad'](),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'trailsColorTexture':function _0x2a69e1(){var _0x3136cf=_0xfc33a5;return _0x551f39[_0x3136cf(0x1ca)][_0x3136cf(0x26c)][_0x3136cf(0x261)](0x0);},'trailsDepthTexture':function _0x34ffdc(){var _0x4779a2=_0xfc33a5;return _0x551f39[_0x4779a2(0x1ca)]['nextTrails'][_0x4779a2(0x1d3)];}},'vertexShaderSource':new Cesium$4['ShaderSource']({'defines':[_0xfc33a5(0x31d)],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4['ShaderSource']({'defines':['DISABLE_LOG_DEPTH_FRAGMENT_WRITE'],'sources':[screenDraw_frag]}),'rawRenderState':Util[_0xfc33a5(0x1b2)]({'viewport':undefined,'depthTest':{'enabled':![]},'depthMask':!![],'blending':{'enabled':!![]}}),'framebuffer':undefined})};}}]),_0x3ff1d4;}()),getWind_frag=_0xee8b26(0x2f5),updateSpeed_frag='uniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0auniform\x20sampler2D\x20particlesWind;\x0a\x0a//\x20used\x20to\x20calculate\x20the\x20wind\x20norm\x0auniform\x20vec2\x20uSpeedRange;\x20//\x20(min,\x20max);\x0auniform\x20vec2\x20vSpeedRange;\x0auniform\x20float\x20pixelSize;\x0auniform\x20float\x20speedFactor;\x0a\x0avarying\x20vec2\x20v_textureCoordinates;\x0a\x0afloat\x20calculateWindNorm(vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec3\x20percent\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20percent.x\x20=\x20(speed.x\x20-\x20uSpeedRange.x)\x20/\x20(uSpeedRange.y\x20-\x20uSpeedRange.x);\x0a\x20\x20\x20\x20percent.y\x20=\x20(speed.y\x20-\x20vSpeedRange.x)\x20/\x20(vSpeedRange.y\x20-\x20vSpeedRange.x);\x0a\x20\x20\x20\x20float\x20normalization\x20=\x20length(percent);\x0a\x0a\x20\x20\x20\x20return\x20normalization;\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20currentSpeed\x20=\x20texture2D(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20texture2D(particlesWind,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20vec4\x20nextSpeed\x20=\x20vec4(speedFactor\x20*\x20pixelSize\x20*\x20windVector,\x20calculateWindNorm(windVector));\x0a\x20\x20\x20\x20gl_FragColor\x20=\x20nextSpeed;\x0a}',updatePosition_frag='uniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0avarying\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20lengthOfLonLat(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x0a\x20\x20\x20\x20float\x20term1\x20=\x20111132.92;\x0a\x20\x20\x20\x20float\x20term2\x20=\x20559.82\x20*\x20cos(2.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term3\x20=\x201.175\x20*\x20cos(4.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term4\x20=\x200.0023\x20*\x20cos(6.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20\x20\x20float\x20term5\x20=\x20111412.84\x20*\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20term6\x20=\x2093.5\x20*\x20cos(3.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term7\x20=\x200.118\x20*\x20cos(5.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avoid\x20updatePosition(vec3\x20lonLatLev,\x20vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLatLev);\x0a\x20\x20\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20vec3\x20windVectorInLonLatLev\x20=\x20vec3(u,\x20v,\x20w);\x0a\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20lonLatLev\x20+\x20windVectorInLonLatLev;\x0a\x0a\x20\x20\x20\x20gl_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture2D(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20speed\x20=\x20texture2D(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20updatePosition(lonLatLev,\x20speed);\x0a}',postProcessingPosition_frag=_0xee8b26(0x31a),postProcessingSpeed_frag=_0xee8b26(0x1bf),Cesium$3=mars3d__namespace[_0xee8b26(0x2f7)],ParticlesComputing=(function(){var _0x4d0d44=_0xee8b26;function _0x29ffa6(_0x26f55b,_0x30c6a8,_0x46ca56,_0x37b8db){var _0x12cc16=_0x1102;_classCallCheck(this,_0x29ffa6),this[_0x12cc16(0x1c9)]=_0x30c6a8,this[_0x12cc16(0x1e7)](_0x26f55b,_0x30c6a8),this[_0x12cc16(0x1c3)](_0x26f55b,_0x46ca56,_0x37b8db),this[_0x12cc16(0x1c2)](_0x30c6a8,_0x46ca56,_0x37b8db);}return _createClass(_0x29ffa6,[{'key':_0x4d0d44(0x1e7),'value':function _0x454899(_0xcca66a,_0x397af8){var _0x3d33de=_0x4d0d44,_0x378004={'context':_0xcca66a,'width':_0x397af8[_0x3d33de(0x22e)][_0x3d33de(0x243)],'height':_0x397af8[_0x3d33de(0x22e)]['lat']*(_0x397af8['dimensions'][_0x3d33de(0x2ac)]||0x1),'pixelFormat':Cesium$3[_0x3d33de(0x316)][_0x3d33de(0x1a5)],'pixelDatatype':Cesium$3[_0x3d33de(0x2cb)]['FLOAT'],'flipY':![],'sampler':new Cesium$3[(_0x3d33de(0x305))]({'minificationFilter':Cesium$3[_0x3d33de(0x1fa)][_0x3d33de(0x205)],'magnificationFilter':Cesium$3[_0x3d33de(0x1f5)][_0x3d33de(0x205)]})};this[_0x3d33de(0x26a)]={'U':Util[_0x3d33de(0x28e)](_0x378004,_0x397af8['U']['array']),'V':Util['createTexture'](_0x378004,_0x397af8['V']['array'])};}},{'key':_0x4d0d44(0x1c3),'value':function _0x5af9f6(_0xbaa2bc,_0x2001f4,_0x384129){var _0x3a2fde=_0x4d0d44,_0x51894c={'context':_0xbaa2bc,'width':_0x2001f4[_0x3a2fde(0x2ef)],'height':_0x2001f4[_0x3a2fde(0x2ef)],'pixelFormat':Cesium$3['PixelFormat'][_0x3a2fde(0x2a6)],'pixelDatatype':Cesium$3[_0x3a2fde(0x2cb)][_0x3a2fde(0x218)],'flipY':![],'sampler':new Cesium$3[(_0x3a2fde(0x305))]({'minificationFilter':Cesium$3[_0x3a2fde(0x1fa)][_0x3a2fde(0x205)],'magnificationFilter':Cesium$3[_0x3a2fde(0x1f5)]['NEAREST']})},_0x21b41a=this[_0x3a2fde(0x224)](_0x2001f4[_0x3a2fde(0x28d)],_0x384129),_0x31aaf1=new Float32Array(0x4*_0x2001f4[_0x3a2fde(0x28d)])[_0x3a2fde(0x1ab)](0x0);this[_0x3a2fde(0x2b5)]={'particlesWind':Util['createTexture'](_0x51894c),'currentParticlesPosition':Util['createTexture'](_0x51894c,_0x21b41a),'nextParticlesPosition':Util[_0x3a2fde(0x28e)](_0x51894c,_0x21b41a),'currentParticlesSpeed':Util['createTexture'](_0x51894c,_0x31aaf1),'nextParticlesSpeed':Util['createTexture'](_0x51894c,_0x31aaf1),'postProcessingPosition':Util['createTexture'](_0x51894c,_0x21b41a),'postProcessingSpeed':Util[_0x3a2fde(0x28e)](_0x51894c,_0x31aaf1)};}},{'key':_0x4d0d44(0x224),'value':function _0x548511(_0x27709e,_0x2cfc34){var _0x52d674=_0x4d0d44,_0x2129bc=new Float32Array(0x4*_0x27709e);for(var _0x18809b=0x0;_0x18809b<_0x27709e;_0x18809b++){_0x2129bc[0x4*_0x18809b]=Cesium$3[_0x52d674(0x291)]['randomBetween'](_0x2cfc34[_0x52d674(0x1ff)]['x'],_0x2cfc34[_0x52d674(0x1ff)]['y']),_0x2129bc[0x4*_0x18809b+0x1]=Cesium$3['Math'][_0x52d674(0x2ba)](_0x2cfc34['latRange']['x'],_0x2cfc34[_0x52d674(0x233)]['y']),_0x2129bc[0x4*_0x18809b+0x2]=Cesium$3[_0x52d674(0x291)][_0x52d674(0x2ba)](this[_0x52d674(0x1c9)][_0x52d674(0x2ac)][_0x52d674(0x2ff)],this[_0x52d674(0x1c9)][_0x52d674(0x2ac)][_0x52d674(0x2a2)]),_0x2129bc[0x4*_0x18809b+0x3]=0x0;}return _0x2129bc;}},{'key':_0x4d0d44(0x1df),'value':function _0x33ca12(){var _0x50cc82=_0x4d0d44,_0x2d6ec3=this;Object[_0x50cc82(0x223)](this[_0x50cc82(0x2b5)])[_0x50cc82(0x30d)](function(_0x251dd6){var _0x1710d4=_0x50cc82;_0x2d6ec3[_0x1710d4(0x2b5)][_0x251dd6][_0x1710d4(0x236)]();});}},{'key':_0x4d0d44(0x1c2),'value':function _0x175992(_0x2e1572,_0x5503ff,_0x585f0a){var _0xfd2586=_0x4d0d44,_0x558a9a=new Cesium$3[(_0xfd2586(0x281))](_0x2e1572[_0xfd2586(0x22e)][_0xfd2586(0x243)],_0x2e1572[_0xfd2586(0x22e)][_0xfd2586(0x2c8)],_0x2e1572[_0xfd2586(0x22e)][_0xfd2586(0x2ac)]),_0x59b044=new Cesium$3[(_0xfd2586(0x281))](_0x2e1572[_0xfd2586(0x243)]['min'],_0x2e1572[_0xfd2586(0x2c8)][_0xfd2586(0x2ff)],_0x2e1572['lev']['min']),_0x5a3ea5=new Cesium$3[(_0xfd2586(0x281))](_0x2e1572['lon'][_0xfd2586(0x2a2)],_0x2e1572['lat']['max'],_0x2e1572[_0xfd2586(0x2ac)][_0xfd2586(0x2a2)]),_0x3ed8e7=new Cesium$3[(_0xfd2586(0x281))]((_0x5a3ea5['x']-_0x59b044['x'])/(_0x558a9a['x']-0x1),(_0x5a3ea5['y']-_0x59b044['y'])/(_0x558a9a['y']-0x1),_0x558a9a['z']>0x1?(_0x5a3ea5['z']-_0x59b044['z'])/(_0x558a9a['z']-0x1):0x1),_0x4601e6=new Cesium$3[(_0xfd2586(0x231))](_0x2e1572['U'][_0xfd2586(0x2ff)],_0x2e1572['U']['max']),_0xba6d83=new Cesium$3[(_0xfd2586(0x231))](_0x2e1572['V'][_0xfd2586(0x2ff)],_0x2e1572['V'][_0xfd2586(0x2a2)]),_0xb499a0=this;this[_0xfd2586(0x1fe)]={'getWind':new CustomPrimitive({'commandType':_0xfd2586(0x19e),'uniformMap':{'U':function _0x355146(){var _0x35aa03=_0xfd2586;return _0xb499a0[_0x35aa03(0x26a)]['U'];},'V':function _0xd15092(){return _0xb499a0['windTextures']['V'];},'currentParticlesPosition':function _0x4b90ec(){var _0x13c252=_0xfd2586;return _0xb499a0[_0x13c252(0x2b5)]['currentParticlesPosition'];},'dimension':function _0x323d89(){return _0x558a9a;},'minimum':function _0x5c8bca(){return _0x59b044;},'maximum':function _0x402394(){return _0x5a3ea5;},'interval':function _0x16c1fc(){return _0x3ed8e7;}},'fragmentShaderSource':new Cesium$3[(_0xfd2586(0x1c6))]({'sources':[getWind_frag]}),'outputTexture':this[_0xfd2586(0x2b5)][_0xfd2586(0x24c)],'preExecute':function _0x56d191(){var _0x1d04b9=_0xfd2586;_0xb499a0['primitives'][_0x1d04b9(0x263)]['commandToExecute']['outputTexture']=_0xb499a0[_0x1d04b9(0x2b5)][_0x1d04b9(0x24c)];}}),'updateSpeed':new CustomPrimitive({'commandType':_0xfd2586(0x19e),'uniformMap':{'currentParticlesSpeed':function _0x74306d(){var _0x408bd9=_0xfd2586;return _0xb499a0['particlesTextures'][_0x408bd9(0x255)];},'particlesWind':function _0x54ee1d(){var _0x11e6bb=_0xfd2586;return _0xb499a0[_0x11e6bb(0x2b5)]['particlesWind'];},'uSpeedRange':function _0x4ed016(){return _0x4601e6;},'vSpeedRange':function _0x5b5eeb(){return _0xba6d83;},'pixelSize':function _0x2c19b4(){var _0x2e6a59=_0xfd2586;return _0x585f0a[_0x2e6a59(0x2aa)];},'speedFactor':function _0x4d0882(){var _0x3a51eb=_0xfd2586;return _0x5503ff[_0x3a51eb(0x226)];}},'fragmentShaderSource':new Cesium$3[(_0xfd2586(0x1c6))]({'sources':[updateSpeed_frag]}),'outputTexture':this['particlesTextures'][_0xfd2586(0x23f)],'preExecute':function _0x453cdf(){var _0x15c4e7=_0xfd2586,_0x16a316=_0xb499a0[_0x15c4e7(0x2b5)][_0x15c4e7(0x255)];_0xb499a0[_0x15c4e7(0x2b5)][_0x15c4e7(0x255)]=_0xb499a0[_0x15c4e7(0x2b5)][_0x15c4e7(0x262)],_0xb499a0['particlesTextures'][_0x15c4e7(0x262)]=_0x16a316,_0xb499a0['primitives'][_0x15c4e7(0x20d)][_0x15c4e7(0x249)][_0x15c4e7(0x1b5)]=_0xb499a0[_0x15c4e7(0x2b5)][_0x15c4e7(0x23f)];}}),'updatePosition':new CustomPrimitive({'commandType':_0xfd2586(0x19e),'uniformMap':{'currentParticlesPosition':function _0xa1859e(){var _0x2bf51d=_0xfd2586;return _0xb499a0[_0x2bf51d(0x2b5)]['currentParticlesPosition'];},'currentParticlesSpeed':function _0x485029(){var _0x700c80=_0xfd2586;return _0xb499a0[_0x700c80(0x2b5)][_0x700c80(0x255)];}},'fragmentShaderSource':new Cesium$3[(_0xfd2586(0x1c6))]({'sources':[updatePosition_frag]}),'outputTexture':this[_0xfd2586(0x2b5)][_0xfd2586(0x2cd)],'preExecute':function _0x3b177d(){var _0x1acac1=_0xfd2586,_0x33fce0=_0xb499a0[_0x1acac1(0x2b5)][_0x1acac1(0x237)];_0xb499a0['particlesTextures'][_0x1acac1(0x237)]=_0xb499a0['particlesTextures'][_0x1acac1(0x2fe)],_0xb499a0['particlesTextures']['postProcessingPosition']=_0x33fce0,_0xb499a0[_0x1acac1(0x1fe)][_0x1acac1(0x274)]['commandToExecute'][_0x1acac1(0x1b5)]=_0xb499a0[_0x1acac1(0x2b5)][_0x1acac1(0x2cd)];}}),'postProcessingPosition':new CustomPrimitive({'commandType':'Compute','uniformMap':{'nextParticlesPosition':function _0x1541dc(){var _0x3b60da=_0xfd2586;return _0xb499a0[_0x3b60da(0x2b5)]['nextParticlesPosition'];},'nextParticlesSpeed':function _0x5f2dc1(){return _0xb499a0['particlesTextures']['nextParticlesSpeed'];},'lonRange':function _0x3159fe(){var _0x3be41d=_0xfd2586;return _0x585f0a[_0x3be41d(0x1ff)];},'latRange':function _0x40d427(){var _0x21adac=_0xfd2586;return _0x585f0a[_0x21adac(0x233)];},'randomCoefficient':function _0x707cda(){var _0x17e02a=_0xfd2586,_0x1c6bc1=Math[_0x17e02a(0x292)]();return _0x1c6bc1;},'dropRate':function _0x2172ff(){return _0x5503ff['dropRate'];},'dropRateBump':function _0x16050a(){return _0x5503ff['dropRateBump'];}},'fragmentShaderSource':new Cesium$3[(_0xfd2586(0x1c6))]({'sources':[postProcessingPosition_frag]}),'outputTexture':this['particlesTextures'][_0xfd2586(0x2fe)],'preExecute':function _0x117c11(){var _0x5b941d=_0xfd2586;_0xb499a0[_0x5b941d(0x1fe)][_0x5b941d(0x2fe)][_0x5b941d(0x249)][_0x5b941d(0x1b5)]=_0xb499a0[_0x5b941d(0x2b5)][_0x5b941d(0x2fe)];}}),'postProcessingSpeed':new CustomPrimitive({'commandType':'Compute','uniformMap':{'postProcessingPosition':function _0x5533af(){var _0x2e2368=_0xfd2586;return _0xb499a0[_0x2e2368(0x2b5)][_0x2e2368(0x2fe)];},'nextParticlesSpeed':function _0x1a7dda(){var _0x2d4101=_0xfd2586;return _0xb499a0[_0x2d4101(0x2b5)][_0x2d4101(0x23f)];}},'fragmentShaderSource':new Cesium$3[(_0xfd2586(0x1c6))]({'sources':[postProcessingSpeed_frag]}),'outputTexture':this['particlesTextures']['postProcessingSpeed'],'preExecute':function _0x514aa7(){var _0x588999=_0xfd2586;_0xb499a0['primitives']['postProcessingSpeed'][_0x588999(0x249)][_0x588999(0x1b5)]=_0xb499a0[_0x588999(0x2b5)][_0x588999(0x262)];}})};}}]),_0x29ffa6;}()),Cesium$2=mars3d__namespace['Cesium'],ParticleSystem=(function(){var _0x5700f2=_0xee8b26;function _0x486285(_0xcb6f23,_0x58ee5f,_0x1a2a65,_0x3402a3){var _0x5bde1b=_0x1102;_classCallCheck(this,_0x486285),this['context']=_0xcb6f23,_0x58ee5f=_objectSpread2({},_0x58ee5f);if(_0x58ee5f[_0x5bde1b(0x31f)]&&_0x58ee5f[_0x5bde1b(0x2be)]){var _0x5bab10,_0x4bb1a1,_0x46560c,_0x255bf3,_0x11b121,_0x19d438;_0x58ee5f[_0x5bde1b(0x22e)]={},_0x58ee5f[_0x5bde1b(0x22e)][_0x5bde1b(0x243)]=_0x58ee5f[_0x5bde1b(0x1e1)],_0x58ee5f[_0x5bde1b(0x22e)][_0x5bde1b(0x2c8)]=_0x58ee5f[_0x5bde1b(0x278)],_0x58ee5f[_0x5bde1b(0x22e)]['lev']=_0x58ee5f['lev']||0x1,_0x58ee5f[_0x5bde1b(0x243)]={},_0x58ee5f[_0x5bde1b(0x243)]['min']=_0x58ee5f[_0x5bde1b(0x313)],_0x58ee5f[_0x5bde1b(0x243)]['max']=_0x58ee5f[_0x5bde1b(0x270)],_0x58ee5f[_0x5bde1b(0x2c8)]={},_0x58ee5f['lat'][_0x5bde1b(0x2ff)]=_0x58ee5f[_0x5bde1b(0x315)],_0x58ee5f[_0x5bde1b(0x2c8)]['max']=_0x58ee5f[_0x5bde1b(0x2ae)],_0x58ee5f[_0x5bde1b(0x2ac)]={},_0x58ee5f[_0x5bde1b(0x2ac)][_0x5bde1b(0x2ff)]=(_0x5bab10=_0x58ee5f[_0x5bde1b(0x280)])!==null&&_0x5bab10!==void 0x0?_0x5bab10:0x1,_0x58ee5f['lev'][_0x5bde1b(0x2a2)]=(_0x4bb1a1=_0x58ee5f[_0x5bde1b(0x1a1)])!==null&&_0x4bb1a1!==void 0x0?_0x4bb1a1:0x1,_0x58ee5f['U']={},_0x58ee5f['U'][_0x5bde1b(0x2ed)]=new Float32Array(_0x58ee5f[_0x5bde1b(0x31f)]),_0x58ee5f['U'][_0x5bde1b(0x2ff)]=(_0x46560c=_0x58ee5f['umin'])!==null&&_0x46560c!==void 0x0?_0x46560c:Math[_0x5bde1b(0x2ff)][_0x5bde1b(0x264)](Math,_toConsumableArray(_0x58ee5f['udata'])),_0x58ee5f['U'][_0x5bde1b(0x2a2)]=(_0x255bf3=_0x58ee5f[_0x5bde1b(0x1b9)])!==null&&_0x255bf3!==void 0x0?_0x255bf3:Math[_0x5bde1b(0x2a2)][_0x5bde1b(0x264)](Math,_toConsumableArray(_0x58ee5f[_0x5bde1b(0x31f)])),_0x58ee5f['V']={},_0x58ee5f['V'][_0x5bde1b(0x2ed)]=new Float32Array(_0x58ee5f[_0x5bde1b(0x2be)]),_0x58ee5f['V'][_0x5bde1b(0x2ff)]=(_0x11b121=_0x58ee5f['vmin'])!==null&&_0x11b121!==void 0x0?_0x11b121:Math['min'][_0x5bde1b(0x264)](Math,_toConsumableArray(_0x58ee5f[_0x5bde1b(0x2be)])),_0x58ee5f['V']['max']=(_0x19d438=_0x58ee5f[_0x5bde1b(0x1c8)])!==null&&_0x19d438!==void 0x0?_0x19d438:Math[_0x5bde1b(0x2a2)][_0x5bde1b(0x264)](Math,_toConsumableArray(_0x58ee5f[_0x5bde1b(0x2be)]));}this[_0x5bde1b(0x1c9)]=_0x58ee5f,this['options']=_0x1a2a65,this[_0x5bde1b(0x21e)]=_0x3402a3,this[_0x5bde1b(0x1fc)]=new ParticlesComputing(this[_0x5bde1b(0x1b1)],this[_0x5bde1b(0x1c9)],this[_0x5bde1b(0x1c5)],this[_0x5bde1b(0x21e)]),this[_0x5bde1b(0x207)]=new ParticlesRendering(this[_0x5bde1b(0x1b1)],this[_0x5bde1b(0x1c9)],this[_0x5bde1b(0x1c5)],this[_0x5bde1b(0x21e)],this[_0x5bde1b(0x1fc)]);}return _createClass(_0x486285,[{'key':_0x5700f2(0x1d5),'value':function _0x48c81d(_0x8a935c){var _0x54df83=_0x5700f2,_0x209397=this;this[_0x54df83(0x1fc)][_0x54df83(0x1df)](),Object[_0x54df83(0x223)](this[_0x54df83(0x1fc)][_0x54df83(0x26a)])[_0x54df83(0x30d)](function(_0x4e2bdc){var _0x35354e=_0x54df83;_0x209397[_0x35354e(0x1fc)][_0x35354e(0x26a)][_0x4e2bdc][_0x35354e(0x236)]();}),this[_0x54df83(0x207)][_0x54df83(0x30b)][_0x54df83(0x211)]['destroy'](),Object[_0x54df83(0x223)](this['particlesRendering']['framebuffers'])[_0x54df83(0x30d)](function(_0x4073a6){var _0xda386a=_0x54df83;_0x209397[_0xda386a(0x207)][_0xda386a(0x1ca)][_0x4073a6][_0xda386a(0x236)]();}),this[_0x54df83(0x1b1)]=_0x8a935c,this['particlesComputing']=new ParticlesComputing(this['context'],this['data'],this[_0x54df83(0x1c5)],this[_0x54df83(0x21e)]),this[_0x54df83(0x207)]=new ParticlesRendering(this[_0x54df83(0x1b1)],this[_0x54df83(0x1c9)],this[_0x54df83(0x1c5)],this[_0x54df83(0x21e)],this[_0x54df83(0x1fc)]);}},{'key':_0x5700f2(0x2b3),'value':function _0x1638cb(){var _0x2fff69=_0x5700f2,_0x41a9fd=this,_0x18e53a=new Cesium$2['ClearCommand']({'color':new Cesium$2['Color'](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Cesium$2['Pass']['OPAQUE']});Object['keys'](this['particlesRendering'][_0x2fff69(0x1ca)])[_0x2fff69(0x30d)](function(_0x149963){var _0x4a099a=_0x2fff69;_0x18e53a[_0x4a099a(0x253)]=_0x41a9fd['particlesRendering'][_0x4a099a(0x1ca)][_0x149963],_0x18e53a[_0x4a099a(0x2a8)](_0x41a9fd[_0x4a099a(0x1b1)]);});}},{'key':_0x5700f2(0x20f),'value':function _0xbb93e0(_0x58308e){var _0x831687=_0x5700f2;this['clearFramebuffers'](),this[_0x831687(0x1fc)][_0x831687(0x1df)](),this[_0x831687(0x1fc)]['createParticlesTextures'](this[_0x831687(0x1b1)],this[_0x831687(0x1c5)],this[_0x831687(0x21e)]);if(_0x58308e){var _0x25fd75=this[_0x831687(0x207)][_0x831687(0x2b0)](this[_0x831687(0x1c5)]);this[_0x831687(0x207)][_0x831687(0x1fe)][_0x831687(0x2a5)][_0x831687(0x244)]=_0x25fd75;var _0x5c985b=Cesium$2[_0x831687(0x271)][_0x831687(0x265)]({'context':this[_0x831687(0x1b1)],'geometry':_0x25fd75,'attributeLocations':this[_0x831687(0x207)][_0x831687(0x1fe)][_0x831687(0x2a5)][_0x831687(0x202)],'bufferUsage':Cesium$2[_0x831687(0x2bc)][_0x831687(0x2f6)]});this[_0x831687(0x207)]['primitives'][_0x831687(0x2a5)][_0x831687(0x249)][_0x831687(0x20c)]=_0x5c985b;}}},{'key':_0x5700f2(0x2ea),'value':function _0x362c16(_0x4b5369){var _0x1145b3=_0x5700f2,_0x50a164=this,_0x4b81bb=![];this[_0x1145b3(0x1c5)][_0x1145b3(0x28d)]!==_0x4b5369[_0x1145b3(0x28d)]&&(_0x4b81bb=!![]),Object[_0x1145b3(0x223)](_0x4b5369)['forEach'](function(_0x2362a3){_0x50a164['options'][_0x2362a3]=_0x4b5369[_0x2362a3];}),this['refreshParticles'](_0x4b81bb);}},{'key':_0x5700f2(0x29e),'value':function _0x2ded4(_0x50981a){var _0x6d6e26=_0x5700f2,_0x183854=this;Object['keys'](_0x50981a)[_0x6d6e26(0x30d)](function(_0xe24aa4){var _0x1902e6=_0x6d6e26;_0x183854[_0x1902e6(0x21e)][_0xe24aa4]=_0x50981a[_0xe24aa4];}),this[_0x6d6e26(0x20f)](![]);}},{'key':_0x5700f2(0x236),'value':function _0x43f362(){var _0xadf8ad=_0x5700f2,_0x1d85d0=this;clearTimeout(this[_0xadf8ad(0x31e)]),this[_0xadf8ad(0x1fc)][_0xadf8ad(0x1df)](),Object['keys'](this[_0xadf8ad(0x1fc)][_0xadf8ad(0x26a)])[_0xadf8ad(0x30d)](function(_0x4548ab){var _0x164bb9=_0xadf8ad;_0x1d85d0[_0x164bb9(0x1fc)]['windTextures'][_0x4548ab][_0x164bb9(0x236)]();}),this['particlesRendering'][_0xadf8ad(0x30b)][_0xadf8ad(0x211)][_0xadf8ad(0x236)](),Object[_0xadf8ad(0x223)](this[_0xadf8ad(0x207)][_0xadf8ad(0x1ca)])[_0xadf8ad(0x30d)](function(_0x820a4b){var _0x31d853=_0xadf8ad;_0x1d85d0[_0x31d853(0x207)][_0x31d853(0x1ca)][_0x820a4b][_0x31d853(0x236)]();});for(var _0x545cde in this){delete this[_0x545cde];}}}]),_0x486285;}()),Cesium$1=mars3d__namespace[_0xee8b26(0x2f7)],BaseLayer$1=mars3d__namespace[_0xee8b26(0x21f)][_0xee8b26(0x2de)],DEF_OPTIONS={'particlesNumber':0x1000,'fixedHeight':0x0,'fadeOpacity':0.996,'dropRate':0.003,'dropRateBump':0.01,'speedFactor':0.5,'lineWidth':0x2,'colors':[_0xee8b26(0x250)]},WindLayer=function(_0x43e3b9){var _0x460629=_0xee8b26;_inherits(_0x15be36,_0x43e3b9);var _0x267963=_createSuper(_0x15be36);function _0x15be36(){var _0x9223b=_0x1102,_0x8fdc7d,_0x21c71b=arguments['length']>0x0&&arguments[0x0]!==undefined?arguments[0x0]:{};return _classCallCheck(this,_0x15be36),_0x21c71b=_objectSpread2(_objectSpread2({},DEF_OPTIONS),_0x21c71b),_0x8fdc7d=_0x267963[_0x9223b(0x21d)](this,_0x21c71b),_0x8fdc7d[_0x9223b(0x28b)](_0x21c71b),_0x8fdc7d;}return _createClass(_0x15be36,[{'key':'layer','get':function _0x1d4356(){return this['primitives'];}},{'key':_0x460629(0x1c9),'get':function _0x1b7ff9(){var _0x5b7967=_0x460629;return this[_0x5b7967(0x2c1)];},'set':function _0x1b9158(_0x4bdcb9){var _0x7e72dd=_0x460629;this[_0x7e72dd(0x1ea)](_0x4bdcb9);}},{'key':_0x460629(0x279),'get':function _0x4f43bb(){var _0x519b9b=_0x460629;return this[_0x519b9b(0x1c5)][_0x519b9b(0x279)];},'set':function _0x3e1742(_0x469c33){var _0x36064b=_0x460629;this[_0x36064b(0x1c5)]['colors']=_0x469c33,this[_0x36064b(0x299)]&&this[_0x36064b(0x299)][_0x36064b(0x2ea)]({'colors':_0x469c33}),this['resize']();}},{'key':'_mountedHook','value':function _0x22c55d(){}},{'key':_0x460629(0x1ee),'value':function _0x5bffc5(){var _0x554407=_0x460629;this[_0x554407(0x24b)]=this[_0x554407(0x307)]['scene'],this[_0x554407(0x1eb)]=this['_map'][_0x554407(0x1eb)],this[_0x554407(0x1fe)]=new Cesium$1['PrimitiveCollection'](),this['_map']['scene'][_0x554407(0x1fe)][_0x554407(0x21b)](this['primitives']),this['viewerParameters']={'lonRange':new Cesium$1['Cartesian2'](),'latRange':new Cesium$1[(_0x554407(0x231))](),'pixelSize':0x0},this[_0x554407(0x2ca)]=new Cesium$1[(_0x554407(0x269))](Cesium$1[_0x554407(0x281)][_0x554407(0x1b3)],0.99*0x615299),this[_0x554407(0x24a)](),window[_0x554407(0x1ed)](_0x554407(0x1ae),this[_0x554407(0x1ae)][_0x554407(0x288)](this),![]),this[_0x554407(0x20a)]=![],this[_0x554407(0x1ac)]=![],this['_map']['on'](mars3d__namespace[_0x554407(0x319)][_0x554407(0x26e)],this[_0x554407(0x30c)],this),this['_map']['on'](mars3d__namespace[_0x554407(0x319)][_0x554407(0x285)],this[_0x554407(0x272)],this),this[_0x554407(0x307)]['on'](mars3d__namespace[_0x554407(0x319)]['mouseUp'],this[_0x554407(0x1f3)],this),this[_0x554407(0x307)]['on'](mars3d__namespace['EventType'][_0x554407(0x1a6)],this[_0x554407(0x27b)],this),this[_0x554407(0x2c1)]&&this[_0x554407(0x1ea)](this[_0x554407(0x2c1)]);}},{'key':_0x460629(0x227),'value':function _0x43cc14(){var _0x1ffd42=_0x460629;window[_0x1ffd42(0x1d8)](_0x1ffd42(0x1ae),this[_0x1ffd42(0x1ae)]),this[_0x1ffd42(0x307)]['off'](mars3d__namespace[_0x1ffd42(0x319)][_0x1ffd42(0x2ee)],this[_0x1ffd42(0x2f2)],this),this[_0x1ffd42(0x307)][_0x1ffd42(0x277)](mars3d__namespace['EventType'][_0x1ffd42(0x26e)],this[_0x1ffd42(0x30c)],this),this[_0x1ffd42(0x307)][_0x1ffd42(0x277)](mars3d__namespace[_0x1ffd42(0x319)][_0x1ffd42(0x285)],this[_0x1ffd42(0x272)],this),this[_0x1ffd42(0x307)]['off'](mars3d__namespace['EventType']['mouseUp'],this['_onMouseUpEvent'],this),this[_0x1ffd42(0x307)][_0x1ffd42(0x277)](mars3d__namespace[_0x1ffd42(0x319)][_0x1ffd42(0x1a6)],this[_0x1ffd42(0x27b)],this),this[_0x1ffd42(0x1fe)][_0x1ffd42(0x2cc)](),this[_0x1ffd42(0x307)]['scene']['primitives'][_0x1ffd42(0x1cd)](this[_0x1ffd42(0x1fe)]);}},{'key':_0x460629(0x1ae),'value':function _0x4bd03c(){var _0x2657f7=_0x460629;if(!this[_0x2657f7(0x25e)]||!this[_0x2657f7(0x299)])return;this[_0x2657f7(0x1fe)][_0x2657f7(0x25e)]=![],this[_0x2657f7(0x1fe)][_0x2657f7(0x2cc)](),this[_0x2657f7(0x307)][_0x2657f7(0x248)](mars3d__namespace[_0x2657f7(0x319)]['preRender'],this[_0x2657f7(0x2f2)],this);}},{'key':_0x460629(0x2f2),'value':function _0x13d366(_0x42cef6){var _0x539f7e=_0x460629;this[_0x539f7e(0x299)][_0x539f7e(0x1d5)](this['scene']['context']),this[_0x539f7e(0x1e0)](),this[_0x539f7e(0x1fe)][_0x539f7e(0x25e)]=!![];}},{'key':'_onMapWhellEvent','value':function _0x404d98(_0x5620f1){var _0x23a38c=_0x460629,_0x25ee79=this;clearTimeout(this['refreshTimer']);if(!this[_0x23a38c(0x25e)]||!this[_0x23a38c(0x299)])return;this[_0x23a38c(0x1fe)][_0x23a38c(0x25e)]=![],this[_0x23a38c(0x1e5)]=setTimeout(function(){var _0x395d37=_0x23a38c;if(!_0x25ee79['show'])return;_0x25ee79[_0x395d37(0x1dd)]();},0xc8);}},{'key':_0x460629(0x272),'value':function _0x50b3bf(_0x4ca66c){this['mouse_down']=!![];}},{'key':_0x460629(0x27b),'value':function _0x53cc25(_0x5afbff){var _0x46ca92=_0x460629;if(!this[_0x46ca92(0x25e)]||!this[_0x46ca92(0x299)])return;this[_0x46ca92(0x20a)]&&(this[_0x46ca92(0x1fe)][_0x46ca92(0x25e)]=![],this['mouse_move']=!![]);}},{'key':_0x460629(0x1f3),'value':function _0x33979a(_0x9eba6f){var _0x18f499=_0x460629;if(!this[_0x18f499(0x25e)]||!this[_0x18f499(0x299)])return;this[_0x18f499(0x20a)]&&this[_0x18f499(0x1ac)]&&this['redraw'](),this['primitives']['show']=!![],this[_0x18f499(0x20a)]=![],this[_0x18f499(0x1ac)]=![];}},{'key':'redraw','value':function _0x5e7e1e(){var _0x5b6460=_0x460629;if(!this[_0x5b6460(0x307)]||!this[_0x5b6460(0x25e)])return;this['updateViewerParameters'](),this[_0x5b6460(0x299)][_0x5b6460(0x29e)](this[_0x5b6460(0x21e)]),this[_0x5b6460(0x1fe)][_0x5b6460(0x25e)]=!![];}},{'key':_0x460629(0x1ea),'value':function _0xff3f17(_0x5c63fc){var _0x429229=_0x460629;this[_0x429229(0x2c1)]=_0x5c63fc,this['particleSystem']&&this['particleSystem']['destroy'](),this[_0x429229(0x299)]=new ParticleSystem(this[_0x429229(0x24b)]['context'],_0x5c63fc,this['getOptions'](),this[_0x429229(0x21e)]),this[_0x429229(0x1e0)]();}},{'key':_0x460629(0x28b),'value':function _0x416093(_0x599746){var _0xe79562=_0x460629;if(_0x599746)for(var _0x5f254f in _0x599746){this[_0x5f254f]=_0x599746[_0x5f254f];}this[_0xe79562(0x299)]&&this[_0xe79562(0x299)]['setOptions'](this['getOptions']());}},{'key':_0x460629(0x25a),'value':function _0x53dcc3(){var _0x15074a=_0x460629,_0x789031=Math[_0x15074a(0x258)](Math[_0x15074a(0x19f)](this[_0x15074a(0x1b6)]));return this['particlesNumber']=_0x789031*_0x789031,{'particlesTextureSize':_0x789031,'maxParticles':this[_0x15074a(0x1b6)],'particleHeight':this[_0x15074a(0x2d9)],'fadeOpacity':this[_0x15074a(0x1e4)],'dropRate':this['dropRate'],'dropRateBump':this[_0x15074a(0x2df)],'speedFactor':this['speedFactor'],'lineWidth':this[_0x15074a(0x22f)],'globeLayer':this['globeLayer'],'WMS_URL':this[_0x15074a(0x1d9)],'colors':this[_0x15074a(0x279)]};}},{'key':_0x460629(0x1e0),'value':function _0x2427dd(){var _0xa68d3f=_0x460629;this[_0xa68d3f(0x1fe)]['add'](this[_0xa68d3f(0x299)][_0xa68d3f(0x1fc)][_0xa68d3f(0x1fe)][_0xa68d3f(0x263)]),this[_0xa68d3f(0x1fe)][_0xa68d3f(0x21b)](this[_0xa68d3f(0x299)]['particlesComputing'][_0xa68d3f(0x1fe)][_0xa68d3f(0x20d)]),this['primitives']['add'](this[_0xa68d3f(0x299)]['particlesComputing'][_0xa68d3f(0x1fe)][_0xa68d3f(0x274)]),this['primitives'][_0xa68d3f(0x21b)](this[_0xa68d3f(0x299)]['particlesComputing'][_0xa68d3f(0x1fe)]['postProcessingPosition']),this[_0xa68d3f(0x1fe)][_0xa68d3f(0x21b)](this[_0xa68d3f(0x299)]['particlesComputing'][_0xa68d3f(0x1fe)][_0xa68d3f(0x262)]),this[_0xa68d3f(0x1fe)]['add'](this[_0xa68d3f(0x299)][_0xa68d3f(0x207)][_0xa68d3f(0x1fe)][_0xa68d3f(0x2a5)]),this[_0xa68d3f(0x1fe)][_0xa68d3f(0x21b)](this[_0xa68d3f(0x299)][_0xa68d3f(0x207)][_0xa68d3f(0x1fe)][_0xa68d3f(0x260)]),this[_0xa68d3f(0x1fe)]['add'](this[_0xa68d3f(0x299)][_0xa68d3f(0x207)][_0xa68d3f(0x1fe)][_0xa68d3f(0x204)]);}},{'key':'updateViewerParameters','value':function _0xd8b183(){var _0x29411f=_0x460629,_0x4b2a5e=this[_0x29411f(0x1eb)]['computeViewRectangle'](this[_0x29411f(0x24b)]['globe']['ellipsoid']);if(!_0x4b2a5e){var _0x469326=this['_map'][_0x29411f(0x1a4)]();_0x4b2a5e=Cesium$1[_0x29411f(0x229)][_0x29411f(0x2a9)](_0x469326[_0x29411f(0x313)],_0x469326[_0x29411f(0x315)],_0x469326[_0x29411f(0x270)],_0x469326[_0x29411f(0x2ae)]);}var _0x52936d=Util[_0x29411f(0x251)](_0x4b2a5e);this[_0x29411f(0x21e)]['lonRange']['x']=_0x52936d['lon']['min'],this['viewerParameters'][_0x29411f(0x1ff)]['y']=_0x52936d[_0x29411f(0x243)][_0x29411f(0x2a2)],this[_0x29411f(0x21e)][_0x29411f(0x233)]['x']=_0x52936d[_0x29411f(0x2c8)][_0x29411f(0x2ff)],this[_0x29411f(0x21e)][_0x29411f(0x233)]['y']=_0x52936d[_0x29411f(0x2c8)][_0x29411f(0x2a2)];var _0x155b55=this[_0x29411f(0x1eb)][_0x29411f(0x287)](this[_0x29411f(0x2ca)],this['scene'][_0x29411f(0x22d)],this[_0x29411f(0x24b)]['drawingBufferHeight']);_0x155b55>0x0&&(this[_0x29411f(0x21e)]['pixelSize']=_0x155b55);}}]),_0x15be36;}(BaseLayer$1);mars3d__namespace[_0xee8b26(0x1cf)][_0xee8b26(0x1c0)](_0xee8b26(0x2e4),WindLayer),mars3d__namespace[_0xee8b26(0x21f)][_0xee8b26(0x1af)]=WindLayer;var CanvasParticle=_createClass(function CanvasParticle(){var _0x36cc04=_0xee8b26;_classCallCheck(this,CanvasParticle),this[_0x36cc04(0x256)]=null,this['lat']=null,this[_0x36cc04(0x2d1)]=null,this[_0x36cc04(0x2b6)]=null,this['age']=null;}),CanvasWindField=(function(){var _0x2fdce1=_0xee8b26;function _0x1338b7(_0x43789c,_0x1f7d50){var _0x35a87d=_0x1102;_classCallCheck(this,_0x1338b7),this['rows']=_0x43789c[_0x35a87d(0x278)],this[_0x35a87d(0x1e1)]=_0x43789c[_0x35a87d(0x1e1)],this[_0x35a87d(0x313)]=_0x43789c[_0x35a87d(0x313)],this[_0x35a87d(0x270)]=_0x43789c[_0x35a87d(0x270)],this[_0x35a87d(0x315)]=_0x43789c[_0x35a87d(0x315)],this[_0x35a87d(0x2ae)]=_0x43789c[_0x35a87d(0x2ae)],this[_0x35a87d(0x1e9)]=[];var _0x45a5b6=_0x43789c[_0x35a87d(0x31f)],_0x13659d=_0x43789c[_0x35a87d(0x2be)],_0x2c2926=![];_0x45a5b6[_0x35a87d(0x2e1)]===this[_0x35a87d(0x278)]&&_0x45a5b6[0x0]['length']===this[_0x35a87d(0x1e1)]&&(_0x2c2926=!![]);var _0x501137=0x0,_0xfa3240=null,_0x215b74=null;for(var _0x42b91f=0x0;_0x42b91f<this[_0x35a87d(0x278)];_0x42b91f++){_0xfa3240=[];for(var _0x56c5cf=0x0;_0x56c5cf<this[_0x35a87d(0x1e1)];_0x56c5cf++,_0x501137++){_0x2c2926?_0x215b74=this[_0x35a87d(0x297)](_0x45a5b6[_0x42b91f][_0x56c5cf],_0x13659d[_0x42b91f][_0x56c5cf]):_0x215b74=this[_0x35a87d(0x297)](_0x45a5b6[_0x501137],_0x13659d[_0x501137]),_0xfa3240[_0x35a87d(0x19b)](_0x215b74);}this[_0x35a87d(0x1e9)][_0x35a87d(0x19b)](_0xfa3240);}_0x1f7d50&&this['grid'][_0x35a87d(0x2d3)]();}return _createClass(_0x1338b7,[{'key':'toGridXY','value':function _0x54a1d0(_0x1830d9,_0x336574){var _0x307ef4=_0x1102,_0x2dc217=(_0x1830d9-this[_0x307ef4(0x313)])/(this[_0x307ef4(0x270)]-this[_0x307ef4(0x313)])*(this['cols']-0x1),_0x3338c9=(this[_0x307ef4(0x2ae)]-_0x336574)/(this['ymax']-this[_0x307ef4(0x315)])*(this[_0x307ef4(0x278)]-0x1);return{'x':_0x2dc217,'y':_0x3338c9};}},{'key':_0x2fdce1(0x2fb),'value':function _0x4b9623(_0x5f40f6,_0xa7d719){var _0x49d4be=_0x2fdce1;if(_0x5f40f6<0x0||_0x5f40f6>=this[_0x49d4be(0x1e1)]||_0xa7d719>=this[_0x49d4be(0x278)])return[0x0,0x0,0x0];var _0x48a0c0=Math['floor'](_0x5f40f6),_0x45cc3b=Math[_0x49d4be(0x2f1)](_0xa7d719);if(_0x48a0c0===_0x5f40f6&&_0x45cc3b===_0xa7d719)return this[_0x49d4be(0x1e9)][_0xa7d719][_0x5f40f6];var _0x282f71=_0x48a0c0+0x1,_0x4f8f9b=_0x45cc3b+0x1,_0x626a87=this[_0x49d4be(0x2fb)](_0x48a0c0,_0x45cc3b),_0x1b0ada=this['getUVByXY'](_0x282f71,_0x45cc3b),_0x28d09f=this[_0x49d4be(0x2fb)](_0x48a0c0,_0x4f8f9b),_0x9ddd9a=this[_0x49d4be(0x2fb)](_0x282f71,_0x4f8f9b),_0x57dcb2=null;try{_0x57dcb2=this[_0x49d4be(0x1f0)](_0x5f40f6-_0x48a0c0,_0xa7d719-_0x45cc3b,_0x626a87,_0x1b0ada,_0x28d09f,_0x9ddd9a);}catch(_0x28b8e0){console[_0x49d4be(0x245)](_0x5f40f6,_0xa7d719);}return _0x57dcb2;}},{'key':'_bilinearInterpolation','value':function _0x11384d(_0x364f96,_0x1d48d6,_0x3498b2,_0x4a230a,_0x3c86ec,_0x4d2e22){var _0x56cd52=_0x2fdce1,_0x384c0d=0x1-_0x364f96,_0x3727c8=0x1-_0x1d48d6,_0x4e0786=_0x384c0d*_0x3727c8,_0x461dc5=_0x364f96*_0x3727c8,_0x4fe79b=_0x384c0d*_0x1d48d6,_0x13d644=_0x364f96*_0x1d48d6,_0x18f8c8=_0x3498b2[0x0]*_0x4e0786+_0x4a230a[0x0]*_0x461dc5+_0x3c86ec[0x0]*_0x4fe79b+_0x4d2e22[0x0]*_0x13d644,_0x532ed6=_0x3498b2[0x1]*_0x4e0786+_0x4a230a[0x1]*_0x461dc5+_0x3c86ec[0x1]*_0x4fe79b+_0x4d2e22[0x1]*_0x13d644;return this[_0x56cd52(0x297)](_0x18f8c8,_0x532ed6);}},{'key':_0x2fdce1(0x297),'value':function _0x1895fa(_0x20bc56,_0x32ebae){var _0x57a475=_0x2fdce1;return[+_0x20bc56,+_0x32ebae,Math[_0x57a475(0x19f)](_0x20bc56*_0x20bc56+_0x32ebae*_0x32ebae)];}},{'key':_0x2fdce1(0x234),'value':function _0x1e8769(_0x18032e,_0x3b29e0){var _0x181014=_0x2fdce1;if(!this[_0x181014(0x2c3)](_0x18032e,_0x3b29e0))return null;var _0x4ffa41=this[_0x181014(0x252)](_0x18032e,_0x3b29e0),_0x4e02a7=this[_0x181014(0x2fb)](_0x4ffa41['x'],_0x4ffa41['y']);return _0x4e02a7;}},{'key':_0x2fdce1(0x2c3),'value':function _0x4adcaa(_0x452b12,_0x3f0da8){var _0x53c0ce=_0x2fdce1;return _0x452b12>=this['xmin']&&_0x452b12<=this[_0x53c0ce(0x270)]&&_0x3f0da8>=this[_0x53c0ce(0x315)]&&_0x3f0da8<=this[_0x53c0ce(0x2ae)]?!![]:![];}},{'key':_0x2fdce1(0x1fd),'value':function _0x2094a8(){var _0x226ecc=_0x2fdce1,_0xff5ed9=fRandomByfloat(this[_0x226ecc(0x313)],this['xmax']),_0xa43844=fRandomByfloat(this[_0x226ecc(0x315)],this[_0x226ecc(0x2ae)]);return{'lat':_0xa43844,'lng':_0xff5ed9};}}]),_0x1338b7;}());function fRandomByfloat(_0x44cb7d,_0x592ee9){var _0x114b4e=_0xee8b26;return _0x44cb7d+Math[_0x114b4e(0x292)]()*(_0x592ee9-_0x44cb7d);}var Cesium=mars3d__namespace[_0xee8b26(0x2f7)],BaseLayer=mars3d__namespace[_0xee8b26(0x21f)]['BaseLayer'],CanvasWindLayer=function(_0x222e35){var _0x572999=_0xee8b26;_inherits(_0x111a6a,_0x222e35);var _0x578ada=_createSuper(_0x111a6a);function _0x111a6a(){var _0x5c4150=_0x1102,_0x320a46,_0xa02f49=arguments[_0x5c4150(0x2e1)]>0x0&&arguments[0x0]!==undefined?arguments[0x0]:{};return _classCallCheck(this,_0x111a6a),_0x320a46=_0x578ada['call'](this,_0xa02f49),_0x320a46['_setOptionsHook'](_0xa02f49),_0x320a46[_0x5c4150(0x257)]=null,_0x320a46;}return _createClass(_0x111a6a,[{'key':_0x572999(0x28b),'value':function _0x3fa5a5(_0x30a74a){var _0x1d0263=_0x572999,_0x1e3d5c,_0x39e583,_0x9a5be6;this[_0x1d0263(0x22c)]=[0x0,0x0],this[_0x1d0263(0x1a2)]=[],this[_0x1d0263(0x1bd)]=_0x30a74a[_0x1d0263(0x1bd)]||0x32,this['_particlesNumber']=_0x30a74a[_0x1d0263(0x1b6)]||0x1000,this[_0x1d0263(0x2f0)]=_0x30a74a[_0x1d0263(0x1a0)]||0x78,this['frameTime']=0x3e8/(_0x30a74a[_0x1d0263(0x2a4)]||0xa),this[_0x1d0263(0x199)]=(_0x1e3d5c=this[_0x1d0263(0x1c5)][_0x1d0263(0x2f8)])!==null&&_0x1e3d5c!==void 0x0?_0x1e3d5c:![],this['color']=_0x30a74a[_0x1d0263(0x217)]||_0x1d0263(0x24e),this[_0x1d0263(0x22f)]=_0x30a74a[_0x1d0263(0x22f)]||0x1,this['fixedHeight']=(_0x39e583=_0x30a74a['fixedHeight'])!==null&&_0x39e583!==void 0x0?_0x39e583:0x0,this[_0x1d0263(0x293)]=(_0x9a5be6=_0x30a74a['reverseY'])!==null&&_0x9a5be6!==void 0x0?_0x9a5be6:![];}},{'key':'layer','get':function _0xb4363d(){var _0x60e256=_0x572999;return this[_0x60e256(0x257)];}},{'key':'canvasWidth','get':function _0x40bb41(){var _0x357c64=_0x572999;return this[_0x357c64(0x307)]['scene'][_0x357c64(0x257)][_0x357c64(0x275)];}},{'key':'canvasHeight','get':function _0x2e76a8(){var _0x3d60c2=_0x572999;return this[_0x3d60c2(0x307)][_0x3d60c2(0x24b)][_0x3d60c2(0x257)][_0x3d60c2(0x1ef)];}},{'key':_0x572999(0x2f8),'get':function _0x3789c9(){var _0x4d7116=_0x572999;return this[_0x4d7116(0x199)];},'set':function _0x5b3f44(_0x2224d6){var _0x35f905=_0x572999;this['_pointerEvents']=_0x2224d6;if(!this[_0x35f905(0x257)])return;_0x2224d6?this[_0x35f905(0x257)][_0x35f905(0x306)][_0x35f905(0x198)]=_0x35f905(0x30f):this['canvas'][_0x35f905(0x306)][_0x35f905(0x198)]=_0x35f905(0x221);}},{'key':'speedRate','get':function _0x291341(){var _0x4ce731=_0x572999;return this[_0x4ce731(0x1f1)];},'set':function _0x49121f(_0x53ef13){var _0x582f21=_0x572999;this[_0x582f21(0x1f1)]=(0x64-(_0x53ef13>0x63?0x63:_0x53ef13))*0x64,this[_0x582f21(0x241)]();}},{'key':'particlesNumber','get':function _0x3e8cb4(){var _0xc3ebb5=_0x572999;return this[_0xc3ebb5(0x239)];},'set':function _0x2854bb(_0x41d916){var _0x33cfa6=_0x572999,_0xea61d6=this;this['_particlesNumber']=_0x41d916,clearTimeout(this[_0x33cfa6(0x238)]),this[_0x33cfa6(0x238)]=setTimeout(function(){var _0x11c184=_0x33cfa6;_0xea61d6[_0x11c184(0x1dd)]();},0x1f4);}},{'key':_0x572999(0x1a0),'get':function _0xadc094(){var _0x129416=_0x572999;return this[_0x129416(0x2f0)];},'set':function _0x2043dc(_0x35f6e2){var _0x9e0e20=_0x572999,_0x3cf41c=this;this[_0x9e0e20(0x2f0)]=_0x35f6e2,clearTimeout(this[_0x9e0e20(0x238)]),this[_0x9e0e20(0x238)]=setTimeout(function(){var _0x2183f9=_0x9e0e20;_0x3cf41c[_0x2183f9(0x1dd)]();},0x1f4);}},{'key':_0x572999(0x1c9),'get':function _0x5d34cb(){var _0x3e01b2=_0x572999;return this[_0x3e01b2(0x220)];},'set':function _0xa01479(_0x43c003){this['setData'](_0x43c003);}},{'key':_0x572999(0x29c),'value':function _0x4e12b(_0x3b37d8){var _0x4b40a5=_0x572999;_0x3b37d8?this[_0x4b40a5(0x1ee)]():(this[_0x4b40a5(0x220)]&&(this[_0x4b40a5(0x1c5)][_0x4b40a5(0x1c9)]=this['windData']),this[_0x4b40a5(0x227)]());}},{'key':'_mountedHook','value':function _0x306545(){}},{'key':'_addedHook','value':function _0x4b8bba(){var _0x3c9ab3=_0x572999;this[_0x3c9ab3(0x257)]=this[_0x3c9ab3(0x27d)](),this[_0x3c9ab3(0x23c)]=this[_0x3c9ab3(0x257)][_0x3c9ab3(0x208)]('2d'),this[_0x3c9ab3(0x2eb)](),this[_0x3c9ab3(0x1c5)]['data']&&this['setData'](this[_0x3c9ab3(0x1c5)][_0x3c9ab3(0x1c9)]);}},{'key':'_removedHook','value':function _0x291e5d(){var _0x378620=_0x572999;this[_0x378620(0x1d6)](),this[_0x378620(0x2d2)](),this[_0x378620(0x257)]&&(this[_0x378620(0x307)]['container'][_0x378620(0x320)](this[_0x378620(0x257)]),delete this[_0x378620(0x257)]);}},{'key':_0x572999(0x27d),'value':function _0x18a072(){var _0x59aa24=_0x572999,_0x5deffd=window[_0x59aa24(0x2c2)][_0x59aa24(0x1e8)]('canvas');_0x5deffd[_0x59aa24(0x306)][_0x59aa24(0x219)]=_0x59aa24(0x2e8),_0x5deffd[_0x59aa24(0x306)][_0x59aa24(0x2b7)]=_0x59aa24(0x2e5),_0x5deffd[_0x59aa24(0x306)][_0x59aa24(0x1a9)]=_0x59aa24(0x2e5),_0x5deffd[_0x59aa24(0x306)][_0x59aa24(0x29a)]=_0x59aa24(0x2a3),_0x5deffd[_0x59aa24(0x306)]['height']=_0x59aa24(0x2a3),_0x5deffd[_0x59aa24(0x306)][_0x59aa24(0x2f8)]=this[_0x59aa24(0x199)]?_0x59aa24(0x2db):'none',_0x5deffd['style'][_0x59aa24(0x2e7)]=0xa,_0x5deffd[_0x59aa24(0x2c7)]('id',_0x59aa24(0x1b8)),_0x5deffd[_0x59aa24(0x2c7)](_0x59aa24(0x20b),_0x59aa24(0x1b8)),this['_map'][_0x59aa24(0x1b7)]['appendChild'](_0x5deffd);var _0x57c456=this['_map'][_0x59aa24(0x24b)];return _0x5deffd[_0x59aa24(0x29a)]=_0x57c456[_0x59aa24(0x257)]['clientWidth'],_0x5deffd['height']=_0x57c456[_0x59aa24(0x257)]['clientHeight'],_0x5deffd;}},{'key':_0x572999(0x1ae),'value':function _0x247a93(){var _0x2cb9d7=_0x572999;this[_0x2cb9d7(0x257)]&&(this[_0x2cb9d7(0x257)][_0x2cb9d7(0x29a)]=this[_0x2cb9d7(0x1fb)],this['canvas'][_0x2cb9d7(0x201)]=this[_0x2cb9d7(0x2ab)]);}},{'key':'bindEvent','value':function _0x1500c8(){var _0xfff239=_0x572999,_0x785da4=this,_0x126207=Date['now']();(function _0x44a58c(){var _0x4df820=_0x1102;_0x785da4[_0x4df820(0x240)]=window[_0x4df820(0x1de)](_0x44a58c);var _0x387bb7=Date['now'](),_0xdc408c=_0x387bb7-_0x126207;_0xdc408c>_0x785da4['frameTime']&&(_0x126207=_0x387bb7-_0xdc408c%_0x785da4[_0x4df820(0x1f2)],_0x785da4['update']());}(),window[_0xfff239(0x1ed)]('resize',this[_0xfff239(0x1ae)][_0xfff239(0x288)](this),![]),this[_0xfff239(0x20a)]=![],this['mouse_move']=![],this['_map']['on'](mars3d__namespace[_0xfff239(0x319)][_0xfff239(0x26e)],this[_0xfff239(0x30c)],this),this[_0xfff239(0x307)]['on'](mars3d__namespace[_0xfff239(0x319)][_0xfff239(0x285)],this[_0xfff239(0x272)],this),this[_0xfff239(0x307)]['on'](mars3d__namespace[_0xfff239(0x319)][_0xfff239(0x2b9)],this['_onMouseUpEvent'],this));}},{'key':'unbindEvent','value':function _0x36a505(){var _0x699797=_0x572999;window[_0x699797(0x2fc)](this[_0x699797(0x240)]),delete this[_0x699797(0x240)],window['removeEventListener'](_0x699797(0x1ae),this[_0x699797(0x1ae)]),this[_0x699797(0x307)]['off'](mars3d__namespace[_0x699797(0x319)]['wheel'],this[_0x699797(0x30c)],this),this[_0x699797(0x307)][_0x699797(0x277)](mars3d__namespace[_0x699797(0x319)][_0x699797(0x285)],this[_0x699797(0x272)],this),this[_0x699797(0x307)][_0x699797(0x277)](mars3d__namespace['EventType'][_0x699797(0x2b9)],this[_0x699797(0x1f3)],this),this[_0x699797(0x307)][_0x699797(0x277)](mars3d__namespace[_0x699797(0x319)][_0x699797(0x1a6)],this[_0x699797(0x27b)],this);}},{'key':'_onMapWhellEvent','value':function _0x3c66f3(_0x5b15ef){var _0x3fc3b1=_0x572999,_0x1f17a6=this;clearTimeout(this[_0x3fc3b1(0x1e5)]);if(!this[_0x3fc3b1(0x25e)]||!this[_0x3fc3b1(0x257)])return;this['canvas'][_0x3fc3b1(0x306)][_0x3fc3b1(0x210)]='hidden',this['refreshTimer']=setTimeout(function(){var _0x56efc4=_0x3fc3b1;if(!_0x1f17a6['show'])return;_0x1f17a6[_0x56efc4(0x1dd)](),_0x1f17a6[_0x56efc4(0x257)][_0x56efc4(0x306)][_0x56efc4(0x210)]=_0x56efc4(0x1a7);},0xc8);}},{'key':_0x572999(0x272),'value':function _0x26075f(_0x2eef3a){var _0x19f069=_0x572999;this[_0x19f069(0x20a)]=!![],this[_0x19f069(0x307)][_0x19f069(0x277)](mars3d__namespace[_0x19f069(0x319)][_0x19f069(0x1a6)],this[_0x19f069(0x27b)],this),this['_map']['on'](mars3d__namespace['EventType'][_0x19f069(0x1a6)],this['_onMouseMoveEvent'],this);}},{'key':_0x572999(0x27b),'value':function _0x1ed4e6(_0x1fc7b7){var _0x4ae538=_0x572999;if(!this[_0x4ae538(0x25e)]||!this[_0x4ae538(0x257)])return;this[_0x4ae538(0x20a)]&&(this['canvas']['style'][_0x4ae538(0x210)]=_0x4ae538(0x1dc),this[_0x4ae538(0x1ac)]=!![]);}},{'key':'_onMouseUpEvent','value':function _0x13030c(_0x2d5852){var _0x2d2bef=_0x572999;if(!this[_0x2d2bef(0x25e)]||!this[_0x2d2bef(0x257)])return;this['_map'][_0x2d2bef(0x277)](mars3d__namespace['EventType']['mouseMove'],this[_0x2d2bef(0x27b)],this),this[_0x2d2bef(0x20a)]&&this[_0x2d2bef(0x1ac)]&&this[_0x2d2bef(0x1dd)](),this[_0x2d2bef(0x257)]['style']['visibility']=_0x2d2bef(0x1a7),this[_0x2d2bef(0x20a)]=![],this[_0x2d2bef(0x1ac)]=![];}},{'key':_0x572999(0x1dd),'value':function _0x408690(){var _0x339707=_0x572999;if(!this[_0x339707(0x25e)]||!this[_0x339707(0x2e0)])return;this[_0x339707(0x1a2)]=[],this[_0x339707(0x1c1)]();}},{'key':_0x572999(0x1ea),'value':function _0x13ec5c(_0x1ed210){var _0x465d18=_0x572999;this['clear'](),this[_0x465d18(0x220)]=_0x1ed210,this[_0x465d18(0x2e0)]=new CanvasWindField(this['windData'],this['reverseY']),this[_0x465d18(0x1c1)]();}},{'key':_0x572999(0x1c1),'value':function _0x18a214(){var _0x2269cb=_0x572999;this[_0x2269cb(0x241)]();for(var _0xb01afc=0x0;_0xb01afc<this[_0x2269cb(0x1b6)];_0xb01afc++){var _0x50b134=this[_0x2269cb(0x2b8)](new CanvasParticle());this[_0x2269cb(0x1a2)][_0x2269cb(0x19b)](_0x50b134);}this[_0x2269cb(0x23c)][_0x2269cb(0x25c)]=_0x2269cb(0x1aa),this[_0x2269cb(0x23c)][_0x2269cb(0x1be)]=0.6,this['update']();}},{'key':_0x572999(0x241),'value':function _0x40b5b1(){var _0x234299=_0x572999;if(!this[_0x234299(0x2e0)])return;this['calc_speedRate']=[(this[_0x234299(0x2e0)]['xmax']-this[_0x234299(0x2e0)][_0x234299(0x313)])/this[_0x234299(0x1bd)],(this['windField'][_0x234299(0x2ae)]-this[_0x234299(0x2e0)]['ymin'])/this[_0x234299(0x1bd)]];}},{'key':_0x572999(0x206),'value':function _0x44e506(){var _0xb56995=_0x572999,_0x1cc822=this;if(!this[_0xb56995(0x25e)]||this[_0xb56995(0x1a2)][_0xb56995(0x2e1)]<=0x0)return;var _0x368389=null,_0x251482=null,_0x659d31=null;this['particles'][_0xb56995(0x30d)](function(_0x54133a){var _0x1f82a8=_0xb56995;_0x54133a['age']<=0x0&&_0x1cc822[_0x1f82a8(0x2b8)](_0x54133a);if(_0x54133a[_0x1f82a8(0x21c)]>0x0){var _0x12d15f=_0x54133a['tlng'],_0x316bb5=_0x54133a[_0x1f82a8(0x2b6)];_0x659d31=_0x1cc822['windField'][_0x1f82a8(0x234)](_0x12d15f,_0x316bb5),_0x659d31?(_0x368389=_0x12d15f+_0x1cc822[_0x1f82a8(0x22c)][0x0]*_0x659d31[0x0],_0x251482=_0x316bb5+_0x1cc822[_0x1f82a8(0x22c)][0x1]*_0x659d31[0x1],_0x54133a['lng']=_0x12d15f,_0x54133a[_0x1f82a8(0x2c8)]=_0x316bb5,_0x54133a[_0x1f82a8(0x2d1)]=_0x368389,_0x54133a[_0x1f82a8(0x2b6)]=_0x251482,_0x54133a[_0x1f82a8(0x21c)]--):_0x54133a[_0x1f82a8(0x21c)]=0x0;}}),this[_0xb56995(0x24d)]();}},{'key':_0x572999(0x222),'value':function _0x5a44ad(_0x38efdb,_0xbb2192,_0x2231f8){var _0x548041=_0x572999,_0x51b6ef=Cesium['Cartesian3'][_0x548041(0x2a9)](_0x38efdb,_0xbb2192,this[_0x548041(0x2d9)]),_0x55957e=this['_map'][_0x548041(0x24b)];if(_0x55957e['mode']===Cesium[_0x548041(0x311)][_0x548041(0x212)]){var _0x165b21=new Cesium[(_0x548041(0x1f4))](_0x55957e[_0x548041(0x2bf)][_0x548041(0x254)],_0x55957e[_0x548041(0x1eb)][_0x548041(0x31c)]),_0x10de73=_0x165b21['isPointVisible'](_0x51b6ef);if(!_0x10de73)return _0x2231f8[_0x548041(0x21c)]=0x0,null;}var _0x302fe0=Cesium[_0x548041(0x317)][_0x548041(0x1d7)](this[_0x548041(0x307)][_0x548041(0x24b)],_0x51b6ef);return _0x302fe0?[_0x302fe0['x'],_0x302fe0['y']]:null;}},{'key':_0x572999(0x24d),'value':function _0xe0a5fa(){var _0x2a871b=_0x572999,_0x1f338e=this,_0x40a98e=this[_0x2a871b(0x1a2)];this[_0x2a871b(0x23c)][_0x2a871b(0x22f)]=_0x1f338e[_0x2a871b(0x22f)],this[_0x2a871b(0x23c)][_0x2a871b(0x2c0)]=_0x2a871b(0x29d),this[_0x2a871b(0x23c)][_0x2a871b(0x2c6)](0x0,0x0,this[_0x2a871b(0x1fb)],this['canvasHeight']),this[_0x2a871b(0x23c)][_0x2a871b(0x2c0)]=_0x2a871b(0x1ec),this[_0x2a871b(0x23c)][_0x2a871b(0x1be)]=0.9,this[_0x2a871b(0x23c)][_0x2a871b(0x214)](),this[_0x2a871b(0x23c)][_0x2a871b(0x235)]=this['color'];var _0x2be6c3=this[_0x2a871b(0x307)]['scene'][_0x2a871b(0x2d7)]!==Cesium[_0x2a871b(0x311)]['SCENE3D'];_0x40a98e[_0x2a871b(0x30d)](function(_0x1a2e7c){var _0x5912f0=_0x2a871b,_0x562e50=_0x1f338e[_0x5912f0(0x222)](_0x1a2e7c[_0x5912f0(0x256)],_0x1a2e7c[_0x5912f0(0x2c8)],_0x1a2e7c),_0x31aef3=_0x1f338e[_0x5912f0(0x222)](_0x1a2e7c['tlng'],_0x1a2e7c['tlat'],_0x1a2e7c);if(_0x562e50!=null&&_0x31aef3!=null){var _0x6811e9=Math[_0x5912f0(0x1ad)](_0x562e50[0x0]-_0x31aef3[0x0]);if(_0x2be6c3&&_0x6811e9>=_0x1f338e[_0x5912f0(0x1fb)]);else _0x1f338e[_0x5912f0(0x23c)][_0x5912f0(0x2e3)](_0x562e50[0x0],_0x562e50[0x1]),_0x1f338e[_0x5912f0(0x23c)]['lineTo'](_0x31aef3[0x0],_0x31aef3[0x1]);}}),this['canvasContext']['stroke']();}},{'key':_0x572999(0x2b8),'value':function _0x2af686(_0x1bb82a){var _0x536904=_0x572999,_0xfeb7e0,_0x43024a;for(var _0x583dbf=0x0;_0x583dbf<0x1e;_0x583dbf++){_0xfeb7e0=this['windField'][_0x536904(0x1fd)](),_0x43024a=this[_0x536904(0x2e0)]['getUVByPoint'](_0xfeb7e0[_0x536904(0x256)],_0xfeb7e0[_0x536904(0x2c8)]);if(_0x43024a&&_0x43024a[0x2]>0x0)break;}if(!_0x43024a)return _0x1bb82a;var _0x53b544=_0xfeb7e0[_0x536904(0x256)]+this[_0x536904(0x22c)][0x0]*_0x43024a[0x0],_0x15b002=_0xfeb7e0[_0x536904(0x2c8)]+this[_0x536904(0x22c)][0x1]*_0x43024a[0x1];return _0x1bb82a[_0x536904(0x256)]=_0xfeb7e0[_0x536904(0x256)],_0x1bb82a[_0x536904(0x2c8)]=_0xfeb7e0[_0x536904(0x2c8)],_0x1bb82a[_0x536904(0x2d1)]=_0x53b544,_0x1bb82a['tlat']=_0x15b002,_0x1bb82a['age']=Math[_0x536904(0x312)](Math[_0x536904(0x292)]()*this[_0x536904(0x1a0)]),_0x1bb82a;}},{'key':'clear','value':function _0x4a1025(){var _0x127f3b=_0x572999;this['particles']=[],delete this[_0x127f3b(0x2e0)],delete this[_0x127f3b(0x220)];}}]),_0x111a6a;}(BaseLayer);mars3d__namespace['LayerUtil'][_0xee8b26(0x1c0)](_0xee8b26(0x1e6),CanvasWindLayer),mars3d__namespace[_0xee8b26(0x21f)][_0xee8b26(0x23a)]=CanvasWindLayer,mars3d__namespace['WindUtil']=WindUtil,exports['CanvasWindLayer']=CanvasWindLayer,exports[_0xee8b26(0x1af)]=WindLayer,exports[_0xee8b26(0x302)]=WindUtil,Object['defineProperty'](exports,_0xee8b26(0x2a0),{'value':!![]});
}));
