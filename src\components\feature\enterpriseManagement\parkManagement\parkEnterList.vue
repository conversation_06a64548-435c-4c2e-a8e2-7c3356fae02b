
<template>
  <!-- 在园企业 -->
  <div class="parkEnterList">
    <!-- {{parkId}} -->
    <div class="header">
      <!-- <div class="title">企业列表</div> -->
      <div class="operation">
        <div class="inputBox">
          <el-select
            v-model="enterTypeList"
            size="small"
            placeholder="请选择企业类型"
            :clearable="true"
          >
            <el-option
              v-for="item in entType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="auditStatusList"
            size="small"
            placeholder="请选择审核状态"
            :clearable="true"
            style="width: 180px"
          >
            <el-option
              v-for="item in auditStatusListSelect"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="enterName"
            size="small"
            placeholder="请输入单位名称"
            class="input"
            clearable
          ></el-input>
          <el-button type="primary" size="small" @click="searches()"
            >查询</el-button
          >
        </div>
        <CA-button
          type="primary"
          size="small"
          class="export"
          plain
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData.records"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        @select="select"
        @select-all="select"
      >
        <!-- <el-table-column
          type="selection"
          width="55"
          fixed="left"
          align="center"
        >
        </el-table-column> -->
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="enterpName"
          label="单位名称"
          width="340"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span
              @click="goEnt(scope.row)"
              style="color: rgb(57, 119, 234);cursor: pointer;"
              class="enterpName"
            >
              {{ scope.row.enterpName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="districtName"
          label="行政区划"
          align="center"
          width="180"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="enterpriseTypeName"
          label="企业类型"
          width="180"
          align="center"
        >
        </el-table-column>

        <el-table-column
          prop="level"
          label="企业等级"
          width="180"
          align="center"
        >
         <template slot-scope="scope">
            <span v-if="scope.row.level == 1">一级</span>
            <span v-else-if="scope.row.level == 2">二级</span>
            <span v-else-if="scope.row.level == 3">三级</span>
            <span v-else-if="scope.row.level == 4">四级</span>
            <span v-else></span>
          </template>
        </el-table-column>

        <el-table-column
          prop="respper"
          label="企业负责人"
          align="center"
          min-width="180"
        >
        </el-table-column>
        <el-table-column
          prop="respTel"
          label="负责人手机"
          align="center"
          min-width="180"
        >
        </el-table-column>

        <el-table-column
          prop="auditStatusName"
          label="审核状态"
          align="center"
          min-width="180"
        >
        </el-table-column>

        <!-- <el-table-column
          prop="address"
          label="操作"
          align="center"        
          fixed="right"
        >
          <template slot-scope="scope">
            <div class="icon_box">             
                  
              <div
                v-if="scope.row.level == null"
                @click="handleClick(scope.row.enterpId, 'basicInformation')"
                type="text"
                size="mini"
              >
                <div class="icon">                
                  <span style="color: #3977ea">查看</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column> -->
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="tableData.size"
        layout="total, prev, pager, next"
        background
        :total="tableData.total"
      >
      </el-pagination>
    </div>
    <!-- <DialogCheckDetails
      :enterpId="enterpId"
      :show="dialogCheckDetailsShow"
      @closeBoolean="closeBoolean"
      @getParentList="search"
      ref="dialogCheckDetails"
    />
    <DialogInformationCheck
      :enterpId="enterpId"
      :show="dialogInformationCheckShow"
      @closeBoolean="closeBoolean"
      ref="dialogInformationCheck"
    /> -->
  </div>
</template>

<script>
import { getEnterpriseList, enterExportExcel } from "@/api/entList";
import {
  parkQueryPageList,
  parkQueryEnterPageList,
} from "@/api/riskAssessment";
import { getEnt } from "@/api/dailySafety";
// import DialogCheckDetails from "./dialogCheckDetails.vue";
// import DialogInformationCheck from "./dialogInformationCheck.vue";

import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  name: "parkEnterList",
  props:['parkId'],
  components: {
    // DialogCheckDetails,
    // DialogInformationCheck,
  },
  data() {
    return {
      currentPage: 1,
      enterpId: "",
      entType: [
        {
          label: "生产",
          value: "01",
        },
        {
          label: "经营",
          value: "02",
        },
        {
          label: "使用",
          value: "03",
        },
        {
          label: "第一类非药品类易制毒",
          value: "04",
        },
      ],
      enterTypeList: "",
      level: [
        {
          label: "一级",
          value: "1",
        },
        {
          label: "二级",
          value: "2",
        },
        {
          label: "三级",
          value: "3",
        },
        {
          label: "四级",
          value: "4",
        },
      ],
      levelVal: ["1", "2", "3", "4"],
      auditStatusListSelect: [
        {
          label: "审核通过",
          value: "1",
        },
        {
          label: "审核不通过",
          value: "2",
        },
        {
          label: "待审核",
          value: "3",
        },
      ],
      auditStatusList: "",
      input: "",
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      value: "",
      tableData: null,
      districtLoading: false,
      enterName: "",
      loading: false,
      selection: [],
      tabName: "",
      entLevel: "",
      dialogCheckDetailsShow: false,
      dialogInformationCheckShow: false,
    };
  },
  //方法集合
  methods: {
    setTag() {
      const data = {
        enterName: this.enterName,
        districtVal: this.districtVal,
        levelVal: this.levelVal,
        enterTypeList: this.enterTypeList,
        auditStatusList: this.auditStatusList,
      };
      // console.log(data);
      this.$store.commit("controler/updateEntListControler", data);
    },
    closeBoolean(data) {
      this[data.name] = data.boolean;
    },
    goEnt(val) {
      this.$router.push({ name: "entManagement",query:{id:val.enterpId,showDetail:true} });
      // this.$store.commit("controler/updateEntId", row.enterpId);  //
     // 跳转到详情页面
    },
    // 导出
    exportExcel() {
      enterExportExcel({
        enterTypeList: this.enterTypeList?[this.enterTypeList]:[],  //请选择企业类型  报错
        auditStatusList: this.auditStatusList?[this.auditStatusList]:[], //审核状态  报错
        enterName: this.enterName,  //单位名称      
        parkId:this.parkId,   
        size:1000,
        current: this.currentPage,  
        // ids: this.selection,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].enterpId;
      }
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.search();
    },
    //获取列表数据
    getData() {
      // if (this.$store.state.login.user.user_type === "ent") {
      //   getEnt({}).then((res) => {
      //     if (res.data.code == 0) {
      //       let bool = true;
      //       this.$emit("entBool", bool);
      //       let id = res.data.data.enterpId;
      //       this.$emit("entId", id);
      //       //点击
      //       if (this.entModelName) {
      //         this.tabName = this.entModelName;
      //       } else if (res.data.data.level) {
      //         this.tabName = "videoInspection";
      //       } else {
      //         this.tabName = "basicInformation";
      //       }
      //       this.$emit("goTag", id, this.tabName);
      //     }
      //   });
      // } else {
      this.currentPage = 1;
      this.search();
      // }
    },
    handleClick(enterpId, name) {
      this.$emit("goTag", enterpId, name);
    },
    handleClickCheck(enterpId, name) {
      this[name] = true;
      this.enterpId = enterpId;
      if (name === "dialogInformationCheckShow") {
        this.$nextTick(() => {
          this.$refs.dialogInformationCheck.getData();
        });
      }
    },
    //查询
    searches() {
      this.currentPage = 1;
      // const data = {
      //   enterName: this.enterName,
      //   distCodeList: this.districtVal,
      // };
      // this.$store.commit("controler/updateEntListControler", data);
      this.search();
    },
    search() {
      this.loading = true;
      parkQueryEnterPageList({       
        "enterTypeList": this.enterTypeList?[this.enterTypeList]:[],  //请选择企业类型  报错
        "auditStatusList": this.auditStatusList?[this.auditStatusList]:[], //审核状态  报错
        "enterName": this.enterName,  //单位名称
        // "levelList": [],
        "parkId":this.parkId,
         // "distCodeList": [],
        // "enterIdList": [],
        current: this.currentPage,
        size: 10,
      }).then((res) => {       
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
  },
  mounted() {
    // this.getSessionStorage();
    // this.getData();
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      entModelName: (state) => state.entModelName,
      entListControler: (state) => state.entListControler,
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    $attrs: {
      handler(newVal, oldVal) {
        console.log(newVal);
      },
    },
    entModelName: {
      handler(newVal, oldVal) {
        this.entModelName = this.tabName;
      },
      deep: true,
      // immediate: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.parkEnterList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
        }
      }
    }
    .export {
      // margin-right: 20px;
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
