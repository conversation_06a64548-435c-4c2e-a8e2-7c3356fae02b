<template>
  <div class="historyList">
    <el-dialog
      title="企业风险数据填报"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="1350px"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="topName">
        {{ topData.enterpName }}
      </div>
      <div class="topBox">
        <div class="topStatus">
          <div class="isYes">
            已完成：<span>{{ topData.submitNum }}</span>
          </div>
          <div class="isNo">
            未完成：<span>{{ topData.notSubmitNum }}</span>
          </div>
        </div>

        <div class="progressBox">
          <span>填报率：</span>
          <el-progress :percentage="percentageNum"></el-progress>
        </div>
      </div>

      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData.list"
        style="width: 100%"
        border
        @select="select"
        @select-all="select"
      >
        <!-- <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column> -->
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="enterpName"
          label="单位名称"
          width="250"
          align="center"
          :show-overflow-tooltip="true"
        >
          <!-- <template slot-scope="{ row, column, $index, store }">
            <span @click="goEnt(row)" style="color: #3977ea; cursor: pointer">{{
              row.enterpName
            }}</span>
          </template> -->
        </el-table-column>
        <el-table-column
          prop="districtName"
          label="行政区划"
          width="150"
          align="center"
          :show-overflow-tooltip="true"
        >
          <!-- <template slot-scope="{ row, column, $index, store }">           
            <span>{{ row.districtName }}</span>
          </template> -->
        </el-table-column>
        <el-table-column label="重大危险源名称" align="center" :show-overflow-tooltip="true" prop="dangerName">
          <!-- <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.dangerName }}</span>
          </template> -->
        </el-table-column>
        <el-table-column label="单元类型" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.riskTypeName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="填报状态" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.submitStatus }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span @click="goEnt(row)" style="color: #3977ea; cursor: pointer"
              >详情</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { statisticsListEnterpStatus } from "@/api/accidentManagement";
import { getDistrictUser } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {},
  name: "AlarmDetailsList",
  data() {
    return {
      show: false,
      currentPage: 1,
      enterpName: "",
      loading: true,
      tableData: [],
      total: 0,
      selection: [],
      topData: {},
      percentageNum: 0,
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.currentPage = 1;
      this.majorHazardLevel = "";
      // console.log(this.majorHazardLevel);
      this.show = val;
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("login/updataActiveName", "riskDynamics");
      this.$store.commit("controler/updateEntId", row.enterpId);
      this.$store.commit("controler/updateEntModelName", "riskDynamics");
    },

    getEntData(row) {
      this.topData = row;
      this.percentageNum = Number(row.submitRate.split("%")[0]);
      // this.distCode = distCode;
      this.loading = true;
      statisticsListEnterpStatus({
        cimRiskUnitId: "",
        enterpId: row.enterpId,
        riskType: "",
        nowPage: this.currentPage,
        pageSize: 10,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data;
          this.total = res.data.data.total;
        }
      });
    },
    exportExcel() {
      // let list = [...this.selection];
      console.log(this.selection);
      alarmEnterpExport({
        districtCode: this.distCode,
        startTime: this.startDate,
        // distCodeList: this.selection.length<=1?null:this.selection,
        endTime: this.endDate,
        // isContainParent: true,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "报警统计分析-报警企业数列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    // 排序
    changeTableSort(column) {
      var fieldName = column.prop;
      var sortingType = column.order;
      var data = [...this.tableData];
      var shiftTotal = [];
      if (data[0].areaCode == this.$store.state.login.userDistCode) {
        shiftTotal = data.shift();
      } else if ((this.areaCode = data[0].areaCode)) {
        shiftTotal = data.shift();
      }
      //按照降序排序
      if (sortingType == "descending") {
        data = data.sort((a, b) => b[fieldName] - a[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        data = data.sort((a, b) => a[fieldName] - b[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      } else {
        this.getSafetyList();
      }
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getEntData(this.topData);
    },

    search() {
      this.currentPage = 1;
      this.getEntData(this.topData);
    },
    clearDis() {
      this.distCode = "";
    },
    clearMa() {
      this.majorHazardLevel = "";
    },
    clearEntName() {
      this.enterpName = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.topName {
  text-align: center;
  font-size: 18px;
}
.topBox {
  display: flex;
  margin: 10px 0;
  justify-content: space-between;
  .topStatus {
    display: flex;
    > div {
      margin: 0 20px 0 0;
      font-size: 15px;
      padding: 0 0 0 20px;
    }
    .isYes {
      position: relative;
      span {
        color: blue;
        font-size: 20px;
      }
    }
    .isYes::before {
      position: absolute;
      content: "";
      width: 15px;
      height: 15px;
      background: blue;
      top: 0;
      left: 0;
      margin-top: 8px;
    }
    .isNo {
      position: relative;
      span {
        color: red;
        font-size: 20px;
      }
    }
    .isNo::before {
      position: absolute;
      content: "";
      width: 15px;
      height: 15px;
      background: red;
      top: 0;
      left: 0;
      margin-top: 8px;
    }
  }
  .progressBox {
    display: flex;
    align-items: center;
    span {
      font-size: 15px;
    }
    .el-progress {
      width: 300px;
    }
  }
}
.historyList {
  .seach-part {
    font-weight: 600;
    // padding: 0 10px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 500px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      > .btn {
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>