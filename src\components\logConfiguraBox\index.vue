<template>
  <div class="logConfiguraBox">
    <!-- {{$route.name}} -->
    <component
      :is="$route.name == 'logConfiguraBox' ? 'logManagement' : $route.name"
    ></component>
  </div>
</template>

<script>
import logManagement from "./logManagement/index.vue";
import logUserManagement from "./logUserManagement/index.vue";
export default {
  //import引入的组件
  components: {
   logManagement,
   logUserManagement
  },
  data() {
    return {
      componentStr: "",
    };
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
.structure {
  margin-top: 15px;
}
</style>