/* To avoid CSS expressions while still supporting IE 7 and IE 6, use this script */
/* The script tag referencing this file must be placed before the ending body tag. */

/* Use conditional comments in order to target IE 7 and older:
	<!--[if lt IE 8]><!-->
	<script src="ie7/ie7.js"></script>
	<!--<![endif]-->
*/

(function() {
	function addIcon(el, entity) {
		var html = el.innerHTML;
		el.innerHTML = '<span style="font-family: \'quanhui\'">' + entity + '</span>' + html;
	}
	var icons = {
		'qh-gaojing1': '&#xe900;',
		'qh-gaojing2': '&#xe901;',
		'qh-gaojing': '&#xe902;',
		'qh-qiehuan': '&#xe903;',
		'qh-fast_forward': '&#xe01f;',
		'qh-fast_rewind': '&#xe020;',
		'qh-widgets': '&#xe1bd;',
		'qh-center_focus_strong2': '&#xe3b5;',
		'qh-person_pin': '&#xe55a;',
		'qh-my_location2': '&#xe55d;',
		'qh-pin_drop': '&#xe55e;',
		'qh-power': '&#xe60b;',
		'qh-disposalReport': '&#xe60e;',
		'qh-affiliates': '&#xe61f;',
		'qh-sort-pic': '&#xe641;',
		'qh-lock_outline2': '&#xe89b;',
		'qh-open_in_new': '&#xe89e;',
		'qh-settings_backup_restore': '&#xe8ba;',
		'qh-zhedie': '&#xe904;',
		'qh-dingwei': '&#xe905;',
		'qh-weixian': '&#xe906;',
		'qh-xiaofangshuan': '&#xe907;',
		'qh-qiye': '&#xe908;',
		'qh-cangku': '&#xe909;',
		'qh-chuguan': '&#xe90a;',
		'qh-chuguan1': '&#xe90b;',
		'qh-chuguan2': '&#xe90c;',
		'qh-dianhua': '&#xe90d;',
		'qh-duanxin': '&#xe90e;',
		'qh-guanxian': '&#xe90f;',
		'qh-jiuyuandui': '&#xe910;',
		'qh-7': '&#xe911;',
		'qh-weixianyuan': '&#xe912;',
		'qh-zhuangbei': '&#xe913;',
		'qh-zhuangbei1': '&#xe914;',
		'qh-zhuanjia': '&#xe915;',
		'qh-zhuanjia1': '&#xe916;',
		'qh-airplan': '&#xe917;',
		'qh-casePlan': '&#xe918;',
		'qh-address': '&#xe919;',
		'qh-grade': '&#xe91a;',
		'qh-name': '&#xe91b;',
		'qh-workPlan': '&#xe91c;',
		'qh-type': '&#xe91d;',
		'qh-time': '&#xe91e;',
		'qh-allFax': '&#xe91f;',
		'qh-project': '&#xe920;',
		'qh-author': '&#xe921;',
		'qh-dutyPeople': '&#xe922;',
		'qh-arrowTop': '&#xe923;',
		'qh-report': '&#xe924;',
		'qh-contacts': '&#xe925;',
		'qh-minusPic': '&#xe926;',
		'qh-addPic': '&#xe927;',
		'qh-gas': '&#xe928;',
		'qh-picture': '&#xe929;',
		'qh-search': '&#xe92a;',
		'qh-smallMessage': '&#xe92b;',
		'qh-garden': '&#xe92c;',
		'qh-coal': '&#xe92d;',
		'qh-dangerous': '&#xe92e;',
		'qh-danger': '&#xe92f;',
		'qh-cross': '&#xe930;',
		'qh-eyeOpen': '&#xe931;',
		'qh-eyeClose': '&#xe932;',
		'qh-dataCount': '&#xe933;',
		'qh-dataManage': '&#xe934;',
		'qh-dataControl': '&#xe935;',
		'qh-structure': '&#xe936;',
		'qh-dutyManage': '&#xe937;',
		'qh-grid2': '&#xe938;',
		'qh-study': '&#xe939;',
		'qh-notice': '&#xe93a;',
		'qh-dataShare': '&#xe93b;',
		'qh-support': '&#xe93c;',
		'qh-draft': '&#xe93d;',
		'qh-collection': '&#xe93e;',
		'qh-icon6': '&#xe93f;',
		'qh-administrativeDivision': '&#xe940;',
		'qh-listEdit': '&#xe941;',
		'qh-rescueUnit': '&#xe942;',
		'qh-home': '&#xe943;',
		'qh-equip': '&#xe944;',
		'qh-icon8': '&#xe945;',
		'qh-home1': '&#xe946;',
		'qh-manage': '&#xe947;',
		'qh-zhishengji': '&#xe948;',
		'qh-infoReport': '&#xe949;',
		'qh-noCoal': '&#xe94a;',
		'qh-keyCounty': '&#xe94b;',
		'qh-laws': '&#xe94c;',
		'qh-layer1': '&#xe94d;',
		'qh-browse': '&#xe94e;',
		'qh-leader': '&#xe94f;',
		'qh-knowledge': '&#xe950;',
		'qh-exercise': '&#xe951;',
		'qh-rescuePlay': '&#xe952;',
		'qh-document': '&#xe953;',
		'qh-firm': '&#xe954;',
		'qh-allList': '&#xe955;',
		'qh-talk': '&#xe956;',
		'qh-respondInfo': '&#xe957;',
		'qh-commandCar': '&#xe958;',
		'qh-apperSite': '&#xe959;',
		'qh-contacts21': '&#xe95a;',
		'qh-picture2': '&#xe95b;',
		'qh-sceneInfo': '&#xe95c;',
		'qh-study2': '&#xe95d;',
		'qh-administrativeDivision2': '&#xe95e;',
		'qh-user322': '&#xe95f;',
		'qh-oilField': '&#xe960;',
		'qh-opinion': '&#xe961;',
		'qh-process': '&#xe962;',
		'qh-professor': '&#xe963;',
		'qh-property': '&#xe964;',
		'qh-asset': '&#xe965;',
		'qh-reporter': '&#xe966;',
		'qh-getLetter': '&#xe967;',
		'qh-getBin': '&#xe968;',
		'qh-reportInfo': '&#xe969;',
		'qh-reportUnit': '&#xe96a;',
		'qh-rescueTeam': '&#xe96b;',
		'qh-resource': '&#xe96c;',
		'qh-fileManage': '&#xe96d;',
		'qh-situation2': '&#xe96e;',
		'qh-emResponse': '&#xe96f;',
		'qh-serviceManagement': '&#xe970;',
		'qh-sendLetter': '&#xe971;',
		'qh-sendBin': '&#xe972;',
		'qh-train2': '&#xe973;',
		'qh-plan': '&#xe974;',
		'qh-safeTrend': '&#xe975;',
		'qh-searchPic': '&#xe976;',
		'qh-sendShort': '&#xe977;',
		'qh-contacts2': '&#xe978;',
		'qh-traffic': '&#xe979;',
		'qh-sentFax': '&#xe97a;',
		'qh-authManage': '&#xe97b;',
		'qh-serManage': '&#xe97c;',
		'qh-building': '&#xe97d;',
		'qh-serSource': '&#xe97e;',
		'qh-storeBox': '&#xe97f;',
		'qh-filePic': '&#xe980;',
		'qh-staffGauge': '&#xe981;',
		'qh-sendTrash': '&#xe982;',
		'qh-getTrash': '&#xe983;',
		'qh-draftMail': '&#xe984;',
		'qh-sendMail': '&#xe985;',
		'qh-getMail': '&#xe986;',
		'qh-infoSearch22': '&#xe987;',
		'qh-sendMessage': '&#xe988;',
		'qh-delete': '&#xe989;',
		'qh-warn': '&#xe98a;',
		'qh-trafficPic': '&#xe98b;',
		'qh-pencil3': '&#xe98c;',
		'qh-keyArea': '&#xe98d;',
		'qh-agentMan': '&#xe98e;',
		'qh-accessory': '&#xe98f;',
		'qh-infoBrief': '&#xe990;',
		'qh-cause': '&#xe991;',
		'qh-matter': '&#xe992;',
		'qh-webCam': '&#xe993;',
		'qh-tel': '&#xe994;',
		'qh-capacity': '&#xe995;',
		'qh-highTrain': '&#xe996;',
		'qh-video': '&#xe997;',
		'qh-modelSet': '&#xe998;',
		'qh-event1': '&#xe999;',
		'qh-title': '&#xe99a;',
		'qh-injuries': '&#xe99b;',
		'qh-workCase': '&#xe99c;',
		'qh-caseInfo': '&#xe99d;',
		'qh-taskPlan': '&#xe99e;',
		'qh-loginUser': '&#xe99f;',
		'qh-yjManage': '&#xe9a0;',
		'qh-exit': '&#xe9a1;',
		'qh-lock': '&#xe9a2;',
		'qh-building1': '&#xe9a3;',
		'qh-screen': '&#xe9a4;',
		'qh-talk1': '&#xe9a5;',
		'qh-facsimile': '&#xe9a6;',
		'qh-weater': '&#xe9a7;',
		'qh-help': '&#xe9a8;',
		'qh-user': '&#xe9a9;',
		'qh-buildingPic': '&#xe9aa;',
		'qh-mobile': '&#xe9ab;',
		'qh-bookOpen': '&#xe9ac;',
		'qh-balance': '&#xe9ad;',
		'qh-hammer': '&#xe9ae;',
		'qh-bookLine': '&#xe9af;',
		'qh-rollPaper': '&#xe9b0;',
		'qh-accCount': '&#xe9b1;',
		'qh-switchPic': '&#xe9b2;',
		'qh-ecoLoss': '&#xe9b3;',
		'qh-metal': '&#xe9b4;',
		'qh-feedBack': '&#xe9b5;',
		'qh-medicine': '&#xe9b6;',
		'qh-comPic': '&#xe9b7;',
		'qh-tradePic': '&#xe9b8;',
		'qh-fireBuid': '&#xe9b9;',
		'qh-danSource': '&#xe9ba;',
		'qh-oilSite': '&#xe9bb;',
		'qh-oilLine': '&#xe9bc;',
		'qh-cancel': '&#xe9bd;',
		'qh-release': '&#xe9be;',
		'qh-folderPic': '&#xe9bf;',
		'qh-team': '&#xe9c0;',
		'qh-agencyPic': '&#xe9c1;',
		'qh-trainSite': '&#xe9c2;',
		'qh-specialImg': '&#xe9c3;',
		'qh-specialPic': '&#xe9c4;',
		'qh-modify': '&#xe9c5;',
		'qh-rimSet': '&#xe9c6;',
		'qh-thingSet': '&#xe9c7;',
		'qh-deploy': '&#xe9c8;',
		'qh-starGrade': '&#xe9c9;',
		'qh-plot': '&#xe9ca;',
		'qh-arrowLeft': '&#xe9cb;',
		'qh-pullTop': '&#xe9cc;',
		'qh-measureGap': '&#xe9cd;',
		'qh-measure': '&#xe9ce;',
		'qh-linkAge': '&#xe9cf;',
		'qh-route': '&#xe9d0;',
		'qh-locate': '&#xe9d1;',
		'qh-makePic': '&#xe9d2;',
		'qh-fullMap': '&#xe9d3;',
		'qh-workBox': '&#xe9d4;',
		'qh-measureArea': '&#xe9d5;',
		'qh-visual': '&#xe9d6;',
		'qh-colliery': '&#xe9d7;',
		'qh-trash': '&#xe9d8;',
		'qh-comcar': '&#xe9d9;',
		'qh-experts': '&#xe9da;',
		'qh-army': '&#xe9db;',
		'qh-legend': '&#xe9dc;',
		'qh-equipImg': '&#xe9dd;',
		'qh-textNote': '&#xe9de;',
		'qh-mapOut': '&#xe9df;',
		'qh-bigsource': '&#xe9e0;',
		'qh-fireflower': '&#xe9e1;',
		'qh-pullLeft': '&#xe9e2;',
		'qh-return': '&#xe9e3;',
		'qh-arrowRight': '&#xe9e4;',
		'qh-fullScreen': '&#xe9e5;',
		'qh-unfold': '&#xe9e6;',
		'qh-fold': '&#xe9e7;',
		'qh-lock2': '&#xe9e8;',
		'qh-unlock': '&#xe9e9;',
		'qh-reTop': '&#xe9ea;',
		'qh-reDown': '&#xe9eb;',
		'qh-arrowDown': '&#xe9ec;',
		'qh-shut': '&#xe9ed;',
		'qh-selected': '&#xe9ee;',
		'qh-unselected': '&#xe9ef;',
		'qh-play': '&#xe9f0;',
		'qh-path': '&#xe9f1;',
		'qh-tube': '&#xe9f2;',
		'qh-dutyGroup': '&#xe9f3;',
		'qh-tower': '&#xe9f4;',
		'qh-channel': '&#xe9f5;',
		'qh-monitor': '&#xe9f6;',
		'qh-popList': '&#xe9f7;',
		'qh-schoolPic': '&#xe9f8;',
		'qh-listPic': '&#xe9f9;',
		'qh-bridge': '&#xe9fa;',
		'qh-ganged': '&#xe9fb;',
		'qh-online': '&#xe9fc;',
		'qh-surround': '&#xe9fd;',
		'qh-analysis': '&#xe9fe;',
		'qh-location': '&#xe9ff;',
		'qh-surAnalyse': '&#xea00;',
		'qh-emerDuty': '&#xea01;',
		'qh-emerDuty1': '&#xea02;',
		'qh-resShare': '&#xea03;',
		'qh-synmeet': '&#xea04;',
		'qh-modAnalyse': '&#xea05;',
		'qh-accAnalyse': '&#xea06;',
		'qh-yuan': '&#xea07;',
		'qh-duobian': '&#xea08;',
		'qh-juxing': '&#xea09;',
		'qh-line': '&#xea0a;',
		'qh-dot': '&#xea0b;',
		'qh-text': '&#xea0c;',
		'qh-shrinkLeft': '&#xea0d;',
		'qh-shrinkRight': '&#xea0e;',
		'qh-tradeFirm': '&#xea0f;',
		'qh-mapImg': '&#xea10;',
		'qh-pullRight': '&#xea11;',
		'qh-pullDown': '&#xea12;',
		'qh-reRight': '&#xea13;',
		'qh-scene': '&#xea14;',
		'qh-sceneSet': '&#xea15;',
		'qh-measure2': '&#xea16;',
		'qh-fastSearch': '&#xea17;',
		'qh-bufSearch': '&#xea18;',
		'qh-claSearch': '&#xea19;',
		'qh-speSearch': '&#xea1a;',
		'qh-target': '&#xea1b;',
		'qh-square': '&#xea1c;',
		'qh-circle': '&#xea1d;',
		'qh-map': '&#xea1e;',
		'qh-track_changes': '&#xea1f;',
		'qh-phonePic': '&#xea20;',
		'qh-homePage': '&#xea21;',
		'qh-display': '&#xea22;',
		'qh-light': '&#xea23;',
		'qh-medical': '&#xea24;',
		'qh-cheer': '&#xea25;',
		'qh-payTarget': '&#xea26;',
		'qh-pwdPic': '&#xea27;',
		'qh-equipPro': '&#xea28;',
		'qh-exercise2': '&#xea29;',
		'qh-keyArea2': '&#xea2a;',
		'qh-emerAns': '&#xea2b;',
		'qh-mine': '&#xea2c;',
		'qh-emerRes': '&#xea2d;',
		'qh-rightPic': '&#xea2e;',
		'qh-infoPic': '&#xea2f;',
		'qh-knodge': '&#xea30;',
		'qh-disaster': '&#xea31;',
		'qh-comSys': '&#xea32;',
		'qh-officePic': '&#xea33;',
		'qh-phoneNote': '&#xea34;',
		'qh-faxPic': '&#xea35;',
		'qh-notePic': '&#xea36;',
		'qh-bigscreen': '&#xea37;',
		'0': 0
		},
		els = document.getElementsByTagName('*'),
		i, c, el;
	for (i = 0; ; i += 1) {
		el = els[i];
		if(!el) {
			break;
		}
		c = el.className;
		c = c.match(/qh-[^\s'"]+/);
		if (c && icons[c[0]]) {
			addIcon(el, icons[c[0]]);
		}
	}
}());
