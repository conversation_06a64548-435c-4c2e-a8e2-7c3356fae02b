<template>
  <div class="dialogCheckDetails">
    <el-dialog
      title="审核详情"
      :visible="show"
      @close="closeBoolean(false)"
      width="800px"
      v-dialog-drag
      :key="dialogKey"
      :close-on-click-modal="false"
    >
      <div class="container">
        <el-form label-position="left" label-width="80px">
          <el-form-item label="审核结果">
            <el-radio-group v-model="auditStatus">
              <el-radio label="1">审核通过</el-radio>
              <el-radio label="2">审核不通过</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="反馈详情">
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model.trim="feedback"
              maxlength="100"
              show-word-limit
              rows="7"
            >
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="footer">
        <el-button type="primary" @click="handleSubmit()">确定</el-button
        ><el-button @click="closeBoolean(false)">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { postEntCheck } from "@/api/entList";
export default {
  name: "dialogCheckDetails",
  data() {
    return {
      auditStatus: "1",
      feedback: "",
      dialogKey: "",
    };
  },
  props: ["enterpId", "show"],
  methods: {
    getParentList() {
      this.$emit("getParentList");
    },
    closeBoolean() {
      this.auditStatus = "1";
      this.feedback = "";
      this.$emit("closeBoolean", {
        boolean: false,
        name: "dialogCheckDetailsShow",
      });
      // 初始化弹窗位置
      this.dialogKey = new Date();
    },
    handleSubmit() {
      // console.log(this.auditStatus);
      if (this.auditStatus === "2" && this.feedback.length <= 0) {
        this.$message.error("请填写反馈详情");
      } else {
        let params = {
          enterpId: this.enterpId,
          auditStatus: this.auditStatus,
          feedback: this.feedback,
        };
        postEntCheck(params).then((res) => {
          if (res.data.code === 0) {
            this.closeBoolean(false);
            this.getParentList();
            this.$emit('refresh')
          }
        });
      }
    },
  },
};
</script>
<style lang="scss" scoped>
// .dialogCheckDetails {
.container {
  padding: 0 20px;
}
.footer {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-left: 50%;
  transform: translateX(-50%);
}
// }
</style>