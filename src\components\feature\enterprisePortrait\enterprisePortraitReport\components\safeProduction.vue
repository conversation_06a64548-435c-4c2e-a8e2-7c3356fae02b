<template>
  <div class="safe-production">
    <div class="title"><i></i>基础信息</div>
    <tableView :previewInfo="previewInfo"></tableView>
    <div class="title"><i></i>各层级安全生产管理</div>
    <produceGcjaqscgl
      :dataList="previewInfo.productionBasicDTO"
      :isShowButton="false"
      :companyName="previewInfo && previewInfo.enterpriseName"
    ></produceGcjaqscgl>
    <div class="title"><i></i>生产经营场所信息</div>
    <production :previewInfo="previewInfo"></production>
    <div class="title"><i></i>重点领域监管信息</div>
    <keyAreas :industryCategoryDTO="previewInfo.industryCategoryDTO"></keyAreas>
    <div class="title"><i></i>环保设施排查</div>
    <epfacilities :propsData="previewInfo.epfReportDTO"></epfacilities>
  </div>
</template>

<script>
import { reportDetail, changePreview } from "../mock";
import { ristReportOneCollect } from "../../common/api";
import tableView from "./safetyBaseInfo.vue";
import production from "./production.vue";
import epfacilities from "./epfacilitiesView.vue";
import keyAreas from "./keyAreas.vue";
import produceGcjaqscgl from "./produceGcjaqscgl.vue";
export default {
  name: "safeProduction",
  components: {
    tableView,
    production,
    epfacilities,
    keyAreas,
    produceGcjaqscgl,
  },
  data() {
    return {
      safeyInfo: reportDetail.data.safetyManageDTO,
      elmenuitemIndex: "1",
      previewInfo: {},
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      ristReportOneCollect().then((res) => {
        if (res.code === 200) {
          this.previewInfo = res.data;
        }
      });
      this.previewInfo = changePreview;
    },
    changeMenu(index) {
      this.elmenuitemIndex = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  color: #086dd4;
  font-size: 16px;
  height: 36px;
  display: flex;
  align-items: center;
  i {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #415ef0;
    border-radius: 3px;
    margin-right: 6px;
  }
}
.safe-production {
  width: 100%;
  height: 480px;
  overflow-y: auto;
}
</style>
