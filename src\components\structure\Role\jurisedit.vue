<template>
  <div class="table">
    <el-dialog
      title="权限配置"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :close-on-click-modal="false"
    >
      <a-tree
        v-model="checkedKeys"
        checkable
        :tree-data="treeData"
        @select="onSelect"
        @check="onCheck"
      />
      <div class="btnlist">
        <el-button type="primary" @click="allSelect"> 全选 </el-button>
        <el-button type="primary" @click="clean"> 清空 </el-button>
        <el-button type="primary" @click="save"> 保存 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMenuTreeDataed,
  getMenuTreeDataIded,
  getSaveRoleMenued,
} from "../../../api/role";
import { mapState } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  name: "JurisEdit",
  components: {},
  props: {
    dialogTableVisibleed: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      loading: false,
      checkedKeys: [],
      allIds: [],
      id: "",
    };
  },
  watch: {
    checkedKeys(val) {
      //   console.log('onCheck', val);
    },
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
      //   console.log(this.$store.state.sa.SARoleListData)
    },
    clean() {
      this.checkedKeys = [];
    },
    allSelect() {
      this.checkedKeys = this.allIds;
    },
    save() {
      getSaveRoleMenued({
        roleId: this.id,
        systemCode: this.$store.state.sa.SARoleListData.systemCode,
        privIds: this.checkedKeys.join(","),
      }).then((res) => {
        if (res.data.code == 0) {
          // this.submitting = false;
          this.$message({
            type: "success",
            message: "修改成功!",
          });
          this.tableVisible = false;
          this.$parent.fatherMethod();
        } else {
          this.$message({
            type: "info",
            message: res.data.msg,
          });
          return;
        }
      });
    },
    getData(systemCode, menuId) {
      this.loading = true;
      this.id = menuId;
      getMenuTreeDataIded({ systemCode: systemCode, id: menuId })
        .then((res) => {
          this.checkedKeys = res.data.data;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    getTreeData(systemCode) {
      getMenuTreeDataed({ systemCode: systemCode })
        .then((res) => {
          this.treeData = res.data.data;
          this.treeData.forEach((item) => {
            item.key = item.id;
            item.title = item.privName;
            this.allIds.push(item.key);
            if (item.children.length > 0) {
              item.children.forEach((items) => {
                items.key = items.id;
                items.title = items.privName;
              });
            }
          });
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    onExpand(expandedKeys) {
      //   console.log('onExpand', expandedKeys);
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onCheck(checkedKeys) {
      //   console.log('onCheck', checkedKeys);
      this.checkedKeys = checkedKeys;
    },
    onSelect(selectedKeys, info) {
      //   console.log('onSelect', info);
      this.selectedKeys = selectedKeys;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
.btnlist {
  text-align: center;
}
.el-dialog__body {
  padding-top: 0;
}
.ant-tree {
  height: 330px;
  overflow: auto;
}
</style>
