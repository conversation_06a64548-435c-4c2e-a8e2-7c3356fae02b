import axios from "axios";
import qs from "qs";

// 代码生成列表
export const getGenGenerator = data => {
  return axios({
    method: "get",
    url: "/gen/generator/page",
    params: data
  });
};
// 代码生成下啦选项
export const getDsconfList = data => {
  return axios({
    method: "get",
    url: "/gen/dsconf/list"
  });
};
// 代码生成下载选项
export const getGeneratorCode = table => {
    return axios({
      url: '/gen/generator/code',
      method: 'post',
      data:table,
      responseType: 'arraybuffer'
    }).then((response) => { // 处理返回的文件流
      const blob = new Blob([response.data], { type: 'application/zip' })
      const filename = table.tableName + '.zip'
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = filename
      document.body.appendChild(link)
      link.click()
      window.setTimeout(function () {
        URL.revokeObjectURL(blob)
        document.body.removeChild(link)
      }, 0)
    })
  }
// 数据源列表
export const getGenDsconf = data => {
    return axios({
      method: "get",
      url: "/gen/dsconf/page",
      params:data
    });
  };
  export const saveDsconf = data => {
    return axios({
      method: "PUT",
      url: "/gen/dsconf/",
      data:data
    });
  };
  export const deleteDsconf = data => {
    return axios({
      method: "DELETE",
      url: "/gen/dsconf/"+data,
      data:data
    });
  };