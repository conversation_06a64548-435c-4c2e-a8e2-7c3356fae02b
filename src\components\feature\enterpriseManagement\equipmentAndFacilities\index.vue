<template>
  <div class="enterprise-info-page">
    <div class="enterprise-info-page-content"
         :class="{ 'side-hidden': sideHidden }">
      <!-- 左侧列表 -->
      <el-card class="left-list"
               shadow="never"
               ref="leftBox">
        <div slot="header"
             class="clearfix title">
          <span>目录</span>
          <!--          <i-->
          <!--            class="select-btn"-->
          <!--            :class="sideHidden ? `el-icon-s-unfold` : `el-icon-s-fold`"-->
          <!--            @click="sideHidden = !sideHidden"-->
          <!--          ></i>-->
        </div>
        <div class="tab-list-btn">
          <!-- <el-scrollbar style="height: 100%; width: 100%"> -->
          <ul>
            <li v-for="(item, index) in routerList"
                :key="index"
                @click="selectLeftListBtn(item, index)"
                :class="{ active: selectRouterItem == item }"
                :title="item.name">
              <el-tooltip class="item"
                          effect="light"
                          :content="item.name"
                          placement="right">
                <!--                <i :class="item.icon"></i>-->
              </el-tooltip>
              <span>{{ item.name }}</span>
            </li>
            <a class="not-data"
               v-if="!routerList.length">开发中</a>
          </ul>
          <!-- </el-scrollbar> -->
        </div>
      </el-card>
      <!-- 右侧内容 -->
      <div class="right-page-list-box"
           id="equipmentAndFacilitiesBox">
        <StorageTank ref="storageTank"
                     class="equipmentAndFacilitiesDiv"></StorageTank>
        <Device ref="device"
                class="equipmentAndFacilitiesDiv"></Device>
        <LeakagePoint ref="leakagePoint"
                      class="equipmentAndFacilitiesDiv"></LeakagePoint>
        <Warehouse ref="warehouse"
                   class="equipmentAndFacilitiesDiv"></Warehouse>
      </div>
    </div>
  </div>
</template>
<script>
// import { getLot, lotExportExcel, getMonitoringMonvalue } from "@/api/entList";
// import { parseTime } from "@/utils/index";
import StorageTank from './storageTank'
import Device from './device'
import LeakagePoint from './leakagePoint'
import Warehouse from './warehouse'
export default {
  //import引入的组件
  name: 'equipmentAndFacilities',
  components: {
    StorageTank,
    Device,
    LeakagePoint,
    Warehouse
  },
  data() {
    return {
      sideHidden: false,
      selectRouterItem: {},
      scrollBox: '',
      routerList: [
        {
          name: '储罐', // 二级菜单名称
          icon: 'nav-icon-cg',
          pageType: 'compontent',
          componentsName: 'storageTank'
        },
        {
          name: '装置', // 二级菜单名称
          icon: 'nav-icon-csgd',
          pageType: 'compontent',
          componentsName: 'device'
        },
        {
          name: '泄漏点', // 二级菜单名称
          icon: 'nav-icon-zdhkzxtda',
          pageType: 'compontent',
          componentsName: 'leakagePoint'
        },
        {
          name: '仓库', // 二级菜单名称
          icon: 'nav-icon-ck',
          pageType: 'compontent',
          componentsName: 'warehouse'
        }
      ]
    }
  },
  //方法集合
  methods: {
    getData(id) {
      // console.log('111')
      this.$nextTick(() => {
        this.$refs.storageTank.getData(id)
        this.$refs.storageTank.getDangerIdIsNotNullListDataList(id)
        this.$refs.storageTank.getDangerIdIsNotNullListDataType()
        this.$refs.device.getData(id)
        this.$refs.device.getDangerIdIsNotNullListDataList(id)
        this.$refs.leakagePoint.getData(id)
        this.$refs.leakagePoint.getDangerIdIsNotNullListDataList(id)
        this.$refs.leakagePoint.getDangerIdIsNotNullListDataType()
        this.$refs.warehouse.getData(id)
        this.$refs.warehouse.getDangerIdIsNotNullListDataList(id)
        this.$refs.warehouse.getDangerIdIsNotNullListDataType();
        document.getElementById('equipmentAndFacilitiesBox').scrollTo({
          top:0
         })
        this.scroll();
         
      })
    },
    selectLeftListBtn(item, index) {
      this.selectRouterItem = item
      const jump = jQuery('.equipmentAndFacilitiesDiv').eq(index)
      let scrollTop = 0
      try {
        scrollTop = jump.position().top + this.scrollBox.scrollTop // 获取需要滚动的距离
      } catch (e) {
        scrollTop = jump.top + this.scrollBox.scrollTop // 获取需要滚动的距离
      }

      // Chrome
      this.scrollBox.scrollTo({
        top: scrollTop,
        behavior: 'smooth' // 平滑滚动
      })
    },
    scroll() {
      const that = this
      // 获取滚动dom元素
      this.scrollBox = document.getElementById('equipmentAndFacilitiesBox')
      const jump = jQuery('.equipmentAndFacilitiesDiv')
      const topArr = []
      for (let i = 0; i < jump.length; i++) {
        topArr.push( (jump.eq(i).position().top)  + jump.eq(i).height() )
      };
      // console.error(topArr,'TOPaRR')

      // 监听dom元素的scroll事件
      this.scrollBox.addEventListener(
        'scroll',
        () => {
          // debugger;
          const current_offset_top = that.scrollBox.scrollTop
          for (let i = 0; i < topArr.length; i++) {
            if (current_offset_top <= topArr[i]) {
              // 根据滚动距离判断应该滚动到第几个导航的位置
              that.selectRouterItem = that.routerList[i]
              break
            }
          }
        },
        true
      )
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.selectRouterItem = this.routerList[0]
  }
}
</script>
<style lang="scss" scoped>

.enterprise-info-page {
  // position: absolute;
  height: 100%;
  width: 100%;
  overflow: hidden;
  .not-data {
    display: block;
    height: 50px;
    line-height: 50px;
    width: 100%;
    text-align: center;
    transform: rotate(-15deg);
    margin-top: 50px;
    //font-size: 18px;
    color: #b1b1b1;
  }
  .enterprise-info-page-content {
    width: 100%;

    display: flex;
    justify-content: space-between;
    /deep/ .el-card__body {
      padding: 0;
    }
    &.side-hidden {
      .left-list {
        width: 60px;
        .title {
          padding: 0;
        }
        span {
          display: none;
        }
        .tab-list-btn {
          i {
            pointer-events: all;
          }
        }
      }
      .right-page-list-box {
        width: 70%;
      }
    }
    .left-list {
      width: 16%;
      height: calc(100% - 40px);

      overflow: hidden;
      /deep/ .el-card__header {
        padding: 0;
      }
      .title {
        height: 47px;
        background: #f5f5f6;
        line-height: 47px;
        overflow: hidden;
        color: #534d6a;
        font-size: 18px;
        font-weight: 700;
        padding: 0 60px 0 19px;
        position: relative;
        .select-btn {
          position: absolute;
          height: 40px;
          width: 40px;
          right: 10px;
          top: 50%;
          transform: translateY(-50%);
          text-align: center;
          line-height: 40px;
          font-size: 26px;
          cursor: pointer;
          &:hover {
            opacity: 0.8;
          }
        }
      }
      /deep/.el-card__body {
        height: calc(100%);
      }
      .tab-list-btn {
        list-style: none;
        width: 100%;
        li {
          width: 100%;
          height: 48px;
          line-height: 48px;
          font-size: 14px;
          padding-left: 20px;
          white-space: nowrap;
          overflow: hidden;
          position: relative;
          text-overflow: ellipsis;
          cursor: pointer;
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          -webkit-transition: 0.3s;
          transition: 0.3s;
          color: #534c6a;
          &:hover {
            background: rgba(0, 0, 0, 0.01);
            color: #4a7dff;
          }
          &.active {
            background: #d4e8ff;
            color: #4a7dff;
            i {
              &.nav-icon-zdhkzxtda {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-zdhkzxtda-active.png')
                  no-repeat center center;
              }
              &.nav-icon-csgd {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-csgd-active.png')
                  no-repeat center center;
              }
              &.nav-icon-cg {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-cg-active.png')
                  no-repeat center center;
              }
              &.nav-icon-ck {
                background-size: 50% 50%;
                background: url('/static/img/assets/img/navIcon/nav-icon-ck-active.png')
                  no-repeat center center;
              }
            }
          }
          i {
            width: 40px;
            height: 40px;
            position: absolute;
            top: 50%;
            line-height: 40px;
            text-align: center;
            transform: translateY(-50%);
            left: 1px;
            font-size: 18px;
            pointer-events: none;
            &.nav-icon-zdhkzxtda {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-zdhkzxtda.png')
                no-repeat center center;
            }
            &.nav-icon-csgd {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-csgd.png')
                no-repeat center center;
            }
            &.nav-icon-cg {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-cg.png')
                no-repeat center center;
            }
            &.nav-icon-ck {
              background-size: 50% 50%;
              background: url('/static/img/assets/img/navIcon/nav-icon-ck.png')
                no-repeat center center;
            }
          }
        }
      }
    }
    .right-page-list-box {
      transition: 0.3s;
      overflow: auto;
      width: 82%;
      height: calc(100vh - 270px);
      .content-box {
        position: relative;
        // margin-bottom: 3rem;
      }
    }
  }
  .el-menu-demo {
    /deep/ .el-menu-item {
      height: 42px;
      height: 42px;
      line-height: 42px;
    }
  }
}
</style>
<style>

</style>
