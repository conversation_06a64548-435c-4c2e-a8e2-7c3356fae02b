<template>
  <div>
    <el-dialog title="查看预案信息"
               :visible.sync="show"
               width="1200px"
               @close="closeBoolean()"
               top="10vh"
               :close-on-click-modal="false"
               v-dialog-drag>
      <div class="flex-full planTable">
        <!-- <div class="search_filter">
                <div class="toolbar_txt">
                <el-page-header title="" content="查看预案信息"></el-page-header>
                </div>
                <div class="toolbar_right">
                        <el-button @click="lookAll" icon="el-icon-arrow-left">查看全部章节</el-button>
                        <el-button @click="go('/planManagement/planList')" icon="el-icon-arrow-left">返回</el-button>
                    </div>
            </div> -->
        <div class="flex-full list-contain">
          <div class="flex-header">
            <span class="infoBtn">基本信息</span>
            <!-- <el-radio-group v-model="checkPage">
              <el-radio-button label="1">基本信息</el-radio-button>
              <el-radio-button label="2">预案内容</el-radio-button>
            </el-radio-group> -->
            <!-- <el-button @click="goback" icon="el-icon-arrow-left">返回</el-button> -->
          </div>
          <div class="flex-full"
               style="margin-top: 10px;max-height: 60vh;overflow: auto;"
               v-if="checkPage == '1'">

            <!-- <iams-form ref="simpleTable" v-model="formdata" :mapinfo="formdata.mapInfo" :gisshow="true"
                            :disabled="true"></iams-form> -->
            <div class="table_box">
              <el-scrollbar>
                <el-form label-position="right"
                         :model="listParams"
                         ref="ruleForm"
                         label-width="120px"
                         class="demo-ruleForm">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="planName"
                                    label="预案名称:">
                        <el-input v-model.trim="listParams.planName" disabled></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="planVersion"
                                    label="预案文号:">
                        <el-input v-model.trim="listParams.planVersion" disabled></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="planTypeCode"
                                    label="预案类型:">
                        <el-col :span="listParams.planTypeSuperCode!='65000'&&listParams.planTypeSuperCode!='69000'?12:24"
                                style="padding-right: 3px">
                          <el-select v-model="listParams.planTypeSuperCodeName" disabled>
                            <el-option v-for="options in planTypeSuperList"
                                       :label="options.label"
                                       :value="options.id"
                                       :key="options.id"></el-option>
                          </el-select>
                        </el-col>
                        <el-col :span="listParams.planTypeSuperCode!='65000'&&listParams.planTypeSuperCode!='69000'?12:0">
                          <el-select v-model="listParams.planTypeCodeName" disabled>
                            <el-option v-for="options in planTypeList"
                                       :label="options.label"
                                       :value="options.id"
                                       :key="options.id"></el-option>
                          </el-select>
                        </el-col>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="planLevel"
                                    label="预案级别:">
                        <el-select v-model="listParams.planLevel" disabled>
                          <el-option v-for="options in planLevelList"
                          :label="options.name"
                          :value="options.value"
                          :key="options.value"></el-option>
                        </el-select>
                        <!-- <el-input v-model.trim="listParams.planLevelName" disabled></el-input> -->
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="establishOrgCode"
                                    label="编制单位:">
                        <!--<iams-combobox :rootcheck="true" listtype="getOrgTree"
                                                    :defaultchecked="listParams.establishOrgCode" @changes="getEstablishInfo"
                                                    v-model="listParams.establishOrgCode">
                                                </iams-combobox>-->
                        <el-input v-model.trim="listParams.establishOrgName" disabled></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="publishOrgCode"
                                    label="发布单位:">
                        <!--<iams-combobox :rootcheck="true" listtype="getOrgTree"
                                                    :defaultchecked="listParams.publishOrgCode" v-model="listParams.publishOrgCode"
                                                    @changes="getPublishhInfo">
                                                </iams-combobox>-->
                        <el-input v-model.trim="listParams.publishOrgName" disabled></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="eventTypeCode"
                                    label="事件类型:">
                        <!--<iams-combobox :rootcheck="true" listtype="getEventTree"
                                                    :defaultchecked="listParams.eventTypeCode" v-model="listParams.eventTypeCode">
                                                </iams-combobox>-->
                        <el-cascader placeholder="请选择事件类型" disabled
                          v-model="listParams.eventTypeCode"
                          size="medium"
                          :options="eventTypeCodeData">
                        </el-cascader>
                        <!-- <el-input v-model.trim="listParams.eventTypeName" disabled></el-input> -->
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="classCode"
                                    label="预案密级:">
                        <!--<el-select v-model="listParams.classCode">
                                                    <el-option v-for="options in classCodeList" :label="options.name"
                                                        :value="options.id" :key="options.id"></el-option>

                                                </el-select>-->
                        <el-select v-model="listParams.classCode" disabled>
                          <el-option v-for="options in classCodeList"
                            :label="options.name"
                            :value="options.value"
                            :key="options.value"></el-option>
                        </el-select>
                        <!-- <el-input v-model.trim="listParams.classCodeName" disabled></el-input> -->
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="contactName"
                                    label="联系人:">
                        <el-input v-model.trim="listParams.contactName" disabled></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="contactPhone"
                                    label="联系电话:">
                        <el-input v-model.trim="listParams.contactPhone" disabled></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="publishTime"
                                    label="发布时间:">
                        <el-date-picker type='datetime' disabled
                                        v-model="listParams.publishTime"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="endTime"
                                    label="下次修订时间:">
                        <el-date-picker type='datetime' disabled
                                        v-model="listParams.endTime"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%"></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="涉及危化品:">
                        <el-select v-model.trim="listParams.msdsId"
                                   filterable disabled
                                   placeholder=""
                                   remote
                                   clearable
                                   @change="currentSel"
                                   reserve-keyword
                                   :remote-method="Tolikesearch">
                      
                           <el-option v-for="item in msdsTitleList"
                                     :key="item.msdsid"
                                     :label="item.msdstitle"
                                     :value="item.msdsid">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item prop="attachmentList"
                                    label="附件:">
                        <!-- <iams-upload ref="upload" v-model="listParams.attachmentList" :limit=5>
                                                </iams-upload> -->
                        <!-- <file-cell :filelist="listParams.attachmentList"></file-cell> -->
                        <AttachmentUpload :attachmentlist="listParams.attachmentList"
                                          :limit="1"
                                          type="office"
                                          v-bind="{}"
                                          :editabled="true"
                                          :disabled="true"></AttachmentUpload>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-scrollbar>
            </div>
          </div>
          <!--<div class="flex-full planContentTable" style="margin-top: 10px;" v-if="checkPage == '2'">-->
          <!--<div class="flex-full">-->
          <!--<div class="plan-btn">-->
          <!--<el-button @click="lookAll" icon="el-icon-arrow-left">查看全部章节</el-button>-->
          <!--&lt;!&ndash;<el-button @click="go('/planManagement/planList')" icon="el-icon-arrow-left">返回</el-button>&ndash;&gt;-->
          <!--</div>-->
          <!--<div style="border:1px solid #ccc;overflow: hidden;">-->
          <!--<el-row :class="temp.style.edit_plan">-->
          <!--&lt;!&ndash; 查看预案内容 &ndash;&gt;-->
          <!--<el-col :span="4" class="edit_plan" style="border-right: 1px solid #ccc;">-->
          <!--<el-scrollbar style="height: 613px!important;">-->
          <!--<el-steps direction="vertical" class="detailStep editStep" v-if="stepData.length != 0">-->
          <!--<el-step :description="item.nodeName" icon="el-icon-circle-plus-outline" class="detailStep"-->
          <!--v-for="item,index in stepData" :key="index"-->
          <!--@click.native="handleStepClick(item.nodeName, item.sort,item.nodeId,item.content)">-->
          <!--</el-step>-->
          <!--</el-steps>-->
          <!--<div v-if="stepData.length == 0" style="text-align: center;color: #666;">暂无数据</div>-->
          <!--</el-scrollbar>-->
          <!--</el-col>-->
          <!--<el-col :span="20" id="editPlan" class="edit_plan">-->
          <!--<el-scrollbar style="height: 72%!important;">-->
          <!--<div v-for="item in stepData" :class="temp.style.edit_plan_note" v-show="showAll" v-if="stepData.length != 0">-->
          <!--<h3>{{item.nodeName}}</h3>-->
          <!--<div v-html="item.content"></div>-->
          <!--</div>-->
          <!--<div :model="planContent" :class="temp.style.edit_plan_note" v-show="!showAll" v-if="stepData.length != 0">-->
          <!--<h3>{{planContent.nodeName}}</h3>-->
          <!--<div v-html="planContent.content"></div>-->
          <!--</div>-->
          <!--<div v-if="stepData.length == 0" style="position: relative;"><div :class="[temp.style.nodata]"></div></div>-->
          <!--</el-scrollbar>-->
          <!--</el-col>-->
          <!--</el-row>-->
          <!--</div>-->
          <!--</div>-->
          <!--</div>-->
          <div class="flex-full planContentTable"
               style="margin-top: 10px;max-height: 60vh;overflow: auto;"
               v-if="checkPage == '2'">
            <div class="flex-full">
              <div class="plan-btn"
                   style="float: right;margin-top: 10px;">
                <el-button @click="lookAll"
                           icon="el-icon-arrow-left">查看全部章节</el-button>
                <!--<el-button @click="go('/planManagement/planList')" icon="el-icon-arrow-left">返回</el-button>-->
              </div>
              <el-row class="edit_plan">
                <!-- 查看预案内容 -->
                <el-col :span="7"
                        class="edit_plan">

                  <!-- <el-steps direction="vertical" class="detailStep editStep">
                                        <el-step :description="item.nodeName" icon="el-icon-circle-plus-outline" class="detailStep"
                                            v-for="item,index in stepData" :key="index"
                                            @click.native="handleStepClick(item.nodeName, item.sort,item.nodeId,item.content)">
                                        </el-step>
                                    </el-steps> -->
                  <div class="chapterModule">
                    <div class="operaFun">
                      <el-input v-model.trim="chapterName"
                                class="chapterNodeInput"
                                placeholder="可用章节标题定位"
                                @keyup.enter.native="searchHighLight"
                                clearable
                                maxlength="10"
                                show-word-limit>
                        <el-button slot="append"
                                   @click="searchHighLight"
                                   icon="el-icon-search"></el-button>
                      </el-input>
                      <!-- <el-button @click="addTree(0)" size="mini" type="success" plain :class="temp.style.chapterModuleBtn" style="margin-left: 10px;">新增章节</el-button>
                                            <el-button @click="addTree(1)" size="mini" type="success" plain :class="temp.style.chapterModuleBtn">添加子章节</el-button>
                                            <el-button @click="editTree" size="mini" type="primary" plain :class="temp.style.chapterModuleBtn">编辑</el-button>
                                            <el-button @click="delectTree" size="mini" type="danger" plain :class="temp.style.chapterModuleBtn">删除</el-button>
                                            <el-button @click="sortupTree" size="mini" type="primary" plain :class="temp.style.chapterModuleBtn">上移</el-button>
                                            <el-button @click="sortdownTree" size="mini" type="primary" plain :class="temp.style.chapterModuleBtn">下移</el-button> -->
                    </div>
                    <el-scrollbar style="height: 585px!important;">
                      <el-tree :data="nodeList"
                               :expand-on-click-node="false"
                               :props="defaultProps"
                               :empty-text="emptyText"
                               render-after-expand
                               @node-click="handleNodeClick"
                               :filter-node-method="searchHighLight"
                               highlight-current
                               :default-expanded-keys="expanded"
                               node-key="id"
                               ref="chapterTree"
                               default-expand-all
                               class="chapterTree">
                        <span slot-scope="scope"
                              :class="scope.data.class">{{scope.node.label}}</span>
                      </el-tree>
                    </el-scrollbar>
                  </div>

                </el-col>
                <el-col :span="17"
                        id="editPlan"
                        class="edit_plan"
                        style="margin-top: 10px;padding-left: 20px;">
                  <el-scrollbar style="height: 69%!important;">
                    <!-- <div v-for="item in stepData" :class="temp.style.edit_plan_note" v-show="showAll">
                                            <h3>{{item.nodeName}}</h3>
                                            <div v-html="item.content"></div>
                                        </div> -->
                    <div v-for="(item,index) in nodeOneList"
                         :key="index"
                         class="edit_plan_note"
                         v-show="item.isShow">
                      <h2 v-if="item.level === 1">{{item.nodeName}}</h2>
                      <h3 v-else-if="item.level === 2">{{item.nodeName}}</h3>
                      <h4 v-else>{{item.nodeName}}</h4>
                      <div v-html="item.content"></div>
                    </div>
                    <!-- <div :model="planContent" :class="temp.style.edit_plan_note" v-show="!showAll">
                                            <h3>{{planContent.nodeName}}</h3>
                                            <div v-html="planContent.content"></div>
                                        </div> -->
                  </el-scrollbar>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { detailPlanData } from '@/api/mergencyResources'
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
import {
  getPlanType,
  getPlanList,
  getEstablishOrgCodeTreeData,
  getEventTypeCodData,
  getExpertTypeData,
  addPlanData,
  editPlanData
} from '@/api/mergencyResources'
import { getDangerIdIsNotNullListData } from '@/api/equipmentAndFacilities'
export default {
  //import引入的组件
  name: 'detailPlan',
  components: {
    AttachmentUpload
  },
  props: ['eventTypeCodeData'],
  data() {
    return {
      show: false,
      checkPage: '1',
      planId: '',
      stepData: [],
      showAll: true,
      planTypeSuperList: [],
      planTypeList: [],
      planLevelList: [],
      nowPage: '',
      classCodeList: [],
      msdsTitleList: [],
      planContent: {
        comments: '', //备用字段
        content: '', //预案内容
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        nodeId: '', //节点id
        nodeName: '', //节点名称
        planId: '', //预案id
        sort: 0, //排序
        updateBy: '', //更新人
        updateTime: '' //($date-time)更新时间
      },
      listParams: {
        attachmentList: [],
        checkStatus: '', //备案状态，建字典项（2不通过0未审核1审核通过）
        classCode: '', //预案密级，建字典项（CONFIDENTIAL、SECRET、LIMIT、OPEN、OTHE）
        comments: '', //备注
        contactName: '', //联系人
        contactPhone: '', //联系人电话
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        dutiesMeasures: '', //职责与措施
        endTime: '', //($date-time)适用结束时间
        establishOrgCode: '', //编制单位编码
        establishOrgName: '', //编制单位名称
        eventTypeCode: '', //事件类型
        notes: '', //编制或修订说明 （0,1）
        planCategory: '', //预案种类，预案大类GENERAL，SPECIAL，DEPARTMENT，COM
        planFlag: '', //预案标志（0：备案，1：预案）
        planId: '', //预案id
        planLevel: '', //预案级别，COUNTRY、PROVINCE、CITY、ARER、INDUSTRY、COM
        planName: '', //预案名称
        planResponseValue: '', //可选响应级别(1、2、3、4级)，给默认值4级
        planSubCategory: '', //预案子类，GENERAL，SPECIAL，DEPARTMENT，COM
        planTypeCode: '', //预案类型
        planTypeSuperCode: '', //父节点备案类型
        planVersion: '', //预案版本
        publishOrgCode: '', //发布单位编码
        publishOrgName: '', //发布单位名称
        publishTime: '', //($date-time)发布时间
        reportOrgCode: '', //上报单位编码
        reportOrgName: '', //上报单位名称
        startTime: '', //($date-time)适用开始时间
        updateBy: '', //更新人
        updateTime: '', //($date-time)更新时间
        versionFlag: '', //发布还是修订(0发布,1修订)
        msdsId: '',
        msdsTitle: ''
      },
      emptyText: '暂无数据',
      expanded: [1],
      nodeList: [],
      nodeOneList: [],
      efaultProps: {
        children: 'childrenList',
        label: 'nodeName'
      },
      parameter: {
        eventId: '',
        id: '',
        planId: '',
        planStatus: '',
        primaryFlag: '',
        resLevel: ''
      },
      chapterName: '',
      switchIsShow: true,
      currentChapterInfo: [],
      currentNodeStructureInfo: []
    }
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val
    },
    getEstablishInfo(item) {
      this.listParams.establishOrgName = item.label
    },
    getPublishhInfo(item) {
      this.listParams.publishOrgName = item.label
    },
    //获取select option选中的值
    currentSel(selVal) {
      this.listParams.msdsId = selVal
      this.msdsTitleList.forEach(item => {
        if (item.msdsId === selVal) {
          this.listParams.msdsTitle = item.msdsTitle
        }
      })
    },
    //搜索发起请求 传入值为当前select输入的值
    Tolikesearch(query) {
      getDangerIdIsNotNullListData({ msdstitle: query }).then(res => {
        if (res.data.status === 200) {
          console.log(res)
          this.msdsTitleList = res.data.data.list
        }
      })
    },
    //获取预案类型
    getPlanType() {
      // let parentCode="60000";
      // this.http.SelectNode.planTypeCode(parentCode).then(res => {
      //     if (res.status == 200) {
      //         // console.log(res);
      //         this.planTypeSuperList = res.data
      //     }
      // });
      getPlanType({}).then(res => {
        if (res.data.status == 200) {
          this.data = res.data.data.treeData
        }
        // this.datas = this.data;
        this.planTypeSuperList.forEach(item => {
          item.value = item.id
          if (item.children && item.children.length > 0) {
            item.children.forEach(items => {
              items.value = items.id
            })
          }
        })
      })
    },
    //获取预案级别
     getPlanLevel() {
      getExpertTypeData({ dicCode: 'PLAN_LEVEL' }).then(res => {
        if (res.data.status == 200) {
          this.planLevelList = res.data.data
        }
      })
    },
    //获取预案密级列表
    getclassCode() {
      getExpertTypeData({ dicCode: 'PLAN_CLASS_CODE' }).then(res => {
        if (res.data.status == 200) {
          this.classCodeList = res.data.data
        }
      })
    },
    //左边step
    stepList(parms) {
      this.planId = parms
      // this.http.PlanRequest.planNodeListGet(parms).then(res => {
      //     if (res.status == 200) {
      //         this.stepData = res.data;
      //     }
      // });
    },
    //选中step
    handleStepClick(name, sort, id, content) {
      this.showAll = false
      this.$set(this.planContent, 'sort', sort)
      this.$set(this.planContent, 'nodeId', id)
      this.$set(this.planContent, 'nodeName', name)
      this.$set(this.planContent, 'content', content)
    },
    searchHighLight() {
      this.getEmergencyTree()
    },
    //   获取预案详情
    planGetById(parms) {
      detailPlanData({
        planId: parms
      }).then(res => {
        if (res.data.status == 200) {
          this.listParams = res.data.data;
           this.Tolikesearch(this.listParams.msdsTitle)
        }
      })
    },
    // 节点列表查询
    getEmergencyTree() {
      const parms = this.parameter
      // this.http.PlanRequest.getEmergencyTree(parms).then(res => {
      //     if (res.status == 200) {

      //         this.currentChapterInfo = {};
      //         if (res.data.length > 0) {
      //         this.nodeOneList = [];
      //         for (let i = 0; i < res.data.length; i++) {
      //             const item = res.data[i];
      //             item.class = '';
      //             if (item.nodeName.indexOf(this.chapterName) === -1 && this.chapterName !== '') {
      //             item.isShow = false;
      //             } else {
      //             item.isShow = true;
      //             }
      //             item.level = 1;
      //             this.nodeOneList.push(item);
      //             if (item.childrenList.length > 0) {
      //             for (let j = 0; j < item.childrenList.length; j++) {
      //                 item.childrenList[j].class = '';
      //                 if (item.childrenList[j].nodeName.indexOf(this.chapterName) === -1 && this.chapterName !== '') {
      //                 item.childrenList[j].isShow = false;
      //                 } else {
      //                 item.childrenList[j].isShow = true;
      //                 }
      //                 item.childrenList[j].level = 2;
      //                 this.nodeOneList.push(item.childrenList[j]);
      //                 for (let k = 0; k < item.childrenList[j].childrenList.length; k++) {
      //                 item.childrenList[j].childrenList[k].class = '';
      //                 if (item.childrenList[j].childrenList[k].nodeName.indexOf(this.chapterName) === -1 && this.chapterName !== '') {
      //                     item.childrenList[j].childrenList[k].isShow = false;
      //                 } else {
      //                     item.childrenList[j].childrenList[k].isShow = true;
      //                 }
      //                 item.childrenList[j].childrenList[k].level = 3;
      //                 this.nodeOneList.push(item.childrenList[j].childrenList[k]);
      //                 }
      //             }
      //             }
      //         }
      //         }
      //         res.data.map((item, index) => {
      //         item.label = item.nodeName;
      //         item.id = index + 1;
      //         return item;
      //         });
      //         if (res.data.length > 0) {
      //         for (let i = 0; i < res.data.length; i++) {
      //             if (res.data[i].nodeName.indexOf(this.chapterName) !== -1 && this.chapterName !== '') {
      //             res.data[i].class = 'active';
      //             } else {
      //             res.data[i].class = '';
      //             }
      //             if (res.data[i].childrenList.length > 0) {
      //             for (let j = 0; j < res.data[i].childrenList.length; j++) {
      //                 if (res.data[i].childrenList[j].nodeName.indexOf(this.chapterName) !== -1 && this.chapterName !== '') {
      //                 res.data[i].childrenList[j].class = 'active';
      //                 } else {
      //                 res.data[i].childrenList[j].class = '';
      //                 }
      //                 for (let k = 0; k < res.data[i].childrenList[j].childrenList.length; k++) {
      //                 if (res.data[i].childrenList[j].childrenList[k].nodeName.indexOf(this.chapterName) !== -1 && this.chapterName !== '') {
      //                     res.data[i].childrenList[j].childrenList[k].class = 'active';
      //                 } else {
      //                     res.data[i].childrenList[j].childrenList[k].class = '';
      //                 }
      //                 }
      //             }
      //             }
      //         }
      //         }
      //         this.nodeList = res.data;
      //     } else {
      //         this.$message("保存失败");
      //     }
      // });
    },
    getEmergencyTreeClick() {
      // const parms = this.parameter;
      // this.http.PlanRequest.getEmergencyTree(parms).then(res => {
      // if (res.status == 200) {
      //     // this.currentChapterInfo = {};
      //     if (res.data.length > 0) {
      //     this.nodeOneList = [];
      //     for (let i = 0; i < res.data.length; i++) {
      //         const item = res.data[i];
      //         item.class = '';
      //         if (JSON.stringify(this.currentChapterInfo) !== '{}' && this.currentChapterInfo.nodeId === item.nodeId) {
      //         item.isShow = true;
      //         item.level = 1;
      //         this.nodeOneList.push(item);
      //         if (item.childrenList.length > 0) {
      //             for (let j = 0; j < item.childrenList.length; j++) {
      //             item.childrenList[j].class = '';
      //             item.childrenList[j].isShow = true;
      //             item.childrenList[j].level = 2;
      //             this.nodeOneList.push(item.childrenList[j]);
      //             if (item.childrenList[j].childrenList.length > 0) {
      //                 for (let k = 0; k < item.childrenList[j].childrenList.length; k++) {
      //                 item.childrenList[j].childrenList[k].class = '';
      //                 item.childrenList[j].childrenList[k].isShow = true;
      //                 item.childrenList[j].childrenList[k].level = 3;
      //                 this.nodeOneList.push(item.childrenList[j].childrenList[k]);
      //                 }
      //             }
      //             }
      //         }
      //         } else {
      //         item.isShow = false;
      //         item.level = 1;
      //         this.nodeOneList.push(item);
      //         if (item.childrenList.length > 0) {
      //             for (let j = 0; j < item.childrenList.length; j++) {
      //             item.childrenList[j].class = '';
      //             if (JSON.stringify(this.currentChapterInfo) !== '{}' && this.currentChapterInfo.nodeId === item.childrenList[j].nodeId) {
      //                 item.childrenList[j].isShow = true;
      //                 item.childrenList[j].level = 2;
      //                 this.nodeOneList.push(item.childrenList[j]);
      //                 if (item.childrenList[j].childrenList.length > 0) {
      //                 for (let k = 0; k < item.childrenList[j].childrenList.length; k++) {
      //                     item.childrenList[j].childrenList[k].class = '';
      //                     item.childrenList[j].childrenList[k].isShow = true;
      //                     item.childrenList[j].childrenList[k].level = 3;
      //                     this.nodeOneList.push(item.childrenList[j].childrenList[k]);
      //                 }
      //                 }
      //             } else {
      //                 item.childrenList[j].isShow = false;
      //                 item.childrenList[j].level = 2;
      //                 this.nodeOneList.push(item.childrenList[j]);
      //                 if (item.childrenList[j].childrenList.length > 0) {
      //                 for (let k = 0; k < item.childrenList[j].childrenList.length; k++) {
      //                     item.childrenList[j].childrenList[k].class = '';
      //                     if (JSON.stringify(this.currentChapterInfo) !== '{}' && this.currentChapterInfo.nodeId === item.childrenList[j].childrenList[k].nodeId) {
      //                     item.childrenList[j].childrenList[k].isShow = true;
      //                     item.childrenList[j].childrenList[k].level = 3;
      //                     this.nodeOneList.push(item.childrenList[j].childrenList[k]);
      //                     } else {
      //                     item.childrenList[j].childrenList[k].isShow = false;
      //                     item.childrenList[j].childrenList[k].level = 3;
      //                     this.nodeOneList.push(item.childrenList[j].childrenList[k]);
      //                     }
      //                 }
      //                 }
      //             }
      //             }
      //         }
      //         }
      //     }
      //     }
      //     res.data.map((item, index) => {
      //     item.label = item.nodeName;
      //     item.id = index + 1;
      //     return item;
      //     });
      //     if (res.data.length > 0) {
      //     for (let i = 0; i < res.data.length; i++) {
      //         if (res.data[i].nodeName.indexOf(this.chapterName) !== -1 && this.chapterName !== '') {
      //         res.data[i].class = 'active';
      //         } else {
      //         res.data[i].class = '';
      //         }
      //         if (res.data[i].childrenList.length > 0) {
      //         for (let j = 0; j < res.data[i].childrenList.length; j++) {
      //             if (res.data[i].childrenList[j].nodeName.indexOf(this.chapterName) !== -1 && this.chapterName !== '') {
      //             res.data[i].childrenList[j].class = 'active';
      //             } else {
      //             res.data[i].childrenList[j].class = '';
      //             }
      //             for (let k = 0; k < res.data[i].childrenList[j].childrenList.length; k++) {
      //             if (res.data[i].childrenList[j].childrenList[k].nodeName.indexOf(this.chapterName) !== -1 && this.chapterName !== '') {
      //                 res.data[i].childrenList[j].childrenList[k].class = 'active';
      //             } else {
      //                 res.data[i].childrenList[j].childrenList[k].class = '';
      //             }
      //             }
      //         }
      //         }
      //     }
      //     }
      //     this.nodeList = res.data;
      // } else {
      //     this.$message("保存失败");
      // }
      // });
    },
    // 点击当前章节信息
    handleNodeClick(data, structureInfo) {
      this.currentNodeStructureInfo = structureInfo
      this.currentChapterInfo = data

      // this.showAll = false;
      // this.$set(this.planContent, "sort", data.sort);
      // this.$set(this.planContent, "nodeId", data.nodeId);
      // this.$set(this.planContent, "nodeName", data.nodeName);
      // this.$set(this.planContent, "content", data.content);
      this.getEmergencyTreeClick()
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.getPlanType()
    this.getPlanLevel()
    this.getclassCode()
  }
}
</script>
<style lang="scss" scoped>
 /deep/.el-input__suffix {
        right: 17px;
      }
.infoBtn{
color: #FFF;
    background-color: #409EFF;
    border-color: #409EFF;
    -webkit-box-shadow: -1px 0 0 0 #409eff;
    box-shadow: -1px 0 0 0 #409eff;    
    text-align: center;
    margin: 0;
    position: relative;    
    padding: 12px 20px;
    font-size: 14px;
    border-radius: 4px;
}
.edit_plan {
  position: relative;
}

.edit_plan_note {
  margin-bottom: 2rem;

  > h3 {
    height: 3rem;
    line-height: 3rem;
  }
  > h2 {
    height: 3rem;
    line-height: 3rem;
  }
  > h4 {
    height: 3rem;
    line-height: 3rem;
  }

  > div {
    line-height: 1.5rem;
  }
}
.table_box {
  height: 95%;
  form {
    padding-right: 1rem;
  }
}
.recordCon {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  height: 200px;
  overflow-y: auto;
  background-color: #f5f7fa;
  cursor: no-drop;
  ul {
    display: flex;
    flex-wrap: wrap;
    li {
      display: inline;
      background-color: #f2f2f2;
      padding: 0px 10px;
      margin: 5px;
      border-radius: 4px;
      font-size: 12px;
      opacity: 0.8;
      .recordConName {
        margin-right: 10px;
      }
      .close {
        cursor: pointer;
      }
      span {
        padding: 0;
      }
    }
  }
}
.filingList {
  display: flex;
  .filingListMain {
    width: calc(100% - 400px);
    .filingListMainScrollbar {
      height: 500px !important;
    }
    .search_input {
      width: 300px;
      right: 18rem;
      .el-input-group__append {
        padding: 0.5rem 1rem;
        margin-left: 0px;
      }
    }
    .btn {
      border-top: 1px solid #e4e4e4;
      padding-top: 20px;
      text-align: center;
      margin-top: 10px;
    }
  }
}
.chapterModule {
  border: 1px solid #e4e4e4;
  margin-top: 10px;
  .operaFun {
    border-bottom: 1px solid #e4e4e4;
    .chapterNodeInput {
      padding: 10px 15px;
      background-color: #f2f2f2;
    }
    .chapterModuleBtn {
      margin-right: -5px;
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
}
.btn {
  text-align: center;
}
.nodata {
  width: 6rem;
  position: absolute;
  top: 12rem;
  height: 6rem;
  // background-image: url(../../../assets/image/nodata.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>