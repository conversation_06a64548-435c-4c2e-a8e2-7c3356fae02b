<template>
    <el-dialog :title="title" :visible.sync="newAddVisible" append-to-body top="10vh" width="1000px"
        :close-on-click-modal="false" @close="closeDialog">
        <div class="newAddVisible" v-loading="dialogLoading">
            <div class="box">
                <el-form label-position="right" label-width="120px" :model="form" ref="form">
                    <!-- <el-form-item label="证书附件：" prop="attachmentList">
                        <div>
                            <AttachmentUpload :attachmentlist="form.attachmentList" :limit="1" type="img" v-bind="{}"
                                :editabled="disabled"></AttachmentUpload>
                        </div>
                    </el-form-item> -->
                    <el-form-item :rules="[
                        { required: true, message: '请选择证书类型', trigger: 'blur' },
                    ]" label="证书类型：" prop="type">
                        <!-- <el-select placeholder="请选择证书类型" :disabled="disabled" style="width: 100%" v-model="form.type"
                            filterable :loading="iconLoading">
                            <el-option v-for="item in options" :key="item.id" :label="item.label" :value="item.id">
                            </el-option>
                        </el-select> -->
                        <el-input placeholder="请输入证书类型" :disabled="disabled" :title="form.zslx" type="text"
                        maxlength="50" v-model.trim="form.zslx"></el-input>
                    </el-form-item>
                    <el-form-item :rules="[
                        { required: true, message: '请输入证书编号', trigger: 'blur' },
                    ]" label="证书编号：" prop="zsbh">
                        <el-input placeholder="请输入证书编号" :disabled="disabled" :title="form.zsbh" type="text"
                            maxlength="50" v-model.trim="form.zsbh"></el-input>
                    </el-form-item>
                    <el-form-item label="企业名称：" prop="scyjdwmc">
                        <el-input disabled type="text" placeholder="请输入企业名称"
                        v-model.trim="form.scyjdwmc"></el-input>
                    </el-form-item>
                    <el-form-item label="主要负责人：" prop="maillistPersonId">
                        <el-input disabled type="text" placeholder="请输入主要负责人"
                            v-model.trim="form.maillistPersonId"></el-input>
                    </el-form-item>
                    <el-form-item :rules="[
                        { required: true, message: '请输入企业地址', trigger: 'blur' },
                    ]" label="企业地址：" prop="zcdz">
                        <el-input placeholder="请输入企业地址" :disabled="disabled" maxlength="100"
                            :title="form.zcdz" type="text" v-model.trim="form.zcdz"></el-input>
                    </el-form-item>
                    <el-form-item :rules="[
                        { required: true, message: '请输入登记品种', trigger: 'blur' },
                    ]" label="登记品种：" prop="djpz">
                        <el-input placeholder="请输入登记品种" :disabled="disabled" maxlength="100"
                            :title="form.djpz" type="text" v-model.trim="form.djpz"></el-input>
                    </el-form-item>
                    
                    <!-- <el-form-item :rules="[
                        { required: true, message: '请输入经济类型', trigger: 'blur' },
                    ]" label="经济类型：" prop="economicType">
                        <el-cascader placeholder="请输入经济类型" style="width: 300px" :props="{ value: 'id' }"
                            v-model="form.economicType" :disabled="disabled" size="medium" :options="economicTypeData"
                            @change="handleChangeEventTypeCode">
                        </el-cascader>
                    </el-form-item> -->
                    <el-form-item :rules="[
                        {
                            required: true,
                            message: '请选择证书生效时间',
                            trigger: 'blur',
                        },
                    ]" label="证件生效时间：" prop="zsyxqzi">
                        <el-date-picker placeholder="请选择证件生效时间" :disabled="disabled" style="width: 100%" type="date"
                            v-model="form.zsyxqzi" value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item :rules="[
                        {
                            required: true,
                            message: '请选择证件失效时间',
                            trigger: 'blur',
                        },
                    ]" label="证件失效时间：" prop="zsyxqzhi">
                        <el-date-picker placeholder="请选择证件失效时间" style="width: 100%" type="date" :disabled="disabled"
                            v-model="form.zsyxqzhi" value-format="yyyy-MM-dd">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item :rules="[
                        { required: true, message: '请输入发证机关', trigger: 'blur' },
                    ]" label="发证机关：" prop="issueOrg">
                        <el-input type="text" :disabled="disabled" maxlength="30" placeholder="请输入发证机关"
                            v-model.trim="form.issueOrg"></el-input>
                    </el-form-item>
                    <el-form-item :rules="[
                        { required: true, message: '请输入许可范围', trigger: 'blur' },
                    ]" label="许可范围：" prop="permitScope">
                        <el-input :rows="2" type="textarea" maxlength="300" :disabled="disabled" placeholder="请输入许可范围"
                            v-model.trim="form.permitScope"></el-input>
                    </el-form-item>
                </el-form>
            </div>
        </div>
        <div slot="footer" style="display: flex; justify-content: center" v-if="!disabled">
            <el-button type="primary" @click="submit(form.id)" :loading="btnLoading">确定</el-button>
            <el-button @click="closeDialog()">取消</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { licenseId } from "@/api/riskAssessment";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import {
    getCompanyCertType,
    getCompanyCertSave,
    getCompanyCertFindOne,
    getCompanyCertUpdate,
    getEconomicType,
} from "@/api/BasicDataManagement";
export default {
    components: { AttachmentUpload },
    props: {
        orgCode: {
            type: String,
            default: "",
        },
        newAddVisible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "",
        },
        id: {
            type: String,
            default: "",
        },
    },

    data() {
        return {
            dialogLoading: false,
            disabled: false,
            btnLoading: false,
            dialogLoading: false,
            iconLoading: false,
            options: [],
            economicTypeData: [],
            enListOption: [],
            form: {
                address: "", //地址/住所
                zcdz: "", //生产地址
                attachmentList: [],
                businessType: "", //业务类型
                zsbh: "", //证件编号
                certName: "", //证件名称
                companyType: "", //单位类型
                economicType: "", //经济类型/经济方式
                zsyxqzhi: "", //证件有效期(结束)
                id: "", //主键
                issueDate: "", //发证日期
                issueOrg: "", //发证机构
                maillistPersonId: "", //负责人/法定代表人
                orgCode: "", //企业机构
                permitScope: "", //许可范围
                standardLevel: "", //安全生产标准化等级
                zsyxqzi: "", //证件有效期(开始)
                type: "", //证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-工业产品生产许可证 5-安全生产标准化证书 6-非药品生产证件 7-非药品经营证件 8-易制爆备案证明)
            },
        }
    },
    created() {
        this.openDialog(this.title, this.id)
    },
    methods: {
        closeDialog() {
            this.$emit("close");
        },
        openDialog(title, id) {
            // this.title = title;
            switch (title) {
                case "新增安全生产许可证":
                    this.disabled = false;
                    this.getOption();
                    // this.form.maillistPersonId = this.enterData.respper;
                    // this.form.scyjdwmc = this.user.org_name;
                    this.form.orgCode = this.orgCode;
                    break;
                case "安全生产许可证详情":
                    this.disabled = true;
                    this.getOption();
                    this.getCompanyCertFindOneFun(id);
                    break;
                case "编辑安全生产许可证":
                    this.disabled = false;
                    this.getOption();
                    this.getCompanyCertFindOneFun(id);
                    break;
                default:
                    break;
            }
        },
        submit(id) {
            this.$refs["form"].validate((valid) => {
                if (valid) {
                    this.form.issueDate = this.form.zsyxqzi;
                    this.form.address = this.form.zcdz;
                    this.btnLoading = true;
                    if (id) {
                        getCompanyCertUpdate({
                            ...this.form,
                            economicType: this.form.economicType.join(","),
                        }).then((res) => {
                            this.btnLoading = false;
                            if (res.data.status === 200) {
                                this.$message.success(res.data.msg);
                                this.getData();
                                this.closeDialog();
                            } else {
                                this.$message.error(res.data.msg);
                            }
                        });
                    } else {
                        getCompanyCertSave({
                            ...this.form,
                            economicType: this.form.economicType.join(","),
                        }).then((res) => {
                            this.btnLoading = false;
                            if (res.data.status === 200) {
                                this.$message.success(res.data.msg);
                                this.$emit('submit');
                            } else {
                                this.$message.error(res.data.msg);
                            }
                        });
                    }
                } else {
                    return false;
                }
            });
        },
        // 获取经济类型
        handleChangeEventTypeCode(value) {
            if (value.length > 0) {
                this.form.economicType = value;
            } else {
                this.form.economicType = "";
            }
        },
        // 获取详情
        getCompanyCertFindOneFun(id) {
            this.dialogLoading = true;
            licenseId({ id: id }).then((res) => {
                this.dialogLoading = false;
                this.form = res.data.data;
                return
                let parentId = "";
                this.economicTypeData.forEach((el) => {
                    el.children.forEach((ele) => {
                        if (ele.id == res.data.data.economicType) {
                            parentId = ele.parentId;
                        }
                    });
                });
                // this.$set(this.form,'economicType',res.data.data.economicType.split(','));
                this.$set(this.form, "economicType", [
                    parentId,
                    res.data.data.economicType,
                ]);
                // this.form.orgCode = this.orgCode;
                // this.form.scyjdwmc = this.user.org_name;
                this.form.type = res.data.data.type.toString();
                if (this.form.attachmentList === null) {
                    this.form.attachmentList = [];
                }
            });
        },
        // 查询类型
        getOption() {
            this.iconLoading = true;
            getCompanyCertType().then((res) => {
                this.options = res.data.data;
                this.iconLoading = false;
            });
            getEconomicType().then((res) => {
                this.economicTypeData = res.data.data;
                this.iconLoading = false;
            });
        },
    },

}</script>
<style lang="scss" scoped>
.newAddVisible {
    width: 100%;
    overflow-y: scroll;
    height: 60vh;

    .box {
        width: 70%;
        margin: 0 auto;
    }
}
</style>