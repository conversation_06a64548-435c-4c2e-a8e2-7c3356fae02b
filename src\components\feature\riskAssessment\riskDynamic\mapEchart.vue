<template>
  <div class="mapEchart">
    <div id="mapEchartBox" style="width: calc(100%); height: 600px"></div>

    <div class="echartMapLegend">
      <img src="../../../../../static/img/echartMap.png" />
    </div>
    <div class="sliderBox">
      <!-- {{(dayjs().valueOf() - dayjs().subtract(12, 'hour').valueOf()) / 100}} -->
      <!-- {{ audio.playing }} -->
      <div class="playBtn" @click="clickPlayFn()">
        <span v-if="audio.playing" class="el-icon-video-play"></span
        ><span v-else class="el-icon-video-pause"></span>
      </div>
      <div class="sliderCon">
        <!-- v-model:{{ audio.currentTime }} -->
           <!-- :marks="marks" -->
        <el-slider
          :max="audio.maxTime"
          v-model="audio.currentTime"       
          @change="progressChange"
          :show-tooltip='true'
          :step='8'
        >
        </el-slider>
      </div>
    </div>
  </div>
</template>
<script>
function realFormatSecond(second) {
  var secondType = typeof second;

  if (secondType === "number" || secondType === "string") {
    second = parseInt(second);

    var hours = Math.floor(second / 3600);
    second = second - hours * 3600;
    var mimute = Math.floor(second / 60);
    second = second - mimute * 60;

    return ("0" + mimute).slice(-2) + ":" + ("0" + second).slice(-2);
  } else {
    return "00:00";
  }
}
var timeNull = null;
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
import hubeiJson from "../../../../../static/map/hubei.json";
import dayjs from 'dayjs'
export default {
  components: {},
  data() {
    return {
      value: 20,
      audio: {
        playing: true,
        currentTime: 0,
        maxTime:96,
        seep: 8,
      },
      cacheCurrent: 0,
      cacheVoice: 1,

      value: [0],
      marks: {
        0: "8:00",
        8: "10:00",
        16: "12:00",
        24: "14:00",
        32: "16:00",
        40: "18:00",
        48: "20:00",
        56: "22:00",
        64: "00:00",
        72: "02:00",
        80: "04:00",
        88: "06:00",
        96: "08:00",
      },
    };
  },
  filters: {
    // 将整数转化成时分秒
    formatSecond(second = 0) {
      return realFormatSecond(second);
    },
  },
  methods: {
    //拖动滑块触发事件
    progressChange(){
      console.log(this.audio.currentTime)
    },
    //暂停播放
    clickPlayFn() {
      this.audio.playing = !this.audio.playing;
      if (this.audio.playing) {
        this.animate();
      } else {
        clearInterval(timeNull);
      }
    },
    //slide动画
    animate() {
      clearInterval(timeNull);
      this.audio.currentTime += this.audio.seep;
      if (this.audio.currentTime > this.audio.maxTime) {
        this.audio.currentTime = 0;
      }
      timeNull = setInterval(this.animate, 1000);
    },
  
    mapInit() {
      this.$echarts.registerMap("hubei", hubeiJson);
      // 基于准备好的dom，初始化echarts实例
      var myChart = this.$echarts.init(document.getElementById("mapEchartBox"));

      // 使用刚指定的配置项和数据显示图表。
      // myChart.setOption({
      //   series: [
      //     {
      //       type: "map",
      //       map: "hubei",
      //     },
      //   ],
      // });

      var option = {
        // tooltip: {
        //   trigger: "item",
        //   show: true,
        //   formatter: function (params) {
        //     return params.name + " : " + params.value;
        //   },
        // },
        grid: {
          //echarts地图距离容器位置
          left: "-50px",
          right: "0",
          bottom: "0",
          top: "0",
          containLabel: true,
          showLegendSymbol: true, // 存在legend时显示
        },
        // visualMap: {
        //   seriesIndex: 1, //选取series里面第二个数值是地图的图例样式
        //   right: "5%",
        //   bottom: "10%",
        //   itemWidth: "12",
        //   itemHeight: "110",
        //   calculable: true,
        //   orient: "horizontal",
        //   inRange: {
        //     color: ["#efefef", "#00abfb", "#0090f9", "#007dfb"],
        //   },
        //   textStyle: {
        //     color: "#fff",
        //   },
        // },
        geo: {
          map: "hubei", //设置地图属于哪个地点，根据插件china.json设置
          zoom:1,
          label: {
            normal: {
              show: true,
              fontSize: 12,
              color: "#000",
            },
            emphasis: {
              show: true,
              formatter: function (params) {
                return params.name;
              },
            },
          },

          //  tooltip: {
          //   trigger: "item",
          //   show: true,
          //   formatter: function (params) {
          //     return params.name + " : " + params.value;
          //   },
          // },

          itemStyle: {
            normal: {
              areaColor: "#edf2fd",
              borderWidth: 1,
              borderColor: "#4b80ff",
            },
          },
          regions: [
            {
              name: "宜昌市",             
              itemStyle: {
                normal: {               
                  areaColor: "#f66749", // 整个省份的颜色
                  Text,
                },
              },
            },
            {
              name: "黄冈市",             
              itemStyle: {
                normal: {                 
                  areaColor: "#f8924b", // 整个省份的颜色
                  Text,
                },
              },
            },

            {
              name: "武汉市",              
              itemStyle: {
                normal: {               
                  areaColor: "#fcc35b", // 整个省份的颜色
                  Text,
                },
              },
            },
            {
              name: "孝感市",              
              itemStyle: {
                normal: {              
                  areaColor: "#4880fa", // 整个省份的颜色
                  Text,
                },
              },
            },
          ],
          data: [
            { name: "十堰市", value: 222 },
            { name: "神农架林区", value: 222 },
            { name: "恩施土家族苗族自治州", value: 222},
            { name: "宜昌市", value: 222 },
            { name: "襄阳市", value: 222 },
            { name: "荆门市", value: 222 },
            { name: "荆州市", value: 222 },
            { name: "潜江市", value: 222 },
            { name: "天门市", value: 222 },
            { name: "仙桃市", value: 222 },
            { name: "随州市", value: 222 },
            { name: "孝感市", value: 222 },
            { name: "咸宁市", value: 222 },
            { name: "武汉市", value: 222 },
            { name: "黄冈市", value: 222 },
            { name: "黄石市", value: 222 },
          ],
        },    
      };
      myChart.setOption(option);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.mapInit();
    this.animate();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.mapEchart {
  position: relative;
  .echartMapLegend {
    position: absolute;
    right: 0;
    bottom: 50px;
  }
  .sliderBox {
    position: relative;
    .playBtn {
      position: absolute;
      left: 0;
      top: 0;
      cursor: pointer;
      font-size: 28px;
      span {
        color: #326eff;
      }
    }
    .sliderCon {
      padding: 0 0 0 50px;
    }
  }
}

/deep/ .el-slider__stop {
  background: transparent;
}
/deep/ .el-slider__runway {
  background: #eaf0ff;
}
/deep/ .el-slider__bar {
  background-color: #a0bcff;
}
/deep/ .el-slider__button {
  background-color: #326eff;
}
</style>