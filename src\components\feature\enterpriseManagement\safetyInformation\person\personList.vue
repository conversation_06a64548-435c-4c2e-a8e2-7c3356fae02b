<template>
  <div class="safetyOrgList">
    <div class="header">
      <div class="title">企业负责人</div>
    </div>

    <el-table
      class="table"
      :data="tableData"
      :loading="loading"
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      border
      height="100%"
      style="width: 100%"
    >
      <el-table-column type="index" label="序号" width="55" align="center">
      </el-table-column>
      <el-table-column
        prop="personName"
        label="姓名"
        width="100"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="duties"
        label="职务"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="certificateType"
        label="证书名称"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="issuanceTimeStr"
        label="发证时间"
        align="center"
        width="160"
      >
      </el-table-column>
      <el-table-column
        prop="validityPeriodStr"
        label="证书有效期"
        align="center"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        align="center"
        width="160"
      >
      </el-table-column>
      <el-table-column
        prop="address"
        label="操作"
        align="center"
        width="100"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="params.nowPage"
        :page-size="params.pageSize"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <personDialog
      v-if="detailVisible"
      :show="detailVisible"
      :entObj="safetyOrgInfo"
      @closeBoolean="closeBoolean"
    ></personDialog>
  </div>
</template>

<script>
import { getSafetOrgList, detailSafetOrgList } from "@/api/safetyInfomation";
import personDialog from "./personDialog.vue";
export default {
  //import引入的组件
  name: "rescueTeam",
  components: {
    personDialog,
  },
  props: {
    companyCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      tableData: [],
      loading: false,
      total: 0,
      params: {
        nowPage: 1,
        pageSize: 10,
        personType: "person",
        personName: "",
        phone: "",
        isFullTime: null,
        companyCode: this.companyCode,
        specialOperationPositions: null,
        specialEquipmentOperationPositions: null,
      },
      detailVisible: false,
      safetyOrgInfo: {},
    };
  },
  created() {},
  //方法集合
  methods: {
    handleCurrentChange(val) {
      this.params.nowPage = val;
      this.getList();
    },
    handleView(row) {
      this.safetyOrgInfo = row;
      this.detailVisible = true;
    },
    closeBoolean() {
      this.detailVisible = false;
      this.safetyOrgInfo = {};
    },
    //获取列表
    getList() {
      this.loading = true;
      getSafetOrgList(this.params)
        .then((res) => {
          if (res.data.status == 200) {
            this.tableData = res.data.data.list;
            this.total = Number(res.data.data.total);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
  mounted() {
    this.getList();
  },
};
</script>
<style lang="scss" scoped>
.safetyOrgList {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .header {
    .title {
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
    }
    .operation {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
          margin-bottom: 15px;
        }
        label {
          margin-right: 3px;
        }
      }
    }
  }
  .table {
    margin-top: 10px;
    width: 100%;
    height: 100%;
  }
  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
