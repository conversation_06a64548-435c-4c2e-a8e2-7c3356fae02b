<template>
  <div class="RegistrationInformation">
<div class="div1">
        <div class="table">
          <ul class="container">
            <li>
              <div class="l">企业编码</div>
              <div class="r">{{ enterprise.companyCode }}</div>
            </li>
            <li class="">
              <div class="l">化学品登记系统编码</div>
              <div class="r">{{ enterprise.nrccCode }}</div>
            </li>
            <li class="">
              <div class="l">父级企业编码</div>
              <div class="r">{{ enterprise.parentCode }}</div>
            </li>
            <li>
              <div class="l">企业名称</div>
              <div class="r">{{ enterprise.companyName }}</div>
            </li>
            <li>
              <div class="l">企业简称</div>
              <div class="r">{{ enterprise.companyShortName }}</div>
            </li>
            <li>
              <div class="l">行政区域编码</div>
              <div class="r">{{ enterprise.areaCode }}</div>
            </li>
            <li>
              <div class="l">工商注册地址</div>
              <div class="r">{{ enterprise.addressRegistry }}</div>
            </li>
            <li>
              <div class="l">生产场所地址</div>
              <div class="r">{{ enterprise.addressWorksite }}</div>
            </li>
            <li>
              <div class="l">经度</div>
              <div class="r">{{ enterprise.longitude }}</div>
            </li>
            <li>
              <div class="l">纬度</div>
              <div class="r">{{ enterprise.latitude }}</div>
            </li>
            <li>
              <div class="l">法定代表人</div>
              <div class="r">{{ enterprise.representativePerson }}</div>
            </li>
            <li>
              <div class="l">企业负责人</div>
              <div class="r">{{ enterprise.responsiblePerson }}</div>
            </li>
            <li>
              <div class="l">企业负责人手机</div>
              <div class="r">
                {{ enterprise.responsibleMobile }}
              </div>
            </li>
            <li>
              <div class="l">企业负责人电话</div>
              <div class="r">{{ enterprise.responsiblePhone }}</div>
            </li>
            <li>
              <div class="l">安全负责人</div>
              <div class="r">{{ enterprise.safetyResponsiblePerson }}</div>
            </li>
            <li>
              <div class="l">安全负责人手机</div>
              <div class="r">{{ enterprise.safetyResponsibleMobile }}</div>
            </li>
            <li>
              <div class="l">安全负责人电话</div>
              <div class="r">{{ enterprise.safetyResponsiblePhone }}</div>
            </li>
            <li class="">
              <div class="l">安全值班电话</div>
              <div class="r">{{ enterprise.dutyPhone }}</div>
            </li>
            <li>
              <div class="l">邮政编码</div>
              <div class="r">{{ enterprise.postCode }}</div>
            </li>
            <li>
              <div class="l">成立日期</div>
              <div class="r">{{ enterprise.establishDate }}</div>
            </li>
            <li>
              <div class="l">企业网址</div>
              <div class="r">{{ enterprise.webSite }}</div>
            </li>
            <li>
              <div class="l">企业规模</div>
              <div class="r" v-if="enterprise.companyScale == 1">大型</div>
              <div class="r" v-else-if="enterprise.companyScale == 2">中型</div>
              <div class="r" v-else-if="enterprise.companyScale == 3">小型</div>
              <div class="r" v-else-if="enterprise.companyScale == 4">微型</div>
              <div class="r" v-else></div>
            </li>
            <li>
              <div class="l">企业类型</div>
              <div class="r">{{ enterprise.companyType }}</div>
            </li>
            <li>
              <div class="l">经济类型</div>
              <div class="r">{{ enterprise.economicType }}</div>
            </li>
            <li>
              <div class="l">所属行业门类</div>
              <div class="r">{{ enterprise.industryCategory }}</div>
            </li>
            <li>
              <div class="l">所属行业大类</div>
              <div class="r">{{ enterprise.industryClass }}</div>
            </li>
            <li>
              <div class="l">统一社会信用代码</div>
              <div class="r">{{ enterprise.socialCreditCode }}</div>
            </li>
            <li>
              <div class="l">营业执照经营范围</div>
              <div class="r">{{ enterprise.businessScope }}</div>
            </li>
            <li class="">
              <div class="l">安全生产标准化等级</div>
              <div class="r" v-if="enterprise.safetyStandardGrad == 0">无</div>
              <div class="r" v-else-if="enterprise.safetyStandardGrad == 1">一级</div>
              <div class="r" v-else-if="enterprise.safetyStandardGrad == 2">二级</div>
              <div class="r" v-else-if="enterprise.safetyStandardGrad == 3">三级</div>
              <div class="r" v-else></div>
            </li>
            <li class="">
              <div class="l">安全生产许可证编号</div>
              <div class="r">{{ enterprise.safetyLicenseNo }}</div>
            </li>
            <li>
              <div class="l">安全生产许可证有效期开始日期</div>
              <div class="r">{{ enterprise.safetyLicenseStart }}</div>
            </li>
            <li>
              <div class="l">安全生产许可证有效期结束日期</div>
              <div class="r">{{ enterprise.safetyLicenseEnd }}</div>
            </li>
            <li>
              <div class="l">职工人数</div>
              <div class="r">{{ enterprise.peopleEmployee }}</div>
            </li>
            <li>
              <div class="l">从业人员人数</div>
              <div class="r">{{ enterprise.peoplePractitioner }}</div>
            </li>
            <li>
              <div class="l">剧毒化学品作业人员人数</div>
              <div class="r">{{ enterprise.peopleToxic }}</div>
            </li>
            <li>
              <div class="l">危险化学品作业人员人数</div>
              <div class="r">{{ enterprise.peopleHazard }}</div>
            </li>
            <li>
              <div class="l">特种作业人员人数</div>
              <div class="r">{{ enterprise.peopleOperation }}</div>
            </li>
            <li>
              <div class="l">是否在化工园区内</div>
              <div class="r" v-if="enterprise.inIndustrialPark != null">{{ enterprise.inIndustrialPark == 1?"是":"否" }}</div>
              <div class="r" v-else> </div>
            </li>
            <li>
              <div class="l">所属化工园区名称</div>
              <div class="r">{{ enterprise.industrialParkName }}</div>
            </li>
            <li class="bottom">
              <div class="l">企业状态</div>
              <div class="r" v-if="enterprise.status != null">{{ enterprise.status == 1?"长期停产":"正常"}}</div>
              <div class="r" v-else></div>
            </li>
            <li class="bottom">
              <div class="l"></div>
              <div class="r"></div>
            </li>
            <li class="bottom">
              <div class="l"></div>
              <div class="r"></div>
            </li>
          </ul>
        </div></div
    >
  </div>
</template>

<script>
import { getInformationDangerinfo } from "@/api/entList";
export default {
  //import引入的组件
  name: "RegistrationInformation",
  components: {},
  data() {
    return { show: false, enterprise: {} };
  },
  //方法集合
  methods: {
    getData(id) {
      getInformationDangerinfo(id).then((res) => {
        console.log(res);
        this.enterprise = res.data.data;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.RegistrationInformation {
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    height: 60vh;
    overflow: auto;
    .title {
      width: 100%;
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
      display: flex;
      justify-content: space-between;
      .btn {
        width: 250px;
        display: flex;
        justify-content: space-around;
      }
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 48%;
            padding: 1%;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
        .lang {
          list-style-type: none;
          width: 66.6%;
          display: flex;
          border-top: 1px solid #eaedf2;
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 24.9%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            background-color: rgb(242, 242, 242);
          }
          .r {
            width: 73.3%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: left;
            padding: 0px 10px;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            background: rgb(242, 246, 255);
          }
          .r {
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
