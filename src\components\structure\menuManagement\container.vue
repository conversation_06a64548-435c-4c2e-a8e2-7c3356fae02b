<template>
  <div class="container">
    <div class="header">
      <div class="title">菜单信息</div>
      <el-button
        type="primary"
        class="newMenu"
        @click="newMenu()"
        icon="plus"
        v-if="
          $store.state.sa.SAMenuListData.systemCode != '' ||
          $store.state.sa.SAMenuListData.systemCode != null
        "
        ><span>新建菜单</span></el-button
      >
    </div>
    <div class="body" v-loading="loading">
      <el-table
        :data="tableData.records"
        style="width: 100%; color: rgb(101, 101, 101)"
      >
        <el-table-column type="index" label="序号" width="80">
        </el-table-column>
        <el-table-column prop="menuName" label="菜单名称" width="180">
        </el-table-column>
        <el-table-column prop="menuAlias" label="菜单别名" width="180">
        </el-table-column>
        <el-table-column prop="url" label="菜单url"> </el-table-column>
        <el-table-column align="right" label="操作">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.$index, scope.row)"
              ><i class="el-icon-edit"></i
            ></el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              ><i class="el-icon-delete"></i
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        @prev-click="handleCurrentChange"
        @next-click="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="tableData.size"
        layout="prev, pager, next, jumper"
        :total="tableData.total"
      >
      </el-pagination>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
      :title="title"
    ></DialogTable>
  </div>
</template>

<script>
import { getMenuList, deleteByMenuId } from "../../../api/user";
import DialogTable from "./table";
import { mapState, mapGetters } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {
    DialogTable,
  },
  data() {
    return {
      dialogTableVisible: true,
      currentPage: 1,
      tableData: {},
      loading: false,
      title:""
    };
  },
  computed: {
    listenMenuList() {
      return this.$store.state.sa.SAMenuListData;
    },
  },
  //方法集合
  methods: {
    //打开编辑弹框
    handleEdit(index, row) {
      this.dialogTableVisible = true;
      this.title = '编辑菜单信息'
      this.$refs.child.parentMsg(this.dialogTableVisible);
      this.$refs.child.getData(row.id);
      this.$refs.child.getTree(row.systemCode);
      // this.$refs.child.getTreeData(row.menuId);
    },
    handleDelete(index, row) {
      this.$confirm(
        "选择的菜单未被同步到其他系统！确定要删除选择的数据吗？",
        "通知",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          deleteByMenuId(row.id)
            .then((res) => {
              this.$message({
                message: res.data.msg,
                type: "success",
              });
              if (this.tableData.total % this.tableData.size == 1) {
                this.currentPage = this.currentPage - 1;
              }
              this.getMenuData(
                this.$store.state.sa.SAMenuListData,
                this.currentPage
              );
              this.$emit("pf");
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    fatherMethod() {
      this.getMenuData(this.$store.state.sa.SAMenuListData, 1);
      this.$emit("pf");
    },
    //新建菜单
    newMenu() {
      this.dialogTableVisible = true;
      this.title = '添加菜单信息'
      this.$refs.child.parentMsg(this.dialogTableVisible);
      this.$refs.child.clearTable();
      this.$refs.child.getData(null);
      this.$refs.child.getTree(this.$store.state.sa.SAMenuListData.systemCode);
    },
    //获取菜单信息列表
    getMenuData(val, pageNo) {
      // console.log(pageNo);
      this.loading = true;
      getMenuList({
        systemCode: val.systemCode,
        menuId: val.menuId,
        current: pageNo || 1,
        size: 8,
      })
        .then((res) => {
          // console.log(res.data.data);
          this.tableData = res.data.data;
          this.currentPage = res.data.data.current;
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    handleCurrentChange(data) {
      this.getMenuData(this.$store.state.sa.SAMenuListData, data);
    },
    bus() {
      var vm = this;
      // 用$on事件来接收参数
      Bus.$on("SAMenuListData", (data) => {
        this.$store.commit("sa/updateSAMenuListData", { ...data });
        this.getMenuData(data, vm.currentPage);
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.bus();
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 15px;

  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .newMenu {
      width: 100px;
      height: 38px;
      padding: 10px;
      color: #fff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .title {
      font-weight: 900;
      font-size: 16px;
      color: #000;
    }
  }
  .pagination {
    float: right;
    margin-top: 30px;
  }
}
</style>
