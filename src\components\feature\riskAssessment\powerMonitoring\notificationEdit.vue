<template>
  <div class="notification">
    <el-dialog
      title="修改重点监管企业"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="show"
      width="1000px"
      @close="closeDialog"
      top="10vh"
      :destroy-on-close="true"
    >
      <div class="topSearch">
        <!-- <div style="width: 400px; margin-right: 10px">         -->
          <!-- <el-autocomplete
            popper-class="my-autocomplete"
            v-model="queryParams.orgName"
            :fetch-suggestions="querySearch"
            placeholder="请输入企业名称关键字"
            clearable
            @clear="clearSensororgCode()"
            @select="handleSelect"
            size="medium"
            style="width: 100%"
          >
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete>
        </div> -->
        <!-- <div>
          <el-button
            class="submit"
            size="medium"
            type="primary"
            @click="searchBaseInfo"
            >查询</el-button
          >
        </div> -->
      </div>

      <div class="div1">
        <div class="table">
          <ul class="container">
            <li class="lang">
              <div class="l"><span style="color:red">*</span>企业名称</div>
              <div class="r">{{ BaseInfoData.enterpName }}</div>
            </li>
            <li class="lang">
              <div class="l"><span style="color:red">*</span>所属区划</div>
              <div class="r">{{ BaseInfoData.districtName }}</div>
            </li>
            <li class="lang">
              <div class="l">企业安全负责人</div>
              <div class="r">{{ BaseInfoData.legalrepper }}</div>
            </li>
            <li class="lang">
              <div class="l">联系电话</div>
              <div class="r">
                {{ BaseInfoData.legalreppTel }}
              </div>
            </li>
            <li class="lang bottom">
              <div class="l">企业厂址</div>
              <div class="r">
                {{ BaseInfoData.businessAddress }}
              </div>
            </li>
          </ul>
        </div>
      </div>

      <div class="div1">
        <div class="table">
          <ul class="container">
            <li class="lang">
              <div class="l"><span style="color:red">*</span>监管状态</div>
              <div class="r">
                <el-select
                  v-model="form.status"
                  size="mini"
                  placeholder="请选择企业当前状态"
                  clearable
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </div>
            </li>
            <li class="lang">
              <div class="l"><span style="color:red">*</span>监管时间范围</div>
              <div class="r">
                <el-date-picker
                  v-model="form.dateTime"
                  size="mini"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"                
                  value-format="yyyy-MM-dd HH:mm:ss"
                  unlink-panels
                  clearable
                >
                </el-date-picker>
              </div>
            </li>
            <li class="lang">
              <div class="l">登记单位</div>
              <div class="r">
                <el-input
                  v-model.trim="address"
                  disabled
                  show-word-limit
                  clearable
                ></el-input>
              </div>
            </li>
            <li class="lang bottom">
              <div class="l"><span style="color:red">*</span>登记人</div>
              <div class="r">
                <el-input maxlength="10" v-model.trim="form.operator" clearable></el-input>
              </div>
            </li>
          </ul>

          <el-button
            class="submit"
            size="medium"
            type="primary"
            @click="addSubmit()"
            >保存</el-button
          >

          <el-button class="submit" size="medium"  @click="datailEchart()"
            >查看企业用电图像</el-button
          >

          <el-button class="submit" size="medium" @click="offSupervise()"
            >解除监管</el-button
          >
        </div>
      </div>
    </el-dialog>


      <!-- 查看企业用电图像 -->
    <el-dialog
      title="企业用电图像"
      :visible.sync="detailAlarmDialog"
      width="1000px"
      @open="open()"
      :close-on-click-modal="false"
    >
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="监管通报" name="1"></el-tab-pane>
        <el-tab-pane label="企业反馈" name="0"></el-tab-pane>
      </el-tabs> -->
      <!-- <div class="warn"> -->
        <!-- <div class="null" v-if="tableData == null"></div> -->     

      <!-- <div v-if='echartData.length==0' style="height:300px;text-align:center;line-height:300px">
        暂无用电量数据！
      </div> -->

      <div>
        <div
          class="echarts"
          id="detailEchart"
          style="width: 1000px; height: 300px"
        ></div>
      </div>
    </el-dialog>








  </div>
</template>

<script>
import {
  getElectricityFindOne,
  getElectricityUpdate,
  getElectricityDelete,
  getEnterpConsumption, //企业用电量查询
} from "@/api/riskAssessment";
import { getSearchArr } from "@/api/entList.js";
import { getInformationBasicInfo } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
var dayjs = require("dayjs");
export default {
  //import引入的组件
  name: "notification",
  components: {},
  data() {
    return {
      echartData:[],//echart
      detailAlarmDialog: false,//查看企业用电图像
      BaseInfoData: {},
      queryParams: {
        orgCode: "",
        orgName: "",
      },
      tableData: {},
      show: false,
      checkList: [],
      text: "",
      nowtime: new Date(new Date()).Format("yy年MM月dd日 hh时mm分"),
      cities: "",
      county: "",
      park: "",
      ent: "",
      address: this.$store.state.login.user.org_name,
      title: "安全生产风险警示通报",
      enterpName: "",
      form: {
        status: "",
        dateTime: [],
        endTime: "",
        startTime: "",
        operator: "",
        creditCode: "", //统一信用代码
        districtCode: "", //行政区划代码
        enterpId: "", //企业id
        enterpName: "",
        superviseId: "",
      },
      options: [
        {
          value: "1",
          label: "停工",
        },
        {
          value: "0",
          label: "试生产",
        },
      ],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      userPark: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
  },
  //方法集合
  methods: {
    //查看企业用电量echart
    datailEchart() {     
      this.detailAlarmDialog = true;     
      this.$nextTick(() => {
        this.chartData();
      });    
    },
    open(){
    },
     //查看企业用电量echart
    chartData() {    
      // var alarmTime=dayjs(row.alarmTime);
      // debugger
      var parme = {
        creditCode: this.form.creditCode,
        enterpriseId: this.form.enterpId, // enterpriseId:'429010033', 
        startDate:dayjs().subtract(30, "day").format("YYYY-MM-DD"), 
        endDate: dayjs().format("YYYY-MM-DD"),      
        // startDate: alarmTime.subtract(15, "day").format("YYYY-MM-DD"),
        // endDate: alarmTime.subtract(-15, "day").format("YYYY-MM-DD"),
        // endDate: "2022-06-28",
        // startDate: "2022-05-29"
      };
      getEnterpConsumption(parme).then((res) => {
        console.log(this.echartData);
        this.echartData = [];
        var nameValue = [];
        var numValue = [];
        if (res.data.status == 200) {
          this.echartData = res.data.data.electricityConsumptions;
          this.echartData.forEach((element) => {
            nameValue.push(element.date);
            numValue.push(element.electricityConsumption);
          });
        }

        this.chartInt(nameValue, numValue);
      });
    },
    //查看企业用电量echart
    chartInt(nameValue, numValue) {  
      var chartDom = document.getElementById("detailEchart");
      var myChart = this.$echarts.init(chartDom);
      var option;
      var start = nameValue.length - 15;
      var end = nameValue.length - 1;

      option = {
        tooltip: {
          trigger: "axis",
          // position: function (pt) {
          //   return [pt[0], "10%"];
          // },
        },
         title: {
          show: !numValue.length || numValue.length==0, // 无数据时展示 title
          textStyle: {
            color: "#000000a6",
            fontSize: 16,
          },
          text: "暂无数据",
          left: "center",
          top: "center",
        },
        // title: {
        //   left: "center",
        //   text: "Large Area Chart",
        // },
        toolbox: {
          show: false,
          feature: {
            dataZoom: {
              yAxisIndex: "none",
            },
            dataView: { readOnly: false },
            magicType: { type: ["line", "bar"] },
            restore: {},
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          // data: date,
          data: nameValue,
        },
        yAxis: {
          type: "value",
          // boundaryGap: [0, "100%"],
          axisLabel: {
            formatter: "{value}",
          },
        },
        dataZoom: [
          {
            type: "inside",
            start: start,
            end: end,
          },
          {
            start: 0,
            end: 10,
          },
        ],
        series: [
          {
            // name: 'Highest',
            type: "line",
            data: numValue,
            markPoint: {
              data: [
                { type: "max", name: "Max" },
                { type: "min", name: "Min" },
              ],
            },
            markLine: {
              // data: [{ type: 'average', name: 'Avg' }]
            },
          },
          // {
          //   name: "Fake Data",
          //   type: "line",
          //   symbol: "none",
          //   sampling: "lttb",
          //   itemStyle: {
          //     color: "rgb(255, 70, 131)",
          //   },
          //   areaStyle: {
          //     color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //       {
          //         offset: 0,
          //         color: "rgb(255, 158, 68)",
          //       },
          //       {
          //         offset: 1,
          //         color: "rgb(255, 70, 131)",
          //       },
          //     ]),
          //   },
          //   // data: data,
          //   data: [8, 3, 8, 7, 11,4,5,8],
          // },
        ],
        // noDataLoadingOption: {
        //   text: "暂无数据", //提示文本
        //   effect: "bubble", //效果图
        //   effectOption: {
        //     backgroundColor: "rgba(0,0,0,0)", //背景颜色
        //     effect: {
        //       n: 0, //个数控制
        //     },
        //   },
        // },
      };

      option && myChart.setOption(option);
    },
    //解除监管
     offSupervise(row){
       this.$confirm("确认解除监管？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {       
          getElectricityDelete({ superviseId:  this.form.superviseId }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success('解除成功');
              this.$parent.getData()
              this.show = false;
            } else {
              this.$message.error(res.data.data);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    //获取企业信息
    searchBaseInfo(enterpId) {
      getInformationBasicInfo(enterpId).then((res) => {
        this.BaseInfoData = res.data.data.enterprise;
        this.form.creditCode = res.data.data.enterprise.entcreditCode; //统一信用代码
        this.form.districtCode = res.data.data.enterprise.districtCode; //行政区划代码
        this.form.enterpId = res.data.data.enterprise.enterpId; //企业id
        this.form.enterpName = res.data.data.enterprise.enterpName;
      });
    },
    clearSensororgCode() {
      this.queryParams.orgCode = "";
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.orgCode = item.enterpId;
      this.queryParams.orgName = item.enterpName;
    },
     //清空
    closeDialog() {     
      this.BaseInfoData={}
      this.queryParams= {
        orgCode: "",
        orgName: "",
      }
      this.form={
        status: "",
        dateTime: [],
        endTime: "",
        startTime: "",
        operator: "",
        creditCode: "", //统一信用代码
        districtCode: "", //行政区划代码
        enterpId: "", //企业id
        enterpName: "",
        superviseId: "",
      }
    },
    closeBoolean(val) {     
       this.show = val;       
    },
    submit() {},
    //查详情
    getData(superviseId) {
      getElectricityFindOne({        
       superviseId:superviseId
      }).then((res) => {
        let resulse=res.data.data   
        this.form.status=resulse.status;
        // this.form.dateTime=['2022-07-14 00:00:00','2022-08-31 00:00:00']
        this.form.dateTime.push(resulse.startTime)
         this.form.dateTime.push(resulse.endTime)
        this.form.superviseId=resulse.superviseId
        this.form.operator=resulse.operator || ''      
      });
    },
    addSubmit() {     
      if (this.form.enterpName=='') {
        return this.$message.error("企业名称不能为空");
      }
      if (this.form.districtCode=='') {
        return this.$message.error("行政区划不能为空");
      }
       if (this.form.status=='') {
        return this.$message.error("监管状态不能为空");
      }
             if (!this.form.dateTime) {
        return this.$message.error("监管日期不能为空");
      }
        if (this.form.operator=='') {
        return this.$message.error("登记人不能为空");
      }
      
      getElectricityUpdate({
        creditCode: this.form.creditCode, //统一信用代码
        districtCode: this.form.districtCode, //行政区划代码
        enterpId: this.form.enterpId, //企业id
        enterpName: this.form.enterpName,
        endTime: this.form.dateTime[1],
        startTime: this.form.dateTime[0],
        status: this.form.status,
        superviseId: this.form.superviseId,
        operator:this.form.operator
      }).then((res) => {
        if (res.data.status === 200) {
          this.$message.success(res.data.msg);
          this.$parent.getData()
          this.show = false;
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
.topSearch {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
  align-items: center;
}
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.notification {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>