<template>
  <div class="safetyProductionCertificate">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 许可证信息分析
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="operation">
      <span class="label">区域名称：</span>
      <el-cascader
        size="small"
        placeholder="请选择行政区划"
        :options="district"
        v-model="params.areaCode"
        :props="distOptions"
        clearable
        :show-all-levels="true"
        style="width: 220px"
      ></el-cascader>
      <!-- <span class="label">经营方式：</span> -->
      <!-- <el-input
        style="width: 240px"
        v-model.trim="params.modeOperation"
        placeholder="请输入经营方式"
        class="input"
        size="small"
        clearable
      ></el-input> -->
      <el-button type="primary" class="label" size="small" @click="search"
        >搜索
      </el-button>
    </div>
    <div class="table">
      <el-table
        :data="list"
        v-loading="loading"
        :header-cell-style="{
          textAlign: 'center',
          backgroundColor: 'rgb(242, 246, 255)',
        }"
        border
        style="width: 100%"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column
          label="企业名称"
          prop="scyjdwmc"
         
          show-overflow-tooltip
        ></el-table-column>
        <!-- <el-table-column label="证书编号" prop="certCode" width="200px"></el-table-column> -->
        <el-table-column
          label="登记品种"
          prop="djpz"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="证书类型"
          prop="zslx"
          show-overflow-tooltip
          width="160"
        ></el-table-column>
        <el-table-column
          label="状态"
          prop="status"
          align="left"
          width="140px"
        >
          <template slot-scope="scope">
            <span slot="reference" v-if="scope.row.status == 2">
              <i class="dotClass" style="background-color: #409eff"></i>
               已生效
            </span>
            <span slot="reference" v-else-if="scope.row.status == 1">
              <i class="dotClass" style="background-color: yellow"></i>
              未生效
            </span>
            <span slot="reference" v-else>
              <i class="dotClass" style="background-color: red"></i>
              逾期
            </span>
          </template>
        </el-table-column>
        <el-table-column
          label="证书生效时间"
          prop="zsyxqzi"
          width="120px"
        ></el-table-column>
        <el-table-column
          label="证书失效时间"
          prop="zsyxqzhi"
          width="120px"
        ></el-table-column>
        <el-table-column label="倒计时天数" prop="days" width="110px">
        </el-table-column>
        <el-table-column label="提醒状态" prop="isRemind" width="110px">
          <template slot-scope="scope">{{
            scope.row.isRemind == 0 ? "未提醒" : "已提醒"
          }}</template>
        </el-table-column>
        <el-table-column label="是否上报" prop="isReport" width="110px">
          <template slot-scope="scope">{{
            scope.row.isReport == 0 ? "未上报" : "已上报"
          }}</template>
        </el-table-column>
        <el-table-column label="操作" width="140px">
          <template slot-scope="{ row }">
            <el-button type="text" @click="openDialog('安全生产许可证详情', row.id)">详情
            </el-button>
            <!-- <el-button type="text" @click="handleSend(row.id)">催办
            </el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page.sync="params.nowPage"
        :page-size="params.pageSize"
        :total="total"
        background
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <LicenseDialog
      v-if="newAddVisible"
      :new-add-visible="newAddVisible"
      :orgCode="orgCode"
      :orgName="orgName"
      :id="certId"
      :title="title"
      @submit="handleSubmit"
      @close="closeDialog"
    />
  </div>
</template>

<script>
import { mapState } from "vuex";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import LicenseDialog from "./licenseDialog.vue";
import { getCertList, getCompanyCertDelete } from "@/api/BasicDataManagement";
import { licensePage, licenseId } from "@/api/riskAssessment";
export default {
  name: "safetyProductionCertificate",
  components: {
    LicenseDialog,
  },
  data() {
    return {
      params: {
        areaCode: this.$store.state.login.userDistCode,
        modeOperation: "",
        nowPage: 1,
        pageSize: 10,
      },
      certCode: "",
      orgCode: this.$store.state.login.userDistCode, //this.$store.state.login.enterData.enterpId
      orgName: this.$store.state.login.userDistName, //this.$store.state.login.enterData.enterpName
      list: [],
      currentPage: 1,
      size: 10,
      total: 0,
      tableData: {},
      distOptions: {
        checkStrictly: true,
        value: "distCode",
        label: "distName",
        children: "children",
        emitPath: false,
      },
      newAddVisible: false,
      disabled: true,
      title: "",
      options: [],
      economicTypeData: [],
      value: "",
      loading: false,
      certId: "", //证书id
    };
  },
  filters: {
    //计算当日到指定日期天数,获取两个日期隔多少天
    getDayDiff(value) {
      let date1 = new Date(value);
      let date2 = new Date();
      let days = date1.getTime() - date2.getTime();
      days = Math.floor(days / (1000 * 60 * 60 * 24));
      return days;
    },
    //是否逾期
    getIsRenewal(value) {
      let date1 = new Date(value);
      let date2 = new Date();
      if (date1.getTime() < date2.getTime()) {
        return true;
      } else {
        return false;
      }
    },
  },
  computed: {
    ...mapStateControler({
      district: (state) => state.district,
    }),
    isRenewal() {
      return (val) => {
        let date1 = new Date(val.zsyxqzhi);
        let date2 = new Date(val.zsyxqzi);
        let date3 = new Date();
        if (date3.getTime() < date2.getTime()) {
          return 1;
        } else if (date3.getTime() > date1.getTime()) {
          return 2;
        } else {
          return 0;
        }
      };
    },
  },
  created() {
    this.getData();
  },
  mounted() {},

  methods: {
    search() {
      this.params.nowPage = 1;
      this.getData();
    },
    clearDangerName(e) {
      this.certCode = "";
      this.getData();
    },
    getData() {
      this.loading = true;
      // const params = {
      //   // certCode: this.certCode,
      //   //  orgCode: this.orgCode,
      //   certName: "",
      //   issueOrg: "",
      //   nowPage: this.currentPage,
      //   pageSize: 10,
      //   // types: ["1", "2", "3"],
      //   //   证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-安全生产标准化证书 )
      // }
      const params = this.params;
      licensePage(params)
        .then((res) => {
          this.list = res.data.data.list;
          this.total = res.data.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleCurrentChange(val) {
      this.params.nowPage = val;
      this.getData();
    },
    deleteFun(id) {
      this.$confirm("确认删除该数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        getCompanyCertDelete({ id: id }).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg);
            if (this.list.length === 1 && this.params.nowPage !== 1) {
              this.params.nowPage--;
            }
            this.getData();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    handleSend(id) {
      this.$confirm("确认催办?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {})
        .catch(() => {});
    },
    closeDialog() {
      this.newAddVisible = false;
      this.title = "";
    },
    openDialog(title, id) {
      this.newAddVisible = true;
      this.title = title;
      this.certId = id;
    },
    handleSubmit() {
      this.newAddVisible = false;
      this.title = "";
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}

/deep/ .el-image__preview {
  cursor: -moz-zoom-in;
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}

.dotClass {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 10px; //这个用于圆点居中
}

.safetyProductionCertificate {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }

  .operation {
    display: flex;
    align-items: center;
    padding: 10px 0;
    margin: 10px 0;

    .label {
      margin-left: 20px;
    }
  }

  .table {
    // margin-top: 15px;
  }

  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}

.newAddVisible {
  width: 100%;
  overflow-y: scroll;
  height: 60vh;

  .box {
    width: 70%;
    margin: 0 auto;
  }
}
</style>
