<template>
  <div class="historyList">
    <el-dialog
      v-dialog-drag
      :title="titleName"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="1350px"
      top="10vh"
      :close-on-click-modal="false"
    >
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
      >
        <el-table-column type="index" label="序号" width="55" align="center"> </el-table-column>
        <el-table-column
          prop="dangerName"
          label="重大危险源名称"
           min-width="250"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <!-- <el-table-column
          prop="sensortypeCode"
          label="设备类型"
          align="center"
          width="80"
        >
        <template slot-scope="scope">
            <span v-if="scope.row.sensortypeCode == 'G0'">储罐</span>
            <span v-else-if="scope.row.sensortypeCode == 'Q0'">泄漏点</span>
            <span v-else-if="scope.row.sensortypeCode == 'P0'">装置</span>
          </template>
        </el-table-column> -->
        <el-table-column
          prop="monitorname"
          label="设备名称"
          min-width="200"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <!-- <el-table-column
          prop="monitemkey"
          label="指标类型"
          width="80"
          align="center"
        >
        </el-table-column> -->
        <el-table-column
          prop="targetName"
          label="指标名称"
          width="150"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column prop="warningType" label="报警类型" align="center"  width="80">
        </el-table-column>
        <el-table-column
          prop="warningTime"
          label="报警时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="alarmHandelTime"
          label="消警时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="warningDuration"
          label="报警时长"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column label="当前状态" align="center"  fixed="right">
          <template slot-scope="scope">
            <span>{{scope.row.state ==0 ? '已消警' : '未消警'}}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getLot } from "@/api/entList";
import { parseTime} from '@/utils/index';
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      show: false,
      type: "",
      currentPage: 1,
      loading: true,
      tableData: [],
      total: 0,
      titleName: "",
      enterpId:'',
      type:'',
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.currentPage = 1;
      this.show = val;
    },
    getData(enterpId, type) {
      this.enterpId = enterpId;
      this.loading = true;
      this.type = type;
      if (type == '0') {
        this.titleName ="报警次数列表";
        getLot({
         enterpriseId:enterpId,
         current:this.currentPage,
        //  state:type,
         startTime:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime()), '{y}-{m}-{d}'),
         endTime:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1), '{y}-{m}-{d}'),
         sensortypeCode:'',
         dangerName:'',
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
      } else if (type == '1') {
        this.titleName ="未消警次数列表";
        getLot({
         enterpriseId:enterpId,
         current:this.currentPage,
         state:type,
         startTime:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime()), '{y}-{m}-{d}'),
         endTime:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1), '{y}-{m}-{d}'),
         sensortypeCode:'',
         dangerName:'',
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
      }




    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    showDialog() {
      this.$refs.History.closeBoolean(true);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getData(this.enterpId, this.type);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.historyList {
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 700px;
      display: flex;
      justify-content: space-between;
      > div{
        margin-right: 15px;
        width: 200px;
      }
      >.btn{
        margin-right: 15px;
      }

    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>
