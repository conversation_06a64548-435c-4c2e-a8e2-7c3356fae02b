<template>

  <el-dialog title="
危险化学品"
             :visible.sync="show"
             width="1350px"
             @close="closeBoolean()"
             :close-on-click-modal="false"
             append-to-body>
    <div class="chemicals"
         v-loading="loading">
      <div class="div1">
        <div class="table">
          <ul class="container">
            <li>
              <div class="l">企业名称</div>
              <div class="r">{{table.enterpName}}</div>
            </li>
            <li>
              <div class="l">企业编码</div>
              <div class="r">{{table.enterpId}}</div>
            </li>
            <li class="liLine">
              <div class="l">* 统一社会信用代码</div>
              <div class="r">{{table.entcreditCode}}</div>
            </li>
            <li>
              <div class="l">* 危险化学品中文名</div>
              <div class="r">{{table.hazarchemName}}</div>
            </li>
            <li>
              <div class="l">别名</div>
              <div class="r">{{table.chemicalAlias}}</div>
            </li>
            <li class="liLine">
              <div class="l">危化品编码</div>
              <div class="r">{{table.hazarchemId}}</div>
            </li>
            <li>
              <div class="l">CAS号</div>
              <div class="r">{{table.casNo}}</div>
            </li>
            <li>
              <div class="l">* 化学品状态</div>
              <div class="r"
                   v-if="table.mediumType==3">液体</div>
              <div class="r"
                   v-else-if="table.mediumType==2">气体</div>
              <div class="r"
                   v-else-if="table.mediumType==1">固体</div>
            </li>
            <li class="liLine">
              <div class="l">* 化学品属性</div>
              <div class="r">{{table.hazarchemProperty}}</div>
              <!-- <div class="r"
                   v-if="table.hazarchemProperty == 1">产品</div>
              <div class="r"
                   v-else-if="table.hazarchemProperty == 2">中间产品</div>
              <div class="r"
                   v-else-if="table.hazarchemProperty == 4">原料</div>
              <div class="r"
                   v-else> 
                   
                   </div> -->
            </li>
            <li>
              <div class="l">是否易制爆化学品</div>
              <div class="r">{{table.isExplosiveChemicals==1?"是":"否"}}</div>
            </li>
            <li>
              <div class="l">是否剧毒品</div>
              <div class="r">{{table.isHighlyToxic==1?"是":"否"}}</div>
            </li>
            <li class="liLine">
              <div class="l">是否易制毒化学品</div>
              <div class="r">{{table.isPrecursorChemicals==1?"是":"否"}}</div>
            </li>
            <li>
              <div class="l">是否重点监管</div>
              <div class="r">{{table.isFirstMonhazchem==1?"是":"否"}}</div>
            </li>
            <li class="lang">
              <div class="l">是否特别管控</div>
              <div class="r">{{table.isSpecialControl==1?"是":"否"}}</div>
            </li>
            <li class="bottom">
              <div class="l">年生产能力(标方)</div>
              <div class="r">{{table.annualProductionCapacity}}</div>
            </li>
            <li class="bottom">
              <div class="l">* 设计储量(标方)</div>
              <div class="r">{{table.storageNum}}</div>
            </li>
            <li class="liLine bottom">
              <div class="l">年使用量(标方)</div>
              <div class="r">{{table.annualUsageMeal}}</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </el-dialog>

</template>

<script>
import { getInformationInfoHazarchem } from '@/api/entList'
export default {
  //import引入的组件
  name: 'chemicals',
  components: {},
  data() {
    return { show: false, table: {}, loading: false }
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val
      this.table = {}
    },
    getData(id) {
      this.loading = true
      getInformationInfoHazarchem(id).then(res => {
        // console.log(res);
        this.table = res.data.data
        this.loading = false
      })
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {}
}
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.chemicals {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    height: 300px;
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 66.6%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 24.9%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 74%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: center;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>