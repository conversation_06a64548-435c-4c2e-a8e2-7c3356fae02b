<!-- 基础信息 -->
<template>
  <div class="baseInfo-container">
    <el-form :model="testInfo" :rules="rules" ref="baseForm" size="small" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="20">
          <el-form-item label="考试名称:" prop="examName">
            <el-input v-model.trim="testInfo.examName" maxlength="100" :disabled="disabled" clearable placeholder="请输入考试名称" />
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="考试时长:" prop="examTime">
            <el-select v-model="testInfo.examTime" placeholder="请选择考试时长" :disabled="disabled" clearable style="width: 100%">
              <el-option v-for="item in timeOptions" :key="item.value" :value="item.value"
                :label="item.label"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="10">
          <el-form-item label="考试总分:" prop="fractionCount">
            <el-input v-model.trim="testInfo.fractionCount" :disabled="disabled" placeholder="请输入考试名称" clearable />
          </el-form-item>
        </el-col>

      </el-row>
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="题目类型:" prop="topicType">
            <el-select v-model="testInfo.topicType" placeholder="请选择题目类型" multiple :disabled="disabled" clearable
              style="width: 100%">
              <el-option v-for="item in examinationTypes" :key="item.value" :value="item.value"
                :label="item.label"></el-option>
            </el-select>

          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="10">
          <el-form-item label="考试科目:" prop="examCourse">
            <el-select v-model="testInfo.examCourse" placeholder="请选择考试科目" :disabled="disabled" clearable style="width: 100%">
              <el-option v-for="item in subjectList" :key="item" :value="item"
                :label="item"></el-option>
            </el-select>

          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  model: {
    prop: "testInfo",
    event: "change",
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    testInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    subjectList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      timeOptions: [
        { value: "30", label: "30分钟" },
        { value: "45", label: "45分钟" },
        { value: "60", label: "60分钟" },
        { value: "90", label: "90分钟" },
      ],
      examinationTypes: [
        { label: "单选", value: "1" },
        { label: "多选", value: "2" },
      ],
      // 考试科目
      // subjectList: [
      //   { label: "111-危化", value: "111-危化" },
      //   { label: "222-安全生产", value: "222-安全生产" },
      // ],
      rules: {
        examName: [
          { required: true, message: "请输入考试名称", trigger: "blur" },
        ],
        examTime: [
          { required: true, message: "请选择考试时长", trigger: "change" },
        ],
        fractionCount: [
          { required: true, message: "请输入考试总分", trigger: "blur" },
          // {
          //   required: true,
          //   trigger: "blur",
          //   message: "请输入数字",
          //   pattern: /^[1-9]?\d$/,
          // },
           { trigger: "blur", pattern: /^(100|([1-9]?\d))$/, message: '请输入100以内的数字' }
        ],
        topicType: [
          { required: true, type: 'array', message: "请输入题目类型", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    updateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.baseForm.validate((valid) => {
          if (valid) {
            this.$emit("change", this.testInfo);
            resolve(valid);
          } else {
            reject(false);
            return false;
          }
        });
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.baseInfo-container {
  height: 400px;
  overflow: auto;
  padding: 30px 20px 20px;
  box-sizing: border-box;
}
</style>
