<template>
  <div class="warnPush">
    <div class="div1">
      <div class="title">
        <div class="red">红色预警</div>
        <span>带<span style="color: red">*</span>号的都为必填项</span>
      </div>

      <div>
        <el-form
          :model="red"
          ref="red"
          label-position="left"
          label-width="150px"
          :inline="true"
          :rules="rules"
        >
          <el-row :gutter="80">
            <el-col :span="12">
              <el-form-item label="推送对象" prop="pushObject">
                <el-select
                  v-model="red.pushObject"
                  class="select"
                  size="small"
                  multiple
                  placeholder="请选择推送对象"
                  clearable
                >
                  <el-option
                    v-for="item in pushObject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-form-item label="重复推送频率">
            <el-select
              v-model="red.frequency"
              class="select"
              size="small"
              placeholder="请选择重复推送频率"
              clearable
            >
              <el-option
                v-for="item in frequency"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
            <el-col :span="12">
              <el-form-item label="是否重复推送" prop="isRepeatPush">
                <el-select
                  v-model="red.isRepeatPush"
                  class="select"
                  size="small"
                  placeholder="请选择重复推送频率"
                  clearable
                >
                  <el-option
                    v-for="item in isRepeatPush"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动通报机制" prop="warningMechanism">
                <el-select
                  v-model="red.warningMechanism"
                  class="select"
                  size="small"
                  placeholder="请选择自动通报机制"
                  clearable
                >
                  <el-option
                    v-for="item in warningMechanism"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通报对象" prop="notificationObject">
                <el-select
                  v-model="red.notificationObject"
                  class="select"
                  size="small"
                  placeholder="请选择通报对象"
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in notificationObject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="div2">
      <div class="orange">橙色预警</div>
      <div>
        <el-form
          :model="orange"
          ref="orange"
          label-position="left"
          label-width="150px"
          :inline="true"
          :rules="rules"
        >
          <el-row :gutter="80">
            <el-col :span="12">
              <el-form-item label="推送对象" prop="pushObject">
                <el-select
                  v-model="orange.pushObject"
                  class="select"
                  size="small"
                  multiple
                  placeholder="请选择推送对象"
                  clearable
                >
                  <el-option
                    v-for="item in pushObject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <!-- <el-form-item label="重复推送频率">
            <el-select
              v-model="orange.pushObject"
              class="select"
              size="small"
              placeholder="请选择重复推送频率"
              clearable
            >
              <el-option
                v-for="item in pushObject"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否重复推送" prop="isRepeatPush">
                <el-select
                  v-model="orange.isRepeatPush"
                  class="select"
                  size="small"
                  placeholder="请选择重复推送频率"
                  clearable
                >
                  <el-option
                    v-for="item in isRepeatPush"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动通报机制" prop="warningMechanism">
                <el-select
                  v-model="orange.warningMechanism"
                  class="select"
                  size="small"
                  placeholder="请选择自动通报机制"
                  clearable
                >
                  <el-option
                    v-for="item in warningMechanism"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通报对象" prop="notificationObject">
                <el-select
                  v-model="orange.notificationObject"
                  class="select"
                  size="small"
                  placeholder="请选择通报对象"
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in notificationObject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="div3">
      <div class="yellow">黄色预警</div>
      <div>
        <el-form
          :model="yellow"
          ref="yellow"
          label-position="left"
          label-width="150px"
          :inline="true"
          :rules="rules"
        >
          <el-row :gutter="80">
            <el-col :span="12">
              <el-form-item label="推送对象" prop="pushObject">
                <el-select
                  v-model="yellow.pushObject"
                  class="select"
                  size="small"
                  multiple
                  placeholder="请选择推送对象"
                  clearable
                >
                  <el-option
                    v-for="item in pushObject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-form-item label="重复推送频率">
            <el-select
              v-model="yellow.frequency"
              class="select"
              size="small"
              placeholder="请选择重复推送频率"
              clearable
            >
              <el-option
                v-for="item in frequency"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item> -->
            <el-col :span="12">
              <el-form-item label="是否重复推送" prop="isRepeatPush">
                <el-select
                  v-model="yellow.isRepeatPush"
                  class="select"
                  size="small"
                  placeholder="请选择重复推送频率"
                  clearable
                >
                  <el-option
                    v-for="item in isRepeatPush"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="自动通报机制" prop="warningMechanism">
                <el-select
                  v-model="yellow.warningMechanism"
                  class="select"
                  size="small"
                  placeholder="请选择自动通报机制"
                  clearable
                >
                  <el-option
                    v-for="item in warningMechanism"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="通报对象" prop="notificationObject">
                <el-select
                  v-model="yellow.notificationObject"
                  class="select"
                  size="small"
                  placeholder="请选择通报对象"
                  clearable
                  multiple
                >
                  <el-option
                    v-for="item in notificationObject"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="submitBox">
      <el-button
        class="submit"
        type="primary"
        size=""
        @click="!loadingBtn ? submit() : null"
        :loading="loadingBtn"
        >提交</el-button
      >
    </div>
  </div>
</template>

<script>
import { getPushRoleList, getPushRoleUpdate } from "../../../api/workbench";
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      loadingBtn: false,
      red: {},
      yellow: {},
      orange: {},
      rules: {
        pushObject: [
          { required: true, message: "请选择推送对象", trigger: "change" },
        ],
        isRepeatPush: [
          { required: true, message: "请选择是否重复推送", trigger: "change" },
        ],
        warningMechanism: [
          { required: true, message: "请选择自动通报机制", trigger: "change" },
        ],
        notificationObject: [
          { required: true, message: "请选择通报对象", trigger: "change" },
        ],
      },
      pushObject: [
        {
          value: "1",
          label: "省级",
        },
        {
          value: "2",
          label: "地市",
        },
        {
          value: "3",
          label: "区县",
        },
        {
          value: "4",
          label: "园区",
        },
        {
          value: "5",
          label: "企业",
        },
      ],
      isRepeatPush: "",
      isRepeatPush: [
        {
          value: "0",
          label: "否",
        },
        {
          value: "1",
          label: "是",
        },
      ],
      frequency: [
        {
          value: "选项1",
          label: "不重复推送",
        },
        {
          value: "选项2",
          label: "15分钟",
        },
        {
          value: "选项3",
          label: "30分钟",
        },
        {
          value: "选项4",
          label: "1小时",
        },
        {
          value: "选项5",
          label: "2小时",
        },
        {
          value: "选项6",
          label: "3小时",
        },
        {
          value: "选项7",
          label: "4小时",
        },
      ],
      warningMechanism: [
        {
          value: "1",
          label: "预警30分钟未处置",
        },
        {
          value: "2",
          label: "预警1小时未处置",
        },
        {
          value: "3",
          label: "预警2小时未处置",
        },
        {
          value: "4",
          label: "预警3小时未处置",
        },
        {
          value: "5",
          label: "预警4小时未处置",
        },
      ],
      notificationObject: [
        {
          value: "1",
          label: "省级",
        },
        {
          value: "2",
          label: "地市",
        },
        {
          value: "3",
          label: "区县",
        },
        {
          value: "4",
          label: "园区",
        },
        {
          value: "5",
          label: "企业",
        },
      ],
      value1: [],
    };
  },
  //方法集合
  methods: {
    getData() {
      getPushRoleList().then((res) => {
        for (var i = 0; i < res.data.data.length; i++) {
          switch (res.data.data[i].riskLevel) {
            case "1":
              this.redData(res, i);
              break;
            case "2":
              this.orangeData(res, i);
              break;
            case "3":
              this.yellowData(res, i);
              break;
            default:
              break;
          }
        }
      });
    },
    redData(res, i) {
      let notificationObject = res.data.data[i].notificationObject.split(",");
      let pushObject = res.data.data[i].pushObject.split(",");
      this.red = {
        ...res.data.data[i],
        notificationObject,
        pushObject,
      };
    },
    orangeData(res, i) {
      let notificationObject = res.data.data[i].notificationObject.split(",");
      let pushObject = res.data.data[i].pushObject.split(",");
      this.orange = {
        ...res.data.data[i],
        notificationObject,
        pushObject,
      };
    },
    yellowData(res, i) {
      let notificationObject = res.data.data[i].notificationObject.split(",");
      let pushObject = res.data.data[i].pushObject.split(",");
      this.yellow = {
        ...res.data.data[i],
        notificationObject,
        pushObject,
      };
    },
    submit() {
      const validRed = new Promise((resolve, reject) => {
        this.$refs["red"].validate((valid) => {
          if (valid) {
            return resolve();
          } else {
            return reject();
          }
        });
      });

      const validOrange = new Promise((resolve, reject) => {
        this.$refs["orange"].validate((valid) => {
          if (valid) {
            return resolve();
          } else {
            return reject();
          }
        });
      });

      const validYellow = new Promise((resolve, reject) => {
        this.$refs["yellow"].validate((valid) => {
          if (valid) {
            return resolve();
          } else {
            return reject();
          }
        });
      });
      const THIS = this;
      Promise.all([validRed, validOrange, validYellow])
        .then((res) => {
          THIS.upDataForm();
        })
        .catch((e) => {
          THIS.$message.error("请检查必填项");
        });
    },
    upDataForm() {
      this.loadingBtn = true;
      let { red, orange, yellow } = this;
      red = {
        ...red,
        notificationObject: red.notificationObject.join(","),
        pushObject: red.pushObject.join(","),
      };
      orange = {
        ...orange,
        notificationObject: orange.notificationObject.join(","),
        pushObject: orange.pushObject.join(","),
      };
      yellow = {
        ...yellow,
        notificationObject: yellow.notificationObject.join(","),
        pushObject: yellow.pushObject.join(","),
      };
      let res = [red, orange, yellow];
      getPushRoleUpdate(res)
        .then((res) => {
          if (res.data.code == 0) {
            this.$message.success(res.data.data);
          } else {
            this.$message.error(res.data.data);
          }
          this.getData();
          setTimeout(() => {
            this.loadingBtn = false;
            console.log(this.loadingBtn);
          }, 500);
        })
        .catch((e) => {
          setTimeout(() => {
            this.loadingBtn = false;
          }, 500);
          this.$message.error("修改失败");
        });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
};
</script>
<style lang="scss" scoped>
// /deep/ .el-tag {
//   color: #fff;
//   border-color: #fff;
// }
/deep/ .el-form--inline .el-form-item__content {
  float: right;
}
.warnPush {
  margin-left: 50%;
  transform: translateX(-50%);
  width: 1100px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  .div1 {
    .title {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  }
  .select {
    width: 350px;
  }
  .red,
  .orange,
  .yellow {
    width: 120px;
    text-align: center;
    height: 30px;
    line-height: 30px;
    margin: 10px 0;
    color: #fff;
    font-weight: 900;
    border-radius: 3px;
    display: inline-block;
  }
  .red {
    background-color: red;
  }
  .orange {
    background-color: orange;
  }
  .yellow {
    background-color: Gold;
  }
  .div1,
  .div2,
  .div3 {
    margin: 15px 0;
  }
  .submitBox {
    width: 100%;
    display: flex;
    justify-content: center;
    .submit {
      width: 150px;
      height: 45px;
      font-size: 16px;
      color: #fff;
    }
  }
}
</style>