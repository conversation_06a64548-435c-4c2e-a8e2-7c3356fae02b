!function(t){var e={};function n(r){if(e[r])return e[r].exports;var a=e[r]={i:r,l:!1,exports:{}};return t[r].call(a.exports,a,a.exports,n),a.l=!0,a.exports}n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)n.d(r,a,function(e){return t[e]}.bind(null,a));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=0)}([function(t,e,n){"use strict";n.r(e);var r={log:function(){},error:function(){},count:function(){},info:function(){}};(function(){function t(){}t.createFromElementId=function(e){for(var n=document.getElementById(e),r="",a=n.firstChild;a;)3===a.nodeType&&(r+=a.textContent),a=a.nextSibling;var o=new t;return o.type=n.type,o.source=r,o},t.createFromSource=function(e,n){var r=new t;return r.type=e,r.source=n,r}})(),function(){function t(t){this.gl=t,this.program=this.gl.createProgram()}t.prototype={attach:function(t){this.gl.attachShader(this.program,t.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(t){return this.gl.getAttribLocation(this.program,t)},setMatrixUniform:function(t,e){var n=this.gl.getUniformLocation(this.program,t);this.gl.uniformMatrix4fv(n,!1,e)}}}(),function(){var t=null;function e(t,e,n){this.gl=t,this.size=e,this.texture=t.createTexture(),t.bindTexture(t.TEXTURE_2D,this.texture),this.format=n||t.LUMINANCE,t.texImage2D(t.TEXTURE_2D,0,this.format,e.w,e.h,0,this.format,t.UNSIGNED_BYTE,null),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE)}e.prototype={fill:function(t,e){var n=this.gl;n.bindTexture(n.TEXTURE_2D,this.texture),e?n.texSubImage2D(n.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,n.UNSIGNED_BYTE,t):n.texImage2D(n.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,n.UNSIGNED_BYTE,t)},bind:function(e,n,r){var a=this.gl;t||(t=[a.TEXTURE0,a.TEXTURE1,a.TEXTURE2]),a.activeTexture(t[e]),a.bindTexture(a.TEXTURE_2D,this.texture),a.uniform1i(a.getUniformLocation(n.program,r),e)}}}();var a=function(){var t=48e3,e=4,n=15,a=[255,511,1023,2047,4095,8191,16383,32767],o=8e3,i=null;function u(t){var r,o,i;return t>=0?r=213:(r=85,t=-t-8),(o=function(t,e){for(var n=0,r=e.length;n<r;n++)if(t<=e[n])return n;return e.length}(t,a))>=8?127^r:(i=o<<e,(i|=o<2?t>>4&n:t>>o+3&n)^r)}function l(){}return l.prototype={setSampleRate:function(e){t=e},encode:function(e){var n=null;null!==i?((n=new Float32Array(e.length+i.length)).set(i,0),n.set(e,i.length)):n=e,n=function(e,n){if(n===t)return e;n>t&&r.log("The rate of device show be smaller than local sample rate");for(var a=t/n,o=Math.floor(e.length/a),u=new Float32Array(o),l=0,s=0;l<u.length;){for(var f=Math.round((l+1)*a),c=0,h=0,g=s,T=e.length;g<f&&g<T;g++)c+=e[g],h++;u[l]=c/h,l++,s=f}if(i=null,Math.round(l*a)!==e.length){var E=Math.round(l*a);i=new Float32Array(e.subarray(E,e.length))}return u}(n,o);for(var a=new Int16Array(n.length),l=new Uint8Array(a.length),s=0,f=n.length;s<f;s++)a[s]=n[s]*Math.pow(2,15),l[s]=u(a[s]);return l}},new l},o=function(t){var e=null,n=null,r=[36,t,0,0,0,0],o=[68,72,65,86],i=[100,104,97,118],u=245,l=0,s=null;function f(t,e,n){var r=[],a=e||4;if(!0===n)for(var o=0;o<a;o++)r[o]=t>>>8*(a-1-o)&255;else for(var i=0;i<a;i++)r[i]=t>>>8*i&255;return r}function c(){n=new a}return c.prototype={setSampleRate:function(t){n.setSampleRate(t)},getRTPPacket:function(a){var c=n.encode(a),h=0;(e=new Uint8Array(r.length+40+c.length+8)).set([36,t],h),h+=2,e.set(f(40+c.length+8,4,!0),h),h+=4,e.set(o,h),h+=4,e.set([240],h),h+=1,e.set([0],h),h+=1,e.set([1],h),h+=1,e.set([0],h),h+=1,u>65535&&(u=240),e.set(f(u),h),h+=4,u++;var g=f(40+c.length+8);e.set(g,h),h+=4;var T=new Date,E=(T.getFullYear()-2e3<<26)+(T.getMonth()+1<<22)+(T.getDate()<<17)+(T.getHours()<<12)+(T.getMinutes()<<6)+T.getSeconds(),p=T.getTime(),m=null===s?0:p-s;s=p,(l+=m)>65535&&(l=65535-l),e.set(f(E),h),h+=4,e.set(f(l,2),h),h+=2,e.set([16],h),h+=1;var d=function(t,e){for(var n=0,r=e;r<t.length;r++)n+=t[r];return n}(e,6);e.set([d],h),h+=1,e.set([131,1,14,2],h),h+=4,e.set([150,1,0,0],h),h+=4;var v=function(t,e){for(var n=0,r=0;r<e;r++)n+=t[r]<<r%4*8;return n}(c,c.length);return e.set([136],h),h+=1,e.set(f(v),h),h+=4,e.set([0,0,0],h),h+=3,e.set(c,h),h+=c.length,e.set(i,h),h+=4,e.set(g,h),e}},new c(t)},i=null;addEventListener("message",function(t){var e,n=t.data;switch(n.type){case"sdpInfo":!function(t){for(var e=0;e<t.length;e++)"sendonly"===t[e].TalkTransType&&(i=new o(t[e].RtpInterlevedID))}(n.data.sdpInfo);break;case"getRtpData":var r=i.getRTPPacket(n.data);e=r,postMessage({type:"rtpData",data:e},[e.buffer]);break;case"sampleRate":null!==i&&i.setSampleRate(n.data)}},!1)}]);