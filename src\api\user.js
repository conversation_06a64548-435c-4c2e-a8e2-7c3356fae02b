import axios from "@/utils/http";
import qs from "qs";

// 组织机构数据
export const getStructureData = (data) => {
  return axios({
    method: "get",
    url: "/admin/org/tree",
  });
};

// 所属角色树
export const getStructureTree = (data) => {
  return axios({
    method: "get",
    url: "/admin/role/all/tree/-1",
  });
};

//通过组织机构获取用户数据
export const getUserData = (data) => {
  let page = data.page;
  return axios({
    method: "get",
    url: "/admin/user/list",
    //   data: qs.stringify(data)
    params: {
      showFlag: data.showFlag,
      name: data.name,
      orgCode: data.orgCode,
      current: data.current,
      size: data.size,
    },
  });
};
// 菜单管理
export const getSystemAll = (data) => {
  return axios({
    method: "get",
    url: `/admin/menu/tree/${data}`,
  });
};
//业务基础平台
export const getMenuListBySysCode = (data) => {
  return axios({
    method: "post",
    url: "/hg/menu/menuListBySysCode.mvc",
    data: qs.stringify(data),
  });
};
//菜单信息
export const getMenuList = (data) => {
  return axios({
    method: "get",
    url: `/admin/menu/list`,
    params: data,
  });
};
//系统信息
export const getSystemList = (data) => {
  return axios({
    method: "get",
    url: "/admin/system/list",
    params: data,
  });
};
//系统密码规则
export const getPwdRuleList = (data) => {
  return axios({
    method: "get",
    url: `/admin/secure/pwdRole`,
    params: data,
  });
};
//查询密级管理
export const getSecurityList = (data) => {
  return axios({
    method: "get",
    url: `/admin/secure/securityLevel`,
    params: data,
  });
};
//编辑密级管理
export const getPwdRuleEditRule = (data) => {
  return axios({
    method: "post",
    url: "/hg/pwdRule/editRule.mvc",
    data: qs.stringify(data),
  });
};
//保存菜单
export const saveMenuTable = (data) => {
  return axios({
    method: "post",
    url: "/admin/menu/add",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
    data: qs.stringify(data),
  });
};
//修改菜单
export const setMenuTable = (data) => {
  return axios({
    method: "post",
    url: "/admin/menu/update",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
    data: qs.stringify(data),
  });
};
//保存系统菜单
export const saveSystem = (data) => {
  return axios({
    method: "post",
    url: "/admin/system/add",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
    data: qs.stringify(data),
  });
};
//保存密码规则
export const savePwdRule = (data) => {
  return axios({
    method: "post",
    url: "/admin/secure/add/pwdRole",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
    data: qs.stringify(data),
  });
};
//保存密级
export const saveSecurity = (data) => {
  return axios({
    method: "post",
    url: "/admin/secure/add/securityLevel",
    headers: {
      "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8",
    },
    data: qs.stringify(data),
  });
};
//删除菜单
export const deleteByMenuId = (data) => {
  return axios({
    method: "get",
    url: `/admin/menu/del/${data}`,
  });
};
//删除系统信息
export const deletValSystemCanDelete = (data) => {
  return axios({
    method: "get",
    url: `/admin/system/del/${data}`,
  });
};
//删除密码规则
export const deleteByPwdRuleId = (data) => {
  return axios({
    method: "get",
    url: `/admin/secure/del/pwdRole/${data}`,
  });
};
//删除密级
export const deleteBysecurityId = (data) => {
  return axios({
    method: "get",
    url: `/admin/secure/del/securityLevel/${data}`,
  });
};
//查询菜单详情
export const getMenmenuByMenuId = (data) => {
  return axios({
    method: "get",
    url: `/admin/menu/info/${data}`,
  });
};
//查询菜单树选项
export const querySystemTreeNoSelf = (data) => {
  return axios({
    method: "get",
    url: `/admin/system/list/${data}`,
  });
};
//查询菜单树选中状态
export const sameMenuFlagSystemCodes = (data) => {
  return axios({
    method: "post",
    url: "/hg/menu/sameMenuFlagSystemCodes.mvc",
    data: qs.stringify(data),
  });
};
//检测用户名
export const validateLoginNames = (data) => {
  return axios({
    method: "get",
    url: "/admin/secure/check?id=" + data.id + "&loginName=" + data.loginName,
  });
};
//检测密码
export const validatePsds = (data) => {
  return axios({
    method: "get",
    url: "/admin/secure/check?password=" + data.password,
  });
};
//新增用户
export const addUser = (data) => {
  return axios({
    method: "post",
    url: "/admin/user/add",
    data: qs.stringify(data),
  });
};
//注销用户
export const DeleteUser = (data) => {
  return axios({
    method: "get",
    url: "/admin/user/logout/" + data.id,
  });
};
//锁定/激活用户
export const lockandunlockUser = (data) => {
  return axios({
    method: "get",
    url: "/admin/user/lock?id=" + data.id + "&lockFlag=" + data.lockFlag,
  });
};
//重置密码
export const restPassword = (data) => {
  return axios({
    method: "get",
    url: "/admin/user/reset/" + data.id,
  });
};
//修改密码规则启用状态
export const updatePwdRuleStatus = (data) => {
  return axios({
    method: "post",
    url: "/hg/pwdRule/updateStatus.mvc",
    data: qs.stringify(data),
  });
};
//查询用户
export const getDetailData = (data) => {
  return axios({
    method: "get",
    url: "/admin/user/sel/info/" + data.id,
  });
};
//查询角色ids
export const getRoleIds = (data) => {
  return axios({
    method: "get",
    url: "/admin/user/role/" + data.id,
  });
};
//保存角色
export const saveRoleIds = (data) => {
  return axios({
    method: "post",
    url: "/admin/user/update/roleUser",
    data: qs.stringify(data),
  });
};
export const saveMenmenuByMenuId = (data) => {
  return axios({
    method: "get",
    url: `/admin/menu/info/${data}`,
  });
};
//根据企业编码查询企业机构信息 {"enterpId":"123"}
export const getfindByEnterpId = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/org/findByEnterpId/v1",
    method: "post",
    data: data,
  });
};
//根据orgCode查询组织机构(包括企业机构) {"orgCode":"111111111"}
export const getUserOrgId = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/org/id/v1",
    method: "post",
    data: data,
  });
};
export const getEnterpriseId = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/org/enterprise/id/v1",
    method: "post",
    data: data,
  });
};
export const getOrgTrees = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/org/trees/all/v1",
    method: "post",
    data: data,
  });
};
export const getOrgTreesN = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/org/trees/superviser/v1",
    method: "post",
    data: data,
  });
};

export const getLogList = (data) => {
  return axios({
    url: "/gemp-user/api/system/log/list/v1",
    method: "post",
    data: data,
  });
};

export const getLogListUser = (data) => {
  return axios({
    url: "/gemp-user/api/system/log/list/operate/v1",
    method: "post",
    data: data,
  });
};

// //查询日志列表
// export const getLogListUser = data => {
//   return axios({
//     method: "post",
//     url: "/gemp-user/api/system/log/list/operate/v1",
//     data: data,
//   });
// };

//配置管理分类列表展示
export const getConfigClassify = (data) => {
  return axios({
    url: "/gemp-user/api/config/classify/list/v1",
    method: "post",
    data: data,
  });
};

//getConfigClassify
export const configModify = (data) => {
  return axios({
    url: "/gemp-user/api/config/delete/modify/v1",
    method: "post",
    data: data,
  });
};

///api/config/delete/classify/v1
export const configDelete = (data) => {
  return axios({
    url: "/gemp-user/api/config/delete/classify/v1",
    method: "post",
    data: data,
  });
};

export const configAdd = (data) => {
  return axios({
    url: "/gemp-user/api/config/add/v1",
    method: "post",
    data: data,
  });
};

//config/search/v1

export const configSearch = (data) => {
  return axios({
    url: "/gemp-user/api/config/search/v1",
    method: "post",
    data: data,
  });
};

//
export const configDeletePage = (data) => {
  return axios({
    url: "/gemp-user/api/config/delete/v1",
    method: "post",
    data: data,
  });
};

export const configRefresh = (data) => {
  return axios({
    url: "/gemp-user/api/config/refresh/v1",
    method: "post",
    data: data,
  });
};

export const findAddressInfo = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/district/geocoder/v1",
    method: "post",
    data: data,
  });
};

// 查询所有监管机构
export const getOrgTree = (data) => {
  return axios({
    url: "/gemp-user/api/gemp/user/org/trees/org/v1",
    method: "post",
    data: data,
  });
};

