import axios from "axios";
import qs from "qs";
// 企业资料管理----安全信息

/**
 * 安全生产管理机构
personType=safetyOrg

企业负责人
personType=person

安全管理人员
personType=safety

特种作业人员
personType=special

特种设备作业人员
personType=equip */
//安全管理机构列表
export function getSafetOrgList(data) {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/tbBasePersonRecord/page/v1",
    data: data,
  });
}
// 查看机构详情
///
export const detailSafetOrgList = (id) => {
  return axios({
    method: "post",
   
    url: "/gemp-chemical/api/gemp/tbBasePersonRecord/id/v1?id=" + id,
  });
};
// 安全信息记录
  //安全管理人员 type=safety
        //特种作业人员 type=special
        //特种设备作业人员 type=equip
export const getSafetyInformation = (type) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/tbBasePersonRecord/findList/v1?type=" + type,
  });
};

// 获取企业安全信息
export const getCompanySafetyInformation = (data) => {
  return axios({
    method: "post",
    url: "/#",
    data: data,
  });
};