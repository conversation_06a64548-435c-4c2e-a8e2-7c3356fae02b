<template>
  <div class="enterprise">
    <entList v-if="status === 0"></entList>
    <entFill v-if="status === 1"></entFill>
    <entLog v-if="status === 2"></entLog>
  </div>
</template>

<script>
import entList from "./enterpriseList";
import entFill from "./enterpriseFill";
import entLog from "./enterpriseLog";
export default {
  name:"enterprise",
  //import引入的组件
  components: { entList, entFill ,entLog},
  data() {
    return {
      status: 0,
    };
  },
  created() {
    this.watchRoute();
  },
  //方法集合
  methods: {
    //监听路由
    watchRoute() {
      if (this.$route.name == "enterprise") {
        this.status = 0;
      } else if (this.$route.name == "enterpriseFill") {
        this.status = 1;
      } else {
        this.status = 2;
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    console.log(this.$route);
  },
  watch: {
    $route(newVal, oldVal) {
      if (newVal.name == "enterprise") {
        this.status = 0;
      } else if (newVal.name == "enterpriseFill") {
        this.status = 1;
      } else {
        this.status = 2;
      }
    },
  },
};
</script>
<style lang="scss" scoped></style>
