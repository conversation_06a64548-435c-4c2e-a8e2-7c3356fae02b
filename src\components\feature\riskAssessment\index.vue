<template>
  <div class="workingaccount">
    <component
      :is="$route.name == 'riskAssessment' ? 'iotMontoringAlarm' : $route.name"
    ></component>
  </div>
</template>

<script>
import iotMontoringAlarm from "./iotMontoringAlarm/index";
import riskEarlyWarningPush from "./riskEarlyWarningPush/index";
import videoOnlineMonitoring from "./videoOnlineMonitoring/index";
import realtimeMonitor from "./realtimeMonitor/entAndGov.vue";
import realtimeVideo from "./realtimeVideo/entAndGov.vue";
import powerMonitoring from "./powerMonitoring/powerMonitoring.vue"; //电力监控
import riskDynamic from "./riskDynamic/riskDynamic.vue"; //风险动态研判
import monitorWarnReport from "./monitorWarnReport/monitorWarnReport.vue"; //监测预警报告
import videoAnalysis from "./videoAnalysis/videoAnalysis.vue"; //视频智能分析
import linkSafetyEducation from "./linkSafetyEducation/linkSafetyEducation.vue"; //安全教育 linkSafetyEducation
import linkIndustryTrade from "./linkIndustryTrade/linkIndustryTrade.vue";
import rankLink from "./rankLink/rankLink.vue";
import IOTmonitoring from "./IOTmonitoring/IOTmonitoring.vue"; //监管端-实时物联监测
import personnelPositioning from "./personnelPositioning/personnelPositioning.vue"; //人员定位
import accidentManagement from "./accidentManagement/accidentManagement.vue"; //事故管理
import preventionAnalysis from "./preventionAnalysis/index";
import licenseInformation from "./licenseInformation/index";
import safetyCommitment from "../workingAccount/safetyCommitment/index";
import specialWork from "../dailySafetyManagement/specialWork/index";
import lawEnforcementData from "./lawEnforcementData/index";
import enterpEducation from "@/components/feature/dailySafetyManagement/enterpEducation";

export default {
  //import引入的组件
  components: {
    iotMontoringAlarm,
    riskEarlyWarningPush,
    videoOnlineMonitoring,
    realtimeMonitor,
    realtimeVideo,
    powerMonitoring,
    riskDynamic,
    monitorWarnReport,
    videoAnalysis,
    linkSafetyEducation,
    linkIndustryTrade,
    rankLink,
    IOTmonitoring,
    personnelPositioning,
    accidentManagement,
    preventionAnalysis,
    licenseInformation,
    safetyCommitment,
    specialWork,
    lawEnforcementData,
    enterpEducation,
  },
  data() {
    return {};
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
