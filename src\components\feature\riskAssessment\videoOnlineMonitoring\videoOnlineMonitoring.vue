<template>
  <div class="enterpriseManagement">
    <div>
      <!-- <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box" @click="goToRunning">
                <a-icon type="home" theme="filled" class="icon" /> 监测联网分析
              </span>
            </a-breadcrumb-item>

          </a-breadcrumb>
        </div>
      </div> -->

      <div class="lot">
        <div class="right">
          <div>
            <div class="title">接入企业数</div>
            <div class="num">
              {{ videoRunningData.linkedEnterpCount }}
            </div>
          </div>
          <div>
            <div class="title">在线企业数</div>
            <div class="num">
              {{ videoRunningData.onlineEnterpCount }}
            </div>
          </div>
          <div>
            <div class="title">离线企业数</div>
            <div class="num">
              {{
                videoRunningData.linkedEnterpCount -
                videoRunningData.onlineEnterpCount
              }}
            </div>
          </div>
          <div>
            <div class="title">企业在线率</div>
            <div class="num1">{{ videoRunningData.onlineEnterpRate }}</div>
          </div>
          <div>
            <div class="title">监控点位数</div>
            <div class="num1">{{ videoRunningData.allTargetCount }}</div>
          </div>
          <div>
            <div class="title">在线质量</div>
            <div class="num1">
              {{ videoRunningData.onlineTargetRate }}
            </div>
          </div>
        </div>
      </div>
      <div class="videoInspection">
        <div class="video-left">
          <div class="tabTit">
            <span
              @click="clickTab(1)"
              :class="[tabTitActive == 1 ? 'active' : '']"
              >实时视频</span
            >
            <span
              @click="clickTab(2)"
              :class="[tabTitActive == 2 ? 'active' : '']"
              >视频回放</span
            >
          </div>

          <div v-if="tabTitActive == 1">
            <div class="videoLeft-top">
              <div class="list-search">
                <el-input
                  v-model.trim="enterpName"
                  size="mini"
                  placeholder="请输入企业名称"
                  class="input"
                  clearable
                  style="width: 260px; margin-left: 10px"
                ></el-input>
                <el-button type="primary" size="mini" @click="search"
                  >查询</el-button
                >
              </div>
              <!-- {{newAllData}} -->
              <div class="video-list" v-loading="loading">
                <!-- <el-tree
                      :data="newAllData"
                      accordion
                      @node-click="onSelect"
                      icon-class="el-icon-caret-right"
                    >
                      <span
                        class="custom-tree-node"
                        :title="node.name"
                        slot-scope="{ node, data }"
                      >
                        <span>
                          <i
                            v-if="data.type === '1'"
                            class="el-icon-folder"
                          ></i>
                          <i
                            v-if="data.type === '2'"
                            class="el-icon-office-building"
                          ></i>
                          <i
                            v-if="data.type === '3'"
                            class="el-icon-video-camera"
                          ></i>
                          {{ node.name }}</span
                        >
                      </span>
                    </el-tree> -->
                <a-directory-tree
                  multiple
                  default-expand-all
                  @select="onSelect"
                  @expand="onExpand"
                  style="padding: 0 10px"
                >
                  <a-tree-node
                    :key="item.id + ',' + item.type"
                    v-for="item in newAllData"
                    :title="item.name"
                  >
                    <a-tree-node
                      v-if="item.children.length > 0"
                      :key="subItem.id + ',' + subItem.type"
                      v-for="subItem in item.children"
                      :title="subItem.name"
                    >
                      <a-tree-node
                        v-if="subItem.children.length > 0"
                        :key="subItems.id + ',' + subItems.type"
                        v-for="subItems in subItem.children"
                        :title="subItems.name"
                      >
                        <a-tree-node
                          v-if="subItems.children.length > 0"
                          :key="subItemd.id + ',' + subItemd.type"
                          v-for="subItemd in subItems.children"
                          :title="subItemd.name"
                        >
                          <a-tree-node
                            v-if="subItemd.children.length > 0"
                            :key="subItemed.id + ',' + subItemed.type"
                            v-for="subItemed in subItemd.children"
                            :title="subItemed.name"
                          ></a-tree-node>
                        </a-tree-node>
                      </a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-directory-tree>
              </div>
            </div>
          </div>

          <div v-if="tabTitActive == 2">
            <div class="videoLeft-top2">
              <div class="list-search">
                <el-input
                  v-model.trim="enterpName"
                  size="mini"
                  placeholder="请输入企业名称"
                  class="input"
                  clearable
                  style="width: 260px; margin-left: 10px"
                ></el-input>
                <el-button type="primary" size="mini" @click="search"
                  >查询</el-button
                >
              </div>
              <div class="video-list" v-loading="loading">
                <a-directory-tree
                  multiple
                  default-expand-all
                  @select="onSelectRecord"
                  @expand="onExpand"
                  style="padding: 0 10px"
                >
                  <a-tree-node
                    :key="item.id + ',' + item.type"
                    v-for="item in newAllData2"
                    :title="item.name"
                  >
                    <a-tree-node
                      v-if="item.children.length > 0"
                      :key="subItem.id + ',' + subItem.type"
                      v-for="subItem in item.children"
                      :title="subItem.name"
                    >
                      <a-tree-node
                        v-if="subItem.children.length > 0"
                        :key="subItems.id + ',' + subItems.type"
                        v-for="subItems in subItem.children"
                        :title="subItems.name"
                      >
                        <a-tree-node
                          v-if="subItems.children.length > 0"
                          :key="subItemd.id + ',' + subItemd.type"
                          v-for="subItemd in subItems.children"
                          :title="subItemd.name"
                        >
                          <a-tree-node
                            v-if="subItemd.children.length > 0"
                            :key="subItemed.id + ',' + subItemed.type"
                            v-for="subItemed in subItemd.children"
                            :title="subItemed.name"
                          ></a-tree-node>
                        </a-tree-node>
                      </a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-directory-tree>
              </div>
              <br />
              <el-form ref="form" :model="form" label-width="100px">
                <!-- <el-form-item label="监控点名称" prop="configKey">
                  <el-input
                    v-model.trim="form.name"
                    placeholder=""
                    disabled
                    size="small"
                  />
                </el-form-item> -->

                <el-form-item label="回放开始时间" prop="configKey">
                  <el-date-picker
                    v-model="form.startTime"
                    type="datetime"
                    placeholder="选择开始时间"
                    style="width: 100%"
                    size="small"
                    value-format="timestamp"
                    :clearable="false"
                  >
                  </el-date-picker>
                </el-form-item>

                <el-form-item label="回放结束时间" prop="configKey">
                  <el-date-picker
                    v-model="form.endTime"
                    type="datetime"
                    placeholder="选择结束时间"
                    style="width: 100%"
                    size="small"
                    value-format="timestamp"
                    :clearable="false"
                  >
                  </el-date-picker>
                </el-form-item>

                <div class="btnBox">
                  <el-button type="primary" size="mini" @click="getRtsp"
                    >查询</el-button
                  >
                </div>
              </el-form>
            </div>
          </div>
        </div>
        <div class="video-right">
          <!-- 实时视频 -->
          <div class="video-content" v-show="tabTitActive == 1">
            <div class="video-box" id="video-box">
              <div id="ws-real-player"></div>
            </div>
            <!-- <flvPlayer :url="cameraUrl"></flvPlayer> -->
          </div>
          <div v-show="tabTitActive == 2">
            <div class="video-box" id="video-box">
              <div id="ws-record-player"></div>
            </div>
          </div>
          <!-- 录像回放 -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { videoSatistics } from "@/api/workingAcc";
import { getOnlineVideoData, getRealmonitorNew } from "@/api/riskAssessment";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import WSPlayer from "@/utils/WSPlayer/WSPlayer";
import ICC from "@/utils/WSPlayer/icc";
import flvPlayer from "./flvPlayer.vue";
var dayjs = require("dayjs");
export default {
  components: { flvPlayer },
  data() {
    return {
      playbackChannelCode: "",
      playbackChannelName: "",
      form: {
        name: "",
        startTime: dayjs(dayjs().format("YYYY-MM-DD") + " 00:00").valueOf(),
        endTime: dayjs(dayjs().format("YYYY-MM-DD") + " 23:59").valueOf(),
        // startTime:1669910400,
        // endTime:1669996740
      },
      serverAdress: "",
      tabTitActive: "1",
      tableCheck: true,
      newAllData: [],
      newAllData2: [],
      enterpName: "",
      loading: true,
      cameraIndexCode: "",
      cameraUrl: null,
      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
      realPlayer: null,
      recordPlayer: null,
      rtspUrl2: "",
      recordSource: "2",
      loadingVideo: false,
      level: ["1", "2", "3", "4"],
      radio1: "0",
      distCode: this.$store.state.login.userDistCode,
      videoRunningData: {
        allTargetCount: 0,
        onlineTargetCount: 0,
        onlineTargetRate: "0%",
        allEnterpCount: 0,
        linkedEnterpCount: 0,
        onlineEnterpCount: 0,
        linkedEnterpRate: "0%",
        onlineEnterpRate: "0%",
      },
    };
  },
  methods: {
    //视频运行分析
    getVideoRunningList() {
      videoSatistics({
        level: this.level.join(","),
        current: 1,
        distCode: this.distCode,
        size: 10,
        type: this.radio1,
      }).then((res) => {
        if (res.data.data != null) {
          this.videoRunningData = res.data.data;
        } else {
          this.videoRunningData = {};
        }
      });
    },
    clickTab(val) {
      this.tabTitActive = val;
      if (val == 1) {
        if (!this.realPlayer) {
          this.realPlayer = new WSPlayer({
            el: "ws-real-player", // 必传
            type: "real", // real | record
            serverIp: this.serverAdress,
            num: 4,
            showControl: true,
          });
        }
      } else {
        if (!this.recordPlayer) {
          this.recordPlayer = new WSPlayer({
            el: "ws-record-player",
            type: "record", // real | record
            serverIp: this.serverAddress,
            num: 4,
            showControl: true, // 默认是否显示工具栏
          });
        }
      }
    },
    //视频运行列表
    getOnlineVideoDataList() {
      this.loading = true;
      getOnlineVideoData({
        enterpName: this.enterpName,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.newAllData = res.data.data;
          this.newAllData2 = res.data.data;
        }
      });
    },

    goToRunning() {},
    onSelect(selectedKeys, info) {
      if (selectedKeys[0].split(",")[1] == "3") {
        this.cameraIndexCode = selectedKeys[0].split(",")[0];
        // this.cameraUrl =
        //   "http://***********:6060/sms/42010000002020000001/flv/hls/42010000002000000701_" +
        //   this.cameraIndexCode +
        //   ".flv";
        // this.yulan(this.cameraIndexCode)
        this.realPlayNew(this.cameraIndexCode, "名称");
      }
    },
    //录像回放
    onSelectRecord(selectedKeys, info) {
      if (selectedKeys[0].split(",")[1] == "3") {
        this.playbackChannelCode = selectedKeys[0].split(",")[0];
        //this.playbackChannelCode='1000542@045@906$1$0$0'
        this.playbackChannelName = "名称";
      }
    },
    getRtsp() {
      if (!this.playbackChannelCode) {
        this.$message.error("请先选择视频通道");
        return;
      }

      var queryPromise = null;
      this.recordSource = "2";
      let opt = {
        channelId: this.playbackChannelCode,
        startTime: this.form.startTime,
        endTime: this.form.endTime,
        recordSource: "2",
      };
      if (this.recordSource == "2") {
        // 存储在设备上的录像，根据时间播放
        queryPromise = ICC.getRecordRtspByTime(opt);
      }
      queryPromise
        .then((data) => {
          this.rtspUrl2 = data.rtspUrl;
          if (!data.rtspUrl) {
            alert("所选设备未查询到录像文件");
            return;
          }
          // 查询到录像之后立即初始化播放器，正常情况进入暂停状态
          this.recordPlay(this.playbackChannelCode, this.playbackChannelName);
        })
        .catch((err) => {
          console.log(err);
          return;
        });
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId
     */
    realPlayNew(channelId, channelName) {
      ICC.getRealmonitor({
        channelId: channelId,
        // dataType: '3', //视频类型：1=视频, 2=音频, 3=音视频
        streamType: "2", //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
      }).then((data) => {
        this.realPlayer.playReal({
          rtspURL: data.rtspUrl, // string | array[string]
          decodeMode: "canvas", // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
          channelId: channelId, // 可选，用来标记当前视频播放的通道id
          channelName: channelName,
        });
      });
    },
    //播放录像
    recordPlay(channelId, channelName) {
      if (this.recordSource === "2") {
        // 存储在设备上的录像，根据时间播放
        this.recordPlayer.playRecord({
          rtspURL: this.rtspUrl2,
          decodeMode: "canvas",
          startTime: this.form.startTime,
          endTime: this.form.endTime,
          channelId,
          channelName,
          // 设置时间偏移量之后重新获取rtsp地址
          setTimeOffset: function (offsetTime) {},
        });
      } else if (this.recordSource === "3") {
      }
    },

    onExpand(val) {},
    search() {
      this.getOnlineVideoDataList();
    },

    // 推送消息
    cbIntegrationCallBack(oData) {
      // window.showCBInfo(JSON.stringify(oData.responseMsg));
    },

    //获取公钥
    getPubKey(callback) {
      const that = this;
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024,
          }),
        })
        .then(function (oData) {
          console.log(oData);
          if (oData.responseMsg.data) {
            that.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },

    //RSA加密
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },
  },
  async created() {
    this.getVideoRunningList();
    await ICC.init();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getOnlineVideoDataList();
    this.videoWidth = $("#video-box").width();
    this.videoHight = $("#video-box").height();
    this.serverAdress = sessionStorage.getItem("videoApi");

    if (!this.realPlayer) {
      this.realPlayer = new WSPlayer({
        el: "ws-real-player", // 必传
        type: "real", // real | record
        serverIp: this.serverAdress,
        num: 4,
        showControl: true,
      });
    }
    if (!this.recordPlayer) {
      this.recordPlayer = new WSPlayer({
        el: "ws-record-player",
        type: "record", // real | record
        serverIp: this.serverAdress,
        num: 4,
        showControl: true, // 默认是否显示工具栏
      });
    }
  },
  destroyed() {
    // this.unloadVideo();
    console.log("销毁视频组件~");
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-form-item {
  margin-top: 0;
  margin-bottom: 0;
}
.btnBox {
  display: flex;
  justify-content: center;
  margin-top: 3px;
}
.tabTit {
  padding: 0;
  position: relative;
  margin: 0 0 15px;
  border-bottom: 2px solid #e4e7ed;
  span {
    margin: 0 20px;
    height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    position: relative;
    cursor: pointer;
    padding: 0 20px;
  }
  span.active {
    background: #daefff;
    color: #409eff;
  }
  span.active:before {
    border-bottom: 2px solid #409eff;
    position: absolute;
    bottom: -2px;
    content: "";
    width: 100%;
    left: 0;
  }
  span:first-child {
    margin-left: 0;
  }
}
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
    .vedioBox {
      display: flex;
      margin-bottom: 10px;
      div:last-child {
        margin: 0 0 0 10px;
      }
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    display: flex;
    box-sizing: border-box;
    .video-left {
      // float: left;
      width: 345px;
      margin-right: 25px;
      margin-top: 15px;
      .videoLeft-top {
        background: #daefff;
        .list-search {
          padding-top: 15px;
        }
        .video-list {
          height: calc(100vh - 250px);
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
      .videoLeft-top2 {
        .list-search {
          padding-top: 15px;
          background: #daefff;
        }
        .video-list {
          background: #daefff;
          height: calc(100vh - 430px);
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
    }
    .video-right {
      float: left;
      position: relative;
      width: calc(100% - 370px);
      .video-box {
        margin-top: 15px;
        border: 1px solid #ddd;
        height: calc(100vh - 150px);
        #ws-real-player,
        #ws-record-player {
          width: 100%;
          height: 100%;
        }
      }

      .video-box {
        border: 1px solid #ddd;
        height: calc(100vh - 150px);
        #ws-real-player,
        #ws-record-player {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}

.lot {
  display: flex;
  overflow: hidden;
  justify-content: center;
  background: #daefff;

  .right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 800px;
    height: 80px;
    text-align: center;
    > div {
      border-right: 1px #86a5e7 solid;
      height: 52px;
      width: 160px;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      align-items: center;
      margin-top: 10px;
    }

    > div:nth-last-of-type(1) {
      border-right: 0;
    }
    .title {
      color: #545c65;
      font-size: 14px;
    }
    .num {
      font-size: 20px;
      color: #31539b;
      font-weight: 600;
      // cursor: pointer;
    }
    .num1 {
      font-size: 20px;
      color: #31539b;
      font-weight: 600;
    }
  }
}
</style>
