<template>
    <el-dialog :title="title" :visible.sync="visible" append-to-body top="10vh" width="1000px"
        :close-on-click-modal="false" @close="closeDialog">
        <div class="dialog-box">
            <div class="operation">
                <span class="label">区域名称：</span>
                <el-cascader size="small" placeholder="请选择行政区划" :options="district" v-model="searchParams.distCode"
                    :props="distOptions" clearable :show-all-levels="true" style="width: 220px"></el-cascader>
                <span class="label">企业名称：</span>
                <el-input size="small" v-model="searchParams.enterpName" placeholder="请输入企业名称"></el-input>
            </div>
            <div class="table">
                <el-table :data="tableData" v-loading="dialogLoading" :header-cell-style="headerCellStyle" border
                    height="100%" style="width: 100%">
                    <el-table-column label="序号" type="index" width="60"></el-table-column>
                    <el-table-column label="单位名称" prop="enterpName">
                        <template slot-scope="{ row }">
                            <el-button type="text">{{ row.enterpName }}</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column label="行政区划" prop="districtName"></el-table-column>
                    <el-table-column label="报警次数" prop="warnNumber" width="150px"></el-table-column>
                    <el-table-column label="信号丢失" prop="signalNumber" width="100px"></el-table-column>
                    <el-table-column label="场景改变" prop="senceNumber" width="100px"></el-table-column>
                    <el-table-column label="视频遮挡" prop="videoBlock" width="100px"></el-table-column>
                    <el-table-column label="视频抖动" prop="videoShake" width="100px"></el-table-column>
                    <el-table-column label="操作" width="120px">
                        <template slot-scope="{ row }">
                            <el-button type="text" @click="handleSend(row.id)">提醒</el-button>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination">
                <el-pagination :current-page.sync="searchParams.nowPage" :page-size="searchParams.pageSize"
                    :total="total" background layout="total, prev, pager, next"
                    @current-change="handleCurrentChange"></el-pagination>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: "武汉市视频问题企业",
        },
        id: {
            type: String,
            default: "",
        },
    },

    data() {
        return {
            dialogLoading: false,
            headerCellStyle: {
                background: '#F1F6FF', color: '#333', textAlign: 'center',
                backgroundColor: 'rgb(242, 246, 255)'
            },
            distOptions: {
                checkStrictly: true,
                value: 'distCode',
                label: 'distName',
                children: 'children',
                emitPath: false,
            },
            searchParams: {
                distCode: this.$store.state.login.userDistCode,
                enterpName: '',
                nowPage: 1,
                pageSize: 10,
            },
            total: 0,
            tableData: [
                { districtName: '武汉市', enterpName: 'ssssssss', districtCode: '110000', entNumber: 20, warnNumber: 10, signalNumber: 5, senceNumber: 5, videoBlock: 5, videoShake: 5 }
            ],
        }
    },
    computed: {
        ...mapStateControler({
            district: (state) => state.district,
        }),
    },
    created() {

    },
    methods: {
        closeDialog() {
            this.$emit("close");
        },
        handleCurrentChange(val) {
            this.searchParams.nowPage = val;
            this.getData();
        },
    },

}
</script>
<style lang="scss" scoped>
.dialog-box {
    .operation {
        display: flex;
        align-items: center;
    }

    .table {
        height: 400px;
        margin: 10px 0;
    }

    .pagination {
        text-align: right;

    }
}

.el-input {
    width: 240px;
}
</style>