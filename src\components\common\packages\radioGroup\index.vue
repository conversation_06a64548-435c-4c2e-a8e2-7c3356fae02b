
<template>
  <div
    class="CA-RadioGroup"
    :style="`background-color:${backgroundColor};border:${border};`"
  >
    <slot></slot>
  </div>
</template>

<script>
export default {
  //import引入的组件
  name: "CARadioGroup",
  components: {},
  data() {
    return {};
  },
  props: {
    value: {
      type: String,
    },
    backgroundColor: {
      type: String,
      default: "#F1F6FF",
    },
    border: {
      type: String,
      default: "1px solid rgba(57, 119, 234, 0.2)",
    },
  },
  //方法集合
  methods: {
    dispatch(value) {
      // 调用所有子组件的checkIsActive方法
      this.$children.forEach((item) => {
        const temp = item;
        temp.checkIsActive(value);
      });
    },
    radioChange(value) {
      this.$emit("input", value);
      this.dispatch(value);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    if (this.value) {
      this.dispatch(this.value);
    }
    this.$on("radioChange", this.radioChange);
  },
  watch: {
      value(newValue, oldValue){
          if (newValue) {
            this.dispatch(newValue);
          }
          this.$on("radioChange", this.radioChange);
      }
  }
};
</script>
<style scoped lang="scss">
.CA-RadioGroup {
  background-color: #f1f6ff;
  display: flex;
  align-items: center;
  border-radius: 30px;
  padding: 1px 2px;
  border: 1px solid rgba(57, 119, 234, 0.2);
  line-height: 1;
  margin-bottom: 10px;
  width: 72.4px;
  height: 38px;
}
</style>