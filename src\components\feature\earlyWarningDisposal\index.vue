<template>
  <div class="workingaccount">
    <component
      :is="
        $route.name == 'earlyWarningDisposal' ? 'videoAnalysis' : $route.name
      "
    ></component>
  </div>
</template>

<script>
import videoAnalysis from "../riskAssessment/videoAnalysis/videoAnalysis.vue";
import iotMontoringAlarm from "../riskAssessment/iotMontoringAlarm/index.vue";
import videoQualityMonitoring from "./videoQualityMonitoring/index.vue";
import lightningWarning from "./lightningWarning/index.vue";
import riskEarlyWarningPush from "../riskAssessment/riskEarlyWarningPush/index.vue";
import hiddenRisk from "./hiddenRisk/index.vue";
import warningMessage from "./warningMessage/index.vue";

export default {
  //import引入的组件
  components: {
    videoAnalysis,
    iotMontoringAlarm,
    videoQualityMonitoring,
    lightningWarning,
    riskEarlyWarningPush,
    hiddenRisk,
    warningMessage,
  },
  data() {
    return {};
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
