<!-- 画像列表 -->
<template>
    <div class="portraitList">
        <div class="operation">
            <div class="inputBox">
                <el-cascader size="small" placeholder="请选择行政区划" :options="district" v-model="searchParams.distCode"
                    :props="{
                        checkStrictly: true,
                        value: 'distCode',
                        label: 'distName',
                        children: 'children',
                        emitPath: false,
                    }" clearable :show-all-levels="true" style="width: 220px"></el-cascader>
                <el-select v-model="searchParams.enterpriseType" size="small" placeholder="请选择企业类型" :clearable="true">
                    <el-option v-for="item in entType" :key="item.id" :label="item.enterpriseType" :value="item.id">
                    </el-option>
                </el-select>
                <el-select v-model="searchParams.level" size="small" placeholder="请选择重大危险源企业" :clearable="true" multiple
                    style="width: 300px">
                    <el-option v-for="item in levelOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <el-select v-model="searchParams.auditStatus" size="small" placeholder="请选择审核状态" :clearable="true"
                    style="width: 180px">
                    <el-option v-for="item in auditStatusList" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
                <el-input v-model.trim="searchParams.enterpName" size="small" placeholder="请输入企业名称" class="input"
                    clearable></el-input>
                <el-select v-model="searchParams.processIds" placeholder="请选择工艺" size="small" :clearable="true"
                    style="width: 200px">
                    <el-option v-for="item in processIdsOption" :key="item.processid" :label="item.processname"
                        :value="item.processid">
                    </el-option>
                </el-select>

                <!-- <div class="item">
                    <el-input v-model.trim="searchParams.hazChemName" style="width: 300px" size="small"
                        placeholder="请输入危化品关键字" class="input" clearable></el-input>
                </div> -->

                <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
            </div>

        </div>
        <div class="table" v-loading="loading">
            <el-table :data="tableData" :header-cell-style="headerCellStyle" border
                style="width: 100%" ref="multipleTable">
                <el-table-column type="index" label="序号" width="55" align="center">
                </el-table-column>
                <el-table-column prop="enterpName" label="企业名称" width="340" align="center"
                    :show-overflow-tooltip="true">
                    <!-- <template slot-scope="scope">
                        <span style="color: rgb(57, 119, 234)" class="enterpName">
                            {{ scope.row.enterpName }}
                        </span>
                    </template> -->
                </el-table-column>
                <el-table-column prop="districtName" label="行政区划 " align="center" width="120"
                    :show-overflow-tooltip="true">

                </el-table-column>
                <el-table-column prop="enterpriseTypeName" label="企业类型" width="180" align="center">
                </el-table-column>
                <el-table-column prop="level" label="企业等级" align="center" min-width="100">
                    <template slot-scope="scope">
                        <span v-if="scope.row.level == 1">一级</span>
                        <span v-else-if="scope.row.level == 2">二级</span>
                        <span v-else-if="scope.row.level == 3">三级</span>
                        <span v-else-if="scope.row.level == 4">四级</span>
                        <span v-else></span>
                    </template>
                </el-table-column>
                <el-table-column prop="level" label="发布时间" align="center" width="160">
                </el-table-column>
                <el-table-column prop="level" label="人(Man)" align="center"></el-table-column>
                <!-- <el-table-column prop="level" label="物(Things)" align="center"></el-table-column>
                <el-table-column prop="level" label="管(Management)" align="center"></el-table-column>
                <el-table-column prop="level" label="环(Environment)" align="center"></el-table-column>
                <el-table-column prop="level" label="监(Supervision)" align="center"></el-table-column>
                <el-table-column prop="level" label="绩(Performance)" align="center"></el-table-column> -->
                <!-- <el-table-column prop="level" label="指标总分" align="center"></el-table-column>
                <el-table-column prop="level" label="指标趋势" align="center"></el-table-column> -->
                <el-table-column prop="address" label="操作" align="center" width="160" fixed="right">
                    <template slot-scope="scope">
                        <el-button @click="handleClick(scope.row)" icon="el-icon-office-building" type="text"
                            size="small">
                            指数分析
                        </el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="pagination">
            <el-pagination @current-change="handleCurrentChange" :current-page.sync="searchParams.current"
                :page-size="searchParams.size" layout="total, prev, pager, next" background :total="total">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {
    getEnterpriseList,
    getKnowledgeListData,
    enterpriseType,
} from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");

export default {
    components: { },
    data() {
        return {
            // district: this.$store.state.controler.district,
            headerCellStyle: { background: '#F1F6FF', color: '#333' },
            districtVal: this.$store.state.login.userDistCode,
            loading: false,
            searchParams: {
                current: 1,
                size: 10,
                distCode: this.$store.state.login.userDistCode,
                enterpName: "",
                enterpriseType: "",
                processIds: "",
                hazChemName: "",
                level: ["1", "2", "3", "4"],
                auditStatus: "",
            },
            total: 0,
            tableData: [],
            entType: [],
            processIdsOption: [],
            entTypeOption: [],
            auditStatusList: [
                { label: "审核通过", value: "1" },
                { label: "审核不通过", value: "2" },
                { label: "待审核", value: "3" },
            ],
            levelOptions: [
                { label: "一级", value: "1" },
                { label: "二级", value: "2" },
                { label: "三级", value: "3" },
                { label: "四级", value: "4" },
            ],
            showAnalysis: false,
            enterpItem:{}
        }
    },
    computed: {
        ...mapStateLogin({
            userDistCode: (state) => state.userDistCode,
        }),
        ...mapStateControler({
            district: (state) => state.district,
        }),
    },
    mounted() {
        this.getProcess();
        this.getEnterpriseType();
        // this.getData();
    },
    methods: {
        handleSearch() {
            this.searchParams.current = 1;
            this.getData();
        },
        //翻页
        handleCurrentChange(val) {
            this.searchParams.current = val;
            this.getData();
        },
        handleClick(row) {
            console.log(row);
            this.enterpItem = row
            this.showAnalysis = true;
        },
        closeBoolean(boolean) {
            this.showAnalysis = boolean;
            this.enterpItem = {}
        },
        // 获取工艺
        getProcess() {
            getKnowledgeListData({
                processid: "",
                processname: "",
            }).then((res) => {
                if (res.data.status === 200) {
                    //processIdsOption
                    this.processIdsOption = res.data.data;
                }
            });
        },
        // 获取企业类型
        getEnterpriseType() {
            enterpriseType({}).then((res) => {
                this.entType = res.data.data;
            });
        },
        async getData() {
            const params = { ...this.searchParams, level: this.searchParams.level.toString() }
            await getEnterpriseList(params).then((res) => {
                this.tableData = res.data.data.records;
                this.total = res.data.data.total;
                this.loading = false;
            });
        },
    }

}
</script>

<style lang="scss" scoped>
/deep/ .subTable.el-table td.el-table__cell,
/deep/ .subTable.el-table th.el-table__cell.is-leaf,
/deep/ .el-table--border .subTable.el-table__cell {
    border-bottom: 0;
    border-right: 0;
}

/deep/ .el-table--border .subTable {
    border-left: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    border-top: 1px solid #ebeef5;
}

.titTop {
    display: flex;
    justify-content: space-between;

    /deep/ .el-radio-group {
        margin: 0 0 0 0;
    }
}

.operation {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;

    .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;

        .input {
            width: 200px;
        }

        >* {
            margin-right: 15px;
        }
    }
}

.pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
}
</style>