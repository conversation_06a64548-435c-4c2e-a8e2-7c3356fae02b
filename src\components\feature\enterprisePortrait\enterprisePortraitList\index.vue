<!-- 企业画像 -->
<template>
   <div>
      <el-tabs v-model="activeName">
         <el-tab-pane label="画像列表" name="portaitList">
            <portaitList ref="portaitList"></portaitList>
         </el-tab-pane>
         <el-tab-pane label="画像统计" name="portaitStatistics">
            <portaitStatistics ref="portaitStatistics"></portaitStatistics>
         </el-tab-pane>
      </el-tabs>
   </div>
</template>

<script>
import portaitList from './portaitList.vue';
import portaitStatistics from './portaitStatistics.vue';

export default {
   components: {
      portaitList,
      portaitStatistics
   },
   data() {
      return {
         activeName: 'portaitList'
      }
   },
   methods: {
      handleClick(tab, event) {
         console.log(tab, event);
      }
   }

}
</script>

<style lang="scss" scoped></style>