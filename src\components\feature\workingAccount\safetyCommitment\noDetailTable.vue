<template>
  <div class="historyList">
    <el-dialog
      :title="titleName"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="1350px"
      top="10vh"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            
            :show-all-levels="true"
            v-if="isShowDist"
          ></el-cascader>
        
          <el-button type="primary" size="mini"  class="btn" @click="search">查询</el-button>
        
        </div>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
       
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="entName"
          label="单位名称"
          width="300"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span @click="goEnt(scope.row)" style="color: #3977ea; cursor: pointer">{{
              scope.row.entName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="areaName" label="区划" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <!-- <el-table-column
          prop="majorHazardLevel"
          label="重大危险源企业等级"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.majorHazardLevel == 1">一级重大危险源</span>
            <span v-if="scope.row.majorHazardLevel == 2">二级重大危险源</span>
            <span v-if="scope.row.majorHazardLevel == 3">三级重大危险源</span>
            <span v-if="scope.row.majorHazardLevel == 4">四级重大危险源</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="promiseFlag" label="是否承诺" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.promiseFlag == 1 ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="riskGrade" label="承诺风险等级" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.riskGrade == 1">高风险</span>
            <span v-if="scope.row.riskGrade == 2">较大风险</span>
            <span v-if="scope.row.riskGrade == 3">一般风险</span>
            <span v-if="scope.row.riskGrade == 4">低风险</span>
          </template>
        </el-table-column>
        <el-table-column prop="commiteDate" label="承诺时间" align="center">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import History from "./history"
import {
  getentArr,
  getSafetyPromiseExportPromiseDetailsToExcel,
  notPromiseEnterprises
} from "@/api/workingAcc";
import { getDistrictUser } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      rowData:{},
      show: false,
      //  districtLoading: false,
      type: "",
      currentPage: 1,
      entName: "",
      loading: true,
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      majorHazardLevel: "",
      district: [],
      distCode: "",
      tableData: [],
      total: 0,
      areaName: "",
      titleName: "",
      selection: [],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist
    }),
  },
  //方法集合
  methods: {
    closeBoolean(val) {
        this.currentPage = 1;
       this.majorHazardLevel="";
       this.entName=''
      this.show = val;
    },
    handleChange(value) {
      // if (value && value.length > 0) {
      //   this.distCode = value[value.length - 1];
      // }
    },
    goEnt(row) {
      // console.log(row);
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.entId);
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then((res) => {
               let child = res.data.data;
        // debugger;
        if (child.children.length > 0) {
          for (let j = 0; j < child.children.length; j++) {
            if (child.children[j].children.length > 0) {
              for (let z = 0; z < child.children[j].children.length; z++) {
                if (child.children[j].children[z].children.length < 1) {
                  //判断children的数组长度
                  child.children[j].children[z].children = undefined;
                }
              }
            } else {
              child.children[j].children = undefined;
            }
          }
        } else {
          child.children = undefined;
        }
        this.district = [child];
      });
    },
    getEntData(row,distCode) {
      // console.log(distCode);
      this.distCode = distCode;
      this.rowData=row
      this.loading = true;
      // this.type = type;
      // this.areaName = areaName;
      this.titleName="未承诺企业数"
      notPromiseEnterprises({
        distCode: this.distCode,
        // entName: this.entName,
        // majorHazardLevel: this.majorHazardLevel,
        // type: type,
        nowPage: this.currentPage,
        pageSize: 10,
        time:row.time
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].areaCode;
      }
    },
    exportExcel() {
     
      let list = [...this.selection];    
      getSafetyPromiseExportPromiseDetailsToExcel({
        distCode: this.distCode,
        entIdList: list.length<=1?null:list,
        type: 0,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "安全承诺趋势分析" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].entId;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    showDialog() {
      this.$refs.History.closeBoolean(true);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getEntData(this.rowData,this.distCode);
    },
    search() {
      this.currentPage = 1;
      this.getEntData(this.rowData,this.distCode);
    },
    clearDis() {
      this.distCode = "";
    },
    clearMa() {
      this.majorHazardLevel = "";
    },
    clearEntName() {
      this.entName = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.historyList {
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      // width: 700px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      >.btn{
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>