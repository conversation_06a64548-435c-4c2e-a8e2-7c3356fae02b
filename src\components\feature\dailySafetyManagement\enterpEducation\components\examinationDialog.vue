<template>
  <el-dialog :title="title" :visible="visible" @close="closeBoolean(false)" width="1000px" top="5vh"
    :close-on-click-modal="true">
    <div class="test-container">
      <el-steps :active="stepActive" align-center class="test-steps">
        <el-step title="基础信息" description=""></el-step>
        <el-step title="题库选择" description=""></el-step>
        <el-step title="分值标准" description=""></el-step>
        <el-step title="待进行" description=""></el-step>
      </el-steps>
      <component :is="currentComponent" :disabled="isView" v-model="textDetail" ref="current" :singleData.sync="singleData"
        :multipleData.sync="multipleData" :subjectList="subjectList" :examinationTypes="examinationTypes"></component>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button type="primary" v-if="stepActive > 0" size="small" @click="handlePre">上一步</el-button>
      <el-button type="primary" v-if="stepActive < 3" size="small" @click="handleNext">下一步</el-button>
      <el-button type="primary" v-if="!isView" size="small" @click="handleSubmit">保存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import testBaseInfo from "./testBaseInfo.vue";
import questionBank from "./questionBank.vue";
import scoreStandard from "./scoreStandard.vue";
import topicInfo from "./topicInfo.vue";
import { examinationAdd, examinationDetail,findByLabelName,findByCourseNo } from "@/api/enterpEducation";
export default {
  components: {
    testBaseInfo,
    questionBank,
    scoreStandard,
    topicInfo,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    educationItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
    dialogType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      title: "新增考试",
      stepActive: 0,
      currentComponent: "testBaseInfo",
      isView: false, //是否查看
      // 考试详情
      textDetail: {
        "examCourse": "",
        "examName": "",
        "examTime": "",
        "excellentNumMax": 0,
        "excellentNumMin": 0,
        "failNumMax": 0,
        "failNumMin": 0,
        "fractionCount": 0,
        "goodNumMax": 0,
        "goodNumMin": 0,
        "ids": [],
        "labelName": "",
        "multipleCount": 0,
        "multipleFraction": 0,
        "multipleNum": 0,
        "passNumMax": 0,
        "passNumMin": 0,
        "qrCode": "",
        "singleCount": 0,
        "singleFraction": 0,
        "singleNum": 0,
        "status": 0,
        "topicType": [],
      },
      multipleData: [],
      singleData: [],
      examinationTypes:[],
      subjectList:[] //单选
    };
  },
  created() {
    if (this.dialogType == "edit") {
      this.title = "修改考试";
      this.isView = true;
      this.getDetail()
    } else if (this.dialogType == "view") {
      this.title = "查看考试";
      this.isView = true;
      this.getDetail()
    } else {
      this.title = "新增考试";
    }
    this.getLabelName();
    this.getCourseNo();
  },
  methods: {
    // 上一页
    handlePre() {
      if (this.stepActive == 0) return;
      this.stepActive--;
      this.switchComponent(this.stepActive);
    },
    // 下一页
    async handleNext() {
      if (this.stepActive == 3) return;
      const isValid = await this.$refs.current.updateForm(); //校验表单
      if (!isValid) return;
      this.stepActive++;
      this.switchComponent(this.stepActive);
      
    },
    // 路由切换
    switchComponent(val) {
      if (val == 0) {
        this.currentComponent = "testBaseInfo";
      } else if (val == 1) {
        this.currentComponent = "questionBank";
      } else if (val == 2) {
        this.currentComponent = "scoreStandard";
      } else if (val == 3) {
        this.currentComponent = "topicInfo";
      }
    },
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    // 提交
    async handleSubmit() {
      const isValid = await this.$refs.current.updateForm(); //校验表单
      if (!isValid) return;
      const params = {
        ...this.textDetail,
        topicType: this.textDetail.topicType.join(","),
      }
      await examinationAdd(params).then(res => {
        if (res.data.status == 200) {
          this.$emit('refresh')
        }
      })

    },
    // 获取详情
    getDetail() {
      examinationDetail(this.educationItem.id).then(res => {
        if (res.data.status == 200) {
          const { multipleMap, singleMap, examination } = res.data.data
          this.textDetail = {
            ...examination,
            topicType: examination.topicType.split("&"),
          }
          this.multipleData = multipleMap;
          this.singleData = singleMap;
        }
      })
    },
    // 获取考试类型
    getLabelName(){
      findByLabelName().then((res) => {
        if(res.data.status == '200'){
          this.examinationTypes = res.data.data;
        }
      });
    },
    // 获取考试课程
    getCourseNo(){
      findByCourseNo().then((res) => {
        if(res.data.status == '200'){
          this.subjectList = res.data.data;
        }
      });
    }
  },
};
</script>

<style lang="scss" scoped>
.test-container {
  .dialog-title {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .test-steps {}
}

.footer {
  text-align: center;
  padding-top: 10px;
}
</style>
