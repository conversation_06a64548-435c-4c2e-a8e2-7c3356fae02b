<template>
  <div class="historyPatrolCheck">
    <div class="header">
      <el-date-picker v-model="dateVal"
                      size="mini"
                      type="daterange"
                      value-format="yyyy-MM-dd"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="searchTime"
                      unlink-panels
                      style="width: 370px"
                      :default-time="['00:00:00', '23:59:59']"
                      :clearable="false"
                      :picker-options="{
          disabledDate: (time) => {
              return time.getTime() >new Date(new Date().Format('yyyy-MM-dd 23:59:59')).getTime();
          },
        }"></el-date-picker>
      <el-select v-model="inspectStatus"
                 placeholder="请选择状态"
                 size="mini"
                 style="width: 190px"
                 clearable
                 v-show="this.$store.state.login.user.user_type === 'gov'">
        <el-option v-for="(item, index) in options"
                   :key="index"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
      <el-cascader size="mini"
                   placeholder="请选择发起巡查地市/区县"
                   :options="districts"
                   v-model="distCode"
                   :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
                   clearable
                   @change="handleChange"
                   ref="myCascader"
                   :show-all-levels="true"
                   style="width: 190px; margin-top: 10px"></el-cascader>
      <el-button type="primary"
                 style="margin-top: 10px"
                 size="mini"
                 @click="getList()">查询</el-button>
      <CA-button type="primary"
                 style="margin-top: 10px"
                 plain
                 size="mini"
                 @click="exportList()">导出</CA-button>
      <!-- <el-button type="primary"
                 style="margin-top: 10px;float: right;margin-right: 0;"
                 size="mini"
                 @click="automaticIssue()">一键自动下发</el-button> -->
    </div>
    <div class="container"
         v-loading="loading">
      <!-- <div class="title">历史巡查记录</div> -->
      <div>
        <el-table :data="tableData"
                  border
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                  @select="select"
                  @select-all="select"
                  @selection-change="handleSelectionChange">
          <el-table-column type="selection"
                           fixed="left"></el-table-column>
          <!-- <el-table-column type="index" label="序号" width="60">
          </el-table-column> -->
          <el-table-column prop="areaName"
                           label="区划"
                           width="150"
                           fixed="left">
          </el-table-column>
          <el-table-column label="系统在线情况">
            <el-table-column prop="onlineRate"
                             label="在线率">
              <template slot-scope="{ row }">{{ row.onlineRate | formatRate }}
              </template>
            </el-table-column>
            <el-table-column prop="onlineSort"
                             label="排名"></el-table-column>
          </el-table-column>
          <el-table-column label="视频监控在线情况">
            <el-table-column prop="videoOnlineRate"
                             label="在线率">
              <template slot-scope="{ row }">{{ row.videoOnlineRate | formatRate }}
              </template>
            </el-table-column>
            <el-table-column prop="videoOnlineSort"
                             label="排名">
            </el-table-column>
          </el-table-column>
          <el-table-column prop="address"
                           label="安全承诺情况">
            <el-table-column prop="promiseRate"
                             label="承诺率">
              <template slot-scope="{ row }">{{ row.promiseRate | formatRate }}
              </template>
            </el-table-column>
            <el-table-column prop="promiseSort"
                             label="排名"> </el-table-column>
          </el-table-column>
          <el-table-column prop="address"
                           label="超24小时未消警指标数">
            <el-table-column prop="latestAlarmTargetCount"
                             label="数量">
            </el-table-column>
            <el-table-column prop="latestAlarmTargetInterval"
                             label="最大持续时长"
                             width="170">
            </el-table-column>
          </el-table-column>
          <el-table-column prop="address"
                           label="预警及通报处置情况">
            <el-table-column prop="warnCount"
                             label="预警未消警"
                             width="120">
            </el-table-column>
            <el-table-column prop="warnNoRespCount"
                             label="预警未反馈"
                             width="120">
            </el-table-column>
            <el-table-column prop="warnNoticeNoRespCount"
                             label="通报未反馈"
                             width="120">
            </el-table-column>
          </el-table-column>
          <el-table-column prop="otherContent"
                           label="其他情况">
          </el-table-column>
          <el-table-column prop="createBy"
                           label="巡查人"
                           width="150">
          </el-table-column>
          <el-table-column prop="genAreaName"
                           label="所属区划"
                           width="150">
          </el-table-column>
          <el-table-column prop="inspectDate"
                           label="巡查时间"
                           width="170">
          </el-table-column>
          <el-table-column prop="inspectStatus"
                           label="反馈状态"
                           width="150">
            <template slot-scope="{ row }">
              <!-- 1已生成2已下发未反馈3已反馈 -->
              <span v-if="row.inspectStatus == 1">未下发</span>
              <span v-if="row.inspectStatus == 2">已下发,待反馈</span>
              <span v-if="row.inspectStatus == 3">已反馈</span>
            </template>
          </el-table-column>
          <el-table-column prop="feedbackTime"
                           label="反馈时间"
                           width="170">
          </el-table-column>
          <el-table-column label="操作"
                           width="180px"
                           fixed="right">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="addSpotCheck(scope.row)"
                         
                         v-if="scope.row.inspectStatus == 1&&$store.state.login.user.isDanger == '1'">下发</el-button>
              <el-button type="text"
                         @click="delSpotCheck(scope.row)"
                         
                         v-if="scope.row.inspectStatus == 1 &&$store.state.login.user.isDanger == '1'">删除</el-button>
                         <!--  -->
              <el-button type="text"
                         :disabled='scope.row.inspectStatus != 3'
                         @click="addSpotCheckDetails(scope.row)"
                         >反馈详情</el-button>
              <!-- <el-button type="text"
                         v-if="scope.row.inspectStatus == 2"
                         disabled>反馈详情</el-button> -->
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="">
      <el-dialog title="下发巡查情况"
                 :visible.sync="showSpotCheck"
                 width="740px"
                 :close-on-click-modal="false"
                 v-dialog-drag
                 @close="otherContent = ''">
        <el-descriptions :column="1"
                         border
                         :labelStyle="labelStyle">
          <el-descriptions-item>
            <template slot="label"> 其他情况说明 </template>
            <el-input type="textarea"
                      placeholder="最多可输入500字"
                      maxlength="500"
                      show-word-limit
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      resize="none"
                      v-model.trim="otherContent">
            </el-input>
          </el-descriptions-item>
        </el-descriptions>
        <div style="width: 100%; height: 40px">
          <el-button type="primary"
                     style="float: right; margin-top: 10px"
                     @click="submitSpotCheck()"
                     :loading="submitSpotCheckDisplay">提交</el-button>
        </div>
      </el-dialog>
      <el-dialog title="反馈详情"
                 :visible.sync="showSpotCheckDetails"
                 width="900px"
                 :close-on-click-modal="false"
                 v-dialog-drag>
        <el-tabs v-model="activeName">
          <el-tab-pane label="系统在线情况"
                       name="0"></el-tab-pane>
          <el-tab-pane label="视频在线情况"
                       name="1"></el-tab-pane>
          <el-tab-pane label="安全承诺情况" name="2"></el-tab-pane>
          <el-tab-pane label="未消警情况"
                       name="3"></el-tab-pane>
          <el-tab-pane label="预警处置及督办情况"
                       name="4"></el-tab-pane>
          <el-tab-pane label="其他情况"
                       name="5"></el-tab-pane>
        </el-tabs>
        <div class="heightCon">

        
        <el-descriptions :column="1"
                         border
                         :labelStyle="labelStyle">
          <el-descriptions-item>
            <template slot="label"> 情况说明 </template>
            <div class="feekBackItem"
                 v-if="activeName == 5">
              {{ feedBackDetails[5].situation }}
            </div>
            <div class="feekBackItem"
                 v-else>
              {{ feedBackDetails[activeName].situation }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 处置措施 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeName].measure }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 补充信息 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeName].otherInfo }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 预计完成时间 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeName].planDate }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期说明 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeName].delayInfo }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期完成时间 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeName].delayDate }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 完成状态 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeName].isFinish == "0" ? "未完成" : "已完成" }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
        </div>
      </el-dialog>
      <el-dialog title="下发巡查情况"
                 :visible.sync="oneShowSpotCheck"
                 :close-on-click-modal="false"
                 width="740px"
                 v-dialog-drag>
        <el-table :data="issueData"
                  border
                  style="width: 100%"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }">
          <el-table-column prop="areaName"
                           label="区划"
                           width="180">
          </el-table-column>
          <el-table-column prop="otherContent"
                           label="其他情况说明">
            <template slot-scope="{ row }">
              <el-input type="textarea"
                        placeholder="最多可输入500字"
                        maxlength="500"
                        show-word-limit
                        :autosize="{ minRows: 4, maxRows: 8 }"
                        resize="none"
                        v-model.trim="row.otherContent">
              </el-input>
            </template>
          </el-table-column>
        </el-table>
        <div style="width: 100%; height: 40px">
          <el-button type="primary"
                     style="float: right; margin-top: 10px"
                     @click="automaticIssue()">提交</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="pagination">
      <el-pagination @current-change="handleCurrentChange"
                     :current-page.sync="currentPage"
                     background
                     layout="total, prev, pager, next"
                     :total="total"
                     :disabled="loading">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import {
  postOnlinePatrolInspectList,
  postOnlinePatrolIssue,
  postOnlinePatrolExport,
  postOnlinePatrolQueryFeedbackDetails
} from '@/api/riskAssessment'
import { getDistrictUser } from '@/api/entList'
import { createNamespacedHelpers, mapState } from 'vuex'
const { mapState: mapStateLogin } = createNamespacedHelpers('login')
const { mapState: mapStateControler } = createNamespacedHelpers('controler')
export default {
  name: 'historyPatrolCheck',
  data() {
    return {
      districts: [],
      distCode: '',
      startTime: '',
      endTime: '',
      showSpotCheck: false,
      oneShowSpotCheck: false,
      showSpotCheckDetails: false,
      showSpotCheckFeedback: false,
      checkList: [],
      activeName: '0',
      labelStyle: {
        textAlign: 'center',
        backgroundColor: 'rgb(242, 246, 255)'
      },
      tableData: [],
      options: [
        {
          value: '1',
          label: '未下发'
        },
        {
          value: '2',
          label: '已下发,待反馈'
        },
        {
          value: '3',
          label: '已反馈'
        }
      ],
      inspectStatus: '',
      loading: false,
      dateVal: [new Date(), new Date()],
      date: [new Date(), new Date()],
      total: 0,
      currentPage: 1,
      otherContent: '',
      submitSpotCheckDisplay: false,
      selection: [],
      feedBackDetails: [
        {
          inspectId: '', //巡查记录ID
          replyType: '1', //巡查记录反馈的类型	1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: '', //情况说明
          measure: '', //处置措施
          otherInfo: '', //补充信息
          delayInfo: '', //延期说明
          delayDate: '', //延期日期
          planDate: '', //计划完成日期
          isFinish: '', //是否已完成  0:未完成 1：已完成
          status: ''
        }
      ],
      issueData: []
    }
  },
  //注册局部过滤器
  filters: {
    formatRate: function(value) {
      return value.split('.')[0] + '%'
    }
  },
  mounted() {
    let nowDate = new Date().Format('yy-MM-dd')
    this.searchTime([nowDate, nowDate])
  },
  methods: {
    onceShowSpotCheck() {
      this.oneShowSpotCheck = true
      this.issueData = []
      if(this.$store.state.controler.automaticPatrolData.length>0){
        this.$store.state.controler.automaticPatrolData.forEach(item =>{
          this.issueData.push({
            inspectId:item.inspectId,
            areaName:item.areaName,
            otherContent:'',
          })
        })
      }
    },
    //一键自动下发
    automaticIssue() {
      postOnlinePatrolIssue({
        action: 0,
        issueReqs: this.issueData
      })
        .then(res => {
          if (res.data.code === 0) {
            this.$message({
              message: '下发成功',
              type: 'success'
            })
            this.oneShowSpotCheck = false;
            this.getList()
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    searchTime(value) {
      // console.log(value);
      if (value) {
        // let date1 = new Date(value[0]);
        // let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        // let date2 = new Date(value[1]);
        // let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.startTime = value[0] + ' 00:00:00'
        this.endTime = value[1] + ' 23:59:59'
      } else {
        this.dateVal = []
        this.startTime = ''
        this.endTime = ''
      }
    },
    handleSelectionChange(val) {
      console.log(val)
    },
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id
      }
    },
    exportList() {
      let params = {}
      if (this.user.distRole == '0') {
        params = {
          areaCode: this.distCode || '',
          startTime: this.startTime,
          endTime: this.endTime,
          inspectStatus: this.inspectStatus,
          nowPage: this.currentPage,
          pageSize: 10
        }
      } else {
        params = {
          areaCode: this.distCode || this.$store.state.login.userDistCode,
          startTime: this.startTime,
          endTime: this.endTime,
          inspectStatus: this.inspectStatus,
          nowPage: this.currentPage,
          pageSize: 10
        }
      }
      postOnlinePatrolExport(params).then(res => {
        console.log(res)
        if (res.status === 200) {
          this.$message.success('导出成功')
        } else {
          this.$message.error('导出失败')
        }
        const blob = new Blob([res.data], { type: 'application/xls' })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '历史巡查记录列表' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    },
    addSpotCheck(row) {
      this.showSpotCheck = true
      this.inspectId = row.id
    },
    //提交下发
    submitSpotCheck() {
      this.submitSpotCheckDisplay = true
      postOnlinePatrolIssue({
        action: 0,
        issueReqs:[{
           otherContent: this.otherContent,
           inspectId: this.inspectId,
        }]
      })
        .then(res => {
          if (res.data.code === 0) {
            this.$message({
              message: '下发成功',
              type: 'success'
            })
          }
          this.showSpotCheck = false
          this.otherContent = ''
          this.submitSpotCheckDisplay = false
          this.getList()
        })
        .catch(e => {
          console.log(e, '请求错误')
          this.submitSpotCheckDisplay = false
        })
    },
    handleChange(value) {
      if (value) {
        this.distCode = value
      } else {
        this.distCode = ''
      }
    },
    //删除
    delSpotCheck(row) {
      this.$confirm('确定要删除选择的数据吗？', '通知', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          postOnlinePatrolIssue({ 
              action: 1,
              issueReqs:[{
                inspectId: row.id,
              }]
          })
            .then(res => {
              this.$message({
                message: '删除成功',
                type: 'success'
              })
              this.getList()
            })
            .catch(e => {
              console.log(e, '请求错误')
            })
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        })
    },
    addSpotCheckDetails(row) {
      this.showSpotCheckDetails = true
      postOnlinePatrolQueryFeedbackDetails({ respectId: row.id }).then(res => {
        if (res.data.code === 0) {
          this.feedBackDetails = res.data.data
        }
      })
    },
    handleCurrentChange() {
      this.getList()
    },
    getList() {
      this.loading = true
      let params = {}
      if (this.user.distRole == '0') {
        params = {
          // inspectAreaCode: this.distCode || '',
          areaCode: this.distCode || '',
          startTime: this.startTime,
          endTime: this.endTime,
          inspectStatus: this.inspectStatus,
          nowPage: this.currentPage,
          pageSize: 10
        }
      } else {
        params = {
          areaCode: this.distCode || this.$store.state.login.userDistCode,
          // inspectAreaCode: this.distCode || '',
          startTime: this.startTime,
          endTime: this.endTime,
          inspectStatus: this.inspectStatus,
          nowPage: this.currentPage,
          pageSize: 10
        }
      }
      postOnlinePatrolInspectList(params).then(res => {
        this.tableData = res.data.data.content
        this.total = res.data.data.totalElements
        this.loading = false
      })
      getDistrictUser().then(res => {
        this.districts = []
        this.districts.push(res.data.data)
      })
    }
  },
  computed: {
    ...mapStateLogin({
      userDistCode: state => state.userDistCode,
      park: state => state.park,
      isShowDist: state => state.isShowDist
    }),
    ...mapState({
      user: state => state.login.user
    }),
    ...mapStateControler({
      automaticPatrolData: state => state.automaticPatrolData
    })
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-select__tags-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
  display: block;
}
/deep/ .el-dialog__body {
  padding-top: 10px;
}
.heightCon{
  height: 600px;
  overflow: auto;
}
.historyPatrolCheck {
  .header {
    margin-bottom: 20px;
    & > * {
      margin-right: 20px;
    }
  }
  .container {
    .title {
      font-size: 18px;
      width: 100%;
      text-align: left;
      padding-bottom: 10px;
      font-weight: 900;
    }
  }
  .pagination {
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .feekBackItem {
    // width: 250px;
    // height: 30px;
    max-width: 570px;
    line-height: 30px;
  }
}
</style>
