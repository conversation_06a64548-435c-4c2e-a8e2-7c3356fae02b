<template>
  <div class="accidentManagement">
    <!-- <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 事件信息
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div> -->
    <div class="seach-part">
      <div class="l">
        <el-cascader
          v-show="this.$store.state.login.user.user_type == 'gov'"
          placeholder="请选择行政区划"
          :options="district"
          v-model="searchObj.districtCode"
          size="small"
          style="width: 240px"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          :show-all-levels="true"
        ></el-cascader>
        <el-cascader
          :options="typeList"
          v-model="searchObj.typeCode"
          :props="{
            value: 'code',
            label: 'name',
          }"
          size="small"
          placeholder="请选择事件类型"
          style="width: 240px"
          clearable
        ></el-cascader>
        <el-date-picker
          v-model="dateValue"
          @change="searchTime"
          size="small"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          unlink-panels
        ></el-date-picker>
        <el-input
          v-model.trim="searchObj.keyword"
          size="small"
          placeholder="请输入事件关键字"
          class="input"
          clearable
          style="width: 240px"
        ></el-input>
        <el-button type="primary" size="small" @click="search">查询</el-button>
      </div>
      <el-button type="primary" size="small" @click="openDialog('add')"
        >新增</el-button
      >
    </div>
    <div class="table-main">
      <div class="table-top">
        <h2>事件列表</h2>
      </div>
      <div>
        <div class="table">
          <el-table
            :data="tableData"
            v-loading="loading"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            style="width: 100%"
            ref="multipleTable"
            @selection-change="handleSelectionChange"
            @select="select"
            @select-all="select"
          >
            <el-table-column
              type="selection"
              width="45"
              fixed="left"
              align="center"
            >
            </el-table-column>
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="title"
              label="事件标题"
              align="center"
              width="300"
            >
            </el-table-column>
            <el-table-column
              prop="time"
              label="事发时间"
              align="center"
              min-width="120"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <div>
                  {{ scope.row.time | timeFn }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="districtCodeName"
              label="所在行政区划"
              align="center"
            >
            </el-table-column>
            <el-table-column prop="typeName" label="事件类型" align="center">
            </el-table-column>
            <el-table-column prop="levelName" label="事件等级" align="center">
            </el-table-column>
            <el-table-column
              prop="dataFlag"
              label="数据生成方式"
              align="center"
            >
              <template slot-scope="scope">
                <span v-if="scope.row.dataFlag == '0'">手动录入</span>
                <span v-else>平台接入</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="360" align="center">
              <template slot-scope="scope">
                <div>
                  <el-button type="text" @click="openDialog('read', scope.row)"
                    >查看</el-button
                  >
                  <el-button
                    type="text"
                    @click="openDialog('edit', scope.row)"
                    :disabled="
                      scope.row.handleFlag == 0 || scope.row.pushFlag == 1
                    "
                    >编辑</el-button
                  ><el-button
                    type="text"
                    @click="deleteAccident(scope.row.id)"
                    :disabled="scope.row.handleFlag == 0"
                    >删除</el-button
                  >
                  <el-button type="text" @click="assess(scope.row)"
                    >研判</el-button
                  >
                  <el-button type="text" @click="checkReport(scope.row)"
                    >查看报告</el-button
                  >
                  <el-button
                    type="text"
                    @click="report(scope.row)"
                    :disabled="scope.row.pushFlag == 1"
                    >上报</el-button
                  >
                  <!-- 推送标识0-未推送1-已推送 -->
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page.sync="searchObj.nowPage"
            background
            layout="total, prev, pager, next"
            :total="searchObj.total"
            v-if="searchObj.total != 0"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- 查看编辑 -->
    <el-dialog
      :title="dialogInfo.title"
      :visible.sync="dialogInfo.visible"
      top="5vh"
      width="1100px"
      @close="closeDialog"
      v-if="dialogInfo.visible"
      :close-on-click-modal="false"
    >
      <div class="dialog">
        <el-form
          :model="accidentForm"
          :rules="rules"
          ref="ruleForm"
          label-width="150px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="事件标题:" prop="title">
                <el-input
                  v-model.trim="accidentForm.title"
                  maxlength="100"
                  :disabled="dialogInfo.disable"
                  placeholder="请输入事件标题"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <!-- <div v-if="this.$store.state.login.user.user_type == 'gov'">
            <el-row :gutter="20">
              <el-col :span="10">
                <el-form-item label="事件地址:" prop="districtCode">
                  <el-cascader
                    placeholder="请选择行政区划"
                    style="width:90%"
                    :options="district"
                    v-model="accidentForm.districtCode"
                    maxlength="100"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    disabled
                    :show-all-levels="true"
                  ></el-cascader>
                </el-form-item>
              </el-col>

              <el-col :span="14">
                <el-form-item prop="address" class="accident-form-address">
                  <el-input

                    v-model.trim="accidentForm.address"
                    class="accident-form-input1"
                    maxlength="100"
                    disabled
                    style="width: 100%"
                    placeholder="请输入事件地址"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div> -->

          <div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item prop="address" label="事件地址:">
                  <el-input
                    refs="addressA"
                    id="addressA"
                    v-model="accidentForm.address"
                    disabled
                    placeholder="请输入事件地址"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事件类型:" prop="typeCode">
                <el-select
                  v-model="accidentForm.typeCode"
                  placeholder="请输入事件类型"
                  :disabled="dialogInfo.disable"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事件等级:" prop="levelCode">
                <el-select
                  v-model="accidentForm.levelCode"
                  placeholder="请输入事件等级"
                  :disabled="dialogInfo.disable"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in levelList"
                    :key="item.levelCode"
                    :value="item.levelCode"
                    :label="item.levelName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="死亡人数:" prop="deathNum">
                <el-input
                  v-model.trim="accidentForm.deathNum"
                  maxlength="10"
                  class="accident-form-input"
                  :disabled="dialogInfo.disable"
                ></el-input
                >人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受伤人数:" prop="woundNum">
                <el-input
                  v-model.trim="accidentForm.woundNum"
                  class="accident-form-input"
                  :disabled="dialogInfo.disable"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- {{accidentForm.time}} -->
            <el-col :span="12">
              <el-form-item label="事发时间:" prop="time">
                <el-date-picker
                  v-model="accidentForm.time"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择事发日期"
                  :disabled="dialogInfo.disable"
                  style="width: 100%"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <div v-if="$store.state.login.user.user_type == 'ent'">
                <el-form-item label="事发企业:" prop="companyName">
                  <el-input
                    v-model.trim="accidentForm.companyName"
                    style="width: 100%"
                    class=""
                    disabled
                  ></el-input>
                </el-form-item>
              </div>

              <div v-else>
                <el-form-item label="事发企业:" prop="companyName">
                  <!-- <el-autocomplete
                    popper-class="my-autocomplete"
                    v-model="accidentForm.companyName"
                    :fetch-suggestions="querySearch"
                    placeholder="请输入企业名称关键字"
                    clearable
                    @clear="clearSensororgCode()"
                    @select="handleSelect"
                    :disabled="dialogInfo.disable"
                    style="width: 100%"
                  >
                    <template slot-scope="{ item }">
                      <div class="name">{{ item.enterpName }}</div>
                    </template>
                  </el-autocomplete> -->
                  <el-select
                    v-model.trim="accidentForm.companyName"
                    filterable
                    placeholder="请输入企业名称搜索选择"
                    remote
                    value-key="enterpId"
                    clearable
                    @change="
                      (item) => {
                        handleSelect(item);
                      }
                    "
                    reserve-keyword
                    :remote-method="querySearch2"
                    :disabled="dialogInfo.disable"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in enListOption"
                      :key="item.enterpId"
                      :title="item"
                      :label="item.enterpName"
                      :value="item"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联重大危险源:" prop="dangerId">
                <el-select
                  v-model="accidentForm.dangerId"
                  placeholder="请选择危险源"
                  :disabled="dialogInfo.disable"
                  style="width: 100%"
                  @change="getHazarchemData(accidentForm.dangerId)"
                >
                  <el-option
                    v-for="item in dangerSourceList"
                    :key="item.dangerId"
                    :value="item.dangerId"
                    :label="item.dangerName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联危险化学品:" prop="chemicalsId">
                <el-select
                  v-model="accidentForm.chemicalsId"
                  placeholder="请选择危险化学品"
                  :disabled="dialogInfo.disable"
                  style="width: 100%"
                  multiple
                >
                  <el-option
                    v-for="item in hazarchemList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.label"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度:" prop="longitude">
                <el-input
                  v-model.trim="accidentForm.longitude"
                  disabled
                  style="width: 240px"
                  placeholder="请输入经度"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度:" prop="latitude">
                <el-input
                  v-model="accidentForm.latitude"
                  disabled
                  style="width: 240px"
                  placeholder="请输入纬度"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="map_height">
                <el-form-item label="地图定位">
                  <egisMap
                    :islistener="false"
                    :isdetail="dialogInfo.disable"
                    ref="detailMap"
                    :datas="accidentForm"
                    style="height: 200px"
                    @mapCallback="mapcallback"
                  ></egisMap>
                </el-form-item>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="详情描述:" prop="description">
                <el-input
                  v-model.trim="accidentForm.description"
                  type="textarea"
                  :rows="5"
                  placeholder="2000字以内"
                  maxlength="2000"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="上传附件:">
                <AttachmentUpload
                  :attachmentlist="accidentForm.file"
                  :limit="1"
                  type="office"
                  v-bind="{}"
                  :editabled="dialogInfo.disable"
                ></AttachmentUpload>
                <!-- <div v-else>
                  <i class="el-icon-upload"></i
                  >{{ accidentForm.file.length && accidentForm.file[0].name }}
                </div> -->
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeAll" v-if="!dialogInfo.disable"
            >取 消</el-button
          >
          <!-- <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button> -->
          <el-button
            type="primary"
            @click="saveData()"
            v-if="!dialogInfo.disable"
            >提 交</el-button
          >
        </div>
      </div>
    </el-dialog>
    <el-dialog
      :title="reportInfo.title"
      :visible.sync="reportInfo.visible"
      top="5vh"
      width="1100px"
      @close="closeDialog"
      v-if="reportInfo.visible"
      :close-on-click-modal="false"
    >
      <div class="dialog">
        <el-form
          :model="accidentForm"
          :rules="rules"
          ref="ruleForm"
          label-width="150px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="事件标题:" prop="title">
                <el-input
                  v-model.trim="accidentForm.title"
                  maxlength="100"
                  placeholder="请输入事件标题"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item prop="address" label="事件地址:">
                  <el-input
                    refs="addressA"
                    id="addressA"
                    v-model="accidentForm.address"
                    placeholder="请输入事件地址"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事件类型:" prop="typeCode">
                <el-select
                  v-model="accidentForm.typeCode"
                  placeholder="请输入事件类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in eventTypes"
                    :key="item.value"
                    :value="item.value"
                    :label="item.label"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事件等级:" prop="levelCode">
                <el-select
                  v-model="accidentForm.levelCode"
                  placeholder="请输入事件等级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in levelList"
                    :key="item.levelCode"
                    :value="item.levelCode"
                    :label="item.levelName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="死亡人数:" prop="deathNum">
                <el-input
                  v-model.trim="accidentForm.deathNum"
                  maxlength="10"
                  class="accident-form-input"
                ></el-input
                >人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受伤人数:" prop="woundNum">
                <el-input
                  v-model.trim="accidentForm.woundNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="重伤人数:" prop="seriousInjureNum">
                <el-input
                  v-model.trim="accidentForm.seriousInjureNum"
                  maxlength="10"
                  class="accident-form-input"
                ></el-input
                >人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="轻伤人数:" prop="minorInjureNum">
                <el-input
                  v-model.trim="accidentForm.minorInjureNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="失踪人数:" prop="lossNum">
                <el-input
                  v-model.trim="accidentForm.lossNum"
                  maxlength="10"
                  class="accident-form-input"
                ></el-input
                >人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受困人数:" prop="trappedNum">
                <el-input
                  v-model.trim="accidentForm.trappedNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受威胁人数:" prop="threatenedNum">
                <el-input
                  v-model.trim="accidentForm.threatenedNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="转移人数:" prop="transferedNum">
                <el-input
                  v-model.trim="accidentForm.transferedNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- {{accidentForm.time}} -->
            <el-col :span="12">
              <el-form-item label="事发时间:" prop="time">
                <el-date-picker
                  v-model="accidentForm.time"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择事发日期"
                  style="width: 100%"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <div v-if="$store.state.login.user.user_type == 'ent'">
                <el-form-item label="事发企业:" prop="companyName">
                  <el-input
                    v-model.trim="accidentForm.companyName"
                    style="width: 100%"
                    class=""
                  ></el-input>
                </el-form-item>
              </div>

              <div v-else>
                <el-form-item label="事发企业:" prop="companyName">
                  <el-select
                    v-model.trim="accidentForm.companyName"
                    filterable
                    placeholder="请输入企业名称搜索选择"
                    remote
                    value-key="enterpId"
                    clearable
                    @change="
                      (item) => {
                        handleSelect(item);
                      }
                    "
                    reserve-keyword
                    :remote-method="querySearch2"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in enListOption"
                      :key="item.enterpId"
                      :title="item"
                      :label="item.enterpName"
                      :value="item"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联重大危险源:" prop="dangerId">
                <el-select
                  v-model="accidentForm.dangerId"
                  placeholder="请选择危险源"
                  style="width: 100%"
                  @change="getHazarchemData(accidentForm.dangerId)"
                >
                  <el-option
                    v-for="item in dangerSourceList"
                    :key="item.dangerId"
                    :value="item.dangerId"
                    :label="item.dangerName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联危险化学品:" prop="chemicalsId">
                <el-select
                  v-model="accidentForm.chemicalsId"
                  placeholder="请选择危险化学品"
                  style="width: 100%"
                  multiple
                >
                  <el-option
                    v-for="item in hazarchemList"
                    :key="item.id"
                    :value="item.id"
                    :label="item.label"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度:" prop="longitude">
                <el-input
                  v-model.trim="accidentForm.longitude"
                  disabled
                  style="width: 240px"
                  placeholder="请输入经度"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度:" prop="latitude">
                <el-input
                  v-model="accidentForm.latitude"
                  disabled
                  style="width: 240px"
                  placeholder="请输入纬度"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="map_height">
                <el-form-item label="地图定位">
                  <egisMap
                    :islistener="false"
                    :isdetail="dialogInfo.disable"
                    ref="detailMap"
                    :datas="accidentForm"
                    style="height: 200px"
                    @mapCallback="mapcallback"
                  ></egisMap>
                </el-form-item>
              </div>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="详情描述:" prop="description">
                <el-input
                  v-model.trim="accidentForm.description"
                  type="textarea"
                  :rows="5"
                  placeholder="2000字以内"
                  maxlength="2000"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="上传附件:">
                <AttachmentUpload
                  :attachmentlist="accidentForm.file"
                  :limit="1"
                  type="office"
                  v-bind="{}"
                >
                </AttachmentUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeDialog" v-if="!dialogInfo.disable"
            >取 消</el-button
          >
          <!-- <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button> -->
          <el-button
            type="primary"
            @click="submitReport"
            v-if="!dialogInfo.disable"
            >提 交</el-button
          >
        </div>
      </div>
    </el-dialog>

    <!-- Word文档预览弹窗 -->
    <el-dialog
      title="文档预览"
      :visible.sync="previewShow"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
      class="preview-dialog"
    >
      <div v-loading="loading" class="preview-content">
        <div ref="word" style="min-height: 400px"></div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closePreviewDialog">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- PDF文档预览弹窗 -->
    <el-dialog
      title="PDF预览"
      :visible.sync="dialogVisible3"
      width="80%"
      top="5vh"
      :close-on-click-modal="false"
      class="pdf-dialog"
    >
      <div v-loading="loading" class="pdf-content">
        <iframe
          v-if="pdfUrl"
          :src="pdfUrl"
          width="100%"
          height="600px"
          frameborder="0"
          style="border: none"
        ></iframe>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closePreviewDialog">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, createNamespacedHelpers } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import {
  getAccidentListDataDuty,
  getAccidentTypeListData,
  deleteAccidentByIdDuty,
  addAccidentDuty,
  updateAccidentDuty,
  reportUpload,
  getAccidentByIdDuty,
} from "@/api/accidentManagement";
import { parseTime } from "@/utils/index";
import { MessageBox } from "element-ui";
const mapConfig = require("@/assets/json/map.json");
import { getSearchArr } from "@/api/entList.js";
import {
  getSelectData,
  getInformationInfoDanger,
  getHazarchemListNew,
} from "@/api/entList";
import { getReport } from "@/api/accidentManagement";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";

import downloadFuc, { Attachmentdownload } from "@/api/download/download.js";
import axios from "axios";
const docx = require("docx-preview");
window.JSZip = require("jszip");
var dayjs = require("dayjs");

export default {
  name: "accidentCount",
  components: {
    AttachmentUpload,
  },
  data() {
    let validateNum = (rule, value, callback) => {
      // debugger
      const reg = /^[+]{0,1}(\d+)$/;
      if (value === "") {
        callback(new Error("请填写人数"));
      } else if (!reg.test(value)) {
        callback(new Error("请输入正整数"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      header: {
        token: this["$store"].state.login.token,
      },
      tableData: [], // 表格数据
      loading: false, // 加载状态
      searchObj: {
        // 表格查询参数
        nowPage: 1,
        total: 0,
        pageSize: 10,
        keyword: "",
        districtCode: this.$store.state.login.userDistCode,
        startTime: "",
        endTime: "",
        typeCode: "",
      },
      enListOption: [],
      dateValue: "", // 时间选择
      typeList: [], // 事件类型数据
      levelList: [], // 事件等级数据
      dialogInfo: {
        // 弹窗控制信息
        visible: false,
        title: "新增事件信息",
        disable: false,
        loading: false,
      },
      reportInfo: {
        // 弹窗控制信息
        visible: false,
        title: "上报事件",
        disable: false,
      },
      accidentForm: {
        // 事件表单
        title: "",
        address: "",
        typeCode: "", //事件类型
        eventTypeCode: "",
        levelCode: "", //事件等级
        deathNum: "", //死亡人数
        woundNum: "", //受伤人数,
        seriousInjureNum: "", //重伤人数
        threatenedNum: "", //受威胁人数
        transferedNum: "",
        transferedNum: "",
        minorInjureNum: "",
        lossNum: "",
        trappedNum: "",
        time: "",
        companyName: "",
        description: "",
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        file: [],
        dangerId: "",
        chemicalsId: "",
        address: "",
      },
      eventTypes: [
        { label: "危险化学品事故", value: "20300" },
        { label: "危险化学品爆炸事故", value: "20301" },
        { label: "危险化学品泄漏事故", value: "20302" },
        { label: "危险化学品中毒和窒息事故", value: "20303" },
        { label: "危险化学品火灾事故", value: "20304" },
        { label: "危险化学品灼烫事故", value: "20305" },
        { label: "危险化学品其他事故", value: "20399" },
      ],
      reset() {
        this.accidentForm = {
          title: "",
          address: "",
          typeCode: "", //事件类型
          eventTypeCode: "",
          levelCode: "", //事件等级
          deathNum: "", //死亡人数
          woundNum: "", //受伤人数,
          threatenedNum: "", //受威胁人数
          time: "",
          companyName: "",
          description: "",
          longitude: mapConfig.map.defaultExtent.center[0],
          latitude: mapConfig.map.defaultExtent.center[1],
          file: [],
          dangerId: "",
          chemicalsId: "",
          address: "",
        };
        if (this.$refs["ruleForm"]) {
          this.$refs["ruleForm"].resetFields();
        }
      },
      rules: {
        // 表单校验规则
        title: [
          { required: true, message: "请填写事件标题", trigger: "blur" },
          // { max: 255, message: "长度 255 个字符一下", trigger: "blur" },
        ],
        time: [
          {
            type: "string",
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        districtCode: [
          {
            required: true,
            message: "行政区划不能为空",
            trigger: "change",
          },
        ],
        address: [
          {
            required: true,
            message: "请填写事发地址",
            trigger: "blur",
          },
        ],
        description: [
          { required: true, message: "请填写事件描述", trigger: "blur" },
        ],
        typeCode: [
          { required: true, message: "请选择事发类型", trigger: "change" },
        ],
        eventTypeCode: [
          { required: true, message: "请选择事件类型", trigger: "change" },
        ],
        levelCode: [
          { required: true, message: "请选择事发等级", trigger: "change" },
        ],
        deathNum: [{ required: true, validator: validateNum, trigger: "blur" }],
        woundNum: [{ required: true, validator: validateNum, trigger: "blur" }],
        companyName: [
          { required: true, message: "请选择事发企业", trigger: "change" },
        ],
        longitude: [
          { required: true, message: "经度不能为空", trigger: "blur" },
        ],
        latitude: [
          { required: true, message: "纬度不能为空", trigger: "blur" },
        ],
      },
      district: this.$store.state.controler.district, // 行政区划
      dangerSourceList: [], // 危险源数据
      hazarchemList: [], // 危化品数据
      // 文档预览相关
      previewShow: false, // Word文档预览弹窗
      dialogVisible3: false, // PDF预览弹窗
      pdfUrl: "", // PDF文件URL
      loading: false, // 加载状态
    };
  },
  filters: {
    timeFn(val) {
      if (val) {
        return dayjs(val).format("YYYY-MM-DD");
      }
    },
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
  created() {},
  methods: {
    closeAll() {
      this.dialogInfo.visible = false;
      this.reset();
    },
    dateOptions(time) {
      return time.getTime() < Date.now() - 8.64e6;
    },
    search() {
      this.searchObj.nowPage = 1;
      this.getAccidentList();
    },
    handleSelectionChange() {},
    select() {},
    // 分页查询
    handleCurrentChange(val) {
      this.searchObj.nowPage = val;
      this.getAccidentList();
    },
    // 获取事件列表数据
    getAccidentList() {
      this.loading = true;
      if (typeof this.searchObj.typeCode !== "string") {
        this.searchObj.typeCode =
          this.searchObj.typeCode && this.searchObj.typeCode[0];
      }
      let params = JSON.parse(JSON.stringify(this.searchObj));
      if (this.$store.state.login.user.user_type !== "gov") {
        params.districtCode = "";
      }
      delete params.total;
      getAccidentListDataDuty(params).then((res) => {
        if (res.data.status === 200) {
          this.tableData = res.data.data.list;
          this.searchObj.nowPage = res.data.data.nowPage + 1;
          this.searchObj.total = Number(res.data.data.total);
          this.loading = false;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
    // 获取事件类型列表
    getaccidentTypeList() {
      getAccidentTypeListData().then((res) => {
        if (res.status === 200) {
          this.typeList = res.data.data.typeCode;
          this.levelList = res.data.data.accidentLevel;
        }
      });
    },
    // 删除事件列
    deleteAccident(id) {
      MessageBox.confirm("确定要删除选择的数据吗?", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(() => {
          deleteAccidentByIdDuty({ id })
            .then((res) => {
              this.$message.success("删除成功");
              if (this.tableData.length === 1 && this.searchObj.nowPage !== 1) {
                this.searchObj.nowPage--;
              }
              this.getAccidentList();
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {});
    },
    // 时间改变执行方法
    searchTime(val) {
      if (val) {
        let date1 = new Date(val[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}") + " 00:00:00";
        let date2 = new Date(val[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}") + " 00:00:00";
        this.searchObj.startTime = dataTime1;
        this.searchObj.endTime = dataTime2;
      } else {
        this.dateValue = "";
        this.searchObj.startTime = "";
        this.searchObj.endTime = "";
      }
    },
    // 关闭弹窗
    closeDialog() {
      this.dialogInfo.visible = false;
      this.reportInfo.visible = false;
      this.reset();
      // this.accidentForm = {
      //   longitude: mapConfig.map.defaultExtent.center[0],
      //   latitude: mapConfig.map.defaultExtent.center[1],
      // };
    },
    assess(data) {
      const windowlivingExample = window.myWindow;
      //研判
      let isHttps = location.protocol == "https:"; //互联网环境

      var username = this.$store.state.login.user.username;
      let path =
        (isHttps ? "https://" + this.BASE_URL : "http://" + this.BASE_URL) +
        "/egis/#/accidentanalysis?u=" +
        username +
        "&eventId=" +
        data.id;
      if (process.env.NODE_ENV == "development") {
        path =
          "http://127.0.0.1:8778/#/accidentanalysis?u=" +
          username +
          "&eventId=" +
          data.id;
      }
      if (username) {
        if (
          (windowlivingExample && windowlivingExample.closed) ||
          !windowlivingExample
        ) {
          window.myWindow = window.open(path);
        } else {
          windowlivingExample.location.href = path;
          windowlivingExample.focus();
        }
      } else {
        this.$message("请先登录！");
      }
    },
    checkReport(data) {
      getReport({ id: data.id }).then((res) => {
        const fileData = res.data.data;
        if (fileData.url) {
          // 根据文件类型进行预览
          this.previewFile(fileData);
        }
      });
    },

    // 文件预览方法
    async previewFile(fileData) {
      const fileName = fileData.name || fileData.fileName || "";

      if (/(.*)\.(docx)$/i.test(fileName)) {
        // Word 文档预览
        this.previewWord(fileData);
      } else if (/(.*)\.(pdf)$/i.test(fileName)) {
        // PDF 文档预览
        this.previewPdf(fileData);
      } else {
        // 其他文件类型直接下载
        this.download(fileData);
      }
    },

    // Word 文档预览
    previewWord(fileData) {
      this.previewShow = true;
      this.loading = true;

      axios({
        method: "post",
        url: "/gemp-file/api/attachment/download/v1",
        data: { fileId: fileData.attachId },
        responseType: "blob",
      })
        .then((res) => {
          this.loading = false;
          // 对后端返回二进制流做处理
          const blob = new Blob([res.data]);
          this.$nextTick(() => {
            if (this.$refs.word) {
              docx.renderAsync(blob, this.$refs.word).catch((err) => {
                console.error("Word预览失败:", err);
                this.$message.error("文档预览失败，请下载查看");
                this.previewShow = false;
                this.download(fileData);
              });
            }
          });
        })
        .catch((err) => {
          this.loading = false;
          console.error("获取文档失败:", err);
          this.$message.error("获取文档失败");
        });
    },

    // PDF 文档预览
    async previewPdf(fileData) {
      this.dialogVisible3 = true;
      this.loading = true;

      try {
        // 获取文件信息
        const axiosRes = await axios({
          method: "post",
          url: "/gemp-file/api/attachment/findByFileId/v1",
          data: { attachId: fileData.attachId },
        });

        if (axiosRes.data.data) {
          this.pdfUrl =
            axiosRes.data.data.fileOuterPath + axiosRes.data.data.filePath;
          this.loading = false;
        } else {
          throw new Error("获取PDF路径失败");
        }
      } catch (error) {
        this.loading = false;
        console.error("PDF预览失败:", error);
        this.$message.error("PDF预览失败，请下载查看");
        this.dialogVisible3 = false;
        this.download(fileData);
      }
    },

    // 下载文件（保留原有功能）
    download(file) {
      let par = {
        fileId: file.attachId || file.response.attachId,
      };
      Attachmentdownload(par).then((res) => {
        console.log(res, "特殊作业管理下载");
        downloadFuc(res);
      });
    },

    // 关闭预览弹窗
    closePreviewDialog() {
      this.previewShow = false;
      this.dialogVisible3 = false;
    },
    // 上报
    report(data) {
      const rowData = Object.assign({}, data);
      this.getSelectDataById(data.companyId);
      this.getHazarchemData(data.dangerId);
      this.accidentForm = rowData;
      this.reportInfo.visible = true;
    },
    submitReport() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          let params = {
            ...this.accidentForm,
            eventLevelCode: this.accidentForm.levelCode, // 事故等级
            eventTitle: this.accidentForm.title, // 事故标题
            eventTypeCode: this.accidentForm.typeCode, // 事故类型
            infoAbstract: this.accidentForm.description, // 事故描述
            infoAddress: this.accidentForm.address, // 事故地址
            incidentEnterprise: this.accidentForm.companyName, // 事故企业
            occurTime: this.accidentForm.time, // 事故时间
            originId: this.accidentForm.id,
            eventId: this.accidentForm.id,
            eventDetail: {
              chemicalsId: this.accidentForm.chemicalsId, // 事故涉及的化学品
              dangerId: this.accidentForm.dangerId, // 事故涉及的危险源
              enterpId: this.accidentForm.companyId, // 事故企业
            },
          };
          console.log(params);
          reportUpload(params).then((res) => {
            if (res.data.status === 200) {
              this.reportInfo.visible = false;
              this.$message.success("上报成功");
              this.reset();
              this.getAccidentList();
              // 获取当前系统登录的用户名
              window.open(
                "http://************:8080/hubei-amap169/#/?phone=" +
                  this.$store.state.login.user.username
              );
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    // 打开弹窗
    async openDialog(type, data) {
      var _this = this;
      if (type === "edit") {
        this.dialogInfo.title = "编辑事件信息";
        const rowData = Object.assign({}, data);
        this.accidentForm = rowData;
        this.getSelectDataById(data.companyId);
        this.getHazarchemData(data.dangerId);
        this.dialogInfo.disable = false;
      } else if (type === "add") {
        this.dialogInfo.title = "新增事件信息";
        this.dialogInfo.disable = false;
        if (this.$store.state.login.user.user_type == "ent") {
          this.accidentForm.companyName = this.$store.state.login.user.org_name;
          getSearchArr(this.accidentForm.companyName).then((res) => {
            if (res.data.code == 0) {
              if (res.data.data.length > 0) {
                this.handleSelect(res.data.data[0]);
              } else {
              }
            }
          });
        }
      } else {
        const rowData = Object.assign({}, data);
        this.accidentForm = rowData;
        this.dialogInfo.title = "事件信息详情";
        this.dialogInfo.disable = true;
        this.getSelectDataById(data.companyId);
        this.getHazarchemData(data.dangerId);
      }
      this.dialogInfo.visible = true;
    },
    // 新增/编辑
    saveData() {
      // console.log(this.accidentForm.address)
      // this.$refs['ruleForm'].clearValidate(['address']);
      //  document.getElementById('addressA').value='111'
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.dialogInfo.title === "新增事件信息"
            ? this.addData()
            : this.updateData();
          this.reset();
          // this.$refs['ruleForm'].resetFields()
        }
      });
    },
    // 新增事件数据
    addData() {
      addAccidentDuty(this.accidentForm).then((res) => {
        if (res.data.status === 200) {
          this.$message.success("操作成功");
          this.dialogInfo.visible = false;
          // this.accidentForm = {
          //   longitude: mapConfig.map.defaultExtent.center[0],
          //   latitude: mapConfig.map.defaultExtent.center[1],
          // };
          this.reset();
          this.getAccidentList();
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },
    // 更新事件数据
    updateData() {
      // debugger
      updateAccidentDuty(this.accidentForm).then((res) => {
        if (res.data.status === 200) {
          this.$message.success("操作成功");
          this.dialogInfo.visible = false;
          // this.accidentForm = {
          //   longitude: mapConfig.map.defaultExtent.center[0],
          //   latitude: mapConfig.map.defaultExtent.center[1],
          // };
          this.reset();
          this.getAccidentList();
        } else {
          this.$message.warning(res.data.mssg);
        }
      });
    },
    // 地图定位
    mapcallback(data) {
      // 标点赋值
      this.accidentForm.longitude = data.location.lon.toFixed(6);
      this.accidentForm.latitude = data.location.lat.toFixed(6);
      this.accidentForm.address = data.formatted_address;
      if (data.addressComponent.county_code != "") {
      }
      //districtName
      this.accidentForm.districtCode = data.addressComponent.county_code.slice(
        3,
        data.addressComponent.county_code.length
      );
      // this.accidentForm.districtCode='420984'
    },
    // 文件上传
    // handleFileSuccess(file) {
    //   this.accidentForm.file = [file];
    // },
    // 企业数据
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    querySearch2(val) {
      getSearchArr(val)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              this.enListOption = res.data.data;
            } else {
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },

    // 清楚建议框
    clearSensororgCode() {
      this.accidentForm.companyId = "";
      this.accidentForm.companyName = "";
    },
    //选择企业
    handleSelect(item) {
      // 选择企业后先将关联重大危险源和关联危险化学品清空
      this.accidentForm.dangerId = "";
      this.accidentForm.chemicalsId = "";
      this.accidentForm.companyId = item.enterpId;
      this.accidentForm.companyName = item.enterpName;
      this.getSelectDataById(item.enterpId);
      this.getHazarchemData(item.dangerId);
      this.accidentForm.longitude = item.longitude;
      this.accidentForm.latitude = item.latitude;
      this.accidentForm.districtCode = item.districtCode;
      this.accidentForm.address = item.address;
      var x = {
        x: item.longitude,
        y: item.latitude,
      };
      //
      this.$refs["detailMap"].setMapDiot2(x);

      this.$nextTick(() => {
        // this.$refs["ruleForm"].clearValidate(); // 取消表单验证
        this.$refs["ruleForm"].clearValidate(["address"]);
        this.$refs["ruleForm"].clearValidate(["districtCode"]);
        this.$refs["ruleForm"].clearValidate(["longitude"]);
        this.$refs["ruleForm"].clearValidate(["latitude"]);
      });
    },
    // 获取危险源数据
    getSelectDataById(enterpriseId) {
      getSelectData({
        enterpId: enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.dangerSourceList = res.data.data;
        }
      });
    },
    // 危险源选择器改变
    handleChangeDangerId(id) {},
    // 获取危化品数据
    getHazarchemData(dangerId) {
      getHazarchemListNew({ dangerId }).then((res) => {
        if (res.data.status == 200) {
          this.hazarchemList = res.data.data;
        }
      });
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  mounted() {
    this.getaccidentTypeList();
    this.getAccidentList();
  },
};
</script>

<style lang="scss" scoped>
.accidentManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }

  .dialog {
    height: 70vh;
    overflow-y: scroll;
    overflow-x: hidden;
    padding-right: 10px;

    .dialog-footer {
      display: flex;
      justify-content: center;

      & > * {
        margin: 0 10px;
      }
    }
  }

  .accident-form-input1 {
    width: 400px;
  }
}

.seach-part {
  font-weight: 600;
  display: flex;
  justify-content: space-between;
  padding-bottom: 10px;
  margin-bottom: 15px;
  // margin-top: 20px;
  border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

  .l {
    display: flex;
    justify-content: space-between;

    & > * {
      margin-right: 15px;
    }
  }
}

.table-main {
  background: #fff;

  .table-top {
    display: flex;
    justify-content: space-between;

    // padding: 10px 0;
    h2 {
      font-size: 18px;
      line-height: 32px;
      margin-bottom: 0;
    }
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
<style lang="scss">
.accident-form-input {
  // display: inline-block;
  width: 80px;
  margin-right: 10px;

  .el-input__inner {
    border: none;
    border-bottom: 1px solid #666;
    width: 80px;
    outline: none;
    height: 20px;
    border-radius: 0;
  }
}

.accident-form-address .el-form-item__content {
  margin-left: -20px !important;
}

// 文档预览弹窗样式
.preview-dialog {
  .el-dialog__body {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
  }

  .preview-content {
    min-height: 400px;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 20px;
    background: #fff;
  }
}

.pdf-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .pdf-content {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    overflow: hidden;
  }
}
</style>
