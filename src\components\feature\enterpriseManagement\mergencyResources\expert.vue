<template>
  <div class="enterpriseList">
    <div class="header">
      <div class="title">专家列表</div>
      <div class="operation">
        <div class="inputBox">
          <el-input v-model.trim="keywords"
                    size="small"
                    placeholder="请输入专家名称"
                    class="input"
                    clearable></el-input>
          <el-select v-model="expertInfoIds"
                     size="small"
                     placeholder="请选择专家职称"
                     :clearable="true">
            <el-option v-for="item in expertTypeData"
                       :key="item.value"
                       :label="item.name"
                       :value="item.value">
            </el-option>
          </el-select>
          <!-- <el-select v-model="speciality"
                     size="mini"
                     placeholder="请选择专业领域"
                     :clearable="true">
            <el-option v-for="item in expertSpecialityData"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select> -->
            <el-input v-model.trim="speciality"
                    size="small"
                    placeholder="请输入专业领域"
                    class="input"
                    clearable></el-input>
<el-cascader v-model="districtCode"
                       size="small"
                       placeholder="请选择工作区域"
                       :clearable="true"
                       :options="expertDistrictData"
                       @change="handleChange">
          </el-cascader>
          <el-button type="primary"
                     size="small"
                     @click="searches()">查询</el-button>
          <CA-button type="primary"
                     size="small"
                     class="export"
                     plain
                     @click="exportExcel">导出</CA-button>
        </div>
        <el-button type="primary"
                   size="small"
                  
                   @click="addEdit"
                   v-if="$store.state.login.user.user_type == 'ent'">新增</el-button>
      </div>
    </div>
    <div class="table"
         v-loading="loading">
      <el-table :data="tableData"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
                ref="multipleTable"
                @selection-change="handleSelectionChange"
                @select="select"
                @select-all="select">
        <el-table-column type="selection"
                         width="55"
                         fixed="left"
                         align="center">
        </el-table-column>
        <el-table-column type="index"
                         label="序号"
                         width="55"
                         align="center">
        </el-table-column>
        <el-table-column prop="expertName"
                         label="专家姓名"
                         width="100"
                         align="center">
        </el-table-column>
        <el-table-column prop="department"
                         label="单位"
                         align="center"
                         min-width="200"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="mobileTel"
                         label="手机号码"
                         align="center"
                         min-width="150">
        </el-table-column>
        <!-- <el-table-column prop="protitle"
                         label="职称"
                         align="center"
                         min-width="180">
        </el-table-column> -->

         <el-table-column prop="protitle"
                         label="职称"
                         align="center"
                         min-width="180">
        </el-table-column>
        <el-table-column prop="speciality"
                         label="专业领域"
                         align="center"
                         min-width="250"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <!-- <el-table-column prop="expertTypeName"
                         label="专家类型"
                         align="center"
                         min-width="300"
                         :show-overflow-tooltip="true">
        </el-table-column> -->
        <el-table-column label="操作"
                         align="center"
                         min-width="160">
          <template slot-scope="scope">
            <span @click="view(scope.row)"
                  style="color: rgb(57, 119, 234);;margin-right: 10px;cursor: pointer;">查看</span>
            <span v-if="$store.state.login.user.user_type == 'ent'"
                  @click="edit(scope.row)"
                  style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">编辑</span>
            <span v-if="$store.state.login.user.user_type == 'ent'"
                  @click="deleter(scope.row)"
                  style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">删除</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination @current-change="handleCurrentChange"
                     :current-page.sync="currentPage"
                     :page-size="size"
                     layout="total, prev, pager, next"
                     background
                     :total="total">
      </el-pagination>
    </div>
    <!-- 新增编辑查看 -->
    <el-dialog :title="title"
               :visible.sync="open"
               width="1100px"
               :close-on-click-modal="false"
               :append-to-body="true">
      <el-form ref="form"
               :model="form"
               :rules="rules"
               :disabled="disabled"
               label-width="150px"
               :hide-required-asterisk="disabled">
        <div class="form_item">
          <h2 class="form_title">基本信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="姓名"
                              prop="expertName">
                  <el-input v-model.trim="form.expertName"
                            placeholder="请输入姓名" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单位详细地址"
                              prop="regresidence">
                  <el-input v-model.trim="form.regresidence"
                            placeholder="请输入单位详细地址" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="职务"
                              prop="protitle">
                  <el-input v-model.trim="form.protitle"
                            placeholder="请输入职务" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职业资格"
                              prop="qualification">
                  <!-- <el-input
                                    v-model="form.qualification"
                                    placeholder="请输入职业资格"
                                /> -->
                  <el-select v-model="form.qualification"
                             placeholder="请选择职业资格"
                             :clearable="true">
                    <el-option v-for="item in expertZigeData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别"
                              prop="sexCode">
                  <el-select v-model="form.sexCode"
                             placeholder="请选择性别">
                    <el-option label="男"
                               value="1"></el-option>
                    <el-option label="女"
                               value="2"></el-option>
                    <el-option label="未说明的性别"
                               value="9"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工作单位全称"
                              prop="department">
                  <el-input v-model.trim="form.department"
                            placeholder="请输入工作单位全称" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="年限"
                              prop="employmentYears">
                  <el-input v-model.trim="form.employmentYears"
                  
                            placeholder="请输入年限" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经度"
                              prop="longitude">
                  <el-input v-model.trim="form.longitude"
                  disabled
                            placeholder="请输入经度" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="就业情况">
                  <!-- <el-input
                                    v-model="form.employment"
                                    placeholder="请输入就业情况"
                                /> -->
                  <el-select v-model="form.employment"
                             placeholder="请选择就业情况"
                             :clearable="true">
                    <el-option v-for="item in expertJiuyeData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度"
                              prop="latitude">
                  <el-input v-model.trim="form.latitude"
                  disabled
                            placeholder="请输入纬度" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="map_height">
                  <el-form-item label="职称"
                                prop="levelCode">
                    <el-select v-model="form.levelCode"
                               placeholder="请选择职称">
                      <el-option v-for="item in expertTypeData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="毕业学校">
                    <el-input v-model.trim="form.graduate"
                              placeholder="请输入毕业学校" />
                  </el-form-item>
                  <el-form-item label="专业领域"
                                prop="speciality" :rules="[{max: 200, message: '最多输入200个字符', trigger: 'change', required: true}]">
                    <el-input v-model.trim="form.speciality"
                              placeholder="请输入专业领域,最多200个字符"
                              type="textarea"
                              :rows="4"/>
                  </el-form-item>
                  <el-form-item label="工作区域">
                  <el-cascader v-model="form.districtCodes"
                               placeholder="请选择工作区域"
                               :clearable="true"
                               :options="expertDistrictData"
                               @change="handleChange">
                  </el-cascader>
                </el-form-item>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="map_height">
                  <el-form-item label="地图定位">
                    <egisMap :islistener="true"
                             :isdetail="disabled"
                             plotting-method="point"
                             :datas="form"
                             ref="detailMap"
                             style="height:220px"
                             @mapCallback="mapcallback"></egisMap>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="form_item">
          <h2 class="form_title">联系方式</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="备用电话" prop="officeTel" :rules="[{pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,message: '请输入11位备用电话'}]">
                  <el-input v-model.trim="form.officeTel"
                            placeholder="请输入备用电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话"
                              prop="mobileTel">
                  <el-input v-model.trim="form.mobileTel"
                            placeholder="请输入联系电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="住宅电话"
                              prop="homeTel">
                  <el-input v-model.trim="form.homeTel"
                            placeholder="请输入住宅电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="传真"
                              prop="fax">
                  <el-input v-model.trim="form.fax"
                            placeholder="请输入传真" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="电子邮箱">
                  <el-input v-model.trim="form.email"
                            placeholder="请输入电子邮箱" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="form_item">
          <h2 class="form_title">补充信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="民族">
                  <el-select v-model="form.nationCode"
                             placeholder="请选择民族">
                    <el-option v-for="item in minzuData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="出生年月"
                              prop="birthday">
                  <el-date-picker v-model="form.birthday"
                                  type="date"
                                  placeholder="选择出生年月">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="行政职务">
                  <el-input v-model="form.headship"
                            placeholder="请输入行政职务" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="所学专业"
                              prop="major">
                  <el-input v-model.trim="form.major"
                            placeholder="请输入所学专业" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="身份证号码"
                              prop="idNo">
                  <el-input v-model.trim="form.idNo"
                            placeholder="请输入身份证号码" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="文化程度"
                              prop="education">
                  <el-select v-model="form.education"
                             placeholder="请选择文化程度"
                             :clearable="true">
                    <el-option v-for="item in expertWenhuaData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="政治面貌"
                              prop="party">
                  <el-select v-model="form.party"
                             placeholder="请选择政治面貌"
                             :clearable="true">
                    <el-option v-for="item in expertZhengzhiData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="通信地址">
                  <el-input v-model="form.homeAddress"
                            placeholder="请输入通信地址" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="邮政编码">
                  <el-input v-model.trim="form.postCode"
                            placeholder="请输入邮政编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
               
               
              </el-col>
              <el-col :span="16">
              </el-col>
              <el-col :span="8">
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="工作简历" prop="resume" :rules="[{max: 500, message: '工作简历最多输入500个字符', trigger: 'change'}]">
                  <el-input type="textarea"
                            v-model.trim="form.resume"
                            placeholder="请输入工作简历,最多500个字符"
                            :rows="4" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="安全生产技术服务机构任(兼)职情况" :rules="[{max: 500, message: '最多输入500个字符', trigger: 'change'}]">
                  <el-input type="textarea"
                            v-model.trim="form.safetyEmployment"
                            placeholder="请输入安全生产技术服务机构任(兼)职情况,最多500个字符"
                            :rows="4" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="应急管理相关工作主要业绩及研究成果" prop="emResume">
                  <el-input type="textarea"
                            v-model.trim="form.emResume"
                            placeholder="请输入应急管理相关工作主要业绩及研究成果,最多500个字符"
                            :rows="4" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="受过何种奖励和处分" :rules="[{max: 500, message: '最多输入500个字符', trigger: 'change'}]">
                  <el-input type="textarea"
                            v-model.trim="form.rewardsAndPunishments"
                            placeholder="请输入受过何种奖励和处分,最多500个字符"
                            :rows="4"/>
                </el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="发表著作学术论文发明专利情况" :rules="[{max: 500, message: '最多输入500个字符', trigger: 'change'}]">
                  <el-input type="textarea"
                            v-model.trim="form.harvest"
                            placeholder="请输入发表著作学术论文发明专利情况,最多500个字符"
                            :rows="4" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="!disabled"
           slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createNamespacedHelpers } from 'vuex'
import { size } from 'lodash-es'
import { getDistrictUser } from '@/api/entList'
import { getZidainData } from '@/api/mailList'
import {
  getExpertListData,
  addExpertListData,
  editExpertListData,
  deleteExpertListData,
  getExpertTypeData,
  getExpertSpecialityData,
  getExpertDistrictData,
  exportexpertListData
} from '@/api/mergencyResources'
const mapConfig = require('@/assets/json/map.json')
export default {
  //import引入的组件
  name: 'expert',
  components: {},
  data() {
    return {
      currentPage: 1,
      enterpId: '',
      title: '新增专家信息',
      open: false,
      district: [],
      expertTypeData: [],
      expertInfoIds: '',
      expertSpecialityData: [],
      speciality: '',
      districtCode: [],
      expertDistrictData: [],
      expertWenhuaData: [],
      expertJiuyeData: [],
      expertZigeData: [],
      expertZhengzhiData: [],
      minzuData: [],
      form: {
        districtCodes: [],
        affiliaction: '',
        attachmentList: [],
        birthday: '',
        chargeDept: '',
        cityDistrictCode: '',
        regresidence: '',
        countyDistrictCode: '',
        department: '',
        education: '',
        emResume: '',
        email: '',
        employment: '',
        employmentYears: '',
        expertId: '',
        expertName: '',
        fax: '',
        firstEmploy: '',
        graduate: '',
        harvest: '',
        headship: '',
        homeAddress: '',
        homeTel: '',
        idNo: '',
        latitude: mapConfig.map.defaultExtent.center[1],
        levelCode: '',
        levelName: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        major: '',
        mobileTel: '',
        nationCode: '',
        nuCode: '',
        officeTel: '',
        party: '',
        postCode: '',
        protitle: '',
        qualification: '',
        resourceTypeName: '',
        resume: '',
        rewardsAndPunishments: '',
        safetyEmployment: '',
        sexCode: '1',
        speciality: ''
      },
      rules: {
        expertName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        qualification: [
          { required: true, message: '请输入职业资格', trigger: 'change' }
        ],
        department: [
          { required: true, message: '请输入工作单位全称', trigger: 'blur' }
        ],
        employmentYears: [
          { required: true, message: '请输入工作年限', trigger: 'blur' },
          {pattern: /^[1-9][0-9]{0,1}$/,message: '请输入100以内的数字'}
        ],
        longitude: [{ required: true, message: '请输入经度', trigger: 'blur' }],
        latitude: [{ required: true, message: '请输入纬度', trigger: 'blur' }],
        sexCode: [{ required: true, message: '请选择性别', trigger: 'change' }],
        levelCode: [
          { required: true, message: '请选择职称', trigger: 'change' }
        ],
      
        //  officeTel: [
        //   {  message: '请输入联系电话', trigger: 'blur' },
        //   {pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,message: '请输入11位备用电话'}
        // ],
       
        mobileTel: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          {pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,message: '请输入11位联系电话'}
        ],
        homeTel: [
          { required: true, message: '请输入住宅电话', trigger: 'blur' },
          {pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,message: '请输入正确的电话号码'}
        ],
        fax: [{ required: true, message: '请输入传真', trigger: 'blur' },
          {pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,message: '请输入正确的传真号码'}],
        regresidence: [
          { required: true, message: '请输入单位详细地址', trigger: 'blur' }
        ],
        protitle: [{ required: true, message: '请输入职务', trigger: 'blur' }],
        // speciality: [{ required: true, message: '请输入专业领域', trigger: 'blur' }],
        
        birthday: [
          { required: true, message: '请选择出生日期', trigger: 'change' }
        ],
        major: [{ required: true, message: '请输入所学专业', trigger: 'blur' }],
        idNo: [{ required: true, message: '请输入身份证号', trigger: 'blur' },
        {pattern: /^\d{6}(18|19|20)?\d{2}(0[1-9]|1[0-2])(([0-2][1-9])|10|20|30|31)\d{3}(\d|X|x)$/,message: '请输入正确的身份证号'}],
        education: [
          { required: true, message: '请输入文化程度', trigger: 'change' }
        ],
        email: [
          {pattern: /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/,message: '请输入正确的邮箱'}
        ],
        party: [{ required: true, message: '请输入政治面貌', trigger: 'change' }],
        emResume: [
          {
            required: true,
            message: '请输入主要业绩及研究成果',
            trigger: 'blur'
          },
          {max: 500, message: '最多输入500个字符', trigger: 'change'}
        ]
      },
      disabled: false,
      tableData: [],
      loading: false,
      selection: [],
      total: 0,
      size: 10,
      keywords: ''
    }
  },
  //方法集合
  methods: {
    getZidianMinzu() {
      getZidainData({
        dicCode: 'nation'
      }).then(res => {
        if (res.data.status == 200) {
          this.minzuData = res.data.data
        }
      })
      // getZidainData({
      //     dicCode: 'nation',
      // }).then((res) => {
      //     if (res.data.status == 200) {
      //         this.minzuData = res.data.data;
      //     }
      // })
    },
    addEdit() {
       this.title = '新增专家信息'
       this.disabled = false;
      this.getZidianMinzu()
      this.reset()
      this.open = true
      // this.getDistrict()
    },
    view(row) {
      this.getZidianMinzu()
      this.reset()
      this.open = true
      this.disabled = true
      // this.rules=[]
      this.title = '查看专家信息'
      // this.form = row
       const rowData = Object.assign({}, row)
       this.form=row
      this.form.districtCodes = [rowData.cityDistrictCode, rowData.countyDistrictCode]
    },
    edit(row) {
      this.getZidianMinzu()
      this.reset()
      this.open = true
      this.disabled = false
      this.title = '编辑专家信息'
      // this.form = row
        const rowData = Object.assign({}, row)
       this.form=row
      this.form.districtCodes = [rowData.cityDistrictCode, rowData.countyDistrictCode]
    },
    deleter(row) {
      const id = row.expertId
      this.$confirm('是否确认删除该专家信息？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteExpertListData({
            expertId: id
          }).then(res => {
            if (res.data.status == 200) {
              this.$message.success('删除成功')
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getList()
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList()
    },
    cancel() {
      this.open = false
      this.reset()
    },
    reset() {
      this.form = {
        districtCodes: [],
        affiliaction: '',
        attachmentList: [],
        birthday: '',
        chargeDept: '',
        cityDistrictCode: '',
        regresidence: '',
        countyDistrictCode: '',
        department: '',
        education: '',
        emResume: '',
        email: '',
        employment: '',
        employmentYears: '',
        expertId: '',
        expertName: '',
        fax: '',
        firstEmploy: '',
        graduate: '',
        harvest: '',
        headship: '',
        homeAddress: '',
        homeTel: '',
        idNo: '',
        latitude: mapConfig.map.defaultExtent.center[1],
        levelCode: '',
        levelName: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        major: '',
        mobileTel: '',
        nationCode: '',
        nuCode: '',
        officeTel: '',
        party: '',
        postCode: '',
        protitle: '',
        qualification: '',
        resourceTypeName: '',
        resume: '',
        rewardsAndPunishments: '',
        safetyEmployment: '',
        sexCode: '',
        speciality: ''
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
    //查询
    searches() {
      this.currentPage = 1
      this.getList()
    },
    handleChange(value) {
      console.log(value)
    },
    //获取列表
    getList() {
      this.loading = true
      getExpertListData({
        // cityDistrictCode: "",
        // countyDistrictCode: "",
        districtCode: this.districtCode
          ? this.districtCode[this.districtCode.length - 1]
          : '1',
        endTime: '',
        levelCode: this.expertInfoIds,
        keywords: this.keywords,
        listOrder: {},
        speciality: this.speciality,
        nowPage: this.currentPage,
        orgCode: '',
        pageSize: this.size,
        range: 0,
        startTime: '',
        tenantId: ''
      }).then(res => {
        if (res.data.status == 200) {
          this.loading = false
          this.tableData = res.data.data.list
          this.total = res.data.data.total
        }
      })
    },
    //获取专家职称类型
    getExpertType() {
      getExpertTypeData({ dicCode: 'POSITION_TITLE' }).then(res => {
        if (res.data.status == 200) {
          this.expertTypeData = res.data.data
        }
      })
      getExpertSpecialityData().then(res => {
        if (res.data.status == 200) {
          this.expertSpecialityData = res.data.data
        }
      })
      getExpertDistrictData().then(res => {
        if (res.data.status == 200) {
          this.expertDistrictData = res.data.data.treeData[0].children
          this.expertDistrictData.forEach(item => {
            item.value = item.id
            if (item.children && item.children.length > 0) {
              item.children.forEach(items => {
                items.value = items.id
              })
            }
          })
        }
      })
      getExpertTypeData({ dicCode: 'HIGHEST_EDUCATION' }).then(res => {
        if (res.data.status == 200) {
          this.expertWenhuaData = res.data.data
        }
      })
      getExpertTypeData({ dicCode: 'POLITIC_COUNTENANCE' }).then(res => {
        if (res.data.status == 200) {
          this.expertZhengzhiData = res.data.data
        }
      })
      getExpertTypeData({ dicCode: 'EMPLOYMENT_SITUATION' }).then(res => {
        if (res.data.status == 200) {
          this.expertJiuyeData = res.data.data
        }
      })
      getExpertTypeData({ dicCode: 'VOCATIONAL_QUALIFICATION' }).then(res => {
        if (res.data.status == 200) {
          this.expertZigeData = res.data.data
        }
      })
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then(res => {
        console.log('获取行政区划------------->', res);
        let child = res.data.data
       if (child.children.length > 0) {
          for (let j = 0; j < child.children.length; j++) {
            if (child.children[j].children.length > 0) {
              for (let z = 0; z < child.children[j].children.length; z++) {
                if (child.children[j].children[z].children.length < 1) {
                  //判断children的数组长度
                  child.children[j].children[z].children = undefined
                }
              }
            } else {
              child.children[j].children = undefined
            }
          }
        } else {
          child.children = undefined
        }
        this.district = [child]
      })
    },
    changeCascader(value) {
      if (!value) {
        this.form.cityDistrictCode = this.$store.state.login.userDistCode
      }
    },
    submitForm() {
      this.$refs.form.validate(flag => {
        if (flag) {
          if (this.form.expertId) {
            if (this.form.districtCodes && this.form.districtCodes.length > 0) {
              if (this.form.districtCodes.length == 1) {
                this.form.cityDistrictCode = this.form.districtCodes[0]
                this.form.countyDistrictCode = ''
              } else if (this.form.districtCodes.length == 2) {
                this.form.cityDistrictCode = this.form.districtCodes[0]
                this.form.countyDistrictCode = this.form.districtCodes[1]
              }
            } else {
              this.form.cityDistrictCode = ''
              this.form.countyDistrictCode = ''
            }
            editExpertListData(this.form).then(res => {
              if (res.data.status == 200) {
                this.$message.success('操作成功')
                this.open = false
                this.reset()
                this.getList()
              } else {
                this.$message.error(res.data.msg)
              }
            })
          } else {
            if (this.form.districtCodes && this.form.districtCodes.length > 0) {
              if (this.form.districtCodes.length == 1) {
                this.form.cityDistrictCode = this.form.districtCodes[0]
                this.form.countyDistrictCode = ''
              } else if (this.form.districtCodes.length == 2) {
                this.form.cityDistrictCode = this.form.districtCodes[0]
                this.form.countyDistrictCode = this.form.districtCodes[1]
              }
            } else {
              this.form.cityDistrictCode = ''
              this.form.countyDistrictCode = ''
            }
            addExpertListData(this.form).then(res => {
              if (res.data.status == 200) {
                this.$message.success('操作成功')
                this.open = false
                this.reset()
                this.getList()
              } else {
                this.$message.error(res.data.msg)
              }
            })
          }
        }
      })
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6)
        this.form.latitude = data.location.lat.toFixed(6)
        this.form.regresidence = data.formatted_address
      } else {
        this.$message({
          message: '详情页面不可选点！',
          type: 'warning'
        })
      }
    },
    handleSelectionChange() {},
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].expertId
      }
    },
    exportExcel() {
      exportexpertListData({
        districtCode: this.districtCode
          ? this.districtCode[this.districtCode.length - 1]
          : '1',
        endTime: '',
        expertInfoIds: this.selection,
        levelCode: this.expertInfoIds,
        keywords: this.keywords,
        listOrder: {},
        speciality: this.speciality,
        nowPage: this.currentPage,
        orgCode: '',
        pageSize: this.size,
        range: 0,
        startTime: '',
        tenantId: ''
      }).then(response => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        const blob = new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '专家' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    }
  },
  mounted() {
    //   this.getList();
    //   this.getExpertType();
  }
}
</script>
<style lang="scss" scoped>
.enterpriseList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
        }
      }
    }
    .export {
      // margin-right: 20px;
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form_item {
    .form_title {
      font-size: 20px;
      color: #2f85de;
      margin-left: 20px;
    }
  }
}
</style>
<style lang="scss" scoped>
/deep/.el-dialog {
  height: 650px;
  overflow: hidden;
  .el-dialog__body {
    height: 80%;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
</style>