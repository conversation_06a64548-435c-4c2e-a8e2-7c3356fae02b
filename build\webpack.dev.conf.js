// 'use strict'
const utils = require("./utils");
const webpack = require("webpack");
const config = require("../config");
const merge = require("webpack-merge");
const path = require("path");
const baseWebpackConfig = require("./webpack.base.conf");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const FriendlyErrorsPlugin = require("friendly-errors-webpack-plugin");
const UglifyJsPlugin = require("uglifyjs-webpack-plugin");
const portfinder = require("portfinder");
const os = require("os");

const HOST = process.env.HOST;
const PORT = process.env.PORT && Number(process.env.PORT);

const devWebpackConfig = merge(baseWebpackConfig, {
  mode: "development",
  module: {
    rules: utils.styleLoaders({
      sourceMap: config.dev.cssSourceMap,
      usePostCSS: true,
    }),
  },
  // cheap-module-eval-source-map is faster for development
  devtool: config.dev.devtool,

  // these devServer options should be customized in /config/index.js
  devServer: {
    // clientLogLevel: 'none',
    historyApiFallback: {
      rewrites: [
        {
          from: /.*/,
          to: path.posix.join(config.dev.assetsPublicPath, "index.html"),
        },
      ],
    },
    onListening: function (devServer) {
      if (!devServer) {
        throw new Error("webpack-dev-server is not defined");
      }
      // const port = devServer.server.address().port;
      // console.log(
      //   `<i>\x1B[32m\x1B[1m [webpack-dev-server] On Your Network (IPv4): \x1B[0m http://${IP()}:${port}/`
      // );
    },
    //浏览器console提示
    client: {
      //提示等级info
      logging: "info",
    },
    // hot: true,
    // contentBase: false, // since we use CopyWebpackPlugin.
    // compress: true,
    host: HOST || config.dev.host,
    port: PORT || config.dev.port,
    open: config.dev.autoOpenBrowser,
    static: false,
    // overlay: config.dev.errorOverlay
    // ? { warnings: false, errors: true }
    // : false,
    // publicPath: config.dev.assetsPublicPath,
    proxy: config.dev.proxyTable,
    quiet: true, // necessary for FriendlyErrorsPlugin - 减少日志输出
  },
  // 文件监听配置
  watchOptions: {
    poll: config.dev.poll,
    ignored: /node_modules/, // 忽略node_modules文件夹，提高性能
    aggregateTimeout: 300, // 文件变化后延迟300ms再重新构建
  },
  //热更新时CMD的提示
  stats: {
    assets: false, //静态文件
    builtAt: true,
    modules: false, //模块热更新提示
    entrypoints: false,
    moduleTrace: false,
    version: false,
    hash: false,
    chunks: false,

    // providedExports: false,
  },
  plugins: [
    new webpack.DefinePlugin({
      "process.env": require("../config/dev.env"),
    }),
    new webpack.HotModuleReplacementPlugin(),
    new webpack.NamedModulesPlugin(), // HMR shows correct file names in console on update.
    new webpack.NoEmitOnErrorsPlugin(),
    // https://github.com/ampedandwired/html-webpack-plugin
    new HtmlWebpackPlugin({
      filename: "index.html",
      template: "index.html",
      inject: true,
    }),
    // copy custom static assets
    new CopyWebpackPlugin([
      {
        from: path.resolve(__dirname, "../static"),
        to: config.dev.assetsSubDirectory,
        ignore: [".*"],
      },
    ]),
  ],
});

module.exports = new Promise((resolve, reject) => {
  portfinder.basePort = process.env.PORT || config.dev.port;
  portfinder.getPort((err, port) => {
    if (err) {
      reject(err);
    } else {
      // publish the new Port, necessary for e2e tests
      // process.env.PORT = port;
      // add port to devServer config
      // devWebpackConfig.devServer.port = port;

      // Add FriendlyErrorsPlugin
      // devWebpackConfig.plugins.push(
      //   new FriendlyErrorsPlugin({
      //     compilationSuccessInfo: {
      //       // messages: [`Your application is running here: http://${devWebpackConfig.devServer.host}:${port}`],
      //     },
      //     onErrors: config.dev.notifyOnErrors
      //       ? utils.createNotifierCallback()
      //       : undefined,
      //   })
      // );

      resolve(devWebpackConfig);
    }
  });
});
