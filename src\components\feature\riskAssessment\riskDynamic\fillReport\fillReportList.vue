<template>
  <div class="enterpriseManagement">
    <div v-show="!TrendChartBool">
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span @click="goToSafety"
                ><a-icon type="home" theme="filled" class="icon" />
                企业数据填报统计
              </span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="showBreak">{{
              districtName
            }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <!-- <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getDate"
            unlink-panels
            size="mini"
            style="width:300px"
          >
          </el-date-picker> -->
          <div>
            <el-cascader
              size="mini"
              placeholder="请选择行政区划"
              :options="district"
              v-model="distCode"
              style="width: 200px"
              :props="{
                checkStrictly: true,
                value: 'distCode',
                label: 'distName',
                children: 'children',
                emitPath: false,
              }"
              clearable
              :show-all-levels="true"
              v-if="isShowDist"
            ></el-cascader>
          </div>
          <div>
            <el-input
              placeholder="请输入企业名称"
              v-model.trim="enterpName"
              size="mini"
              clearable
              @clear="clearEntName"
              style="width: 200px"
            >
            </el-input>
          </div>
          <div>
            <el-select
              v-model="approvalStatus"
              placeholder="企业当前状态"
              clearable
              size="mini"
              style="width: 200px"
            >
              <!-- <el-option
              v-for="item in approvalStatusData"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option> -->
            </el-select>
          </div>
          <div>
            <el-select
              v-model="approvalStatus"
              placeholder="状态"
              clearable
              size="mini"
            >
              <!-- <el-option
              v-for="item in approvalStatusData"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option> -->
            </el-select>
          </div>
          <div>
            <el-button type="primary" size="mini" @click="search"
              >查询</el-button
            >
          </div>

          <div>
            <CA-button type="primary" size="mini" plain @click="exportExcel"
              >导出</CA-button
            >
          </div>
        </div>
        <!-- <CA-button type="primary" size="mini" plain @click="gotoTrendAnalysis"
          ><a-icon type="bar-chart" class="chart-icon" />企业分析</CA-button
        > -->
      </div>
      <div class="table-main">
        <!-- <div class="table-top">
          <h2>企业数据填报统计</h2>
        </div> -->
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span>{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column label="行政区划" align="center" prop="districtName"> 
                <!-- <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <div
                      v-if="
                        row.districtCode == 429004 ||
                        row.districtCode == 429005 ||
                        row.districtCode == 429006 ||
                        row.districtCode == 429021
                      "
                    >
                      {{ row.districtName }}
                    </div>
                    <div v-else>
                      <span
                        v-if="$index != 0 && !showBreak && isXiaZuan"
                        @click="xiaZuan(row.districtCode, row.districtName)"
                        style="color: #3977ea; cursor: pointer"
                        >{{ row.districtName }}</span
                      >
                      <span v-else>{{ row.districtName }}</span>
                    </div>
                  </div>
                </template> -->
              </el-table-column>

              <el-table-column
                label="重大危险源数量"
                prop="companyAlarmNum"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    v-if="row.companyAlarmNum || tableData.length == 1"
                    @click="
                      openDialogAlarmDetailsList(
                        row.districtCode,
                        0,
                        row.districtName
                      )
                    "
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.companyAlarmNum }}</span
                  >
                  <span v-else>{{ row.companyAlarmNum }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="已填报"
                prop="companyAlarmClearedNum"
                align="center"
              >
              </el-table-column>
              <el-table-column
                label="未填报"
                prop="companyAlarmClearedNum"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <!-- v-if="row.companyAlarmClearedNum || tableData.length == 1" -->
                  <span                    
                    @click="xiaZuan(row.districtCode, row.districtName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.companyAlarmClearedNum }}</span
                  >
                  <!-- <span>{{ row.companyAlarmClearedNum }}</span> -->
                </template>
              </el-table-column>

              <el-table-column
                label="填报率"
                prop="totalAlarmNum"
                align="center"
              >
              </el-table-column>
              <el-table-column
                label="状态"
                prop="totalAlarmClearedNum"
                align="center"
              >
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <!-- <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage"
                    :page-size="100"
                    layout="total, prev, pager, next"
                    :total="1000"
                >
                </el-pagination> -->
          </div>
        </div>
       
      </div>
    </div>

    <fillReportDetailsList ref="fillReportDetailsList"></fillReportDetailsList>
  </div>
</template>
<script>
//D:\job\李大佬\dev-hbs\src\components\feature\riskAssessment\riskDynamic\fillReport\fillReportDetailsList.vue
import fillReportDetailsList from "@/components/feature/riskAssessment/riskDynamic/fillReport/fillReportDetailsList.vue";
import { alarmDistrictExport, alarmDistrictCount } from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {
    fillReportDetailsList,
  },
  data() {
    return {
      // 查询
      enterpName: "",
      approvalStatus: "",
      distCode: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      tableData: [],
      level: ["1", "2", "3", "4"],
      tableData: [],
      mode: "统计",
      showtable: true,
      currentPage: 1,
      total: 0,
      widthBox: 1400,

      showBreak: false,
      districtName: "",
      loading: false,
      selection: [],
      districtName: "",
      TrendChartBool: false,
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (144 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      endData: "",
      statrData: "",
      echartDist: [],
      alarm: [],
      companyAlarmClearedNum: [],
      clearedAlarmRate: [],
    };
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  methods: {
    getEnRiskList() {
      this.loading = true;
      alarmDistrictCount({
        districtCode: this.distCode,
        startTime:
          this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        endTime:
          this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss"),
        // size: 25,
        // current: this.currentPage,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data;
          // this.total = res.data.data.total;
          if (this.$store.state.login.user.user_type == "park") {
            for (let i = 0; i < this.tableData.length; i++) {
              //园区 ["告警企业数alarm", "消警企业数companyAlarmClearedNum", "企业消警率clearedAlarmRate"]
              this.echartDist[0] = this.tableData[i].districtName;
              this.alarm[0] = this.tableData[i].companyAlarmNum;
              this.companyAlarmClearedNum[0] =
                this.tableData[i].companyAlarmClearedNum;
              this.clearedAlarmRate[0] =
                this.tableData[i].companyAlarmClearedRate;
            }
          } else {
            for (let i = 1; i < this.tableData.length; i++) {
              //地市
              this.echartDist[i - 1] = this.tableData[i].districtName;
              this.alarm[i - 1] = this.tableData[i].companyAlarmNum;
              this.companyAlarmClearedNum[i - 1] =
                this.tableData[i].companyAlarmClearedNum;
              this.clearedAlarmRate[i - 1] =
                this.tableData[i].companyAlarmClearedRate;
            }
          }
        }
      });
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      this.getEnRiskList();
    },
    getDate() {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd hh:mm:ss");
    },
    // 导出
    exportExcel() {
      let list = [this.distCode, ...this.selection];
      alarmDistrictExport({
        districtCode: this.distCode,
        // distCodeList: list.length<=1?null:list,
        startTime:
          this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        endTime:
          this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss"),
        // isContainParent: list.length<=1?true:false,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "视频分析报警地区统计" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].districtCode;
      }
    },

    xiaZuan(districtCode, districtName) {
      this.districtName = districtName;
      this.showBreak = true;
      this.distCode = districtCode;
      this.getEnRiskList();
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      //   this.level = "";
      this.distCode = this.$store.state.login.userDistCode;
      this.getEnRiskList();
    },

    openDialogNotClearedAlarm(distCode, type, districtName) {
      this.$refs.NotClearedAlarm.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.NotClearedAlarm.getEntData(
        distCode,
        type,
        districtName,
        this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss")
      );
      this.$refs.NotClearedAlarm.getDistrict();
    },
    openDialogAlarmDetailsList(distCode, type, districtName) {
      this.$refs.fillReportDetailsList.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.fillReportDetailsList.getEntData(
        distCode,
        type,
        districtName,
        this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss")
      );
      this.$refs.fillReportDetailsList.getDistrict();
    },
    search() {
      this.currentPage = 1;
      this.getEnRiskList();
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data;
      this.getEnRiskList();
    },
    // gotoTrendAnalysis() {
    //   this.TrendChartBool = true;
    //   this.$nextTick(() => {
    //     this.$refs.TrendChart.getData();
    //   });
    // },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getEnRiskList();
    this.$setEchart("myCharted", 250, 250);
    this.$nextTick(function () {
      this.drawLine();
    });
  },
  watch: {
    // mode(newValue, oldValue) {
    //   if (newValue == "统计") {
    //     this.showtable = true;
    //   } else {
    //     this.showtable = false;
    //   }
    // },
    //观察option的变化
    // tableData: {
    //   handler(newVal, oldVal) {
    //     this.drawLine();
    //   },
    //   deep: true,
    //   // immediate: true,
    // },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .seach-part {
    // font-weight: 600;
    // display: flex;
    // justify-content: space-between;
    // padding-bottom: 10px;
    margin-bottom: 15px;
    // margin-top: 20px;
    // border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      // width: 500px;
      display: flex;
      width: 100%;
      div {
        margin-right: 5px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // padding: 10px 0;
      margin-bottom: 10px;
      height: 40px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
        float: left;
      }
      .radio {
        float: right;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
