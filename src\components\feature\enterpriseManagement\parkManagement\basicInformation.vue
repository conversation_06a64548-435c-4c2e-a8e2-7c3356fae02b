<template>
  <div class="basicInformation" v-loading="loading">
    <div class="div1">
      <div class="title">
        <span>园区基本信息</span>     
      </div>
      <div class="table">
        <ul class="container">
          <li>
            <div class="l">园区名称</div>
            <div class="r">{{ enterprise.parkName }}</div>
          </li>
          <li class="">
            <div class="l">行政区划</div>
           <div class="r">{{ enterprise.districtName }}</div>
          </li>
          <li class="">
            <div class="l">占地面积(㎡)</div>
            <div class="r">{{ enterprise.area }}</div>          
          </li>
          <li>
            <div class="l">入园企业数量(个)</div>
            <div class="r">{{ enterprise.intoEnterNum }}</div>
          </li>
          <li>
            <div class="l">在建企业数(个)</div>
            <div class="r">{{ enterprise.underEnterNum }}</div>
          </li>
          <li >
            <div class="l">园区级别</div>
            <div class="r">{{ enterprise.levelName }}</div>
          </li>
          <li >
            <div class="l">成立日期</div>
            <div class="r">{{ enterprise.setupDate }}</div>
          </li>
          <li >
            <div class="l">员工数量(人)</div>
            <div class="r">{{ enterprise.employNum }}</div>
          </li>
          <!-- <li v-if="enterprise.enterpriseType == '04'">
            <div class="l">危险化学品安全使用许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li> -->
          <li>
            <div class="l">值班室电话</div>
            <div class="r">{{ enterprise.employNum }}</div>
          </li>
          <li>
            <div class="l">园区负责人姓名</div>
            <div class="r">{{ enterprise.respPersonName }}</div>
          </li>
          <li>
            <div class="l">负责人联系电话</div>
            <div class="r">{{ enterprise.respPersonTel }}</div>
          </li>
          <li>
            <div class="l">安环部长姓名</div>
            <div class="r">{{ enterprise.secretaryPersonName }}</div>
          </li>
          <li>
            <div class="l">安环部长移动电话</div>
            <div class="r">{{ enterprise.secretaryPersonTel }}</div>
          </li>
          <li>
            <div class="l">经度/纬度</div>
            <div class="r">{{ (enterprise.longitude || '' ) +' / '+ (enterprise.latitude || '') }}</div>
          </li>
          <li>
            <div class="l">园区地址</div>
            <div class="r">{{ enterprise.adress }}</div>
          </li>
          <li>
            <div class="l">中控室负责人</div>
            <div class="r">{{ enterprise.centralPersonName }}</div>
          </li>
          <li class="">
            <div class="l">中控室办公电话</div>
            <div class="r">{{ enterprise.centralPersonTel }}</div>
          </li>
          <li>
            <div class="l">生产部长</div>
            <div class="r">{{ enterprise.prodPersonName }}</div>
          </li>
          <li>
            <div class="l">应急联络员</div>
            <div class="r">{{ enterprise.eploPersonName }}</div>
          </li>
          <li>
            <div class="l">应急联络员手机</div>
            <div class="r">{{ enterprise.eploPersonTel }}</div>
          </li>
          <li>
            <div class="l">调度室负责人</div>
            <div class="r">{{ enterprise.switchyardPersonName }}</div>
          </li>
          <li>
            <div class="l">调度室负责人手机</div>
            <div class="r">{{ enterprise.switchyardPersonTel }}</div>
          </li>
          <li>
            <div class="l">内保负责人</div>
           <div class="r">{{ enterprise.uchihoPersonName }}</div>
          </li>
          <li>
            <div class="l">内保负责人手机</div>
            <div class="r">{{ enterprise.uchihoPersonTel }}</div>
          </li>
          <li>
            <div class="l">企业兼职消防员人数(人)</div>
           <div class="r">{{ enterprise.partFiremenNum }}</div>
          </li>
           <li>
            <div class="l"></div>
            <div class="r"></div>
          </li>
          
          <li>
            <div class="l">专职消防员(人)</div>
            <div class="r">{{ enterprise.fullFiremenNum }}</div>
          </li>
          <li class="lang bottom">
            <div class="l">企业简介</div>
            <div class="r">
             {{ enterprise.parkSurvey }}
            </div>
          </li>
        </ul>
      </div>

       <div class="title">
        <span>园区图纸</span>     
      </div>

 <div class="table">
        <ul class="container">
          <li class="bottom">
            <div class="l">园区平面图纸</div>
            <div class="r"> {{ enterprise.parkPlaneAttach }}</div>
          </li>
          <li class="bottom">
            <div class="l">园区疏散图</div>
            <div class="r"> {{ enterprise.parkEvacAttach }}</div>
           
          </li>
          <li class="bottom">
            <div class="l">园区四色图</div>
            <div class="r"> {{ enterprise.parkFourColorAttach }}</div>           
          </li>        
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { getInformationBasicInfo,getParkView } from "@/api/entList";
import { getfindByEnterpId } from "../../../../api/user";
export default {
  //import引入的组件
  name: "basicInformation",
  components: {
  },
  props: ["level"],
  data() {
    return {
      enterprise: {},
      enterpriseId: {},
      danger: [],
      hazarchem: [],
      regprocess: [],
      loading: false,
      activeName: "FillInformation",
      dialogVisible: false,
      orgCode: "",
      tabLoading: false,
    };
  },
  //方法集合
  methods: {
    handleClick() {
      this.$nextTick(() => {
        if (this.activeName === "FocusOnAndMajor") {
          this.$refs[this.activeName].getData({
            enterpriseId: this.enterpriseId,
            danger: this.danger,
            hazarchem: this.hazarchem,
            regprocess: this.regprocess,
            level: this.level,
          });
        } else {
          this.$refs[this.activeName].getData(this.enterpriseId);
        }
      });
    },    
    getData(id) {
      this.enterpriseId = id;
      this.loading = true;
      getParkView(id).then((res) => {
        this.$nextTick(() => {  
          console.log(res.data,'这是园区管理-园区详情信息')       
          this.enterprise = res.data.data;         
          this.loading = false;
        });
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素
  mounted() {},
 
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 15px;
}
.basicInformation {
  overflow: hidden;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .back {
    height: 40px;
  }
  .div1 {
    // margin-top: 20px;
    .title {
      width: 100%;
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
      display: flex;
      justify-content: space-between;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 48%;
            padding: 1%;
            word-break: break-all;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            word-break: break-all;
          }
        }
        .lang {
          list-style-type: none;
          width:99.97%;
          display: flex;
          border-top: 1px solid #eaedf2;
          border-right: 1px solid #eaedf2;
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 16.65%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            // background-color: rgb(242, 242, 242);
          }
          .r {
            width: 82.5%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: left;
            padding: 0px 10px;
            word-break: break-all;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            background: rgb(242, 246, 255);
          }
          .r {
            width: 50%;
            word-break: break-all;
          }
        }
      }
    }
  }
  .div2 {
    margin-top: 20px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 600;
        height: 40px;
        line-height: 40px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 12.5%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-all;
        }
        .liLine {
          list-style-type: none;
          width: 12.5%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          text-align: center;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
  .div3 {
    margin-top: 20px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 600;
        height: 40px;
        line-height: 40px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          // height: 40px;
          // line-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-all;
        }
        .liLine {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          text-align: center;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
  .div4 {
    margin-top: 20px;
    padding-bottom: 40px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 600;
        height: 40px;
        line-height: 40px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          // height: 40px;
          // line-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-all;
        }
        .liLine {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          // height: 40px;
          // line-height: 40px;
          text-align: center;
          display: flex;
          justify-content: space-around;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
}
</style>
