<template>
  <div class="personnelPositioning">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 人员定位分析
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <div class="searchTop">
      <!-- <div>
        <el-select
          v-model="enterName"
          size="mini"
          placeholder="请选择企业"
          clearable
        >
          <el-option
            v-for="item in options2"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="area"
          size="mini"
          placeholder="请选择区域"
          clearable
        >
          <el-option
            v-for="item in options1"
            :key="item.id"
            :label="item.label"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </div> -->

      <!-- <div>
        <el-button type="primary" size="mini"
          ><span class="el-icon-time"></span>历史警告</el-button
        >
      </div> -->
    </div>

    <div class="personnelBox">
      <div class="personnelLeft">
        <!-- <img
          style="width: 100%;heigth:300px"
          src="../../../../../static/img/position/person_bg.png"
        /> -->

        <EgisMapPerson
          :datas="formData"
          ref="detailMap"
          style="height: 700px"
          @mapCallback="mapcallback"
        ></EgisMapPerson>

        <!-- <div class="lend">
          <img src="../../../../../static/img/position/lend.png" />
        </div> -->

        <!-- <div class="mapTab">
          <div class="Item">
            <div
              :class="['Electronics', active == 'Electronics' ? 'active' : '']"
              @click="clickMap('Electronics')"
            ></div>
            <div
              :class="['videoSe', active == 'videoSe' ? 'active' : '']"
              @click="clickMap('videoSe')"
            ></div>
          </div>
        </div> -->
      </div>
      <div class="personnelRight">
        <div class="personnelTot">
          <span class="icon"></span>
          <div class="item">
            <p>总数</p>
            <span>3</span>
          </div>
        </div>

        <!-- <div class="personnelClass">
          <div
            :class="['itemBox', 'itemBox' + el.type]"
            v-for="(el, index) of personnelClassData"
            :key="index"
          >
            <span class="icon"></span>
            <div class="itemR">
              <p>{{ el.name }}</p>
              <span>{{ el.num }}</span>
            </div>
          </div>
        </div> -->

        <div class="personnelList">
          <div class="tit">监测人员列表</div>

          <div
            class="item"
            v-for="(el, index) of personnelListData"
            :key="index"
          >
            <div class="itemL">
              <span :class="['icon', 'icon' + el.type]"></span>
              <span>{{ el.name }}</span>
            </div>
            <div class="itemR" @click="clickMapTo(el)">
              <span class="iconPerson"></span>
              <span class="iconAdress"></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
const mapConfig = require("@/assets/json/map.json");
import EgisMapPerson from "@/components/common/packages/EgisMapPerson/index.vue";

// src\components\common\packages\EgisMapPerson
export default {
  name: "personnelPositioning",
  components: { EgisMapPerson },
  data() {
    return {
      formData: {
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
      },
      active: "Electronics",
      personnelClassData: [
        { name: "SOS", num: "1", type: "1" },
        { name: "报警", num: "2", type: "2" },
        { name: "正常", num: "6", type: "3" },
        { name: "离线", num: "2", type: "4" },
      ],
      personnelListData: [
        // { name: "宋江", type: "1" },
        // { name: "卢俊义", type: "2" },
        {
          name: "吴用",
          type: "3",
          longitude: 114.311467,
          latitude: 30.580942,
          phone: "15023234534",
          time: "20120-12-13 10:30",
          adress: "厂区一",
        },
        {
          name: "史进",
          type: "3",
          longitude: 114.371467,
          latitude: 30.580942,
          phone: "1323434567",
          time: "20120-12-13 10:30",
          adress: "厂区二",
        },
        {
          name: "燕青",
          type: "3",
          longitude: 114.371467,
          latitude: 30.680942,
          phone: "1323434567",
          time: "20120-12-13 10:30",
          adress: "厂区三",
        },
        // { name: "陈瑞明", type: "3" },
        // { name: "王博涛", type: "3" },
        // { name: "朱聪明", type: "3" },
        // { name: "秦明", type: "4" },
        // { name: "张希月", type: "4" },
      ],
      enterName: "1",
      area: "B",
      options1: [{ id: "1", label: "B区", value: "B" }],
      options2: [{ id: "1", label: "武汉有机实业有限公司", value: "1" }],
    };
  },
  watch: {},
  created() {},
  methods: {
    mapcallback(data) {
      debugger;
      // if (!this.disabled) {
      //   // 标点赋值
      //   this.form.longitude = data.location.lon.toFixed(6);
      //   this.form.latitude = data.location.lat.toFixed(6);
      //   this.form.altitude = data.location.altitude
      //     ? data.location.altitude.toFixed(6)
      //     : "0";
      //   this.form.addr = data.formatted_address;
      // } else {
      //   this.$message({
      //     message: "详情页面不可选点！",
      //     type: "warning",
      //   });
      // }
    },
    clickMapTo(val) {
      this.$refs["detailMap"].clickItem(val);
    },
    clickMap(val) {
      if (val == "Electronics") {
        this.active = "Electronics";
      } else this.active = "videoSe";
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
    }),
  },
  mounted() {
    debugger;
    this.$refs["detailMap"].aroundSearch(this.personnelListData);
    console.log(this.$refs["detailMap"]);
  },
};
</script>

<style lang="scss" scoped>
.personnelPositioning {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .searchTop {
    display: flex;
    justify-content: space-between;
  }
  .personnelBox {
    display: flex;
    margin: 10px 0 0 0;
    justify-content: space-between;
    .personnelLeft {
      width: 70%;
      // height: calc(100% - 150px);
      height: 700px;
      justify-content: space-between;
      border: 1px solid #e7e7e7;
      position: relative;
      .lend {
        position: absolute;
        left: 20px;
        bottom: 20px;
      }
      .mapTab {
        position: absolute;
        top: 20px;
        right: 20px;
        .Electronics {
          background: url("../../../../../static/img/position/电子围栏.png");
          width: 76px;
          height: 64px;
          margin: 0 0 10px 0;
          cursor: pointer;
        }
        .videoSe {
          background: url("../../../../../static/img/position/视频设备.png");
          width: 76px;
          height: 64px;
          cursor: pointer;
        }
        .Electronics.active {
          background: url("../../../../../static/img/position/电子围栏-选中.png");
          width: 76px;
          height: 64px;
        }
        .videoSe.active {
          background: url("../../../../../static/img/position/视频设备-选中.png");
          width: 76px;
          height: 64px;
        }
      }
    }
    .personnelRight {
      width: 29%;
      .personnelTot {
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e7e7e7;
        padding: 0 0 10px 0;
        margin: 0 0 10px 0;
        .icon {
          background: url("../../../../../static/img/position/icon-总数.png");
          width: 46px;
          height: 46px;
          margin: 0 20px 0 0;
        }
        .item {
          p {
            margin: 0 0 -5px 0;
          }
          span {
            font-size: 25px;
            color: #000;
            font-weight: bold;
          }
        }
      }
      .personnelClass {
        display: flex;
        justify-content: space-between;
        .itemBox {
          display: flex;
          align-items: center;
          .icon {
            width: 36px;
            height: 36px;
            margin: 0 10px 0 0;
          }
          .itemR {
            p {
              margin: 0 0 -5px 0;
            }
            span {
              font-size: 20px;
              color: #000;
              font-weight: bold;
            }
          }
        }
        .itemBox.itemBox1 .icon {
          background: url("../../../../../static/img/position/icon-sos.png");
        }
        .itemBox.itemBox1 .itemR span {
          color: rgb(139, 6, 6);
        }
        .itemBox.itemBox2 .itemR span {
          color: red;
        }
        .itemBox.itemBox2 .icon {
          background: url("../../../../../static/img/position/icon报警.png");
        }
        .itemBox.itemBox3 .icon {
          background: url("../../../../../static/img/position/icon-离线.png");
        }
        .itemBox.itemBox4 .icon {
          background: url("../../../../../static/img/position/icon-正常.png");
        }
      }
    }
  }
  .personnelList {
    .tit {
      font-size: 20px;
      color: #000;
      position: relative;
      padding: 0 0 0 12px;
      margin: 20px 0 0 0;
    }
    .tit::before {
      position: absolute;
      padding: 0 0 0 12px;
      content: "";
      left: 0;
      top: 50%;
      border-left: 2px solid #409eff;
      height: 18px;
      margin-top: -9px;
    }
    .item {
      display: flex;
      justify-content: space-between;
      padding: 15px 0;
      border-bottom: 1px solid #e7e7e7;
      align-items: center;
      .itemL {
        display: flex;
        align-items: center;
        .icon {
          width: 56px;
          height: 30px;
          margin: 0 10px 0 0;
          display: block;
        }
        .icon.icon1 {
          background: url("../../../../../static/img/position/SOS.png");
        }
        .icon.icon2 {
          background: url("../../../../../static/img/position/报警.png");
        }
        .icon.icon3 {
          background: url("../../../../../static/img/position/正常.png");
        }
        .icon.icon4 {
          background: url("../../../../../static/img/position/离线.png");
        }
      }
      .itemR {
        display: flex;
        align-items: center;
        cursor: pointer;
        .iconPerson {
          background: url("../../../../../static/img/position/人员信息.png");
          display: block;
          width: 20px;
          height: 16px;
          margin: 0 20px 0 0;
        }
        .iconPerson:hover {
          background: url("../../../../../static/img/position/人员信息-hover.png");
          display: block;
          width: 20px;
          height: 16px;
        }
        .iconAdress {
          width: 16px;
          height: 18px;
          background: url("../../../../../static/img/position/轨迹.png");
        }
        .iconAdress:hover {
          width: 16px;
          height: 18px;
          background: url("../../../../../static/img/position/轨迹-hover.png");
        }
      }
    }
  }
}
</style>
