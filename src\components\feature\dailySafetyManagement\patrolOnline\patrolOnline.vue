<template>
  <div class="patrolOnline">
    <div class="header">
      <el-button type="primary" size="mini" @click="onClick" v-if="$store.state.login.user.isDanger == '1'"
        >一键自动巡查</el-button
      >
    </div>
    <div v-loading="loading">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        style="width: 100%"
      >
        <el-table-column
          prop="areaName"
          label="区划"
          width="150"
          fixed="left"
          show-overflow-tooltip
        >
        </el-table-column>
        <el-table-column label="系统在线情况">
          <el-table-column prop="onlineRate" label="在线率" width="100">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.onlineRate == 0"
                @click="openSystem(row.areaCode)"
                >{{ row.onlineRate }}%</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="sysOnlineEnterprise"
            label="接入企业"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.sysAccessEnterprise == 0"
                @click="openSystem(row.areaCode, '', '')"
                >{{ row.sysAccessEnterprise }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="sysOnlineEnterprise"
            label="在线企业"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.sysOnlineEnterprise == 0"
                @click="openSystem(row.areaCode, '0', '')"
                >{{ row.sysOnlineEnterprise }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="sysOfflineReported"
            label="离线已报备"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.sysOfflineReported == 0"
                @click="openSystem(row.areaCode, '1', '1')"
                >{{ row.sysOfflineReported }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="sysShutdownHasBeenReported"
            label="停产已报备"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.sysShutdownHasBeenReported == 0"
                @click="openSystem(row.areaCode, '', '3')"
                >{{ row.sysShutdownHasBeenReported }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="sysOfflineWithoutReport"
            label="未报备"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.sysOfflineWithoutReport == 0"
                @click="openSystem(row.areaCode, '', '2')"
                >{{ row.sysOfflineWithoutReport }}</el-button
              >
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="视频监控在线情况">
          <el-table-column prop="videoOnlineRate" label="在线率" width="110">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.videoOnlineRate == 0"
                @click="openVideo(row.areaCode, '0', '')"
                >{{ row.videoOnlineRate }}%</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="videoAccessEnterprise"
            label="接入企业"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.videoAccessEnterprise == 0"
                @click="openVideo(row.areaCode, '', '')"
                >{{ row.videoAccessEnterprise }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="videoOnlineEnterprise"
            label="在线企业"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.videoOnlineEnterprise == 0"
                @click="openVideo(row.areaCode, '0', '')"
                >{{ row.videoOnlineEnterprise }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="videoOfflineReported"
            label="离线已报备"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.videoOfflineReported == 0"
                @click="openVideo(row.areaCode, '1', '0')"
                >{{ row.videoOfflineReported }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="videoOfflineNotReported"
            label="离线未报备"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.videoOfflineNotReported == 0"
                @click="openVideo(row.areaCode, '1', '1')"
                >{{ row.videoOfflineNotReported }}</el-button
              >
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="安全承诺情况">
          <el-table-column prop="promiseRate" label="承诺率" width="100">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.promiseRate == 0"
                @click="openSecurityCommitments(row.areaCode, '', '')"
                >{{ row.promiseRate }}%</el-button
              >
            </template>
          </el-table-column>
          <el-table-column prop="safetyCommitment" label="应承诺" width="100">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.safetyCommitment == 0"
                @click="openSecurityCommitments(row.areaCode, '', '')"
                >{{ row.safetyCommitment }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="safetyCommitmentCommitted"
            label="已承诺"
            width="100"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.safetyCommitmentCommitted == 0"
                @click="openSecurityCommitments(row.areaCode, '0', '')"
                >{{ row.safetyCommitmentCommitted }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="safetyCommitmentNotPromised"
            label="未承诺"
            width="100"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.safetyCommitmentNotPromised == 0"
                @click="openSecurityCommitments(row.areaCode, '1', '')"
                >{{ row.safetyCommitmentNotPromised }}</el-button
              >
            </template>
          </el-table-column>
        </el-table-column>

        <el-table-column label="超24小时未消警指标数" width="190">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              :disabled="row.latestAlarmTargetCount == 0"
              @click="openNoWarningIndicators(row.areaCode)"
              >{{ row.latestAlarmTargetCount }}</el-button
            >
          </template>
        </el-table-column>

        <el-table-column label="预警及通报处置情况">
          <el-table-column prop="warnCount" label="预警未消警" width="110">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.warnCount == 0"
                @click="openWarningAndNotification(row.areaCode, '预警未消警')"
                >{{ row.warnCount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="warnNoRespCount"
            label="预警未反馈"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.warnNoRespCount == 0"
                @click="openWarningAndNotification(row.areaCode, '预警未反馈')"
                >{{ row.warnNoRespCount }}</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            prop="warnNoticeNoRespCount"
            label="通报未反馈"
            width="110"
          >
            <template slot-scope="{ row }">
              <el-button
                type="text"
                :disabled="row.warnNoticeNoRespCount == 0"
                @click="openWarningAndNotification(row.areaCode, '通报未反馈')"
                >{{ row.warnNoticeNoRespCount }}</el-button
              >
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="{ row, $index }">
            <!-- {{ disabled[$index]  }} --bb -->
            <el-button
              type="text"
              v-if="disabled[$index] && $store.state.login.user.isDanger == '1'"
              :loading="$index === btnLoadingIndex"
              @click="inspectAdd(row, $index)"
              v-show="$index != 0"
            >
              加入巡查</el-button
            >
            <el-button v-else :disabled="true" type="text"
              >已加入巡查</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div>
      <el-dialog
        title="系统在离线情况表"
        :visible.sync="systemVisible"
        width="1300px"
        v-dialog-drag
        top="5vh"
        :close-on-click-modal="false"
        @close="closeTable('system')"
        :key="dialogKey + 'system'"
      >
        <System
          ref="system"
          :districtProps="districtVal"
          :isOnlineProps="systemIsOnlineProps"
          :isReportedProps="systemIsReportedProps"
        ></System>
      </el-dialog>
      <el-dialog
        title="视频监控在线情况"
        :visible.sync="videoVisible"
        width="1000px"
        v-dialog-drag
        top="5vh"
        :close-on-click-modal="false"
        @close="closeTable('video')"
        :key="dialogKey + 'video'"
      >
        <Video
          ref="video"
          :districtProps="districtVal"
          :isOnlineProps="videoIsOnlineProps"
          :isReportedProps="videoIsReportedProps"
        ></Video>
      </el-dialog>

      <el-dialog
        title="安全承诺情况"
        :visible.sync="securityCommitmentsVisible"
        width="1300px"
        v-dialog-drag
        top="5vh"
        :close-on-click-modal="false"
        @close="closeTable('securityCommitments')"
        :key="dialogKey + 'securityCommitments'"
      >
        <SecurityCommitments
          ref="securityCommitments"
          :districtProps="districtVal"
          :isCommitProps="isCommitProps"
          :isCommitAbnormalProps="isCommitAbnormalProps"
        ></SecurityCommitments>
      </el-dialog>
      <el-dialog
        title="超24小时未消警指标"
        :visible.sync="noWarningIndicatorsVisible"
        width="1600px"
        v-dialog-drag
        top="5vh"
        :close-on-click-modal="false"
        @close="closeTable('noWarningIndicators')"
        :key="dialogKey + 'noWarningIndicators'"
      >
        <NoWarningIndicators
          ref="noWarningIndicators"
          :districtProps="districtVal"
        ></NoWarningIndicators>
      </el-dialog>
      <el-dialog
        title="预警及通报处置情况"
        :visible.sync="warningAndNotificationVisible"
        width="1400px"
        v-dialog-drag
        :close-on-click-modal="false"
        top="5vh"
        @close="closeTable('warningAndNotification')"
        :key="dialogKey + 'warningAndNotification'"
      >
        <WarningAndNotification
          ref="warningAndNotification"
          :districtProps="districtVal"
        ></WarningAndNotification>
      </el-dialog>
    </div>
    <!-- 成功加入巡查提示 -->
    <transition name="el-fade-in-linear">
      <div class="model" v-show="visible">
        <div class="box">
          <a-icon type="close" class="close" @click="handleOff()" />
          <div class="content">已生成巡查记录</div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
const System = () => import("./system");
const Video = () => import("./video");
const SecurityCommitments = () => import("./SecurityCommitments");
const NoWarningIndicators = () => import("./noWarningIndicators");
const WarningAndNotification = () => import("./warningAndNotification");
import { MessageBox } from "element-ui";
import {
  getOnlinePatrolList,
  postOnlinePatrolInspectAdd,
  oneClick,
} from "@/api/riskAssessment";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {
    System,
    Video,
    SecurityCommitments,
    NoWarningIndicators,
    WarningAndNotification,
  },
  data() {
    return {
      visible: false,
      systemVisible: false,
      videoVisible: false,
      securityCommitmentsVisible: false,
      noWarningIndicatorsVisible: false,
      warningAndNotificationVisible: false,
      tableData: [],
      districtVal: "",
      loading: false,
      btnLoadingIndex: null,
      disabled: [],
      systemIsOnlineProps: "",
      systemIsReportedProps: "",
      videoIsOnlineProps: "",
      videoIsReportedProps: "",
      isCommitProps: "",
      isCommitAbnormalProps: "",
      dialogKey: new Date().getTime(),
      patrolData: [],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
  },
  methods: {
    //一键巡查
    onClick() {
      this.loading = true;
      oneClick({ distCode: this.$store.state.login.userDistCode })
        .then((res) => {
          if (res.data.code == 0) {
            this.loading = false;
            this.patrolData = res.data.data;
            this.patrolData.forEach((item) => {
              item.genAreaCode = this.$store.state.login.userDistCode;
            });
            this.oneAutomaticPatrolData();
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    oneAutomaticPatrolData() {
      MessageBox.confirm("是否确定一键生成巡查?", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          postOnlinePatrolInspectAdd(this.patrolData).then((res) => {
            if (res.data.code === 0) {
              this.$message.success("一键巡查成功");
              this.$store.commit(
                "controler/updateAutomaticPatrolData",
                res.data.data
              );
              this.$parent.$parent.$parent.fatherMethod();
              this.$parent.$parent.$parent.handleClick();
            } else {
              this.$message.error("一键巡查失败");
            }
          });
        })
        .catch(() => {});
    },
    closeTable(type) {
      this.$nextTick(() => {
        this.$refs[type].closeTable();
        //重置弹窗位置
        this.dialogKey = new Date().getTime();
      });
    },
    tipPatrol() {
      this.visible = true;
    },
    handleOff() {
      this.visible = false;
    },
    openSystem(areaCode, isOnline, isReported) {
      this.systemVisible = true;
      this.districtVal = areaCode;
      this.systemIsOnlineProps = isOnline;
      this.systemIsReportedProps = isReported;
      // console.log(this.systemIsReportedProps);
      this.$nextTick(() => {
        this.$refs.system.getData();
      });
    },
    openVideo(areaCode, isOnline, isReported) {
      this.videoVisible = true;
      this.districtVal = areaCode;
      this.videoIsOnlineProps = isOnline;
      this.videoIsReportedProps = isReported;
      this.$nextTick(() => {
        this.$refs.video.getData();
      });
    },
    openSecurityCommitments(areaCode, isCommit, isCommitAbnormal) {
      this.securityCommitmentsVisible = true;
      this.districtVal = areaCode;
      this.isCommitProps = isCommit;
      this.isCommitAbnormalProps = isCommitAbnormal;
      this.$nextTick(() => {
        this.$refs.securityCommitments.getData();
      });
    },
    openNoWarningIndicators(areaCode) {
      this.noWarningIndicatorsVisible = true;
      this.districtVal = areaCode;
      this.$nextTick(() => {
        this.$refs.noWarningIndicators.getData();
      });
    },
    openWarningAndNotification(areaCode, isActive) {
      this.warningAndNotificationVisible = true;
      this.districtVal = areaCode;
      this.$nextTick(() => {
        if (isActive == "预警未消警") {
          this.$refs.warningAndNotification.warnStatus = "0";
        }else{
          this.$refs.warningAndNotification.isFeedback = "0";
        }
        this.$refs.warningAndNotification.getData();
      });
    },
    getList() {
      this.loading = true;
      getOnlinePatrolList({
        distParentCode: this.$store.state.login.userDistCode,
        distCode: this.$store.state.login.userDistCode,
        current: 1,
        size: 60,
      }).then((res) => {
        this.tableData = res.data.data;
        for (let i = 0; i < this.tableData.length; i++) {
          this.disabled[i] = true;
        }
        this.loading = false;
      });
    },
    //加入巡查
    inspectAdd(row, index) {
      // console.log(row);
      this.btnLoadingIndex = index;
      postOnlinePatrolInspectAdd([
        {
          genAreaCode: this.$store.state.login.userDistCode, //巡查人所在区域编码
          areaCode: row.areaCode, //   巡查区域编码
          areaName: row.areaName, // 巡查区域名称
          onlineRate: row.onlineRate, //在线率
          sysAccessEnterprise: row.sysAccessEnterprise, //系统接入企业数
          sysOnlineEnterprise: row.sysOnlineEnterprise, // 系统在线企业数
          sysOfflineReported: row.sysOfflineReported, // 系统离线已报备
          sysShutdownHasBeenReported: row.sysShutdownHasBeenReported, //系统关停已报备
          sysOfflineWithoutReport: row.sysOfflineWithoutReport, // 系统未报备
          onlineSort: row.onlineSort, //在线率排名
          videoOnlineRate: row.videoOnlineRate, //视频在线率
          videoAccessEnterprise: row.videoAccessEnterprise, // 视频接入企业
          videoOnlineEnterprise: row.videoOnlineEnterprise, //视频在线企业
          videoOfflineReported: row.videoOfflineReported, //视频离线已报备
          videoOfflineNotReported: row.videoOfflineNotReported, //视频离线未报备
          videoOnlineSort: row.videoOnlineSort, // 视频在线率排名
          promiseRate: row.promiseRate, //安全承诺率
          safetyCommitment: row.safetyCommitment, //安全承诺应承诺数
          safetyCommitmentCommitted: row.safetyCommitmentCommitted, //安全承诺已承诺
          safetyCommitmentNotPromised: row.safetyCommitmentNotPromised, //安全承诺未承诺
          promiseSort: row.promiseSort, //安全承诺排名
          latestAlarmTargetCount: row.latestAlarmTargetCount, //超24小时未消警数
          latestAlarmTargetInterval: row.latestAlarmTargetInterval, //时间统计
          maxDuration: row.maxDuration, //最大告警时间
          warnCount: row.warnCount, //预警未消警
          warnNoRespCount: row.warnNoRespCount, //预警未反馈
          warnNoticeNoRespCount: row.warnNoticeNoRespCount, //通报未反馈
        },
      ]).then((res) => {
        if (res.data.code === 0) {
          this.btnLoadingIndex = null;
          this.visible = true;
          this.disabled[index] = false;
          setTimeout(() => {
            this.visible = false;
          }, 3000);
        } else {
          this.$message.error("加入巡查失败");
        }

        //
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.patrolOnline {
  // width: 700px;
  // height: 70vh;
  overflow: auto;
  .header {
    display: flex;
    flex-direction: row-reverse;
    justify-content: space-between;
    margin-bottom: 20px;
    .title {
      font-size: 18px;
      width: 100%;
      text-align: left;
      font-weight: 900;
    }
  }
  .model {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100000;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    & > .box {
      width: 524px;
      height: 415px;
      position: relative;
      border-radius: 5px;
      background-image: url("../../../../../static/img/SpotCheckDialogBg.png");
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .close {
        position: absolute;
        right: 30px;
        top: 10px;
        font-size: 18px;
      }
      .content {
        width: 100%;
        text-align: center;
        margin-top: 260px;
        color: #545c65;
        font-weight: 900;
        font-size: 16px;
      }
    }
  }
}
</style>
