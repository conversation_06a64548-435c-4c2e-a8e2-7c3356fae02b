<template>
  <div v-loading="loading">
    <div class="work-heard">
      <h2>设备异常报备</h2>
      <p>
        <el-button
          size="mini"
          type="primary"
          icon="el-icon-plus"
          plain
          @click="edit('')"
          >申报</el-button
        >
      </p>
      <span @click="more">更多</span>
    </div>
    <div class="zhongdian-main">
      <!-- <el-radio-group
        size="small"
        v-model="mode"
        style="margin-bottom:10px"
        >
        <el-radio-button label="0">设备设施</el-radio-button>
        <el-radio-button label="1">视频设备</el-radio-button>
        </el-radio-group> -->
      <div class="shebei-box">
        <el-collapse accordion v-if="tableData.length > 0">
          <el-collapse-item v-for="item in tableData" :key="item.id">
            <template slot="title">
              <div class="zhongdian-heard">
                <!-- <p class="dangerName">{{ mode == "0" ? (item.dangerName?item.dangerName :'全部的重大危险源(全厂设备)'):item.equipment}}</p> -->
                <p class="dangerName">
                  {{ item.type == "0" ? "设备设施报备" : "视频设备报备" }}
                </p>
                <i
                  class="bianji"
                  v-if="item.state == '0'"
                  @click="edit(item.id)"
                ></i
                ><i class="chakan" @click="look(item.id)"></i
                ><span v-if="item.state == '0'" style="color: #ee8742"
                  >待审核</span
                ><span v-else-if="item.state == '1'" style="color: #58bd77"
                  >审核通过</span
                ><span v-else-if="item.state == '2'" style="color: #ff1818"
                  >审核驳回</span
                ><span v-else-if="item.state == '3'" style="color: #909090"
                  >过期未审核</span
                >
              </div>
            </template>
            <div class="shebei-time">
              <p>开始时间:{{ item.endTime }}</p>
              <p>结束时间:{{ item.startTime }}</p>
            </div>
            <p class="shebei-name" v-if="item.type == '0'">
              重大危险源:{{
                item.dangerid == "-1"
                  ? "全部的重大危险源(全厂设备)"
                  : item.dangerName
              }}
            </p>
            <p class="shebei-name">设备名称:{{ item.equipment }}</p>
          </el-collapse-item>
        </el-collapse>
        <div v-if="tableData.length == 0" class="null">
          <img
            src="/static/img/assets/img/noData.png"
            style="width: 80%; margin-top: 10px"
          />
        </div>
      </div>
    </div>
    <AddEdit ref="addEdit"></AddEdit>
    <Detail ref="detail"></Detail>
  </div>
</template>
<script>
import { getAbnormalList, getEnt } from "@/api/dailySafety";
import { parseTime } from "@/utils/index";
import AddEdit from "../../dailySafetyManagement/equipmentAbnormalDeclaration/addEdit";
import Detail from "../../dailySafetyManagement/equipmentAbnormalDeclaration/detail";
export default {
  components: {
    AddEdit,
    Detail,
  },
  data() {
    return {
      mode: "0",
      enterpId: "",
      loading: true,
      type: "",
      tableData: [],
      entInfoData: {},
    };
  },
  methods: {
    getIotMontoringDataList(id) {
      this.enterpId = id;
      this.loading = true;
      getAbnormalList({
        current: 1,
        size: 5,
        enterpid: id || this.enterpId,
        // type:this.type,
        // startTime:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime()), '{y}-{m}-{d}'),
        // endTime:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1), '{y}-{m}-{d}'),
        // startTime:'2010-01-01',
        // endTime:'2021-05-25',
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
        }
      });
      //   getEnt({
      //   }).then((res) => {
      //       if(res.data.code == 0){
      //         //   console.log(1)
      //           this.entInfoData = res.data.data;
      //       }
      //   });
    },
    edit(id) {
      this.$refs.addEdit.closeBoolean(true);
      this.$refs.addEdit.getData(this.entInfoData, id, this.type);
    },
    look(id) {
      this.$refs.detail.closeBoolean(true);
      this.$refs.detail.getData(id, this.type);
    },
    more() {
      if (this.mode == "0") {
        this.$router.push({
          path: `/dailySafetyManagement/equipmentAbnormalDeclaration`,
          query: { enterpId: "equipmentFacilities" },
        });
      } else if (this.mode == "1") {
        this.$router.push({
          path: `/dailySafetyManagement/equipmentAbnormalDeclaration`,
          query: { enterpId: "videoAbnormal" },
        });
      }
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {},
  watch: {
    mode(newValue, oldValue) {
      if (newValue == "0") {
        this.type = "0";
      } else if (newValue == "1") {
        this.type = "1";
      }
      this.getIotMontoringDataList(this.enterpId);
    },
    $attrs: {
      handler(newVal, oldVal) {
        // console.log(newVal);
        this.entInfoData = newVal.entInfoData;
      },
      immediate: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
    cursor: pointer;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.zhongdian-main {
  width: 92%;
  margin: 0 auto;
  .shebei-box {
    height: 230px;
    overflow-y: auto;
    .null {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
  .zhongdian-heard {
    font-size: 14px;
    color: #3b4046;
    font-weight: bold;
    position: relative;
    width: 100%;
    height: 35px;
    line-height: 38px;
    // background: red;
    // padding-left: 15px;
    // height: 35px;
    .dangerName {
      display: inline-block;
      width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    i {
      display: inline-block;
      width: 20px;
      height: 20px;
    }
    .bianji {
      background: url("/static/img/assets/img/bianji.png") center center
        no-repeat;
      background-size: contain;
      position: absolute;
      right: 125px;
      top: 8px;
    }
    .chakan {
      background: url("/static/img/assets/img/chakan.png") center center
        no-repeat;
      background-size: contain;
      position: absolute;
      right: 95px;
      top: 8px;
    }
    span {
      font-weight: lighter;
      position: absolute;
      right: 15px;
    }
  }
  .shebei-time {
    width: 92%;
    margin: 0 auto;
    font-size: 12px;
    p {
      display: inline-block;
      width: 50%;
      margin-bottom: 0px;
    }
  }
  .shebei-name {
    width: 92%;
    margin: 0 auto;
    margin-bottom: 0px;
  }
}
/deep/ .el-collapse {
  border-bottom: none;
}
/deep/ .el-collapse-item__header {
  background: #daefff;
  padding-left: 15px;
  height: 35px;
  margin-bottom: 10px;
}
/deep/ .el-collapse-item__content {
  padding-bottom: 15px;
}
</style>
