<template>
  <div class="SMSPush">
    <div class="header">
        <div><el-tag>短信推送类型</el-tag><el-tag type="danger">警示通报:风险预警 *不可编辑</el-tag></div>
        <CA-button size="small" type="primary" plain @click="add"><i class="el-icon-plus"></i>  新增</CA-button>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData.records"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        @select="select"
        @select-all="select"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          label="短信接收人"
          width="340"
          align="center"
          :show-overflow-tooltip="true"
          prop="ren"
        >
          <template slot-scope="scope">
            <input type="text" v-model="scope.row.ren" />
          </template>
        </el-table-column>
        <el-table-column
          label="手机号"
          width="340"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <input type="text" v-model="scope.row.ren" />
          </template>
        </el-table-column>
        <el-table-column
          label="编辑人"
          width="340"
          align="center"
          :show-overflow-tooltip="true"
          prop="ren"
        >
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          min-width="240"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button type="text" class="icon_box" @click="del(scope.$index)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button class="save" @click="save()" size="medium" type="primary"> 保存 </el-button>
    </div>
  </div>
</template>
<script>
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  name: "enterpriseList",
  components: {},
  data() {
    return {
      loading: false,
      tableData: {
        records: [{ ren: 1 }, { ren: 1 }, { ren: 1 }, { ren: 1 }, { ren: 1 }],
      },
    };
  },
  //方法集合
  methods: {
    select(selection, row) {
      this.selection = [];
      console.log(selection);
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].enterpId;
      }
    },
    add(){
      this.tableData.records.push({ren:1})
    },
    del(index){
      this.tableData.records.splice(index,1);
      console.log(this.tableData.records,index);
    },
    save(){

    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
  },
};
</script>
<style lang="scss" scoped>
.SMSPush {
  .header {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    > div{
      width: 280px;
          display: flex;
    justify-content: space-between;
    }
  }
  .save{
    float: right;
    margin-top: 20px;
  }
  input{
    border: 0;
    background-color: #eee;
  }
}
</style>