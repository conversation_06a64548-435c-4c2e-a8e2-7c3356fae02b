<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" /> 应急资源
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <div class="tabTit">
      <span @click="handleClick2('rescueTeam')" :class="[activeName=='rescueTeam'?'active':'']">救援队伍</span>
      <span @click="handleClick2('expert')" :class="[activeName=='expert'?'active':'']">专家</span>
      <span @click="handleClick2('materialsAndEquipment')" :class="[activeName=='materialsAndEquipment'?'active':'']">物资装备</span>
      <span @click="handleClick2('emergencyPlan')" :class="[activeName=='emergencyPlan'?'active':'']">应急预案</span>
    </div>
    <div v-if="activeName == 'rescueTeam'">
      <RescueTeam ref="rescueTeam"></RescueTeam>
    </div>
    <div v-if="activeName == 'expert'">
      <Expert ref="expert"></Expert>
    </div>
    <div v-if="activeName == 'materialsAndEquipment'">
      <MaterialsAndEquipment
        ref="materialsAndEquipment"
      ></MaterialsAndEquipment>
    </div>
    <div v-if="activeName == 'emergencyPlan'">
      <EmergencyPlan ref="emergencyPlan"></EmergencyPlan>
    </div>

    <!-- <div class="tabs">
      <el-tabs v-model="activeName"
               @tab-click="handleClick">
        <el-tab-pane label="救援队伍"
                     name="rescueTeam">
          <RescueTeam ref="rescueTeam"></RescueTeam>
        </el-tab-pane>
        <el-tab-pane label="专家"
                     name="expert">
          <Expert ref="expert"></Expert>
        </el-tab-pane>
        <el-tab-pane label="物资装备"
                     name="materialsAndEquipment">
          <MaterialsAndEquipment ref="materialsAndEquipment"></MaterialsAndEquipment>
        </el-tab-pane>
        <el-tab-pane label="应急预案"
                     name="emergencyPlan">
          <EmergencyPlan ref="emergencyPlan"></EmergencyPlan>
        </el-tab-pane>
      </el-tabs>
    </div> -->
  </div>
</template>
<script>
import RescueTeam from "./rescueTeam";
import Expert from "./expert";
import MaterialsAndEquipment from "./materialsAndEquipment";
import EmergencyPlan from "./emergencyPlan";
export default {
  name: "mergencyResources",
  components: {
    RescueTeam,
    Expert,
    MaterialsAndEquipment,
    EmergencyPlan,
  },
  data() {
    return {
      activeName: "rescueTeam",
    };
  },

  mounted() {},

  watch: {},

  methods: {
    handleClick2(tab) {
      this.activeName = tab;
      if (tab == "rescueTeam") {
        this.$refs.rescueTeam.getList();
        this.$refs.rescueTeam.getRescueTeamType();
      } else if (tab == "expert") {
        this.$nextTick(() => {
          this.$refs.expert.getList();
          this.$refs.expert.getExpertType();
        });
      } else if (tab == "materialsAndEquipment") {
        this.$nextTick(() => {
          this.$refs.materialsAndEquipment.getList();
          this.$refs.materialsAndEquipment.getMaterialsAndEquipmentType();
        });
      } else if (tab == "emergencyPlan") {
        this.$nextTick(() => {
          this.$refs.emergencyPlan.getList();
          this.$refs.emergencyPlan.getPlanTypeList();
        });
      }
    },
    // handleClick(tab, event) {
    //   if (tab.name == 'rescueTeam') {
    //     this.$refs.rescueTeam.getList()
    //     this.$refs.rescueTeam.getRescueTeamType()
    //   } else if (tab.name == 'expert') {
    //     this.$refs.expert.getList()
    //     this.$refs.expert.getExpertType()
    //   } else if (tab.name == 'materialsAndEquipment') {
    //     this.$refs.materialsAndEquipment.getList()
    //     this.$refs.materialsAndEquipment.getMaterialsAndEquipmentType()
    //   } else if (tab.name == 'emergencyPlan') {
    //     this.$refs.emergencyPlan.getList()
    //     this.$refs.emergencyPlan.getPlanTypeList()
    //   }
    // }
  },
};
</script>
<style lang="scss" scoped>
.tabTit {
  padding: 0;
  position: relative;
  margin: 0 0 15px;
  border-bottom:2px solid #E4E7ED;
  span {
    margin: 0 20px;
    height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    position: relative; 
    cursor: pointer;   
  }
  span.active:before{
    border-bottom:2px solid #409EFF;
    position: absolute;
    bottom:-2px;
    content:"";
    width:100%
  }
  span:first-child{
    margin-left:0;
  }
}
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  .tabs {
    padding-top: 10px;
    background-color: #fff;
  }
}
</style>
