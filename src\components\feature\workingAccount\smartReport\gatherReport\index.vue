<template>
  <div class="count-report">
    <count-report-list
      v-show="currentView === 'list'"
      @goDetail="goDetail"
      @goAdd="goAdd"
      @goEdit="goEdit"
      @goEnterpInfo="goEnterpInfo"
      @goReportH5="goReportH5"
      :is-enterprise-user="isEnterpriseUser"
      ref="list"
    ></count-report-list>
    <count-report-detail
      v-show="currentView === 'detail'"
      ref="detail"
      :reportId="reportId"
      @goBack="goBack"
    ></count-report-detail>
    <count-report-edit
      ref="edit"
      v-show="currentView === 'edit'"
      @goBack="goBack"
    ></count-report-edit>

    <enterp-info
      v-show="currentView === 'enterpInfo'"
      ref="enterpInfo"
      @goBack="goBack"
    ></enterp-info>
    <report-h5
      v-show="currentView === 'reportH5'"
      ref="reportH5"
      :is-from-list="true"
      @goBack="goBack"
    ></report-h5>
    <el-dialog
      :visible.sync="addDialogVisible"
      title="新增采集报表"
      width="50%"
      :close-on-click-modal="false"
    >
      <div class="add-report-form">
        <el-form
          :model="reportForm"
          :rules="reportRules"
          ref="reportForm"
          label-width="100px"
        >
          <el-form-item label="报表名称" prop="reportName">
            <el-input
              v-model.trim="reportForm.reportName"
              placeholder="请输入报表名称"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleAddReport">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CountReportList from "./list.vue";
import CountReportDetail from "./detail.vue";
import CountReportEdit from "./edit.vue";
import EnterpInfo from "./enterpInfo.vue";
import ReportH5 from "./reportH5.vue";
import { addReportDesign } from "@/api/smartReport";

export default {
  name: "CountReport",
  components: {
    CountReportList,
    CountReportDetail,
    CountReportEdit,
    EnterpInfo,
    ReportH5,
  },
  data() {
    return {
      currentView: "list",
      reportId: "",
      addDialogVisible: false,
      reportForm: {
        reportName: "",
      },
      reportRules: {
        reportName: [
          { required: true, message: "请输入报表名称", trigger: "blur" },
          {
            min: 2,
            max: 50,
            message: "长度在 2 到 50 个字符",
            trigger: "blur",
          },
        ],
      },
    };
  },
  // 计算是否为企业用户
  computed: {
    isEnterpriseUser() {
      try {
        const vuexData = JSON.parse(sessionStorage.getItem("VueX_local"));
        return vuexData?.root?.login?.user?.user_type === "ent";
      } catch (error) {
        return false;
      }
    },
  },
  methods: {
    goDetail(row) {
      this.reportId = row.id;
      this.currentView = "detail";
      this.$nextTick(() => {
        this.$refs.detail.initData(row);
      });
    },
    goBack() {
      this.currentView = "list";
      this.$nextTick(() => {
        this.$refs.list.getList();
      });
    },
    goAdd() {
      this.showAddDialog();
    },
    goEdit(data = null) {
      this.currentView = "edit";
      this.$nextTick(() => {
        this.$refs.edit.initData(data);
      });
    },
    goEnterpInfo(data) {
      this.currentView = "enterpInfo";
      this.$nextTick(() => {
        this.$refs.enterpInfo.initData(data);
      });
    },
    showAddDialog() {
      this.addDialogVisible = true;
      this.reportForm.reportName = "";
      if (this.$refs.reportForm) {
        this.$refs.reportForm.clearValidate();
      }
    },
    goReportH5(data) {
      this.currentView = "reportH5";
      this.$nextTick(() => {
        this.$refs.reportH5.initData(data);
      });
    },
    async handleAddReport() {
      try {
        await this.$refs.reportForm.validate();

        const params = {
          gatherName: this.reportForm.reportName,
        };

        const res = await addReportDesign(params);
        if (res.data.status === 200) {
          this.$message.success("创建成功");
          this.addDialogVisible = false;
          this.$refs.list.getList();
        } else {
          this.$message.error(res.data.msg || "创建失败");
        }
      } catch (error) {
        console.error("创建报表失败:", error);
        this.$message.error("创建失败");
      }
    },
  },
  created() {
    // 检查路由参数，决定初始视图
    // const { id, fields } = this.$route.query;
    // if (id) {
    //   this.goDetail(id, fields);
    // }
  },
};
</script>

<style lang="scss" scoped>
.count-report {
  height: 100%;
}

.add-report-form {
  padding: 20px;
}
</style>
