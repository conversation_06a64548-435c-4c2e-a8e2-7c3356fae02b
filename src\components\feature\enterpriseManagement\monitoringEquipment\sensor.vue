<template>
  <div class="storageTank" id="sensor">
    <div class="header">
      <div class="operation">
        <h2 class="title" alt="传感器列表">传感器列表</h2>
        <div class="inputBox">
          <!-- <el-autocomplete popper-class="my-autocomplete"
            v-model="formInline.orgName"
            :fetch-suggestions="querySearch"
            placeholder="请选择/输入承办单位"
            @clear="clearSensororgCode()"
            size="small"
            @select="handleSelect"
            style="width: 150px">
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete> -->
          <el-select
            size="small"
            v-model="formInline.monitorEqpmtType"
            clearable
            placeholder="请选择传感类别"
            style="width: 150px"
          >
            <el-option
              v-for="item in monitorEqpmtTypeData"
              :key="item.id"
              :label="item.deviceTypeName"
              :value="item.id"
            ></el-option>
          </el-select>
          <!-- <el-select size="small"
            v-model="formInline.itemCode"
            clearable
            placeholder="请输入监测项"
            style="width: 150px">
            <el-option v-for="item in itemListData"
              :key="item.itemCode"
              :label="item.item"
              :value="item.itemCode"></el-option>
          </el-select> -->
          <el-input
            v-model.trim="formInline.itemCode"
            placeholder="请输入监测项"
            class="input"
            size="small"
            clearable
          ></el-input>
          <el-select
            size="small"
            v-model="formInline.onlineStatus"
            clearable
            placeholder="请选择设备状态"
            style="width: 150px"
          >
            <el-option
              v-for="item in onlineStatusData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
          <el-button type="primary" size="small" @click="search()"
            >查询</el-button
          >
        </div>
        <div class="btnBox">
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            size="mini"-->
          <!--            v-if="$store.state.login.user.user_type == 'gov'"-->
          <!--            @click="alarmRuleSettings('edit')"-->
          <!--            >报警规则设置</el-button-->
          <!--          >-->
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            v-if="$store.state.login.user.user_type != 'gov'"-->
          <!--            size="mini"-->
          <!--            @click="addEdit"-->
          <!--            >新增</el-button-->
          <!--          >-->
          <!--          <el-button @click="batchDelete" type="primary" size="mini"-->
          <!--            >批量删除</el-button-->
          <!--          >-->
          <CA-button
            type="primary"
            plain
            class="export"
            size="small"
            @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        height="530px"
        @selection-change="handleSelectionChange"
        @select="select"
        @select-all="select"
      >
        <el-table-column type="selection" width="40" align="center">
        </el-table-column>
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="deviceNo"
          label="设备编号"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="companyName"
          label="企业名称"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="monitorEqpmtTypeName"
          label="传感器类别"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="items"
          label="监测项"
          align="center"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column label="设备状态" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.onlineStatus == 0 ? "在线" : "离线" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="120">
          <template slot-scope="scope">
            <div>
              <span
                v-if="$store.state.login.user.user_type === 'ent'"
                @click="edit(scope.row)"
                style="
                  color: rgb(57, 119, 234);
                  margin-right: 10px;
                  cursor: pointer;
                "
                >编辑</span
              >
              <span
                @click="view(scope.row)"
                style="
                  color: rgb(57, 119, 234);
                  margin-right: 10px;
                  cursor: pointer;
                "
                >详情</span
              >
            </div>
            <!--            <span-->
            <!--              v-if="$store.state.login.user.user_type != 'gov'"-->
            <!--              @click="deleter(scope.row.id)"-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              >删除</span-->
            <!--            >-->
            <!--            <span-->
            <!--              v-if="$store.state.login.user.user_type == 'gov'"-->
            <!--              @click="alarmRuleSettings('see')"-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              >报警规则查看</span-->
            <!--            >-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>

    <!-- 新增编辑查看 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      v-if="open"
      width="1100px"
      :close-on-click-modal="false"
      :append-to-body="false"
    >
      <el-scrollbar>
        <div class="dialog_h">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            :hide-required-asterisk="disabled"
          >
            <div class="form_item">
              <h2 class="form_title">设备信息</h2>
              <div class="form_main">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="设备名称">
                      <el-input
                        v-model.trim="form.deviceName"
                        disabled
                        maxlength="10"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="deviceNo" label="设备编号">
                      <el-input
                        v-model.trim="form.deviceNo"
                        disabled
                        maxlength="30"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="orgName" label="所属单位">
                      <el-input
                        v-model.trim="form.orgName"
                        disabled
                        maxlength="40"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item prop="monitorEqpmtType" label="传感器类别">
                      <el-select
                        v-model="form.monitorEqpmtType"
                        disabled
                        clearable
                        placeholder="请选择传感器类别"
                      >
                        <el-option
                          v-for="item in monitorEqpmtTypeData"
                          :key="item.id"
                          :label="item.deviceTypeName"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="monitorObjectType" label="监测对象类型">
                      <el-select
                        v-model="form.monitorObjectType"
                        clearable
                        placeholder="请选择监测对象类型"
                        disabled
                        @change="
                          monitorObjectTypeChange(form.monitorObjectType)
                        "
                      >
                        <el-option
                          v-for="item in monitorObjectTypeData"
                          :key="item.id"
                          :label="item.deviceTypeName"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="主要技术参数">
                      <el-input
                        v-model.trim="form.mainTechnicalParameters"
                        disabled
                        maxlength="50"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="所在位置描述">
                      <el-input
                        v-model.trim="form.installLocation"
                        :disabled="disabled"
                        maxlength="50"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item
                      prop="longitude"
                      label="经度"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.longitude"
                        maxlength="10"
                        disabled
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      prop="latitude"
                      label="纬度  "
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.latitude"
                        maxlength="10"
                        disabled
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      prop="altitude"
                      label="高程"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.altitude"
                        :disabled="disabled"
                        maxlength="5"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="map_height">
                      <el-form-item label="地图定位">
                        <egisMap
                          :islistener="true"
                          :isdetail="disabled"
                          :datas="form"
                          ref="detailMap"
                          style="height: 220px"
                          @mapCallback="mapcallback"
                        ></egisMap>
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item
                      prop="respPersionId"
                      label="设备负责人"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.respPersionId"
                        :disabled="disabled"
                        maxlength="10"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      prop="phone"
                      label="联系电话"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.phone"
                        maxlength="11"
                        :disabled="disabled"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="8">
                <el-form-item label="所属控制系统"
                              style="width: 100%">
                  <el-select v-model="form.dataSources"
                             clearable
                             placeholder="请选择所属系统"
                             disabled>
                    <el-option v-for="item in dataSourcesData"
                               :key="item.id"
                               :label="item.name"
                               :value="item.id"></el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->
                </el-row>
              </div>
            </div>
            <div
              class="form_item form_items bottom_form"
              v-if="!!form.monitorEqpmtType"
            >
              <h2 class="form_title" style="">监测信息</h2>
              <!--          <el-button-->
              <!--            type="primary"-->
              <!--            size="mini"-->
              <!--            @click="addItem"-->
              <!--            style="float: right"-->
              <!--            >新增</el-button-->
              <!--          >-->
              <div class="form_main">
                <!-- <el-form :model="formData" ref="formData"> -->
                <el-table
                  :data="tableDatas"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                  border
                  stripe
                  style="width: 100%"
                  height="300px"
                  max-height="300px"
                >
                  <el-table-column
                    prop="item"
                    width="150"
                    label="监测项"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-select
                        size="mini"
                        v-model="scope.row.monItemType"
                        clearable
                        placeholder="请选择监测项"
                        @change="getUnit(scope.row, scope.$index)"
                        disabled
                      >
                        <el-option
                          v-for="item in itemListData"
                          :key="item.itemCode"
                          :label="item.item"
                          :value="item.itemCode"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="unit"
                    width="100"
                    label="单位"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-input
                        size="mini"
                        v-model.trim="scope.row.unit"
                        disabled
                        placeholder="单位"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="monitemGbCode"
                    width="200"
                    label="指标地址"
                    align="center"
                  >
                    <template slot-scope="scope">
                      <el-input
                        size="mini"
                        v-model.trim="scope.row.monitemGbCode"
                        disabled
                        placeholder="请输入指标地址"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="address"
                    label="阈值"
                    align="center"
                    show-overflow-tooltip
                  >
                    <template slot-scope="scope">
                      <span>上上限：</span>
                      <el-input
                        style="width: 15%"
                        type="number"
                        v-model.trim="scope.row.thresholdHighHigh"
                        size="mini"
                        class="input_number"
                        disabled
                      ></el-input>
                      <span>上限：</span>
                      <el-input
                        style="width: 15%"
                        type="number"
                        v-model.trim="scope.row.thresholdHigh"
                        size="mini"
                        class="input_number"
                        disabled
                      ></el-input>
                      <span v-if="form.monitorEqpmtType != 'device_10005'"
                        >下下限：</span
                      >
                      <el-input
                        type="number"
                        v-if="form.monitorEqpmtType != 'device_10005'"
                        v-model.trim="scope.row.thresholdLowLow"
                        size="mini"
                        class="input_number"
                        style="width: 15%"
                        disabled
                      ></el-input>
                      <span v-if="form.monitorEqpmtType != 'device_10005'"
                        >下限：</span
                      >
                      <el-input
                        type="number"
                        disabled
                        v-if="form.monitorEqpmtType != 'device_10005'"
                        v-model.trim="scope.row.thresholdLow"
                        size="mini"
                        class="input_number"
                        style="width: 15%"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <!--                <el-table-column-->
                  <!--                  fixed="right"-->
                  <!--                  label="操作"-->
                  <!--                  width="100"-->
                  <!--                  align="center"-->
                  <!--                >-->
                  <!--                  <template slot-scope="scope">-->
                  <!--                    <el-button-->
                  <!--                      round-->
                  <!--                      size="mini"-->
                  <!--                      style="color: #79b2ec"-->
                  <!--                      @click="delListData(scope.$index)"-->
                  <!--                      >删除</el-button-->
                  <!--                    >-->
                  <!--                  </template>-->
                  <!--                </el-table-column>-->
                </el-table>
                <!-- </el-form> -->
              </div>
            </div>
            <br />
          </el-form>
        </div>
      </el-scrollbar>

      <!-- v-if="dialogType === 'edit'" -->
      <div slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          v-if="dialogType === 'edit'"
          >保 存</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 报警规则设置 -->
    <el-dialog
      :title="flag == 'see' ? '报警规则查看' : '报警规则设置'"
      :visible.sync="visible"
      append-to-body
      :close-on-click-modal="false"
      width="60%"
    >
      <div class="flex-full" style="position: relative">
        <div :class="flag == 'see' ? 'mask' : ''"></div>
        <!-- 表单 -->
        <!-- <simple-table
            :formdata="formData"
            @changeOrgPerson="changeOrgPerson"
            :gisshow="true"
            ref="simpleTable"
        ></simple-table> -->
        <!-- 底部按钮 -->
        <el-form ref="ruleForm" :model="ruleForm" label-width="200px">
          <el-form-item label="允许持续时间">
            <el-input v-model.trim="ruleForm.highDuration">
              <el-select
                style="width: 80px"
                v-model="ruleForm.highDurationUnit"
                slot="append"
                placeholder="单位"
              >
                <el-option label="秒" value="ss"></el-option>
                <el-option label="分" value="mm"></el-option>
                <el-option label="时" value="hh"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item label="高报报警短信推送人">
            <el-input v-model.trim="ruleForm.highDurationReceiverName">
              <el-button type="primary" slot="append">选择人员</el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="允许持续时间">
            <el-input v-model.trim="ruleForm.highHighDuration">
              <el-select
                style="width: 80px"
                v-model="ruleForm.highHighDurationUnit"
                slot="append"
                placeholder="单位"
              >
                <el-option label="秒" value="ss"></el-option>
                <el-option label="分" value="mm"></el-option>
                <el-option label="时" value="hh"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item label="高高报报警短信推送人">
            <el-input v-model.trim="ruleForm.highHighDurationReceiverName">
              <el-button type="primary" slot="append">选择人员</el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="允许持续时间">
            <el-input v-model.trim="ruleForm.lowDuration">
              <el-select
                style="width: 80px"
                v-model="ruleForm.lowDurationUnit"
                slot="append"
                placeholder="单位"
              >
                <el-option label="秒" value="ss"></el-option>
                <el-option label="分" value="mm"></el-option>
                <el-option label="时" value="hh"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item label="低报报警短信推送人">
            <el-input v-model.trim="ruleForm.lowDurationReceiverName">
              <el-button type="primary" slot="append">选择人员</el-button>
            </el-input>
          </el-form-item>
          <el-form-item label="允许持续时间">
            <el-input v-model.trim="ruleForm.lowLowDuration">
              <el-select
                style="width: 80px"
                v-model="ruleForm.lowLowDurationUnit"
                slot="append"
                placeholder="单位"
              >
                <el-option label="秒" value="ss"></el-option>
                <el-option label="分" value="mm"></el-option>
                <el-option label="时" value="hh"></el-option>
              </el-select>
            </el-input>
          </el-form-item>
          <el-form-item label="低低报报警短信推送人">
            <el-input v-model.trim="ruleForm.lowLowDurationReceiverName">
              <el-button type="primary" slot="append">选择人员</el-button>
            </el-input>
          </el-form-item>
        </el-form>
        <span class="list-btn" style="text-align: center; display: inherit">
          <el-button
            type="primary"
            icon="el-icon-check"
            @click="submit"
            v-if="flag == 'edit'"
          >
            确认提交
          </el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getSensorListData,
  addSensorData,
  editSensorData,
  detailSensorData,
  deleteSensorData,
  getSensorTypeData,
  getDeviceinfoData,
  exportDeviceinfoData,
  getSensorBbjectListData,
} from "@/api/sensor";
import { getSearchArr } from "@/api/entList.js";
import { getExpertTypeData } from "@/api/mergencyResources";
import downloadFuc from "../../../../api/download/download";
// import { parseTime } from "@/utils/index";
export default {
  //import引入的组件
  name: "sensor",
  components: {},
  data() {
    var validatelongitude = (rule, value, callback) => {
      // 校验经度
      const reg =
        /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,9})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,9}|180)$/;
      if (value !== "" && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error("经度整数部分为0-180,小数部分为0到9位!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validatelatitude = (rule, value, callback) => {
      // 校验纬度
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,9}|90\.0{0,9}|[0-8]?\d{1}|90)$/;
      if (value !== "" && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error("纬度整数部分为0-90,小数部分为0到9位!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      dialogType: "",
      flag: "edit",
      loading: true,
      ruleForm: {
        highDuration: "",
        highDurationUnit: "",
        highDurationReceiverName: "",
        highDurationReceiver: "",
        highHighDuration: "",
        highHighDurationUnit: "",
        highHighDurationReceiverName: "",
        highHighDurationReceiver: "",
        lowDuration: "",
        lowDurationUnit: "",
        lowDurationReceiverName: "",
        lowDurationReceiver: "",
        lowLowDuration: "",
        lowLowDurationUnit: "",
        lowLowDurationReceiverName: "",
        lowLowDurationReceiver: "",
        dialogType: "",
      },
      visible: false,
      rowData: "",
      orgData: [],
      formInline: {
        orgCode: "",
        orgName: "",
        monitorEqpmtType: "",
        deviceNo: "",
        itemCode: "",
        onlineStatus: "",
        offlineWhy: "",
      },
      monitorEqpmtTypeData: [],
      monitorObjectTypeData: [
        // 监测对象类型下拉数据
        // {
        //   name: '储罐',
        //   id: 'G0'
        // },
        // {
        //   name: '仓库',
        //   id: 'K0'
        // },
        // {
        //   name: '生产场所',
        //   id: 'S0'
        // },
        // {
        //   name: '装置',
        //   id: 'P0'
        // },
        // {
        //   name: '泄漏点',
        //   id: 'Q0'
        // },
        // {
        //   name: '其他',
        //   id: 'T0'
        // }
      ],
      itemListData: [],
      onlineStatusData: [
        { id: "1", name: "离线" },
        { id: "0", name: "在线" },
      ],
      dataSourcesData: [],
      open: false,
      activeName: 1,
      title: "新增储罐信息",
      currentPage: 1,
      size: 10,
      dangerName: "",
      selection: [],
      options: [
        {
          value: "G0",
          label: "储罐",
        },
        {
          value: "Q0",
          label: "泄漏点",
        },
        {
          value: "P0",
          label: "装置",
        },
      ],
      options1: [
        {
          value: "0",
          label: "已消警",
        },
        {
          value: "1",
          label: "未消警",
        },
      ],
      sensortypeCode: "",
      state: "",
      value1: "",
      starTime: "",
      endTime: "",
      total: 0,
      disabled: false,
      tableData: [],
      tableDatas: [
        {
          monItemType: "",
          unit: "",
          thresholdHighHigh: "",
          thresholdHigh: "",
          thresholdLowLow: "",
          thresholdLow: "",
          monitemGbCode: "",
        },
      ],
      form: {
        deviceName: "",
        deviceNo: "",
        latitude: "",
        longitude: "",
        altitude: "",
        respPersionId: "",
        installLocation: "",
        phone: "",
        orgCode: "",
        monitorEqpmtType: "",
        monitorObjectType: "",
        monitorObjectCode: "",
        mainTechnicalParameters: "",
        options: [],
        disabled: true,
      },
      rules: {
        deviceNo: [
          {
            required: true,
            message: "请输入设备编号",
            trigger: ["blur"],
          },
        ],
        orgCode: [
          {
            required: true,
            message: "请输入所属企业",
            trigger: "blur",
          },
        ],
        monitorEqpmtType: [
          {
            required: true,
            message: "请输入监测设备类型",
            trigger: ["blur"],
          },
        ],
        monitorObjectType: [
          {
            required: true,
            message: "请输入监测对象类型",
            trigger: ["blur"],
          },
        ],
        longitude: [
          {
            required: true,
            validator: validatelongitude,
            trigger: ["blur"],
          },
        ],
        latitude: [
          {
            required: true,
            validator: validatelatitude,
            trigger: ["blur"],
          },
        ],
        altitude: [
          {
            required: true,
            pattern: /^\d{1,6}(\.\d{1,6})?$/,
            message: "请输入正确的高程",
            trigger: ["blur"],
          },
        ],
        respPersionId: [
          {
            required: true,
            message: "请输入设备负责人",
            trigger: ["blur"],
          },
        ],
        phone: [
          {
            required: true,
            pattern:
              /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
            message: "请输入手机号",
            trigger: "blur",
          },
        ],
      },
      enterpriseId: "",
      tipData: {},
    };
  },
  watch: {
    enterpriseId: {
      handler(newVal, oldVal) {
        this.currentPage = 1;
      },
      deep: true,
      immediate: true,
    },
  },
  filters: {
    numToFixed(val) {
      if (val) {
        return parseInt(val).toFixed(2);
      } else {
        return val;
      }
    },
  },
  //方法集合
  methods: {
    aaa(val) {
      this.mapcallback();
      console.log(val, "aaaaaaaaaaa");
    },
    querySearch(queryString, cb) {
      let roleInfo =
        JSON.parse(sessionStorage.getItem("VueX_local")).root.login.user || {};
      if (queryString === "") {
        queryString = roleInfo.user_type === "ent" ? roleInfo.org_name : "";
      }
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.formInline.orgCode = item.enterpId;
      this.formInline.orgName = item.enterpName;
    },
    clearSensororgCode() {
      this.formInline.orgCode = "";
      this.formInline.orgName = "";
    },
    //获取监测类型
    getMonitorEqpmtType() {
      getSensorTypeData().then((res) => {
        if (res.data.status == 200) {
          this.monitorEqpmtTypeData = res.data.data;
        }
      });
      //
      getExpertTypeData({ dicCode: "SAFETY_DATA_SOURCE" }).then((res) => {
        if (res.data.status == 200) {
          // this.dataSourcesData = res.data.data
          this.dataSourcesData = [
            {
              $type: "DicItem,http://www.dv.com",
              dicInfo: null,
              group: "SAFETY_DATA_SOURCE",
              id: "7594a3942cff4716ba39dcff4eff141f",
              indexNum: 1,
              name: "DSC系统",
              parentId: "1c725fe5ece142b0b29a30677206ac0f",
              value: "1",
            },
            {
              $type: "DicItem,http://www.dv.com",
              dicInfo: null,
              group: "SAFETY_DATA_SOURCE",
              id: "e50d9c816ff64498b34d7b12a17ee39c",
              indexNum: 2,
              name: "GDS系统",
              parentId: "1c725fe5ece142b0b29a30677206ac0f",
              value: "2",
            },
            {
              $type: "DicItem,http://www.dv.com",
              dicInfo: null,
              group: "SAFETY_DATA_SOURCE",
              id: "289f1ffab229428885f7ad212a5b54aa",
              indexNum: 3,
              name: "SIS系统",
              parentId: "1c725fe5ece142b0b29a30677206ac0f",
              value: "3",
            },
          ];
        }
      });
    },
    getArt() {
      getSensorBbjectListData().then((res) => {
        if (res.data.status == 200) {
          console.log(res.data.data, "监测对象类型 res.data.data");
          this.monitorObjectTypeData = res.data.data;
        }
      });
    },
    //获取监测项
    // getDeviceinfoType() {
    //   getDeviceinfoData().then(res => {
    //     if (res.data.status == 200) {
    //       this.itemListData = res.data.data
    //     }
    //   })
    // },
    getData(id) {
      this.enterpriseId = id;
      getSensorListData({
        // monitorEqpmtType: this.formInline.monitorEqpmtType,
        // itemCode:this.formInline.itemCode,
        // onlineStatus:this.formInline.onlineStatus,
        // nowPage: this.currentPage,
        // pageSize:this.size,
        systemFlag: "", //系统标识
        // deviceMainType: 'device_10', //固定值
        deviceMainType: "", //固定值
        pageSize: this.size, //条数
        nowPage: this.currentPage, //页数
        orgCode: id,
        monitorEqpmtType: this.formInline.monitorEqpmtType
          ? this.formInline.monitorEqpmtType
          : "",
        deviceNo: this.formInline.deviceNo ? this.formInline.deviceNo : "",
        itemCode: this.formInline.itemCode ? this.formInline.itemCode : "",
        onlineStatus: this.formInline.onlineStatus
          ? this.formInline.onlineStatus
          : "",
        offlineWhy: this.formInline.offlineWhy
          ? this.formInline.offlineWhy
          : "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.total = res.data.data.total;
          this.tableData = res.data.data.list;
        }
      });
    },

    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    search() {
      this.currentPage = 1;
      console.log(this.enterpriseId, "点击查询按钮this.enterpriseId");
      this.getData(this.enterpriseId);
    },
    clearSensortypeCode(e) {
      this.sensortypeCode = "";
    },
    clearState(e) {
      this.state = "";
    },
    clearDangerName(e) {
      this.dangerName = "";
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData(this.enterpriseId);
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    // 导出
    exportExcel() {
      exportDeviceinfoData({
        // monitorEqpmtType: this.formInline.monitorEqpmtType,
        // itemCode:this.formInline.itemCode,
        // onlineStatus:this.formInline.onlineStatus,
        // nowPage: this.currentPage,
        // pageSize:this.size,
        systemFlag: "SAFETY", //系统标识
        deviceMainType: "device_10", //固定值
        deviceNo: this.formInline.deviceNo ? this.formInline.deviceNo : "",
        itemCode: this.formInline.itemCode ? this.formInline.itemCode : "",
        monitorEqpmtType: this.formInline.monitorEqpmtType
          ? this.formInline.monitorEqpmtType
          : "",
        nowPage: this.currentPage,
        offlineWhy: this.formInline.offlineWhy
          ? this.formInline.offlineWhy
          : "",
        onlineStatus: this.formInline.onlineStatus
          ? this.formInline.onlineStatus
          : "",
        // orgCode: this.formInline.orgCode ? this.formInline.orgCode : '',
        orgCode: this.enterpriseId,
        pageSize: this.size,
        // ids: this.selection,
      }).then((response) => {
        this.$message({
          message: "导出成功",
          type: "success",
        });
        // console.log(response);
        downloadFuc(response);
      });
    },
    addEdit() {
      this.reset();
      this.open = true;
    },
    edit(row) {
      this.reset();
      this.open = true;
      this.disabled = false;
      this.dialogType = "edit";
      this.title = "编辑监测设备信息";
      //   this.form = row;
      detailSensorData({ id: row.id }).then((res) => {
        if (res.data.status == 200) {
          this.form = res.data.data;
          this.form.orgName = res.data.data.companyName;
          this.tableDatas = res.data.data.sensorMonItemInfoDTOS;
          // if (!!this.form.monitorObjectType) {
          //    this.monitorObjectTypeChange(this.form.monitorObjectType);
          // }
        }
      });
    },
    view(row) {
      this.reset();
      this.open = true;
      this.disabled = true;
      this.title = "查看监测设备信息";
      this.dialogType = "view";
      //   this.form = row;
      detailSensorData({ id: row.id }).then((res) => {
        if (res.data.status == 200) {
          this.form = res.data.data;
          this.form.orgName = res.data.data.companyName;
          this.tableDatas = res.data.data.sensorMonItemInfoDTOS;
          // if (!!this.form.monitorObjectType) {
          //    this.monitorObjectTypeChange(this.form.monitorObjectType);
          // }
        }
      });
    },
    deleter(row) {
      const id = row;
      this.$confirm("是否确认删除该传感器设备信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteSensorData({
            ids: [id],
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("删除成功");
              this.getData();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    batchDelete() {
      if (this.selection.length == 0) {
        this.$message.info("请勾选需要删除的传感器设备信息");
      } else {
        this.$confirm("是否确认批量删除勾选的传感器设备信息", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            deleteSensorData({
              ids: this.selection,
            }).then((res) => {
              if (res.data.status == 200) {
                this.$message.success("批量删除成功");
                this.getData();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          })
          .catch(() => {});
      }
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    addItem() {
      this.tableDatas.push({
        monItemType: "",
        unit: "",
        thresholdHighHigh: "",
        thresholdHigh: "",
        thresholdLowLow: "",
        thresholdLow: "",
        monitemGbCode: "",
      });
    },
    delListData(index) {
      this.tableDatas.splice(index, 1);
    },
    // 获取单位并赋值
    getUnit(val, val2) {
      this.itemListData.map((item) => {
        if (item.itemCode == val.monItemType) {
          return (this.tableDatas[val2].unit = item.unit);
        }
      });
      console.log(this.tableData, "获取单位并赋值");
    },
    reset() {
      this.form = {
        deviceName: "",
        deviceNo: "",
        latitude: "",
        longitude: "",
        altitude: "",
        respPersionId: "",
        installLocation: "",
        phone: "",
        orgCode: "",
        monitorEqpmtType: "",
        monitorObjectType: "",
        monitorObjectCode: "",
        mainTechnicalParameters: "",
        options: [],
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            let params = {
              id: this.form.id,
              companyHazardId: this.form.companyHazardId,
              deviceName: this.form.deviceName ? this.form.deviceName : "",
              deviceNo: this.form.deviceNo ? this.form.deviceNo : "",
              // latitude: this.form.latitude ? this.form.latitude.toFixed(9) : '',
              // longitude: this.form.longitude ? this.form.longitude.toFixed(9) : '',
              latitude: this.form.latitude ? this.form.latitude : "",
              longitude: this.form.longitude ? this.form.longitude : "",
              altitude: this.form.altitude ? this.form.altitude : "", // 高程
              respPersionId: this.form.respPersionId
                ? this.form.respPersionId
                : "",
              installLocation: this.form.installLocation
                ? this.form.installLocation
                : "",
              phone: this.form.phone ? this.form.phone : "",
              orgCode: this.form.orgCode ? this.form.orgCode : "",
              monitorEqpmtType: this.form.monitorEqpmtType
                ? this.form.monitorEqpmtType
                : "",
              monitorObjectType: this.form.monitorObjectType
                ? this.form.monitorObjectType
                : "",
              monitorObjectCode: this.form.monitorObjectCode
                ? this.form.monitorObjectCode
                : "",
              mainTechnicalParameters: this.form.mainTechnicalParameters
                ? this.form.mainTechnicalParameters
                : "",
              sensorMonItemInfoDTOS: this.tableDatas,
              dataSources: this.form.dataSources,
              // companyHazardId: this.hazardId,
            };
            editSensorData(params).then((response) => {
              if (response.data.status == 200) {
                this.open = false;
                this.$message({
                  type: "success",
                  message: "修改成功",
                });
                this.reset();
                this.getData();
              }
            });
          } else {
            let params = {
              systemFlag: "SAFETY", //系统标识
              deviceMainType: "device_10", //固定值
              deviceName: this.form.deviceName ? this.form.deviceName : "",
              deviceNo: this.form.deviceNo ? this.form.deviceNo : "",
              // latitude: this.form.latitude ? this.form.latitude.toFixed(9) : '',
              // longitude: this.form.longitude ? this.form.longitude.toFixed(9) : '',
              latitude: this.form.latitude ? this.form.latitude : "",
              longitude: this.form.longitude ? this.form.longitude : "",
              altitude: this.form.altitude ? this.form.altitude : "", // 高程
              respPersionId: this.form.respPersionId
                ? this.form.respPersionId
                : "",
              installLocation: this.form.installLocation
                ? this.form.installLocation
                : "",
              phone: this.form.phone ? this.form.phone : "",
              orgCode: this.form.orgCode ? this.form.orgCode : "",
              monitorEqpmtType: this.form.monitorEqpmtType
                ? this.form.monitorEqpmtType
                : "",
              monitorObjectType: this.form.monitorObjectType
                ? this.form.monitorObjectType
                : "",
              monitorObjectCode: this.form.monitorObjectCode
                ? this.form.monitorObjectCode
                : "",
              mainTechnicalParameters: this.form.mainTechnicalParameters
                ? this.form.mainTechnicalParameters
                : "",
              sensorMonItemInfoDTOS: this.tableDatas,
              companyHazardId: this.form.companyHazardId,
              regionId: this.form.regionId ? this.form.regionId[2] : "",
              dataSources: this.form.dataSources,
            };
            addSensorData(params).then((response) => {
              if (response.data.status == 200) {
                this.open = false;
                this.$message({
                  type: "success",
                  message: "新增成功",
                });
                this.reset();
                this.getData();
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    alarmRuleSettings(flag) {
      this.flag = flag;
      this.visible = true;
    },
    submit() {},
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        this.form.altitude = data.location.altitude.toFixed(6) || 0;
        this.form.installLocation = data.formatted_address;
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getArt();
    if (this.$store.state.login.user.user_type == "gov") {
      this.formInline.orgCode = "";
      this.formInline.orgName = "";
      this.form.orgCode = "";
      this.form.orgName = "";
    } else {
      this.formInline.orgCode = this.$store.state.login.user.org_code;
      this.formInline.orgName = this.$store.state.login.user.org_name;
      this.form.orgCode = this.$store.state.login.user.org_code;
      this.form.orgName = this.$store.state.login.user.org_name;
    }
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0;
}
.dialog_h {
  height: 600px;
  padding: 30px 20px;
}
.bottom_form {
  clear: both;
}
/deep/ h2 {
  margin: 0;
}
.storageTank {
  background-color: #fff;
  padding-bottom: 50px;
  .header {
    .title {
      //margin-left: 20px;
      //font-weight: 600;
      //width: 130px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        width: 700px;
        float: left;
        justify-content: space-between;
        .input {
          width: 180px;
        }
      }
      .btnBox {
        display: flex;
        & > * {
          margin-left: 10px;
        }
      }
    }
    .export {
      margin-right: 5px;
    }
  }
  .table {
    width: 100%;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
.mask {
  position: absolute;
  width: 100%;
  height: 88%;
  z-index: 99;
  background: rgba(255, 255, 255, 0);
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
/* .form_items .el-input{
    width:30%;
} */
</style>
