<template>
  <div class="enterpriseManagement">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box">
                <a-icon type="home" theme="filled" class="icon" /> 电力监测
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="dataOverview">
        <div class="echartTop">
          <div class="table-top">
            <h2>数据概览</h2>
          </div>
          <div class="changeTime">
            <div
              :class="[active == '7' ? 'active' : '', 'item']"
              @click="clickTime(7)"
            >
              本周
            </div>
            <div
              :class="[active == '30' ? 'active' : '', 'item']"
              @click="clickTime(30)"
            >
              近一个月
            </div>
            <div
              :class="[active == 'quarter' ? 'active' : '', 'item']"
              @click="clickTime('quarter')"
            >
              本季度
            </div>
            <div
              :class="[active == 'custom' ? 'active' : '', 'item']"
              @click="clickTime('custom')"
            >
              自定义
            </div>
            <!-- {{ timeCustom }} -->
            <div style="width: 0; height: 0">
              <el-date-picker
                v-model="timeCustom"
                class="time-data-picker"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="dateChange"
              >
              </el-date-picker>
            </div>
          </div>
        </div>

        <div class="echrtBox">
          <div class="dataOverviewL">
            <echartlL></echartlL>
          </div>
          <div class="dataOverviewR">
            <echartlR :timeObj="timeObj"></echartlR>
          </div>
        </div>
      </div>
    </div>

    <div class="activeBody">
      <div class="activeHeader">
        <el-tabs v-model="activeTabClass" @tab-click="handleClickActiveTab()">
          <el-tab-pane label="报警企业" name="alarmEnterprise">
            <alarmEnterprise ref="alarmEnterprise"></alarmEnterprise>
          </el-tab-pane>
          <el-tab-pane label="重点监管企业" name="keyAlarmEnterprise">
            <keyAlarmEnterprise ref="keyAlarmEnterprise"></keyAlarmEnterprise>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import echartlL from "./echartlL.vue"; //
import echartlR from "./echartlR.vue"; //echartlR
import alarmEnterprise from "./alarmEnterprise.vue";
import keyAlarmEnterprise from "./keyAlarmEnterprise.vue";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
var dayjs = require("dayjs");
export default {
  components: {
    alarmEnterprise,
    keyAlarmEnterprise,
    echartlL,
    echartlR,
  },
  data() {
    return {
      timeObj: {},
      timeCustom: "",
      active: "7",
      activeTabClass: "alarmEnterprise",
      tableCheck: true,
      loading: false,
      areaName: "",
      tableData: {},

      value: "",
      warnStatus: "",
      rank: "",
      showBreak: false,
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      selection: [],
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      startDate: "",
      endDate: "",
      showArea: true,
      disposalSituationShow: false,
      receivingUnit: [],
      reasonDescription: "",
      notificationContent: "",
      nowtime: new Date(new Date()).Format("yy年MM月dd日 hh时mm分"),
      park: "",
      listId: "",
      title: "风险预警信息核查、处置情况报告",
      disabled: false,
    };
  },
  methods: {
    clickTime(valTime) {
      this.active = valTime;
      if (valTime == 7) {       
        this.timeObj = {
          startTime: dayjs().subtract(6, "day").format("YYYY-MM-DD"),
          endTime: dayjs().format("YYYY-MM-DD"),
        };
      } else if (valTime == 30) {
        this.timeObj = {
          startTime: dayjs().subtract(30, "day").format("YYYY-MM-DD"),
          endTime: dayjs().format("YYYY-MM-DD"),
        };
      } else if (valTime == "quarter") {
        this.timeObj = {
          startTime: dayjs().subtract(3, "month").format("YYYY-MM-DD"),
          endTime: dayjs().format("YYYY-MM-DD"),
        };
      } else if (valTime == "custom") {      
        document
          .querySelector(".time-data-picker")
          .querySelector("input")
          .focus();
      }    
    },
    dateChange(val) {
      this.timeObj = {     
        startTime: val[0],
        endTime: val[1],
      };
    },
    handleClickActiveTab() {},
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.dataOverview {
  .echartTop {
    display: flex;
    justify-content: space-between;
    .table-top h2 {
      font-size: 16px;
    }
    .changeTime {
      display: flex;
      align-items: center;
      > div.item {
        padding: 0 10px;
        background: #fff;
        margin: 0 0 0 10px;
        border: 1px solid #c0c4cc;
        cursor: pointer;
      }
      .item.active {
        background: #409eff;
        border: 1px solid #409eff;
        color: #fff;
      }
    }
  }
  .echrtBox {
    display: flex;
    > div {
      width: 50%;
    }
  }
  /deep/ .el-range-editor.el-input__inner {
    width: 0;
    height: 0;
    border: 0;
    padding: 0;
    margin: 0;
  }
  /deep/ .el-date-editor .el-range-separator {
    font-size: 0;
  }
  /deep/ .el-date-editor .el-range__icon {
    font-size: 0;
  }
}

.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style >
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
