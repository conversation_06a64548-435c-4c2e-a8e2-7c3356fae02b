<template>
  <div class="enterpriseManagement">
    <!-- <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box">
                <a-icon parentCode="home" theme="filled" class="icon" />
                在线动态监测
              </span>
            </a-breadcrumb-item>

          </a-breadcrumb>
        </div>
      </div> -->
    <div class="lot">
      <div class="right">
        <div>
          <div class="title">接入企业数</div>
          <div class="num" @click="openOnline(1)">
            <!-- {{ onlineNetworking.linkedNumber }} -->
            {{ onlineNetworking.linkedEnterpCount }}
          </div>
        </div>
        <div>
          <div class="title">在线企业数</div>
          <div class="num" @click="openOnline(2)">
            {{ onlineNetworking.onlineEnterpCount }}
          </div>
        </div>
        <div>
          <div class="title">离线企业数</div>
          <div class="num" @click="openOnline(3)">
            <!-- {{ onlineNetworking.offlineCompanyNum }} -->
            {{
              (onlineNetworking.linkedEnterpCount || 0) -
              (onlineNetworking.onlineEnterpCount || 0)
            }}
          </div>
        </div>
        <div>
          <div class="title">企业在线率</div>
          <div class="num1">
            {{ onlineNetworking.onlineEnterpRate }}
          </div>
        </div>
        <div>
          <div class="title">接入指标数</div>
          <div class="num1">{{ onlineNetworking.allTargetCount }}</div>
        </div>
        <div>
          <div class="title">在线质量</div>
          <div class="num1">{{ onlineNetworking.onlineTargetRate }}</div>
        </div>
      </div>
    </div>
    <div class="videoInspection">
      <div class="video-left">
        <div class="videoLeft-top">
          <div class="list-search">
            <el-input
              v-model.trim="enterpName"
              size="mini"
              placeholder="请输入企业名称"
              class="input"
              clearable
              style="width: 260px; margin-left: 10px"
            ></el-input>
            <el-button parentCode="primary" size="mini" @click="search"
              >查询</el-button
            >
          </div>
          <div class="video-list" v-loading="loading">
            <a-directory-tree
              multiple
              default-expand-all
              @select="onSelect"
              @expand="onExpand"
              style="padding: 0 10px"
            >
              <a-tree-node
                :key="item.id + ',' + item.parentCode"
                v-for="item in newAllData"
                :title="item.label"
              >
                <a-tree-node
                  v-if="item.children.length > 0"
                  :key="subItem.id + ',' + subItem.parentCode"
                  v-for="subItem in item.children"
                  :title="subItem.label"
                >
                  <a-tree-node
                    v-if="subItem.children.length > 0"
                    :key="subItems.id + ',' + subItems.parentCode"
                    v-for="subItems in subItem.children"
                    :title="subItems.label"
                  >
                    <a-tree-node
                      v-if="subItems.children.length > 0"
                      :key="subItemd.id + ',' + subItemd.parentCode"
                      v-for="subItemd in subItems.children"
                      :title="subItemd.label"
                    >
                      <a-tree-node
                        v-if="subItemd.children.length > 0"
                        :key="subItemed.id + ',' + subItemed.parentCode"
                        v-for="subItemed in subItemd.children"
                        :title="subItemed.label"
                      ></a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-tree-node>
              </a-tree-node>
            </a-directory-tree>
          </div>
        </div>
      </div>
      <div class="video-right">
        <!-- <div class="video-box" id="video-box">
            </div> -->
        <RealTimeMonitoring ref="realTimeMonitoring"></RealTimeMonitoring>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getOnlineVideoData,
  getRealmonitorNew,
  districtEnterprise,
} from "@/api/riskAssessment";
import { itoSatistics } from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import RealTimeMonitoring from "@/components/feature/enterpriseManagement/realTimeMonitoring/index";
// import WSPlayer from '@/utils/WSPlayer/WSPlayer';
// import ICC from '@/utils/icc';
export default {
  components: { RealTimeMonitoring },
  data() {
    return {
      tableCheck: true,
      newAllData: [],
      enterpName: "",
      loading: true,
      cameraIndexCode: "",
      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
      realPlayer: null,
      enterpId: "",
      loadingOnline: false,
      level: ["1", "2", "3", "4"],
      radio1: "0",
      distCode: this.$store.state.login.userDistCode,
      onlineNetworking: {
        allTargetCount: 0,
        onlineTargetCount: 0,
        onlineTargetRate: "0%",
        allEnterpCount: 0,
        linkedEnterpCount: 0,
        onlineEnterpCount: 0,
        linkedEnterpRate: "0%",
        onlineEnterpRate: "0%",
      },
    };
  },
  methods: {
    //在线互联网分析
    getOnlineNetworkingList() {
      this.loadingOnline = true;
      itoSatistics({
        level: this.level.join(","),
        current: 1,
        distCode: this.distCode,
        type: this.radio1,
        size: 10,
      }).then((res) => {
        if (res.data.data != null) {
          this.onlineNetworking = res.data.data;
        } else {
          this.onlineNetworking = {};
        }
        this.loadingOnline = false;
      });
    },
    //视频运行列表
    getOnlineVideoDataList() {
      this.loading = true;
      districtEnterprise({
        districtcode: this.$store.state.login.userDistCode, //行政区划代码
        enterpname: this.enterpName,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          //   this.tableData = res.data.data.records;
          //   this.NetWork = false;
          this.newAllData = res.data.data;
        }
      });
    },

    goToRunning() {
      //   this.showBreak = false;
      //   // this.level = "";
      //   this.distCode = this.$store.state.login.userDistCode;
      //   if (this.tableCheck) {
      //     this.getOnlineNetworkingList();
      //   } else {
      //     this.getVideoRunningList();
      //   }
    },
    onSelect(selectedKeys, info) {
      this.enterpId = selectedKeys[0].split(",")[0];
      this.$refs.realTimeMonitoring.getData(this.enterpId);
      this.$refs.realTimeMonitoring.getSelectData(this.enterpId);
      this.$refs.realTimeMonitoring.getStateData(this.enterpId);
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId
     */
    // realPlayNew(channelId) {
    //   ICC.getRealmonitor({
    //       channelId: channelId,
    //       // dataType: '3', //视频类型：1=视频, 2=音频, 3=音视频
    //       streamType: '2' //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
    //   }).then((data) => {
    //       this.realPlayer.playReal({
    //           rtspURL: data.rtspUrl, // string | array[string]
    //           decodeMode: 'canvas', // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
    //           channelId: channelId, // 可选，用来标记当前视频播放的通道id
    //       })
    //   })
    // },
    onExpand() {},
    search() {
      this.getOnlineVideoDataList();
    },

    //获取公钥
    // getPubKey (callback) {
    //     const that = this;
    //     that.oWebControl.JS_RequestInterface({
    //         funcName: "getRSAPubKey",
    //         argument: JSON.stringify({
    //             keyLength: 1024
    //         })
    //     }).then(function (oData) {
    //         console.log(oData);
    //         if (oData.responseMsg.data) {
    //             that.pubKey = oData.responseMsg.data;
    //             callback()
    //         }
    //     })
    // },

    //RSA加密
    // setEncrypt (value) {
    //     var encrypt = new JSEncrypt();
    //     encrypt.setPublicKey(this.pubKey);
    //     return encrypt.encrypt(value);
    // },
  },
  async created() {
    //  await ICC.init();
    this.getOnlineNetworkingList();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getOnlineVideoDataList();
    // this.videoWidth = $("#video-box").width();
    // this.videoHight = $("#video-box").height();

    // this.videoModuleData = this.$store.state.login.videoModuleData;
    // if (this.videoModuleData.data.length === 0) {
    // } else {
    //   window.showCBInfo = function() {};
    //   this.initPlugin();
    // }
    // this.initPlugin();
    // this.updateVideoCode();
    // console.log("进入视频组件111~");
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // 初始化平台信息获取
    // let serverAdress = sessionStorage.getItem('videoApi');
    // if (process.env.NODE_ENV === 'development') {
    //     serverAdress = '************:2443';
    // } else {
    //     serverAdress = '***********:9100';
    // }
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // if (!this.realPlayer) {
    //   this.realPlayer = new WSPlayer({
    //       el: 'ws-real-player', // 必传
    //       parentCode: 'real', // real | record
    //       serverIp: serverAdress,
    //       num: 4,
    //       showControl: true,
    //   })
    // }
  },
  destroyed() {
    // this.unloadVideo();
    console.log("销毁视频组件~");
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    .video-left {
      float: left;
      width: 345px;
      position: absolute;
      margin-right: 25px;
      margin-top: 15px;
      .videoLeft-top {
        background: #daefff;
        .list-search {
          padding-top: 15px;
        }
        .video-list {
          height: calc(100vh - 195px);
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
    }
    .video-right {
      float: left;
      position: relative;
      width: calc(100% - 370px);
      margin-left: 370px;
      margin-top: 15px;
      .video-box {
        margin-top: 15px;
        border: 1px solid #ddd;
        height: calc(100vh - 150px);
        #ws-real-player {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
}
.lot {
  display: flex;
  overflow: hidden;
  justify-content: center;
  background: #daefff;

  .right {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 800px;
    height: 80px;
    text-align: center;
    > div {
      border-right: 1px #86a5e7 solid;
      height: 52px;
      width: 160px;
      display: flex;
      justify-content: space-around;
      flex-direction: column;
      align-items: center;
      margin-top: 10px;
    }

    > div:nth-last-of-type(1) {
      border-right: 0;
    }
    .title {
      color: #545c65;
      font-size: 14px;
    }
    .num {
      font-size: 20px;
      color: #31539b;
      font-weight: 600;
      // cursor: pointer;
    }
    .num1 {
      font-size: 20px;
      color: #31539b;
      font-weight: 600;
    }
  }
}
</style>
