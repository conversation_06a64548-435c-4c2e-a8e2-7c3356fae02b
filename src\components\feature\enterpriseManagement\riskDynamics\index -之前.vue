<template>
  <div class="riskDy">
      <div class="riskDy-top">
          <div class="risk-boxone"></div>
          <div class="risk-boxtwo">
              <!-- <h2>实时风险状态</h2> -->
              <div id="myChart" :style="{width: '348px', height: '230px'}"></div>
          </div>
          <div class="risk-boxthree">
              <div id="myCharts" :style="{width: '570px', height: '230px'}"></div>
          </div>
      </div>
      <div class="riskDy-bottom">
          <div class="risk-boone"></div>
          <div class="risk-botwo"></div>
      </div>
  </div>
</template>

<script>
// const echarts = require('echarts');
export default {
  //import引入的组件
  name: "riskDynamics",
  components: {},
  data() {
    return {
      dataLink : [{
            source: '引发因素',
            target: '人为因素',
            value: 66

        },
        {
            source: '引发因素',
            target: '自然因素',
            value: 88
        },
        {
            source: '引发因素',
            target: 'xx因素',
            value: 53
        },
        {
            source: '引发因素',
            target: 'yy因素',
            value: 44
        },
        {
            source: 'xx因素',
            target: 'xx分析',
            value: 42
        },
        {
            source: 'yy因素',
            target: 'yy分析',
            value: 67
        },
        {
            source: '自然因素',
            target: '用户分析',
            value: 12
        },
        {
            source: '自然因素',
            target: '话题分析',
            value: 18
        },
        {
            source: '自然因素',
            target: '评论分析',
            value: 50
        },
        {
            source: '人为因素',
            target: '图书分析',
            value: 23
        },
        {
            source: '人为因素',
            target: '借阅分析',
            value: 19
        },
        {
            source: '人为因素',
            target: '借阅排行',
            value: 6
        }, {
            source: '人为因素',
            target: '图书收录',
            value: 8

        }],
        dataSerise : [{
            name: '引发因素',
            symbolSize: 60,
            draggable: true,
            value: 0,
            category: 0,
            itemStyle: {
                normal: {
                    borderColor: '#04f2a7',
                    borderWidth: 4,
                    shadowBlur: 10,
                    shadowColor: '#04f2a7',
                    color: '#001c43',
                }
            }
        }],
        xdata : ['B4', 'B3', 'C4', 'A1', 'E2', 'C1', 'D1', 'A3', 'E3', 'A2', 'D2', 'C2', 'D3', 'D4', 'B2', 'A4', 'C3', 'E1', 'B1','B2', 'A4', 'C3', 'E1', 'B1'],//x轴
        dataArr : [400, 300, 200, 131.14, 500, 131.14, 100, 131.14, 500, 131.14, 100, 200, 500, 700, 131.14, 400, 600, 131.14, 100,131.14, 400, 600, 131.14, 300],
        dashedArr : [150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150,150, 150, 150, 150, 150,],
        titleName : '红色风险',
        titleNames : '当前风险',
    };
  },
  //方法集合
  methods: {
    drawLine(){
        // 基于准备好的dom，初始化echarts实例
        let myChart = this.$echarts.init(document.getElementById('myChart'))
        // 绘制图表
        for (var i = 0; i < this.dataLink.length; i++) {
            var dataChild = {
                name: '',
                symbolSize: 0,
                value: 0,
                category: 0,
                itemStyle: {
                    normal: {
                        borderColor: '#82dffe',
                        borderWidth: 4,
                        shadowBlur: 5,
                        shadowColor: '#04f2a7',
                        color: '#001c43'
                    }
                },
            }
            dataChild.value = this.dataLink[i].value
            dataChild.name = this.dataLink[i].target
            if (this.dataLink[i].source === '引发因素') {
                this.dataSerise[0].value += this.dataLink[i].value
                dataChild.symbolSize = 40
                dataChild.category = 1
                dataChild.itemStyle.normal.borderColor = '#5BD1FF'
            } else if (this.dataLink[i].source === '人为因素' || this.dataLink[i].source === '自然因素' || this.dataLink[i].source === 'xx因素' || this.dataLink[i].source === 'yy因素') {
                dataChild.symbolSize = 30
                dataChild.category = 2
                dataChild.itemStyle.normal.borderColor = '#b457ff'
            }
            this.dataSerise.push(dataChild)
        }
        myChart.setOption({
            title: { text: '实时风险状态',left:'center'},
            backgroundColor: '#fff',
            tooltip: {
                trigger: 'item',
                formatter: (item) => {
                    return item.name + ':' + item.data.value
                }
            },
            animationDurationUpdate: 110,
            animationEasingUpdate: 'quinticInOut',
            color: ['#83e0ff', '#45f5ce', '#b158ff'],
            series: [{
                type: 'graph',
                layout: 'force',
                force: {
                    repulsion: 150,
                    edgeLength: 10
                },
                roam: true,
                label: {
                    normal: {
                        show: true
                    }
                },
                data: this.dataSerise,
                links: this.dataLink,
                lineStyle: {
                    normal: {
                        opacity: 0.9,
                        width: 2,
                        curveness: 0
                    }
                },
                categories: [{
                        name: '0'
                    },
                    {
                        name: '1'
                    },
                    {
                        name: '2'
                    }
                ]
            }]
        })
    },
    initCanv(){
        // 基于准备好的dom，初始化echarts实例
        let myChart = this.$echarts.init(document.getElementById('myCharts'))
        // 绘制图表
        myChart.setOption({
            title: { text: '风险变化趋势图',left:'center'},
            backgroundColor: '#fff',
            grid: {
                // left: "5%",
                bottom: "1%",
                top: "18%",
                containLabel: true
            },
            tooltip: {
                trigger: "axis",
                axisPointer: {
                    type: "shadow",
                }
            },
            legend: {
                show: false,
                x: 0,
                y: 0,
                textStyle: {
                    color: '#000', // 图例文字颜色
                    fontSize: 16,
                }
            },
            xAxis: {
                type: "category",
                boundaryGap: false,
                data: this.xdata,
                axisLabel: {
                    show: true,
                    textStyle: {
                        color: "#000"
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: 'transparent',
                        width: 2 //这里是为了突出显示加上的
                    }
                }
            },
            yAxis: [{
                type: 'value',
                name: '',
                nameTextStyle: {
                    color: '#000',

                },
                min: 131.14,
                max: 700,
                axisLabel: {
                    formatter: '{value}',
                    textStyle: {
                        color: '#000',
                    }
                },
                axisLine: {
                    lineStyle: {
                        color: 'transparent',
                        width: 2 //这里是为了突出显示加上的
                    }
                },
                axisTick: {
                    show: false,
                },
                splitLine: {
                    show: true,
                    lineStyle: {
                        color: '#11366e'
                    }
                }
            }],
            series: [{
                    name: this.titleName,
                    type: 'line',
                    smooth: true,
                    symbol: "none",
                    itemStyle: {
                        normal: {
                            lineStyle: {
                                type: 'dashed',
                            },
                            color: "red", //拐点的颜色
                            borderColor: "#01F699" //拐点边框的颜色
                        }
                    },
                    data: this.dashedArr
                },
                {
                    name: this.titleNames,
                    type: 'line',
                    stack: '总量',
                    smooth: true, //平滑曲线显示
                    symbol: "circle", //标记的图形为实心圆
                    symbolSize: 8,
                    itemStyle: {
                        normal: {
                            color: new this.$echarts.graphic.LinearGradient(0, 0, 1, 0, [{
                                offset: 0,
                                color: 'rgba(3,191,255,1)'
                            }, {
                                offset: 1,
                                color: 'rgba(18,93,236,1)'
                            }]),
                            areaStyle: {
                                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [{
                                    offset: 0,
                                    color: 'rgba(3,191,255,.3)'
                                }, {
                                    offset: 1,
                                    color: 'rgba(18,93,236,.3)'
                                }]),
                            }
                        },
                    },
                    data: this.dataArr
                },
            ]
        })
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.drawLine();
    this.initCanv();
  },
};
</script>
<style lang="scss" scoped>
.riskDy{
    padding:15px;
    .riskDy-top{
        display: flex;
        .risk-boxone{
            width: calc(20% - 10px);
            height: 250px;
            border: 1px solid #ddd;
            margin-right: 10px;
        }
        .risk-boxtwo{
            width: calc(30% - 10px);
            height: 250px;
            border: 1px solid #ddd;
            margin-right: 10px;
        }
        .risk-boxthree{
            width: 50%;
            height: 250px;
            border: 1px solid #ddd
        }
    }
    .riskDy-bottom{
        display: flex;
        margin-top: 15px;
        justify-content:space-between;
        .risk-boone{
           width: calc(50% - 5px);
           height: 250px;
           border: 1px solid #ddd;
        }
        .risk-botwo{
            width: calc(50% - 5px);
            height: 250px;
            border: 1px solid #ddd;

        }
    }
}
</style>