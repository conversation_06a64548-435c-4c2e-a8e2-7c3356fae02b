<template>
  <div v-loading="loading">
    <div class="work-heard">
      <h2>安全承诺</h2>
      <span @click="goEnt">更多</span>
    </div>
    <div v-if="fillBool">
      <div class="fengxianLevel1" v-if="table.riskGrade == '1'">
        <p>承诺风险等级:<span>高风险</span></p>
      </div>
      <div class="fengxianLevel2" v-else-if="table.riskGrade == '2'">
        <p>承诺风险等级:<span>较大风险</span></p>
      </div>
      <div class="fengxianLevel3" v-else-if="table.riskGrade == '3'">
        <p>承诺风险等级:<span>一般风险</span></p>
      </div>
      <div class="fengxianLevel4" v-else-if="table.riskGrade == '4'">
        <p>承诺风险等级:<span>低风险</span></p>
      </div>
      <div class="safty-list">
        <p>
          <span
            >生产装置(套) <i>{{ table.unitsNumber }}</i></span
          ><span
            >重点监管工艺(种) <i>{{ table.dangermsds }}</i></span
          >
        </p>
        <p>
          <span
            >特殊动火作业(处) <i>{{ table.firesNumber }}</i></span
          ><span
            >一级动火作业(处) <i>{{ table.fire1Number }}</i></span
          >
        </p>
        <p>
          <span
            >二级动火作业(处) <i>{{ table.fire2Number }}</i></span
          ><span
            >受限空间作业(处) <i>{{ table.spaceworkNumber }}</i></span
          >
        </p>
        <p>
          <span
            >盲板作业(处) <i>{{ table.blindplateNumber }}</i></span
          ><span
            >高处作业(处) <i>{{ table.highworkNumber }}</i></span
          >
        </p>
        <p>
          <span
            >吊装作业(处) <i>{{ table.liftingworkNumber }}</i></span
          ><span
            >临时用电作业(处) <i>{{ table.electricityworkNumber }}</i></span
          >
        </p>
        <p>
          <span
            >动土作业(处) <i>{{ table.soilworkNumber }}</i></span
          ><span
            >断路作业(处) <i>{{ table.roadworkNumber }}</i></span
          >
        </p>
        <p>
          <span
            >检维修作业(处) <i>{{ table.inspectionNumber }}</i></span
          ><span
            >运行套数(套) <i>{{ table.runNumber }}</i></span
          >
        </p>
      </div>
    </div>
    <div v-if="!fillBool">
      <img
        src="../../../../../static/img/aqcnsb.png"
        style="width: 100%; margin: 20px 0"
      />
      <div style="text-align: center; margin-top: 25px">
        <el-button type="primary" @click="upSafty">上报承诺</el-button>
      </div>
    </div>
    <Fill @save="saveTable" :enterpriseId="enterpriseId" ref="fill"></Fill>
  </div>
</template>
<script>
import { getPromiseToday, getPromiseAdd } from "@/api/entList";
import Fill from "./fill";
export default {
  components: {
    Fill,
  },
  data() {
    return {
      enterpriseId: "",
      table: {},
      fillBool: false,
      loading: true,
      timer: "",
    };
  },
  watch: {},
  methods: {
    getData(id) {
      this.enterpriseId = id;
      this.loading = true;
      getPromiseToday(this.enterpriseId).then((res) => { 
        if (res.data.data == null) {
          this.fillBool = false;
        } else if (res.data.data.commitmentstate == 0) {
          this.fillBool = true;
          this.table = res.data.data;
          //废弃字段
          this.table.onoffdevice = "0";
          //初始化本机时间
          this.table.commiteDate = new Date(new Date()).Format(
            "yy-MM-dd hh:mm"
          );
        } else if (res.data.data.commitmentstate == 1) {
          this.fillBool = false;
          this.table = res.data.data;
          //废弃字段
          this.table.onoffdevice = "0";
          //初始化本机时间
          this.table.commiteDate = new Date(new Date()).Format(
            "yy-MM-dd hh:mm"
          );
        }
        console.log(this.table, "传过去的数据table");
      }).finally(() => {
        this.loading = false;
      });
    },
    saveTable() {
      this.getData(this.enterpriseId);
    },
    upSafty() {
      getPromiseToday(this.enterpriseId).then((res) => {        
        if (res.data.code == 0) {
          var dataList = res.data.data;
          this.$refs.fill.closeBoolean(true, dataList);
        } else {
          this.$refs.fill.closeBoolean(true, "");
        }
      });
      // this.$refs.fill.closeBoolean(true, this.table);

      this.$refs.fill.selectInit();
    },
    goEnt() {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("login/updataActiveName", "safetyCommitment");
      this.$store.commit("controler/updateEntId", this.enterpriseId);
      this.$store.commit("controler/updateEntModelName", "safetyCommitment");
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {
    this.timer = setInterval(() => {
      this.table.commiteDate = new Date(new Date()).Format("yy-MM-dd hh:mm");
    }, 1000);
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 0px !important;
}
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
    cursor: pointer;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.fengxianLevel1 {
  width: 92%;
  margin: 0 auto;
  background: url("/static/img/assets/img/gaofx.png") center center no-repeat;
  background-size: contain;
  p {
    color: #3c4043;
    line-height: 78px;
    text-align: center;
    margin-bottom: 0;
  }
  span {
    color: #ff881e;
    font-size: 20px;
    margin-left: 10px;
  }
}
.fengxianLevel1 {
  width: 92%;
  margin: 0 auto;
  background: url("/static/img/assets/img/jiaodafx.png") center center no-repeat;
  background-size: contain;
  p {
    color: #3c4043;
    line-height: 78px;
    text-align: center;
    margin-bottom: 0;
  }
  span {
    color: #ff481e;
    font-size: 20px;
    margin-left: 10px;
  }
}
.fengxianLevel2 {
  width: 92%;
  margin: 0 auto;
  background: url("/static/img/assets/img/yibanfx.png") center center no-repeat;
  background-size: contain;
  p {
    color: #3c4043;
    line-height: 78px;
    text-align: center;
    margin-bottom: 0;
  }
  span {
    color: #ff881e;
    font-size: 20px;
    margin-left: 10px;
  }
}
.fengxianLevel3 {
  width: 92%;
  margin: 0 auto;
  background: url("/static/img/assets/img/yibanfx.png") center center no-repeat;
  background-size: contain;
  p {
    color: #3c4043;
    line-height: 78px;
    text-align: center;
    margin-bottom: 0;
  }
  span {
    color: #ffc21e;
    font-size: 20px;
    margin-left: 10px;
  }
}
.fengxianLevel4 {
  width: 92%;
  margin: 0 auto;
  background: url("/static/img/assets/img/difx.png") center center no-repeat;
  background-size: contain;
  p {
    color: #3c4043;
    line-height: 78px;
    text-align: center;
    margin-bottom: 0;
  }
  span {
    color: #3d86ff;
    font-size: 20px;
    margin-left: 10px;
  }
}
.safty-list {
  width: 92%;
  margin: 0 auto;
  margin-top: 10px;
  p {
    line-height: 25px;
    span {
      display: inline-block;
      width: 175px;
      font-size: 12px;
      i {
        // display: inline-block;
        float: right;
        font-style: normal;
        font-size: 16px;
        font-weight: bold;
      }
    }
    span:nth-child(2) {
      float: right;
    }
  }
}
</style>
