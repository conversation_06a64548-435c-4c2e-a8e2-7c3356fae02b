<template>
  <div class="systemRunningState">
    <div class="header">
      <div class="title">系统运行状态</div>

      <div class="zhuBox">
        <el-radio-group v-model="radio1" size="small">
          <el-radio-button label="0">场所</el-radio-button>
          <el-radio-button label="1">主体</el-radio-button>
        </el-radio-group>
        <div class="more" @click="toUrl('/workingAccount/runningState')">
          更多
        </div>
      </div>
    </div>
    <div class="container">
      <div class="lot" v-loading="loadingOnline">
        <div class="left">
          <div>在线联网分析</div>
          <img src="../../../../../static/img/gongzuotai_lot_icon.png" alt="" />
        </div>
        <div class="right" v-if="!loadingOnline">
          <div>
            <div class="title">接入企业数</div>
            <div class="num" @click="openOnline(1)">
              <!-- {{ onlineNetworking.linkedNumber }} -->
              {{ onlineNetworking.linkedEnterpCount }}
            </div>
          </div>
          <div>
            <div class="title">在线企业数</div>
            <div class="num" @click="openOnline(2)">
              {{ onlineNetworking.onlineEnterpCount }}
            </div>
          </div>
          <div>
            <div class="title">离线企业数</div>
            <div class="num" @click="openOnline(3)">
              <!-- {{ onlineNetworking.offlineCompanyNum }} -->
              {{
                (onlineNetworking.linkedEnterpCount || 0) -
                (onlineNetworking.onlineEnterpCount || 0)
              }}
            </div>
          </div>
          <div>
            <div class="title">企业在线率</div>
            <div class="num1">
              {{ onlineNetworking.onlineEnterpRate }}
            </div>
          </div>
          <div>
            <div class="title">接入指标数</div>
            <div class="num1">{{ onlineNetworking.allTargetCount }}</div>
          </div>
          <div>
            <div class="title">在线质量</div>
            <div class="num1">{{ onlineNetworking.onlineTargetRate }}</div>
          </div>
        </div>
      </div>
      <div class="video" v-loading="loadingVideo">
        <div class="left">
          <div>视频运行分析</div>
          <img
            src="../../../../../static/img/gongzuotai_video_icon.png"
            alt=""
          />
        </div>
        <div class="right" v-if="!loadingVideo">
          <div>
            <div class="title">接入企业数</div>
            <div class="num" @click="openVideo(1)">
              {{ videoRunningData.linkedEnterpCount }}
            </div>
          </div>
          <div>
            <div class="title">在线企业数</div>
            <div class="num" @click="openVideo(2)">
              {{ videoRunningData.onlineEnterpCount }}
            </div>
          </div>
          <div>
            <div class="title">离线企业数</div>
            <div class="num" @click="openVideo(3)">
              {{
                videoRunningData.linkedEnterpCount -
                videoRunningData.onlineEnterpCount
              }}
            </div>
          </div>
          <div>
            <div class="title">企业在线率</div>
            <div class="num1">{{ videoRunningData.onlineEnterpRate }}</div>
          </div>
          <div>
            <div class="title">监控点位数</div>
            <div class="num1">{{ videoRunningData.allTargetCount }}</div>
          </div>
          <div>
            <div class="title">在线质量</div>
            <div class="num1">
              {{ videoRunningData.onlineTargetRate }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="showes"
      width="1350px"
      top="10vh"
      @close="handleClose()"
      v-dialogDrag
      :key="demoKey"
      :close-on-click-modal="false"
    >
      <div>
        <el-scrollbar class="diagHeight">
          <div class="seach-part">
            <div class="l">
              <el-cascader
                size="mini"
                placeholder="请选择行政区划"
                :options="district"
                v-model="distCode"
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                clearable
                :show-all-levels="true"
                v-if="isShowDist"
              ></el-cascader>
              <el-select
                v-model="majorHazardLevel"
                placeholder="危险源等级"
                size="mini"
                clearable
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-input
                placeholder="请输入企业名称"
                v-model.trim="entName"
                size="mini"
                clearable
              >
              </el-input>
              <el-button
                type="primary"
                size="mini"
                class="btn"
                @click="
                  btnType == 'Video' ? getEntDataVideo() : getEntDataOnline()
                "
                >查询</el-button
              >
              <div v-if="radio1 == 0">
                <CA-button
                  type="primary"
                  size="mini"
                  plain
                  @click="exportExcel()"
                  >导出</CA-button
                >
              </div>
            </div>
          </div>

          <div v-if="radio1 == 0">
            <el-table
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              v-loading="loading"
              :data="tableData.list"
              style="width: 100%"
              border
              @select="select"
              @select-all="select"
            >
              <el-table-column
                type="selection"
                width="50"
                align="center"
              ></el-table-column>
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span>{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="entName"
                label="单位名称"
                width="300"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span
                    @click="goEnt(scope.row)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ scope.row.entName }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column prop="areaName" label="区划" align="center">
              </el-table-column>
              <el-table-column
                prop="majorHazardLevel"
                label="重大危险源企业等级"
                align="center"
              >
                <!-- <template slot-scope="scope">
                <span v-if="scope.row.majorHazardLevel == 1"
                  >一级重大危险源</span
                >
                <span v-if="scope.row.majorHazardLevel == 2"
                  >二级重大危险源</span
                >
                <span v-if="scope.row.majorHazardLevel == 3"
                  >三级重大危险源</span
                >
                <span v-if="scope.row.majorHazardLevel == 4"
                  >四级重大危险源</span
                >
              </template> -->
              </el-table-column>
              <el-table-column
                :prop="
                  btnType == 'Video'
                    ? 'linkedTargetNumber'
                    : 'linkedTargetNumber'
                "
                :label="btnType == 'Video' ? '接入视频点位数' : '接入指标数'"
                align="center"
              >
              </el-table-column>

              <el-table-column
                :prop="btnType == 'Video' ? 'onlineTarget' : 'onlineTarget'"
                label="在线点位数"
                align="center"
              >
              </el-table-column>
              <el-table-column label="在线质量" align="center" width="120">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <span>{{
                      btnType == "Video"
                        ? row.onlineQuality + "%"
                        : row.onlineQuality + "%"
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :prop="
                  btnType == 'Video' ? 'lastCommiteDate' : 'lastCommiteDate'
                "
                label="最近数据上传时间"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
            </el-table>
          </div>

          <div v-else>
            <el-table
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              v-loading="loading"
              :data="tableData.list"
              style="width: 100%"
              border
              @select="select"
              @select-all="select"
            >
              <el-table-column
                type="selection"
                width="50"
                align="center"
              ></el-table-column>
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span>{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="entName"
                label="单位名称"
                width="300"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column prop="areaName" label="区划" align="center">
              </el-table-column>

              <el-table-column
                :prop="
                  btnType == 'Video'
                    ? 'linkedTargetNumber'
                    : 'linkedTargetNumber'
                "
                :label="btnType == 'Video' ? '接入视频点位数' : '接入指标数'"
                align="center"
              >
              </el-table-column>

              <el-table-column
                :prop="btnType == 'Video' ? 'onlineTarget' : 'onlineTarget'"
                label="在线点位数"
                align="center"
              >
              </el-table-column>
              <el-table-column label="在线质量" align="center" width="120">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <span>{{
                      btnType == "Video"
                        ? row.onlineQuality + "%"
                        : row.onlineQuality + "%"
                    }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                :prop="
                  btnType == 'Video' ? 'lastCommiteDate' : 'lastCommiteDate'
                "
                label="最近数据上传时间"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column type="expand" label="展开" width="120">
                <template slot-scope="props">
                  <el-table
                    :data="props.row.siteList"
                    size="mini"
                    class="subTable"
                    style="width: 92%; margin:15px auto;"
                  >
                    <el-table-column
                      prop="entName"
                      label="单位名称"
                      width="300"
                      align="center"
                      :show-overflow-tooltip="true"
                    >
                      <template #default="{ row }">
                        <span
                          @click="goEnt(row)"
                          style="color: #3977ea; cursor: pointer"
                          >{{ row.entName }}</span
                        >
                      </template>
                    </el-table-column>

                    <el-table-column
                      prop="areaName"
                      label="区划"
                      align="center"
                    >
                    </el-table-column>

                    <el-table-column
                      :prop="
                        btnType == 'Video'
                          ? 'linkedTargetNumber'
                          : 'linkedTargetNumber'
                      "
                      :label="
                        btnType == 'Video' ? '接入视频点位数' : '接入指标数'
                      "
                      align="center"
                    >
                    </el-table-column>

                    <el-table-column
                      :prop="
                        btnType == 'Video' ? 'onlineTarget' : 'onlineTarget'
                      "
                      label="在线点位数"
                      align="center"
                    >
                    </el-table-column>
                    <el-table-column
                      label="在线质量"
                      align="center"
                      width="120"
                    >
                      <template slot-scope="{ row }">
                        <div>
                          <span>{{
                            btnType == "Video"
                              ? row.onlineQuality + "%"
                              : row.onlineQuality + "%"
                          }}</span>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :prop="
                        btnType == 'Video'
                          ? 'lastCommiteDate'
                          : 'lastCommiteDate'
                      "
                      label="最近数据上传时间"
                      align="center"
                      :show-overflow-tooltip="true"
                    >
                    </el-table-column>
                  </el-table>
                </template>
              </el-table-column>
            </el-table>
          </div>          
          <div class="pagination">
            <el-pagination
              @current-change="
                btnType == 'Video' ? getEntDataVideo() : getEntDataOnline()
              "
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getVideoRunningData,
  getOnlineNetworking,
  selTargetCount,
  getVideoworkingAnalysisDetail,
  getOnlineNetworkingAnalysisExportExcel,
  exportPromiseDetailsToExcelA,
  exportVideoDetailsToExcelB,
  getVideoAnalysisExportExcel,
  getOnlineNetworkingAnalysisDetail,
  itoSatistics,
  videoSatistics,
  onlineIotSite,
  videoSitePage,
  onlineExport,
  videoExport,
  iotMainPage,
  videoMainPage,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      radio1: "0",
      demoKey: 0,
      distCode: this.$store.state.login.userDistCode,
      loading: false,
      level: ["1", "2", "3", "4"],
      videoRunningData: {},
      onlineNetworking: {},
      currentPage: 1,
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      entName: "",
      showes: false,
      tableData: {},
      title: "",
      majorHazardLevel: "",
      district: this.$store.state.controler.district,
      type: "",
      status: "",
      btnType: "",
      loadingVideo: false,
      loadingOnline: false,
      selection: [],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    radio1: {
      handler(newVal) {
        this.getVideoRunningList();
        this.getOnlineNetworkingList();
        if (newVal == "1") {
        } else {
        }
      },
    },
  },
  //方法集合
  methods: {
    handleClose() {
      // this.$emit("close");
      this.tableData = {};
      this.currentPage = 1;
      this.distCode = this.$store.state.login.userDistCode;
      this.majorHazardLevel = "";
      this.entName = "";
      this.demoKey =
        "demo-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
    },
    toUrl(url) {
      this.$router.push(url);
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].entId;
      }
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.entId);
    },
    openOnline(status) {
      this.showes = true;
      this.btnType = "Online";
      this.status = status;
      this.getEntDataOnline();
    },
    openVideo(status) {
      this.showes = true;
      this.btnType = "Video";
      this.status = status;
      this.getEntDataVideo();
    },
    getEntDataVideo() {
      this.loading = true;
      if (this.status == 1) {
        this.title = "视频运行分析-已接入企业列表";
      } else if (this.status == 2) {
        this.title = "视频运行分析-在线企业列表";
      } else if (this.status == 3) {
        this.title = "视频运行分析-离线企业列表";
      }

      if (this.radio1 == 1) {
        //主体
        videoMainPage({
          distCode: this.distCode,
          entName: this.entName,
          majorHazardLevel: this.majorHazardLevel,
          status: this.status,
          type: this.radio1,
          nowPage: this.currentPage,
          pageSize: 10,
        }).then((res) => {
          if (res.data.data != null) {
            this.tableData = res.data.data;
          } else {
            this.tableData = {};
          }
          this.loading = false;
        });
      } else {
        videoSitePage({
          distCode: this.distCode,
          entName: this.entName,
          majorHazardLevel: this.majorHazardLevel,
          status: this.status,
          type: this.radio1,
          nowPage: this.currentPage,
          pageSize: 10,
        }).then((res) => {
          if (res.data.data != null) {
            this.tableData = res.data.data;
          } else {
            this.tableData = {};
          }
          this.loading = false;
        });
      }
    },
    getEntDataOnline() {
      this.loading = true;
      if (this.status == 1) {
        this.title = "在线联网分析-已接入企业列表";
      } else if (this.status == 2) {
        this.title = "在线联网分析-在线企业列表";
      } else if (this.status == 3) {
        this.title = "在线联网分析-离线企业列表";
      }
      if (this.radio1 == 1) {
        iotMainPage({
          distCode: this.distCode,
          entName: this.entName,
          majorHazardLevel: this.majorHazardLevel,
          status: this.status,
          type: this.radio1,
          nowPage: this.currentPage,
          pageSize: 10,
        }).then((res) => {
          if (res.data.data != null) {
            this.tableData = res.data.data;
          } else {
            this.tableData = {};
          }
          this.loading = false;
        });
      } else {
        onlineIotSite({
          distCode: this.distCode,
          entName: this.entName,
          majorHazardLevel: this.majorHazardLevel,
          status: this.status,
          type: this.radio1,
          nowPage: this.currentPage,
          pageSize: 10,
        }).then((res) => {
          if (res.data.data != null) {
            this.tableData = res.data.data;
          } else {
            this.tableData = {};
          }
          this.loading = false;
        });
      }
    },

    //视频运行分析
    getVideoRunningList() {
      this.loadingVideo = true;
      videoSatistics({
        level: this.level.join(","),
        current: this.currentPage,
        distCode: this.distCode,
        size: 10,
        type: this.radio1,
      }).then((res) => {
        if (res.data.data != null) {
          this.videoRunningData = res.data.data;
        } else {
          this.videoRunningData = {};
        }
        this.loadingVideo = false;
      });
    },
    //在线互联网分析
    getOnlineNetworkingList() {
      this.loadingOnline = true;
      itoSatistics({
        level: this.level.join(","),
        current: this.currentPage,
        distCode: this.distCode,
        type: this.radio1,
        size: 10,
      }).then((res) => {
        if (res.data.data != null) {
          this.onlineNetworking = res.data.data;
        } else {
          this.onlineNetworking = {};
        }
        this.loadingOnline = false;
      });
    },

    exportExcel() {
      let list = [this.distCode, ...this.selection];
      if (this.btnType == "Online") {
        onlineExport({
          // level: this.level.toString(),
          // distCode: this.distCode,
          // distCodeList: list.length <= 1 ? null : list,
          // isContainParent: true,
          entIdList: this.selection.length <= 0 ? null : this.selection,
          status: this.selection.length > 0 ? 4 : this.status,
          type: this.radio1,
          distCode: this.distCode,
          entName: this.entName,

          majorHazardLevel: this.majorHazardLevel,
        }).then((response) => {
          // 处理返回的文件流
          // console.log(response);
          if (response.status == 200) {
            this.$message({
              message: "导出成功",
              type: "success",
            });
          } else {
            this.$message.error("导出失败");
          }
          const blob = new Blob([response.data], { type: "application/xls" });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "在线联网分析" + timestamp + ".xls";
          //下载文件
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
          }, 0);
        });
      } else {
        videoExport({
          // level: this.level.toString(),
          // distCode: this.distCode,
          // distCodeList: list.length <= 1 ? null : list,
          // isContainParent: true,
          distCode: this.distCode,
          entName: this.entName,
          majorHazardLevel: this.majorHazardLevel,
          entIdList: this.selection.length <= 0 ? null : this.selection,
          status: this.status,
          type: this.radio1,
        }).then((response) => {
          // 处理返回的文件流
          // console.log(response);
          if (response.status == 200) {
            this.$message({
              message: "导出成功",
              type: "success",
            });
          } else {
            this.$message.error("导出失败");
          }
          const blob = new Blob([response.data], { type: "application/xls" });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "视频运行分析" + timestamp + ".xls";
          //下载文件
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
          }, 0);
        });
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getVideoRunningList();
    this.getOnlineNetworkingList();
  },
};
</script>
<style lang="scss" scoped>
.subTable{
}
/deep/ .subTable.el-table td.el-table__cell, 
/deep/ .subTable.el-table th.el-table__cell.is-leaf,
/deep/ .el-table--border .subTable.el-table__cell {
    border-bottom: 0;
    border-right:0;
}
/deep/ .el-table--border .subTable{
  border-left:1px solid #EBEEF5;
  border-right:1px solid #EBEEF5;
  border-top:1px solid #EBEEF5
}
.el-table--border .el-table__cell, 
.el-table__body-wrapper .el-table--border.is-scrolling-left~.el-table__fixed{

}
.zhuBox {
  display: flex;
  align-items: center;
}
/deep/ .el-radio-group {
  margin: 0 20px 0 0;
}
/deep/ .el-scrollbar__wrap {
  overflow-x: auto;
  // height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
}
.diagHeight {
  height: 70vh;
  // overflow-y: scroll;
  // overflow-x: hidden;
}
.systemRunningState {
  width: 100%;
  border: 1px solid #d8e0ee;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  .header {
    width: 95%;
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .container {
    color: #3c4043;
    width: 95%;
    padding-bottom: 15px;
    > div {
      width: 100%;
      height: 90px;
      margin-top: 10px;
      background-image: url("../../../../../static/img/runningStateBG.png");
      background-repeat: no-repeat;
    }
    .lot {
      display: flex;
      overflow: hidden;
      .left {
        width: 105px;
        height: 90px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        color: #fff;
        margin-left: 15px;
        font-size: 12px;
        img {
          width: 33px;
          height: 30px;
        }
      }
      .right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 78%;
        text-align: center;
        > div {
          border-right: 1px #dae6ff solid;
          height: 52px;
          width: 80px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          margin-top: 10px;
        }

        > div:nth-last-of-type(1) {
          border-right: 0;
        }
        .title {
          color: #545c65;
          font-size: 12px;
        }
        .num {
          font-size: 18px;
          color: #545c65;
          text-decoration: underline;
          font-weight: 600;
          cursor: pointer;
        }
        .num1 {
          font-size: 18px;
          color: #545c65;
          font-weight: 600;
        }
      }
    }
    .video {
      display: flex;
      overflow: hidden;
      .left {
        width: 105px;
        height: 90px;
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: center;
        color: #fff;
        margin-left: 15px;
        font-size: 12px;
        img {
          width: 33px;
          height: 30px;
        }
      }
      .right {
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 78%;
        text-align: center;
        > div {
          border-right: 1px #dae6ff solid;
          height: 52px;
          width: 80px;
          display: flex;
          justify-content: center;
          flex-direction: column;
          align-items: center;
          margin-top: 10px;
        }

        > div:nth-last-of-type(1) {
          border-right: 0;
        }
        .title {
          color: #545c65;
          font-size: 12px;
        }
        .num {
          font-size: 18px;
          color: #545c65;
          text-decoration: underline;
          font-weight: 600;
          cursor: pointer;
        }
        .num1 {
          font-size: 18px;
          color: #545c65;
          font-weight: 600;
        }
      }
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 700px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      > .btn {
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>