<template>
  <div class="enterpriseList">
    <div class="header">
      <div class="title">救援队伍列表</div>

      <div class="operation">
        <div class="inputBox">
          <el-input
            v-model.trim="keywords"
            size="small"
            placeholder="请输入救援队伍名称"
            class="input"
            clearable
          ></el-input>
          
          <el-cascader
            v-model="rescueTeamTyp"
            size="small"
            placeholder="请选择救援队伍类型"
            :clearable="true"
            :options="rescueTeamTypeData"
            @change="handleChange"
          >
          </el-cascader>
          <!-- <el-select
            v-model="rescueTeamTyp"
            size="small"
            placeholder="请选择救援队伍类型"
            :clearable="true"
          >
            <el-option
              v-for="item in rescueTeamTypeData"
              :key="item.resourceTypeCode"
              :label="item.resourceTypeName"
              :value="item.resourceTypeCode"
            >
            </el-option>
          </el-select> -->
          <!-- <el-select
            v-if="$store.state.login.user.user_type == 'gov'"
            v-model="orgCode"
            size="small"
            filterable
            :clearable="true"
            placeholder="请输入/选择所属单位名称"
          >
            <el-option
              v-for="item in orgData"
              :key="item.orgCode"
              :label="item.orgName"
              :value="item.orgCode"
            >
            </el-option>
          </el-select> -->
          <el-input
            v-model.trim="chargeDept"
            v-if="$store.state.login.user.user_type == 'gov'"
            size="small"
            placeholder="请输入所属单位名称"
            class="input"
            clearable
          ></el-input>
          <el-button type="primary" size="small" @click="searches()"
            >查询</el-button
          >
          <CA-button
            type="primary"
            size="small"
            class="export"
            plain
            @click="exportExcel"
            >导出</CA-button
          >
        </div>
        <el-button
          type="primary"
          size="small"
          @click="addEdit"
          v-if="$store.state.login.user.user_type == 'ent'"
          >新增</el-button
        >
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="55"
          fixed="left"
          align="center"
        >
        </el-table-column>
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="teamName"
          label="救援队伍名称"
          width="340"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="teamtypeName"
          label="类型"
          align="center"
          width="150"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="orgName"
          label="所属单位"
          align="center"
          min-width="300"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="respPer"
          label="主要负责人"
          align="center"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="respmTel"
          label="负责人移动电话"
          align="center"
          min-width="120"
        >
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          min-width="160"
        >
          <template slot-scope="scope">
            <span
              @click="view(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >查看</span
            >
            <span
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="edit(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >编辑</span
            >
            <span
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="deleter(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="size"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增编辑查看 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1100px"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        label-width="157px"
        :hide-required-asterisk="disabled"
      >
        <div class="form_item">
          <h2 class="form_title">基本信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="救援队伍名称" prop="teamName">
                  <el-input
                    v-model.trim="form.teamName"
                    placeholder="请输入救援队伍名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="驻地位置">
                  <el-input
                    v-model.trim="form.address"
                    placeholder="请输入驻地位置"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
               
                <el-form-item label="救援队伍类型" prop="teamtypecode">
                  <!-- <el-select
                    v-model="form.teamtypecode"
                    placeholder="请选择救援队伍类型"
                  >
                    <el-option
                      v-for="item in rescueTeamTypeData"
                      :key="item.resourceTypeCode"
                      :label="item.resourceTypeName"
                      :value="item.resourceTypeCode"
                    />
                  </el-select> -->
                  <!-- {{teamtypecode}} -->
                   <el-cascader
            v-model="form.teamtypecode"
            size="small"
            placeholder="请选择救援队伍类型"
            :clearable="true"
            :options="rescueTeamTypeData"
            @change="handleChange"
            style="width:100%"
          >
          </el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="经度">
                  <el-input
                    v-model.trim="form.longitude"
                    disabled
                    placeholder="请输入经度"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属单位" prop="orgcode">
                  <el-input
                    :disabled="true"
                    v-model.trim="form.orgname"
                    placeholder="请输入所属单位"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度">
                  <el-input
                    v-model.trim="form.latitude"
                    disabled
                    placeholder="请输入纬度"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="map_height">
                  <el-form-item
                    label="队伍性质(专职、兼职)"
                    prop="propertyTeam"
                  >
                    <el-select
                      style="width：100%"
                      v-model="form.propertyTeam"
                      placeholder="请选择队伍性质(专职、兼职)"
                      
                    >
                      <el-option label="专职" value="1"></el-option>
                      <el-option label="兼职" value="0"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="队伍级别(在编、非编)"  >
                    <el-select
                      v-model="form.levelCode"
                      placeholder="请选择队伍级别(在编、非编)"
                      style="width：100%"
                    >
                      <el-option label="在编" value="1"></el-option>
                      <el-option label="非编" value="0"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="队伍人数" prop="totalPerNum">
                    <el-input
                      v-model.trim="form.totalPerNum"
                      placeholder="请输入队伍人数"
                      maxlength="5"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="队伍所在区划">
                  <el-cascader v-model="form.cityDistrictCode"
                               placeholder="请选择队伍所在区划"
                               :clearable="true"
                               :options="district"
                               @change="handleChange">
                  </el-cascader>
                </el-form-item> -->
                  <el-form-item label="队伍所在区划" prop="cityDistrictCodeAry">
                    <el-cascader
                      placeholder="请选择队伍所在区划"
                      :options="district"
                      v-model="form.cityDistrictCodeAry"
                      @change="handleChange"
                      clearable
                      style="width:100%"
                    ></el-cascader>
                  </el-form-item>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="map_height">
                  <el-form-item label="地图定位">
                    <egisMap2
                      :islistener="true"
                      :isdetail="disabled"
                      :datas="form"
                      ref="detailMap"
                      style="height: 220px"
                      @mapCallback="mapcallback"
                    ></egisMap2>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="form_item">
          <h2 class="form_title">负责人信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="主要负责人" prop="respPer">
                  <el-input
                    v-model="form.respPer"
                    placeholder="请输入主要负责人"
                    maxlength="5"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="主要负责人移动电话" prop="respmTel">
                  <el-input
                    v-model.trim="form.respmTel"
                    placeholder="请输入主要负责人移动电话"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="主要负责人固定电话" prop="respoTel">
                  <el-input
                    v-model.trim="form.respoTel"
                    placeholder="请输入主要负责人固定电话"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="传真号码" prop="fax">
                  <el-input
                    v-model.trim="form.fax"
                    placeholder="请输入传真号码"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <div class="form_item">
          <h2 class="form_title">联系人信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="应急负责人" prop="contactPer">
                  <el-input
                    v-model.trim="form.contactPer"
                    placeholder="请输入应急负责人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="应急负责人移动电话" prop="contactmTel">
                  <el-input
                    v-model.trim="form.contactmTel"
                    placeholder="请输入应急负责人移动电话"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="应急负责人固定电话" prop="contactoTel">
                  <el-input
                    v-model.trim="form.contactoTel"
                    placeholder="请输入应急负责人固定电话"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12"> </el-col>
            </el-row>
          </div>
        </div>
        <div class="form_item">
          <h2 class="form_title">补充信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="指挥中心电话">
                  <el-input
                    v-model.trim="form.commType"
                    placeholder="请输入指挥中心电话"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="值班电话">
                  <el-input
                    v-model.trim="form.dutyTel"
                    placeholder="请输入值班电话"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建队时间">
                  <el-date-picker
                  style="width:100%"
                    v-model="form.foundedDate"
                    type="date"
                    placeholder="选择建队时间日期"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="12"> </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="装备情况" prop="equipDesc">
                  <!-- <el-input v-model="form.equipDesc"
                            placeholder="请输入装备情况" /> -->

                  <el-input
                    type="textarea"
                    placeholder="请输入装备情况"
                    v-model.trim="form.equipDesc"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="队伍特长">
                  <el-input
                    type="textarea"
                    placeholder="请输入队伍特长"
                    v-model.trim="form.speciality"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input
                    type="textarea"
                    placeholder="请输入备注"
                    v-model.trim="form.notes"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="!disabled" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
import { size } from "lodash-es";
// import { getDistrictUser } from "@/api/entList";
import {
  getRescueTeamTypeData,
  getRescueTeamTypeData2,
  getRescueTeamListData,
  addRescueTeamListData,
  editRescueTeamListData,
  deleteRescueTeamListData,
  getOrgList,
  getExpertDistrictData,
  exportRescueTeamListData,
  getDistrictUser,
} from "@/api/mergencyResources";

// const { mapState: mapStateLogin } = createNamespacedHelpers("login");
// const { mapState: mapStateControler } = createNamespacedHelpers("controler");
const mapConfig = require("@/assets/json/map.json");
import egisMap2 from "@/components/common/packages/EgisMap";
//D:\job\李大佬\dev-hbs\src\components\common\packages\EgisMap
export default {
  //import引入的组件
  name: "rescueTeam",
  components: {
    egisMap2,
  },
  data() {
    return {
      currentPage: 1,
      enterpId: "",
      title: "新增救援队伍",
      open: false,
      district: [],
      rescueTeamTypeData: [],
      orgCode: "",
      chargeDept: "",
      orgData: [],
      form: {
        teamName: "",
        address: "",
        teamtypecode: '',
        longitude: "",
        latitude: "",
        orgcode: "",
        orgname: "",
        propertyTeam: "",
        levelCode: "",
        totalPerNum: "",
        respPer: "",
        respmTel: "",
        respoTel: "",
        fax: "",
        contactPer: "",
        contactmTel: "",
        contactoTel: "",
        cityDistrictCode: "",
        cityDistrictCodeAry: [],
        commType: "",
        foundedDate: "",
        equipDesc: "",
        speciality: "",
        notes: "",
        dutyTel: "",

        chargeDept: "",
        countyDistrictCode: "",
        districtCode: "",
        objectId: "",
        resourceTypeName: "",
        resourceTypeShortName: "",
      },
      rules: {
        teamName: [
          { required: true, message: "请输入救援队伍名称", trigger: "blur" },
        ],
        teamtypecode: [
          { required: true, message: "请选择救援队伍类型", trigger: "change" },
        ],
        propertyTeam: [
          {
            required: true,
            message: "请选择队伍性质(专职、兼职)",
            trigger: "change",
          },
        ],
        totalPerNum: [
          { required: true, message: "请输入队伍人数", trigger: "blur" },
          {
            pattern: /^(1|([1-9]\d{0,1})|100)$/,
            message: "请输入1-100以内的数字",
          },
        ],
        cityDistrictCodeAry: [
          { required: true, message: "请选择队伍所在区划", trigger: "change" },
        ],
        respPer: [
          { required: true, message: "请输入主要负责人", trigger: "blur" },
        ],
        respmTel: [
          {
            required: true,
            message: "请输入主要负责人移动电话",
            trigger: "blur",
          },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,
            message: "请输入正确的移动电话",
          },
        ],
        respoTel: [
          {
            required: true,
            message: "请输入主要负责人固定电话",
            trigger: "blur",
          },
          {
            pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,
            message: "请输入正确的电话号码",
          },
        ],
        fax: [
          { required: true, message: "请输入传真号码", trigger: "blur" },
          {
            pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,
            message: "请输入正确的传真号码",
          },
        ],
        commType: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,
            message: "请输入正确的电话号码",
          },
        ],
        dutyTel: [
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,
            message: "请输入正确的电话号码",
          },
        ],
        contactPer: [
          { required: true, message: "请输入应急负责人", trigger: "blur" },
        ],
        contactmTel: [
          {
            required: true,
            message: "请输入应急负责人移动电话",
            trigger: "blur",
          },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,
            message: "请输入正确的电话号码",
          },
        ],
        contactoTel: [
          {
            required: true,
            message: "请输入应急负责人固定电话",
            trigger: "blur",
          },
          {
            pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,
            message: "请输入正确的电话号码",
          },
        ],
        equipDesc: [
          { required: true, message: "请输入装备情况", trigger: "blur" },
        ],
      },
      disabled: false,
      tableData: [],
      entType: [
        {
          label: "生产",
          value: "01",
        },
        {
          label: "经营",
          value: "02",
        },
        {
          label: "使用",
          value: "03",
        },
        {
          label: "第一类非药品类易制毒",
          value: "04",
        },
      ],
      loading: false,
      selection: [],
      total: 0,
      size: 10,
      keywords: "",
      rescueTeamTyp: [],
      entTypeVal: "",
    };
  },
  created() {},
  //方法集合
  methods: {
    handleChange(value) {
      console.log(value, "选择地区");
    },
    addEdit() {
      this.reset();
      this.title = "新增救援队伍";
      this.disabled = false;
      this.form.orgcode = this.$store.state.login.user.org_code;
      this.form.orgname = this.$store.state.login.user.org_name;
      console.log(this.form, "新增表单数据");
      this.open = true;
      this.getDistrict();
      this.form.longitude = mapConfig.map.defaultExtent.center[0];
      this.form.latitude = mapConfig.map.defaultExtent.center[1];
    },
    view(row) {
      this.open = true;
      this.disabled = true;
      this.title = "查看救援队伍";
      this.rules = {}; // 清空验证规则
      const rowV = Object.assign({ orgname: row.orgName }, row);
      this.$set(this, "form", rowV);
      this.form.cityDistrictCodeAry = [
        row.districtCode,
        row.cityDistrictCode,
        row.countyDistrictCode,
      ];
      this.getDistrict();
    },
    edit(row) {
      this.reset();
      this.open = true;
      this.disabled = false;
      this.title = "编辑救援队伍";
      const rowV = Object.assign({ orgname: row.orgName }, row);
      this.$set(this, "form", rowV);
      this.form.cityDistrictCodeAry = [
        row.districtCode,
        row.cityDistrictCode,
        row.countyDistrictCode,
      ];
      this.getDistrict();
      if (!this.form.longitude || !this.form.latitude) {
        this.form.longitude = mapConfig.map.defaultExtent.center[0];
        this.form.latitude = mapConfig.map.defaultExtent.center[1];
      }
      // debugger
      // this.form.teamtypecode = [row.teamtypecode]
    },
    deleter(row) {
      const id = row.teamId;
      this.$confirm("是否确认删除该救援队伍信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteRescueTeamListData({
            teamId: id,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("删除成功");
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getList();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getList();
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        teamName: "",
        address: "",
        teamtypecode: "",
        longitude: "",
        latitude: "",
        orgcode: "",
        orgname: "",
        propertyTeam: "",
        levelCode: "",
        totalPerNum: "",
        respPer: "",
        respmTel: "",
        respoTel: "",
        fax: "",
        contactPer: "",
        contactmTel: "",
        contactoTel: "",
        cityDistrictCode: "",
        cityDistrictCodeAry: "",
        commType: "",
        foundedDate: "",
        equipDesc: "",
        speciality: "",
        notes: "",
        dutyTel: "",

        chargeDept: "",
        countyDistrictCode: "",
        districtCode: "",
        objectId: "",
        resourceTypeName: "",
        resourceTypeShortName: "",
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
    //查询
    searches() {
      this.currentPage = 1;
      this.getList();
    },
    getOrgData() {
      getOrgList({
        districtCode: "",
        nowPage: 1,
        orgCode: "",
        orgName: "",
        pageSize: 100,
        tenantId: "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.orgData = res.data.data;
        }
      });
    },
    //获取列表
    getList() {
      console.log(this.rescueTeamTyp)
      this.loading = true;
      getRescueTeamListData({
        chargeDept: this.chargeDept,
        keywords: this.keywords,
        levelCode: "",
        listOrder: {},
        nowPage: this.currentPage,
        orgCode: this.orgCode,
        pageSize: this.size,
        rescueInfoIds: [],
        // teamtypecode: this.rescueTeamTyp ? this.rescueTeamTyp : [],
        teamtypecode:this.rescueTeamTyp.length >0
          ? [this.rescueTeamTyp[this.rescueTeamTyp.length - 1]]
          : [],
        tenantId: "",
        updateTime: "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    //获取救援队伍类型
    getRescueTeamType() {
      getRescueTeamTypeData2({}).then((res) => {
        if (res.data.status == 200) {
          // debugger;
          // this.rescueTeamTypeData = res.data.data;
          this.rescueTeamTypeData = res.data.data;
          this.rescueTeamTypeData.forEach((item) => {
            item.value = item.id;
            if (item.children && item.children.length > 0) {
              item.children.forEach((items) => {
                items.value = items.id;
              });
            }
          });
        }
      });
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then((res) => {
        res.data.data.treeData.forEach((item) => {
          item.value = item.id;
          if (item.children && item.children.length > 0) {
            item.children.forEach((items) => {
              items.value = items.id;
              if (items.children && items.children.length > 0) {
                items.children.forEach((item3) => {
                  item3.value = item3.id;
                });
              }
            });
          }
        });
        this.district = res.data.data.treeData;
      });
      //  getExpertDistrictData().then(res => {
      //   if (res.data.status == 200) {
      //     this.district = res.data.data.treeData[0].children
      //     this.district.forEach(item => {
      //       item.value = item.id
      //       if (item.children && item.children.length > 0) {
      //         item.children.forEach(items => {
      //           items.value = items.id
      //         })
      //       }
      //     })
      //   }
      // })
    },
    changeCascader(value) {
      if (!value) {
        this.form.cityDistrictCode = this.$store.state.login.userDistCode;
      }
    },
    submitForm() {
      console.log(this.$store.state.login, "this.$store.state.login");
      console.log(this.form, "cityDistrictCode测试测试");
      //cityDistrictCode
      this.$refs.form.validate((flag) => {
        if (flag) {
          console.log(this.form.teamtypecode)
          if (this.form.teamtypecode instanceof Array) {
             if (this.form.teamtypecode && this.form.teamtypecode.length > 0) {
              if (this.form.teamtypecode.length == 1) {
                this.form.teamtypecode = this.form.teamtypecode[0]
                
              } else if (this.form.teamtypecode.length == 2) {
                this.form.teamtypecode = this.form.teamtypecode[1]
              }
            } else {
              this.form.teamtypecode = ''
            }
          }else{
             this.form.teamtypecode = this.form.teamtypecode
          }



          if (this.form.teamId) {
            const newAry = this.form.cityDistrictCodeAry;
            this.form.districtCode = this.form.cityDistrictCodeAry[0];
            this.form.cityDistrictCode = this.form.cityDistrictCodeAry[1];
            this.form.countyDistrictCode = this.form.cityDistrictCodeAry[2];
            editRescueTeamListData(this.form).then((res) => {
              if (res.data.status == 200) {
                this.$message.success("操作成功");
                this.open = false;
                this.reset();
                this.getList();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {
            const newAry = this.form.cityDistrictCodeAry;
            this.form.districtCode = this.form.cityDistrictCodeAry[0];
            this.form.cityDistrictCode = this.form.cityDistrictCodeAry[1];
            this.form.countyDistrictCode = this.form.cityDistrictCodeAry[2];
            addRescueTeamListData(this.form).then((res) => {
              if (res.data.status == 200) {
                this.$message.success("操作成功");
                this.open = false;
                this.reset();
                this.getList();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        }
      });
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        this.form.address = data.formatted_address;
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
    handleSelectionChange() {},
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].teamId;
      }
    },
    exportExcel() {
      exportRescueTeamListData({
        chargeDept: "",
        keywords: this.keywords,
        levelCode: "",
        listOrder: {},
        nowPage: this.currentPage,
        orgCode: this.orgCode,
        pageSize: this.size,
        rescueInfoIds: this.selection,
        // teamtypecode: this.rescueTeamTyp ? this.rescueTeamTyp: [],
         teamtypecode:this.rescueTeamTyp.length >0
          ? [this.rescueTeamTyp[this.rescueTeamTyp.length - 1]]
          : [],
        tenantId: "",
        updateTime: "",
      }).then((response) => {
        this.$message({
          message: "导出成功",
          type: "success",
        });
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "救援队" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
  },
  mounted() {
    this.getList();
    this.getRescueTeamType();
    this.getOrgData();
    this.form.orgcode = this.$store.state.login.user.org_code;
    console.log(this.$store.state.login.user.org_name, "所属单位");
    this.form.orgname = this.$store.state.login.user.org_name;
  },
};
</script>
<style lang="scss" scoped>
.enterpriseList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
        }
      }
    }
    .export {
      // margin-right: 20px;
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form_item {
    .form_title {
      font-size: 20px;
      color: #2f85de;
      margin-left: 20px;
    }
  }
}
</style>
<style lang="scss" scoped>
/deep/.el-dialog {
  height: 650px;
  overflow: hidden;
  .el-dialog__body {
    height: 80%;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
</style>