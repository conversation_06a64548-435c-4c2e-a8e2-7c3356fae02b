"use strict";
const path = require("path");
const { BASE_URL } = require("../static/ip.js");
const url = "http://" + BASE_URL;

// const url = "https://" + 'yyzc.hbsis.gov.cn:30003';
// const url=''
const TRAIN_URL = "http://***********:8895/"; // 考试开发环境
module.exports = {
  BASE_URL: BASE_URL,
  dev: {
    // Paths
    assetsSubDirectory: "static",
    assetsPublicPath: "/",
    useEslint: false,
    // https:true,
    proxyTable: [
      {
        context: [
          "/admin",
          "/auth",
          "/gen",
          "/enterprise",
          "/file",
          "/artemis",
          "/examine",
          "/gapi",
          "/yun-enterprise-biz",
          "/hardStone",
          "/gemp-model",
          "/api/gemp/model",
          "/gemp-chemical",
          "/gempProxy",
          "/ht",
          "/gemp-general",
          // '/evo-apigw'
        ],
        target: url,
        //target: 'http://**************:8099'
        secure: false,
        changeOrigin: true,
      },
      {
        context: ["/evo"],
        target: "https://***********0:2443",
        // target: 'https://59.208.147.51/', // 政务内网
        pathRewrite: {
          "/evo": "",
        },
        secure: false,
      },
      {
        context: ["/egisUrl"],
        target: "http://120.52.31.31:590/",
        // target: 'https://59.208.147.51/', // 政务内网
        pathRewrite: {
          "/egisUrl": "",
        },
        secure: false,
      },
      {
        context: ["/gemp-general"],
        target: TRAIN_URL, // 考试开发环境
        pathRewrite: {
          "/gemp-general": "",
        },
        secure: false,
      },
      // {
      //   context: ['/enterprise'],
      //   target: 'http://***********:8012',  // 执法推荐
      //   pathRewrite: {
      //     '/enterprise': '',
      //   },
      //   secure: false
      // },
      // {
      //   context: ['/api/gemp/model'],
      //   target: url,
      //   pathRewrite: { //重写
      //     '/^api/gemp/model/': ''
      //   }
      // },
      // {
      //   context: ['/gapi/gemp-model'],
      //   target: 'http://***********:8868',
      //   pathRewrite: { //重写
      //     '/^gapi/': ''
      //   }
      // }
      // {
      //   context: ['/evo-websocket/**'],
      //   target: 'wss://***********0:2443',
      //   ws: true,
      //   secure: false,
      // }
    ],
    host: "0.0.0.0", // can be overwritten by process.env.HOST
    port: 8086, // can be overwritten by process.env.PORT, if port is in use, a free one will be determined
    autoOpenBrowser: false,
    errorOverlay: true,
    notifyOnErrors: true,
    poll: 1000, // 启用轮询，每1秒检查一次文件变化，提高响应速度
    /**
     * Source Maps
     */
    // https://webpack.js.org/configuration/devtool/#development
    devtool: "cheap-module-eval-source-map",
    // If you have problems debugging vue-files in devtools,
    // set this to false - it *may* help
    // https://vue-loader.vuejs.org/en/options.html#cachebusting
    cacheBusting: true,
    cssSourceMap: true,
  },

  build: {
    // Template for index.html
    index: path.resolve(__dirname, "../dist/index.html"),

    // Paths
    assetsRoot: path.resolve(__dirname, "../dist"),
    assetsSubDirectory: "static",
    assetsSubDirectorys: "config",
    assetsPublicPath: "/",

    /**
     * Source Maps
     */

    productionSourceMap: false,
    // https://webpack.js.org/configuration/devtool/#production
    devtool: "source-map",
    configureWebpack: {
      performance: {
        hints: "warning",
        //入口起点的最大体积 整数类型（以字节为单位）
        maxEntrypointSize: 50000000,
        //生成文件的最大体积 整数类型（以字节为单位 300k）
        maxAssetSize: 30000000,
        //只给出下面代码中的文件性能提示
        assetFilter: function (assetFilename) {
          return (
            assetFilename.endsWith(".svg") ||
            assetFilename.endsWith(".png") ||
            assetFilename.endsWith(".css") ||
            assetFilename.endsWith(".js")
          );
        },
      },
    },
    // Gzip off by default as many popular static hosts such as
    // Surge or Netlify already gzip all static assets for you.
    // Before setting to `true`, make sure to:
    // npm install --save-dev compression-webpack-plugin
    productionGzip: true,
    productionGzipExtensions: ["js", "css"],

    // Run the build command with an extra argument to
    // View the bundle analyzer report after build finishes:
    // `npm run build --report`
    // Set to `true` or `false` to always turn it on or off
    bundleAnalyzerReport: process.env.npm_config_report,
  },
};
