<template>
  <div class="safety-check">
    <!-- 添加头部标题 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box" @click="goBack">
              <a-icon type="home" theme="filled" class="icon" />
              统计报表管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <span>{{ reportName }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 内容区域包裹 -->
    <div class="content">
      <!-- 头部工具栏 -->
      <div class="tool-bar">
        <div class="left">
          <el-button-group>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-setting"
              @click="handleToolbarClick('fields')"
              >字段设定</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-download"
              @click="handleToolbarClick('export')"
              >导出</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-share"
              @click="handleToolbarClick('share')"
              >分享</el-button
            >
          </el-button-group>
        </div>
        <div class="right">
          <el-form :inline="true" :model="searchForm" class="search-form">
            <el-form-item>
              <el-date-picker
                v-model="timeRange"
                size="mini"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                unlink-panels
                @change="handleDateChange"
                style="width: 240px"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-cascader
                v-show="this.$store.state.login.user.user_type == 'gov'"
                size="mini"
                placeholder="请选择地市/区县"
                :options="district"
                v-model="searchForm.districtCode"
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                clearable
                @change="handleChange"
                :show-all-levels="true"
                style="width: 180px"
              ></el-cascader>
            </el-form-item>
            <el-form-item>
              <el-select
                size="mini"
                v-model="searchForm.typeCode"
                placeholder="事故类型"
                clearable
                style="width: 120px"
              >
                <el-option
                  v-for="item in accidentTypes"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input
                v-model.trim="searchForm.keyword"
                size="mini"
                placeholder="请输入关键字"
                clearable
                style="width: 160px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="mini"
                icon="el-icon-search"
                @click="handleSearch"
                >检索</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 表格内容 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        height="550px"
        v-loading="tableLoading"
        :span-method="tableMergeMethod"
      >
        <template v-for="column in displayColumns">
          <!-- 处理嵌套列 -->
          <template v-if="column.children">
            <el-table-column
              :key="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template v-for="child in column.children">
                <el-table-column
                  :key="child.prop"
                  :prop="child.prop"
                  :label="child.label"
                  :width="child.width"
                >
                </el-table-column>
              </template>
            </el-table-column>
          </template>
          <!-- 处理普通列 -->
          <el-table-column
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
          >
          </el-table-column>
        </template>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          background
        >
        </el-pagination>
      </div>

      <!-- 使用字段设定弹窗组件 -->
      <field-settings-dialog
        :visible.sync="fieldSettingsVisible"
        :all-fields="allFields"
        :table-list="tableList"
        :current-fields="selectedFields"
        @save="handleFieldSettingsSave"
      />

      <!-- 添加导出预览弹窗 -->
      <el-dialog
        :visible.sync="exportDialogVisible"
        title="导出预览"
        width="90%"
        top="5vh"
        :close-on-click-modal="false"
        custom-class="export-dialog"
        append-to-body
      >
        <div class="export-preview" ref="exportPreview">
          <h2 class="preview-title">{{ reportName }}</h2>
          <div class="preview-time">
            {{ getCurrentDate() }}
          </div>
          <el-table
            :data="tableData"
            border
            height="500px"
            style="width: 100%"
            class="preview-table"
          >
            <template v-for="column in displayColumns">
              <template v-if="column.children">
                <el-table-column
                  :key="column.prop"
                  :label="column.label"
                  :width="column.width"
                >
                  <template v-for="child in column.children">
                    <el-table-column
                      :key="child.prop"
                      :prop="child.prop"
                      :label="child.label"
                      :width="child.width"
                    >
                    </el-table-column>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                v-else
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                :width="column.width"
              >
              </el-table-column>
            </template>
          </el-table>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleExport">导出</el-button>
          <!-- <el-button type="primary" @click="handlePrint">打印</el-button> -->
        </div>
      </el-dialog>

      <!-- 添加分享弹窗 -->
      <el-dialog
        title="分享"
        :visible.sync="shareDialogVisible"
        width="600px"
        append-to-body
      >
        <div class="share-dialog">
          <div v-loading="isLoadingOrgTree">
            <div v-if="isLoadingOrgTree">正在获取机构树数据，请稍候...</div>
            <div v-else class="org-tree">
              <el-tree
                ref="orgTree"
                :data="orgTreeData"
                :props="orgTreeProps"
                show-checkbox
                node-key="id"
                default-expand-all
              >
              </el-tree>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="shareDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleShare">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import FieldSettingsDialog from "../components/FieldSettingsDialog.vue";
import { getOrgTree } from "@/api/user";
import {
  getReportById,
  getReportData,
  exportReport,
  shareReport,
} from "@/api/smartReport";

export default {
  name: "CountReportDetail",
  components: {
    FieldSettingsDialog,
  },
  props: {
    reportId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      isLoadingOrgTree: false, // 新增加载状态
      orgTreeCache: null, // 新增缓存
      loadingTimeout: null, // 新增超时处理
      reportName: "",
      searchForm: {
        startTime: "",
        endTime: "",
        districtCode: "",
        typeCode: "",
        keyword: "",
      },
      timeRange: [],
      district: this.$store.state.controler.district,
      tableData: [],
      tableLoading: false, // 表格加载状态
      // 分页相关数据
      pagination: {
        currentPage: 1,
        pageSize: 20,
        total: 0,
      },
      fieldSettingsVisible: false,
      selectedFields: [],
      allFields: [], // 从接口获取的字段列表
      displayColumns: [], // 显示的列配置
      transferData: [], // transfer 组件的数据列表
      mergeInfo: {}, // 合并单元格信息
      exportDialogVisible: false,
      shareDialogVisible: false,
      orgTreeData: [],
      tableList: [], // 添加 tableList
      orgTreeProps: {
        children: "children",
        label: "orgName",
      },
      // 修改分享相关数据结构
      shareForm: {
        reportBaseId: [], // 修改为数组类型
        departmentId: [], // 部门ID数组
      },
      accidentTypes: [
        { value: "1", label: "火灾事故" },
        { value: "2", label: "交通事故" },
        { value: "3", label: "生产安全事故" },
        { value: "4", label: "自然灾害" },
        { value: "5", label: "其他事故" },
      ],
    };
  },

  methods: {
    // 处理地市/区县选择
    handleChange(value) {
      if (value) {
        this.searchForm.districtCode = value;
      } else {
        this.searchForm.districtCode = this.$store.state.login.userDistCode;
      }
    },
    // 处理日期选择
    handleDateChange(value) {
      this.searchForm.startTime = value[0];
      this.searchForm.endTime = value[1];
    },
    // 获取报表详情
    async getReportDetail() {
      try {
        const res = await getReportById({ id: this.reportId });
        if (res.data.status === 200) {
          const reportData = res.data.data;

          // 设置报表名称
          this.reportName = reportData.reportName;

          // 构造 tableList
          this.tableList = [
            {
              reportNameEn: reportData.reportTitleEn,
              reportNameCn: reportData.reportTitleCn,
            },
          ];

          // 转换字段数据结构为二维数组 [key, label]
          this.allFields = reportData.reportContentEns.map((key, index) => [
            key,
            reportData.reportContentCns[index],
          ]);

          // 初始化选中所有字段
          this.selectedFields = reportData.reportContentEns;

          // 转换为 transfer 组件需要的数据格式
          this.convertToTransferData();

          // 更新显示列
          this.updateDisplayColumns();

          // 保存分享需要的数据
          this.shareForm = {
            reportBaseId: [this.reportId], // 使用数组包裹报表ID
            departmentId: [], // 初始化为空数组
          };
        }
      } catch (error) {
        console.error("获取报表详情失败:", error);
        this.$message.error("获取报表详情失败");
      }
    },

    // 转换为 transfer 组件需要的数据格式
    convertToTransferData() {
      const result = [];
      this.allFields.forEach(([key, label]) => {
        result.push({
          key: key,
          label: label,
          disabled: false,
        });
      });
      this.transferData = result;
    },

    // 更新显示列
    updateDisplayColumns() {
      if (!this.allFields.length) return;

      // 如果没有选中字段，显示所有字段
      if (!this.selectedFields.length) {
        this.displayColumns = this.allFields.map(([key, label]) => ({
          prop: key,
          label: label,
        }));
        return;
      }

      // 根据选中的字段过滤显示列
      this.displayColumns = this.allFields
        .filter(([key]) => this.selectedFields.includes(key))
        .map(([key, label]) => ({
          prop: key,
          label: label,
        }));
    },

    // 字段设置保存
    handleFieldSettingsSave({ selectedFields, selectedLabels }) {
      this.selectedFields = selectedFields;
      this.updateDisplayColumns();
      this.fieldSettingsVisible = false;
    },

    // 工具栏按钮点击
    handleToolbarClick(type) {
      switch (type) {
        case "fields":
          this.fieldSettingsVisible = true;
          break;
        case "export":
          this.exportDialogVisible = true;
          break;
        case "share":
          this.shareDialogVisible = true;
          this.getOrgTreeData();
          break;
        case "search":
          this.handleSearch();
          break;
      }
    },

    // 处理搜索
    handleSearch() {
      this.pagination.currentPage = 1; // 重置到第一页
      this.getReportData();
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pagination.pageSize = val;
      this.pagination.currentPage = 1; // 重置到第一页
      this.getReportData();
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.currentPage = val;
      this.getReportData();
    },

    // 保存为Excel
    async handleExport() {
      this.exportDialogVisible = false;

      try {
        this.$message.info("正在导出数据，请稍候...");

        const response = await exportReport({
          id: this.reportId,
          nowPage: 1,
          pageSize: 10000, // 导出时获取所有数据
          ...this.searchForm,
        });

        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });

        // 获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = this.reportName + timestamp + ".xls";

        // 下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();

        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);

        this.$message.success("导出成功");
      } catch (error) {
        console.error("导出失败:", error);
        this.$message.error("导出失败，请稍后重试");
      }
    },

    // 打印预览内容
    handlePrint() {},

    // 修改获取当前日期的方法
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}年${month}月${day}日`;
    },

    // 处理合并单元格数据
    processMergeData(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return data;
      }

      // 重置合并信息
      this.mergeInfo = {};

      // 按event_name分组
      const groupedData = {};
      data.forEach((item, index) => {
        const eventName = item.event_name || item.eventName;
        if (eventName) {
          if (!groupedData[eventName]) {
            groupedData[eventName] = [];
          }
          groupedData[eventName].push({ ...item, originalIndex: index });
        }
      });

      // 计算合并信息
      let currentIndex = 0;
      Object.keys(groupedData).forEach((eventName) => {
        const group = groupedData[eventName];
        const groupLength = group.length;

        if (groupLength > 1) {
          // 需要合并的字段
          const mergeFields = [
            "event_name",
            "eventName",
            "org_num",
            "orgNum",
            "person_num",
            "personNum",
          ];

          mergeFields.forEach((field) => {
            if (!this.mergeInfo[field]) {
              this.mergeInfo[field] = {};
            }

            // 第一行显示合并单元格
            this.mergeInfo[field][currentIndex] = {
              rowspan: groupLength,
              colspan: 1,
            };

            // 其他行隐藏
            for (let i = 1; i < groupLength; i++) {
              this.mergeInfo[field][currentIndex + i] = {
                rowspan: 0,
                colspan: 0,
              };
            }
          });
        }

        currentIndex += groupLength;
      });

      return data;
    },

    // 表格合并单元格方法
    tableMergeMethod({ row, column, rowIndex, columnIndex }) {
      const fieldName = column.property;

      if (this.mergeInfo[fieldName] && this.mergeInfo[fieldName][rowIndex]) {
        return this.mergeInfo[fieldName][rowIndex];
      }

      return {
        rowspan: 1,
        colspan: 1,
      };
    },

    // 返回列表
    goBack() {
      this.$emit("goBack");
    },

    // 获取报表数据
    async getReportData() {
      this.tableLoading = true;
      try {
        const res = await getReportData({
          id: this.reportId,
          nowPage: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          ...this.searchForm,
        });

        if (res.data?.status === 200) {
          const pageData = res.data?.data?.page?.data;
          let tableData = pageData?.list || [];

          // 检查是否需要合并单元格
          const reportTitleEn = res.data?.data?.dto?.reportTitleEn;
          if (reportTitleEn === "admin_punishment_info") {
            // 处理合并单元格数据
            tableData = this.processMergeData(tableData);
          }

          this.tableData = tableData;
          this.pagination.total = pageData?.total || 0;
        } else {
          this.$message.error(res.data?.msg || "获取数据失败");
          this.tableData = [];
          this.pagination.total = 0;
        }
      } catch (error) {
        console.error("获取报表数据失败:", error);
        this.$message.error("获取数据失败，请稍后重试");
        this.tableData = [];
        this.pagination.total = 0;
      } finally {
        this.tableLoading = false;
      }
    },

    // 获取机构树数据
    async getOrgTreeData() {
      // 如果已经有缓存数据，直接使用缓存
      if (this.orgTreeCache) {
        this.orgTreeData = this.orgTreeCache;
        return;
      }

      this.isLoadingOrgTree = true; // 标记开始加载

      // 设置加载超时
      this.loadingTimeout = setTimeout(() => {
        if (this.isLoadingOrgTree) {
          this.$message.warning("数据加载较慢，请耐心等待...");
        }
      }, 3000);

      try {
        const res = await getOrgTree({
          districtCode: this.$store.state.login.userDistCode,
          type: this.$store.state.login.user.user_type === "gov" ? 1 : 2,
        });

        if (res.data.status === 200) {
          this.orgTreeData = res.data.data;
          this.orgTreeCache = res.data.data; // 缓存数据

          if (!this.orgTreeData || this.orgTreeData.length === 0) {
            this.$message.warning("暂无机构数据");
          }
        } else {
          this.$message.error(res.data.msg || "获取机构树失败");
        }
      } catch (error) {
        console.error("获取机构树失败:", error);
        this.$message.error("获取机构树失败，请稍后重试");
      } finally {
        clearTimeout(this.loadingTimeout); // 清除超时定时器
        this.isLoadingOrgTree = false; // 标记加载结束
      }
    },

    // 处理分享
    async handleShare() {
      const selectedNodes = this.$refs.orgTree.getCheckedNodes();
      this.shareForm.departmentId = selectedNodes.map((node) => node.id);

      try {
        // 调用分享接口
        const res = await shareReport({
          reportBaseId: this.shareForm.reportBaseId, // 已经是数组
          departmentId: this.shareForm.departmentId,
        });

        if (res.data.status === 200) {
          this.$message.success("分享成功");
          this.shareDialogVisible = false;
        } else {
          this.$message.error(res.data.msg || "分享失败");
        }
      } catch (error) {
        console.error("分享失败:", error);
        this.$message.error("分享失败");
      }
    },

    // 显示分享弹窗
    showShareDialog() {
      this.shareDialogVisible = true;
      this.getOrgTreeData();
    },

    // 修改初始化方法
    async initData(row) {
      // 如果传入了row参数，说明是从列表页跳转过来的
      if (row) {
        await this.getReportDetail();
        this.getReportData();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-check {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }

      .icon-box {
        cursor: pointer;

        &:hover {
          color: #3977ea;

          .icon {
            color: #3977ea;
          }
        }
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;
  }

  .tool-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 12px 0;
    border-bottom: 1px solid #ebeef5;

    .left {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 8px;

      .el-button-group {
        margin-right: 0;

        .el-button {
          margin-right: 0;

          &:not(:last-child) {
            margin-right: -1px;
          }
        }
      }

      .el-divider--vertical {
        margin: 0 16px;
        height: 24px;
      }
    }

    .right {
      display: flex;
      align-items: center;
      flex-wrap: wrap;

      .search-form {
        margin: 0;
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        .el-form-item {
          margin-bottom: 0;
          margin-right: 12px;

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    // 响应式布局
    @media (max-width: 1400px) {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .left,
      .right {
        width: 100%;
      }

      .right {
        justify-content: flex-end;
      }
    }

    @media (max-width: 768px) {
      .left {
        .el-button-group {
          .el-button {
            padding: 7px 10px;
            font-size: 12px;
          }
        }
      }

      .right {
        .search-form {
          .el-form-item {
            margin-right: 8px;
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    padding: 20px 0;
  }

  .field-settings {
    display: flex;
    height: 500px;
    gap: 20px; // 添加间距

    .left-panel {
      .table-select {
        margin-bottom: 20px;

        .panel-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .el-select {
          width: 100%;
        }
      }

      .field-list {
        .panel-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
        }
      }
    }
  }
}

// 优化弹窗样式
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    border-top: 1px solid #ebeef5;
    padding: 15px 20px;
  }
}

::v-deep .el-transfer {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-transfer-panel {
    width: 300px;

    &__header {
      background: #f5f7fa;
    }
  }

  .el-transfer__buttons {
    padding: 0 20px;

    .el-button {
      display: block;
      margin: 10px 0;
    }
  }
}

// 优化多选样式
::v-deep .el-select {
  .el-select__tags {
    max-width: calc(100% - 30px);
  }

  .el-tag {
    margin: 2px;
  }
}

.export-preview {
  padding: 20px;
  background-color: #fff;

  .preview-title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .preview-time {
    text-align: right;
    margin-bottom: 20px;
    color: #333;
    font-size: 15px;
  }

  .preview-table {
    margin-bottom: 20px;
  }
}

// 打印样式
@media print {
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }

  .export-preview {
    padding: 0;
  }
}

// 优化弹窗样式
::v-deep .export-dialog {
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.share-dialog {
  .org-tree {
    height: 400px;
    overflow: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
  }
}

// 编辑弹窗样式
::v-deep .el-dialog {
  .el-form {
    .el-form-item {
      margin-bottom: 18px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }
  }
}

// 工具栏按钮状态优化
::v-deep .tool-bar {
  .el-button-group {
    .el-button {
      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
    }
  }

  .el-button {
    transition: all 0.2s ease;
    font-weight: 500;
  }
}
</style>
