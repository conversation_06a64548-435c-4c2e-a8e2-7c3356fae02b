import axios from "axios";
import qs from "qs";


// 角色侧导航展开数据
export const getRoleGroupData = data => {
    return axios({
        method: "get",
        url: "/admin/role/tree/-1",
        data: qs.stringify(data)
    });
};
//角色组信息
export const getRoleList = data => {
    return axios({
      method: "get",
      url: "/admin/role/group/list?systemCode="+data.systemCode+"&parentId="+data.parentId+"&size="+data.pageSize+'&current='+data.pageNo,
    });
};
export const getRoleGroupList = data => {
    return axios({
      method: "get",
      url: "/admin/role/list/"+data.groupId+"?size="+data.pageSize+'&current='+data.pageNo,
    });
};
//保存角色组
export const getSaveRole = data => {
    return axios({
      method: "post",
      url: "/admin/role/group/add",
      data: qs.stringify(data)
    });
};
//详情
export const saveRoleByGroupId = data =>{
    return axios({
      method:"get",
      url:"/admin/role/group/info/"+data.id
    })
}
//删除
export const deleteByGroupId = data =>{
    return axios({
      method:"get",
      url:"/admin/role/group/del/"+data.id
    })
}

//保存角色
export const getSaveRoles = data => {
    return axios({
      method: "post",
      url: "/admin/role/add",
      data: qs.stringify(data)
    });
};
//角色详情
export const saveRoleByGroupIds = data =>{
    return axios({
      method:"get",
      url:"/admin/role/info/"+data.id
    })
}
//删除角色
export const deleteByGroupIds = data =>{
    return axios({
      method:"get",
      url:"/admin/role/del/"+data.id
    })
}
//菜单树
export const getMenuTreeData = data =>{
    return axios({
      method:"get",
      url:"/admin/menu/tree/"+data.systemCode
    })
}
//菜单树id
export const getMenuTreeDataId = data =>{
    return axios({
      method:"get",
      url:"/admin/role/roleMenu?systemCode="+data.systemCode+"&roleId="+data.id
    })
}
//保存菜单树id
export const getSaveRoleMenu = data =>{
    return axios({
      method:"post",
      url:"/admin/role/add/roleMenu",
      data: qs.stringify(data)
    })
}

//权限树
export const getMenuTreeDataed = data =>{
    return axios({
      method:"get",
      url:"/admin/priv/tree/"+data.systemCode
    })
}
//权限树id
export const getMenuTreeDataIded = data =>{
    return axios({
      method:"get",
      url:"/admin/role/rolePriv?systemCode="+data.systemCode+"&roleId="+data.id
    })
}
//保存权限树id
export const getSaveRoleMenued = data =>{
    return axios({
      method:"post",
      url:"/admin/role/add/rolePriv",
      data: qs.stringify(data)
    })
}

