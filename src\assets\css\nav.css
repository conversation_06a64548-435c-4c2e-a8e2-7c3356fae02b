html, body {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
    font-family: "Microsoft Yahei";
}

h1, h2, h3, h4, h5, h6 {
    margin: 0;
}

ul, li, ol, dl, dt, dd, p {
    margin: 0 !important;
    padding: 0;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

a {
    color: inherit;
    text-decoration: none;
}

a:hover {
    outline: none;
}

/*.scroll-min {*/
    /*overflow-y: hidden !important;*/
/*}*/

.scroll-min::-webkit-scrollbar, .scroll-min-webkit::-webkit-scrollbar {
    width: 6px;
    /*background:rgba(2, 232, 253,.7);
    border-radius:10px;*/
}

.scroll-min::-webkit-scrollbar-track, .scroll-min-webkit::-webkit-scrollbar-track {
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .5);
    -webkit-border-radius: 3px;
    border-radius: 3px;
}

.scroll-min::-webkit-scrollbar-thumb, .scroll-min-webkit::-webkit-scrollbar-thumb {
    -webkit-border-radius: 3px;
    roder-radius: 3px;
    background: rgba(15, 133, 232, .5);
    -webkit-box-shadow: inset 0 0 3px rgba(15, 133, 232, .5);
}

.scroll-min::-webkit-scrollbar-thumb:window-inactive, .scroll-min-webkit::-webkit-scrollbar-thumb:window-inactive {
    background: rgba(15, 133, 232, .5)
}

.qh-bg {
    background-color: #edf1f5;
}

.qh-main-container /*.qh-wrap去掉了。*/
{
    margin-left: 65px;
    height: calc(100vh - 85px);
}

.qh-icon-left {
    vertical-align: -1px;
    margin-right: 10px;
}

.qh-padding-r {
    padding-right: 10px;
}

.qh-text-right {
    text-align: right;
}

/*侧边导航-start*/
.qh-sideNav {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 9;
    width: 224px;
    max-width: 224px;
    background-color: #fff;
    -webkit-box-shadow: 6px 0px 16px rgba(84, 102, 123, .21);
    -moz-box-shadow: 6px 0px 16px rgba(84, 102, 123, .21);
    box-shadow: 6px 0px 16px rgba(84, 102, 123, .21);
    transition: all .3s;
}

.qh-sideNav-logo {
    width: 36px;
    height: 66px;
    display: block;
    background: url("/static/img/assets/img/logo.png") no-repeat center 15px;
    padding-top: 15px;
    padding-bottom: 11px;
    margin-left: auto;
    margin-right: auto;
}
.qh-sideNav-logo.hb {
    width: 36px;
    height: 66px;
    display: block;
    background: url("/static/img/assets/img/hb-logo.png") no-repeat center 15px;
    padding-top: 15px;
    padding-bottom: 11px;
    margin-left: auto;
    margin-right: auto;
}

.qh-sideNav-logo.djd {
    width: 36px;
    height: 66px;
    display: block;
    background: url("/static/img/assets/img/mis-djd-logo.png") no-repeat center 15px;
    padding-top: 15px;
    padding-bottom: 11px;
    margin-left: auto;
    margin-right: auto;
}
.qh-sideNav-ul {
    margin: 0;
    padding-left: 6px;
    list-style: none;
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100vh - 66px);
    padding-bottom: 20px;

}

.qh-sideNav-item {
    margin-left: -6px;
    line-height: 1;
    color: #54667b;
    -webkit-transition: background .3s;
    -moz-transition: background .3s;
    -ms-transition: background .3s;
    -o-transition: background .3s;
    transition: background .3s;
    cursor: pointer;
    white-space: nowrap;
}

.qh-sideNav-item.cur > a,
.qh-sideNav-item > a:hover {
    background-color: #2c93ea;
    color: #ffffff;
}

.qh-sideNav-item.cur .qh-sideNav-item_right_arrow {
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    transform: rotate(90deg);
}

.qh-sideNav-item > a {
    position: relative;
    text-align: center;
    padding: 24px 20px;
    display: block;
    vertical-align: middle;
    text-align: left;
}

.qh-sideNav-item > a:after {
    content: '';
    height: 100%;
    vertical-align: middle;
    width: 0;
    display: inline-block;
}

.qh-sideNav-item_icon {
    display: inline-block;
    vertical-align: middle;
    font-size: 24px;
    margin-right: 10px;
    transition: transform .3s;
}

.qh-sideNav-item_text {
    display: inline-block;
    vertical-align: middle;
    white-space: nowrap;
    max-width: 130px;
    text-overflow: ellipsis;
    overflow: hidden;
}

.qh-sideNav-sun .qh-sideNav-item_text {
    font-size: 14px;
}

.qh-sideNav-sun li.cur a {
    color: #2c93ea;
}

.qh-sideNav-item_right {
    display: block;
    position: absolute;
    right: 10px;
    top: 0;
    vertical-align: middle;
    padding-top: 24px;
    padding-bottom: 24px;
}

.qh-sideNav-item_right_arrow {
    -webkit-transition: transform .3s;
    -moz-transition: transform .3s;
    -ms-transition: transform .3s;
    -o-transition: transform .3s;
    transition: transform .3s;
    display: inline-block;
    width: 23px;
    height: 23px;
    vertical-align: middle;
    text-align: center;
    font-size: 20px;
}

.qh-sideNav-item_num {
    display: inline-block;
    height: 23px;
    min-width: 20px;
    max-width: 50px;
    width: 30px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 16px;
    line-height: 23px;
    vertical-align: middle;
    background-color: #ff9800;
    text-align: center;
    border-radius: 5px;
    color: #fff;

}

.side-min .qh-sideNav {
    width: 65px;
}

.qh-sideNav-sun {
    display: none;
    list-style: none;
    padding-left: 0;
    padding: 5px 0;
}

.qh-sideNav-sun a {
    display: block;
    vertical-align: middle;
    padding: 15px 10px 15px 34px;
}

.qh-sideNav-sun a:hover {
    color: #2c93ea;
}

.qh-sideNav-sun .qh-sideNav-item_icon {
    font-size: 20px;
}

.side-min .qh-sideNav-item_icon {
    -webkit-transform: scale(.8);
    -moz-transform: scale(.8);
    -ms-transform: scale(.8);
    -o-transform: scale(.8);
    transform: scale(.8);
}

.side-min .qh-sideNav-item_text,
.side-min .qh-sideNav-item_right,
.side-min .qh-sideNav-sun {
    display: none !important;
}

.side-min .qh-sideNav-item > a:after {
    position: absolute;
}

/*侧边导航-end*/

/*banner部分-start*/
.qh-head {
    background-color: #1086e8;
    min-height: 70px;
    height: 70px;
    /* padding-left: 65px; */
}
@media screen and (max-width: 768px){
    .qh-logo-link {
        display: none;
        float: left;
    }
}
@media screen and (min-width:768px) and (max-width: 1024px){
    .qh-logo-link {
        display: none;
        float: left;
    }
}
@media screen and (min-width:1024px) and (max-width: 1200px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        width: 12% !important;
        float: left;
    }
}
@media screen and (min-width:1200px) and (max-width:1366px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        width: 28%;
        float: left;
    }
}
@media screen and (min-width:1367px) and (max-width:1480px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        width: 35%;
        float: left;
    }
}
@media screen and (min-width:1480px) and (max-width:1600px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        width: 40%;
        float: left;
    }
}
@media screen and (min-width:1600px) and (max-width:1720px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        width: 40%;
        float: left;
    }
}
@media screen and (min-width:1720px) and (max-width:1920px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        /*width: 30%;*/
        float: left;
    }
}
@media screen and (min-width:1920px){
    .qh-logo-link {
        display: inline-block;
        color: #fff;
        /*width: 25%;*/
        float: left;
    }
}
/*.qh-logo-link {*/
/*    display: inline-block;*/
/*    color: #fff;*/
/*    width: 25%;*/
/*}*/

.qh-logo-text {
    padding: 24px 0 24px 15px;
    font: 20px/1 FZHTJW;
    font-family: "Microsoft Yahei";
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
}

.qh-head-tool {
    position: relative;
    float: right;
    min-width: 350px;
    /*max-width: 400px;*/
    font-size: 0;
    margin: 0;
    list-style: none;
    vertical-align: middle;
    display: flex;
    overflow: hidden;
    text-overflow:ellipsis;
    white-space: nowrap;
    float: left;
}

.qh-head-tool_item-user,
.qh-head-tool_item {
    font-size: 15px;
    display: inline-block;
    vertical-align: middle;
    cursor: pointer;
}

.qh-head-tool_item-user > a {
    padding: 17px 8px 14px 10px;
    display: block;
    color: #ffffff;
}

.qh-head-tool_item > a {
    display: block;
    color: #ffffff;
    padding: 25px 25px;
}

.qh-head-tool > li.cur > a,
.qh-head-tool > li:hover > a {
    background-color: #0d7ddc;
}

.qh-head-tool_item_color.cur > a,
.qh-head-tool_item_color:hover > a {
    color: yellow;
}


.qh-head-tool_user {
    padding-left: 10px;
    display: inline-block;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

.qh-head-tool_item-user:hover .qh-icon-trigon {
    -webkit-transform: rotate(-180deg);
    -moz-transform: rotate(-180deg);
    -ms-transform: rotate(-180deg);
    -o-transform: rotate(-180deg);
    transform: rotate(-180deg);

}

.qh-icon-trigon {
    position: relative;
    display: inline-block;
    font-size: 0;
    width: 16px;
    height: 16px;
    outline: 10px solid transparent;
    margin-left: 8px;
    transition: .3s transform ease;
    vertical-align: top;
    vertical-align: middle;
}

.qh-icon-trigon:before {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    position: absolute;
    display: block;
    content: '';
    border-top: 6px solid #fff;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    top: 50%;
    left: 50%;
    margin-left: -6px;
    margin-top: -3px;
}

.qh-user-default {
    display: inline-block;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: #edf1f5;
    vertical-align: middle;
}

.qh-icon-user-default {
    background: url("/static/img/assets/img/user-default.png") top center no-repeat;
    background-size: 100% 100%;
}

.qh-head-tool_hot {
    position: relative;
}


.qh-head-tool_hot:after {
    position: absolute;
    display:block;
    content: '';
    right: -23px;
    /* top:50%; */
    /* margin-top: -25%; */
    width: 8px;
    height: 8px;
    border-radius: 50%;
    border: 1px solid transparent;
    /*-webkit-box-shadow: 0px 0px 0px 2px red,inset 0 0 0px 12px red;*/
    /*-moz-box-shadow: 0px 0px 0px 2px red,inset 0 0 0px 12px red;*/
    /*box-shadow: 0px 0px 0px 2px red,inset 0 0 0px 12px red;*/
    animation: animation-tip 1.5s ease-out 1.5s infinite;
}

@keyframes animation-tip {
    0% {
        box-shadow: 0 0 0 0 red,inset 0 0 0 8px red;
    }
    25% {
        box-shadow: 0 0 0 2px red,inset 0 0 0 9px red;
    }
    50% {
        box-shadow: 0 0 0 4px red,inset 0 0 0 10px red;
    }
    75% {
        box-shadow: 0 0 0 2px red,inset 0 0 0 9px red;
    }
    100% {
        box-shadow: 0 0 0 0 red,inset 0 0 0 8px red;
    }
}

@-webkit-keyframes animation-tip {
    0% {
        -webkit-box-shadow: 0 0 0 0 red, inset 0 0 0 8px red;
    }
    25% {
        -webkit-box-shadow: 0 0 0 2px red, inset 0 0 0 9px red;
    }
    50% {
        -webkit-box-shadow: 0 0 0 4px red, inset 0 0 0 10px red;

    }
    75% {
        -webkit-box-shadow: 0 0 0 2px red, inset 0 0 0 9px red;

    }
    100% {
        -webkit-box-shadow: 0 0 0 0 red, inset 0 0 0 8px red;

    }
}

@-moz-keyframes animation-tip {
    0% {
        -moz-box-shadow: 0 0 0 0 red, inset 0 0 0 8px red;
    }
    25% {
        -moz-box-shadow: 0 0 0 2px red, inset 0 0 0 9px red;
    }
    50% {
        -moz-box-shadow: 0 0 0 4px red, inset 0 0 0 10px red;

    }
    75% {
        -webkit-box-shadow: 0 0 0 2px red, inset 0 0 0 9px red;

    }
    100% {
        -webkit-box-shadow: 0 0 0 0 red, inset 0 0 0 8px red;

    }
}

.qh-head-user {
    width: 340px;
    color: #393939;
}

/*
.qh-head-user>a{
    display: block;
    text-align: center;
    font-size: 14px;
    padding: 10px;
    border-top: 1px solid #e8e8e8;
    margin-top:-1px;
}
.qh-head-user>a:hover{
    color: #1086e8;
}

.qh-head-user:before{
    content: '';
    display: inline-block;
    position: absolute;
    top:0;
    left:100%;
    margin-top:-6px;
    margin-left: -17px;
    width: 10px;
    height:10px;
    background-color: #fff;
    -webkit-transform: rotate(45deg);
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-box-shadow:  0px -1px 1px 0px rgba(0,0,0,.1);
    -moz-box-shadow:  0px -1px 1px 0px rgba(0,0,0,.1);
    box-shadow:  0px -1px 1px 0px rgba(0,0,0,.1);
}*/
/*banner部分-end*/

/*分割快-start*/
.qh-wrap--default {
    background-color: #ffffff;
    padding: 12px 18px;;
}

.qh-wrap--inner {
    position: relative;
    padding-top: 62px;
    margin-left: 15px;
    margin-right: 15px;
    margin-top: 15px;
    /* -webkit-box-shadow:  -1px 0px 1px #e6eaee,1px 0px 1px #e6eaee,0 1px 1px #d9dde1,0 1px 1px 2px #e6eaee;
     -moz-box-shadow:  -1px 0px 1px #e6eaee,1px 0px 1px #e6eaee,0 1px 1px #d9dde1,0 1px 1px 2px #e6eaee;
     box-shadow:  -1px 0px 1px #e6eaee,1px 0px 1px #e6eaee,0 1px 1px #d9dde1,0 1px 1px 2px #e6eaee;*/
    background-color: #fff;
    overflow-y: hidden;
    border-radius: 5px;
}

.qh-inner-hd {
    margin-top: -62px;
    padding: 15px 19px 15px 30px;
    vertical-align: middle;
    vertical-align: middle;
    border-bottom: 1px solid #f0f0f0;
}

.qh-inner-hd_title {
    display: inline-block;
    margin-top: 10px;
    font-size: 18px;
    color: #000;
    vertical-align: middle;
}

.qh-inner-hd_opt {
    float: right;
    display: inline-block;
    vertical-align: middle;
}

.qh-inner-hd:after {
    display: table;
    content: '';
    clear: both;
}

.qh-inner-bd {
    position: absolute;
    bottom: 0;
    top: 0;
    left: 0px;
    right: 0px;
    /*overflow-y: auto;*/
    padding-left: 6px;
}

.qh-inner-bd_cnt {
    margin-left: -6px;
    margin-right: 6px;
}

.qh-content {
    padding: 16px;
}

/*分割快-end*/

/*详情列表start*/
.qh-info-dl {
    padding: 10px 10px 10px 15px
}

.qh-info-dt {
    font-size: 16px;
    line-height: 1;
    color: #656565;
    padding-top: 10px;
    padding-bottom: 10px;
    padding-left: 10px;
}

.qh-info-dt > h2 {
    font-size: 16px;
    line-height: 1;
    color: inherit;
    font-weight: 600;
}

.qh-info-dt > h3 {
    font-size: 14px;
    line-height: 1;
    color: inherit;
    font-weight: 600;
}

.qh-info-dd {
    padding-top: 10px;
}

.qh-wrap-ft {
    padding-top: 15px;
    padding-bottom: 15px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    text-align: center;
    border-top: 1px solid #dfe0e0;
    z-index: 100;
}
.qh-wrap-ft1 {
    padding-top: 15px;
    padding-bottom: 15px;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    text-align: center;
    border-top: 1px solid #dfe0e0;
}
.qh-wrap-ftqh {
    padding-top: 15px;
    padding-bottom: 15px;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    text-align: center;
    border-top: 1px solid #dfe0e0;
    position: fixed;
}

/*详情列表end*/

/*详情锚点 start*/
.Catalog_btn {
    position: absolute;
    right: 15px;
    bottom: 56px;
}

.Catalog_btn a {
    margin-top: 5px;
    display: block;
    padding: 0px 5px;
    font-size: 32px;
    color: #cbc9ca;
    border: 1px solid #bbbbbb;
    border-radius: 5px;
    background-color: #fff;

}

.Catalog_btn a.cur,
.Catalog_btn a:hover {
    border-color: #1085e5;
    color: #1085e5;
}

/*.page_right{
    position: relative;
}*/
#Catalog_box {
    position: fixed;
    bottom: 180px;
    margin-left: 50px;
    right: 26px;
    background-color: rgba(187, 187, 187, .3);
    border-radius: 5px;
}

#Catalog_box dl {
    padding: 20px;
}

#Catalog_box dd:last-of-type:before,
#Catalog_box dd:first-of-type:before {
    height: 50%;
}

#Catalog_box dd:first-of-type:before {
    top: 50%;
}

#Catalog_box dd:last-of-type:before {
    bottom: 50%;
}

#Catalog_box dd:before {
    z-index: -1;
    left: 0;
    top: 0;
    bottom: 0;
    display: block;
    content: '';
    position: absolute;
    border-left: 5px solid #ffffff;
}

.cate-item1 {
    position: relative;
    padding: 10px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    color: #989898

}

#Catalog_box .cate-item1 span {
    display: inline-block;
    width: 15px;
    height: 16px;
    margin-left: -15px;
    background-color: #fff;
    border-radius: 50%;
    margin-right: 20px;

}

#Catalog_box dd.active span {
    background-color: #1086e8;
}

#Catalog_box .cate-item2 {
    padding-left: 20px;
}

#Catalog_box .cate-item2 span {
    font-weight: 500;
}

#Catalog_box dd.active, #Catalog_box dd.active a {
    color: #1086e8 !important;
}

/*详情锚点 */
/*tab样式 start*/
.qh-tab-nav {
    display: inline-block;
    color: #565656;
    padding: 13px 15px;
    margin-bottom: -12px;
    border-bottom: 6px solid transparent;
}

.qh-tab-nav:focus,
.qh-tab-nav:hover,
.qh-tab-nav.cur {
    border-bottom-color: #1086e8
}

.qh-tab-nav em {
    font-style: normal;
    font-size: 12px;
    color: red;
}

.qh-tab-opt {
    color: #565656;
    text-align: right;
    margin-bottom: -12px;
}

.qh-tab-opt_text {
    display: inline-block;
    padding-right: 15px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.qh-tab-opt_text_state {
    color: #8b8b8b;
    font-style: normal;
}

.qh-tab-cnt {
    display: none;
}

.qh-tab-cnt.cur {
    display: block;
}

/*tab样式 end*/

/*搜索输入框 start*/
.qh-input {
    position: relative;
}

.qh-input_icon {
    position: absolute;
    left: 0;
    top: 0;
    width: 35px;
    height: 100%;
    text-align: center;
    color: #1488e8;
    transition: all .3s;
    cursor: pointer;
}

.qh-input_icon:after {
    content: '';
    height: 100%;
    width: 0;
    display: inline-block;
    vertical-align: middle;
}

.qh-input_icon i {
    display: inline-block;
    vertical-align: middle;
    font-size: 20px;
}

.qh-input_icon + .gs-el-input__inner {

    padding-left: 35px;
}

.qh-search_input__radius {
    border-radius: 8px;
    font-size: 14px;
}

.qh-search_input__radius input {
    border-radius: 8px;
}

/*搜索输入框 end*/
.qh-textarea_text {
    position: absolute;
    bottom: 3px;
    right: 8px;
    display: inline-block;
    font-size: 12px;
    color: #ddd;
    cursor: default;
    -webkit-transform: scale(.9);
    -moz-transform: scale(.9);
    -ms-transform: scale(.9);
    -o-transform: scale(.9);
    transform: scale(.9);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.qh-textarea_wrap {
    position: relative;
}

.gjyi-textarea_text {
    position: absolute;
    bottom: 3px;
    right: 8px;
    display: inline-block;
    font-size: 12px;
    color: #ddd;
    cursor: default;
    -webkit-transform: scale(.9);
    -moz-transform: scale(.9);
    -ms-transform: scale(.9);
    -o-transform: scale(.9);
    transform: scale(.9);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.gjyi-textarea_wrap {
    position: relative;
}

.qh-list-a {
    color: #2892e2;
    text-decoration: underline;
    cursor: pointer;
}

/*  显示屏宽度在1366px --1600px 样式 */
@media screen and (min-width: 1366px) and (max-width: 1599px) {
    .qh-gis-panel-air img {
        height: 100%;
        width: 100%;
    }

    .qh-gis-panel-duty_ul > .qh-gis-panel-duty-dot {
        padding: 5px 10px 5px 15px;
    }

    .gs-el-col-14.qh-padding-r {
        width: 35% !important;
    }
}
.mis_btn_css{
    display: inline-block;
    font-size: 12px;
    box-sizing: border-box;
    padding-left: 5px;
    padding-right: 5px;
    width: auto;
    min-width: 90px;
    height: 27px;
    text-align: center;
    background-color: #1086e8;
    color: #fff;
    cursor: pointer;
    box-shadow: 0px 0px 5px rgba(0,0,0,.1);
    line-height: 2.5;
    border-radius: 3px;
    top: 20px;
    right: 20px;
    position: absolute;
}
