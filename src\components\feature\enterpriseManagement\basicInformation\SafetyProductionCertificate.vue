<template>
  <div class="safetyProductionCertificate">
    <div class="header">
      <div style="display: flex; justify-content: flex-start">
        <div class="title">安全生产证照列表</div>
        <div class="legend">
          <div class="red">
            <div class="point"></div>
            逾期
          </div>
          <div class="yellow">
            <div class="point"></div>
            即将逾期（三个月）
          </div>
        </div>
      </div>
      <div>
          <el-input
            style="width:260px"
            v-model.trim="certCode"
            placeholder="请输入查询证书编号"
            class="input"
            size="mini"
            clearable
            @clear="clearDangerName(event)"
            @keyup.enter.native="search"
          ></el-input>
      <el-button
        icon="el-icon-plus"
        size="mini"
        type="success"
        @click="openDialog('新增安全生产许可证')"
        v-if="user.user_type === 'ent'"
        >新增
      </el-button>
      </div>
       
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="list"
        :header-cell-style="{
          textAlign: 'center',
          backgroundColor: 'rgb(242, 246, 255)',
        }"
        border
        style="width: 100%"
        height="50vh"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column
          label="更新时间"
          prop="updateTime"
          width="170px"
        ></el-table-column>
        <el-table-column label="证书编号" prop="certCode"></el-table-column>
        <el-table-column label="证照类型" prop="typeName"></el-table-column>
        <el-table-column label="发证机关" prop="issueOrg"></el-table-column>
        <el-table-column
          label="证书生效时间"
          prop="startTime"
          width="120px"
        ></el-table-column>
        <el-table-column
          label="证书失效时间"
          prop="endTime"
          width="120px"
        ></el-table-column>
        <el-table-column
          label="状态"
          prop="certStatusName"
          width="110px"
        >
        <template slot-scope="scope" >           
            <span slot="reference" v-if="scope.row.certStatus == 2">
              <i class="dotClass" style="background-color: red"></i>
            </span>
            <span slot="reference" v-if="scope.row.certStatus ==3" >
              <i class="dotClass" style="background-color: yellow"></i>
            </span>
             {{scope.row.certStatusName}}
          </template>        
        </el-table-column>
        <el-table-column
          label="倒计时天数"
          prop="expDays"
          width="110px"
        ></el-table-column>
        <el-table-column label="操作" width="200px">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              @click="openDialog('安全生产许可证详情', row.id)"
              >详情
            </el-button>
            <el-button
              type="text"
              v-if="user.user_type === 'ent'"
              @click="openDialog('编辑安全生产许可证', row.id)"
              >编辑
            </el-button>
            <el-button
              type="text"
              v-if="user.user_type === 'ent'"
              @click="deleteFun(row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size="size"
        :total="total"
        background
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="newAddVisible"
      append-to-body
      top="10vh"
      width="1000px"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <div class="newAddVisible" v-loading="dialogLoading">
        <div class="box">
          <el-form
            label-position="right"
            label-width="120px"
            :model="form"
            ref="form"
          >
            <el-form-item label="证书附件：" prop="attachmentList">
              <div>
                <AttachmentUpload
                  :attachmentlist="form.attachmentList"
                  :limit="1"
                  type="img"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请选择证书类型', trigger: 'blur' },
              ]"
              label="证书类型："
              prop="type"
            >
              <el-select
                placeholder="请选择证书类型"
                :disabled="disabled"
                style="width: 100%"
                v-model="form.type"
                filterable
                :loading="iconLoading"
              >
                <el-option
                  v-for="item in options"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入证书编号', trigger: 'blur' },
              ]"
              label="证书编号："
              prop="certCode"
            >
              <el-input
                placeholder="请输入证书编号"
                :disabled="disabled"
                :title='form.certCode'
                type="text"
                maxlength="50"
                v-model.trim="form.certCode"
              ></el-input>
            </el-form-item>
            <el-form-item label="企业名称：" prop="orgName">
              <el-input
                disabled
                type="text"
                placeholder="请输入企业名称"
                v-model.trim="form.orgName"
              ></el-input>
            </el-form-item>
            <el-form-item label="主要负责人：" prop="maillistPersonId">
              <el-input
                disabled
                type="text"
                placeholder="请输入主要负责人"
                v-model.trim="form.maillistPersonId"
              ></el-input>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入企业地址', trigger: 'blur' },
              ]"
              label="企业地址："
              prop="addressWorksite"
            >
              <el-input
                placeholder="请输入企业地址"
                :disabled="disabled"
                maxlength="100"
                :title='form.addressWorksite'
                type="text"
                v-model.trim="form.addressWorksite"
              ></el-input>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入经济类型', trigger: 'blur' },
              ]"
              label="经济类型："
              prop="economicType"
            >
              <!-- <el-input
                type="text"
                :disabled="disabled"
                maxlength="40"
                placeholder="请输入经济类型"
                v-model="form.economicType"
              ></el-input> -->
              <!-- <el-select
                placeholder="请输入经济类型"
                :disabled="disabled"
                style="width: 100%"
                v-model="form.economicType"
                filterable
                :loading="iconLoading"
              >
                <el-option
                  v-for="item in economicTypeData"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select> -->
              <el-cascader placeholder="请输入经济类型"
                      style="width:300px"
                          :props='{value:"id"}'
                          v-model="form.economicType"
                          :disabled="disabled"
                          size="medium"
                          :options="economicTypeData"
                          @change="handleChangeEventTypeCode">
               </el-cascader>
            </el-form-item>
            <el-form-item
              :rules="[
                {
                  required: true,
                  message: '请选择证书生效时间',
                  trigger: 'blur',
                },
              ]"
              label="证件生效时间："
              prop="startTime"
            >
              <el-date-picker
                placeholder="请选择证件生效时间"
                :disabled="disabled"
                style="width: 100%"
                type="date"
                v-model="form.startTime"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              :rules="[
                {
                  required: true,
                  message: '请选择证件失效时间',
                  trigger: 'blur',
                },
              ]"
              label="证件失效时间："
              prop="endTime"
            >
              <el-date-picker
                placeholder="请选择证件失效时间"
                style="width: 100%"
                type="date"
                :disabled="disabled"
                v-model="form.endTime"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入发证机关', trigger: 'blur' },
              ]"
              label="发证机关："
              prop="issueOrg"
            >
              <el-input
                type="text"
                :disabled="disabled"
                maxlength="30"
                placeholder="请输入发证机关"
                v-model.trim="form.issueOrg"
              ></el-input>
            </el-form-item>
            <el-form-item
              :rules="[
                { required: true, message: '请输入许可范围', trigger: 'blur' },
              ]"
              label="许可范围："
              prop="permitScope"
            >
              <el-input
                :rows="2"
                type="textarea"
                maxlength="300"
                :disabled="disabled"
                placeholder="请输入许可范围"
                v-model.trim="form.permitScope"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div
        slot="footer"
        style="display: flex; justify-content: center"
        v-if="!disabled"
      >
        <el-button type="primary" @click="submit(form.id)" :loading="btnLoading"
          >确定</el-button
        >
        <el-button @click="closeDialog()">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
import {
  getCompanyCertType,
  getCompanyCertSave,
  getCompanyCertList,
  getCompanyCertFindOne,
  getCompanyCertUpdate,
  getCompanyCertDelete,
  getEconomicType
} from "../../../../api/BasicDataManagement";

export default {
  name: "safetyProductionCertificate",
  components: {
    AttachmentUpload
  },
  props: ["orgCode", "orgName"],
  data() {
    return {
      certCode:'',
      list: [],
      currentPage: 1,
      size: 10,
      total: 0,
      tableData: {},
      newAddVisible: false,
      disabled: true,
      iconLoading: false,
      title: "",
      options: [],
      economicTypeData:[],
      value: "",
      loading: false,
      btnLoading: false,
      dialogLoading: false,
      form: {
        address: "", //地址/住所
        addressWorksite: "", //生产地址
        attachmentList: [],
        businessType: "", //业务类型
        certCode: "", //证件编号
        certName: "", //证件名称
        companyType: "", //单位类型
        economicType: "", //经济类型/经济方式
        endTime: "", //证件有效期(结束)
        id: "", //主键
        issueDate: "", //发证日期
        issueOrg: "", //发证机构
        maillistPersonId: "", //负责人/法定代表人
        orgCode: "", //企业机构
        permitScope: "", //许可范围
        standardLevel: "", //安全生产标准化等级
        startTime: "", //证件有效期(开始)
        type: "", //证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-工业产品生产许可证 5-安全生产标准化证书 6-非药品生产证件 7-非药品经营证件 8-易制爆备案证明)
      },
    };
  },
  created() {},
  mounted() {
  },

  methods: {
    search(){
     this.getData()
    },
    clearDangerName(e) {
      this.certCode = "";
      this.getData()
    },
    getData(id) {
      this.loading = true;
      getCompanyCertList({
        certCode: this.certCode,
        certName: "",
        issueOrg: "",
        nowPage: this.currentPage,
        orgCode: this.orgCode, //this.$store.state.login.enterData.enterpId  this.orgCode
        pageSize: 10,
        types: ["1", "2", "3"],
        //   证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-安全生产标准化证书 )
      }).then((res) => {
        this.loading = false;
        this.list = res.data.data.list;
        this.total=res.data.data.total
      });
    },
      handleChangeEventTypeCode(value) {
        console.log(value);
        if (value.length > 0) {
          this.form.economicType = value
        } else {
          this.form.economicType = ''
        }
    },
    submit(id) {      
      this.$refs["form"].validate((valid) => {
        if(valid){     
            this.form.issueDate = this.form.startTime;
            this.form.address = this.form.addressWorksite;
            this.btnLoading = true;    
          if (id) {           
            getCompanyCertUpdate({
              ...this.form,
               economicType:this.form.economicType.join(',')

            }).then((res) => {
              this.btnLoading = false;
              if (res.data.status === 200) {
                this.$message.success(res.data.msg);
                this.getData();
                this.closeDialog();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {            
            getCompanyCertSave({
              ...this.form,
              economicType:this.form.economicType.join(',')
            }).then((res) => {
              this.btnLoading = false;
              if (res.data.status === 200) {
                this.$message.success(res.data.msg);
                this.getData();
                this.closeDialog();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        }else{
          return false
        }

      })      
    },
    getOption() {
      this.iconLoading = true;
      getCompanyCertType().then((res) => {
        this.options = res.data.data;
        this.iconLoading = false;
        // console.log(res.data);
      });
      getEconomicType().then((res) => {
        this.economicTypeData = res.data.data;
        this.iconLoading = false;
        console.log(res.data,'经济类型的数据下拉');
      });
    },
   
    handleCurrentChange() {
      this.getData();
    },
    deleteFun(id) {
      


      this.$confirm("确认删除该数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        getCompanyCertDelete({ id: id }).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg);
            if (this.list.length === 1 && this.currentPage !== 1) {
              this.currentPage--;
            }
            this.getData();
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });



    },
    closeDialog() {
      this.newAddVisible = false;
      this.form = {
        address: "", //地址/住所
        addressWorksite: "", //生产地址
        attachmentList: [],
        businessType: "", //业务类型
        certCode: "", //证件编号
        certName: "", //证件名称
        companyType: "", //单位类型
        economicType: "", //经济类型/经济方式
        endTime: "", //证件有效期(结束)
        id: "", //主键
        issueDate: "", //发证日期
        issueOrg: "", //发证机构
        maillistPersonId: "", //负责人/法定代表人
        orgCode: "", //企业机构
        permitScope: "", //许可范围
        standardLevel: "", //安全生产标准化等级
        startTime: "", //证件有效期(开始)
        type: "", //证照类型(1-安全生产许可证 2-安全使用许可证 3-经营许可证 4-工业产品生产许可证 5-安全生产标准化证书 6-非药品生产证件 7-非药品经营证件 8-易制爆备案证明)
      };
      this.$refs.form.resetFields();
    },
    openDialog(title, id) {
      this.title = title;
      switch (title) {
        case "新增安全生产许可证":
          this.disabled = false;
          this.getOption();
          this.form.maillistPersonId = this.enterData.respper;
          this.form.orgName = this.user.org_name;
          this.form.orgCode = this.orgCode;
          break;
        case "安全生产许可证详情":
          this.disabled = true;
          this.getOption();
          this.getCompanyCertFindOneFun(id);
          break;
        case "编辑安全生产许可证":
          this.disabled = false;
          this.getOption();
          this.getCompanyCertFindOneFun(id);
          break;
        default:
          break;
      }
      this.newAddVisible = true;
    },
    getCompanyCertFindOneFun(id) {
      this.dialogLoading = true;
      getCompanyCertFindOne({ id: id }).then((res) => {
        this.dialogLoading = false;
        this.form = res.data.data;
        console.log(this.economicTypeData,'aaaaa');
        let parentId = '';
        this.economicTypeData.forEach((el)=>{
          el.children.forEach((ele)=>{
            if(ele.id ==res.data.data.economicType ){
              parentId = ele.parentId;
            }
          })
        });

        // this.$set(this.form,'economicType',res.data.data.economicType.split(','));
        this.$set(this.form,'economicType',[parentId,res.data.data.economicType]);
        // this.form.orgCode = this.orgCode;
        // this.form.orgName = this.user.org_name;
        this.form.type=(res.data.data.type).toString();
        console.log('我是详情',this.form)
        if (this.form.attachmentList === null) {
          this.form.attachmentList = [];
        }
      });
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {},
};
</script>

<style lang="scss" scoped>
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
/deep/ .el-image__preview {
  cursor: -moz-zoom-in;
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}

.dotClass {
  width:10px;
  height:10px;
  border-radius: 50%;
  display: inline-block;
  margin-left: 10px;  //这个用于圆点居中
}
.safetyProductionCertificate {
  height: 60vh;
  overflow:auto;
  .header {
    display: flex;
    justify-content: space-between;

    .title {
      font-weight: 600;
      font-size: 18px;
      margin-right: 50px;
    }

    .legend {
      display: flex;
      align-items: center;
      margin-right: 5px;

      .red {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .point {
          background: red;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }

      .yellow {
        display: flex;
        align-items: center;

        .point {
          background: yellow;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }
    }
  }

  .table {
    margin-top: 15px;
  }

  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}

.newAddVisible {
  width: 100%;
  overflow-y: scroll;
  height: 60vh;

  .box {
    width: 70%;
    margin: 0 auto;
  }
}
</style>
