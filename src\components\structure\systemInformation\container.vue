<template>
  <div class="container">
    <div class="header">
      <div class="title">菜单信息</div>
      <el-button type="primary" class="newMenu" @click="newMenu()" icon="plus"
        ><span>添加系统</span></el-button
      >
    </div>
    <div class="body" v-loading="loading">
      <el-table
        :data="tableData.records"
        style="width: 100%; color: rgb(101, 101, 101)"
      >
        <el-table-column prop="orderNum" label="序号" width="80">
        </el-table-column>
        <el-table-column prop="systemName" label="系统名称" width="180">
        </el-table-column>
        <el-table-column prop="systemCode" label="系统编码" width="180">
        </el-table-column>
        <el-table-column prop="systemUrl" label="系统访问地址">
        </el-table-column>
        <el-table-column prop="iconUrl" label="系统菜单图片"> </el-table-column>
        <el-table-column align="right" label="操作">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.$index, scope.row)"
              ><i class="el-icon-edit"></i
            ></el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              ><i class="el-icon-delete"></i
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        @prev-click="handleCurrentChange"
        @next-click="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="tableData.size"
        layout="prev, pager, next, jumper"
        :total="tableData.total"
      >
      </el-pagination>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
    ></DialogTable>
  </div>
</template>

<script>
import { getSystemList, deletValSystemCanDelete } from "../../../api/user";
import DialogTable from "./table";
export default {
  //import引入的组件
  components: {
    DialogTable,
  },
  data() {
    return {
      dialogTableVisible: true,
      currentPage: 1,
      tableData: {},
      loading: false,
    };
  },
  //方法集合
  methods: {
    //打开编辑弹框
    handleEdit(index, row) {
      this.dialogTableVisible = true;
      this.$refs.child.parentMsg(this.dialogTableVisible);
      //数据给到编辑表单，关闭系统编码填报
      this.$refs.child.getData(row, true);
    },
    handleDelete(index, row) {
      // console.log(row.systemCode);
      this.$confirm("确定要删除选择的数据吗？", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deletValSystemCanDelete(row.id)
            .then((res) => {
              if (res.data.code == "error") {
                this.$message({
                  message: res.data.msg,
                  type: "error",
                });
              } else {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                //解决当前页只有一条数据删除时不会跳到上一页的情况
                if (this.tableData.total % this.tableData.size == 1) {
                  this.currentPage = this.currentPage - 1;
                }
                this.getMenuData(this.currentPage);
                this.$emit("pf");
              }
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    //新建菜单
    newMenu() {
      this.dialogTableVisible = true;
      this.$refs.child.parentMsg(this.dialogTableVisible);
      this.$refs.child.clearTable();
      //数据给到编辑表单,清空表单，并开放系统编码填报
      this.$refs.child.getData({}, false);
    },
    //获取菜单信息列表
    getMenuData(pageNo) {
      // console.log(val);
      this.loading = true;
      getSystemList({
        current: pageNo || 1,
        size: 8,
      })
        .then((res) => {
          // console.log(res.data.data);
          this.tableData = res.data.data;
          this.currentPage = res.data.data.current;
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    fatherMethod() {
      this.getMenuData();
    },
    handleSizeChange() {},
    handleCurrentChange(data) {
      this.getMenuData({}, data);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getMenuData();
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 15px;

  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .newMenu {
      width: 100px;
      height: 38px;
      padding: 10px;
      color: #fff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .title {
      font-weight: 900;
      font-size: 16px;
      color: #000;
    }
  }
  .pagination {
    float: right;
    margin-top: 30px;
  }
}
</style>
