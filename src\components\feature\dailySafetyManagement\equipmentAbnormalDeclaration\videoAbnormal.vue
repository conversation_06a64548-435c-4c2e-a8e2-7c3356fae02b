<template>
  <div class="equipmentFacilities">
    <div>
      <div class="seach-part">
        <div class="l">
          <el-cascader
          v-show="this.$store.state.login.user.user_type == 'gov'"
            size="mini"
            placeholder="请选择地市/区县"
            :options="district"
            v-model="distCode"
            :props="{
                checkStrictly: true,
                value: 'distCode',
                label: 'distName',
                children: 'children',
                emitPath: false,
            }"
            clearable
            @change="handleChange"
            :show-all-levels="true"
          ></el-cascader>

          <el-input
          v-show="this.$store.state.login.user.user_type == 'gov' || this.$store.state.login.user.user_type == 'park'"
            v-model.trim="enterpName"
            size="mini"
            placeholder="请输入企业名称"
            class="input"
            clearable
            @clear="clearKey"
            style="width:200px"
          ></el-input>

          <el-select
            v-model="state"
            size="mini"
            placeholder="请选择审核状态"
            clearable
            @clear="clearState()"
          >
            <el-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-date-picker
            v-model="value1"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            style="width:370px"
          ></el-date-picker>

          <el-button
            type="primary"
            size="mini"
            @click="search"
            >查询</el-button
          >
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
          <el-button
            v-if="this.$store.state.login.user.user_type == 'ent'"
            type="primary"
            size="mini"
            @click="addEdit('','1')"
            style="position: absolute;right: 0;"
            >新增</el-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>设备设施异常申报列表</h2>
        </div>
        <div>
          <div class="table">
            <el-table
                :data="tableData"
                v-loading="loading"
                :header-cell-style="{background:'#F1F6FF',color:'#333'}"
                border
                style="width: 100%"

                ref="multipleTable"
                @selection-change="handleSelectionChange"
                @select="select"
                @select-all="select"
            >
                <el-table-column type="selection" width="45" fixed="left" align="center">
                </el-table-column>
                <el-table-column type="index" label="序号" width="50" align="center"> </el-table-column>
                <el-table-column
                prop="distName"
                label="行政区划"
                align="center"
                min-width="80"
                >
                </el-table-column>
                <el-table-column
                prop="enterpName"
                label="单位名称"
                align="center"
                width='210'
                :show-overflow-tooltip="true"
                >
                <template slot-scope="scope">
                  <span
                    @click="goEnt(scope.row)"
                    style="color: rgb(57, 119, 234); cursor: pointer"
                    class="enterpName"
                  >
                    {{ scope.row.enterpName }}
                  </span>
                </template>
                </el-table-column>
                <el-table-column
                prop="equipment"
                label="设备名称"
                align="center"
                min-width="100"
                :show-overflow-tooltip="true"
                >
                </el-table-column>
                <el-table-column
                prop="declareReason"
                label="报备原因"
                align="center"
                width="80"
                >
                <template slot-scope="scope">
                    <span v-if="scope.row.declareReason == '01'">故障检修</span>
                    <span v-else-if="scope.row.declareReason == '02'">设备损坏</span>
                    <span v-else-if="scope.row.declareReason == '03'">停工停产</span>
                    <span v-else-if="scope.row.declareReason == '04'">其它原因</span>
                </template>
                </el-table-column>
                <el-table-column
                prop="endTime"
                label="开始停用"
                align="center"
                width="155"
                >
                </el-table-column>
                <el-table-column
                prop="startTime"
                label="恢复启用"
                align="center"
                width="155"
                >
                </el-table-column>
                <el-table-column
                prop="state"
                label="审核状态"
                align="center"
                >
                <template slot-scope="scope">
                    <span v-if="scope.row.state == '0'" style="color:#ee8742">待审核</span>
                    <span v-else-if="scope.row.state == '1'" style="color:#58bd77">审核通过</span>
                    <span v-else-if="scope.row.state == '2'" style="color:#ff1818">审核驳回</span>
                    <span v-else-if="scope.row.state == '3'" style="color:#909090">过期未审核</span>
                </template>
                </el-table-column>
                <el-table-column
                label="操作"
                align="center"
                 width="180"
                >
                <template slot-scope="scope">
                    <span v-if="$store.state.login.user.user_type == 'gov' && $store.state.login.userDistCode != '420000' && scope.row.state == 0" style='color: rgb(57, 119, 234);margin-right:10px;cursor: pointer;' @click="audit(scope.row.id,scope.row.enterpId)">审核</span>
                    <span v-if="$store.state.login.user.user_type == 'ent' && scope.row.state == 0" style='color: rgb(57, 119, 234);margin-right:10px;cursor: pointer;' @click="addEdit(scope.row.id,'1')">编辑</span>
                    <span style='color: rgb(57, 119, 234);margin-right:10px;cursor: pointer;' @click="detail(scope.row.id)">详情</span>
                    <span style='color: rgb(57, 119, 234);cursor: pointer;' v-if="scope.row.enclosure" @click="lookImg(scope.row.enclosure)">附件</span>
                </template>
                </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                background
                layout="total, prev, pager, next"
                :total="total"
                v-if="total != 0"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <AddEdit ref="addEdit"></AddEdit>
    <Audit ref="audit"></Audit>
    <Detail ref="detail"></Detail>
    <LookImg ref="lookImg"></LookImg>
  </div>
</template>
<script>
import { getAbnormalList,getEquipmentExportExcel } from "@/api/dailySafety";
import { parseTime} from '@/utils/index';
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import Audit from "./audit";
import AddEdit from "./addEdit";
import Detail from "./detail";
import LookImg from "./lookImg";
export default {
  name:"videoAbnormal",
  components: {
      Audit,
      AddEdit,
      Detail,
      LookImg,
  },
  props: {
      entInfoData: {
      type: Object,
    },
  },
  data() {
    return {
      districtVal: this.$store.state.login.userDistCode || '',
      district: this.$store.state.controler.district,
      enterpName:'',
      value1:"",
      sensortypeCode:'',
      monitemkey:'',
      warningType:'',
      state:'',
      startTime:'',
      endTime:'',
      loading: true,
      areaName: "",
      selection:[],
      options3: [
        {
          value: "0",
          label: "待审核",
        },
        {
          value: "1",
          label: "审核通过",
        },
        {
          value: "2",
          label: "审核驳回",
        },
        {
          value: "3",
          label: "过期未审核",
        }
      ],
      tableData: [],
      distCode: this.$store.state.login.userDistCode || '',
      currentPage: 1,
      size:10,
      total:'',
      enterpid:''
    };
  },
  methods: {
    // getDistrict() {
        // if(this.$store.state.login.user.user_type == 'gov'){
        //     getDistrictUser().then((res) => {
        //         let child = res.data.data;
        //         // debugger;
        //         if (child.children.length > 0) {
        //         for (let j = 0; j < child.children.length; j++) {
        //             if (child.children[j].children.length > 0) {
        //             for (let z = 0; z < child.children[j].children.length; z++) {
        //                 if (child.children[j].children[z].children.length < 1) {
        //                 //判断children的数组长度
        //                 child.children[j].children[z].children = undefined;
        //                 }
        //             }
        //             } else {
        //             child.children[j].children = undefined;
        //             }
        //         }
        //         } else {
        //         child.children = undefined;
        //         }
        //         this.district = [child];
        //         // console.log(this.district);
        //     });
        // }
    // },
    //物联监测报警列表
    getIotMontoringDataList(id) {
      this.enterpid = id;
      this.loading = true;
      getAbnormalList({
        current: this.currentPage,
        distCode: this.distCode,
        size: this.size,
        enterpid:id || this.enterpid,
        type:'1',
        state:this.state,
        enterpName:this.enterpName,
        startTime:this.startTime,
        endTime:this.endTime
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    searchTime(value){
        if(value){
            let date1 = new Date(value[0]);
            let dataTime1 = parseTime(date1, '{y}-{m}-{d}');
            let date2 = new Date(value[1]);
            let dataTime2 = parseTime(date2, '{y}-{m}-{d}')
            this.startTime = dataTime1;
            this.endTime = dataTime2;
        }else{
            this.value1 = '';
            this.startTime = '';
            this.endTime = '';
        } 
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpid);
    },
    search(){
        this.currentPage = 1
        this.getIotMontoringDataList(this.enterpid);
    },
    addEdit(id,type){
       this.$refs.addEdit.closeBoolean(true);
       this.$refs.addEdit.getData(this.entInfoData,id,type); 
    },
    lookImg(img,type){
       this.$refs.lookImg.closeBoolean(true);
       this.$refs.lookImg.getData(img,'1'); 
    },
    audit(id,enterpId){
        this.$refs.audit.closeBoolean(true);
        this.$refs.audit.getData(id,enterpId,'1');
    },
    detail(id){
        this.$refs.detail.closeBoolean(true);
        this.$refs.detail.getData(id,'1');
    },
    handleChange(value) {
      if (value) {
        this.distCode= value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getIotMontoringDataList(this.enterpid);
    },
    clearKey(){
        this.enterpName = '';
    },
    clearSensortypeCode(){
        this.sensortypeCode='';
    },
    clearmonitemkey(){
        this.monitemkey ='';
    },
    clearwarningType(){
        this.warningType ='';
    },
    clearState(){
        this.state ='';
    },
    // 导出
    exportExcel() {
    //   let list = [this.distCode, ...this.selection];
      getEquipmentExportExcel({
        ids:this.selection.length <= 0 ? null : this.selection.join(','),
        distCode: this.distCode,
        // size: this.size,
        enterpid: this.enterpid,
        type:'1',
        state:this.state,
        enterpName:this.enterpName,
        startTime:this.startTime,
        endTime:this.endTime
        }).then((response) => {
          // 处理返回的文件流
          // console.log(response);
          if (response.status == 200) {
            this.$message({
              message: "导出成功",
              type: "success",
            });
          } else {
            this.$message.error("导出失败");
          }
          const blob = new Blob([response.data], { type: "application/xls" });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "视频异常申报信息" + timestamp + ".xls";
          //下载文件
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
          }, 0);
        });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    goToRunning() {
      this.$router.push({ path: `/riskAssessment/iotMontoringAlarm`});
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    //   debugger;
    //   console.log(this.entInfoData)
    //   this.entInfoDataes = this.entInfoData;
    // this.getDistrict();
    // this.getIotMontoringDataList(this.entInfoData.enterpId);
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  watch:{
   entInfoData(newValue,oldValue){ 
    this.enterpId = newValue.enterpId
   },
  }
};
</script>
<style lang="scss" scoped>
.equipmentFacilities {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    width: 100%;
    font-weight: 600;
    padding-bottom: 10px;
    margin-bottom: 0px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > *{
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
/deep/ .l .el-input{width: 200px;}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group{
  margin-bottom: 15px;
}

</style>
