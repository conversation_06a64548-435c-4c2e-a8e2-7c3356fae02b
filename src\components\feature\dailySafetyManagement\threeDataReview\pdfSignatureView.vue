<template>
  <div class="handleProcedures">  
      <div class="pageBoxInfo page">
          <!-- <button class="btn-outline-dark" id="view-fullscreen">全屏</button>
          <button class="btn-outline-dark" id="cancel-fullscreen">
            退出全屏
          </button> -->
          <div class="boxCon">
            <div class="pageCon">
              <button class="btn-outline-dark" @click="prevPage">上一页</button>
              <button class="btn-outline-dark" @click="nextPage">下一页</button>
              <button class="btn-outline-dark">
                {{ pageNum }}/{{ numPages }}页
              </button>
              <input
                id="page-input"
                class="btn-outline-dark"
                :value="pageNum"
                type="number"
                ref="getPages"
                onkeyup="this.value=this.value.replace(/\D/g,'')"
                onafterpaste="this.value=this.value.replace(/\D/g,'')"
              />
              <button class="btn-outline-dark" @click="cutover">跳转</button>
            </div>

            <!-- <div class="ele-control" style="margin: 0; padding: 0">
              <button class="btn-outline-dark" @click="removeSignature">
                删除签章
              </button>
              <button class="btn-outline-dark" @click="clearSignature">
                清除所有签章
              </button>
              <button class="btn-outline-dark" @click="submitSignature">
                生成报告
              </button>
            </div> -->
          </div>
        </div>
    <div class="elesign" id="app" v-loading="loading">
      <!-- <div class="left">
        <div class="left-title">专家签名</div>
        <draggable
          v-model="mainImagelist"
          :group="{ name: 'itext', pull: 'clone' }"
          :sort="false"
          @end="end"
        >
          <transition-group type="transition">
            <li v-for="item in mainImagelist" :key="item" class="item">
              <img :src="item" width="100%;" height="100%" class="imgstyle" />
            </li>
          </transition-group>
        </draggable>
      </div> -->
      <!-- pdf的预览 -->
      <div class="center">      
        <canvas id="the-canvas" />
      </div>
      <!-- 盖章部分 -->
      <canvas id="ele-canvas" v-loading="loading"></canvas>

      <!--  任务信息  -->
      <!-- <div class="right">
        <div class="left-title">任务信息</div>
        <div>
          <div>
            <div class="right-item">
              <span class="right-item-title">文件主题</span>
              <span class="detail-item-desc">{{ taskInfo.title }}</span>
            </div>
            <div class="right-item">
              <span class="right-item-title">发起方</span>
              <span class="detail-item-desc">{{ taskInfo.uname }}</span>
            </div>
            <div class="right-item">
              <span class="right-item-title">截止时间</span>
              <span class="detail-item-desc">{{ taskInfo.endtime }}</span>
            </div>
          </div>
        </div>
      </div> -->

      <div fix v-show="isSpinShow">
        <div class="loadingclass">
          <div id="preloader_6">
            <span></span>
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import draggable from "vuedraggable";
let pdfjsLib = window["pdfjs-dist/build/pdf"];
pdfjsLib.GlobalWorkerOptions.workerSrc =`http://${window.location.host}/static/pdf/pdf.worker.js`
// pdfjsLib.GlobalWorkerOptions.workerSrc = `http://${window.location.host}/pdf.worker.js`;
// pdfjsLib.GlobalWorkerOptions.workerSrc =
//   "https://mozilla.github.io/pdf.js/build/pdf.worker.js";
import findByFileIdAttachment from "../../../../api/download/download.js"; //@/api/download/download.js
import axios from "axios";
import {
  getCompanyProjectFindById, //查询详情
  projectApprovalReviewResult,
  projectEvaluatePass,
} from "@/api/companyParticularJob";
let storage = window.localStorage;
export default {
  //import引入的组件
  components: {
    draggable,
  },
  props:{
    linkattachId:String
  },
  data() {
    return {
      loading: false,
      filePathPdf: "",
      attachId: "bd4c6c1f530d4be6b7f6f06e7ad3bc00",
      pdfUrl: "",
      pdfDoc: null,
      numPages: 1,
      pageNum: 1,
      scale: 1,
      pageRendering: false,
      pageNumPending: null,
      sealUrl: "",
      signUrl: "",
      canvas: null,
      ctx: null,
      canvasEle: null,
      whDatas: null,
      isSpinShow: false,
      mainImagelist: [],
      taskInfo: {},
    };
  },
  //计算属性,就是依赖其它的属性计算所得出最后的值
  computed: {
    hasSigna() {
      return !!this.canvasEle && !!this.canvasEle.getObjects()[0]
        ? true
        : false;
    },
  },

  //监听一个值的变化,然后执行相对应的函数。
  watch: {
    whDatas: {
      handler() {
        if (!!this.whDatas) {
          this.renderFabric();
          this.canvasEvents();
        }
      },
    },

    pdfUrl: function (val) {
      this.$nextTick(() => {
        this.showpdf(val);
      });
    },
    pageNum: function (pageNum) {
      let pageNums = Number(pageNum);
      this.commonSign(this.pageNum);
      this.queueRenderPage(this.pageNum);
    },
  },

  methods: {
    getCompanyParticularJobFun() {
      getCompanyProjectFindById({ infoId: this.$route.query.infoId }).then(
        (res) => {
          return res;
        }
      );
    },
    goToRunning() {
      this.$router.push({
        path: `/dailySafetyManagement/threeDataReview`,
      });
    },
    closeCurrent() {
      this.$router.push({
        path: `/dailySafetyManagement/threeDataReview`,
      });
    },

    renderPage(num) {
      let _this = this;
      this.pageRendering = true;
      // Using promise to fetch the page
      return this.pdfDoc.getPage(num).then((page) => {
        let viewport = page.getViewport({ scale: _this.scale });        
        _this.canvas.height = viewport.height;
        _this.canvas.width = viewport.width;

        // Render PDF page into canvas context
        let renderContext = {
          canvasContext: _this.ctx,
          viewport: viewport,
        };
        let renderTask = page.render(renderContext);

        // Wait for rendering to finish
        renderTask.promise.then(() => {
          _this.pageRendering = false;
          if (_this.pageNumPending !== null) {
            // New page rendering is pending
            this.renderPage(_this.pageNumPending);
            _this.pageNumPending = null;
          }
          this.isSpinShow = false;
        });
      });
    },
    queueRenderPage(num) {
      if (this.pageRendering) {
        this.pageNumPending = num;
      } else {
        this.renderPage(num);
      }
    },
    prevPage() {
      this.confirmSignature();
      if (this.pageNum <= 1) {
        return;
      }
      this.pageNum--;
    },
    nextPage() {
      this.confirmSignature();
      if (this.pageNum >= this.numPages) {
        return;
      }
      this.pageNum++;
    },
    cutover() {
      this.confirmSignature();
      let pageNums = Number(this.$refs.getPages.value);
      if (pageNums > this.numPages) {
        this.pageNum = this.numPages;
        return;
      }

      if (pageNums < 1) {
        this.pageNum = 1;
        return;
      }

      if (!/(^[1-9]\d*$)/.test(pageNums)) {
        this.pageNum = 1;
        return;
      }
      this.pageNum = pageNums;
    },
    showpdf(pdfUrl) {
      this.canvas = document.getElementById("the-canvas");
      this.ctx = this.canvas.getContext("2d");
      pdfjsLib
        .getDocument({
          url: pdfUrl,
          rangeChunkSize: 65536,
          disableAutoFetch: false,
        })
        .promise.then((pdfDoc_) => {
          this.pdfDoc = pdfDoc_;
          this.numPages = this.pdfDoc.numPages;
          this.renderPage(this.pageNum).then((res) => {
            this.renderPdf({
              width: this.canvas.width,
              height: this.canvas.height,
            });
            // this.isSpinShow = false;
          });
          this.commonSign(this.pageNum, true);
        });
    },

    /**
     *  盖章部分开始
     */
    // 设置绘图区域宽高
    renderPdf(data) {
      this.whDatas = data;
      // document.querySelector(".elesign").style.width = 750 + "px";
    },

    // 生成绘图区域
    renderFabric() {
      let canvaEle = document.querySelector("#ele-canvas");

      canvaEle.width = this.whDatas.width;
      canvaEle.height = this.whDatas.height;

      this.canvasEle = new fabric.Canvas(canvaEle);
      let container = document.querySelector(".canvas-container");
      container.style.position = "absolute";
      container.style.top = "42px";
    },

    // 相关事件操作哟
    canvasEvents() {
      // 拖拽边界 不能将图片拖拽到绘图区域外
      this.canvasEle.on("object:moving", function (e) {
        var obj = e.target;
        // if object is too big ignore
        if (
          obj.currentHeight > obj.canvas.height ||
          obj.currentWidth > obj.canvas.width
        ) {
          return;
        }
        obj.setCoords();
        // top-left  corner
        if (obj.getBoundingRect().top < 0 || obj.getBoundingRect().left < 0) {
          obj.top = Math.max(obj.top, obj.top - obj.getBoundingRect().top);
          obj.left = Math.max(obj.left, obj.left - obj.getBoundingRect().left);
        }
        // bot-right corner
        if (
          obj.getBoundingRect().top + obj.getBoundingRect().height >
            obj.canvas.height ||
          obj.getBoundingRect().left + obj.getBoundingRect().width >
            obj.canvas.width
        ) {
          obj.top = Math.min(
            obj.top,
            obj.canvas.height -
              obj.getBoundingRect().height +
              obj.top -
              obj.getBoundingRect().top
          );
          obj.left = Math.min(
            obj.left,
            obj.canvas.width -
              obj.getBoundingRect().width +
              obj.left -
              obj.getBoundingRect().left
          );
        }
      });
    },

    // 添加公章
    addSeal(sealUrl, left, top, index) {
      fabric.Image.fromURL(sealUrl, (oImg) => {
        oImg.set({
          left: left,
          top: top,
          // angle: 10,
          scaleX: 0.8,
          scaleY: 0.8,
          index: index,
        });
        // oImg.scale(0.5); //图片缩小一
        this.canvasEle.add(oImg);
      });
    },

    // 删除签章
    removeSignature() {
      this.canvasEle.remove(this.canvasEle.getActiveObject());
    },

    //翻页展示盖章信息
    commonSign(pageNum, isFirst = false) {
      if (isFirst == false) this.canvasEle.remove(this.canvasEle.clear()); //清空页面所有签章
      let caches = JSON.parse(storage.getItem("signs")); //获取缓存字符串后转换为对象
      if (caches == null) return false;
      let datas = caches[this.pageNum];
      if (datas != null && datas != undefined) {
        for (let index in datas) {
          this.addSeal(
            datas[index].sealUrl,
            datas[index].left,
            datas[index].top,
            datas[index].index
          );
        }
      }
    },

    //确认签章位置并保存到缓存
    confirmSignature() {
      let data = this.canvasEle.getObjects(); //获取当前页面内的所有签章信息
      // if (data.length == 0) {
      //   this.$message.info("pdf还没有任何专家签名");
      //   return;
      // }
      let caches = JSON.parse(storage.getItem("signs")); //获取缓存字符串后转换为对象
      let signDatas = {}; //存储当前页的所有签章信息
      let i = 0;
      let sealUrl = "";
      for (var val of data) {
        signDatas[i] = {
          width: val.width,
          height: val.height,
          picTop: val.top,
          picLeft: val.left,
          angle: val.angle,
          translateX: val.translateX,
          translateY: val.translateY,
          percentX: val.scaleX * 100,
          percentY: val.scaleY * 100,
          pdfPage: this.pageNum,
          picPath: this.mainImagelist[val.index],
          index: val.index,
        };
        i++;
      }
      if (caches == null) {
        caches = {};
        caches[this.pageNum] = signDatas;
      } else {
        caches[this.pageNum] = signDatas;
      }
      storage.setItem("signs", JSON.stringify(caches)); //对象转字符串后存储到缓存
    },

    //提交数据
    submitSignature() {     
      this.confirmSignature();
      let caches = JSON.parse(storage.getItem("signs"));

      if (Object.keys(caches[1]).length == 0) {
        this.$message.info("pdf还没有任何专家签名");
        return;
      }

      console.log(caches, "提交caches");
      // for(var prop of caches[1]){
      //     console.log("obj." + prop + " = " + caches[1][prop])
      // }
      var picSignDTOs = [];
      Object.keys(caches).forEach((key) => {
        Object.keys(caches[key]).forEach((key2) => {
          console.log("obj." + key2 + " = " + caches[key][key2]);
          picSignDTOs.push(caches[key][key2]);
        });
      });

      console.log(picSignDTOs, "picSignDTOs");
      var signDTO = {
        pdfWidth: this.whDatas.width,
        pdfHight: this.whDatas.height,
        picSignDTOs: picSignDTOs,
        sourcePath: this.filePathPdf,
      };
      var passDTO = {
        infoId: this.$route.query.infoId,
        signDTO: signDTO,
      };
      console.log(passDTO, "pamrs+pdf参数");
      //确定提交
      this.$confirm("确定提交", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          projectEvaluatePass(passDTO).then((res) => {
            if (res.data.status === 200) {
              storage.removeItem("signs");
              this.$message.success(res.data.data);
              this.$router.push({
                path: "/dailySafetyManagement/threeDataReview",
                query: {
                  infoId: this.$route.query.infoId,
                  approvalStatus: this.$route.query.approvalStatus,
                },
              });
            } else {
              this.$message.error(res.data.data);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },

    //清空数据
    clearSignature() {
      this.canvasEle.remove(this.canvasEle.clear()); //清空页面所有签章
      storage.removeItem("signs"); //清除缓存
    },
    /**
     *  盖章部分结束
     */

    //获取url参数
    getParam(str) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == str) {
          return pair[1];
        }
      }
      return false;
    },
    end(e) {
      console.log(this.mainImagelist);
      this.addSeal(
        this.mainImagelist[e.newDraggableIndex],
        e.originalEvent.layerX,
        e.originalEvent.layerY,
        e.newDraggableIndex
      );
    },
  },
  //未渲染html页面时执行
  created: async function () { 
    this.loading=true  
    var that = this;
    // that.pdfUrl = `http://${window.location.host}/` + "static/pdf/show.pdf";
    // var imgattachId = await getCompanyProjectFindById({
    //   infoId: this.$route.query.infoId,
    // });  
    // console.log(imgattachId.data.data.reviseAttachList[0].attachId);
    console.log(this.linkattachId)
    var param = {
      attachId:this.linkattachId, //测试：bd4c6c1f530d4be6b7f6f06e7ad3bc00
    };
   
    axios({
      method: "post",
      url: "/gemp-file/api/attachment/findByFileId/v1",
      data: param,     
    }).then((res) => {   
      this.loading=false  
      console.log(res.data.data.fileOuterPath, "fileOuterPath"); //  http://************:9080/upload/
      console.log(res.data.data.filePath, "filePath"); //uploadFile/duty/bd4c6c1f530d4be6b7f6f06e7ad3bc00.pdf
      this.filePathPdf = res.data.data.filePath;
      // that.pdfUrl = './show.pdf';
      // that.pdfUrl = `http://${window.location.host}/` + "static/pdf/show.pdf";
      that.pdfUrl = ["/upload/" + this.filePathPdf];
      //that.mainImagelist = [`http://${window.location.host}/`+'sign.png',`http://${window.location.host}/`+'seal.png'];
      console.log(that.pdfUrl,'获取pdf路径')
    });   
  },
  async mounted() {},
};

(function () {
  var viewFullScreen = document.getElementById("view-fullscreen");
  if (viewFullScreen) {
    viewFullScreen.addEventListener(
      "click",
      function () {
        var docElm = document.documentElement;
        if (docElm.requestFullscreen) {
          docElm.requestFullscreen();
        } else if (docElm.msRequestFullscreen) {
          docElm = document.body; //overwrite the element (for IE)
          docElm.msRequestFullscreen();
        } else if (docElm.mozRequestFullScreen) {
          docElm.mozRequestFullScreen();
        } else if (docElm.webkitRequestFullScreen) {
          docElm.webkitRequestFullScreen();
        }
      },
      false
    );
  }

  var cancelFullScreen = document.getElementById("cancel-fullscreen");
  if (cancelFullScreen) {
    cancelFullScreen.addEventListener(
      "click",
      function () {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        }
      },
      false
    );
  }

  var fullscreenState = document.getElementById("fullscreen-state");
  if (fullscreenState) {
    document.addEventListener(
      "fullscreenchange",
      function () {
        fullscreenState.innerHTML = document.fullscreenElement ? "" : "not ";
      },
      false
    );

    document.addEventListener(
      "msfullscreenchange",
      function () {
        fullscreenState.innerHTML = document.msFullscreenElement ? "" : "not ";
      },
      false
    );

    document.addEventListener(
      "mozfullscreenchange",
      function () {
        fullscreenState.innerHTML = document.mozFullScreen ? "" : "not ";
      },
      false
    );

    document.addEventListener(
      "webkitfullscreenchange",
      function () {
        fullscreenState.innerHTML = document.webkitIsFullScreen ? "" : "not ";
      },
      false
    );
  }

  // var marioVideo = document.getElementById("mario-video")
  //     videoFullscreen = document.getElementById("video-fullscreen");

  // if (marioVideo && videoFullscreen) {
  //     videoFullscreen.addEventListener("click", function (evt) {
  //         if (marioVideo.requestFullscreen) {
  //             marioVideo.requestFullscreen();
  //         }
  //         else if (marioVideo.msRequestFullscreen) {
  //             marioVideo.msRequestFullscreen();
  //         }
  //         else if (marioVideo.mozRequestFullScreen) {
  //             marioVideo.mozRequestFullScreen();
  //         }
  //         else if (marioVideo.webkitRequestFullScreen) {
  //             marioVideo.webkitRequestFullScreen();
  //         }
  //     }, false);
  // }
})();
</script>


<style lang="scss" scoped>
.handleProcedures { 
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumbDetails {
      display: flex;
      justify-content: space-between;
      // .icon-box {
      //   border: 1px solid #d9d9d9;
      //   padding: 0 10px;
      //   height: 30px;
      //   display: inline-block;
      //   text-align: center;
      //   line-height: 30px;
      //   border-radius: 5px;
      //   color: #252525;
      //   margin: 0 15px 0 0;
      // }
      // .icon-box.box-con {
      //   background-color: #326eff;
      //   border: 1px solid #326eff;
      //   color: #fff;
      //   i.el-icon-close {
      //     display: inline-block;
      //     margin: 0 0 0 10px;
      //   }
      // }
    }
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }
  .activeBody {
    //  height:calc(100% - 150px);
    border: 1px solid #d9d9d9;
    padding: 0 15px 0 0;
    margin: 10px 0 0 0;
    .activeHeader {
      width: 100%;
      position: relative;
    }
  }

  /deep/ .el-tabs__header {
    margin: 0 0 0 15px;
  }
  /deep/ .el-tabs__nav-scroll {
    overflow: inherit;
  }
  /deep/ .el-tabs__content {
    overflow: inherit;
  }
}
</style>

<style scoped>
html:fullscreen {
  background: white;
}
.elesign {
  /* display: flex;
  flex: 1;
  flex-direction: column; */
  position: relative;
  overflow:auto;
  /* padding-left: 180px; */
  margin: 0; 
}
 .elesign .center{
    height: 100%;
    /* height: calc(100% - 200px); */
    /* overflow-y:scroll;
    overflow-x:inherit; */
    text-align: center;
  }
.page {
     position: absolute;
    left: 0;
    /* width: 612px; */
    /* top: -28px; */
    top: 59px;
    z-index: 9999;
    left: 50%;
    margin-left: -150px;
}
.pageBoxInfo .boxCon {
  display: flex;
  justify-content: space-between;
}
.pageCon {
  width: 300px;
}
#ele-canvas {
  /* border: 1px solid #5ea6ef; */
}
.ele-control {
  text-align: center;
  margin-top: 3%;
 
}
#page-input {
  width: 52px;
}

@keyframes ani-demo-spin {
  from {
    transform: rotate(0deg);
  }
  50% {
    transform: rotate(180deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.loadingclass {
  position: absolute;
  top: 30%;
  left: 49%;
  z-index: 99;
}
.left {
  position: absolute;
  top: 42px;
  left: -5px;
  padding: 5px 5px;
  /*border: 1px solid #eee;*/
  /*border-radius: 4px;*/
}
.left-title {
  text-align: center;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}
li {
  list-style-type: none;
  padding: 10px;
}
.imgstyle {
  vertical-align: middle;
  width: 130px;
  border: solid 1px #e8eef2;
  /* background-image: url("tuo.png"); */
  background-repeat: no-repeat;
}
.right {
  position: absolute;
  top: 7px;
  right: -177px;
  margin-top: 34px;
  padding-top: 10px;
  padding-bottom: 20px;
  width: 152px;
  /*border: 1px solid #eee;*/
  /*border-radius: 4px;*/
}
.right-item {
  margin-bottom: 15px;
  margin-left: 10px;
}
.right-item-title {
  color: #777;
  height: 20px;
  line-height: 20px;
  font-size: 12px;
  font-weight: 400;
  text-align: left !important;
}
.detail-item-desc {
  color: #333;
  line-height: 20px;
  width: 100%;
  font-size: 12px;
  display: inline-block;
  text-align: left;
}
.btn-outline-dark {
  color: #333;
  background-color: #fff;
  background-image: none;
  border: 1px solid #326eff;
  border-radius: 3px;
  padding: 0 5px;
}

.btn-outline-dark:hover {
  color: #fff;
  background-color: #326eff;
  border-color: #326eff;
}

#preloader_6 {
  position: relative;
  width: 42px;
  height: 42px;
  animation: preloader_6 5s infinite linear;
}
#preloader_6 span {
  width: 20px;
  height: 20px;
  position: absolute;
  background: red;
  display: block;
  animation: preloader_6_span 1s infinite linear;
}
#preloader_6 span:nth-child(1) {
  background: #2ecc71;
}
#preloader_6 span:nth-child(2) {
  left: 22px;
  background: #9b59b6;
  animation-delay: 0.2s;
}
#preloader_6 span:nth-child(3) {
  top: 22px;
  background: #3498db;
  animation-delay: 0.4s;
}
#preloader_6 span:nth-child(4) {
  top: 22px;
  left: 22px;
  background: #f1c40f;
  animation-delay: 0.6s;
}
@keyframes preloader_6 {
  from {
    -ms-transform: rotate(0deg);
  }
  to {
    -ms-transform: rotate(360deg);
  }
}
@keyframes preloader_6_span {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(0.5);
  }
  100% {
    transform: scale(1);
  }
}
</style>
