<template>
    <div class="law-list">
        <el-table :data="sceneData" v-loading="loading" style="width: 100%"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }" border ref="multipleTable">
            <el-table-column type="index" label="序号" width="50" align="center">
            </el-table-column>
            <el-table-column prop="documentNum" label="文件编号" align="center" min-width="150">
            </el-table-column>
            <!-- enterpName -->
            <!-- <el-table-column prop="enterpName" label="检查对象名称" align="center" min-width="150">
            </el-table-column> -->
            <el-table-column prop="parentOrgName" label="检查机构" align="center" width="180">
            </el-table-column>
            <el-table-column prop="orgName" label="检查部门" align="center"  min-width="120">
            </el-table-column>
            <el-table-column prop="startTime" label="检查开始时间" align="center" width="200">
            </el-table-column>
            <el-table-column prop="dataSource" label="数据来源" align="center" width="150">
            </el-table-column>
            <el-table-column prop="inspectPerson" label="检查员" align="center" width="150">
            </el-table-column>
        </el-table>
        <div class="pagination">
            <el-pagination @current-change="handleCurrentChange" :current-page.sync="sceneParams.nowPage"  background
                layout="total, prev, pager, next" :total="total" v-if="total != 0">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import { spotInspectFindPage } from "@/api/riskAssessment";
export default {
    data() {
        return {
            sceneData: [],
            datePickerValue:[],
            sceneParams: {        
                nowPage: 1,
                pageSize: 10,
                // districtCode: '',
                // // orgCode: "",
                // entType: "",
                // startTime: "",
                // endTime: "",
            },
            enterpriseId: '', //企业id
            total: 0,
            loading: false,
            district: this.$store.state.controler.district, // 行政区划
            entTypes: [], // 企业类型
            orgList: [], // 检查部门
        }
    },
    created() {
        // this.getEnterpriseType();
        // this.spotInspectFindPage();
    },
    methods: {
        // 分页查询
        handleCurrentChange(val) {
            this.sceneParams.nowPage = val;
            this.sceneData =[];
            this.total = 0;
            this.spotInspectFindPage();
        },
        //安全生产事故警示
        getData(val) {
            if (val) this.enterpriseId = val;
            const params = {
                page: this.sceneParams.nowPage,
                pageSize: this.sceneParams.pageSize,
                enterpriseCode: [this.enterpriseId]
            }
            spotInspectFindPage(params).then((res) => {
                const { data } = res;
                if (data.code == 0) {
                    this.sceneData = data.data.records;
                    this.total = data.data.total;
                } else {
                    this.$message({
                        message: data.msg || "获取数据失败",
                        type: 'warning'
                    })
                }
            });
        },
        clickBan(val) {
            var url = `https://yyzc-hlwzf.hbsis.gov.cn:31443/#v_form/task/check/?id=${val.originId}&menu-id=model%3Atask.check&create_by_me=2`
            window.open(url)
        },
    }
}
</script>

<style lang="scss" scoped>
.law-list {
    .pagination {
        margin-top: 30px;
        padding-bottom: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>