<template>
  <div class="enterpriseManagement">
    <div>
      <div class="main-center">
        <riskWarning ref="riskWarning" :enterpid="enterpid"></riskWarning>
        <div class="main-list">
          <ul class="work-list">
            <li>
              <saftyPromise
                ref="saftyPromise"
                :enterpid="enterpid"
              ></saftyPromise>
            </li>
            <li>
              <systemRunning
                ref="systemRunning"
                :enterpid="enterpid"
              ></systemRunning>
            </li>
            <li>
              <emergencyMenege
                ref="emergencyMenege"
                :enterpid="enterpid"
              ></emergencyMenege>
            </li>
            <li>
              <warningPush ref="warningPush" :enterpid="enterpid"></warningPush>
            </li>
            <li>
              <videoMonitor
                ref="videoMonitor"
                :enterpid="enterpid"
              ></videoMonitor>
            </li>
            <li>
              <foucsOn ref="foucsOn" :entInfoData="entInfoData"></foucsOn>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import riskWarning from "./riskWarning";
import saftyPromise from "./saftyPromise";
import systemRunning from "./systemRunning";
import emergencyMenege from "./emergencyMenege";
import warningPush from "./warningPush";
import foucsOn from "./foucsOn";
import videoMonitor from "./videoMonitor";
import { mapState } from "vuex";
export default {
  components: {
    riskWarning: riskWarning,
    saftyPromise: saftyPromise,
    systemRunning: systemRunning,
    emergencyMenege: emergencyMenege,
    warningPush: warningPush,
    foucsOn: foucsOn,
    videoMonitor: videoMonitor,
  },
  data() {
    return {
      enterpid: "",
      entInfoData: {},
    };
  },
  methods: {
    getData() {
      console.log('我执行了首页')
      this.entInfoData = this.enterData;
      this.enterpid = this.enterData.enterpId;
      // this.$store.commit("login/updataEnter", res.data.data);
      this.$nextTick(() => {
        this.$refs.systemRunning.getData(this.enterData.enterpId);
        this.$refs.saftyPromise.getData(this.enterData.enterpId);
        this.$refs.riskWarning.getData(this.enterData.enterpId);
        this.$refs.foucsOn.getIotMontoringDataList(this.enterData.enterpId);
        this.$refs.videoMonitor.getData(this.enterData.enterpId);
        this.$refs.warningPush.getData(this.enterData.enterpId);
      });
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {
    this.getData()
  },
  computed: {
    ...mapState({
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {
    enterData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.getData();
        }
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  min-width: 1440px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .main-center {
    .main-list {
      .work-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        li {
          margin-top: 20px;
          width: calc(100% / 3 - 13px);
          height: 406px;
          border: 1px solid #d8e0ee;
          box-shadow: 0px 0px 8px 0px rgba(121, 163, 241, 0.3);
          border-radius: 4px;
          list-style: none;
        }
        li:nth-child(4),
        li:nth-child(5),
        li:nth-child(6) {
          height: 332px;
          overflow: hidden;
        }
      }
    }
  }
}
</style>
