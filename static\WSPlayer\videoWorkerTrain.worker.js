!function(t){var e={};function r(a){if(e[a])return e[a].exports;var n=e[a]={i:a,l:!1,exports:{}};return t[a].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.m=t,r.c=e,r.d=function(t,e,a){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},r.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var n in t)r.d(a,n,function(e){return t[e]}.bind(null,n));return a},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=0)}([function(t,e,r){"use strict";r.r(e);var a={log:function(){},error:function(){},count:function(){},info:function(){}};(function(){function t(){}t.createFromElementId=function(e){for(var r=document.getElementById(e),a="",n=r.firstChild;n;)3===n.nodeType&&(a+=n.textContent),n=n.nextSibling;var i=new t;return i.type=r.type,i.source=a,i},t.createFromSource=function(e,r){var a=new t;return a.type=e,a.source=r,a}})(),function(){function t(t){this.gl=t,this.program=this.gl.createProgram()}t.prototype={attach:function(t){this.gl.attachShader(this.program,t.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(t){return this.gl.getAttribLocation(this.program,t)},setMatrixUniform:function(t,e){var r=this.gl.getUniformLocation(this.program,t);this.gl.uniformMatrix4fv(r,!1,e)}}}(),function(){var t=null;function e(t,e,r){this.gl=t,this.size=e,this.texture=t.createTexture(),t.bindTexture(t.TEXTURE_2D,this.texture),this.format=r||t.LUMINANCE,t.texImage2D(t.TEXTURE_2D,0,this.format,e.w,e.h,0,this.format,t.UNSIGNED_BYTE,null),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE)}e.prototype={fill:function(t,e){var r=this.gl;r.bindTexture(r.TEXTURE_2D,this.texture),e?r.texSubImage2D(r.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,r.UNSIGNED_BYTE,t):r.texImage2D(r.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,r.UNSIGNED_BYTE,t)},bind:function(e,r,a){var n=this.gl;t||(t=[n.TEXTURE0,n.TEXTURE1,n.TEXTURE2]),n.activeTexture(t[e]),n.bindTexture(n.TEXTURE_2D,this.texture),n.uniform1i(n.getUniformLocation(r.program,a),e)}}}();var n=function(){this.map={}};n.prototype={put:function(t,e){this.map[t]=e},get:function(t){return this.map[t]},containsKey:function(t){return t in this.map},containsValue:function(t){for(var e in this.map)if(this.map[e]===t)return!0;return!1},isEmpty:function(t){return 0===this.size()},clear:function(){for(var t in this.map)delete this.map[t]},remove:function(t){delete this.map[t]},keys:function(){var t=new Array;for(var e in this.map)t.push(e);return t},values:function(){var t=new Array;for(var e in this.map)t.push(this.map[e]);return t},size:function(){var t=0;for(var e in this.map)t++;return t}};var i=n;var _=function(){var t,e,r,n,i,_,o,u,f,p=null,s=new Uint8Array,c=!1;function l(){p=Module._OpenDecoder(0,0,0),l.prototype.setIsFirstFrame(!1)}return l.prototype={init:function(){a.log("H264 Decoder init")},setOutputSize:function(t){u!=1.5*t&&(u=1.5*t,f=Module._malloc(u),s=new Uint8Array(Module.HEAPU8.buffer,f,u))},decode:function(a,f){if(t=Date.now(),e=new Uint8Array(a),s.set(e),r=Module._FrameAlloc(),Module._DecodeFrame(p,s.byteOffset,a.byteLength,u,r),n=Date.now()-t,_=Module._getYLength(r),i=Module._getHeight(r),!l.prototype.isFirstFrame())return l.prototype.setIsFirstFrame(!0),{firstFrame:!0};if(_>0&&i>0){t=Date.now();var c=new Uint8Array(s);return o={data:c,option:{yaddr:Module._getY(r),uaddr:Module._getU(r),vaddr:Module._getV(r),ylen:_,height:i,beforeDecoding:t},width:_,height:i,codecType:"h264",decodingTime:n,frameType:f},Module._FrameFree(r),o}},setIsFirstFrame:function(t){c=t},isFirstFrame:function(){return c},free:function(){Module._free(f),f=null}},new l};({isWasm:!1}).isWasm?importScripts("./ffmpegwasm.js"):importScripts("./ffmpegasm.js"),addEventListener("message",function(t){var e=t.data;switch(u=t.data.channelId,e.type){case"MediaData":null===o&&(o=new p),e.data.rtspInterleave[1],o.parseRTPData(e.data.payload)}},!1),Module.onRuntimeInitialized=function(){Module._RegisterAll(),f("WorkerReady")};var o=null,u=null;function f(t,e,r){var a={type:t,data:e,channelId:u,option:r};"canvasRender"===t?postMessage(a,[e.buffer]):postMessage(a)}var p=function(){var t,e=new s,r=0,a=null;function n(){this.decoder=new _}return n.prototype={init:function(t){},parseRTPData:function(n){for(var i=null,_={},o=n[22],u=(n[11]<<24)+(n[10]<<16)+(n[9]<<8)+n[8]>>>0,p=n.subarray(24+o,n.length-8),s=n.subarray(n.length-8,n.length),c=(s[7],s[6],s[5],s[4],(n[19]<<24)+(n[18]<<16)+(n[17]<<8)+n[16]>>>0),l=[],m=0;m<=p.length;)if(0==p[m])if(0==p[m+1])if(1==p[m+2]){if(l.push(m),5==(31&p[m+=3])||1==(31&p[m]))break}else 0==p[m+2]?m++:m+=3;else m+=2;else m+=1;for(m=0;m<l.length;m++)switch(i=p.subarray(l[m]+3,l[m+1]),31&p[l[m]+3]){default:break;case 1:"P",l[m]-1;break;case 5:"I",l[m]-1;break;case 28:break;case 7:e.parse(i),a=e.getSizeInfo().decodeSize;break;case 8:case 6:case 9:}r!==a&&(this.decoder.free(),r=a,this.decoder.setOutputSize(r)),_.frameData=null,_.frameData=this.decoder.decode(p),null!=_.frameData&&null!=_.frameData.data&&(c!==t&&(_.frameData.option.time=c,_.frameData.option.frameNo=u,f("canvasRender",_.frameData.data,_.frameData.option)),t=c)}},new n};function s(){var t=7,e=7,r=2,a=3,n=4,_=5,o=8,u=16,f=32,p=255,s=0,c=null;function l(){s=0,c=new i}function m(r,n){var i=n,_=s+i>>a;return i=s+n&t,r[_]>>e-(i&e)&1}function g(t,e){var r=s>>a,n=8*(r+1)-s;if(n<8)for(var i=0;i<3;i++){var _=t[r+i];_=0==i?_>>n<<n:2==i?_&255>>8-n|1<<n:0,t.set([_],r+i)}else t.set([0],r),t.set([1],r+1)}function d(t,e){var r=0;if(1===e)r=m(t,0);else for(var a=0;a<e;a++)r=(r<<1)+m(t,a);return s+=e,r}function h(t,e){for(var a=0,n=0,i=e;s+i<8*t.length&&!m(t,i++);)a++;if(0===a)return s+=1,0;n=1<<a;for(var _=a-1;_>=0;_--,i++)n|=m(t,i)<<_;return s+=a*r+1,n-1}function v(t,e){var a=h(t,e);return 1&a?(a+1)/r:-a/r}function y(t){c.put("cpb_cnt_minus1",h(t,0)),c.put("bit_rate_scale",d(t,n)),c.put("cpb_size_scale",d(t,n));for(var e=c.get("cpb_cnt_minus1"),r=new Array(e),a=new Array(e),i=new Array(e),o=0;o<=e;o++)r[o]=h(t,0),a[o]=h(t,0),i[o]=d(t,1);c.put("bit_rate_value_minus1",r),c.put("cpb_size_value_minus1",a),c.put("cbr_flag",i),c.put("initial_cpb_removal_delay_length_minus1",d(t,_)),c.put("cpb_removal_delay_length_minus1",d(t,_)),c.put("dpb_output_delay_length_minus1",d(t,_)),c.put("time_offset_length",d(t,_))}return l.prototype={parse:function(t){s=0,c.clear(),c.put("forbidden_zero_bit",d(t,1)),c.put("nal_ref_idc",d(t,r)),c.put("nal_unit_type",d(t,_)),c.put("profile_idc",d(t,o)),c.put("profile_compatibility",d(t,o)),c.put("level_idc",d(t,o)),c.put("seq_parameter_set_id",h(t,0));var e=c.get("profile_idc");if((100===e||110===e||122===e||244===e||44===e||83===e||86===e||118===e||128===e||138===e||139===e||134===e)&&(c.put("chroma_format_idc",h(t,0)),c.get("chroma_format_idc")===a&&c.put("separate_colour_plane_flag",d(t,1)),c.put("bit_depth_luma_minus8",h(t,0)),c.put("bit_depth_chroma_minus8",h(t,0)),c.put("qpprime_y_zero_transform_bypass_flag",d(t,1)),c.put("seq_scaling_matrix_present_flag",d(t,1)),c.get("seq_scaling_matrix_present_flag"))){for(var n=c.get("chroma_format_idc")!==a?o:12,i=new Array(n),l=0;l<n;l++)if(i[l]=d(t,1),i[l])for(var m=l<6?u:64,b=8,T=8,E=0;E<m;E++)T&&(T=(b+v(t,0)+256)%256),b=0===T?b:T;c.put("seq_scaling_list_present_flag",i)}if(c.put("log2_max_frame_num_minus4",h(t,0)),c.put("pic_order_cnt_type",h(t,0)),0===c.get("pic_order_cnt_type"))c.put("log2_max_pic_order_cnt_lsb_minus4",h(t,0));else if(1===c.get("pic_order_cnt_type")){c.put("delta_pic_order_always_zero_flag",d(t,1)),c.put("offset_for_non_ref_pic",v(t,0)),c.put("offset_for_top_to_bottom_field",v(t,0)),c.put("num_ref_frames_in_pic_order_cnt_cycle",h(t,0));for(var w=0;w<c.get("num_ref_frames_in_pic_order_cnt_cycle");w++)c.put("num_ref_frames_in_pic_order_cnt_cycle",v(t,0))}return c.put("num_ref_frames",h(t,0)),c.put("gaps_in_frame_num_value_allowed_flag",d(t,1)),c.put("pic_width_in_mbs_minus1",h(t,0)),c.put("pic_height_in_map_units_minus1",h(t,0)),c.put("frame_mbs_only_flag",d(t,1)),0===c.get("frame_mbs_only_flag")&&c.put("mb_adaptive_frame_field_flag",d(t,1)),c.put("direct_8x8_interence_flag",d(t,1)),c.put("frame_cropping_flag",d(t,1)),1===c.get("frame_cropping_flag")&&(c.put("frame_cropping_rect_left_offset",h(t,0)),c.put("frame_cropping_rect_right_offset",h(t,0)),c.put("frame_cropping_rect_top_offset",h(t,0)),c.put("frame_cropping_rect_bottom_offset",h(t,0))),c.put("vui_parameters_present_flag",d(t,1)),c.get("vui_parameters_present_flag")&&function(t){c.put("aspect_ratio_info_present_flag",d(t,1)),c.get("aspect_ratio_info_present_flag")&&(c.put("aspect_ratio_idc",d(t,o)),c.get("aspect_ratio_idc")===p&&(g(t),c.put("sar_width",d(t,u)),g(t),c.put("sar_height",d(t,u)))),c.put("overscan_info_present_flag",d(t,1)),c.get("overscan_info_present_flag")&&c.put("overscan_appropriate_flag",d(t,1)),c.put("video_signal_type_present_flag",d(t,1)),c.get("video_signal_type_present_flag")&&(c.put("video_format",d(t,a)),c.put("video_full_range_flag",d(t,1)),c.put("colour_description_present_flag",d(t,1)),c.get("colour_description_present_flag")&&(c.put("colour_primaries",d(t,o)),c.put("transfer_characteristics",d(t,o)),c.put("matrix_coefficients",d(t,o)))),c.put("chroma_loc_info_present_flag",d(t,1)),c.get("chroma_loc_info_present_flag")&&(c.put("chroma_sample_loc_type_top_field",h(t,0)),c.put("chroma_sample_loc_type_bottom_field",h(t,0))),c.put("timing_info_present_flag",d(t,1)),c.get("timing_info_present_flag")&&(c.put("num_units_in_tick",d(t,f)),c.put("time_scale",d(t,f)),c.put("fixed_frame_rate_flag",d(t,1))),c.put("nal_hrd_parameters_present_flag",d(t,1)),c.get("nal_hrd_parameters_present_flag")&&y(t),c.put("vcl_hrd_parameters_present_flag",d(t,1)),c.get("vcl_hrd_parameters_present_flag")&&y(t),(c.get("nal_hrd_parameters_present_flag")||c.get("vcl_hrd_parameters_present_flag"))&&c.put("low_delay_hrd_flag",d(t,1)),c.put("pic_struct_present_flag",d(t,1)),c.put("bitstream_restriction_flag",d(t,1)),c.get("bitstream_restriction_flag")&&(c.put("motion_vectors_over_pic_boundaries_flag",d(t,1)),c.put("max_bytes_per_pic_denom",h(t,0)),c.put("max_bits_per_mb_denom",h(t,0)))}(t),!0},getSizeInfo:function(){var t=0,e=0;0===c.get("chroma_format_idc")?t=e=0:1===c.get("chroma_format_idc")?t=e=r:c.get("chroma_format_idc")===r?(t=r,e=1):c.get("chroma_format_idc")===a&&(0===c.get("separate_colour_plane_flag")?t=e=1:1===c.get("separate_colour_plane_flag")&&(t=e=0));var n=c.get("pic_width_in_mbs_minus1")+1,i=c.get("pic_height_in_map_units_minus1")+1,_=(r-c.get("frame_mbs_only_flag"))*i,o=0,f=0,p=0,s=0;1===c.get("frame_cropping_flag")&&(o=c.get("frame_cropping_rect_left_offset"),f=c.get("frame_cropping_rect_right_offset"),p=c.get("frame_cropping_rect_top_offset"),s=c.get("frame_cropping_rect_bottom_offset"));var l=n*u*(_*u);return{width:n*u-t*(o+f),height:_*u-e*(r-c.get("frame_mbs_only_flag"))*(p+s),decodeSize:l}},getSpsValue:function(t){return c.get(t)},getCodecInfo:function(){return c.get("profile_idc").toString(u)+(c.get("profile_compatibility")<15?"0"+c.get("profile_compatibility").toString(u):c.get("profile_compatibility").toString(u))+c.get("level_idc").toString(u)}},new l}}]);