<template>
  <div class="riskDynamic">
    <header>
      <!-- vvvvvv ==={{this.$store.state.login.user.user_type}} -->
      <div>
        <el-tabs v-model="activeName" class="tab">
          <el-tab-pane label="风险动态研判" name="1">
            <div class="riskDynamicBox">
              <div class="riskDynamicL">
                <!-- <h3>湖北省</h3> -->
                <div class="riskDynamicLevel" v-loading="loading">
                  <div
                    class="riskDynamicLevelItem"
                    v-for="(item, index) of LevelData"
                    :key="index"
                  >
                    <div :class="['icon', 'icon' + item.riskLevelCode]"></div>
                    <div class="itemcon">
                      <p>{{ item.riskLevelName }}</p>
                      <div>
                        <span>{{ item.riskCount }}</span
                        >家
                      </div>
                    </div>
                  </div>
                </div>
                <div class="map">
                  <!-- <img src="../../../../../static/img/map.png" /> -->
                  <!-- <mapEchart></mapEchart> -->
                  <sctterMap @distCodeFn="showDistCodeFn"></sctterMap>
                </div>
              </div>
              <div class="riskDynamicR">
                <el-tabs v-model="activeTabClass">
                  <el-tab-pane label="风险排名" name="riskRank">
                    <riskRank
                      ref="riskRank"
                      :riskRankDataPro="riskRankData"
                    ></riskRank>
                  </el-tab-pane>
                  <!-- <el-tab-pane label="企业分布" name="enterpriseDistribution">
            <enterpriseDistribution ref="enterpriseDistribution"></enterpriseDistribution>
          </el-tab-pane> -->
                  <el-tab-pane label="风险图谱" name="riskEchart">
                    <riskEchartR
                      :distCodePro="distCode"
                      ref="riskEchart"
                    ></riskEchartR>
                  </el-tab-pane>
                </el-tabs>

                <br />

                <div class="titlList">较大风险及以上企业清单</div>
                <div class="jiaoList">
                  <div class="header">
                    <div class="order"><span>序号</span></div>
                    <div class="title">企业名称</div>
                    <div class="ping">风险评分</div>
                    <div class="level">风险等级</div>
                  </div>
                  <div
                    style="min-height: 282px"
                    v-if="jiaoData.length > 0"
                    v-loading="loading"
                  >
                    <div
                      class="jiaoItem"
                      v-for="(el, index) of jiaoData"
                      :key="index"
                      @click="clickDetail(el)"
                    >
                      <div class="order">
                        <span :class="['topHost' + index]">{{ el.index }}</span>
                      </div>
                      <div class="title">{{ el.enterpName }}</div>
                      <div class="ping">{{ el.riskValue }}</div>
                      <div class="level">{{ el.riskLevelName }}</div>
                    </div>
                  </div>
                  <div
                    style="
                      height: 364px;
                      line-height: 364px;
                      text-align: center;
                    "
                    v-else
                  >
                    暂无数据
                  </div>
                  <div
                    class="pagination"
                    style="margin-top: 10px; text-align: center"
                  >
                    <el-pagination
                      @current-change="handleCurrentChange"
                      :current-page.sync="currentPage"
                      :page-size="pageSize"
                      :pager-count="5"
                      layout="total, prev, pager, next"
                      background
                      :total="total"
                    >
                    </el-pagination>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="风险配置管理" name="2">
            <riskDynamicConfigure></riskDynamicConfigure>
          </el-tab-pane>

          <el-tab-pane label="企业数据填报统计" name="3">
            <fillReport></fillReport>
          </el-tab-pane>
        </el-tabs>
      </div>

      <!--
       <div class="breadcrumb" v-else>
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" /> 风险动态研判
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      -->
    </header>
  </div>
</template>
<script>
import {
  queryPageRiskPage,
  queryRiskAreaByDistCode, //区域查询企业风险  风险排名
  queryDistCodeCount, //区域查询风险等级企业统计
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");

import riskRank from "./riskRank.vue";
import riskEchart from "./riskEchart.vue";
import sctterMap from "./sctterMap.vue";
import riskEchartR from "./riskEchartR.vue";
import riskDynamicConfigure from "./riskDynamicConfigure.vue";
import fillReport from "@/components/feature/riskAssessment/riskDynamic/fillReport/fillReport.vue";

export default {
  components: {
    riskRank,
    riskEchart,
    sctterMap,
    riskEchartR,
    riskDynamicConfigure,
    fillReport,
  },
  data() {
    return {
      activeName: "1",
      distCode: this.$store.state.login.userDistCode,
      loading: false,
      currentPage: 1,
      pageSize: 6,
      total: 0,
      pageConunt: 3,
      riskRankData: [],
      activeTabClass: "riskRank", //tab切换
      LevelData: [],
      jiaoData: [
        {
          title: "武汉恒基达鑫国际化工仓储有限公司",
          ping: "87",
          level: "重大",
        },
      ],
    };
  },

  methods: {
    showDistCodeFn(val) {
      this.distCode = val;
    },
    clickDetail(el) {
      this.$router.push({ path: "/gardenEnterpriseManagement/entManagement" });
      this.$store.commit("login/updataActiveName", "riskDynamics");
      this.$store.commit("controler/updateEntId", el.enterpId);
    },
    getInitData(distCode) {
      this.loading = true;
      queryDistCodeCount({
        distCode: distCode,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          this.LevelData = res.data.data;
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
    //区域风险排名
    getRiskAreaByDistCodeData(distCode) {
      this.loading = true;
      queryRiskAreaByDistCode({
        distCode: distCode,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          this.riskRankData = res.data.data;
          this.riskRankData.forEach((element, index) => {
            if (element.areaCode == "422800") {
              this.riskRankData[index].areaName = "恩施";
            }
            if (element.areaCode == "429021") {
              this.riskRankData[index].areaName = "神农架";
            }
          });
          this.riskRankData.push({ areaName: "", riskAreaLevelName: "" });
          console.log(this.riskRankData);
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
    handleCurrentChange(val) {
      this.getRiskPage(this.distCode);
    },
    //企业
    getRiskPage(distCode) {
      this.loading = true;
      queryPageRiskPage({
        distCode: distCode,
        // nowPage: this.currentPage,
        nowPage: this.currentPage,
        pageSize: this.pageSize,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          const nowPage = res.data.data.nowPage;
          const pageSize = res.data.data.pageSize;
          res.data.data.list.forEach((item) => {
            item.index = 0;
          });
          var aa = res.data.data.list.forEach((item, i) => {
            if (i == 0) {
              item.index = (nowPage + 1) * pageSize - pageSize + 1;
            } else {
              item.index = 1 + res.data.data.list[i - 1].index;
            }
          });
          this.jiaoData = res.data.data.list;
          this.total = res.data.data.total;
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getInitData(this.distCode);
    this.getRiskAreaByDistCodeData(this.distCode);
    this.getRiskPage(this.distCode);
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    distCode: {
      handler(newVal, oldVal) {
        this.getInitData(newVal);
        this.getRiskAreaByDistCodeData(newVal);
        this.getRiskPage(newVal);
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.riskDynamic {
  header {
    background-color: #fff;
    overflow: hidden;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  .riskDynamicBox {
    display: flex;
    .riskDynamicL {
      width: calc(100% - 400px);
      padding: 0 50px 0 0;
      box-sizing: border-box;
      .map img {
        width: 100%;
      }
      .riskDynamicLevel {
        display: flex;
        justify-content: space-between;
        .riskDynamicLevelItem {
          display: flex;
          width: 23%;
          background: #f7f8fa;
          align-items: center;
          padding: 20px 10px 20px 20px;
          .itemcon {
            padding: 0 0 0 20px;
            p {
              margin: 0 0 5px 0;
              font-size: 18px;
            }
            span {
              font-size: 18px;
            }
          }
        }

        .icon {
          width: 40px;
          height: 40px;
        }
        .icon1 {
          background: url("../../../../../static/img/lev1.png") no-repeat center;
        }
        .icon2 {
          background: url("../../../../../static/img/lev2.png") no-repeat center;
        }
        .icon3 {
          background: url("../../../../../static/img/lev3.png") no-repeat center;
        }
        .icon4 {
          background: url("../../../../../static/img/lev4.png") no-repeat center;
        }
      }
    }
    .riskDynamicR {
      width: 500px;
      // border: 1px solid red;
    }
  }
  .jiaoList {
    .header {
      display: flex;
      height: 40px;
      line-height: 40px;
      background: #f7f8fa;
      .ping,
      .level {
        color: #000;
      }
    }

    .order {
      width: 10%;
    }
    .title {
      width: 50%;
    }
    .ping,
    .level {
      width: 20%;
      text-align: center;
    }
    .jiaoItem {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #d9d9d9;
      padding: 10px 0;
      cursor: pointer;
      .order {
        span {
          min-width: 26px;
          height: 26px;
          border-radius: 3px;
          background: #ebf1fd;
          color: #326eff;
          display: inline-block;
          text-align: center;
          line-height: 26px;
        }
      }
      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    .jiaoItem:hover .title {
      color: #409eff;
    }
    .jiaoItem .order span.topHost0 {
      background: #f8e9e8;
      color: #cb5f5c;
    }
    .jiaoItem .order span.topHost1 {
      background: #fef3dc;
      color: #faad14;
    }
    .jiaoItem .order span.topHost2 {
      background: #fef5da;
      color: #f8d253;
    }
  }

  .titlList {
    font-size: 14px;
    color: #252525;
    position: relative;
    padding: 0 0 0 15px;
  }
  .titlList::before {
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    height: 20px;
    width: 4px;
    background: #3977ea;
  }
}
</style>
