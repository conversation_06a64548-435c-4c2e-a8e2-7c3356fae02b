<template>
  <div class="equipmentFacilities">
    <div>
      <div class="seach-part">
        <div class="l">
          <!-- <el-select v-model="queryParams.orgCode" placeholder="请选择填报企业">
            <el-option
              v-for="dict in whpEntList"
              :key="dict.orgCode"
              :label="dict.orgName"
              :value="dict.orgCode"
            />
          </el-select> -->
         

          <!-- <el-select size="medium"
                     v-model="queryParams.isBlacklist"
                     placeholder="请选择是否黑名单承包商">
            <el-option label="是"
                       :value="1"></el-option>
            <el-option label="否"
                       :value="0"></el-option>
          </el-select> -->

         <div>
           <el-autocomplete popper-class="my-autocomplete"
                           v-model="queryParams.orgName"
                           :fetch-suggestions="querySearch"
                           placeholder="请选择/输入填报企业"
                           clearable
                           @clear="clearSensororgCode()"
                           @select="handleSelect"
                           size="medium"
                           v-if="$store.state.login.user.user_type == 'gov'">
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete>

          <el-input v-model.trim="queryParams.keyword"
                    placeholder="请输入承包商名称"
                    clearable
                    size="medium"
                    @keyup.enter.native="handleQuery" />

            <el-button type="primary"
                     size="medium"
                     @click="handleQuery">查询</el-button>
          <CA-button type="primary"
                     size="medium"
                     plain
                     @click="exportExcel">导出</CA-button>


         </div>
        
          <el-button type="primary"
                     @click="handleAdd"
                    
                     size="medium"
                     v-if="$store.state.login.user.user_type == 'ent'">新增</el-button>
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>承包商管理列表</h2>
        </div>
        <div>
          <div class="table">
            <el-table :data="safetyTrainingList"
                      v-loading="loading"
                      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                      border
                      style="width: 100%"
                      ref="multipleTable"
                      @selection-change="handleSelectionChange">
              <el-table-column label="承包商名称"
                               align="center"
                               prop="name"   
                               min-width="250"                             
                              />
              <el-table-column label="统一社会信用代码"
                               align="center"
                               prop="unifiedCreditCode" 
                               width="250">
              </el-table-column>
              <el-table-column label="承包商负责人"
                               align="center"
                               prop="principal" />
              <el-table-column label="负责人联系方式"
                               align="center"
                               prop="principalContact">
              </el-table-column>
              <el-table-column label="填报企业"
                               align="center"
                               prop="orgName"
                               min-width="250">
              </el-table-column>
              <el-table-column label="操作"
                               align="center"
                               width="200"
                               class-name="small-padding fixed-width">
                <template slot-scope="scope">
                  <el-button round
                             size="mini"
                             @click="handleUpdate(scope.row)"
                             v-if="$store.state.login.user.user_type == 'ent'">编辑</el-button>
                  <el-button round
                             size="mini"
                             @click="handleDetail(scope.row)">查看</el-button>
                  <el-button round
                             size="mini"
                             class="del"
                             @click="handleDelete(scope.row)"
                             v-if="$store.state.login.user.user_type == 'ent'">删除</el-button>
                  <!-- <el-button
                        round
                        size="mini"
                        class="del"
                        @click="handleDelete(scope.row)"
                    >移入黑名单</el-button> -->
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination @size-change="handleSizeChange"
                           @current-change="handleCurrentChange"
                           :current-page.sync="queryParams.nowPage"
                           background
                           layout="total, prev, pager, next"
                           :total="total"
                           v-if="total != 0">
            </el-pagination>
          </div>

          <!-- 添加或修改承包商对话框 -->
          <el-dialog :title="title"
                     :visible.sync="open"                   
                     width="900px"
                     :close-on-click-modal="false"
                     :append-to-body="true"
                     top="1%">
            <el-form ref="form"
                     :model="form"
                     :rules="rules"
                     :disabled="isDisabled"
                     label-width="218px"
                     :hide-required-asterisk="isDisabled">
              <div class="form_item">
                <!-- <h2 class="form_title">基本信息</h2> -->
                <div class="form_main">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="承包商名称"
                                    prop="name">
                        <el-input v-model.trim="form.name" maxlength="30"
                                  placeholder="请输入承包商名称" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="统一信用代码"
                                    prop="unifiedCreditCode">
                        <el-input v-model.trim="form.unifiedCreditCode"
                                  placeholder="请输入统一信用代码" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <!-- :rules="[{max: 10, message: '法人不超过20个字符',trigger: 'change'}]" -->
                      <el-form-item label="承包商法人">
                        <el-input v-model.trim="form.legalPerson" maxlength="10"
                                  placeholder="请输入承包商法人" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="法人联系方式"
                                    prop="legalPersonContact">
                        <el-input v-model.trim="form.legalPersonContact"
                                  placeholder="请输入法人联系方式" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="承包商进场人数"
                                    prop="visitorsNum">
                        <el-input v-model.trim="form.visitorsNum" maxlength="6"
                                  placeholder="请输入进场人数" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="参与作业类型"
                                    prop="workType">
                        <el-select v-model="form.workType"
                                   placeholder="请选择参与作业类型">
                          <el-option v-for="dict in trainingFormOptions"
                                     :key="dict.id"
                                     :label="dict.label"
                                     :value="dict.id" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="承包商负责人" prop="principal">
                        <el-input v-model.trim="form.principal" maxlength="10" placeholder="请输入承包商负责人" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="负责人联系方式" prop="principalContact">
                        <el-input v-model.trim="form.principalContact" placeholder="请输入负责人联系方式" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="承包商安全负责人" prop="safetyPrincipal">
                        <el-input v-model.trim="form.safetyPrincipal" maxlength="10" placeholder="请输入承包商安全负责人" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="安全负责人联系方式" prop="safetyPrincipalContact">
                        <el-input v-model.trim="form.safetyPrincipalContact" placeholder="请输入安全负责人联系方式" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="是否签订安全管理协议"  prop="isSafetyProtocol">
                        <el-select v-model="form.isSafetyProtocol">
                          <el-option label="是" value="1"></el-option>
                          <el-option label="否" value="0"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="入场人员是否进行安全培训教育" prop="isSafetyEducation">
                        <el-select v-model="form.isSafetyEducation">
                          <el-option label="是" value="1"></el-option>
                          <el-option label="否" value="0"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="注册地址" prop="registeredAddress">
                        <el-input v-model.trim="form.registeredAddress" maxlength="60" show-word-limit placeholder="请输入注册地址" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="经营范围">
                        <el-input v-model.trim="form.businessScope" maxlength="60" show-word-limit placeholder="请输入经营范围" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="营业期限">
                        <el-date-picker v-model="value1"
                          type="daterange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          @change="searchTime"
                          value-format="yyyy-MM-dd" 
                          :picker-options="pickerOptions0">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="营业执照附件上传">
                        <!-- <fileUpload v-model="form.licenseAttachId" :disabled="disabled" /> -->
                        <AttachmentUpload :attachmentlist="form.licenseAttachIdList"
                          :limit="1"
                          type="img"
                          v-bind="{}"
                          :editabled="isDisabled"></AttachmentUpload>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-form>
            <div v-if="!isDisabled" slot="footer" class="dialog-footer">
              <el-button type="primary" @click="submitForm">确 定</el-button>
              <el-button @click="cancel">取 消</el-button>
            </div>
          </el-dialog>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getContractorManagementListData,
  addContractorManagementData,
  editContractorManagemenData,
  detailContractorManagemenData,
  deleteContractorManagemenData,
  getCompanyParticularJobData,
  exportContractorManagemen
} from '@/api/contractorManagement'
// import ImageUpload from '@/components/common/packages/ImageUpload';
// import FileUpload from '@/components/common/packages/FileUpload';
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
import { getDistrictUser } from '@/api/entList'
import { getSearchArr } from '@/api/entList.js'
import { getEnterprise } from '@/api/reportedList'
import { parseTime } from '@/utils/index'
import { createNamespacedHelpers } from 'vuex'
const { mapState: mapStateLogin } = createNamespacedHelpers('login')

export default {
  name: 'equipmentFacilities',
  components: {
    //   ImageUpload,
    //   FileUpload
    AttachmentUpload
  },
  props: {
    entInfoData: {
      type: Object
    }
  },
  data() {
    var valiNumberPass1 = (rule, value, callback) => {
      // 包含小数的数字
      const reg = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g
      if (value === '') {
        callback(new Error('请输入内容'))
      } else if (!reg.test(value)) {
        callback(new Error('请输入数字'))
      } else {
        callback()
      }
    }
    var valiNumberPass2 = (rule, value, callback) => {
      // 正整数
      const reg = /^[+]{0,1}(\d+)$/g
      if (value === '') {
        callback(new Error('请输入承包商进场人数'))
      } else if (!reg.test(value)) {
        callback(new Error('请输入0及0以上的整数'))
      } else {
        callback()
      }
    }
    // 手机号验证
    var checkPhone1 = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/
      if (!value) {
        return callback(new Error('法人联系电话不能为空'))
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error('请输入数字值'))
        } else {
          if (phoneReg.test(value)) {
            callback()
          } else {
            callback(new Error('联系电话格式不正确'))
          }
        }
      }, 100)
    }
    var checkPhone2 = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/
      if (!value) {
        return callback(new Error('负责人联系方式不能为空'))
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error('请输入数字值'))
        } else {
          if (phoneReg.test(value)) {
            callback()
          } else {
            callback(new Error('联系电话格式不正确'))
          }
        }
      }, 100)
    }
    var checkPhone3 = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/
      if (!value) {
        return callback(new Error('安全负责人联系方式不能为空'))
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error('请输入数字值'))
        } else {
          if (phoneReg.test(value)) {
            callback()
          } else {
            callback(new Error('联系电话格式不正确'))
          }
        }
      }, 100)
    }
    return {
      pickerOptions0: { // 禁止选择今天以前的日期
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      actionType: '', // 操作类型，区分新增和修改，add-新增，edit-修改
      // 表单禁用
      isDisabled: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 承包商表格数据
      safetyTrainingList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 承包商形式字典
      trainingFormOptions: [],
      // 培训形式字典
      whpEntList: [],
      // 查询参数
      queryParams: {
        isBlacklist: '',
        keyword: '',
        nowPage: 1,
        orgCode: '',
        orgName: '',
        pageSize: 10
      },
      value1:[] ,//['2021-01-02','2021-01-02']
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          {
            required: true,
            // max: 20,
            message: '请输入承包商名称',
            trigger: 'blur'
          }
        ],
        unifiedCreditCode: [
          {
            required: true,
            message: '请输入统一社会信用代码',
            trigger: 'blur'
          },
          {
            pattern: /^([0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}|[1-9]\d{14})$/,message: '请输入正确的统一社会信用代码'
          }
        ],
        legalPersonContact: [
          { required: true, validator: checkPhone1, trigger: 'blur' }
        ],
        visitorsNum: [
          { required: true, validator: valiNumberPass2, trigger: 'blur' }
        ],
        workType: [
          { required: true, message: '请选择参与作业类型', trigger: 'change' }
        ],
        principal: [
          { required: true, max: 20, message: '请输入负责人且不超过20个字', trigger: 'blur' }
        ],
        principalContact: [
          { required: true, validator: checkPhone2, trigger: 'blur' }
        ],
        safetyPrincipal: [
          {
            required: true,
            max: 20,
            message: '请输入安全负责人且不超过20个字',
            trigger: 'blur'
          }
        ],
        safetyPrincipalContact: [
          { required: true, validator: checkPhone3, trigger: 'blur' }
        ],
        isSafetyProtocol: [
          {
            required: true,
            message: '请选择是否签订安全管理协议',
            trigger: 'change'
          }
        ],
        isSafetyEducation: [
          {
            required: true,
            message: '请选择是否通过安全教育培训',
            trigger: 'change'
          }
        ],
        registeredAddress: [
          {
            required: true, message: '请输入注册地址', trigger: 'blur'
          }
        ]
      },
      // 用户信息
      role: {},
      btnPermissList: [], // 按钮权限
      orgType: '2'
    }
  },
  created() {
    // 判断是企业还是政务登录
    // this.queryParams.id = this.$route.query.id
    // this.role = JSON.parse(sessionStorage.getItem('role') || '')
    // this.orgType = this.role.orgType
    // if (this.orgType == '2') {
    //   // 当前用户是企业
    //   this.queryParams.orgCode = this.role.orgCode
    // }
    if (this.$store.state.login.user.user_type == 'ent') {
      this.queryParams.orgCode = this.$store.state.login.user.org_code
    }
    // this.btnPermissionsFun()
    this.getList()
    // this.getDicts('trainingForm').then(response => {
    //   this.trainingFormOptions = response.data
    // })
    this.getCompanyParticularJobListData()
    // getEnterprise({ enterpriseName: '' }).then(res => {
    //   // console.log(res)
    //   if (res.status === 200) {
    //     this.whpEntList = res.data
    //   }
    // })
  },
  methods: {
    // 获取按钮权限
    // btnPermissionsFun() {
    //   btnPer({
    //     menuId: JSON.parse(sessionStorage.getItem('currentMenu')).id,
    //     userId: JSON.parse(sessionStorage.getItem('role')).userId
    //   })
    //     .then(res => {
    //       this.btnPermissList = res.data
    //     })
    //     .catch(err => {
    //       console.log(err)
    //     })
    // },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || '', cb)
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then(res => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data)
            } else {
              cb([])
            }
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.orgCode = item.enterpId
      this.queryParams.orgName = item.enterpName
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0])
        let dataTime1 = parseTime(date1, '{y}-{m}-{d}')
        let date2 = new Date(value[1])
        let dataTime2 = parseTime(date2, '{y}-{m}-{d}')
        this.form.operatingPeriodStart = dataTime1
        this.form.operatingPeriodEnd = dataTime2
      } else {
        this.value1 = []
        this.form.operatingPeriodStart = ''
        this.form.operatingPeriodEnd = ''
      }
    },
    getCompanyParticularJobListData() {
      getCompanyParticularJobData({}).then(response => {
        this.trainingFormOptions = response.data.data
      })
    },
    /** 查询承包商列表 */
    getList() {
      if (this.$store.state.login.user.user_type == 'ent') {
        this.queryParams.orgCode = this.$store.state.login.user.org_code
      }
      this.loading = true
      getContractorManagementListData(this.queryParams).then(response => {
        this.safetyTrainingList = response.data.data.list
        this.total = response.data.data.total
        this.loading = false
        this.actionType = '';
      })
    },
    // 培训形式字典翻译
    // trainingFormFormat(row, column) {
    //   return this.selectDictLabel(this.trainingFormOptions, row.trainingForm)
    // },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        businessScope: null,
        isSafetyEducation: null,
        isSafetyProtocol: null,
        // jobCount: 0,
        legalPerson: null,
        legalPersonContact: null,
        licenseAttachId: null,
        licenseAttachIdList: [],
        name: null,
        operatingPeriodEnd: null,
        operatingPeriodStart: null,
        principal: null,
        principalContact: null,
        projectCount: 0,
        registeredAddress: null,
        safetyPrincipal: null,
        safetyPrincipalContact: null,
        unifiedCreditCode: null,
        violationCount: 0,
        visitorsNum: 0,
        workType: ''
      }
      this.value1 = [];
      if (this.$refs['form'] != undefined) {
        this.$refs['form'].resetFields()
      }
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.nowPage = 1
      this.getList()
    },
    /** 重置按钮操作 */
    // resetQuery() {
    //   this.resetForm('queryForm')
    //   this.handleQuery()
    // },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.safetyTrainingCode)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.actionType = 'add';
      this.isDisabled = false
      this.reset()
      this.open = true
      this.title = '添加承包商信息'
    },
    /**
     * 查看按钮操作
     */
    handleDetail(row) {
      this.reset()
      this.isDisabled = true
      const safetyTrainingCode = row.id
      this.value1=[];
      detailContractorManagemenData({ id: safetyTrainingCode }).then(
        response => {
          this.form = response.data.data
          this.open = true
          this.title = '查看承包商信息'
          this.value1.push(response.data.data.operatingPeriodStart,response.data.data.operatingPeriodEnd)        
        }
      )
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.actionType = 'edit';
      this.isDisabled = false
      this.reset()
      const safetyTrainingCode = row.id
      this.value1=[];
      detailContractorManagemenData({ id: safetyTrainingCode }).then(
        response => {
          this.form = response.data.data
          this.value1.push(response.data.data.operatingPeriodStart,response.data.data.operatingPeriodEnd)
          if (!this.form.licenseAttachIdList || this.form.licenseAttachIdList == null) {
            this.form.licenseAttachIdList = [];
          }
          this.open = true
          this.title = '修改承包商'
        }
      )
    },
    debounce(func, delay) {
      const context = this // this指向发生变化，需要提出来
      const args = arguments
      return (function() {
        if (context.timeout) {
          clearTimeout(context.timeout)
        }
        context.timeout = setTimeout(() => {
          func.apply(context, args)
        }, delay)
      })()
    },
    submitForm() {
      const that = this
      that.debounce(() => {
        this.$refs['form'].validate(valid => {
          if (valid) {
            if (this.actionType === 'edit') {
              editContractorManagemenData(this.form).then(response => {
                // this.msgSuccess('修改成功')
                if (response.data.status === 200) {
                  this.open = false
                  this.$message({
                    type: 'success',
                    message: '修改成功'
                  })
                  this.getList()
                } else {
                  this.$message.error(response.data.msg || '修改失败~');
                }
              })
            } else {
              addContractorManagementData(this.form).then(response => {
                // this.msgSuccess('新增成功')
                if (response.data.status === 200) {
                  this.open = false
                  this.$message({
                    type: 'success',
                    message: '新增成功'
                  })
                  this.getList()
                } else {
                  this.$message.error(response.data.msg || '新增失败~');
                }
              })
            }
          }
        })
      }, 500)
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const safetyTrainingCodes = row.id
      this.$confirm('确认删除该数据？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(function() {
          return deleteContractorManagemenData({ id: safetyTrainingCodes })
        })
        .then(() => {
          if (this.safetyTrainingList.length === 1 && this.queryParams.nowPage !== 1) {
                this.queryParams.nowPage--
              }
          this.getList()
          //   this.msgSuccess('删除成功')
          this.$message({
            type: 'success',
            message: '删除成功'
          })
        })
    },
    /** 导出按钮操作 */
    exportExcel() {
      const queryParams = this.queryParams
      exportContractorManagemen(queryParams).then(response => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        const blob = new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '承包商管理' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    },
    clearSensororgCode() {
      this.queryParams.orgCode = ''
    },
    handleCurrentChange(val) {
      // debugger
      this.queryParams.nowPage = val
      this.getList()
    },
    handleSizeChange() {}
  }
}
</script>
<style lang="scss" scoped>
.equipmentFacilities {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    width: 100%;
    font-weight: 600;
    padding-bottom: 10px;
    margin-bottom: 0px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content:space-between;
      align-items: center;
      > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
/deep/ .l .el-input {
  width: 215px;
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
/deep/.el-dialog {
  height: 700px;
  overflow: hidden;
  .el-dialog__body {
    height: 80%;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
/deep/.el-range-separator {
  width: 7%;
}
</style>
