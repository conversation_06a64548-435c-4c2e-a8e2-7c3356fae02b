!function(e){var t={};function r(n){if(t[n])return t[n].exports;var a=t[n]={i:n,l:!1,exports:{}};return e[n].call(a.exports,a,a.exports,r),a.l=!0,a.exports}r.m=e,r.c=t,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="",r(r.s=0)}([function(e,t,r){"use strict";r.r(t);var n={log:function(){},error:function(){},count:function(){},info:function(){}};(function(){function e(){}e.createFromElementId=function(t){for(var r=document.getElementById(t),n="",a=r.firstChild;a;)3===a.nodeType&&(n+=a.textContent),a=a.nextSibling;var o=new e;return o.type=r.type,o.source=n,o},e.createFromSource=function(t,r){var n=new e;return n.type=t,n.source=r,n}})(),function(){function e(e){this.gl=e,this.program=this.gl.createProgram()}e.prototype={attach:function(e){this.gl.attachShader(this.program,e.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(e){return this.gl.getAttribLocation(this.program,e)},setMatrixUniform:function(e,t){var r=this.gl.getUniformLocation(this.program,e);this.gl.uniformMatrix4fv(r,!1,t)}}}(),function(){var e=null;function t(e,t,r){this.gl=e,this.size=t,this.texture=e.createTexture(),e.bindTexture(e.TEXTURE_2D,this.texture),this.format=r||e.LUMINANCE,e.texImage2D(e.TEXTURE_2D,0,this.format,t.w,t.h,0,this.format,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)}t.prototype={fill:function(e,t){var r=this.gl;r.bindTexture(r.TEXTURE_2D,this.texture),t?r.texSubImage2D(r.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,r.UNSIGNED_BYTE,e):r.texImage2D(r.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,r.UNSIGNED_BYTE,e)},bind:function(t,r,n){var a=this.gl;e||(e=[a.TEXTURE0,a.TEXTURE1,a.TEXTURE2]),a.activeTexture(e[t]),a.bindTexture(a.TEXTURE_2D,this.texture),a.uniform1i(a.getUniformLocation(r.program,n),t)}}}();function a(){var e=[1,2,4,8,16,32,64,128,256,512,1024,2048,4096,8192,16384],t=function(e,t,r){var n=0,a=0;for(n=0;n<r&&!(e<t[a]);n++)a++;return n},r=function(r,n){var a,o,i,u,s;return i=(o=t(a=r>0?r:8191&-r,e,15)-6)+(n>>6&15)-13,u=(0===a?32:o>=0?a>>o:a<<-o)*(n&parseInt("077",8))+48>>4,s=i>=0?u<<i&32767:u>>-i,(r^n)<0?-s:s};this.g726InitState=function(){var e={},t=0;for(e.pp=new Array(2),e.zp=new Array(6),e.pk=new Array(2),e.dq=new Array(6),e.sr=new Array(2),e.yl=34816,e.yu=544,e.dms=0,e.dml=0,e.ppp=0,t=0;t<2;t++)e.pp[t]=0,e.pk[t]=0,e.sr[t]=32;for(t=0;t<6;t++)e.zp[t]=0,e.dq[t]=32;return e.td=0,e},this.predictorZero=function(e){var t=0,n=0;for(n=r(e.zp[0]>>2,e.dq[0]),t=1;t<6;t++)n+=r(e.zp[t]>>2,e.dq[t]);return n},this.predictorPole=function(e){return r(e.pp[1]>>2,e.sr[1])+r(e.pp[0]>>2,e.sr[0])},this.stepSize=function(e){var t=0,r=0,n=0;return e.ppp>=256?e.yu:(t=e.yl>>6,r=e.yu-t,n=e.ppp>>2,r>0?t+=r*n>>6:r<0&&(t+=r*n+63>>6),t)},this.quantize=function(r,n,a,o){var i,u,s;return i=Math.abs(r),u=t(i>>1,e,15),s=t((u<<7)+(i<<7>>u&127)-(n>>2),a,o),r<0?1+(o<<1)-s:0===s?1+(o<<1):s},this.reconstruct=function(e,t,r){var n,a=0;return(n=t+(r>>2))<0?e?-32768:0:(a=128+(127&n)<<7>>14-(n>>7&15),e?a-32768:a)},this.update=function(r,n,a,o,i,u,s,c){var p,f,l,d,h,y=0,g=0,m=0,v=0,w=0,T=0,E=0,b=0;if(h=s<0?1:0,g=32767&i,p=c.yl>>15,d=c.yl>>10&31,l=(f=p>9?31744:32+d<<p)+(f>>1)>>1,b=0===c.td?0:g<=l?0:1,c.yu=n+(a-n>>5),c.yu<544?c.yu=544:c.yu>5120&&(c.yu=5120),c.yl+=c.yu+(-c.yl>>6),1===b)c.pp[0]=0,c.pp[1]=0,c.zp[0]=0,c.zp[1]=0,c.zp[2]=0,c.zp[3]=0,c.zp[4]=0,c.zp[5]=0,v=0;else for(T=h^c.pk[0],v=c.pp[1]-(c.pp[1]>>7),0!==s&&((E=T?c.pp[0]:-c.pp[0])<-8191?v-=256:v+=E>8191?255:E>>5,h^c.pk[1]?v<=-12160?v=-12288:v>=12416?v=12288:v-=128:v<=-12416?v=-12288:v>=12160?v=12288:v+=128),c.pp[1]=v,c.pp[0]-=c.pp[0]>>8,0!==s&&(0===T?c.pp[0]+=192:c.pp[0]-=192),w=15360-v,c.pp[0]<-w?c.pp[0]=-w:c.pp[0]>w&&(c.pp[0]=w),y=0;y<6;y++)c.zp[y]-=5===r?c.zp[y]>>9:c.zp[y]>>8,32767&i&&((i^c.dq[y])>=0?c.zp[y]+=128:c.zp[y]-=128);for(y=5;y>0;y--)c.dq[y]=c.dq[y-1];return 0===g?c.dq[0]=i>=0?32:64544:(m=t(g,e,15),c.dq[0]=i>=0?(m<<6)+(g<<6>>m):(m<<6)+(g<<6>>m)-1024),c.sr[1]=c.sr[0],0===u?c.sr[0]=32:u>0?(m=t(u,e,15),c.sr[0]=(m<<6)+(u<<6>>m)):u>-32768?(m=t(g=-u,e,15),c.sr[0]=(m<<6)+(g<<6>>m)-1024):c.sr[0]=64544,c.pk[1]=c.pk[0],c.pk[0]=h,c.td=1===b?0:v<-11776?1:0,c.dms+=o-c.dms>>5,c.dml+=(o<<2)-c.dml>>7,1===b?c.ppp=256:n<1536?c.ppp+=512-c.ppp>>4:1===c.td?c.ppp+=512-c.ppp>>4:Math.abs((c.dms<<2)-c.dml)>=c.dml>>3?c.ppp+=512-c.ppp>>4:c.ppp+=-c.ppp>>4,c}}var o=function(e){var t=132,r=128,n=15,a=4,o=112;function i(e){var i=0,u=~e;return i=((u&n)<<3)+t,i<<=(u&o)>>a,u&r?t-i:i-t}function u(e){var t,i=0;switch(i=((e^=85)&n)<<4,t=(e&o)>>a){case 0:i+=8;break;case 1:i+=264;break;default:i+=264,i<<=t-1}return e&r?i:-i}function s(){}return s.prototype={decode:function(t){var r=new Uint8Array(t),n=new Int16Array(r.length),a=0;if("G.711A"==e)for(a=0;a<r.length;a++)n[a]=u(r[a]);else if("G.711Mu"==e)for(a=0;a<r.length;a++)n[a]=i(r[a]);var o=new Float32Array(n.length);for(a=0;a<n.length;a++)o[a]=n[a]/Math.pow(2,15);return o}},new s};function i(){var e=3,t=[116,365,365,116],r=[-704,14048,14048,-704],n=[0,3584,3584,0],o={},i=null;function u(a,u){var s,c,p,f,l,d,h,y=a,g=u;switch(y=y&=3,c=(s=i.predictorZero(o))>>1,p=s+i.predictorPole(o)>>1,f=i.stepSize(o),h=(d=(l=i.reconstruct(2&y,t[y],f))<0?p-(16383&l):p+l)-p+c,o=i.update(2,f,r[y],n[y],l,d,h,o),g){case e:return d<<2;default:return-1}}function s(){i=new a,o=i.g726InitState()}return s.prototype={decode:function(t){for(var r=new Int16Array(4*t.length),n=0,a=0;n<t.length;n++){var o=null;o=u(t[n]>>6,e),r[a]=65280&o,a++,o=u(t[n]>>4,e),r[a]=65280&o,a++,o=u(t[n]>>2,e),r[a]=65280&o,a++,o=u(t[n],e),r[a]=65280&o,a++}return r}},new s}function u(){var e=3,t=[-2048,135,273,373,373,273,135,-2048],r=[-128,960,4384,18624,18624,4384,960,-128],n=[0,512,1024,3584,3584,1024,512,0],o={},i=null;function u(a,u){var s,c,p,f,l,d,h,y=a,g=u;switch(y&=7,c=(s=i.predictorZero(o))>>1,p=s+i.predictorPole(o)>>1,f=i.stepSize(o),h=(d=(l=i.reconstruct(4&y,t[y],f))<0?p-(16383&l):p+l)-p+c,o=i.update(3,f,r[y],n[y],l,d,h,o),g){case e:return d<<2;default:return-1}}function s(){i=new a,o=i.g726InitState()}return s.prototype={decode:function(t){for(var r=new Int16Array(8*t.length/3),n=0,a=0;n<t.length-3;n+=3){var o=null;o=u(t[n]>>5,e),r[a]=65280&o,a++,o=u(t[n]>>2,e),r[a]=65280&o,a++,o=u(t[n]<<1|t[n+1]>>7,e),r[a]=65280&o,a++,o=u(t[n+1]>>4,e),r[a]=65280&o,a++,o=u(t[n+1]>>1,e),r[a]=65280&o,a++,o=u(t[n+1]<<2|t[n+2]>>6,e),r[a]=65280&o,a++,o=u(t[n+2]>>3,e),r[a]=65280&o,a++,o=u(t[n+2]>>0,e),r[a]=65280&o,a++}return r}},new s}function s(){var e=3,t=[-2048,4,135,213,273,323,373,425,425,373,323,273,213,135,4,-2048],r=[-12,18,41,64,112,198,355,1122,1122,355,198,112,64,41,18,-12],n=[0,0,0,512,512,512,1536,3584,3584,1536,512,512,512,0,0,0],o={},i=null;function u(a,u){var s,c,p,f,l,d,h,y=0,g=a,m=u;switch(g&=15,c=(s=i.predictorZero(o))>>1,p=s+i.predictorPole(o)>>1,f=i.stepSize(o),h=(d=(l=i.reconstruct(8&g,t[g],f))<0?p-(16383&l):p+l)-p+c,o=i.update(4,f,r[g]<<5,n[g],l,d,h,o),m){case e:return y=(y=(y=d<<2)>32767?32767:y)<-32768?-32768:y;default:return-1}}function s(){i=new a,o=i.g726InitState()}return s.prototype={decode:function(t){for(var r=new Int16Array(2*t.length),n=0,a=0;n<t.length;n++){var o=null;o=u((240&t[n])>>4,e),r[a]=65280&o,a++,o=u(15&t[n],e),r[a]=65280&o,a++}return r}},new s}function c(){var e=3,t=[-2048,-66,28,104,169,224,274,318,358,395,429,459,488,514,539,566,566,539,514,488,459,429,395,358,318,274,224,169,104,28,-66,-2048],r=[448,448,768,1248,1280,1312,1856,3200,4512,5728,7008,8960,11456,14080,16928,22272,22272,16928,14080,11456,8960,7008,5728,4512,3200,1856,1312,1280,1248,768,448,448],n=[0,0,0,0,0,512,512,512,512,512,1024,1536,2048,2560,3072,3072,3072,3072,2560,2048,1536,1024,512,512,512,512,512,0,0,0,0,0],o={},i=null;function u(a,u){var s,c,p,f,l,d,h,y=a,g=u;switch(y&=31,c=(s=i.predictorZero(o))>>1,p=s+i.predictorPole(o)>>1,f=i.stepSize(o),h=(l=(d=i.reconstruct(16&y,t[y],f))<0?p-(32767&d):p+d)-p+c,o=i.update(5,f,r[y],n[y],d,l,h,o),g){case e:return l<<2;default:return-1}}function s(){i=new a,o=i.g726InitState()}return s.prototype={decode:function(t){for(var r=new Int16Array(1.6*t.length),n=0,a=0;n<t.length-5;n+=5){var o=null;o=u(t[n]>>3,e),r[a]=65280&o,a++,o=u(t[n]<<2|t[n+1]>>6,e),r[a]=65280&o,a++,o=u(t[n+1]>>1,e),r[a]=65280&o,a++,o=u(t[n+1]<<4|t[n+2]>>4,e),r[a]=65280&o,a++,o=u(t[n+2]<<1|t[n+3]>>7,e),r[a]=65280&o,a++,o=u(t[n+3]>>2,e),r[a]=65280&o,a++,o=u(t[n+3]<<3|t[n+4]>>5,e),r[a]=65280&o,a++,o=u(t[n+4]>>0,e),r[a]=65280&o,a++}return r}},new s}var p=function(e){var t=null;switch(e){case 16:t=new i;break;case 24:t=new u;break;case 32:t=new s;break;case 40:t=new c;break;default:debug.log("wrong bits")}return t},f={"mpeg4-generic":86018,"G.723":86068,"G.729":86069,mpeg2:86016,"G.722.1":69660};function l(e){e=new Int16Array(e.buffer);for(var t=new Float32Array(e.length),r=0;r<e.length;r++)t[r]=e[r]/Math.pow(2,15);return t}var d=function(){var e=null,t=null,r="",n=null;function a(){}return a.prototype={open:function(t,a){if(f[r=t])e=Module._OpenAudioDecoder(f[t]);else switch(r){case"G.711A":case"G.711Mu":n=new o(r);break;case"G.726-16":case"G.726-24":case"G.726-32":case"G.726-40":n=new p(a)}},close:function(){f[r]&&(Module._CloseAudioDecoder(e),Module._free(t),t=null)},decodeByFFMPEG:function(n){var a=new Uint8Array(Module.HEAPU8.buffer,t,102400),o=new Uint8Array(102400),i=0;if("G.729"===r||"G.723"===r){for(var u="G.729"===r?10:20,s=Math.floor(n.length/u),c=0;c<s;c++){a.set(new Uint8Array(n.subarray(c*u,(c+1)*u)));var p=Module._DecodeAudioFrame(e,a.byteOffset,u,a.byteLength);o.set(new Uint8Array(a.subarray(0,p)),i),i+=p}o=l(new Uint8Array(o.subarray(0,i)))}else a.set(new Uint8Array(n)),i=Module._DecodeAudioFrame(e,a.byteOffset,n.byteLength,a.byteLength),o=new Uint8Array(a.subarray(0,i)),o="mpeg4-generic"!==r?l(o):new Float32Array(o.buffer);return o},decodeBySelf:function(e){var t;return t="PCM"===r?new Uint8Array(e):n.decode(e),"G.711A"!==r&&"G.711Mu"!==r&&(t=l(t)),t},decode:function(e){var n=null;return f[r]?(null===t&&(t=Module._malloc(102400)),n=this.decodeByFFMPEG(e)):n=this.decodeBySelf(e),n}},new a},h=function(e,t){var r=null,a=null,o=0,i=(new Uint8Array(7),{seconds:null,useconds:null}),u=new d;function s(){this.firstTime=0,this.lastMSW=0}return u.open(e,t),s.prototype={parseRTPData:function(e,t,n,a){var s=t[22];t[21],t[20],t.length;var c=t.subarray(24+s,t.length-8),p=(c.subarray(0,2),(t[19]<<24)+(t[18]<<16)+(t[17]<<8)+t[16]>>>0),f=Date.UTC("20"+(p>>26),(p>>22&15)-1,p>>17&31,p>>12&31,p>>6&63,63&p)/1e3;if(f-=28800,0==this.firstTime)this.firstTime=f,this.lastMSW=0,o=(t[21]<<8)+t[20],i.seconds=f,i.useconds=0;else{var l,d=(t[21]<<8)+t[20];l=d>o?d-o:d+65535-o,this.lastMSW+=l,f>this.firstTime&&(this.lastMSW-=1e3),this.firstTime=f,i.seconds=f,i.useconds=this.lastMSW,o=d}var h=u.decode(c);return{codec:"AAC",audio_type:a.audio_type,bufferData:h,rtpTimeStamp:1e3*i.seconds+i.useconds,samplingRate:r}},setCodecInfo:function(e){n.log("Set codec info. for AAC"),e.config,a=e.bitrate,r=e.ClockFreq},getCodecInfo:function(){return{bitrate:a,clockFreq:r}}},new s};({isWasm:!1}).isWasm?importScripts("./ffmpegwasm.js"):importScripts("./ffmpegasm.js"),Module.onRuntimeInitialized=function(){Module._RegisterAll(),w("WorkerReady")},addEventListener("message",function(e){var t=e.data;switch(t.type){case"sdpInfo":g=t.data.sdpInfo;t.data.aacCodecInfo;!function(e,t){for(var r=e,a=0;a<e.length;a++)if(-1===r[a].trackID.search("trackID=t")){switch(m=null,r[a].codecName){case"G.726-16":case"G.726-24":case"G.726-32":case"G.726-40":var o=parseInt(r[a].codecName.substr(6,2));n.log(o),(m=new h(r[a].codecName,o)).setCodecInfo(r[a]);break;case"mpeg4-generic":case"G.723":case"G.729":case"mpeg2":case"G.722.1":case"PCM":case"G.711A":case"G.711Mu":(m=new h(r[a].codecName)).setCodecInfo(r[a])}var i=r[a].RtpInterlevedID;if(y[i]=m,null!=m)return}}(g,0,t.data.mp4Codec);break;case"MediaData":var r=t.data.rtspInterleave[1];if("undefined"!==typeof y[r]){var a=t.data,o=y[r].parseRTPData(a.rtspInterleave,a.payload,v,t.info);null!==o&&"undefined"!==typeof o&&null!==o.streamData&&"undefined"!==typeof o.streamData&&(o.streamData=null),w("render",o)}}},!1);var y=[],g=null,m=null,v=!1;function w(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r={type:e,codec:t.codec,audio_type:t.audio_type,data:t.bufferData,rtpTimeStamp:t.rtpTimeStamp,samplingRate:t.samplingRate||8e3};if("render"===e)postMessage(r,[t.bufferData.buffer]);else if("backup"===e){postMessage({type:e,data:t})}else postMessage(r)}}]);