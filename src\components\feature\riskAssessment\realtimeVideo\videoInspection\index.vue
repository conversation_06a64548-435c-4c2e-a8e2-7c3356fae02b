<template>
  <div class="videoInspection">
    <div class="video-left">
      <div class="videoLeft-top">
        <h2>设备监控列表</h2>
        <div class="video-list">
          <div class="video-item">
            <p
              v-for="(item, index) in videoData"
              :key="index"
              @click="getVideoId(item.monitornum, index)"
              :class="active == index ? 'activeP' : ''"
            >
              <i class="video-camera"></i>
              <span class="video-name">{{ item.monitorname }}</span>
              <span class="zaixian" v-if="item.onlinestatus == 1">在线</span>
              <span class="lixian" v-else-if="item.onlinestatus == 0"
                >离线</span
              >
            </p>
            <img
              v-if="videoData.length == 0"
              style="
                margin: 0 auto;
                display: block;
                margin-top: 30px;
                width: 70%;
              "
              src="/static/img/assets/img/noData.png"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="video-right">
      <!-- <el-button type="primary" size="small">播放</el-button>
        <el-button type="secondary" size="small">预览</el-button> -->
      <el-radio-group
        size="small"
        v-model="sign"
        style="position: absolute; right: 0"
      >
        <el-radio-button label="0"
          >全部({{ videoNumData.total }})</el-radio-button
        >
        <el-radio-button label="1"
          >在线({{ videoNumData.onlineNum }})</el-radio-button
        >
      </el-radio-group>
      <div class="video-box" id="video-box">
        <!-- <div id="playWnd" class="playWnd" style="left: 109px; top: 133px"></div> -->
        <div id="ws-real-player"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getVideoList, getVideoNumData } from "@/api/entList";
import { getRealmonitorNew } from "@/api/riskAssessment";
import WSPlayer from '@/utils/WSPlayer/WSPlayer';
import ICC from '@/utils/icc';
export default {
  //import引入的组件
  name: "videoInspection",
  components: {},
  props: ["showVideo"],
  data() {
    return {
      //声明公用变量
      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
      videoModuleData: {},
      enterpriseId: "",
      sign: "0",
      videoData: [],
      cameraIndexCode: "",
      videoNumData: {},
      active: 0,
      activeName: "",
      realPlayer: null
    };
  },
  //方法集合
  methods: {
    getData(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoList({
        enterpId: this.enterpriseId,
        sign: this.sign,
      }).then((res) => {
        console.log('getVideoList+++++++++++++++000', res);
        if (res.data.code == 0) {
          this.videoData = res.data.data;
          this.cameraIndexCode = this.videoData.length > 0 ? this.videoData[0].monitornum : '';
        }
      });
    },
    getVideoId(monitornum, index) {
      this.active = index;
      this.cameraIndexCode = monitornum;
      this.realPlayNew(this.cameraIndexCode);
      // this.unloadVideo();
      // this.initPlugin();
      // this.yulan(this.cameraIndexCode);
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId 
     */
    realPlayNew(channelId) {
      ICC.getRealmonitor({
          channelId: channelId,
          // dataType: '3', //视频类型：1=视频, 2=音频, 3=音视频
          streamType: '2' //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
      }).then((data) => {
          this.realPlayer.playReal({
              rtspURL: data.rtspUrl, // string | array[string]
              decodeMode: 'canvas', // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
              channelId: channelId, // 可选，用来标记当前视频播放的通道id
          })
      })
    },
    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
    // 创建播放实例
    // initPlugin() {
    //   const that = this;
    //   that.oWebControl = new WebControl({
    //     szPluginContainer: "playWnd", // 指定容器id
    //     iServicePortStart: 15900, // 指定起止端口号，建议使用该值
    //     iServicePortEnd: 15909,
    //     szClassId: "23BF3B0A-2C56-4D97-9C03-0CB103AA8F11", // 用于IE10使用ActiveX的clsid
    //     cbConnectSuccess: function () {
    //       // 创建WebControl实例成功
    //       if (that.oWebControl == null) {
    //         that.initPlugin();
    //         return;
    //       }
    //       that.oWebControl
    //         .JS_StartService("window", {
    //           // WebControl实例创建成功后需要启动服务
    //           dllPath: "./VideoPluginConnect.dll", // 值"./VideoPluginConnect.dll"写死
    //         })
    //         .then(
    //           function () {
    //             // 启动插件服务成功
    //             that.oWebControl.JS_SetWindowControlCallback({
    //               // 设置消息回调
    //               cbIntegrationCallBack: that.cbIntegrationCallBack,
    //             });

    //             that.oWebControl
    //               .JS_CreateWnd("playWnd", that.videoWidth, that.videoHight)
    //               .then(function () {
    //                 //JS_CreateWnd创建视频播放窗口，宽高可设定
    //                 that.init(); // 创建播放实例成功后初始化
    //               });
    //           },
    //           function () {
    //             // 启动插件服务失败
    //           }
    //         );
    //     },
    //     cbConnectError: function () {
    //       // 创建WebControl实例失败
    //       that.oWebControl = null;
    //       $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
    //       window.WebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
    //       that.initCount++;
    //       if (that.initCount < 3) {
    //         setTimeout(function () {
    //           that.initPlugin();
    //         }, 3000);
    //       } else {
    //         // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
    //         let host = "http://************:9080/file/image/VideoWebPlugin.exe";
    //         $("#playWnd").html(
    //           "插件启动失败，请检查插件是否安装！是否安装:" +
    //             "<a href=" +
    //             host +
    //             ' download="VideoWebPlugin.exe">点击下载</a>'
    //         );
    //       }
    //     },
    //     cbConnectClose: function (bNormalClose) {
    //       // 异常断开：bNormalClose = false
    //       // JS_Disconnect正常断开：bNormalClose = true
    //       console.log("cbConnectClose");
    //       that.oWebControl = null;
    //     },
    //   });
    // },

    // 设置窗口控制回调
    // setCallbacks() {
    //     oWebControl.JS_SetWindowControlCallback({
    //         cbIntegrationCallBack: cbIntegrationCallBack
    //     });
    // },

    // 推送消息
    cbIntegrationCallBack(oData) {
      // window.showCBInfo(JSON.stringify(oData.responseMsg));
    },

    //初始化
    // init() {
    //   const that = this;
    //   that.getPubKey(function () {
    //     ////////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
    //     var appkey = "29553988"; //综合安防管理平台提供的appkey，必填
    //     var secret = that.setEncrypt("NtJZ6GjAj5RbHiqaZXmr"); //综合安防管理平台提供的secret，必填
    //     var ip = "**************"; //综合安防管理平台IP地址，必填
    //     var playMode = 0; //初始播放模式：0-预览，1-回放
    //     var port = 1443; //综合安防管理平台端口，若启用HTTPS协议，默认443
    //     var snapDir = "D:\\SnapDir"; //抓图存储路径
    //     var videoDir = "D:\\VideoDir"; //紧急录像或录像剪辑存储路径
    //     var layout = "1x1"; //playMode指定模式的布局
    //     var enableHTTPS = 1; //是否启用HTTPS协议与综合安防管理平台交互，是为1，否为0
    //     var encryptedFields = "secret"; //加密字段，默认加密领域为secret
    //     var showToolbar = 1; //是否显示工具栏，0-不显示，非0-显示
    //     var showSmart = 1; //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
    //     var buttonIDs =
    //       "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769"; //自定义工具条按钮
    //     ////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////

    //     that.oWebControl
    //       .JS_RequestInterface({
    //         funcName: "init",
    //         argument: JSON.stringify({
    //           appkey: appkey, //API网关提供的appkey
    //           secret: secret, //API网关提供的secret
    //           ip: ip, //API网关IP地址
    //           playMode: playMode, //播放模式（决定显示预览还是回放界面）
    //           port: port, //端口
    //           snapDir: snapDir, //抓图存储路径
    //           videoDir: videoDir, //紧急录像或录像剪辑存储路径
    //           layout: layout, //布局
    //           enableHTTPS: enableHTTPS, //是否启用HTTPS协议
    //           encryptedFields: encryptedFields, //加密字段
    //           showToolbar: showToolbar, //是否显示工具栏
    //           showSmart: showSmart, //是否显示智能信息
    //           buttonIDs: buttonIDs, //自定义工具条按钮
    //         }),
    //       })
    //       .then(function (oData) {
    //         // that.oWebControl.JS_Resize(1000, 600);  // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题

    //         console.log("init成功！");
    //         that.oWebControl.JS_Resize(that.videoWidth, that.videoHight); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
    //         // that.yulan(that.videoModuleData.data[0].cameraIndexCode);
    //         that.yulan(that.cameraIndexCode);
    //       });
    //   });
    // },

    //获取公钥
    getPubKey(callback) {
      const that = this;
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024,
          }),
        })
        .then(function (oData) {
          console.log(oData);
          if (oData.responseMsg.data) {
            that.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },

    //RSA加密
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },

    // 预览
    // yulan(code) {
    //   const that = this; // 获取输入的监控点编号值，必填
    //   const streamMode = 0; // 主子码流标识：0-主码流，1-子码流
    //   const transMode = 1; // 传输协议：0-UDP，1-TCP
    //   const gpuMode = 0; // 是否启用GPU硬解，0-不启用，1-启用
    //   const wndId = -1; // 播放窗口序号（在2x2以上布局下可指定播放窗口）
    //   //      cameraIndexCode = cameraIndexCode.replace(/(^\s*)/g, "");
    //   //      cameraIndexCode = cameraIndexCode.replace(/(\s*$)/g, "");
    //   //   let cameraIndexCode = "42011001001320000004";
    //   let cameraIndexCode = code;
    //   if (typeof cameraIndexCode === "string") {
    //     console.log("开始预览！");
    //     that.oWebControl.JS_RequestInterface({
    //       funcName: "startPreview",
    //       argument: JSON.stringify({
    //         cameraIndexCode, // 监控点编号
    //         streamMode, // 主子码流标识
    //         transMode, // 传输协议
    //         gpuMode, // 是否开启GPU硬解
    //         wndId, // 可指定播放窗口
    //       }),
    //     });
    //   } else {
    //     // cameraIndexCode.forEach(function(item, index) {
    //     //   that.oWebControl.JS_RequestInterface({
    //     //     funcName: "startPreview",
    //     //     argument: JSON.stringify({
    //     //       cameraIndexCode: item, // 监控点编号
    //     //       streamMode, // 主子码流标识
    //     //       transMode, // 传输协议
    //     //       gpuMode, // 是否开启GPU硬解
    //     //       wndId // 可指定播放窗口
    //     //     })
    //     //   });
    //     // });
    //   }
    // },
    // 销毁视频插件
    // unloadVideo(type) {
    //   if (this.oWebControl != null) {
    //     this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
    //     this.oWebControl.JS_Disconnect().then(
    //       function () {},
    //       function () {}
    //     );
    //     if (type === "updateInit") {
    //       this.initPlugin();
    //     }
    //   }
    // },
    // setWndCover() {
    //   console.log("裁剪裁剪");
    //   var iWidth = $(window).width();
    //   var iHeight = $(window).height();
    //   var oDivRect = $("#playWnd").get(0).getBoundingClientRect();

    //   var iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0;
    //   var iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0;
    //   var iCoverRight =
    //     oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0;
    //   var iCoverBottom =
    //     oDivRect.bottom - iHeight > 0
    //       ? Math.round(oDivRect.bottom - iHeight)
    //       : 0;

    //   iCoverLeft = iCoverLeft > 1000 ? 1000 : iCoverLeft;
    //   iCoverTop = iCoverTop > 600 ? 600 : iCoverTop;
    //   iCoverRight = iCoverRight > 1000 ? 1000 : iCoverRight;
    //   iCoverBottom = iCoverBottom > 600 ? 600 : iCoverBottom;

    //   this.oWebControl.JS_RepairPartWindow(0, 0, 1001, 600); // 多1个像素点防止还原后边界缺失一个像素条
    //   if (iCoverLeft != 0) {
    //     this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, 600);
    //   }
    //   if (iCoverTop != 0) {
    //     this.oWebControl.JS_CuttingPartWindow(0, 0, 1001, iCoverTop); // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
    //   }
    //   if (iCoverRight != 0) {
    //     this.oWebControl.JS_CuttingPartWindow(
    //       1000 - iCoverRight,
    //       0,
    //       iCoverRight,
    //       600
    //     );
    //   }
    //   if (iCoverBottom != 0) {
    //     this.oWebControl.JS_CuttingPartWindow(
    //       0,
    //       600 - iCoverBottom,
    //       1000,
    //       iCoverBottom
    //     );
    //   }
    // },
    // initVideo() {
    //   // this.videoWidth = $("#video-box").width();
    //   // this.videoHight = $("#video-box").height();
    //   // this.initPlugin();
    //   // // this.updateVideoCode();
    //   // console.log("进入视频组件~");
    //   // const that = this;
    //   // this.$nextTick(() => {
    //   //     window.addEventListener("resize", function() {
    //   //         if (that.oWebControl != null) {
    //   //             console.log('监听resize事件，使插件窗口尺寸跟随DIV窗口变化')
    //   //             that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
    //   //             that.setWndCover();
    //   //         }
    //   //     });
    //   //     window.addEventListener("scroll", function() {
    //   //         if (that.oWebControl != null) {
    //   //             console.log('监听滚动条scroll事件')
    //   //             that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
    //   //             that.setWndCover();
    //   //         }
    //   //     });
    //   // })
    //   // debugger;
    //   if (this.showVideo) {
    //     this.videoWidth = $("#video-box").width();
    //     this.videoHight = $("#video-box").height();
    //     //    this.videoHight = 400;
    //     // this.videoModuleData = this.$store.state.login.videoModuleData;
    //     // if (this.videoModuleData.data.length === 0) {
    //     // } else {
    //     //   window.showCBInfo = function() {};
    //     //   this.initPlugin();
    //     // }
    //     this.initPlugin();
    //     // this.updateVideoCode();
    //     console.log("进入视频组件~");
    //     const that = this;
    //     window.addEventListener("resize", function () {
    //       that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
    //       that.setWndCover();
    //     });
    //     window.addEventListener("scroll", function () {
    //       that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
    //       that.setWndCover();
    //     });
    //   }
    // },
    initVideoNew() {
      // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
      // 初始化平台信息获取
      let serverAdress = sessionStorage.getItem('videoApi');
      // if (process.env.NODE_ENV === 'development') {
      //     serverAdress = '************:2443';
      // } else {
      //     serverAdress = '***********:9100';
      // }
      // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
      if (!this.realPlayer) {
        this.realPlayer = new WSPlayer({
            el: 'ws-real-player', // 必传
            type: 'real', // real | record
            serverIp: serverAdress,
            num: 4,
            showControl: true,
        })
      }
    }
  },
  destroyed() {
    // this.unloadVideo();
    console.log("销毁视频组件~");
  },
  async created() {
     await ICC.init();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.initVideoNew();
  },
  watch: {
    showVideo(val, newval) {
      if (val) {
        this.initVideoNew();
        //    if (document.body.clientWidth <= 1024) {
        //     this.videoWidth = document.body.clientWidth * 0.26534765625;
        //     this.videoHight = document.body.clientHeight * 0.1906949559647718;
        //     } else if (document.body.clientWidth <= 1366) {
        //     this.videoWidth = document.body.clientWidth * 0.20434765625;
        //     this.videoHight = document.body.clientHeight * 0.1900949559647718;
        //     } else {
        //     this.videoWidth = document.body.clientWidth * 0.20434765625;
        //     this.videoHight = document.body.clientHeight * 0.2366949559647718;
        //     }
        //     this.videoWidth = document.body.clientWidth * 0.20434765625;
        //     this.videoHight = document.body.clientHeight * 0.2366949559647718;
        // this.videoWidth = 907;
        // this.videoHight = 680;
        // this.videoWidth = $("#video-box").width();
        // this.videoHight = $("#video-box").height();
        //    this.videoHight = 400;
        // this.videoModuleData = this.$store.state.login.videoModuleData;
        // if (this.videoModuleData.data.length === 0) {
        // } else {
        //   window.showCBInfo = function() {};
        //   this.initPlugin();
        // }
        // this.initPlugin();
        // this.updateVideoCode();
        console.log("进入视频组件~");
        // const that = this;
        // window.addEventListener("resize", function () {
        //   that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
        //   that.setWndCover();
        // });
        // window.addEventListener("scroll", function () {
        //   that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
        //   that.setWndCover();
        // });
      } else {
        // this.unloadVideo();
        console.log("销毁视频组件~");
      }
    },
    sign(val, newval) {
      this.getData(this.enterpriseId);
    },
  },
};
</script>
<style lang="scss" scoped>
.videoInspection {
  // padding: 15px 0;
  overflow: hidden;
  .video-left {
    float: left;
    width: 345px;
    margin-right: 25px;
    .videoLeft-top {
      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #3b4046;
        line-height: 32px;
      }
      .video-list {
        background: #daefff;
        height: calc(100vh - 285px);
        padding: 15px 0;
        border-radius: 4px;
        overflow-y: scroll;
        .activeP {
          background-color: rgba(0, 120, 255, 0.1);
          color: #3977ea;
        }
        p {
          display: flex;
          align-items: center;
          line-height: 50px;
          height: 50px;
          margin-bottom: 0;
          font-size: 14px;
          padding: 0 15px;
          color: #545c65;
          border-bottom: 1px solid #bdddf3;
          .video-camera {
            display: inline-block;
            width: 17px;
            height: 20px;
            background: url("/static/img/assets/img/camera.png") no-repeat
              center;
            background-size: cover;
            vertical-align: middle;
            margin-right: 14px;
          }
          span {
            float: right;
          }
          .video-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis; 
            white-space: nowrap; 
          }
          .zaixian {
            color: #43be98;
          }
          .lixian {
            color: #999999;
          }
          .baojing {
            color: #f9646f;
          }
        }
      }
    }
  }
  .video-right {
    float: left;
    position: relative;
    width: calc(100% - 370px);
    .video-box {
      margin-top: 40px;
      border: 1px solid #ddd;
      height: calc(100vh - 285px);
      #ws-real-player {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
