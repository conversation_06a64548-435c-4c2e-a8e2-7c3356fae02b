<template>
  <div class="enterpriseManagement"
       ref="enterpriseManagement">
    <div v-show="!TrendChartBool">
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span @click="goToSafety">
                <a-icon type="home"
                        theme="filled"
                        class="icon" />重大危险源分析
              </span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="showBreak">{{ areaName }}
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part"
           v-if="$store.state.login.user.user_type != 'park'">
        <div class="l">
          <!-- <el-select
            v-model="level"
            multiple
            placeholder="危险源等级"
            size="mini"
            style="width: 270px"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <el-cascader size="mini"
                       placeholder="请选择行政区划"
                       :options="district"
                       v-model="distCode"
                       :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
                       @change="changeCascader"
                       ></el-cascader>
          <el-button type="primary"
                     size="mini"
                     @click="search">查询</el-button>
          <CA-button type="primary"
                     size="mini"
                     plain
                     @click="exportExcel">
            导出
          </CA-button>
        </div>
        <CA-button type="primary" size="mini" plain @click="gotoTrendAnalysis"
          ><a-icon type="bar-chart" class="chart-icon" />趋势分析</CA-button
        >
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>重大危险源分析</h2>
          <CA-RadioGroup class="radio"
                         v-model="mode"
                         backgroundColor="#F1F6FF"
                         border="1px solid rgba(57, 119, 234, 0.2)">
            <CA-radio :label="{
                src: '../../../static/img/liebiao_icon.png',
                style: 'width:15px;height:15px',
              }"
                      :labelTwo="{
                src: '../../../static/img/liebiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
                      value="统计"
                      bgColorActive="#409eff">
            </CA-radio>
            <CA-radio :label="{
                src: '../../../static/img/tubiao_icon.png',
                style: 'width:15px;height:15px',
              }"
                      :labelTwo="{
                src: '../../../static/img/tubiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
                      bgColorActive="#409eff"
                      value="图表">
            </CA-radio>
          </CA-RadioGroup>
        </div>
        <div v-show="showtable">
          <div class="table">
            <el-table :data="tableData"
                      v-loading="loading"
                      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                      border
                      style="width: 100%"
                      ref="multipleTable"
                      @selection-change="handleSelectionChange"
                      @sort-change="changeTableSort"
                      :default-sort="{ prop: 'date', order: 'descending' }"
                      @select="select"
                      @select-all="select">
              <el-table-column type="selection"
                               width="50"
                               align="center">
              </el-table-column>
              <el-table-column label="行政区划"
                               align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <!-- 仙桃市,潜江市,天门市,神农区 禁止下钻 -->
                  <div v-if="
                      row.distCode == 429004 ||
                      row.distCode == 429005 ||
                      row.distCode == 429006 ||
                      row.distCode == 429021
                    ">
                    {{ row.areaName }}
                  </div>
                  <div v-else>
                    <span v-if="$index != 0 && !showBreak && isXiaZuan"
                          @click="xiaZuan(row.distCode, row.areaName)"
                          style="color: #3977ea; cursor: pointer">{{ row.areaName }}</span>
                    <span v-else> {{ row.areaName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="已接入重大危险源"
                               sortable="custom"
                               prop="total"
                               align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="row.total != 0 || tableData.length==1"
                        @click="openDialog(row.distCode, 0, row.areaName)"
                        style="color: #3977ea; cursor: pointer">{{ row.total }}</span>
                  <span v-else>{{ row.total }}</span>
                </template>
              </el-table-column>
              <el-table-column label="一级重大危险源"
                               sortable="custom"
                               prop="levelOneMajorHazardSource"
                               align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="row.levelOneMajorHazardSource != 0 || tableData.length==1"
                        @click="openDialog(row.distCode, 1, row.areaName)"
                        style="color: #3977ea; cursor: pointer">{{ row.levelOneMajorHazardSource }}</span>
                  <span v-else>{{ row.levelOneMajorHazardSource }}</span>
                </template>
              </el-table-column>
              <el-table-column label="二级重大危险源"
                               sortable="custom"
                               prop="levelTwoMajorHazardSource"
                               align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="row.levelTwoMajorHazardSource != 0 || tableData.length==1"
                        @click="openDialog(row.distCode, 2, row.areaName)"
                        style="color: #3977ea; cursor: pointer">{{ row.levelTwoMajorHazardSource }}</span>
                  <span v-else>{{ row.levelTwoMajorHazardSource }}</span>
                </template>
              </el-table-column>
              <el-table-column label="三级重大危险源"
                               sortable="custom"
                               prop="levelThreeMajorHazardSource"
                               align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="row.levelThreeMajorHazardSource != 0 || tableData.length==1"
                        @click="openDialog(row.distCode, 3, row.areaName)"
                        style="color: #3977ea; cursor: pointer">{{ row.levelThreeMajorHazardSource }}</span>
                  <span v-else>{{ row.levelThreeMajorHazardSource }}</span>
                </template>
              </el-table-column>
              <el-table-column label="四级重大危险源"
                               sortable="custom"
                               prop="levelFourMajorHazardSource"
                               align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="row.levelFourMajorHazardSource != 0 || tableData.length==1"
                        @click="openDialog(row.distCode, 4, row.areaName)"
                        style="color: #3977ea; cursor: pointer">{{ row.levelFourMajorHazardSource }}</span>
                  <span v-else>{{ row.levelFourMajorHazardSource }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
          </div>
        </div>
        <div v-show="!showtable">
          <div id="echart"></div>
        </div>
      </div>
    </div>
    <TrendChart
          v-show="TrendChartBool"
          @RunningState="TrendChartFun"
          ref="TrendChart"
        ></TrendChart>
    <DetailTable ref="DetailTable"></DetailTable>
  </div>
</template>
<script>
import TrendChart from "./trendChart";
import DetailTable from './detailTable'
import { queryMajors, exportMajor } from '@/api/workingAcc'
import { createNamespacedHelpers } from 'vuex'
const { mapState: mapStateLogin } = createNamespacedHelpers('login')
const { mapState: mapStateControler } = createNamespacedHelpers('controler')
export default {
  components: {
    // DetailTable
    // TrendChart: (resolve) => {
    //   require(["./TrendChart.vue"], resolve);
    // },
    // DetailTable: () => import('./detailTable')
    // DetailTable: (resolve) => {
    //   require(["./detailTable.vue"], resolve);
    // },
    DetailTable:() => import("./detailTable"),
    TrendChart:() => import("./trendChart"),
  },
  data() {
    return {
      TrendChartBool: false,
      root: this.$refs.enterpriseManagement,
      //   options: [
      //     {
      //       value: "1",
      //       label: "一级",
      //     },
      //     {
      //       value: "2",
      //       label: "二级",
      //     },
      //     {
      //       value: "3",
      //       label: "三级",
      //     },
      //     {
      //       value: "4",
      //       label: "四级",
      //     },
      //   ],
      //   level: ["1", "2", "3", "4"],
      tableData: [],
      mode: '统计',
      showtable: true,
      currentPage: 1,
      total: '',
      showBreak: false,
      areaName: '',
      loading: true,
      selection: [],
      district: this.$store.state.controler.district,
      distCode: this.$store.state.login.userDistCode
    }
  },
  methods: {
    getSafetyList() {
      this.loading = true
      queryMajors({
        current: this.currentPage,
        distCode: this.distCode,
        size: 100
      }).then(res => {
        if (res.data.code == 0) {
          this.loading = false
          this.tableData = res.data.data
        }
      })
    },
    // 排序
    changeTableSort(column) {
      var fieldName = column.prop
      var sortingType = column.order
      var data = [...this.tableData]
      var shiftTotal = []
      if (data[0].distCode == this.$store.state.login.userDistCode) {
        shiftTotal = data.shift()
      } else if ((this.distCode = data[0].distCode)) {
        shiftTotal = data.shift()
      }
      //按照降序排序
      if (sortingType == 'descending') {
        data = data.sort((a, b) => b[fieldName] - a[fieldName])
        this.tableData = data
        this.tableData.unshift(shiftTotal)
      } else if (sortingType == 'ascending') {
        //按照升序排序
        data = data.sort((a, b) => a[fieldName] - b[fieldName])
        this.tableData = data
        this.tableData.unshift(shiftTotal)
      } else {
        this.getSafetyList()
      }
    },
    // 导出
    exportExcel() {
      //   let list = [this.distCode, ...this.selection];
      exportMajor({
        // level: this.level.toString(),
        distCode: this.distCode,
        idList: this.selection.length <= 0 ? null : this.selection
      }).then(response => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: '导出成功',
            type: 'success'
          })
        } else {
          this.$message.error('导出失败')
        }
        const blob = new Blob([response.data], { type: 'application/xls' })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '重大危险源分析' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    },
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].distCode
      }
    },
    xiaZuan(distCode, areaName) {
      this.areaName = areaName
      this.showBreak = true
      this.distCode = distCode
      this.getSafetyList()
    },
    goToSafety() {
      this.showBreak = false
      // this.level = "";
      this.distCode = this.$store.state.login.userDistCode
      this.getSafetyList()
    },
    search() {
      this.currentPage = 1
      this.getSafetyList()
    },
    gotoTrendAnalysis() {
      this.TrendChartBool = true;
      this.$nextTick(()=> {
        // console.log(this.$refs.TrendChart);
        // this.$refs.TrendChart.getTime();
        // this.$refs.TrendChart.getSaftyTrendListData();
        // this.$refs.TrendChart.$setEchart("myCharted", 250, 250);
        this.$refs.TrendChart.getData();
        // this.$refs.TrendChart.drawLine();
      });
    },
    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBooleanOne(true)
      distCode = this.isShowDist ? distCode : null
      this.$refs.DetailTable.getEntData(distCode, type, areaName)
      this.$refs.DetailTable.getDistrict()
    },
    handleSelectionChange(val) {
      console.log(val)
    },
    handleClick() {
      console.log(123)
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data
      this.getSafetyList()
    },
    changeCascader(value) {
      if (!value) {
        this.distCode = this.$store.state.login.userDistCode
      }
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.drawLine();
    this.getSafetyList()
    this.$setEchart('echart', 250, 250)
    this.$nextTick(() => {
      this.root = this.$refs.enterpriseManagement
      console.log(this.root)
    })
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: state => state.isXiaZuan,
      isShowDist: state => state.isShowDist
    }),
    ...mapStateControler({
      vuexDistrict: state => state.district
    })
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal
      }
    },
    mode(newValue, oldValue) {
      if (newValue == '统计') {
        this.showtable = true
      } else {
        this.showtable = false
      }
    },
    //观察option的变化
    tableData: {
      handler(newVal, oldVal) {
        if (newVal) {
          let echart = this.$echarts.init(document.getElementById('echart'))
          var getname = []
          var getvalue = []
          var getvalue1 = []
          var getvalue2 = []
          var getvalue3 = []
          var getvaluesum = []
          if (this.$store.state.login.user.user_type == 'park') {
            for (var i = 0; i < newVal.length; i++) {
              getname.push(newVal[i].areaName)
              getvalue.push(newVal[i].levelOneMajorHazardSource)
              getvalue1.push(newVal[i].levelTwoMajorHazardSource)
              getvalue2.push(newVal[i].levelThreeMajorHazardSource)
              getvalue3.push(newVal[i].levelFourMajorHazardSource)
            }
          } else {
            for (var i = 1; i < newVal.length; i++) {
              getname.push(newVal[i].areaName)
              getvalue.push(newVal[i].levelOneMajorHazardSource)
              getvalue1.push(newVal[i].levelTwoMajorHazardSource)
              getvalue2.push(newVal[i].levelThreeMajorHazardSource)
              getvalue3.push(newVal[i].levelFourMajorHazardSource)
            }
          }
          for (var i = 0; i < getname.length; i++) {
            getvaluesum[i] =
              getvalue[i] + getvalue1[i] + getvalue2[i] + getvalue3[i]
          }

          var maxnum = Math.max.apply(null, getvaluesum)
          var maxlen = Math.pow(10, String(Math.ceil(maxnum)).length - 2)
          if (maxnum >= 5) {
            var max = Math.ceil(maxnum / (0.95 * maxlen)) * maxlen
          } else {
            var max = 5
          }
          var option = {
            grid: {
              top: '33',
              right: '15',
              left: '50',
              bottom: '55'
            },
            toolbox: {
              feature: {
                saveAsImage: {}
              }
            },
            tooltip: {
              trigger: 'axis'
              //   axisPointer: {
              //     type: "none",
              //   },
              //   formatter:
              //     "{b0}</br>{a0}个数：{c0}个</br>{a1}个数：{c1}个</br>{a2}个数：{c2}个</br>{a3}个数：{c3}个",
            },
            legend: {
              type: 'scroll',
              right: '80',
              top: '0',
              data: [
                '一级重大危险源',
                '二级重大危险源',
                '三级重大危险源',
                '四级重大危险源'
              ],
              itemGap: 25,
              itemWidth: 16,
              itemHeight: 16,
              textStyle: {
                fontSize: '13',
                color: '#666666'
              }
            },
            xAxis: [
              {
                data: getname,
                axisLabel: {
                  //interval: 0,
                  // formatter: function(value) {
                  //     var ret = ""; //拼接加\n返回的类目项
                  //     var maxLength = 5; //每项显示文字个数
                  //     var valLength = value.length; //X轴类目项的文字个数
                  //     var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                  //     if (rowN > 1) //如果类目项的文字大于5,
                  //     {
                  //         for (var i = 0; i < rowN; i++) {
                  //             var temp = ""; //每次截取的字符串
                  //             var start = i * maxLength; //开始截取的位置
                  //             var end = start + maxLength; //结束截取的位置
                  //             //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                  //             temp = value.substring(start, end) + "\n";
                  //             ret += temp; //凭借最终的字符串
                  //         }
                  //         return ret;
                  //     } else {
                  //         return value;
                  //     }
                  // },
                  margin: 10,
                  rotate: 20,
                  color: '#666666',
                  textStyle: {
                    fontSize: 13,
                    fontWeight: 400
                  }
                },
                axisLine: {
                  lineStyle: {
                    color: '#B0C5DB'
                  }
                },
                axisTick: {
                  show: false
                }
              }
            ],
            yAxis: [
              {
                min: 0,
                max: max, // 计算最大值
                interval: max / 5, //  平均分为5份
                splitNumber: 5,
                name: '单位：个',
                nameTextStyle: {
                  color: '#999999',
                  fontSize: 13,
                  padding: [0, 0, 0, 5]
                },
                axisLabel: {
                  color: '#666666',
                  textStyle: {
                    fontSize: 13
                  }
                },
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                splitLine: {
                  lineStyle: {
                    color: '#CCDBEB',
                    type: 'dashed',
                    opacity: 0.5
                  }
                }
              }
            ],
            series: [
              {
                name: '一级重大危险源',
                type: 'bar',
                data: getvalue,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#f65959' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#f65959' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#f65959'
                      }
                    }
                  }
                }
              },
              {
                name: '二级重大危险源',
                type: 'bar',
                data: getvalue1,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#f98e2f' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#f98e2f' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#f98e2f'
                      }
                    }
                  }
                }
              },
              {
                name: '三级重大危险源',
                type: 'bar',
                data: getvalue2,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#f9c22f' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#f9c22f' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#f9c22f'
                      }
                    }
                  }
                }
              },
              {
                name: '四级重大危险源',
                type: 'bar',
                data: getvalue3,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#2f8ef9' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#2f8ef9' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#2f8ef9'
                      }
                    }
                  }
                }
              }
            ]
          }
          echart.setOption(option)
        } else {
          let echart = this.$echarts.init(document.getElementById('echart'))
          var getname = []
          var getvalue = []
          var getvalue1 = []
          var getvalue2 = []
          var getvalue3 = []
          var getvaluesum = []
          for (var i = 1; i < oldVal.length; i++) {
            getname.push(oldVal[i].areaName)
            getvalue.push(oldVal[i].levelOneMajorHazardSource)
            getvalue1.push(oldVal[i].levelTwoMajorHazardSource)
            getvalue2.push(oldVal[i].levelThreeMajorHazardSource)
            getvalue3.push(oldVal[i].levelFourMajorHazardSource)
          }
          for (var i = 0; i < getname.length; i++) {
            getvaluesum[i] =
              getvalue[i] + getvalue1[i] + getvalue2[i] + getvalue3[i]
          }

          var maxnum = Math.max.apply(null, getvaluesum)
          var maxlen = Math.pow(10, String(Math.ceil(maxnum)).length - 2)
          if (maxnum >= 5) {
            var max = Math.ceil(maxnum / (0.95 * maxlen)) * maxlen
          } else {
            var max = 5
          }
          var option = {
            grid: {
              top: '33',
              right: '15',
              left: '50',
              bottom: '55'
            },
            tooltip: {
              trigger: 'axis'
              //   axisPointer: {
              //     type: "none",
              //   },
              //   formatter:
              //     "{b0}</br>{a0}个数：{c0}个</br>{a1}个数：{c1}个</br>{a2}个数：{c2}个</br>{a3}个数：{c3}个",
            },
            legend: {
              type: 'scroll',
              right: '10',
              top: '0',
              data: [
                '一级重大危险源',
                '二级重大危险源',
                '三级重大危险源',
                '四级重大危险源'
              ],
              itemGap: 25,
              itemWidth: 16,
              itemHeight: 16,
              textStyle: {
                fontSize: '13',
                color: '#666666'
              }
            },
            xAxis: [
              {
                data: getname,
                axisLabel: {
                  //interval: 0,
                  // formatter: function(value) {
                  //     var ret = ""; //拼接加\n返回的类目项
                  //     var maxLength = 5; //每项显示文字个数
                  //     var valLength = value.length; //X轴类目项的文字个数
                  //     var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                  //     if (rowN > 1) //如果类目项的文字大于5,
                  //     {
                  //         for (var i = 0; i < rowN; i++) {
                  //             var temp = ""; //每次截取的字符串
                  //             var start = i * maxLength; //开始截取的位置
                  //             var end = start + maxLength; //结束截取的位置
                  //             //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                  //             temp = value.substring(start, end) + "\n";
                  //             ret += temp; //凭借最终的字符串
                  //         }
                  //         return ret;
                  //     } else {
                  //         return value;
                  //     }
                  // },
                  margin: 10,
                  rotate: 20,
                  color: '#666666',
                  textStyle: {
                    fontSize: 13,
                    fontWeight: 400
                  }
                },
                axisLine: {
                  lineStyle: {
                    color: '#B0C5DB'
                  }
                },
                axisTick: {
                  show: false
                }
              }
            ],
            yAxis: [
              {
                min: 0,
                max: max, // 计算最大值
                interval: max / 5, //  平均分为5份
                splitNumber: 5,
                name: '单位：个',
                nameTextStyle: {
                  color: '#999999',
                  fontSize: 13,
                  padding: [0, 0, 0, 5]
                },
                axisLabel: {
                  color: '#666666',
                  textStyle: {
                    fontSize: 13
                  }
                },
                axisLine: {
                  show: false
                },
                axisTick: {
                  show: false
                },
                splitLine: {
                  lineStyle: {
                    color: '#CCDBEB',
                    type: 'dashed',
                    opacity: 0.5
                  }
                }
              }
            ],
            series: [
              {
                name: '一级重大危险源',
                type: 'bar',
                data: getvalue,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#f65959' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#f65959' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#f65959'
                      }
                    }
                  }
                }
              },
              {
                name: '二级重大危险源',
                type: 'bar',
                data: getvalue1,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#f98e2f' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#f98e2f' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#f98e2f'
                      }
                    }
                  }
                }
              },
              {
                name: '三级重大危险源',
                type: 'bar',
                data: getvalue2,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#f9c22f' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#f9c22f' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#f9c22f'
                      }
                    }
                  }
                }
              },
              {
                name: '四级重大危险源',
                type: 'bar',
                data: getvalue3,
                stack: '各专业本科生年级分布',
                barWidth: '20px',
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: '#2f8ef9' // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: '#2f8ef9' // 100% 处的颜色
                        }
                      ]
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: 'top',
                      offset: [15, 20],
                      align: 'left',
                      formatter: function(params) {
                        return params.value
                      },
                      textStyle: {
                        fontSize: 12,
                        color: '#2f8ef9'
                      }
                    }
                  }
                }
              }
            ]
          }
          echart.setOption(option)
        }
      },
      deep: true //对象内部属性的监听，关键。
    }
  }
}
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      width: 330px;
      display: flex;
      justify-content: space-between;
    }
  }
  .table-main {
    background: #fff;

    .table-top {
      // padding: 10px 0;
      margin-bottom: 10px;
      // height: 40px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
        float: left;
      }
      .radio {
        float: right;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  .tabs {
    //   margin-top:30px;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
.table-top {
  overflow: hidden;
}
</style>
