<template>
  <a-layout
    id="components-layout-demo-side"
    style="height: 100%"
    v-loading.fullscreen.lock.body="loginStatus"
  >
    <a-layout-header v-if="userType && userType == 'ent'">
      <layoutHeader ref="layoutHeader"></layoutHeader>
    </a-layout-header>
    <a-layout>
      <a-layout-sider v-model="collapsed" class="sider" v-if="!isGmIframe">
        <div class="realTimeDate" style="height: 100px">
          <div class="realTime">{{ realTime }}</div>
          <div class="realDate">{{ realDate }}{{ realDay }}</div>
        </div>
        <div class="menu-box">
          <a-menu
            v-if="this.menu.length > 0"
            :default-selected-keys="defaultSelectedMenu"
            mode="inline"
            theme="dark"
            :inline-collapsed="collapsed"
            @click="select"
            :multiple="false"
            :selectedKeys="selectedKeys"
            @openChange="openChange"
            :openKeys="openKeys"
          >
            <!-- :openKeys="[openKeys]" -->
            <a-menu-item
              :key="
                user.user_type == 'gov' || user.user_type == 'park'
                  ? '/workbench/superviseWorkbench'
                  : '/workbench/enterWorkbench'
              "
              @click="
                toUrlWork(
                  user.user_type == 'gov' || user.user_type == 'park'
                    ? '/workbench/superviseWorkbench'
                    : '/workbench/enterWorkbench'
                )
              "
              class="menuItem"
              v-if="showWorkbench"
            >
              <span class="title">
                <span class="anticon">
                  <img class="icon" src="../../static/icon/work.png" alt="" />
                </span>
                <span>工作台</span>
              </span>
            </a-menu-item>

            <!-- <div></div> -->

            <template v-for="item in menu">
              <a-sub-menu
                v-if="item.children"
                :key="item.url"
                popupClassName="s"
              >
                <span slot="title" class="title">
                  <span class="anticon">
                    <img class="icon" :src="'../..' + item.iconUrl" />
                  </span>
                  <span>{{
                    user.user_type == "ent" ? item.menuAlias : item.menuName
                  }}</span>
                </span>
                <a-menu-item
                  :key="subItem.url"
                  v-for="subItem in item.children"
                  @click="toUrl(subItem.url, subItem.menuFlag, subItem)"
                  :id="subItem.menuFlag"
                >
                  <!-- <a-icon :type="subItem.icon" /> -->
                  {{ subItem.menuName }}
                </a-menu-item>
              </a-sub-menu>
            </template>

            <!-- <a-menu-item
            @click="toUrlWork(1)"
            class="menuItem"
            v-if="showWorkbench"
          >
            <span class="title">
              <span class="anticon">
                <img class="icon" src="../../static/icon/work.png" alt="" />
              </span>
              <span>新双控系统测试</span>
            </span>
          </a-menu-item> -->
          </a-menu>
        </div>
      </a-layout-sider>
      <a-layout-content>
        <div
          :class="[this.$route.name == 'linkSafetyEducation' ? 'outBox' : '']"
          style="width: calc(100% - 30px); margin: 15px 0 15px 15px"
        >
          <router-view></router-view>
        </div>
      </a-layout-content>
    </a-layout>
    <!-- 全局预警弹框 -->
    <div class="modal" v-if="show && msg.type == '1'">
      <div class="dialog">
        <div class="header">
          <i class="el-icon-close close" @click="closeBox"></i>
          <div class="title">
            <div>安全生产风险警示通报</div>
            <div class="num">{{ msg.year + "年 " + msg.number + "号" }}</div>
          </div>
        </div>
        <div class="body">
          <div>{{ msg.orgName }}</div>
          <div>{{ msg.year + "年 " + msg.number + "号" }}</div>
        </div>
        <div class="container">
          <div class="dear">{{ msg.orgName || msg.enterpName + ":" }}</div>
          <div class="content">
            {{ msg.message }}
          </div>
        </div>
        <div
          class="footer"
          @click="toUrl_1('/riskAssessment/riskEarlyWarningPush')"
        >
          风险预警列表
          <i class="el-icon-right"></i>
        </div>
      </div>
    </div>
    <div class="modal" v-if="show && msg.type == '2' && msg.riskLevel == '1'">
      <div class="dialog">
        <div class="header">
          <i class="el-icon-close close" @click="closeBox"></i>
          <div class="title">
            <div class="hscolor">安全生产风险 <span>红色</span>预警</div>
            <!-- <div class="num">{{ msg.year + "年 " + msg.number + "号" }}</div> -->
          </div>
        </div>
        <div class="body" style="height: 0px; overflow: hidden; border: none">
          <div>{{ msg.orgName }}</div>
          <div>{{ msg.year + "年 " + msg.number + "号" }}</div>
        </div>
        <div class="container">
          <div class="dear">{{ msg.orgName || msg.enterpName + ":" }}</div>
          <div class="content">
            {{ msg.message }}
          </div>
        </div>
        <div
          class="footer"
          @click="toUrl_1('/riskAssessment/riskEarlyWarningPush')"
        >
          风险预警列表
          <i class="el-icon-right"></i>
        </div>
      </div>
    </div>
    <div class="modal" v-if="show && msg.type == '2' && msg.riskLevel == '2'">
      <div class="dialog dialogs">
        <div class="header">
          <i class="el-icon-close close" @click="closeBox"></i>
          <div class="title">
            <div class="ccolor">安全生产风险 <span>橙色</span>预警</div>
            <!-- <div class="num">{{ msg.year + "年 " + msg.number + "号" }}</div> -->
          </div>
        </div>
        <div class="body" style="height: 0px; overflow: hidden; border: none">
          <div>{{ msg.orgName }}</div>
          <div>{{ msg.year + "年 " + msg.number + "号" }}</div>
        </div>
        <div class="container">
          <div class="dear">{{ msg.orgName || msg.enterpName + ":" }}</div>
          <div class="content">
            {{ msg.message }}
          </div>
        </div>
        <div
          class="footer"
          @click="toUrl_1('/riskAssessment/riskEarlyWarningPush')"
        >
          风险预警列表
          <i class="el-icon-right"></i>
        </div>
      </div>
    </div>
    <div class="modal" v-if="show && msg.type == '2' && msg.riskLevel == '3'">
      <div class="dialog dialoges">
        <div class="header">
          <i class="el-icon-close close" @click="closeBox"></i>
          <div class="title">
            <div class="hcolor">安全生产风险 <span>黄色</span>预警</div>
            <!-- <div class="num">{{ msg.year + "年 " + msg.number + "号" }}</div> -->
          </div>
        </div>
        <div class="body" style="height: 0px; overflow: hidden; border: none">
          <div>{{ msg.orgName }}</div>
          <div>{{ msg.year + "年 " + msg.number + "号" }}</div>
        </div>
        <div class="container">
          <div class="dear">{{ msg.orgName + ":" }}</div>
          <div class="content">
            {{ msg.message }}
          </div>
        </div>
        <div
          class="footer"
          @click="toUrl_1('/riskAssessment/riskEarlyWarningPush')"
        >
          风险预警列表
          <i class="el-icon-right"></i>
        </div>
      </div>
    </div>
  </a-layout>
</template>
<script>
import layoutHeader from "./common/header";
import { getAsideData, getMenulist } from "@/api/reportedList";
import { getDistrictUser, getSystemKey } from "@/api/entList";
import {
  adminUserLoginInfo,
  districtByUser,
  adminUserPark,
  adminUserParkCode,
  loginMock,
} from "../api/login";
import { getEnt } from "@/api/dailySafety";
import { mapState } from "vuex";
import Bus from "@/utils/bus";
import WS from "@/utils/Ws";
import dayjs from "dayjs";

export default {
  name: "Zhome",
  components: {
    layoutHeader,
  },
  data() {
    return {
      userType: "",
      menuNew: [],
      loginStatus: false,
      path: `ws://${this.BASE_URL}/file/webSocket/warning/`,
      //path: `wss://hbwh.hbsis.gov.cn:9081/file/webSocket/warning`,
      collapsed: false,
      menu: [],
      defaultSelectedMenu: [],
      openKeys: [this.$route.path.substr(0, this.$route.path.lastIndexOf("/"))],
      selectedKeys: [
        this.$route.path.substr(this.$route.path.lastIndexOf("/")),
      ],
      rootSubmenuKeys: [],
      show: false,
      msg: {
        message: "",
        number: "",
        orgName: "",
        time: "",
        year: "",
      },
      showWorkbench: false,
      routerViewKey: "0",
      realTime: "16:21:06",
      realDate: "2024年10月12日 ",
      realDay: "星期六",
      intervalId: null,
      // 存储来自父页面的路由信息
      pendingRoute: null,
    };
  },
  created() {
    this.checkAndAutoLogin();
  },
  async mounted() {
    // 添加 postMessage 监听器
    this.setupPostMessageListener();

    if (!this.isGmIframe) {
      if (this.$route.query.token) {
        this.$store.state.login.user = {};
        this.$store.state.login.user.token_type = "bearer";
        this.$store.state.login.user.access_token = this.$route.query.token;
        this.Login(0);
      } else {
        this.Login(1);
      }
    }

    Bus.$on("warningMessage", (data) => {
      this.msg = data;
      if (this.msg) {
        this.show = true;
      } else {
        this.show = false;
      }
    });
    // this.realTime=dayjs().format("HH:mm:ss")
    this.realDate = dayjs().format("YYYY年MM月DD日");
    this.realDay = this.getWeek();
    this.intervalId = setInterval(() => {
      this.realTime = dayjs().format("HH:mm:ss");
    }, 1000);
  },
  unmounted() {
    clearInterval(this.intervalId);
  },
  beforeDestroy() {
    // 移除 postMessage 监听器
    window.removeEventListener("message", this.handlePostMessage, false);

    // 移除事件总线监听器
    Bus.$off("RELOAD_MENU_FOR_AUXILIARY_MODE");
  },
  computed: {
    ...mapState({
      isXiaZuan: (state) => state.login.isXiaZuan,
      isShowDist: (state) => state.login.isShowDist,
      userDistCode: (state) => state.login.userDistCode,
      park: (state) => state.login.park,
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
      district: (state) => state.controler.district,
      isGmIframe: (state) => state.login.isGmIframe,
    }),
  },
  watch: {
    $route: {
      handler(newVal, oldVal) {
        if (newVal) {
          if (newVal.path === "/") {
            if (newVal.query.isGmIframe) {
              this.$store.commit("login/updataIsGmIframe", true);
              return;
            } else {
              this.Login(0);
            }
          }
          this.selectedKeys = [newVal.path];
          this.openChange([
            newVal.path.substr(0, newVal.path.lastIndexOf("/")),
          ]);
        }
      },
      deep: true,
      immediate: true,
    },
    "user.user_id": {
      handler(newVal, oldVal) {
        if (newVal) {
          this.init();
          // this.$nextTick(() => {
          //   this.$refs.layoutHeader.init();
          // });
        }
      },
      immediate: true,
    },
    // 监听辅助监管模式状态变化
    "$store.state.controler.auxiliaryMode": {
      handler(newVal, oldVal) {
        if (newVal && !oldVal) {
          console.log("检测到辅助监管模式启用，重新加载菜单");
          this.getAside();
        } else if (!newVal && oldVal) {
          console.log("检测到辅助监管模式关闭，还原菜单");
          this.restoreNormalMode();
        }
      },
      immediate: false,
    },
  },
  methods: {
    // 获取用户类型
    getUserType() {
      // 优先从 store 中获取
      if (this.user && this.user.user_type) {
        return this.user.user_type;
      }
      if (this.user && this.user.userType) {
        return this.user.userType;
      }
      // 如果 store 中没有，返回当前组件的 userType
      return this.userType;
    },

    // 设置用户类型
    setUserType(userType) {
      this.userType = userType;
    },

    // 获取今天星期几
    getWeek() {
      let datas = dayjs().day();
      let week = ["日", "一", "二", "三", "四", "五", "六"];
      return "星期" + week[datas];
    },
    //获取行政区划列表
    getDistrict() {
      return new Promise((resolve, reject) => {
        getDistrictUser()
          .then((res) => {
            var child = res.data.data;
            if (child?.children.length > 0) {
              for (var j = 0; j < child.children.length; j++) {
                if (child.children[j].children.length > 0) {
                  for (var z = 0; z < child.children[j].children.length; z++) {
                    if (child.children[j].children[z].children.length < 1) {
                      //判断children的数组长度
                      child.children[j].children[z].children = undefined;
                    }
                  }
                } else {
                  child.children[j].children = undefined;
                }
              }
            } else {
              child = child;
              // child.children = undefined;
            }
            resolve([child]);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //获取监管用户账号所在地区的code
    getbyUser() {
      return new Promise((resolve, reject) => {
        districtByUser()
          .then((res) => {
            resolve(res.data.data[0]);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //获取园区用户账号所在地区的code
    getAdminUserPark() {
      return new Promise((resolve, reject) => {
        adminUserPark()
          .then((res) => {
            resolve(res.data.data);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //获取园区用户账号所在地区的code
    getAdminUserParkCode() {
      return new Promise((resolve, reject) => {
        adminUserParkCode()
          .then((res) => {
            resolve(res.data.data.distCode);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    /*
    @type 0：初次进入，1：刷新页面
     */
    Login(type) {
      this.loginStatus = true;
      adminUserLoginInfo()
        .then(async (res) => {
          this.loginStatus = false;

          // 获取并设置用户类型
          const userType = res.data.data.user_type;
          this.setUserType(userType);
          console.log("设置用户类型");
          //保存用户信息
          this.$store.commit("login/updataIsShowDist", true);
          this.$store.commit("login/updataUser", {
            ...res.data.data,
          });

          // this.$store.commit("login/updataToken", res.data.data.token);
          //获取用户账号所在地区的code
          if (userType === "sup") {
            //超级管理员
            this.showWorkbench = false;
            this.$store.commit("login/updataPark", {});
            this.$store.commit("login/updataEnter", null);
            this.$router.push({ name: "user" });
          } else if (userType === "gov") {
            //监管
            this.showWorkbench = true;
            let userCode = await this.getbyUser();
            this.$store.commit("login/updataUserDistCode", userCode.distCode);
            this.$store.commit(
              "login/updataIsXiaZuan",
              userCode.distCode == "420000" ? true : false
            );
            this.$store.commit("login/updataPark", {});
            this.$store.commit("login/updataEnter", null);
            if (type === 0) {
              this.$router.push({ path: `/workbench/superviseWorkbench` });
            }
            sessionStorage.setItem("videoApi", "*************:9100"); // 监管端对应政务外网ip *************
          } else if (userType === "ent") {
            // let token = this.$route.query.token;
            // console.log(token);
            // this.$router.push({
            //   path:`/Middler?token=bearer ${token}`
            // });
            // return;
            //企业
            this.$store.commit("login/updataPark", {});
            let getEntId = await this.getEntId();
            this.$store.commit("login/updataEnter", getEntId);
            this.$store.commit(
              "login/updataUserDistCode",
              getEntId.districtCode
            );
            if (
              getEntId.level === null ||
              getEntId.level === "" ||
              getEntId.showWorkbench === "1"
            ) {
              this.showWorkbench = false;
              if (type === 0) {
                await this.$router.push({ name: "entManagement" });
              }
            } else {
              this.showWorkbench = true;
              if (type === 0) {
                await this.$router.push({ path: `/workbench/enterWorkbench` });
              }
            }
            sessionStorage.setItem("videoApi", "************:9100"); // 企业端对应互联网ip ************
          } else if (userType === "park") {
            //园区
            this.showWorkbench = true;
            let userPark = await this.getAdminUserPark();
            let userparkCode = await this.getAdminUserParkCode();
            this.$store.commit("login/updataPark", userPark);
            this.$store.commit("login/updataIsShowDist", false);
            this.$store.commit("login/updataUserDistCode", userparkCode);
            this.$store.commit("login/updataEnter", null);
            if (type === 0) {
              this.$router.push({ name: "entManagement" });
            }
          } else {
            //如果无法识别任意以上四种用户类型，则跳转到企业管理，只允许查看企业
            if (type === 0) {
              this.$router.push({ name: "entManagement" });
            }
          }
          //如果不是重大危险源企业只保留企业管理
          //获取侧导航数据
          this.getAside();
          // 检查是否有待处理的路由跳转
          this.$nextTick(() => {
            this.navigateToPendingRoute();
          });
          //webSokect
        })
        .catch((e) => {
          this.loginStatus = false;
        });
    },
    getEntId() {
      return new Promise((resolve, reject) => {
        getEnt({})
          .then((res) => {
            if (res.data.code == 0) {
              resolve(res.data.data);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    closeBox() {
      this.show = false;
    },
    init() {
      // if (typeof WebSocket === "undefined") {
      //   this.$message("您的浏览器不支持消息推送");
      // } else {
      //   // 实例化socket
      //   this.socket = new WebSocket(this.path + this.user.user_id);
      //   // 监听socket连接
      //   this.socket.onopen = this.open;
      //   // 监听socket错误信息
      //   this.socket.onerror = this.error;
      //   // 监听socket消息
      //   this.socket.onmessage = this.getMessage;
      // }
      // `ws://${this.BASE_URL}/file/webSocket/warning/`,
      let isHttps = location.protocol == "https:"; //互联网环境
      const path = isHttps
        ? "wss://" + this.BASE_URL + "/file/webSocket/warning/"
        : "ws://" + this.BASE_URL + "/file/webSocket/warning/";
      this.socket = new WS(path + this.user.user_id);
    },
    open(msg) {
      console.log("socket连接成功");
    },
    error() {
      console.log("连接错误");
    },
    getMessage(msg) {
      if (msg.data) {
        this.msg = JSON.parse(msg.data);
        this.show = true;
      } else {
        this.show = false;
      }
      // console.log(this.msg);
    },
    send() {
      this.socket.send(params);
    },
    close() {
      console.log("socket已经关闭");
    },
    toUrl_1(url) {
      this.show = false;
      this.$router.push(url);
    },
    openChange(openKeys) {
      // 只展开一个子菜单
      const latestOpenKey = openKeys.find(
        (key) => this.openKeys.indexOf(key) === -1
      );
      if (this.rootSubmenuKeys.indexOf(latestOpenKey) === -1) {
        this.openKeys = openKeys;
      } else {
        this.openKeys = latestOpenKey ? [latestOpenKey] : [];
      }
    },
    select({ item, key, selectedKeys }) {
      // 选中项
      this.selectedKeys = [key];
    },
    getAside() {
      var that = this;
      getMenulist()
        .then((data) => {
          this.menu = data.data.data;

          // 检查是否为辅助监管模式
          if (this.$store.getters["controler/getAuxiliaryMode"]) {
            console.log("检测到辅助监管模式，过滤菜单");
            this.filterMenuForAuxiliaryMode();
          } else {
            console.log("正常模式，移除辅助监管菜单");
            this.filterNormalMode();
          }
          this.menu.forEach((item) => {
            // if (this.user.user_type == "gov") {
            //   if (item.menuName === "统计报表") {
            //     item.children.push({
            //       id: "a2da2817d8d84f2299a4894667f0510d1",
            //       menuName: "自定义报表",
            //       menuAlias: "自定义报表",
            //       url: "",
            //       iconUrl: null,
            //       parentId: "edc0f2578a7e44edbd1aea39f5892183",
            //       systemCode: "information",
            //       moduleName: "自定义报表",
            //       menuOrder: 5,
            //       menuFlag: "a2da2817d8d84f2299a4894667f0510d1",
            //       tenantId: null,
            //       children: [],
            //     });
            //   }
            // }
            // if (item.menuName === "风险管理") {
            //     item.children.push({
            //       id: "8483b21bec0e475cbba5d0ba1ee6d865",
            //       menuName: "风险管理",
            //       menuAlias: "风险管理",
            //       url: "/enterpriseManagement/riskManagement",
            //       iconUrl: null,
            //       parentId: "ca787e4ec15047fba7edfefe924da7f1",
            //       systemCode: "information",
            //       moduleName: "风险管理",
            //       menuOrder: 5,
            //       menuFlag: "8483b21bec0e475cbba5d0ba1ee6d865",
            //       tenantId: null,
            //       children: [],
            //     },
            //     );
            //   }
          });
          // if(this.enterData && ){
          //   if (this.enterData.level == null || this.enterData.level == ''){
          //       this.menu = [
          //         this.menu.find((item, index) => {
          //           return item.dangerFlag == 0
          //         })
          //       ]
          //   }
          // }

          //如果不是重大危险源企业只保留企业管理
          if (this.enterData) {
            if (this.enterData.level == null || this.enterData.level == "") {
              // this.menu = [
              //   this.menu.find((item, index) => {
              //     return item.url == '/gardenEnterpriseManagement'
              //   })
              // ]
              var menuAry = [];
              this.menu.forEach((item) => {
                if (item.dangerFlag == 0) {
                  menuAry.push(item);
                }
              });

              menuAry.forEach((el) => {
                if (el.children) {
                  el.children = el.children.filter((el) => {
                    return el.dangerFlag == 0;
                  });
                }
              });
              this.menu = menuAry;
              // this.menu = [
              //   this.menu.find((item, index) => {
              //     return item.dangerFlag == 0
              //   })
              // ]
              //提取url 作为侧导航的key值
              console.log(this.menu, "这是过滤的数据");
              for (let i = 0; i < this.menu.length; i++) {
                this.rootSubmenuKeys[i] = this.menu[i].url;
              }
            } else {
              //提取url 作为侧导航的key值
              for (let i = 0; i < this.menu.length; i++) {
                this.rootSubmenuKeys[i] = this.menu[i].url;
              }
            }
          } else {
            //提取url 作为侧导航的key值
            for (let i = 0; i < this.menu.length; i++) {
              this.rootSubmenuKeys[i] = this.menu[i].url;
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    enter() {
      this.collapsed = false;
    },
    leave() {
      this.collapsed = true;
    },
    toUrl(url, menuFlag, subItem) {
      if (subItem.menuName == "自定义报表") {
        getSystemKey({ key: "ssbi_url" }).then((res) => {
          console.log(res);
          if (res.data.status == 200) {
            let data = res.data.data.configValue;
            console.log(data, "赛思报表");
            window.open(data);
          }
        });
      } else {
        window.location.hash = "#" + menuFlag;
        //跳转
        this.routerViewKey = new Date() + "routerViewKey";
        //点击导航栏时清空updateEntId
        this.$store.commit("controler/updateEntId", "");
        this.$router.push({ path: url });
        // this.selectedKeys = [key];
      }
      //
    },
    toUrlWork(key) {
      if (key == 1) {
        window.open(
          "https://zhpt.hbsis.gov.cn:9033/ZHPT_LOGIN_SERVER//login?service=https://scyf.hbsis.gov.cn:8088/login-cas&utype=ent1"
        );
      } else {
        //跳转
        this.routerViewKey = new Date() + "routerViewKey";
        //点击导航栏时清空updateEntId
        this.$store.commit("controler/updateEntId", "");
        this.$router.push({ path: key });
        // this.selectedKeys = [key];
      }
    },
    // 免登方法
    async handleAutoLogin(username) {
      if (!username) {
        return;
      }
      const response = await loginMock(username);

      if (response.status === 200) {
        const userData = response.data.data;
        this.$store.commit("login/updataIsShowDist", true);
        console.log(userData, "userData");
        this.$store.commit("login/updataUser", {
          ...userData,
        });
        const userType = userData.userType;
        if (userType === "gov") {
          let userCode = await this.getbyUser();
          this.$store.commit("login/updataUserDistCode", userCode.distCode);
          this.$store.commit(
            "login/updataIsXiaZuan",
            userCode.distCode == "420000" ? true : false
          );
          this.$store.commit("login/updataPark", {});
          this.$store.commit("login/updataEnter", null);
          this.$router.push({
            path: `/gardenEnterpriseManagement/gatherReport`,
          });
        }

        console.log("免登成功:", userData);
      }
    },

    // 检查URL参数并执行免登
    async checkAndAutoLogin() {
      const urlParams = new URLSearchParams(window.location.search);
      const username = urlParams.get("username");

      if (username && this.isGmIframe) {
        await this.handleAutoLogin(username);
      }
      if (!this.district.length) {
        const district = await this.getDistrict();
        this.$store.dispatch("controler/setDistrict", district);
      }
    },

    // 设置 postMessage 监听器
    setupPostMessageListener() {
      window.addEventListener("message", this.handlePostMessage, false);
    },

    // 处理来自父页面的 postMessage
    handlePostMessage(event) {
      // 安全检查：验证消息来源（可根据实际情况调整）
      // if (event.origin !== 'https://expected-parent-domain.com') {
      //   return;
      // }
      try {
        const data = event.data;
        console.log("收到 postMessage:", data);

        if (data && data.type === "navigateToRoute" && data.route) {
          // 存储路由信息
          this.pendingRoute = data.route;

          // 如果已经登录，立即跳转
          if (this.user && this.user.access_token) {
            this.navigateToPendingRoute();
          }
        }
      } catch (error) {
        console.error("处理 postMessage 时出错:", error);
      }
    },

    // 跳转到待处理的路由
    navigateToPendingRoute() {
      if (this.pendingRoute) {
        console.log("跳转到路由:", this.pendingRoute);
        this.$router.push({ path: `/${this.pendingRoute}` });
        // 清除待处理的路由
        this.pendingRoute = null;
      } else {
        window.top.postMessage({ action: "loaded" }, "*");
      }
    },

    // 过滤菜单为辅助监管模式
    filterMenuForAuxiliaryMode() {
      // 定义辅助监管模式下允许的菜单项
      const allowedMenuItems = [
        "执法数据分析",
        "事故统计分析",
        "其他信息统计分析",
        "教育培训管理",
      ];

      // 辅助监管模式下不显示工作台
      this.showWorkbench = false;

      // 过滤菜单，只保留辅助监管下的指定项目
      this.menu = this.menu.filter((item) => {
        // 如果是辅助监管菜单
        if (item.menuName === "辅助监管" || item.menuAlias === "辅助监管") {
          // 过滤其子菜单
          if (item.children && item.children.length > 0) {
            item.children = item.children.filter(
              (child) =>
                allowedMenuItems.includes(child.menuName) ||
                allowedMenuItems.includes(child.menuAlias)
            );
          }
          return item.children && item.children.length > 0;
        }

        // 其他菜单都不显示
        return false;
      });

      console.log("辅助监管模式过滤后的菜单:", this.menu);

      // 自动跳转到辅助监管下的第一个路由
      this.$nextTick(() => {
        this.$router.push("/auxiliaryMode/lawEnforcementData");
      });
    },

    // 正常模式下过滤菜单，移除辅助监管菜单
    filterNormalMode() {
      // 过滤掉辅助监管菜单
      this.menu = this.menu.filter((item) => {
        // 移除辅助监管菜单
        if (item.menuName === "辅助监管" || item.menuAlias === "辅助监管") {
          return false;
        }
        return true;
      });

      console.log("正常模式过滤后的菜单:", this.menu);
    },

    // 还原到正常模式
    restoreNormalMode() {
      // 重新加载完整菜单
      this.getAside();

      // 还原工作台显示状态（根据用户类型）
      this.restoreWorkbenchVisibility();

      // 跳转到工作台或默认页面
      this.$nextTick(() => {
        if (this.showWorkbench) {
          // 根据用户类型跳转到对应的工作台
          const workbenchPath =
            this.user.user_type == "gov" || this.user.user_type == "park"
              ? "/workbench/superviseWorkbench"
              : "/workbench/enterWorkbench";
          this.$router.push(workbenchPath);
        } else {
          // 如果不显示工作台，跳转到首页或其他默认页面
          this.$router.push("/");
        }
      });
    },

    // 还原工作台显示状态
    restoreWorkbenchVisibility() {
      const userType = this.user.user_type;

      if (userType === "sup") {
        // 超级管理员
        this.showWorkbench = false;
      } else if (userType === "gov" || userType === "park") {
        // 监管用户或园区用户
        this.showWorkbench = true;
      } else if (userType === "ent") {
        // 企业用户，根据企业信息决定
        const enterData = this.$store.state.login.enterData;
        if (
          enterData &&
          (enterData.level === null ||
            enterData.level === "" ||
            enterData.showWorkbench === "1")
        ) {
          this.showWorkbench = false;
        } else {
          this.showWorkbench = true;
        }
      } else {
        this.showWorkbench = false;
      }

      console.log(
        "还原工作台显示状态:",
        this.showWorkbench,
        "用户类型:",
        userType
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.outBox {
  margin: 0 !important;
  width: 100% !important;
  height: 100%;
}
#components-layout-demo-side .logo {
  height: 32px;
  // background: rgba(255, 255, 255, 0.2);
  // margin: 16px;
}
/deep/ .ant-layout-header {
  height: 68px;
  padding: 0;
  line-height: 68px;
  background: #ffffff;
  overflow: hidden;
}
.icon {
  width: 15px;
  height: 15px;
  margin-right: 6px;
  // color: black !important;
}

.title {
  display: flex;
  align-items: center;
  // color: black;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
}

//修改菜单箭头样式
/deep/ .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::before {
  background: #333333 !important;
}
/deep/ .ant-menu-dark .ant-menu-submenu-title .ant-menu-submenu-arrow::after {
  background: #333333 !important;
}
/deep/
  .ant-menu-dark
  .ant-menu-submenu-open
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::after {
  background: #333333 !important;
}
/deep/
  .ant-menu-dark
  .ant-menu-submenu-open
  > .ant-menu-submenu-title
  > .ant-menu-submenu-arrow::before {
  background: #333333 !important;
}

.ant-menu-inline .ant-menu-item:not(:last-child) {
  margin-bottom: 0 !important;
}

//
.ant-menu-dark .ant-menu-submenu-active {
  color: #333333 !important;
}
.ant-menu-inline .ant-menu-submenu {
  border: none;
}
.ant-menu-submenu-inline .ant-menu-submenu .ant-menu-submenu-active {
  color: #333333 !important;
}

//修改侧导航样式
/deep/ .ant-layout-sider-children {
  overflow-y: auto;
  margin-left: 15px;
}

/deep/ .ant-layout-content {
  overflow-x: scroll !important;
  z-index: 1;
}

/deep/ .ant-menu-submenu-arrow::before {
  color: black !important;
}

/deep/ .ant-menu-submenu-arrow::after {
  color: black !important;
}

/deep/ .ant-menu-dark {
  // background-color: rgb(69, 77, 109);
  background: url(/static/img/assets/img/menu-bg.png) no-repeat center;
}

/deep/ .ant-layout {
  background-color: #fff;
}

/deep/ .ant-menu.ant-menu-dark .ant-menu-item-selected {
  background-color: #31539b !important;
  color: #fff !important;
}

/deep/ .ant-menu-dark .ant-menu-inline.ant-menu-sub {
  // background-color: #313a59;
  box-shadow: none;
}

/deep/ .ant-layout-sider {
  // background-color: #313a59;
  // margin: 5px 15px;
  background: url(/static/img/assets/img/left-bg.png) no-repeat center;
}

/deep/ .ant-layout-sider-trigger {
  // background-color: rgb(69, 77, 109);
  background-color: #fff !important;
  color: black !important;
}

/deep/ .ant-menu.ant-menu-dark .ant-menu-item-selected,
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
  background-color: #31539b !important;
}

/deep/ .ant-menu-inline > .ant-menu-submenu > .ant-menu-submenu-title {
  height: 59px;
  line-height: 59px;
}

/deep/ .ant-menu-sub.ant-menu-inline > .ant-menu-item {
  height: 59px;
  line-height: 59px;
  padding: 0px 16px !important;
  text-align: center;
  color: black;
  background-color: #f2f7fe;
}

/deep/ .ant-menu-inline .ant-menu-item {
  margin: 0;
}

//修改侧导航结束
.menuItem {
  height: 59px !important;
  line-height: 59px !important;
  // border-bottom: 1px #313a59 solid;
}

.modal {
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2999;

  .dialog {
    width: 800px;
    height: 530px;
    background-color: #fff;
    background-image: url(../../static/img/jingshitongbao.png);
    background-repeat: no-repeat;
    background-size: contain;
    position: fixed;
    top: 100px;
    z-index: 3000;
    margin-left: 50%;
    transform: translateX(-50%);
    position: relative;
    overflow: hidden;

    .header {
      position: relative;

      .close {
        color: #545454;
        font-size: 22px;
        font-weight: 900;
        position: absolute;
        right: 10px;
        top: 10px;
        cursor: pointer;
      }

      .title {
        position: absolute;
        width: 100%;
        height: 60px;
        display: flex;
        flex-direction: column;
        color: #f83e3e;
        text-align: center;
        font-size: 32px;
        margin-top: 50px;
        margin-left: 50%;
        transform: translateX(-50%);

        .hscolor {
          color: #333;

          span {
            display: inline-block;
            color: #fff;
            background: #f73e3e;
            padding: 0 15px;
          }
        }

        .ccolor {
          color: #333;

          span {
            display: inline-block;
            color: #fff;
            background: #ff7744;
            padding: 0 15px;
          }
        }

        .hcolor {
          color: #333;

          span {
            display: inline-block;
            color: #fff;
            background: #f7c93e;
            padding: 0 15px;
          }
        }

        .num {
          font-size: 18px;
        }
      }
    }

    .body {
      width: 90%;
      height: 30px;
      border-bottom: 2px solid #000000;
      margin-top: 160px;
      margin-left: 50%;
      transform: translateX(-50%);
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      font-size: 18px;
    }

    .container {
      margin-top: 50px;
      width: 90%;
      margin-left: 50%;
      transform: translateX(-50%);

      .dear {
        font-size: 16px;
        font-weight: 600;
        color: #333333;
      }

      .content {
        width: 100%;
        text-indent: 33px;
        line-height: 36px;
        font-size: 16px;
        color: #333333;
        margin-top: 15px;
      }
    }

    .footer {
      width: 142px;
      height: 38px;
      font-size: 14px;
      color: #ff7d4f;
      line-height: 38px;
      border-radius: 3px;
      float: right;
      border: 1px solid #ff7d4f;
      text-align: center;
      cursor: pointer;
      position: absolute;
      bottom: 30px;
      right: 30px;
    }
  }

  .dialogs {
    background-image: url(../../static/img/cs.png);
  }

  .dialoges {
    background-image: url(../../static/img/hs.png);
  }
}
.realTimeDate {
  display: flex; /* 设置为 flex 容器 */
  flex-direction: column; /* 子项目垂直排列 */
  justify-content: center; /* 在主轴上居中 */
  align-items: center; /* 在交叉轴上居中 */
  height: 138px;
  margin: 20px 0;
  z-index: 1;
  // padding: 20px 0;
  // border-radius: 10px;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(211, 232, 255, 0.15) 100%
  );
  box-shadow: 0px 6px 13px 0px rgba(0, 55, 116, 0.07);
  border-radius: 4px;
  border: 2px solid #ffffff;
  opacity: 0.99;
  background: url(/static/img/assets/img/time-bg.png) no-repeat center;
}
.realTime {
  display: block; /* 确保它们占据整个容器的宽度 */
  text-align: center; /* 文本居中 */
  font-family: Arial;
  font-weight: bold;
  font-size: 30px;
  color: #333333;
  line-height: 28px;
}
.realDate {
  display: block; /* 确保它们占据整个容器的宽度 */
  text-align: center; /* 文本居中 */
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  color: #333333;
  line-height: 28px;
}
.menu-box {
  height: 696px;
  background: linear-gradient(
    0deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(211, 232, 255, 0.15) 100%
  );
  box-shadow: 0px 6px 13px 0px rgba(0, 55, 116, 0.07);
  border-radius: 4px;
  border: 2px solid #ffffff;
  opacity: 0.99;
}
</style>
