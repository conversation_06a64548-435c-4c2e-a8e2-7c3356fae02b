{"name": "hbsecondaryprojectfront", "version": "1.0.0", "description": "A Vue.js project", "author": "", "private": true, "scripts": {"dev": "cross-env process.env.NODE_ENV=development webpack-dev-server --config build/webpack.dev.conf.js", "start": "npm run dev --progress --watch --colors --profile", "build": "rimraf dist && node build/build.js", "build:h5": "rimraf dist && node build/build-h5.js", "report": "npm run build --report"}, "dependencies": {"@babel/core": "^7.17.5", "@babel/polyfill": "^7.12.1", "@babel/preset-env": "^7.16.11", "@fullcalendar/core": "^5.11.3", "@fullcalendar/daygrid": "^5.11.3", "@fullcalendar/interaction": "^5.11.3", "@fullcalendar/timegrid": "^5.11.3", "@fullcalendar/vue": "^5.11.2", "animate.css": "^4.1.1", "ant-design-vue": "^1.7.8c", "axios": "^0.21.1", "babel-plugin-dynamic-import-node": "^2.3.3", "css-loader": "^0.28.11", "dayjs": "^1.11.2", "default-passive-events": "^2.0.0", "docx-preview": "^0.1.8", "echarts": "^4.8.0", "element-ui": "^2.15.6", "flv-h265": "^1.7.0", "jquery": "^3.6.0", "js-cookie": "^3.0.1", "jszip": "^3.10.0", "qrcode": "^1.5.4", "quill": "^1.3.7", "signature_pad": "^4.0.6", "vue": "^2.5.2", "vue-class-component": "^7.2.6", "vue-property-decorator": "^9.1.2", "vue-quill-editor": "^3.0.6", "vue-router": "^3.0.1", "vue-style-loader": "^3.1.2", "vue-tsx-support": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.6.0", "vuex-along": "^1.2.11"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/runtime-corejs2": "^7.16.7", "@types/node": "^17.0.12", "@typescript-eslint/parser": "^4.31.1", "@webpack-cli/serve": "^1.5.2", "autoprefixer": "^7.1.2", "awesome-typescript-loader": "^5.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^8.0.0", "babel-plugin-component": "^1.1.1", "babel-plugin-import": "^1.13.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-polyfill": "^6.26.0", "babel-preset-es2015": "^6.24.1", "cache-loader": "^4.1.0", "chalk": "^2.0.1", "clean-webpack-plugin": "^4.0.0", "compression-webpack-plugin": "^1.1.12", "copy-webpack-plugin": "^4.0.1", "cross-env": "^7.0.3", "cross-spawn": "^7.0.3", "crypto-js": "^4.0.0", "css-loader": "^0.28.0", "extract-text-webpack-plugin": "^4.0.0-beta.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "happypack": "^5.0.1", "html-webpack-plugin": "^3.2.0", "mini-css-extract-plugin": "^2.3.0", "node-notifier": "^5.1.2", "node-sass": "^4.14.1", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "prettier": "^2.5.1", "progress-bar-webpack-plugin": "^2.1.0", "progress-webpack-plugin": "^1.0.12", "rimraf": "^2.6.0", "sass-loader": "^7.3.1", "scss-loader": "^0.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "style-loader": "^2.0.0", "ts-loader": "^8.2.0", "tslib": "^2.3.1", "tslint": "^6.1.3", "tslint-config-standard": "^9.0.0", "tslint-loader": "^3.5.4", "typescript": "^4.5.4", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-loader": "^15.9.8", "vue-loader-plugin": "^1.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-cli": "^4.8.0", "webpack-dev-server": "^4.2.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"]}