<template>
  <div class="count-report-add">
    <!-- 添加头部标题 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box" @click="goBack">
              <a-icon type="home" theme="filled" class="icon" />
              采集报表管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <span>编辑采集报表</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
      <div class="header-right">
        <el-button type="primary" size="small" @click="submitForm"
          >保存</el-button
        >
      </div>
    </div>

    <!-- 内容区域包裹 -->
    <div class="content">
      <!-- 报表名称 -->
      <div class="report-name">
        <el-input
          v-model="reportName"
          style="width: 500px"
          placeholder="请输入报表名称"
        />
      </div>
      <!-- 报表设计器 -->
      <v-form-designer
        :form-json="formJson"
        :form-data="formData"
        :option-data="optionData"
        ref="designer"
      ></v-form-designer>
    </div>
  </div>
</template>

<script>
import { getReportDesignById, editReportDesign } from "@/api/smartReport";

export default {
  name: "CountReportEdit",
  data() {
    return {
      reportId: "", // 报表ID
      reportName: "", // 报表名称
      formJson: {},
      formData: {},
      optionData: {},
      emptyFormJson: {
        widgetList: [],
        formConfig: {
          modelName: "formData",
          refName: "vForm",
          rulesName: "rules",
          labelWidth: 80,
          labelPosition: "left",
          size: "small",
          labelAlign: "label-left-align",
          cssCode: "",
          customClass: "",
          functions: "",
          layoutType: "PC",
          jsonVersion: 3,
        },
      },
    };
  },
  methods: {
    // 提交表单
    submitForm() {
      const formJson = this.$refs.designer.getFormJson();
      editReportDesign({
        id: this.reportId,
        gatherName: this.reportName,
        textContent: JSON.stringify(formJson),
      })
        .then((res) => {
          this.$message.success("保存成功");
          this.goBack();
        })
        .catch((err) => {
          this.$message.error("保存失败");
        });
    },

    // 初始化数据
    async initData(data) {
      if (!data || !data.id) {
        this.$message.error("参数错误");
        return;
      }
      this.reportId = data.id;
      this.reportName = data.gatherName;
      // 获取设计器实例并隐藏不需要的元素
      const designer = this.$refs.designer;
      designer.setFormJson(this.emptyFormJson);
      const mainHeader = designer.$el.querySelector(".main-header");
      const tabFormLib = designer.$el.querySelector("#tab-formLib");
      mainHeader.style.display = "none";
      tabFormLib.style.display = "none";

      try {
        const res = await getReportDesignById({
          id: data.id,
        });
        if (res.data.status === 200) {
          if (res.data.data.textContent) {
            designer.setFormJson(JSON.parse(res.data.data.textContent));
          }
        }
      } catch (error) {
        console.error("获取报表详情失败:", error);
        this.$message.error("获取报表详情失败");
      }
    },

    goBack() {
      this.$emit("goBack");
    },
  },
};
</script>

<style lang="scss" scoped>
.count-report-add {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }

      .icon-box {
        cursor: pointer;

        &:hover {
          color: #3977ea;

          .icon {
            color: #3977ea;
          }
        }
      }
    }

    .header-right {
      display: flex;
      align-items: center;
    }
  }

  .content {
    height: calc(100vh - 75px);
    overflow-y: auto;
    .report-name {
      height: 40px;
      line-height: 40px;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;
    }
  }

  /deep/ .el-aside.side-panel {
    width: 300px !important;
  }

  /deep/ .el-container.full-height {
    overflow: auto;
  }
}
</style>
