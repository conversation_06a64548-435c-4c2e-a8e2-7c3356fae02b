<template>
  <div class="education-training">
    <div class="search-container">
      <el-input v-model.trim="searchParams.courseName" size="small" placeholder="请输入课程名称" class="input"
        clearable></el-input>
      <el-select v-model="searchParams.courseType" placeholder="请选择题目类型" size="small" clearable>
        <el-option v-for="item in examinationTypes" :key="item.value" :value="item.value"
          :label="item.label"></el-option>
      </el-select>
      <el-button type="primary" size="small" @click="handleSearch">查询</el-button>
    </div>
    <div class="table-container">
      <el-table :data="tableData" v-loading="loading" :header-cell-style="headerCellStyle" border style="width: 100%" ref="multipleTable">
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column prop="courseno" label="课程" align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="tmlx" label="题目类型" width="120" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.tmlx=='1'?'单选':'多选' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="options" label="题干内容" :show-overflow-tooltip="true" align="center">
        </el-table-column>
        <el-table-column prop="content" label="选项内容" :show-overflow-tooltip="true" align="center">
        </el-table-column>
        <el-table-column prop="answer" label="答案" width="80" align="center">
        </el-table-column>
        <el-table-column prop="analysis" label="题目解析" width="120" align="center" />
        <el-table-column prop="lablename" label="所属标签" width="120" align="center" />
        <el-table-column prop="operdate" label="更新时间" width="160" align="center" />
        <el-table-column prop="usageNum" label="使用次数" width="80" align="center" />
        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit('view', scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination @current-change="handleCurrentChange" :current-page.sync="searchParams.nowPage"
          :page-size="searchParams.pageSize" layout="total, prev, pager, next" background :total="total">
        </el-pagination>
      </div>
    </div>
    <questionDialog v-if="showDialog" :visible="showDialog" :education-item="educationItem"
      @closeBoolean="showDialog = false" />
  </div>
</template>

<script>
import { questionsPage, questionsDelete } from "@/api/enterpEducation";
import questionDialog from "./components/questionDialog.vue";
import { get } from "http";
export default {
  components: {
    questionDialog,
  },
  data() {
    return {
      loading: false,
      searchParams: {
        courseName: "",
        courseType: "",
        nowPage: 1,
        pageSize: 10,
      },
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      total: 0,
      tableData: [],
      educationItem: {},
      showDialog: false,
      dialogType: "add",
      examinationTypes: [
        { label: "单选", value: "1" },
        { label: "多选", value: "2" },
      ],
    };
  },
  mounted() {
    this.getData();

  },
  methods: {
    handleSearch() {
      this.searchParams.nowPage = 1;
      this.getData();
    },
    handleCurrentChange(val) {
      this.searchParams.nowPage = val;
      this.getData();
    },
    // 删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          questionsDelete(row.id).then((res) => {
            if (res.data.status == '200') {
              this.getData();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            }
          });

        })
        .catch(() => { });
    },
    // 编辑 查看
    handleEdit(type, row) {
      this.dialogType = type;
      this.showDialog = true;
      if (row) {
        this.educationItem = row;
      }
    },
    async getData() {
      const params = { ...this.searchParams };
      this.loading = true;
      await questionsPage(params).then((res) => {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
      }).finally(() => {
        this.loading = false;
      });
    },
   
  },
};
</script>

<style lang="scss" scoped>
.education-training {
  .search-container {
    min-width: 650px;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;

    .input {
      width: 200px;
    }

    >* {
      margin-right: 15px;
    }
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
