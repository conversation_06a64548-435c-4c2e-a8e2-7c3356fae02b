<template>
  <div class="alarmAnalysis">
    <div v-show="!TrendChartBool">
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span @click="goToSafety"
                ><a-icon type="home" theme="filled" class="icon" /> 企业风险分析
              </span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="showBreak">{{
              areaName
            }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <el-select
            v-model="level"
            multiple
            placeholder="请选择危险源等级"
            size="mini"
            style="width: 270px"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
        <CA-button type="primary" size="mini" plain @click="gotoTrendAnalysis"
          ><a-icon type="bar-chart" class="chart-icon" />趋势分析</CA-button
        >
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>企业风险分析</h2>
          <!-- <div v-show="!showtable">
                <el-radio-group size="small" v-model="modees">
                    <el-radio-button label="物联监测"></el-radio-button>
                    <el-radio-button label="视频监测"></el-radio-button>
                </el-radio-group>
            </div> -->
          <CA-RadioGroup
            class="radio"
            v-model="mode"
            backgroundColor="#F1F6FF"
            border="1px solid rgba(57, 119, 234, 0.2)"
          >
            <CA-radio
              :label="{
                src: '../../../static/img/liebiao_icon.png',
                style: 'width:15px;height:15px',
              }"
              :labelTwo="{
                src: '../../../static/img/liebiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
              value="统计"
              bgColorActive="#409eff"
            >
            </CA-radio>
            <CA-radio
              :label="{
                src: '../../../static/img/tubiao_icon.png',
                style: 'width:15px;height:15px',
              }"
              :labelTwo="{
                src: '../../../static/img/tubiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
              bgColorActive="#409eff"
              value="图表"
            >
            </CA-radio>
          </CA-RadioGroup>
        </div>
        <div v-show="showtable">
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              @sort-change="changeTableSort"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <el-table-column type="selection" width="50" align="center">
              </el-table-column>
              <el-table-column label="行政区划" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <!-- 仙桃市,潜江市,天门市,神农区 禁止下钻 -->
                  <div
                    v-if="
                      row.areaCode == 429004 ||
                      row.areaCode == 429005 ||
                      row.areaCode == 429006 ||
                      row.areaCode == 429021
                    "
                  >
                    {{ row.areaName }}
                  </div>
                  <div v-else>
                    <span
                      v-if="$index != 0 && !showBreak && isXiaZuan"
                      @click="xiaZuan(row.areaCode, row.areaName)"
                      style="color: #3977ea; cursor: pointer"
                      >{{ row.areaName }}</span
                    >
                    <span v-else> {{ row.areaName }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="接入企业数"
                sortable="custom"
                prop="linkedNumber"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    v-if="row.linkedNumber != 0 || tableData.length==1"
                    @click="openDialog(row.areaCode, 0, row.areaName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.linkedNumber }}</span
                  >
                  <span v-else>{{ row.linkedNumber }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="重大风险企业数"
                sortable="custom"
                prop="riskOne"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    v-if="row.riskOne != 0 || tableData.length==1"
                    @click="openDialog(row.areaCode, 1, row.areaName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.riskOne }}</span
                  >
                  <span v-else>{{ row.riskOne }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="较大风险企业数"
                sortable="custom"
                prop="riskTwo"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    v-if="row.riskTwo != 0 || tableData.length==1"
                    @click="openDialog(row.areaCode, 2, row.areaName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.riskTwo }}</span
                  >
                  <span v-else>{{ row.riskTwo }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="一般风险企业数"
                sortable="custom"
                prop="riskThree"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    v-if="row.riskThree != 0 || tableData.length==1"
                    @click="openDialog(row.areaCode, 3, row.areaName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.riskThree }}</span
                  >
                  <span v-else>{{ row.riskThree }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="较低风险企业数"
                sortable="custom"
                prop="riskFour"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    v-if="row.riskFour != 0 || tableData.length==1"
                    @click="openDialog(row.areaCode, 4, row.areaName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.riskFour }}</span
                  >
                  <span v-else>{{ row.riskFour }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <!-- <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage"
                    :page-size="100"
                    layout="total, prev, pager, next"
                    :total="1000"
                >
                </el-pagination> -->
          </div>
        </div>
        <div v-show="!showtable">
          <div id="myCharted"></div>
          <!-- :style="{ width: '100%',height: '100%',minHeight: '100%', margin: '0 auto' }" -->
        </div>
      </div>
    </div>
    <TrendChart
      v-show="TrendChartBool"
      @RunningState="TrendChartFun"
      ref="TrendChart"
    ></TrendChart>
    <DetailTable ref="DetailTable"></DetailTable>
  </div>
</template>
<script>
import DetailTable from "./detailTable";
import TrendChart from "./trendChart";
import {
  getEnRiskListData,
  getCimRiskAnalysisExportExcel,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
import dayjs from 'dayjs';
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {
    DetailTable:() => import("./detailTable"),
    TrendChart:() => import("./trendChart"),
  },
  data() {
    return {
      options: [
        {
          value: "1",
          label: "一级",
        },
        {
          value: "2",
          label: "二级",
        },
        {
          value: "3",
          label: "三级",
        },
        {
          value: "4",
          label: "四级",
        },
      ],
      tableData: [],
      level: ["1", "2", "3", "4"],
      mode: "统计",
      showtable: true,
      currentPage: 1,
      total: 0,
      distCode: this.$store.state.login.userDistCode,
      showBreak: false,
      areaName: "",
      loading: false,
      selection: [],
      areaName: "",
      TrendChartBool: false,
    };
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist
    }),
  },
  methods: {
    getEnRiskList() {
      this.loading = true;
      getEnRiskListData({
        level: this.level,
        distCode: this.distCode,
        startDate:dayjs(new Date()).format("YYYY-MM-DD"),
        endDate:dayjs(new Date()).format("YYYY-MM-DD"),
      }).then((res) => {
        debugger
        if (res.status === 200) {
          this.loading = false;
          this.tableData = res.data.data;
          this.total = res.data.data.length;
        }
      });
    },
    // 导出
    exportExcel() {
      let list = [this.distCode, ...this.selection];
      getCimRiskAnalysisExportExcel({
        level: this.level,
        distCode: this.distCode,
        distCodes: list.length <= 1 ? null : list,
        startDate:dayjs(new Date()).format("YYYY-MM-DD"),
        endDate:dayjs(new Date()).format("YYYY-MM-DD"),
      }).then((response) => {
        // 处理返回的文件流
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业风险分析" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].areaCode;
      }
    },

    xiaZuan(areaCode, areaName) {
      this.areaName = areaName;
      this.showBreak = true;
      this.distCode = areaCode;
      this.getEnRiskList();
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      //   this.level = "";
      this.distCode = this.$store.state.login.userDistCode;
      this.getEnRiskList();
    },
    // 排序
    changeTableSort(column) {
      var fieldName = column.prop;
      var sortingType = column.order;
      var data = [...this.tableData];
      var shiftTotal = [];
      if (data[0].areaCode == this.$store.state.login.userDistCode) {
        shiftTotal = data.shift();
      } else if ((this.areaCode = data[0].areaCode)) {
        shiftTotal = data.shift();
      }
      //按照降序排序
      if (sortingType == "descending") {
        data = data.sort((a, b) => b[fieldName] - a[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        data = data.sort((a, b) => a[fieldName] - b[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      } else {
        this.getEnRiskList();
      }
    },
    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode=this.isShowDist?distCode:null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
      this.$refs.DetailTable.getDistrict();
    },
    search() {
        this.currentPage = 1;
      this.getEnRiskList();
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data;
      this.getEnRiskList();
    },
    gotoTrendAnalysis() {
      this.TrendChartBool = true;
      this.$nextTick(() => {
        this.$refs.TrendChart.getData();

      });
    },
    handleSelectionChange(val) {
      // console.log(val);
    },
    handleClick() {
      // console.log(123);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getEnRiskList();
    this.$setEchart("myCharted", 250, 250);
  },
  watch: {
    mode: {
      handler(newValue, oldValue) {
        if (newValue == "统计") {
          this.showtable = true;
        } else {
          this.showtable = false;
        }
      },
      deep: true,
      immediate: true,
    },
    //观察option的变化
    tableData: {
      handler(newVal, oldVal) {
        if (newVal) {
          let myChart = this.$echarts.init(
            document.getElementById("myCharted")
          );
          var getname = [];
          var getvalue = [];
          var getvalue1 = [];
          var getvalue2 = [];
          var getvalue3 = [];
          var getvaluesum = [];
          if(this.$store.state.login.user.user_type == 'park'){
            for (var i = 0; i < newVal.length; i++) {
                getname.push(newVal[i].areaName);
                getvalue.push(newVal[i].riskOne);
                getvalue1.push(newVal[i].riskTwo);
                getvalue2.push(newVal[i].riskThree);
                getvalue3.push(newVal[i].riskFour);
            }
          }else{
            for (var i = 1; i < newVal.length; i++) {
                getname.push(newVal[i].areaName);
                getvalue.push(newVal[i].riskOne);
                getvalue1.push(newVal[i].riskTwo);
                getvalue2.push(newVal[i].riskThree);
                getvalue3.push(newVal[i].riskFour);
            }
          }
          // console.log(newVal);
          for (var i = 0; i < getname.length; i++) {
            getvaluesum[i] =
              getvalue[i] + getvalue1[i] + getvalue2[i] + getvalue3[i];
          }

          var maxnum = Math.max.apply(null, getvaluesum);
          var maxlen = Math.pow(10, String(Math.ceil(maxnum)).length - 2);
          if (maxnum >= 5) {
            var max = Math.ceil(maxnum / (0.95 * maxlen)) * maxlen;
          } else {
            var max = 5;
          }
          var option = {
            grid: {
              top: "33",
              right: "15",
              left: "50",
              bottom: "55",
            },
            toolbox: {
              feature: {
                saveAsImage: {},
              },
            },
            tooltip: {
              trigger: "axis",
              //   axisPointer: {
              //     type: "none",
              //   },
              //   formatter:
              //     "{b0}</br>{a0}个数：{c0}个</br>{a1}个数：{c1}个</br>{a2}个数：{c2}个</br>{a3}个数：{c3}个",
            },
            legend: {
              type: "scroll",
              right: "80",
              top: "0",
              data: ["重大风险", "较大风险", "一般风险", "低风险"],
              itemGap: 25,
              itemWidth: 16,
              itemHeight: 16,
              textStyle: {
                fontSize: "13",
                color: "#666666",
              },
            },
            xAxis: [
              {
                data: getname,
                axisLabel: {
                  margin: 10,
                  rotate: 20,
                  color: "#666666",
                  textStyle: {
                    fontSize: 13,
                    fontWeight: 400,
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#B0C5DB",
                  },
                },
                axisTick: {
                  show: false,
                },
              },
            ],
            yAxis: [
              {
                min: 0,
                max: max, // 计算最大值
                interval: max / 5, //  平均分为5份
                splitNumber: 5,
                name: "单位：个",
                nameTextStyle: {
                  color: "#999999",
                  fontSize: 13,
                  padding: [0, 0, 0, 5],
                },
                axisLabel: {
                  color: "#666666",
                  textStyle: {
                    fontSize: 13,
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    color: "#CCDBEB",
                    type: "dashed",
                    opacity: 0.5,
                  },
                },
              },
            ],
            series: [
              {
                name: "重大风险",
                type: "bar",
                data: getvalue,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#f65959", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#f65959", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#f65959",
                      },
                    },
                  },
                },
              },
              {
                name: "较大风险",
                type: "bar",
                data: getvalue1,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#f98e2f", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#f98e2f", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#f98e2f",
                      },
                    },
                  },
                },
              },
              {
                name: "一般风险",
                type: "bar",
                data: getvalue2,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#f9c22f", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#f9c22f", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#f9c22f",
                      },
                    },
                  },
                },
              },
              {
                name: "低风险",
                type: "bar",
                data: getvalue3,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#2f8ef9", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#2f8ef9", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#2f8ef9",
                      },
                    },
                  },
                },
              },
            ],
          };
          option && myChart.setOption(option);
        } else {
          let myChart = this.$echarts.init(
            document.getElementById("myCharted")
          );
          var getname = [];
          var getvalue = [];
          var getvalue1 = [];
          var getvalue2 = [];
          var getvalue3 = [];
          var getvaluesum = [];
          if(this.$store.state.login.user.user_type == 'park'){
            for (var i = 0; i < oldVal.length; i++) {
                getname.push(oldVal[i].areaName);
                getvalue.push(oldVal[i].riskOne);
                getvalue1.push(oldVal[i].riskTwo);
                getvalue2.push(oldVal[i].riskThree);
                getvalue3.push(oldVal[i].riskFour);
            }
          }else{
            for (var i = 1; i < oldVal.length; i++) {
                getname.push(oldVal[i].areaName);
                getvalue.push(oldVal[i].riskOne);
                getvalue1.push(oldVal[i].riskTwo);
                getvalue2.push(oldVal[i].riskThree);
                getvalue3.push(oldVal[i].riskFour);
            }
          }
          for (var i = 0; i < getname.length; i++) {
            getvaluesum[i] =
              getvalue[i] + getvalue1[i] + getvalue2[i] + getvalue3[i];
          }

          var maxnum = Math.max.apply(null, getvaluesum);
          var maxlen = Math.pow(10, String(Math.ceil(maxnum)).length - 2);
          if (maxnum >= 5) {
            var max = Math.ceil(maxnum / (0.95 * maxlen)) * maxlen;
          } else {
            var max = 5;
          }
          var option = {
            grid: {
              top: "33",
              right: "15",
              left: "50",
              bottom: "55",
            },
            tooltip: {
              trigger: "axis",
              //   axisPointer: {
              //     type: "none",
              //   },
              //   formatter:
              //     "{b0}</br>{a0}个数：{c0}个</br>{a1}个数：{c1}个</br>{a2}个数：{c2}个</br>{a3}个数：{c3}个",
            },
            legend: {
              type: "scroll",
              right: "10",
              top: "0",
              data: ["重大风险", "较大风险", "一般风险", "低风险"],
              itemGap: 25,
              itemWidth: 16,
              itemHeight: 16,
              textStyle: {
                fontSize: "13",
                color: "#666666",
              },
            },
            xAxis: [
              {
                data: getname,
                axisLabel: {
                  //interval: 0,
                  // formatter: function(value) {
                  //     var ret = ""; //拼接加\n返回的类目项
                  //     var maxLength = 5; //每项显示文字个数
                  //     var valLength = value.length; //X轴类目项的文字个数
                  //     var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                  //     if (rowN > 1) //如果类目项的文字大于5,
                  //     {
                  //         for (var i = 0; i < rowN; i++) {
                  //             var temp = ""; //每次截取的字符串
                  //             var start = i * maxLength; //开始截取的位置
                  //             var end = start + maxLength; //结束截取的位置
                  //             //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                  //             temp = value.substring(start, end) + "\n";
                  //             ret += temp; //凭借最终的字符串
                  //         }
                  //         return ret;
                  //     } else {
                  //         return value;
                  //     }
                  // },
                  margin: 10,
                  rotate: 20,
                  color: "#666666",
                  textStyle: {
                    fontSize: 13,
                    fontWeight: 400,
                  },
                },
                axisLine: {
                  lineStyle: {
                    color: "#B0C5DB",
                  },
                },
                axisTick: {
                  show: false,
                },
              },
            ],
            yAxis: [
              {
                min: 0,
                max: max, // 计算最大值
                interval: max / 5, //  平均分为5份
                splitNumber: 5,
                name: "单位：个",
                nameTextStyle: {
                  color: "#999999",
                  fontSize: 13,
                  padding: [0, 0, 0, 5],
                },
                axisLabel: {
                  color: "#666666",
                  textStyle: {
                    fontSize: 13,
                  },
                },
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                splitLine: {
                  lineStyle: {
                    color: "#CCDBEB",
                    type: "dashed",
                    opacity: 0.5,
                  },
                },
              },
            ],
            series: [
              {
                name: "重大风险",
                type: "bar",
                data: getvalue,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#f65959", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#f65959", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#f65959",
                      },
                    },
                  },
                },
              },
              {
                name: "较大风险",
                type: "bar",
                data: getvalue1,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#f98e2f", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#f98e2f", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#f98e2f",
                      },
                    },
                  },
                },
              },
              {
                name: "一般风险",
                type: "bar",
                data: getvalue2,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#f9c22f", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#f9c22f", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#f9c22f",
                      },
                    },
                  },
                },
              },
              {
                name: "低风险",
                type: "bar",
                data: getvalue3,
                stack: "各专业本科生年级分布",
                barWidth: "20px",
                itemStyle: {
                  normal: {
                    color: {
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: "#2f8ef9", // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: "#2f8ef9", // 100% 处的颜色
                        },
                      ],
                    },
                    barBorderRadius: [0, 0, 0, 0],
                    label: {
                      show: false,
                      position: "top",
                      offset: [15, 20],
                      align: "left",
                      formatter: function (params) {
                        return params.value;
                      },
                      textStyle: {
                        fontSize: 12,
                        color: "#2f8ef9",
                      },
                    },
                  },
                },
              },
            ],
          };
          option && myChart.setOption(option);
        }
      },
      deep: true, //对象内部属性的监听，关键。
      // immediate:true
    },
  },
};
</script>
<style lang="scss" scoped>
.alarmAnalysis {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      width: 400px;
      display: flex;
      justify-content: space-between;
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // padding: 10px 0;
      margin-bottom: 10px;
      height: 40px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
        float: left;
      }
      .radio {
        float: right;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
