<template>
  <div v-loading="loading">
    <div class="work-heard">
      <h2>物联报警推送</h2>
       <span @click="goEnt">更多</span>
    </div>


    <!-- <div class="tabBox">
       <span class="goEntStyle" @click="goEnt">更多</span>
     <el-tabs v-model="activeTabClass" @tab-click="handleClickActiveTab()">
            <el-tab-pane label="物联报警推送" name="1"> 
                <div class="wlbj-list" v-if="listData.length > 0">
                <div class="wlbj-item" v-for="(item, index) in listData" :key="index">
                  <h3><i></i>{{ item.creatTime }}</h3>
                  <p>{{ item.message }}</p>
                </div>
              </div>
              <div class="null" v-else>
                <img
                  src="/static/img/assets/img/noData.png"
                  style="width: 80%; margin-top: 30px"
                />
              </div>          
            </el-tab-pane>
            <el-tab-pane label="视频智能分析列表" name="2">
             <div class="wlbj-list" v-if="listData.length > 0">
                <div class="wlbj-item" v-for="(item, index) in listData" :key="index">
                  <h3><i></i>{{ item.creatTime }}</h3>
                  <p>{{ item.message }}</p>
                </div>
              </div>
              <div class="null" v-else>
                <img
                  src="/static/img/assets/img/noData.png"
                  style="width: 80%; margin-top: 30px"
                />
              </div>  
            </el-tab-pane>
          </el-tabs>
    </div> -->
   





    <div class="wlbj-list" v-if="listData.length > 0">
      <div class="wlbj-item" v-for="(item, index) in listData" :key="index">
        <h3><i></i>{{ item.creatTime }}</h3>
        <p>{{ item.message }}</p>
      </div>
    </div>
    <div class="null" v-else>
      <img
        src="/static/img/assets/img/noData.png"
        style="width: 80%; margin-top: 30px"
      />
    </div>
  </div>
</template>
<script>
import { getMessageListData } from "@/api/workbench";
import { formatTime } from "@/utils/index";
export default {
  components: {},
  data() {
    return {
      activeTabClass:"1",
      enterpId: "",
      listData: [],
      loading: false,
    };
  },
  methods: {
    handleClickActiveTab(){

    },
    getData(id) {
      this.enterpId = id;
      this.loading = true;
      getMessageListData().then((res) => {
        this.loading = false;
        if (res.data.code == 0) {
          this.listData = res.data.data.records;
          this.listData.forEach((item) => {
            item.creatTime = formatTime(item.creatTime);
          });
        }
      });
    },
     goEnt() {  
        this.$router.push({name: "entManagement" });
        this.$store.commit("login/updataActiveName", 'IotDetection');
        this.$store.commit("controler/updateEntId", this.enterpId);
        this.$store.commit("controler/updateEntModelName", "IotDetection");   
     }
    // goEnt() {     
    //   if(this.activeTabClass==1){      
    //     this.$router.push({name: "entManagement" });
    //     this.$store.commit("login/updataActiveName", 'IotDetection');
    //     this.$store.commit("controler/updateEntId", this.enterpId);
    //     this.$store.commit("controler/updateEntModelName", "IotDetection");
    //   }else{
    //     this.$router.push({name:'videoAnalysis'})
    //   }      
    // },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {},
};
</script>
<style lang="scss" scoped>
.tabBox{
  position: relative;
  width: 94%;
  margin: 0 auto;
  .goEntStyle{
    position: absolute;
    right: 0;
    top: 10px;
    cursor: pointer;
    z-index: 999999;

  }
}
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
    cursor: pointer;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.null {
  display: flex;
  justify-content: center;
  align-items: center;
}
.wlbj-list {
  width: 94%;
  padding: 0 2% 0 1%;
  margin: 0 auto;
  height: 275px;
  overflow-y: auto;
  .wlbj-item {
    h3 {
      font-size: 14px;
      color: #666;
      height: 30px;
      line-height: 30px;
      margin-bottom: 0;
      i {
        display: inline-block;
        width: 16px;
        height: 19px;
        background: url("/static/img/assets/img/bjtsxx.png") center center
          no-repeat;
        background-size: contain;
        margin-right: 15px;
      }
    }
    p {
      font-size: 14px;
      color: #545c65;
      padding-bottom: 10px;
      border-bottom: 1px solid #dee3ed;
      line-height: 24px;
    }
  }
}
</style>
