<template>
 <div>  
   <!-- 111 -->
   <iframe class="iframeBox" src="https://jy.hbsis.gov.cn/#/web/home" style="border:0" width='100%' height="100%"></iframe>

 </div>
</template>
<script>
import {
  getCompanyProjectList, //列表查询 
} from "@/api/companyParticularJob";

import { getInformationBasicInfo } from "@/api/entList";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");


import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: { 
  },
  data() {
    return {        
    };
  },

  filters:{  
  },

  methods:{},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.getData();
    // this.serchStatusList();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.iframeBox {
    height:calc(100vh - 80px);
    padding-top: 0;
    width:100%;
    // background: #f3f6f8;
}

</style>

