<template>
  <el-dialog title="查看考题" :visible="visible" @close="closeBoolean(false)" width="800px" top="5vh"
    :close-on-click-modal="true">
    <div class="questionDialog">
      <div class="dialog-title">单选题</div>
      <div class="examination-item">
        <div class="question">
          <div class="question-title">
            {{ question.content }}
          </div>
          <div class="question-title">
            {{ question.options }}
          </div>
        </div>
        <div class="answer">
          <!-- <div class="answer-item">考生答案：{{ question.answer }} </div> -->
          <div class="answer-item">
            解析：{{ question.analysis }}
          </div>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button type="primary" size="small" @click="handleSubmit">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { questionsDetail } from "@/api/enterpEducation";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    educationItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      question: {
      
      },
    };
  },
  created() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      questionsDetail(this.educationItem.id).then((res) => {
        this.question = res.data.data;
      });
    },
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    handleSubmit() { },
  },
};
</script>

<style lang="scss" scoped>
.questionDialog {
  height: 320px;

  .dialog-title {
    font-weight: 550;
    margin-bottom: 20px;
  }

  .examination-item {
    padding-right: 20px;
    box-sizing: border-box;

    .question {
      padding-left: 20px;
      .question-title {
        margin-bottom: 20px;
      }
    }

    .answer {
      flex: 1;
      font-weight: 550;

      .answer-item {
        margin-bottom: 10px;
      }
    }
  }
}

.footer {
  text-align: center;
}
</style>
