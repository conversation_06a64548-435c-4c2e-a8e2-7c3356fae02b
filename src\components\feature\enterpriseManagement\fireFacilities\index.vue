<template>
  <div class="fire-facilities">
    <div class="content">
      <!-- 左侧目录树 -->
      <div class="left">
        <div class="tree-header">
          <span>目录</span>
          <el-button
            type="text"
            icon="el-icon-plus"
            @click="showAddDialog"
          ></el-button>
        </div>
        <div class="tree-content">
          <div class="tree-item" v-for="(item, index) in menuList" :key="index">
            <div class="item-title" :class="{ active: currentIndex === index }">
              <span class="name" @click="handleClick(index)">{{ item }}</span>
              <div class="operations">
                <el-button
                  type="text"
                  icon="el-icon-edit"
                  @click.stop="handleEditMenu(index)"
                ></el-button>
                <el-button
                  type="text"
                  icon="el-icon-delete"
                  @click.stop="handleDeleteMenu(index)"
                ></el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧表格 -->
      <div class="right">
        <div class="table-header">
          <div class="title">{{ currentMenuName }}</div>
          <el-button type="primary" size="small" @click="handleAddItem"
            >新建</el-button
          >
        </div>
        <el-table
          :data="currentTableData"
          style="width: 100%"
          height="60vh"
          border
          v-loading="loading"
        >
          <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column>
          <el-table-column prop="name" label="名称" align="center">
          </el-table-column>
          <el-table-column prop="detailInfo" label="描述" align="center">
          </el-table-column>
          <el-table-column prop="drawType" label="绘制形式" align="center">
          </el-table-column>
          <el-table-column label="操作" width="180" align="center">
            <template slot-scope="scope">
              <el-button type="text" @click="handleEdit(scope.row)"
                >编辑</el-button
              >
              <el-button type="text" @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!-- 添加分页组件 -->
        <div class="pagination-container" v-if="total > 0">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>

    <!-- 左侧目录新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form ref="form" :model="formData" label-width="80px">
        <!-- 第一行：目录名称和绘制类型 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="目录名称">
              <el-input v-model="formData.name"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="绘制类型">
              <el-select v-model="formData.type" placeholder="请选择">
                <el-option label="点状" value="1"></el-option>
                <el-option label="线状" value="2"></el-option>
                <el-option label="面状" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第二行：根据类型显示不同的配置项 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <!-- 点状显示图标选择 -->
            <el-form-item v-if="formData.type === '1'" label="图标选择">
              <image-upload
                :value="iconUrl"
                @input="handleIconChange"
                accept=".jpg,.jpeg,.png,.gif,.bmp,.webp,.JPG,.JPEG,.PNG,.GIF,.BMP,.WEBP"
              />
            </el-form-item>
            <!-- 线框显示线框配置 -->
            <template v-else>
              <el-form-item label="线颜色">
                <el-color-picker
                  v-model="formData.borderColor"
                ></el-color-picker>
              </el-form-item>
              <el-form-item label="线宽度">
                <el-input-number
                  v-model="formData.borderWidth"
                  :min="1"
                  :max="10"
                  :step="1"
                ></el-input-number>
              </el-form-item>
              <el-form-item label="填充颜色" v-if="formData.type === '3'">
                <el-color-picker
                  v-model="formData.fillColor"
                  show-alpha
                ></el-color-picker>
              </el-form-item>
            </template>
          </el-col>
          <!-- 第三列：字体设置(两种类型都显示) -->
          <el-col :span="12">
            <div class="font-settings">
              <el-form-item label="字体颜色">
                <el-color-picker v-model="formData.fontColor"></el-color-picker>
              </el-form-item>
              <el-form-item label="字体大小">
                <el-input-number
                  v-model="formData.fontSize"
                  :min="12"
                  :max="30"
                  :step="1"
                ></el-input-number>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">保存</el-button>
      </div>
    </el-dialog>

    <!-- 新增设施弹窗 -->
    <el-dialog
      :title="(isEdit ? '编辑' : '新增') + addItemTitle"
      :visible.sync="addItemVisible"
      width="800px"
      :close-on-click-modal="false"
      top="10vh"
    >
      <el-form ref="addForm" :model="addItemForm" label-width="120px">
        <el-form-item :label="addItemTitle + '名称'">
          <el-input v-model="addItemForm.name"></el-input>
        </el-form-item>
        <el-form-item :label="addItemTitle + '描述'">
          <el-input
            type="textarea"
            v-model="addItemForm.detailInfo"
            :rows="3"
          ></el-input>
        </el-form-item>
        <el-form-item label="绘制" v-if="menuData.length > 0">
          <!-- 地图组件 -->
          <plot-map
            :type="menuData[currentIndex].type"
            :plotStyle="plotStyle"
            :resourceId="resourceId"
            :enterpriseId="enterpriseId"
            ref="plotMap"
            @plotComplete="mapcallback"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addItemVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAddItem">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ImageUpload from "@/components/common/packages/ImageUpload";
import PlotMap from "./plot/map.vue";
import {
  addResource,
  getResourceList,
  getResourceById,
  updateResource,
  deleteResource,
  addResourceDetail,
  getResourceDetailList,
  updateResourceDetail,
  deleteResourceDetail,
} from "@/api/draw";

export default {
  name: "FireFacilities",
  components: {
    ImageUpload,
    PlotMap,
  },
  props: {
    enterpriseId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      currentIndex: 0,
      loading: false,
      dialogVisible: false,
      addItemVisible: false,
      addItemTitle: "",
      menuData: [],
      plotStyle: {},
      formData: {
        name: "",
        type: "1",
        iconList: [],
        fontColor: "#333333",
        fontSize: "14",
        borderColor: "#3388ff",
        borderWidth: "",
        fillColor: "",
        enterpId: this.enterpriseId,
      },
      iconUrl: "",
      isEdit: false,
      addItemForm: {
        name: "",
        detailInfo: "",
        plot: null,
        id: null,
      },
      dialogTitle: "新增目录",
      isEditMenu: false,
      types: {
        室外消防栓: "xfs",
        消防水炮: "xfsp",
        水泵接合器: "sbjhq",
        泡沫推车: "pmtc",
        道路中心线: "dl",
        建筑物: "jz",
        外管架: "wgj",
        相对标高: "xdbg",
        出入口: "crk",
        消防水罐: "xfsg",
      },
      pageNum: 1,
      pageSize: 10,
      total: 0,
    };
  },
  computed: {
    // 当前选中的目录名称
    currentMenuName() {
      return this.menuData[this.currentIndex]?.name || "";
    },
    // 当前表格数据
    currentTableData() {
      return this.menuData[this.currentIndex]?.data || [];
    },
    // 目录列表
    menuList() {
      return this.menuData.map((item) => item.name) || [];
    },
    // 当前目录的resourceId
    resourceId() {
      return this.types[this.menuData[this.currentIndex]?.name] || "";
    },
  },
  watch: {
    addItemVisible(newVal) {
      if (!newVal) {
        this.handleDialogClose();
      }
    },
    enterpriseId(newVal) {
      if (newVal) {
        this.getMenuData();
      }
    },
  },
  methods: {
    // 点击目录
    async handleClick(index) {
      this.currentIndex = index;
      this.pageNum = 1; // 切换目录时重置到第一页
      await this.getResourceDetailList();
      await this.updatePlotStyle();
    },
    // 新增设施
    async handleAddItem() {
      this.isEdit = false;
      this.addItemTitle = this.currentMenuName;
      this.addItemForm = {
        name: "",
        detailInfo: "",
        plot: null,
        id: null,
      };
      this.addItemVisible = true;
      // 更新标绘样式
      await this.updatePlotStyle();
    },
    // 更新标绘样式
    async updatePlotStyle() {
      const currentMenu = this.menuData[this.currentIndex];
      if (!currentMenu) return;

      // 根据类型设置不同的样式
      if (currentMenu.type === "1") {
        // 点状
        let image = "";
        if (currentMenu.id) {
          const res = await getResourceById({ id: currentMenu.id });
          if (res.data?.data?.iconList?.length > 0) {
            image = await this.createCanvasFromUrl(
              res.data.data.iconList[0].url
            );
          }
        }
        this.plotStyle = {
          type: "billboard",
          image: image,
          fontColor: currentMenu.fontColor,
          fontSize: currentMenu.fontSize,
        };
      } else if (currentMenu.type === "2") {
        // 线状
        this.plotStyle = {
          type: "polyline",
          borderColor: currentMenu.borderColor,
          borderWidth: Number(currentMenu.borderWidth),
        };
      } else {
        // 面状
        this.plotStyle = {
          type: "polygon",
          borderColor: currentMenu.borderColor,
          borderWidth: Number(currentMenu.borderWidth),
          fillColor: currentMenu.fillColor,
          fontColor: currentMenu.fontColor,
          fontSize: currentMenu.fontSize,
        };
      }
    },
    // 根据url创建HTMLCanvasElement
    async createCanvasFromUrl(url) {
      if (!url) return null;

      try {
        // 创建一个新的Image对象
        const img = new Image();
        img.crossOrigin = "anonymous"; // 处理跨域问题
        img.src = url;

        // 等待图片加载完成
        await new Promise((resolve, reject) => {
          img.onload = resolve;
          img.onerror = reject;
        });

        // 创建固定大小的canvas元素
        const canvas = document.createElement("canvas");
        canvas.width = 16;
        canvas.height = 16;

        // 获取绘图上下文
        const ctx = canvas.getContext("2d");
        if (!ctx) return null;

        // 计算缩放比例和位置，保持图片比例
        const scale = Math.min(16 / img.width, 16 / img.height);
        const width = img.width * scale;
        const height = img.height * scale;
        const x = (16 - width) / 2;
        const y = (16 - height) / 2;

        // 清空画布
        ctx.clearRect(0, 0, 16, 16);

        // 将图片绘制到canvas上，居中显示
        ctx.drawImage(img, x, y, width, height);

        return canvas;
      } catch (error) {
        console.error("创建图标canvas失败:", error);
        return null;
      }
    },
    // 提交新增/编辑设施
    async submitAddItem() {
      try {
        // 表单验证
        if (!this.addItemForm.name) {
          this.$message.warning("请输入名称");
          return;
        }
        if (!this.addItemForm.plot) {
          this.$message.warning("请绘制图形");
          return;
        }

        this.loading = true;

        // 构造请求参数
        const params = {
          name: this.addItemForm.name,
          detailInfo: this.addItemForm.detailInfo,
          plot: this.addItemForm.plot,
          enterpId: this.enterpriseId,
          indexId: this.menuData[this.currentIndex].id,
        };

        let res;
        if (this.isEdit) {
          // 编辑模式
          params.id = this.addItemForm.id;
          res = await updateResourceDetail(params);
        } else {
          // 新增模式
          res = await addResourceDetail(params);
        }

        if (res.data?.status === 200) {
          this.$message.success(this.isEdit ? "编辑成功" : "新增成功");
          this.addItemVisible = false;
          await this.afterSuccessOperation();
        } else {
          this.$message.error(
            res.data?.message || (this.isEdit ? "编辑失败" : "新增失败")
          );
        }
      } catch (error) {
        console.error(this.isEdit ? "编辑设施失败:" : "新增设施失败:", error);
        this.$message.error(this.isEdit ? "编辑失败" : "新增失败");
      } finally {
        this.loading = false;
      }
    },
    // 获取设施列表
    async getResourceDetailList() {
      if (this.menuData.length == 0) return;
      try {
        const res = await getResourceDetailList({
          enterpId: this.enterpriseId,
          indexId: this.menuData[this.currentIndex].id,
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        });

        if (res.data?.status === 200) {
          // 更新当前目录的设施列表
          this.$set(
            this.menuData[this.currentIndex],
            "data",
            res.data.data.list.map((item) => ({
              id: item.id,
              name: item.name,
              detailInfo: item.detailInfo,
              drawType:
                this.menuData[this.currentIndex].type === "1"
                  ? "点状"
                  : this.menuData[this.currentIndex].type === "2"
                  ? "线状"
                  : "面状",
              plot: item.plot,
            }))
          );
          // 更新总数
          this.total = res.data.data.total;
        }
      } catch (error) {
        console.error("获取设施列表失败:", error);
        this.$message.error("获取设施列表失败");
      }
    },
    // 地图标绘完成回调
    mapcallback(data) {
      this.addItemForm.plot = JSON.stringify(data.geometry);
    },
    // 显示新增目录弹窗
    showAddDialog() {
      this.isEditMenu = false;
      this.dialogTitle = "新增目录";
      this.iconUrl = "";
      this.formData = {
        name: "",
        type: "1",
        iconList: [],
        fontColor: "#333333",
        fontSize: "14",
        borderColor: "#3388ff",
        borderWidth: "",
        fillColor: "",
        enterpId: this.enterpriseId,
      };
      this.dialogVisible = true;
    },
    // 编辑目录
    async handleEditMenu(index) {
      this.isEditMenu = true;
      this.dialogTitle = "编辑目录";
      const currentMenu = this.menuData[index];

      // 获取目录详情
      const res = await getResourceById({ id: currentMenu.id });
      if (res.data?.status === 200) {
        const detail = res.data.data;
        this.formData = {
          id: detail.id,
          name: detail.name,
          type: detail.type,
          iconList: detail.iconList || [],
          fontColor: detail.fontColor,
          fontSize: detail.fontSize,
          borderColor: detail.borderColor,
          borderWidth: detail.borderWidth,
          fillColor: detail.fillColor,
          enterpId: this.enterpriseId,
        };
        this.iconUrl = detail.iconList?.[0]?.url || "";
        this.dialogVisible = true;
      }
    },
    // 删除目录
    handleDeleteMenu(index) {
      const currentMenu = this.menuData[index];
      this.$confirm("删除目录将同时删除该目录下的所有设施，是否继续?", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            this.loading = true;
            const res = await deleteResource({ id: currentMenu.id });
            if (res.data?.status === 200) {
              this.$message.success("删除成功");
              await this.getMenuData();
              // 如果删除的是当前选中的目录，重置currentIndex
              if (this.currentIndex === index) {
                this.currentIndex = 0;
              }
            } else {
              this.$message.error(res.data?.message || "删除失败");
            }
          } catch (error) {
            console.error("删除目录失败:", error);
            this.$message.error("删除失败");
          } finally {
            this.loading = false;
          }
        })
        .catch(() => {});
    },
    // 新增目录
    async handleSubmit() {
      try {
        if (!this.formData.name) {
          this.$message.warning("请输入目录名称");
          return;
        }

        this.loading = true;
        let res;

        if (this.isEditMenu) {
          // 编辑模式
          res = await updateResource(this.formData);
        } else {
          // 新增模式
          res = await addResource(this.formData);
        }

        if (res.data?.status === 200) {
          this.$message.success(this.isEditMenu ? "编辑成功" : "新增成功");
          this.dialogVisible = false;
          await this.getMenuData();
        } else {
          this.$message.error(
            res.data?.message || (this.isEditMenu ? "编辑失败" : "新增失败")
          );
        }
      } catch (error) {
        console.error(
          this.isEditMenu ? "编辑目录失败:" : "新增目录失败:",
          error
        );
        this.$message.error(this.isEditMenu ? "编辑失败" : "新增失败");
      } finally {
        this.loading = false;
      }
    },
    // 获取目录列表
    async getMenuData() {
      try {
        this.loading = true;
        const res = await getResourceList({
          enterpId: this.enterpriseId,
          pageNum: 1,
          pageSize: 999,
        });

        if (res.data.status === 200) {
          this.menuData = res.data.data.list.map((item) => ({
            ...item,
            data: [], // 初始化空数组,等待获取详情数据
          }));
          this.getResourceDetailList();
        }
      } catch (error) {
        console.error("获取目录列表失败:", error);
        this.$message.error("获取目录列表失败");
      } finally {
        this.loading = false;
      }
    },
    // 修改编辑方法
    async handleEdit(row) {
      this.isEdit = true;
      this.addItemTitle = this.currentMenuName;
      this.addItemForm = {
        name: row.name,
        detailInfo: row.detailInfo,
        plot: row.plot,
        id: row.id,
      };
      this.addItemVisible = true;

      // 等待弹窗显示完成后再绘制图形
      await this.$nextTick();
      if (this.$refs.plotMap && this.addItemForm.plot) {
        try {
          const geometry = JSON.parse(this.addItemForm.plot);
          this.$refs.plotMap.drawExistingPlot({
            type: "Feature",
            geometry: geometry,
            properties: {},
          });
        } catch (error) {
          console.error("解析plot数据失败:", error);
        }
      }
    },
    // 删除设施
    async handleDelete(row) {
      this.$confirm("确认删除该记录?", "提示", {
        type: "warning",
      })
        .then(async () => {
          try {
            this.loading = true;

            // 调用删除接口
            const res = await deleteResourceDetail({
              id: row.id,
            });

            if (res.data?.status === 200) {
              this.$message.success("删除成功");
              await this.afterSuccessOperation();
            } else {
              this.$message.error(res.data?.message || "删除失败");
            }
          } catch (error) {
            console.error("删除设施失败:", error);
            this.$message.error("删除失败");
          } finally {
            this.loading = false;
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        });
    },
    handleIconChange(data) {
      this.iconUrl = data.url;
      this.formData.iconList = [data];
    },
    // 关闭弹窗时清理状态
    handleDialogClose() {
      this.isEdit = false;
      this.addItemForm = {
        name: "",
        detailInfo: "",
        plot: null,
        id: null,
      };
      if (this.$refs.plotMap) {
        this.$refs.plotMap.clearDraw();
      }
    },
    // 切换每页显示数量
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1; // 重置到第一页
      this.getResourceDetailList();
    },
    // 切换页码
    handleCurrentChange(val) {
      this.pageNum = val;
      this.getResourceDetailList();
    },
    // 修改新增/编辑成功后的处理
    async afterSuccessOperation() {
      this.pageNum = 1; // 重置到第一页
      await this.getResourceDetailList();
    },
  },
  created() {
    if (this.enterpriseId) {
      // 组件创建时获取目录列表
      this.getMenuData();
    }
  },
};
</script>

<style lang="scss" scoped>
.fire-facilities {
  height: 100%;
  width: 100%;
  overflow: hidden;

  .content {
    display: flex;
    height: 100%;
    // padding: 20px 0;
    box-sizing: border-box;

    .left {
      width: 280px;
      background: #fff;
      margin-right: 20px;
      height: 100%;
      border-radius: 4px;

      .tree-header {
        height: 60px;
        padding: 0 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ebeef5;
        background: #f5f5f6;
        span {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
        }

        .el-button {
          padding: 0;
          font-size: 16px;
        }
      }

      .tree-content {
        padding: 0;

        .tree-item {
          .item-title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 40px;
            line-height: 40px;
            padding-right: 10px;

            &:hover {
              background: #f5f7fa;
              .operations {
                display: flex;
              }
            }

            &.active {
              background: #f0f5ff;
              color: #409eff;
              border-right: 2px solid #409eff;
            }

            .name {
              flex: 1;
              padding: 0 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              cursor: pointer;
            }

            .operations {
              display: none;
              .el-button {
                padding: 0 5px;
                font-size: 14px;
                color: #606266;

                &:hover {
                  color: #409eff;
                }
              }
            }
          }
        }
      }
    }

    .right {
      flex: 1;
      background: #fff;
      height: 100%;
      border-radius: 4px;
      // padding: 20px;
      box-sizing: border-box;
      overflow: hidden; // 防止右侧整体滚动

      .table-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;

        .title {
          font-size: 16px;
          font-weight: bold;
          color: #303133;
          position: relative;
          padding-left: 10px;

          &::before {
            content: "";
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 16px;
            background-color: #409eff;
            border-radius: 2px;
          }
        }
      }

      .el-table {
        margin-bottom: 20px;

        ::v-deep .el-table__header-wrapper {
          th {
            background: #f5f7fa;
            color: #606266;
          }
        }
      }

      .pagination-container {
        padding: 20px;
        display: flex;
        justify-content: flex-end;
      }
    }
  }
}

// 滚动条样式移动到 .table-wrapper 下
.table-wrapper {
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c0c4cc;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background: #f5f7fa;
  }
}

::v-deep .el-dialog {
  .el-dialog__body {
    // padding: 5px;
    max-height: 70vh;
    // overflow-y: auto;
  }

  .font-settings {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
  }

  ::v-deep .el-color-picker {
    width: 100%;
    .el-color-picker__trigger {
      width: 100%;
    }
  }

  ::v-deep .el-input-number {
    width: 100%;
  }

  // 调整图标上传组件的高度以匹配右侧两个输入框的总高度
  .image-upload {
    ::v-deep .upload-box {
      height: 82px; // 根据实际右侧两个输入框的总高度调整
    }
  }
}

::v-deep .el-select {
  width: 100%;
}

::v-deep .el-input__inner {
  font-family: inherit;
}
</style>
