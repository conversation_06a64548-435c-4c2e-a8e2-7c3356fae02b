<template>
  <div class="education-training">
    <div class="search-container">
      <!-- <el-input
        v-model.trim="searchParams.enterpName"
        size="small"
        placeholder="请输入企业名称"
        class="input"
        clearable
      ></el-input> -->
      <el-input
        v-model.trim="searchParams.trainName"
        size="small"
        placeholder="请输入培训资料名称"
        class="input"
        clearable
      ></el-input>
      <el-button type="primary" size="small" @click="handleSearch"
        >查询</el-button
      >
      <el-button type="primary" size="small" @click="handleEdit('add')"
        >新增</el-button
      >
    </div>
    <div class="table-container">
      <el-table
        :data="tableData"
        :header-cell-style="headerCellStyle"
        border
        style="width: 100%"
        ref="multipleTable"
        v-loading="loading"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="trainName"
          label="培训资料名称 "
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="trainType"
          label="培训类型"
          width="120"
          align="center"
        >
        </el-table-column>
        <el-table-column prop="trainTheme" label="培训主题" align="center">
        </el-table-column>
        <el-table-column
          prop="issuedTime"
          label="下发时间"
          width="180"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="receivedTime"
          label="签收时间"
          width="180"
          align="center"
        >
        </el-table-column>
        <el-table-column
          label="签收状态"
          prop="status"
          width="180"
          align="center"
        >
          <template slot-scope="scope">
            <el-tag size="small" type="success" v-if="scope.row.status == 1"
              >已签收</el-tag
            >
            <el-tag size="small" type="danger" v-else>未签收</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleEdit('view', scope.row)"
              >查看</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="handleEdit('edit', scope.row)"
              >编辑</el-button
            >
            <el-button type="text" size="small" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="searchParams.nowPage"
          :page-size="searchParams.pageSize"
          layout="total, prev, pager, next"
          background
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <EducationDialog
      v-if="showDialog"
      :visible="showDialog"
      :educationItem="educationItem"
      :dialogType="dialogType"
      @closeBoolean="handleClose"
    />
  </div>
</template>

<script>
import { trainDataPage, deleteTrainData } from "@/api/enterpEducation";
import EducationDialog from "./components/educationDialog.vue";
export default {
  components: {
    EducationDialog,
  },
  data() {
    return {
      loading: false,
      searchParams: {
        nowPage: 1,
        pageSize: 10,
        trainName: "",
      },
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      total: 0,
      tableData: [],
      educationItem: {},
      showDialog: false,
      dialogType: "add",
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleSearch() {
      this.searchParams.nowPage = 1;
      this.getData();
    },
    handleCurrentChange(val) {
      this.searchParams.nowPage = val;
      this.getData();
    },
    // 删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteTrainData(row.id).then((res) => {
            if (res.data.status == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getData();
            }
          });
        })
        .catch(() => {});
    },
    // 编辑 查看
    handleEdit(type, row) {
      this.dialogType = type;
      this.showDialog = true;
      if (row) {
        this.educationItem = row;
      }
    },
    handleClose(val) {
      this.showDialog = false;
      this.educationItem = {};
      if (val) {
        this.getData();
      }
    },
    async getData() {
      const params = { ...this.searchParams };
      this.loading = true;
      await trainDataPage(params).then((res) => {
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.education-training {
  .search-container {
    min-width: 650px;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;

    .input {
      width: 200px;
    }

    > * {
      margin-right: 15px;
    }
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
