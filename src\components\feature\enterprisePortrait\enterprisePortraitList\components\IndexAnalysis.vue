<!-- 指数分析 -->
<template>
  <div class="container" v-loading="loading">
    <!-- title start -->
    <div class="radar-body">
      <div class="title">
        <div class="title-left">
          <span class="title-text">指数分析</span>
          <span class="sub-text">
            数据来源：武汉市智慧应急平台，湖北省行政执法系统；于{{
              portraitInfo.createTime
            }}取值
          </span>
        </div>
        <el-radio-group size="small" v-model="radio" @change="handleChange">
          <el-radio-button label="1">
            <i class="solid"></i> 立体评分</el-radio-button
          >
          <el-radio-button label="2"
            ><i class="many"></i> 多维分析</el-radio-button
          >
        </el-radio-group>
      </div>
      <!-- title end -->
      <div class="content-box" v-if="hasData && radio === '1'">
        <div class="content-left">
          <div class="list-box">
            <div class="list-title">
              <!-- <h4>{{listPageHeader.analysisTitle}}</h4> -->
              <el-dropdown trigger="click" @command="handleCommand">
                <span class="el-dropdown-link">
                  {{ titleOption[currentHeader.mainCode]
                  }}<i class="icon-down"></i>
                </span>
                <el-dropdown-menu slot="dropdown" class="dropdown-item-box">
                  <el-dropdown-item
                    :command="item.mainCode"
                    v-for="(item, index) in mainDTOs"
                    :key="index"
                    >{{ titleOption[item.mainCode] }}</el-dropdown-item
                  >
                </el-dropdown-menu>
              </el-dropdown>
              <!-- 分数 -->
              <div>
                <span style="margin-right: 16px; font-weight: 600">
                  <i style="color: #999">满分:</i>
                  <i style="font-size: 24px; color: #333">
                    {{ currentHeader.fullScore }}</i
                  >
                  <small style="font-size: 18px; font-weight: 600">分</small>
                </span>
                <span style="font-weight: 600">
                  <i style="color: #999">得分:</i>
                  <i style="color: #415ef0; font-size: 24px">
                    {{ currentHeader.score }}</i
                  >
                  <small style="font-size: 18px; font-weight: 600">分</small>
                </span>
              </div>
            </div>
            <div class="list-page" v-if="isNow === 0">
              <div
                class="list-item"
                v-for="(manDTO, index) in currentHeader.subDTOs"
                :key="index"
              >
                <div class="item-left">
                  <p class="item-content" :title="manDTO.subName">
                    {{ manDTO.subName }}
                  </p>
                  <span :title="manDTO.remark">{{ manDTO.remark }}</span>
                </div>
                <div class="item-right">
                  <span style="margin-right: 16px"
                    >满分：<i style="color: #333">{{ manDTO.fullScore }}</i>
                    <small style="font-size: 18px">分</small>
                  </span>
                  <span
                    >得分：<i style="color: #415ef0">{{
                      manDTO.calculateScore
                    }}</i>
                    <small style="font-size: 18px">分</small>
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div class="rule">
            政策法规依据：《中华人民共和国安全生产法》、《注册安全工程师管理规定》、国家安全生产监督管理总局令第11号《企业安全生产费用提取和使用管理办法》（财企〔2012〕16
            号）
          </div>
        </div>
        <div class="content-right">
          <div style="height: 100%">
            <div class="solid-score">
              <div class="total" @click="showRiskDetail = true">
                <p
                  :class="[
                    { blue: activeClassName === 'blue' },
                    { yellow: activeClassName === 'yellow' },
                    { orange: activeClassName === 'orange' },
                    { red: activeClassName === 'red' },
                  ]"
                >
                  {{ portraitInfo.score }}
                </p>
                <p>总体风险指数</p>
              </div>
              <!-- 企业的指数展示数据 -->
              <div
                class="rotateData"
                @mouseover="isPaused = true"
                @mouseout="isPaused = false"
                :class="{ paused: isPaused }"
              >
                <div
                  class="risk"
                  v-for="(item, index) in mainDTOs"
                  :key="item.id"
                  @click="handleClick(index)"
                  :class="{ active: isNow === index }"
                >
                  <p style="white-space: nowrap">
                    <span>{{ item.score }}</span> <small>分</small>
                  </p>
                  <p>{{ describeOption[item.mainCode].describe }}</p>
                  <p>{{ describeOption[item.mainCode].describeEn }}</p>
                  <div class="background-box">
                    <img
                      src="../../../../../../static/portrait/solid/pink.png"
                      alt="拐点"
                    />
                  </div>
                </div>
              </div>

              <div class="rotate-bg"></div>
              <!-- 工厂插画 -->
              <div class="factory">
                <img
                  src="../../../../../../static/portrait/solid/pic.png"
                  alt="工厂插画"
                />
              </div>
              <!-- 弧形企业名称 -->
              <div class="enterpriseName-box">
                <h1 class="enterpriseName" :title="portraitInfo.enterpName">
                  {{ enterpriseName }}
                </h1>
              </div>
              <!-- 图例 -->
            </div>
            <div class="legend">
              <span
                class="legend-item"
                v-for="item of legendList"
                :key="item.icon"
              >
                <i :class="item.icon" style="margin-right: 10px"></i>
                <span>{{ item.title }}</span>
              </span>
            </div>
          </div>
          <!-- 雷达图 -->
        </div>
      </div>
      <div v-if="hasData && radio === '2'">
        <div class="analysis-container">
          <!-- 左侧卡片 -->
          <div class="left-cards">
            <div class="card" v-for="(item, index) in leftCards" :key="index">
              <div class="card-header">
                <div class="header-content">
                  <span class="card-title">
                    <img
                      :src="getCardIconImg(item.mainCode)"
                      style="
                        width: 20px;
                        height: 20px;
                        margin-right: 2px;
                        vertical-align: middle;
                      "
                    />
                    {{ titleOption[item.mainCode] }}
                  </span>
                  <div class="card-score">
                    <span>满分：{{ item.fullScore }}分</span>
                    <span
                      >得分：<i class="score-blue">{{ item.score }}</i
                      >分</span
                    >
                  </div>
                </div>
              </div>
              <div class="card-content">
                <div
                  class="sub-item"
                  v-for="(sub, idx) in item.subDTOs"
                  :key="idx"
                  @click="toggleCardRemark(item.mainCode, idx)"
                  :class="{
                    expandable: true,
                    expanded: isCardRemarkExpanded(item.mainCode, idx),
                  }"
                >
                  <div class="sub-content">
                    <p class="sub-name" :title="sub.subName">
                      {{ sub.subName }}
                    </p>
                    <div class="sub-score">
                      <span>满分：{{ sub.fullScore }}分</span>
                      <span
                        >得分：<i class="score-blue">{{ sub.calculateScore }}</i
                        >分</span
                      >
                    </div>
                  </div>
                  <transition name="fade">
                    <div
                      v-if="
                        isCardRemarkExpanded(item.mainCode, idx) && sub.remark
                      "
                      class="remark-content"
                    >
                      {{ sub.remark }}
                    </div>
                  </transition>
                </div>
              </div>
            </div>
          </div>

          <!-- 中间雷达图 -->
          <div class="center-radar">
            <risk-radar
              @resetData="handleResetData"
              :enterpriseData="enterpriseData"
              :sourceData="sourceData"
              :planData="portraitInfo"
              :legendInfo="describeOption"
              :mainCode="currentHeader.mainCode"
              @amendIsnow="handleIsnow"
              @showRiskDetail="handleShowRiskDetail"
              v-bind="dropDownValue"
              :activeClassName="activeClassName"
            />
          </div>

          <!-- 右侧卡片 -->
          <div class="right-cards">
            <div class="card" v-for="(item, index) in rightCards" :key="index">
              <div class="card-header">
                <div class="header-content">
                  <span class="card-title">
                    <img
                      :src="getCardIconImg(item.mainCode)"
                      style="
                        width: 20px;
                        height: 20px;
                        margin-right: 2px;
                        vertical-align: middle;
                      "
                    />
                    {{ titleOption[item.mainCode] }}
                  </span>
                  <div class="card-score">
                    <span>满分：{{ item.fullScore }}分</span>
                    <span
                      >得分：<i class="score-blue">{{ item.score }}</i
                      >分</span
                    >
                  </div>
                </div>
              </div>
              <div class="card-content">
                <div
                  class="sub-item"
                  v-for="(sub, idx) in item.subDTOs"
                  :key="idx"
                  @click="toggleCardRemark(item.mainCode, idx)"
                  :class="{
                    expandable: true,
                    expanded: isCardRemarkExpanded(item.mainCode, idx),
                  }"
                >
                  <div class="sub-content">
                    <p class="sub-name" :title="sub.subName">
                      {{ sub.subName }}
                    </p>
                    <div class="sub-score">
                      <span>满分：{{ sub.fullScore }}分</span>
                      <span
                        >得分：<i class="score-blue">{{ sub.calculateScore }}</i
                        >分</span
                      >
                    </div>
                  </div>
                  <transition name="fade">
                    <div
                      v-if="
                        isCardRemarkExpanded(item.mainCode, idx) && sub.remark
                      "
                      class="remark-content"
                    >
                      {{ sub.remark }}
                    </div>
                  </transition>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="!hasData"
        style="text-align: center; height: 560px; margin-top: 30px"
      >
        <img src="~@/assets/img/empty.png" alt="兜底空" />
        <p style="color: #b0b0b0; font-size: 18px; margin-top: 20px">
          该企业暂未进行指数分析，请先点击右上角的指数分析按钮
        </p>
      </div>
    </div>
    <!-- body end -->

    <!-- 将模态框改为悬浮卡片 -->
    <div class="floating-card" v-show="showRiskDetail" @click.stop>
      <div class="card-header">
        <h3>企业总体风险指数分析</h3>
        <i class="el-icon-close" @click="showRiskDetail = false"></i>
      </div>
      <div class="card-content">
        <div class="analysis-section">
          <div class="section-title">综合得分</div>
          <div class="section-content">
            <div class="total-score">
              <span class="label">总体风险指数：</span>
              <span class="score" :class="activeClassName">{{
                portraitInfo.score
              }}</span>
              <span class="unit">分</span>
            </div>
          </div>
        </div>

        <div
          class="analysis-section"
          v-for="(item, index) in mainDTOs"
          :key="index"
        >
          <div class="section-title">{{ titleOption[item.mainCode] }}</div>
          <div class="section-content">
            <div class="dimension-score">
              <span class="label">得分：</span>
              <span class="score score-blue">{{ item.score }}</span>
              <span class="unit">分</span>
              <span class="label">（满分{{ item.fullScore }}分）</span>
            </div>
            <div
              class="dimension-details"
              v-if="item.subDTOs && item.subDTOs.length"
            >
              <div
                class="detail-item"
                v-for="(sub, idx) in item.subDTOs"
                :key="idx"
              >
                <div class="detail-name">{{ sub.subName }}</div>
                <div class="detail-score">
                  <span class="label">得分：</span>
                  <span class="score score-blue">{{ sub.calculateScore }}</span>
                  <span class="unit">分</span>
                </div>
                <div class="detail-remark" v-if="sub.remark">
                  {{ sub.remark }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import "@/utils/arctext";
import RiskRadar from "./RiskRadar.vue";
import { mockData } from "../mock.js";
import {
  getPortraitInfoEnterpAnalysis,
  getPortraitInfoEnterp,
} from "@/api/enterprisePortrait";
import {
  thingsList,
  managementList,
  performanceList,
  supervisionList,
  environmentList,
  manList,
} from "../../portaitConfiguration/const";
export default {
  components: {
    RiskRadar,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    enterpriseInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  computed: {
    reportTime() {
      return this.portraitInfo.createTime;
    },
    enterpriseName() {
      // 公司名称最长为 16 个字符 否则弧形有问题
      let textLength = this.portraitInfo.enterpName.length;
      if (textLength > 16) {
        let firstName = this.portraitInfo.enterpName.slice(0, 13);
        return firstName + "...";
      } else {
        return this.portraitInfo.enterpName;
      }
    },
    activeClassName() {
      return this.portraitInfo.score < this.portraitInfo.rankD
        ? "red"
        : this.portraitInfo.score >= this.portraitInfo.rankD &&
          this.portraitInfo.score < this.portraitInfo.rankC
        ? "orange"
        : this.portraitInfo.score >= this.portraitInfo.rankC &&
          this.portraitInfo.score < this.portraitInfo.rankB
        ? "yellow"
        : this.portraitInfo.score >= this.portraitInfo.rankB
        ? "blue"
        : "blue";
    },
    leftCards() {
      return this.mainDTOs.filter((item) =>
        ["man", "things", "supervision"].includes(item.mainCode)
      );
    },
    rightCards() {
      return this.mainDTOs.filter((item) =>
        ["management", "environment", "performance"].includes(item.mainCode)
      );
    },
  },
  data() {
    return {
      loading: false,
      dialogKey: "model",
      dropDownValue: {},
      hasData: false,
      isPaused: false, //暂停动画
      isNow: 0,
      radio: "1",
      analyticalData: [], // 右边风险分析图数据
      sourceData: {}, // 元数据
      enterpriseData: [], // 企业得分数据
      bannerData: {}, // banner数据，
      listPageHeader: {
        //  得分详情的头部数据
        analysisTitle: "人(Man)",
        fullMark: "",
        source: "",
      },
      legendList: [
        // 图例
        {
          icon: "icon1",
          title: "500分以下",
        },
        {
          icon: "icon2",
          title: "500~700分",
        },
        {
          icon: "icon3",
          title: "700~800分",
        },
        {
          icon: "icon4",
          title: "800分以上",
        },
      ],
      RiskTotalSource: {
        //总体风险指数数据
        totalScore: "",
      },

      // 1.[人的详情得分数据]
      manDTO: {},

      // 2.[管法的详情得分数据]
      managementDTO: {},

      // 3.[环境的详情得分数据]
      environmentDTO: {},
      portraitInfo: {}, // 企业风险分析数据
      mainDTOs: [], // 主体配置信息
      currentHeader: {
        // 当前头部数据
      },
      titleOption: {
        man: "人(Man)",
        management: "管法(Management)",
        performance: "绩效(Performance)",
        supervision: "监督(Supervision)",
        environment: "环境(Environment)",
        supervision: "监督(Supervision)",
        things: "物(Things)",
      },
      describeOption: {
        man: {
          describe: "人Man",
          describeEn: "",
        },
        things: {
          describe: "物(机、料)Things",
          describeEn: "",
        },
        management: {
          describe: "管(法)",
          describeEn: "Management",
        },
        environment: {
          describe: "环(境)",
          describeEn: "Environment",
        },
        supervision: {
          describe: "监(监管执法)",
          describeEn: "Supervision",
        },
        performance: {
          describe: "绩(安全绩效)",
          describeEn: "Performance",
        },
      },
      showRiskDetail: false, // 控制风险维度详情悬浮框的显示
      cardRemarkExpanded: {}, // 记录多维分析卡片remark展开状态
    };
  },

  mounted() {
    //this.getSourceData();
    this.getEnterpAnalysis();
    setTimeout(() => {
      $(".enterpriseName").show().arctext({
        radius: 280,
        dir: 0,
      });
    }, 500);
    // 添加点击遮罩层关闭事件
    document.addEventListener("click", this.handleOutsideClick);
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener("click", this.handleOutsideClick);
  },
  methods: {
    async getEnterpAnalysis() {
      this.loading = true;
      await getPortraitInfoEnterp({
        enterpId: this.enterpriseInfo.enterpId,
      })
        .then((res) => {
          if (res.data.status == 200) {
            this.hasData = true; // data有数据展示风险指数页面
            const { data } = res.data;
            this.portraitInfo = data;
            this.legendList[0].title = data.rankD + "分以下";
            this.legendList[1].title = data.rankD + "分~" + data.rankC + "分";
            this.legendList[2].title = data.rankC + "分~" + data.rankB + "分";
            this.legendList[3].title = data.rankB + "分以上";
            const manItem = this.portraitInfo.mainDTOs.find(
              (item) => item.mainCode == "man"
            );
            const thingsItem = this.portraitInfo.mainDTOs.find(
              (item) => item.mainCode == "things"
            );
            const managementItem = this.portraitInfo.mainDTOs.find(
              (item) => item.mainCode == "management"
            );
            const environmentItem = this.portraitInfo.mainDTOs.find(
              (item) => item.mainCode == "environment"
            );
            const performanceItem = this.portraitInfo.mainDTOs.find(
              (item) => item.mainCode == "performance"
            );
            const supervisionItem = this.portraitInfo.mainDTOs.find(
              (item) => item.mainCode == "supervision"
            );
            this.mainDTOs = [
              manItem,
              thingsItem,
              managementItem,
              environmentItem,
              performanceItem,
              supervisionItem,
            ];
            this.currentHeader = this.mainDTOs[0];
            this.enterpriseData = [
              manItem.score,
              thingsItem.score,
              managementItem.score,
              environmentItem.score,
              performanceItem.score,
              supervisionItem.score,
            ];
            this.sourceData = {
              manFullMark: manItem.fullScore,
              thingsFullMark: thingsItem.fullScore,
              managementFullMark: managementItem.fullScore,
              environmentFullMark: environmentItem.fullScore,
              performanceFullMark: performanceItem.fullScore,
              supervisionFullMark: supervisionItem.fullScore,
              manScore: manItem.score,
              thingsScore: thingsItem.score,
              managementScore: managementItem.score,
              environmentScore: environmentItem.score,
              performanceScore: performanceItem.score,
              supervisionScore: supervisionItem.score,
            };
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    closeBoolean(boolean) {
      this.$emit("close", boolean);
    },
    handleSubmit() {
      this.closeBoolean(false);
    },
    async getSourceData() {
      try {
        const res = mockData; //await this.$api.enterpriseQueryData(this.uuitNo) //调用查询接口
        if (res && res.code === 200 && res.data) {
          this.hasData = true; // data有数据展示风险指数页面
          let conversionCraftName = "";
          this.sourceData = res.data; // 所有源数据
          let craftName = JSON.parse(res.data.craftName);
          conversionCraftName = craftName.map((item) => item.name).join("、");
          let conversionIndusType = res.data.indusType;
          conversionIndusType = conversionIndusType.slice(
            1,
            conversionIndusType.length - 1
          );
          // banner 数据
          this.bannerData = {
            enterpriseName: res.data.enterpriseName,
            uuitNo: res.data.uuitNo,
            aboveDesignated: res.data.aboveDesignated, // 企业规模
            addr: res.data.addr, // 地址
            legalPerson: res.data.legalPerson, // 法人
            indusType: conversionIndusType, // 国民经济
            businessScope: res.data.businessScope, // 经营范围
            majorProduct: res.data.majorProduct, // 主营产品
            craftName: conversionCraftName, // 工艺名称
            phone: res.data.phone,
          };

          // 1.环境的得分详情数据
          this.environmentDTO = res.data.environmentDTO;

          // 2.人man的得分详情数据
          this.manDTO = res.data.manDTO;

          // 3.管法的得分详情数据
          this.managementDTO = res.data.managementDTO;

          // 4.得分详情头部数据
          this.listPageHeader.fullMark = res.data.manFullMark;
          this.listPageHeader.source = res.data.manScore;

          // 5.总体风险指数数据
          this.RiskTotalSource.totalScore = res.data.totalScore;

          // 6.企业得分数据
          this.enterpriseData = [
            res.data.manScore,
            res.data.thingsScore,
            res.data.managementScore,
            res.data.environmentScore,
            res.data.supervisionScore,
            res.data.performanceScore,
          ];

          // 7. 右边分析图的数据
          this.analyticalData = [
            {
              id: 1,
              value: res.data.manScore,
              describe: "人Man",
              describeEn: "",
            },
            {
              id: 2,
              value: res.data.thingsScore,
              describe: "物(机、料)Things",
              describeEn: "",
            },
            {
              id: 3,
              value: res.data.managementScore,
              describe: "管(法)",
              describeEn: "Management",
            },
            {
              id: 4,
              value: res.data.environmentScore,
              describe: "环(境)",
              describeEn: "Environment",
            },
            {
              id: 5,
              value: res.data.supervisionScore,
              describe: "监(监管执法)",
              describeEn: "Supervision",
            },
            {
              id: 6,
              value: res.data.performanceScore,
              describe: "绩(安全绩效)",
              describeEn: "Performance",
            },
          ];
        } else {
          this.$message.error(res.msg);
        }
      } catch (err) {
        this.$message.error("获取服务器数据失败");
      } finally {
      }
    },
    handleClick(num) {
      this.currentHeader = this.mainDTOs[num];
      this.dropDownValue.index = num;
      this.dropDownValue.name = this.mainDTOs[num].name;
      return;
      this.isNow = num;
      if (num === 0) {
        this.listPageHeader.analysisTitle = "人(Man)";
        this.listPageHeader.fullMark = this.sourceData.manFullMark;
        this.listPageHeader.source = this.sourceData.manScore;
      } else if (num === 1) {
        this.listPageHeader.analysisTitle = "物(Things)";
        this.listPageHeader.fullMark = this.sourceData.thingsFullMark;
        this.listPageHeader.source = this.sourceData.thingsScore;
      } else if (num === 2) {
        this.listPageHeader.analysisTitle = "管(Management)";
        this.listPageHeader.fullMark = this.sourceData.managementFullMark;
        this.listPageHeader.source = this.sourceData.managementScore;
      } else if (num === 3) {
        this.listPageHeader.analysisTitle = "环(Environment)";
        this.listPageHeader.fullMark = this.sourceData.environmentFullMark;
        this.listPageHeader.source = this.sourceData.environmentScore;
      } else if (num === 4) {
        this.listPageHeader.analysisTitle = "监(Supervision)";
        this.listPageHeader.fullMark = this.sourceData.supervisionFullMark;
        this.listPageHeader.source = this.sourceData.supervisionScore;
      } else if (num === 5) {
        this.listPageHeader.analysisTitle = "绩(Performance)";
        this.listPageHeader.fullMark = this.sourceData.performanceFullMark;
        this.listPageHeader.source = this.sourceData.performanceScore;
      }
    },
    // 最优质企业
    handleResetData() {
      // 刷新页面
      location.reload();
    },
    handleIsnow(num, name) {
      // this.handleClick(num);
      // const index = this.mainDTOs.findIndex((item) => item.mainCode === code);
      this.handleCommand(name);
    },
    handleChange(type) {
      this.isNow = 0;
      this.listPageHeader.analysisTitle = "人(Man)";
      this.listPageHeader.fullMark = this.sourceData.manFullMark;
      this.listPageHeader.source = this.sourceData.manScore;
      if (type === "1") {
        $(".enterpriseName").show().arctext({
          radius: 280,
          dir: 0,
        });
      }
    },
    async handleClickResult() {
      try {
        const res = await this.$api.enterpriseCountData(this.uuitNo);
        // console.log(res);
        if (res && res.code === 200) {
          this.getSourceData();
          // 刷新页面,解决弧形文字bug
          location.reload();
        } else {
          // console.log(res.msg);
        }
      } catch (err) {
        this.$message.error(err.message);
      }
    },
    handleCommand(command) {
      // this.isNow = command.slice(0, 1) * 1; // 取出下标
      // let name = command.slice(1); // 取出名称
      this.dropDownValue.index = this.mainDTOs.findIndex(
        (item) => item.mainCode == command
      );
      this.dropDownValue.name = command;
      // this.handleClick(this.isNow);
      if (command == this.currentHeader.mainCode) return;
      this.currentHeader = this.mainDTOs.find(
        (item) => item.mainCode == command
      );
    },
    handleShowRiskDetail() {
      console.log("显示风险详情"); // 添加调试日志
      this.showRiskDetail = true;
    },
    handleOutsideClick(e) {
      if (
        this.showRiskDetail &&
        !e.target.closest(".floating-card") &&
        !e.target.closest(".total")
      ) {
        this.showRiskDetail = false;
      }
    },
    toggleCardRemark(mainCode, idx) {
      const key = `${mainCode}_${idx}`;
      this.$set(this.cardRemarkExpanded, key, !this.cardRemarkExpanded[key]);
    },
    isCardRemarkExpanded(mainCode, idx) {
      return !!this.cardRemarkExpanded[`${mainCode}_${idx}`];
    },
    getCardIcon(mainCode) {
      const iconMap = {
        man: "icon-person",
        things: "icon-wu",
        supervision: "icon-jian",
        management: "icon-guan",
        environment: "icon-huan",
        performance: "icon-ji",
      };
      return iconMap[mainCode] || "";
    },
    getCardIconImg(mainCode) {
      const iconMap = {
        man: require("../../../../../../static/portrait/solid/icon-person.png"),
        things: require("../../../../../..//static/portrait/solid/icon-wu.png"),
        supervision: require("../../../../../../static/portrait/solid/icon-jian.png"),
        management: require("../../../../../../static/portrait/solid/icon-guan.png"),
        environment: require("../../../../../../static/portrait/solid/icon-huan.png"),
        performance: require("../../../../../../static/portrait/solid/icon-ji.png"),
      };
      return iconMap[mainCode] || "";
    },
  },
};
</script>

<style lang="scss" scoped>
$imgUrl: "../../../../../../static/portrait/";
$iconUrl: "../../../../../assets/img";
@keyframes run {
  0% {
    transform: rotateX(-14deg) rotateY(0deg);
  }
  100% {
    transform: rotateX(-14deg) rotateY(360deg);
  }
}

.radar-body {
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    border-bottom: 2px solid #086dd4;

    .title-left {
      flex: 1;
      // width:84%;
      display: flex;
      align-items: center;
      .title-text {
        font-size: 20px;
        font-weight: 600;
        color: #49445f;
        margin-right: 10px;
      }
      .sub-text {
        font-size: 16px;
        color: #666;
        display: inline-block;
        max-width: 90%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &:first-of-type {
        padding-left: 15px;
        border-left: 4px solid #086dd4;
      }
    }

    .is-active .solid {
      background: url("#{$imgUrl}/white-circle.png");
    }

    .solid {
      display: inline-block;
      width: 10px;
      height: 12px;
      background: url("#{$imgUrl}/blue-circle.png") no-repeat center center;
      background-size: 100% 100%;
    }

    .is-active .many {
      background: url("#{$imgUrl}/white-radar.png") no-repeat center center;
      background-size: 100% 100%;
    }

    .many {
      display: inline-block;
      width: 12px;
      height: 12px;
      background: url("#{$imgUrl}/blue-radar.png") no-repeat center center;
      background-size: 100% 100%;
    }
  }

  .content-box {
    flex: 1;
    display: flex;
    margin-top: 12px;
    height: 640px;

    .content-left {
      flex: 1;
      min-width: 0;
      .list-box {
        height: 475px;
        background: url("#{$imgUrl}/solid/bg-detail.png") no-repeat 0px 0px;
        background-size: 100% 100%;

        .list-title {
          display: flex;
          padding: 0 25px 0 25px;
          justify-content: space-between;
          align-items: center;
          height: 65px;
          width: 94%;

          .el-dropdown {
            font-weight: 600;
            font-size: 24px;
            color: #49445f;
            // width: 30%;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;

            .icon-down {
              display: inline-block;
              margin-left: 10px;
              width: 14px;
              height: 9px;
              background: url("#{$iconUrl}/icon-down.png");
            }
          }

          > div {
            max-width: 70%;
          }
        }

        .list-page {
          z-index: 2;
          height: calc(100% - 75px);
          width: 92%;
          margin-left: 11px;
          overflow-y: auto;
          padding: 0 10px 0 10px;

          &::-webkit-scrollbar {
            width: 6px;
            height: 6px;
            background-color: transparent;
          }

          &::-webkit-scrollbar-track {
            border-radius: 0;
            -webkit-box-shadow: inset 0 0 1px #f5f5f5;
            background-color: transparent;
          }

          &::-webkit-scrollbar-thumb {
            border-radius: 3px;
            -webkit-box-shadow: inset 0 0 6px #c5dfff;
            background-color: #c5dfff;
          }
        }

        .list-item {
          width: 100%;
          height: 60px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #dfdfe3;
          box-sizing: border-box;
          // &:last-child {
          //   border: none;
          // }
          .item-left {
            // width: 60%;
            flex: 1;
            min-width: 0;
            > p {
              margin-bottom: 5px;
            }

            > span {
              display: block;
              color: #999;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .item-content {
              width: 100%;
              // white-space: nowrap;
              // overflow: hidden;
              // text-overflow: ellipsis;
              color: #49445f;
              font-size: 18px;
              font-weight: 600;
              line-height: 30px;
              overflow: hidden; /* 超出的部分隐藏 */
              text-overflow: ellipsis; /* 用省略号显示超出部分 */
              display: -webkit-box; /* 使文本能折行 */
              -webkit-line-clamp: 1; /* 设置最大显示行数 */
              -webkit-box-orient: vertical; /* 垂直排列 */
            }
          }

          .item-right {
            width: 180px;
            color: #999;
            font-weight: 600;
            margin-left: 20px;
          }
        }
      }

      .rule {
        color: #666;
        font-size: 14px;
        // line-height: 20px;
        // margin: 10px 0 10px 11px;
        // width: 93%;
        max-height: 50px;
        overflow-y: auto;
      }
    }

    .content-right {
      width: 660px;
      margin: 0 auto;
      overflow: hidden;
      .solid-score {
        height: calc(100% - 30px);
        position: relative;
        width: 100%;
        background: url("#{$imgUrl}/solid/bg-rotate.png") no-repeat -105px 50px;
        background-size: 113% auto;

        .rotate-bg {
          position: absolute;
          transform-origin: center;
          transform: rotate3d(1, 0, 0, 43deg);
          opacity: 0.6;
          top: 195px;
          left: 163px;
          width: 249px;
          height: 109px;
          background: url("#{$imgUrl}/solid/rotate.png") no-repeat center;
          background-size: 160% 100%;
        }

        .factory {
          z-index: 1;
          position: absolute;
          top: 178px;
          left: 233px;

          img {
            width: 96px;
            height: 84px;
          }
        }

        .risk {
          position: absolute;
          height: 112px;
          font-weight: 600;
          text-align: center;
          cursor: pointer;

          p {
            &:first-of-type {
              position: relative;

              &::before {
                content: "";
                top: 0;
                left: 0;
                position: absolute;
                width: 74px;
                height: 78px;
                background: url("#{$imgUrl}/solid/bg-blue.png") no-repeat 0px
                  0px;
              }

              span {
                font-size: 24px;
                font-family: "Quartz-Regular";
                padding-left: 25px;
              }
            }
          }

          .background-box {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);

            img {
              width: 19px;
              height: 22px;
            }
          }
        }

        .total {
          position: absolute;
          text-align: center;
          font-weight: 600;
          top: 6px;
          left: 218px;

          p {
            &:first-of-type {
              font-size: 38px;
              font-family: "Quartz-Regular";
            }

            &:nth-child(2) {
              font-size: 20px;
            }
          }
        }

        .bg {
          position: absolute;
          width: 19px;
          height: 22px;
          background: url("#{$imgUrl}/solid/pink.png") no-repeat center;
          background-size: 100% 100%;
        }

        .bg-total {
          top: 0;
          left: 0;
        }
      }

      .legend {
        margin: 0 auto;
        width: 75%;
        height: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .legend-item {
          color: #666;
          display: flex;
          align-items: center;

          .icon1 {
            width: 19px;
            height: 21px;
            background: url("#{$imgUrl}/solid/red.png");
          }

          .icon2 {
            width: 19px;
            height: 21px;
            background: url("#{$imgUrl}/solid/origin.png");
          }

          .icon3 {
            width: 19px;
            height: 21px;
            background: url("#{$imgUrl}/solid/yellow.png");
          }

          .icon4 {
            width: 19px;
            height: 21px;
            background: url("#{$imgUrl}/solid/blue.png");
          }
        }
      }

      .active {
        color: #415ef0;

        p {
          &:first-of-type {
            &::before {
              width: 78px;
              height: 78px;
              top: -9px;
              background: url("#{$imgUrl}/solid/bg-pink.png") no-repeat 0px 0px !important;
            }
          }
        }
      }
    }
  }
}

.rotateData {
  transform-style: preserve-3d;
  position: absolute;
  z-index: 2;
  top: 28%;
  left: 37%;
  width: 88px;
  height: 88px;
  transform: rotateX(-14deg);
  animation: run 30s linear infinite;
  /*环执行run动画，每30秒执行一次 */

  > div {
    &:nth-child(1) {
      transform: rotateY(0deg) translateZ(200px);
    }

    &:nth-child(2) {
      transform: rotateY(60deg) translateZ(200px);
    }

    &:nth-child(3) {
      transform: rotateY(120deg) translateZ(200px);
    }

    &:nth-child(4) {
      transform: rotateY(180deg) translateZ(200px);
    }

    &:nth-child(5) {
      transform: rotateY(240deg) translateZ(200px);
    }

    &:nth-child(6) {
      transform: rotateY(300deg) translateZ(200px);
    }
  }
}

.enterpriseName-box {
  transform: rotateX(54deg);
  position: absolute;
  bottom: 22px;
  width: 92%;
  height: 50px;
  line-height: 50px;

  .enterpriseName {
    font-family: "YouSheBiaoTiHei";
    margin: 0 auto;
    text-align: center;
    color: #fff;
    font-size: 22px;
    letter-spacing: 2px;
  }
}

.paused {
  animation-play-state: paused;
  -webkit-animation-play-state: paused;
  /* Safari 和 Chrome */
}

.blue {
  color: #1c6bff;
}

.yellow {
  color: #f7e546;
}

.orange {
  color: #f9ae1a;
}

.red {
  color: #ef1515;
}
p {
  margin-bottom: 0;
}

.analysis-container {
  display: flex;
  justify-content: space-between;
  align-items: stretch;
  height: calc(100vh - 250px);
  padding: 15px 2px;
  gap: 6px;
  width: 98%;
  margin: 0 auto;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("#{$imgUrl}/many/qianshe.png") no-repeat center;
    background-size: contain;
    opacity: 0.1;
    pointer-events: none;
    z-index: 0;
  }

  .left-cards,
  .right-cards {
    width: 28%;
    display: flex;
    flex-direction: column;
    gap: 15px;
    height: 100%;
    position: relative;
    z-index: 1;
  }

  .center-radar {
    width: 50%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    padding: 0 20px;

    .Risk-container {
      width: 100%;
      flex: 1;
      min-height: 500px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 15px;

      .radar-box {
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .policy-reference {
      width: 100%;
      color: #666;
      font-size: 13px;
      line-height: 1.5;
      padding: 10px 15px;
      background: #f8f9fa;
      border-radius: 4px;
      text-align: left;
    }
  }

  .card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 12px;
    cursor: default;
    transition: all 0.3s;
    height: calc((100% - 30px) / 3);
    display: flex;
    flex-direction: column;
    position: relative;

    &:hover {
      transform: none;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
      margin-bottom: 8px;
      flex-shrink: 0;

      .header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #49445f;
      }

      .card-score {
        display: flex;
        gap: 12px;
        color: #666;
        font-size: 13px;

        i {
          font-style: normal;
          font-weight: 600;
          &.score-blue {
            color: #1c6bff;
          }
        }
      }
    }

    .card-content {
      flex: 1;
      overflow-y: auto;
      &::-webkit-scrollbar {
        width: 4px;
      }
      &::-webkit-scrollbar-thumb {
        background-color: #c5dfff;
        border-radius: 2px;
      }
      &::-webkit-scrollbar-track {
        background-color: transparent;
      }

      .sub-item {
        padding: 8px 0;
        border-bottom: 1px dashed #eee;

        &:last-child {
          border-bottom: none;
        }

        .sub-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 12px;
        }

        .sub-name {
          font-size: 13px;
          color: #333;
          margin: 0;
          flex: 1;
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .sub-score {
          display: flex;
          gap: 12px;
          font-size: 12px;
          color: #666;
          white-space: nowrap;

          i {
            font-style: normal;
            font-weight: 600;
            &.score-blue {
              color: #1c6bff;
            }
          }
        }
      }
    }
  }
}

// 添加悬浮卡片样式
.floating-card {
  position: fixed;
  top: 50%;
  right: 40px;
  transform: translateY(-50%);
  width: 500px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 2000;
  overflow: hidden;
  max-height: 90vh;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #eef0f5;

    h3 {
      margin: 0;
      font-size: 20px;
      color: #333;
      font-weight: 600;
    }

    .el-icon-close {
      font-size: 18px;
      color: #909399;
      cursor: pointer;
      transition: all 0.3s;
      padding: 4px;

      &:hover {
        color: #333;
        background: #f5f7fa;
        border-radius: 4px;
      }
    }
  }

  .card-content {
    max-height: 80vh;
    overflow-y: auto;
    padding: 24px;
    background: #fff;

    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: #e4e7ed;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-track {
      background-color: transparent;
    }

    .analysis-section {
      margin-bottom: 28px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
        padding-left: 12px;
        border-left: 4px solid #1c6bff;
        line-height: 1.4;
      }

      .section-content {
        padding-left: 16px;

        .total-score {
          font-size: 16px;
          margin-bottom: 12px;
          padding: 12px 16px;
          background: #f8faff;
          border-radius: 8px;
          border: 1px solid #eef0f5;

          .label {
            color: #606266;
          }

          .score {
            font-size: 28px;
            font-weight: 600;
            margin: 0 4px;
          }

          .unit {
            color: #606266;
          }
        }

        .dimension-score {
          font-size: 15px;
          margin-bottom: 16px;
          padding: 12px 16px;
          background: #f8faff;
          border-radius: 8px;
          border: 1px solid #eef0f5;

          .label {
            color: #606266;
          }

          .score {
            font-weight: 600;
            margin: 0 4px;
            &.score-blue {
              color: #1c6bff;
            }
          }

          .unit {
            color: #606266;
          }
        }

        .dimension-details {
          .detail-item {
            margin-bottom: 12px;
            padding: 16px;
            background: #fff;
            border-radius: 8px;
            border: 1px solid #eef0f5;
            transition: all 0.3s;

            &:hover {
              border-color: #dcdfe6;
              box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
            }

            &:last-child {
              margin-bottom: 0;
            }

            .detail-name {
              font-size: 15px;
              color: #333;
              font-weight: 500;
              margin-bottom: 8px;
            }

            .detail-score {
              font-size: 14px;
              color: #606266;
              margin-bottom: 6px;

              .label {
                color: #606266;
              }

              .score {
                font-weight: 600;
                margin: 0 4px;
                &.score-blue {
                  color: #1c6bff;
                }
              }

              .unit {
                color: #606266;
              }
            }

            .detail-remark {
              font-size: 13px;
              color: #909399;
              line-height: 1.6;
              margin-top: 6px;
              padding-top: 6px;
              border-top: 1px dashed #eef0f5;
            }
          }
        }
      }
    }
  }
}

// 优化遮罩层
.floating-card::before {
  content: "";
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(2px);
  z-index: -1;
  pointer-events: none;
}

// 添加遮罩层点击区域
.floating-card::after {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: transparent;
  z-index: -2;
  cursor: pointer;
}

// 展开remark动画和样式
.expandable {
  cursor: pointer;
  transition: background 0.2s;
}
.expandable:hover {
  background: #f5f7fa;
}
.remark-content {
  color: #ff6600;
  font-size: 13px;
  margin-top: 6px;
  padding: 6px 10px;
  background: #fffbe6;
  border-left: 3px solid #ffd666;
  border-radius: 3px;
  box-shadow: 0 1px 4px rgba(255, 214, 102, 0.08);
}
.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
  max-height: 0;
}
</style>
