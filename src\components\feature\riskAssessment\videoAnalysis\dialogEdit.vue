<template>
  <div class="notification">
    <el-dialog
      title="报警处置反馈"
      top="3vh"
      :visible.sync="detailAlarmDialog"
      width="1000px"
      :close-on-click-modal="false"
    >
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="监管通报" name="1"></el-tab-pane>
        <el-tab-pane label="企业反馈" name="0"></el-tab-pane>
      </el-tabs> -->
      <div class="warn">
        <!-- <div class="null" v-if="tableData == null"></div> -->
        <!-- <div class="warnStatus">
          <div class="active">
            <div class="step_line"><span>开始反馈</span></div>

            <p>2022-03-22 15:30</p>
          </div>
          <div>反馈完成</div>
        </div> -->

        <div class="showImage">
          <div v-if="dataList.length > 0" class="lunbo" @mouseenter="clear" @mouseleave="run">
            <div class="img">
              <img :src="dataList[currentIndex]" alt=""  @click.self="showBigImage(dataList[currentIndex])"/>
            </div>
            <!-- <div class="dooted" v-if="this.dataList.length">
              <ul class="doo">
                <li
                  v-for="(item, index) in this.dataList"
                  :key="index"
                  :class="{ current: currentIndex == index }"
                  @click="gotoPage(index)"
                ></li>
              </ul>
            </div> -->

            <!-- <div class="right turn" @click="next()">
              <i class="el-icon-arrow-right"></i>
            </div>
            <div class="left turn" @click="up()">
              <i class="el-icon-arrow-left"></i>
            </div> -->

            <div class="botttomBox">
              <!-- {{dataList[currentIndex]}} -->
              <span
                :title="dataList[currentIndex]"
                class="el-icon-zoom-in"
                @click.self="showBigImage(dataList[currentIndex])"
              ></span>
              <!-- <span class="el-icon-download"></span> -->
            </div>
          </div>
           <div v-else>暂无图片！</div>
        </div>

        <div class="div1">
          <div class="table">
            <ul class="container">
              <li class="lang">
                <div class="l">企业名称</div>
                <div class="r">{{ detailData.enterpName || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">企业编码</div>
                <div class="r">{{ detailData.districtCode || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">报警时间</div>
                <div class="r">{{ detailData.alarmTime || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">报警类型</div>
                <div class="r">{{ detailData.alarmTypeName || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">点位名称</div>
                <div class="r">
                  {{ detailData.channelName || "--" }}
                </div>
              </li>
              <li class="lang">
                <div class="l">设备编码</div>
                <div class="r">
                  {{ detailData.channelId || "--" }}
                </div>
              </li>
              <!-- <li class="lang">
                <div class="l">电力户号</div>
                <div class="r">
                  {{ detailData }}
                </div>
              </li> -->
              <li class="lang bottom">
                <div class="l">原因说明<span style="color:red">*</span></div>
                <div class="r rHeight">
                  <el-input
                    type="textarea"
                    placeholder="请输入最大500字"
                    v-model.trim="form.feedbackReason"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                  >
                  </el-input>
                </div>
              </li>

              <li class="lang bottom">
                <div class="l">处置内容<span style="color:red">*</span></div>
                <div class="r rHeight">
                  <el-input
                    type="textarea"
                    placeholder="请输入最大500字"
                    v-model.trim="form.feedbackHandle"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                  >
                  </el-input>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div slot="footer" style="display: flex; justify-content: center">
          <el-button type="primary" size="mini" @click="submit()"
            >保 存</el-button
          >
        </div>
      </div>
    </el-dialog>
    <bigImg
      :visible="photoVisible"
      :url="bigImgUrl"
      @closeClick="
        () => {
          photoVisible = false;
        }
      "
    ></bigImg>
  </div>
</template>

<script>
import {
  videoAlarmFindById,
  videoAlarmFeedback,
} from "@/api/companyParticularJob";
import { getSearchArr } from "@/api/entList.js";
import { getInformationBasicInfo } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
var dayjs = require("dayjs");
import bigImg from "./bigImg.vue";
export default {
  //import引入的组件
  name: "notification",
  components: { bigImg },
  data() {
    return {
      form: {
        feedbackHandle:'',
        feedbackReason: "",
        alarmId:''
      },
      detailData: {},
      photoVisible: false,
      bigImgUrl: "",
      detailAlarmDialog: false,
      dataList: [
        // "../../../../../static/img/橙-较大风险.png",
        // "../../../../../static/img/红-重大风险.png",
        // "../../../../../static/img/黄-一般风险.png",
      ],
      currentIndex: 0, // 默认显示图片
      timer: null, // 定时器
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      userPark: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
  },
  //方法集合
  methods: {
    submit() {
      if(this.form.feedbackReason==''){
         this.$message.info("反馈原因说明不能为空");
         return false
      }
      if(this.form.feedbackHandle==''){
         this.$message.info("反馈处置结果不能为空");
         return false
      }
      videoAlarmFeedback(this.form).then((res) => {
        if (res.data.status === 200) {
          //this.$message.success(res.data.data);
          this.$message.success("保存成功");       
          this.detailAlarmDialog = false;
          this.form.feedbackReason='';
          this.form.feedbackHandle='';
          this.form.alarmId=''
          this.$parent.getData();
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    detailFn(row) {
      this.form.alarmId=row.alarmId;
      videoAlarmFindById({ alarmId: row.alarmId }).then((res) => {
        if (res.data.status == 200) {
          this.detailData = res.data.data;
          
          this.dataList.push(res.data.data.alarmPicUrl);
          console.log(this.dataList);
        }
      });
    },
    showBigImage(e) {
      //点击图片函数，点击后，把photoVisible设置成true
      if (e != "") {
        this.photoVisible = true;
        this.bigImgUrl = e;
      }
    },
    isDisposal(val) {
      this.detailAlarmDialog = val;
    },
    //图片展示
    gotoPage(index) {
      this.currentIndex = index;
    },
    next() {
      if (this.currentIndex === this.dataList.length - 1) {
        this.currentIndex = 0;
      } else {
        this.currentIndex++;
      }
    },
    up() {
      if (this.currentIndex === 0) {
        this.currentIndex = this.dataList.length - 1;
      } else {
        this.currentIndex--;
      }
    },
    // 定时器
    run() {
      // this.timer = setInterval(() => {
      //   this.next()
      // }, 2000)
    },
    clear() {
      clearInterval(this.timer);
    },
  },

  mounted() {   
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
//弹框样式
.warn {
  .rHeight{
    height: 100px;
    overflow: auto;
  }
  overflow: auto;
  color: #000;
  .warnStatus {
    width: 80%;
    display: flex;
    margin: 0 auto;
    > div.active {
      color: #409eff;
    }
    > div {
      width: 50%;
      .step_line {
        width: 100%;
        position: relative;
      }
      span {
        display: inline-block;
        // background: #409eff;
      }
      span:after {
        content: "";
        position: absolute;
        right: 0;
        border-bottom: 1px solid #ccc;
        top: 9px;
        width: 77%;
      }
    }
  }
  ul {
    padding-inline-start: 0px;
  }
  .null {
    width: 467px;
    height: 285px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 50%;
          display: flex;
          // border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 30%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 70%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}

//图片
.showImage {
  position: relative;
  height: 300px;
  height: 300px;
  line-height: 300px;
  width: 80%;
  margin: 0 auto 20px auto;
  ul li {
    float: left;
    width: 30px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    color: rgba(240, 238, 238, 0.8);
    font-size: 14px;
  }
  .img {
    height: 300px;
    width: 100%;
    border: 1px solid gray;
    img {
      height: 100%;
      width: 100%;
    }
  }
  .dooted {
    position: absolute;
    bottom: -10px;
    right: 0px;
  }
  .container {
  }
  .turn:hover .el-icon-arrow-right,
  .turn:hover .el-icon-arrow-left {
    color: #fff;
  }
  .turn:hover {
    background: #409eff;
  }
  .turn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    line-height: 40px;
    text-align: center;
    border: 2px solid #409eff;
    cursor: pointer;
  }
  .right {
    position: absolute;
    top: 100px;
    right: -60px;
  }
  .left {
    position: absolute;
    top: 100px;
    left: -60px;
  }
  .current {
    color: gray;
  }
  .el-icon-arrow-right,
  .el-icon-arrow-left {
    color: #409eff;
  }
  .botttomBox {
    position: absolute;
    left: 0;
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: right;
    bottom: 0;
    padding: 0 0;
    background: rgba(0, 0, 0, 0.5);
    span {
      display: inline-block;
      height: 30px;
      vertical-align: top;
      line-height: 30px;
      width: 30px;
      text-align: center;
      cursor: pointer;
      color: #fff;
    }
    span:hover {
      color: #409eff;
    }
  }
}
</style>