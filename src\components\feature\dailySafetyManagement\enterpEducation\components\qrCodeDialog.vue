<template>
    <el-dialog title="二维码" :visible="visible" @close="closeBoolean(false)" width="500px" top="5vh"
        :close-on-click-modal="true">
        <div class="questionDialog">
            <canvas ref="qrCodeCanvas" class="qrcode"></canvas>
        </div>
        <!-- <div class="footer">
            <el-button size="small" @click="closeBoolean">取消</el-button>
            <el-button type="primary" size="small" @click="handleSubmit">关闭</el-button>
        </div> -->
    </el-dialog>
</template>

<script>
import QRCode from 'qrcode';
import { mapState } from "vuex";
import { H5_URL } from '../../../../../../static/ip'
export default {
    components: {},
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        educationItem: {
            type: Object,
            default: () => { },
        },
    },
    data() {
        return {
            qrcodeDataURL: '',
            qrcode: '',
        };
    },
    computed: {
        ...mapState({
            token_type: (state) => state.login.user.token_type,
            access_token: (state) => state.login.user.access_token,
        }),
    },
    created() {
        const token = this.token_type + " " + this.access_token;
        this.qrcodeDataURL = H5_URL + '?token=' + token + '&id=' + this.educationItem.id;
        console.log(this.qrcodeDataURL);
        //  window.open(this.qrcodeDataURL)
        this.$nextTick(() => {
            this.generateQRCode();
        });
    },
    methods: {
        generateQRCode() {
            const canvas = this.$refs.qrCodeCanvas;
            QRCode.toCanvas(canvas, this.qrcodeDataURL,
                {
                    color: {
                        dark: '#000',  // 二维码颜色
                        light: '#ffffff' // 背景颜色
                    },
                    width: 300,  // 二维码宽度
                    height: 300  // 二维码高度
                }, (error) => {
                    if (error) {
                        console.error(error);
                    } else {
                        console.log('QR code generated!');
                    }
                });
        },

        closeBoolean(val) {
            this.$emit("closeBoolean", val);
        },
        handleSubmit() { },
    },
};
</script>

<style lang="scss" scoped>
.questionDialog {
    // height: 320px;
    text-align: center;

    .dialog-title {
        font-weight: 550;
        margin-bottom: 20px;
    }

    .qrcode {
        margin: 0 auto;

    }
}

.footer {
    text-align: center;
}
</style>