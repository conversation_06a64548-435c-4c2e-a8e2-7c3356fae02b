<template>
  <!--  -->
  <div class="boxCon">
    <div class="boxRight">
      <div class="searchTop">
        <div>
          <el-input
            v-model="searchObj.keywords"
            size="mini"
            placeholder="请输入灾害名称"
            clearable
          >
          </el-input>
        </div>
        <div>
          <el-select
            v-model="searchObj.draftFlag"
            size="mini"
            placeholder="请选择策略状态"
            clearable
          >
            <el-option
              v-for="item in draftFlagOption"
              :key="item.code"
              :label="item.name"
              :value="item.code"
            >
            </el-option>
          </el-select>
        </div>
        <div>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
        </div>
      </div>

      <div class="importantBox">
        <h2>自然灾害</h2>
        <div>
          <!-- <el-button type="primary" size="mini" @click="search">查询</el-button> -->

          <el-button @click="openDialog('add')" type="primary" size="mini">
            <!----><!----><span>添加</span>
          </el-button>
        </div>
      </div>

      <div class="table-main">
        <!-- <div class="table-top">
          <h2>特殊时期列表</h2>
        </div> -->
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
            >
              <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="disasterName"
                label="灾害名称"
                align="center"
                width="200"
              >
              </el-table-column>
              <el-table-column
                prop="disasterTypeName"
                label="灾害类型"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <!-- <el-table-column
                prop="disasterLevel"
                label="灾害等级"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column> -->

              <el-table-column
                prop="disasterInfo"
                label="灾害描述"
                align="center"
                width="200"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="startTime"
                label="起始时间"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="endTime"
                label="结束时间"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="districtName"
                label="生效区域"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="draftFlag"
                label="策略状态"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row }">
                  <div class="tabButton">
                    <!-- {{row}} -->
                    <span>{{ row.draftFlag == 0 ? "未启用" : "启用" }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="180" align="center">
                <template slot-scope="{ row }">
                  <div class="tabButton">
                    <el-button
                      type="text"
                      @click="openDialog('read', row)"
                      v-if="
                        row.handleFlag == 2 ||
                        row.handleFlag == 0 ||
                        row.handleFlag == 1
                      "
                      >查看</el-button
                    >
                    <el-button
                      type="text"
                      @click="openDialog('edit', row)"
                      v-if="row.handleFlag == 2 || row.handleFlag == 1"
                      >编辑</el-button
                    ><el-button
                      type="text"
                      @click="deleteAccident(row.id)"
                      v-if="row.handleFlag == 2"
                      >删除</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="searchObj.nowPage"
              background
              layout="total, prev, pager, next"
              :total="searchObj.total"
              v-if="searchObj.total != 0"
            >
            </el-pagination>
          </div>
        </div>
      </div>
      <el-dialog
        :title="dialogInfo.title"
        :visible.sync="dialogInfo.visible"
        width="1100px"
        @close="closeDialog"
        v-if="dialogInfo.visible"
        :close-on-click-modal="false"
      >
        <div class="dialog">
          <el-form
            :model="accidentForm"
            :rules="rules"
            ref="ruleForm"
            label-width="150px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="灾害名称:" prop="disasterName">
                  <el-input
                    v-model.trim="accidentForm.disasterName"
                    maxlength="50"
                    placeholder="请输入灾害名称"
                    :disabled="dialogInfo.disable"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item
                  label="灾害发生行政区划:"
                  prop="disasterDistrictCode"
                >
                  <!-- {{district}} -->
                  <el-cascader
                    placeholder="请选择行政区划"
                    :options="district"
                    :disabled="dialogInfo.disable"
                    v-model="accidentForm.disasterDistrictCode"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    :show-all-levels="true"
                    style="width: 100%"
                  ></el-cascader>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <!-- <el-col :span="12">
                <el-form-item label="灾害等级:" prop="disasterLevel">
                  <el-select
                    v-model="accidentForm.disasterLevel"
                    placeholder="请输入事故等级"
                    style="width: 100%"
                    :disabled="dialogInfo.disable"
                  >
                    <el-option
                      v-for="item in levelList"
                      :key="item.levelCode"
                      :value="item.levelCode"
                      :label="item.levelName"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col> -->

              <el-col :span="12">
                <el-form-item label="灾害类型:" prop="disasterType">
                  <!-- <el-select
                    v-model="accidentForm.disasterType"
                    placeholder="请输入灾害类型"
                    style="width: 100%"
                    :disabled="dialogInfo.disable"
                  >
                    <el-option
                      v-for="item in typeList"
                      :key="item.code"
                      :value="item.code"
                      :label="item.name"
                    ></el-option>
                  </el-select> -->
                  <el-cascader placeholder="请输入灾害类型"
                    v-model="accidentForm.disasterType"
                    :options="disasterTypeCode"
                    style="width:100%"
                    @change="handleChangeEstablishOrgCode" 
                    :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                  </el-cascader>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="发生灾害日期" prop="startTime">
                  <!-- <el-date-picker
                    v-model="value1"
                    type="daterange"
                    style="width: 100%"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="searchTime"
                    value-format="yyyy-MM-dd"
                    :disabled="dialogInfo.disable"
                  >
                  </el-date-picker> -->
                  <el-date-picker
                    v-model="value1"
                    type="datetimerange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    @change="searchTime"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    unlink-panels
                    style="width: 100%"
                    :disabled="dialogInfo.disable"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="涉及行政区划:" prop="districtCode">
                  <!-- {{district}} -->
                  <el-cascader
                    placeholder="请选择行政区划"
                    :options="district"
                    :disabled="isEditDisabled"
                    v-model="accidentForm.districtCode"
                    :props="{
                      checkStrictly: true,
                      value: 'distCode',
                      label: 'distName',
                      children: 'children',
                      emitPath: false,
                    }"
                    clearable
                    :show-all-levels="true"
                    style="width: 100%"
                  ></el-cascader>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="是否启用:" prop="draftFlag">
                  <el-select
                    v-model="accidentForm.draftFlag"
                    placeholder="请选择"
                    style="width: 100%"
                    :disabled="isEditDisabled"
                  >
                    <el-option
                      v-for="item in draftFlagOption"
                      :key="item.code"
                      :value="item.code"
                      :label="item.name"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12"> </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="灾害描述:" prop="disasterInfo">
                  <el-input
                    v-model.trim="accidentForm.disasterInfo"
                    type="textarea"
                    :rows="5"
                    placeholder="500字以内"
                    maxlength="500"
                    :disabled="dialogInfo.disable"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="closeDialog" v-if="!isEditDisabled"
              >取 消</el-button
            >
            <!-- <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button> -->
            <el-button type="primary" @click="saveData()" v-if="!isEditDisabled"
              >提 交</el-button
            >
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { mapState, createNamespacedHelpers } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import {
  getAccidentListData,
  getAccidentTypeListData,
  deleteAccidentById,
  addAccident,
  updateAccident,
  disasterTypeV1
} from "@/api/accidentManagement";
import { parseTime } from "@/utils/index";
import { MessageBox } from "element-ui";
const mapConfig = require("@/assets/json/map.json");
import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");
// import {
//   getSelectData,
//   getInformationInfoDanger,
//   getHazarchemList,
// } from "@/api/entList";
import {
  naturalDisasterPageList,
  naturalDisasterAdd,
  naturalDisasterDelete,
  naturalDisasterUpdate,
  naturalDisasterFind,
} from "@/api/accidentManagement";
export default {
  data() {
    //  pickerOptions0: { // 禁止选择今天以前的日期
    //     disabledDate(time) {
    //       return time.getTime() < Date.now() - 8.64e7
    //     }
    //   },
    return {
      disasterTypeCode:[],
      isEditDisabled: false,
      statutoryData: [],
      calendarData: new Date(),
      tableDataQuery: [], //重要活动
      legalData: [], //法定节假日
      value1: [],
      header: {
        token: this["$store"].state.login.token,
      },
      typeList: [], // 事故类型数据
      levelList: [], // 事故等级数据
      tableData: [], // 表格数据
      loading: false, // 加载状态
      searchObj: {
        // 表格查询参数
        nowPage: 1,
        total: 0,
        pageSize: 10,
        keywords: "",
        draftFlag: "",
      },
      dateValue: "", // 时间选择
      options: [
        {
          value: "1",
          label: "停工",
        },
        {
          value: "0",
          label: "试生产",
        },
        {
          value: "2",
          label: "正常开工",
        },
      ],
      draftFlagOption: [
        { name: "未启用", code: "0" },
        { name: "启用", code: "1" },
      ],
      levelList: [], // 特殊时期等级数据
      dialogInfo: {
        // 弹窗控制信息
        visible: false,
        title: "新增自然灾害",
        disable: false,
      },
      accidentForm: {
        disasterType: "", //灾害类型
        disasterLevel: "", //灾害等级
        disasterDistrictCode: this.$store.state.login.userDistCode, //行政区划代
        districtCode: "", // 涉及行政区划
        disasterName: "", // 灾害名称
        draftFlag: "", //  是否启用
        disasterInfo: "", //灾害描述
        endTime: "",
        startTime: "",
      },
      rules: {
        // 表单校验规则
        disasterName: [
          {
            required: true,
            message: "灾害名称不能为空",
            trigger: "blur",
          },
        ],
        disasterDistrictCode: [
          {
            required: true,
            message: "灾害发生行政区划不能为空",
            trigger: "change",
          },
        ],
        disasterType: [
          {
            required: true,
            message: "灾害类型不能为空",
            trigger: "change",
          },
        ],
        startTime: [
          {
            required: true,
            message: "发生灾害日期不能为空",
            trigger: "change",
          },
        ],
        districtCode: [
          {
            required: true,
            message: "涉及行政区划不能为空",
            trigger: "change",
          },
        ],
        draftFlag: [
          {
            required: true,
            message: "是否启用不能为空",
            trigger: "change",
          },
        ],
      },
      district: this.$store.state.controler.district, // 行政区划
    };
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
  created() {},
  methods: {
    handleChangeEstablishOrgCode(value) {
      if (value.length > 0) {
        this.accidentForm.disasterType = (value[value.length - 1])
      } else {
        this.accidentForm.disasterType = ''
      }
    },
    // 获取事故类型列表
    getaccidentTypeList() {
      // getAccidentTypeListData().then((res) => {
      //   if (res.status === 200) {         
      //     this.levelList = res.data.data.accidentLevel;
      //   }
      // });

      disasterTypeV1().then((res) => {
        if (res.status === 200) {
          this.disasterTypeCode = res.data.data[0].children;
          
        }
      });


    },
    searchTime(value1) {
      if (value1) {
        //  this.value1 = value1;
        // let date1 = new Date(value1[0]);
        // let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        // let date2 = new Date(value1[1]);
        // let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.accidentForm.startTime = value1[0];
        this.accidentForm.endTime = value1[1];
      } else {
        this.value1 = [];
        this.accidentForm.startTime = "";
        this.accidentForm.endTime = "";
      }
    },
    search() {
      this.searchObj.nowPage = 1;
      this.getAccidentList();
    },
    handleSelectionChange() {},

    // 分页查询
    handleCurrentChange(val) {
      this.searchObj.nowPage = val;
      this.getAccidentList();
    },
    //
    getAccidentList() {
      this.loading = true;
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        disasterLevel: "",
        disasterType: "",
        draftFlag: this.searchObj.draftFlag,
        endTime: "",
        id: "",
        keywords: this.searchObj.keywords,
        nowPage: this.searchObj.nowPage,
        pageSize: this.searchObj.pageSize,
        startTime: "",
      };
      naturalDisasterPageList(params).then((res) => {
        if (res.data.status === 200) {
          this.tableData = res.data.data.list;
          this.searchObj.nowPage = res.data.data.nowPage + 1;
          this.searchObj.total = res.data.data.total;
          this.loading = false;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },

    // 删除特殊时期列
    deleteAccident(id) {
      MessageBox.confirm("确定要删除选择的数据吗?", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info",
      })
        .then(() => {
          naturalDisasterDelete({ id })
            .then((res) => {
              this.$message.success("删除成功");
              if (this.tableData.length === 1 && this.searchObj.nowPage !== 1) {
                this.searchObj.nowPage--;
              }
              this.getAccidentList();
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {});
    },

    // 关闭弹窗
    closeDialog() {
      this.dialogInfo.visible = false;
      this.empty();
    },
    empty() {
      this.accidentForm = {
        disasterDistrictCode: this.$store.state.login.userDistCode, //行政区划代
        disasterType: "", //灾害类型
        disasterLevel: "", //灾害等级
        districtCode: "", // 涉及行政区划
        disasterName: "", // 活动名称
        draftFlag: "", //  是否启用
        disasterInfo: "", //灾害描述
        endTime: "",
        startTime: "",
      };
      this.resetForm("ruleForm");
    },
    // 打开弹窗
    openDialog(type, data) {
      const dataList = Object.assign({}, data);
      if (type === "edit") {
        this.dialogInfo.title = "编辑自然灾害";
        this.value1 = [];
        this.value1.push(dataList.startTime, dataList.endTime);
        this.accidentForm = dataList;
        this.dialogInfo.disable = true;
        if (data.handleFlag == 1) {
          //handleFlag为1的时候只能编辑启用
          this.dialogInfo.disable = true;
          this.isEditDisabled = false;
        } else if (data.handleFlag == 2) {
          //handleFlag是2的时候编辑的时候可以全部编辑
          this.dialogInfo.disable = false;
          this.isEditDisabled = false;
        }
      } else if (type === "add") {
        this.value1 = [];
        this.dialogInfo.title = "新增自然灾害";
        this.dialogInfo.disable = false;
      } else {
        this.value1 = [];
        this.value1.push(dataList.startTime, dataList.endTime);
        this.accidentForm = dataList;
        this.dialogInfo.title = "生产安全灾害描述";
        this.dialogInfo.disable = true;
        this.isEditDisabled = true;
      }

      this.dialogInfo.visible = true;
    },
    // 新增/编辑
    saveData() {
      // debugger
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.dialogInfo.title === "新增自然灾害"
            ? this.addData()
            : this.updateData();
        }
      });
    },
    // 新增特殊时期数据
    addData() {
      naturalDisasterAdd(this.accidentForm).then((res) => {
        if (res.data.status === 200) {
          this.$message.success("操作成功");
          this.dialogInfo.visible = false;
          this.getAccidentList();
          this.empty();
        } else {
          this.$message.warning(res.data.msg);
        }
      });
    },
    // 更新特殊时期数据
    updateData() {
      // debugger
      naturalDisasterUpdate(this.accidentForm).then((res) => {
        if (res.data.status === 200) {
          this.$message.success("操作成功");
          this.dialogInfo.visible = false;
          this.getAccidentList();
          this.empty();
        } else {
          this.$message.warning(res.data.mssg);
        }
      });
    },

    // 企业数据
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    // 清楚建议框
    clearSensororgCode() {
      this.accidentForm.companyId = "";
      this.accidentForm.companyName = "";
    },
    //选择企业
    handleSelect(item) {
      // consle.log(this)
      this.accidentForm.companyId = item.enterpId;
      this.accidentForm.companyName = item.enterpName;
      this.accidentForm.dangerId = "";
      this.getSelectDataById(item.enterpId);
      this.accidentForm.chemicalsId = "";
      this.getHazarchemData(item.enterpId);

      this.accidentForm.longitude = item.longitude;
      this.accidentForm.latitude = item.latitude;
      this.accidentForm.disasterDistrictCode = item.disasterDistrictCode;
      this.accidentForm.address = item.address;
      var x = {
        x: item.longitude,
        y: item.latitude,
      };
      //
      this.$refs["detailMap"].setMapDiot2(x);
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  mounted() {
    // this.getAccidentList();
    this.getaccidentTypeList();
  },
};
</script>
<style scoped lang="scss">
.searchTop {
  display: flex;
  margin: 0 0 10px 0;
  > div {
    margin: 0 10px 0 0;
  }
}
/deep/ .el-tabs__item {
  font-size: 16px;
}
/deep/ .el-calendar-table .el-calendar-day {
  height: 30px;
}
/deep/ .el-calendar__header .el-calendar__button-group {
  display: none !important;
}
/deep/ .el-calendar-table td.is-today {
  background-color: #409eff !important;
  color: #ccc !important;
}
/deep/ .el-table td.el-table__cell div {
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
}
/deep/ .el-tabs--left .el-tabs__header.is-left {
  height: calc(100% - 50px);
}
/deep/ .cell .tabButton .el-button {
  padding: 5px;
}
/deep/ .labelWidth .el-form-item__content {
  margin-left: 0 !important;
}
.boxRight {
  .legalBox {
    margin: 0 0 20px 0;
  }
}
.table {
  // height: 300px;
}
.dialog-footer {
  text-align: center;
}

.importantBox {
  display: flex;
  justify-content: space-between;
}
.table-main {
  background: #fff;
  .table-top {
    display: flex;
    justify-content: space-between;
    // padding: 10px 0;
    h2 {
      font-size: 18px;
      line-height: 45px;
      margin-bottom: 0;
    }
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>