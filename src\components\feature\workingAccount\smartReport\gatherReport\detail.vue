<template>
  <div class="safety-check">
    <!-- 添加头部标题 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box" @click="goBack">
              <a-icon type="home" theme="filled" class="icon" />
              采集报表管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <span>{{ reportName }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 内容区域包裹 -->
    <div class="content">
      <!-- 头部工具栏 -->
      <div class="tool-bar">
        <div class="left">
          <el-button-group>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-setting"
              @click="handleToolbarClick('fields')"
              >字段设定</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-download"
              @click="handleToolbarClick('export')"
              >导出</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-search"
              @click="handleToolbarClick('search')"
              >查询</el-button
            >
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-share"
              @click="handleToolbarClick('share')"
              >分享</el-button
            >
          </el-button-group>
        </div>
        <div class="right">
          <el-form :inline="true" :model="searchForm">
            <el-form-item>
              <el-date-picker
                v-model="searchForm.timeRange"
                size="mini"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                unlink-panels
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-select
                size="mini"
                v-model="searchForm.region"
                placeholder="行政区划"
              >
                <el-option label="区域一" value="1"></el-option>
                <el-option label="区域二" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select
                size="mini"
                v-model="searchForm.accidentType"
                placeholder="事故类型"
              >
                <el-option label="类型一" value="1"></el-option>
                <el-option label="类型二" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="searchForm.accidentLevel"
                placeholder="事故类型"
                size="mini"
              >
                <el-option label="级别一" value="1"></el-option>
                <el-option label="级别二" value="2"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="mini" @click="handleSearch"
                >检索</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 表格内容 -->
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :span-method="objectSpanMethod"
      >
        <template v-for="column in displayColumns">
          <!-- 处理嵌套列 -->
          <template v-if="column.children">
            <el-table-column
              :key="column.prop"
              :label="column.label"
              :width="column.width"
            >
              <template v-for="child in column.children">
                <el-table-column
                  :key="child.prop"
                  :prop="child.prop"
                  :label="child.label"
                  :width="child.width"
                >
                </el-table-column>
              </template>
            </el-table-column>
          </template>
          <!-- 处理普通列 -->
          <el-table-column
            v-else
            :key="column.prop"
            :prop="column.prop"
            :label="column.label"
            :width="column.width"
          >
          </el-table-column>
        </template>
      </el-table>

      <!-- 使用字段设定弹窗组件 -->
      <field-settings-dialog
        :visible.sync="fieldSettingsVisible"
        :all-fields="allFields"
        :table-list="tableList"
        :current-fields="selectedFields"
        @save="handleFieldSettingsSave"
      />

      <!-- 添加导出预览弹窗 -->
      <el-dialog
        :visible.sync="exportDialogVisible"
        title="导出预览"
        width="90%"
        :close-on-click-modal="false"
        custom-class="export-dialog"
        append-to-body
      >
        <div class="export-preview" ref="exportPreview">
          <h2 class="preview-title">2024年生产安全事故查处情况表</h2>
          <div class="preview-time">
            {{ getCurrentDate() }}
          </div>
          <el-table
            :data="tableData"
            border
            style="width: 100%"
            :span-method="objectSpanMethod"
            class="preview-table"
          >
            <template v-for="column in displayColumns">
              <template v-if="column.children">
                <el-table-column
                  :key="column.prop"
                  :label="column.label"
                  :width="column.width"
                >
                  <template v-for="child in column.children">
                    <el-table-column
                      :key="child.prop"
                      :prop="child.prop"
                      :label="child.label"
                      :width="child.width"
                    >
                    </el-table-column>
                  </template>
                </el-table-column>
              </template>
              <el-table-column
                v-else
                :key="column.prop"
                :prop="column.prop"
                :label="column.label"
                :width="column.width"
              >
              </el-table-column>
            </template>
          </el-table>
        </div>

        <!-- 底部按钮 -->
        <div slot="footer" class="dialog-footer">
          <el-button @click="exportDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSave">保存</el-button>
          <el-button type="primary" @click="handlePrint">打印</el-button>
        </div>
      </el-dialog>

      <!-- 添加分享弹窗 -->
      <el-dialog
        title="分享"
        :visible.sync="shareDialogVisible"
        width="600px"
        append-to-body
      >
        <div class="share-dialog">
          <div class="org-tree">
            <el-tree
              ref="orgTree"
              :data="orgTreeData"
              :props="orgTreeProps"
              show-checkbox
              node-key="id"
              default-expand-all
            >
            </el-tree>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="shareDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleShare">确定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import FieldSettingsDialog from "../components/FieldSettingsDialog.vue";
import { getOrgTree } from "@/api/user";

export default {
  name: "CountReportDetail",
  components: {
    FieldSettingsDialog,
  },
  props: {
    reportId: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      searchForm: {
        timeRange: [],
        region: "",
        accidentType: "",
        accidentLevel: "",
      },
      tableData: [], // 表格数据
      fieldSettingsVisible: false, // 字段设定弹窗显示状态
      selectedFields: [], // 选中字段的 key 列表
      // 所有可选字段
      allFields: [
        { key: "index", label: "序号", width: "60" },
        { key: "region", label: "所属区", width: "120" },
        { key: "accidentName", label: "事故名称" },
        { key: "approvalDate", label: "批复日期", width: "120" },
        {
          key: "handlePeople",
          label: "处理人员（不含单位处分人数）",
          width: "200",
          children: [
            { key: "totalPeople", label: "总人数", width: "80" },
            { key: "punishedPeople", label: "处分人数", width: "120" },
          ],
        },
        {
          key: "govPunishment",
          label: "政务处分",
          width: "400",
          children: [
            { key: "warning", label: "警告", width: "80" },
            { key: "record", label: "记过", width: "80" },
            { key: "bigRecord", label: "记大过", width: "80" },
            { key: "demotion", label: "降级", width: "80" },
            { key: "dismissal", label: "撤职", width: "80" },
          ],
        },
        {
          key: "partyPunishment",
          label: "党纪处分",
          width: "400",
          children: [
            { key: "partyWarning", label: "警告", width: "80" },
            { key: "severWarning", label: "严重警告", width: "100" },
            { key: "removePosition", label: "撤销职务", width: "100" },
            { key: "observation", label: "留党察看", width: "100" },
          ],
        },
        { key: "companyPunishment", label: "公司内部处理", width: "120" },
        { key: "punishmentUnit", label: "处罚单位数", width: "120" },
        { key: "punishmentPeople", label: "处罚人数", width: "120" },
        {
          key: "fine",
          label: "罚款金额（万元）",
          width: "200",
          children: [
            { key: "accidentFine", label: "事故罚款", width: "100" },
            { key: "responsibleFine", label: "责任人", width: "100" },
          ],
        },
        { key: "transferJustice", label: "移送检察机关人数", width: "150" },
        { key: "investigationStatus", label: "调查情况", width: "120" },
        { key: "remark", label: "备注" },
      ],
      // 数据表列表
      tableList: [
        { label: "行政处罚表", value: "punishment" },
        { label: "企业信息表", value: "enterprise" },
        { label: "事故记录表", value: "accident" },
      ],
      // 修改为 transfer 组件需要的数据格式
      transferData: [], // 用于 transfer 组件的数据列表
      exportDialogVisible: false, // 导出预览弹窗显示状态
      reportName: "", // 报表名称
      // 将 displayColumns 从计算属性移到 data 中
      displayColumns: [], // 表格显示的列配置
      shareDialogVisible: false, // 分享弹窗显示状态
      orgTreeData: [], // 机构树数据
      orgTreeProps: {
        children: "children",
        label: "orgName",
      },
    };
  },
  computed: {
    // 根据selectedFields过滤显示的列
    // displayColumns() {
    //   if (this.selectedFields.length === 0) {
    //     return this.allFields;
    //   }
    //   return this.allFields.filter((column) => {
    //     if (column.children) {
    //       // 如果是嵌套列，检查是否有子列被选中
    //       return column.children.some((child) =>
    //         this.selectedFields.includes(child.key)
    //       );
    //     }
    //     return this.selectedFields.includes(column.key);
    //   });
    // },
  },
  watch: {
    // 监听数据表选择变化
    selectedFields(newVal) {
      // 当选择的数据表变化时，更新可选字段
      this.updateAvailableFields();
    },
  },
  methods: {
    // 修改工具栏按钮点击事件
    handleToolbarClick(type) {
      switch (type) {
        case "fields":
          this.showFieldSettings();
          break;
        case "export":
          this.showExportDialog();
          break;
        case "search":
          this.handleSearch();
          break;
        case "share":
          this.showShareDialog();
          break;
        default:
          break;
      }
    },

    // 显示字段设定弹窗
    showFieldSettings() {
      this.fieldSettingsVisible = true;
    },

    // 显示导出预览弹窗
    showExportDialog() {
      this.exportDialogVisible = true;
    },

    // 处理搜索
    handleSearch() {
      // TODO: 实现搜索逻辑
      console.log("搜索条件:", this.searchForm);
    },

    // 根据选中的字段更新显示列
    updateDisplayColumns() {
      // 如果没有选中任何字段，显示所有字段
      if (this.selectedFields.length === 0) {
        this.displayColumns = this.allFields;
        return;
      }

      // 根据选中的字段过滤显示列
      this.displayColumns = this.allFields.filter((column) => {
        if (column.children) {
          // 如果是嵌套列，检查是否有子列被选中
          return column.children.some((child) =>
            this.selectedFields.includes(child.key)
          );
        }
        return this.selectedFields.includes(column.key);
      });
    },
    // 字段设置保存
    handleFieldSettingsSave(settings) {
      this.selectedFields = settings.selectedFields;
      this.updateDisplayColumns();
      this.fieldSettingsVisible = false;
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: 2,
          colspan: 1,
        };
      }
    },
    // 更新可选字段
    updateAvailableFields() {
      // 这里可以根据选择的数据表过滤或合并字段
      // 后续可以接入接口动态获取字段
    },
    // 修改转换方法，只转换父级字段
    convertToTransferData() {
      const result = [];
      this.allFields.forEach((field) => {
        result.push({
          key: field.key,
          label: field.label,
          disabled: false,
          children: field.children || [], // 保存子字段信息
        });
      });
      this.transferData = result;
    },
    // 字段选择变化
    handleFieldChange(selectedKeys) {
      this.selectedFields = selectedKeys;
      this.updateDisplayColumns();
    },
    // 保存为Excel
    handleSave() {
      // TODO: 实现保存为Excel的逻辑
      this.exportDialogVisible = false;
    },

    // 打印预览内容
    handlePrint() {},

    // 修改获取当前日期的方法
    getCurrentDate() {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      return `${year}年${month}月${day}日`;
    },

    // 返回列表
    goBack() {
      this.$emit("goBack");
    },

    // 修改初始化方法
    initData(row) {
      if (row.name) {
        this.reportName = row.name;
      }

      // 转换数据格式
      this.convertToTransferData();

      // 初始化显示所有列
      this.displayColumns = this.allFields;
      // 初始化选中状态为所有字段的 key（包括子字段）
      this.selectedFields = this.getAllFieldKeys(this.allFields);

      this.getReportData();
    },

    // 添加获取所有字段 key 的方法（包括子字段）
    getAllFieldKeys(fields) {
      let keys = [];
      fields.forEach((field) => {
        if (field.children) {
          // 如果有子字段，只添加子字段的 key
          keys = keys.concat(field.children.map((child) => child.key));
        } else {
          // 如果是普通字段，添加字段的 key
          keys.push(field.key);
        }
      });
      return keys;
    },

    // 获取报表数据
    getReportData() {
      // TODO: 调用接口获取报表数据
    },

    // 显示分享弹窗
    showShareDialog() {
      this.shareDialogVisible = true;
      this.getOrgTreeData();
    },

    // 获取机构树数据
    async getOrgTreeData() {
      try {
        const res = await getOrgTree({
          districtCode: this.$store.state.login.userDistCode,
        });
        if (res.data.status == 200) {
          this.orgTreeData = res.data.data;
        }
      } catch (error) {
        console.error("获取机构树失败:", error);
      }
    },

    // 处理分享
    handleShare() {
      const selectedNodes = this.$refs.orgTree.getCheckedNodes();
      const selectedIds = selectedNodes.map((node) => node.id);

      // TODO: 调用分享接口
      console.log("分享给以下机构:", selectedIds);

      this.$message.success("分享成功");
      this.shareDialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-check {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }

      .icon-box {
        cursor: pointer;

        &:hover {
          color: #3977ea;

          .icon {
            color: #3977ea;
          }
        }
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;
  }

  .tool-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    .left {
      .el-button {
        margin-right: 10px;
      }
    }
  }

  .field-settings {
    display: flex;
    height: 500px;
    gap: 20px; // 添加间距

    .left-panel {
      .table-select {
        margin-bottom: 20px;

        .panel-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
        }

        .el-select {
          width: 100%;
        }
      }

      .field-list {
        .panel-title {
          font-size: 14px;
          font-weight: bold;
          margin-bottom: 10px;
        }
      }
    }
  }
}

// 优化弹窗样式
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    border-top: 1px solid #ebeef5;
    padding: 15px 20px;
  }
}

::v-deep .el-transfer {
  display: flex;
  justify-content: center;
  align-items: center;

  .el-transfer-panel {
    width: 300px;

    &__header {
      background: #f5f7fa;
    }
  }

  .el-transfer__buttons {
    padding: 0 20px;

    .el-button {
      display: block;
      margin: 10px 0;
    }
  }
}

// 优化多选样式
::v-deep .el-select {
  .el-select__tags {
    max-width: calc(100% - 30px);
  }

  .el-tag {
    margin: 2px;
  }
}

.export-preview {
  padding: 20px;
  background-color: #fff;

  .preview-title {
    text-align: center;
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
  }

  .preview-time {
    text-align: right;
    margin-bottom: 20px;
    color: #333;
    font-size: 15px;
  }

  .preview-table {
    margin-bottom: 20px;
  }
}

// 打印样式
@media print {
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }

  .export-preview {
    padding: 0;
  }
}

// 优化弹窗样式
::v-deep .export-dialog {
  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    padding: 15px 20px;
    border-top: 1px solid #ebeef5;
  }
}

.share-dialog {
  .org-tree {
    height: 400px;
    overflow: auto;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
  }
}
</style>
