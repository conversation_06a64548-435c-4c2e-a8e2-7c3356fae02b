import axios from "axios";
import qs from "qs";

//风险研判-物联监测报警列表
// export const getIotMontoringData = data => {
//   return axios({
//     method: "get",
//     url: '/enterprise/monitoring/riskAssessmentMonitoringWarning/list?size=' + data.size + '&current=' + data.current + '&distCode=' + data.distCode + '&sensortypeCode=' + data.sensortypeCode + '&monitemkey=' + data.monitemkey + '&warningType=' + data.warningType + '&state=' + data.state + '&enterpName=' + data.enterpName + '&endTime=' + data.endTime + '&startTime=' + data.startTime,
//     data: data
//   });
// };

//换成post
export const getIotMontoringData = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/monitoring/riskAssessmentMonitoringWarning/list?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//风险研判-物联监测报警导出
export const getIotMontoringExportExcel = (table) => {
  return axios({
    method: "post",
    url: "/enterprise/monitoring/riskAssessmentMonitoringWarning/exportExcel",
    data: { ...table },
    responseType: "arraybuffer",
  });
};
//风险研判-视频在线监控
export const getOnlineVideoData = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/video/tree?enterpName=" + data.enterpName,
    data: data,
  });
};
export const getOnlineVideoStatus = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/video/tree?enterpName=" +
      data.enterpName +
      "&onlineStatus=" +
      data.onlineStatus,
    data: data,
  });
};
//风险预警推送列表
// export const postCimEarlyWarningPushList = data => {
//   return axios({
//     method: "post",
//     url: '/enterprise/cimEarlyWarning/cimEarlyWarningPushList?size=' + data.size + '&current=' + data.current,
//     data: data
//   });
// };

export const postCimEarlyWarningPushList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/cimEarlyWarning/page/v1",
    data: data,
  });
};

//风险预警推送列表导出
// export const cimEarlyWarningExportExcel = data => {
//   return axios({
//     method: "post",
//     url: '/enterprise/cimEarlyWarning/exportExcel',
//     data: data,
//     responseType: "arraybuffer"
//   });
// };
export const cimEarlyWarningExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/cimEarlyWarning/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
//预警通报 废弃！！！
export const postCimEarlyWarningWarningPush = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/cimEarlyWarning/warningPush",
    data: data,
  });
};
//预警通报详情 废弃！！！
export const postCimEarlyWarningWarningDetails = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/cimEarlyWarning/warningDetails?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};
//台账预警详情
export const postCimEarlyWarningWorkLedgerWarningDetails = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/cimEarlyWarning/workLedgerWarningDetails",
    data: data,
  });
};
//台账预警详情2
export const cimEarlyWarningCount = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/cimEarlyWarning/count/v1",
    data: data,
  });
};
// 预警手动通报
export const postEnterpriseWarningNoticeAdd = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/warningNotice/add",
    data: data,
  });
};
//监管通报
export const getWarningNoticeList = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/warningNotice/list",
    params: data,
  });
};
export const getSelCompany = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/cimEarlyWarning/selCompany/" + data,
    params: data,
  });
};
// export const postCimEarlyWarningFeedBackAdd = data => {
//   return axios({
//     method: "post",
//     url: "/enterprise/cimEarlyWarning/feedBack/add",
//     data: data
//   })
// }
export const postCimEarlyWarningFeedBackAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/cimEarlyWarning/feedback/add/v1",
    data: data,
  });
};
export const getCimEarlyWarningFeedBackInfo = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/cimEarlyWarning/feedBack/info/" + data,
    data: data,
  });
};
// 查询在线巡查列表
export const getOnlinePatrolList = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/list",
    params: data,
  });
};
//系统在线详情
export const getOnlinePatrolQuerySysDetails = (params) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/querySysDetails",
    params: params,
  });
};
//查询视频详情
export const getOnlinePatrolQueryVideoDetails = (params) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/queryVideoDetails",
    params: params,
  });
};
//查询预警详情
export const postOnlinePatrolQueryEarlyWarningAndNotificationDetails = (
  params
) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/queryEarlyWarningAndNotificationDetails",
    params: params,
  });
};

//查询安全承诺详情
export const postOnlinePatrolQuerySafetyCommitDetails = (params) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/querySafetyCommitDetails",
    params: params,
  });
};
//查询超24小时未消警详情

export const postOnlinePatrolQueryNoAlarmCancelDetails = (params) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/queryNoAlarmCancelDetails",
    params: params,
  });
};
// 加入巡查
export const postOnlinePatrolInspectAdd = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/inspectAdd",
    data: data,
  });
};
// 查询巡查记录列表
export const postOnlinePatrolInspectList = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/inspectList",
    params: data,
  });
};
// 巡查反馈列表
export const postOnlinePatrolReplyList = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/replyList",
    params: data,
  });
};
// 提交巡查反馈
export const postOnlinePatrolReply = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/reply",
    data: data,
  });
};
// 生成自动选择的巡查数据
export const oneClick = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/oneClick",
    data: data,
  });
};
//查看巡查报告
export const postOnlinePatrolViewReportDetails = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/viewReportDetails",
    params: data,
  });
};
//--下发|删除操作
export const postOnlinePatrolIssue = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/issue",
    data: data,
  });
};
//历史巡查记录导出
export const postOnlinePatrolExport = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/export",
    data: data,
    responseType: "arraybuffer",
  });
};
// ---查询反馈详情：
export const postOnlinePatrolQueryFeedbackDetails = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/onlinePatrol/queryFeedbackDetails",
    params: data,
  });
};
// 获取视频的rtsp地址
export const getRealmonitor = (data) => {
  return axios({
    method: "post",
    url: "/dahuaSSO/evo-apigw/admin/API/MTS/Video/StartVideo",
    data: data,
  });
};

// 获取视频的rtsp地址-new
export const getRealmonitorNew = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data-sync/api/gemp/videoSync/getUrl/v1",
    data: data,
  });
};

//企业电力监管-分页查询
export const getElectricityPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/page/v1",
    data: data,
  });
};

//企业电力监管-新增监管
export const getElectricityAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/add/v1",
    data: data,
  });
};

//企业电力监管-修改监管
export const getElectricityUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/update/v1",
    data: data,
  });
};

//企业电力监管-删除监管
export const getElectricityDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/delete/v1",
    data: data,
  });
};

//企业电力监管-详情
export const getElectricityFindOne = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/findOne/v1",
    data: data,
  });
};

//企业电力监管-企业电力监管饼状图
export const getSupervisePieChart = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/pieChart/enterpStatus/v1",
    data: data,
  });
};

//企业电力监管-导出excel ok
export const getSuperviseExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/supervise/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//企业电力报警-报警类型下拉列表 ok
export const getAlarmType = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/alarm/alarmType/v1",
    data: data,
  });
};
//企业电力报警-企业用电量查询
export const getEnterpConsumption = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/alarm/enterpConsumption/v1",
    data: data,
  });
};

//企业电力报警-报警数据导出excel ok
export const getElectricityExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/alarm/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//企业电力报警-根据alarmId查询报警详情 ok
export const getElectricityFindById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/alarm/findById/v1",
    data: data,
  });
};

//企业电力报警-报警数据分页查询 ok
export const getElectricityFindPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/alarm/findPage/v1",
    data: data,
  });
};

//企业电力报警-报警数据饼状图
export const getElectricityPieChart = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/electricity/alarm/pieChart/v1",
    data: data,
  });
};

//监测预警日报控制器-根据reportId删除日报记录 ok
export const dailyReportDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/delete/v1",
    data: data,
  });
};

//监测预警日报控制器-根据reportId查询日报记录详情
export const dailyReportFindOne = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/findOne/v1",
    data: data,
  });
};

//监测预警日报控制器-分页查询日报记录 ok
export const dailyReportFindPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/findPage/v1",
    data: data,
  });
};

//监测预警日报控制器-生成日报记录（所有字段可以不传值）
export const dailyReportGenerate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/generate/v1",
    data: data,
  });
};

//监测预警日报控制器-发布日报记录（必须有附件）
export const dailyReportIssue = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/issue/v1",
    data: data,
  });
};

//监测预警日报控制器-生成日报
export const dailyReportExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/exportReport/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//监测预警周报控制器-根据reportId删除周报记录 ok
export const weeklyReportDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/delete/v1",
    data: data,
  });
};

//监测预警周报控制器-根据reportId查询周报记录详情
export const weeklyReportFindOne = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/findOne/v1",
    data: data,
  });
};

//监测预警周报控制器-分页查询周报记录 ok
export const weeklyReportFindPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/findPage/v1",
    data: data,
  });
};

//监测预警周报控制器-生成周报记录（所有字段可以不传值）
export const weeklyReportGenerate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/generate/v1",
    data: data,
  });
};

//监测预警周报控制器-发布周报记录（必须有附件）
export const weeklyReportIssue = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/issue/v1",
    data: data,
  });
};

//监测预警周报控制器-生成周报
export const weeklyReportExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/exportReport/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//监测预警周报控制器-查询本年最新期号
export const weeklyReportissueNum = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/weeklyReport/issueNum/v1",
    data: data,
  });
};

//监测预警日报控制器-查询本年最新期号
export const dailyReportissueNum = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dailyReport/issueNum/v1",
    data: data,
  });
};

//监测预警月报控制器-根据reportId删除月报记录 ok
export const monthlyReportDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/delete/v1",
    data: data,
  });
};

//监测预警月报控制器-根据reportId查询月报记录详情
export const monthlyReportFindOne = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/findOne/v1",
    data: data,
  });
};

//监测预警月报控制器-分页查询月报记录 ok
export const monthlyReportFindPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/findPage/v1",
    data: data,
  });
};

//监测预警月报控制器-生成月报记录（所有字段可以不传值）
export const monthlyReportGenerate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/generate/v1",
    data: data,
  });
};

//监测预警月报控制器-发布月报记录（必须有附件）
export const monthlyReportIssue = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/issue/v1",
    data: data,
  });
};

//监测预警月报控制器-生成月报 ？？?
export const monthlyReportExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/exportReport/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//监测预警月报控制器-查询本年最新期号 ????
export const monthlyReportissueNum = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/monthlyReport/issueNum/v1",
    data: data,
  });
};

//企业模型控制器-区域查询风险等级企业统计
export const queryDistCodeCount = (data) => {
  return axios({
    method: "get",
    url:
      "/gemp-model/api/gemp/companyRiskModel/queryDistCodeCount/v1?distCode=" +
      data.distCode,
    data: data,
  });
};

//企业模型控制器-区域查询企业风险
export const queryPageRiskPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/companyRiskModel/queryPageRiskPage/v1",
    data: data,
  });
};

//企业模型控制器-区域查询企业风险
export const queryRiskAreaByDistCode = (data) => {
  return axios({
    method: "get",
    url:
      "/gemp-model/api/gemp/companyRiskModel/queryRiskAreaByDistCode/v1?distCode=" +
      data.distCode,
    data: data,
  });
};

//企业模型控制器-企业风险详情查询
export const queryRiskCompanyInfo = (data) => {
  return axios({
    method: "get",
    url:
      "/gemp-model/api/gemp/companyRiskModel/queryRiskCompanyInfo/v1?enterId=" +
      data.enterId,
    data: data,
  });
};

//企业经济类型列表
export const getPreDistrict = (data) => {
  return axios({
    method: "post",
    url: `/gemp-user/api/gemp/user/district/id/v1`,
    data: data,
  });
};

//在园企业-分页查询
export const parkQueryEnterPageList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/cim/park/queryEnterPageList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//园区信息-分页查询
export const parkQueryPageList = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/cim/park/queryPageList?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
  });
};

//地区企业机构树
export const districtEnterprise = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/org/trees/districtEnterprise/v1",
    data: data,
  });
};

//近五年事故类型趋势
export const eventStatisticsType = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/eventStatistics/type/v1?code=" + data.code,
    data: data,
  });
};

//近五年事故等级趋势
export const eventStatisticsLevel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/eventStatistics/level/v1?code=" + data.code,
    data: data,
  });
};

//环比统计
export const eventStatisticsMothly = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/eventStatistics/monthly/v1?date=" +
      data.date +
      "&code=" +
      data.code,
    data: data,
  });
};

//同比统计
export const eventStatisticsGrowth = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/eventStatistics/growth/v1?code=" + data.code,
    data: data,
  });
};

//统计列表
export const eventStatisticsList = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/eventStatistics/statistics/v1?districtCode=" +
      data.districtCode +
      "&startDate=" +
      data.startDate +
      "&endDate=" +
      data.endDate,
    data: data,
  });
};
//现场检查记录控制器
export const spotInspectFindPage = (data) => {
  return axios({
    method: "post",
    // url: "/gemp-chemical/api/gemp/spotInspect/findPage/v1",
    url:
      "/enterprise/lawEnforcement/list?size=" +
      data.pageSize +
      "&current=" +
      data.page,
    data: data,
  });
};
// 获取执法辅助推荐记录
export const getSpotCheck = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/lawEnforcement/recommendList?size=" +
      data.pageSize +
      "&current=" +
      data.page,
    data: data,
  });
};

//企业模型控制器-
export const queryRiskUnitInfo = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/companyRiskModel/queryRiskUnitInfo/v1",
    data: data,
  });
};

//企业模型控制器-企业风险详情查询
export const getFindDevice = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/inherent/findDevice/v1",
    data: data,
  });
};

//风险点设备设施固有风险取值下拉框
export const getDeviceInherentIndex = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/deviceInherentIndex/v1",
    data: data,
  });
};

//更新企业风险点设备设施固有危险指数
export const updateDevice = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/inherent/updateDevice/v1",
    data: data,
  });
};

//风险点场所人员暴露指数取值下拉框
export const getExposeIndex = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/exposeIndex/v1",
    data: data,
  });
};

//查询企业风险点危化工艺固有危险指数
export const getFindProcess = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/inherent/findProcess/v1",
    data: data,
  });
};

//更新企业风险点危化工艺固有危险指数
export const updateProcess = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/inherent/updateProcess/v1",
    data: data,
  });
};

//风险点场所人员暴露指数取值下拉框
export const exposeIndexOption = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/exposeIndex/v1",
    data: data,
  });
};

//查询企业风险点人员暴露指数记录
export const exposeFind = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/expose/find/v1",
    data: data,
  });
};

//更新企业风险点人员暴露指数记录
export const exposeUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/expose/update/v1",
    data: data,
  });
};

//根据keywords搜索特定危险化学品指数
export const specificChemicalsIndex = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/specificChemicalsIndex/search/v1",
    data: data,
  });
};

//通用危险化学品指数下拉框
export const generalChemicalsIndex = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/generalChemicalsIndex/v1",
    data: data,
  });
};

//更新企业风险点高风险物品物质列表
export const updateMaterial = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/material/updateMaterial/v1",
    data: data,
  });
};
//
//查询企业风险点高风险物品物质列表
export const findMaterial = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/material/findMaterial/v1",
    data: data,
  });
};

//新增特殊时期活动记录
export const addPeriod = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/specialPeriod/addPeriod/v1",
    data: data,
  });
};

//分页查询特殊时期活动记录
export const findPeriod = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/specialPeriod/findPeriod/page/v1",
    data: data,
  });
};

//根据id删除特殊时期活动记录
export const deletePeriod = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/specialPeriod/deletePeriod/v1",
    data: data,
  });
};

//更新特殊时期活动记录
export const updatePeriod = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/specialPeriod/updatePeriod/v1",
    data: data,
  });
};

//列表查询特殊时期活动记录
export const findPeriodList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/specialPeriod/findPeriod/list/v1",
    data: data,
  });
};

//批量新增高风险作业
export const batchSave = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/particularJob/batchSave/v1",
    data: data,
  });
};

//根据风险单元id查询所有下属设备
export const unitDeviceFindList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/unitDevice/findList/v1",
    data: data,
  });
};

//更新风险单元下属设备和风险点的关联关系
export const modelUpdateDevice = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/unitDevice/updateDevice/v1",
    data: data,
  });
};

//根据风险单元类型riskType查询所属风险点列表
export const riskPointList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/riskPointList/v1",
    data: data,
  });
};

//查询企业风险点高风险作业分页列表
export const particularJobPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/particularJob/page/v1",
    data: data,
  });
};

//查询企业风险点高风险作业指数
export const particularFindJob = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/particularJob/findJob/v1",
    data: data,
  });
};

//查询企业高风险作业列表--安全承诺
export const particularJobList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/particularJob/jobList/v1",
    data: data,
  });
};

//
//查询企业风险点高风险作业分页列表
export const particularPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/particularJob/page/v1",
    data: data,
  });
};

//
//查询企业风险点监测特征修正系数
export const alarmFind = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/alarm/find/v1",
    data: data,
  });
};
//双重预防分析

// 查询 武汉按区划分
export const preventionAnalysisAll = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/doublePreventionAnalysis/all/v1",
    data: data,
  });
};

// 根据区县编码查询
export const preventionAnalysisDistrictCode = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/doublePreventionAnalysis/districtCode/v1",
    data: data,
  });
};

// 分页查询 区下面的全部公司
export const preventionAnalysisPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/doublePreventionAnalysis/page/v1",
    data: data,
  });
};
// 根据id查询
export const preventionAnalysisId = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/doublePreventionAnalysis/id/v1",
    data: data,
  });
};

// 许可证信息
// 条件分页查询
export const licensePage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/permitInformation/page/v1",
    data: data,
  });
};

// 根据id查询
export const licenseId = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/permitInformation/id/v1?id=" + data.id,
    data: data,
  });
};

// 预警工单情况
export const getWarningOrderList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/riskMonitorWorkorder/list/v1",
    data: data,
  });
};
// 预警工单详情
export const getWarningOrderDetail = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/riskMonitorWorkorder/id/v1?id=" + data?.id,
    data: data,
  });
};
// 危险化学品企业情况
export const getDangerChemicalList = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/information/typeCount?distCode=" + data?.distCode,
    data: data,
  });
};
// 统计重点工艺
export const getImportantProcessList = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/knowledge/statistics/regprocess/v2?paraType=" +
      data?.paraType,
    data: data,
  });
};
// 统计重点工艺企业
export const getImportantProcessCompany = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-chemical/api/gemp/knowledge/regprocessDetail/v1?processid=" +
      data?.processid,
    data: data,
  });
};
// 重大危险源情况统计(按区域)
export function getMajorHazardByArea(data) {
  // 接口替换  enterprise/major/danger/queryMajorsByDistrict
  const url =
    "/enterprise/major/danger/queryMajorsByDistrict?size=" +
    data.size +
    "&current=" +
    data.current +
    "&distCode=" +
    data.distCode;
  return axios({
    method: "post",
    url: url,
    data: data,
  });
}
// 重大危险源情况统计(按等级)
export function getMajorHazardByLevel(data) {
  // 接口替换  enterprise/major/danger/queryMajorsByLevel
  const url =
    "/enterprise/major/danger/queryMajorsByDistrict?size=" +
    data.size +
    "&current=" +
    data.current +
    "&distCode=" +
    data.distCode;
  return axios({
    method: "post",
    url: url,
    data: data,
  });
}

/**
 * 获取视频预览地址
 * @param {Object} params 参数对象，包含 channelId, schema, subType
 * @returns {Promise} 返回处理结果的 Promise
 */
export const getVideoPreviewUrl = (data) => {
  return axios({
    method: "get",
    url: "/gemp-chemical/api/gemp/gaode/video/realtime/open/preview/channel",
    params: data,
  });
};
