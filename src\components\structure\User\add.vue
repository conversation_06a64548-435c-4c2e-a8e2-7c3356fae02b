<template>
  <div class="table">
    <el-dialog
      title="新增用户"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      :destroy-on-close="true"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="container">
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="用户名" prop="loginName">
            <el-input v-model.trim="ruleForm.loginName"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              type="password"
              v-model.trim="ruleForm.password"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPsd">
            <el-input
              type="password"
              v-model.trim="ruleForm.confirmPsd"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model.trim="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="six">
            <el-radio v-model="ruleForm.sex" label="1">男</el-radio>
            <el-radio v-model="ruleForm.sex" label="2">女</el-radio>
          </el-form-item>
          <el-form-item label="办公电话" prop="telephone">
            <el-input v-model.trim="ruleForm.telephone"></el-input>
          </el-form-item>
          <el-form-item label="移动电话" prop="cellphone">
            <el-input v-model.trim="ruleForm.cellphone"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="ruleForm.email"></el-input>
          </el-form-item>
          <el-form-item label="用户类型" prop="userType">
            <el-select v-model="ruleForm.userType" placeholder="请选择用户类型">
              <el-option label="超级用户" value="9"></el-option>
              <el-option label="上级用户" value="0"></el-option>
              <el-option label="下级用户" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="所属角色">
            <a-tree
              v-model="checkedKeys"
              checkable
              :tree-data="treeData"
              @select="onSelects"
              @check="onChecks"
            />
          </el-form-item>
        </el-form>
        <el-button
          size="default"
          :loading="submitting"
          type="primary"
          class="commit"
          @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStructureTree,
  validateLoginNames,
  validatePsds,
  addUser,
} from "@/api/user";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
    orgCode: {
      type: String,
    },
  },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        validatePsds({ password: value })
          .then((data) => {
            if (data.data.code == 0) {
              if (this.ruleForm.confirmPsd !== "") {
                this.$refs.ruleForm.validateField("confirmPsd");
              }
              callback();
            } else if (data.data.code == "error") {
              callback(new Error(data.data.msg));
            }
          })
          .catch((e) => {
            console.log(e, "请求错误");
          });
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    var validateLoginName = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入用户名"));
      } else {
        validateLoginNames({ id: "", loginName: value })
          .then((data) => {
            if (data.data.code == 0) {
              callback();
            } else if (data.data.code == "error") {
              callback(new Error(data.data.msg));
            }
          })
          .catch((e) => {
            console.log(e, "请求错误");
          });
      }
    };
    return {
      tableVisible: false,
      submitting: false,
      ruleForm: {
        userType: "",
        sex: "1",
        allTreeData: [],
        treeData: [],
        input: "",
        loginName: "",
        password: "",
        confirmPsd: "",
        name: "",
        cellphone: "",
        telephone: "",
        email: "",
        selectedRoleIds: "",
      },
      checkedKeys: [],
      rules: {
        password: [
          { validator: validatePass, trigger: "blur", required: true },
        ],
        confirmPsd: [
          { validator: validatePass2, trigger: "blur", required: true },
        ],
        name: [{ message: "请输入姓名", trigger: "blur", required: true }],
        loginName: [
          { validator: validateLoginName, trigger: "blur", required: true },
        ],
        userType: [
          { required: true, message: "请选择用户类型", trigger: "change" },
        ],
      },
      allTreeData: [],
      treeData: [],
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
    },
    onSelects(selectedKeys, info) {
      //   console.log('selected', selectedKeys, info);
    },
    onChecks(checkedKeys, info) {
      // console.log(checkedKeys)
      if (checkedKeys) {
        this.ruleForm.selectedRoleIds = checkedKeys.join(",");
      } else {
        this.ruleForm.selectedRoleIds = "";
      }
    },
    getStructureTreeData() {
      getStructureTree()
        .then((data) => {
          if (data.data.code == 0) {
            this.treeData = data.data.data;
            this.treeData.forEach((item) => {
              item.key = item.id;
              item.title = item.systemName;
              // this.allIds.push(item.key)
              if (item.children.length > 0) {
                item.children.forEach((items) => {
                  items.key = items.id;
                  items.title = items.name;
                  if (items.children.length > 0) {
                    items.children.forEach((itemed) => {
                      itemed.key = itemed.id;
                      itemed.title = itemed.name;
                    });
                  }
                });
              }
            });
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },

    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.submitting) return;
          this.submitting = true;
          addUser({
            name: this.ruleForm.name,
            loginName: this.ruleForm.loginName,
            password: this.ruleForm.password,
            sexCode: this.ruleForm.sex,
            orgCode: this.orgCode,
            telephone: this.ruleForm.telephone,
            cellphone: this.ruleForm.cellphone,
            // idNumber
            email: this.ruleForm.email,
            userTypeCode: this.ruleForm.userType,
            // extendstr1
            roleIds: this.ruleForm.selectedRoleIds,
          })
            .then((data) => {
              if (data.data.code == 0) {
                this.submitting = false;
                this.$message({
                  type: "success",
                  message: data.data.msg,
                });
                this.tableVisible = false;
                this.$refs.ruleForm.resetFields();
                this.$emit("add-callback");
              } else {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: data.data.msg,
                });
              }
            })
            .catch((e) => {
              this.submitting = false;
              this.$message({
                type: "info",
                message: "接口报错",
              });
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    /**
     * 点击关闭图标或者遮罩回调
     * @param done
     */
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(() => {
          done();
        })
        .catch(() => {});
    },
    clearTable() {
      this.ruleForm = {};
      this.treeData = [];
      this.submitting = false;
      if (this.$refs["ruleForm"] !== undefined) {
        this.$refs["ruleForm"].resetFields();
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getStructureTreeData();
  },
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    .input {
      width: 60%;
    }
    .el-select,
    .radio-box {
      width: 60%;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .el-input {
    width: 80%;
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
