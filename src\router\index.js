import Vue from "vue";
import Router from "vue-router";

import store from "../store";
//公共部分
import Zhome from "@/components/Zhome";
import login from "@/components/login";
import notFound from "@/components/404";
import Errory from "@/components/error";
import Middler from "@/components/middler";
//安全承诺
// import Home from "@/components/feature/securityFill/Home";
// import securityFill from "@/components/feature/securityFill";
// import enterprise from "@/components/feature/securityFill/enterprise";
// import enterpriseFill from "@/components/feature/securityFill/enterprise/enterpriseFill";
// import enterpriseLog from "@/components/feature/securityFill/enterprise/enterpriseLog";
// import reportedEnterprise from "@/components/feature/securityFill/Home/reportedEnterprise";
// import notReportedEnterprise from "@/components/feature/securityFill/Home/notReportedEnterprise";
//import enterpriseDetail from "@/components/feature/securityFill/Home/enterpriseDetail";

//企业管理
import gardenEnterpriseManagement from "@/components/feature/enterpriseManagement/index.vue";
import entManagement from "@/components/feature/enterpriseManagement/entAndGov.vue";
import parkManagement from "@/components/feature/enterpriseManagement/parkManagement.vue"; //园区管理
import riskManagement from "@/components/feature/enterpriseManagement/riskManagement/riskManagement.vue"; //风险管理
//通讯录
import mailList from "@/components/feature/enterpriseManagement/mailList/index.vue";
import mergencyResources from "@/components/feature/enterpriseManagement/mergencyResources/index.vue";

//超级管理员
import user from "@/components/structure/User";
import structure from "@/components/structure";
import menuManagement from "@/components/structure/menuManagement";
import systemInformation from "@/components/structure/systemInformation";
import passwordRules from "@/components/structure/passwordRules";
import classifiedManagement from "@/components/structure/classifiedManagement";
import role from "@/components/structure/Role";
import auditConfig from "@/components/structure/auditConfig";
import jurisdiction from "@/components/structure/jurisdiction";
import gen from "@/components/structure/gen";
import DataSourceManagement from "@/components/structure/DataSourceManagement";

import messagePushBox from "@/components/messagePushBox";
import messagePush from "@/components/messagePushBox/messagePush";

import setConfiguraBox from "@/components/setConfiguraBox";
import setConfigura from "@/components/setConfiguraBox/setConfigura/setConfigura";

import logConfiguraBox from "@/components/logConfiguraBox";
import logManagement from "@/components/logConfiguraBox/logManagement";
import logUserManagement from "@/components/logConfiguraBox/logUserManagement";
//基础数据管理
import baseConfiguraBox from "@/components/baseConfiguraBox";
import administration from "@/components/baseConfiguraBox/BasicDataManagement/administration";
import organization from "@/components/baseConfiguraBox/BasicDataManagement/organization";
import organizationMange from "@/components/baseConfiguraBox/BasicDataManagement/organizationMange/organizationMange.vue";

//工作台账
import workingAccount from "@/components/feature/workingAccount";
import runningState from "@/components/feature/workingAccount/runningState";
import enterpriseRisk from "@/components/feature/workingAccount/enterpriseRisk";
import safetyCommitment from "@/components/feature/workingAccount/safetyCommitment";
import focuscompany from "@/components/feature/workingAccount/focuscompany";
import alarmAnalysis from "@/components/feature/workingAccount/alarmAnalysis";
import analysisOfMajorHazardSources from "@/components/feature/workingAccount/analysisOfMajorHazardSources";
import doublePrevention from "@/components/feature/workingAccount/doublePrevention/doublePrevention.vue";
import videoReport from "@/components/feature/workingAccount/videoReport/videoReport.vue"; //报警统计分析
import accidentAnalysis from "@/components/feature/workingAccount/accidentAnalysis/accidentAnalysis.vue"; //事故统计分析
import countReport from "@/components/feature/workingAccount/smartReport/countReport/index.vue"; //安全检查
import gatherReport from "@/components/feature/workingAccount/smartReport/gatherReport/index.vue"; //安全检查

//风险研判
import riskAssessment from "@/components/feature/riskAssessment";
import iotMontoringAlarm from "@/components/feature/riskAssessment/iotMontoringAlarm";
import riskEarlyWarningPush from "@/components/feature/riskAssessment/riskEarlyWarningPush";
import videoOnlineMonitoring from "@/components/feature/riskAssessment/videoOnlineMonitoring";
import realtimeMonitor from "@/components/feature/riskAssessment/realtimeMonitor"; //实时物联监测
import realtimeVideo from "@/components/feature/riskAssessment/realtimeVideo";
import powerMonitoring from "@/components/feature/riskAssessment/powerMonitoring/powerMonitoring.vue"; //电力监控
import riskDynamic from "@/components/feature/riskAssessment/riskDynamic/riskDynamic.vue"; //风险动态研判
import monitorWarnReport from "@/components/feature/riskAssessment/monitorWarnReport/monitorWarnReport.vue"; //监测预警报告
import videoAnalysis from "@/components/feature/riskAssessment/videoAnalysis/videoAnalysis.vue"; //视频智能分析
import linkSafetyEducation from "@/components/feature/riskAssessment/linkSafetyEducation/linkSafetyEducation.vue"; //外部链接-安全教育
import linkIndustryTrade from "@/components/feature/riskAssessment/linkIndustryTrade/linkIndustryTrade.vue"; //     制度化管理
import rankLink from "@/components/feature/riskAssessment/rankLink/rankLink.vue"; //
import IOTmonitoring from "@/components/feature/riskAssessment/IOTmonitoring/IOTmonitoring.vue"; //监管端-实时物联监测
import personnelPositioning from "@/components/feature/riskAssessment/personnelPositioning/personnelPositioning.vue"; //人员定位
import accidentManagement from "@/components/feature/riskAssessment/accidentManagement/accidentManagement.vue"; //事故管理
import preventionAnalysis from "@/components/feature/riskAssessment/preventionAnalysis";
import licenseInformation from "@/components/feature/riskAssessment/licenseInformation";
import lawEnforcementData from "@/components/feature/riskAssessment/lawEnforcementData";
// 预警处置
import earlyWarningDisposal from "@/components/feature/earlyWarningDisposal";
import videoQualityMonitoring from "@/components/feature/earlyWarningDisposal/videoQualityMonitoring";
import lightningWarning from "@/components/feature/earlyWarningDisposal/lightningWarning";
import hiddenRisk from "@/components/feature/earlyWarningDisposal/hiddenRisk";
import warningMessage from "@/components/feature/earlyWarningDisposal/warningMessage";

//日常安全管理
import dailySafetyManagement from "@/components/feature/dailySafetyManagement";
import equipmentAbnormalDeclaration from "@/components/feature/dailySafetyManagement/equipmentAbnormalDeclaration";

import enterpEducation from "@/components/feature/dailySafetyManagement/enterpEducation";
import checkOnline from "@/components/feature/dailySafetyManagement/checkOnline";
import patrolOnline from "@/components/feature/dailySafetyManagement/patrolOnline";
import safetyTraining from "@/components/feature/dailySafetyManagement/safetyTraining";
import specialWork from "@/components/feature/dailySafetyManagement/specialWork";
//信息发布
import informationRelease from "@/components/feature/dailySafetyManagement/informationRelease";
//承包商管理
import contractorManagement from "@/components/feature/dailySafetyManagement/contractorManagement";
//三同时资料复查
import threeDataReview from "@/components/feature/dailySafetyManagement/threeDataReview";
import handleProcedures from "@/components/feature/dailySafetyManagement/threeDataReview/handleProcedures.vue";
import pdfSignature from "@/components/feature/dailySafetyManagement/threeDataReview/pdfSignature";
//现场核查与行政检查
import otherInspections from "@/components/feature/dailySafetyManagement/otherInspections";

//工作台
import workbench from "@/components/feature/workbench";
import enterWorkbench from "@/components/feature/workbench/enterWorkbench";
import superviseWorkbench from "@/components/feature/workbench/superviseWorkbench";

import auxiliaryMode from "@/components/feature/auxiliaryMode";
//通知中心
import notification from "@/components/feature/notification";
//
import review from "@/components/h5/review";
import reviewPc from "@/components/h5/reviewPc";
import signH5big from "@/components/h5/signH5big";

// 知识库
import knowLedge from "@/components/feature/knowLedge/index";
import expertSystem from "@/components/feature/knowLedge/main";

// 行政审批

import administrativeApproval from "@/components/feature/administrativeApproval/index";
import TodoList from "@/components/feature/administrativeApproval/todoList";
import historyTodoList from "@/components/feature/administrativeApproval/historyTodoList";

// 企业画像管理
import enterprisePortrait from "@/components/feature/enterprisePortrait/index";
import enterprisePortraitList from "@/components/feature/enterprisePortrait/enterprisePortraitList";
import enterprisePortraitReport from "@/components/feature/enterprisePortrait/enterprisePortraitReport";
import portaitConfiguration from "@/components/feature/enterprisePortrait/portaitConfiguration";

Vue.use(Router);
const routes = [
  {
    path: "/login",
    name: "login",
    component: login,
    /**
     *
     * @param {Object} to 进入到哪个路由去
     * @param {Object} from 从哪个路由离开
     * @param {Function} next 路由的控制参数，常用的有next(true)和next(false)，next({ptah:""})
     */
    beforeEnter: (to, from, next) => {
      if (store.state.login.user.access_token || store.state.login.user.token) {
        // 在已登陆的情况下访问登陆页会重定向到首页
        if (to.path === "/login") {
          // next({ path: "/gardenEnterpriseManagement/entManagement" });
          next({ path: "/gardenEnterpriseManagement/entManagement" });
        } else {
          // next({ path: to.path || "/gardenEnterpriseManagement/entManagement" });
          next({ path: to.path || "/404" });
        }
        next();
      } else {
        // 没有登陆则访问任何页面都重定向到登陆页
        if (to.path === "/login") {
          next();
        } else {
          next(`/login?redirect=${to.path}`);
        }
      }
    },
  },
  {
    path: "/",
    name: "Zhome",
    component: Zhome,
    children: [
      // //安全承诺
      // {
      //   path: "securityFill",
      //   name: "securityFill",
      //   component: securityFill,
      //   children: [
      //     {
      //       path: "Home",
      //       name: "Home",
      //       component: Home,
      //       children: [
      //         {
      //           path: "reportedEnterprise/:id",
      //           name: "reportedEnterprise",
      //           component: reportedEnterprise,
      //         },
      //         {
      //           path: "notReportedEnterprise/:id",
      //           name: "notReportedEnterprise",
      //           component: notReportedEnterprise,
      //         },
      //       ],
      //     },
      //     {
      //       path: "enterprise",
      //       name: "enterprise",
      //       component: enterprise,
      //       children: [
      //         {
      //           path: "enterpriseFill/:id?",
      //           name: "enterpriseFill",
      //           component: enterpriseFill,
      //         },
      //         {
      //           path: "enterpriseLog/:id",
      //           name: "enterpriseLog",
      //           component: enterpriseLog,
      //         },
      //       ],
      //     },
      //   ],
      // },
      //企业管理

      {
        path: "gardenEnterpriseManagement",
        name: "gardenEnterpriseManagement",
        component: gardenEnterpriseManagement,
        children: [
          {
            path: "entManagement",
            name: "entManagement",
            component: entManagement,
          },
          {
            path: "mailList",
            name: "mailList",
            component: mailList,
          },
          {
            path: "mergencyResources",
            name: "mergencyResources",
            component: mergencyResources,
          },
          {
            path: "parkManagement", //园区管理
            name: "parkManagement",
            component: parkManagement,
          },
          //
          {
            path: "riskManagement", //风险管理
            name: "riskManagement",
            component: riskManagement,
          }, //采集报表
          {
            path: "gatherReport",
            name: "gatherReport",
            component: gatherReport,
          },
          {
            path: "portaitConfiguration",
            name: "portaitConfiguration",
            component: portaitConfiguration,
          },
          {
            path: "videoOnlineMonitoring",
            name: "videoOnlineMonitoring",
            // component: resolve => require(['@/components/feature/riskAssessment/videoOnlineMonitoring'],resolve)
            component: videoOnlineMonitoring,
          },
        ],
      },
      //超级管理员
      {
        path: "structure",
        name: "structure",
        component: structure,
        children: [
          {
            path: "menuManagement",
            name: "menuManagement",
            component: menuManagement,
          },
          {
            path: "systemInformation",
            name: "systemInformation",
            component: systemInformation,
          },
          {
            path: "passwordRules",
            name: "passwordRules",
            component: passwordRules,
          },
          {
            path: "classifiedManagement",
            name: "classifiedManagement",
            component: classifiedManagement,
          },
          {
            path: "user",
            name: "user",
            component: user,
          },
          {
            path: "role",
            name: "role",
            component: role,
          },
          {
            path: "jurisdiction",
            name: "jurisdiction",
            component: jurisdiction,
          },
          {
            path: "auditConfig",
            name: "auditConfig",
            component: auditConfig,
          },

          {
            path: "gen",
            name: "gen",
            component: gen,
          },
          {
            path: "DataSourceManagement",
            name: "DataSourceManagement",
            component: DataSourceManagement,
          },
        ],
      },
      //配置管理
      {
        path: "setConfiguraBox",
        name: "setConfiguraBox",
        component: setConfiguraBox,
        children: [
          //
          {
            path: "setConfigura",
            name: "setConfigura",
            component: setConfigura,
          },
        ],
      },
      //日志
      {
        path: "logConfiguraBox",
        name: "logConfiguraBox",
        component: logConfiguraBox,
        children: [
          {
            path: "logManagement",
            name: "logManagement",
            component: logManagement,
          },
          {
            path: "logUserManagement",
            name: "logUserManagement",
            component: logUserManagement,
          },
        ],
      },

      //基础数据管理
      {
        path: "baseConfiguraBox",
        name: "baseConfiguraBox",
        component: baseConfiguraBox,
        children: [
          {
            path: "administration",
            name: "administration",
            component: administration,
          },
          {
            path: "organization",
            name: "organization",
            component: organization,
          },
          {
            path: "organizationMange",
            name: "organizationMange",
            component: organizationMange,
          },
        ],
      },
      {
        path: "knowledgeManage",
        name: "knowledgeManage",
        component: knowLedge,
        children: [
          {
            path: "expertSystem",
            name: "expertSystem",
            component: expertSystem,
          },
        ],
      },
      {
        path: "administrativeApproval",
        name: "administrativeApproval",
        component: administrativeApproval,
        children: [
          {
            path: "todoList",
            name: "todoList",
            component: TodoList,
          },
          {
            path: "historyTodoList",
            name: "historyTodoList",
            component: historyTodoList,
          },
        ],
      },
      //

      //messagePushBox
      {
        path: "messagePushBox",
        name: "messagePushBox",
        component: messagePushBox,
        children: [
          {
            path: "messagePush",
            name: "messagePush",
            component: messagePush,
          },
        ],
      },

      //工作台账
      {
        path: "workingAccount",
        name: "workingAccount",
        // component: resolve => require(['@/components/feature/workingAccount'],resolve),
        component: workingAccount,
        children: [
          {
            path: "runningState",
            name: "runningState",
            // component: resolve => require(['@/components/feature/workingAccount/runningState'],resolve),
            component: runningState,
          },
          {
            path: "enterpriseRisk",
            name: "enterpriseRisk",
            // component: resolve => require(['@/components/feature/workingAccount/enterpriseRisk'],resolve),
            component: enterpriseRisk,
          },
          {
            path: "focuscompany",
            name: "focuscompany",
            // component: resolve => require(['@/components/feature/workingAccount/focuscompany'],resolve),
            component: focuscompany,
          },
          {
            path: "alarmAnalysis",
            name: "alarmAnalysis",
            // component: resolve => require(['@/components/feature/workingAccount/alarmAnalysis'],resolve),
            component: alarmAnalysis,
          },
          {
            path: "analysisOfMajorHazardSources",
            name: "analysisOfMajorHazardSources",
            // component: resolve => require(['@/components/feature/workingAccount/analysisOfMajorHazardSources'],resolve),
            component: analysisOfMajorHazardSources,
          },
          {
            path: "doublePrevention",
            name: "doublePrevention",
            component: doublePrevention,
          },
          //视频智能分析报警统计
          {
            path: "videoReport",
            name: "videoReport",
            component: videoReport,
          },
          //事故统计分析
          {
            path: "accidentAnalysis",
            name: "accidentAnalysis",
            component: accidentAnalysis,
          },
        ],
      },
      //风险研判
      {
        path: "riskAssessment",
        name: "riskAssessment",
        // component: resolve => require(['@/components/feature/riskAssessment'],resolve),
        component: riskAssessment,
        children: [
          {
            path: "videoAnalysis",
            name: "videoAnalysis",
            component: videoAnalysis,
          },
          //realtimeMonitor
          {
            path: "realtimeMonitor",
            name: "realtimeMonitor",
            // component: resolve => require(['@/components/feature/riskAssessment/videoOnlineMonitoring'],resolve)
            component: realtimeMonitor,
          },
          {
            path: "realtimeVideo",
            name: "realtimeVideo",
            // component: resolve => require(['@/components/feature/riskAssessment/videoOnlineMonitoring'],resolve)
            component: realtimeVideo,
          },

          {
            path: "powerMonitoring",
            name: "powerMonitoring",
            component: powerMonitoring,
          },
          {
            path: "riskDynamic",
            name: "riskDynamic",
            component: riskDynamic,
          },
          //监测预警报告
          {
            path: "monitorWarnReport",
            name: "monitorWarnReport",
            component: monitorWarnReport,
          },

          //安全教育
          {
            path: "linkSafetyEducation",
            name: "linkSafetyEducation",
            component: linkSafetyEducation,
          },
          {
            path: "linkIndustryTrade",
            name: "linkIndustryTrade",
            component: linkIndustryTrade,
          },
          //rankLink
          {
            path: "rankLink",
            name: "rankLink",
            component: rankLink,
          },
          //监管端-实时物联监测
          {
            path: "IOTmonitoring",
            name: "IOTmonitoring",
            component: IOTmonitoring,
          },
          //人员定位
          {
            path: "personnelPositioning",
            name: "personnelPositioning",
            component: personnelPositioning,
          },
          //事故管理
          {
            path: "accidentManagement",
            name: "accidentManagement",
            component: accidentManagement,
          },
          // 双重预防分析
          {
            path: "preventionAnalysis",
            name: "preventionAnalysis",
            component: preventionAnalysis,
          },
          // 许可证信息分析
          {
            path: "licenseInformation",
            name: "licenseInformation",
            component: licenseInformation,
          },
          {
            path: "safetyCommitment",
            name: "safetyCommitment",
            // component: resolve => require(['@/components/feature/workingAccount/safetyCommitment'],resolve),
            component: safetyCommitment,
          },
          {
            path: "specialWork",
            name: "specialWork",
            component: specialWork,
          },
        ],
      },
      {
        path: "earlyWarningDisposal",
        name: "earlyWarningDisposal",
        component: earlyWarningDisposal,
        children: [
          //视频智能分析
          {
            path: "videoAnalysis",
            name: "videoAnalysis",
            component: videoAnalysis,
          },
          {
            path: "iotMontoringAlarm",
            name: "iotMontoringAlarm",
            // component: resolve => require(['@/components/feature/riskAssessment/iotMontoringAlarm'],resolve)
            component: iotMontoringAlarm,
          },
          {
            path: "videoQualityMonitoring",
            name: "videoQualityMonitoring",
            component: videoQualityMonitoring,
          },
          {
            path: "riskEarlyWarningPush",
            name: "riskEarlyWarningPush",
            // component: resolve => require(['@/components/feature/riskAssessment/riskEarlyWarningPush'],resolve)
            component: riskEarlyWarningPush,
          },
          {
            path: "hiddenRisk",
            name: "hiddenRisk",
            component: hiddenRisk,
          },
          {
            path: "warningMessage",
            name: "warningMessage",
            component: warningMessage,
          },
        ],
      },
      {
        path: "auxiliaryMode",
        name: "auxiliaryMode",
        component: auxiliaryMode,
        children: [
          {
            path: "lawEnforcementData",
            name: "lawEnforcementData",
            component: lawEnforcementData,
          },
          {
            path: "accidentManagement",
            name: "accidentManagement",
            component: accidentManagement,
          },
          {
            path: "countReport",
            name: "countReport",
            component: countReport,
          },
          {
            path: "enterpEducation",
            name: "enterpEducation",
            component: enterpEducation,
          },
        ],
      },
      //日常安全管理
      {
        path: "dailySafetyManagement",
        name: "dailySafetyManagement",
        component: dailySafetyManagement,
        children: [
          {
            path: "iotMontoringAlarm",
            name: "iotMontoringAlarm",
            component: iotMontoringAlarm,
          },
          {
            path: "lightningWarning",
            name: "lightningWarning",
            component: lightningWarning,
          },
          {
            path: "equipmentAbnormalDeclaration",
            name: "equipmentAbnormalDeclaration",
            component: equipmentAbnormalDeclaration,
          },
          {
            path: "checkOnline",
            name: "checkOnline",
            component: checkOnline,
          },
          {
            path: "patrolOnline",
            name: "patrolOnline",
            component: patrolOnline,
          },
          {
            path: "safetyTraining",
            name: "safetyTraining",
            component: safetyTraining,
          },
          {
            path: "informationRelease",
            name: "informationRelease",
            component: informationRelease,
          },
          {
            path: "contractorManagement",
            name: "contractorManagement",
            component: contractorManagement,
          },
          {
            path: "threeDataReview",
            name: "threeDataReview",
            component: threeDataReview,
          },
          //handleProcedures
          {
            path: "handleProcedures",
            name: "handleProcedures",
            component: handleProcedures,
          },
          {
            path: "pdfSignature",
            name: "pdfSignature",
            component: pdfSignature,
          },
          //
          {
            path: "otherInspections",
            name: "otherInspections",
            component: otherInspections,
          },
        ],
      },
      //工作台
      {
        path: "workbench",
        name: "workbench",
        component: workbench,
        // meta: {
        //   keepAlive: false, // 需要缓存
        // },
        children: [
          {
            path: "enterWorkbench",
            name: "enterWorkbench",
            component: enterWorkbench,
            // meta: {
            //   keepAlive: false, // 需要缓存
            // },
          },
          {
            path: "superviseWorkbench",
            name: "superviseWorkbench",
            component: superviseWorkbench,
            // meta: {
            //   keepAlive: false, // 需要缓存
            // },
          },
        ],
      },
      {
        path: "notification",
        name: "notification",
        component: notification,
      },
      // 企业画像管理
      {
        path: "enterprisePortrait",
        name: "enterprisePortrait",
        component: enterprisePortrait,
        children: [
          {
            path: "enterprisePortraitList",
            name: "enterprisePortraitList",
            component: enterprisePortraitList,
          },
          {
            path: "enterprisePortraitReport",
            name: "enterprisePortraitReport",
            component: enterprisePortraitReport,
          }, //统计报表
        ],
      },
    ],
  },
  {
    path: "/middler",
    name: "middler",
    component: Middler,
  },
  {
    path: "/404",
    name: "notFound",
    component: notFound,
  },
  {
    path: "/error",
    name: "Errory",
    component: Errory,
  },
  {
    path: "*",
    redirect: "/404",
  },
  {
    path: "/review",
    name: "Review",
    component: review,
  },
  {
    path: "/reviewPc",
    name: "ReviewPc",
    component: reviewPc,
  },
  //signH5big
  {
    path: "/signH5big",
    name: "signH5big",
    component: signH5big,
  },
  {
    path: "/reportH5",
    name: "reportH5",
    component: () =>
      import(
        "@/components/feature/workingAccount/smartReport/gatherReport/reportH5"
      ),
  },

  {
    path: "/workingAccount/gatherReport",
    component: () =>
      import(
        "@/components/feature/workingAccount/smartReport/gatherReport/index"
      ),
    name: "GatherReport",
    meta: { title: "采集报表" },
  },
];
const originalPush = Router.prototype.push;
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
const router = new Router({
  mode: "history",
  routes,
});

// 路由守卫：检查URL参数中的辅助监管模式
router.beforeEach((to, from, next) => {
  // 检查URL参数中是否有auxiliaryMode标识
  if (to.query.auxiliaryMode === "true") {
    console.log("URL中检测到辅助监管模式参数");

    // 设置辅助监管模式
    store.commit("controler/SET_AUXILIARY_MODE", true);

    // 菜单过滤现在在Zhome.vue的getAside方法中处理
  }

  next();
});

export default router;
