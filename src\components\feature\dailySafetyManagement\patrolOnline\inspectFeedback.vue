<template>
  <div class="inspectFeedback">
    <div class="header">
      <el-date-picker
        v-model="value1"
        size="mini"
        type="daterange"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="searchTime"
        unlink-panels
        style="width: 370px"
        clearable
        :picker-options="{
          disabledDate: (time) => {
            return (
              time.getTime() >
              new Date(new Date().Format('yyyy-MM-dd 23:59:59')).getTime()
            );
          },
        }"
      ></el-date-picker>
      <el-select
        v-model="inspectStatus"
        placeholder="请选择状态"
        size="mini"
        style="width: 190px"
        clearable
        v-show="this.$store.state.login.user.user_type == 'gov'"
      >
        <el-option
          v-for="(item, index) in optionses"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select>

      <el-cascader
        size="mini"
        placeholder="请选择接受巡查的地市/区县"
        :options="districts"
        v-model="distCode"
        :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
        clearable
        @change="handleChange"
        ref="myCascader"
        v-if="user.distRole == '1' || user.distRole == '2'"
        :show-all-levels="true"
        style="width: 190px; margin-top: 10px"
      ></el-cascader>
      <el-button
        type="primary"
        style="margin-top: 10px"
        size="mini"
        @click="getList()"
        >查询</el-button
      >
    </div>
    <div class="container">
      <!-- <div class="title">政府巡查反馈</div> -->
      <div>
        <el-table
          :data="tableData"
          border
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          v-loading="loading"
        >
          <!-- <el-table-column type="selection"></el-table-column> -->
          <el-table-column type="index" label="序号" width="60" fixed="left">
          </el-table-column>
          <el-table-column
            prop="areaName"
            label="区划"
            width="100"
            fixed="left"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column label="系统在线情况">
            <el-table-column prop="onlineRate" label="在线率" width="75">
              <template slot-scope="{ row }"
                >{{ row.onlineRate | formatRate }}
              </template>
            </el-table-column>
            <el-table-column prop="onlineSort" label="排名" width="60">
            </el-table-column>
          </el-table-column>
          <el-table-column label="视频监控在线情况">
            <el-table-column prop="videoOnlineRate" label="在线率" width="75">
              <template slot-scope="{ row }"
                >{{ row.videoOnlineRate | formatRate }}
              </template>
            </el-table-column>
            <el-table-column prop="videoOnlineSort" label="排名" width="60">
            </el-table-column>
          </el-table-column>
          <el-table-column label="安全承诺情况">
            <el-table-column prop="promiseRate" label="承诺率" width="75">
              <template slot-scope="{ row }"
                >{{ row.promiseRate | formatRate }}
              </template>
            </el-table-column>
            <el-table-column prop="promiseSort" label="排名" width="60">
            </el-table-column>
          </el-table-column>
          <el-table-column label="超24小时未消警指标数">
            <el-table-column
              prop="latestAlarmTargetCount"
              label="数量"
              width="70"
            >
            </el-table-column>
            <el-table-column
              prop="latestAlarmTargetInterval"
              label="最大持续时长"
              width="100"
              show-overflow-tooltip
            >
            </el-table-column>
          </el-table-column>
          <el-table-column label="预警及通报处置情况">
            <el-table-column prop="warnCount" label="预警未消警" width="100">
            </el-table-column>
            <el-table-column
              prop="warnNoRespCount"
              label="预警未反馈"
              width="100"
            >
            </el-table-column>
            <el-table-column
              prop="warnNoticeNoRespCount"
              label="通报未反馈"
              width="100"
            >
            </el-table-column>
          </el-table-column>
          <el-table-column
            prop="otherContent"
            label="其他情况"
            width="150"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="createBy"
            label="巡查人"
            width="120"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column prop="inspectDate" label="巡查时间" width="170">
          </el-table-column>
          <el-table-column
            prop="inspectStatus"
            label="反馈状态"
            width="90"
            show-overflow-tooltip
          >
            <template slot-scope="{ row }">
              <!-- 1已生成2已下发未反馈3已反馈 -->
              <span v-if="row.inspectStatus == 1">未下发</span>
              <span v-if="row.inspectStatus == 2">未反馈</span>
              <span v-if="row.inspectStatus == 3">已反馈</span>
            </template>
          </el-table-column>
          <el-table-column prop="feedbackTime" label="反馈时间" width="170">
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
            width="210"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                v-if="
                  scope.row.inspectStatus == 1 ||
                  (scope.row.inspectStatus == 2 &&
                    $store.state.login.user.isDanger == '1')
                "
                @click="addSpotCheck(scope.row)"
                >反馈</el-button
              >
              <el-button
                type="text"
                v-if="scope.row.inspectStatus == 3"
                @click="SpotCheckDetails(scope.row)"
                >反馈详情</el-button
              >
              <el-button type="text" @click="addSpotCheckDetails(scope.row)"
                >查看报告</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="dialog">
      <el-dialog
        title="反馈详情"
        :visible.sync="showFeedBackDetails"
        width="900px"
        :close-on-click-modal="false"
        v-dialog-drag
        @close="activeNameFeedBack === '0'"
      >
        <el-tabs v-model="activeNameFeedBack">
          <!-- <el-tab-pane label="安全承诺情况" name="2" disabled></el-tab-pane> -->
          <el-tab-pane label="系统在线情况" name="0"></el-tab-pane>
          <el-tab-pane label="视频在线情况" name="1"></el-tab-pane>
          <el-tab-pane label="安全承诺情况" name="2"></el-tab-pane>
          <el-tab-pane label="未消警情况" name="3"></el-tab-pane>
          <el-tab-pane label="预警处置及督办情况" name="4"></el-tab-pane>
          <el-tab-pane label="其他情况" name="5"></el-tab-pane>
        </el-tabs>
        <el-descriptions :column="1" border :labelStyle="labelStyle">
          <el-descriptions-item>
            <template slot="label"> 情况说明 </template>
            <div class="feekBackItem" v-if="activeNameFeedBack == 5">
              {{ feedBackDetails[5].situation }}
            </div>
            <div class="feekBackItem" v-else>
              {{ feedBackDetails[activeNameFeedBack].situation }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 处置措施 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeNameFeedBack].measure }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 补充信息 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeNameFeedBack].otherInfo }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 预计完成时间 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeNameFeedBack].planDate }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期说明 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeNameFeedBack].delayInfo }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期完成时间 </template>
            <div class="feekBackItem">
              {{ feedBackDetails[activeNameFeedBack].delayDate }}
            </div>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 完成状态 </template>
            <div class="feekBackItem">
              {{
                feedBackDetails[activeNameFeedBack].isFinish == "0"
                  ? "未完成"
                  : "已完成"
              }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-dialog>
      <el-dialog
        title="监管反馈"
        :visible.sync="showSpotCheck"
        width="900px"
        v-dialog-drag
        top="5vh"
        :close-on-click-modal="false"
        @close="activeName === '0'"
      >
        <el-tabs v-model="activeName">
          <el-tab-pane label="系统在线情况" name="0" disabled></el-tab-pane>
          <el-tab-pane label="视频在线情况" name="1" disabled></el-tab-pane>

          <el-tab-pane label="安全承诺情况" name="2" disabled></el-tab-pane>

          <el-tab-pane label="未消警情况" name="3" disabled></el-tab-pane>

          <el-tab-pane
            label="预警处置及督办情况"
            name="4"
            disabled
          ></el-tab-pane>
          <el-tab-pane label="其他情况" name="5" disabled></el-tab-pane>
        </el-tabs>
        <el-form
          class="dialogForm"
          :model="enterpriseFeedback[activeName]"
          :class="['enterpriseFeedback' + activeName]"
          :ref="'enterpriseFeedback' + activeName"
          :key="activeName"
        >
          <el-descriptions :column="1" border :labelStyle="labelStyle">
            <!-- 因其他情况的输入框从列表中拿到的数据，所以在这里会无法显示正确数据。因此我在这里做了一个判断保证一定有数据 -->
            <el-descriptions-item v-if="activeName !== '5'">
              <template slot="label">
                <span v-show="activeName !== '5'" style="color: red">*</span>
                情况说明
              </template>
              <el-form-item
                :rules="[
                  {
                    required: activeName !== '5' ? true : false,
                    message: '请输入情况说明',
                    trigger: ['change'],
                  },
                ]"
                prop="situation"
              >
                <el-input
                  type="textarea"
                  placeholder="请输入情况说明"
                  maxlength="500"
                  show-word-limit
                  :autosize="{ minRows: 3, maxRows: 4 }"
                  resize="none"
                  clearabl
                  v-model.trim="enterpriseFeedback[activeName].situation"
                >
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item v-else>
              <template slot="label">
                <span v-show="activeName !== '5'" style="color: red">*</span>
                情况说明
              </template>
              <el-form-item
                :rules="[
                  {
                    required: activeName !== '5' ? true : false,
                    message: '请输入情况说明',
                    trigger: ['change'],
                  },
                ]"
                prop="situation"
              >
                <el-input
                  type="textarea"
                  placeholder="请输入情况说明"
                  maxlength="500"
                  show-word-limit
                  :autosize="{ minRows: 3, maxRows: 4 }"
                  resize="none"
                  clearabl
                  v-model.trim="enterpriseFeedback[5].situation"
                >
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span v-show="activeName !== '5'" style="color: red">*</span>
                处置措施
              </template>
              <el-form-item
                :rules="[
                  {
                    required: activeName !== '5' ? true : false,
                    message: '请输入处置措施',
                    trigger: ['change'],
                  },
                ]"
                prop="measure"
              >
                <el-input
                  type="textarea"
                  placeholder="请输入处置措施"
                  maxlength="500"
                  show-word-limit
                  :autosize="{ minRows: 3, maxRows: 4 }"
                  resize="none"
                  clearable
                  v-model.trim="enterpriseFeedback[activeName].measure"
                >
                </el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 补充信息 </template>
              <el-input
                type="textarea"
                placeholder="请输入补充信息"
                maxlength="500"
                show-word-limit
                :autosize="{ minRows: 3, maxRows: 4 }"
                resize="none"
                clearable
                v-model.trim="enterpriseFeedback[activeName].otherInfo"
              >
              </el-input>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span v-show="activeName !== '5'" style="color: red">*</span>
                预计完成时间
              </template>
              <el-form-item
                label=""
                :rules="[
                  {
                    required: activeName !== '5' ? true : false,
                    message: '请选择预计完成时间',
                    trigger: ['change'],
                  },
                ]"
                prop="planDate"
              >
                <el-date-picker
                  type="datetime"
                  v-model="enterpriseFeedback[activeName].planDate"
                  placeholder="选择预计完成时间"
                  clearable
                  value-format="timestamp"
                >
                </el-date-picker>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 延期说明 </template>
              <el-input
                type="textarea"
                placeholder="最多可输入500字"
                maxlength="500"
                show-word-limit
                :autosize="{ minRows: 3, maxRows: 4 }"
                resize="none"
                clearable
                v-model.trim="enterpriseFeedback[activeName].delayInfo"
              >
              </el-input>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"> 延期完成时间 </template>
              <!-- <el-form-item
                label=""
                :rules="[
                  {
                    required: activeName !== '5' ? true : false,
                    message: '请选择延期完成时间',
                    trigger: ['change'],
                  },
                ]"
                prop="delayDate"
              > -->
              <el-date-picker
                type="datetime"
                v-model="enterpriseFeedback[activeName].delayDate"
                placeholder="选择延期完成时间"
                clearable
                value-format="timestamp"
                :picker-options="pickerOptionsDelayDate"
              >
              </el-date-picker>
              <!-- </el-form-item> -->
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span style="color: red">*</span>
                完成状态
              </template>
              <el-form-item
                :rules="[
                  {
                    required:true,
                    message: '请选择完成状态',
                    trigger: ['change', 'blur'],
                  },
                ]"
                prop="isFinish"
              >
                <el-select
                  v-model="enterpriseFeedback[activeName].isFinish"
                  clearable
                  placeholder="请选择完成状态"
                >
                  <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
          <div class="btnList">
            <el-button
              size="small"
              plain
              v-if="activeName !== '0'"
              @click="subtractionActiveName()"
              >上一步</el-button
            >
            <el-button
              type="primary"
              size="small"
              v-if="activeName !== '5'"
              @click="addActiveName()"
              >下一步</el-button
            >
            <el-button
              type="primary"
              size="small"
              v-if="activeName === '5'"
              @click="submit()"
              >提交</el-button
            >
          </div>
        </el-form>
      </el-dialog>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        :disabled="loading"
      >
      </el-pagination>
    </div>
    <!-- 查看报告 -->
    <div class="model" v-if="showSpotCheckDetails">
      <div class="box" v-loading="reportLoading">
        <a-icon type="close" class="close" @click="handleOff()" />
        <div class="title">巡查报告</div>
        <div class="code">编号：{{ report.reportCode }}</div>
        <div class="content">
          <div class="dear">{{ report.inspectDepartment }}</div>

          <div class="generalSituation">
            <div>
              {{
                report.inspectTime
              }}巡查你单位监测预警系统运行和使用情况，请核查以下情况并将核查处置情况及时上报。
            </div>
            <div>
              1.系统在线情况：系统在线率{{ report.onlineRate }}%，排名第{{
                report.onlineSort
              }}位。
            </div>
            <div>
              2.视频在线及预览情况：视频在线率{{
                report.videoOnlineRate
              }}%，排名第{{ report.videoOnlineSort }}位。
            </div>
            <div>
              3.安全承诺情况：承诺率{{ report.promiseRate }}%，排名第{{
                report.promiseSort
              }}位。
            </div>
            <div>
              4.指标未销警情况：超过24小时未销警的指标{{
                report.latestAlarmTargetCount
              }}条，最长未销警时长{{ report.latestAlarmTargetInterval }}。
            </div>
            <div>
              5.预警和督办处置情况：未消除预警的预警信息{{
                report.warnCount
              }}条；未反馈的预警信息{{
                report.warnNoRespCount
              }}条；未反馈的警示通报{{ report.warnNoticeNoRespCount }}条。
            </div>
            <div>其他情况：{{ report.otherContent }}</div>
          </div>
        </div>
        <div class="footer">
          <div>{{ report.genAreaName }}</div>
          <div>联系人：{{ report.contactor }}</div>
          <div>{{ report.inspectDate }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  postOnlinePatrolReplyList,
  postOnlinePatrolReply,
  postOnlinePatrolViewReportDetails,
  postOnlinePatrolQueryFeedbackDetails,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { createNamespacedHelpers, mapState } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
// const { mapState: mapStateControler } = createNamespacedHelpers('controler')
export default {
  name: "inspectFeedback",
  data() {
    return {
      value1: [
        new Date(new Date().getTime() - 3600 * 1000 * 24 * 30).Format(
          "yy-MM-dd"
        ),
        new Date().Format("yy-MM-dd"),
      ],
      districts: [],
      distCode: "",
      startTime: "",
      endTime: "",
      showSpotCheck: false,
      showSpotCheckDetails: false,
      showFeedBackDetails: false,
      checkList: [],
      activeName: "0",
      activeNameFeedBack: "0",
      labelStyle: {
        textAlign: "center",
        backgroundColor: "rgb(242, 246, 255)",
      },
      enterpriseFeedback: [
        {
          inspectId: "",
          replyType: "1", //1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
        },
        {
          inspectId: "",
          replyType: "2", //1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
        },
        {
          inspectId: "",
          replyType: "3", //1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
        },
        {
          inspectId: "",
          replyType: "4", //1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
        },
        {
          inspectId: "",
          replyType: "5", //1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
        },
        {
          inspectId: "",
          replyType: "6", //1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
        },
      ],
      tableData: [],
      options: [
        {
          value: "0",
          label: "未完成",
        },
        {
          value: "1",
          label: "已完成",
        },
      ],
      optionses: [
        {
          value: "2",
          label: "未反馈",
        },
        {
          value: "3",
          label: "已反馈",
        },
      ],
      value: "",
      inspectStatus: "",
      total: 0,
      currentPage: 1,
      otherContent: "",
      // 开始起始点禁用日期筛选
      pickerOptionsDelayDate: {
        disabledDate: (time) => {
          return this.dealDisabledDate(time);
        },
      },
      report: {},
      reportLoading: false,
      loading: false,
      feedBackDetails: [
        {
          inspectId: "", //巡查记录ID
          replyType: "1", //巡查记录反馈的类型	1系统在线情况；2视频在线及预览情况；3安全承诺情况；4未销警情况；5预警处置及督办情况；6其他情况
          situation: "", //情况说明
          measure: "", //处置措施
          otherInfo: "", //补充信息
          delayInfo: "", //延期说明
          delayDate: "", //延期日期
          planDate: "", //计划完成日期
          isFinish: "", //是否已完成  0:未完成 1：已完成
          status: "",
        },
      ],
    };
  },
  filters: {
    formatRate: function (value) {
      return value.split(".")[0] + "%";
    },
  },
  methods: {
    SpotCheckDetails(row) {
      this.showFeedBackDetails = true;
      postOnlinePatrolQueryFeedbackDetails({ respectId: row.id }).then(
        (res) => {
          if (res.data.code === 0) {
            this.feedBackDetails = res.data.data;
          }
        }
      );
    },
    // 预计完成时间大于延期完成时间
    dealDisabledDate(time) {
      return time.getTime() < this.enterpriseFeedback[this.activeName].planDate;
    },
    searchTime(value) {
      console.log(value);
      // if (value) {
      //   console.log(value);
      //   let date1 = new Date(value[0]);
      //   // let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
      //   let date2 = new Date(value[1]);
      //   // let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
      //   this.startTime = date1;
      //   this.endTime = date2;
      // } else {
      //   this.value1 = [];
      //   this.startTime = "";
      //   this.endTime = "";
      // }
    },
    addSpotCheck(row) {
      this.showSpotCheck = true;
      // this.otherContent = row.otherContent;
      for (let i = 0; i < this.enterpriseFeedback.length; i++) {
        this.enterpriseFeedback[i].inspectId = row.id;
        this.enterpriseFeedback[i].flag = row.flag;
        // if(i === 5){
        this.enterpriseFeedback[5].situation = row.otherContent;
        // }
      }
      // console.log(this.enterpriseFeedback);
    },
    addSpotCheckDetails(row) {
      this.showSpotCheckDetails = true;
      this.reportLoading = true;
      postOnlinePatrolViewReportDetails({
        inspectId: row.id,
        flag: row.flag,
      }).then((res) => {
        this.report = res.data.data;
        this.reportLoading = false;
      });
    },
    handleOff() {
      this.showSpotCheckDetails = false;
    },
    //
    handleCurrentChange() {
      this.getList();
    },
    //提交反馈
    submit() {
     this.$nextTick(() => {
        this.$refs["enterpriseFeedback" + this.activeName].validate((valid) => {
          if (valid) {
             postOnlinePatrolReply(this.enterpriseFeedback).then(res => {
            if (res.data.code === 0) {
              this.$message.success('反馈成功')
              this.showSpotCheck = false
              this.activeName = '0'
              this.getList()
            } else {
              this.$message.success(res.data.msg)
            }
          })
          } else {
            return false;
          }
        });
      });
      console.log(this.enterpriseFeedback, "this.enterpriseFeedback");
     
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      } else {
        this.distCode = this.$store.state.login.userDistCode;
      }
    },
    //上一步
    subtractionActiveName() {
      this.activeName = String(this.activeName - 1);
    },
    //点击下一步时校验当前必填项
    addActiveName() {
      this.$nextTick(() => {
        this.$refs["enterpriseFeedback" + this.activeName].validate((valid) => {
          if (valid) {
            this.activeName = String(this.activeName * 1 + 1);
          } else {
            return false;
          }
        });
      });
    },
    //获取列表数据
    getList() {
      this.loading = true;
      let params = {};
      if (this.user.distRole == "0") {
        params = {
          areaCode: this.$store.state.login.userDistCode,
          // inspectAreaCode: this.distCode || '',
          startTime: this.value1[0] || null,
          endTime: this.value1[1] || null,
          replyStatus: this.inspectStatus,
          nowPage: this.currentPage,
          pageSize: 10,
        };
      } else {
        params = {
          areaCode: this.distCode || this.$store.state.login.userDistCode,
          // areaCode: 420800,
          // inspectAreaCode:this.distCode || '',
          startTime: this.value1[0] || null,
          endTime: this.value1[1] || null,
          replyStatus: this.inspectStatus,
          nowPage: this.currentPage,
          pageSize: 10,
        };
      }
      postOnlinePatrolReplyList(params).then((res) => {
        this.tableData = res.data.data.content;
        this.total = res.data.data.totalElements;
        this.loading = false;
      });
      getDistrictUser().then((res) => {
        this.districts = [];
        this.districts.push(res.data.data);
        // if (this.user.distRole == '0') {
        //   this.districts.forEach(item => {
        //     if (item.children && item.children.length > 0) {
        //       item.children.forEach(items => {
        //         if (items.children && items.children.length > 0) {
        //           items.children = null
        //         }
        //       })
        //     }
        //   })
        // } else
        if (this.user.distRole == "0") {
          this.districts.forEach((item) => {
            if (item.children && item.children.length > 0) {
              item.children = null;
            }
          });
        }
      });
    },
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapState({
      user: (state) => state.login.user,
    }),
    // ...mapStateControler({
    //   vuexDistrict: state => state.district
    // })
  },
  // watch: {
  //   vuexDistrict: {
  //     handler(newVal, oldVal) {
  //       this.district = newVal
  //     }
  //   }
  // }
};
</script>

<style lang="scss" scoped>
/deep/ .el-select__tags-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
  display: block;
}
/deep/ .el-dialog__body {
  padding-top: 10px;
}
/deep/ .el-form-item {
  margin-bottom: 8px;
  margin-top: 5px;
}
// /deep/ .el-table__body-wrapper{
//       max-height:45vh;
//     overflow-y: auto;
// }
// /deep/ .el-table__fixed-body-wrapper{
//   max-height: 45vh;
//     overflow-y: auto;
//   overflow-x: overlay;
// }
.inspectFeedback {
  .header {
    margin-bottom: 20px;
    & > * {
      margin-right: 20px;
    }
  }
  .container {
    .title {
      font-size: 18px;
      width: 100%;
      text-align: left;
      padding-bottom: 10px;
      font-weight: 900;
    }
  }
  .dialogForm {
    height: 65vh;
    overflow: auto;
    .btnList {
      display: flex;
      justify-content: flex-end;
      margin-top: 15px;
      padding-bottom: 20px;
      & > * {
        margin-right: 3px;
        margin-left: 20px;
      }
    }
  }
  .pagination {
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
  .model {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100000;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    & > .box {
      width: 724px;
      // height: 515px;
      padding-bottom: 20px;
      position: relative;
      border-radius: 5px;
      background: #fff;
      // background-image: url("../../../../../static/img/SpotCheckDialogBg.png");
      // background-size: 100% 100%;
      // background-repeat: no-repeat;
      .close {
        position: absolute;
        right: 12px;
        top: 12px;
        z-index: 10000;
        font-size: 24px;
      }
      .title {
        width: 100%;
        text-align: center;
        font-size: 36px;
        color: red;
        margin-top: 20px;
        font-weight: 900;
      }
      .code {
        font-size: 22px;
        width: 100%;
        text-align: center;
        margin-top: 5px;
      }

      .content {
        width: 80%;
        margin-left: 50%;
        transform: translateX(-50%);
        margin-top: 20px;
        color: #000;
        font-size: 18px;
        .dear {
          height: 40px;
        }
        .generalSituation {
          text-indent: 2em;
          & > * {
            line-height: 35px;
          }
        }
      }
      .footer {
        margin-top: 10px;
        & > * {
          margin-left: 50%;
          transform: translateX(-50%);
          width: 80%;
          text-align: right;
          font-size: 16px;
        }
      }
    }
  }
  .feekBackItem {
    // width: 250px;
    // height: 30px;
    max-width: 570px;
    line-height: 30px;
  }
}
</style>
