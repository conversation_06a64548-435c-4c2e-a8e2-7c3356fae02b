import axios from "axios";
import qs from "qs";

// 企业安全承诺重大危险源列表
export const getSecurityCommitmentList = (data) => {
  return axios({
    method: "post",
    url: "/hg/dybusi/list.mvc",
    data: qs.stringify(data),
  });
};
// 企业安全承诺重大危险源详情
export const getSecurityUpdate = (data) => {
  return axios({
    method: "post",
    url: "/hg/dybusi/update.mvc",
    data: data,
  });
};
// 企业安全承诺重大危险源详情
export const getSecuritySave = (data) => {
  return axios({
    method: "post",
    url: "/hg/dybusi/save.mvc",
    data: data,
  });
};
//判断企业重复上报
export const getSecurityJudgeReport = (data) => {
  return axios({
    method: "post",
    url: "/hg/dybusi/judgeReport.mvc",
    data: qs.stringify(data),
  });
};
//企业模糊查询
export const getSearchArr = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/information/selByKeyword?keyword=" + data,
  });
};
//企业管理列表
export const getEnterpriseList = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/information/list",
    params: data,
  });
};
//主体
export const enterpriseUserMain = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/org/page/enterprise/main/v1",
    data: data,
  });
};
//湖北省的行政区划
export const getDistrictUser = (data) => {
  return axios({
    method: "get",
    url: `/admin/district/user`,
  });
};
//查询企业详情

export const getInformationInfo = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/info/${data}`,
  });
};
//企业基本信息
export const getInformationBasicInfo = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/basic/info/${data}`,
  });
};
//根据信用代码获取企业基本信息
export const getEnterpriseInfo = (data) => {
  return axios({
    method: "post",
    url: `/gemp-chemical/api/gemp/enterprise/integrity/getEnterprise/v1?entcreditcode=${data}`,
  });
};
//危险化学品详情
export const getInformationInfoHazarchem = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/info/hazarchem/${data}`,
  });
};
//重大危险源
export const getInformationInfoDanger = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/info/danger/${data}`,
  });
};
export const getHazarchemList = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/info/hazarchemList/${data}`,
  });
};
export const getHazarchemListNew = (data) => {
  return axios({
    method: "post",
    url: `/gemp-chemical/api/gemp/danger/dangerChemList/v1`,
    data,
  });
};
//重大危险源设备清单
export const getInformationInfoDangerEquipmen = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/danger/equipmen/list/${data.id}?sensortypecode=${data.sensortypecode}`,
  });
};
//导出重大危险源设备清单
export const exportInformationInfoDangerEquipmentExcel = (data) => {
  return axios({
    method: "post",
    url: `/enterprise/information/danger/equipment/exportExcel/${data.id}?sensortypecode=${data.sensortypecode}`,
    // data: qs.stringify(data),
    responseType: "arraybuffer",
  });
};
//重大危险源设备清单详情
export const getInformationEquipmenAlarmvalue = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/equipmen/alarmvalue/list/${data}`,
  });
};
//重大危险源工艺
export const getInformationInfoRelation = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/info/relation/${data}`,
  });
};
//退回
export const sendBack = (data) => {
  return axios({
    method: "post",
    url: `/enterprise/promise/sendBack`,
    data: data,
  });
};
//今日安全承诺
export const getPromiseToday = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/promise/today/${data}`,
  });
};
//新增安全承诺
export const getPromiseAdd = (data) => {
  return axios({
    method: "post",
    url: `/enterprise/promise/add`,
    data: data,
  });
};
//历史承诺
export const getHistoryPromise = (data) => {
  return axios({
    method: "get",
    url:
      `/enterprise/promise/list/${data.enterpriseId}?current=` + data.current,
    data: qs.stringify(data),
  });
};
//历史承诺详情
export const getHistoryPromiseinfo = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/promise/info/" + data,
  });
};

//查询安全承诺装置列表
export const getPromiseDeviceState = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/promise/device/state/" +
      data +
      "?date=" +
      new Date(new Date()).Format("yyyy-MM-dd"),
  });
};
//修改装置
export const postUpdateDevice = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/promise/update/device/state",
    data: data,
  });
};
//查询特殊作业字典
export const postPromiseSpecialJobType = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/promise/specialJob/type",
    data: data,
  });
};

//新增特殊作业
export const postPromiseSpecialJobAdd = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/promise/specialJob/add",
    data: data,
  });
};
//查询特殊作业列表

export const getPromiseSpecialJobList = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/promise/specialJob/list/" +
      data +
      "?date=" +
      new Date(new Date()).Format("yyyy-MM-dd"),
    data: data,
  });
};

export const getPromiseSpecialDelete = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/promise/specialJob/delete/" + data,
  });
};

// 查询企业上报安全承诺
export const getPromiseEnterpriseStatus = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/promise/enterprise/status/" +
      data +
      "?date=" +
      new Date(new Date()).Format("yyyy-MM-dd"),
    data: data,
  });
};
//登记信息
export const getInformationDangerinfo = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/information/danger/info/" + data,
  });
};
//报警统计分析列表
export const getAlarm = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/warning/list?enterpId=" +
      data.enterpriseId +
      "&current=" +
      data.current +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//报警统计分析汇总列表
export const getTotalAlarm = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/warning/allEliminate/" +
      data.enterpriseId +
      "?startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//报警统计分析详情
export const getDetailData = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/warning/statistical?dangerId=" +
      data.dangerId +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//报警统计分析汇总详情
export const getTotalDetailData = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/warning/statistical?enterpid=" +
      data.enterpriseId +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime,
    data: qs.stringify(data),
  });
};
//物联监测报警列表
export const getLot = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/monitoring/list?enterpId=" +
      data.enterpriseId +
      "&current=" +
      data.current +
      "&startTime=" +
      data.startTime +
      "&endTime=" +
      data.endTime +
      "&sensortypeCode=" +
      data.sensortypeCode +
      "&state=" +
      data.state +
      "&dangerName=" +
      data.dangerName,
    data: qs.stringify(data),
  });
};
//实时监测全部重大危险源下拉
export const getSelectData = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/realtimeda/danger/list/" + data.enterpId,
    data: qs.stringify(data),
  });
};
//实时监测列表
export const getRealtimeData = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/realtimeda/realtimeda/list?enterpId=" +
      data.enterpId +
      "&sign=" +
      data.sign +
      "&dangerId=" +
      data.dangerId,
    data: qs.stringify(data),
  });
};
//实时监测状态数据
export const getStateArr = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/realtimeda/statistics/" + data.enterpId,
    data: qs.stringify(data),
  });
};
//视频巡查列表
export const getVideoList = (data) => {
  return axios({
    method: "get",
    url:
      "/enterprise/video/list?enterpId=" + data.enterpId + "&sign=" + data.sign,
    data: qs.stringify(data),
  });
};
//视频全部在线数
export const getVideoNumData = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/video/statistics/" + data.enterpId,
    data: qs.stringify(data),
  });
};
//物联监测导出
export const lotExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/monitoring/exportExcel",
    data: qs.stringify(data),
    responseType: "arraybuffer",
  });
};
//报警统计分析导出
export const alarmExportExcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/warning/exportExcel",
    data: qs.stringify(data),
    responseType: "arraybuffer",
  });
};
//企业基本信息导出
export const InformationExportexcel = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/information/exportExcel",
    data: qs.stringify(data),
    responseType: "arraybuffer",
  });
};
//园区
export const enterExportExcel = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/cim/park/enterExportExcel?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
    responseType: "arraybuffer",
  });
};
//实时监测echart下拉
export const getEchartSelectData = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/realtimeda/index/" + data.equipCode,
    data: qs.stringify(data),
  });
};
//实时监测对接硬石数据
// export const getEchartData = data => {
//     return axios({
//       method: "post",
//       url: 'http://**************:8082/api/v1/datapoints/query',
//       data: { ...data },
//     });
// };
export const getEchartData = (data) => {
  return axios({
    method: "post",
    url: "/hardStone/api/v1/datapoints/query",
    data: { ...data },
  });
};
export const yuanquDonwLoad = (data) => {
  return axios({
    method: "post",
    url:
      "/enterprise/cim/park/parkExportExcel?size=" +
      data.size +
      "&current=" +
      data.current,
    data: data,
    responseType: "arraybuffer",
  });
};

//实时监测通过指标获取上下限
export const getShangXia = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/realtimeda/threshold/" + data.targetCode,
    data: qs.stringify(data),
  });
};
//审核状态提交
export const postEntCheck = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/information/examine",
    data: data,
  });
};
//审核状态提交
export const getExamineRecord = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/information/examine/record/" + data,
  });
};
// ----------------------------------------------------------------
export const getVideoH5VideoH5 = (data) => {
  return axios({
    method: "post",
    url: `/enterprise/videoH5/selVideoH5Url/${data}`,
  });
};
//指标名称
export const getMonitoringMonvalue = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/monitoring/monvalue/${data}`,
  });
};

//
export const getParkView = (data) => {
  return axios({
    method: "get",
    url: "/enterprise/cim/park/view/" + data,
  });
};

//
export const parkVideo = (data) => {
  return axios({
    method: "post",
    url: "/enterprise/cim/park/video/tree",
    data: data,
  });
};

//化学品列表查询（不分页）
export const knowledgeFindById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/knowledge/findAll/msds/v1",
    data: data,
  });
};

//化学品名称搜索
export const msdstitles = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/knowledge/findAll/msdstitles/v1",
    data: data,
  });
};

export const audioToText = (data) => {
  return axios({
    method: "post",
    url: "/gemp-file/api/speechRecognition/audioToText/v1",
    data: data,
  });
};

//工艺列表
export const getKnowledgeListData = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/knowledge/findAll/process/v1",
    data: data,
  });
};

///gemp-user/api/config/system/get/key/v1

//
export const systemGetKey = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/config/system/get/key/v1",
    data: data,
  });
};

export const enterpriseType = (data) => {
  return axios({
    method: "post",
    url: `/enterprise/information/enterpriseType`,
  });
};

export const getSystemKey = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/config/system/get/key/v1",
    data: data,
  });
};

// 企业报告列表
export const getReportList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/report/latestReport/v1",
    data: data,
  });
};
// 企业报告导出
export const getReportExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/report/generateReport/v1",
    data: data,
    // responseType: "arraybuffer"
  });
};
// 企业报告下发
export const getReportSend = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/report/publishReport/v1",
    data: data,
  });
};
// 企业报告历史报告
export const getReportHistory = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/report/historyReport/v1",
    data: data,
  });
};
