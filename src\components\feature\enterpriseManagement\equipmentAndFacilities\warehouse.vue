<template>
  <div id="warehouse"
       class="warehouse">
    <div class="header">
      <div class="operation">
        <h2 style="margin-bottom: 0">仓库列表</h2>
        <div class="inputBox">
          <el-select v-model="searchParam.warehouseType" style="margin-right: 10px"
            size="small"
            clearable
            placeholder="请选仓库类型">
            <el-option v-for="item in warehouseTypeData"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <el-input v-model.trim="searchParam.keyword"
            placeholder="请输入仓库名称"
            class="input"
            size="small"
            clearable
            style="margin-right: 10px"></el-input>
          <el-button size="small"
            type="primary"
            @click="search()">查询</el-button>
        </div>
        <div class="btnBox">
          <!--          <el-button size="mini" type="primary" @click="addEdit"-->
          <!--            >新增</el-button-->
          <!--          >-->
          <CA-button class="export"
            plain
            size="small"
            type="primary"
            @click="exportExcel">导出
          </CA-button>
        </div>
      </div>
    </div>
    <div class="table"
         v-loading="loading">
      <el-table ref="multipleTable"
                :data="tableData"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
                height="530px">
        <el-table-column align="center"
                         label="序号"
                         type="index"
                         width="55">
        </el-table-column>
        <el-table-column label="仓库名称"
                         prop="name"
                         width="120"
                         align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="warehouseTypeName"
                         label="仓库类型">
        </el-table-column>
        <el-table-column prop="constructionDate"
                         label="建造日期"
                         align="center">
          <template slot-scope="{ row }">
            {{ new Date(row.constructionDate).Format("yyyy-MM-dd") }}
          </template>
        </el-table-column>
        <el-table-column prop="useDate"
                         label="投产日期"
                         align="center">
          <template slot-scope="{ row }">
            {{ new Date(row.useDate).Format("yyyy-MM-dd") }}
          </template>
        </el-table-column>
        <el-table-column align="center"
                         label="操作"
                         min-width="120">
          <template slot-scope="scope">
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="view(scope.row)">详情</span>
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="edit(scope.row)"
                  v-if="$store.state.login.user.user_type === 'ent'">编辑</span>
            <!--            <span-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              @click="deleter(scope.row.id)"-->
            <!--              v-if="$store.state.login.user.user_type === 'ent'"-->
            <!--              >删除</span-->
            <!--            >-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination v-if="total != 0"
                     :current-page.sync="currentPage"
                     :total="total"
                     :page-size='size'
                     background
                     layout="total, prev, pager, next"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增编辑查看 -->
    <div     v-if='open'>
    <el-dialog :title="title"
                :visible.sync='open'
               :before-close="beforeClose"
               width="1100px"
               :close-on-click-modal="false"
               :append-to-body="true">
      <div style="
          height: 600px;
          padding:30px 20px;
          overflow-x: hidden;
          overflow-y: overlay;         
        ">
        <el-form ref="form"
                 :model="form"
                 :rules="rules"
                 label-width="150px"
                 :hide-required-asterisk="disabled">
          <div class="form_item">
            <h2 class="form_title">基本信息</h2>
            <div class="form_main">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="所属重大危险源">
                    <el-select v-model="form.sysGreatHazard"
                               disabled
                               placeholder="请选择所属重大危险源">
                      <el-option v-for="item in dangerIdIsNotNullListDataList"
                                 :key="item.dangerid"
                                 :label="item.dangername"
                                 :value="item.dangerid" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="名称">
                    <el-input v-model.trim="form.name"
                              disabled
                              placeholder="请输入名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="层数">
                    <el-input v-model.trim="form.layers"
                              disabled
                              placeholder="请输入层数" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="地址">
                    <el-input v-model.trim="form.address"
                              disabled
                              placeholder="请选择地址" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="经度">
                    <el-input v-model.trim="form.longitude"
                    maxlength="10"
                              placeholder="请输入经度"
                              disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="纬度">
                    <el-input v-model.trim="form.latitude"
                    maxlength="10"
                              placeholder="请输入纬度"
                              disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="高程">
                    <el-input v-model.trim="form.altitude"
                    maxlength="5"
                              placeholder="请输入高程"
                              :disabled="disabled" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="map_height">
                    <el-form-item label="地图定位">
                      <egisMap :islistener="true"
                               :isdetail="disabled"
                               :datas="form"
                               ref="detailMap"
                               style="height: 220px"
                               @mapCallback="mapcallback"></egisMap>
                    </el-form-item>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="仓库类型">
                    <!-- <el-input
                    v-model="form.warehouseType"
                    placeholder="请输入仓库类型"
                  /> -->
                    <el-select v-model="form.warehouseType"
                               placeholder="请选择仓库类型"
                               disabled>
                      <el-option v-for="item in warehouseTypeData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设计储量">
                    <el-input v-model.trim="form.volume"
                              placeholder="请输入设计储量"
                              disabled>
                      <template slot="append">立方米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="占地面积">
                    <el-input v-model.trim="form.area"
                              placeholder="请输入占地面积"
                              disabled>
                      <template slot="append">平方米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="主要负责人">
                    <el-input v-model.trim="form.principalMaillistPersonName"
                              disabled
                              placeholder="请输入主要负责人" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="联系电话">
                    <el-input v-model.trim="form.principalMaillistPersonTel"
                              disabled
                              placeholder="请输入联系电话" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设计使用年限">
                    <el-input v-model.trim="form.designLife"
                              placeholder="请输入设计使用年限"
                              disabled>
                      <template slot="append">年</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="建设日期">
                    <el-date-picker disabled
                                    v-model="form.constructionDate"
                                    type="date"
                                    placeholder="选择建设日期"
                                    value-format="yyyy-MM-dd 00:00:00">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="投产日期">
                    <el-date-picker v-model="form.useDate"
                                    type="date"
                                    placeholder="选择投产日期"
                                    value-format="yyyy-MM-dd 00:00:00"
                                    disabled>
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="form_item"
               style="overflow: hidden">
            <h2 class="form_title"
                style="float: left">存储危化品信息</h2>
                       <el-button
                        type="primary"
                          size="mini"
                          v-if="!disabled"
                        @click="addItem"
                        style="float: right"
                          >新增</el-button
                        >
            <div class="form_main">
              <el-col :span="24">
                <el-table :data="tableDatas"
                          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                          border
                          stripe
                          style="width: 100%"
                          height="300px"
                          max-height="300px">
                  <!-- <el-table-column
                    header-align="center"
                    align="center"
                    label="危化品名称"
                  >
                    <template slot-scope="scope">
                      <el-form-item
                        :prop="'tableData.' + scope.$index + '.goodsName'"
                        :rules="rules.goodsName"
                      >
                        <el-input
                          v-model="scope.row.goodsName"
                          placeholder="物品名称"
                          maxlength="50"
                          show-word-limit
                        ></el-input>
                      </el-form-item>
                    </template>
                  </el-table-column> -->
                  <el-table-column prop="item"
                                   width="200"
                                   label="危化品名称"
                                   align="center">
                    <template slot-scope="scope">
                      <!--                      <el-autocomplete-->
                      <!--                        popper-class="my-autocomplete"-->
                      <!--                        v-model="scope.row.baseChemicalName"-->
                      <!--                        :fetch-suggestions="querySearch"-->
                      <!--                        placeholder="请选择/输入物品名称"-->
                      <!--                        clearable-->
                      <!--                        @clear="clearSensororgCode(scope.row)"-->
                      <!--                        size="mini"-->
                      <!--                        @select="-->
                      <!--                          (value) => {-->
                      <!--                            handleSelect(value, scope.row);-->
                      <!--                          }-->
                      <!--                        "-->
                      <!--                        style="width: 150px"-->
                      <!--                      >-->
                      <!--                        <template slot-scope="{ item }">-->
                      <!--                          <div class="name">{{ item.msdstitle }}</div>-->
                      <!--                        </template>-->
                      <!--                      </el-autocomplete>-->
                      <!-- <el-select popper-class="my-autocomplete"
                                 v-model="form.baseChemicalName"
                                 :filter-method="querySearch"
                                 filterable
                                 placeholder="请选择/输入物品名称"
                                 clearable
                                 @clear="clearSensororgCode(scope.row)"
                                 @change="
                          (value) => {
                            handleSelect(value, scope.row);
                          }
                        "
                                 style="width: 150px">
                        <el-option v-for="item in options"
                                   :key="item.msdsid"
                                   :label="item.msdsalias"
                                   :value="item.msdsid">
                        </el-option>
                      </el-select> -->


                       <el-select v-model.trim="scope.row.msdstitle"
                                   filterable
                                   style="width:160px"
                                   placeholder="请输入危化品名称"
                                   remote
                                   :disabled="disabled"
                                   value-key="msdsid"
                                   clearable
                                   @change="(item)=>{
                            currentSel(item, scope.row)
                          }"
                                   reserve-keyword
                                   :remote-method="Tolikesearch">
                          <el-option v-for="item in options"
                                     :key="item.msdsid"
                                     :title='item'
                                     :label="item.msdstitle"
                                     :value="item">
                          </el-option>
                        </el-select>
                        <span style="color:red">*</span>

                        <!-- value, scope.row -->


                        <!-- <el-autocomplete
                          popper-class="my-autocomplete"
                          v-model="scope.row.msdsid"
                          :fetch-suggestions="querySearch"
                          placeholder="请输入危化品名称"
                          style="width:160px"
                          clearable
                          ref="forcusTitle"
                          @clear="clearSensororgCode(scope.row)"
                          @select="(value)=>{
                            currentSel(value, scope.row)
                          }"                           
                        >
                          <template slot-scope="{ item }">                            
                            <div class="name">{{ item.msdstitle }}</div>
                          </template>
                        </el-autocomplete> -->





                    </template>
                  </el-table-column>
                  <el-table-column header-align="center"
                                   align="center"
                                   prop="chemicalAlias"
                                    width='80'
                                   label="别名">
                  </el-table-column>
                  <el-table-column header-align="center"
                                   align="center"
                                   prop="casCode"
                                    width='80'
                                   label="CAS号">
                  </el-table-column>
                  <el-table-column header-align="center"
                                   align="center"
                                   label="化学品状态">
                    <template slot-scope="scope">
                      <el-select v-model="scope.row.chemicalStatus"
                      :disabled="disabled"
                                 placeholder="化学品状态">
                        <el-option v-for="item in dangerIdIsNotNullListData"
                                   :key="item.value"
                                   :label="item.name"
                                   :value="item.value">
                        </el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column header-align="center"
                                   align="center"
                                   label="最大储存量(m³)">
                    <template slot-scope="scope">
                      <el-input v-model.trim="scope.row.maxStorage"
                      maxlength="10"
                      :disabled="disabled"
                                placeholder="最大储存量(m³)"
                                ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column header-align="center"
                                   align="center"
                                    maxlength="10"
                                   label="日常储存量(m³)">
                    <template slot-scope="scope">
                      <el-input v-model="scope.row.dailyStorage"
                      :disabled="disabled"
                                placeholder="日常储存量(m³)"></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column header-align="center"
                                   align="center"
                                   prop="isImportantName"
                                   width='80'
                                   label="是否重点监管">
                    <template slot-scope="scope">
                      <span>{{ scope.row.isImportantName == "1" ? "是" : "否" }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作"
                                   header-align="center"
                                   width='80'
                                   align="center">
                    <template #default="scope">
                      <el-button size="mini" :disabled='disabled'
                                 type="text"
                                 @click="handleDelete(scope.$index, scope.row)">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </div>
          </div>
        </el-form>
      </div>
      <div v-if="!disabled"
           slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  getWarehouseListData,
  addWarehouseListData,
  editWarehouseListData,
  deleteWarehouseListData,
  getDangerIdIsNotNullListData,
  getDangerIdIsNotNullTypeData,
  datailWarehouseListData,
  getDangerIdIsNotNullListDatas,
  exportWarehouseListData
} from '@/api/equipmentAndFacilities'
import { parseTime } from '@/utils/index'
import {msdstitles,knowledgeFindById} from "@/api/entList.js";

const mapConfig = require('@/assets/json/map.json')
export default {
  //import引入的组件
  name: 'warehouse',
  components: {},
  data() {
    return {
      msdstitle:"",
      queryParams: {
        msdsid: "",
        msdstitle: "",
      },
      options:[],
      option: [],
      loading: false,
      open: false,
      warehouseTypeData: [],
      dangerIdIsNotNullListDataList: [], //化学品名称
      dangerIdIsNotNullListData: [], //化学品状态
      title: '新增仓库信息',
      currentPage: 1,
      size: 11,
      disabled: false,
      selection: [],
      total: 0,
      tableData: [],
      tableDatas: [],
      form: {
        sysGreatHazard: '',
        name: '',
        layers: '',
        address: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: '0',
        warehouseType: '',
        volume: '',
        area: '',
        orgCode: '',
        principalMaillistPersonName: '',
        principalMaillistPersonTel: '',
        designLife: '',
        constructionDate: '',
        useDate: '',
        chemicalsList: [],
        type: 1
      },
      enterpriseId: '',
      rules: {
        // goodsName: [
        //   {
        //     required: true,
        //     message: "物品名称不能为空",
        //     trigger: ["blur", "change"],
        //   },
        // ],
      },
      searchParam: { // 搜索
        keyword: '', // 关键字
        warehouseType: '' // 仓库类型
      },
    }
  },
  //   watch: {
  //     enterpriseId: {
  //       handler(newVal, oldVal) {
  //         this.currentPage = 1;
  //       },
  //       deep: true,
  //       immediate: true,
  //     },
  //   },
  //   filters: {
  //     numToFixed(val) {
  //       if (val) {
  //         return parseInt(val).toFixed(2);
  //       } else {
  //         return val;
  //       }
  //     },
  //   },
  //方法集合
  methods: {
    beforeClose(){
      this.open = false;
    },
    closeFun() {
      this.queryParams.msdstitle = "";
    },
  
    // querySearch(queryString, cb) {  
       
    //   this.getSeachData(queryString || "", cb);
    // },
    // getSeachData(keyWord, cb) {
    //   var param = {
    //     msdstitle: keyWord,
    //     // nowPage: 0,
    //     // pageSize: 500,
    //   };
    //   getDangerIdIsNotNullListData(param)
    //     .then((res) => {                
    //       if (res.data.status == 200) {          
    //         if (res.data.data.list.length > 0) {
    //           cb(res.data.data.list);
    //         } else {
    //           cb([]);
    //         }
    //       }
    //     })
    //     .catch((e) => {
    //       console.log(e, "请求错误");
    //     });
    // },
  
    // },
    //自动输入end
    //获取select option选中的值
    // currentSel(selVal) {
    //   debugger
    //   this.listParams.msdsId = selVal
    //   this.options.forEach(item => {
    //     if (item.msdsId === selVal) {
    //       this.listParams.msdsTitle = item.msdsTitle
    //     }
    //   })
    // },
    //搜索发起请求 传入值为当前select输入的值
    Tolikesearch(query) {       
      getDangerIdIsNotNullListData({ msdstitle: query }).then(res => {
        if (res.data.status === 200) {
          console.log(res)
          this.options = res.data.data.list
        }
      })
    },
    //重大危险源
    getDangerIdIsNotNullListDataList(id) {
      getDangerIdIsNotNullListDatas({ enterpid: id }).then(res => {
        if (res.data.status == 200) {
          this.dangerIdIsNotNullListDataList = res.data.data
        }
      })
    },
    getDangerIdIsNotNullListDataType() {
      getDangerIdIsNotNullTypeData({
        dicCode: 'CHEMICAL_STATUS'
      }).then(res => {
        if (res.data.status == 200) {
          this.dangerIdIsNotNullListData = res.data.data
        }
      })
      getDangerIdIsNotNullTypeData({
        dicCode: 'WAREHOUSE_TYPE'
      }).then(res => {
        if (res.data.status == 200) {
          this.warehouseTypeData = res.data.data
        }
      })
    },
    // querySearch(queryString, cb) {   
    //   debugger  
    //   this.getSeachData(queryString)
    // },
    // getSeachData(keyWord) {    
    //   getDangerIdIsNotNullListData({ msdstitle: keyWord })
    //     .then(res => {
    //       if (res.data.status == 200) {
    //         this.option = res.data.data
    //       }
    //     })
    //     .catch(e => {
    //       console.log(e, '请求错误')
    //     })
    // },
    //选择化学品
    currentSel(value, item) {   
      
      // item.msdsid = value
      // this.options.forEach(item => {
      //   if (item.msdsId === value) {
      //     item.baseChemicalName = item.msdsTitle
      //   }
      // })

      this.msdstitle=value.msdstitle
      item.msdsid = value.msdsid
      item.msdstitle = value.msdstitle
      item.chemicalAlias = value.msdsalias
      item.casCode = value.casno
      item.isImportantName = value.supervisionflag
    },
    clearSensororgCode(item) {
      item.msdsid = ''
      item.baseChemicalName = ''
      item.chemicalAlias = ''
      item.casCode = ''
      item.isImportantName = ''
    },
    getData(id) {
      this.loading = true
      this.enterpriseId = id
      var orgCode = ''
      if (this.$store.state.login.user.user_type === 'ent') {
        orgCode = this.$store.state.login.user.org_code
      }
      getWarehouseListData({
        pageSize: this.size, //条数
        nowPage: this.currentPage, //页数
        orgCode: id,
        keyword: this.searchParam.keyword, // 关键字
        warehouseType: this.searchParam.warehouseType // 罐类型
        // orgCode: orgCode
      }).then(res => {
        this.loading = false
        if (res.data.status === 200) {
          this.total = res.data.data.total
          this.tableData = res.data.data.list
        }
      })
    },
    search() {
      this.currentPage = 1
      this.getData(this.enterpriseId)
    },
    // clearSensortypeCode(e) {
    //   this.sensortypeCode = "";
    // },
    // clearState(e) {
    //   this.state = "";
    // },
    // clearDangerName(e) {
    //   this.dangerName = "";
    // },
    handleSelectionChange(val) {
      console.log(val)
    },
    handleClick() {
      console.log(123)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(this.enterpriseId)
    },
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId
      }
    },
    addEdit() {
      this.reset()
      this.open = true
      this.disabled = false
    },
    async view(row) {
     
      this.reset()
      this.open = true
      this.disabled = true
      this.title = '查看仓库信息'

// getDangerIdIsNotNullListData({ msdstitle: query }).then(res => {
//         if (res.data.status === 200) {
//           console.log(res)
//           this.options = res.data.data.list
//         }
//       })
var resq=await getDangerIdIsNotNullListData({ msdstitle: '' })
this.options = resq.data.data.list
    
      // this.Tolikesearch()
      datailWarehouseListData({ id: row.id }).then(res => {
        if (res.data.status == 200) {
          this.form = res.data.data
          if (!this.form.longitude || !this.form.latitude) {
            this.form.longitude = mapConfig.map.defaultExtent.center[0];
            this.form.latitude = mapConfig.map.defaultExtent.center[1];
          }

        
          this.tableDatas = res.data.data.chemicalsList || []


          // this.tableDatas.push({
          //     "id": "ff80808183d0c6cb0183d0cdffd50001",
          //     "facilityWarehouseId": "ff80808182d909880182d90d1a190001",
          //     "msdsid": "quanhuizhonghua1987",
          //     "msdstitle": "芳烃抽提原料",
          //     "maxStorage": "11",
          //     "dailyStorage": "11",
          //     "chemicalStatus": "1",
          //     "delFlag": "0",
          //     "createTime": "2022-10-13 18:05:04",
          //     "createBy": "0bdabebd3c7447ecac107c764bfb1ac1",
          //     "updateTime": "2022-10-13 18:05:04",
          //     "updateBy": "0bdabebd3c7447ecac107c764bfb1ac1",
          //     "sysOrgCode": "facc2296202911e8b21700ff3c1702d7",
          //     "chemicalAlias": null,
          //     "casCode": null,
          //     "isImportantName": "0"
          // })
                    // this.querySearch()
         
        }
      })
    },
    edit(row) {
      this.reset()
      this.open = true
      this.disabled = false
      this.Tolikesearch()
      this.title = '编辑仓库信息'
      //   this.form = row;
      datailWarehouseListData({ id: row.id }).then(res => {
        if (res.data.status == 200) {
          this.form = res.data.data
          if (!this.form.longitude || !this.form.latitude) {
            this.form.longitude = mapConfig.map.defaultExtent.center[0];
            this.form.latitude = mapConfig.map.defaultExtent.center[1];
          }
          this.tableDatas = res.data.data.chemicalsList || []

          
          // this.tableDatas.push({
          //     "id": "ff80808183d0c6cb0183d0cdffd50001",
          //     "facilityWarehouseId": "ff80808182d909880182d90d1a190001",
          //     "msdsid": "quanhuizhonghua1987",
          //     "msdstitle": "芳烃抽提原料",
          //     "maxStorage": "11",
          //     "dailyStorage": "11",
          //     "chemicalStatus": "1",
          //     "delFlag": "0",
          //     "createTime": "2022-10-13 18:05:04",
          //     "createBy": "0bdabebd3c7447ecac107c764bfb1ac1",
          //     "updateTime": "2022-10-13 18:05:04",
          //     "updateBy": "0bdabebd3c7447ecac107c764bfb1ac1",
          //     "sysOrgCode": "facc2296202911e8b21700ff3c1702d7",
          //     "chemicalAlias": null,
          //     "casCode": null,
          //     "isImportantName": "0"
          // })
          
        }
      })
    },
    deleter(row) {
      const id = row
      this.$confirm('是否确认删除该仓库信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteWarehouseListData({
            ids: [id]
          }).then(res => {
            if (res.data.status == 200) {
              this.$message.success('删除成功')
              this.getData()
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    cancel() {
      this.open = false
      this.reset()
    },
    addItem() {     
      this.tableDatas.push({
        msdsid: '',
        chemicalAlias: '',
        casCode: '',
        chemicalStatus: '',
        maxStorage: '',
        dailyStorage: '',
        isImportantName: ''
      })
    },
    handleDelete(index, row) {
      this.tableDatas.splice(index, 1)
    },
    reset() {
      this.form = {
        sysGreatHazard: '',
        name: '',
        layers: '',
        address: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: '0',
        warehouseType: '',
        volume: '',
        area: '',
        orgCode: '',
        principalMaillistPersonName: '',
        principalMaillistPersonTel: '',
        designLife: '',
        constructionDate: '',
        useDate: '',
        type: 1
      }
      this.tableDatas = []
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
    submitForm() {
      // this.tableDatas.forEach(item=>{
      //   if(item.msdsid==''){
      //     this.$message.error('危化品名称不能为空')
          
      //   }
      // })
      for(var i=0;i<this.tableDatas.length;i++){
         if(this.tableDatas[i].msdsid==''){
          this.$message.error('危化品名称不能为空')
          return false          
        }
      }




      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.title === '编辑仓库信息') {
            let params = {
              id: this.form.id,
              companyHazardId: this.form.companyHazardId,
              deviceName: this.form.deviceName ? this.form.deviceName : '',
              deviceNo: this.form.deviceNo ? this.form.deviceNo : '',
              // latitude: this.form.latitude ? this.form.latitude.toFixed(9) : '',
              // longitude: this.form.longitude ? this.form.longitude.toFixed(9) : '',
              latitude: this.form.latitude ? this.form.latitude : '',
              longitude: this.form.longitude ? this.form.longitude : '',
              altitude: this.form.altitude ? this.form.altitude : '', // 高程
              respPersionId: this.form.respPersionId
                ? this.form.respPersionId
                : '',
              installLocation: this.form.installLocation
                ? this.form.installLocation
                : '',
              phone: this.form.phone ? this.form.phone : '',
              orgCode: this.form.orgCode ? this.form.orgCode : '',
              monitorEqpmtType: this.form.monitorEqpmtType
                ? this.form.monitorEqpmtType
                : '',
              monitorObjectType: this.form.monitorObjectType
                ? this.form.monitorObjectType
                : '',
              monitorObjectCode: this.form.monitorObjectCode
                ? this.form.monitorObjectCode
                : '',
              mainTechnicalParameters: this.form.mainTechnicalParameters
                ? this.form.mainTechnicalParameters
                : '',
              chemicalsList: this.tableDatas,
              dataSources: this.form.dataSources
              // companyHazardId: this.hazardId,
            }
            editWarehouseListData(params).then(response => {   //editWarehousData没有这个？？
              if (response.data.status == 200) {
                this.open = false
                this.$message({
                  type: 'success',
                  message: response.data.msg || '修改成功'
                })
                this.reset()
                this.getData()
              } else {
                this.$message.error(response.data.msg || '修改失败')
              }
            })
          } else {
            this.form.chemicalsList = this.tableDatas
            this.form.orgCode = this.$store.state.login.user.org_code
            addWarehouseListData(this.form).then(response => {
              if (response.data.status == 200) {
                this.open = false
                this.$message({
                  type: 'success',
                  message: response.data.msg || '新增成功'
                })
                this.reset()
                this.getData()
              } else {
                this.$message.error(response.data.msg || '新增失败')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6)
        this.form.latitude = data.location.lat.toFixed(6)
        this.form.altitude = data.location.altitude ? data.location.altitude.toFixed(6) : '0'
        this.form.address = data.formatted_address
      } else {
        this.$message({
          message: '详情页面不可选点！',
          type: 'warning'
        })
      }
    },
    exportExcel() {
      exportWarehouseListData({
        nowPage: this.currentPage,
        // orgCode: this.$store.state.login.user.org_code,
        orgCode: this.enterpriseId,
        pageSize: this.size,
        keyword: this.searchParam.keyword, // 关键字
        warehouseType: this.searchParam.warehouseType // 罐类型
      }).then(response => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        const blob = new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '仓库' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getDangerIdIsNotNullListDataType()
  }
}
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body{
  padding:0
}

.warehouse {
  background-color: #fff;
  // padding-top: 15px;
  .header {
    .title {
      margin-left: 20px;
      font-weight: 600;
    }

    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      .inputBox {
        // width: 1020px;
        //width: 940px;
        //float: left;
        display: flex;
        align-items: center;
        .input {
          width: 160px;
        }
      }
      .btnBox {
        display: flex;
        & > * {
          margin-left: 10px;
        }
      }
    }

    .export {
      margin-right: 20px;
    }
  }

  .table {
    width: 100%;
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
