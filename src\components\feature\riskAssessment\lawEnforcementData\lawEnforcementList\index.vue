<template>
    <div class="law-list">
        <div class="search-box">
            <el-form :model="sceneParams" ref="queryForm" :inline="true" label-width="68px !important">
                <el-form-item prop="distCode">
                    <el-cascader placeholder="请选择行政区划" :options="district" v-model="sceneParams.distCode"
                        size="small" style="width: 300px" :props="{
                            checkStrictly: true,
                            value: 'distCode',
                            label: 'distName',
                            children: 'children',
                            emitPath: false,
                        }" clearable :show-all-levels="true">
                    </el-cascader>
                </el-form-item>
                <el-form-item prop="enterpriseType">
                    <el-select v-model="sceneParams.enterpriseType" size="small" placeholder="请选择企业类型" style="width: 340px" multiple clearable>
                        <el-option v-for="item in entTypes" :key="item.id" :label="item.enterpriseType"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <!-- <el-form-item prop="entType">
                    <el-select v-model="sceneParams.orgCode" size="small" placeholder="请选择检查部门" clearable>
                        <el-option v-for="item in orgList" :key="item.id" :label="item.enterpriseType"
                            :value="item.id">
                        </el-option>
                    </el-select>
                </el-form-item> -->
                <el-form-item prop="datePickerValue">
                    <el-date-picker
                        v-model="datePickerValue"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        unlink-panels
                        size="small"
                        style="width: 370px"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        :clearable="true"
                        class="date-picker"
                    ></el-date-picker>
                </el-form-item>
                <el-form-item>
                <el-button type="primary" size="small" @click="handleRearch">查询</el-button>
                <el-button type="primary" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
            </el-form>
        </div>
        <el-table :data="sceneData" v-loading="loading" style="width: 100%"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }" border ref="multipleTable">
            <el-table-column type="index" label="序号" width="50" align="center">
            </el-table-column>
            <el-table-column prop="documentNum" label="文件编号" align="center" min-width="120">
            </el-table-column>
            <!-- enterpName -->
            <el-table-column prop="enterpName" label="检查对象名称" align="center" min-width="150">
            </el-table-column>
            <el-table-column prop="parentOrgName" label="检查机构" align="center" width="150">
            </el-table-column>
            <el-table-column prop="orgName" label="检查部门" align="center" width="200">
            </el-table-column>
            <el-table-column prop="startTime" label="检查开始时间" align="center" width="180">
            </el-table-column>
            <el-table-column prop="dataSource" label="数据来源" align="center" width="150">
            </el-table-column>
            <el-table-column prop="inspectPerson" label="检查员" align="center" width="150">
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
                <template slot-scope="{ row }">
                    <div>
                        <el-button type="text" @click="clickBan(row)">办理</el-button>
                    </div>
                </template>
            </el-table-column>



        </el-table>
        <div class="pagination">
            <el-pagination @current-change="handleCurrentChange" :current-page.sync="sceneParams.nowPage" background
                layout="total, prev, pager, next" :total="total" v-if="total != 0">
            </el-pagination>
        </div>
    </div>
</template>

<script>
import {
    getSelectData,
    enterpriseType,
    getHazarchemList,
    getKnowledgeListData,
} from "@/api/entList";
import { spotInspectFindPage } from "@/api/riskAssessment";
export default {
    data() {
        return {
            sceneData: [],
            datePickerValue:'',
            sceneParams: {        
                nowPage: 1,
                pageSize: 10,
                distCode: '',
                // orgCode: "",
                enterpriseType: [],
                startTime: "",
                endTime: "",
            },
            total: 0,
            loading: false,
            district: this.$store.state.controler.district, // 行政区划
            entTypes: [], // 企业类型
            orgList: [], // 检查部门
        }
    },
    created() {
        this.getEnterpriseType();
        this.spotInspectFindPage();
    },
    methods: {
        handleRearch() {
            this.sceneParams.nowPage = 1;
            this.spotInspectFindPage();
        },
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.datePickerValue = '';
            this.handleRearch();
        },
        //获取企业类型
        getEnterpriseType() {
            enterpriseType({}).then((res) => {
                this.entTypes = res.data.data;
            });
        },
        // 分页查询
        handleCurrentChange(val) {
            this.sceneParams.nowPage = val;
            this.spotInspectFindPage();
        },
        //安全生产事故警示
        spotInspectFindPage() {
            this.sceneData = [];
            this.total = 0;
            const distCode = this.sceneParams.distCode ? [this.sceneParams.distCode] : [];
            const startTime = this.datePickerValue ? this.datePickerValue[0] : '';
            const endTime = this.datePickerValue ? this.datePickerValue[1] : '';
            const params = {
                // ...this.sceneParams,
                page: this.sceneParams.nowPage,
                pageSize: this.sceneParams.pageSize,
                startTime: startTime,
                endTime: endTime,
                distCode: distCode,
                enterpriseType: this.sceneParams.enterpriseType,       
            }
            spotInspectFindPage(params).then((res) => {
                const { data } = res;
                if (data.code == 0) {
                    this.sceneData = data.data.records;
                    this.total = data.data.total;
                } else {
                    this.$message({
                        message: data.msg || "获取数据失败",
                        type: 'warning'
                    })
                }
            });
        },
        clickBan(val) {
            var url = `https://yyzc-hlwzf.hbsis.gov.cn:31443/#v_form/task/check/?id=${val.originId}&menu-id=model%3Atask.check&create_by_me=2`
            window.open(url)
        },
    }
}
</script>

<style lang="scss" scoped>
.law-list {
    .pagination {
        margin-top: 30px;
        padding-bottom: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>