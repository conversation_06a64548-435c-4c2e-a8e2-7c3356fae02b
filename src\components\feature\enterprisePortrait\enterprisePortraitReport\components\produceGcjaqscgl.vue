<template>
  <div class="produceGcjaqscgl">
    <div class="produceGcjaqscgl_content">
      <div class="companyTitle">
        <div class="icon_title">
          {{ companyName }}
        </div>
      </div>
      <div class="company-content border">
        <div class="label">第一责任人：</div>
        <div
          class="value"
          v-for="(item, index) in dataList.dutyDraftList"
          :key="index"
        >
          {{ item.dutyName }}-{{ item.dutyPost }}-{{ item.dutyPhone }}
        </div>
      </div>
    </div>
    <div class="production-title">
      <div class="label">安全管理层级：</div>
      <div class="value">{{ levelFilter(dataList.rankSelect) }}</div>
    </div>
    <div
      class="produceGcjaqscgl_content"
      v-for="(item, index) in dataList.childList"
      :key="index"
    >
      <div class="companyTitle">
        <div class="icon_title">
          {{ item.rankName }}
        </div>
      </div>
      <div class="flex border"> 
         <div style="width: 40%" class="company-content">
          <div class="label">班组名称：</div>
          <div class="value">
            {{ item.rankName }}
          </div>
        </div>
        <div style="width: 60%" class="company-content">
          <div class="label">第一责任人：</div>
          <div class="value" v-for="(value, val) in item.dutyDraftList" :key="index">
            {{ value.dutyName }}-{{ value.dutyPost }}-{{ value.dutyPhone }}
          </div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>

export default {
  name: "Tree",
  components: {

  },
  props: {
    isShowButton: {
      type: Boolean,
      default: true,
      required: false,
    },
    disabled: {
      type: Boolean,
      default: false,
      required: false,
    },
    dataList: {
      type: Object,
      default: {},
      required: false,
    },
    companyName: {
      type: String,
      default: "",
      required: false,
    },
    enterpriseName: {
      type: String,
      default: "",
      required: false,
    },
  },
  data() {
    this.safetyManagementList = [
      {
        label: "分厂级",
        id: "2",
        disabled: false,
      },
      {
        label: "车间级",
        id: "3",
        disabled: false,
      },
      {
        label: "班组级",
        id: "4",
        disabled: true,
      },
    ];
    return {
      branchTitle: "添加分厂",
    };
  },
  created() {
  },

  methods: {
    levelFilter(val) {
      const item = this.safetyManagementList.find((item) => item.id == val);
      return item.label || "";
    },
  },
};
</script>

<style lang="scss" scoped>
.produceGcjaqscgl {
  width: 100%;

  .red {
    color: red;
  }
  .no_ml {
    margin-left: 8px;
    padding-top: 20px;
  }
  .infoButton {
    height: 36px;
    font-size: 16px;
    line-height: 36px;
    display: inline-block;
    white-space: nowrap;
    cursor: pointer;
    background: #409eff;
    border: 1px solid #409eff;
    color: #fff;
    text-align: center;
    outline: 0;
    margin: 0;
    font-weight: 500;
    padding: 0 20px;
    font-size: 16px;
    border-radius: 4px;
  }
  .produceGcjaqscgl_content {
    width: 100%;
    position: relative;
    height: 99%;
  }
}
.companyTitle {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  background-color: #f1f5fb;
  .icon_title {
    font-size: 16px;
    color: #49445f;
    font-weight: 600;
  }
}
.company-content,
.production-title {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  padding: 0 20px;
  .label {
    // font-weight: 600;
    margin-right: 10px;
    color: #49445f;
  }
  .value {
  }
}
.border {
  border: #f1f5fb 1px solid;
  border-top: none;
}
.flex{
  display: flex;
  align-items: center;
}
</style>
