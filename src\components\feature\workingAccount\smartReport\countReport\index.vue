<template>
  <div class="count-report-container">
    <el-tabs v-model="activeTab" @tab-click="handleTabClick">
      <!-- 统计报表tab -->
      <el-tab-pane label="统计报表" name="report">
        <count-report-list
          v-if="currentView === 'list'"
          @goDetail="goDetail"
          ref="list"
        ></count-report-list>
        <count-report-detail
          v-if="currentView === 'detail'"
          ref="detail"
          :reportId="reportId"
          @goBack="goBack"
        ></count-report-detail>
      </el-tab-pane>

      <!-- 数据填报tab -->
      <el-tab-pane label="数据填报" name="dataEntry">
        <data-entry-list
          v-if="dataEntryView === 'list'"
          @goDetail="goDataEntryDetail"
          ref="dataEntryList"
        ></data-entry-list>
        <data-entry-detail
          v-if="dataEntryView === 'detail'"
          ref="dataEntryDetail"
          :tableId="selectedTableId"
          @goBack="goDataEntryBack"
        ></data-entry-detail>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import CountReportList from "./list.vue";
import CountReportDetail from "./detail.vue";
import DataEntryList from "./dataEntry/list.vue";
import DataEntryDetail from "./dataEntry/detail.vue";

export default {
  name: "CountReport",
  components: {
    CountReportList,
    CountReportDetail,
    DataEntryList,
    DataEntryDetail,
  },
  data() {
    return {
      activeTab: "report", // 当前激活的tab
      // 统计报表相关
      currentView: "list",
      reportId: "",
      // 数据填报相关
      dataEntryView: "list",
      selectedTableId: "",
    };
  },
  methods: {
    // tab切换处理
    handleTabClick(tab) {
      if (tab.name === "report") {
        // 切换到统计报表时，重置为列表视图
        this.currentView = "list";
      } else if (tab.name === "dataEntry") {
        // 切换到数据填报时，重置为列表视图
        this.dataEntryView = "list";
        // 初始化数据填报列表
        this.$nextTick(() => {
          if (this.$refs.dataEntryList) {
            this.$refs.dataEntryList.getTableList();
          }
        });
      }
    },

    // 统计报表相关方法
    goDetail(row) {
      this.reportId = row.id;
      this.currentView = "detail";
      this.$nextTick(() => {
        this.$refs.detail.initData(row);
      });
    },
    goBack() {
      this.currentView = "list";
      this.$nextTick(() => {
        this.$refs.list.getList();
      });
    },

    // 数据填报相关方法
    goDataEntryDetail(table) {
      this.selectedTableId = table.id;
      this.dataEntryView = "detail";
      this.$nextTick(async () => {
        if (this.$refs.dataEntryDetail) {
          await this.$refs.dataEntryDetail.initData(table);
        }
      });
    },
    goDataEntryBack() {
      this.dataEntryView = "list";
      this.$nextTick(() => {
        if (this.$refs.dataEntryList) {
          this.$refs.dataEntryList.getTableList();
        }
      });
    },
  },
  created() {
    // 检查路由参数，决定初始视图
    // const { id, fields } = this.$route.query;
    // if (id) {
    //   this.goDetail(id, fields);
    // }
  },
};
</script>

<style lang="scss" scoped>
.count-report-container {
  height: 100%;

  ::v-deep .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;

    .el-tabs__header {
      margin: 0 0 20px 0;

      .el-tabs__nav-wrap {
        &::after {
          height: 1px;
        }
      }

      .el-tabs__item {
        padding: 0 30px;
        font-size: 14px;
        font-weight: 500;

        &.is-active {
          color: #409eff;
        }
      }
    }

    .el-tabs__content {
      flex: 1;
      overflow: hidden;

      .el-tab-pane {
        height: 100%;

        > div {
          height: 100%;
        }
      }
    }
  }
}
</style>
