const state = {
  vuexEntId: "",
  entModelName: "",
  entPatroName: "",
  automaticPatrolData: [],
  automaticSpotCheckData: [],
  urlData: [],
  auxiliaryMode: false, // 辅助监管模式标识
  entListControler: {
    levelVal: ["1", "2", "3", "4"],
    districtVal: "",
    entTypeVal: "",
    enterpName: "",
    auditStatusVal: "",
  },
  district: [],
  clickTableBar: "baseInfo",
  informationClickBar: "announcements",
};
const getters = {
  getEntId: (state) => {
    return state.vuexEntId;
  },
  getEntModelName: (state) => {
    return state.entModelName;
  },
  getEntPatroName: (state) => {
    return state.entPatroName;
  },
  getAutomaticPatrolData: (state) => {
    return state.automaticPatrolData;
  },
  getAutomaticSpotCheckData: (state) => {
    return state.automaticSpotCheckData;
  },
  getUrlData: (state) => {
    return state.urlData;
  },
  getDistrict: (state) => {
    return state.district;
  },
  getTableBar: (state) => {
    return state.clickTableBar;
  },
  getInformationClickBar: (state) => {
    return state.informationClickBar;
  },
  getAuxiliaryMode: (state) => {
    return state.auxiliaryMode;
  },
};
const actions = {
  setDistrict(content, payload) {
    content.commit("updateDistrict", payload);
  },
};
const mutations = {
  updateEntId(state, val) {
    state.vuexEntId = val;
  },
  updateEntModelName(state, val) {
    state.entModelName = val;
  },
  updateEntPatroName(state, val) {
    state.entPatroName = val;
  },
  updateAutomaticPatrolData(state, val) {
    state.automaticPatrolData = val;
  },
  updateAutomaticSpotCheckData(state, val) {
    state.automaticSpotCheckData = val;
  },
  updateUrlData(state, val) {
    state.urlData = val;
  },
  updateEntListControler(state, val) {
    state.entListControler = val;
  },
  updateDistrict(state, val) {
    state.district = val;
  },
  updateTableBar(state, val) {
    state.clickTableBar = val;
  },
  updateInformationClickBar(state, val) {
    state.informationClickBar = val;
  },
  SET_AUXILIARY_MODE(state, val) {
    state.auxiliaryMode = val;
  },
};
export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
