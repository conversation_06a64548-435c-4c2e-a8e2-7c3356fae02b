define(["./Matrix2-e6265fb0","./defaultValue-69ee94f4","./EllipseGeometry-43f9e5b7","./RuntimeError-ac440aa5","./ComponentDatatype-a9820060","./WebGLConstants-f63312fc","./GeometryOffsetAttribute-4d39b441","./Transforms-06c05e21","./_commonjsHelpers-3aae1032-15991586","./combine-0259f56f","./EllipseGeometryLibrary-2b119a38","./GeometryAttribute-b7edcc35","./GeometryAttributes-1b4134a9","./GeometryInstance-19ac39d5","./GeometryPipeline-311a1f9e","./AttributeCompression-6e71d14f","./EncodedCartesian3-20914bf5","./IndexDatatype-1cbc8622","./IntersectionTests-94cb8698","./Plane-042297c7","./VertexFormat-e68722dd"],(function(e,t,r,n,o,a,i,s,c,l,f,m,b,d,p,u,y,G,E,C,_){"use strict";return function(n,o){return t.defined(o)&&(n=r.EllipseGeometry.unpack(n,o)),n._center=e.Cartesian3.clone(n._center),n._ellipsoid=e.Ellipsoid.clone(n._ellipsoid),r.EllipseGeometry.createGeometry(n)}}));
