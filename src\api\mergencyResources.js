import axios from "axios";
import qs from "qs";

//获取行政区划
export const getDistrictUser = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/gemp-user/api/gemp/user/district/list/v1",
      data: data,
    });
  };

  export const getMapData = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/gemp-data/api/gemp/casc/3dMap/park/id/v1",
      data: data,
    });
  };
//救援队伍类型
export const getRescueTeamTypeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/rescueteam/type/list/v1",
    data: data,
  });
};
//救援队伍类型2
export const getRescueTeamTypeData2 = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/dict/rescueteam/type/v1",
    data: data,
  });
};
//救援列表
export const getRescueTeamListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/rescueteam/list/v1",
    data: data,
  });
};
//新增救援队伍
export const addRescueTeamListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/rescueteam/add/v1",
    data: data,
  });
};
//修改救援队伍
export const editRescueTeamListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/rescueteam/modify/v1",
    data: data,
  });
};
//删除救援队伍
export const deleteRescueTeamListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/rescueteam/delete/v1",
    data: data,
  });
};
//救援队伍导出
export const exportRescueTeamListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/rescueteam/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//专家职称
export const getExpertTypeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-dic/api/dic/code/v1",
    data: data,
  });
};
//专家领域
export const getExpertSpecialityData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/dict/expert/field/v1",
    data: data,
  });
};
//专家区域
export const getExpertDistrictData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/district/list/v1",
    data: data,
  });
};
//专家列表
export const getExpertListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/expert/list/v1",
    data: data,
  });
};
//新增专家
export const addExpertListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/expert/add/v1",
    data: data,
  });
};
//修改专家
export const editExpertListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/expert/modify/v1",
    data: data,
  });
};
//删除专家
export const deleteExpertListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/expert/delete/v1",
    data: data,
  });
};
//专家导出
export const exportexpertListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/expert/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//物资装备类型
export const getMaterialsAndEquipmentTypeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/dict/material/type/v1",
    data: data,
  });
};
//物资装备列表
export const getMaterialsAndEquipmentListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/material/list/v1",
    data: data,
  });
};
//新增物资装备
export const addMaterialsAndEquipmentListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/material/add/v1",
    data: data,
  });
};
//修改救援队伍
export const editMaterialsAndEquipmentListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/material/modify/v1",
    data: data,
  });
};
//删除救援队伍
export const deleteMaterialsAndEquipmentListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/material/delete/v1",
    data: data,
  });
};
//物资装备导出
export const exportMaterialListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-data/api/gemp/resource/material/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
//获取组织机构
export const getOrgList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/org/list/all/v1",
    data: data,
  });
};

//预案类型
export const getPlanType = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/all/type/v1",
    data: data,
  });
};
//根据父节点获取子节点预案类型列表
export const getPlanTypeChild = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/type/v1",
    data: data,
  });
};
//预案列表
export const getPlanList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/list/v1",
    data: data,
  });
};
//编制单位
export const getEstablishOrgCodeTreeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/org/trees/all/v1",
    data: data,
  });
};
//事件类型
export const getEventTypeCodData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/duty/info/event/type/tree/v1",
    data: data,
  });
};
//新增预案
export const addPlanData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/add/v1",
    data: data,
  });
};
//编辑预案
export const editPlanData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/modify/v1",
    data: data,
  });
};
//详情预案
export const detailPlanData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/id/v1",
    data: data,
  });
};
//删除预案
export const deletePlanData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/emergency/delete/v1",
    data: data,
  });
};

//数字化列表
export const getDigitalList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/findAllDigital",
    data: data,
  });
};
//数字化新增
export const addDigitalList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/add/v1",
    data: data,
  });
};
//数字化编辑
export const editDigitalList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/modify/v1",
    data: data,
  });
};
//数字化详情
export const detailDigitalList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/id/v1",
    data: data,
  });
};

//工作手册列表
export const getHandbookList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/handbook/list/v1",
    data: data,
  });
};
//工作手册新增
export const addHandbookList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/handbook/add/v1",
    data: data,
  });
};
//工作手册编辑
export const editHandbookList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/handbook/modify/v1",
    data: data,
  });
};
//工作手册详情
export const detailhandbookList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/handbook/id/v1",
    data: data,
  });
};
//工作手册删除
export const deletehandbookList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/handbook/delete/v1",
    data: data,
  });
};
//获取指挥长信息
export const commanderListGetList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/commander/list/v1",
    data: data,
  });
};
//预案指挥长新增
export const commanderAdd = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/gemp-plan/api/gemp/plan/commander/add/v1",
      data: data,
    });
  };
//保存应急小组信息
export const teamAdd = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/team/save/v1",
    data: data,
  });
};
//删除指挥长
export const commanderDeleteById = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/commander/delete/v1?id=" + data.id,
    data: data,
  });
};
//获取预案小组集合
export const teamListGet = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/team/list/v1",
    data: data,
  });
};
//小组ID删除应急小组记录
export const teamDeleteById = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/team/delete/v1?id=" + data.id,
    data: data,
  });
};
//应急单位ID删除应急单位
export const comDeleteById = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/com/delete/v1?id=" + data.id,
    data: data,
  });
};
// 保存应急单位记录
export const comAdd = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/plan/digital/com/save/v1",
    data: data,
  });
};
//查看成员信息
export const comGetById = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/gemp-plan/api/gemp/plan/digital/com/id/v1",
      data: data,
    });
};
//修改成员信息
export const comEdit = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/gemp-plan/api/gemp/plan/digital/com/modify/v1",
      data: data,
    });
};
// 获取监管端用户下编制单位、发布单位列表
export const getOrgTree = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/org/trees/superviser/v1",
    data: data,
  });
};

// 应急资源-查看所有章节 
export const getAllChapter = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/node/emergency/all/v1",
    data: data,
  });
};
// 应急资源-添加章节
export const addChapter = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: "/gemp-plan/api/gemp/node/emergency/add/v1",
      data: data,
    });
};

export const modifyChapter = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-plan/api/gemp/node/emergency/modify/v1",
    data: data,
  });
};
// 应急资源-点击章节查看章节内容
export const viewChapterCont = (data) => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json",
      },
      url: `/gemp-plan/api/gemp/node/emergency/id/v1`,
      data: data,
    });
};
// 应急资源-删除节点 
export const deleteChapter = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: `/gemp-plan/api/gemp/node/emergency/delete/v1`,
    data: data,
  });
};
