<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span @click="gotoRunningState"
              ><a-icon type="home" theme="filled" class="icon" /> 安全承诺分析
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>趋势分析</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="seach-part">
      <div class="l">
        <el-cascader
          size="mini"
          placeholder="请选择行政区划"
          :options="district"
          v-model="districtVal"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          
          :show-all-levels="true"
          style="width: 250px"
        ></el-cascader>
        <el-date-picker
          v-model="value1"
          size="mini"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="searchTime"
          unlink-panels
        >
        </el-date-picker>
        <el-button type="primary" size="mini" @click="seach">查询</el-button>
        <CA-button
          v-if="mode === '统计'"
          type="primary"
          size="mini"
          plain
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
      <!-- <el-button type="primary" size="mini">趋势分析</el-button>      -->
    </div>
    <div class="table-main">
      <div class="table-top">
        <h2 v-show="showtable">安全承诺趋势分析</h2>
        <div v-show="!showtable">
          <el-radio-group size="small" v-model="modees">
            <el-radio-button label="承诺趋势分析"></el-radio-button>
            <el-radio-button label="承诺风险变化"></el-radio-button>
            <el-radio-button
              v-if="isShowDist"
              label="平均承诺率"
            ></el-radio-button>
          </el-radio-group>
        </div>
        <!-- <el-radio-group size="small" v-if="tableData.length>0?true:false" v-model="mode">
          <el-radio-button label="图表"></el-radio-button>
          <el-radio-button label="统计"></el-radio-button>
        </el-radio-group> -->
        <CA-RadioGroup
          class="radio"
          v-model="mode"
          backgroundColor="#F1F6FF"
          border="1px solid rgba(57, 119, 234, 0.2)"
        >
          <CA-Radio
            :label="{
              src: '../../../static/img/tubiao_icon.png',
              style: 'width:15px;height:15px',
            }"
            :labelTwo="{
              src: '../../../static/img/tubiao_icon_hover.png',
              style: 'width:15px;height:15px',
            }"
            bgColorActive="#409eff"
            value="图表"
          ></CA-Radio>
          <CA-Radio
            :label="{
              src: '../../../static/img/liebiao_icon.png',
              style: 'width:15px;height:15px',
            }"
            :labelTwo="{
              src: '../../../static/img/liebiao_icon_hover.png',
              style: 'width:15px;height:15px',
            }"
            value="统计"
            bgColorActive="#409eff"
          ></CA-Radio>
        </CA-RadioGroup>
      </div>
      <div v-show="showtable">
        <div class="table">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%"
            ref="multipleTable"
            @selection-change="handleSelectionChange"
            :default-sort="{ prop: 'date', order: 'descending' }"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            @select="select"
            @select-all="select"
          >
            <el-table-column type="selection" width="50" align="center">
            </el-table-column>
            <el-table-column label="日期" align="center" prop="time">
            </el-table-column>
            <el-table-column label="行政区划" align="center" prop="distName">
            </el-table-column>
            <el-table-column
              label="应承诺企业数"
              align="center"
              prop="shouldPromiseNum"
            >
            </el-table-column>
            <el-table-column label="应承诺企业" align="center">
              <el-table-column
                label="高风险"
                align="center"
                prop="high"
              ></el-table-column>
              <el-table-column
                label="较大风险"
                align="center"
                prop="higher"
              ></el-table-column>
              <el-table-column
                label="一般风险"
                align="center"
                prop="commonly"
              ></el-table-column>
              <el-table-column
                label="低风险"
                align="center"
                prop="low"
              ></el-table-column>
            </el-table-column>
            <el-table-column label="承诺率" align="center" prop="promiseRatio">
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.promiseRatio ? row.promiseRatio : "0%" }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="未承诺企业数"
              align="center"
              prop="noPromiseNum"
            >
            <template slot-scope="{ row}">
                <span v-if="row.noPromiseNum != 0 || tableData.noPromiseNum == 1"
                        style="color: #3977ea; cursor: pointer"
                        @click="openDialog(row)">{{ row.noPromiseNum }}</span>
                  <span v-else>{{ row.noPromiseNum }}</span>


                  
                  <!-- <span @click="openDialog(row)">{{ row.noPromiseNum }}</span> -->
            </template>
           
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <!-- <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="100"
            layout="total, prev, pager, next"
            :total="1000"
            background
          >
          </el-pagination> -->
        </div>
      </div>
      <div v-show="!showtable" v-loading="echartDataLoading">
        <div v-show="showChartType == 1" id="myCharted"></div>
        <div v-show="showChartType == 2" id="myChartedes"></div>
        <div v-show="showChartType == 3" id="myCharteded"></div>
      </div>
    </div>
    <noDetailTable ref="noDetailTable"></noDetailTable>
  </div>
</template>
<script>
import {
  getSaftyEchartData,
  getCommitRiskEchartData,
  getSaftyTrendList,
  getSafetyPromiseAverageOnlineRate,
  getSafetyTrendExportExcel,
} from "@/api/workingAcc";
// import { getDistrictUser } from "@/api/entList";
import { parseTime } from "@/utils/index";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import noDetailTable from './noDetailTable'
export default {
  components: {
     noDetailTable: resolve => {
      require(['./noDetailTable.vue'], resolve)
    }
  },
  data() {
    return {
      activeName: "first",
      tableData: [],
      mode: "图表",
      modees: "承诺趋势分析",
      showChartType: 1,
      showtable: false,
      currentPage: 1,
      widthBox: 1100,
      value1: "",
      starTime: "",
      endTime: "",
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (144 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      districtVal: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      echartData1: [],
      echartData2: [],
      echartData3: [],
      echartDataLoading: false,
      loading: false,
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  methods: {
    openDialog(row) {
      this.$refs.noDetailTable.closeBoolean(true)
      let distCode = this.isShowDist ? row.distCode : null
      this.$refs.noDetailTable.getEntData(row,distCode)
      this.$refs.noDetailTable.getDistrict()
    },
    gotoRunningState() {
      let data = false;
      this.$emit("RunningState", data);
      // this.$router.go(0);
    },
    seach() {
      if (this.modees == "承诺趋势分析") {
        this.getData();
        this.getSaftyTrendListData();
      } else if (this.modees == "承诺风险变化") {
        this.getCommitRiskData();
        this.getSaftyTrendListData();
      } else if (this.modees == "平均承诺率") {
        this.getCommitRiskData();
        this.getSafetyPromiseAverageOnlineRateData();
      }
    },
    getData() {
      this.echartDataLoading = true;
      getSaftyEchartData({
        distCode: this.districtVal,
        // distCode: '',
        startTime: this.starTime,
        endTime: this.endTime,
      }).then((res) => {
        this.echartData1 = res.data.data;
        this.echartDataLoading = false;
      });
    },
    getCommitRiskData() {
      this.echartDataLoading = true;
      getCommitRiskEchartData({
        distCode: this.districtVal,
        startTime: this.starTime,
        endTime: this.endTime,
      }).then((res) => {
        this.echartData2 = res.data.data;
        this.echartDataLoading = false;
      });
    },
    getSaftyTrendListData() {
      this.loading = true;
      getSaftyTrendList({
        distCode: this.districtVal,
        startTime: this.starTime,
        endTime: this.endTime,
      }).then((res) => {
        this.loading = false;
        this.tableData = res.data.data;
      });
    },
    getSafetyPromiseAverageOnlineRateData() {
      this.echartDataLoading = true;
      getSafetyPromiseAverageOnlineRate({
        distCode: this.districtVal,
        startDate: this.starTime,
        endDate: this.endTime,
      }).then((res) => {
        this.echartData3 = res.data.data.records;
        this.echartDataLoading = false;
      });
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    getTime() {
      this.value1 = [this.get_date(-6), this.get_date(0)];
      this.starTime = this.get_date(-6);
      this.endTime = this.get_date(0);
      console.log(this.value1);
    },
    get_date(num) {
      var date = new Date(); //获取今天的时间
      var today =
        date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
      var new_date = new Date(date);
      new_date.setDate(date.getDate() + num);
      // num为正数时，获取后num天 ，为负数时，获取前num天，0表示今天。
      var new_day =
        new_date.getFullYear().toString() +
        "-" +
        (new_date.getMonth() + 1 >= 10
          ? new_date.getMonth() + 1
          : "0" + (new_date.getMonth() + 1)
        ).toString() +
        "-" +
        (new_date.getDate() >= 10
          ? new_date.getDate()
          : "0" + new_date.getDate()
        ).toString();
      return new_day;
    },
    // 导出
    exportExcel() {
      getSafetyTrendExportExcel({
        distCode: this.districtVal,
        startTime: this.starTime,
        endTime: this.endTime,
        ids: this.selection,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "安全承诺趋势列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].time;
      }
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    let date1 = new Date(this.date[0]);
    this.starTime = parseTime(date1, "{y}-{m}-{d}");
    let date2 = new Date(this.date[1]);
    this.endTime = parseTime(date2, "{y}-{m}-{d}");
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    mode(newValue, oldValue) {
      if (newValue == "统计") {
        this.showtable = true;
        this.getSaftyTrendListData();
        console.log("统计");
      } else {
        this.showtable = false;
      }
    },
    modees: {
      handler(newValue, oldValue) {
        if (newValue == "承诺趋势分析") {
          this.showChartType = 1;
          this.getData();
          this.$nextTick(() => {
            // this.getSaftyTrendListData();
            this.$setEchart("myCharted", 250, 250);
          });
          console.log("承诺趋势分析");
        } else if (newValue == "承诺风险变化") {
          this.showChartType = 2;
          this.getCommitRiskData();
          // this.getSaftyTrendListData();
          this.$setEchart("myChartedes", 250, 250);
           console.log("承诺风险变化");
        } else if (newValue == "平均承诺率") {
          this.showChartType = 3;
          this.getSafetyPromiseAverageOnlineRateData();
          // this.getSaftyTrendListData();
          this.$nextTick(() => {
            this.$setEchart("myCharteded", 250, 250);
          });
          console.log("平均承诺率");
        }
      },
      deep: true,
      // immediate: true,
    },
    echartData1(newValue, oldValue) {
      let myChart = this.$echarts.init(document.getElementById("myCharted"));
      var option;
      var xAxisData = [];
      var chengnuoNum = [];
      var chengnuoLv = [];
      var option = {
        color: ["#1890ff", "#1f9"],
        grid: {
          top: "15%",
          right: "5%",
          left: "5%",
          bottom: "10%",
        },
        tooltip: {
          trigger: "axis",
          extraCssText: "width:160px;height:75px;overflow: hidden;",
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        legend: {
          top: 5,
          left: 250,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 14,
            color: "#000",
            padding: [3, 8, 0, 2],
          },
          data: ["承诺率", "已承诺企业数"],
        },

        xAxis: [
          {
            type: "category",
            data: [],
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              
              show: true,
              color: "#000",
                            interval: 0,
              // formatter: function (value) {
              //   return value.length > 5 ? value.slice(0, 5) + "..." : value;
              // },
              rotate:40,
              textStyle: {
                fontSize: 10,
              },
              fontFamily: "LCDEF",
            },
          },
        ],
        yAxis: [
          {
            name: "已承诺企业数(家)",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            type: "value",
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#384267",
              },
            },
            axisLabel: {
              color: "#545C65",
              fontSize: "10",
            },
          },
          {
            type: "value",
            name: "承诺率 (%)",
            position: "right",
            axisLabel: {
              formatter: "{value} %",
              color: "#545C65",
            },
            max: 100,
            splitLine: {
              show: false,
            },
            axisPointer: {
              show: true,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#545C65",
              },
            },
          },
        ],
        dataZoom: [
          {
            type: "slider", //数据滑块
            start: 0,
            minSpan: 8, //5min
            // minSpan:16,   //10min
            // minSpan:24,   //15min
            // minSpan:50,   //30min
            dataBackground: {
              lineStyle: {
                // color:'#95BC2F'
              },
              areaStyle: {
                // color:'#95BC2F',
                opacity: 1,
              },
            },
            // fillerColor:'rgba(255,255,255,.6)'
          },
          {
            type: "inside", //使鼠标在图表中时滚轮可用
          },
        ],
        series: [
          {
            type: "bar",
            name: "已承诺企业数",
            barWidth: "20",
            data: [],
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
                  {
                    offset: 0,
                    color: "#42B7FD",
                  },
                  {
                    offset: 1,
                    color: "#3299FF",
                  },
                ]),
              },
            },
          },
          {
            type: "line",
            name: "承诺率",
            yAxisIndex: 1,
            itemStyle: {
              color: "#20C578",
            },
            symbolSize: 10,
            symbol: "circle",
            data: [],
          },
        ],
      };
      for (let i = 0; i < this.echartData1.length; i++) {
        xAxisData.push(this.echartData1[i].time);
        chengnuoNum.push(this.echartData1[i].enterpriseNum);
        chengnuoLv.push(this.echartData1[i].promiseRatio.replace(/[%]/g, ""));
      }
      option.xAxis[0].data = xAxisData;
      option.series[0].data = chengnuoNum;
      option.series[1].data = chengnuoLv;
      option && myChart.setOption(option);
    },
    echartData2(newValue, oldValue) {
      let myChart = this.$echarts.init(document.getElementById("myChartedes"));
      var option;
      var timeArr = [];
      var highArr = [];
      var higherArr = [];
      var commonlyArr = [];
      var lowArr = [];
      var option = {
        backgroundColor: "#fff",
        tooltip: {
          trigger: "axis",
        },
        legend: {
          color: ["#F58080", "#47D8BE", "#F9A589", "#F29362"],
          data: ["高风险企业", "较大风险企业", "一般风险企业", "低风险企业"],
          left: "center",
          textStyle: {
            color: "#333",
          },
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        grid: {
          // top: 'middle',
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          data: timeArr,
          axisLine: {
            lineStyle: {
              color: "#27C2BC",
            },
          },

          axisLabel: {
            show: true,
            textStyle: {
              color: "#333", //X轴文字颜色
              fontSize: 18,
            },
          },
        },
        dataZoom: [
          {
            type: "slider", //数据滑块
            start: 0,
            minSpan: 8, //5min
            // minSpan:16,   //10min
            // minSpan:24,   //15min
            // minSpan:50,   //30min
            dataBackground: {
              lineStyle: {
                // color:'#95BC2F'
              },
              areaStyle: {
                // color:'#95BC2F',
                opacity: 1,
              },
            },
            // fillerColor:'rgba(255,255,255,.6)'
          },
          {
            type: "inside", //使鼠标在图表中时滚轮可用
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "m³",
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#27C2BC",
              },
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#27C2BC",
              },
            },

            axisLabel: {
              show: true,
              textStyle: {
                color: "#333", //X轴文字颜色
                fontSize: 18,
              },
            },
            nameTextStyle: {
              color: "#999",
            },
            splitArea: {
              show: false,
            },
          },
          {
            name: "同比",
            splitLine: { show: false },
            axisLine: {
              lineStyle: {
                color: "#27C2BC",
              },
            },
            axisLabel: {
              formatter: "{value} ",
            },
          },
        ],
        series: [
          {
            name: "高风险企业",
            type: "line",
            data: highArr,
            color: "#F58080",
            lineStyle: {
              normal: {
                width: 5,
                color: {
                  type: "linear",

                  colorStops: [
                    {
                      offset: 0,
                      color: "#FFCAD4", // 0% 处的颜色
                    },
                    {
                      offset: 0.4,
                      color: "#F58080", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#F58080", // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
                shadowColor: "rgba(245,128,128, 0.5)",
                shadowBlur: 10,
                shadowOffsetY: 7,
              },
            },
            itemStyle: {
              normal: {
                color: "#F58080",
                borderWidth: 10,
                /*shadowColor: 'rgba(72,216,191, 0.3)',
                        shadowBlur: 100,*/
                borderColor: "#F58080",
              },
            },
            smooth: true,
          },
          {
            name: "较大风险企业",
            type: "line",
            data: higherArr,
            lineStyle: {
              normal: {
                width: 5,
                color: {
                  type: "linear",

                  colorStops: [
                    {
                      offset: 0,
                      color: "#AAF487", // 0% 处的颜色
                    },
                    {
                      offset: 0.4,
                      color: "#47D8BE", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#47D8BE", // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
                shadowColor: "rgba(71,216,190, 0.5)",
                shadowBlur: 10,
                shadowOffsetY: 7,
              },
            },
            itemStyle: {
              normal: {
                color: "#AAF487",
                borderWidth: 10,
                /*shadowColor: 'rgba(72,216,191, 0.3)',
                        shadowBlur: 100,*/
                borderColor: "#AAF487",
              },
            },
            smooth: true,
          },
          {
            name: "一般风险企业",
            type: "line",
            data: commonlyArr,
            lineStyle: {
              normal: {
                width: 5,
                color: {
                  type: "linear",

                  colorStops: [
                    {
                      offset: 0,
                      color: "#7ED1FA", // 0% 处的颜色
                    },
                    {
                      offset: 0.4,
                      color: "#316FD8", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#3FA7DC", // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
                shadowColor: "rgba(249,165,137, 0.5)",
                shadowBlur: 10,
                shadowOffsetY: 7,
              },
            },
            itemStyle: {
              normal: {
                color: "#4083DF",
                borderWidth: 10,
                /*shadowColor: 'rgba(72,216,191, 0.3)',
                        shadowBlur: 100,*/
                borderColor: "#76BFFA",
              },
            },
            smooth: true,
          },
          {
            name: "低风险企业",
            type: "line",
            data: lowArr,
            lineStyle: {
              normal: {
                width: 5,
                color: {
                  type: "linear",

                  colorStops: [
                    {
                      offset: 0,
                      color: "#F7B26F", // 0% 处的颜色
                    },
                    {
                      offset: 0.4,
                      color: "#F7AD27", // 100% 处的颜色
                    },
                    {
                      offset: 1,
                      color: "#F2A243", // 100% 处的颜色
                    },
                  ],
                  globalCoord: false, // 缺省为 false
                },
                shadowColor: "rgba(249,165,137, 0.5)",
                shadowBlur: 10,
                shadowOffsetY: 7,
              },
            },
            itemStyle: {
              normal: {
                color: "#F6D06F",
                borderWidth: 10,
                /*shadowColor: 'rgba(72,216,191, 0.3)',
                        shadowBlur: 100,*/
                borderColor: "#F6D06F",
              },
            },
            smooth: true,
          },
        ],
      };
      for (let i = 0; i < this.echartData2.length; i++) {
        timeArr.push(this.echartData2[i].time);
        highArr.push(this.echartData2[i].high ? this.echartData2[i].high : 0);
        higherArr.push(
          this.echartData2[i].higher ? this.echartData2[i].higher : 0
        );
        commonlyArr.push(
          this.echartData2[i].commonly ? this.echartData2[i].commonly : 0
        );
        lowArr.push(this.echartData2[i].low ? this.echartData2[i].low : 0);
      }
      option.xAxis.data = timeArr;
      option.series[0].data = highArr;
      option.series[1].data = higherArr;
      option.series[2].data = commonlyArr;
      option.series[3].data = lowArr;
      option && myChart.setOption(option);
    },
    echartData3(newValue, oldValue) {
      let myChart = this.$echarts.init(document.getElementById("myCharteded"));
      var option;
      var color = ["#00CA69", "#8B5CFF"];
      var xAxisData = [];
      var yAxisData1 = [];
      const hexToRgba = (hex, opacity) => {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
          )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
        }
        return rgbaColor;
      };
      var option = {
        backgroundColor: "#fff",
        color: color,
        legend: {
          top: 20,
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let html = "";
            params.forEach((v) => {
              html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      color[v.componentIndex]
                    };"></span>
                    ${v.seriesName} ${v.name}  
                    <span style="color:${
                      color[v.componentIndex]
                    };font-weight:700;font-size: 18px;margin-left:5px">${
                v.value
              }</span>
                    %`;
            });
            return html;
          },
          extraCssText:
            "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "#ffffff",
              shadowColor: "rgba(225,225,225,1)",
              shadowBlur: 5,
            },
          },
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        grid: {
          // top: 'middle',
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              formatter: function (value) {
                return value.length > 6 ? value.slice(0, 6) + "..." : value;
              },
              interval: 0,
              rotate:40,
              textStyle: {
                color: "#333",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#D9D9D9",
              },
            },
            data: xAxisData,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "平均承诺率（%）",
            axisLabel: {
              textStyle: {
                color: "#666",
              },
            },
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
              lineHeight: 40,
            },
            // 分割线
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#E9E9E9",
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            // name: "2018",
            name: "平均承诺率",
            type: "line",
            smooth: true,
            symbolSize: 8,
            zlevel: 3,
            lineStyle: {
              normal: {
                color: color[0],
                shadowBlur: 3,
                shadowColor: hexToRgba(color[0], 0.5),
                shadowOffsetY: 8,
              },
            },
            symbol: "circle", //数据交叉点样式
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: hexToRgba(color[0], 0.3),
                    },
                    {
                      offset: 1,
                      color: hexToRgba(color[0], 0.1),
                    },
                  ],
                  false
                ),
                shadowColor: hexToRgba(color[0], 0.1),
                shadowBlur: 10,
              },
            },
            data: yAxisData1,
          },
        ],
      };
      for (let i = 0; i < this.echartData3.length; i++) {
        xAxisData.push(this.echartData3[i].areaName);
        yAxisData1.push(
          this.echartData3[i].averageOnlineRate
            ? this.echartData3[i].averageOnlineRate
            : 0
        );
      }
      option.xAxis.data = xAxisData;
      option.series[0].data = yAxisData1;
      console.log(option);
      option && myChart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-bottom: 20px;
    margin-bottom: 15px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      & > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      // height: 48px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
