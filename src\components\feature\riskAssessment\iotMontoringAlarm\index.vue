<template>
  <div class="enterpriseManagement">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box" @click="goToRunning">
                <a-icon type="home" theme="filled" class="icon" /> 物联监测报警
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择地市区县"
            :options="district"
            v-model="districtVal"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            @change="handleChange"
            :show-all-levels="true"
            style="width: 130px"
            v-if="this.$store.state.login.user.user_type != 'ent'"
          ></el-cascader>

          <el-input
            v-model.trim="enterpName"
            v-if="this.$store.state.login.user.user_type != 'ent'"
            size="mini"
            placeholder="请输入企业名称"
            class="input"
            clearable
            @clear="clearKey"
            style="width: 150px"
          ></el-input>

          <el-select
            v-model="sensortypeCode"
            size="mini"
            placeholder="请选择设备类型"
            clearable
            @clear="clearSensortypeCode()"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-select
            v-model="monitemkey"
            size="mini"
            placeholder="请选择指标类型"
            clearable
            @clear="clearMonitemkey()"
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <!-- <el-select
            v-model="warningType"
            size="mini"
            placeholder="请选择报警类型"
            clearable
            @clear="clearWarningType()"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->

          <el-select
            v-model="state"
            size="mini"
            placeholder="请选择报警状态"
            clearable
            @clear="clearState()"
          >
            <el-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-date-picker
            v-model="value1"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
          ></el-date-picker>

          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>物联监测报警列表</h2>
        </div>
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              @select="select"
              @select-all="select"
            >
              <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column>
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="enterpName"
                label="单位名称"
                align="center"
                width="200"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span
                    @click="goEnt(scope.row)"
                    style="color: rgb(57, 119, 234); cursor: pointer"
                    class="enterpName"
                  >
                    {{ scope.row.enterpName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="isShowDist == true ? '行政区划' : '归属园区'"
                width="80"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="isShowDist == true">{{ row.districtName }}</span>
                  <span v-else>{{ park.parkName }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="dangerName"
                label="重大危险源名称"
                align="center"
                min-width="120"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="sensortypeCode"
                label="设备类型"
                align="center"
                width="80"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.sensortypeCode == 'G0'">储罐</span>
                  <span v-else-if="scope.row.sensortypeCode == 'P0'">装置</span>
                  <span v-else-if="scope.row.sensortypeCode == 'Q0'"
                    >泄漏点</span
                  >
                  <span v-else-if="scope.row.sensortypeCode == 'K0'">仓库</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="monitorname"
                label="设备名称"
                align="center"
                min-width="100"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="monitemkey"
                label="指标类型"
                align="center"
                width="80"
              >
              </el-table-column>
              <el-table-column
                prop="targetName"
                label="指标名称"
                align="center"
                min-width="100"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="monvalue"
                label="报警值"
                align="center"
                width="100"
              >
                <template slot-scope="scope">
                  <div
                    @mouseenter="showTip(scope.row.warningId)"
                    @mouseleave="tipData = {}"
                  >
                    <el-tooltip effect="dark" placement="right">
                      <div slot="content">
                        <div
                          v-loading="tipLoading"
                          element-loading-spinner="el-icon-loading"
                          element-loading-background="rgba(0, 0, 0, 0.8)"
                        >
                          <span>监测指标：{{ scope.row.targetName }}</span
                          ><br />
                          <span>一级上限：{{ tipData.up1 }}</span
                          ><br />
                          <span>一级下限：{{ tipData.down1 }}</span
                          ><br />
                          <span>二级上限：{{ tipData.up2 }}</span
                          ><br />
                          <span>二级下限：{{ tipData.down2 }}</span
                          ><br />
                          <span>仪表量程上限：{{ tipData.rangeUp }} </span
                          ><br />
                          <span
                            >仪表量程下限：{{ tipData.rangeDown }}
                            <!-- numToFixed --> </span
                          ><br />
                        </div>
                      </div>
                      <el-button type="text"
                        >{{ scope.row.monvalue }}
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                prop="warningType"
                label="报警类型"
                align="center"
                width="80"
              >
              </el-table-column>
              <el-table-column
                prop="warningTime"
                label="报警时间"
                align="center"
                width="155"
              >
              </el-table-column>
              <el-table-column
                prop="alarmHandelTime"
                label="消警时间"
                align="center"
                width="155"
              >
              </el-table-column>
              <el-table-column
                prop="warningDuration"
                label="报警时长"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column prop="state" label="当前状态" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.state == '0'">已消警</span>
                  <span v-else-if="scope.row.state == '1'">未消警</span>
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                width="180"
                align="center"
                fixed="right"
              >
                <template slot-scope="scope">
                  <div>
                    <el-button type="text" @click="relateVideo(scope.row)"
                      >关联视频</el-button
                    >
                    <el-button type="text" @click="generateEvent(scope.row)"
                      >生成事件信息</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="total"
              v-if="total != 0"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>

    <!-- 生成事件信息弹窗 -->
    <el-dialog
      title="生成事件信息"
      :visible.sync="eventDialogVisible"
      top="5vh"
      width="1100px"
      @close="closeEventDialog"
      v-if="eventDialogVisible"
      :close-on-click-modal="false"
    >
      <div class="dialog">
        <el-form
          :model="eventForm"
          :rules="eventRules"
          ref="eventForm"
          label-width="150px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="事件标题:" prop="title">
                <el-input
                  v-model.trim="eventForm.title"
                  maxlength="100"
                  placeholder="请输入事件标题"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item prop="address" label="事件地址:">
                  <el-input
                    v-model="eventForm.address"
                    placeholder="请输入事件地址"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事件类型:" prop="typeCode">
                <el-select
                  v-model="eventForm.typeCode"
                  placeholder="请输入事件类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事件等级:" prop="levelCode">
                <el-select
                  v-model="eventForm.levelCode"
                  placeholder="请输入事件等级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in levelList"
                    :key="item.levelCode"
                    :value="item.levelCode"
                    :label="item.levelName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="死亡人数:" prop="deathNum">
                <el-input
                  v-model.trim="eventForm.deathNum"
                  maxlength="10"
                  class="accident-form-input"
                ></el-input
                >人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受伤人数:" prop="woundNum">
                <el-input
                  v-model.trim="eventForm.woundNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事发时间:" prop="time">
                <el-date-picker
                  v-model="eventForm.time"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择事发日期"
                  style="width: 100%"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事发企业:" prop="companyName">
                <el-input
                  v-model.trim="eventForm.companyName"
                  style="width: 100%"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联重大危险源:" prop="dangerId">
                <el-select
                  v-model="eventForm.dangerId"
                  placeholder="请选择危险源"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dangerSourceList"
                    :key="item.dangerId"
                    :value="item.dangerId"
                    :label="item.dangerName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联危险化学品:" prop="chemicalsId">
                <el-select
                  v-model="eventForm.chemicalsId"
                  placeholder="请选择危险化学品"
                  style="width: 100%"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="item in hazarchemList"
                    :key="item.hazarchemId"
                    :value="item.hazarchemId"
                    :label="item.hazarchemName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度:" prop="longitude">
                <el-input
                  v-model.trim="eventForm.longitude"
                  style="width: 240px"
                  placeholder="请输入经度"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度:" prop="latitude">
                <el-input
                  v-model="eventForm.latitude"
                  style="width: 240px"
                  placeholder="请输入纬度"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="map_height">
                <el-form-item label="地图定位">
                  <egisMap
                    :islistener="false"
                    ref="detailMap"
                    :datas="eventForm"
                    style="height: 200px"
                    @mapCallback="mapcallback"
                  ></egisMap>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="详情描述:" prop="description">
                <el-input
                  v-model.trim="eventForm.description"
                  type="textarea"
                  :rows="5"
                  placeholder="2000字以内"
                  maxlength="2000"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="上传附件:">
                <AttachmentUpload
                  :attachmentlist="eventForm.file"
                  :limit="1"
                  type="office"
                  v-bind="{}"
                >
                </AttachmentUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeEventDialog">取 消</el-button>
          <el-button type="primary" @click="saveEventData">提 交</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 查看关联视频弹窗 -->
    <el-dialog
      title="查看关联视频"
      :visible.sync="videoDialogVisible"
      width="70%"
      @close="closeVideoDialog"
      :close-on-click-modal="false"
      class="video-dialog"
    >
      <div class="video-container">
        <div class="video-list">
          <h3>{{ currentHazard?.dangerName }}</h3>
          <div class="video-list-content" v-loading="videoLoading">
            <ul>
              <li
                v-for="(item, index) in videoList"
                :key="index"
                :class="{
                  active: selectedVideo && selectedVideo.id === item.id,
                }"
                @click="selectVideo(item)"
              >
                <span>{{ item.deviceName || "视频" + (index + 1) }}</span>
              </li>
              <li
                v-if="videoList.length === 0 && !videoLoading"
                class="no-data"
              >
                暂无关联视频
              </li>
            </ul>
          </div>
        </div>
        <div class="video-player">
          <!-- 四路视频播放器 -->
          <div class="player-container">
            <multiVideoPlayer ref="multiVideoPlayer" />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getIotMontoringData,
  getIotMontoringExportExcel,
} from "@/api/riskAssessment";
import { getMonitoringMonvalue } from "@/api/entList";
import { parseTime } from "@/utils/index";
import {
  getAccidentTypeListData,
  addAccidentDuty,
} from "@/api/accidentManagement";
import { getDangerList } from "@/api/workingAcc";
import { getSelectData, getHazarchemList } from "@/api/entList";
import { findCamerainfoList } from "@/api/sensor";
import { getVideoPreviewUrl } from "@/api/riskAssessment";
import { createNamespacedHelpers } from "vuex";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import multiVideoPlayer from "@/components/feature/riskAssessment/videoOnlineMonitoring/multiVideoPlayer.vue";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: { AttachmentUpload, multiVideoPlayer },
  data() {
    return {
      districtVal: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      enterpName: "",
      value1: "",
      sensortypeCode: "",
      monitemkey: "",
      warningType: "",
      state: this.$route.query ? this.$route.query.state : "",
      startTime: "",
      endTime: "",
      selection: [],
      loading: true,
      areaName: "",
      // 事件信息相关数据
      eventDialogVisible: false,
      typeList: [], // 事件类型列表
      levelList: [], // 事件等级列表
      dangerSourceList: [], // 危险源列表
      hazarchemList: [], // 危险化学品列表
      eventForm: {
        title: "", // 事件标题
        address: "", // 事件地址
        typeCode: "", // 事件类型
        levelCode: "", // 事件等级
        deathNum: "", // 死亡人数
        woundNum: "", // 受伤人数
        time: "", // 事发时间
        file: [],
        companyName: "", // 事发企业
        companyId: "", // 事发企业ID
        dangerId: "", // 关联危险源
        chemicalsId: [], // 关联危险化学品（多选）
        longitude: "", // 经度
        latitude: "", // 纬度
        description: "", // 详情描述
      },
      eventRules: {
        title: [{ required: true, message: "请输入事件标题", trigger: "blur" }],
        address: [
          { required: true, message: "请输入事件地址", trigger: "blur" },
        ],
        typeCode: [
          { required: true, message: "请选择事件类型", trigger: "change" },
        ],
        levelCode: [
          { required: true, message: "请选择事件等级", trigger: "change" },
        ],
        time: [
          { required: true, message: "请选择事发时间", trigger: "change" },
        ],
        description: [
          { required: true, message: "请填写事件描述", trigger: "blur" },
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      options: [
        {
          value: "G0",
          label: "储罐",
        },
        {
          value: "Q0",
          label: "泄漏点",
        },
        {
          value: "P0",
          label: "装置",
        },
        {
          value: "K0",
          label: "仓库",
        },
      ],
      options1: [
        {
          value: "WD",
          label: "温度",
        },
        {
          value: "YL",
          label: "压力",
        },
        {
          value: "YW",
          label: "液位",
        },
        {
          value: "YDQT",
          label: "有毒气体",
        },
        {
          value: "KRQT",
          label: "可燃气体",
        },
        {
          value: "LL",
          label: "流量",
        },
      ],
      options2: [
        {
          value: "1",
          label: "高报",
        },
        {
          value: "2",
          label: "高高报",
        },
        {
          value: "3",
          label: "低报",
        },
        {
          value: "4",
          label: "低低报",
        },
      ],
      options3: [
        {
          value: "1",
          label: "未消警",
        },
        {
          value: "0",
          label: "已消警",
        },
      ],
      tableData: [],
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      size: 10,
      total: "",
      tipData: {},
      tipLoading: false,

      // 视频相关数据
      videoDialogVisible: false,
      videoList: [],
      videoLoading: false,
      selectedVideo: null,
      currentHazard: null,
    };
  },
  filters: {
    numToFixed(val) {
      if (val) {
        return parseInt(val).toFixed(2);
      } else {
        return val;
      }
    },
  },
  methods: {
    showTip(id) {
      this.tipLoading = true;
      getMonitoringMonvalue(id).then((res) => {
        this.tipLoading = false;
        this.tipData = res.data.data;
      });
    },
    //物联监测报警列表
    getIotMontoringDataList() {
      this.loading = true;
      var dto = {
        current: this.currentPage,
        distCode: this.distCode,
        size: this.size,
        sensortypeCode: this.sensortypeCode,
        monitemkey: this.monitemkey,
        warningType: this.warningType,
        state: this.state,
        enterpName: this.enterpName,
        startTime: this.startTime,
        endTime: this.endTime,
      };

      console.log(this.$store.state, "取名称名称");
      console.log(this.$store.state.login.user.org_name, "企业名称");
      if (this.$store.state.login.user.user_type === "ent") {
        dto.distCode = this.$store.state.login.enterData.districtCode;
        dto.enterpName = this.$store.state.login.user.org_name;
      } else {
        dto.distCode = this.distCode;
        dto.enterpName = this.enterpName;
      }

      getIotMontoringData(dto).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.startTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.startTime = "";
        this.endTime = "";
      }
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpId);
    },
    search() {
      this.currentPage = 1;
      this.getIotMontoringDataList();
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getIotMontoringDataList();
    },
    clearKey() {
      this.enterpName = "";
    },
    clearSensortypeCode() {
      this.sensortypeCode = "";
    },
    clearmonitemkey() {
      this.monitemkey = "";
    },
    clearwarningType() {
      this.warningType = "";
    },
    clearState() {
      this.state = "";
    },
    // 导出
    exportExcel() {
      //   let list = [this.distCode, ...this.selection];
      getIotMontoringExportExcel({
        // level: this.level.toString(),
        // distCode: this.distCode,
        // distCodeList: list.length <= 1 ? null : list,
        // isContainParent: true,
        // current: this.currentPage,
        ids: this.selection.length <= 0 ? null : this.selection.join(","),
        distCode: this.distCode,
        // size: this.size,
        sensortypeCode: this.sensortypeCode,
        monitemkey: this.monitemkey,
        warningType: this.warningType,
        state: this.state,
        enterpName:
          this.$store.state.login.user.user_type === "ent"
            ? this.$store.state.login.user.org_name
            : this.enterpName,
        startTime: this.startTime,
        endTime: this.endTime,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "物联监测报警" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId;
      }
    },
    goToRunning() {
      this.$router.push({ path: `/riskAssessment/iotMontoringAlarm` });
    },
    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
      this.$refs.DetailTable.getDistrict();
    },
    openDialoges(distCode, type, areaName) {
      this.$refs.DetailTablees.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTablees.getEntData(distCode, type, areaName);
      this.$refs.DetailTablees.getDistrict();
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },

    // 查看关联视频
    relateVideo(row) {
      this.videoDialogVisible = true;
      this.currentHazard = row;
      this.getRelatedVideos(row.dangerId, row.enterpId);
    },

    // 获取关联视频列表
    getRelatedVideos(companyHazardId, orgCode) {
      this.videoLoading = true;
      this.videoList = [];
      this.selectedVideo = null;

      findCamerainfoList({
        companyHazardId: companyHazardId,
        orgCode: orgCode,
      })
        .then((res) => {
          this.videoLoading = false;
          if (res.data.status === 200) {
            this.videoList = res.data.data || [];
            // 如果有视频，默认选中第一个
            if (this.videoList.length > 0) {
              this.selectVideo(this.videoList[0]);
            }
          } else {
            this.$message.error(res.data.msg || "获取视频列表失败");
          }
        })
        .catch(() => {
          this.videoLoading = false;
          this.$message.error("获取视频列表失败");
        });
    },

    // 选择视频
    selectVideo(video) {
      this.selectedVideo = video;

      // 播放视频
      this.playVideo(video.id);
    },

    // 播放视频
    async playVideo(channelId) {
      try {
        // 使用新的视频预览接口
        const res = await getVideoPreviewUrl({
          channelId: channelId,
          schema: 5, // 使用默认的流媒体协议
          subType: 1, // 实时预览
        });

        if (res.status === 200 && res.data?.data?.url) {
          const videoSource = {
            id: channelId,
            name: `摄像头-${channelId}`,
            url: res.data.data.url,
            channelId: channelId,
            expireTime: res.data.data.expireTime,
            schema: res.data.data.schema,
            status: "online",
          };

          // 添加到四路视频播放器
          this.$nextTick(() => {
            if (
              this.$refs.multiVideoPlayer &&
              typeof this.$refs.multiVideoPlayer.addVideoToChannel ===
                "function"
            ) {
              const channelIndex =
                this.$refs.multiVideoPlayer.addVideoToChannel(videoSource);
              if (channelIndex >= 0) {
                console.log(`视频已添加到通道 ${channelIndex + 1}`);
              } else {
                console.error("添加视频到多路播放器失败");
              }
            } else {
              console.error("多路播放器组件未准备好");
            }
          });
        } else {
          this.$message.error(
            "获取视频流失败：" + (res.data?.msg || "未知错误")
          );
        }
      } catch (error) {
        console.error("播放视频失败", error);
        this.$message.error("播放视频失败：" + (error.message || "未知错误"));
      }
    },

    // 关闭视频弹窗
    closeVideoDialog() {
      // 关闭所有视频播放
      if (this.$refs.multiVideoPlayer) {
        this.$refs.multiVideoPlayer.closeAllVideos();
      }

      this.videoDialogVisible = false;
      this.videoList = [];
      this.selectedVideo = null;
    },

    // 生成事件信息
    generateEvent(row) {
      // 初始化事件相关数据
      this.getEventRelatedData(row.enterpId);

      // 初始化表单
      this.resetEventForm();
      // 填充报警相关信息
      this.eventForm.address = row.enterpAddress || "";
      this.eventForm.companyName = row.enterpName || "";
      this.eventForm.companyId = row.enterpId || "";
      this.eventForm.dangerId = row.dangerId || "";
      // 如果有经纬度信息
      if (row.enterpLatitude && row.enterpLongitude) {
        this.eventForm.longitude = row.enterpLongitude;
        this.eventForm.latitude = row.enterpLatitude;
      }
      this.eventDialogVisible = true;
    },

    // 重置事件表单
    resetEventForm() {
      this.eventForm = {
        title: "",
        address: "",
        typeCode: "",
        levelCode: "",
        deathNum: "",
        woundNum: "",
        time: "",
        companyName: "",
        companyId: "",
        dangerId: "",
        chemicalsId: [], // 初始化为空数组，支持多选
        longitude: "",
        latitude: "",
        description: "",
      };
    },
    // 地图定位
    mapcallback(data) {
      // 标点赋值
      this.eventForm.longitude = data.location.lon.toFixed(6);
      this.eventForm.latitude = data.location.lat.toFixed(6);
      this.eventForm.address = data.formatted_address;
      if (data.addressComponent.county_code != "") {
      }
      //districtName
      this.eventForm.districtCode = data.addressComponent.county_code.slice(
        3,
        data.addressComponent.county_code.length
      );
    },
    // 关闭事件弹窗
    closeEventDialog() {
      this.eventDialogVisible = false;
      this.resetEventForm();
    },

    // 保存事件数据
    saveEventData() {
      this.$refs.eventForm.validate((valid) => {
        if (valid) {
          // 构建保存数据
          const saveData = {
            ...this.eventForm,
            // 添加其他必要字段
            districtCode: this.$store.state.login.userDistCode,
          };
          addAccidentDuty(saveData)
            .then((res) => {
              if (res.data.status === 200) {
                this.$message.success("事件信息保存成功");
                this.closeEventDialog();
              } else {
                this.$message.error(res.data.msg || "保存失败");
              }
            })
            .catch(() => {
              this.$message.error("保存失败，请稍后重试");
            });
        } else {
          return false;
        }
      });
    },

    // 获取事件相关数据
    getEventRelatedData(enterpriseId) {
      // 获取事件类型和等级列表
      getAccidentTypeListData().then((res) => {
        if (res.data.status === 200) {
          this.typeList = res.data.data.typeCode || [];
          this.levelList = res.data.data.accidentLevel || [];
        }
      });

      getSelectData({
        enterpId: enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.dangerSourceList = res.data.data;
        }
      });

      // 获取危险化学品列表
      getHazarchemList(enterpriseId).then((res) => {
        if (res.data.code == 0) {
          this.hazarchemList = res.data.data;
        }
      });
    },
  },
  // 生命周期 - 创建完成
  created() {
    // 组件创建完成
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getIotMontoringDataList();
    // 初始化事件相关数据
    this.getEventRelatedData();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
    ...createNamespacedHelpers({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
      district: (state) => state.controler.district,
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.dialog {
  height: 70vh;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-right: 10px;

  .dialog-footer {
    display: flex;
    justify-content: center;

    & > * {
      margin: 0 10px;
    }
  }
}
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: space-between;
      & > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
/deep/ .l .el-input {
  width: 130px;
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}

/* 事件表单样式 */
.accident-form-input {
  width: calc(100% - 30px);
}

.dialog {
  padding: 0 20px;
}

.dialog-footer {
  text-align: center;
  margin-top: 20px;
  margin-bottom: 20px;
}

/* 视频弹窗样式 */
.video-dialog .el-dialog__body {
  padding: 0;
}

.video-container {
  display: flex;
  height: 600px;
}

.video-list {
  width: 250px;
  border-right: 1px solid #e6e6e6;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.video-list h3 {
  padding: 15px;
  margin: 0;
  border-bottom: 1px solid #e6e6e6;
  background-color: #f5f7fa;
}

.video-list-content {
  flex: 1;
  overflow-y: auto;
}

.video-list-content ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.video-list-content li {
  padding: 12px 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.video-list-content li:hover {
  background-color: #f5f7fa;
}

.video-list-content li.active {
  background-color: #ecf5ff;
  color: #409eff;
}

.video-list-content li.no-data {
  text-align: center;
  color: #909399;
  cursor: default;
}

.video-player {
  flex: 1;
  background-color: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
}

.player-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.player-placeholder {
  text-align: center;
  color: #909399;
}

.player-placeholder h3 {
  margin-bottom: 10px;
  color: #fff;
}

.player-placeholder p {
  font-size: 14px;
}
</style>
