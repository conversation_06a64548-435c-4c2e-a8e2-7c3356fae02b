<template>
  <div class="notification">
    <el-dialog
      :title="title + '设备异常申报信息'"
      :visible.sync="show"
      width="1000px"
      @close="closeBoolean()"
      top="10vh"
      :close-on-click-modal="false"
      v-dialog-drag
    >
      <div class="div1">
        <div class="table">
          <el-scrollbar style="height: 600px">
            <ul class="container">
              <li class="lang">
                <div class="l">企业名称</div>
                <div class="r">{{ entInfoDataes.enterpName }}</div>
              </li>
              <li class="lang">
                <div class="l">统一社会信用代码</div>
                <div class="r">{{ entInfoDataes.entcreditCode }}</div>
              </li>
              <li class="lang">
                <div class="l"><span style="color: red">*</span>报备类型</div>
                <div class="r">
                  <el-select
                    v-model="type"
                    size="mini"
                    placeholder="请选择报备类型"
                    clearable
                    @change="changeType"
                  >
                    <el-option
                      v-for="item in ReportTypeOpt"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </div>
              </li>
              <li class="lang" v-if="type == '0'">
                <div class="l">
                  <span style="color: red">*</span>所属重大危险源
                </div>
                <div class="r">
                  <el-select
                    v-model="dangerIds"
                    size="mini"
                    placeholder="请选择所属重大危险源"
                    clearable
                    @clear="clearSensortypeCode()"
                    @change="changeDangerId"
                  >
                    <el-option
                      v-for="item in options"
                      :key="item.dangerId"
                      :label="item.dangerName"
                      :value="item.dangerId"
                    >
                    </el-option>
                  </el-select>
                </div>
              </li>
              <li class="lang" v-if="showEquipment" :key="type">
                <div class="l"><span style="color: red">*</span>设备名称</div>
                <div class="r">
                  <el-select
                    v-model="equipmentCode"
                    multiple
                    size="mini"
                    placeholder="请选择设备名称"
                    clearable
                    @clear="clearSensortypeCode()"
                  >
                    <el-option
                      v-for="item in options1"
                      :key="item.equipmentCode"
                      :label="item.equipmentName"
                      :value="item.equipmentName"
                    >
                    </el-option>
                  </el-select>
                </div>
              </li>
              <li class="lang">
                <div class="l"><span style="color: red">*</span>报备原因</div>
                <div class="r">
                  <el-select
                    v-model="reasonId"
                    size="mini"
                    placeholder="请选择报备原因"
                    clearable
                    @clear="clearSensortypeCode()"
                  >
                    <el-option
                      v-for="item in options2"
                      :key="item.id"
                      :label="item.name"
                      :value="item.id"
                    >
                    </el-option>
                  </el-select>
                </div>
              </li>
              <li class="lang">
                <div class="l"><span style="color: red">*</span>报备说明</div>
                <div class="r">
                  <el-input
                    name=""
                    cols="5"
                    type="textarea"
                    resize="none"
                    maxlength="500"
                    class="textarea"
                    step
                    :autosize="{ minRows: 2, maxRows: 12 }"
                    id=""
                    placeholder="请输入申报说明"
                    v-model.trim="text"
                  ></el-input>
                  <div class="textLength">
                    {{ "还能输入：" + textLength + "个字" }}
                  </div>
                </div>
              </li>
              <li class="lang">
                <div class="l">
                  <span style="color: red">*</span>状态开始时间
                </div>
                <div class="r">
                  <el-date-picker
                    v-model="value1"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptionsStart"
                    type="date"
                    placeholder="选择状态开始日期"
                  >
                  </el-date-picker>
                  <!-- {{value11}} -->
                  <!-- minTime: value1
                        ? value1 == getNowData()
                          ? new Date().getHours() + ':00'+ ':00'
                          : ''
                        : '00:00:00', -->
                  <el-time-picker
                    v-model="value11"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      start: '00:00:00',
                      step: '01:00:00',
                      end: '23:00:00',
                      
                    }"
                    placeholder="选择状态开始时间"
                  >
                  </el-time-picker>
                </div>
              </li>
              <li class="lang">
                <div class="l">
                  <span style="color: red">*</span>状态结束时间
                </div>
                <div class="r">
                  <el-date-picker
                    v-model="value2"
                    value-format="yyyy-MM-dd"
                    :picker-options="pickerOptionsEnd"
                    type="date"
                    placeholder="选择状态结束日期"
                  >
                  </el-date-picker>

<!-- {{value22}} -->
 <!-- minTime: value1 == value2 ? value11 : '', -->
                  <el-time-picker
                    v-model="value22"
                    value-format="HH:mm:ss"
                    :picker-options="{
                      start: '00:00',
                      step: '01:00',
                      end: '23:00',
                     
                    }"
                    placeholder="选择状态结束时间"
                  >
                  </el-time-picker>
                </div>
              </li>
              <li class="lang bottom">
                <div class="l">附件</div>
                <div class="r" style="position: relative">
                  <el-upload
                    class="upload-demo"
                    action=""
                    :before-remove="beforeRemove"
                    :on-exceed="handleExceed"
                    :file-list="fileList"
                    list-type="picture"
                    accept=".jpg,.jpeg,.png,.gif"
                    :limit="1"
                    :http-request="beforeUpload"
                    :before-upload="newBefore"
                  >
                    <span style="color: rgb(57, 119, 234)">点击上传审批</span>
                    <div slot="tip" class="el-upload__tip">
                      只能上传jpg/jpeg/png/gif文件，且不超过2M
                    </div>
                  </el-upload>

                  <!-- <img :src="img" style="width: 60px; left: 230px; top: 8px" /> -->
                </div>
              </li>
              <!-- <li class="lang bottom">
              <div class="l">通报时间</div>
              <div class="r">{{time}}</div>
            </li> -->
            </ul>
          </el-scrollbar>

          <div class="bottomFooter">
            <el-button class="submit" size="medium" @click="closeBoolean()"
              >取消</el-button
            >
            <el-button
              class="submit"
              size="medium"
              type="primary"
              @click="submit()"
              >提交</el-button
            >
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDengerListData,
  getEquipmentData,
  getReasonData,
  uploadApi,
  getEnt,
  addOrEdit,
  getAbnormalDetail,
  getVideoData,
} from "@/api/dailySafety";
export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  props: ["entInfoData"],
  data() {
    return {
      title: "",
      show: false,
      title: "",
      checkList: [],
      text: "",
      textLength: 500,
      value1: "",
      value2: "",
      // value11: new Date().getHours() + 1 + ":00",
      value11:'',
      value22: "",
      options: [],
      type: "",
      dangerIds: "-1",
      equipmentCode: ["全部的设备设施"],
      fileList: [],
      options1: [],
      showEquipment: false,
      reasonId: "",
      options2: [
        {
          id: "0",
          name: "离线",
        },
        {
          id: "1",
          name: "停产",
        },
      ],
      img: "",
      entInfoDataes: {},
      enterpid: "",
      type: "",
      id: "",
      ReportTypeOpt: [
        {
          value: "0",
          label: "设备设施",
        },
        {
          value: "1",
          label: "视频报备",
        },
      ],
      detailData: "",
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.value2;
          if (endDateVal) {
            return time.getTime() > new Date(endDateVal).getTime();
          } else {
            return time.getTime() < Date.now() - 24 * 60 * 60 * 1000;
          }
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.value1;
          if (beginDateVal) {
            return time.getTime() < new Date(beginDateVal).getTime() - 8.64e7;
          }
        },
      },
    };
  },
  //方法集合
  methods: {
    clearSensortypeCode() {},
    //获取当天的年月日
    getNowData() {
      var datetime = new Date();
      var year = datetime.getFullYear();
      var month =
        datetime.getMonth() + 1 < 10
          ? "0" + (datetime.getMonth() + 1)
          : datetime.getMonth() + 1;
      var date =
        datetime.getDate() < 10 ? "0" + datetime.getDate() : datetime.getDate();
      return year + "-" + month + "-" + date;
    },
    closeBoolean(val) {
      this.show = val;
      if (!val) {
        this.dangerIds = "-1";
        this.type = "";
        this.equipmentCode = ["全部的设备设施"];
        this.reasonId = "";
        this.text = "";
        this.value1 = "";
        this.value2 = "";
        this.value11 = "";
        this.value22 = "";
        this.img = "";
        this.showEquipment = false;
        this.fileList = [];
      }
    },
    submit() {
      if (!this.type) {
        this.$message({
          type: "info",
          message: "请填写报备类型！",
        });
        return;
      } else if (this.equipmentCode.length === 0) {
        this.$message({
          type: "info",
          message: "请填写设备名称！",
        });
        return;
      } else if (!this.text) {
        this.$message({
          type: "info",
          message: "请填写报备说明！",
        });
        return;
      } else if (!this.reasonId) {
        this.$message({
          type: "info",
          message: "请选择报备原因！",
        });
        return;
      } else if (!this.value1) {
        this.$message({
          type: "info",
          message: "请选择状态开始时间！",
        });
        return;
      } else if (!this.value2 && !this.value22) {
        this.$message({
          type: "info",
          message: "请选择状态结束日期和状态结束时间",
        });
        return;
      } else if (!this.value2) {
        if (this.value22) {
          this.$message({
            type: "info",
            message: "请选择状态结束日期！",
          });
          return;
        }
      } else if (!this.value22) {
        if (this.value2) {
          this.$message({
            type: "info",
            message: "请选择状态结束时间！",
          });
          return;
        }
      } else if (this.dangerIds != "-1") {
        if (this.equipmentCode.length == 0) {
          this.$message({
            type: "info",
            message: "请选择设备！",
          });
          return;
        }
      }
      addOrEdit({
        enterpid: this.enterpid,
        // enterpid: '420110098',
        dangerid: this.dangerIds,
        type: this.type,
        declareExplain: this.text,
        declareReason: this.reasonId,
        enclosure: this.img,
        fileList: this.fileList,
        endTime:
          this.value1 && this.value11 ? this.value1 + " " + this.value11 : null,
        equipment: this.equipmentCode.join(",") || "",
        startTime:
          this.value2 && this.value22 ? this.value2 + " " + this.value22 : null,
        type: this.type,
        id: this.id,
      }).then((res) => {
        if (res.data.code == 0) {
          this.$message({
            type: "success",
            message: "保存成功",
          });
          this.fileList = [];
          this.show = false;
          this.$nextTick(() => {
            this.$parent.getIotMontoringDataList(this.enterpid);
          });
        } else {
          this.$message({
            type: "error",
            message: res.data.msg,
          });
        }
      });
    },
    getData(entInfoData, id, type, dangerId, title) {
      debugger;
      this.title = title?title:'';
      this.entInfoDataes = entInfoData;
      this.enterpid = entInfoData.enterpId;
      this.type = type;
      this.id = id;
      if (type == "0") {
        this.showEquipment = true;
        this.changeDangerId(dangerId);
      } else if (type == "1") {
        // this.options1 = [];
        let arr = [];
        this.showEquipment = true;
        getVideoData({
          enterpid: this.enterpid,
        }).then((res) => {
          arr = res.data.data;
          if (arr) {
            arr.forEach((item) => {
              item.equipmentName = item.name;
              item.equipmentCode = item.id;
            });
          }
          this.options1 = arr;
        });
      }
      if (id) {
        // if (type == "0") {
        //   this.title = "编辑设备异常申报信息";
        // } else if (type == "1") {
        //   this.title = "编辑视频异常申报信息";
        // }
        getAbnormalDetail({
          id: id,
        }).then((res) => {
          // console.log(res);
          this.detailData = res.data.data;
          this.dangerIds = res.data.data.dangerid;
          this.equipmentCode = res.data.data.equipment.split(",");
          this.reasonId = res.data.data.declareReason;
          this.text = res.data.data.declareExplain;
          this.value1 = res.data.data.endTime.split(" ")[0] || "";
          this.value11 = res.data.data.endTime.split(" ")[1] || "";
          if (!res.data.data.enclosure) {
            this.fileList = [];
          } else {
            this.fileList = [
              {
                // name: "附件4.png",
                url: res.data.data.enclosure,
              },
            ];
          }

          // console.log(res.data.data);
          if (res.data.data.startTime) {
            this.value2 = res.data.data.startTime.split(" ")[0] || "";
            this.value22 = res.data.data.startTime.split(" ")[1] || "";
          } else {
            this.value2 = "";
            this.value22 = "";
          }

          // if (res.data.data.startTime) {

          // } else {

          // }

          this.img = res.data.data.enclosure;
          this.type = res.data.data.type;
        });
      } else {
        if (type == "0") {
          this.title = "新增设备异常报备信息";
        } else if (type == "1") {
          this.title = "新增视频异常报备信息";
        }
      }
      getDengerListData({
        enterpid: this.entInfoDataes.enterpId,
        // enterpid: '420110098'
      }).then((res) => {
        this.options = res.data.data;
      });

      //   getReasonData({}).then((res) => {
      //     this.options2 = res.data.data;
      //   });
    },
    changeDangerId(val) {
      this.equipmentCode = ["全部的设备设施"];
      if (val) {
        if (val == "-1") {
          this.showEquipment = false;
        } else {
          this.showEquipment = true;
          getEquipmentData({
            dangerid: val,
          }).then((res) => {
            this.options1 = res.data.data;
          });
        }
      }
    },
    changeType(val) {
      this.equipmentCode = ["全部的设备设施"];
      if (val == "0") {
        this.showEquipment = false;
        this.options2 = [
          {
            id: "0",
            name: "离线",
          },
          {
            id: "1",
            name: "停产",
          },
        ];
        this.dangerIds = "-1";
      } else {
        this.showEquipment = true;
        this.options2 = [
          {
            id: "0",
            name: "离线",
          },
        ];
        this.dangerIds = "";
        let arr = [];
        getVideoData({
          enterpid: this.enterpid,
        }).then((res) => {
          arr = res.data.data;
          if (arr) {
            arr.forEach((item) => {
              item.equipmentName = item.name;
              item.equipmentCode = item.id;
            });
          }
          this.options1 = arr;
          //   console.log(arr)
        });
      }
    },
    newBefore(file){
      const isJPG = true;
      const isLt2M = file.size / 1024 / 1024 < 2;
      var testmsg = file.name.substring(
        file.name.lastIndexOf(".") + 1
      );
      const extension1 = testmsg === "jpg";
      const extension2 = testmsg === "jpeg";
      const extension3 = testmsg === "png";
      const extension4 = testmsg === "gif";
      if (!isLt2M) {
        this.$message.error("上传的图片大小不能超过 2MB!");
        return false;
      } else if (!extension1 && !extension2 && !extension3 && !extension4) {
        this.$message.error("请上传jpg 、png、jpeg、gif 类型图片!");
        return false;
      } else { 
      }
    },
    beforeUpload(file) {
      // accept=".jpg,.jpeg,.png,.gif,.JPG,.JPEG,.PNG,.GIF,"
      // const isJPG = true;
      // const isLt2M = file.file.size / 1024 / 1024 < 2;
      // var testmsg = file.file.name.substring(
      //   file.file.name.lastIndexOf(".") + 1
      // );
      // const extension1 = testmsg === "jpg";
      // const extension2 = testmsg === "jpeg";
      // const extension3 = testmsg === "png";
      // const extension4 = testmsg === "gif";
      // if (!isLt2M) {
      //   this.$message.error("上传的图片大小不能超过 2MB!");
      //   return false;
      // } else if (!extension1 && !extension2 && !extension3 && !extension4) {
      //   this.$message.error("请上传jpg 、png、jpeg、gif 类型图片!");
      //   return false;
      // } else { 
      // }



       let formData = new FormData();
        formData.append("files", file.file);
        uploadApi(formData).then((res) => {
          if (res.data.code == 0) {
            this.img = res.data.data[0].filePath;
            let imgArr = {
              name: res.data.data[0].name,
              url: res.data.data[0].filePath,
            };
            this.fileList[0] = imgArr;
          }
        });
      
      // return isJPG && isLt2M;
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制只能上传1个文件`);
    },
    beforeRemove(file, fileList) {
      this.img = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {
    text: {
      handler(newVal, oldVal) {
        this.textLength = 500 - newVal.length;
      },
      deep: true,
      immediate: true,
    },
    equipmentCode(val, oldval) {
      let newindex = val.indexOf("全部的设备设施"),
        oldindex = oldval.indexOf("全部的设备设施");
      if (newindex != "-1" && oldindex == "-1" && val.length > 1) {
        this.equipmentCode = ["全部的设备设施"];
      } else if (newindex != "-1" && oldindex != "-1" && val.length > 1) {
        this.equipmentCode.splice(val.indexOf("全部的设备设施"), 1);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
/deep/ .el-scrollbar__wrap {
  overflow-x: auto;
  // height: calc(100% + 20px); //多出来的20px是横向滚动条默认的样式
}
.notification {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    // overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        // float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .bottomFooter {
        display: flex;
        justify-content: center;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
/deep/ .r .el-select {
  width: 80%;
}
/deep/ .m .el-select {
  width: 100%;
}
</style>