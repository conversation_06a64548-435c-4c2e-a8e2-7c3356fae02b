<template>
  <div class="warn" ref="warn">
    <el-dialog
      title="
警示通报详情"
      :visible.sync="show"
      width="1000px"
      @close="closeBoolean()"
      :close-on-click-modal="false"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="监管通报" name="1"></el-tab-pane>
        <el-tab-pane label="企业反馈" name="0"></el-tab-pane>
      </el-tabs>
      <div :style="{ height: height }" v-loading="loading">
        <div class="null" v-if="tableData == null"></div>

        <div v-else class="div1">
          <div class="table" v-if="activeName == '1'">
            <ul class="container">
              <li class="lang">
                <div class="l">接收单位</div>
                <div class="r">{{ tableData.receivingUnit }}</div>
              </li>
              <li class="lang">
                <div class="l">报告标题</div>
                <div class="r">{{ tableData.title }}</div>
              </li>
              <li class="lang">
                <div class="l">报告内容</div>
                <div class="r" style="position: relative; padding:10px 80px 10px 10px">
                  {{ tableData.notificationContent }}
                  <div style="color:yellow" v-if="preData.yellowWarningTime" class="toFenXianStyle" @click="toFenXian"><img style="width:80px;position:absolute;left: 0;" src="/static/img/assets/img/huang.gif" />风险动态</div>
                <div style="color:orange" v-if="preData.orangeWarningTime" class="toFenXianStyle" @click="toFenXian"><img style="width:80px;position:absolute;left: 0;" src="/static/img/assets/img/cheng.gif" />风险动态</div>               
                <div v-if="preData.redWarningTime" class="toFenXianStyle" @click="toFenXian"><img style="width:80px;position:absolute;left: 0;" src="/static/img/assets/img/hong.gif" />风险动态</div>
                </div>
              </li>
              <li class="lang">
                <div class="l">发文单位</div>
                <div class="r">{{ tableData.sendingUnit }}</div>
              </li>
              <li class="lang bottom">
                <div class="l">报告时间</div>
                <div class="r">
                  {{tableData.createTime?
                    new Date(tableData.createTime).Format(
                      "yy年MM月dd日 hh时mm分ss秒"
                    ):"--"
                  }}
              
                </div>
              </li>
            </ul>
          </div>
          <div class="table" v-else>
            <ul class="container">
              <li class="lang">
                <div class="l">报告标题</div>
                <div class="r">{{ tableData.title }}</div>
              </li>
              <li class="lang">
                <div class="l">报告编号</div>
                <div class="r">{{ tableData.feedbackCode }}</div>
              </li>
              <li class="lang">
                <div class="l">接收单位</div>
                <div class="r">{{ tableData.receivingUnit }}</div>
              </li>
              <li class="lang">
                <div class="l">原因说明</div>
                <div class="r">
                  {{ tableData.reasonDescription }}
                </div>
              </li>
              <li class="lang">
                <div class="l">处置措施</div>
                <div class="r">
                  {{ tableData.notificationContent }}
                </div>
              </li>
              <li class="lang">
                <div class="l">上报单位</div>
                <div class="r">
                  {{ companyName || "--" }}
                </div>
              </li>
              <li class="lang bottom">
                <div class="l">报告时间</div>
                <div class="r">
                   {{tableData.creatTime?
                    new Date(tableData.creatTime).Format(
                      "yy年MM月dd日 hh时mm分ss秒"
                    ):"--"
                  }}
                </div>
              </li>
            </ul>
          </div>
          <div class="pagination" v-if="activeName == '1'">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="1"
              layout="total, prev, pager, next"
              background
              :total="total"
              :pager-count="5"
            >
              <!-- :small="true" -->
            </el-pagination>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  postCimEarlyWarningWarningDetails,
  getWarningNoticeList,
  getCimEarlyWarningFeedBackInfo,
} from "@/api/riskAssessment";
export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  data() {
    return {
      show: false,
      tableData: {},
      activeName: "1",
      currentPage: 1,
      loading: false,
      height: "",
      total: 0,
      preData:{},
      companyName: "",
    };
  }, 
  //方法集合
  methods: {
    toFenXian(){
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", this.companyCode);
    },
    closeBoolean(val) {
      this.show = val;
      this.activeName = "1";
      this.tableData = {};
      this.currentPage = 1;
    },
    // submit() {},
    getData(id, companyCode, companyName) {
      this.warningId = id;
      this.companyCode = companyCode;
      this.companyName = companyName?companyName:this.companyName;
      this.loading = true;
      getWarningNoticeList({
        warningId: this.warningId,
        size: 1,
        current: this.currentPage,
      }).then((res) => {
        // console.log(res);
        if (res.data.data.records.length > 0) {
          this.tableData = res.data.data.records[0];
          this.total = res.data.data.total;
          this.height = "auto";
        } else {
          this.tableData = null;
          this.height = "350px";
        }
        // console.log(this.tableData);
        this.loading = false;
      });
    },
    handleCurrentChange(val) {
      this.getData(this.warningId, this.companyCode, this.companyName);
    },
    handleClick() {
      this.loading = true;
      if (this.activeName == 0) {
        getCimEarlyWarningFeedBackInfo(this.warningId).then((res) => {
          if (res.data.data) {
            this.tableData = res.data.data;
            this.total = 0;
            this.height = "auto";
          } else {
            this.tableData = null;
            this.height = "350px";
          }
          // console.log(this.tableData);
          this.loading = false;
        });
      }else{
        this.getData(this.warningId, this.companyCode)  
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.toFenXianStyle{
    text-align: center;
    width: 80px;
    height: 30px;
    line-height: 30px;
    border-radius: 5px;
    // border: 1px solid red;
    color: red;
    position: absolute;
    bottom: 30px;
    right: 6px;
    cursor: pointer;
    top: 50%;
    margin-top: -15px;
}
/deep/ .el-dialog__body {
  padding-top: 10px;
}
.warn {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .null {
    width: 467px;
    height: 285px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
  .pagination {
    float: right;
  }
}
</style>