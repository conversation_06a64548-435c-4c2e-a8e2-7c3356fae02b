import Vue from "vue";
//导入element-ui

import Scrollbar from "element-ui";
import {
  Autocomplete,
  Popover,
  Loading,
  Tooltip,
  Badge,
  Dialog,
  Button,
  Tabs,
  TabPane,
  Cascader,
  Select,
  Option,
  Input,
  Table,
  TableColumn,
  DatePicker,
  Pagination,
  Form,
  FormItem,
  Radio,
  RadioButton,
  RadioGroup,
  Card,
  Tree,
  Row,
  Col,
} from "element-ui";
import "element-ui/lib/theme-chalk/index.css";

const components: any = [
  Autocomplete,
  Popover,
  // Loading,
  Tooltip,
  Badge,
  Dialog,
  Button,
  Tabs,
  TabPane,
  Cascader,
  Select,
  Option,
  Input,
  Table,
  TableColumn,
  DatePicker,
  Pagination,
  Form,
  FormItem,
  Radio,
  RadioButton,
  RadioGroup,
  Card,
  Tree,
  Row,
  Col,
];
Vue.use(Scrollbar);
Vue.use(Loading.directive);
export default function ElementUI(): void {
  for (const component of components) {
    Vue.component(component.name, component);
  }
}
