<template>
  <div class="side" v-loading="loading">
    <a-collapse
      :bordered="false"
      :expand-icon-position="expandIconPosition"
      :destroyInactivePanel="true"
      :accordion="true"
      @change="getMenuList"
      :defaultActiveKey="defaultActiveKey"
      :activeKey="activeKey"
    >
      <!-- <template #expandIcon="props">
        <a-icon type="right" :rotate="props.isActive ? 90 : 0" />
      </template> -->
      <a-collapse-panel
        v-for="item in systemAll"
        :key="item.systemCode+','+item.id"
        :header="item.systemName"
        class="customStyle"
      >
        <!-- <el-input
          style="margin-bottom: 8px"
          placeholder="请输入搜索内容"
          v-model="searchVal"
          @input="search"
        /> -->
        <a-directory-tree
          multiple
          @select="onSelect"
          v-loading="loadingTree"
          :autoExpandParent="false"
        >
          <a-tree-node
            :title="item_menuList.privName"
            v-for="item_menuList in item.list"
            :key="item_menuList.id + ',' + item_menuList.systemCode"
          >
            <a-tree-node
              :title="item_menuList_children.privName"
              is-leaf
              v-for="item_menuList_children in item_menuList.children"
              :key="
                item_menuList_children.id +
                ',' +
                item_menuList_children.systemCode +
                ','
              "
            />
          </a-tree-node>
        </a-directory-tree>

        <!-- <a-directory-tree
          multiple
          default-expand-all
          @select="onSelect"
          v-loading="loadingTree"
          :autoExpandParent="false"
          v-else
        >
          <a-tree-node
            :title="item_menuList.menuName"
            v-for="item_menuList in menuListBySysCodeBak"
            :key="item_menuList.menuId + ',' + item_menuList.systemCode + ','"
          >
            <a-tree-node
              :title="item_menuList_children.menuName"
              is-leaf
              v-for="item_menuList_children in item_menuList.children"
              :key="
                item_menuList_children.menuId +
                ',' +
                item_menuList_children.systemCode +
                ','+','+','
              "
            />
          </a-tree-node>
        </a-directory-tree> -->
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script>
import { getprivsData } from "../../../api/jurisdiction";
import { mapState, mapGetters, mapActions, mapMutations } from "vuex";
import Bus from "../../../utils/bus"
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      expandIconPosition: "right",
      systemAll: [],
      menuListBySysCode: [],
      loading: false,
      loadingTree: false,
      defaultActiveKey: "1",
      activeKey: "1",
      searchVal: "",
      searchData: [],
      // menuListBool: true,
      menuListBySysCodeBak: [],
    };
  },
  //方法集合
  methods: {
    onSelect(keys, event) {
      console.log(keys)
      let menuId = keys[0].split(",")[0];
      let systemCode = keys[0].split(",")[1];
      this.$store.commit("sa/updateSAPrivListData", { menuId, systemCode });
      Bus.$emit('SAPrivListData', { menuId, systemCode })
    },
    getList() {
      this.loading = true;
      getprivsData("-1").then((res) => {
        this.systemAll = res.data.data;
        this.activeKey = [res.data.data[0].systemCode+','+res.data.data[0].id];
        let systemCode=this.systemAll[0].systemCode;
        let menuId="";
        Bus.$emit('SAPrivListData', { menuId, systemCode })
        this.loading = false;
      });
    },
    getMenuList(keys) {
      if (keys != undefined) {
        // console.log(keys);
        //每次请求初始化this.menuListBySysCode
        this.menuListBySysCode = [];
        //每次请求加载动画
        // this.loadingTree = true;
        let systemCode = keys.split(',')[0];
        // let menuId = keys.split(',')[1];
        let menuId = "";
        this.$store.commit("sa/updateSAPrivListData", { menuId, systemCode });
        Bus.$emit('SAPrivListData', { menuId, systemCode })
        // getprivsData({ systemCode: systemCode })
        //   .then((res) => {
        //     // this.searchData = res.data.data; //搜索用的
        //     // this.dataTOTree(res.data.data, this.menuListBySysCode);
        //     this.menuListBySysCode = res.data.data
        //     this.loadingTree = false;
        //   })
        //   .catch((e) => {
        //     console.log(e, "请求错误");
        //   });
      }
    },
    dataTOTree(data, val) {
      let map = {};
      data.forEach((item) => {
        map[item.privId] = item;
      });
      data.forEach((item) => {
        let parent = map[item.parentId];
        if (parent) {
          (parent.children || (parent.children = [])).push(item);
        } else {
          val.push(item);
        }
      });
    },
    onExpand() {},
    onChange() {},
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getList();
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
.side {
  width: 280px;
  background-color: #fff;
  overflow: auto;
  height: 89vh;
  .customStyle {
    font-size: 16px;
    background-color: #fff;
  }
}
</style>

<style>
    .ant-collapse-borderless > .ant-collapse-item > .ant-collapse-content {
    min-height: 200px;
    max-height: 250px;
    overflow-y: scroll;
  }
</style>