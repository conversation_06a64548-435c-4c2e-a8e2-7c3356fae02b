<template>
  <div class="riskManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" />风险管理
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- <full-calendar ref="fullCalendar" style="height: 100%" :options="calendarOptions"></full-calendar> -->

    <div class="top_bg">
      <span>风险单元信息表</span>
    </div>

    <!-- <fullcalendarNew></fullcalendarNew> -->

    <div class="top_suo">
      <div class="top_suo_l" @click="popScoreFn">
        风险单元频率指标：<span class="num">{{ topData.indexG }}</span
        ><span class="icon"></span>
      </div>
      <div class="top_suo_r">
        风险单元和事故风险点：<span>{{ topData.riskUnintCount }}</span
        >个风险单元， <span>{{ topData.riskUnitPointCount }}</span
        >个事故风险点
      </div>
    </div>

    <div
      :class="['riskItemBox', 'riskItemBox' + el.riskUnitRank]"
      v-for="(el, index) of riskItemData"
      :key="index"
    >
      <div class="itemLeft">
        <div class="unitLeft">{{ el.riskUnitName }}</div>
        <div class="unitRight">
          <p>{{ el.riskLevelName }}</p>
          <span>{{ el.riskUnitValue }}</span>
        </div>
        <div class="rightBg">风险单元</div>
      </div>
      <div class="itemCenter">
        <div class="centerZhongDa">
          <div class="centerZhongDaCon" @click="clickUrl">
            <!-- @click="zhongDaFn" -->
            <div class="centerZhongDaL">
              <p>一般</p>
              <span :class="[el.generalDanger?'red':'']" >{{ el.generalDanger }}</span>
            </div>
            <div class="centerZhongDaR">
              <p>重大</p>

              
              <label v-if='el.majorDanger == 1'></label>
              <span v-else></span>

            </div>
          </div>
          <p>重大事故隐患</p>
        </div>

        <div class="specialBox specialItem">
          <div v-if="topData.indexI1 == 0">
            <span></span>
            <p>特殊时期修正</p>
          </div>

          <div v-else @click="amendmentFn" class="amendmentHover">
            <label></label>
            <p>特殊时期修正</p>
          </div>
        </div>
        <div class="InternetBox specialItem">
          <div v-if="topData.indexI2 == 0">
            <span></span>
            <p>物联网指标修正</p>
          </div>
          <div v-else @click="InternetDetailFn" class="amendmentHover">
            <label></label>
            <p>物联网指标修正</p>
          </div>
        </div>
        <div class="naturalBox specialItem">
          <div v-if="topData.indexI3 == 0">
            <span></span>
            <p>自然灾害修正</p>
          </div>

          <div v-else @click="naturalDetailDataFn" class="amendmentHover">
            <label></label>
            <p>自然灾害修正</p>
          </div>
        </div>
      </div>
      <div class="itemRight">
        <div class="fengXd">
          <span>事故风险点</span>
        </div>
        <div class="fengList">
          <div class="riskPoinConfigure" @click="riskPoinConfigureFn(el)">
            配置
          </div>
          <div
            class="list_con"
            v-for="(item, k) of el.cimRiskUnitPointDTOList"
            :key="k"
            @click="handleClickFeng(item, el.riskUnitName)"
          >
            <span class="iconSquer"></span>
            <p>{{ item.riskPointName }}</p>
            <span class="iconArrow"></span>
          </div>
        </div>
      </div>
    </div>

    <popRiskpoint ref="popRiskpoint"></popRiskpoint>

    <!-- 特殊时期修正 -->
    <enterSpecial ref="enterSpecial"></enterSpecial>
    <InternetDetail ref="InternetDetail"></InternetDetail>
    <naturalDetail ref="naturalDetail"></naturalDetail>
    <popZhongDa ref="popZhongDa"></popZhongDa>

    <popScore ref="popScore"></popScore>
    <!-- 配置 -->
    <riskPoinConfigure
      ref="riskPoinConfigure"
      @refash="refashFn"
    ></riskPoinConfigure>
  </div>
</template>
<script>
import popRiskpoint from "@/components/feature/enterpriseManagement/riskManagement/popRiskpoint.vue";
import enterSpecial from "@/components/feature/enterpriseManagement/riskManagement/popAll/enterSpecial.vue";
//
import naturalDetail from "@/components/feature/enterpriseManagement/riskManagement/popAll/naturalDetail.vue";
import InternetDetail from "@/components/feature/enterpriseManagement/riskManagement/popAll/InternetDetail.vue";
import riskPoinConfigure from "@/components/feature/enterpriseManagement/riskManagement/popAll/riskPoinConfigure.vue";

import popZhongDa from "./popZhongDa.vue";
import popScore from "./popScore.vue";
import fullcalendarNew from "./fullcalendarNew.vue";
import {
  queryRiskCompanyInfo, //
  findPeriodList,
} from "@/api/riskAssessment";
import {
  getAccidentRecordList,
  naturalDisasterPageList,
} from "@/api/accidentManagement";
// import FullCalendar from "@fullcalendar/vue";
// import dayGridPlugin from "@fullcalendar/daygrid";
// import interactionPlugin from "@fullcalendar/interaction";
var dayjs = require("dayjs");
export default {
  components: {
    popRiskpoint,
    popZhongDa,
    popScore,
    fullcalendarNew,
    enterSpecial,
    riskPoinConfigure,
    InternetDetail,
    naturalDetail,
  },
  data() {
    return {
      clickShuang:"",
      topData: {},
      getInternetData: [],
      enterSpecialData: [],
      enterSpecialData2: [],
      naturalDetailData: [],
      riskItemData: [
        {
          name: "氯化工艺",
          leveName: "重大风险",
          level: "1",
          num: "122.1",
          list: [{ name: "中毒事故风险点" }, { name: "火灾、爆炸事故风险点" }],
        },
      ],
    };
  },
  watch: {},
  methods: {
    clickUrl(){
      console.log(this.clickShuang,'双控系统')
      // window.open(this.clickShuang)
      window.open('https://zhpt.hbsis.gov.cn:9033/ZHPT_LOGIN_SERVER//login?service=https://scyf.hbsis.gov.cn:8088/login-cas&utype=ent1')
    },
    riskPoinConfigureFn(item) {
      this.$refs["riskPoinConfigure"].getData(true, item);
    },
    refashFn() {
      this.getData();
    },
    getData() {
      this.loading = true;
      queryRiskCompanyInfo({
        enterId: this.$store.state.login.enterData.enterpId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          this.topData = res.data.data;
          this.riskItemData = res.data.data.cimRiskUnitDTOList;
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
    popScoreFn() {
      this.$nextTick(() => {
        this.$refs.popScore.isScore = true;
        this.$refs.popScore.scoreData = this.topData;
      });
    },
    zhongDaFn() {
      this.$refs.popZhongDa.iszhongDa = true;
    },
    //特殊时期修正
    amendmentFn() {
      this.$nextTick(() => {
        this.$refs["enterSpecial"].getData(true, 1);
        this.$refs["enterSpecial"].getData(true, 0);
      });
    },
    InternetDetailFn() {
      this.$nextTick(() => {
        this.$refs["InternetDetail"].getData(true);
        this.$refs["InternetDetail"].getaccidentTypeList();
      });
    },
    naturalDetailDataFn() {
      this.$nextTick(() => {
        this.$refs["naturalDetail"].getData(true);
        this.$refs["naturalDetail"].getaccidentTypeList();
      });
    },
    enterSpecialFn(type) {
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        draftFlag: "1",
        endTime: dayjs().format("YYYY-MM-DD"),
        id: "",
        keywords: "",
        nowPage: 0,
        pageSize: 0,
        periodType: type,
        startTime: dayjs().format("YYYY-MM-DD"),
      };

      findPeriodList(params).then((res) => {
        if (res.data.status === 200) {
          if (type == 1) {
            this.enterSpecialData = res.data.data;
          } else {
            this.enterSpecialData2 = res.data.data;
          }
        } else {
        }
      });
    },

    handleClick(val) {},
    handleClickFeng(item, pre) {
      item.preRiskUnitName = pre;
      this.$nextTick(() => {
        this.$refs.popRiskpoint.ishandleClick = true;
        this.$refs.popRiskpoint.parentData = item;
        this.$refs.popRiskpoint.getHS();
        this.$refs.popRiskpoint.getK1();
        this.$refs.popRiskpoint.getE();
        this.$refs.popRiskpoint.getM();
        this.$refs.popRiskpoint.initData();
        this.$refs.popRiskpoint.getK2();
        this.$refs.popRiskpoint.getK3();
      });
    },
    //物联网指标修正
    getInternetFn() {
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        accidentLevel: "",
        accidentType: "",
        draftFlag: "1",
        endTime: dayjs().format("YYYY-MM-DD") + " 23:59:59",
        id: "",
        keywords: "",
        nowPage: 1,
        pageSize: 10,
        startTime: dayjs().format("YYYY-MM-DD") + " 00:00:00",
      };
      getAccidentRecordList(params).then((res) => {
        if (res.data.status === 200) {
          this.getInternetData = res.data.data.list;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },

    getNatrualList() {
      var params = {
        districtCode: this.$store.state.login.userDistCode,
        disasterLevel: "",
        disasterType: "",
        draftFlag: "1",
        endTime: dayjs().format("YYYY-MM-DD") + " 23:59:59",
        id: "",
        keywords: "",
        nowPage: 1,
        pageSize: 10,
        startTime: dayjs().format("YYYY-MM-DD") + " 00:00:00",
      };
      naturalDisasterPageList(params).then((res) => {
        if (res.data.status === 200) {
          this.naturalDetailData = res.data.data.list;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
  },
  mounted() {
    this.getData();
    var urlData=this.$store.state.controler.urlData
    urlData.forEach(el=>{
      if(el.name=="双控系统"){
        this.clickShuang=el.url
      }
    })
    // this.enterSpecialFn(0);
    // this.enterSpecialFn(1);
    // this.getInternetFn();
    // this.getNatrualList()
    // this.calendarApi = this.$refs.fullCalendar.getApi();
  },
  create() {},
};
</script>
<style scoped lang="scss">
.red{
  color:red;
}
.amendmentHover {
  cursor: pointer;
  span {
    cursor: pointer;
  }
  label {
    cursor: pointer;
  }
}
.amendmentHover:hover {
  color: #f06844;
}
.iszhongDaCon {
  text-align: center;
}
.amendmentCon {
  text-align: center;
  img {
    width: 100%;
  }
}
.riskPoinConfigure {
  cursor: pointer;
  font-weight: bold;
  margin: 0 0 5px 0;
}
.riskPoinConfigure:hover {
  color: blue;
}

.riskManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  .top_bg {
    background: url("../../../../../static/img/riskManagementIcon/title_bg.png");
    background-size: 100%;
    height: 70px;
    text-align: center;
    line-height: 70px;
    span {
      color: #fff;
      font-size: 40px;
      font-weight: bold;
    }
  }
  .top_suo {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    padding: 15px;
    background: #fff7f7;
    border: 1px solid #ffadad;
    margin: 15px 0;
    .top_suo_l {
      cursor: pointer;
      .num {
        color: #326eff;
        font-size: 24px;
        font-weight: bold;
        cursor: pointer;
      }
      .icon {
        background: url("../../../../../static/img/riskManagementIcon/edit.png")
          no-repeat left center;
        display: inline-block;
        width: 16px;
        height: 16px;
        margin: 0 0 0 10px;
      }
    }
    .top_suo_r {
      span {
        color: #326eff;
      }
    }
  }
  .riskItemBox {
    padding: 25px;
    border: 1px solid #d9d9d9;
    display: flex;
    justify-content: space-between;
    margin: 0 0 15px 0;
    .itemLeft {
      width: 415px;
      display: flex;
      justify-content: space-between;
      background-color: #fff;
      align-items: center;
      background: url("../../../../../static/img/riskManagementIcon/unit_bg.png")
        no-repeat left center;
      padding: 0 0 0 15px;
      height: 136px;
      .unitLeft {
        width: 200px;
        font-size: 22px;
        color: #333333;
      }
      .unitRight {
        text-align: right;
      }
      // .unitRight p {
      //   margin: 0;
      //   color: #f06844;
      // }
      // .unitRight span {
      //   font-size: 30px;
      //   font-weight: bold;
      //   color: #f06844;
      // }
      .rightBg {
        background: url("../../../../../static/img/riskManagementIcon/rightBg.png")
          no-repeat left center;
        width: 50px;
        text-align: center;
        font-size: 18px;
        color: #fff;
        writing-mode: vertical-lr; /*从左向右 从右向左是 writing-mode: vertical-rl;*/
        height: 100%;
        padding: 0 10px;
        letter-spacing: 5px;
      }
    }
    .itemCenter {
      display: flex;
      height: 136px;
      .centerZhongDa {
        border: 1px solid #d9d9d9;
        width: 200px;
        height: 100%;
        text-align: center;
        .centerZhongDaCon {
          display: flex;
          text-align: center;
          align-items: center;
          padding: 20px 0 15px 0;
          .centerZhongDaL {
            width: 50%;
            border-right: 1px solid #d9d9d9;
            cursor: pointer;
            p {
              margin: 0;
            }
            span {
              display: block;
              width: 50px;
              height: 50px;
              border-radius: 50%;
              border: 1px solid #d9d9d9;
              margin: 0 auto;
              text-align: center;
              line-height: 50px;
            }
          }
          .centerZhongDaR {
            width: 50%;
            p {
              margin: 0;
            }
            span {
              display: block;
              width: 50px;
              height: 50px;
              background: url("../../../../../static/img/riskManagementIcon/normal.png")
                no-repeat center center;
              margin: 0 auto;
            }
            label {
              display: block;
              width: 50px;
              height: 50px;
              // background: url("../../../../../static/img/riskManagementIcon/up.png")
              //   no-repeat center center;
              margin: auto;
               background: url("../../../../../static/img/riskManagementIcon/big.png")
      no-repeat center center;
            }
          }
        }
      }
      .specialItem {
        > div {
          width: 136px;
          height: 136px;
          width: 136px;
          height: 136px;
          border: 1px solid #d9d9d9;
          margin: 0 0 0 15px;
          text-align: center;
          // cursor: pointer;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
        }
        label {
          display: block;
          width: 50px;
          height: 50px;
          background: url("../../../../../static/img/riskManagementIcon/up.png")
            no-repeat center center;
          margin: 0 auto;
          // width:100%
        }
        span {
          display: block;
          width: 50px;
          height: 50px;
          background: url("../../../../../static/img/riskManagementIcon/normal.png")
            no-repeat center center;
          margin: 0 auto;
          //  width:100%
        }
        p {
          width: 100%;
        }
      }
    }
    .itemRight {
      display: flex;
      height: 136px;
      .fengXd {
        background: url("../../../../../static/img/riskManagementIcon/fengXd.png")
          no-repeat left center;
        width: 50px;
        text-align: center;
        font-size: 18px;
        color: #fff;
        -webkit-writing-mode: vertical-lr;
        -ms-writing-mode: tb-lr;
        writing-mode: vertical-lr;
        height: 100%;
        padding: 0 10px;
        margin-right: 15px;
        letter-spacing: 5px;
      }
      .fengList {
        border: 1px solid #d9d9d9;
        width: 410px;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        padding: 20px 20px 20px 30px;
        .list_con {
          position: relative;
          width: 100%;
          cursor: pointer;
          p {
            margin: 0;
          }

          .iconArrow {
            background: url("../../../../../static/img/箭头.png") no-repeat
              center center;
            width: 12px;
            height: 10px;
            position: absolute;
            right: 0;
            top: 3px;
          }
        }
        .list_con:hover {
          color: #326eff;
        }
        .list_con:hover .iconArrow {
          background: url("../../../../../static/img/riskManagementIcon/arrow_hover.png")
            no-repeat center center;
        }
      }
    }
  }

  .riskItemBox .iconSquer {
    position: absolute;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #ccc;
    top: 50%;
    margin-top: -3px;
    left: -10px;
    background: #feb60f;
  }

  .unitRight p {
    margin: 0;
    color: #f06844;
  }
  .unitRight span {
    font-size: 30px;
    font-weight: bold;
    color: #f06844;
  }
  .riskItemBox2 .unitRight p,
  .riskItemBox2 .unitRight span {
    color: #ff9d47;
  }
  .riskItemBox3 .unitRight p,
  .riskItemBox3 .unitRight span {
    color: #feb60f;
  }
  .riskItemBox4 .unitRight p,
  .riskItemBox4 .unitRight span {
    color: #5d93fd;
  }
  .riskItemBox1
    .itemCenter
    .centerZhongDa
    .centerZhongDaCon
    .centerZhongDaR
    span {
    // background: url("../../../../../static/img/riskManagementIcon/big.png")
    //   no-repeat center center;
  }
}
.riskH {
  // max-height:calc(100% - 500px);
  max-height: 700px;
  overflow: auto;
  text-align: center;
}
</style>