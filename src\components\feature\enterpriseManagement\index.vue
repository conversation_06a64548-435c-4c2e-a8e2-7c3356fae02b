<template>
  <div class="entManagement">
    <component
      :is="$route.name === 'entManagement' ? 'entManagement' : $route.name"
    ></component>
  </div>
</template>

<script>
// import entManagement from "./entAndGov.vue";
// import mailList from "./mailList";
// import mergencyResources from "./mergencyResources";

export default {
  //import引入的组件
  name: "enterpriseManagement",
  components: {
    entManagement: () => import("./entAndGov.vue"),
    mailList: () => import("./mailList"),
    mergencyResources: () => import("./mergencyResources"),
    parkManagement: () => import("./parkAndGov.vue"),
    riskManagement: () => import("./riskManagement/riskManagement.vue"), //风险管理
    gatherReport: () =>
      import("../workingAccount/smartReport/gatherReport/index.vue"), //采集报表
    portaitConfiguration: () =>
      import("../enterprisePortrait/portaitConfiguration/index.vue"), //配置报表
    videoOnlineMonitoring: () =>
      import("../riskAssessment/videoOnlineMonitoring/index.vue"), //视频在线监测
  },
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
