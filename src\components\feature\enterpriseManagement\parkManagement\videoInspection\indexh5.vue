<template>
  <div class="videoInspection" ref="videoInspection" id="videoInspection">
    <div class="video-left">
      <div class="videoLeft-top">
        <h2>设备监控列表</h2>
        <div class="video-list" v-loading="loading">
          <div class="video-item">
            <p
              v-for="(item, index) in videoData"
              :key="index"
              @click="
                videoCheck === true ? getVideoId(item.monitornum, index) : null
              "
              :class="active == index ? 'activeP' : ''"
              :style="
                videoCheck === true ? 'cursor: pointer;' : 'cursor: not-allowed'
              "
            >
              <i class="video-camera"></i>
              {{ item.monitorname }}
              <span class="zaixian" v-if="item.onlinestatus == 1">在线</span>
              <span class="lixian" v-else-if="item.onlinestatus == 0"
                >离线</span
              >
            </p>
            <img
              v-if="videoData.length == 0"
              style="
                margin: 0 auto;
                display: block;
                margin-top: 30px;
                width: 70%;
              "
              src="/static/img/assets/img/noData.png"
            />
            <!-- v-if="videoData.length == 0" -->
          </div>
        </div>
      </div>
    </div>
    <div class="video-right">
      <!-- <el-popover
        placement="right"
        trigger="click"
        width="560"
        v-model="visible"
      >
        <div class="videoBtn" style="">
          <span>选择回放时间：</span>
          <el-date-picker
            v-model="value2"
            type="datetimerange"
            :picker-options="pickerOptions"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            align="right"
            size="mini"
          >
          </el-date-picker>
        </div>
        <el-button type="" size="small" slot="reference">回放</el-button>
      </el-popover> -->
      <el-radio-group
        size="small"
        v-model="sign"
        style="position: absolute; right: 0"
      >
        <el-radio-button label="0"
          >全部({{ videoNumData.total }})</el-radio-button
        >
        <el-radio-button label="1"
          >在线({{ videoNumData.onlineNum }})</el-radio-button
        >
      </el-radio-group>
      <div class="video-box" id="video-box">
        <!-- <div id="playWind"></div> -->
        <div id="ws-real-player"></div>
        <!-- <div class="items" id="playWind_items" style="display: none;">
          <div class="items_button">
            <div class="button" @click="stop()">停止预览</div>
            <div class="button" @click="StopRealPlayAll()">关闭所有预览</div>
            <div class="button" @click="CapturePicture('JPEG')">抓图</div>
            <div class="button" @click="fullSreen()">全屏</div>
          </div>

          <div class="fenping_box">
            <img
              src="../../../../../static/img/fenping1.png"
              class="fenping"
              @click="arrangeWindow(1)"
            />
            <img
              src="../../../../../static/img/fenping2.png"
              class="fenping"
              @click="arrangeWindow(2)"
            />
            <img
              src="../../../../../static/img/fenping3.png"
              class="fenping"
              @click="arrangeWindow(3)"
            />
            <img
              src="../../../../../static/img/fenping4.png"
              class="fenping"
              @click="arrangeWindow(4)"
            />
          </div>
        </div> -->
      </div>
    </div>
  </div>
</template>

<script>
import {
  getVideoList,
  getVideoNumData,
  getVideoH5VideoH5,
} from "@/api/entList";
import { getRealmonitorNew } from "@/api/riskAssessment";
import WSPlayer from "@/utils/WSPlayer/WSPlayer";
import ICC from "@/utils/icc";
export default {
  //import引入的组件
  name: "videoInspection",
  components: {},
  props: ["showVideo"],
  data() {
    return {
      //声明公用变量
      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
      videoModuleData: {},
      enterpriseId: "",
      sign: "0",
      videoData: [],
      cameraIndexCode: "",
      videoNumData: {},
      active: 0,
      visible: true,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      value2: "",
      iWind: 0,
      MaxIWind: 0,
      oPlugin: "",
      previewUrl: "",
      splitScreenValue: "",
      videoCheck: true,
      resizeObserver: null,
      nowtime: new Date().getTime(),
      selectedKeys: "",
      iWndIndex: [],
      loading: false,
      realPlayer: null,
    };
  },
  //方法集合
  methods: {
    getVideo() {
      this.videoCheck = false;
      getVideoH5VideoH5(this.cameraIndexCode).then((res) => {
        this.previewUrl = res.data.data + "?transcode=1&bitrate=2048";
        this.realplay(this.iWind);
        if (this.MaxIWind > this.iWind) {
          this.selectWnd(++this.iWind);
        } else {
          this.selectWnd(this.iWind);
        }
      });
    },
    selectWnd(iWind) {
      this.oPlugin.JS_SelectWnd(iWind).then(
        function () {
          console.log("JS_SelectWnd success");
        },
        function () {
          console.log("JS_SelectWnd failed");
        }
      );
    },
    //加载动画
    canvasAnimation(elName, wd, ht) {
      // console.log(elName, wd, ht);
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(255, 204, 0,1)";
        ctx.lineWidth = 3;
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        if (deg1 < 360) {
          if (deg1 < 180) {
            index *= 1.08;
          } else {
            index /= 1.08;
          }
          deg1 += index;
        }
        if (deg1 >= 360) {
          deg1 = 0;
        }
        ctx.stroke();
        ctx.beginPath();
        if (flag) {
          opa -= 0.02;
          if (opa < 0.2) {
            flag = false;
          }
        } else {
          opa += 0.02;
          if (opa > 1) {
            flag = true;
          }
        }
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgba(255, 204, 0," + opa + ")";
        ctx.fillText("正在取流...", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }
      window.requestAnimationFrame(arc);
    },
    //错误动画
    canvasError(elName, wd, ht) {
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("取流失败", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    //初始动画
    canvasDef(elName, wd, ht) {
      var deg1 = 0;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    initPlugin() {
      const THIS = this;
      this.oPlugin.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {
          //插件选中窗口回调
          THIS.iWind = iWndIndex;
          // console.log(iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
          //插件错误回调
          console.error(
            `window-${iWndIndex}, errorCode: ${iErrorCode}`,
            oError
          );
          let canvasEl = document.getElementById(
            `canvas_animation${iWndIndex}`
          );
          let wd = canvasEl.getAttribute("width");
          let ht = canvasEl.getAttribute("height");
          THIS.canvasError(`canvas_animation${iWndIndex}`, wd, ht);
          THIS.iWndIndex[iWndIndex] = false;
          // THIS.$message.error("取流异常");
        },
        windowEventOver: function (iWndIndex) {
          //鼠标移过回调
          //console.log(iWndIndex);

          // console.log(THIS.iWndIndex);
          if (THIS.iWndIndex[iWndIndex]) {
            $("#stopBtn" + iWndIndex).show();
            $("#stopBtn" + iWndIndex)
              .children()
              .eq(0)
              .click(() => {
                THIS.stopBtn(iWndIndex);
                THIS.iWndIndex[iWndIndex] = null;
              });
          }
        },
        windowEventOut: function (iWndIndex) {
          //鼠标移出回调
          //console.log(iWndIndex);

          $("#stopBtn" + iWndIndex).hide();
        },
        windowEventUp: function (iWndIndex) {
          //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {
          //全屏切换回调
          console.log(bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
          //首帧显示回调
          console.log(iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {
          //性能不足回调
        },
      });
      this.oPlugin
        .JS_SetOptions({
          bSupportSound: false, //是否支持音频，默认支持
          bSupporDoubleClickFull: false, //是否双击窗口全屏，默认支持
          // bOnlySupportMSE: true  //只支持MSE
          // bOnlySupportJSDecoder: true  //只支持JSDecoder
        })
        .then(function () {
          console.log("JS_SetOptions");
        });
    },

    getVersion() {
      this.oPlugin.JS_GetPluginVersion().then(function (szVersion) {
        console.log(szVersion);
      });
    },
    Destroy() {
      this.oPlugin.JS_DestroyWorker().then(function () {
        console.log("destroyWorker success");
      });
    },
    SetSecretKey() {
      var secretKey = document.getElementById("secretKey").value;
      this.oPlugin.JS_SetSecretKey(iWind, secretKey).then(
        function () {
          console.log("JS_SetSecretKey success");
        },
        function () {
          console.log("JS_SetSecretKey failed");
        }
      );
    },

    realplay(iWind) {
      this.videoCheck = true;
      const THIS = this;
      THIS.iWndIndex[iWind] = true;
      this.oPlugin
        .JS_Play(
          THIS.previewUrl,
          {
            playURL: THIS.previewUrl,
            mode: 0,
            session: "", //定制设备
            token: "",
          },
          iWind
        )
        .then(
          (res) => {
            console.log("realplay success");
            $(`#canvas_animation${iWind}`).hide();
          },
          function () {
            THIS.iWndIndex[iWind] = false;
            let canvasEl = document.getElementById(`canvas_animation${iWind}`);
            let wd = canvasEl.getAttribute("width");
            let ht = canvasEl.getAttribute("height");
            THIS.canvasError(`canvas_animation${iWind}`, wd, ht);
            console.log("realplay failed");
            THIS.$message.error("取流异常");
          }
        );
    },
    playbackLocation() {
      var szStartDate = document.getElementById("sDate1").value;
      var szEndDate = document.getElementById("eDate1").value;
      this.oPlugin.JS_Seek(this.iWind, szStartDate, szEndDate).then(
        function () {
          console.log("playbackLocation success");
        },
        function () {
          console.log("playbackLocation failed");
        }
      );
    },
    // stop() {
    //   this.oPlugin.JS_Stop(this.iWind).then(
    //     function () {
    //       console.log("stop success");
    //     },
    //     function (e) {
    //       console.error("stop failed", e);
    //     }
    //   );
    // },
    stopBtn(i) {
      this.oPlugin.JS_Stop(i).then(
        () => {
          console.log("stop success");
          console.log($(`#canvas_animation${i}`).parent());
          $(`#canvas_animation${i}`).show();
          this.canvasDef(
            `canvas_animation${i}`,
            $(`#canvas_animation${i}`).parent().width(),
            $(`#canvas_animation${i}`).parent().height()
          );
          $("#stopBtn" + i).hide();
        },
        function (e) {
          console.error("stop failed", e);
        }
      );
    },
    arrangeWindow(i) {
      this.MaxIWind = i * i - 1;
      this.oPlugin.JS_ArrangeWindow(i).then(() => {
        console.log("JS_ArrangeWindow success");
        // console.log(this.iWndIndex.length);
        for (var i = 0; i < this.iWndIndex.length; i++) {
          // console.log(this.iWndIndex.length);
          $(`#canvas_animation${i}`)[0].width = $(`#canvas_animation${i}`)
            .parent()
            .width();
          $(`#canvas_animation${i}`)[0].height = $(`#canvas_animation${i}`)
            .parent()
            .height();
          if (this.iWndIndex[i] == true) {
            this.canvasAnimation(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width() * 2,
              $(`#canvas_animation${i}`).parent().height() * 2
            );
          } else if (this.iWndIndex[i] == false) {
            this.canvasError(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width() * 2,
              $(`#canvas_animation${i}`).parent().height() * 2
            );
          }
        }
      });
    },
    Pause() {
      this.oPlugin.JS_Pause(this.iWind).then(
        function () {
          console.log("Pause success");
        },
        function (e) {
          console.error("Pause failed", e);
        }
      );
    },
    Resume() {
      this.oPlugin.JS_Resume(this.iWind).then(
        function () {
          console.log("Resume success");
        },
        function (e) {
          console.error("Resume failed", e);
        }
      );
    },
    GetVolume() {
      this.oPlugin.JS_GetVolume(this.iWind).then(function (i) {
        console.log(i);
      });
    },
    CapturePicture(szType) {
      const timestamp = new Date();
      console.log(this.iWind);
      this.oPlugin
        .JS_CapturePicture(this.iWind, `img-${timestamp}`, szType)
        .then(
          function () {
            console.log("CapturePicture success");
          },
          function () {
            console.log("CapturePicture failed");
          }
        );
    },
    //关闭全部预览
    StopRealPlayAll() {
      this.oPlugin.JS_StopRealPlayAll().then(
        () => {
          console.log("JS_StopRealPlayAll success");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        },
        () => {
          console.log("JS_StopRealPlayAll failed");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        }
      );
    },
    dateFormat(oDate, fmt) {
      var o = {
        "M+": oDate.getMonth() + 1, //月份
        "d+": oDate.getDate(), //日
        "h+": oDate.getHours(), //小时
        "m+": oDate.getMinutes(), //分
        "s+": oDate.getSeconds(), //秒
        "q+": Math.floor((oDate.getMonth() + 3) / 3), //季度
        S: oDate.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (oDate.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    stopRecord() {
      this.oPlugin.JS_StopSave(this.iWind).then(
        function () {
          console.log("stopRecord success");
        },
        function () {
          console.log("stopRecord failed");
        }
      );
    },
    startTalk() {
      var talkurl = document.getElementById("talkurl").value;
      this.oPlugin.JS_StartTalk(talkurl).then(
        function () {
          console.log("startTalk success");
        },
        function () {
          console.log("startTalk failed");
        }
      );
    },
    fullSreen() {
      this.oPlugin.JS_FullScreenDisplay(true).then(
        function () {
          console.log("JS_FullScreenDisplay success");
        },
        function () {
          console.log("JS_FullScreenDisplay failed");
        }
      );
    },
    getData(enterpriseId) {
      this.loading = true;
      this.enterpriseId = enterpriseId;
      getVideoList({
        enterpId: this.enterpriseId,
        sign: this.sign,
      }).then((res) => {
        this.loading = false;
        if (res.data.code == 0) {
          this.videoData = res.data.data;
          this.cameraIndexCode = this.videoData[0].monitornum;
        }
      });
    },
    getVideoId(monitornum, index) {
      this.active = index;
      if (
        this.selectedKeys === monitornum &&
        new Date().getTime() - this.nowtime < 500
      ) {
        this.cameraIndexCode = monitornum;
        // $(`#canvas_animation${this.iWind}`).show();
        // let canvasEl = document.getElementById(`canvas_animation${this.iWind}`);
        // let wd = canvasEl.getAttribute("width");
        // let ht = canvasEl.getAttribute("height");
        // this.canvasAnimation(`canvas_animation${this.iWind}`, wd, ht);
        // // console.log(`canvas_animation${this.iWind}`);
        // this.getVideo();
        this.realPlayNew(this.cameraIndexCode);
      } else {
        console.log("click");
        this.nowtime = new Date().getTime();
        this.selectedKeys = monitornum;
      }
      console.log(this.selectedKeys);
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId
     */
    realPlayNew(channelId) {
      ICC.getRealmonitor({
        channelId: channelId,
        // dataType: '3', //视频类型：1=视频, 2=音频, 3=音视频
        streamType: "2", //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
      }).then((data) => {
        this.realPlayer.playReal({
          rtspURL: data.rtspUrl, // string | array[string]
          decodeMode: "canvas", // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
          channelId: channelId, // 可选，用来标记当前视频播放的通道id
        });
      });
    },
    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
    setVideoSize() {
      var playWind = document.getElementById("playWind_items");
      this.oPlugin.JS_Resize({ iWidth: playWind.scrollWidth }).then(
        () => {
          console.info("JS_Resize success");
          // do you want...
        },
        (err) => {
          console.info("JS_Resize failed");
          // do you want...
        }
      );
    },
    // initializationVideo() {
    //   const THIS = this;
    //   function getScript(url, fn) {
    //     if ("string" === typeof url) {
    //       url = [url]; //如果不是数组带个套
    //     }
    //     var ok = 0; //加载成功几个js
    //     var len = url.length; //一共几个js
    //     var head = document.getElementsByTagName("head").item(0);
    //     var js = null;
    //     var _url;
    //     var create = function (url) {
    //       //创建js
    //       var oScript = null;
    //       oScript = document.createElement("script");
    //       oScript.type = "text/javascript";
    //       oScript.src = url;
    //       head.appendChild(oScript);
    //       return oScript;
    //     };
    //     for (var i = 0; i < len; i++) {
    //       _url = url[i];
    //       js = create(_url); //创建js
    //       fn &&
    //         (js.onload = function () {
    //           if (++ok >= len) {
    //             //如果加载完所有的js则执行回调
    //             fn();
    //           }
    //         });
    //     }
    //   }
    //   //var szBrowserVersion = "";
    //   //var iBrowserVersion = -1;
    //   var aScript = [];
    //   var szUserAgent = navigator.userAgent.toLowerCase();
    //   // if (szUserAgent.match(/chrome\/([\d.]+)/) || szUserAgent.match(/Firefox\/([\d.]+)/)) {
    //   //szBrowserVersion = szUserAgent.match(/chrome\/([\d.]+)/)[1];
    //   //iBrowserVersion = parseInt(szBrowserVersion.split(".")[0], 10);
    //   if (
    //     szUserAgent.indexOf("win64") > -1 ||
    //     szUserAgent.indexOf("x64") > -1
    //   ) {
    //     aScript = ["../../../../static/h5player.min.js"];
    //   } else {
    //     aScript = ["../../../../static/h5player.min.js"];
    //   }
    //   // }
    //   var playWind = document.getElementById("playWind_items");
    //   // console.log(playWind.scrollWidth);
    //   // const THIS = this;
    //   getScript(aScript, function () {
    //     //初始化插件
    //     THIS.oPlugin = new JSPlugin({
    //       szId: "playWind",
    //       iWidth: playWind.scrollWidth,
    //       // iHeight: 500,
    //       iMaxSplit: 3,
    //       iCurrentSplit: 1,
    //       szBasePath: "../../../../static/",
    //       oStyle: {
    //         border: "#343434",
    //         borderSelect: "#FFCC00",
    //         background: "#000",
    //       },
    //       openDebug: false,
    //     });
    //     THIS.initPlugin();
    //     //初始化播放器大小
    //     THIS.setVideoSize();
    //     THIS.resizeObserver = new ResizeObserver((entries) => {
    //       // console.log(entries);
    //       THIS.$nextTick(() => {
    //         THIS.$refs.videoInspection.style.width =
    //           entries[0].target.offsetWidth;
    //         THIS.setVideoSize();
    //       });
    //     });

    //     THIS.resizeObserver.observe(document.getElementById("videoInspection"));
    //     //初始化canvas动画
    //     for (var i = 0; i < $(".parent-wnd").children().length; i++) {
    //       THIS.iWndIndex[i] = null;
    //       let canvasWidth = $("#playWind_playCanvas" + i).width();
    //       let canvasHeight = $("#playWind_playCanvas" + i).height();
    //       $("#playWind_playCanvas" + i).before(
    //         `<canvas id="canvas_animation${i}" width="${canvasWidth}" height="${canvasHeight}"></canvas>`
    //       );
    //       $("#canvas_animation" + i).css({
    //         transform: "scale(0.5) translate(-50%, -50%)",
    //         "transform-origin": "0 0",
    //         position: "absolute",
    //         top: "50%",
    //         left: "50%",
    //         // // zIndex: 200,
    //       });
    //       $("#canvas_draw" + i).css({
    //         cursor: "default",
    //       });
    //       $("#playWindow" + i)
    //         .after(`<div id="stopBtn${i}" style="width:100%;height: 35px;background: rgba(0,0,0,0.4);position: absolute;top:0;left:0;z-index: 9999999;display:none;cursor: default;">
    //       <i class="el-icon-close"  style="float: right;line-height:35px;margin-right:10px;font-size:18px;color:#eee;cursor: pointer;"></i>
    //     </div>`);
    //     }
    //   });
    // },
  },

  //生命周期 - 挂载完成（可以访问DOM元素）
  async created() {
    await ICC.init();
  },
  mounted() {
    const THIS = this;
    //初始化播放器
    // this.initializationVideo();

    // window.onresize = () => {
    //   THIS.setVideoSize();
    // };
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // 初始化平台信息获取
    let serverAdress = sessionStorage.getItem("videoApi");
    // if (process.env.NODE_ENV === 'development') {
    //     serverAdress = '************:2443';
    // } else {
    //     serverAdress = '***********:9100';
    // }
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    if (!this.realPlayer) {
      this.realPlayer = new WSPlayer({
        el: "ws-real-player", // 必传
        type: "real", // real | record
        serverIp: serverAdress,
        num: 4,
        showControl: true,
      });
    }
  },
  watch: {},
  beforeDestroy() {
    // this.StopRealPlayAll();
    // 离开页面删除检测器和所有侦听器
    // this.resizeObserver.disconnect();
  },
};
</script>
<style lang="scss" scoped>
$video-right-width: calc(100% - 370px);
$video-right-height: calc(100vh - 255px);
.videoInspection {
  // padding: 15px 0;
  overflow: hidden;
  .video-left {
    float: left;
    width: 345px;
    margin-right: 25px;
    .videoLeft-top {
      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #3b4046;
        line-height: 32px;
      }
      .video-list {
        background: #daefff;
        height: 550px;
        padding: 15px 0;
        border-radius: 4px;
        overflow-y: scroll;
        height: calc(100vh - 285px);
        .activeP {
          background-color: rgba(0, 120, 255, 0.1);
          color: #3977ea;
        }
        p {
          line-height: 50px;
          height: 50px;
          margin-bottom: 0;
          font-size: 14px;
          padding: 0 15px;
          color: #545c65;
          border-bottom: 1px solid #bdddf3;
          -webkit-touch-callout: none; /* iOS Safari */
          -webkit-user-select: none; /* Chrome/Safari/Opera */
          -khtml-user-select: none; /* Konqueror */
          -moz-user-select: none; /* Firefox */
          -ms-user-select: none; /* Internet Explorer/Edge */
          user-select: none;
          .video-camera {
            display: inline-block;
            width: 17px;
            height: 20px;
            background: url("/static/img/assets/img/camera.png") no-repeat
              center;
            background-size: cover;
            vertical-align: middle;
            margin-right: 14px;
          }
          span {
            float: right;
          }
          .zaixian {
            color: #43be98;
          }
          .lixian {
            color: #999999;
          }
          .baojing {
            color: #f9646f;
          }
        }
      }
    }
  }
  .video-right {
    float: left;
    position: relative;
    width: $video-right-width;
    height: $video-right-height;
    .video-box {
      margin-top: 40px;
      // border: 1px solid #ddd;
      background-color: #000 !important;
      height: calc($video-right-height - 30px);
      & > .items {
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background-color: #2e2e2e;
        padding-top: 10px;
        padding-bottom: 10px;
        padding: 0 20px;
        height: 50px;
        .items_button {
          width: 60%;
          height: 30px;
          display: flex;
        }
        .button {
          color: #fff;
          border-radius: 5px;
          padding: 5px 10px;
          margin-right: 20px;
          cursor: pointer;
          background-color: #777;
        }
        .fenping_box {
          width: 120px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .fenping {
            width: 23px;
            height: 23px;
          }
        }
      }
    }
    #playWind {
      height: calc(100vh - 335px);
    }
    #ws-real-player {
      height: calc(100vh - 295px);
    }
  }
}
</style>
