<template>
  <div style="width: 100%; height: 100%; background-color: #000000">
    <section v-if="!loadStatus" style="width: 100%; height: 100%">
      <video
        autoplay
        controls
        width="100%"
        height="100%"
        :id="id"
        muted
      ></video>
    </section>
    <div
      v-else
      style="
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        cursor: pointer;
      "
    >
      <span style="color: wheat; font-size: 14px"
        ><i class="el-icon-warning-outline" style="margin-right: 10px"></i
        >{{ statusMsg }}</span
      >
    </div>
  </div>
</template>

<script>
import flvjs from "flv-h265";

export default {
  name: "viewVideo",
  props: {
    url: {
      default: null,
      required: true,
      type: [String, Object],
    },
  },
  data() {
    return {
      id: "videoElement" + Math.random(),
      loadStatus: true,
      statusMsg: "未选择视频",
      flvPlayer: null,
      src: "",
      timerId: null,
      isCreating: false, // 防止重复创建
    };
  },
  watch: {
    url: {
      handler(newValue, oldValue) {
        console.log(`[${this.id}] URL变化:`, {
          newValue: newValue ? newValue.substring(0, 50) + "..." : newValue,
          oldValue: oldValue ? oldValue.substring(0, 50) + "..." : oldValue,
          currentSrc: this.src ? this.src.substring(0, 50) + "..." : this.src,
          isCreating: this.isCreating,
        });

        // 防止重复处理
        if (this.isCreating) {
          console.log(`[${this.id}] 正在创建中，跳过`);
          return;
        }

        // 只有真正的URL变化才处理
        if (newValue && newValue !== this.src) {
          console.log(`[${this.id}] 开始处理URL变化`);
          this.handleUrlChange(newValue);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  created() {
    console.log("flvPlayer组件创建");
  },
  mounted() {
    console.log("flvPlayer组件挂载");

    // 检查 flv-h265 是否正确加载
    if (typeof flvjs !== "undefined") {
      console.log("flv-h265 已成功加载，版本:", flvjs.version || "未知");
      console.log("浏览器支持状态:", flvjs.isSupported());
    } else {
      console.error("flv-h265 未加载！请检查 npm 依赖");
    }

    // 添加全局错误捕获，防止flv-h265内部错误影响页面
    this.originalErrorHandler = window.onerror;
    window.onerror = (message, source, lineno, colno, error) => {
      // 捕获flv-h265相关的错误
      if (
        message &&
        (message.includes("flushStashedSamples") ||
          message.includes("Cannot read properties of null") ||
          message.includes("TransmuxingController") ||
          message.includes("IOController"))
      ) {
        console.warn(`[${this.id}] 捕获到flv-h265内部错误，已忽略:`, message);
        return true; // 阻止错误冒泡
      }

      // 其他错误继续处理
      if (this.originalErrorHandler) {
        return this.originalErrorHandler(message, source, lineno, colno, error);
      }
      return false;
    };
  },
  methods: {
    // 处理URL变化
    async handleUrlChange(newUrl) {
      if (this.isCreating) {
        console.log(`[${this.id}] 正在创建中，忽略URL变化`);
        return;
      }

      console.log(`[${this.id}] 处理URL变化:`, newUrl.substring(0, 50) + "...");

      this.loadStatus = false;
      this.src = newUrl;

      // 清理现有播放器
      if (this.flvPlayer) {
        this.destroyPlayer();
      }

      // 延迟一点再创建，避免快速连续调用
      await this.$nextTick();
      await new Promise((resolve) => setTimeout(resolve, 50));

      if (this.src === newUrl) {
        // 确保URL没有再次变化
        await this.createVideo();
      }
    },

    async setsrc() {
      if (this.url) {
        await this.handleUrlChange(this.url);
      }
    },

    async createVideo() {
      // 防止重复创建
      if (this.isCreating) {
        console.log(`[${this.id}] 播放器正在创建中，跳过重复调用`);
        return;
      }

      // 检查是否有有效的URL
      if (!this.src) {
        console.log(`[${this.id}] 没有有效的视频URL，跳过创建`);
        return;
      }

      this.isCreating = true;
      console.log(`[${this.id}] 开始创建视频播放器`);

      try {
        if (this.flvPlayer) {
          this.destroyPlayer(); // 使用安全的销毁方法
        }

        // 检查 flv-h265 模块是否可用
        if (typeof flvjs === "undefined") {
          console.error("flv-h265 模块未加载，请检查 npm 依赖");
          this.statusMsg = "播放器加载失败";
          this.loadStatus = true;
          this.isCreating = false; // 重置标志
          return;
        }

        if (!flvjs.isSupported()) {
          console.error("浏览器不支持 FLV 播放");
          this.statusMsg = "您的浏览器不支持 FLV 播放";
          this.loadStatus = true;
          this.isCreating = false; // 重置标志
          return;
        }

        // 等待DOM更新后再查找元素
        await this.$nextTick();

        console.log(
          "查找视频元素，ID:",
          this.id,
          "loadStatus:",
          this.loadStatus
        );
        let videoElement = document.getElementById(this.id);

        if (!videoElement) {
          console.error("第一次找不到视频元素");
          console.error("当前组件状态:", {
            id: this.id,
            loadStatus: this.loadStatus,
            src: this.src,
            hasEl: !!this.$el,
          });

          // 如果loadStatus为true，说明video被隐藏了，需要先显示
          if (this.loadStatus) {
            console.log("loadStatus为true，重置为false后重试");
            this.loadStatus = false;
            await this.$nextTick();
          }

          // 再次等待并重试
          await new Promise((resolve) => setTimeout(resolve, 100));
          videoElement = document.getElementById(this.id);

          if (!videoElement) {
            console.error("重试后仍找不到视频元素，放弃创建");
            this.statusMsg = "播放器初始化失败";
            this.loadStatus = true;
            this.isCreating = false; // 重置标志
            return;
          } else {
            console.log("重试成功找到视频元素");
          }
        } else {
          console.log("成功找到视频元素");
        }

        console.log("视频URL:", this.src);

        // 创建播放器配置
        const mediaDataSource = {
          type: "flv",
          url: this.src,
          isLive: true,
          hasAudio: false,
          cors: true,
          withCredentials: false,
        };

        const config = {
          enableWorker: false, // 禁用Worker，减少复杂性
          enableStashBuffer: false, // 禁用缓存，减少内存问题
          reuseRedirectedURL: true,
          autoCleanupSourceBuffer: true,
          lazyLoad: false, // 禁用懒加载，立即连接
          // lazyLoadMaxDuration: 3 * 60,
          // deferLoadAfterSourceOpen: true,

          // 添加连接超时配置
          seekType: "range",
          rangeLoadZeroStart: false,
          customSeekHandler: undefined,

          // WebSocket 特定配置
          headers: {},

          // 错误恢复配置
          fixAudioTimestampGap: true,
          accurateSeek: false,
        };

        console.log(
          `[${this.id}] 创建播放器，URL:`,
          this.src.substring(0, 80) + "..."
        );
        this.flvPlayer = flvjs.createPlayer(mediaDataSource, config);

        this.flvPlayer.on(
          flvjs.Events.ERROR,
          (errorType, errorDetail, errorInfo) => {
            console.error(`[${this.id}] 播放器错误:`, {
              errorType,
              errorDetail,
              errorInfo,
              url: this.src ? this.src.substring(0, 80) + "..." : "no url",
            });

            // 根据错误类型提供更详细的信息
            switch (errorType) {
              case flvjs.ErrorTypes.NETWORK_ERROR:
                this.statusMsg = "网络连接失败";
                console.log(`[${this.id}] WebSocket连接失败，可能原因：`);
                console.log(`- 视频流服务器不可达`);
                console.log(`- 网络连接问题`);
                console.log(`- 认证token过期`);
                break;
              case flvjs.ErrorTypes.MEDIA_ERROR:
                this.statusMsg = "媒体解码失败";
                console.log(`[${this.id}] 媒体数据错误，可能原因：`);
                console.log(`- 视频格式不支持`);
                console.log(`- 数据流损坏`);
                break;
              default:
                this.statusMsg = "播放错误";
                console.log(`[${this.id}] 其他播放错误:`, errorType);
            }

            // 使用安全销毁方法
            this.destroyPlayer();
            this.loadStatus = true;
            this.isCreating = false;

            // 如果需要重试，可以手动点击刷新按钮
            // setTimeout(async () => {
            //   if (this.src && !this.flvPlayer && !this.isCreating) {
            //     console.log("5秒后重试创建播放器");
            //     this.loadStatus = false;
            //     await this.createVideo();
            //   }
            // }, 5000);
          }
        );

        this.flvPlayer.attachMediaElement(videoElement);
        this.flvPlayer.load();

        const playWithRetry = (retryCount = 0) => {
          if (this.flvPlayer) {
            this.flvPlayer.play().catch((error) => {
              if (error.name !== "AbortError") {
                console.error("播放失败:", error);
                if (retryCount < 3) {
                  setTimeout(() => playWithRetry(retryCount + 1), 1000);
                }
              }
            });
          }
        };

        setTimeout(() => {
          playWithRetry();
        }, 1000);

        // 创建成功，重置标志
        this.isCreating = false;
      } catch (error) {
        console.error("创建播放器失败:", error);
        this.statusMsg = "创建播放器失败";
        this.loadStatus = true;
        this.isCreating = false; // 重置标志
      }
    },

    // 安全销毁播放器
    destroyPlayer() {
      if (this.flvPlayer) {
        try {
          console.log(`[${this.id}] 销毁播放器实例`);

          // 先暂停播放
          try {
            this.flvPlayer.pause();
          } catch (e) {
            console.warn(`[${this.id}] 暂停播放失败:`, e.message);
          }

          // 卸载媒体
          try {
            this.flvPlayer.unload();
          } catch (e) {
            console.warn(`[${this.id}] 卸载媒体失败:`, e.message);
          }

          // 分离媒体元素
          try {
            this.flvPlayer.detachMediaElement();
          } catch (e) {
            console.warn(`[${this.id}] 分离媒体元素失败:`, e.message);
          }

          // 最后销毁播放器
          try {
            this.flvPlayer.destroy();
          } catch (e) {
            console.warn(`[${this.id}] 销毁播放器失败:`, e.message);
          }
        } catch (error) {
          console.error(`[${this.id}] 销毁播放器出错:`, error);
        } finally {
          this.flvPlayer = null;
          console.log(`[${this.id}] 播放器已清理`);
        }
      }
    },
  },
  beforeDestroy() {
    console.log("销毁视频播放器组件");
    this.destroyPlayer();

    // 恢复原始错误处理器
    if (this.originalErrorHandler) {
      window.onerror = this.originalErrorHandler;
    }
  },
};
</script>

<style scoped>
h3 {
  margin: 40px 0 0;
}
ul {
  list-style-type: none;
  padding: 0;
}
li {
  display: inline-block;
  margin: 0 10px;
}
a {
  color: #42b983;
}
</style>
