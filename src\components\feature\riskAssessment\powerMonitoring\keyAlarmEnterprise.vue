<template>
  <div class="enterpriseManagement">
    <div>
      <div class="seach-part">
        <div class="l">
          <!-- v-if="isShowDist && showArea" -->
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtCode"
             v-if="isShowDist && showArea"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"            
          ></el-cascader>
          <el-select
            v-model="status"
            size="mini"
            placeholder="请选择企业当前状态"
            clearable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <!-- <el-select
            v-model="warnStatus"
            size="mini"
            placeholder="请选择预警类型"
            clearable
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <!-- {{queryParams.orgName}} -->
           <div style="width: 200px">
            <el-autocomplete
              popper-class="my-autocomplete"
              v-model="queryParams.orgName"
              :fetch-suggestions="querySearch"
              placeholder="请输入企业名称关键字"
              clearable
              @clear="clearSensororgCode()"
              @select="handleSelect"
              size="mini"
              style="width: 200"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.enterpName }}</div>
              </template>
            </el-autocomplete>
          </div>
          <!-- <el-date-picker
            v-model="date"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            clearable
          >
          </el-date-picker> -->
          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >
          <CA-button type="primary" size="mini" plain @click="exportExcel()"
            >导出</CA-button
          >
        </div>
        <div>
               <el-button type="primary" size="mini" @click="NotificationBool()"
            >新增</el-button
          >
        </div>
      </div>
      <div class="table-main">      
        <div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="单位名称"
                min-width="180"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row}">               
                   {{ row.enterpName }}
                </template>
              </el-table-column>
              <el-table-column
                label="行政区划"
                width="120"
                align="center"
              >
                <template slot-scope="{ row }">
                   {{ row.districtName }}
                  <!-- <span v-if="isShowDist == true">{{ row.distName }}</span>
                  <span v-else>{{ park.parkName }}</span> -->
                </template>
              </el-table-column>
              <el-table-column label="监管时间" width="200" align="center">
                <template slot-scope="{ row }">
                  <div>
                      {{row | fiterTime}}
                    <!-- {{ dayjs(row.startTime).format("YYYY/MM/DD")}} -->
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="监管状态"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.status==0?'试生产':'停工' }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="操作登记单位"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.orgName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="操作登记人"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.operator }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作日期" min-width="110" align="center">
                <template slot-scope="{ row }">
                  <div>
                    {{ row.updateTime | filterUpdateTime	 }}
                  </div>
                </template>
              </el-table-column>

               <el-table-column label="操作" min-width="80" align="center">
                <template slot-scope="{ row }">
                  <div>
                    <!-- <el-button
                      type="text"                    
                      @click="disposalSituationBool(row)"                    
                      >反馈</el-button
                    >-->
                    <el-button                   
                      type="text"
                      @click="clickDelete(row)"
                      >删除</el-button
                    > 
                    <el-button type="text" @click="clickEdit(row)"
                      >修改</el-button
                    >
                  </div>
                </template>
              </el-table-column>

            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.size"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <Notification ref="Notification"></Notification>
    <notificationEdit ref="notificationEdit"></notificationEdit>
  
  </div>
</template>
<script>
import {
  getElectricityDelete,
  getElectricityPage,
  getSuperviseExport,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";
import Notification from "./notification";
import notificationEdit from "./notificationEdit";
import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");
import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {
    Notification,
    notificationEdit      
  },
  data() {
    return {
      queryParams: {
        orgCode: "",
        orgName: "",
      },
      projectName:'',
      tableCheck: true,
      loading: false,
      areaName: "",
      tableData: {},
       options: [
        {
          value: "1",
          label: "停工",
        },
        {
          value: "0",
          label: "试生产",
        },
      ],
      value: "",
      warnStatus: "",
      rank: "",
      showBreak: false,
      status:'',
      districtCode: this.$store.state.login.userDistCode, //行政区划代码
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      selection: [],
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (720 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      // date:"",
      startDate: "",
      endDate: "",
      showArea: true,
      disposalSituationShow: false,
      receivingUnit: [],
      reasonDescription: "",
      notificationContent: "",
      nowtime: new Date(new Date()).Format("yy年MM月dd日 hh时mm分"),
      park: "",
      listId: "",
      title: "风险预警信息核查、处置情况报告",
      disabled: false,
    };
  },
   filters:{     
    fiterTime(val){      
      return dayjs(val.startTime).format("YYYY/MM/DD") + ' - ' + dayjs(val.endTime).format("YYYY/MM/DD");
    },
    filterUpdateTime(val){
      return dayjs(val).format("YYYY/MM/DD HH:mm")
    }
  },
  methods: {
    //自动输入
    clearSensororgCode() {
      this.queryParams.orgCode = "";
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.orgCode = item.enterpId;
      this.queryParams.orgName = item.enterpName;
    },
    //自动输入end
    clickDelete(row){
       this.$confirm("确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {       
          getElectricityDelete({ superviseId: row.superviseId }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success(res.data.msg);
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getData();
            } else {
              this.$message.error(res.data.data);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },

    disposalSituationBool(row) {
      this.disposalSituationShow = true;
      this.listId = row.id;
      getSelCompany(row.id)
        .then((res) => {
          this.receivingUnit = res.data.data.receivingUnit;
        })
        .catch((error) => {
          reject(error);
        });
    },
    addCimEarlyWarning() {
      if(!this.reasonDescription){
          Message({
              message: "原因说明不能为空",
              type: "info",
            });
          return
      }else if(!this.notificationContent){
          Message({
              message: "处置措施不能为空",
              type: "info",
            });
          return
      }
      this.disable = true;
      postCimEarlyWarningFeedBackAdd({
        warningId:this.listId,
        title: this.title,
        reasonDescription: this.reasonDescription,
        notificationContent: this.notificationContent,
        receivingUnit: this.receivingUnit.join(","),
      })
        .then((res) => {
          this.disable = false;
          this.disposalSituationShow = false;
          this.receivingUnit = [];
          this.reasonDescription = "";
          this.notificationContent = "";
          if (res.data.code === 0) {
            Message({
              message: "反馈成功",
              type: "success",
            });
            this.getData();
          } else {
            Message.error("反馈失败");
          }
        })
        .catch((error) => {
          this.disable = false;
          this.disposalSituationShow = false;
          Message.error("反馈失败");
        });
    },
    //新增
    NotificationBool(row) {    
      this.$refs.Notification.closeBoolean(true);
      // this.$refs.Notification.getData(row);
    },
    //修改-notificationEdit
    clickEdit(row){    
       this.$refs.notificationEdit.closeBoolean(true);
       this.$refs.notificationEdit.getData(row.superviseId)
       this.$refs.notificationEdit.searchBaseInfo(row.enterpId)
    },
    // WarnBool(row) {   
    //   this.$refs.Warn.closeBoolean(true);   
    //   console.log(row);
    //   this.$refs.Warn.getData(row.id, row.companyCode,row.companyName);
      
    // },
    // searchTime(value) {
    //   this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
    //   this.endDate = new Date(
    //     new Date(this.date[1].getTime() + 86399900)
    //   ).Format("yy-MM-dd hh:mm:ss");
    // },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    getData() {
      if (this.$store.state.login.user.user_type == "ent") {
        this.showArea = false;
        getEnt({}).then((res) => {
          if (res.data.code == 0) {           
            this.getDataes(res.data.data.enterpId);
          }
        });
      } else {
        this.getDataes(null);
      }
    },
    getDataes(id) {
      this.loading = true;
      getElectricityPage({        
        creditCode:'',  //统一信用代码  
        districtCode:this.districtCode, //行政区划代码
        enterpId:id ? id : this.queryParams.orgCode, //企业id
        enterpName: this.queryParams.orgName, //企业名称
        status:this.status,  //监管状态0试生产1停工
        superviseId:'', //监管id
        size: 10,
        current: this.currentPage,
      }).then((res) => {        
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
    // 导出
    exportExcel() {
      console.log(32);
      getSuperviseExport({
        creditCode: "", //统一信用代码
        districtCode: this.districtCode, //行政区划代码
        enterpId: this.queryParams.orgCode, //企业id
        enterpName:  this.queryParams.orgName, //企业名称
        status:this.status,  //监管状态0试生产1停工
        superviseId:'', //监管id
        endTime: "",
        startTime: "",
        pageSize: 0,
        nowPage: 0,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业监管状态" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },

    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
      this.$refs.DetailTable.getDistrict();
    },
    openDialoges(distCode, type, areaName) {
      this.$refs.DetailTablees.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTablees.getEntData(distCode, type, areaName);
      this.$refs.DetailTablees.getDistrict();
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleCurrentChange(val) {
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style >
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
.disposalSituation {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
