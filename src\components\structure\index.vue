<template>
  <div class="structure">
    <component
      :is="$route.name == 'structure' ? 'role' : $route.name"
    ></component>
  </div>
</template>

<script>
import menuManagement from "./menuManagement";
import systemInformation from "./systemInformation";
import passwordRules from "./passwordRules";
import classifiedManagement from "./classifiedManagement";
import auditConfig from "./auditConfig";
import role from "./Role";
import user from "./User";
import jurisdiction from "./jurisdiction";
import gen from "./gen"
import DataSourceManagement from "./DataSourceManagement"
// import messagePush from "./messagePush"
// import setConfigura from "./setConfigura/setConfigura.vue"
export default {
  //import引入的组件
  components: {
    menuManagement,
    systemInformation,
    passwordRules,
    classifiedManagement,
    role,
    user,
    jurisdiction,
 
    auditConfig,
 
    gen,
    DataSourceManagement,
    // messagePush,
    // setConfigura
  },
  data() {
    return {
      componentStr: "",
    };
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
.structure {
  margin-top: 15px;
}
</style>