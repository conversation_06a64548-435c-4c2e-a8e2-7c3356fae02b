<template>
  <div class="riskDy">
    <IndexAnalysis
      v-if="enterpItem && enterpItem.enterpId"
      :enterpriseInfo="enterpItem"
    />
  </div>
</template>

<script>
import { getPortraitInfoPage } from "@/api/enterprisePortrait";
import IndexAnalysis from "@/components/feature/enterprisePortrait/enterprisePortraitList/components/IndexAnalysis.vue";
export default {
  //import引入的组件
  name: "riskDynamics",
  components: { IndexAnalysis },
  data() {
    return {
      enterpItem: {},
    };
  },

  //方法集合
  methods: {
    getData(enterpriseId) {
      this.loading = true;
      getPortraitInfoPage({
        nowPage: 1,
        pageSize: 10,
        enterpIds: [enterpriseId],
        // enterId: enterpriseId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          console.log(res.data.data);
          // 选出res.data.data.list中enterpId等于enterpriseId的记录
          this.enterpItem = res.data.data.list.filter(
            (item) => item.enterpId == enterpriseId
          )[0];
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.riskDy {
}
</style>
