<template>
  <button
    class="CA-button"
    @click="handleClick"
    :disabled="buttonDisabled || loading"
    :autofocus="autofocus"
    :type="nativeType"
    :class="[
      type ? 'CA-button--' + type : '',
      buttonSize ? 'CA-button--' + buttonSize : '',
      {
        'is-disabled': buttonDisabled,
        'is-loading': loading,
        'is-plain': plain,
        'is-round': round,
        'is-circle': circle
      }
    ]"
  >
    <i class="CA-icon-loading" v-if="loading"></i>
    <i  :class="icon" v-if="icon && !loading"></i>
    <span v-if="$slots.default" class="btn-icon"><slot></slot></span>
  </button>
</template>
<script>
  export default {
    name: 'CA<PERSON>utton',

    inject: {
      CAForm: {
        default: ''
      },
      CAFormItem: {
        default: ''
      }
    },

    props: {
      type: {
        type: String,
        default: 'default'
      },
      size: String,
      icon: {
        type: String,
        default: ''
      },
      nativeType: {
        type: String,
        default: 'button'
      },
      loading: <PERSON><PERSON><PERSON>,
      disabled: <PERSON>olean,
      plain: Boolean,
      autofocus: <PERSON>olean,
      round: <PERSON>olean,
      circle: <PERSON><PERSON><PERSON>
    },

    computed: {
      _CAFormItemSize() {
        return (this.CAFormItem || {}).CAFormItemSize;
      },
      buttonSize() {
        return this.size || this._CAFormItemSize || (this.$CAEMENT || {}).size;
      },
      buttonDisabled() {
        return this.disabled || (this.CAForm || {}).disabled;
      }
    },

    methods: {
      handleClick(evt) {
        this.$emit('click', evt);
      }
    }
  };
</script>
<style lang="scss" scoped>
.CA-button-group>.CA-button.is-active,
.CA-button-group>.CA-button.is-disabled,
.CA-button-group>.CA-button:active,
.CA-button-group>.CA-button:focus,
.CA-button-group>.CA-button:hover {
  z-index: 1
}
.btn-icon{
  display: flex;
  align-items: center;
}
.CA-button {
  display: inline-block;
  line-height: 1;
  white-space: nowrap;
  cursor: pointer;
  background: #FFF;
  border: 1px solid #DCDFE6;
  color: #606266;
  -webkit-appearance: none;
  text-align: center;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  outline: 0;
  margin: 0;
  -webkit-transition: .1s;
  transition: .1s;
  font-weight: 500;
  -moz-user-sCAect: none;
  -webkit-user-sCAect: none;
  -ms-user-sCAect: none;
  padding: 12px 20px;
  font-size: 14px;
  border-radius: 4px
}

.CA-button+.CA-button {
  margin-left: 10px
}

.CA-button:focus,
.CA-button:hover {
  color: #409EFF;
  border-color: #c6e2ff;
  background-color: #ecf5ff
}

.CA-button:active {
  color: #3a8ee6;
  border-color: #3a8ee6;
  outline: 0
}

.CA-button::-moz-focus-inner {
  border: 0
}

.CA-button [class*=CA-icon-]+span {
  margin-left: 5px
}

.CA-button.is-plain:focus,
.CA-button.is-plain:hover {
  background: #FFF;
  border-color: #409EFF;
  color: #409EFF
}

.CA-button.is-active,
.CA-button.is-plain:active {
  color: #3a8ee6;
  border-color: #3a8ee6
}

.CA-button.is-plain:active {
  background: #FFF;
  outline: 0
}

.CA-button.is-disabled,
.CA-button.is-disabled:focus,
.CA-button.is-disabled:hover {
  color: #C0C4CC;
  cursor: not-allowed;
  background-image: none;
  background-color: #FFF;
  border-color: #EBEEF5
}

.CA-button.is-disabled.CA-button--text {
  background-color: transparent
}

.CA-button.is-disabled.is-plain,
.CA-button.is-disabled.is-plain:focus,
.CA-button.is-disabled.is-plain:hover {
  background-color: #FFF;
  border-color: #EBEEF5;
  color: #C0C4CC
}

.CA-button.is-loading {
  position: rCAative;
  pointer-events: none
}

.CA-button.is-loading:before {
  pointer-events: none;
  content: '';
  position: absolute;
  left: -1px;
  top: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: inherit;
  background-color: rgba(255, 255, 255, .35)
}

.CA-button.is-round {
  border-radius: 20px;
  padding: 12px 23px
}

.CA-button.is-circle {
  border-radius: 50%;
  padding: 12px
}

.CA-button--primary {
  color: #FFF;
  background-color: #409EFF;
  border-color: #409EFF
}

.CA-button--primary:focus,
.CA-button--primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
  color: #FFF
}

.CA-button--primary.is-active,
.CA-button--primary:active {
  background: #3a8ee6;
  border-color: #3a8ee6;
  color: #FFF
}

.CA-button--primary:active {
  outline: 0
}

.CA-button--primary.is-disabled,
.CA-button--primary.is-disabled:active,
.CA-button--primary.is-disabled:focus,
.CA-button--primary.is-disabled:hover {
  color: #FFF;
  background-color: #a0cfff;
  border-color: #a0cfff
}

.CA-button--primary.is-plain {
  color: #409EFF;
  background: #fff;
  border-color: #409EFF;
}

.CA-button--primary.is-plain:focus,
.CA-button--primary.is-plain:hover {
  background: #409EFF;
  border-color: #2b90f5;
  color: #FFF
}

.CA-button--primary.is-plain:active {
  background: #3a8ee6;
  border-color: #3a8ee6;
  color: #FFF;
  outline: 0
}

.CA-button--primary.is-plain.is-disabled,
.CA-button--primary.is-plain.is-disabled:active,
.CA-button--primary.is-plain.is-disabled:focus,
.CA-button--primary.is-plain.is-disabled:hover {
  color: #8cc5ff;
  background-color: #ecf5ff;
  border-color: #d9ecff
}

.CA-button--success {
  color: #FFF;
  background-color: #67C23A;
  border-color: #67C23A
}

.CA-button--success:focus,
.CA-button--success:hover {
  background: #85ce61;
  border-color: #85ce61;
  color: #FFF
}

.CA-button--success.is-active,
.CA-button--success:active {
  background: #5daf34;
  border-color: #5daf34;
  color: #FFF
}

.CA-button--success:active {
  outline: 0
}

.CA-button--success.is-disabled,
.CA-button--success.is-disabled:active,
.CA-button--success.is-disabled:focus,
.CA-button--success.is-disabled:hover {
  color: #FFF;
  background-color: #b3e19d;
  border-color: #b3e19d
}

.CA-button--success.is-plain {
  color: #67C23A;
  background: #f0f9eb;
  border-color: #c2e7b0
}

.CA-button--success.is-plain:focus,
.CA-button--success.is-plain:hover {
  background: #67C23A;
  border-color: #67C23A;
  color: #FFF
}

.CA-button--success.is-plain:active {
  background: #5daf34;
  border-color: #5daf34;
  color: #FFF;
  outline: 0
}

.CA-button--success.is-plain.is-disabled,
.CA-button--success.is-plain.is-disabled:active,
.CA-button--success.is-plain.is-disabled:focus,
.CA-button--success.is-plain.is-disabled:hover {
  color: #a4da89;
  background-color: #f0f9eb;
  border-color: #e1f3d8
}

.CA-button--warning {
  color: #FFF;
  background-color: #E6A23C;
  border-color: #E6A23C
}

.CA-button--warning:focus,
.CA-button--warning:hover {
  background: #ebb563;
  border-color: #ebb563;
  color: #FFF
}

.CA-button--warning.is-active,
.CA-button--warning:active {
  background: #cf9236;
  border-color: #cf9236;
  color: #FFF
}

.CA-button--warning:active {
  outline: 0
}

.CA-button--warning.is-disabled,
.CA-button--warning.is-disabled:active,
.CA-button--warning.is-disabled:focus,
.CA-button--warning.is-disabled:hover {
  color: #FFF;
  background-color: #f3d19e;
  border-color: #f3d19e
}

.CA-button--warning.is-plain {
  color: #E6A23C;
  background: #fdf6ec;
  border-color: #f5dab1
}

.CA-button--warning.is-plain:focus,
.CA-button--warning.is-plain:hover {
  background: #E6A23C;
  border-color: #E6A23C;
  color: #FFF
}

.CA-button--warning.is-plain:active {
  background: #cf9236;
  border-color: #cf9236;
  color: #FFF;
  outline: 0
}

.CA-button--warning.is-plain.is-disabled,
.CA-button--warning.is-plain.is-disabled:active,
.CA-button--warning.is-plain.is-disabled:focus,
.CA-button--warning.is-plain.is-disabled:hover {
  color: #f0c78a;
  background-color: #fdf6ec;
  border-color: #faecd8
}

.CA-button--danger {
  color: #FFF;
  background-color: #F56C6C;
  border-color: #F56C6C
}

.CA-button--danger:focus,
.CA-button--danger:hover {
  background: #f78989;
  border-color: #f78989;
  color: #FFF
}

.CA-button--danger.is-active,
.CA-button--danger:active {
  background: #dd6161;
  border-color: #dd6161;
  color: #FFF
}

.CA-button--danger:active {
  outline: 0
}

.CA-button--danger.is-disabled,
.CA-button--danger.is-disabled:active,
.CA-button--danger.is-disabled:focus,
.CA-button--danger.is-disabled:hover {
  color: #FFF;
  background-color: #fab6b6;
  border-color: #fab6b6
}

.CA-button--danger.is-plain {
  color: #F56C6C;
  background: #fef0f0;
  border-color: #fbc4c4
}

.CA-button--danger.is-plain:focus,
.CA-button--danger.is-plain:hover {
  background: #F56C6C;
  border-color: #F56C6C;
  color: #FFF
}

.CA-button--danger.is-plain:active {
  background: #dd6161;
  border-color: #dd6161;
  color: #FFF;
  outline: 0
}

.CA-button--danger.is-plain.is-disabled,
.CA-button--danger.is-plain.is-disabled:active,
.CA-button--danger.is-plain.is-disabled:focus,
.CA-button--danger.is-plain.is-disabled:hover {
  color: #f9a7a7;
  background-color: #fef0f0;
  border-color: #fde2e2
}

.CA-button--info {
  color: #FFF;
  background-color: #909399;
  border-color: #909399
}

.CA-button--info:focus,
.CA-button--info:hover {
  background: #a6a9ad;
  border-color: #a6a9ad;
  color: #FFF
}

.CA-button--info.is-active,
.CA-button--info:active {
  background: #82848a;
  border-color: #82848a;
  color: #FFF
}

.CA-button--info:active {
  outline: 0
}

.CA-button--info.is-disabled,
.CA-button--info.is-disabled:active,
.CA-button--info.is-disabled:focus,
.CA-button--info.is-disabled:hover {
  color: #FFF;
  background-color: #c8c9cc;
  border-color: #c8c9cc
}

.CA-button--info.is-plain {
  color: #909399;
  background: #f4f4f5;
  border-color: #d3d4d6
}

.CA-button--info.is-plain:focus,
.CA-button--info.is-plain:hover {
  background: #909399;
  border-color: #909399;
  color: #FFF
}

.CA-button--info.is-plain:active {
  background: #82848a;
  border-color: #82848a;
  color: #FFF;
  outline: 0
}

.CA-button--info.is-plain.is-disabled,
.CA-button--info.is-plain.is-disabled:active,
.CA-button--info.is-plain.is-disabled:focus,
.CA-button--info.is-plain.is-disabled:hover {
  color: #bcbec2;
  background-color: #f4f4f5;
  border-color: #e9e9eb
}
// 新增button样式





.CA-button--text,
.CA-button--text.is-disabled,
.CA-button--text.is-disabled:focus,
.CA-button--text.is-disabled:hover,
.CA-button--text:active {
  border-color: transparent
}

.CA-button--medium {
  padding: 10px 20px;
  font-size: 14px;
  border-radius: 4px
}

.CA-button--mini,
.CA-button--small {
  font-size: 12px;
  border-radius: 3px
}

.CA-button--medium.is-round {
  padding: 10px 20px
}

.CA-button--medium.is-circle {
  padding: 10px
}

.CA-button--small,
.CA-button--small.is-round {
  padding: 9px 15px
}

.CA-button--small.is-circle {
  padding: 9px
}

.CA-button--mini,
.CA-button--mini.is-round {
  padding: 7px 15px
}

.CA-button--mini.is-circle {
  padding: 7px
}

.CA-button--text {
  color: #409EFF;
  background: 0 0;
  padding-left: 0;
  padding-right: 0
}

.CA-button--text:focus,
.CA-button--text:hover {
  color: #66b1ff;
  border-color: transparent;
  background-color: transparent
}

.CA-button--text:active {
  color: #3a8ee6;
  background-color: transparent
}

.CA-button-group {
  display: inline-block;
  vertical-align: middle
}

.CA-button-group::after,
.CA-button-group::before {
  display: table;
  content: ""
}

.CA-button-group::after {
  clear: both
}

.CA-button-group>.CA-button {
  float: left;
  position: rCAative
}

.CA-button-group>.CA-button+.CA-button {
  margin-left: 0
}

.CA-button-group>.CA-button:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0
}

.CA-button-group>.CA-button:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0
}

.CA-button-group>.CA-button:first-child:last-child {
  border-radius: 4px
}

.CA-button-group>.CA-button:first-child:last-child.is-round {
  border-radius: 20px
}

.CA-button-group>.CA-button:first-child:last-child.is-circle {
  border-radius: 50%
}

.CA-button-group>.CA-button:not(:first-child):not(:last-child) {
  border-radius: 0
}

.CA-button-group>.CA-button:not(:last-child) {
  margin-right: -1px
}

.CA-button-group>.CA-dropdown>.CA-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--primary:first-child {
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--primary:last-child {
  border-left-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--primary:not(:first-child):not(:last-child) {
  border-left-color: rgba(255, 255, 255, .5);
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--success:first-child {
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--success:last-child {
  border-left-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--success:not(:first-child):not(:last-child) {
  border-left-color: rgba(255, 255, 255, .5);
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--warning:first-child {
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--warning:last-child {
  border-left-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--warning:not(:first-child):not(:last-child) {
  border-left-color: rgba(255, 255, 255, .5);
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--danger:first-child {
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--danger:last-child {
  border-left-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--danger:not(:first-child):not(:last-child) {
  border-left-color: rgba(255, 255, 255, .5);
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--info:first-child {
  border-right-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--info:last-child {
  border-left-color: rgba(255, 255, 255, .5)
}

.CA-button-group .CA-button--info:not(:first-child):not(:last-child) {
  border-left-color: rgba(255, 255, 255, .5);
  border-right-color: rgba(255, 255, 255, .5)
}
</style>
