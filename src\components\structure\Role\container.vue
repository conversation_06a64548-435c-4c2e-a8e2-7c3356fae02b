<template>
  <div class="container">
    <div v-if="!$store.state.sa.SARoleListData.tabCon">
      <div class="header">
        <div class="title">角色信息</div>
        <el-button
          type="primary"
          class="newMenu"
          @click="newMenu()"
          icon="plus"
          v-if="
            $store.state.sa.SARoleListData.systemCode != '' ||
            $store.state.sa.SARoleListData.systemCode != null
          "
          ><span>新建角色组</span></el-button
        >
      </div>
      <div class="body" v-loading="loading">
        <el-table
          :data="tableData.records"
          style="width: 100%; color: rgb(101, 101, 101)"
        >
          <el-table-column prop="orderNum" label="序号" width="80">
          </el-table-column>
          <el-table-column prop="groupName" label="角色组名称">
          </el-table-column>
          <el-table-column align="right" label="操作">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(scope.$index, scope.row)"
                ><i class="el-icon-edit"></i
              ></el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                ><i class="el-icon-delete"></i
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
          :current-page.sync="currentPage3"
          :page-size="tableData.pageSize"
          layout="prev, pager, next, jumper"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
    </div>
    <div v-else>
      <div class="header">
        <div class="title">角色组信息</div>
      </div>
      <div class="roleGroupInfo">
        <el-row>
          <el-col :span="8"
            ><div>
              角色组名称:{{ $store.state.sa.SARoleListData.groupName }}
            </div></el-col
          >
          <el-col :span="8"
            ><div></div>
            序号:{{ $store.state.sa.SARoleListData.orderNum }}</el-col
          >
          <el-col :span="8"
            ><div></div>
            所属角色组:</el-col
          >
        </el-row>
      </div>
      <div class="header">
        <div class="title">子组信息</div>
        <el-button
          type="primary"
          class="newMenu"
          @click="newMenu()"
          icon="plus"
          v-if="
            $store.state.sa.SARoleListData.systemCode != '' ||
            $store.state.sa.SARoleListData.systemCode != null
          "
          ><span>添加角色组</span></el-button
        >
      </div>
      <div class="body" v-loading="loading">
        <el-table
          :data="tableData.records"
          style="width: 100%; color: rgb(101, 101, 101)"
        >
          <el-table-column prop="orderNum" label="序号" width="80">
          </el-table-column>
          <el-table-column prop="groupName" label="角色组名称">
          </el-table-column>
          <el-table-column align="right" label="操作">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(scope.$index, scope.row)"
                ><i class="el-icon-edit"></i
              ></el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                ><i class="el-icon-delete"></i
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
          :current-page.sync="currentPage3"
          :page-size="tableData.pageSize"
          layout="prev, pager, next, jumper"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
      <div class="header" style="padding-top: 25px">
        <div class="title">包含角色</div>
        <el-button
          type="primary"
          class="newMenu"
          @click="newMenus()"
          icon="plus"
          v-if="
            $store.state.sa.SARoleListData.systemCode != '' ||
            $store.state.sa.SARoleListData.systemCode != null
          "
          ><span>添加角色</span></el-button
        >
      </div>
      <div class="body" v-loading="loading">
        <el-table
          :data="tableDatas.records"
          style="width: 100%; color: rgb(101, 101, 101)"
        >
          <el-table-column prop="orderNum" label="序号" width="80">
          </el-table-column>
          <el-table-column prop="roleName" label="角色组名称">
          </el-table-column>
          <el-table-column align="right" label="操作">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="success"
                @click="menuEdit(scope.$index, scope.row)"
                style="
                  color: #fff;
                  background-color: #67c23a;
                  border-color: #67c23a;
                "
                ><i class="el-icon-postcard"></i
              ></el-button>
              <el-button
                size="small"
                type="warning"
                @click="jurisEdit(scope.$index, scope.row)"
                style="
                  color: #fff;
                  background-color: #e6a23c;
                  border-color: #e6a23c;
                "
                ><i class="el-icon-s-custom"></i
              ></el-button>
              <el-button
                size="small"
                type="primary"
                @click="handleEdits(scope.$index, scope.row)"
                ><i class="el-icon-edit"></i
              ></el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDeletes(scope.$index, scope.row)"
                ><i class="el-icon-delete"></i
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
          :current-page.sync="currentPage3"
          :page-size="tableData.pageSize"
          layout="prev, pager, next, jumper"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
    ></DialogTable>
    <DialogTables
      ref="childs"
      :dialogTableVisibles="dialogTableVisibles"
    ></DialogTables>
    <MenuEdit
      ref="childes"
      :dialogTableVisiblees="dialogTableVisiblees"
    ></MenuEdit>
    <JurisEdit
      ref="childed"
      :dialogTableVisibleed="dialogTableVisibleed"
    ></JurisEdit>
  </div>
</template>

<script>
import { getMenuList, saveMenuTable, deleteByMenuId } from "../../../api/user";
import {
  getRoleList,
  getRoleGroupList,
  deleteByGroupId,
  deleteByGroupIds,
} from "../../../api/role";
import DialogTable from "./table";
import DialogTables from "./roletable";
import MenuEdit from "./menuedit";
import JurisEdit from "./jurisedit";
import { mapState, mapGetters } from "vuex";
import Bus from "../../../utils/bus";
export default {
  name: "Container",
  //import引入的组件
  components: {
    DialogTable,
    DialogTables,
    MenuEdit,
    JurisEdit,
  },
  data() {
    return {
      dialogTableVisible: true,
      dialogTableVisibles: true,
      dialogTableVisiblees: true,
      dialogTableVisibleed: true,
      currentPage3: 1,
      tableData: {},
      tableDatas: {},
      loading: false,
    };
  },
  computed: {
    listenMenuList() {
      return this.$store.state.sa.SARoleListData;
    },
  },
  //方法集合
  methods: {
    //打开编辑弹框
    handleEdit(index, row) {
      this.dialogTableVisible = true;
      this.$refs.child.parentMsg(this.dialogTableVisible);
      this.$refs.child.getData(row.id);
    },
    handleEdits(index, row) {
      this.dialogTableVisibles = true;
      this.$refs.childs.parentMsg(this.dialogTableVisibles);
      this.$refs.childs.getData(row.id);
    },
    menuEdit(index, row) {
      this.dialogTableVisiblees = true;
      this.$refs.childes.parentMsg(this.dialogTableVisiblees);
      this.$refs.childes.getData(
        this.$store.state.sa.SARoleListData.systemCode,
        row.id
      );
      this.$refs.childes.getTreeData(
        this.$store.state.sa.SARoleListData.systemCode
      );
    },
    jurisEdit(index, row) {
      this.dialogTableVisibleed = true;
      this.$refs.childed.parentMsg(this.dialogTableVisibleed);
      this.$refs.childed.getData(
        this.$store.state.sa.SARoleListData.systemCode,
        row.id
      );
      this.$refs.childed.getTreeData(
        this.$store.state.sa.SARoleListData.systemCode
      );
    },
    handleDelete(index, row) {
      this.$confirm("确定要删除选择的数据吗？", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteByGroupId({ id: row.id })
            .then((res) => {
              if (res.data.code == 0) {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.getMenuData(this.$store.state.sa.SARoleListData, 1);
                this.$emit("pf");
              }
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    handleDeletes(index, row) {
      this.$confirm("确定要删除选择的数据吗？", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteByGroupIds({ id: row.id })
            .then((res) => {
              console.log(res,'ress')
              if (res.data.code == 0) {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.getMenuData(this.$store.state.sa.SARoleListData, 1);
                this.$emit("pf");
              }else {

              }
            })
            .catch((e) => {
              console.log(e, "请求错误");
              this.$message({
                  message: e.data.msg,
                  type: "error",
                });
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    fatherMethod() {
      this.getMenuData(this.$store.state.sa.SARoleListData, 1);
      this.$emit("pf");
    },
    //新建菜单
    newMenu() {
      this.dialogTableVisible = true;
      this.$refs.child.parentMsg(this.dialogTableVisible);
      this.$refs.child.clearTable();
      //   this.$refs.child.getTree(this.$store.state.sa.SARoleListData.systemCode);
    },
    newMenus() {
      this.dialogTableVisibles = true;
      this.$refs.childs.parentMsg(this.dialogTableVisibles);
      this.$refs.childs.clearTable();
      //   this.$refs.child.getTree(this.$store.state.sa.SARoleListData.systemCode);
    },
    //获取菜单信息列表
    getMenuData(val, pageNo) {
      console.log(val);
      this.loading = true;
      getRoleList({
        systemCode: val.systemCode,
        parentId: val.menuId || "",
        pageNo: pageNo || 1,
        pageSize: 8,
      })
        .then((res) => {
          // console.log(res.data.data);
          this.tableData = res.data.data;
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
      if (this.$store.state.sa.SARoleListData.tabCon) {
        getRoleGroupList({
          groupId: val.menuId,
          pageNo: pageNo || 1,
          pageSize: 8,
        })
          .then((res) => {
            // console.log(res.data.data);
            this.tableDatas = res.data.data;
            this.loading = false;
          })
          .catch((e) => {
            this.loading = false;
            console.log(e, "请求错误");
          });
      }
    },

    handleSizeChange() {},
    handleCurrentChange(data) {
      this.getMenuData(this.$store.state.sa.SARoleListData, data);
    },
    bus() {
      var vm = this;
      // 用$on事件来接收参数
      Bus.$on("SARoleListData", (data) => {
        this.getMenuData(data);
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.bus();
  },
  //   watch: {
  //     listenMenuList: function (newVal, oldVal) {
  //       this.getMenuData(newVal);
  //     },
  //   },
};
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 15px;

  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .newMenu {
      width: 120px;
      height: 38px;
      padding: 10px;
      color: #fff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .title {
      font-weight: 900;
      font-size: 16px;
      color: #000;
    }
  }
  .pagination {
    float: right;
    margin-top: 30px;
  }
}
.roleGroupInfo {
  padding: 25px 15px;
}
</style>
