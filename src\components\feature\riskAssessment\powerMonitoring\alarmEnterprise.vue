<template>
  <div class="enterpriseManagement">
    <div>
      <!-- {{districtCode}} -->
      <!-- v-if="isShowDist && showArea" -->
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtCode"
            v-if="isShowDist && showArea"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            :show-all-levels="true"
          ></el-cascader>
          <el-select
            v-model="enterpriseStatus"
            size="mini"
            placeholder="请选择企业当前状态"
            clearable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="alarmType"
            size="mini"
            placeholder="请选择预警类型"
            clearable
          >
            <el-option
              v-for="item in options1"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <!-- <el-input
            v-model="enterpriseId"
            placeholder="请输入企业名称"
            class="input"
            clearable
            style="width: 200px"
            size="mini"
          ></el-input> -->
          <!-- {{queryParams.orgCode}} -->
          <div style="width: 200px">
            <el-autocomplete
              popper-class="my-autocomplete"
              v-model="queryParams.orgName"
              :fetch-suggestions="querySearch"
              placeholder="请输入企业名称关键字"
              clearable
              @clear="clearSensororgCode()"
              @select="handleSelect"
              size="mini"
              style="width: 200"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.enterpName }}</div>
              </template>
            </el-autocomplete>
          </div>
          <!-- <el-date-picker
            v-model="date"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            clearable
          >
          </el-date-picker> -->
          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >
          <CA-button type="primary" size="mini" plain @click="exportExcel()"
            >导出</CA-button
          >
        </div>
      </div>
      <div class="table-main">
        <div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="enterpriseName"
                label="单位名称"
                min-width="180"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.enterpriseName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="行政区划"
                width="150"
                align="center"
                prop="districtName"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.districtName }}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column label="电力用户-没有这个字段" width="120" align="center">
                <template slot-scope="{ row  }">
                  <div>
                    {{ row.highestRankLevel }}
                  </div>
                </template>
              </el-table-column> -->
              <el-table-column
                label="监管状态"
                min-width="80"
                align="center"
                prop="enterpriseStatusName"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.enterpriseStatusName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="报警类型"
                min-width="80"
                align="center"
                prop="alarmType"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.alarmType }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="报警生成时间"
                min-width="110"
                align="center"
                prop="alarmTime"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.alarmTime }}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column label="状态" min-width="110" align="center">
                <template slot-scope="{ row  }">
                  <div>
                    {{ row.endTime }}
                  </div>
                </template>
              </el-table-column> -->

              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="{ row }">
                  <div>
                    <!-- <el-button
                      type="text"                    
                      @click="disposalSituationBool(row)"                    
                      >反馈</el-button
                    >
                    <el-button                   
                      type="text"
                      @click="NotificationBool(row)"
                      >通报</el-button
                    > -->
                    <el-button type="text" @click="WarnBool(row)"
                      >详情</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.pageSize"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- <alarmDeatil :detailAlarmDialog='detailAlarmDialog' @closeCenterDialog='closeCenterDialog'  ref="alarmDeatil"></alarmDeatil> -->

    <!-- 弹窗 -->
    <el-dialog
      title="报警详情"
      :visible.sync="detailAlarmDialog"
      width="1000px"
      @open="open()"
      :close-on-click-modal="false"
    >
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="监管通报" name="1"></el-tab-pane>
        <el-tab-pane label="企业反馈" name="0"></el-tab-pane>
      </el-tabs> -->
      <div class="warn">
        <!-- <div class="null" v-if="tableData == null"></div> -->
        <div class="div1">
          <div class="table">
            <ul class="container">
              <li class="lang">
                <div class="l">企业名称</div>
                <div class="r">{{ detailData.enterpriseName }}</div>
              </li>
                <li class="lang">
                <div class="l">行政区划</div>
                <div class="r">{{ detailData.districtName }}</div>
              </li>
              <li class="lang">
                <div class="l">企业编码</div>
                <div class="r">{{ enterData.enterpId }}</div>
              </li>
              <li class="lang">
                <div class="l">企业负责人</div>
                <div class="r">{{ enterData.legalrepper }}</div>
              </li>
              <li class="lang">
                <div class="l">负责人联系电话</div>
                <div class="r">
                  {{ enterData.legalreppTel }}
                </div>
              </li>
              <li class="lang">
                <div class="l">当前状态</div>
                <div class="r">
                  {{ detailData.enterpriseStatusName }}
                </div>
              </li>
              <!-- <li class="lang">
                <div class="l">电力户号</div>
                <div class="r">
                  {{ detailData }}
                </div>
              </li> -->
              <li class="lang">
                <div class="l">报警类型</div>
                <div class="r">
                  {{ detailData.alarmType }}
                </div>
              </li>
            
              <li class="lang">
                <div class="l">报警时间</div>
                <div class="r">
                  {{ detailData.alarmTime }}
                </div>
              </li>
                <li class="lang bottom">
                <div class="l">规则说明</div>
                <div class="r">
                  {{ detailData.alarmRule }}
                </div>
              </li>
              <li class="lang bottom">
                <div class="l">持续天数(天)</div>
                <div class="r">
                  {{ detailData.alarmDays }}
                </div>
              </li>

              
            </ul>
          </div>
        </div>
      </div>

      <!-- <div v-if='echartData.length==0' style="height:300px;text-align:center;line-height:300px">
        暂无用电量数据！
      </div> -->

      <div style="position:relative">
        <div class="echartTit">电量单位(kwh)</div>
        <div
          class="echarts"
          id="detailEchart"
          style="width: 1000px; height: 300px"
        ></div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAlarmType, //报警类型下拉列表
  getElectricityFindPage, //报警数据分页查询
  getElectricityExport, //导出
  getElectricityFindById, //根据alarmId查询报警详情
  getEnterpConsumption, //企业用电量查询
  getElectricityPage, //分页查询
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getInformationBasicInfo } from "@/api/entList";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";
import alarmDeatil from "./alarmDeatil";
import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {
    alarmDeatil,
  },
  data() {
    return {
      echartData: [],
      queryParams: {
        orgCode: "",
        orgName: "",
      },
      detailData: {},
      enterData: {},
      detailAlarmDialog: false,
      tableCheck: true,
      loading: false,
      areaName: "",
      tableData: {},
      options: [
        {
          value: "1",
          label: "停工",
        },
        {
          value: "0",
          label: "试生产",
        },
        {
          value: "2",
          label: "正常开工",
        },
      ],
      options1: [],
      startTime: "",
      endTime: "",
      alarmType: "", //报警类型
      alarmId: "",
      enterpriseStatus: "", //企业监管状态
      enterpriseId: "", //企业id
      creditCode: "", //统一信用代码
      districtCode: this.$store.state.login.userDistCode, //行政区划代码
      currentPage: 1,
      selection: [],
      // showBreak: false,
      district: this.$store.state.controler.district,
      // districtVal: this.$store.state.login.userDistCode,
      // date: [
      //   new Date(new Date().toLocaleDateString()).getTime() -
      //     (720 * 60 * 60 * 1000 - 1),
      //   new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      // ],
      // // date:"",
      // startDate: "",
      // endDate: "",
      showArea: true,
      disposalSituationShow: false,
      receivingUnit: [],
      reasonDescription: "",
      notificationContent: "",
      nowtime: new Date(new Date()).Format("yy年MM月dd日 hh时mm分"),
      park: "",
      listId: "",
      title: "风险预警信息核查、处置情况报告",
      disabled: false,
    };
  },

  methods: {
    //自动输入
    clearSensororgCode() {
      this.queryParams.orgCode = "";
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.orgCode = item.enterpId;
      this.queryParams.orgName = item.enterpName;
    },
    //自动输入end
    //信息复核状态列表下拉框
    serchStatusList() {
      getAlarmType().then((res) => {
        if (res.data.status == 200) {
          this.options1 = res.data.data;
        }
      });
    },
    //弹奏执行echart
    open() {
      // const _self = this;
      // setTimeout(() => {
      //   _self.chartData();
      // }, 0);
    },
    closeCenterDialog() {
      this.detailAlarmDialog = false;
    },
    disposalSituationBool(row) {
      this.disposalSituationShow = true;
      this.listId = row.id;
      getSelCompany(row.id)
        .then((res) => {
          this.receivingUnit = res.data.data.receivingUnit;
        })
        .catch((error) => {
          reject(error);
        });
    },

    //填报信息
    NotificationBool(row) {
      this.$refs.Notification.closeBoolean(true);
      this.$refs.Notification.getData(row);
    },
    WarnBool(row) {
      // this.$refs.alarmDeatil.closeBoolean(true);
      this.detailAlarmDialog = true;
      this.detailFn(row.alarmId);
      this.enterDataFn(row.enterpriseId);
      this.$nextTick(() => {
        this.chartData(row);
      });
      // this.$refs.alarmDeatil.chartInt()
      // console.log(row);
      // this.$refs.alarmDeatil.getData(row.id, row.companyCode, row.companyName);
    },
    //查详情
    detailFn(alarmId) {
      getElectricityFindById({ alarmId: alarmId }).then((res) => {
        if (res.data.status == 200) {
          this.detailData = res.data.data;
        }
      });
    },
    //查企业详情
    enterDataFn(enterpriseId) {
      getInformationBasicInfo(enterpriseId).then((res) => {
        this.enterData = res.data.data.enterprise;
      });
    },
    searchTime(value) {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd hh:mm:ss");
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    getData() {
      if (this.$store.state.login.user.user_type == "ent") {
        this.showArea = false;
        getEnt({}).then((res) => {
          if (res.data.code == 0) {
            this.getDataes(res.data.data.enterpId);
          }
        });
      } else {
        this.getDataes(null);
      }
    },
    getDataes(id) {
      console.log(this.$store, "缓存一些信息");
      this.loading = true;
      getElectricityFindPage({
        creditCode: "", //统一信用代码
        districtCode: this.districtCode, //行政区划代码
        enterpriseId: id ? id : this.queryParams.orgCode, //企业id
        enterpName: this.queryParams.orgName, //企业名称
        enterpriseStatus: this.enterpriseStatus, //监管状态0试生产1停工
        alarmId: "alarmId", //报警id
        alarmType: this.alarmType,
        endTime: "",
        startTime: "",
        pageSize: 10,
        nowPage: this.currentPage,
      }).then((res) => {
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
    // 导出
    exportExcel() {
      console.log(32);
      getElectricityExport({
        // idList: this.selection,
        creditCode: "", //统一信用代码
        districtCode: this.districtCode, //行政区划代码
        enterpriseId: this.queryParams.orgCode, //企业id
        enterpName: this.queryParams.orgName, //企业名称
        enterpriseStatus: this.enterpriseStatus, //监管状态0试生产1停工
        alarmId: "alarmId", //报警id
        alarmType: this.alarmType,
        endTime: "",
        startTime: "",
        pageSize: 0,
        nowPage: 0,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业用电报警" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },

    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
      this.$refs.DetailTable.getDistrict();
    },
    openDialoges(distCode, type, areaName) {
      this.$refs.DetailTablees.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTablees.getEntData(distCode, type, areaName);
      this.$refs.DetailTablees.getDistrict();
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleCurrentChange(val) {
      this.getData();
    },
    //
    chartData(row) {
      var alarmTime = dayjs(row.alarmTime);
      var parme = {
        creditCode: row.creditCode,
        enterpriseId: row.enterpriseId, // enterpriseId:'*********',
        startDate: alarmTime.subtract(15, "day").format("YYYY-MM-DD"),
        endDate: alarmTime.subtract(-15, "day").format("YYYY-MM-DD"),
      };
      getEnterpConsumption(parme).then((res) => {
        console.log(this.echartData);
        this.echartData = [];
        var nameValue = [];
        var numValue = [];
        if (res.data.status == 200) {
          this.echartData = res.data.data.electricityConsumptions;
          this.echartData.forEach((element) => {
            nameValue.push(element.date);
            numValue.push(element.electricityConsumption);
          });
        }

        this.chartInt(nameValue, numValue);
      });
    },
    //弹出框echart
    chartInt(nameValue, numValue) {
      console.log(this.detailData, "拿到highThreshold和lowThreshold");

      var chartDom = document.getElementById("detailEchart");
      var myChart = this.$echarts.init(chartDom);
      var option;
      var start = nameValue.length - 15;
      var end = nameValue.length - 1;

      option = {
        tooltip: {
          trigger: "axis",
          // position: function (pt) {
          //   return [pt[0], "10%"];
          // },
        },
        title: {
          show: !numValue.length, // 无数据时展示 title
          textStyle: {
            color: "#000000a6",
            fontSize: 16,
          },
          text: "暂无数据",
          left: "center",
          top: "center",
        },
        // title: {
        //   left: "center",
        //   text: "Large Area Chart",
        // },
        toolbox: {
          show: false,
          feature: {
            dataZoom: {
              yAxisIndex: "none",
            },
            dataView: { readOnly: false },
            magicType: { type: ["line", "bar"] },
            restore: {},
            saveAsImage: {},
          },
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          // data: date,
          data: nameValue,
          // axisLine: {
          //   //x轴线的颜色以及宽度
          //   show: true,
          //   lineStyle: {
          //     color: "#f0f2f5",
          //     // width: 1,
          //     type: "solid",
          //   },
          // },

          splitLine: {
            //分割线配置
            show: true,
            lineStyle: {
              type: "dashed",
              color: "#e1dfdf78",
            },
          },
        },
        yAxis: {
          type: "value",
          // boundaryGap: [0, "100%"],
          axisLabel: {
            formatter: "{value}",
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed",
              color: "#e1dfdf78",
            },
          },
        },
        dataZoom: [
          {
            show: true,
            type: 'slider',
            handleSize: 32, // 两边的按钮大小
            startValue: start,
            endValue: end,
          },
          {
             type: "inside",
          }
          // {
          //   start: 0,
          //   end: 10,
          // },
        ],
        series: [
          {
            // name: 'Highest',
            type: "line",
            // 变得圆滑
            smooth: true,
            // 隐藏折点
            showSymbol: false,
            data: numValue,
            // itemStyle: {
            //   color: "#f76c41",
            // },
            // 改变线条的颜色和粗细
            lineStyle: {
                color: '#f76c41',
                width: 3,
                shadowColor: 'rgba(247, 108, 65, 0.5)',//设置折线阴影
                shadowBlur: 5,
                shadowOffsetY: 10,
            },
            markPoint: {
              data: [
                { type: "max", name: "Max" },
                { type: "min", name: "Min" },
              ],
            },
            markLine: {
              data: [
                // { type: "max", name: "highThreshold" },
                // { type: "min", name: "lowThreshold" },
                // 也可以是某个值
                {
                  name: "lowThreshold",
                  yAxis: this.detailData.lowThreshold,
                  lineStyle: { color: "#ffd200" },
                  label: {
                    color: "#000",
                    formatter: "最低用电量",
                    fontSize: 10,
                  },
                },
                {
                  name: "highThreshold",
                  yAxis: this.detailData.highThreshold,
                  lineStyle: { color: "#f76c41" },
                  label: {
                    color: "#000",
                    formatter: "最高用电量",
                    fontSize: 10,
                  },
                },
              ],
              // lineStyle: {
              //   // color: 'rgba(255,88,0,0.7)',
              //   // 线的样式，emphasis调整hover后的样式
              //   color: "rgba(0,255,0,0.9)",
              //   // type: "solid",
              // },
            },
          },
          // {
          //   name: "Fake Data",
          //   type: "line",
          //   symbol: "none",
          //   sampling: "lttb",
          //   itemStyle: {
          //     color: "rgb(255, 70, 131)",
          //   },
          //   areaStyle: {
          //     color: this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
          //       {
          //         offset: 0,
          //         color: "rgb(255, 158, 68)",
          //       },
          //       {
          //         offset: 1,
          //         color: "rgb(255, 70, 131)",
          //       },
          //     ]),
          //   },
          //   // data: data,
          //   data: [8, 3, 8, 7, 11,4,5,8],
          // },
        ],
        // noDataLoadingOption: {
        //   text: "暂无数据", //提示文本
        //   effect: "bubble", //效果图
        //   effectOption: {
        //     backgroundColor: "rgba(0,0,0,0)", //背景颜色
        //     effect: {
        //       n: 0, //个数控制
        //     },
        //   },
        // },
      };

      option && myChart.setOption(option);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    this.serchStatusList();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
//弹框样式
.warn {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .null {
    width: 467px;
    height: 285px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 50%;
          display: flex;
          // border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 30%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 70%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
.echartTit {
  text-align: right;
  padding: 0 20px 0 0;
  position: absolute;
  right:0;
  top:0
}
</style>

