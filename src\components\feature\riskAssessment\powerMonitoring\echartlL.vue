<template>
  <div class="echartDetalBox">
    <div class="echartCon">
      <div
        class="echarts"
        id="securityCommitmentsEcharts"
        style="width: 250px; height: 250px"
      ></div>     
      <div class="echartCenter" v-if="chartTot!=0">
        <span class="name">监测企业</span><span class="num">{{chartTot}}</span>
      </div>
      <div class="chartList">
        <ul>
          <li v-for="(item, index) of chartListData" :key="index">
            <span class="squery" :style="{ background: item.color }"></span>
            {{ item.name
            }}<span class="num" :style="{ color: item.color }"
              > {{ item.value }} </span
            >家
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>
<script>
import { getSupervisePieChart } from "@/api/riskAssessment";
export default {
  data() {
    return {
      chartTot:0,
      chartListData: [
        { name: "开工企业", value: "0", color: "#66b1ff",type:"normal" },
        { name: "责令停工企业", value: "0", color: "#ffd200",type:'shutDown' },
        { name: "试生产企业", value: "0", color: "#f76c41",type:'preProduction' },
      ],
    };
  },
  methods: {
    initChart(echartData,noData) {
      var chartDom = document.getElementById("securityCommitmentsEcharts");
      //   var myChart = echarts.init(chartDom);
      var myChart = this.$echarts.init(chartDom);
      var option;

      option = {
        tooltip: {
          trigger: "item",
        },
        title: {
          show: noData, // 无数据时展示 title
          textStyle: {
            color: "#000000a6",
            fontSize: 16,
          },
          text: "暂无数据",
          left: "center",
          top: "center",
        },
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            // name: "Access From",
            type: "pie",
            radius: ["40%", "60%"],
            avoidLabelOverlap: false,
            color: ["#66b1ff", "#ffd200", "#f76c41"], //设置对应块的数据
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                show: false,
                fontSize: "40",
                fontWeight: "bold",
              },
            },
            labelLine: {
              show: false,
            },
            data:echartData
            // [
            //   { value: 1048, name: "开工企业", type: "normal" },
            //   { value: 735, name: "责令停工企业", type: "shutDown" },
            //   { value: 580, name: "试生产企业", type: "preProduction" },
            // ],
          },
        ],
      };

      option && myChart.setOption(option);
    },
    chartData() {
      var time = {
        districtCode:this.$store.state.login.userDistCode,
      };     
      getSupervisePieChart(time).then((res) => {        
        this.chartTot=0
        if (res.data.status == 200) {         
          var chartListObj = res.data.data;
          this.chartTot=Number(chartListObj.normal) + Number(chartListObj.preProduction) + Number(chartListObj.shutDown)

          this.chartListData.forEach((item) => {
            for (var obj in chartListObj) {                           
              if (obj == item.type) {
                item.value = chartListObj[obj];
              }
            }
          });

          var noData=this.chartListData.every((item)=>{
            return item.value==0
        }) 
          
          this.initChart(this.chartListData,noData)
        }
      });
    },
  },
  mounted() {
    this.chartData();
    // this.initChart();
  },
};
</script>
<style scoped lang='scss'>
.echartDetalBox {
  position: relative;
  .echartCon {
    display: flex;
    justify-content: center;
    align-items: center;
    .echartCenter {
      position: absolute;
      text-align: center;
      margin-left: -144px;
      span {
        display: block;
      }
      span.num {
        font-size: 18px;
        color: #000;
      }
    }
    .chartList ul {
      margin: 10px 0;
      padding: 10px 0;
      list-style: none;
      li {
        position: relative;
        padding: 0 0 0 20px;
        .squery {
          width: 10px;
          height: 10px;
          position: absolute;
          top: 50%;
          left: 0;
          margin-top: -5px;
        }
      }
      .num {
        font-size: 18px;
      }
    }
  }
}
</style>