<template>
  <div class="equipmentFacilities">
    <div>
      <div class="seach-part">
        <div class="l">
          <el-cascader
            v-show="this.$store.state.login.user.user_type == 'gov'"
            size="mini"
            placeholder="请选择地市/区县"
            :options="district"
            v-model="districtVal"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            
            @change="handleChange"
            :show-all-levels="true"
          ></el-cascader>

          <el-input
            v-show="
              this.$store.state.login.user.user_type == 'gov' ||
              this.$store.state.login.user.user_type == 'park'
            "
            v-model.trim="enterpName"
            size="mini"
            placeholder="请输入企业名称"
            class="input"
            clearable
            @clear="clearKey"
            style="width: 200px"
          ></el-input>
          <el-select
            v-model="ReportType"
            size="mini"
            placeholder="请选择报备类型"
            clearable
          >
            <el-option
              v-for="item in ReportTypeOpt"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="declareReason"
            size="mini"
            placeholder="请选择报备原因"
            clearable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <!-- <el-select
            v-model="sensortypeCode"
            size="mini"
            placeholder="请选择设备类型"
            clearable
            @clear="clearSensortypeCode()"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-select
            v-model="monitemkey"
            size="mini"
            placeholder="请选择指标类型"
            clearable
            @clear="clearMonitemkey()"
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-select
            v-model="warningType"
            size="mini"
            placeholder="请选择报警类型"
            clearable
            @clear="clearWarningType()"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-select
            v-model="state"
            size="mini"
            placeholder="请选择报警状态"
            clearable
            @clear="clearState()"
          >
            <el-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->

          <el-select
            v-model="state"
            size="mini"
            placeholder="请选择审核状态"
            clearable
            @clear="clearState()"
          >
            <el-option
              v-for="item in options3"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>

          <el-date-picker
            v-model="value1"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            style="width: 370px"
          ></el-date-picker>

          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
          <el-button
            v-if="this.$store.state.login.user.user_type == 'ent'"
            type="primary"
            size="mini"
            @click="addEdit('')"
            style="position: absolute; right: 0"
            >新增</el-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>异常报备列表</h2>
        </div>
        <div>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              @select="select"
              @select-all="select"
            >
              <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column>
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="distName"
                label="行政区划"
                align="center"
                width="180"
              >
              </el-table-column>
              <el-table-column
                prop="enterpName"
                label="单位名称"
                align="center"
                width="210"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span
                    @click="goEnt(scope.row)"
                    style="color: rgb(57, 119, 234); cursor: pointer"
                    class="enterpName"
                  >
                    {{ scope.row.enterpName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                prop="type"
                label="报备类型"
                align="center"
                width="110"
              >
                <template slot-scope="{ row }">
                  <span>{{ row.type == 0 ? "设备设施" : "视频设备" }}</span>
                </template>
              </el-table-column>
              <el-table-column
                prop="dangerName"
                label="重大危险源名称"
                align="center"
                min-width="150"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="equipment"
                label="设备名称"
                align="center"
                min-width="150"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="declareReason"
                label="报备原因"
                align="center"
                width="80"
              >
                <!-- <template slot-scope="scope">
                    <span v-if="scope.row.declareReason == '01'">故障检修</span>
                    <span v-else-if="scope.row.declareReason == '02'">设备损坏</span>
                    <span v-else-if="scope.row.declareReason == '03'">停工停产</span>
                    <span v-else-if="scope.row.declareReason == '04'">其它原因</span>
                </template> -->
              </el-table-column>
              <el-table-column
                prop="endTime"
                label="状态开始时间"
                align="center"
                width="155"
              >
              </el-table-column>
              <el-table-column
                prop="startTime"
                label="状态结束时间"
                align="center"
                width="155"
              >
              </el-table-column>
              <el-table-column prop="state" label="审核状态" align="center" width="110">
                <template slot-scope="scope">
                  <span v-if="scope.row.state == '0'" style="color: #ee8742"
                    >待审核</span
                  >
                  <span
                    v-else-if="scope.row.state == '1'"
                    style="color: #58bd77"
                    >审核通过</span
                  >
                  <span
                    v-else-if="scope.row.state == '2'"
                    style="color: #ff1818"
                    >审核驳回</span
                  >
                  <span
                    v-else-if="scope.row.state == '3'"
                    style="color: #909090"
                    >过期未审核</span
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                width="180"
                fixed="right"
              >
                <template slot-scope="scope">
                  <span
                    @click="abnormalEquipmentDel(scope.row)"
                    v-if="
                      scope.row.state == '0' && $store.state.login.user.user_type == 'ent'
                    "
                    style="
                      color: rgb(57, 119, 234);
                      margin-right: 10px;
                      cursor: pointer;
                    "
                  >
                    删除  
                  </span>
                  <span
                    @click="resumeEnable(scope.row)"
                    v-if="
                      scope.row.state == '1' && $store.state.login.user.user_type == 'ent' && compareTime(scope.row.startTime) 
                    "
                    style="
                      color: rgb(57, 119, 234);
                      margin-right: 10px;
                      cursor: pointer;
                    "
                  >
                    恢复启用
                  </span>
                  <span
                     v-if="
                      $store.state.login.user.user_type == 'gov' &&
                      $store.state.login.userDistCode != '420000' &&
                      scope.row.state == 0 &&
                      $store.state.login.user.isDanger == '1'
                    "
                    style="
                      color: rgb(57, 119, 234);
                      margin-right: 10px;
                      cursor: pointer;
                    "
                    @click="audit(scope.row.id, scope.row.enterpId,scope.row.type)"
                    >审核</span
                  >
                  <span
                    v-if="
                      $store.state.login.user.user_type == 'ent' &&
                      scope.row.state == 0
                    "
                    style="
                      color: rgb(57, 119, 234);
                      margin-right: 10px;
                      cursor: pointer;
                    "
                    @click="addEdit(scope.row.id,scope.row.type,scope.row.dangerid)"
                    >编辑</span
                  >
                  <span
                    style="
                      color: rgb(57, 119, 234);
                      margin-right: 10px;
                      cursor: pointer;
                    "
                    @click="detail(scope.row.id,scope.row.type)"
                    >详情</span
                  >
                  <span
                    v-if="scope.row.enclosure"
                    style="color: rgb(57, 119, 234); cursor: pointer"
                    @click="lookImg(scope.row.enclosure,scope.row.type)"
                    >附件</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="total"
              v-if="total != 0"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <AddEdit ref="addEdit"></AddEdit>
    <Audit ref="audit"></Audit>
    <Detail ref="detail"></Detail>
    <LookImg ref="lookImg"></LookImg>
  </div>
</template>
<script>
import {
  getAbnormalList,
  getEquipmentExportExcel,
  postAbnormalEquipmentResumeEnable,
  getAbnormalEquipmentDel
} from "@/api/dailySafety";
import { getDistrictUser } from "@/api/entList";
import { parseTime } from "@/utils/index";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import Audit from "./audit";
import AddEdit from "./addEdit";
import Detail from "./detail";
import LookImg from "./lookImg";

export default {
  components: {
    Audit,
    AddEdit,
    Detail,
    LookImg,
  },
  props: {
    entInfoData: {
      type: Object,
    },
  },
  data() {
    return {
      districtVal: this.$store.state.login.userDistCode || "",
      district: this.$store.state.controler.district,
      ReportType: "",
      declareReason:"",
      ReportTypeOpt: [
        {
          value: "0",
          label: "设备设施",
        },
        {
          value: "1",
          label: "视频报备",
        },
      ],
      options:[
        {
          value: "0",
          label: "离线",
        },
        {
          value: "1",
          label: "停产",
        },
      ],
      enterpName: "",
      value1: "",
      sensortypeCode: "",
      monitemkey: "",
      warningType: "",
      state: "",
      startTime: "",
      endTime: "",
      loading: true,
      areaName: "",
      selection: [],
      options3: [
        {
          value: "0",
          label: "待审核",
        },
        {
          value: "1",
          label: "审核通过",
        },
        {
          value: "2",
          label: "审核驳回",
        },
        {
          value: "3",
          label: "过期未审核",
        },
      ],
      tableData: [],
      distCode: this.$store.state.login.userDistCode || "",
      currentPage: 1,
      size: 10,
      total: "",
      enterpid: "",
      //   url: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg',
      //     srcList: [
      //       'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg',
      //       'https://fuss10.elemecdn.com/1/8e/aeffeb4de74e2fde4bd74fc7b4486jpeg.jpeg'
      //     ]
    };
  },
  methods: {
    abnormalEquipmentDel(row){
      this.$confirm("确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getAbnormalEquipmentDel(row.id).then((res) => {
            if (res.data.code === 0) {
              this.$message.success("删除成功");
              this.getIotMontoringDataList(this.enterpid);
            }else{
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    compareTime(val){
      if(val){
        var d = new Date(Date.parse(val.replace(/-/g,"/")));
        var curData = new Date();
        if(d > curData){
            return true
        }else{
            return false
        }
      }else{
        return true
      }
    },
    resumeEnable(row) {
      this.$confirm("确认恢复设备启用状态？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      })
        .then(() => {
          postAbnormalEquipmentResumeEnable(row.id).then((res) => {
            if (res.data.code === 0) {
              this.$message.success("成功恢复启用");
              this.getIotMontoringDataList(this.enterpid);
            }else{
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    //物联监测报警列表
    getIotMontoringDataList(id) {
      this.enterpid = id;
      this.loading = true;
      getAbnormalList({
        current: this.currentPage,
        distCode: this.distCode,   //districtVal
        size: this.size,
        enterpid: id || this.enterpid,
        type: this.ReportType,
        declareReason:this.declareReason,
        state: this.state,
        enterpName: this.enterpName,
        startTime: this.startTime,
        endTime: this.endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.startTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.startTime = "";
        this.endTime = "";
      }
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpid);
    },
    search() {
      this.currentPage = 1;
      this.getIotMontoringDataList(this.enterpid);
    },
    addEdit(id, type,dangerId) {
      var title=''
      if(id==''){
        title='新增'
      }else{
        title='编辑'
      }      
      // console.log(this.entInfoData, id, type,dangerId);
      this.$refs.addEdit.closeBoolean(true);
      this.$refs.addEdit.getData(this.entInfoData, id, type,dangerId,title);
    },
    lookImg(img, type) {
      this.$refs.lookImg.closeBoolean(true);
      this.$refs.lookImg.getData(img, type);
    },
    audit(id, enterpId,type) {
      this.$refs.audit.closeBoolean(true);
      this.$refs.audit.getData(id, enterpId,type);
    },
    detail(id,type) {
      this.$refs.detail.closeBoolean(true);
      this.$refs.detail.getData(id, type);
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getIotMontoringDataList(this.enterpid);
    },
    clearKey() {
      this.enterpName = "";
    },
    clearSensortypeCode() {
      this.sensortypeCode = "";
    },
    clearmonitemkey() {
      this.monitemkey = "";
    },
    clearwarningType() {
      this.warningType = "";
    },
    clearState() {
      this.state = "";
    },
    // 导出
    exportExcel() {
      //   let list = [this.distCode, ...this.selection];
      getEquipmentExportExcel({
        //   level: this.level.toString(),
        //   distCode: this.distCode,
        //   distCodeList: list.length <= 1 ? null : list,
        //   isContainParent: true,
        ids: this.selection.length <= 0 ? null : this.selection.join(","),
        distCode: this.distCode,
        // size: this.size,
        enterpid: this.enterpid,
        type: this.ReportType,
        state: this.state,
        enterpName: this.enterpName,
        startTime: this.startTime,
        endTime: this.endTime,
        declareReason:this.declareReason,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "设备异常报备信息" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    goToRunning() {
      this.$router.push({ path: `/riskAssessment/iotMontoringAlarm` });
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    //   debugger;
    //   console.log(this.entInfoData)
    //   this.entInfoDataes = this.entInfoData;
    // this.getDistrict();
    // this.getIotMontoringDataList(this.entInfoData.enterpId);
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  watch: {
    entInfoData(newValue, oldValue) {
      this.enterpId = newValue.enterpId;
    },
  },
};
</script>
<style lang="scss" scoped>
.equipmentFacilities {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    width: 100%;
    font-weight: 600;
    padding-bottom: 10px;
    margin-bottom: 0px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
/deep/ .l .el-input {
  width: 150px;
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
