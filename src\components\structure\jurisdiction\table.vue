<template>
  <div class="table">
    <el-dialog
      :title="showTree ? '新增' : '编辑'"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :before-close="handleClose"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableData"
        ref="tableData"
        v-loading="loading"
        label-width="150px"
      >
        <div class="inputBox">
          <el-form-item label="权限编码" prop="privCode" class="lable">
            <el-input
              v-model.trim="tableData.privCode"
              placeholder="权限编码"
              label="权限编码"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="权限类型" prop="privType">
            <el-select
              v-model="tableData.privType"
              placeholder="请选择权限类型"
            >
              <el-option label="功能点" value="2"></el-option>
              <el-option label="模块点" value="1"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="权限顺序" prop="privOrder" class="lable">
            <el-input
              v-model.trim="tableData.privOrder"
              placeholder="权限顺序"
              label="权限顺序"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="权限名称" prop="privName" class="lable">
            <el-input
              v-model.trim="tableData.privName"
              placeholder="权限名称"
              label="权限名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="所属组件名称" prop="moduleName" class="lable">
            <el-input
              v-model.trim="tableData.moduleName"
              placeholder="所属组件名称"
              label="所属组件名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="操作日志key" prop="privLogKey" class="lable">
            <el-input
              v-model.trim="tableData.privLogKey"
              placeholder="操作日志key"
              label="操作日志key"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox" v-if="showTree">
          <el-form-item label="同步到其他系统" prop="tenantId" class="lable">
            <el-input
              v-model.trim="tableData.tenantId"
              placeholder="请输入搜索内容"
              label="同步到其他系统"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="tree" v-if="showTree">
          <a-tree
            checkable
            :tree-data="treeData"
            @check="onCheck"
            v-model="checkedKeys"
          >
          </a-tree>
        </div>
        <el-button
          size="default"
          type="primary"
          class="commit"
          @click="handleSaveTable"
          >保存</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
// import {
// //   saveMenuTable,
// //   saveMenmenuByMenuId,
//   querySystemTreeNoSelf,
//   sameMenuFlagSystemCodes,
// } from "../../../api/user";
import {
  getSavePriv,
  savePrivByMenuId,
  querySystemTreeNoSelf,
  updataPrive,
} from "../../../api/jurisdiction";
import { menuRules } from "../../../api/rules";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
    showTree: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loading: false,
      tableData: {
        // menuId: "",
        // menuName: "",
        // menuAlias: "",
        // menuUrl: "",
        // iconUrl: "",
        // parentId: "",
        // systemCode: "",
        // moduleName: "",
        // menuOrder: null,
        // menuFlag: "",
        // tenantId: "",
        // belongSystemCode: "",
        parentId: "",
        tenantId: "",
        systemCode: "",
        belongSystemCode: "",
        privCode: "",
        privType: "",
        privOrder: "",
        privName: "",
        moduleName: "",
        privLogKey: "",
        id: "",
      },
      checkedKeys: [],
      rules: menuRules,
      parentId: "",
    };
  },
  //方法集合
  methods: {
    parentMsg(val, flag) {
      this.tableVisible = val;
      this.showTree = flag;
    },
    clearTable() {
      this.tableData = {};
    },
    handleSaveTable() {
      // this.loading = true;
      // this.bus();
      this.tableData.systemCode =
        this.$store.state.sa.SAPrivListData.systemCode;
      this.tableData.parentId =
        this.$store.state.sa.SAPrivListData.menuId || -1;
      // console.log(this.tableData);
      // console.log(this.$store.state.sa.SAPrivListData)
      this.$refs["tableData"].validate((valid) => {
        if (valid) {
          if (this.tableData.id) {
            updataPrive({ ...this.tableData })
              .then((res) => {
                // this.loading = false;
                this.tableVisible = false;
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.$parent.fatherMethod();
              })
              .catch((e) => {
                this.loading = false;
                console.log(e, "请求错误");
              });
          } else {
            getSavePriv({ ...this.tableData })
              .then((res) => {
                // this.loading = false;
                this.tableVisible = false;
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.$parent.fatherMethod();
              })
              .catch((e) => {
                this.loading = false;
                console.log(e, "请求错误");
              });
          }
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    getData(menuId) {
      this.loading = true;
      savePrivByMenuId({ privId: menuId })
        .then((res) => {
          this.loading = false;
          // console.log(res);
          this.tableData = res.data.data;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    getTree(data) {
      querySystemTreeNoSelf({ systemCode: data })
        .then((res) => {
          let data = res.data.data;
          for (let i = 0; i < data.length; i++) {
            data[i].title = data[i].systemName;
            data[i].key = data[i].systemCode;
          }
          // console.log(data);
          this.treeData = data;
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    getTreeData(data) {
      sameMenuFlagSystemCodes({ menuId: data })
        .then((res) => {
          let data = res.data.data;
          // console.log(data);
          this.checkedKeys = data.split(",");
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    onCheck(checkedKeys, e) {
      // console.log(checkedKeys, e)
      let key = JSON.stringify(checkedKeys);
      key = key.split('"').join("");
      key = key.split("[").join("");
      key = key.split("]").join("");
      // console.log(key);
      this.tableData.belongSystemCode = key;
    },
    handleClose(done) {
      this.$refs["tableData"].resetFields();
      done();
    },
    // bus() {
    //   var vm = this;
    //   // 用$on事件来接收参数
    //   Bus.$on("SAPrivListData", (data) => {
    //     vm.parentId=data.menuId;

    //   });
    //   console.log(vm.parentId);
    // },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input,
    .el-select {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
