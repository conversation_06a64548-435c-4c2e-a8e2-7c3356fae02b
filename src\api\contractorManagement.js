import axios from "axios";
import qs from "qs";

//承包商列表
export const getContractorManagementListData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyContractor/pageList/v1',
      data: data
    });
};
//承包商新增
export const addContractorManagementData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyContractor/add/v1',
      data: data
    });
};
//承包商修改
export const editContractorManagemenData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyContractor/update/v1',
      data: data
    });
};
//承包商详情
export const detailContractorManagemenData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyContractor/id/v1',
      data: data
    });
};
//承包商删除
export const deleteContractorManagemenData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyContractor/delete/v1',
      data: data
    });
};
//承包商参与作业类型
export const getCompanyParticularJobData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyParticularJob/type/all/v1',
      data: data
    });
};
//承包商导出
export const exportContractorManagemen = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/companyContractor/export/v1',
      data: data,
      responseType: "arraybuffer"
    });
};
