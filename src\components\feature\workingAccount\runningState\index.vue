<template>
  <div class="runningState">
    <div v-show="!TrendChartBool">
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box" @click="goToRunning">
                <a-icon type="home" theme="filled" class="icon" /> 运行状态分析
              </span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="showBreak">{{
              areaName
            }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <el-select
            v-model="level"
            multiple
            placeholder="危险源等级"
            size="mini"
            style="width: 270px"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button
            type="primary"
            size="mini"
            v-if="tableCheck"
            @click="getOnlineNetworkingList"
            >查询</el-button
          >

          <div v-else>
            <el-button
              type="primary"
              size="mini"
              v-if="addTableCheck"
              @click="clcEnterCountFn"
              >查询</el-button
            >
            <el-button
              type="primary"
              size="mini"
              v-else
              @click="getVideoRunningList"
              >查询</el-button
            >
          </div>

          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
        <CA-button type="primary" size="mini" plain @click="gotoTrendAnalysis"
          ><a-icon type="bar-chart" class="chart-icon" />趋势分析</CA-button
        >
      </div>
      <div class="table-main">
        <div class="table-top">
          <!-- <h2 v-show="showtable">重大危险源企业运行状态分析</h2> -->
          <el-radio-group
            size="small"
            v-model="modeed"
            style="margin-bottom: 10px"
          >
            <el-radio-button label="在线联网分析"></el-radio-button>
            <el-radio-button label="视频运行分析"></el-radio-button>
            <el-radio-button label="超量程"></el-radio-button>
          </el-radio-group>

          <!-- <div v-show="!showtable">
                <el-radio-group size="small" v-model="modees">
                    <el-radio-button label="物联监测"></el-radio-button>
                    <el-radio-button label="视频监测"></el-radio-button>
                </el-radio-group>
            </div> -->
          <CA-RadioGroup
            class="radio"
            v-model="mode"
            backgroundColor="#F1F6FF"
            border="1px solid rgba(57, 119, 234, 0.2)"
          >
            <CA-radio
              :label="{
                src: '../../../static/img/liebiao_icon.png',
                style: 'width:15px;height:15px',
              }"
              :labelTwo="{
                src: '../../../static/img/liebiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
              value="统计"
              bgColorActive="#409eff"
            >
            </CA-radio>
            <CA-radio
              :label="{
                src: '../../../static/img/tubiao_icon.png',
                style: 'width:15px;height:15px',
              }"
              :labelTwo="{
                src: '../../../static/img/tubiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
              bgColorActive="#409eff"
              value="图表"
            >
            </CA-radio>
          </CA-RadioGroup>
        </div>
        <div v-show="showtable">
          <div v-if="!addTableCheck">
            <!-- 在线联网分析 -->
            <div class="table" v-if="tableCheck">
              <el-table
                :data="tableData"
                v-loading="loading"
                style="width: 100%"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                ref="multipleTable"
                @selection-change="handleSelectionChange"
                :default-sort="{ prop: 'date', order: 'descending' }"
                @select="select"
                @select-all="select"
                @sort-change="changeTableSort"
              >
                <el-table-column type="selection" width="50" align="center">
                </el-table-column>
                <el-table-column label="行政区划" width="170" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div
                        v-if="
                          row.areaCode == 429004 ||
                          row.areaCode == 429005 ||
                          row.areaCode == 429006 ||
                          row.areaCode == 429021
                        "
                      >
                        {{ row.areaName }}
                      </div>
                      <div v-else>
                        <span
                          v-if="$index != 0 && !showBreak && isXiaZuan"
                          @click="xiaZuan(row.areaCode, row.areaName)"
                          style="color: #3977ea; cursor: pointer"
                          >{{ row.areaName }}</span
                        >
                        <span v-else>{{ row.areaName }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="应接入企业数" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div v-if="NetWork">
                        <span>{{ row.shouldLinkedNumber }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="已接入企业数" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div v-if="NetWork">
                        <span
                          v-if="row.linkedNumber != 0 || tableData.length == 1"
                          style="color: #3977ea; cursor: pointer"
                          @click="openDialog(row.areaCode, 1, row.areaName)"
                          >{{ row.linkedNumber }}</span
                        >
                        <span v-else>{{ row.linkedNumber }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="接入率" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div v-if="NetWork">
                        <span>{{ row.linkedRate + "%" }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="在线企业数"
                  sortable="custom"
                  align="center"
                  v-if="NetWork"
                  prop="onlineCompanyNum"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <span
                        v-if="
                          row.onlineCompanyNum != 0 || tableData.length == 1
                        "
                        style="color: #3977ea; cursor: pointer"
                        @click="openDialog(row.areaCode, 2, row.areaName)"
                        >{{ row.onlineCompanyNum }}</span
                      >
                      <span v-else>{{ row.onlineCompanyNum }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="离线企业数"
                  sortable="custom"
                  align="center"
                  v-if="NetWork"
                  prop="offlineCompanyNum"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <span
                        v-if="
                          row.offlineCompanyNum != 0 || tableData.length == 1
                        "
                        style="color: #3977ea; cursor: pointer"
                        @click="openDialog(row.areaCode, 3, row.areaName)"
                        >{{ row.offlineCompanyNum }}</span
                      >
                      <span v-else>{{ row.offlineCompanyNum }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="企业在线率"
                  sortable="custom"
                  align="center"
                  v-if="NetWork"
                  prop="onlineCompanyRate"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <!-- <span v-if="$index != 0 && row.offlineCompanyNum != 0" style="color: #3977ea" @click="openDialog(row.areaCode, 3, row.areaName)">{{ row.offlineCompanyNum }}</span> -->
                      <span>{{ row.onlineCompanyRate + "%" }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="tableCheck"
                  label="接入指标数"
                  sortable="custom"
                  align="center"
                  prop="linkedTargerNumber"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <!-- <span
                        v-if="$index != 0 && row.onlineTarger != 0"
                        style="color: #3977ea"
                        @click="openDialog(row.areaCode, 0, row.areaName)"
                        >{{ row.onlineTarger }}</span
                      > -->
                    <span>{{ row.linkedTargerNumber }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="在线质量"
                  sortable="custom"
                  align="center"
                  v-if="NetWork"
                  prop="onlineQuality"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <!-- <span v-if="$index != 0 && row.offlineCompanyNum != 0" style="color: #3977ea" @click="openDialog(row.areaCode, 3, row.areaName)">{{ row.offlineCompanyNum }}</span> -->
                      <span>{{ row.onlineQuality + "%" }}</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 视频运行分析 -->
            <div class="table" v-if="!tableCheck">
              <el-table
                :data="videoData"
                v-loading="loading"
                style="width: 100%"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                ref="multipleTable"
                @selection-change="handleSelectionChange"
                :default-sort="{ prop: 'date', order: 'descending' }"
                @select="select"
                @select-all="select"
                @sort-change="changeTableSort"
              >
                <el-table-column type="selection" width="50" align="center">
                </el-table-column>
                <el-table-column label="行政区划" width="170" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div
                        v-if="
                          row.areaCode == 429004 ||
                          row.areaCode == 429005 ||
                          row.areaCode == 429006 ||
                          row.areaCode == 429021
                        "
                      >
                        {{ row.areaName }}
                      </div>
                      <div v-else>
                        <span
                          v-if="$index != 0 && !showBreak && isXiaZuan"
                          @click="xiaZuan(row.areaCode, row.areaName)"
                          style="color: #3977ea; cursor: pointer"
                          >{{ row.areaName }}</span
                        >
                        <span v-else>{{ row.areaName }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="应接入企业数" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <span>{{ row.shouldAccessNum }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="已接入企业数" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div>
                        <span
                          v-if="row.yetAccessNum != 0 || videoData.length == 1"
                          style="color: #3977ea; cursor: pointer"
                          @click="openDialoges(row.areaCode, 0, row.areaName)"
                          >{{ row.yetAccessNum }}</span
                        >
                        <span v-else>{{ row.yetAccessNum }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="接入率" align="center">
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <div>
                        <!-- <span
                        v-if="$index != 0 && row.accessRate != 0"
                        style="color: #3977ea"
                        @click="openDialog(row.areaCode, 0, row.areaName)"
                        >{{ row.accessRate +"%"}}</span
                      > -->
                        <span>{{ row.accessRate + "%" }}</span>
                      </div>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="在线企业数"
                  sortable="custom"
                  align="center"
                  prop="onlineNum"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <span
                        v-if="row.onlineNum != 0"
                        style="color: #3977ea; cursor: pointer"
                        @click="
                          openDialoges(row.areaCode, 1, row.areaName) ||
                            videoData.length == 1
                        "
                        >{{ row.onlineNum }}</span
                      >
                      <span v-else>{{ row.onlineNum }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="离线企业数"
                  sortable="custom"
                  align="center"
                  prop="offlineNum"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <span
                        v-if="row.offlineNum != 0 || videoData.length == 1"
                        style="color: #3977ea; cursor: pointer"
                        @click="openDialoges(row.areaCode, 2, row.areaName)"
                        >{{ row.offlineNum }}</span
                      >
                      <span v-else>{{ row.offlineNum }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  label="企业在线率"
                  sortable="custom"
                  align="center"
                  prop="onlineRate"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <!-- <span v-if="$index != 0 && row.offlineNum != 0" style="color: #3977ea" @click="openDialoges(row.areaCode, 2, row.areaName)">{{ row.offlineNum }}</span> -->
                      <span>{{ row.onlineRate + "%" }}</span>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="!tableCheck"
                  label="监控点位数"
                  sortable="custom"
                  align="center"
                  prop="monitorPoint"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <!-- <span
                        v-if="$index != 0 && row.monitorPoint != 0"
                        style="color: #3977ea"
                        @click="openDialog(row.areaCode, 0, row.areaName)"
                        >{{ row.monitorPoint }}</span
                      > -->
                    <span>{{ row.monitorPoint }}</span>
                  </template>
                </el-table-column>

                <el-table-column
                  label="在线质量"
                  sortable="custom"
                  align="center"
                  prop="onlineQualityRate"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <div>
                      <!-- <span v-if="$index != 0 && row.offlineNum != 0" style="color: #3977ea" @click="openDialoges(row.areaCode, 2, row.areaName)">{{ row.offlineNum }}</span> -->
                      <span>{{ row.onlineQualityRate + "%" }}</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 超量程 -->
          <div class="table" v-if="addTableCheck">
            <el-table
              :data="clcEnterCountData"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
              @sort-change="changeTableSort"
            >
              <el-table-column type="selection" width="50" align="center">
              </el-table-column>
              <el-table-column label="行政区划" width="170" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <div
                      v-if="
                        row.distCode == 429004 ||
                        row.distCode == 429005 ||
                        row.distCode == 429006 ||
                        row.distCode == 429021
                      "
                    >
                      {{ row.distName }}
                    </div>
                    <div v-else>
                      <span
                        v-if="$index != 0 && !showBreak && isXiaZuan"
                        @click="xiaZuan(row.distCode, row.distName)"
                        style="color: #3977ea; cursor: pointer"
                        >{{ row.distName }}</span
                      >
                      <span v-else>{{ row.distName }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="接入企业数量" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <div>
                      <span>{{ row.enterCount }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="企业在线低于6小时" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <div>
                      <span>{{ row.lostEnterCount }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="超量程指标异常企业数量"
                align="center"
                prop="upEnterCount"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <span
                      v-if="
                        row.upEnterCount != 0 || clcEnterCountData.length == 1
                      "
                      style="color: #3977ea; cursor: pointer"
                      @click="
                        openDialogOverrange(row.distCode, 1, row.distName)
                      "
                      >{{ row.upEnterCount }}</span
                    >
                    <span v-else>{{ row.upEnterCount }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="超量程指标比列" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <div>
                      <span>{{ row.clcEnterRate }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination"></div>
        </div>
        <div v-show="!showtable">
          <div id="myCharted"></div>
        </div>
      </div>
    </div>
    <TrendChart
      v-show="TrendChartBool"
      @RunningState="TrendChartFun"
      ref="TrendChart"
    ></TrendChart>
    <DetailTable ref="DetailTable"></DetailTable>
    <DetailTablees ref="DetailTablees"></DetailTablees>
    <DverrangeDetail ref="overrangeDetail"></DverrangeDetail>
  </div>
</template>
<script>
import {
  getVideoRunningData,
  getOnlineNetworking,
  getOnlineNetworkingAnalysisExportExcel,
  getVideoAnalysisExportExcel,
  clcEnterCount,
  exportClcEnterCount,
} from "@/api/workingAcc";
import DetailTable from "./detailTable";
import DetailTablees from "./detailTablees";
import TrendChart from "./TrendChart";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {
    DetailTable: () => import("./detailTable"),
    DetailTablees: () => import("./detailTablees"),
    TrendChart: () => import("./TrendChart"),
    DverrangeDetail: () => import("./overrangeDetail"),
  },
  data() {
    return {
      tableCheck: true,
      addTableCheck: false,
      level: ["1", "2", "3", "4"],
      loading: true,
      areaName: "",
      options: [
        {
          value: "1",
          label: "一级",
        },
        {
          value: "2",
          label: "二级",
        },
        {
          value: "3",
          label: "三级",
        },
        {
          value: "4",
          label: "四级",
        },
      ],
      tableData: [],
      videoData: [],
      clcEnterCountData: [],
      showBreak: false,
      mode: "统计",
      modees: "物联监测",
      modeed: "在线联网分析",
      showtable: true,
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      TrendChartBool: false,
      NetWork: false,
      selection: [],
    };
  },
  methods: {
    gotoTrendAnalysis() {
      this.TrendChartBool = true;
      this.$nextTick(() => {
        this.$refs.TrendChart.getTime();
        this.$refs.TrendChart.getData();
      });
    },
    TrendChartFun(data) {
      this.TrendChartBool = data;
      // this.getOnlineNetworkingList();
      // this.getVideoRunningList();
      if (this.modeed == "在线联网分析") {
        this.getOnlineNetworkingList();
      } else if (this.modeed == "视频运行分析") {
        this.getVideoRunningList();
      } else {
        this.clcEnterCountFn();
      }
    },
    //超量程
    clcEnterCountFn() {
      this.loading = true;
      clcEnterCount({
        abnFlagList: [], //是否超量程异常1是0否
        enterIdList: [], //行政区划
        levelList: this.level, //危险源等级
        current: this.currentPage,
        distCode: this.distCode == "420000" ? this.distCode : "",
        distParentCode: this.distCode != "420000" ? this.distCode : "",
        size: 25,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.clcEnterCountData = res.data.data.records;
        }
      });
    },
    clcEnterCountFn2() {
      this.loading = true;
      clcEnterCount({
        abnFlagList: [], //是否超量程异常1是0否
        enterIdList: [], //行政区划
        levelList: this.level, //危险源等级
        current: this.currentPage,
        distCode: "",
        distParentCode: this.distCode,
        size: 25,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.clcEnterCountData = res.data.data.records;
        }
      });
    },
    //视频运行分析
    getVideoRunningList() {
      this.loading = true;
      getVideoRunningData({
        level: this.level.join(","),
        current: this.currentPage,
        distCode: this.distCode,
        size: 25,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          // this.tableData = res.data.data.records;
          this.videoData = res.data.data.records;
          this.NetWork = false;
        }
      });
    },
    //在线互联网分析
    getOnlineNetworkingList() {
      this.loading = true;
      getOnlineNetworking({
        level: this.level.join(","),
        current: this.currentPage,
        distCode: this.distCode,
        size: 25,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.NetWork = true;
        }
      });
    },
    // 导出
    exportExcel() {
      let list = [this.distCode, ...this.selection];
      if (this.tableCheck) {
        getOnlineNetworkingAnalysisExportExcel({
          level: this.level.toString(),
          distCode: this.distCode,
          distCodeList: list.length <= 1 ? null : list,
          isContainParent: true,
        }).then((response) => {
          // 处理返回的文件流
          // console.log(response);
          if (response.status == 200) {
            this.$message({
              message: "导出成功",
              type: "success",
            });
          } else {
            this.$message.error("导出失败");
          }
          const blob = new Blob([response.data], { type: "application/xls" });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "在线联网分析" + timestamp + ".xls";
          //下载文件
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
          }, 0);
        });
      } else {
        if (this.addTableCheck) {
          exportClcEnterCount({
            abnFlagList: [], //是否超量程异常1是0否
            enterIdList: [], //行政区划
            levelList: this.level, //危险源等级
            distCode: this.distCode == "420000" ? this.distCode : "",
            distParentCode: this.distCode != "420000" ? this.distCode : "",
            distCodeList: list.length <= 1 ? null : list,
          }).then((response) => {
            // 处理返回的文件流
            // console.log(response);
            if (response.status == 200) {
              this.$message({
                message: "导出成功",
                type: "success",
              });
            } else {
              this.$message.error("导出失败");
            }
            const blob = new Blob([response.data], { type: "application/xls" });
            //获取今天的时间
            let day = new Date();
            day.setTime(day.getTime());
            let timestamp =
              day.getFullYear() +
              "-" +
              (day.getMonth() + 1) +
              "-" +
              day.getDate();
            const filename = "超量程" + timestamp + ".xls";
            //下载文件
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            window.setTimeout(function () {
              URL.revokeObjectURL(blob);
              document.body.removeChild(link);
            }, 0);
          });
        } else {
          getVideoAnalysisExportExcel({
            level: this.level.toString(),
            distCode: this.distCode,
            distCodeList: list.length <= 1 ? null : list,
            isContainParent: true,
          }).then((response) => {
            // 处理返回的文件流
            // console.log(response);
            if (response.status == 200) {
              this.$message({
                message: "导出成功",
                type: "success",
              });
            } else {
              this.$message.error("导出失败");
            }
            const blob = new Blob([response.data], { type: "application/xls" });
            //获取今天的时间
            let day = new Date();
            day.setTime(day.getTime());
            let timestamp =
              day.getFullYear() +
              "-" +
              (day.getMonth() + 1) +
              "-" +
              day.getDate();
            const filename = "视频运行分析" + timestamp + ".xls";
            //下载文件
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            window.setTimeout(function () {
              URL.revokeObjectURL(blob);
              document.body.removeChild(link);
            }, 0);
          });
        }
      }
    },
    select(selection, row) {
      this.selection = [];
      if (this.addTableCheck) {
        for (let i = 0; i < selection.length; i++) {
          this.selection[i] = selection[i].distCode;
        }
      } else {
        for (let i = 0; i < selection.length; i++) {
          this.selection[i] = selection[i].areaCode;
        }
      }
    },
    xiaZuan(areaCode, areaName) {
      this.areaName = areaName;
      this.showBreak = true;
      this.distCode = areaCode;
      if (this.tableCheck) {
        this.getOnlineNetworkingList();
      } else {
        if (this.addTableCheck) {
          this.clcEnterCountFn();
        } else {
          this.getVideoRunningList();
        }
      }
    },
    // 排序
    changeTableSort(column) {
      var fieldName = column.prop;
      var sortingType = column.order;
      var data = [];
      if (this.tableCheck) {
        data = [...this.tableData];
      } else {
        data = [...this.videoData];
      }

      var shiftTotal = [];
      if (data[0].areaCode == "420000") {
        shiftTotal = data.shift();
      } else if ((this.areaCode = data[0].areaCode)) {
        shiftTotal = data.shift();
      }
      //按照降序排序
      if (sortingType == "descending") {
        data = data.sort((a, b) => b[fieldName] - a[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        data = data.sort((a, b) => a[fieldName] - b[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      } else {
        // this.getSafetyList();
        if (this.tableCheck) {
          this.getOnlineNetworkingList();
        } else {
          this.getVideoRunningList();
        }
      }
    },
    goToRunning() {
      this.showBreak = false;
      // this.level = "";
      this.distCode = this.$store.state.login.userDistCode;
      if (this.tableCheck) {
        this.getOnlineNetworkingList();
      } else {
        if (this.addTableCheck) {
          this.clcEnterCountFn();
        } else {
          this.getVideoRunningList();
        }
      }
    },
    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
    },
    openDialoges(distCode, type, areaName) {
      this.$refs.DetailTablees.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTablees.getEntData(distCode, type, areaName);
    },
    openDialogOverrange(distCode, type, areaName) {
      this.$refs.overrangeDetail.closeBoolean(true);
      this.$refs.overrangeDetail.majorHazardLevel = ["1", "2", "3", "4"];
      distCode = this.isShowDist ? distCode : null;
      this.$refs.overrangeDetail.getEntData(distCode, type, areaName);
    },

    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.$setEchart("myCharted", 250, 250);
    this.getOnlineNetworkingList();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  watch: {
    mode(newValue, oldValue) {
      if (newValue == "统计") {
        this.showtable = true;
      } else {
        this.showtable = false;
      }
    },
    modeed(newValue, oldValue) {
      this.mode = "统计";
      if (newValue == "在线联网分析") {
        this.tableCheck = true;
        this.addTableCheck = false;
        this.getOnlineNetworkingList();
      } else if (newValue == "视频运行分析") {
        this.tableCheck = false;
        this.addTableCheck = false;
        this.getVideoRunningList();
      } else {
        this.tableCheck = false;
        this.addTableCheck = true; //显示超量程
        this.clcEnterCountFn();
      }
    },
    //在线联网分析
    tableData(newValue, oldValue) {
      // console.log(newValue);
      // debugger
      let myChart = this.$echarts.init(document.getElementById("myCharted"));
      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
          formatter: (params) => {
            return params[0].name + '<br>' +
              params[0].marker + params[0].seriesName + ': ' + params[0].value + '<br>' +
              params[1].marker + params[1].seriesName + ': ' + params[1].value + '<br>' +
              params[2].marker + params[2].seriesName + ': ' + params[2].value + ' %';
          },
        },
        grid: {
          x: 45,
          y: 65,
          x2: 50,
          y2: 80,
          borderWidth: 1,
        },
        legend: {
          data: ["接入总数", "在线数", "在线率"],
          left: 100,
          top: 0,
          icon: "circle",
          itemWidth: 10,
          selectedMode: false,
          orient: "horizontal",
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisLabel: {
              interval: 0,
              rotate: 40,
              textStyle: {
                color: "#545C65",
              },
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "#F2F4F8",
              },
            },
            splitArea: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            name: "企业数（个）",
            splitLine: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
            },
          },
          {
            name: "在线率（%）",
            // "max": 1,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
              formatter: function (value, index) {
                // console.log(value,index)
                var numStr = value.toString();
                var arr = numStr.split(".");
                if (arr[1] && arr[1].length > 0) {
                  // console.log(numStr)
                  return Number(numStr).toFixed(2);
                } else {
                  return value;
                }
              },
            },
          },
        ],
        series: [
          {
            name: "接入总数",
            type: "bar",
            yAxisIndex: 0,
            barWidth: "14",
            data: [],
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
               itemStyle: {
              normal: {
                color: "#89D8FE",
              },
            },
            // itemStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       type: "linear",
            //       global: false,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "#458EF3",
            //         },
            //         {
            //           offset: 1,
            //           color: "#89D8FE",
            //         },
            //       ],
            //     },
            //     barBorderRadius: [14, 14, 0, 0],
            //   },
            // },
          },
          {
            name: "在线数",
            type: "bar",
            yAxisIndex: 0,
            symbolSize: [14, "100%"],
            barWidth: "14",
            barGap: "60%",
            data: [],
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
              itemStyle: {
              normal: {
                color: "rgba(250,180,101,1)",
              },
            },
            // itemStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       type: "linear",
            //       global: false,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "rgba(250,180,101,0.8)",
            //         },
            //         {
            //           offset: 1,
            //           color: "rgba(250,180,101,1)",
            //         },
            //       ],
            //     },
            //     barBorderRadius: [14, 14, 0, 0],
            //   },
            // },
          },
          {
            name: "在线率",
            type: "line",
            data: [],
            yAxisIndex: 1,
            smooth: true,
            symbolSize: 0,
            lineStyle: {
              color: "#2fca95",
              width: 2,
            },
            itemStyle: {
              normal: {
                color: "#2fca95",
              },
            },
            // "markLine": {
            //     "label": {
            //         "show": true,
            //         "position": "end"
            //     },
            //     "lineStyle": {
            //         "color": "#2fca95"
            //     },
            //     "data": [{
            //         "name": "平均值",
            //         "yAxis": 52.54
            //     }]
            // }
          },
        ],
      };
      var xData = [];
      var totalData = [];
      var zaixianData = [];
      var zaixianLvData = [];
      if (this.$store.state.login.user.user_type == "park") {
        for (var i = 0; i < this.tableData.length; i++) {
          xData.push(this.tableData[i].areaName);
          if (this.tableCheck) {
            totalData.push(this.tableData[i].linkedNumber);
            zaixianData.push(this.tableData[i].onlineCompanyNum);
            zaixianLvData.push(this.tableData[i].onlineCompanyRate);
          } else {
            totalData.push(this.tableData[i].yetAccessNum);
            zaixianData.push(this.tableData[i].onlineNum);
            zaixianLvData.push(this.tableData[i].onlineRate);
          }
        }
      } else {
        for (var i = 1; i < this.tableData.length; i++) {
          xData.push(this.tableData[i].areaName);
          if (this.tableCheck) {
            totalData.push(this.tableData[i].linkedNumber);
            zaixianData.push(this.tableData[i].onlineCompanyNum);
            zaixianLvData.push(this.tableData[i].onlineCompanyRate);
          } else {
            totalData.push(this.tableData[i].yetAccessNum);
            zaixianData.push(this.tableData[i].onlineNum);
            zaixianLvData.push(this.tableData[i].onlineRate);
          }
        }
      }

      option.xAxis[0].data = xData;
      option.series[0].data = totalData;
      option.series[1].data = zaixianData;
      option.series[2].data = zaixianLvData;
      myChart.setOption(option);
    },
    //视频echart
    videoData(newValue, oldValue) {
      // console.log(newValue);
      let myChart = this.$echarts.init(document.getElementById("myCharted"));
      var option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
           formatter: (params) => {
            return params[0].name + '<br>' +
              params[0].marker + params[0].seriesName + ': ' + params[0].value + '<br>' +
              params[1].marker + params[1].seriesName + ': ' + params[1].value + '<br>' +
              params[2].marker + params[2].seriesName + ': ' + params[2].value + ' %';
          },
        },
        grid: {
          x: 45,
          y: 65,
          x2: 50,
          y2: 80,
          borderWidth: 1,
        },
        legend: {
          data: ["接入总数", "在线数", "在线率"],
          left: 100,
          top: 0,
          icon: "circle",
          itemWidth: 10,
          selectedMode: false,
          orient: "horizontal",
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisLabel: {
              interval: 0,
              rotate: 40,
              textStyle: {
                color: "#545C65",
              },
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "#F2F4F8",
              },
            },
            splitArea: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            name: "企业数（个）",
            splitLine: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
            },
          },
          {
            name: "在线率（%）",
            // "max": 1,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
              formatter: function (value, index) {
                // console.log(value,index)
                var numStr = value.toString();
                var arr = numStr.split(".");
                if (arr[1] && arr[1].length > 0) {
                  // console.log(numStr)
                  return Number(numStr).toFixed(2);
                } else {
                  return value;
                }
              },
            },
          },
        ],
        series: [
          {
            name: "接入总数",
            type: "bar",
            yAxisIndex: 0,
            barWidth: "14",
            data: [],
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
               itemStyle: {
              normal: {
                color: "#89D8FE",
              },
            },
            // itemStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       type: "linear",
            //       global: false,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "#458EF3",
            //         },
            //         {
            //           offset: 1,
            //           color: "#89D8FE",
            //         },
            //       ],
            //     },
            //     barBorderRadius: [14, 14, 0, 0],
            //   },
            // },
          },
          {
            name: "在线数",
            type: "bar",
            yAxisIndex: 0,
            symbolSize: [14, "100%"],
            barWidth: "14",
            barGap: "60%",
            data: [],
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
              itemStyle: {
              normal: {
                color: "rgba(250,180,101,1)",
              },
            },
            // itemStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       type: "linear",
            //       global: false,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "rgba(250,180,101,0.8)",
            //         },
            //         {
            //           offset: 1,
            //           color: "rgba(250,180,101,1)",
            //         },
            //       ],
            //     },
            //     barBorderRadius: [14, 14, 0, 0],
            //   },
            // },
          },
          {
            name: "在线率",
            type: "line",
            data: [],
            yAxisIndex: 1,
            smooth: true,
            symbolSize: 0,
            lineStyle: {
              color: "#2fca95",
              width: 2,
            },
            itemStyle: {
              normal: {
                color: "#2fca95",
              },
            },
            // "markLine": {
            //     "label": {
            //         "show": true,
            //         "position": "end"
            //     },
            //     "lineStyle": {
            //         "color": "#2fca95"
            //     },
            //     "data": [{
            //         "name": "平均值",
            //         "yAxis": 52.54
            //     }]
            // }
          },
        ],
      };
      var xData = [];
      var totalData = [];
      var zaixianData = [];
      var zaixianLvData = [];
      if (this.$store.state.login.user.user_type == "park") {
        for (var i = 0; i < this.videoData.length; i++) {
          xData.push(this.videoData[i].areaName);
          totalData.push(this.videoData[i].yetAccessNum);
          zaixianData.push(this.videoData[i].onlineNum);
          zaixianLvData.push(this.videoData[i].onlineRate);
        }
      } else {
        for (var i = 1; i < this.videoData.length; i++) {
          xData.push(this.videoData[i].areaName);
          totalData.push(this.videoData[i].yetAccessNum);
          zaixianData.push(this.videoData[i].onlineNum);
          zaixianLvData.push(this.videoData[i].onlineRate);
        }
      }

      option.xAxis[0].data = xData;
      option.series[0].data = totalData;
      option.series[1].data = zaixianData;
      option.series[2].data = zaixianLvData;
      myChart.setOption(option);
    },
    //超量程
    clcEnterCountData(newValue, oldValue) {
      // console.log(newValue);
      // debugger
      let myChart = this.$echarts.init(document.getElementById("myCharted"));
      var option = {
        color: ["#458EF3", "#2fca95", "ff0000"],
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
          formatter: (params) => {
            return params[0].name + '<br>' +
              params[0].marker + params[0].seriesName + ': ' + params[0].value + '<br>' +
              params[1].marker + params[1].seriesName + ': ' + params[1].value + '<br>' +
              params[2].marker + params[2].seriesName + ': ' + params[2].value ;
          },

          // formatter: function (params) {
          // debugger
          // let html = "";
          // params.forEach((v) => {
          //   html += `<div style="color: #fff;font-size: 14px;line-height: 24px">
          //         <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;color:#fff;"></span>
          //         ${v.seriesName}
          //         <span style=";font-weight:700;font-size: 18px;margin-left:5px;color:#fff;">${
          //     v.value
          //   }</span>
          //         %`;
          // });

          // return html;
          // },
        },
        grid: {
          x: 45,
          y: 65,
          x2: 50,
          y2: 80,
          borderWidth: 1,
        },
        legend: {
          data: ["接入企业数量", "企业在线低于6小时", "超量程指标异常企业数量"],
          left: 100,
          top: 0,
          icon: "circle",
          itemWidth: 10,
          selectedMode: false,
          orient: "horizontal",
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: [
          {
            type: "category",
            data: [],
            axisLabel: {
              interval: 0,
              rotate: 40,
              textStyle: {
                color: "#545C65",
              },
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "#F2F4F8",
              },
            },
            splitArea: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            name: "企业数（个）",
            type: "value",
            splitLine: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
            },
          },
          {
            name: "",
            // "max": 1,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: false,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
              formatter: function (value, index) {
                // console.log(value,index)
                var numStr = value.toString();
                var arr = numStr.split(".");
                if (arr[1] && arr[1].length > 0) {
                  // console.log(numStr)
                  return Number(numStr).toFixed(2);
                } else {
                  return value;
                }
              },
            },
          },
        ],
        series: [
          {
            name: "接入企业数量",
            type: "bar",
            yAxisIndex: 0,
            barWidth: "14",
            data: [],
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
             itemStyle: {
              normal: {
                color: "#89D8FE",
              },
            },
            // itemStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       type: "linear",
            //       global: false,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "#458EF3",
            //         },
            //         {
            //           offset: 1,
            //           color: "#89D8FE",
            //         },
            //       ],
            //     },
            //     barBorderRadius: [14, 14, 0, 0],
            //   },
            // },
          },
          {
            name: "企业在线低于6小时",
            type: "bar",
            yAxisIndex: 0,
            symbolSize: [14, "100%"],
            barWidth: "14",
            barGap: "60%",
            data: [],
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
             itemStyle: {
              normal: {
                color: "rgba(250,180,101,1)",
              },
            },
            // itemStyle: {
            //   normal: {
            //     color: {
            //       x: 0,
            //       y: 0,
            //       x2: 0,
            //       y2: 1,
            //       type: "linear",
            //       global: false,
            //       colorStops: [
            //         {
            //           offset: 0,
            //           color: "rgba(250,180,101,0.8)",
            //         },
            //         {
            //           offset: 1,
            //           color: "rgba(250,180,101,1)",
            //         },
            //       ],
            //     },
            //     barBorderRadius: [14, 14, 0, 0],
            //   },
            // },
          },
          {
            name: "超量程指标异常企业数量",
            type: "line",
            data: [],
            yAxisIndex: 1,
            smooth: true,
            symbolSize: 0,
            lineStyle: {
              color: "#2fca95",
              width: 2,
            },
            itemStyle: {
              normal: {
                color: "#2fca95",
              },
            },
            // "markLine": {
            //     "label": {
            //         "show": true,
            //         "position": "end"
            //     },
            //     "lineStyle": {
            //         "color": "#2fca95"
            //     },
            //     "data": [{
            //         "name": "平均值",
            //         "yAxis": 52.54
            //     }]
            // }
          },
        ],
      };
      var xData = [];
      var totalData = [];
      var zaixianData = [];
      var zaixianLvData = [];
      if (this.$store.state.login.user.user_type == "park") {
        for (var i = 0; i < this.clcEnterCountData.length; i++) {
          xData.push(this.clcEnterCountData[i].distName);
          totalData.push(this.clcEnterCountData[i].enterCount); //接入企业数量 enterCount
          zaixianData.push(this.clcEnterCountData[i].lostEnterCount); //企业在线低于6小时
          zaixianLvData.push(this.clcEnterCountData[i].upEnterCount); //超量程指标异常企业数量
        }
      } else {
        for (var i = 1; i < this.clcEnterCountData.length; i++) {
          xData.push(this.clcEnterCountData[i].distName);
          totalData.push(this.clcEnterCountData[i].enterCount);
          zaixianData.push(this.clcEnterCountData[i].lostEnterCount);
          zaixianLvData.push(this.clcEnterCountData[i].upEnterCount);
        }
      }

      option.xAxis[0].data = xData;
      option.series[0].data = totalData;
      option.series[1].data = zaixianData;
      option.series[2].data = zaixianLvData;
      myChart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.runningState {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      & > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // height: 48px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
