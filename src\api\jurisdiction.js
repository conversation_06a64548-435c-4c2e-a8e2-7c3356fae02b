import axios from "axios";
import qs from "qs";


// 权限侧导航展开数据
export const getprivsData = data => {
    return axios({
      method: "get",
      url: `/admin/priv/tree/${data}`,
    });
};
//权限信息
export const getPrivList = data => {
    return axios({
      method: "get",
      url: "/admin/priv/list?systemCode="+data.systemCode+"&privId="+data.privId+"&size="+data.pageSize+'&current='+data.pageNo
    });
};
//权限url信息
export const getPrivUrlList = data => {
    return axios({
      method: "get",
      url: "/admin/priv/url/list?privId="+data.privId+"&size="+data.pageSize+'&current='+data.pageNo
    });
};
//保存权限
export const getSavePriv = data => {
    return axios({
      method: "post",
      url: "/admin/priv/add",
      data: qs.stringify(data)
    });
};
//当前权限数
export const querySystemTreeNoSelf = data => {
    return axios({
      method: "get",
      url: "/admin/system/list/"+data.systemCode
    });
};
//详情
export const savePrivByMenuId = data =>{
    return axios({
      method:"get",
      url:"/admin/priv/info/"+data.privId
    })
}
//修改权限
export const updataPrive = data => {
    return axios({
      method: "post",
      url: "/admin/priv/update",
      data: qs.stringify(data)
    });
};
//删除权限
export const deleteByPrivId = data =>{
    return axios({
      method:"get",
      url:"/admin/priv/del/"+data.privId
    })
}

//保存权限url
export const getSavePrivUrl = data => {
    return axios({
      method: "post",
      url: "/admin/priv/url/add",
      data: qs.stringify(data)
    });
};
//修改权限url
export const updataPriveUrl = data => {
    return axios({
      method: "post",
      url: "/admin/priv/url/update",
      data: qs.stringify(data)
    });
};
//删除权限url
export const deleteByPrivIdUrl = data =>{
    return axios({
      method:"get",
      url:"/admin/priv/url/del/"+data.id
    })
}
//url详情
export const savePrivByMenuIdUrl = data =>{
    return axios({
      method:"get",
      url:"/admin/priv/url/info/"+data.id
    })
}



