<template>
    <div class="accidentManagement">
        <div class="seach-part">
            <div class="l">
                <el-cascader :options="typeList" v-model="searchObj.typeCode" :props="{
                    value: 'code',
                    label: 'name',
                }" size="mini" placeholder="请选择事故类型" style="width: 240px" clearable></el-cascader>
                <el-date-picker v-model="dateValue" @change="searchTime" size="mini" type="daterange"
                    value-format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
                    unlink-panels></el-date-picker>
                <el-input v-model.trim="searchObj.keyword" size="mini" placeholder="请输入事故关键字" class="input" clearable
                    style="width: 240px"></el-input>
                <el-button type="primary" size="mini" @click="search">查询</el-button>
            </div>
            <!-- <el-button type="primary" size="mini" @click="openDialog('add')"
          >新增</el-button
        > -->
        </div>
        <div class="table-main">

            <div class="table">
                <el-table :data="tableData" v-loading="loading"
                    :header-cell-style="{ background: '#F1F6FF', color: '#333' }" border style="width: 100%"
                    ref="multipleTable">

                    <el-table-column type="index" label="序号" width="50" align="center">
                    </el-table-column>
                    <el-table-column prop="title" label="事故标题" align="center" width="300">
                    </el-table-column>
                    <el-table-column prop="time" label="事发时间" align="center" min-width="120"
                        :show-overflow-tooltip="true">
                        <template slot-scope="scope">
                            <div>
                                {{ scope.row.time | timeFn }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="districtCodeName" label="所在行政区划" align="center">
                    </el-table-column>
                    <el-table-column prop="typeName" label="事故类型" align="center">
                    </el-table-column>
                    <el-table-column prop="levelName" label="事故等级" align="center">
                    </el-table-column>
                    <el-table-column prop="dataFlag" label="数据生成方式" align="center">
                        <template slot-scope="scope">
                            <span v-if="scope.row.dataFlag == '0'">手动录入</span>
                            <span v-else>平台接入</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" width="240" align="center">
                        <template slot-scope="scope">
                            <div>
                                <el-button type="text" @click="openDialog('read', scope.row)">查看</el-button>
                            </div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="pagination">
                <el-pagination @current-change="handleCurrentChange" :current-page.sync="searchObj.nowPage" background
                    layout="total, prev, pager, next" :total="searchObj.total" v-if="searchObj.total != 0">
                </el-pagination>
            </div>

        </div>
        <el-dialog :title="dialogInfo.title" :visible.sync="dialogInfo.visible" width="1100px" @close="closeDialog"
            v-if="dialogInfo.visible" :close-on-click-modal="false">
            <div class="dialog">
                <el-form :model="accidentForm" :rules="rules" ref="ruleForm" label-width="150px">
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="事故标题:" prop="title">
                                <el-input v-model.trim="accidentForm.title" maxlength="100"
                                    :disabled="dialogInfo.disable" placeholder="请输入事故标题" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <div>
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item prop="address" label="事故地址:">
                                    <el-input refs="addressA" id="addressA" v-model="accidentForm.address" disabled
                                        placeholder="请输入事故地址" maxlength="100" />
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </div>

                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="事故类型:" prop="typeCode">
                                <el-select v-model="accidentForm.typeCode" placeholder="请输入事故类型"
                                    :disabled="dialogInfo.disable" style="width: 100%">
                                    <el-option v-for="item in typeList" :key="item.code" :value="item.code"
                                        :label="item.name"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="事故等级:" prop="levelCode">
                                <el-select v-model="accidentForm.levelCode" placeholder="请输入事故等级"
                                    :disabled="dialogInfo.disable" style="width: 100%">
                                    <el-option v-for="item in levelList" :key="item.levelCode" :value="item.levelCode"
                                        :label="item.levelName"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="死亡人数:" prop="deathNum">
                                <el-input v-model.trim="accidentForm.deathNum" maxlength="10"
                                    class="accident-form-input" :disabled="dialogInfo.disable"></el-input>人
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="受伤人数:" prop="woundNum">
                                <el-input v-model.trim="accidentForm.woundNum" class="accident-form-input"
                                    :disabled="dialogInfo.disable" maxlength="10"></el-input>
                                人
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                
                        <el-col :span="12">
                            <el-form-item label="事发时间:" prop="time">
                                <el-date-picker v-model="accidentForm.time" type="date"
                                    value-format="yyyy-MM-dd HH:mm:ss" placeholder="请选择事发日期"
                                    :disabled="dialogInfo.disable" style="width: 100%"
                                    :picker-options="pickerOptions"></el-date-picker>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <div v-if="$store.state.login.user.user_type == 'ent'">
                                <el-form-item label="事发企业:" prop="companyName">
                                    <el-input v-model.trim="accidentForm.companyName" style="width: 100%" class=""
                                        disabled></el-input>
                                </el-form-item>
                            </div>

                            <div v-else>
                                <el-form-item label="事发企业:" prop="companyName">
                    
                                    <el-select v-model.trim="accidentForm.companyName" filterable
                                        placeholder="请输入企业名称搜索选择" remote value-key="enterpId" clearable @change="(item) => {
                                                handleSelect(item);
                                            }
                                            " reserve-keyword :remote-method="querySearch2" :disabled="dialogInfo.disable"
                                        style="width: 100%">
                                        <el-option v-for="item in enListOption" :key="item.enterpId" :title="item"
                                            :label="item.enterpName" :value="item">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                            </div>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="关联重大危险源:" prop="dangerId">
                                <el-select v-model="accidentForm.dangerId" placeholder="请选择危险源"
                                    :disabled="dialogInfo.disable" style="width: 100%">
                                    <el-option v-for="item in dangerSourceList" :key="item.dangerId"
                                        :value="item.dangerId" :label="item.dangerName"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="关联危险化学品:" prop="chemicalsId">
                                <el-select v-model="accidentForm.chemicalsId" placeholder="请选择危险化学品"
                                    :disabled="dialogInfo.disable" style="width: 100%">
                                    <el-option v-for="item in hazarchemList" :key="item.hazarchemId"
                                        :value="item.hazarchemId" :label="item.hazarchemName"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="经度:" prop="longitude">
                                <el-input v-model.trim="accidentForm.longitude" disabled style="width: 240px"
                                    placeholder="请输入经度" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="纬度:" prop="latitude">
                                <el-input v-model="accidentForm.latitude" disabled style="width: 240px"
                                    placeholder="请输入纬度" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <div class="map_height">
                                <el-form-item label="地图定位">
                                    <egisMap :islistener="false" :isdetail="dialogInfo.disable" ref="detailMap"
                                        :datas="accidentForm" style="height: 200px" @mapCallback="mapcallback">
                                    </egisMap>
                                </el-form-item>
                            </div>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="详情描述:" prop="description">
                                <el-input v-model.trim="accidentForm.description" type="textarea" :rows="5"
                                    placeholder="2000字以内" maxlength="2000" :disabled="dialogInfo.disable" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="上传附件:">
                                <AttachmentUpload :attachmentlist="accidentForm.file" :limit="1" type="office"
                                    v-bind="{}" :editabled="dialogInfo.disable"></AttachmentUpload>
                                <!-- <div v-else>
                    <i class="el-icon-upload"></i
                    >{{ accidentForm.file.length && accidentForm.file[0].name }}
                  </div> -->
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="closeAll" v-if="!dialogInfo.disable">取 消</el-button>
                    <!-- <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button> -->
                    <el-button type="primary" @click="saveData()" v-if="!dialogInfo.disable">提 交</el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { mapState, createNamespacedHelpers } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import {
    getAccidentListData,
    getAccidentTypeListData,
    deleteAccidentById,
    addAccident,
    updateAccident,
} from "@/api/accidentManagement";
import { parseTime } from "@/utils/index";
import { MessageBox } from "element-ui";
const mapConfig = require("@/assets/json/map.json");
import { getSearchArr } from "@/api/entList.js";
import {
    getSelectData,
    getInformationInfoDanger,
    getHazarchemList,
} from "@/api/entList";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
var dayjs = require("dayjs");

export default {
    name: "accidentCount",
    components: {
        AttachmentUpload,
    },
    data() {
        let validateNum = (rule, value, callback) => {
            // debugger
            const reg = /^[+]{0,1}(\d+)$/;
            if (value === "") {
                callback(new Error("请填写人数"));
            } else if (!reg.test(value)) {
                callback(new Error("请输入正整数"));
            } else {
                callback();
            }
        };
        return {
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            header: {
                token: this["$store"].state.login.token,
            },
            tableData: [], // 表格数据
            loading: false, // 加载状态
            searchObj: {
                // 表格查询参数
                nowPage: 1,
                total: 0,
                pageSize: 10,
                keyword: "",
                districtCode: this.$store.state.login.userDistCode,
                startTime: "",
                endTime: "",
                typeCode: "",
            },
            enterpriseId: "", // 企业id
            enListOption: [],
            dateValue: "", // 时间选择
            typeList: [], // 事故类型数据
            levelList: [], // 事故等级数据
            dialogInfo: {
                // 弹窗控制信息
                visible: false,
                title: "新增事故信息",
                disable: false,
            },
            accidentForm: {
                // 事故表单
                title: "",
                address: "",
                typeCode: "", //事故类型
                levelCode: "", //事故等级
                deathNum: "", //死亡人数
                woundNum: "", //受伤人数,
                time: "",
                companyName: "",
                description: "",
                longitude: mapConfig.map.defaultExtent.center[0],
                latitude: mapConfig.map.defaultExtent.center[1],
                file: [],
                dangerId: "",
                chemicalsId: "",
                address: "",
            },
            reset() {
                this.accidentForm = {
                    title: "",
                    address: "",
                    typeCode: "", //事故类型
                    levelCode: "", //事故等级
                    deathNum: "", //死亡人数
                    woundNum: "", //受伤人数,
                    time: [],
                    companyName: "",
                    description: "",
                    longitude: mapConfig.map.defaultExtent.center[0],
                    latitude: mapConfig.map.defaultExtent.center[1],
                    file: [],
                    dangerId: "",
                    chemicalsId: "",
                    address: "",
                };
                if (this.$refs["ruleForm"]) {
                    this.$refs["ruleForm"].resetFields();
                }
            },
            rules: {
                // 表单校验规则
                title: [
                    { required: true, message: "请填写事故标题", trigger: "blur" },
                    // { max: 255, message: "长度 255 个字符一下", trigger: "blur" },
                ],
                time: [
                    {
                        type: "string",
                        required: true,
                        message: "请选择日期",
                        trigger: "change",
                    },
                ],
                districtCode: [
                    {
                        required: true,
                        message: "行政区划不能为空",
                        trigger: "change",
                    },
                ],
                address: [
                    {
                        required: true,
                        message: "请填写事发地址",
                        trigger: "blur",
                    },
                ],

                typeCode: [
                    { required: true, message: "请选择事发类型", trigger: "change" },
                ],
                levelCode: [
                    { required: true, message: "请选择事发等级", trigger: "change" },
                ],
                deathNum: [{ required: true, validator: validateNum, trigger: "blur" }],
                woundNum: [{ required: true, validator: validateNum, trigger: "blur" }],
                companyName: [
                    { required: true, message: "请选择事发企业", trigger: "change" },
                ],
                longitude: [
                    { required: true, message: "经度不能为空", trigger: "blur" },
                ],
                latitude: [
                    { required: true, message: "纬度不能为空", trigger: "blur" },
                ],
            },
            district: this.$store.state.controler.district, // 行政区划
            dangerSourceList: [], // 危险源数据
            hazarchemList: [], // 危化品数据
        };
    },
    filters: {
        timeFn(val) {
            if (val) {
                return dayjs(val).format("YYYY-MM-DD");
            }
        },
    },
    watch: {
        vuexDistrict: {
            handler(newVal, oldVal) {
                this.district = newVal;
            },
        },
    },
    created() { },
    methods: {
        closeAll() {
            this.dialogInfo.visible = false;
            this.reset();
        },
        dateOptions(time) {
            return time.getTime() < Date.now() - 8.64e6;
        },
        search() {
            this.searchObj.nowPage = 1;
            this.getAccidentList();
        },
        handleSelectionChange() { },
        select() { },
        // 分页查询
        handleCurrentChange(val) {
            this.searchObj.nowPage = val;
            this.getAccidentList();
        },
        // 获取事故列表数据
        getAccidentList(val) {
            if (val) this.enterpriseId = val;
            this.loading = true;
            if (typeof this.searchObj.typeCode !== "string") {
                this.searchObj.typeCode =
                    this.searchObj.typeCode && this.searchObj.typeCode[0];
            }
            let params = JSON.parse(JSON.stringify(this.searchObj));
            if (this.$store.state.login.user.user_type !== "gov") {
                params.districtCode = "";
            }
            params.enterpriseCode = this.enterpriseId;
            console.log(params);
            delete params.total;
            getAccidentListData(params).then((res) => {
                if (res.data.status === 200) {
                    this.tableData = res.data.data.list;
                    this.searchObj.nowPage = res.data.data.nowPage + 1;
                    this.searchObj.total = res.data.data.total;
                    this.loading = false;
                } else {
                    this.$message({
                        message: res.data.msg,
                        type: "warning",
                    });
                }
            });
        },
        // 获取事故类型列表
        getaccidentTypeList() {
            getAccidentTypeListData().then((res) => {
                if (res.status === 200) {
                    this.typeList = res.data.data.typeCode;
                    this.levelList = res.data.data.accidentLevel;
                }
            });
        },
        // 删除事故列
        deleteAccident(id) {
            MessageBox.confirm("确定要删除选择的数据吗?", "通知", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "info",
            })
                .then(() => {
                    deleteAccidentById({ id })
                        .then((res) => {
                            this.$message.success("删除成功");
                            if (this.tableData.length === 1 && this.searchObj.nowPage !== 1) {
                                this.searchObj.nowPage--;
                            }
                            this.getAccidentList();
                        })
                        .catch((e) => {
                            console.log(e, "请求错误");
                        });
                })
                .catch(() => { });
        },
        // 时间改变执行方法
        searchTime(val) {
            if (val) {
                let date1 = new Date(val[0]);
                let dataTime1 = parseTime(date1, "{y}-{m}-{d}") + " 00:00:00";
                let date2 = new Date(val[1]);
                let dataTime2 = parseTime(date2, "{y}-{m}-{d}") + " 00:00:00";
                this.searchObj.startTime = dataTime1;
                this.searchObj.endTime = dataTime2;
            } else {
                this.dateValue = "";
                this.searchObj.startTime = "";
                this.searchObj.endTime = "";
            }
        },
        // 关闭弹窗
        closeDialog() {
            this.dialogInfo.visible = false;
            this.reset();
            // this.accidentForm = {
            //   longitude: mapConfig.map.defaultExtent.center[0],
            //   latitude: mapConfig.map.defaultExtent.center[1],
            // };
        },
        // 打开弹窗
        openDialog(type, data) {
            var _this = this;
            if (type === "edit") {
                this.dialogInfo.title = "编辑事故信息";
                const rowData = Object.assign({}, data);
                this.accidentForm = rowData;
                this.getSelectDataById(data.companyId);
                this.getHazarchemData(data.companyId);
                this.dialogInfo.disable = false;
            } else if (type === "add") {
                this.dialogInfo.title = "新增事故信息";
                this.dialogInfo.disable = false;
                if (this.$store.state.login.user.user_type == "ent") {
                    this.accidentForm.companyName = this.$store.state.login.user.org_name;
                    getSearchArr(this.accidentForm.companyName).then((res) => {
                        if (res.data.code == 0) {
                            if (res.data.data.length > 0) {
                                this.handleSelect(res.data.data[0]);
                            } else {
                            }
                        }
                    });
                }
            } else {
                const rowData = Object.assign({}, data);
                this.accidentForm = rowData;
                this.dialogInfo.title = "事故信息详情";
                this.dialogInfo.disable = true;
                this.getSelectDataById(data.companyId);
                this.getHazarchemData(data.companyId);
            }
            this.dialogInfo.visible = true;
        },
        // 新增/编辑
        saveData() {
            // console.log(this.accidentForm.address)
            // this.$refs['ruleForm'].clearValidate(['address']);
            //  document.getElementById('addressA').value='111'
            this.$refs["ruleForm"].validate((valid) => {
                if (valid) {
                    this.dialogInfo.title === "新增事故信息"
                        ? this.addData()
                        : this.updateData();
                    this.reset();
                    // this.$refs['ruleForm'].resetFields()
                }
            });
        },
        // 新增事故数据
        addData() {
            addAccident(this.accidentForm).then((res) => {
                if (res.data.status === 200) {
                    this.$message.success("操作成功");
                    this.dialogInfo.visible = false;
                    // this.accidentForm = {
                    //   longitude: mapConfig.map.defaultExtent.center[0],
                    //   latitude: mapConfig.map.defaultExtent.center[1],
                    // };
                    this.reset();
                    this.getAccidentList();
                } else {
                    this.$message.warning(res.data.msg);
                }
            });
        },
        // 更新事故数据
        updateData() {
            // debugger
            updateAccident(this.accidentForm).then((res) => {
                if (res.data.status === 200) {
                    this.$message.success("操作成功");
                    this.dialogInfo.visible = false;
                    // this.accidentForm = {
                    //   longitude: mapConfig.map.defaultExtent.center[0],
                    //   latitude: mapConfig.map.defaultExtent.center[1],
                    // };
                    this.reset();
                    this.getAccidentList();
                } else {
                    this.$message.warning(res.data.mssg);
                }
            });
        },
        // 地图定位
        mapcallback(data) {
            // 标点赋值
            this.accidentForm.longitude = data.location.lon.toFixed(6);
            this.accidentForm.latitude = data.location.lat.toFixed(6);
            this.accidentForm.address = data.formatted_address;
            if (data.addressComponent.county_code != "") {
            }
            //districtName
            this.accidentForm.districtCode = data.addressComponent.county_code.slice(
                3,
                data.addressComponent.county_code.length
            );
            // this.accidentForm.districtCode='420984'
        },
        // 文件上传
        // handleFileSuccess(file) {
        //   this.accidentForm.file = [file];
        // },
        // 企业数据
        querySearch(queryString, cb) {
            this.getSeachData(queryString || "", cb);
        },
        getSeachData(keyWord, cb) {
            getSearchArr(keyWord)
                .then((res) => {
                    if (res.data.code == 0) {
                        if (res.data.data.length > 0) {
                            cb(res.data.data);
                        } else {
                            cb([]);
                        }
                    }
                })
                .catch((e) => {
                    console.log(e, "请求错误");
                });
        },
        querySearch2(val) {
            getSearchArr(val)
                .then((res) => {
                    if (res.data.code == 0) {
                        if (res.data.data.length > 0) {
                            this.enListOption = res.data.data;
                        } else {
                        }
                    }
                })
                .catch((e) => {
                    console.log(e, "请求错误");
                });
        },

        // 清楚建议框
        clearSensororgCode() {
            this.accidentForm.companyId = "";
            this.accidentForm.companyName = "";
        },
        //选择企业
        handleSelect(item) {
            this.accidentForm.companyId = item.enterpId;
            this.accidentForm.companyName = item.enterpName;
            this.getSelectDataById(item.enterpId);
            this.getHazarchemData(item.enterpId);
            this.accidentForm.longitude = item.longitude;
            this.accidentForm.latitude = item.latitude;
            this.accidentForm.districtCode = item.districtCode;
            this.accidentForm.address = item.address;
            var x = {
                x: item.longitude,
                y: item.latitude,
            };
            //
            this.$refs["detailMap"].setMapDiot2(x);

            this.$nextTick(() => {
                // this.$refs["ruleForm"].clearValidate(); // 取消表单验证
                this.$refs["ruleForm"].clearValidate(["address"]);
                this.$refs["ruleForm"].clearValidate(["districtCode"]);
                this.$refs["ruleForm"].clearValidate(["longitude"]);
                this.$refs["ruleForm"].clearValidate(["latitude"]);
            });
        },
        // 获取危险源数据
        getSelectDataById(enterpriseId) {
            getSelectData({
                enterpId: enterpriseId,
            }).then((res) => {
                if (res.data.code == 0) {
                    this.dangerSourceList = res.data.data;
                }
            });
        },
        // 危险源选择器改变
        handleChangeDangerId(id) { },
        // 获取危化品数据
        getHazarchemData(dangerId) {
            // getInformationInfoDanger(dangerId).then(res => {
            //   if (res.data.code == 0) {
            //     this.hazarchemList = res.data.data.hazarchemSaiSi;
            //   }
            // })
            //getHazarchemList
            getHazarchemList(dangerId).then((res) => {
                if (res.data.code == 0) {
                    this.hazarchemList = res.data.data;
                }
            });
        },
    },
    computed: {
        ...mapState({
            user: (state) => state.login.user,
            enterData: (state) => state.login.enterData,
        }),
        ...mapStateControler({
            vuexDistrict: (state) => state.district,
        }),
    },
    mounted() {
        this.getaccidentTypeList();
        // this.getAccidentList();
    },
};
</script>

<style lang="scss" scoped>
.accidentManagement {
    .icon {
        color: #6f81b5;
        font-size: 15px;
    }

    .header {
        background-color: #fff;
        overflow: hidden;
        margin-bottom: 10px;
        border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

        .breadcrumb {
            margin-bottom: 10px;
            cursor: pointer;
            color: #4f5b69;
            //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
        }
    }

    .dialog {
        height: 60vh;
        overflow-y: scroll;
        overflow-x: hidden;

        .dialog-footer {
            display: flex;
            justify-content: center;

            &>* {
                margin: 0 10px;
            }
        }
    }

    .accident-form-input1 {
        width: 400px;
    }
}

.seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;

    .l {
        display: flex;
        justify-content: space-between;

        &>* {
            margin-right: 15px;
        }
    }
}

.table-main {
    background: #fff;

    .table-top {
        display: flex;
        justify-content: space-between;

        // padding: 10px 0;
        h2 {
            font-size: 18px;
            line-height: 32px;
            margin-bottom: 0;
        }
    }

    .pagination {
        margin-top: 30px;
        padding-bottom: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>
<style lang="scss">
.accident-form-input {
    // display: inline-block;
    width: 80px;
    margin-right: 10px;

    .el-input__inner {
        border: none;
        border-bottom: 1px solid #666;
        width: 80px;
        outline: none;
        height: 20px;
        border-radius: 0;
    }
}

.accident-form-address .el-form-item__content {
    margin-left: -20px !important;
}
</style>