<template>
  <div class="historyList">
    <el-dialog title="数字化"
               :visible.sync="show"
               width="1200px"
               @close="closeBoolean()"
               top="10vh"
               :close-on-click-modal="false"
               v-dialog-drag>
      <div class="flex-full planTable">
        <!-- <div class="spliterSty"> -->
        <el-menu :default-active="activeIndex"
                 class="el-menu-demo"
                 mode="horizontal"
                 @select="(value) => changecheckPage(value, planId)">
          <el-menu-item index="1">四级响应</el-menu-item>
          <el-menu-item index="2">三级响应</el-menu-item>
          <el-menu-item index="3">二级响应</el-menu-item>
          <el-menu-item index="4">一级响应</el-menu-item>
        </el-menu>
        <div class="flex-full list-contain">
          <div class="flex-full"
               style="margin-top: 10px;"
               v-if="activeIndex == '1'">
            <Responseofour ref="responseofour" :edVisible='edVisible' ></Responseofour>
          </div>
          <div class="flex-full"
               style="margin-top: 10px;"
               v-if="activeIndex == '2'">
            <Responseothree ref="responseothree" :edVisible='edVisible'></Responseothree>
          </div>
          <div class="flex-full"
               style="margin-top: 10px;"
               v-if="activeIndex == '3'">
            <Responseotwo ref="responseotwo" :edVisible='edVisible'></Responseotwo>
          </div>
          <div class="flex-full"
               style="margin-top: 10px;"
               v-if="activeIndex == '4'">
            <Responseoone ref="responseoone" :edVisible='edVisible'></Responseoone>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Responseofour from './responseofour'
import Responseothree from './responseothree'
import Responseotwo from './responseotwo'
import Responseoone from './responseoone'
export default {
  //import引入的组件
  name: 'digitization',
  props: ['planId','edVisible'],
  components: {
    Responseofour,
    Responseothree,
    Responseotwo,
    Responseoone
  },
  data() {
    return {
      activeIndex: '1',
      show: false,
      // planId: ''
    }
  },
  // props:{
  //   edVisible: {
  //     type: Boolean,
  //     default: true,
  //   },
  // },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val
      if (!val) {
        this.$nextTick(() => {
          this.activeIndex = '1'
          this.$refs.responseofour.resetPage()
          this.$refs.responseofour.reset()
          this.$refs.responseothree.reset()
          this.$refs.responseotwo.reset()
          this.$refs.responseoone.reset()
        })
      }
    },
    changecheckPage(index, planId) {
      // this.planId = planId
      this.activeIndex = index
      if (index == 1) {
        this.$nextTick(() => {
          this.$refs.responseofour.planDigitalGet(planId, 4)
        })
      } else if (index == 2) {
        this.$nextTick(() => {
          this.$refs.responseothree.planDigitalGet(planId, 3)
        })
      } else if (index == 3) {
        this.$nextTick(() => {
          this.$refs.responseotwo.planDigitalGet(planId, 2)
        })
      } else if (index == 4) {
        this.$nextTick(() => {
          this.$refs.responseoone.planDigitalGet(planId, 1)
        })
      }
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.$nextTick(() => {
      this.activeIndex = '1'
    })
  }
}
</script>
<style lang="scss" scoped>
.historyList {
}
</style>