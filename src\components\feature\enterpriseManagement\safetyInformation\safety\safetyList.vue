<template>
  <div class="safetyOrgList">
    <div class="header">
      <div class="title">安全管理人员</div>
      <div class="operation">
        <div class="inputBox">
          <el-input
            v-model.trim="params.personName"
            size="small"
            placeholder="请输入姓名"
            class="input"
            clearable
          ></el-input>
          <el-select
            v-model="params.isFullTime"
            size="small"
            placeholder="请选择专（兼）职"
            :clearable="true"
          >
            <el-option
              v-for="item in isFullTimeData"
              :key="item"
              :label="item.resourceTypeName"
              :value="item.resourceTypeCode"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="params.phone"
            size="small"
            placeholder="请输入手机号码"
            class="input"
            clearable
          ></el-input>
          <el-button type="primary" size="small" @click="handleSearch()"
            >查询</el-button
          >
          <el-button type="primary" size="small" pain @click="handleReset"
            >重置</el-button
          >
        </div>
      </div>
    </div>
    
      <el-table
      class="table" 
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
         height="100%"
        v-loading="loading"
        style="width: 100%"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="personName"
          label="姓名"
          width="100"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="isFullTime"
          label="专（兼）职"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.isFullTime == "1" ? "专职" : "兼职" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="duties"
          label="职务"
          align="center"
          width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="duty"
          label="职责"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="fullTimePersonnel"
          label="手机号码"
          align="center"
          width="120"
        >
        </el-table-column>
        <el-table-column
          prop="certificateType"
          label="证书名称"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="issuanceTimeStr"
          label="发证时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="validityPeriodStr"
          label="证书有效期"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="更新时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          width="100"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)"
              >查看</el-button
            >
          </template>
        </el-table-column>
      </el-table>
  
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="params.nowPage"
        :page-size="params.pageSize"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <safetyDialog
      v-if="detailVisible"
      :show="detailVisible"
      :entObj="safetyOrgInfo"
      @closeBoolean="closeBoolean"
    ></safetyDialog>
  </div>
</template>

<script>
import { getSafetOrgList, getSafetyInformation } from "@/api/safetyInfomation";
import safetyDialog from "./safetyDialog";
export default {
  //import引入的组件
  name: "rescueTeam",
  components: { safetyDialog },
  props: {
    companyCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      tableData: [],
      loading: false,
      total: 0,
      params: {
        nowPage: 1,
        pageSize: 10,
        personType: "safety",
        personName: "",
        phone: "",
        isFullTime: null,
        companyCode: this.companyCode,
        specialOperationPositions: null,
        specialEquipmentOperationPositions: null,
      },
      isFullTimeData: [
        { resourceTypeCode: "1", resourceTypeName: "专职" },
        { resourceTypeCode: "2", resourceTypeName: "兼职" },
      ],
      detailVisible: false,
      safetyOrgInfo: {},
    };
  },
  created() {},
  //方法集合
  methods: {
    handleCurrentChange(val) {
      this.params.nowPage = val;
      this.getList();
    },
    handleSearch() {
      this.params.nowPage = 1;
      this.getList();
    },
    handleReset() {
      this.params = {
        nowPage: 1,
        pageSize: 10,
        personType: "safety",
        personName: "",
        phone: "",
        isFullTime: null,
        companyCode: this.companyCode,
        specialOperationPositions: null,
        specialEquipmentOperationPositions: null,
      };
      this.getList();
    },
    handleView(row) {
      this.safetyOrgInfo = row;
      this.detailVisible = true;
    },
    closeBoolean() {
      this.detailVisible = false;
      this.safetyOrgInfo = {};
    },
    //获取列表
    getList() {
      this.loading = true;
      getSafetOrgList(this.params)
        .then((res) => {
          if (res.data.status == 200) {
            this.tableData = res.data.data.list;
            this.total = Number(res.data.data.total);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    exportExcel() {},
  },
  mounted() {
    this.getList();
  },
};
</script>
<style lang="scss" scoped>
.safetyOrgList {
  width: 100%;
  height: 100%;
  background-color: #fff;

  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
    }
    .operation {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
          margin-bottom: 15px;
        }
        label {
          margin-right: 3px;
        }
      }
    }
  }
  .table {
    width: 100%;
    height: 100%;
  }
  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form_item {
    .form_title {
      font-size: 20px;
      color: #2f85de;
      margin-left: 20px;
    }
  }
}
</style>
