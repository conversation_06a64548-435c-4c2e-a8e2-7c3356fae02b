<template>
  <div class="enterpriseManagement">
    <div class="more" @click="toUrl()">更多</div>
    <el-tabs v-model="activeName" @tab-click="handleClick" class="tab">
      <el-tab-pane label="视频监控" name="1">
        <div class="videoInspection">
          <div class="video-left">
            <div class="videoLeft-top">
              <div class="list-search">
                <el-input
                  v-model.trim="enterpName"
                  size="mini"
                  placeholder="请输入企业名称"
                  class="input"
                  clearable
                  style="width: 260px; margin-left: 10px"
                  @input="changeInput"
                ></el-input>
                <el-button type="primary" size="mini" @click="search"
                  >查询</el-button
                >
              </div>
              <div class="video-list" v-loading="loading">
                <a-directory-tree
                  multiple
                  default-expand-all
                  @select="onSelect"
                  @expand="onExpand"
                  style="padding: 0 10px"
                >
                  <a-tree-node
                    :key="item.id + ',' + item.type"
                    v-for="item in newAllData"
                    :title="item.name"
                  >
                    <a-tree-node
                      v-if="item.children.length > 0"
                      :key="subItem.id + ',' + subItem.type"
                      v-for="subItem in item.children"
                      :title="subItem.name"
                    >
                      <a-tree-node
                        v-if="subItem.children.length > 0"
                        :key="subItems.id + ',' + subItems.type"
                        v-for="subItems in subItem.children"
                        :title="subItems.name"
                      >
                        <a-tree-node
                          v-if="subItems.children.length > 0"
                          :key="subItemd.id + ',' + subItemd.type"
                          v-for="subItemd in subItems.children"
                          :title="subItemd.name"
                        >
                          <a-tree-node
                            v-if="subItemd.children.length > 0"
                            :key="subItemed.id + ',' + subItemed.type"
                            v-for="subItemed in subItemd.children"
                            :title="subItemed.name"
                          ></a-tree-node>
                        </a-tree-node>
                      </a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-directory-tree>
                <!-- <a-tree
                  :multiple="false"
                  default-expand-all
                  @select="onSelect"
                  @expand="onExpand"
                  :tree-data="newAllData"
                  style="padding: 0 10px"
                >
                </a-tree> -->
              </div>
            </div>
          </div>
          <div class="video-right">
            <div class="video-box" id="video-box">
              <div id="playWnd" class="playWnd"></div>
              <!-- <video width="600" height="400" controls='controls' preload="auto">
                    <source src="rtsp://**************:554/openUrl/QwH83Di">
                </video> -->
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="物联报警" name="2"
        ><div class="table">
          <el-table
            :data="tableData"
            v-loading="loading"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            ref="multipleTable"
            class="elTable"
            height="100%"
            width="1100px"
          >
            <!-- height="100%" -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="enterpName"
              label="单位名称"
              align="center"
              min-width="170"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <span
                  @click="goEnt(scope.row)"
                  style="color: rgb(57, 119, 234); cursor: pointer"
                >
                  {{ scope.row.enterpName }}
                </span>
              </template>
            </el-table-column>
            <el-table-column
              :label="isShowDist == true ? '行政区划' : '归属园区'"
              width="80"
              align="center"
            >
              <template slot-scope="{ row, column, $index, store }">
                <span v-if="isShowDist == true">{{ row.districtName }}</span>
                <span v-else>{{ park.parkName }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="dangerName"
              label="重大危险源名称"
              align="center"
              min-width="120"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="monitemkey"
              label="指标类型"
              align="center"
              width="80"
            >
            </el-table-column>
            <el-table-column
              prop="warningType"
              label="报警类型"
              align="center"
              width="80"
            >
            </el-table-column>
            <el-table-column
              prop="warningTime"
              label="报警时间"
              align="center"
              width="155"
            >
            </el-table-column>
            <el-table-column
              prop="alarmHandelTime"
              label="消警时间"
              align="center"
              width="155"
            >
            </el-table-column>
            <el-table-column
              prop="warningDuration"
              label="报警时长"
              align="center"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="state"
              label="当前状态"
              align="center"
              fixed="right"
            >
              <!-- fixed="right" -->
              <template slot-scope="scope">
                <span v-if="scope.row.state == '0'">已消警</span>
                <span v-else-if="scope.row.state == '1'">未消警</span>
                <span v-else></span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import { getOnlineVideoData, getIotMontoringData } from "@/api/riskAssessment";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {},
  data() {
    return {
      tableCheck: true,
      newAllData: [],
      enterpName: "",
      loading: true,
      cameraIndexCode: "",
      videoWidth: 0,
      videoHight: 0,
      oWebControl: null,
      pubKey: "",
      initCount: 0,
      activeName: "1",
      districtVal: this.$store.state.login.userDistCode,
      enterpName: "",
      value1: "",
      sensortypeCode: "",
      monitemkey: "",
      warningType: "",
      state: "",
      startTime: "",
      endTime: "",

      loading: true,
      areaName: "",
      options: [
        {
          value: "G0",
          label: "储罐",
        },
        {
          value: "Q0",
          label: "泄漏点",
        },
        {
          value: "P0",
          label: "装置",
        },
      ],
      options1: [
        {
          value: "WD",
          label: "温度",
        },
        {
          value: "YL",
          label: "压力",
        },
        {
          value: "YW",
          label: "液位",
        },
        {
          value: "YDQT",
          label: "有毒气体",
        },
        {
          value: "KRQT",
          label: "可燃气体",
        },
      ],
      options2: [
        {
          value: "1",
          label: "高报",
        },
        {
          value: "2",
          label: "高高报",
        },
        {
          value: "3",
          label: "低报",
        },
        {
          value: "4",
          label: "低低报",
        },
      ],
      options3: [
        {
          value: "1",
          label: "未消警",
        },
        {
          value: "0",
          label: "已消警",
        },
      ],
      tableData: [],
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      size: 10,
      total: "",
      url: "/gardenEnterpriseManagement/videoOnlineMonitoring",
    };
  },
  methods: {
    handleClick(tab, event) {
      if (this.activeName == "2") {
        this.unloadVideo();
      } else {
        this.initPlugin();
      }
    },
    goEnt(row) {
      // console.log(row)
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpId);
    },
    //物联监测报警列表
    getIotMontoringDataList() {
      this.loading = true;
      getIotMontoringData({
        current: this.currentPage,
        distCode: this.distCode,
        size: 20,
        sensortypeCode: this.sensortypeCode,
        monitemkey: this.monitemkey,
        warningType: this.warningType,
        state: this.state,
        enterpName: this.enterpName,
        startTime: this.startTime,
        endTime: this.endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
        }
      });
    },
    //视频运行列表
    getOnlineVideoDataList() {
      this.loading = true;
      getOnlineVideoData({
        enterpName: this.enterpName,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          //   this.tableData = res.data.data.records;
          //   this.NetWork = false;
          this.newAllData = res.data.data;
          this.setMenus(this.newAllData);
        }
      });
    },
    changeInput(val) {
      if (this.enterpName == "") {
        this.getOnlineVideoDataList();
      }
    },
    setMenus(arr) {
      /**
       * 利用递归替换key值
       * title替换orgName
       * key替换orgCode
       */
      var keyMap = {
        name: "title",
        id: "key",
      };
      for (var i = 0; i < arr.length; i++) {
        delete arr[i].isLeaf;
        var obj = arr[i];
        for (var key in obj) {
          var newKey = keyMap[key];
          //   console.log(newKey);
          if (newKey) {
            obj[newKey] = obj[key];
            if (obj.children.length > 0) {
              this.setMenus(obj.children);
            }
            // delete obj[key];
          }
        }
      }
    },
    goToRunning() {
      //   this.showBreak = false;
      //   // this.level = "";
      //   this.distCode = this.$store.state.login.userDistCode;
      //   if (this.tableCheck) {
      //     this.getOnlineNetworkingList();
      //   } else {
      //     this.getVideoRunningList();
      //   }
    },
    onSelect(selectedKeys, info) {
      if (selectedKeys[0].split(",")[1] == "3") {
        this.cameraIndexCode = selectedKeys[0].split(",")[0];
        this.yulan(this.cameraIndexCode);
      }
    },
    onExpand() {},
    search() {
      this.getOnlineVideoDataList();
    },
    // 创建播放实例
    initPlugin() {
      const that = this;
      that.oWebControl = new WebControl({
        szPluginContainer: "playWnd", // 指定容器id
        iServicePortStart: 15900, // 指定起止端口号，建议使用该值
        iServicePortEnd: 15909,
        szClassId: "23BF3B0A-2C56-4D97-9C03-0CB103AA8F11", // 用于IE10使用ActiveX的clsid
        cbConnectSuccess: function () {
          // 创建WebControl实例成功
          if (that.oWebControl == null) {
            that.initPlugin();
            return;
          }
          that.oWebControl
            .JS_StartService("window", {
              // WebControl实例创建成功后需要启动服务
              dllPath: "./VideoPluginConnect.dll", // 值"./VideoPluginConnect.dll"写死
            })
            .then(
              function () {
                // 启动插件服务成功
                that.oWebControl.JS_SetWindowControlCallback({
                  // 设置消息回调
                  cbIntegrationCallBack: that.cbIntegrationCallBack,
                });

                that.oWebControl
                  .JS_CreateWnd("video-box", that.videoWidth, that.videoHight)
                  .then(function () {
                    //JS_CreateWnd创建视频播放窗口，宽高可设定
                    that.init(); // 创建播放实例成功后初始化
                  });
              },
              function () {
                // 启动插件服务失败
              }
            );
        },
        cbConnectError: function () {
          // 创建WebControl实例失败
          that.oWebControl = null;
          $("#playWnd").html("插件未启动，正在尝试启动，请稍候...");
          window.WebControl.JS_WakeUp("VideoWebPlugin://"); // 程序未启动时执行error函数，采用wakeup来启动程序
          that.initCount++;
          if (that.initCount < 3) {
            setTimeout(function () {
              that.initPlugin();
            }, 3000);
          } else {
            // $("#playWnd").html("插件启动失败，请检查插件是否安装！");
            let host =
              "http://" + window.location.host + "/hg/json/VideoWebPlugin.exe";
            $("#playWnd").html(
              "插件启动失败，请检查插件是否安装！是否安装:" +
                "<a href=" +
                host +
                ' download="VideoWebPlugin.exe">点击下载</a>'
            );
          }
        },
        cbConnectClose: function (bNormalClose) {
          // 异常断开：bNormalClose = false
          // JS_Disconnect正常断开：bNormalClose = true
          console.log("cbConnectClose");
          that.oWebControl = null;
        },
      });
    },

    // 推送消息
    cbIntegrationCallBack(oData) {
      // window.showCBInfo(JSON.stringify(oData.responseMsg));
    },

    //初始化
    init() {
      const that = this;
      that.getPubKey(function () {
        ////////////////////////////////// 请自行修改以下变量值	////////////////////////////////////
        var appkey = "29553988"; //综合安防管理平台提供的appkey，必填
        var secret = that.setEncrypt("NtJZ6GjAj5RbHiqaZXmr"); //综合安防管理平台提供的secret，必填
        var ip = "**************"; //综合安防管理平台IP地址，必填
        var playMode = 0; //初始播放模式：0-预览，1-回放
        var port = 1443; //综合安防管理平台端口，若启用HTTPS协议，默认443
        var snapDir = "D:\\SnapDir"; //抓图存储路径
        var videoDir = "D:\\VideoDir"; //紧急录像或录像剪辑存储路径
        var layout = "1x1"; //playMode指定模式的布局
        var enableHTTPS = 1; //是否启用HTTPS协议与综合安防管理平台交互，是为1，否为0
        var encryptedFields = "secret"; //加密字段，默认加密领域为secret
        var showToolbar = 1; //是否显示工具栏，0-不显示，非0-显示
        var showSmart = 1; //是否显示智能信息（如配置移动侦测后画面上的线框），0-不显示，非0-显示
        var buttonIDs =
          "0,16,256,257,258,259,260,512,513,514,515,516,517,768,769"; //自定义工具条按钮
        ////////////////////////////////// 请自行修改以上变量值	////////////////////////////////////

        that.oWebControl
          .JS_RequestInterface({
            funcName: "init",
            argument: JSON.stringify({
              appkey: appkey, //API网关提供的appkey
              secret: secret, //API网关提供的secret
              ip: ip, //API网关IP地址
              playMode: playMode, //播放模式（决定显示预览还是回放界面）
              port: port, //端口
              snapDir: snapDir, //抓图存储路径
              videoDir: videoDir, //紧急录像或录像剪辑存储路径
              layout: layout, //布局
              enableHTTPS: enableHTTPS, //是否启用HTTPS协议
              encryptedFields: encryptedFields, //加密字段
              showToolbar: showToolbar, //是否显示工具栏
              showSmart: showSmart, //是否显示智能信息
              buttonIDs: buttonIDs, //自定义工具条按钮
            }),
          })
          .then(function (oData) {
            that.oWebControl.JS_Resize(
              $("#video-box").width(),
              that.videoHight
            ); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题

            // console.log("init成功！");
            // that.oWebControl.JS_Resize(that.videoWidth, that.videoHight); // 初始化后resize一次，规避firefox下首次显示窗口后插件窗口未与DIV窗口重合问题
            // that.yulan(that.videoModuleData.data[0].cameraIndexCode);
            that.yulan(that.cameraIndexCode);
          });
      });
    },

    //获取公钥
    getPubKey(callback) {
      const that = this;
      that.oWebControl
        .JS_RequestInterface({
          funcName: "getRSAPubKey",
          argument: JSON.stringify({
            keyLength: 1024,
          }),
        })
        .then(function (oData) {
          console.log(oData);
          if (oData.responseMsg.data) {
            that.pubKey = oData.responseMsg.data;
            callback();
          }
        });
    },

    //RSA加密
    setEncrypt(value) {
      var encrypt = new JSEncrypt();
      encrypt.setPublicKey(this.pubKey);
      return encrypt.encrypt(value);
    },

    // 预览
    yulan(code) {
      const that = this; // 获取输入的监控点编号值，必填
      const streamMode = 0; // 主子码流标识：0-主码流，1-子码流
      const transMode = 1; // 传输协议：0-UDP，1-TCP
      const gpuMode = 0; // 是否启用GPU硬解，0-不启用，1-启用
      const wndId = -1; // 播放窗口序号（在2x2以上布局下可指定播放窗口）
      //      cameraIndexCode = cameraIndexCode.replace(/(^\s*)/g, "");
      //      cameraIndexCode = cameraIndexCode.replace(/(\s*$)/g, "");
      //   let cameraIndexCode = "42011001001320000004";
      let cameraIndexCode = code;
      if (typeof cameraIndexCode === "string") {
        console.log("开始预览！");
        that.oWebControl.JS_RequestInterface({
          funcName: "startPreview",
          argument: JSON.stringify({
            cameraIndexCode, // 监控点编号
            streamMode, // 主子码流标识
            transMode, // 传输协议
            gpuMode, // 是否开启GPU硬解
            wndId, // 可指定播放窗口
          }),
        });
      } else {
        // cameraIndexCode.forEach(function(item, index) {
        //   that.oWebControl.JS_RequestInterface({
        //     funcName: "startPreview",
        //     argument: JSON.stringify({
        //       cameraIndexCode: item, // 监控点编号
        //       streamMode, // 主子码流标识
        //       transMode, // 传输协议
        //       gpuMode, // 是否开启GPU硬解
        //       wndId // 可指定播放窗口
        //     })
        //   });
        // });
      }
    },
    // 销毁视频插件
    unloadVideo(type) {
      if (this.oWebControl != null) {
        this.oWebControl.JS_HideWnd(); // 先让窗口隐藏，规避可能的插件窗口滞后于浏览器消失问题
        this.oWebControl.JS_Disconnect().then(
          function () {},
          function () {}
        );
        if (type === "updateInit") {
          this.initPlugin();
        }
      }
    },
    setWndCover() {
      console.log("裁剪裁剪");
      var iWidth = $("#video-box").width();
      var iHeight = $("#video-box").height();
      var oDivRect = $("#playWnd").get(0).getBoundingClientRect();

      var iCoverLeft = oDivRect.left < 0 ? Math.abs(oDivRect.left) : 0;
      var iCoverTop = oDivRect.top < 0 ? Math.abs(oDivRect.top) : 0;
      var iCoverRight =
        oDivRect.right - iWidth > 0 ? Math.round(oDivRect.right - iWidth) : 0;
      var iCoverBottom =
        oDivRect.bottom - iHeight > 0
          ? Math.round(oDivRect.bottom - iHeight)
          : 0;

      iCoverLeft = iCoverLeft > 1000 ? 1000 : iCoverLeft;
      iCoverTop = iCoverTop > 600 ? 600 : iCoverTop;
      iCoverRight = iCoverRight > 1000 ? 1000 : iCoverRight;
      iCoverBottom = iCoverBottom > 600 ? 600 : iCoverBottom;

      this.oWebControl.JS_RepairPartWindow(0, 0, 1001, 600); // 多1个像素点防止还原后边界缺失一个像素条
      if (iCoverLeft != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, iCoverLeft, 600);
      }
      if (iCoverTop != 0) {
        this.oWebControl.JS_CuttingPartWindow(0, 0, 1001, iCoverTop); // 多剪掉一个像素条，防止出现剪掉一部分窗口后出现一个像素条
      }
      if (iCoverRight != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          1000 - iCoverRight,
          0,
          iCoverRight,
          600
        );
      }
      if (iCoverBottom != 0) {
        this.oWebControl.JS_CuttingPartWindow(
          0,
          600 - iCoverBottom,
          1000,
          iCoverBottom
        );
      }
    },
    initVideo() {
      this.$nextTick(() => {
        this.videoWidth = $("#video-box").width();
        this.videoHight = $("#video-box").height();
        console.log("yyy", this.videoWidth, this.videoHight);
      });

      //    this.videoHight = 400;
      // this.videoModuleData = this.$store.state.login.videoModuleData;
      // if (this.videoModuleData.data.length === 0) {
      // } else {
      //   window.showCBInfo = function() {};
      //   this.initPlugin();
      // }
      this.initPlugin();
      // this.updateVideoCode();
      console.log("进入视频组件~");

      const that = this;
      that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
      that.setWndCover();
      window.addEventListener("resize", function () {
        that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
        that.setWndCover();
      });
      window.addEventListener("scroll", function () {
        that.oWebControl.JS_Resize(that.videoWidth, that.videoHight);
        that.setWndCover();
      });
    },
    toUrl() {
      if (this.activeName == "1") {
        this.url = "/gardenEnterpriseManagement/videoOnlineMonitoring";
      } else {
        this.url = "/dailySafetyManagement/iotMontoringAlarm";
      }
      this.$router.push(this.url);
    },
  },
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getOnlineVideoDataList();
    this.initVideo();
    // console.log(this.videoWidth ,this.videoHight)
    this.getIotMontoringDataList();
  },
  beforeDestroy() {
    this.unloadVideo();
    // console.log("销毁视频组件~");
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
    }),
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-table {
  flex: none;
  width: none;
  max-width: none;
}
.enterpriseManagement {
  width: 100%;
  height: 100%;

  border: 1px solid #d8e0ee;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  position: relative;
  .more {
    color: #3977ea;
    font-size: 14px;
    cursor: pointer;
    position: absolute;
    z-index: 2000;
    right: 15px;
    top: 10px;
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .tab {
    width: 96.5%;
    margin: 0 auto;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    .video-left {
      float: left;
      width: 37%;
      margin-right: 2%;
      // margin-top: 15px;
      .videoLeft-top {
        // background: #daefff;
        .list-search {
          padding-top: 15px;
        }
        .video-list {
          width: 100%;
          height: 285px;
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
    }
    .video-right {
      float: left;
      position: relative;
      width: 61%;
      .video-box {
        width: 100%;
        // margin-top: 15px;
        border: 1px solid #ddd;
        height: 330px;
      }
    }
  }
  .table {
    width: 100%;
    height: 330px;
    overflow-y: auto;
    overflow-x: auto;
    .elTable {
      // width: 1100px;
      min-width: 100%;
      overflow-y: auto;
      overflow-x: auto;
    }
  }
}
</style>
