<template>
  <div class="enterpriseList">
    <div class="header">
      <div>
        <el-select
          v-model="commStateSearchValue"
          clearable
          placeholder="上报情况"
        >
          <el-option
            v-for="item in commStateSearch"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select v-model="levelSearchValue" clearable placeholder="风险级别">
          <el-option
            v-for="item in levelSearch"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-date-picker
          v-model="onTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          unlink-panels
        >
        </el-date-picker>
      </div>
      <div class="commit" @click="getData()">
        <i class="qh-icon-left icon-search_Search"></i>
        查询
      </div>
    </div>

    <div class="from">
      <div class="container">
        <div class="text">安全承诺</div>
        <div class="btnBox">
          <div class="btn" @click="add('/enterprise/enterpriseFill')">
            增加
          </div>
          <div class="btn">导出</div>
        </div>
      </div>
      <el-table :data="tableData" style="table" v-loading="loading">
        <el-table-column type="selection" width="80"> </el-table-column>
        <el-table-column label="日期" width="180">
          <template slot-scope="scope">
            <span
              @click.stop="
                scope.row.commitmentState == '1'
                  ? toPath('/enterprise/enterpriseFill/' + scope.row.id)
                  : toLog(scope.row.id)
              "
              class="toUrl"
              style="color: #2892e2;cursor: pointer;text-decoration:underline"
              >{{ scope.row.dateTime }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="levelRiskName"
          label="级别"
          width="180"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="commitmentStateStr"
          label="上报状态"
          width="180"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="updateTimeStr"
          label="上报完成时间"
          width="450"
          align="center"
        >
        </el-table-column>
        <el-table-column label="操作" width="180" align="center">
          <template slot-scope="scope">
            <i
              href="javascript:void(0);"
              @click="
                scope.row.commitmentState == '1'
                  ? toPath('/enterprise/enterpriseFill/' + scope.row.id)
                  : null
              "
              class="icon-edit toUrl pointer"
              :style="
                scope.row.commitmentState == '1'
                  ? 'color: #2892e2;cursor: pointer;'
                  : 'color: #bfcbd9;cursor: not-allowed;'
              "
            ></i>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="page.pageNo"
        :page-sizes="[10, 20, 50, 100]"
        layout="prev, pager, next, jumper"
        :total="page.total"
        class="pag"
        @prev-click="handleSizeChange"
        @next-click="handleSizeChange"
      >
      </el-pagination>
    </div>
    <div class="back"></div>
  </div>
</template>

<script>
import { getSecurityCommitmentList } from "@/api/reportedList";
import { getSecurityJudgeReport } from "@/api/entList";
import Bus from "../../../../utils/bus";
export default {
  name:"enterpriseList",
  //import引入的组件
  components: {},
  data() {
    return {
      commStateSearch: [
        {
          value: "0",
          label: "已上报"
        },
        {
          value: "1",
          label: "已保存，待上报"
        }
      ],
      commStateSearchValue: "",
      levelSearch: [
        {
          value: "1",
          label: "高风险"
        },
        {
          value: "2",
          label: "较大风险"
        },
        {
          value: "3",
          label: "一般风险"
        },
        {
          value: "4",
          label: "低风险"
        }
      ],
      levelSearchValue: "",
      tableData: [],
      currentPage3: 5,
      onTime: "",
      starTime: "",
      endTime: "",
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 100
      },
      loading: false
    };
  },
  //方法集合
  methods: {
    add(path) {
      console.log(path)
      getSecurityJudgeReport({
        //正式环境enterpriseId需要更换
        enterpriseId: "7a3452a1181246b598d9307d6bab4778"
      }).then(res => {
        if (res.data.data.flag == true) {
          this.toPath('/enterprise/enterpriseFill');
        } else {
          this.$message({
            message: "今天已上报",
            type: "warning"
          });
        }
      });
    },
    toPath(ptah) {
      this.$router.push({ path: ptah });
    },
    toLog(id) {
      Bus.$emit("entId", id);
      this.$router.push({ path: "/enterprise/enterpriseLog/" + id });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    getData() {
      console.log(this.onTime[0], this.onTime[1]);
      this.starTime = String(this.onTime[0]);
      this.endTime = String(this.onTime[1]);
      this.loading = true;
      getSecurityCommitmentList({
        pageNo: this.page.pageNo,
        pageSize: this.page.pageSize,
        starTime: this.starTime,
        endTime: this.endTime,
        commStateSearch: this.commStateSearchValue,
        levelSearch: this.levelSearchValue
      })
        .then(data => {
          if (data.data.code == "success") {
            this.loading = false;
            let listData = data.data.data.rows;
            this.page.total = data.data.data.total;
            this.tableData = listData;
          }
        })
        .catch(e => {
          console.log(e, "请求错误");
        });
    },
    /**
     * 切换分页尺寸
     * @param val
     */
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getData();
      this.$refs.multipleTable.clearSelection();
    },
    /**
     * 切换当前页
     * @param val
     */
    handleCurrentChange(val) {
      this.page.pageNo = val;
      this.getData();
      this.$refs.multipleTable.clearSelection();
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  }
};
</script>
<style lang="scss" scoped>
.enterpriseList {
  //
  // overflow-y: auto;
  overflow-x: hidden;
  .header {
    display: flex;
    justify-content: space-between;
    padding: 10px 30px;
    background-color: #fff;
    white-space: nowrap;
    margin-top: 20px;
    .commit {
      color: #fff;
      background-color: #2892e2;
      border-color: #2892e2;
      text-align: center;
      display: flex;
      align-items: center;
      border-radius: 4px;
      padding: 0 20px;
    }
  }
  .container {
    padding-top: 20px;
    padding-bottom: 20px;
    display: flex;
    justify-content: space-between;
    background: #fff;
    .text {
      margin-left: 20px;
      font-size: 18px;
      font-weight: 900;
      display: flex;
      align-items: center;
    }
    .btnBox {
      display: flex;
    }
  }
  .from {
    background-color: #fff;
    padding: 20px;
    width: 100%;
    overflow-x: hidden;
    margin-top: 20px;
    margin-left: 50%;
    transform: translateX(-50%);
    .table {
    }
  }
  .btn {
    color: #fff;
    background-color: #2892e2;
    border-color: #2892e2;
    border-radius: 4px;
    padding: 10px 15px;
    margin-left: 10px;
    margin-right: 10px;
  }
  .pag {
    margin-top: 10px;
    float: right;
    margin-right: 50px;
  }
  a:visited {
    color: #2892e2;
  }
  a:link {
    color: #2892e2;
  }
  .pointer {
    font-size: 17px;
  }
}
.el-table__header {
  margin-left: 50%;
  transform: translateX(-50%);
}
.back {
  width: 100%;
  height: 20px;
}
</style>
