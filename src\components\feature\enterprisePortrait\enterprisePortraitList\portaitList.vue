<!-- 画像列表 -->
<template>
  <div class="portraitList">
    <div class="operation">
      <div class="inputBox">
        <el-cascader
          size="small"
          placeholder="请选择行政区划"
          :options="district"
          v-model="searchParams.districtCode"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          :show-all-levels="true"
          style="width: 220px"
        ></el-cascader>
        <el-select
          v-model="searchParams.enterpriseTypes"
          size="small"
          multiple
          placeholder="请选择企业类型"
          clearable
        >
          <el-option
            v-for="item in entType"
            :key="item.id"
            :label="item.enterpriseType"
            :value="item.id"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="searchParams.majorHazardLevel"
          size="small"
          placeholder="请选择重大危险源等级"
          clearable
          style="width: 300px"
        >
          <el-option
            v-for="item in levelOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>

        <el-input
          v-model.trim="searchParams.enterpName"
          size="small"
          placeholder="请输入企业名称"
          class="input"
          clearable
        ></el-input>
        <!-- <el-select
          v-model="searchParams.processIds"
          placeholder="请选择工艺"
          size="small"
          :clearable="true"
          style="width: 200px"
        >
          <el-option
            v-for="item in processIdsOption"
            :key="item.processid"
            :label="item.processname"
            :value="item.processid"
          >
          </el-option>
        </el-select> -->

        <el-button type="primary" size="small" @click="handleSearch"
          >查询</el-button
        >
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData"
        :header-cell-style="headerCellStyle"
        border
        style="width: 100%"
        ref="multipleTable"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="enterpName"
          label="企业名称"
          width="340"
          align="center"
          :show-overflow-tooltip="true"
        >
          <!-- <template slot-scope="scope">
                        <span style="color: rgb(57, 119, 234)" class="enterpName">
                            {{ scope.row.enterpName }}
                        </span>
                    </template> -->
        </el-table-column>
        <el-table-column
          prop="districtName"
          label="行政区划 "
          align="center"
          width="160"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="enterpriseType"
          label="企业类型"
          width="160"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ enterpriseTypeName(scope.row.enterpriseType) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="lastCreateTime"
          label="更新时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column prop="manScore" label="人(Man)" align="center">
          <template slot-scope="scope">
            <span>{{ scoreFn("man", scope.row.mainDTOs) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="thingsScore" label="物(Things)" align="center">
          <template slot-scope="scope">
            <span>{{ scoreFn("things", scope.row.mainDTOs) }}</span>
          </template></el-table-column
        >
        <el-table-column
          prop="managementScore"
          label="管(Management)"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("management", scope.row.mainDTOs) }}</span>
          </template></el-table-column
        >
        <el-table-column
          prop="environmentScore"
          label="环(Environment)"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("environment", scope.row.mainDTOs) }}</span>
          </template></el-table-column
        >
        <el-table-column
          prop="supervisionScore"
          label="监(Supervision)"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("supervision", scope.row.mainDTOs) }}</span>
          </template></el-table-column
        >
        <el-table-column
          prop="performanceScore"
          label="绩(Performance)"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("performance", scope.row.mainDTOs) }}</span>
          </template></el-table-column
        >
        <el-table-column
          prop="score"
          label="指标总分"
          align="center"
          fixed="right"
        ></el-table-column>
        <el-table-column
          prop="score"
          label="风险等级"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <span>{{ riskLevel(scope.row.rank) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="趋势" align="center" fixed="right">
          <template slot-scope="scope">
            <div>
              <i
                v-if="scope.row.score > scope.row.lastScore"
                class="el-icon-top"
                style="color: green; font-weight: 700; font-size: 18px"
              ></i>
              <i
                v-else-if="scope.row.score < scope.row.lastScore"
                class="el-icon-bottom"
                style="color: red; font-weight: 700; font-size: 18px"
              ></i>
              <i
                v-else-if="scope.row.score === scope.row.lastScore"
                class="el-icon-minus"
                style="color: #ccc; font-weight: 700; font-size: 18px"
              ></i>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          width="220"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              @click="handleClick(scope.row)"
              icon="el-icon-office-building"
              type="text"
              size="small"
            >
              指数分析
            </el-button>
            <el-button
              @click="handleHistory(scope.row)"
              icon="el-icon-school"
              type="text"
              size="small"
            >
              历史指标
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="searchParams.nowPage"
        :page-size="searchParams.pageSize"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <el-dialog
      v-if="showAnalysis"
      :title="enterpItem.enterpName"
      :visible.sync="showAnalysis"
      @close="closeBoolean(false)"
      width="1500px"
      top="5vh"
      v-dialog-drag
      :key="dialogKey"
      :close-on-click-modal="false"
    >
      <IndexAnalysis :enterpriseInfo="enterpItem"> </IndexAnalysis>
    </el-dialog>
    <el-dialog
      v-if="showHistory"
      :title="enterpItem.enterpName"
      :visible.sync="showHistory"
      @close="closeHistory(false)"
      width="1400px"
      top="5vh"
      v-dialog-drag
      :key="historyKey"
      :close-on-click-modal="false"
    >
      <el-table
        :data="historyData"
        :header-cell-style="headerCellStyle"
        border
        style="width: 100%; margin-top: 20px"
        max-height="450"
      >
        <el-table-column
          prop="enterpName"
          label="企业名称"
          width="300"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="districtName"
          label="行政区划 "
          align="center"
          width="160"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="scope"
          label="风险等级 "
          align="center"
          width="160"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{ riskLevel(scope.row.score) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="enterpriseType"
          label="企业类型"
          width="160"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ enterpriseTypeName(scope.row.enterpriseType) }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column prop="rank" label="等级" align="center" width="120">
        </el-table-column> -->
        <el-table-column
          prop="createTime"
          label="更新时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column prop="manScore" label="人(Man)" align="center">
          <template slot-scope="scope">
            <span>{{ scoreFn("man", scope.row.mainDTOs) }}</span>
            <span v-if="scope.$index < historyData.length - 1">
              <i
                v-if="
                  scoreFn('man', scope.row.mainDTOs) >
                  scoreFn('man', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-top"
                style="color: green"
              ></i>
              <i
                v-else-if="
                  scoreFn('man', scope.row.mainDTOs) <
                  scoreFn('man', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-bottom"
                style="color: red"
              ></i>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="thingsScore"
          label="物(Things)"
          align="center"
          width="100"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("things", scope.row.mainDTOs) }}</span>
            <span v-if="scope.$index < historyData.length - 1">
              <i
                v-if="
                  scoreFn('things', scope.row.mainDTOs) >
                  scoreFn('things', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-top"
                style="color: green"
              ></i>
              <i
                v-else-if="
                  scoreFn('things', scope.row.mainDTOs) <
                  scoreFn('things', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-bottom"
                style="color: red"
              ></i>
            </span> </template
        ></el-table-column>
        <el-table-column
          prop="managementScore"
          label="管(Management)"
          align="center"
          width="150"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("management", scope.row.mainDTOs) }}</span>
            <span v-if="scope.$index < historyData.length - 1">
              <i
                v-if="
                  scoreFn('management', scope.row.mainDTOs) >
                  scoreFn('management', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-top"
                style="color: green"
              ></i>
              <i
                v-else-if="
                  scoreFn('management', scope.row.mainDTOs) <
                  scoreFn('management', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-bottom"
                style="color: red"
              ></i>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="environmentScore"
          label="环(Environment)"
          align="center"
          width="150"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("environment", scope.row.mainDTOs) }}</span>
            <span v-if="scope.$index < historyData.length - 1">
              <i
                v-if="
                  scoreFn('environment', scope.row.mainDTOs) >
                  scoreFn('environment', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-top"
                style="color: green"
              ></i>
              <i
                v-else-if="
                  scoreFn('environment', scope.row.mainDTOs) <
                  scoreFn('environment', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-bottom"
                style="color: red"
              ></i>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="supervisionScore"
          label="监(Supervision)"
          align="center"
          width="150"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("supervision", scope.row.mainDTOs) }}</span>
            <span v-if="scope.$index < historyData.length - 1">
              <i
                v-if="
                  scoreFn('supervision', scope.row.mainDTOs) >
                  scoreFn('supervision', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-top"
                style="color: green"
              ></i>
              <i
                v-else-if="
                  scoreFn('supervision', scope.row.mainDTOs) <
                  scoreFn('supervision', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-bottom"
                style="color: red"
              ></i>
            </span> </template
        ></el-table-column>
        <el-table-column
          prop="performanceScore"
          label="绩(Performance)"
          align="center"
          width="150"
        >
          <template slot-scope="scope">
            <span>{{ scoreFn("performance", scope.row.mainDTOs) }}</span>
            <span v-if="scope.$index < historyData.length - 1">
              <i
                v-if="
                  scoreFn('performance', scope.row.mainDTOs) >
                  scoreFn('performance', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-top"
                style="color: green"
              ></i>
              <i
                v-else-if="
                  scoreFn('performance', scope.row.mainDTOs) <
                  scoreFn('performance', historyData[scope.$index + 1].mainDTOs)
                "
                class="el-icon-bottom"
                style="color: red"
              ></i>
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="score"
          label="指标总分"
          align="center"
          fixed="right"
        ></el-table-column>
        <el-table-column label="趋势" align="center" fixed="right">
          <template slot-scope="scope">
            <div>
              <i
                v-if="scope.row.score > scope.row.lastScore"
                class="el-icon-top"
                style="color: green; font-weight: 700; font-size: 18px"
              ></i>
              <i
                v-else-if="scope.row.score < scope.row.lastScore"
                class="el-icon-bottom"
                style="color: red; font-weight: 700; font-size: 18px"
              ></i>
              <i
                v-else-if="scope.row.score === scope.row.lastScore"
                class="el-icon-minus"
                style="color: #ccc; font-weight: 700; font-size: 18px"
              ></i>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import { getKnowledgeListData, enterpriseType } from "@/api/entList";
import {
  getPortraitInfoPage,
  getPortraitInfoHistory,
} from "@/api/enterprisePortrait";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import IndexAnalysis from "./components/IndexAnalysis.vue";
export default {
  components: { IndexAnalysis },
  data() {
    return {
      historyData: [],
      // district: this.$store.state.controler.district,
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      districtVal: this.$store.state.login.userDistCode,
      loading: false,
      dialogKey: "model",
      historyKey: "history",
      searchParams: {
        //level: ["1", "2", "3", "4"],
        districtCode: this.$store.state.login.userDistCode,
        enterpIds: [],
        enterpName: "",
        enterpriseTypes: [],
        majorHazardLevel: "",
        nowPage: 1,
        pageSize: 10,
      },
      total: 0,
      tableData: [],
      entType: [],
      processIdsOption: [],
      entTypeOption: [],
      auditStatusList: [
        { label: "审核通过", value: "1" },
        { label: "审核不通过", value: "2" },
        { label: "待审核", value: "3" },
      ],
      levelOptions: [
        { label: "一级", value: "1" },
        { label: "二级", value: "2" },
        { label: "三级", value: "3" },
        { label: "四级", value: "4" },
      ],
      showAnalysis: false,
      showHistory: false,
      enterpItem: {},
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
    }),
    ...mapStateControler({
      district: (state) => state.district,
    }),
    riskLevel() {
      return (value) => {
        // 如果value在0到10之间取红,如果在10到20之间取橙,如果在20到30之间取黄,如果在30到40取蓝
        if (value == 'D') {
          return "红";
        } else if (value == 'C') {
          return "橙";
        } else if (value == 'B') {
          return "黄";
        } else if (value == 'A') {
          return "蓝";
        } else {
          return "";
        }
      };
    },
    enterpriseTypeName() {
      return (value) => {
        const item =
          this.entType && this.entType.find((item) => item.id == value);
        return item ? item.enterpriseType : value;
      };
    },
    scoreFn() {
      return (type, list) => {
        if(list && list.length > 0) {
          return list.find((item) => item.mainCode == type).score;
        }
      };
    },
  },
  created() {
    this.getProcess();
    this.getEnterpriseType();
    this.getData();
    if (this.$route.query.enterpName) {
      this.searchParams.enterpName = this.$route.query.enterpName;
      this.getData().then(() => {
        // 查到后自动弹窗
        const target = this.tableData.find(
          item => item.enterpName === this.$route.query.enterpName
        );
        if (target && this.$route.query.openIndexAnalysis) {
          this.handleClick(target); // 打开指数分析弹窗
        }
      });
    }
  },
  methods: {
    handleSearch() {
      this.searchParams.nowPage = 1;
      this.getData();
    },
    //翻页
    handleCurrentChange(val) {
      this.searchParams.nowPage = val;
      this.getData();
    },
    handleClick(row) {
      this.enterpItem = row;
      this.showAnalysis = true;
    },
    // 历史指标
    handleHistory(row) {
      this.enterpItem = row;
      this.showHistory = true;
      // 请求接口获取历史数据
      this.getHistoryData(row?.enterpId);
    },
    // 获取历史指标列表
    getHistoryData(enterpId) {
      const params = {
        enterpId: enterpId,
        nowPage: 1,
        pageSize: 1000,
      };
      getPortraitInfoHistory(params).then((res) => {
        this.historyData = res.data.data.list;
      });
    },
    closeBoolean(boolean) {
      this.showAnalysis = boolean;
      this.enterpItem = {};
    },
    closeHistory(boolean) {
      this.showHistory = boolean;
      this.enterpItem = {};
    },
    // 获取工艺
    getProcess() {
      getKnowledgeListData({
        processid: "",
        processname: "",
      }).then((res) => {
        if (res.data.status === 200) {
          //processIdsOption
          this.processIdsOption = res.data.data;
        }
      });
    },
    // 获取企业类型
    async getEnterpriseType() {
      await enterpriseType({}).then((res) => {
        this.entType = res.data.data;
      });
    },
    async getData() {
      const params = {
        ...this.searchParams,
        //majorHazardLevel: this.searchParams.level.toString(),
      };
      this.loading = true;
      await getPortraitInfoPage(params)
        .then((res) => {
          this.tableData = res.data.data.list;
          this.total = res.data.data.total;
          this.loading = false;
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .subTable.el-table td.el-table__cell,
/deep/ .subTable.el-table th.el-table__cell.is-leaf,
/deep/ .el-table--border .subTable.el-table__cell {
  border-bottom: 0;
  border-right: 0;
}

/deep/ .el-table--border .subTable {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
}

.titTop {
  display: flex;
  justify-content: space-between;

  /deep/ .el-radio-group {
    margin: 0 0 0 0;
  }
}

.operation {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;

  .inputBox {
    min-width: 1150px;
    display: flex;
    justify-content: flex-start;

    .input {
      width: 200px;
    }

    > * {
      margin-right: 15px;
    }
  }
}

.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
::v-deep .el-dialog__body {
  padding-top: 0px;
}
</style>
