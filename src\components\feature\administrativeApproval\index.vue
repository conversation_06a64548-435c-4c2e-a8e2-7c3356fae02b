<template>
    <div class="dailySafetyManagement">
        <component
      :is="
        $route.name === 'todoList'
          ? 'todoList'
          : $route.name
      "
    ></component>
    </div>
  </template>
  
  <script>
import todoList from './todoList.vue'
import historyTodoList from './historyTodoList.vue'

  export default {
    //import引入的组件
    name: "administrativeApproval",
    components: {
        todoList,
        historyTodoList
    },
  };
  </script>
  <style lang="scss" scoped>
  .dailySafetyManagement {
    height: 100%;
  }
  .chart-icon {
    font-size: 12px;
    margin-right: 3px;
  }
  </style>
  