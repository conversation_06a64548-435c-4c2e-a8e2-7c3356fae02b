define(["exports","./ComponentDatatype-a9820060"],(function(t,n){"use strict";const o={computePositions:function(t,o,e,s,r){const i=.5*t,a=-i,c=s+s,u=new Float64Array(3*(r?2*c:c));let y,f=0,m=0;const p=r?3*c:0,h=r?3*(c+s):3*s;for(y=0;y<s;y++){const t=y/s*n.CesiumMath.TWO_PI,c=Math.cos(t),l=Math.sin(t),C=c*e,M=l*e,d=c*o,P=l*o;u[m+p]=C,u[m+p+1]=M,u[m+p+2]=a,u[m+h]=d,u[m+h+1]=P,u[m+h+2]=i,m+=3,r&&(u[f++]=C,u[f++]=M,u[f++]=a,u[f++]=d,u[f++]=P,u[f++]=i)}return u}};t.CylinderGeometryLibrary=o}));
