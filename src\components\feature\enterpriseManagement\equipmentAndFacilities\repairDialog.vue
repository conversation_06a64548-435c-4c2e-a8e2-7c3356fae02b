<template>
  <div>
    <!-- 录入 -->
    <el-dialog :append-to-body="true"
               :close-on-click-modal="false"
               :visible.sync="open"
               title="检维修记录"
               width="900px"
               @close="close()"
               top="10vh">
      <div class="container">
        <div class="form_item">
          <div class="form_main">
            <el-form ref="form"
                     :model="formTitle"
                     label-width="150px">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="所属重大危险源">
                    <el-input v-model="type === 'device' ? formTitle.sysGreatHazardName : formTitle.hazardIdName"
                              :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="危险源编码">
                    <el-input v-model="type === 'device' ? formTitle.sysGreatHazard : formTitle.hazardId"
                              :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="设备名称">
                    <el-input v-model="type === 'device' ? formTitle.nameOfProductionUnit : formTitle.tankName"
                              :disabled="true" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="设备编码">
                    <el-input v-model="type === 'device' ? formTitle.productionUnitCode : formTitle.tankNum"
                              :disabled="true" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
        <div class="form_item">
          <h3 class="form_title">检维修记录</h3>
          <div class="form_main"
               v-loading="loading">
            <el-collapse v-model="activeName"
                         accordion
                         v-if="formDataRepair.length > 0">
              <el-collapse-item :name="item.maintenanceId"
                                v-for="(item, index) in formDataRepair"
                                :key="index">
                <template slot="title">
                  <h3 class="time">
                    <!--                    <span> {{ index + 1 }}. </span>-->
                    {{ item.maintenanceTime ? new Date(item.maintenanceTime).Format("yyyy-MM-dd") : "" }}
                  </h3>
                </template>
                <div class="box">
                  <div>
                    <span class="title">操作负责人：</span>
                    <span>{{ item.operator }}</span>
                  </div>
                  <div>
                    <span class="title">联系方式：</span>
                    <span>{{ item.contact }}</span>
                  </div>
                  <div>
                    <span class="title">更换备品清单：</span>
                    <span>{{ item.replacementContent }}</span>
                  </div>
                  <div>
                    <span class="title">检维修内容：</span>
                    <span>{{ item.maintenanceContent }}</span>
                  </div>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-empty v-else
                      :image-size="160"
                      description="暂无数据"></el-empty>
          </div>
        </div>
      </div>
      <div slot="footer"
           class="dialog-footer"
           v-if="formDataRepair.length > 0">
        <el-pagination background
                       @current-change="handleCurrentChange"
                       :current-page.sync="nowPage"
                       :page-size="10"
                       layout="total,prev, pager, next"
                       :total="total">
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'repairDialog',
  data() {
    return {
      formDataRepair: this.form.list || [],
      allShebeiNameData: [],
      dangerIdIsNotNullListDataList: [],
      open: this.value,
      activeName: '',
      nowPage: 0,
      formTitle: this.viewForm,
      loading: false,
      total: 0
    }
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    form: {
      type: Object,
      default: {}
    },
    viewForm: {
      type: Object,
      default: {}
    },
    type: {
      type: String,
      default: ''
    }
  },
  watch: {
    value: {
      handler(newVal) {
        //监听props中的属性
        this.open = newVal
      }
    },
    form: {
      handler(newVal) {
        //监听props中的属性
        this.formDataRepair = newVal.list
        this.total = newVal.total
      }
    },
    viewForm: {
      handler(newVal) {
        //监听props中的属性
        this.formTitle = newVal
      }
    }
  },
  methods: {
    close() {
      this.formDataRepair = []
      this.form.total = 0
      this.nowPage = 1
      this.$emit('input', false)
    },
    handleCurrentChange() {
      this.$emit('handleCurrentChange', {
        currentPage: this.nowPage,
        total: this.formDataRepair.total,
        pageSize: this.formDataRepair.pageSize
      })
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-collapse-item__header {
  //background: rgb(241, 246, 255);
  //color: rgb(51, 51, 51);
}
.container {
  height: 60vh;
  overflow-y: auto;
  overflow-y: overlay;
  overflow-x: hidden;
  padding-right: 10px;
  .form_item {
    .form_main {
      .box {
        padding-left: 10px;
        .title {
          font-weight: 900;
        }
      }
      .time {
        padding-left: 5px;
        display: flex;
        & > span {
          width: 30px;
          margin-right: 5px;
          display: block;
          overflow: auto;
          text-overflow: clip;
          white-space: normal;
        }
      }
    }
  }
}
</style>
