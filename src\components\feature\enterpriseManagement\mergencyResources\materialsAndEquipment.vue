<template>
  <div class="enterpriseList">
    <div class="header">
      <div class="title">物资装备列表</div>
      <div class="operation">
        <div class="inputBox">
          <!-- <el-select
            v-if="$store.state.login.user.user_type == 'gov'"
            v-model="orgCode"
            size="small"
            filterable
            :clearable="true"
            placeholder="请输入/选择所属单位名称"
          >
            <el-option
              v-for="item in orgData"
              :key="item.orgCode"
              :label="item.orgName"
              :value="item.orgCode"
            >
            </el-option>
          </el-select> -->

          <el-input
            v-model.trim="orgName"
            v-if="$store.state.login.user.user_type == 'gov'"
            size="small"
            placeholder="请输入所属单位名称"
            class="input"
            clearable
          ></el-input>

          <el-select
            v-model="type"
            size="small"
            placeholder="请选择物资装备类型"
            :clearable="true"
          >
            <el-option
              v-for="item in materialsAndEquipmentTypeData"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-button type="primary" size="small" @click="searches()"
            >查询</el-button
          >
          <CA-button
            type="primary"
            size="mini"
            class="export"
            plain
            @click="exportExcel"
            >导出</CA-button
          >
        </div>
        <el-button
          type="primary"
          size="small"
          @click="addEdit"
          v-if="$store.state.login.user.user_type == 'ent'"
          >新增</el-button
        >
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="55"
          fixed="left"
          align="center"
        >
        </el-table-column>
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="name"
          label="名称"
          width="230"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="resourceTypeName"
          label="类型"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="materialNum"
          label="数量"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="unit"
          label="计量单位"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="orgName"
          label="所属单位/队伍"
          align="center"
          min-width="300"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="respPer"
          label="主要负责人"
          align="center"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="respoTel"
          label="负责人办公电话"
          align="center"
          min-width="120"
        >
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          min-width="160"
        >
          <template slot-scope="scope">
            <span
              @click="view(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >查看</span
            >
            <span
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="edit(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >编辑</span
            >
            <span
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="deleter(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >删除</span
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="size"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <!-- 新增编辑查看 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1100px"
      :close-on-click-modal="false"
      :append-to-body="true"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        :disabled="disabled"
        label-width="150px"
        :hide-required-asterisk="disabled"
      >
        <div class="form_item">
          <h2 class="form_title">基本信息</h2>
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="名称" prop="name">
                  <el-input
                    v-model.trim="form.name"
                    maxlength="50"
                    placeholder="请输入名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="类型" prop="type">
                  <!-- <el-select v-model="form.type" placeholder="请选择类型">
                    <el-option
                      v-for="item in materialsAndEquipmentTypeData"
                      :key="item.id"
                      :label="item.label"
                      :value="item.id"
                    />
                  </el-select> -->
                  <!-- {{form.type}} -->
                  <el-cascader
                    v-model="form.type"
                    placeholder="请选择类型"
                    style="width: 100%"
                    size="small"
                    :options="materialsAndEquipmentTypeData"
                    @change="handleChangePublishOrgCode"
                    :props="{
                      disabled: 'virtualNode',
                      value: 'id',
                      checkStrictly: true,
                    }"
                  >
                  </el-cascader>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计量单位" prop="unit">
                  <el-input
                    v-model.trim="form.unit"
                    maxlength="10"
                    placeholder="请输入计量单位"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物资数量" prop="materialNum">
                  <el-input
                    v-model.trim="form.materialNum"
                    placeholder="请输入物资数量"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人">
                  <el-input
                    v-model.trim="form.respPer"
                    maxlength="20"
                    placeholder="请输入负责人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="负责人办公电话" prop="respoTel">
                  <el-input
                    v-model.trim="form.respoTel"
                    placeholder="请输入负责人办公电话"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单价(元)" prop="materialPrice">
                  <el-input
                    v-model.trim="form.materialPrice"
                    placeholder="请输入单价"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="所属单位" prop="orgname">
                  <el-input
                    :disabled="true"
                    v-model.trim="form.orgname"
                    placeholder="请输入所属单位"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="经度:">
                  <el-input
                    v-model.trim="form.longitude"
                    disabled
                    placeholder="请输入经度"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="纬度:">
                  <el-input
                    v-model.trim="form.latitude"
                    disabled
                    placeholder="请输入纬度"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="12">
              <el-col :span="24">
                <div class="map_height">
                  <el-form-item label="地图定位">
                    <egisMap
                      :islistener="false"
                      ref="detailMap"
                      :datas="form"
                      style="height: 200px"
                      @mapCallback="mapcallback"
                    ></egisMap>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="备注" prop="notes">
                  <el-input
                    type="textarea"
                    placeholder="请输入备注"
                    v-model.trim="form.notes"
                    :rows="4"
                    maxlength="500"
                    show-word-limit
                  >
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="附件">
                  <AttachmentUpload
                    :attachmentlist="form.attachmentList"
                    :limit="1"
                    type="exVideo"
                    v-bind="{}"
                    :editabled="disabled"
                  ></AttachmentUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="!disabled" slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import {
  getMaterialsAndEquipmentTypeData,
  getMaterialsAndEquipmentListData,
  addMaterialsAndEquipmentListData,
  editMaterialsAndEquipmentListData,
  deleteMaterialsAndEquipmentListData,
  getOrgList,
  exportMaterialListData,
} from "@/api/mergencyResources";
// import FileUpload from '@/components/common/packages/FileUpload';
const mapConfig = require("@/assets/json/map.json");
import { createNamespacedHelpers } from "vuex";
import { size } from "lodash-es";
// const { mapState: mapStateLogin } = createNamespacedHelpers("login");
// const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  name: "materialsAndEquipment",
  components: {
    AttachmentUpload,
  },
  data() {
    return {
      chargeDept: "",
      currentPage: 1,
      title: "新增物资装备",
      open: false,
      materialsAndEquipmentTypeData: [],
      type: "",
      orgCode: "",
      orgName: "",
      orgData: [],
      accidentForm: {
        // 事故表单
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
      },
      form: {
        address: "",
        attachmentList: [],
        districtCode: "",
        emresourceTypeId: "RES_MAT_STORAGE_PT",
        emresourceTypeName: "",
        id: "",
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        materialNum: "",
        materialPrice: "",
        memo: "",
        name: "",
        notes: "",
        orgcode: "",
        orgname: "",
        parentCode: "",
        placeId: "",
        placeName: 0,
        resourceTypeCode: "",
        resourceTypeName: "",
        resourceTypeShortName: "",
        respFax: "",
        respPer: "",
        respmTel: "",
        respoTel: "",
        spcType: "",
        type: "",
        unit: "",
      },
      rules: {
        name: [{ required: true, message: "请输入名称", trigger: "blur" }],
        type: [
          {
            required: true,
            message: "请选择类型",
            trigger: ["blur", "change"],
          },
        ],
        // type: [
        //   {
        //     type: "array",
        //     required: true,
        //     message: "请选择类型",
        //     trigger: "blur",
        //   },
        // ],
        unit: [{ required: true, message: "请输入计量单位", trigger: "blur" }],
        materialNum: [
          { required: true, message: "请输入物资装备数量", trigger: "blur" },
          { pattern: /^\d{1,9}$/, message: "最多输入9位数字" },
        ],
        respoTel: [
          { required: true, message: "请输入负责人办公电话", trigger: "blur" },
          {
            pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,
            message: "请输入正确的电话号码",
          },
        ],

        latitude: [
          { required: true, message: "经纬度不能为空", trigger: "blur" },
        ],

        latitude: [
          { required: true, message: "经纬度不能为空", trigger: "blur" },
        ],

        orgcode: [
          { required: true, message: "请选择所属单位", trigger: "change" },
        ],
        materialPrice: [
          {
            validator: (rule, value, callback) => {
              if (!value) {
                callback();
                return;
              }
              // 检查是否为有效数字且不以小数点结尾，最多9位数字
              if (/^\d[0-9.]{0,8}$/.test(value) && !value.endsWith(".")) {
                callback();
              } else {
                callback(new Error("最多输入9位数字"));
              }
            },
          },
        ],
      },
      disabled: false,
      tableData: [],
      loading: false,
      selection: [],
      total: 0,
      size: 10,
    };
  },
  //方法集合
  methods: {
    handleChangePublishOrgCode(value) {
      // v-model="form.type"
      // debugger
      if (value.length > 0) {
        this.form.type = value[value.length - 1];
      } else {
        this.form.type = "";
      }
    },
    // 地图定位
    mapcallback(data) {
      // 标点赋值
      this.form.longitude = data.location.lon.toFixed(6);
      this.form.latitude = data.location.lat.toFixed(6);
    },
    addEdit() {
      this.reset();
      this.form.orgcode = this.$store.state.login.user.org_code;
      this.form.orgname = this.$store.state.login.user.org_name;
      // this.form.type = "010100  ";
      this.open = true;
      this.disabled = false;
      // this.getDistrict()
    },
    view(row) {
      this.reset();
      this.open = true;
      this.disabled = true;
      this.title = "查看物资装备";
      // this.form = row;
      // this.form.orgname = row.orgName;
      const rowData = Object.assign({}, row);
      this.form = rowData;
      this.form.orgname = rowData.orgName;
    },
    edit(row) {
      // debugger
      this.reset();
      this.open = true;
      this.disabled = false;
      this.title = "编辑物资装备";
      const rowData = Object.assign({}, row);
      this.form = rowData;
      this.form.orgname = rowData.orgName;
    },
    deleter(row) {
      const id = row.id;
      this.$confirm("是否确认删除该物资装备信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteMaterialsAndEquipmentListData({
            id: id,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("删除成功");
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getList();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getList();
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        address: "",
        attachmentList: [],
        districtCode: "",
        emresourceTypeId: "RES_MAT_STORAGE_PT",
        emresourceTypeName: "",
        id: "",
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        materialNum: "",
        materialPrice: "",
        memo: "",
        name: "",
        notes: "",
        orgcode: "",
        orgname: "",
        parentCode: "",
        placeId: "",
        placeName: 0,
        resourceTypeCode: "",
        resourceTypeName: "",
        resourceTypeShortName: "",
        respFax: "",
        respPer: "",
        respmTel: "",
        respoTel: "",
        spcType: "",
        type: "",
        unit: "",
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
    //查询
    searches() {
      this.currentPage = 1;
      this.getList();
    },
    getOrgData() {
      getOrgList({
        districtCode: "",
        nowPage: 1,
        orgCode: "",
        orgName: "",
        pageSize: 100,
        tenantId: "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.orgData = res.data.data;
        }
      });
    },
    //获取列表
    getList() {
      this.loading = true;
      getMaterialsAndEquipmentListData({
        emresourceTypeId: [],
        endTime: "",
        id: "",
        keyWords: "",
        listOrder: {},
        materialInfoIds: [],
        name: "",
        nowPage: this.currentPage,
        orgCode: this.orgCode,
        pageSize: this.size,
        spcType: "",
        startTime: "",
        tenantId: "",
        orgName: this.orgName,
        type: this.type ? [this.type] : [],
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    //获取物资装备类型
    getMaterialsAndEquipmentType() {
      getMaterialsAndEquipmentTypeData({}).then((res) => {
        if (res.data.status == 200) {
          this.materialsAndEquipmentTypeData =
            res.data.data.treeData[0].children;
          this.materialsAndEquipmentTypeData.forEach((item) => {
            item.value = item.id;
            if (item.children && item.children.length > 0) {
              item.children.forEach((items) => {
                items.value = items.id;
                if (items.children && items.children.length > 0) {
                  items.children.forEach((itemed) => {
                    itemed.value = itemed.id;
                  });
                }
              });
            }
          });
        }
      });
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then((res) => {
        let child = res.data.data;
        // debugger;
        if (child.children.length > 0) {
          for (let j = 0; j < child.children.length; j++) {
            if (child.children[j].children.length > 0) {
              for (let z = 0; z < child.children[j].children.length; z++) {
                if (child.children[j].children[z].children.length < 1) {
                  //判断children的数组长度
                  child.children[j].children[z].children = undefined;
                }
              }
            } else {
              child.children[j].children = undefined;
            }
          }
        } else {
          child.children = undefined;
        }
        this.district = [child];
      });
    },
    changeCascader(value) {
      if (!value) {
        this.form.cityDistrictCode = this.$store.state.login.userDistCode;
      }
    },
    submitForm() {
      // debugger
      if (this.form.type == "") {
        this.$message.error("类型不能为空");
        return false;
      }
      this.$refs.form.validate((flag) => {
        if (flag) {
          if (this.form.id) {
            editMaterialsAndEquipmentListData(this.form).then((res) => {
              if (res.data.status == 200) {
                this.$message.success("操作成功");
                this.open = false;
                this.reset();
                this.getList();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else {
            addMaterialsAndEquipmentListData(this.form).then((res) => {
              if (res.data.status == 200) {
                this.$message.success("操作成功");
                this.open = false;
                this.reset();
                this.getList();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        }
      });
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        this.form.address = data.formatted_address;
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
    handleSelectionChange() {},
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    exportExcel() {
      exportMaterialListData({
        emresourceTypeId: [],
        endTime: "",
        id: "",
        keyWords: "",
        listOrder: {},
        materialInfoIds: [],
        name: "",
        nowPage: this.currentPage,
        orgCode: this.orgCode,
        pageSize: this.size,
        spcType: "",
        startTime: "",
        tenantId: "",
        orgName: this.orgName,
        type: this.type ? [this.type] : [],
        materialInfoIds: this.selection,
      }).then((response) => {
        this.$message({
          message: "导出成功",
          type: "success",
        });
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "物资装备列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
  },
  mounted() {
    this.form.orgcode = this.$store.state.login.user.org_code;
    this.form.orgname = this.$store.state.login.user.org_name;
    this.getOrgData();
    console.log(mapConfig.map.defaultExtent.center[0]);
    setInterval(function () {
      // 解决级联选择器不点击圆点选中元素问题
      document.querySelectorAll(".el-cascader-node__label").forEach((el) => {
        el.onclick = function () {
          if (this.previousElementSibling) this.previousElementSibling.click();
        };
      });
    }, 1000);
  },
};
</script>
<style lang="scss" scoped>
.enterpriseList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    margin-bottom: 10px;
    .title {
      margin-right: 10px;
      margin-bottom: 5px;
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      // margin-top: 20px;
      // margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
        }
      }
    }
    .export {
      // margin-right: 20px;
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form_item {
    .form_title {
      font-size: 20px;
      color: #2f85de;
      margin-left: 20px;
    }
  }
}
</style>
<style lang="scss" scoped>
/deep/.el-dialog {
  height: 650px;
  overflow: hidden;
  .el-dialog__body {
    height: 80%;
    overflow: auto;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
}
// /deep/ .el-cascader-panel .el-radio{
//   display:none!important;
//   background: red;
// }
// /deep/ .el-cascader-node>.el-radio, .el-radio:last-child{
//   display:none!important;
// }
// /deep/ .el-radio__original{
//   visibility: hidden!important;
// }
</style>
