<template>
  <div class="safetyCommitment" v-loading="loading">
    <div v-if="fillBool">
      <div class="div1">
        <div class="title">
          <span>作业现状</span>
          <div>
            <span
              @click="backFn"
              v-if="
                $store.state.login.user.user_type == 'gov' &&
                $store.state.login.user.isDanger == '1'
              "
            >
              <el-button type="primary" size="mini">退 回</el-button>
            </span>&nbsp;&nbsp;&nbsp;&nbsp;
            <el-button type="primary" @click="openDialog" size="mini"
              >历史承诺</el-button
            >
          </div>
        </div>
        <div class="table">
          <ul class="container">
            <li>
              <div class="l"><span class="red">*</span>生产装置套数（套）</div>
              <div class="r">{{ table.unitsNumber }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>运行套数（套）</div>
              <div class="r">{{ table.runNumber }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>停车套数（套）</div>
              <div class="r">{{ table.parkNumber }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>一级重大危险源（处）
              </div>
              <div class="r">{{ table.dangerlevelone }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>二级重大危险源（处）
              </div>
              <div class="r">{{ table.dangerleveltwe }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>三级重大危险源（处）
              </div>
              <div class="r">{{ table.dangerlevelthree }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>四级重大危险源（处）
              </div>
              <div class="r">{{ table.dangerleveltfour }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>试生产装置数（处）</div>
              <div class="r">{{ table.tryunitsNumber }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>重点监管危险工艺（种）
              </div>
              <div class="r">{{ table.dangermsds }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>开车装置数（处）</div>
              <div class="r">{{ table.workNumber }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>停车装置数（处）</div>
              <div class="r">{{ table.notWorkNumber }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>是否有承包商作业</div>
              <div class="r">{{ table.contractor == 1 ? "是" : "否" }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>是否处于试生产期</div>
              <div class="r">
                {{ table.trialProduction == 1 ? "是" : "否" }}
              </div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>是否处于开停车状态</div>
              <div class="r">{{ table.openParking == 1 ? "是" : "否" }}</div>
            </li>
            <li class="">
              <div class="l"><span class="red">*</span>是否开展中（扩）</div>
              <div class="r">{{ table.test == 1 ? "是" : "否" }}</div>
            </li>
            <li class="">
              <div class="l"><span class="red">*</span>有无重大隐患</div>
              <div class="r">{{ table.mhazards == 1 ? "有" : "无" }}</div>
            </li>
            <li class="bottom">
              <div class="l"><span class="red">*</span>检维修套数（处）</div>
              <div class="r">{{ table.fixnum }}</div>
            </li>
            <li class="bottom">
              <div class="l"></div>
              <div class="r"></div>
            </li>
          </ul>
        </div>
      </div>
      <div class="div2">
        <div class="title">风险作业</div>
        <div class="table">
          <ul class="container">
            <li>
              <div class="l">
                <span class="red">*</span>特级动火作业数（处）
              </div>
              <div class="r">{{ table.firesNumber }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>一级动火作业数（套）
              </div>
              <div class="r">{{ table.fire1Number }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span> 二级动火作业数（处）
              </div>
              <div class="r">{{ table.fire2Number }}</div>
            </li>
            <li>
              <div class="l">
                <span class="red">*</span>受限空间作业数（处）
              </div>
              <div class="r">{{ table.spaceworkNumber }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>盲板作业数（处）</div>
              <div class="r">{{ table.blindplateNumber }}</div>
            </li>
            <li>
              <div class="l"><span class="red">*</span>高处作业数（处）</div>
              <div class="r">{{ table.highworkNumber }}</div>
            </li>
            <li class="">
              <div class="l"><span class="red">*</span>吊装作业数（处）</div>
              <div class="r">{{ table.liftingworkNumber }}</div>
            </li>
            <li class="">
              <div class="l">
                <span class="red">*</span>临时用电作业数（处）
              </div>
              <div class="r">{{ table.electricityworkNumber }}</div>
            </li>
            <li class="">
              <div class="l"><span class="red">*</span>动土作业数（处）</div>
              <div class="r">{{ table.soilworkNumber }}</div>
            </li>
            <li class="bottom">
              <div class="l"><span class="red">*</span>断路作业数（处）</div>
              <div class="r">{{ table.roadworkNumber }}</div>
            </li>
            <li class="bottom">
              <div class="l"><span class="red">*</span>检维修作业数（处）</div>
              <div class="r">{{ table.inspectionNumber }}</div>
            </li>
            <li class="bottom">
              <div class="l"></div>
              <div class="r"></div>
            </li>
          </ul>
        </div>
      </div>
      <div class="div3">
        <div class="title">企业承诺</div>
        <div class="table">
          <!-- 2021.7.29 根据需求反馈再次更改安全承诺等级描述 -->
          <div class="imgBox">
            <div
              class="img1"
              v-if="
                table.riskGrade == 4 ||
                table.riskGrade == null ||
                table.riskGrade == ''
              "
            >
              <div>低风险</div>
            </div>
            <div class="img2" v-if="table.riskGrade == 3">
              <div>一般风险</div>
            </div>
            <div class="img3" v-if="table.riskGrade == 2">
              <div>较大风险</div>
            </div>
            <div class="img4" v-if="table.riskGrade == 1">
              <div>高风险</div>
            </div>
          </div>
          <div class="text">
            <div class="promise">
              {{ table.subject }}
            </div>
            <div class="box">
              <div class="name">承诺人：{{ table.commitment }}</div>
              <div class="time">{{ table.commiteDate }}</div>
            </div>
          </div>
        </div>
      </div>
      <HistoryList ref="HistoryList"></HistoryList>
    </div>
    <Fill
      @save="saveTable"
      :enterpriseId="enterpriseId"
      v-if="!fillBool"
      v-bind="$attrs"
    ></Fill>
  </div>
</template>

<script>
import HistoryList from "./historyList";
import Fill from "./fill";
import { getPromiseToday, sendBack } from "@/api/entList";
export default {
  //import引入的组件
  components: {
    Fill,
    HistoryList,
  },
  data() {
    return {
      enterpriseId: "",
      table: {},
      fillBool: false,
      loading: false,
    };
  },
  //方法集合
  methods: {
    backFn() {
      // this.getData(this.enterpriseId)
      //退回

      this.$confirm("确定要退回吗？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          sendBack({ enterpId: this.enterpriseId }).then((res) => {
            if (res.data.data) {
              this.$message.success("退回成功");
              this.getData(this.enterpriseId);
            }
          });
        })
        .catch(() => {});

      // getPromiseToday({enterpId:this.enterpriseId}).then((res)=>{
      //   if(res.data.data){
      //     this.$message.success("退回成功");
      //   }
      // })
    },
    openDialog() {
      this.$refs.HistoryList.closeBoolean(true);
      this.$refs.HistoryList.getData(this.enterpriseId);
    },
    getData(id) {
      this.enterpriseId = id;
      this.loading = true;
      getPromiseToday(this.enterpriseId).then((res) => {
        this.loading = false;
        if (res.data.data == null) {
          this.fillBool = false;
        } else if (res.data.data.commitmentstate == 0) {
          this.fillBool = true;
          this.table = res.data.data;
        } else if (res.data.data.commitmentstate == 1) {
          this.fillBool = false;
          this.table = res.data.data;
        }
      });
    },
    saveTable(data) {
      this.fillBool = data;
      this.loading = true;
      getPromiseToday(this.enterpriseId).then((res) => {
        this.loading = false;
        if (res.data.data == null) {
          this.fillBool = false;
        } else if (res.data.data.commitmentstate == 0) {
          this.fillBool = true;
          this.table = res.data.data;
        } else if (res.data.data.commitmentstate == 1) {
          this.fillBool = false;
          this.table = res.data.data;
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素
  mounted() {},
};
</script>
<style lang="scss" scoped>
.safetyCommitment {
  overflow: hidden;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    // margin-top: 20px;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      font-size: 18px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
      }
    }
  }
  .div2 {
    margin-top: 20px;
    .title {
      font-weight: 600;

      font-size: 18px;
      margin-bottom: 10px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;

            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
      }
    }
  }
  .div3 {
    margin-top: 20px;
    padding-bottom: 50px;
    width: 100%;
    .title {
      font-weight: 600;
      margin-bottom: 10px;

      font-size: 18px;
    }
    .table {
      width: 100%;
      // min-height: 240px;
      border: 1px solid rgb(231, 231, 231);
      position: relative;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: #f3f8fd;
      .imgBox {
        width: 20%;
        height: 100%;
        // position: absolute;
        // background-color: #f3f8fd;
        // border-right: 1px solid rgb(231, 231, 231);
        display: flex;
        align-items: center;
        justify-content: center;
        .img1 {
          background: url("../../../../../static/img/lowFx.png") no-repeat;
          background-position: center center;
          width: 174px;
          height: 216px;
          color: #4f8efd;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          div {
            height: 65px;
            width: 100%;
            text-align: center;
          }
        }
        .img2 {
          background: url("../../../../../static/img/mediumFx.png") no-repeat;
          background-position: center center;
          width: 174px;
          height: 216px;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          color: #f2b625;
          div {
            height: 65px;
            width: 100%;
            text-align: center;
          }
        }
        .img3 {
          background: url("../../../../../static/img/hightFx.png") no-repeat;
          background-position: center center;
          width: 174px;
          height: 216px;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          color: #ff9332;
          div {
            height: 65px;
            width: 100%;
            text-align: center;
          }
        }
        .img4 {
          background: url("../../../../../static/img/majorFx.png") no-repeat;
          background-position: center center;
          width: 174px;
          height: 216px;
          display: flex;
          justify-content: center;
          align-items: flex-end;
          color: #f8574d;
          div {
            height: 65px;
            width: 100%;
            text-align: center;
          }
        }
      }
      .text {
        width: calc(100% - 174px);
        height: 100%;
        // padding-bottom: 10px;
        border-left: 1px solid rgb(231, 231, 231);
        background: #fff;
        min-height: 216px;
        // margin-left: 22%;
        // position: absolute;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .promise {
          font-size: 16px;
          text-indent: 2em;
          padding: 10px 0 0 20px;
        }
        .box {
          display: flex;
          flex-direction: column;
          justify-content: flex-end;
          align-items: flex-end;
          text-align: center;
          & > div {
            width: 200px;
          }
          .name {
            font-weight: 600;
            margin: 5px 0;
          }
        }
      }
    }
  }
}
</style>
