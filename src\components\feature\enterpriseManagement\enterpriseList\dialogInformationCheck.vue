<template>
  <div class="dialogInformationCheck">
    <el-dialog
      title="审核信息"
      :visible="show"
      @close="closeBoolean(false)"
      width="900px"
      v-dialog-drag
      :key="dialogKey"
      :close-on-click-modal="false"
    >
      <div class="container">
        <el-table :data="tableData" border style="width: 100%">
          <el-table-column
            fixed
            prop="creatBy"
            label="审核人员"
            width="150"
            align="center"
          >
          </el-table-column
          ><el-table-column
            fixed
            prop="creatTime"
            label="审核时间"
            width="250"
            align="center"
          >
          </el-table-column>
          <el-table-column
            fixed
            prop="auditStatus"
            label="审核结果"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ row.auditStatus === "1" ? "审核通过" : "审核不通过" }}
            </template>
          </el-table-column>
          <el-table-column
            fixed
            prop="feedback"
            label="反馈详情"
            align="center"
          >
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getExamineRecord } from "@/api/entList";
export default {
  name: "dialogInformationCheck",
  data() {
    return {
      CloseData: {},
      tableData: [],
      dialogKey: "",
      textarea: "",
      radio: 0,
    };
  },
  props: ["enterpId", "show"],
  methods: {
    closeBoolean() {
      this.radio = 1;
      this.textarea = "";
      // 初始化弹窗位置
      this.dialogKey = new Date();
      this.$emit("closeBoolean", {
        boolean: false,
        name: "dialogInformationCheckShow",
      });
    },
    getData() {
      getExamineRecord(this.enterpId).then((res) => {
        this.tableData = res.data.data;
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.dialogInformationCheck {
  .container {
    max-height: 60vh;
    min-height: 130px;
    overflow: auto;
  }
}
</style>
