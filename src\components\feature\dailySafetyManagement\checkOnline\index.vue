<template>
  <div class="checkOnline">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span
              class="icon-box"
              v-if="
                $store.state.login.user.user_type == 'gov' ||
                $store.state.login.user.user_type == 'park'
              "
            >
              <a-icon type="home" theme="filled" class="icon" />
              排查整治分析（抽查）
            </span>
            <span
              class="icon-box"
              v-if="$store.state.login.user.user_type == 'ent'"
            >
              <a-icon type="home" theme="filled" class="icon" /> 在线抽查反馈
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <el-tabs
      v-model="activeName"
      @tab-click="handleClick"
      v-if="
        $store.state.login.user.user_type == 'gov' ||
        $store.state.login.user.user_type == 'park'
      "
    >
      <el-tab-pane label="在线抽查" name="在线抽查">
        <SpotCheckOnline ref="spotCheckOnline"></SpotCheckOnline>
      </el-tab-pane>
      <el-tab-pane label="历史抽查记录" name="历史抽查记录">
        <HistorySpotCheck ref="historySpotCheck"></HistorySpotCheck>
      </el-tab-pane>
      <el-tab-pane
        label="部抽查记录"
        name="部抽查记录"
        v-if="$store.state.login.user.distRole == '0'"
      >
        <DepartmentSpotCheck ref="departmentSpotCheck"></DepartmentSpotCheck>
      </el-tab-pane>
    </el-tabs>
    <HistorySpotCheckEnt
      ref="HistorySpotCheckEnt"
      v-if="user.user_type === 'ent'"
    ></HistorySpotCheckEnt>
  </div>
</template>

<script>
// const SpotCheckOnline = ()=> import ("./spotCheckOnline");
import { mapState } from "vuex";

// const HistorySpotCheck = () => import("./historySpotCheck");
// const HistorySpotCheckEnt = () => import("./historySpotCheckEnt");
import SpotCheckOnline from "./SpotCheckOnline";
import HistorySpotCheck from "./HistorySpotCheck";
import HistorySpotCheckEnt from "./HistorySpotCheckEnt";
import DepartmentSpotCheck from "./DepartmentSpotCheck";
// const { mapState: mapStateLogin } = createNamespacedHelpers('login')
export default {
  name: "checkOnline",
  components: {
    SpotCheckOnline,
    HistorySpotCheck,
    HistorySpotCheckEnt,
    DepartmentSpotCheck,
  },
  data() {
    return {
      activeName: "在线抽查",
    };
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
    }),
    // ...mapStateLogin({
    //   user: state => state.user
    // }),
  },
  mounted() {
    if (this.user.user_type !== "ent") {
      this.handleClick();
    } else {
      this.$nextTick(() => {
        console.log(this.$refs);
        this.$refs.HistorySpotCheckEnt.getQuerySpotCheckHis();
      });
    }
  },
  methods: {
    fatherMethod() {
      this.activeName = "历史抽查记录";
      this.$refs.historySpotCheck.onceShowSpotCheck();
    },
    handleClick(tab, event) {
      if (this.activeName === "在线抽查") {
        this.$nextTick(() => {
          this.$refs.spotCheckOnline.getData();
        });
      } else if (this.activeName === "历史抽查记录") {
        this.$nextTick(() => {
          this.$refs.historySpotCheck.getQuerySpotCheckHis();
          this.$refs.historySpotCheck.getPark();
          this.$refs.historySpotCheck.gettime();
        });
      } else if (this.activeName === "部抽查记录") {
        this.$nextTick(() => {
          this.$refs.departmentSpotCheck.getQuerySpotCheckHis();
          this.$refs.departmentSpotCheck.getPark();
          this.$refs.departmentSpotCheck.gettime();
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.checkOnline {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }
}
</style>
