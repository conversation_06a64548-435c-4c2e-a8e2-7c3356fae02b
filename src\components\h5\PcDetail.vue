<template>
  <div>
    <a-layout class="review pc">
      <a-layout-header>
        <div class="header">
          <div class="mainPoint">
            <i class="accAnalyse"></i>
            <div>武汉市危险化学品安全生产风险监测预警系统</div>
          </div>
        </div>
      </a-layout-header>

      <div v-if="isShow">
        <a-layout class="review-main">
          <div class="review-main-title">危化企业“三同时”复查</div>
          <div class="content">
            <div class="content-list">
              <p class="tipInfo">
                尊敬的{{
                  approvalReviewData.expertName
                }}专家您好，欢迎使用湖北省企业安全生产“同时”专家审查意见在线复查功能
              </p>
              <div class="content-list-item">
                <div class="title">
                  <i></i>
                  <span>基本信息</span>
                </div>
                <div class="detail">
                  <table>
                    <tr>
                      <th>企业名称</th>
                      <td colspan="3">{{ infoDTO.enterpName }}</td>
                      <th>项目名称</th>
                      <td colspan="2">{{ infoDTO.projectName }}</td>
                    </tr>
                    <tr>
                      <th>报告类型</th>
                      <td>{{ infoDTO.reportTypeName }}</td>
                      <th>项目类型</th>
                      <td>{{ infoDTO.projectTypeName }}</td>
                      <th>审批级别</th>
                      <td>
                        {{ infoDTO.approvalLevel | filterapprovalStatusName }}
                      </td>
                    </tr>
                  </table>
                  <ul class="list">
                    <li>
                      <span class="label">电子报告：</span>
                      <div class="file-con">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.reportAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                        <!-- <AttachmentUpload
                        :attachmentlist="infoDTO.reportAttachList"
                        :limit="5"
                        type="pdf"
                        v-bind="{}"
                        :editabled="true"
                      ></AttachmentUpload> -->
                      </div>
                    </li>
                    <li>
                      <span class="label">专家意见表：</span>
                      <div class="file-con">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.commentAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                        <!-- <AttachmentUpload
                        :attachmentlist="infoDTO.commentAttachList"
                        :limit="5"
                        type="pdf"
                        v-bind="{}"
                        :editabled="true"
                      ></AttachmentUpload> -->
                      </div>
                    </li>
                    <li>
                      <span class="label">修订对照表：</span>
                      <div class="file-con">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.reviseAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                        <!-- <AttachmentUpload
                        :attachmentlist="infoDTO.reviseAttachList"
                        :limit="5"
                        type="pdf"
                        v-bind="{}"
                        :editabled="true"
                      ></AttachmentUpload> -->
                      </div>
                    </li>
                    <li>
                      <span class="label">附件：</span>
                      <div v-if="!infoDTO.otherAttachList">暂无数据</div>
                      <div class="file-con" v-else>
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.otherAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                      </div>
                    </li>
                    <li>
                      <span class="label">备注：</span>
                      <div class="file-con">
                        {{ infoDTO.remark || "无" }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="content-list-item" v-if="feedbackList.length > 0">
                <div class="title">
                  <i></i>
                  <span>专家组成员复查意见</span>
                </div>
                <div class="detail">
                  <el-timeline>
                    <el-timeline-item
                      v-for="(item, index) in feedbackList"
                      :key="index"
                      color="#3470ff"
                      timestamp=""
                    >
                      <div class="feedCon">
                        <div class="info">
                          <div class="info-left">
                            <span class="name">{{ item.expertName }}</span>
                            <span class="line"></span>
                            <span class="tel">{{ item.expertTelephone }}</span>
                            <span
                              :class="[
                                'status',
                                `status_${item.expertApproval}`,
                              ]"
                            >
                              <i
                                class="el-icon-check"
                                v-if="item.expertApproval === '0'"
                              ></i>
                              <!-- {{ item.headStatus | filterStatus }} -->
                              {{ item.expertApprovalName }}
                            </span>
                          </div>
                          <div class="info-date">{{ item.updateTime }}</div>
                        </div>

                        <div class="exprertWenj">
                          <div class="exprertWenjBox">
                            <span> 附件： </span>
                            <div class="experComment">
                              <AttachmentUpload
                                :attachmentlist="item.commentAttachments"
                                :limit="5"
                                type="pdf"
                                v-bind="{}"
                                :editabled="true"
                              ></AttachmentUpload>
                            </div>
                          </div>
                        </div>

                        <div class="exprertWenj commentStly">
                          <div class="exprertWenjBox">
                            <span>意见说明：</span>
                            <div class="experComment">
                              {{ item.expertComment || "无意见" }}
                            </div>
                          </div>
                        </div>

                        <!-- <div class="mark">
                          {{ item.expertComment || "无意见" }}
                        </div> -->
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
              <div class="content-list-item">
                <div class="title">
                  <i></i>
                  <span>专家复查意见</span>
                </div>
                <div class="detail">
                  <ul class="list">
                    <li>
                      <span class="label">专家姓名：</span>
                      <span class="val">
                        {{ approvalReviewData.expertName }}</span
                      >
                    </li>
                    <li>
                      <span class="label">结论：</span>
                      <div class="val">
                        <el-radio-group
                          v-model="reviewResult"
                          @change="handleCheck"
                        >
                          <el-radio label="0">通过</el-radio>
                          <el-radio label="1">不通过</el-radio>
                        </el-radio-group>
                      </div>
                    </li>

                    <li>
                      <span class="label">附件：</span>
                      <div class="val">
                        <div class="uploadBox">
                          <AttachmentUpload
                            :attachmentlist="commentAttachments"
                            :limit="5"
                            type="office"
                            v-bind="{}"
                          ></AttachmentUpload>
                        </div>
                      </div>
                    </li>

                    <li>
                      <!-- <el-form
                    class="dialogForm"
                    :model="dialogForm"
                    :ref="dialogForm"
                  > -->
                      <span class="label">意见说明：</span>
                      <div class="file-con" style="flex: 1">
                        <el-input
                          type="textarea"
                          :rows="3"
                          :rules="[
                            {
                              required: true,
                              message: '请输入意见说明',
                            },
                          ]"
                          placeholder="请输入意见说明"
                          prop="checkMark"
                          v-model.trim="checkMark"
                          maxlength="500"
                          show-word-limit
                        >
                        </el-input>
                      </div>
                      <!-- </el-form> -->
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="btnStyle">
              <el-button size="mini" @click="clickDraft">草稿</el-button>

              <el-button size="mini" type="primary" @click="handleSign"
                >签名提交</el-button
              >
            </div>

            <!-- <div
              class="content-btn"
              @click="handleSign"
              :class="[isclick ? 'isclick' : '']"
            >
              <div>
                <i class="sign"></i>
                <span>签名提交</span>
              </div>
            </div> -->
          </div>
        </a-layout>
        <!-- :before-close="handleClose" -->
        <el-dialog
          custom-class="sign-dialog"
          :visible.sync="dialogVisible"
          width="30%"
          :close-on-click-modal="false"
        >
          <span slot="title" class="sign-header">
            <span>手写签名</span>
            <span class="tips">请在下方灰色区域手写签名</span>
          </span>
          <Sign @closeSign="handleClose"></Sign>
        </el-dialog>
      </div>
      <div v-else class="tipMore">
        <div>{{ isShowTit || "复核已完成，请勿重复访问！" }}</div>
      </div>
    </a-layout>

    <el-dialog
      title="预览"
      v-if="dialogVisible3"
      :visible.sync="dialogVisible3"
      width="80%"
      top="0"
      :close-on-click-modal="false"
    >
      <div class="dialog dialogInfo" style="height: calc(100vh - 100px)">
        <!-- <pdfSignatureView :linkattachId='linkattachId'></pdfSignatureView> -->
        <!-- <iframe ref="pdf" :src="pdfUrl" width="100%" frameborder='no' border='0' marginwidth='0' marginheight='0'></iframe> -->
        <div v-loading="loading">
          <iframe
            id="pdfRef"
            ref="pdf"
            :src="pdfUrl"
            width="100%"
            frameborder="no"
            border="0"
            marginwidth="0"
            marginheight="0"
            style="height: calc(100vh - 100px)"
          ></iframe>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-if="previewShow"
      title="预览"
      top="0"
      :visible.sync="previewShow"
      append-to-body
      width="80%"
      :close-on-click-modal="false"
    >
      <!-- word 显示-->
      <div
        style="height: calc(100vh - 100px); overflow: auto"
        class="wordReview"
      >
        <div ref="word"></div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="previewShow = false"
          >关 闭</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Sign from "./sign.vue";
import { shortCodeRestore } from "@/api/mailList";
import axios from "axios";
import {
  projectApprovalReview,
  projectApprovalDecide,
  projectEvaluateDraft, //草稿
} from "@/api/companyParticularJob";
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
import pdfSignatureView from "@/components/feature/dailySafetyManagement/threeDataReview/pdfSignatureView.vue";
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js";
const docx = require("docx-preview");
window.JSZip = require("jszip");

export default {
  name: "PcDetail",
  components: {
    Sign,
    AttachmentUpload,
    pdfSignatureView,
  },
  data() {
    return {
      previewShow: false,
      loading: true,
      pdfUrl: "",
      linkattachId: "",
      dialogVisible3: false,
      isShow: true,
      isShowTit: "",
      isFole: false, // 是否折叠
      approvalId: "",
      expertTelephone: "",
      approvalReviewData: "",
      infoDTO: {}, //基本信息
      feedbackList: [
        {
          name: "王芳",
          tel: "15678768098",
          status: "1",
          time: "09-26/08:30",
          mark: "无审核意见",
        },
        {
          name: "王芳",
          tel: "15678768098",
          status: "0",
          time: "09-26/08:30",
          mark: "三同时修订对照表不符合相关条例规定，需按照规定重新填写上传。",
        },
      ],
      reviewResult: "", // 是否复查通过
      checkMark: "", // 审核说明
      commentAttachments: [],
      dialogVisible: false, // 签名弹窗
    };
  },
  filters: {
    // filterStatus(val) {
    //   let str = "";
    //   switch (val) {
    //     case "1":
    //       str = "不通过";
    //       break;
    //     case "0":
    //       str = "通过";
    //       break;
    //     default:
    //       break;
    //   }
    //   return str;
    // },
    //filterapprovalStatusName
    filterapprovalStatusName(val) {
      let str = "";
      switch (val) {
        case "0":
          str = "省级";
          break;
        case "1":
          str = "市级";
          break;
        case "2":
          str = "区级";
          break;
        default:
          break;
      }
      return str;
    },
  },
  // created(){
  //   document.oncontextmenu=function(evt){  evt.preventDefault();}
  //   document.onselectstart=function(evt){ evt.preventDefault();}
  // },
  computed: {},
  mounted() {
    var shortCode = this.getQueryVariable("code");
    shortCodeRestore({ shortCode: shortCode }).then((res) => {
      if (res.status == 200) {
        var dataUrl2 = res.data.data.split("?")[1];
        var vars = dataUrl2.split("&");
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split("=");
          if (pair[0] == "approvalId") {
            this.approvalId = pair[1];
          } else {
            this.expertTelephone = pair[1];
          }
        }

        projectApprovalReview({
          approvalId: this.approvalId,
          expertTelephone: this.expertTelephone,
        }).then((res) => {
          if (res.data.status == 200) {
            this.approvalReviewData = res.data.data;
            this.reviewResult = res.data.data.expertApproval || "";
            this.commentAttachments = res.data.data.commentAttachments || "";
            this.checkMark = res.data.data.expertComment;
            this.feedbackList = res.data.data.approvalDTOs || []; //专家组成员复查意见
            this.infoDTO = res.data.data.infoDTO || {};
          } else {
            this.isShowTit = res.data.msg;
            this.isShow = false;
          }
        });
      }
    });
  },
  methods: {
    //草稿
    clickDraft() {
      var param = {
        approvalId: this.approvalId,
        expertApproval: this.reviewResult,
        expertComment: this.checkMark,
        attachmentOutDTOs: [],
        commentAttachments: this.commentAttachments,
      };
      projectEvaluateDraft(param).then((res) => {
        if (res.data.status === 200) {
          this.$message.success(res.data.msg); //?？？？?????有问题
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    preview(val) {
      // 这里需要提起打开弹窗，因为有时很找不到word的ref 会报错
      this.previewShow = true;
      axios({
        method: "post",
        url: "/gemp-file/api/attachment/download/v1",
        //文件流：gapi/gemp-file/api/attachment/download/v1    res:/gemp-file/api/attachment/findByFileId/v1
        // url:'http://ashuai.work:10000/getDoc',
        data: { fileId: val.attachId },
        responseType: "blob",
      }).then((res) => {
        // 对后端返回二进制流做处理
        const blob = new Blob([res.data]);
        docx.renderAsync(blob, this.$refs.word);
        // docx.renderAsync(res, this.$refs.word); // 渲染到页面
      });
    },

    async clickPdf(val) {
      if (/(.*)\.(docx)$/i.test(val.name)) {
        this.previewShow = true;
        axios({
          method: "post",
          url: "/gemp-file/api/attachment/download/v1",
          //文件流：gapi/gemp-file/api/attachment/download/v1    res:/gemp-file/api/attachment/findByFileId/v1
          // url:'http://ashuai.work:10000/getDoc',
          data: { fileId: val.attachId },
          responseType: "blob",
        }).then((res) => {
          // 对后端返回二进制流做处理
          const blob = new Blob([res.data]);
          docx.renderAsync(blob, this.$refs.word);
          // docx.renderAsync(res, this.$refs.word); // 渲染到页面
        });
      } else if (/(.*)\.(pdf)$/i.test(val.name)) {
        this.dialogVisible3 = true;
        // document.getElementById('pdfRef').contentDocument.oncontextmenu=new Function("event.returnValue=false");
        // document.getElementById('pdfRef').contentDocument.onselectstart=new Function("event.returnValue=false");
        // document.getElementById('pdfRef').contentDocument.οncοntextmenu="return false"
        // document.getElementById('pdfRef').contentDocument.onselectstart="return false"
        //this.linkattachId = val.attachId;  //用签名插件
        var axiosRes = await axios({
          //用ifram
          method: "post",
          url: "/gemp-file/api/attachment/findByFileId/v1",
          data: { attachId: val.attachId },
        });
        this.pdfUrl =
          axiosRes.data.data.fileOuterPath + axiosRes.data.data.filePath;
        this.loading = false;
      } else if (
        /(.*)\.(zip)$/i.test(val.name) ||
        /(.*)\.(rar)$/i.test(val.name)
      ) {
        let par = {
          fileId: val.attachId,
        };
        Attachmentdownload(par).then((res) => {
          downloadFuc(res);
        });
      } else {
        this.$message.error("读取文件格式错误");
      }
    },
    closeDialog() {
      this.dialogVisible3 = false;
    },
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month =
        new Date(timeStamp).getMonth() + 1 < 10
          ? "0" + (new Date(timeStamp).getMonth() + 1)
          : new Date(timeStamp).getMonth() + 1;
      let date =
        new Date(timeStamp).getDate() < 10
          ? "0" + new Date(timeStamp).getDate()
          : new Date(timeStamp).getDate();
      let hh =
        new Date(timeStamp).getHours() < 10
          ? "0" + new Date(timeStamp).getHours()
          : new Date(timeStamp).getHours();
      let mm =
        new Date(timeStamp).getMinutes() < 10
          ? "0" + new Date(timeStamp).getMinutes()
          : new Date(timeStamp).getMinutes();
      let ss =
        new Date(timeStamp).getSeconds() < 10
          ? "0" + new Date(timeStamp).getSeconds()
          : new Date(timeStamp).getSeconds();
      let week = new Date(timeStamp).getDay();
      let weeks = ["日", "一", "二", "三", "四", "五", "六"];
      let getWeek = "星期" + weeks[week];
      return year + month + date;
    },
    //url处理
    getQueryVariable(variable) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    },
    handleFole() {
      this.isFole = !Boolean(this.isFole);
    },
    handleCheck(val) {
      this.reviewResult = val;
    },
    handleSign() {
      if (this.reviewResult == "") {
        this.$message.info("请选择结论通过/不通过");
        return false;
      }
      if (this.checkMark == "") {
        this.$message.info("请填写意见说明");
        return false;
      }
      this.dialogVisible = true;
    },
    //签名提交
    handleClose(imgUrl) {
      this.dialogVisible = false;
      //base64图片附件上传 attachment/uploadBase64/v1
      var paramUploadBase64 = {
        file: imgUrl,
        module: "chemical",
        name: this.timeFormate(new Date()) + this.approvalReviewData.expertName, //当前日期+专家姓名 20220707AB
        path: "/uploadFile/chemical/projectApproval/" + this.infoDTO.infoId,
      };
      axios({
        method: "post",
        url: "/gemp-file/api/attachment/uploadBase64/v1",
        data: paramUploadBase64,
        // responseType: "arraybuffer",
      }).then((res) => {
        if (res && res.status == 200) {
          var attachmentOutDTOs = [res.data.data];
          var param = {
            approvalId: this.approvalId,
            expertApproval: this.reviewResult,
            expertComment: this.checkMark,
            attachmentOutDTOs: attachmentOutDTOs,
            commentAttachments: this.commentAttachments,
          };
          projectApprovalDecide(param).then((res) => {
            if (res.data.status === 200) {
              location.reload();
              this.$message.success(res.data.msg); //?？？？?????有问题
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .experComment div {
  text-align: left !important;
  align-items: self-start !important;
}
/deep/ .slot_img .slot_name,
/deep/ .slot_file .slot_name {
  text-align: left !important;
}
/deep/ .slot_img,
/deep/ .slot_file {
  align-items: flex-start !important;
}
.review {
  display: flex;
  flex-direction: column;
  width: 100%;
  font-size: 10px;
  .commentStly {
    padding: 10px 0 0 0;
  }
  .exprertWenj {
    .exprertWenjBox {
      display: flex;
      align-items: center;
      > span {
        width: 100px;
      }
      .experComment {
        width: calc(100% - 100px);
      }
    }
  }
  .tipInfo {
    margin: 10px 0 0 0;
    font-size: 17px;
    text-align: center;
  }
  /deep/.ant-layout-header {
    padding: 0;
  }
  .header {
    background: url(/static/img/assets/img/top-bg.png) no-repeat center center;
    background-size: 100% 100%;
    width: 100%;
    // height: 68px;
    // line-height: 68px;
    display: flex;
    justify-content: space-between;
    .mainPoint {
      font-size: 22px;
      color: white;
      margin-left: 20px;
      width: 40vw;
      height: 68px;
      .accAnalyse {
        // display: inline-block;
        background: url(/static/img/assets/img/nationalEmblem.png) no-repeat
          center center;
        background-size: 100% 100%;
        width: 41px;
        height: 41px;
        position: relative;
        top: 10px;
        float: left;
        margin-right: 10px;
      }
    }
  }
  &-main {
    flex: 1;
    &-title {
      width: 100%;
      color: #252525;
    }
    .content {
      &-list {
        &-item {
          .detail {
            table {
              width: 100%;
              font-size: 14px;
              color: #252525;
              border-width: 1px;
              border-color: #c9c9c9;
              border-collapse: collapse;
              th {
                background: #f7f8fa;
                border-width: 1px;
                padding: 8px;
                border-style: solid;
                border-color: #c9c9c9;
              }
              td {
                border-width: 1px;
                padding: 8px;
                border-style: solid;
                border-color: #c9c9c9;
              }
            }
          }
        }
      }
    }
  }
  &.pc {
    font-size: 14px;
    .review-main {
      background: url(/static/img/assets/img/review/bg.png) no-repeat center top,
        #fafafa;
      background-size: contain;
      &-title {
        padding: 20px 0;
        text-align: center;
        background: unset;
        font-size: 20px;
        font-weight: 500;
      }
    }
    .content {
      margin: 0 15% 20px 15%;
      padding: 10px;
      background: #fff;
      &-list {
        &-item {
          margin-bottom: 0.5em;
          background: #fff;
          .title {
            position: relative;
            height: 50px;
            line-height: 50px;
            padding-left: 10px;
            font-size: 16px;
            i {
              position: absolute;
              top: 50%;
              transform: translateY(-50%);
              display: inline-block;
              width: 3px;
              height: 15px;
              background: #3470ff;
            }
            span {
              padding-left: 10px;
            }
          }
          .detail {
            padding: 0 10px;
            ul.list {
              padding: 0 10px;
              li {
                display: flex;
                align-items: center;
                padding: 8px 0;
                .label {
                  position: relative;
                  margin-right: 10px;
                  padding-left: 10px;
                  color: #707070;
                  width: 100px;
                  &::before {
                    content: "";
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    width: 5px;
                    height: 5px;
                    background: #3470ff;
                    border-radius: 2px;
                  }
                }
                .file-con {
                  display: flex;
                  align-items: center;
                  margin-right: 10px;
                  width: calc(100% - 100px);
                  .file-name {
                    padding: 0 20px 0 0;
                    i {
                      width: 12px;
                      height: 12px;
                      background: url(/static/img/assets/img/review/ico_link.png)
                        no-repeat center center / contain;
                    }
                    span {
                      color: #3470ff;
                      text-decoration: underline;
                      cursor: pointer;
                    }
                  }
                  .file-size {
                    color: #707070;
                  }
                  &:last-child {
                    border-bottom: none;
                  }
                }
              }
              /deep/.el-timeline {
                margin-top: 2em;
                .el-timeline-item {
                  padding-bottom: 0.5em;
                  .el-timeline-item__wrapper {
                    top: -1em;
                  }
                }
              }
              /deep/.el-radio-group {
                margin-bottom: 0;
                .el-radio__label {
                  font-size: 1em;
                  color: #252525;
                }
              }
              /deep/.el-textarea {
                textarea {
                  resize: none;
                }
              }
            }
          }
        }
      }
      &-btn {
        position: relative;
        height: 45px;
        line-height: 45px;
        font-weight: 500;
        cursor: pointer;
        div {
          background: #3470ff;
          color: #fff;
          width: 100px;
          border-radius: 4px;
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          text-align: center;
        }
      }
    }

    li {
      list-style: none;
    }
    i {
      display: inline-block;
    }
  }
  .feedCon {
    padding: 0.5em;
    border-radius: 0.4em;
    background: #f8f9fb;
    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 0.5em;
      &-left {
        display: flex;
        align-items: center;
        .line {
          width: 0.05em;
          height: 1.2em;
          background: #707070;
          display: inline-block;
          margin: 0 0.5em;
        }
        .status {
          margin-left: 1em;
          margin-left: 1em;
          padding: 0 0.5em;
          border-radius: 0.3em;
          background: #fef7e7;
          color: #faad14;
          i {
            margin-right: 0.1em;
          }
          &.status_0 {
            background: #d7eece;
            color: #52c41a;
          }
          &.status_1 {
            background: #edcecf;
            color: #bf241f;
          }
        }
      }
      &-date {
        color: #707070;
      }
    }
    .mark {
      color: #707070;
    }
  }
}
.tipMore {
  text-align: center;
  height: calc(100vh - 68px);
  line-height: calc(100vh - 68px);
  font-size: 24px;
}
.content-btn.isclick {
  pointer-events: none;
  > div {
    background: #ccc;
  }
}
.wordReview {
  img {
    width: 100% !important;
  }
}
.btnStyle {
  display: flex;
  justify-content: center;
}
</style>
