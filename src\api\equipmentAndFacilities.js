import axios from "axios";
import qs from "qs";

//仓库列表
export const getWarehouseListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitywarehouse/findAll/v1",
    data: data,
  });
};
//新增仓库
export const addWarehouseListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitywarehouse/add/v1",
    data: data,
  });
};
//修改仓库
export const editWarehouseListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitywarehouse/update/v1",
    data: data,
  });
};
//删除仓库
export const deleteWarehouseListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitywarehouse/delete/v1",
    data: data,
  });
};
//仓库详情
export const datailWarehouseListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitywarehouse/id/v1",
    data: data,
  });
};
//仓库导出
export const exportWarehouseListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitywarehouse/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
//化学品列表
export const getDangerIdIsNotNullListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    // url: "/gemp-chemical/api/gemp/knowledge/findAll/msds/v1",
    url: "/gemp-chemical/api/gemp/knowledge/findPage/msds/v1",
    data: data,
  });
};
//工艺列表
export const getKnowledgeListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/knowledge/findAll/process/v1",
    data: data,
  });
};
//企业重大危险源
export const getDangerIdIsNotNullListDatas = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/knowledge/findAll/danger/v1",
    data: data,
  });
};
//化学品状态
export const getDangerIdIsNotNullTypeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-dic/api/dic/code/v1",
    data: data,
  });
};

// 储罐列表
export const getStorageTankListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/listpage/v1",
    data: data,
  });
};
// 储罐列表（全部）
export const getStorageTankListAllData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/list/v1",
    data: data,
  });
};
//新增储罐
export const addStorageTankListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/add/v1",
    data: data,
  });
};
//修改储罐
export const editStorageTankListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/update/v1",
    data: data,
  });
};
//删除储罐
export const deleteStorageTankListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/delete/v1",
    data: data,
  });
};
//储罐详情
export const datailStorageTankListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/id/v1?id=" + data.id,
    data: data,
  });
};
//储罐导出
export const exportStorageTankListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/companyfacilitytank/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
//新增检维修
export const maintenanceSave = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/maintenance/save/v1",
    data: data,
  });
};
//查询检维修
export const getmaintenanceSave = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/maintenance/findOne/v1",
    data: data,
  });
};
export const getmaintenanceList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/maintenance/list/v1",
    data: data,
  });
};
//装置列表
export const getDeviceListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/enterprise/production/list/v1",
    data: data,
  });
};
//新增装置
export const addDeviceListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/enterprise/production/save/v1",
    data: data,
  });
};
//修改装置
export const editDeviceListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/enterprise/production/update/v1",
    data: data,
  });
};
//装置详情
export const datailDeviceListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url:
      "/gemp-chemical/api/gemp/enterprise/production/find/v1?productionUnitCode=" +
      data.productionUnitCode,
    data: data,
  });
};
//删除装置
export const deleteDeviceListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/enterprise/production/delete/v1",
    data: data,
  });
};
// 装置列表（全部）
export const getDeviceListAllData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/enterprise/production/list/all/v1",
    data: data,
  });
};
//装置导出
export const exportDeviceListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/enterprise/production/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

//泄漏点列表
export const getLeakagePointListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/list/v1",
    data: data,
  });
};
//新增泄漏点
export const addLeakagePointListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/save/v1",
    data: data,
  });
};
//修改泄漏点
export const editLeakagePointListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/update/v1",
    data: data,
  });
};
//泄漏点详情
export const datailLeakagePointListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url:
      "/gemp-chemical/api/gemp/leakage/findOne/v1?leakageId=" + data.leakageId,
    data: data,
  });
};
//删除泄漏点
export const deleteLeakagePointListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/delete/v1",
    data: data,
  });
};
// 泄漏点列表（全部）
export const getLeakagePointListAllData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/list/all/v1",
    data: data,
  });
};
// 传感器类型
export const getLeakageDeviceTypeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/deviceType/v1",
    data: data,
  });
};
//泄漏点导出
export const exportLeakagePointListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/leakage/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
