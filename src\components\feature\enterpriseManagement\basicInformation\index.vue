<template>
  <div class="basicInformation" v-loading="loading">
    <div class="div1">
      <div class="title">
        <span>基本信息</span>
        <div class="btn">
          <!-- <el-button
            size="small"
            type="primary"
            icon="el-icon-edit"
            style="float: left"
            @click="RegistrationInformationBool"
            v-if="level != null ? true : false"
            >登记信息</el-button
          >
          <el-button
            size="small"
            type="primary"
            icon="el-icon-tickets"
            style="float: right"
            @click="FillInformationBool"
            >完整信息</el-button
          > -->
          <el-button
            @click="openArchives"
            type="primary"
            icon="el-icon-office-building"
            size="small"
            >一企一档</el-button
          >
          <!-- <el-button size="mini" type="primary">编辑</el-button> -->
        </div>
      </div>
      <div class="table">
        <ul class="container">
          <li>
            <div class="l">企业名称</div>
            <div class="r">{{ enterprise.enterpName }}</div>
          </li>
          <li class="">
            <div class="l">企业类型大类</div>
            <!-- <div class="r"
                 v-if="enterprise.enterpriseType == '01'">生产</div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '02'">
              经营
            </div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '03'">
              使用
            </div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '04'">
              第一类非药品易制毒
            </div> -->

            <div class="r">{{ enterprise.enterpriseTypeName }}</div>
          </li>
          <li class="">
            <div class="l">企业类型小类</div>
            <div class="r">{{ managementType(enterprise.managementType) }}</div>
            <!-- <div class="r">{{enterprise.managementType }}</div> -->
          </li>
          <li>
            <div class="l">企业编码</div>
            <div class="r">{{ enterprise.enterpId }}</div>
          </li>
          <li>
            <div class="l">统一社会信用代码</div>
            <div class="r">{{ enterprise.entcreditCode }}</div>
          </li>
          <li v-if="enterprise.enterpriseType == '01'">
            <div class="l">安全生产许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li>
          <li v-if="enterprise.enterpriseType == '02'">
            <div class="l">危险化学品经营许可证</div>
            <div class="r">{{ enterprise.msdslicensenum }}</div>
          </li>
          <li v-if="enterprise.enterpriseType == '03'">
            <div class="l">危险化学品安全使用许可证</div>
            <div class="r">{{ enterprise.msdsseclicensenum }}</div>
          </li>
          <!-- <li v-if="enterprise.enterpriseType == '04'">
            <div class="l">危险化学品安全使用许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li> -->
          <li v-if="enterprise.enterpriseType == null">
            <div class="l">许可证</div>
            <div class="r"></div>
          </li>
          <li>
            <div class="l">许可证有效开始日期</div>
            <div class="r">{{ enterprise.secLicenseNumStartDate }}</div>
          </li>
          <li>
            <div class="l">许可证有效结束日期</div>
            <div class="r">{{ enterprise.secLicenseNumEndDate }}</div>
          </li>
          <li>
            <div class="l">许可范围</div>
            <div class="r">
              <el-popover
                placement="left-start"
                width="250"
                trigger="hover"
                :content="enterprise.scopeSafety"
              >
                <div
                  slot="reference"
                  style="
                    width: 90%;
                    margin: 0 auto;
                    cursor: default;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  "
                >
                  {{ enterprise.scopeSafety }}
                </div>
              </el-popover>
            </div>
          </li>
          <li>
            <div class="l">所属行业门类</div>
            <div class="r">{{ enterprise.industryCategory }}</div>
          </li>
          <li>
            <div class="l">所属行业大类</div>
            <div class="r">{{ enterprise.industryClass }}</div>
          </li>
          <li>
            <div class="l">经济类型代码（大类）</div>
            <div class="r">{{ enterprise.economyTypeCode }}</div>
          </li>
          <li>
            <div class="l">经济类型代码（小类）</div>
            <div class="r">{{ enterprise.economyTypeCode2 }}</div>
          </li>
          <li class="">
            <div class="l">行政区划</div>
            <div class="r">{{ enterprise.districtName }}</div>
          </li>
          <li>
            <div class="l">工商注册地址</div>
            <div class="r">{{ enterprise.businessAddress }}</div>
          </li>
          <li>
            <div class="l">生产场所地址</div>
            <div class="r">{{ enterprise.bornAddress }}</div>
          </li>
          <li>
            <div class="l">职工总人数</div>
            <div class="r">{{ enterprise.employmentNum }}</div>
          </li>
          <li>
            <div class="l">占地面积(单位：平方米)</div>
            <div class="r">{{ enterprise.area }}</div>
          </li>
          <li>
            <div class="l">成立日期</div>
            <div class="r">{{ enterprise.regDate }}</div>
          </li>
          <li>
            <div class="l">是否在化工园区</div>
            <div class="r" v-if="enterprise.inPark != null">
              {{ enterprise.inPark == 1 ? "是" : "否" }}
            </div>
            <div class="r" v-else></div>
          </li>
          <li>
            <div class="l">所属化工园区区名称</div>
            <div class="r">{{ enterprise.parkName }}</div>
          </li>
          <li>
            <div class="l">企业经营状态</div>
            <div class="r" v-if="enterprise.businessType != null">
              {{ enterprise.businessType == 0 ? "正常" : "长期停产" }}
            </div>
            <div class="r" v-else></div>
          </li>
          <li>
            <div class="l">企业负责人</div>
            <div class="r">{{ enterprise.respper }}</div>
          </li>
          <li>
            <div class="l">企业负责人手机</div>
            <div class="r">{{ enterprise.respTel }}</div>
          </li>
          <li class="bottom">
            <div class="l">安全负责人</div>
            <div class="r">{{ enterprise.principal }}</div>
          </li>
          <li class="bottom">
            <div class="l">安全负责人手机</div>
            <div class="r">{{ enterprise.principalPhone }}</div>
          </li>
          <li class="bottom">
            <div class="l">安全值班电话</div>
            <div class="r">{{ enterprise.safeTel }}</div>
          </li>
        </ul>
      </div>
    </div>
    <div class="div2">
      <div class="title">危险化学品</div>
      <div class="table" v-if="hazarchem.length > 0">
        <ul class="container header">
          <li>序号</li>
          <li>危化品中文名</li>
          <li>别名</li>
          <li>CAS号</li>
          <li>化学品属性</li>
          <li>* 设计储存量(吨/标方)</li>
          <li>年产量/消耗量(吨/标方)</li>
          <li class="liLine">操作</li>
        </ul>
        <ul class="container" v-for="(item, index) in hazarchem" :key="index">
          <li>{{ index + 1 }}</li>
          <li>{{ item.hazarchemName }}</li>
          <li>{{ item.chemicalAlias }}</li>
          <li>{{ item.casNo }}</li>

          <!-- <li v-if="item.hazarchemProperty == 1">产品</li>
          <li v-else-if="item.hazarchemProperty == 2">中间产品</li>
          <li v-else-if="item.hazarchemProperty == 4">原料</li>
          <li v-else></li> -->

          <li>{{ item.hazarchemProperty }}</li>

          <li>{{ item.storageNum }}</li>
          <li>{{ item.annualUsageMeal }}</li>
          <li class="liLine">
            <el-button type="text" @click="chemicalsBool(item.hazarchemId)"
              >详情</el-button
            >
          </li>
        </ul>
      </div>
      <div class="table" v-else>
        <ul class="container header">
          <li>序号</li>
          <li>危化品中文名</li>
          <li>别名</li>
          <li>CAS号</li>
          <li>化学品属性</li>
          <li>* 设计储存量(吨/标方)</li>
          <li>年产量/消耗量(吨/标方)</li>
          <li class="liLine">操作</li>
        </ul>
        <ul class="container">
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li class="liLine back"></li>
        </ul>
      </div>
    </div>
    <div class="div3" v-if="level">
      <div class="title">重点监管危险化工工艺</div>
      <div class="table" v-if="regprocess.length > 0">
        <ul class="container header">
          <li>序号</li>
          <li>工艺名称</li>
          <li>装置名称</li>
          <li>工艺危险性特征</li>
          <li>工艺装置地址</li>
          <li>投用日期</li>
          <li class="liLine">操作</li>
        </ul>
        <ul class="container" v-for="(item, index) in regprocess" :key="index">
          <li>{{ index + 1 }}</li>
          <li>{{ item.processName }}</li>
          <li>{{ item.deviceName }}</li>
          <!-- <li v-if="item.isTypicalProcess != null">
            {{ item.isTypicalProcess == 1 ? "是" : "否" }}
          </li>
          <li v-else></li> -->
          <li>{{ item.hazardFeature }}</li>
          <li>{{ item.processAddress }}</li>
          <li>{{ item.investmentTime }}</li>
          <li class="liLine">
            <el-button type="text" @click="processBool(item.relationId)"
              >详情</el-button
            >
          </li>
        </ul>
      </div>
      <div class="table" v-else>
        <ul class="container header">
          <li>序号</li>
          <li>工艺名称</li>
          <li>装置名称</li>
          <li>是否涉及典型工艺</li>
          <li>典型工艺名称</li>
          <li>投用日期</li>
          <li class="liLine">操作</li>
        </ul>
        <ul class="container">
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li class="liLine back"></li>
        </ul>
      </div>
    </div>
    <div class="div4" v-if="level">
      <div class="title">重大危险源</div>
      <div class="table" v-if="danger.length > 0">
        <ul class="container header">
          <li>序号</li>
          <li>重大危险源名称</li>
          <li>R值</li>
          <li>危险源等级</li>
          <li>重大危险源分类</li>
          <li>投用日期</li>
          <li class="liLine">操作</li>
        </ul>
        <ul class="container" v-for="(item, index) in danger" :key="index">
          <li>{{ index + 1 }}</li>
          <li>{{ item.dangerName }}</li>
          <li>{{ item.rvalue }}</li>
          <li v-if="item.level == 1">一级重大危险源</li>
          <li v-else-if="item.level == 2">二级重大危险源</li>
          <li v-else-if="item.level == 3">三级重大危险源</li>
          <li v-else-if="item.level == 4">四级重大危险源</li>
          <li v-else></li>
          <!-- <li v-if="item.hazardClassify == 0">罐区</li>
          <li v-else-if="item.hazardClassify == 1">装置</li>
          <li v-else-if="item.hazardClassify == 2">库区</li>
          <li v-else></li> -->
          <li>{{ item.hazardClassify }}</li>
          <li>{{ item.useTime }}</li>
          <li class="liLine">
            <el-button type="text" @click="hazardsBool(item.dangerId)"
              >详情</el-button
            >
            <el-button type="text" @click="EquipmentListBool(item.dangerId)"
              >设备清单</el-button
            >
          </li>
        </ul>
      </div>
      <div class="table" v-else>
        <ul class="container header">
          <li>序号</li>
          <li>重大危险源名称</li>
          <li>R值</li>
          <li>危险源等级</li>
          <li>重大危险源分类</li>
          <li>投用日期</li>
          <li class="liLine">操作</li>
        </ul>
        <ul class="container">
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li></li>
          <li class="liLine back"></li>
        </ul>
      </div>
    </div>
    <el-dialog
      :title="enterprise.enterpName"
      :visible.sync="dialogVisible"
      top="5vh"
      width="1400px"
      :append-to-body="true"
      :close-on-click-modal="false"
    >
      <el-tabs
        v-model="activeName"
        @tab-click="handleClick"
        v-loading="tabLoading"
      >
        <el-tab-pane label="业务填报" name="FillInformation">
          <!-- 填报信息 -->
          <FillInformation
            ref="FillInformation"
            v-if="activeName === 'FillInformation'"
          ></FillInformation>
        </el-tab-pane>
        <el-tab-pane label="登记信息" name="RegistrationInformation">
          <!-- 登记信息 -->
          <RegistrationInformation
            ref="RegistrationInformation"
            v-if="activeName === 'RegistrationInformation'"
          ></RegistrationInformation>
        </el-tab-pane>
        <el-tab-pane label="两重点一重大" name="FocusOnAndMajor">
          <!-- 登记信息 -->
          <FocusOnAndMajor
            v-if="activeName === 'FocusOnAndMajor'"
            ref="FocusOnAndMajor"
          ></FocusOnAndMajor>
        </el-tab-pane>

        <el-tab-pane label="安全生产证照" name="SafetyProductionCertificate">
          <SafetyProductionCertificate
            v-if="activeName === 'SafetyProductionCertificate'"
            ref="SafetyProductionCertificate"
            :orgCode="orgCode"
            :orgName="enterprise.enterpName"
          ></SafetyProductionCertificate>
        </el-tab-pane>
        <el-tab-pane
          label="安全生产标准化"
          name="SafetyProductionStandardization"
        >
          <SafetyProductionStandardization
            v-if="activeName === 'SafetyProductionStandardization'"
            ref="SafetyProductionStandardization"
            :orgCode="orgCode"
            :orgName="enterprise.enterpName"
          ></SafetyProductionStandardization>
        </el-tab-pane>
        <el-tab-pane label="安全评价管理" name="SafetyEvaluation">
          <SafetyEvaluation
            v-if="activeName === 'SafetyEvaluation'"
            ref="SafetyEvaluation"
            :orgCode="orgCode"
            :orgName="enterprise.enterpName"
          ></SafetyEvaluation>
        </el-tab-pane>

        <!-- 废弃 -->
        <!-- <el-tab-pane label="安全“三同时”"
                     name="ThreeMeanwhile">
          <ThreeMeanwhile v-if="activeName === 'ThreeMeanwhile'"
                          :orgCode="orgCode"
                          :orgName="enterprise.enterpName"
                          ref="ThreeMeanwhile"></ThreeMeanwhile>
        </el-tab-pane> -->
      </el-tabs>
    </el-dialog>
    <!-- 危险工艺 -->
    <Process ref="Process"></Process>
    <!-- 危险化学品 -->
    <Chemicals ref="Chemicals"></Chemicals>
    <!-- 重大危险源详情 -->
    <Hazards ref="Hazards"></Hazards>
    <!-- 重大危险源列表 -->
    <EquipmentList ref="EquipmentList"></EquipmentList>
  </div>
</template>

<script>
import FillInformation from "./FillInformation";
import RegistrationInformation from "./RegistrationInformation";
import Process from "./process";
import Chemicals from "./chemicals";
import Hazards from "./hazards";
import EquipmentList from "./EquipmentList";
import { getInformationBasicInfo } from "@/api/entList";
import FocusOnAndMajor from "./FocusOnAndMajor";
import SafetyProductionCertificate from "./SafetyProductionCertificate";
import SafetyProductionStandardization from "./SafetyProductionStandardization";
import SafetyEvaluation from "./SafetyEvaluation";
// import ThreeMeanwhile from './ThreeMeanwhile'
import { getfindByEnterpId } from "../../../../api/user";
export default {
  //import引入的组件
  name: "basicInformation",
  components: {
    FillInformation,
    RegistrationInformation,
    Process,
    Chemicals,
    Hazards,
    EquipmentList,
    FocusOnAndMajor,
    SafetyProductionCertificate,
    SafetyProductionStandardization,
    SafetyEvaluation,
    // ThreeMeanwhile
  },
  props: ["level"],
  data() {
    return {
      enterprise: {},
      enterpriseId: {},
      danger: [],
      hazarchem: [],
      regprocess: [],
      loading: false,
      activeName: "FillInformation",
      dialogVisible: false,
      orgCode: "",
      tabLoading: false,
    };
  },
  //方法集合
  methods: {
    handleClick() {
      this.$nextTick(() => {
        if (this.activeName === "FocusOnAndMajor") {
          this.$refs[this.activeName].getData({
            enterpriseId: this.enterpriseId,
            danger: this.danger,
            hazarchem: this.hazarchem,
            regprocess: this.regprocess,
            level: this.level,
          });
        } else {
          this.$refs[this.activeName].getData(this.enterpriseId);
        }
      });
    },
    managementType(type) {
      let val = "";
      switch (type) {
        case "10":
          val = "危险化学品生产企业";
          break;
        case "20":
          val = "危险化学品经营企业";
          break;
        case "21":
          val = "加油站";
          break;
        case "22":
          val = "仓储经营";
          break;
        case "23":
          val = "带储存经营（构成重大危险源）";
          break;
        case "24":
          val = "带储存经营（不构成重大危险源）";
          break;
        case "30":
          val = "危险化学品使用企业（使用许可）";
          break;
        case "40":
          val = "化工企业（不实施使用许可）";
          break;
        case "50":
          val = "医药企业";
          break;
        case "60":
          val = "气体充装企业";
          break;
        case "70":
          val = "油气储存企业";
          break;
        case "90":
          val = "其它企业";
          break;
        default:
          val = " ";
          break;
      }
      return val;
    },
    openArchives() {
      this.dialogVisible = true;
      // this.tabLoading = true
      this.handleClick();
      this.orgCode = this.enterpriseId;
      //获取企业的orgCode
      // getfindByEnterpId({ enterpId: this.enterpriseId }).then((res) => {
      //   this.tabLoading = false;
      //   this.orgCode = res.data.data.id;
      // });
    },
    //填报信息
    FillInformationBool() {
      this.$refs.FillInformation.closeBoolean(true);
      this.$refs.FillInformation.getData(this.enterpriseId);
    },
    //登记信息
    RegistrationInformationBool() {
      this.$refs.RegistrationInformation.closeBoolean(true);
      this.$refs.RegistrationInformation.getData(this.enterpriseId);
    },
    //危险化工工艺
    processBool(relationId) {
      this.$refs.Process.closeBoolean(true);
      this.$refs.Process.getData(relationId);
    },
    //危险化学品
    chemicalsBool(id) {
      this.$refs.Chemicals.closeBoolean(true);
      this.$refs.Chemicals.getData(id);
    },
    //危险源
    hazardsBool(dangerId) {
      this.$refs.Hazards.closeBoolean(true);
      this.$refs.Hazards.getData(dangerId);
    },
    //设备清单
    EquipmentListBool(dangerId) {
      this.$refs.EquipmentList.closeBoolean(true);
      this.$refs.EquipmentList.getData(dangerId);
    },
    getData(id) {
      this.enterpriseId = id;
      this.loading = true;
      getInformationBasicInfo(id).then((res) => {
        this.$nextTick(() => {
          this.enterprise = res.data.data.enterprise;
          this.danger = res.data.data.danger;
          this.hazarchem = res.data.data.hazarchem;
          this.regprocess = res.data.data.regprocess;
          // console.log(id, res);
          this.loading = false;
        });
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素
  mounted() {},
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding-top: 15px;
}
.basicInformation {
  overflow: hidden;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .back {
    height: 40px;
  }
  .div1 {
    // margin-top: 20px;
    .title {
      width: 100%;
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
      display: flex;
      justify-content: space-between;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 48%;
            padding: 1%;
            word-break: break-all;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            word-break: break-all;
          }
        }
        .lang {
          list-style-type: none;
          width: 66.6%;
          display: flex;
          border-top: 1px solid #eaedf2;
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 24.9%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            background-color: rgb(242, 242, 242);
          }
          .r {
            width: 73.3%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: left;
            padding: 0px 10px;
            word-break: break-all;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            background: rgb(242, 246, 255);
          }
          .r {
            width: 50%;
            word-break: break-all;
          }
        }
      }
    }
  }
  .div2 {
    margin-top: 20px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 600;
        height: 40px;
        line-height: 40px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 12.5%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-all;
        }
        .liLine {
          list-style-type: none;
          width: 12.5%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          text-align: center;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
  .div3 {
    margin-top: 20px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 600;
        height: 40px;
        line-height: 40px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          // height: 40px;
          // line-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-all;
        }
        .liLine {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          text-align: center;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
  .div4 {
    margin-top: 20px;
    padding-bottom: 40px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 600;
        height: 40px;
        line-height: 40px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          // height: 40px;
          // line-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          word-break: break-all;
        }
        .liLine {
          list-style-type: none;
          width: 14.28%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          // height: 40px;
          // line-height: 40px;
          text-align: center;
          display: flex;
          justify-content: space-around;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
}
</style>
