<template>
  <div class="enterpriseManagement">
    <div v-show="!TrendChartBool">
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span @click="goToSafety"
                ><a-icon type="home" theme="filled" class="icon" />
                企业数据填报统计
              </span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="showBreak">{{
              districtName
            }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <!-- <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getDate"
            unlink-panels
            size="mini"
            style="width:300px"
          >
          </el-date-picker> -->
          <div>
            <el-cascader
              size="mini"
              placeholder="请选择行政区划"
              :options="district"
              v-model="districtCode"
              :disabled='showBreak'
              style="width: 200px"
              :props="{
                checkStrictly: true,
                value: 'distCode',
                label: 'distName',
                children: 'children',
                emitPath: false,
              }"
              clearable
              :show-all-levels="true"
              v-if="isShowDist"
            ></el-cascader>
          </div>
          <div v-if="showBreak">
            <el-input
              placeholder="请输入企业名称"
              v-model.trim="enterpName"
              size="mini"
              clearable
              style="width: 200px"
            >
            </el-input>
          </div>
         
          <!-- <div v-if="!showBreak">
            <el-select
              v-model="submitStatus"
              placeholder="状态"
              clearable
              size="mini"
            >
              <el-option
                v-for="item in approvalStatusData"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </div> -->
          <div>
            <div v-if="showBreak">
              <el-button type="primary" size="mini" @click="search2"
                >查询</el-button
              >
            </div>
            <div v-else>
              <el-button type="primary" size="mini" @click="search"
                >查询</el-button
              >
            </div>
          </div>

          <div>
            <div v-if="showBreak">
               <CA-button type="primary" size="mini" plain @click="EnterexportExcel"
              >导出</CA-button
            >
            </div>
            <div v-else>
               <CA-button type="primary" size="mini" plain @click="exportExcel"
              >导出</CA-button
            >
            </div>
           
          </div>
        </div>
        <!-- <CA-button type="primary" size="mini" plain @click="gotoTrendAnalysis"
          ><a-icon type="bar-chart" class="chart-icon" />企业分析</CA-button
        > -->
      </div>
      <div class="table-main">
        <!-- 这是企业详情 -->
        <div v-if="showBreak">
          <div class="table">
            <el-table
              :data="tableData2.list"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span>{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="单位名称"
                align="center"
                prop="enterpName"
                width="400"
              >
              </el-table-column>
              <el-table-column
                label="行政区划"
                align="center"
                prop="districtName"
                width="200"
              >
              </el-table-column>

              <el-table-column
                label="应填报风险单元数量"
                prop="dangerNum"
                align="center"
              >
              </el-table-column>
              <el-table-column label="已填报" prop="submitNum" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  {{ row.submitNum }}
                </template>
              </el-table-column>
              <el-table-column
                label="未填报"
                prop="notSubmitNum"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  {{ row.notSubmitNum }}
                </template>
              </el-table-column>

              <el-table-column label="填报率" prop="submitRate" align="center">
              </el-table-column>
              <el-table-column label="状态" prop="submitStatus" align="center">
              </el-table-column>

              <el-table-column label="操作" prop="opter" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    @click="openDialogAlarmDetailsList(row)"
                    style="color: #3977ea; cursor: pointer"
                    >详情</span
                  >
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange2"
              @current-change="handleCurrentChange2"
              :current-page.sync="currentPage2"
              background
              layout="total, prev, pager, next"
              :total="total2"
            >
            </el-pagination>
          </div>
        </div>

        <div v-else>
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <span>{{ $index + 1 }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="行政区划"
                align="center"
                prop="districtName"
              >
              </el-table-column>

              <el-table-column
                label="应填报风险单元数量"
                prop="enterpNum"
                align="center"
              >
              </el-table-column>
              <el-table-column label="已填报" prop="submitNum" align="center">
              </el-table-column>
              <el-table-column
                label="未填报"
                prop="notSubmitNum"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <!-- -->
                  <span
                    v-if="row.notSubmitNum || tableData.length == 1"
                    @click="xiaZuan(row.districtCode, row.districtName)"
                    style="color: #3977ea; cursor: pointer"
                    >{{ row.notSubmitNum }}</span
                  >
                  <span v-else>{{ row.notSubmitNum }}</span>
                </template>
              </el-table-column>

              <el-table-column label="填报率" prop="submitRate" align="center">
              </el-table-column>
              <el-table-column label="状态" prop="submitStatus" align="center">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <fillReportDetailsList ref="fillReportDetailsList"></fillReportDetailsList>
    <!-- <fillReportList ref="fillReportList"></fillReportList> -->
  </div>
</template>
<script>
//D:\job\李大佬\dev-hbs\src\components\feature\riskAssessment\riskDynamic\fillReport\fillReportDetailsList.vue
import fillReportDetailsList from "@/components/feature/riskAssessment/riskDynamic/fillReport/fillReportDetailsList.vue";
import fillReportList from "@/components/feature/riskAssessment/riskDynamic/fillReport/fillReportList.vue";
import { alarmDistrictExport, alarmDistrictCount } from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import {
  statisticsList,
  statisticsExport,
  statisticsListEnterpList,
  statisticsListEnterpExport,
} from "@/api/accidentManagement";
export default {
  components: {
    fillReportDetailsList,
    fillReportList,
  },
  data() {
    return {
      // 查询
      enterpName: "",
      submitStatus: "",
      districtCode: this.$store.state.login.userDistCode,
      distCode: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      tableData: [],
      tableData2: [],
      approvalStatusData: [
        { id: '未完成', label: "未完成" },
        { id: '已完成', label: "已完成" },
      ],
      showtable: true,
      currentPage: 1,
      currentPage2: 1,
      total: 0,
      total2: "",
      showBreak: false,
      districtName: "",
      loading: false,
      selection: [],
      TrendChartBool: false,
      alarm: [],
      companyAlarmClearedNum: [],
      clearedAlarmRate: [],
    };
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  methods: {
    getEnRiskList() {
      this.loading = true;
      statisticsList({
        districtCode: this.districtCode,
        enterpName: this.enterpName,
        id: "",
        submitStatus: this.submitStatus,
        pageSize: 25,
        current: this.currentPage,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data;
        }
      });
    },
    getEnRiskList2() {
      this.loading = true;
      statisticsListEnterpList({
        districtCode: this.districtCode,
        enterpName: this.enterpName,
        id: "",
        nowPage: this.currentPage2,
        pageSize: 10,
        submitStatus: this.submitStatus,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData2 = res.data.data;
          this.total2 = res.data.data.total;
        }
      });
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      this.getEnRiskList();
    },
    getDate() {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd hh:mm:ss");
    },
    // 导出
    exportExcel() {
      // let list = [this.distCode, ...this.selection];
      statisticsExport({
        districtCode: this.districtCode,
        enterpName: this.enterpName,
        id: "",
        submitStatus: this.submitStatus,
        pageSize: 25,
        current: this.currentPage,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业数据填报统计" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },   
    EnterexportExcel() {
      // let list = [this.distCode, ...this.selection];
      statisticsListEnterpExport({
        districtCode: this.districtCode,
        enterpName: this.enterpName,
        id: "",
        nowPage:0,
        pageSize: 0,
        submitStatus: this.submitStatus,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename =this.districtName + "企业数据填报统计" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].districtCode;
      }
    },

    xiaZuan(districtCode, districtName) {
      this.districtName = districtName;
      this.showBreak = true;
      this.districtCode = districtCode;
      this.getEnRiskList2();
      // this.$nextTick(()=>{
      //   this.$refs.fillReportList.xiaZuan(districtCode, districtName)
      // })
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      //   this.level = "";
      this.districtCode = this.$store.state.login.userDistCode;
      this.getEnRiskList();
    },

    openDialogAlarmDetailsList(row) {
      this.$refs.fillReportDetailsList.closeBoolean(true);
      // districtCode = this.isShowDist ? districtCode : null;
      this.$refs.fillReportDetailsList.getEntData(row);
      // this.$refs.fillReportDetailsList.getDistrict();
    },
    search() {
      this.currentPage = 1;
      this.getEnRiskList();
    },
    search2() {
      this.currentPage = 1;
      this.getEnRiskList2();
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data;
      this.getEnRiskList();
    },
    handleCurrentChange2(val) {
      this.currentPage = val;
      this.getEnRiskList2();
    },
    handleSizeChange2() {},
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getEnRiskList();
    this.$setEchart("myCharted", 250, 250);
    this.$nextTick(function () {
      this.drawLine();
    });
  },
  watch: {
    // mode(newValue, oldValue) {
    //   if (newValue == "统计") {
    //     this.showtable = true;
    //   } else {
    //     this.showtable = false;
    //   }
    // },
    //观察option的变化
    // tableData: {
    //   handler(newVal, oldVal) {
    //     this.drawLine();
    //   },
    //   deep: true,
    //   // immediate: true,
    // },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .seach-part {
    // font-weight: 600;
    // display: flex;
    // justify-content: space-between;
    // padding-bottom: 10px;
    margin-bottom: 15px;
    // margin-top: 20px;
    // border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      // width: 500px;
      display: flex;
      width: 100%;
      div {
        margin-right: 5px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // padding: 10px 0;
      margin-bottom: 10px;
      height: 40px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
        float: left;
      }
      .radio {
        float: right;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
