<template>
  <div class="table">
    <el-dialog
      title="编辑用户"
      :visible.sync="edUserVisible"
      :modal="true"
      :destroy-on-close="true"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="container">
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="用户名" prop="loginName">
            <el-input v-model.trim="ruleForm.loginName"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input
              type="password"
              v-model.trim="ruleForm.password"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPsd">
            <el-input
              type="password"
              v-model.trim="ruleForm.password"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item label="姓名" prop="name">
            <el-input v-model.trim="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="性别" prop="six">
            <el-radio v-model="ruleForm.sexCode" label="1">男</el-radio>
            <el-radio v-model="ruleForm.sexCode" label="2">女</el-radio>
          </el-form-item>
          <el-form-item label="办公电话" prop="telephone">
            <el-input v-model.trim="ruleForm.telephone"></el-input>
          </el-form-item>
          <el-form-item label="移动电话" prop="cellphone">
            <el-input v-model.trim="ruleForm.cellphone"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model.trim="ruleForm.email"></el-input>
          </el-form-item>
          <el-form-item label="用户类型">
            <el-select
              v-model="ruleForm.userTypeCode"
              placeholder="请选择用户类型"
            >
              <el-option label="超级用户" :value="9"></el-option>
              <el-option label="上级用户" :value="0"></el-option>
              <el-option label="下级用户" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-button
          size="default"
          :loading="submitting"
          type="primary"
          class="commit"
          @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStructureTree,
  validateLoginNames,
  validatePsds,
  addUser,
  getDetailData,
} from "@/api/user";
export default {
  //import引入的组件
  props: {
    edVisible: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
    },
    orgCode: {
      type: String,
    },
  },
  computed: {},
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入密码"));
      } else {
        validatePsds({ password: value })
          .then((data) => {
            if (data.data.code == 0) {
              if (this.ruleForm.confirmPsd !== "") {
                this.$refs.ruleForm.validateField("confirmPsd");
              }
              callback();
            } else {
              callback(new Error(data.data.message));
            }
          })
          .catch((e) => {
            callback(new Error("密码不能小于八位数"));
            console.log(e, "请求错误");
          });
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请再次输入密码"));
      } else if (value !== this.ruleForm.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    var validateLoginName = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入用户名"));
      } else {
        validateLoginNames({ id: this.rowData.userId, loginName: value })
          .then((data) => {
            if (data.data.code == 0) {
              callback();
            } else {
              callback(new Error(data.data.message));
            }
          })
          .catch((e) => {
            callback(new Error("用户名存在"));
            console.log(e, "请求错误");
          });
      }
    };
    return {
      edUserVisible: false,
      submitting: false,
      ruleForm: {
        userTypeCode: "",
        sexCode: "",
        loginName: "",
        name: "",
        cellphone: "",
        telephone: "",
        email: "",
        password: "",
        confirmPsd: "",
      },
      password: "",
      confirmPsd: "",
      selectedRoleIds: "",
      rules: {
        password: [
          { validator: validatePass, trigger: "blur", required: true },
        ],
        confirmPsd: [{ trigger: "blur", required: true }],
        name: [{ message: "请输入姓名", trigger: "blur", required: true }],
        loginName: [
          { validator: validateLoginName, trigger: "blur", required: true },
        ],
      },
      allTreeData: [],
      treeData: [],
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.edUserVisible = val;
    },
    getRowData() {
      // this.edUserVisible = this.edVisible;
      this.ruleForm = this.rowData;
      // this.password = this.rowData.password;
      this.ruleForm.confirmPsd = this.rowData.password;
    },
    onSelects(selectedKeys, info) {
      //   console.log('selected', selectedKeys, info);
    },
    onChecks(checkedKeys, info) {
      if (checkedKeys) {
        this.selectedRoleIds = checkedKeys.join(",");
      } else {
        this.selectedRoleIds = "";
      }
    },
    getStructureTreeData() {
      getStructureTree()
        .then((data) => {
          if (data.data.code == 0) {
            this.treeData = data.data.data;
            this.treeData.forEach((item) => {
              item.key = item.id;
              item.title = item.systemName;
              // this.allIds.push(item.key)
              if (item.children.length > 0) {
                item.children.forEach((items) => {
                  items.key = items.id;
                  items.title = items.name;
                  if (items.children.length > 0) {
                    items.children.forEach((itemed) => {
                      itemed.key = itemed.id;
                      itemed.title = itemed.name;
                    });
                  }
                });
              }
            });
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    // dataTOTree (data,val) {
    //     const map = {};
    //     data.forEach((item) => {
    //         item.key = item.id;
    //         item.disabled= item.chkDisabled;
    //         map[item.id] = item;
    //     });
    //     data.forEach((item) => {
    //         const parent = map[item.parentId];
    //         if (parent) {
    //             (parent.children || (parent.children = [])).push(item);
    //         } else {
    //             val.push(item);
    //         }
    //     });
    // },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.submitting) return;
          this.submitting = true;
          addUser({
            id: this.rowData.id,
            name: this.ruleForm.name,
            loginName: this.ruleForm.loginName,
            password: this.ruleForm.password,
            sexCode: this.ruleForm.sexCode,
            orgCode: this.orgCode,
            telephone: this.ruleForm.telephone,
            cellphone: this.ruleForm.cellphone,
            email: this.ruleForm.email,
            userTypeCode: this.ruleForm.userTypeCode,
          })
            .then((data) => {
              if (data.data.code == 0) {
                this.submitting = false;
                this.$message({
                  type: "success",
                  message: data.data.msg,
                });
                this.edUserVisible = false;
                this.$refs.ruleForm.resetFields();
                this.$emit("ed-callback");
              } else {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: "success",
                });
              }
            })
            .catch((e) => {
              this.submitting = false;
              this.$message({
                type: "info",
                message: "接口报错",
              });
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    /**
     * 点击关闭图标或者遮罩回调
     * @param done
     */
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(() => {
          done();
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getStructureTreeData();
  },
  watch: {
    rowData() {
      this.getRowData();
    },
  },
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    .input {
      width: 60%;
    }
    .el-select,
    .radio-box {
      width: 60%;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .el-input {
    width: 80%;
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
