import axios from "axios";
import qs from "qs";

// 获取事故列表
export const getAccidentListData = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/page/v1",
    data: params,
  });
};

// 获取事故类型列表
export const getAccidentTypeListData = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/code/v1",
    data: params,
  });
};

// 删除事故列
export const deleteAccidentById = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/delete/v1?id=" + params.id,
    data: params,
  });
};

// 新增事故列
export const addAccident = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/save/v1",
    data: params,
  });
};

// 更新事故列
export const updateAccident = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/update/v1",
    data: params,
  });
};

//分页查询安全事故记录
export const getAccidentRecordList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/AccidentRecord/page/v1",
    data: data,
  });
};

//新增安全事故记录
export const getAccidentRecordAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/AccidentRecord/add/v1",
    data: data,
  });
};

//修改安全事故记录
export const getAccidentRecorUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/AccidentRecord/update/v1",
    data: data,
  });
};

//删除安全事故记录
export const getAccidentRecorDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/AccidentRecord/delete/v1",
    data: data,
  });
};

//详情安全事故记录
export const getAccidentRecorFind = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/AccidentRecord/find/v1",
    data: data,
  });
};

//新增自然灾害记录
export const naturalDisasterAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/naturalDisaster/add/v1",
    data: data,
  });
};

//根据id删除自然灾害记录
export const naturalDisasterDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/naturalDisaster/delete/v1",
    data: data,
  });
};

//根据id查询自然灾害记录
export const naturalDisasterFind = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/naturalDisaster/find/v1",
    data: data,
  });
};

//分页查询自然灾害记录
export const naturalDisasterPageList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/naturalDisaster/page/v1",
    data: data,
  });
};

//修改自然灾害记录
export const naturalDisasterUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/naturalDisaster/update/v1",
    data: data,
  });
};

//企业风险填报情况行政区划统计列表导出
export const statisticsExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/statistics/distList/export/v1",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

//企业风险填报情况行政区划统计列表
export const statisticsList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/statistics/distList/v1",
    data: data,
  });
};

//企业风险填报状态统计列表导出
export const statisticsListEnterpExport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/statistics/enterpList/export/v1",
    data: { ...data },
    responseType: "arraybuffer",
  });
};

//企业风险填报状态统计分页列表
export const statisticsListEnterpList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/statistics/enterpList/v1",
    data: data,
  });
};

//企业风险单元填报状态统计分页列表
export const statisticsListEnterpStatus = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/statistics/enterpStatus/v1",
    data: data,
  });
};

// //企业风险单元填报状态统计分页列表
// export const statisticsListEnterpStatus= data => {
//   return axios({
//     method: "post",
//     url: '/gemp-model/api/gemp/model/statistics/enterpStatus/v1',
//     data: data
//   });
// };

//安全生产事故等级下拉框
export const accidentLevel = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/accidentLevel/v1",
    data: data,
  });
};

//安全生产事故分类下拉框
export const accidentType = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/accidentType/v1",
    data: data,
  });
};

//灾害类型
export const disasterTypeV1 = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/dict/disasterType/v1",
    data: data,
  });
};

// 获取事故列表
export const getAccidentListDataDuty = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/page/v1",
    data: params,
  });
};
export const getReport = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/getReport/v1",
    data: params,
  });
};
// 删除事故列
export const deleteAccidentByIdDuty = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/delete/v1?id=" + params.id,
    data: params,
  });
};

// 新增事故列
export const addAccidentDuty = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/save/v1",
    data: params,
  });
};

// 更新事故列
export const updateAccidentDuty = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/update/v1",
    data: params,
  });
};
// 根据id查询详情事故

export const getAccidentByIdDuty = (id) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/id/v1?id=" + id,
  });
};
// 上报事故列
export const reportUpload = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/dutyEvent/pushEvent/v1",
    data: params,
  });
};
// 分享给我的事故列表
export const getAccidentShareData = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/sharePage/v1",
    data: params,
  });
};
// 分享
export const shareAccident = (params) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/event/share/v1",
    data: params,
  });
};

export const getEventBase = (params) => {
  return axios({
    method: "post",
    url: "/gempProxy/gapi/gemp-event/api/gemp/event/eventbase/list/outer/v1",
    data: params,
  });
};
