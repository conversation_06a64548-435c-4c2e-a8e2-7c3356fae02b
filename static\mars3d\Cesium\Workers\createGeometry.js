define(["./defaultValue-69ee94f4","./PrimitivePipeline-9bc85f23","./createTaskProcessorWorker","./Transforms-06c05e21","./Matrix2-e6265fb0","./RuntimeError-ac440aa5","./ComponentDatatype-a9820060","./WebGLConstants-f63312fc","./_commonjsHelpers-3aae1032-15991586","./combine-0259f56f","./GeometryAttribute-b7edcc35","./GeometryAttributes-1b4134a9","./GeometryPipeline-311a1f9e","./AttributeCompression-6e71d14f","./EncodedCartesian3-20914bf5","./IndexDatatype-1cbc8622","./IntersectionTests-94cb8698","./Plane-042297c7","./WebMercatorProjection-50f5da31"],(function(e,t,r,n,o,i,s,a,c,f,u,m,b,l,d,p,y,P,k){"use strict";const C={};function G(t){let r=C[t];return e.defined(r)||("object"==typeof exports?C[r]=r=require(`Workers/${t}`):require([`Workers/${t}`],(function(e){r=e,C[r]=e}))),r}return r((function(r,n){const o=r.subTasks,i=o.length,s=new Array(i);for(let t=0;t<i;t++){const r=o[t],n=r.geometry,i=r.moduleName;if(e.defined(i)){const e=G(i);s[t]=e(n,r.offset)}else s[t]=n}return Promise.all(s).then((function(e){return t.PrimitivePipeline.packCreateGeometryResults(e,n)}))}))}));
