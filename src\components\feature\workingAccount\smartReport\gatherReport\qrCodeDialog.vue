<template>
  <el-dialog
    title="分享"
    :visible="visible"
    @close="closeBoolean(false)"
    width="600px"
    top="5vh"
    :close-on-click-modal="true"
  >
    <div class="share-dialog">
      <!-- 添加 Tab 切换 -->
      <el-tabs v-model="activeTab">
        <el-tab-pane label="二维码分享" name="qrcode">
          <div class="qrcode-container">
            <canvas ref="qrCodeCanvas" class="qrcode"></canvas>
          </div>
        </el-tab-pane>
        <el-tab-pane label="企业分享" name="enterprise">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入企业名称"
              prefix-icon="el-icon-search"
              clearable
              @input="handleSearch"
            ></el-input>
          </div>
          <!-- 企业列表 -->
          <div class="enterprise-list">
            <el-table
              ref="enterpriseTable"
              :data="filteredEnterpriseList"
              height="250"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column
                prop="enterpName"
                label="企业名称"
              ></el-table-column>
              <el-table-column
                prop="enterpId"
                label="企业编码"
                width="120"
              ></el-table-column>
            </el-table>
          </div>
          <div class="list-footer">
            <el-button type="primary" size="small" @click="handleShare"
              >确认分享</el-button
            >
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-dialog>
</template>

<script>
import QRCode from "qrcode";
import { mapState } from "vuex";
import { getEnterpriseList } from "@/api/entList"; // 导入企业列表接口
import { shareReportGather } from "@/api/smartReport"; // 导入分享报表接口

export default {
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    url: {
      type: String,
      default: "",
    },
    reportId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      qrcodeDataURL: "",
      qrcode: "",
      activeTab: "qrcode", // 默认显示二维码 tab
      enterpriseList: [], // 企业列表数据
      searchKeyword: "", // 搜索关键词
      selectedEnterprises: [], // 选中的企业
    };
  },
  computed: {
    ...mapState({
      token_type: (state) => state.login.user.token_type,
      access_token: (state) => state.login.user.access_token,
      userDistCode: (state) => state.login.userDistCode,
    }),
    // 过滤后的企业列表
    filteredEnterpriseList() {
      if (!this.searchKeyword) {
        return this.enterpriseList;
      }
      return this.enterpriseList.filter((item) =>
        item.enterpName.toLowerCase().includes(this.searchKeyword.toLowerCase())
      );
    },
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 确保对话框显示后再执行
        this.$nextTick(() => {
          this.getEnterpriseData();
          // 延迟生成二维码，确保DOM已经渲染
          setTimeout(() => {
            this.generateQRCode();
          }, 100);
        });
      }
    },
    activeTab(newVal) {
      if (newVal === "qrcode") {
        // 延迟生成二维码，确保DOM已经渲染
        setTimeout(() => {
          this.generateQRCode();
        }, 100);
      } else if (newVal === "enterprise") {
        this.getEnterpriseData();
      }
    },
  },
  created() {
    this.qrcodeDataURL = this.url;
  },
  mounted() {
    // 组件挂载后延迟生成二维码
    setTimeout(() => {
      this.generateQRCode();
    }, 300);
  },
  methods: {
    // 生成二维码
    generateQRCode() {
      const canvas = this.$refs.qrCodeCanvas;
      if (!canvas) return;

      QRCode.toCanvas(
        canvas,
        this.qrcodeDataURL,
        {
          color: {
            dark: "#000", // 二维码颜色
            light: "#ffffff", // 背景颜色
          },
          width: 300, // 二维码宽度
          height: 300, // 二维码高度
        },
        (error) => {
          if (error) {
            console.error(error);
          } else {
            console.log("QR code generated!");
          }
        }
      );
    },

    // 获取企业列表数据
    async getEnterpriseData() {
      try {
        const res = await getEnterpriseList({
          districtCode: this.userDistCode,
          current: 1,
          size: 10000,
        });
        if (res.status === 200) {
          this.enterpriseList = res.data.data.records || [];
        } else {
          this.$message.error(res.data.msg || "获取企业列表失败");
        }
      } catch (error) {
        console.error("获取企业列表失败:", error);
        this.$message.error("获取企业列表失败");
      }
    },

    // 处理搜索
    handleSearch() {
      // 通过计算属性自动过滤
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      this.selectedEnterprises = selection;
    },

    // 处理企业分享
    async handleShare() {
      if (this.selectedEnterprises.length === 0) {
        this.$message.warning("请选择至少一个企业");
        return;
      }

      const departmentIds = this.selectedEnterprises.map(
        (item) => item.enterpId
      );

      try {
        const res = await shareReportGather({
          reportGatherId: [this.reportId], // 使用数组包裹报表ID
          enterpId: departmentIds,
        });
        if (res.data.status === 200) {
          this.$message.success("分享成功");
          this.closeBoolean(false);
        } else {
          this.$message.error(res.data.msg || "分享失败");
        }
      } catch (error) {
        console.error("分享失败:", error);
        this.$message.error("分享失败");
      }
    },

    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.share-dialog {
  .qrcode-container {
    text-align: center;
    padding: 20px 0;

    .qrcode {
      margin: 0 auto;
    }
  }

  .search-box {
    margin-bottom: 15px;
  }

  .enterprise-list {
    height: 250px;
    margin-bottom: 15px;
  }

  .list-footer {
    text-align: right;
  }
}

.footer {
  text-align: center;
}
</style>
