<!-- 企业画像报告 -->
<template>
  <div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="画像列表" name="portraitList">
        <portaitList ref="portaitList"></portaitList>
      </el-tab-pane>
      <el-tab-pane label="企业报告" name="enterpriseReport">
        <enterpriseReport></enterpriseReport>
        <!-- <entReportDetail></entReportDetail> -->
      </el-tab-pane>
      <el-tab-pane label="行业报告" name="industryReport">
        <industryReport></industryReport>
      </el-tab-pane>
      <el-tab-pane label="区域报告" name="regionReport">
        <regionReport></regionReport>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import enterpriseReport from "./enterpriseReport.vue";
import regionReport from "./regionReport.vue";
import industryReport from "./industryReport.vue";
import entReportDetail from "./entReportDetail";
import portaitList from "../enterprisePortraitList/portaitList.vue";
export default {
  components: {
    enterpriseReport,
    regionReport,
    industryReport,
    entReportDetail,
    portaitList,
  },
  data() {
    return {
      activeName: "portraitList",
    };
  },
};
</script>

<style lang="scss" scoped></style>
