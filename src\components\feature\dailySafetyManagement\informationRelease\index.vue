<template>
  <div class="informationRelease">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 信息发布
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="activeBody">
      <div class="activeHeader">
        <el-tabs v-model="activeTabClass" @tab-click="handleClickActiveTab()">
          <el-tab-pane label="通知公告" name="announcements"
            ><Announcements ref="announcements"></Announcements
          ></el-tab-pane>
          <el-tab-pane label="安全警示" name="safetyWarning"
            ><SafetyWarning ref="safetyWarning"></SafetyWarning
          ></el-tab-pane>
          <el-tab-pane label="宣传教育" name="publicityAndEducation"
            ><Education ref="publicityAndEducation"></Education
          ></el-tab-pane>
          <el-tab-pane label="事故警示" name="accidentWarning"
            ><AccidentWarning ref="accidentWarning"></AccidentWarning
          ></el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import Announcements from "./announcements";
import SafetyWarning from "./safetyWarning";
import Education from "./education";
import AccidentWarning from "./accidentWarning/accidentWarning.vue";
export default {
  //import引入的组件
  components: {
    Announcements: Announcements,
    SafetyWarning: SafetyWarning,
    Education: Education,
    AccidentWarning
  },
  data() {
    return { activeTabClass: this.$store.state.controler.informationClickBar };
  },
  mounted() {},
  methods: {
    handleClickActiveTab() {     
      this.$nextTick(() => {     
        this.$refs[this.activeTabClass].getList();
      });
    },
  },
  destroyed(){
    this.$store.commit('controler/updateInformationClickBar', 'announcements')
  }
};
</script>

<style lang="scss" scoped>
.informationRelease {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }
  .activeBody {
    .activeHeader {
      width: 100%;
    }
  }
}
</style>
