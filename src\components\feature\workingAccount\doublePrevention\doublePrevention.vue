<template>
  <div>
        <!-- <iframe
      class="iframeBox"
      :src="url + '/dualControl/#/finishStaAll'"
      style="border: 0"
      width="100%"
      height="100%"
    ></iframe> -->



    <iframe
      class="iframeBox"
      src="https://scyf-jgd.hbsis.gov.cn:38080/home"
      style="border: 0"
      width="100%"
      height="100%"
    ></iframe>




    <!-- <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              双重预防机制推进工作
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>

        <div class="doubleBox">
          <div class="doubleLeft">双重预防机制推进专项工作</div>
          <div class="doubleRight">
            <div class="item">
              <a
                target="_blank"
                href="https://succezbi.hbyjkffn.hb.cegn.cn:30443/birpt-engine-ui/DX/ana?:open=MH41ld9TMUFd5XxVmuRmOF&:play=true&:playinterval=false"
                >企业完成率</a
              >
            </div>

            <div class="item">
              <a
                target="_blank"
                href="https://succezbi.hbyjkffn.hb.cegn.cn:30443/birpt-engine-ui/DX/ana?:open=TRU2EL757cBd1d2hISA3DB&:play=true&:playinterval=false"
                >地市排名</a
              >
            </div>
          </div>
        </div>
      </div>
    </div> -->
  </div>
</template>
<script>
import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
export default {
  components: {},
  data() {
    return {
      url:''
    };
  },
  created(){  
  },
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.iframeBox {
  height: calc(100vh - 80px);
  padding-top: 0;
  width: 100%;
  // background: #f3f6f8;
}
// .doubleBox {
//   display: flex;
//   align-items: center;
//   /* justify-content: center; */
//   padding: 50px 0 0 0;
//   border-top: 1px rgba(198, 207, 217, 0.33) solid;
//   margin: 50px 0 0 0;
//   .doubleLeft {
//     padding-right: 20px;
//   }
//   .doubleRight > .item {
//     padding: 10px 0;
//     a {
//       border: 1px solid #1890ff;
//       display: inline-block;
//       min-width: 100px;
//       text-align: center;
//       padding: 10px 0;
//       border-radius: 3px;
//       color: #1890ff;
//     }
//      a:hover {
//       border: 1px solid #f1d650;   
//       color: #f1d650;
//     }
//   }
// }
</style>

