<!-- 企业画像 -->
<template>
  <div class="portrait-configuration">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 风险研判指标配置
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="指标配置方案列表" name="configurationList">
        <configurationList
          ref="configurationList"
          @check="handleClick"
          v-if="activeName == 'configurationList'"
        ></configurationList>
      </el-tab-pane>
      <el-tab-pane
        label="指标设置方案"
        name="configurationEdit"
        v-if="activeName == 'configurationEdit'"
      >
        <configurationEdit
          ref="configurationEdit"
          :planId="planId"
          @submit="handleBack"
        ></configurationEdit>
      </el-tab-pane>
    </el-tabs>
    <el-button
      class="active-box"
      type="primary"
      size="small"
      v-if="activeName == 'configurationList'"
      @click="handleAdd"
      >新增</el-button
    >
    <el-dialog
      title="新增企业画像评分方案"
      :visible="showAdd"
      @close="handleClose"
      width="550px"
      :close-on-click-modal="true"
    >
      <el-form
        label-position="left"
        ref="addModel"
        :model="addForm"
        :rules="rules"
        label-width="150px"
      >
        <el-form-item label="指数配置方案名称" prop="name">
          <el-input
            placeholder="请输入指数配置方案名称"
            v-model.trim="addForm.name"
            clearable
          >
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="handleClose" size="small">取 消</el-button>
        <el-button @click="handleSubmit" type="primary" size="small"
          >提交</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import configurationList from "./list.vue";
import configurationEdit from "./edit.vue";
import { getPortraitPlanAdd } from "@/api/enterprisePortrait";
export default {
  components: {
    configurationList,
    configurationEdit,
  },
  data() {
    return {
      activeName: "configurationList",
      showEdit: false,
      editData: {},
      addForm: {
        name: "",
      },
      planId:'',
      rules: {
        name: [
          {
            required: true,
            message: "请输入指数配置方案名称",
            trigger: "blur",
          },
        ],
      },
      showAdd: false,
    };
  },
  methods: {
    handleAdd() {
      this.showAdd = true;
      this.addForm.name = "";
    },
    handleClose() {
      this.showAdd = false;
    },
    handleSubmit() {
      this.$refs.addModel.validate((valid) => {
        if (valid) {
          getPortraitPlanAdd(this.addForm).then((res) => {
            if (res.data.status == 200) {
              this.showAdd = false;
              this.showEdit = true;
              this.activeName = "configurationEdit";
              this.planId = res.data.data;
            } else {
              this.$message.error(res.msg);
            }
          });
        } else {
          return false;
        }
      });
    },
    handleClick(row) {
      // this.addForm.name = row ? row.enterpName : "";
      this.planId = row.id;
      this.showEdit = true;
      this.activeName = "configurationEdit";
      //   this.$refs.configurationEdit.itemEdit('edit', row);
    },
    handleBack(data) {
      this.showEdit = false;
      this.activeName = "configurationList";
      //   this.$refs.configurationList.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.portrait-configuration {
  position: relative;
  .active-box {
    position: absolute;
    top: 40px;
    right: 20px;
  }
}
.header {
  background-color: #fff;
  overflow: hidden;
  margin-bottom: 5px;
  border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
  .breadcrumb {
    margin-bottom: 10px;
    cursor: pointer;
    color: #4f5b69;
    //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
  }
}
.form-box {
  display: flex;
  align-items: center;
}
</style>
