<template>
  <div class="menuManagement">
    <Side class="side" ref='goodslist'></Side>
    <div class="right" :style="width" ><Container @pf="parent"></Container></div>
  </div>
</template>

<script>
import Side from "./side";
import Container from "./Container";
export default {
  //import引入的组件
  components: {
    Side,
    Container,
  },
  data() {
    return {
      width: "width:80%",
    };
  },
  //方法集合
  methods: {
    parent(){
				this.$refs.goodslist.getMenuList(this.$store.state.sa.SAMenuListData.systemCode)
			}
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.menuManagement {
  display: flex;
  justify-content: flex-start;
  .side {
    margin-right: 15px;
  }

  .right {
    height: 89vh;
    background-color: #fff;
      overflow: auto;
  }
}
</style>