<template>
  <div class="preventive-page">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box" >
              <a-icon type="home" theme="filled" class="icon" />
              <span @click="goEnt()">双重预防分析</span><span v-if="showEnterprise||showDistrict" @click="goDist">\{{ distName }}</span>
              <span v-if="showEnterprise">\{{ entName }}</span>
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <prevetionList v-show="showCity" @detail="openDistrict"></prevetionList>
    <districtList
      v-if="showDistrict"
      :districtCode="districtCode"
      @detail="openEnt"
    ></districtList>
    <entList
      v-if="showEnterprise"
      :enterpriseCode="enterpriseCode"
      @return="showDetail = false"
    ></entList>
  </div>
</template>
<script>
import entList from "./entList.vue";
import prevetionList from "./list.vue";
import districtList from "./distList.vue";
export default {
  name: "preventiveAnalysis",
  components: { entList, prevetionList, districtList },
  data() {
    return {
      pageSize: 10,
      showDetail: false,
      enterpriseCode: "", //企业编码
      entName: "", // 企业名称
      districtCode: "", // 区域编码
      distName: "", // 区域名称
      showCity: true, //当前层级 city：市 area：区 enterprise：企业
      showDistrict: false,
      showEnterprise: false,
    };
  },
  mounted() {},
  methods: {
    openDistrict(row) {
      this.showCity = false;
      this.showDistrict = true;
      this.districtCode = row.qxbm;
      this.distName = row.qxmc;
    },
    openEnt(row) {
      this.showDistrict = false;
      this.showEnterprise = true;
      this.enterpriseCode = row.qybm;
      this.entName = row.entName;
    },
    goDist(){
      this.showDistrict = true;
      this.showEnterprise = false;
    },
    // 返回
    goEnt() {
      this.showDetail = false;
      this.showCity = true;
      this.showDistrict = false;
      this.showEnterprise = false;
    },
  },
};
</script>
<style scoped lang="scss">
.preventive-page {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
}
</style>
