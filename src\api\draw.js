import axios from "axios";

// 新增资源目录
export const addResource = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/index/add/v1",
    data: data,
  });
};

// 分页查询资源目录
export const getResourceList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/index/pageList/v1",
    data: data,
  });
};

// 根据id产看目录详情
export const getResourceById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/index/find/v1",
    data: data,
  });
};

// 编辑目录
export const updateResource = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/index/update/v1",
    data: data,
  });
};

// 删除目录
export const deleteResource = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/index/delete/v1",
    data: data,
  });
};

// 新增资源详情
export const addResourceDetail = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/detail/add/v1",
    data: data,
  });
};

//分页查询资源详情
export const getResourceDetailList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/detail/pageList/v1",
    data: data,
  });
};

// 编辑资源详情
export const updateResourceDetail = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/detail/update/v1",
    data: data,
  });
};

// 删除资源详情
export const deleteResourceDetail = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyResources/draw/detail/delete/v1",
    data: data,
  });
};
