!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("mars3d")):"function"==typeof define&&define.amd?define(["exports","mars3d"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["mars3d-widget"]={},e.mars3d)}(this,(function(e,t){"use strict";function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(i){if("default"!==i){var n=Object.getOwnPropertyDescriptor(e,i);Object.defineProperty(t,i,n.get?n:{enumerable:!0,get:function(){return e[i]}})}})),t.default=e,t}var n=i(t);function r(e,t){var i=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),i.push.apply(i,n)}return i}function o(e){for(var t=1;t<arguments.length;t++){var i=null!=arguments[t]?arguments[t]:{};t%2?r(Object(i),!0).forEach((function(t){c(e,t,i[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(i)):r(Object(i)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(i,t))}))}return e}function a(e){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},a(e)}function s(e,t){for(var i=0;i<t.length;i++){var n=t[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function c(e,t,i){return t in e?Object.defineProperty(e,t,{value:i,enumerable:!0,configurable:!0,writable:!0}):e[t]=i,e}function u(e){return u=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},u(e)}function l(e,t){return l=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},l(e,t)}function f(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function d(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var i,n=u(e);if(t){var r=u(this).constructor;i=Reflect.construct(n,arguments,r)}else i=n.apply(this,arguments);return f(this,i)}}var h=new RegExp("\\.css"),p=document.head||document.getElementsByTagName("head")[0],v=+navigator.userAgent.replace(/.*(?:AppleWebKit|AndroidWebKit)\/?(\d+).*/i,"$1")<536;function y(e){return"complete"===e.readyState||"loaded"===e.readyState}function w(e,t){var i;e.sheet&&(i=!0),setTimeout((function(){i?t():w(e,t)}),20)}function g(e,t,i){var n="onload"in e;function r(){e.onload=e.onreadystatechange=null,e=null,t()}"css"===i&&(v||!n)?setTimeout((function(){w(e,t)}),1):n?(e.onload=r,e.onerror=function(t){e.onerror=null,"css"===i?console.error("该css文件不存在："+e.href,t):console.error("该js文件不存在："+e.src,t),r()}):e.onreadystatechange=function(){y(e)&&r()}}function b(e,t,i,n){function r(){var i=t.indexOf(e);i>-1&&t.splice(i,1),0===t.length&&n()}e?h.test(e)?function(e,t,i){var n=document.createElement("link");n.rel="stylesheet",g(n,i,"css"),n.async=!0,n.href=e,p.appendChild(n)}(e,0,r):function(e,t,i){var n=document.createElement("script");n.charset="utf-8",g(n,i,"js"),n.async=!t.sync,n.src=e,p.appendChild(n)}(e,i,r):setTimeout((function(){r()}))}function _(e,t,i){var n=function(){i&&i()};if(0!==(e=Array.prototype.slice.call(e||[])).length)for(var r=0,o=e.length;r<o;r++)b(e[r],e,t,n);else n()}function m(e,t){if(y(e))t();else{var i=!1;window.addEventListener("load",(function(){i||(t(),i=!0)})),setTimeout((function(){i||(t(),i=!0)}),1500)}}var O,x=function(e,t){m(document,(function(){_(e,{},t)}))},k={beforeCreate:"beforeCreate",created:"created",beforeActivate:"beforeActivate",activated:"activated",openView:"openView",beforeDisable:"beforeDisable",disabled:"disabled",loadBefore:"loadBefore",load:"load"},A=window.jQuery;if(!A)throw new Error("请引入 jQuery 库");var C,I,P,W,j="",V=[],T=["_class"];function B(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";O=e,j=i,V=[],C=n.Util.merge({windowOptions:{position:"rt",maxmin:!1,resize:!0},autoDisable:!0,disableOther:!0},t.defaultOptions),"time"===(I=t.version)&&(I=(new Date).getTime());var r=t.openAtStart;if(r&&r.length>0)for(var a=0;a<r.length;a++){var s=r[a];s.hasOwnProperty("uri")&&""!==s.uri?s.hasOwnProperty("visible")&&!s.visible||(s.autoDisable=!1,s.openAtStart=!0,s._nodebug=!0,D(s),s._firstConfigBak=o({},s),V.push(s)):console.error("widget未配置uri",s)}if(P=t.debugger){var c='<div id="widget-testbar" class="mars3d-widgetbar animation-slide-bottom no-print-view" >      <div style="height: 30px; line-height:30px;"><b style="color: #4db3ff;">widget测试栏</b>&nbsp;&nbsp;<button  id="widget-testbar-remove"  type="button" class="btn btn-link btn-xs">关闭</button> </div>     <button id="widget-testbar-disableAll" type="button" class="btn btn-info" ><i class="fa fa-globe"></i>漫游</button></div>';A("body").append(c),A("#widget-testbar-remove").click((function(e){q()})),A("#widget-testbar-disableAll").click((function(e){K()}))}if((r=t.widgets)&&r.length>0){for(var u=0;u<r.length;u++){var l=r[u];if("group"===l.type){for(var f=' <div class="btn-group dropup">  <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-expanded="false"><i class="fa fa-align-justify"></i>'+l.name+' <span class="caret"></span></button> <ul class="dropdown-menu">',d=0;d<l.children.length;d++){var h=l.children[d];h.hasOwnProperty("uri")&&""!==h.uri?(f+=' <li data-widget="'+h.uri+'" class="widget-btn" ><a href="#"><i class="fa fa-star"></i>'+h.name+"</a></li>",D(h),h._firstConfigBak=o({},h),V.push(h)):console.error("widget未配置uri",h)}f+="</ul></div>",P&&!l._nodebug&&A("#widget-testbar").append(f)}else{if(!l.hasOwnProperty("uri")||""===l.uri){console.error("widget未配置uri",l);continue}if(P&&!l._nodebug){var p='<button type="button" class="btn btn-primary widget-btn" data-widget="'+l.uri+'"  > <i class="fa fa-globe"></i>'+l.name+" </button>";A("#widget-testbar").append(p)}D(l),l._firstConfigBak=o({},l),V.push(l)}}P&&A("#widget-testbar .widget-btn").each((function(){A(this).click((function(e){var t=A(this).attr("data-widget");t&&""!==t&&(R(t)?U(t):z(t))}))}))}for(var v=0;v<V.length;v++){var y=V[v];(y.openAtStart||y.createAtStart)&&F.push(y)}if(A(window).resize((function(){for(var e=0;e<V.length;e++){var t=V[e];t._class&&t._class.indexResize()}})),P){var w=S();w&&z(w)}G()}function E(){return n.Util.clone(C.windowOptions,T)}function S(){var e=window.location.toString();return-1===e.indexOf("#")?"":(e=e.split("#"))&&e.length>0?e[1]:void 0}function D(e){if(C)for(var t in C)"windowOptions"===t||e.hasOwnProperty(t)||(e[t]=C[t]);var i,n;e.path=(i=j+e.uri,n=i.lastIndexOf("/"),i.substring(0,n+1)),e.name=e.name||e.label}function z(e,t){var i;!O&&e.map&&B(e.map,{},e.basePath),"string"==typeof e?(e={uri:e},null!=t&&(e.disableOther=!t)):e.uri||console.error("activate激活widget时需要uri参数！",e);for(var n=0;n<V.length;n++){var r=V[n];if(e.uri===r.uri||r.id&&e.uri===r.id){if((i=r).isloading)return i;for(var a in e)"uri"!==a&&(i[a]=e[a]);break}}if(i||(D(e),i=e,e._firstConfigBak||(e._firstConfigBak=o({},e)),V.push(e)),P&&(console.log("开始激活widget："+i.uri),window.location.hash="#"+i.uri),i.disableOther&&(Array.isArray(i.disableOther)?U(i.disableOther):K(i.uri,i.group)),i.group&&M(i.group,i.uri),i._class)i._class.isActivate?i._class.update?i._class.update():function(e){clearInterval(W),e._class.disableBase(),W=setInterval((function(){e._class.isActivate||(e._class.activateBase(),clearInterval(W))}),200)}(i):i._class.activateBase();else{for(var s=0;s<F.length;s++)if(F[s].uri===i.uri)return F[s];F.push(i),1===F.length&&G()}return i}function H(e){for(var t=0;t<V.length;t++){var i=V[t];if(e===i.uri||e===i.id)return i}}function N(e){var t=H(e);return t?t._class:null}function R(e){var t=N(e);return!!t&&t.isActivate}function U(e){if(!e)return!1;if(Array.isArray(e))for(var t=e,i=0;i<V.length;i++)for(var n=V[i],r=0;r<t.length;r++){var o=t[r];if(n._class&&(o===n.uri||o===n.id)){n._class.disableBase(),t.splice(r,1);break}}else{"object"===a(e)&&(e=e.uri);for(var s=0;s<V.length;s++){var c=V[s];if(c._class&&(e===c.uri||e===c.id))return c._class.disableBase(),!0}}return!1}function K(e,t){for(var i=0;i<V.length;i++){var n=V[i];if(t&&n.group===t);else if(!0!==e&&!n.autoDisable)continue;(!e||e!==n.uri&&e!==n.id)&&(n._class&&n._class.disableBase())}}function M(e,t){if(e)for(var i=0;i<V.length;i++){var n=V[i];if(n.group===e){if(t&&(t===n.uri||t===n.id))continue;n._class&&n._class.disableBase()}}}var Q,L,F=[];function G(){if(0!==F.length)if(L)setTimeout(G,500);else{L=!0,(Q=F[0]).isloading=!0;var e=Q.uri;I&&(-1===e.indexOf("?")?e+="?cache="+I:e+="&cache="+I),window.NProgress&&window.NProgress.start(),X(k.loadBefore,{sourceTarget:Q}),x([j+e],(function(){L=!1,Q.isloading=!1,window.NProgress&&window.NProgress.done(!0),F.shift(),G()}))}}function q(){A("#widget-testbar").remove()}function $(){return I}var J=new n.BaseClass;function X(e,t,i){return J.fire(e,t,i)}var Y={__proto__:null,init:B,getDefWindowOptions:E,activate:z,getWidget:H,getClass:N,isActivate:R,disable:U,disableAll:K,disableGroup:M,eachWidget:function(e){for(var t=0;t<V.length;t++){e(V[t])}},bindClass:function(e){if(X(k.load,{sourceTarget:e}),Q)return Q.isloading=!1,Q._class=new e(O,Q),Q._class.activateBase(),Q._class;for(var t=function(){for(var e,t=document.scripts,i=t.length-1;i>=0;i--)if((e=t[i].src)&&-1!==e.indexOf("widgets"))return e;return""}(),i=0;i<V.length;i++){var n=V[i];if(t.endsWith(n.uri))return n.isloading=!1,n._class=new e(O,n),n._class.activateBase(),n._class}},removeDebugeBar:q,getCacheVersion:$,getBasePath:function(){return j},destroy:function(){for(var e=0;e<V.length;e++){var t=V[e];t._class&&(t._class.disableBase(),t._class.destroy&&t._class.destroy(),delete t._class)}O=null},eventTarget:J,on:function(e,t,i){return J.on(e,t,i)},off:function(e,t,i){return J.off(e,t,i)},fire:X,once:function(e,t,i){return J.once(e,t,i)},listens:function(e,t){return J.listens(e,t)}},Z=window.jQuery;if(!Z)throw new Error("请引入 jQuery 库");var ee=window.layer;if(!ee)throw new Error("请引入 layer.js弹窗 库");var te=n.BaseClass,ie=[],ne=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&l(e,t)}(u,e);var t,i,r,c=d(u);function u(e,t){var i;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),(i=c.call(this,t)).map=e,i.options=t,i.config=t,i.path=t.path||"",i.isActivate=!1,i.isCreate=!1,i._viewcreate_allcount=0,i._viewcreate_okcount=0,i._viewConfig=i.view,i.init(),i}return t=u,i=[{key:"resources",get:function(){return null}},{key:"view",get:function(){return null}},{key:"activateBase",value:function(){var e=this;if(this.isActivate)this.eachView((function(e){e._dom&&(Z(".layui-layer").each((function(){Z(this).css("z-index",19891e3)})),Z(e._dom).css("z-index",19891014))}));else{if(J.fire(k.beforeActivate,{sourceTarget:this}),this.beforeActivate(),this.isActivate=!0,!this.isCreate){if(J.fire(k.beforeCreate,{sourceTarget:this}),this.resources&&this.resources.length>0){for(var t=[],i=0;i<this.resources.length;i++){var n=this.resources[i];n=this._getUrl(n),-1===ie.indexOf(n)&&t.push(n)}return ie=ie.concat(t),void x(t,(function(){var t=e.create((function(){e._createWidgetView(),e.isCreate=!0}));if(J.fire(k.created,{sourceTarget:e}),!t){if(e.options.createAtStart)return e.options.createAtStart=!1,e.isActivate=!1,void(e.isCreate=!0);e._createWidgetView(),e.isCreate=!0}}))}var r=this.create((function(){e._createWidgetView(),this.isCreate=!0}));if(J.fire(k.created,{sourceTarget:this}),r)return;if(e.options.createAtStart)return e.options.createAtStart=!1,e.isActivate=!1,void(e.isCreate=!0);this.isCreate=!0}this._createWidgetView()}}},{key:"init",value:function(){}},{key:"create",value:function(){}},{key:"_createWidgetView",value:function(){var e=this._viewConfig;if(void 0===e||null==e)this._startActivate();else if(Array.isArray(e)){this._viewcreate_allcount=e.length,this._viewcreate_okcount=0;for(var t=0;t<e.length;t++)this.createItemView(e[t])}else this._viewcreate_allcount=1,this._viewcreate_okcount=0,this.createItemView(e)}},{key:"eachView",value:function(e,t){var i=this._viewConfig;if(void 0===i||null==i)return!1;if(Array.isArray(i)){var n=!1;if(null!=t)return e(i[t]);for(var r=0;r<i.length;r++)n=e(i[r]);return n}return e(i)}},{key:"createItemView",value:function(e){var t=this;switch(e.type){case"divwindow":this._openDivWindow(e);break;case"append":t.getHtml(this._getUrl(e.url),(function(i){t._appendView(e,i)}));break;case"custom":e.open(this._getUrl(e.url),(function(i){t.winCreateOK(e,i),J.fire(k.openView,{sourceTarget:t,view:e,dom:i}),t._viewcreate_okcount++,t._viewcreate_okcount>=t._viewcreate_allcount&&t._startActivate(i)}),this);break;default:this._openWindow(e)}}},{key:"_openWindow",value:function(e){var t=this,i=this._getUrl(e.url),r={type:2,content:[i,"no"],success:function(r,o){if(t.isActivate){e._layerIdx!==o&&(ee.close(e._layerIdx),e._layerIdx=o),e._layerOpening=!1,e._dom=r;var a,s=window[r.find("iframe")[0].name];s.map=t.map,s.mars3d=n,s.Cesium=n.Cesium,t.options.css&&Z("#layui-layer"+e._layerIdx).css(t.options.css),e.windowOptions.hasOwnProperty("show")&&!e.windowOptions.show&&Z(r).hide(),ee.setTop(r),t.winCreateOK(e,s),J.fire(k.openView,{sourceTarget:t,view:e,dom:r}),t._viewcreate_okcount++,t._viewcreate_okcount>=t._viewcreate_allcount&&t._startActivate(r),s&&s.initWidgetView?(null!==(a=t.config)&&void 0!==a&&a.style&&Z(s.document.body).addClass(t.config.style),s.initWidgetView(t)):n.Log.logError(i+"页面没有定义function initWidgetView(widget)方法，无法初始化widget页面!")}else ee.close(o)}};e._layerIdx&&e._layerIdx>0&&(ee.close(e._layerIdx),e._layerIdx=-1),e._layerOpening=!0,e._layerIdx=ee.open(this._getWinOpt(e,r))}},{key:"_openDivWindow",value:function(e){var t=this,i=this._getUrl(e.url);this.getHtml(i,(function(i){var n={type:1,content:i,success:function(i,n){t.isActivate?(e._layerIdx!==n&&(ee.close(e._layerIdx),e._layerIdx=n),e._layerOpening=!1,e._dom=i,e.windowOptions.hasOwnProperty("show")&&!e.windowOptions.show&&Z(i).hide(),ee.setTop(i),t.winCreateOK(e,i),J.fire(k.openView,{sourceTarget:t,view:e,dom:i}),t._viewcreate_okcount++,t._viewcreate_okcount>=t._viewcreate_allcount&&t._startActivate(i)):ee.close(n)}};e._layerOpening=!0,e._layerIdx=ee.open(t._getWinOpt(e,n))}))}},{key:"_getUrl",value:function(e){return(e=this.addCacheVersion(e)).startsWith("/")||e.startsWith(".")||e.startsWith("http")?e:this.path+e}},{key:"_getWinOpt",value:function(e,t){var i=o(o(o({},E()),e.windowOptions),this.options.windowOptions);e.windowOptions=i;var n=this,r=this._getWinSize(i),a=!1;return i.noTitle||(a=this.options.name||" ",this.options.icon&&(a='<i class="'+this.options.icon+'" ></i>&nbsp;'+a)),o(o(o({},{title:a,area:r.area,offset:r.offset,shade:0,maxmin:!1,beforeEnd:function(){n.beforeDisable()},end:function(){e._layerIdx=-1,e._dom=null,n.disableBase()},full:function(e){n.winFull(e)},min:function(e){n.winMin(e)},restore:function(e){n.winRestore(e)}}),i),t)}},{key:"_getWinSize",value:function(e){var t=this.bfb2Number(e.width,document.documentElement.clientWidth,e),i=this.bfb2Number(e.height,document.documentElement.clientHeight,e),n="",r=e.position;if(r)if("string"==typeof r)n=r;else if("object"===a(r)){var o,s;if(r.hasOwnProperty("top")&&null!=r.top&&(o=this.bfb2Number(r.top,document.documentElement.clientHeight,e)),r.hasOwnProperty("bottom")&&null!=r.bottom){e._hasresize=!0;var c=this.bfb2Number(r.bottom,document.documentElement.clientHeight,e);null!=o?i=document.documentElement.clientHeight-o-c:o=document.documentElement.clientHeight-i-c}if(r.hasOwnProperty("left")&&null!=r.left&&(s=this.bfb2Number(r.left,document.documentElement.clientWidth,e)),r.hasOwnProperty("right")&&null!=r.right){e._hasresize=!0;var u=this.bfb2Number(r.right,document.documentElement.clientWidth,e);null!=s?t=document.documentElement.clientWidth-s-u:s=document.documentElement.clientWidth-t-u}null!=o&&void 0!==o||(o=(document.documentElement.clientHeight-i)/2),null!=s&&void 0!==s||(s=(document.documentElement.clientWidth-t)/2),n=[o+"px",s+"px"]}return e.hasOwnProperty("minHeight")&&i<e.minHeight&&(e._hasresize=!0,i=e.minHeight),e.hasOwnProperty("maxHeight")&&i>e.maxHeight&&(e._hasresize=!0,i=e.maxHeight),e.hasOwnProperty("minWidth")&&t<e.minWidth&&(e._hasresize=!0,t=e.minWidth),e.hasOwnProperty("maxWidth")&&t>e.maxWidth&&(e._hasresize=!0,t=e.maxWidth),{area:t&&i?[t+"px",i+"px"]:t+"px",offset:n}}},{key:"indexResize",value:function(){if(this.isActivate){var e=this;this.eachView((function(t){if(t._layerIdx&&-1!==t._layerIdx&&t.windowOptions&&t.windowOptions._hasresize){var i=e._getWinSize(t.windowOptions),n={};Array.isArray(i.area)&&(i.area[0]&&(n.width=i.area[0]),i.area[1]&&(n.height=i.area[1])),Array.isArray(i.offset)&&(i.offset[1]&&(n.top=i.offset[0]),i.offset[1]&&(n.left=i.offset[1])),Z(t._dom).attr("myTopLeft",!0),ee.style(t._layerIdx,n),"divwindow"===t.type&&ee.iframeAuto(t._layerIdx)}}))}}},{key:"_appendView",value:function(e,t){e._dom=Z(t).appendTo(e.parent||"body"),this.options.css&&Z(e._dom).css(this.options.css),this.winCreateOK(e,t),this._viewcreate_okcount++,this._viewcreate_okcount>=this._viewcreate_allcount&&this._startActivate(t)}},{key:"winCreateOK",value:function(e,t){}},{key:"winFull",value:function(){}},{key:"winMin",value:function(){}},{key:"minView",value:function(){this.eachView((function(e){e._layerIdx&&ee.min(e._layerIdx,e)}))}},{key:"restoreView",value:function(){this.eachView((function(e){e._layerIdx&&ee.restore(e._layerIdx)}))}},{key:"fullView",value:function(){this.eachView((function(e){e._layerIdx&&ee.full(e._layerIdx,e)}))}},{key:"winRestore",value:function(){}},{key:"_startActivate",value:function(e){this.activate(e),J.fire(k.activated,{sourceTarget:this}),this.options.success&&(this.options.success(this),delete this.options.success),this.isActivate||this.disableBase()}},{key:"beforeActivate",value:function(){}},{key:"activate",value:function(){}},{key:"disableBase",value:function(){this.isActivate&&(this.isActivate=!1,this.beforeDisable(),J.fire(k.beforeDisable,{sourceTarget:this}),this.eachView((function(e){return e._layerIdx&&e._layerIdx>0?(ee.close(e._layerIdx),e._layerOpening||(e._layerIdx=-1),!0):("append"===e.type&&e._dom&&(e._dom.remove(),e._dom=null),"custom"===e.type&&e.close&&e.close(),!1)})),this.disable(),this.options.autoReset&&this.resetConfig(),J.fire(k.disabled,{sourceTarget:this}))}},{key:"beforeDisable",value:function(){}},{key:"disable",value:function(){}},{key:"bfb2Number",value:function(e,t,i){return"string"==typeof e&&-1!==e.indexOf("%")?(i._hasresize=!0,t*Number(e.replace("%",""))/100):e}},{key:"addCacheVersion",value:function(e){if(!e)return e;var t=$();return t&&(-1===e.indexOf("?")?e+="?cache="+t:-1===e.indexOf("cache="+t)&&(e+="&cache="+t)),e}},{key:"resetConfig",value:function(){if(this.options._firstConfigBak){var e=this.options._firstConfigBak;for(var t in e)"uri"!==t&&(this.options[t]=e[t])}}},{key:"setViewShow",value:function(e,t){this.eachView((function(t){t._layerIdx&&t._layerIdx>0?e?Z("#layui-layer"+t._layerIdx).show():Z("#layui-layer"+t._layerIdx).hide():"append"===t.type&&t._dom&&(e?Z(t._dom).show():Z(t._dom).hide())}),t)}},{key:"setViewCss",value:function(e,t){this.eachView((function(t){null!=t._layerIdx&&t._layerIdx>0?ee.style(t._layerIdx,e):"append"===t.type&&t._dom&&Z(t._dom).css(e)}),t)}},{key:"setTitle",value:function(e,t){this.eachView((function(t){t._dom&&t._dom.find(".layui-layer-title").html(e)}),t)}},{key:"getHtml",value:function(e,t){Z.ajax({url:e,type:"GET",dataType:"html",timeout:0,success:function(e){t(e)}})}}],i&&s(t.prototype,i),r&&s(t,r),Object.defineProperty(t,"prototype",{writable:!1}),u}(te);n.widget=Y,n.widget.BaseWidget=ne,n.widget.WidgetEventType=k,n.widget.EventType=k,e.widget=Y,Object.defineProperty(e,"__esModule",{value:!0})}));
