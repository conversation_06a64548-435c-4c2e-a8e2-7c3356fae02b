<template>
  <div class="realTimeMonitoring">
    <div class="realTimeMonitoring-header">
      <div class="select-time">
        <el-select v-model="dangerId"
                   size="small"
                   clearable
                   placeholder="全部危险源">
          <el-option v-for="item in option1"
                     :key="item.dangerId"
                     :label="item.dangerName"
                     :value="item.dangerId">
          </el-option>
        </el-select>
      </div>
      <div class="seach-btn">
        <el-button type="primary"
                   icon="el-icon-search"
                   size="small"
                   @click="search">查询</el-button>
      </div>
      <div class="seach-tab">
        <el-radio-group size="small"
                        v-model="mode"
                        style="position: absolute; right: 0; top: 0">
          <el-radio-button label="1">全部({{ total }})</el-radio-button>
          <el-radio-button label="2">报警({{ warning }})</el-radio-button>
          <el-radio-button label="3">离线({{ offLine }})</el-radio-button>
        </el-radio-group>
      </div>
    </div>
    <div class="realTimeMonitoring-box"
         v-loading="loading">
      <div class="realTimeMonitoring-item"
           v-for="(item, index) in allData"
           :key="index"
           v-if="item.tank.length > 0 || item.device.length > 0 || item.leak.length > 0">
        <h2>
          <span class="real-title">{{ item.dangerName }}({{ item.dangerId }})</span>
          <span class="real-leav"
                v-if="item.level == '1'">一级重大危险源</span>
          <span class="real-leav"
                v-else-if="item.level == '2'">二级重大危险源</span>
          <span class="real-leav"
                v-else-if="item.level == '3'">三级重大危险源</span>
          <span class="real-leav"
                v-else-if="item.level == '4'">四级重大危险源</span>
        </h2>
        <ul class="realTimeMonitoring-list"
            v-if="item.tank.length > 0">
          <li v-for="(itemes, indexes) in item.tank"
              :key="indexes">
            <p>
              {{ itemes.equipmentName }}
              <span class="realTimeMonitoring-time">{{ itemes.montime }}</span>
              <span class="realTimeMonitoring-look">
                <i class="realTimeMonitoring-look-icon"
                   @click="processBool(itemes)"></i>
              </span>
            </p>
            <div class="realTimeMonitoring-detail">
              <div class="realTimeMonitoring-icon">
                <i class="realTimeMonitoring-state"
                   v-if="itemes.typeTankBh == '1' || itemes.typeTankBh == '01'"></i>
                <i class="realTimeMonitoring-state-waifuding"
                   v-else-if="itemes.typeTankBh == '2'"></i>
                <i class="realTimeMonitoring-state-neifuding"
                   v-else-if="itemes.typeTankBh == '3'"></i>
                <i class="realTimeMonitoring-state-qiuguang"
                   v-else-if="itemes.typeTankBh == '4'"></i>
                <i class="realTimeMonitoring-state-woguang"
                   v-else-if="itemes.typeTankBh == '5'"></i>
                <i class="realTimeMonitoring-state-cucao"
                   v-else-if="
                    itemes.typeTankBh == '6' || itemes.typeTankBh == '10'
                  "></i>
                <div class="realTimeMonitoring-state-name-tingyong"
                     v-if="itemes.state == '0'">
                  停用
                </div>
                <div class="realTimeMonitoring-state-name"
                     v-else-if="itemes.state == '1'">
                  正常
                </div>
                <div class="realTimeMonitoring-state-name-baojing"
                     v-else-if="itemes.state == '2'">
                  报警
                </div>
                <div class="realTimeMonitoring-state-name-lixian"
                     v-else-if="itemes.state == '3'">
                  离线
                </div>
              </div>
              <div class="realTimeMonitoring-txt">
                <div class="realTimeMonitoring-detailLeft">
                  <p>
                    储罐类型:
                    <span>{{ itemes.typeTank ? itemes.typeTank : "--" }}
                    </span>
                  </p>
                  <p>
                    温度类型:
                    <span>{{ itemes.typeWD ? itemes.typeWD : "--" }}
                    </span>
                  </p>
                  <p>
                    压力类型:
                    <span>{{ itemes.typeYL ? itemes.typeYL : "--" }}
                    </span>
                  </p>
                  <p>
                    储存介质:
                    <span>{{ itemes.storageMedium ? itemes.storageMedium : "--" }}
                    </span>
                  </p>
                  <p>
                    介质形态:
                    <span>{{ itemes.mediumType ? itemes.mediumType : "--" }}
                    </span>
                  </p>
                </div>
                <div class="realTimeMonitoring-detailRight"
                     v-if="itemes.index.length > 0">
                  <el-tooltip placement="top"
                              :open-delay="500"
                              v-for="(itemed, indexed) in itemes.index"
                              :key="indexed">
                    <div slot="content">
                      监测指标：{{ itemed.indexName ? itemed.indexName : itemed.indexType }}
                      <br />一级上限：{{ itemed.up1 ? Number(itemed.up1).toFixed(2) : itemed.up1 == 0 ? Number(itemed.up1).toFixed(2) : "--" }}
                      <br />一级下限：{{ itemed.down1 ? Number(itemed.down1).toFixed(2) : itemed.down1 == 0 ? Number(itemed.down1).toFixed(2) : "--" }}
                      <br />二级上限：{{ itemed.up2 ? Number(itemed.up2).toFixed(2) : itemed.up2 == 0 ? Number(itemed.up2).toFixed(2) : "--" }}
                      <br />二级下限：{{ itemed.down2 ? Number(itemed.down2).toFixed(2) : itemed.down2 == 0 ? Number(itemed.down2).toFixed(2) : "--" }}
                      <br />量程上限：{{ itemed.rangeUp ? Number(itemed.rangeUp).toFixed(2) : itemed.rangeUp == 0 ? Number(itemed.rangeUp).toFixed(2) : "--" }}
                      <br />量程下限：{{ itemed.rangeDown ? Number(itemed.rangeDown).toFixed(2) : itemed.rangeDown == 0 ? Number(itemed.rangeDown).toFixed(2) : "--" }}
                      <br />
                    </div>
                    <p>
                      {{ wordlimit( itemed.indexName ? itemed.indexName : itemed.indexType, 5 ) }}:
                      <span v-if="itemed.isWarning == 1"
                            style="
                          display: inline-block;
                          padding: 0px 10px;
                          margin-left: 8px;
                          background: red;
                          height: 18px;
                          line-height: 18px;
                          color: #fff;
                          border-radius: 15px;
                        ">{{ itemed.reading ? itemed.reading : itemed.reading == 0 ? itemed.reading : "--" }}{{ itemed.unit }}</span>
                      <span v-else>{{ itemed.reading ? itemed.reading : itemed.reading == 0 ? itemed.reading : "--" }}{{ itemed.unit }}</span>
                    </p>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </li>
        </ul>
        <ul class="realTimeMonitoring-list"
            v-if="item.device.length > 0">
          <li v-for="(itemes, indexes) in item.device"
              :key="indexes">
            <p>
              {{ itemes.equipmentName }}
              <span class="realTimeMonitoring-time">{{ itemes.montime }}</span>
              <span class="realTimeMonitoring-look">
                <i class="realTimeMonitoring-look-icon"
                   @click="processBool(itemes)"></i>
              </span>
            </p>
            <div class="realTimeMonitoring-detail">
              <div class="realTimeMonitoring-icon">
                <i class="realTimeMonitoring-state-zhuangzhi"></i>
                <div class="realTimeMonitoring-state-name-tingyong"
                     v-if="itemes.state == '0'">
                  停用
                </div>
                <div class="realTimeMonitoring-state-name"
                     v-else-if="itemes.state == '1'">
                  正常
                </div>
                <div class="realTimeMonitoring-state-name-baojing"
                     v-else-if="itemes.state == '2'">
                  报警
                </div>
                <div class="realTimeMonitoring-state-name-lixian"
                     v-else-if="itemes.state == '3'">
                  离线
                </div>
              </div>
              <div class="realTimeMonitoring-txt">
                <div class="realTimeMonitoring-detailLeft">
                  <p>
                    总回路数(回):
                    <span>{{ itemes.circuitsNum ? itemes.circuitsNum : itemes.circuitsNum == 0 ? itemes.circuitsNum : "--" }}
                    </span>
                  </p>
                </div>
                <div class="realTimeMonitoring-detailRight"
                     v-if="itemes.index.length > 0">
                  <el-tooltip placement="top"
                              :open-delay="500"
                              v-for="(itemed, indexed) in itemes.index"
                              :key="indexed">
                    <div slot="content">
                      监测指标：{{ itemed.indexName ? itemed.indexName : itemed.indexType }}
                      <br />一级上限：{{ itemed.up1 ? Number(itemed.up1).toFixed(2) : itemed.up1 == 0 ? Number(itemed.up1).toFixed(2) : "--" }}
                      <br />一级下限：{{ itemed.down1 ? Number(itemed.down1).toFixed(2) : itemed.down1 == 0 ? Number(itemed.down1).toFixed(2) : "--" }}
                      <br />二级上限：{{ itemed.up2 ? Number(itemed.up2).toFixed(2) : itemed.up2 == 0 ? Number(itemed.up2).toFixed(2) : "--" }}
                      <br />二级下限：{{ itemed.down2 ? Number(itemed.down2).toFixed(2) : itemed.down2 == 0 ? Number(itemed.down2).toFixed(2) : "--" }}
                      <br />量程上限：{{ itemed.rangeUp ? Number(itemed.rangeUp).toFixed(2) : itemed.rangeUp == 0 ? Number(itemed.rangeUp).toFixed(2) : "--" }}
                      <br />量程下限：{{ itemed.rangeDown ? Number(itemed.rangeDown).toFixed(2) : itemed.rangeDown == 0 ? Number(itemed.rangeDown).toFixed(2) : "--" }}
                      <br />
                    </div>
                    <p>
                      {{ wordlimit( itemed.indexName ? itemed.indexName : itemed.indexType, 5 ) }}:
                      <span v-if="itemed.isWarning == 1"
                            style="
                          display: inline-block;
                          padding: 0px 10px;
                          margin-left: 8px;
                          background: red;
                          height: 18px;
                          line-height: 18px;
                          color: #fff;
                          border-radius: 15px;
                        ">{{ itemed.reading ? itemed.reading : itemed.reading == 0 ? itemed.reading : "--" }}{{ itemed.unit }}</span>
                      <span v-else>{{ itemed.reading ? itemed.reading : itemed.reading == 0 ? itemed.reading : "--" }}{{ itemed.unit }}</span>
                    </p>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </li>
        </ul>
        <ul class="realTimeMonitoring-list"
            v-if="item.leak.length > 0">
          <li v-for="(itemes, indexes) in item.leak"
              :key="indexes">
            <p>
              {{ itemes.equipmentName }}
              <span class="realTimeMonitoring-time">{{ itemes.montime }}</span>
              <span class="realTimeMonitoring-look">
                <i class="realTimeMonitoring-look-icon"
                   @click="processBool(itemes)"></i>
              </span>
            </p>
            <div class="realTimeMonitoring-detail">
              <div class="realTimeMonitoring-icon1">
                <i class="realTimeMonitoring-state"></i>
                <div class="realTimeMonitoring-state-name-tingyong"
                     v-if="itemes.state == '0'">
                  停用
                </div>
                <div class="realTimeMonitoring-state-name"
                     v-else-if="itemes.state == '1'">
                  正常
                </div>
                <div class="realTimeMonitoring-state-name-baojing"
                     v-else-if="itemes.state == '2'">
                  报警
                </div>
                <div class="realTimeMonitoring-state-name-lixian"
                     v-else-if="itemes.state == '3'">
                  离线
                </div>
              </div>
              <div class="realTimeMonitoring-txt"
                   style="height: 88px; overflow: hidden">
                <div class="realTimeMonitoring-detailLeftes">
                  <p style="color: #000; font-weight: bold">
                    {{ itemes.monitoringMedium }}
                  </p>
                  <!-- <p>
                    气体浓度:<span
                      >{{ itemes.index[0].reading
                      }}{{ itemes.index[0].unit }}</span
                    >
                  </p> -->
                  <p class="realTimeMonitoring-nongdu">
                    <el-tooltip placement="top"
                                :open-delay="500"
                                v-for="(itemed, indexed) in itemes.index"
                                :key="indexed">
                      <div slot="content">
                        监测指标：{{ itemed.indexName ? itemed.indexName : itemed.indexType }}
                        <br />一级上限：{{ itemed.up1 ? Number(itemed.up1).toFixed(2) : itemed.up1 == 0 ? Number(itemed.up1).toFixed(2) : "--" }}
                        <br />一级下限：{{ itemed.down1 ? Number(itemed.down1).toFixed(2) : itemed.down1 == 0 ? Number(itemed.down1).toFixed(2) : "--" }}
                        <br />二级上限：{{ itemed.up2 ? Number(itemed.up2).toFixed(2) : itemed.up2 == 0 ? Number(itemed.up2).toFixed(2) : "--" }}
                        <br />二级下限：{{ itemed.down2 ? Number(itemed.down2).toFixed(2) : itemed.down2 == 0 ? Number(itemed.down2).toFixed(2) : "--" }}
                        <br />量程上限：{{ itemed.rangeUp ? Number(itemed.rangeUp).toFixed(2) : itemed.rangeUp == 0 ? Number(itemed.rangeUp).toFixed(2) : "--" }}
                        <br />量程下限：{{ itemed.rangeDown ? Number(itemed.rangeDown).toFixed(2) : itemed.rangeDown == 0 ? Number(itemed.rangeDown).toFixed(2) : "--" }}
                        <br />
                      </div>
                      <p>
                        气体浓度:
                        <span v-if="itemed.isWarning == 1"
                              style="
                            display: inline-block;
                            padding: 0px 10px;
                            margin-left: 8px;
                            background: red;
                            height: 18px;
                            line-height: 18px;
                            color: #fff;
                            border-radius: 15px;
                          ">{{ itemed.reading ? itemed.reading : itemed.reading == 0 ? itemed.reading : "--" }}{{ itemed.unit }}</span>
                        <span v-else>{{ itemed.reading ? itemed.reading : itemed.reading == 0 ? itemed.reading : "--" }}{{ itemed.unit }}</span>
                      </p>
                    </el-tooltip>
                  </p>
                </div>
              </div>
            </div>
          </li>
        </ul>
      </div>
      <div class="nodata"
           v-if="showNodata">
        <img style="margin: 0 auto; display: block; margin-top: 30px"
             src="/static/img/assets/img/noData.png" />
      </div>

      <!-- <div class="realTimeMonitoring-item">
              <h2><span class="real-title">甲醇罐区（420610062001）</span><span class="real-leav">二级重大危险源</span></h2>
              <ul class="realTimeMonitoring-list">
                 <li>
                     <p>MTBE储罐3740-T-003A(MTBE)<span class="realTimeMonitoring-time">2020-07-28 11:36</span><span class="realTimeMonitoring-look"><i class="realTimeMonitoring-look-icon" @click="processBool"></i></span></p>
                     <div class="realTimeMonitoring-detail">
                         <div class="realTimeMonitoring-icon">
                             <i class="realTimeMonitoring-state"></i>
                             <div class="realTimeMonitoring-state-name">正常</div>
                         </div>
                         <div class="realTimeMonitoring-txt">
                            <div class="realTimeMonitoring-detailLeft">
                                <p>储罐类型:<span>固定罐</span></p>
                                <p>温度类型:<span>常温罐</span></p>
                                <p>压力类型:<span>压力罐</span></p>
                                <p>储存介质:<span>环氧乙烷</span></p>
                                <p>介质形态:<span>液态</span></p>
                            </div>
                            <div class="realTimeMonitoring-detailRight">
                                <el-tooltip placement="top" open-delay="500">
                                    <div slot="content">监测指标：温度<br/>一级上限：1<br/>一级下限：2<br/>二级上限：3<br/>二级下限：4<br/>量程上限：5<br/>量程下限：6<br/></div>
                                    <p>温度（°C）:<span>27.55</span></p>
                                </el-tooltip>
                                <el-tooltip placement="top" open-delay="500">
                                    <div slot="content">监测指标：压力<br/>一级上限：7<br/>一级下限：8<br/>二级上限：9<br/>二级下限：10<br/>量程上限：11<br/>量程下限：12<br/></div>
                                    <p>压力（MPa）:<span>80</span></p>
                                </el-tooltip>
                                <el-tooltip placement="top" open-delay="500">
                                    <div slot="content">监测指标：磁翻板液位计液位<br/>一级上限：13<br/>一级下限：14<br/>二级上限：15<br/>二级下限：16<br/>量程上限：17<br/>量程下限：18<br/></div>
                                    <p>磁翻板液位计液位:<span>78%</span></p>
                                </el-tooltip>

                                <p>液位:<span>84%</span></p>
                                <p>温度（°C）:<span>27.55</span></p>
                                <p>压力（MPa）:<span>80</span></p>
                                <p>磁翻板液位计液位:<span>78%</span></p>
                                <p>液位:<span>84%</span></p>
                            </div>
                         </div>
                     </div>
                 </li>
                 <li>
                     <p>MTBE储罐3740-T-003A(MTBE)<span class="realTimeMonitoring-time">2020-07-28 11:36</span><span class="realTimeMonitoring-look"><i class="realTimeMonitoring-look-icon" @click="processBool"></i></span></p>
                     <div class="realTimeMonitoring-detail">
                         <div class="realTimeMonitoring-icon">
                             <i class="realTimeMonitoring-state-cucao"></i>
                             <div class="realTimeMonitoring-state-name-tingyong">停用</div>
                         </div>
                         <div class="realTimeMonitoring-txt">
                             <div class="realTimeMonitoring-detailLeft">
                                <p>储罐类型:<span>固定罐</span></p>
                                <p>温度类型:<span>常温罐</span></p>
                                <p>压力类型:<span>压力罐</span></p>
                                <p>储存介质:<span>环氧乙烷</span></p>
                                <p>储存介质:<span>环氧乙烷</span></p>
                            </div>
                            <div class="realTimeMonitoring-detailRight">
                                <p>温度（°C）:<span>27.55</span></p>
                                <p>压力（MPa）:<span>80</span></p>
                                <p>磁翻板液位计液位:<span>78%</span></p>
                                <p>液位:<span>84%</span></p>
                            </div>
                         </div>
                     </div>
                 </li>
                 <li>
                     <p>MTBE储罐3740-T-003A(MTBE)<span class="realTimeMonitoring-time">2020-07-28 11:36</span><span class="realTimeMonitoring-look"><i class="realTimeMonitoring-look-icon" @click="processBool"></i></span></p>
                     <div class="realTimeMonitoring-detail">
                         <div class="realTimeMonitoring-icon">
                             <i class="realTimeMonitoring-state-qiuguang"></i>
                             <div class="realTimeMonitoring-state-name-lixian">离线</div>
                         </div>
                         <div class="realTimeMonitoring-txt">
                            <div class="realTimeMonitoring-detailLeft">
                                <p>储罐类型:<span>固定罐</span></p>
                                <p>温度类型:<span>常温罐</span></p>
                                <p>压力类型:<span>压力罐</span></p>
                                <p>储存介质:<span>环氧乙烷</span></p>
                            </div>
                            <div class="realTimeMonitoring-detailRight">
                                <p>温度（°C）:<span>27.55</span></p>
                                <p>压力（MPa）:<span>80</span></p>
                                <p>磁翻板液位计液位:<span>78%</span></p>
                                <p>液位:<span>84%</span></p>
                                <p>温度（°C）:<span>27.55</span></p>
                                <p>压力（MPa）:<span>80</span></p>
                                <p>磁翻板液位计液位:<span>78%</span></p>
                                <p>液位:<span>84%</span></p>
                            </div>
                         </div>
                     </div>
                 </li>
                 <li>
                     <p>MTBE储罐3740-T-003A(MTBE)<span class="realTimeMonitoring-time">2020-07-28 11:36</span><span class="realTimeMonitoring-look"><i class="realTimeMonitoring-look-icon" @click="processBool"></i></span></p>
                     <div class="realTimeMonitoring-detail">
                         <div class="realTimeMonitoring-icon1">
                             <i class="realTimeMonitoring-state"></i>
                             <div class="realTimeMonitoring-state-name">报警</div>
                         </div>
                         <div class="realTimeMonitoring-txt" style="height:88px;overflow: hidden;">
                            <div class="realTimeMonitoring-detailLeftes">
                                <p>储罐类型:<span>固定罐</span></p>
                                <p>温度类型:<span>常温罐</span></p>
                            </div>
                         </div>
                     </div>
                 </li>
                 <li>
                     <p>MTBE储罐3740-T-003A(MTBE)<span class="realTimeMonitoring-time">2020-07-28 11:36</span><span class="realTimeMonitoring-look"><i class="realTimeMonitoring-look-icon" @click="processBool"></i></span></p>
                     <div class="realTimeMonitoring-detail">
                         <div class="realTimeMonitoring-icon1">
                             <i class="realTimeMonitoring-state"></i>
                             <div class="realTimeMonitoring-state-name">报警</div>
                         </div>
                         <div class="realTimeMonitoring-txt" style="height:88px;overflow: hidden;">
                            <div class="realTimeMonitoring-detailLeftes">
                                <p>储罐类型:<span>固定罐</span></p>
                                <p>温度类型:<span>78%</span></p>
                            </div>
                         </div>
                     </div>
                 </li>
              </ul>
          </div> -->
    </div>
    <Process :processBoolean="processBoolean"
             @close="processClose"
             ref="processChart"></Process>
  </div>
</template>

<script>
// const echarts = require('echarts');
import { getSelectData, getRealtimeData, getStateArr } from '@/api/entList'
import Process from './process'
export default {
  //import引入的组件
  name: 'realTimeMonitoring',
  components: {
    Process
  },
  data() {
    return {
      processBoolean: false,
      dangerId: '',
      enterpriseId: '',
      option1: [],
      mode: '1',
      sign: '',
      allData: [],
      total: '',
      offLine: '',
      warning: '',
      loading: false,
      showNodata: true
    }
  },
  //方法集合
  methods: {
    processBool(item) {
      this.processBoolean = true
      this.$refs.processChart.getTime()
      this.$refs.processChart.getSelectData(item)
    },
    processClose(close) {
      this.processBoolean = close
    },
    search() {
      this.currentPage = 1
      this.getData(this.enterpriseId)
    },
    wordlimit(cname, wordlength) {
      var nowLength = cname.length
      if (nowLength > wordlength) {
        // cname[i].innerHTML = cname[i].innerHTML.substr(0, wordlength) + '...';
        return cname.substr(0, wordlength) + '...'
      } else {
        return cname
      }
    },
    getSelectData(enterpriseId) {
      this.enterpriseId = enterpriseId
      getSelectData({
        enterpId: this.enterpriseId
      }).then(res => {
        if (res.data.code == 0) {
          this.option1 = res.data.data
        }
      })
    },
    getData(enterpriseId) {
      this.loading = true
      this.enterpriseId = enterpriseId
      getRealtimeData({
        enterpId: this.enterpriseId,
        sign: this.sign,
        dangerId: this.dangerId
      }).then(res => {
        if (res.data.code == 0) {
          this.loading = false
          this.allData = res.data.data
          this.showNodata = true
          this.allData.forEach(item => {
            if (
              item.tank.length > 0 ||
              item.device.length > 0 ||
              item.leak.length > 0
            ) {
              this.showNodata = false
              return
            }
          })
        }
      })
    },
    getStateData(enterpriseId) {
      this.enterpriseId = enterpriseId
      getStateArr({
        enterpId: this.enterpriseId
      }).then(res => {
        if (res.data.code == 0) {
          this.total = res.data.data.total
          this.offLine = res.data.data.offLine
          this.warning = res.data.data.warning
        }
      })
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {
    mode(newValue, oldValue) {
      if (newValue == '1') {
        this.sign = ''
      } else if (newValue == '2') {
        this.sign = 1
      } else if (newValue == '3') {
        this.sign = 2
      }
      this.getData(this.enterpriseId)
    }
  }
}
</script>
<style lang="scss" scoped>
.realTimeMonitoring {
  .realTimeMonitoring-header {
    position: relative;
    .select-time {
      display: inline-block;
    }
    .seach-btn {
      display: inline-block;
    }
    .seach-tab {
      display: inline-block;
    }
  }
  .realTimeMonitoring-box {
    min-height: 400px;
    .realTimeMonitoring-item {
      overflow: hidden;
      > h2 {
        color: #000;
        font-size: 14px;
        background: url('/static/img/assets/img/title-bg.png') no-repeat center
          center;
        //  background-size: cover;
        background-size: 100%;
        height: 52px;
        width: 100%;
        margin: 0;
        position: relative;
        .real-title {
          position: absolute;
          left: 15px;
          top: 20px;
        }
        .real-leav {
          position: absolute;
          right: 2.5%;
          font-size: 12px;
          top: 13px;
          color: #fff;
          display: inline-block;
          width: 8%;
          text-align: center;
          height: 20px;
        }
      }
      .realTimeMonitoring-list {
        //    display: flex;
        //    justify-content:space-between;
        overflow: hidden;
        padding-left: 0;
        li {
          width: 32.6%;
          float: left;
          list-style-type: none;
          border: 1px solid #d8e0ee;
          box-shadow: 0px 0px 8px 0px rgba(121, 163, 241, 0.3);
          margin-top: 15px;
          border-radius: 4px;
          > p {
            background: #f1f6ff;
            height: 40px;
            line-height: 40px;
            padding: 0 10px;
            font-size: 13px;
            font-weight: bold;
            position: relative;
            .realTimeMonitoring-time {
              position: absolute;
              right: 40px;
              font-weight: lighter;
              font-size: 11px;
            }
            .realTimeMonitoring-look {
              position: absolute;
              right: 10px;
              top: 6px;
              .realTimeMonitoring-look-icon {
                display: inline-block;
                width: 23px;
                height: 23px;
                background: url('/static/img/assets/img/qushi.png');
              }
            }
          }
          .realTimeMonitoring-detail {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
            padding: 0 10px 15px 10px;
            .realTimeMonitoring-icon {
              width: 89px;
              margin-right: 15px;
              height: 102px;
              text-align: center;
              background: url('/static/img/assets/img/icon-box1.png') no-repeat;
              background-size: contain;
              .realTimeMonitoring-state {
                display: inline-block;
                margin-top: 11px;
                width: 50px;
                height: 64px;
                background: url('/static/img/assets/img/gudingguan.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-cucao {
                display: inline-block;
                margin-top: 7px;
                width: 45px;
                height: 70px;
                background: url('/static/img/assets/img/cucao.png') no-repeat;
              }
              .realTimeMonitoring-state-qiuguang {
                display: inline-block;
                margin-top: 11px;
                width: 58px;
                height: 66px;
                background: url('/static/img/assets/img/qiuguang.png') no-repeat;
              }
              .realTimeMonitoring-state-waifuding {
                display: inline-block;
                margin-top: 11px;
                width: 51px;
                height: 59px;
                background: url('/static/img/assets/img/waifuding.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-neifuding {
                display: inline-block;
                margin-top: 11px;
                width: 53px;
                height: 63px;
                background: url('/static/img/assets/img/neifuding.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-woguang {
                display: inline-block;
                margin-top: 11px;
                width: 64px;
                height: 51px;
                background: url('/static/img/assets/img/woguang.png') no-repeat;
              }
              .realTimeMonitoring-state-zhuangzhi {
                display: inline-block;
                margin-top: 15px;
                width: 54px;
                height: 56px;
                background: url('/static/img/assets/img/zhuangzhi.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-name {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -5px;
                font-weight: bold;
                background: url('/static/img/assets/img/zhengchang.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-name-tingyong {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -10px;
                font-weight: bold;
                background: url('/static/img/assets/img/tingyong.png') no-repeat;
              }
              .realTimeMonitoring-state-name-lixian {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -6px;
                font-weight: bold;
                background: url('/static/img/assets/img/lixian.png') no-repeat;
              }
              .realTimeMonitoring-state-name-baojing {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -6px;
                font-weight: bold;
                background: url('/static/img/assets/img/baojing.png') no-repeat;
              }
            }
            .realTimeMonitoring-txt {
              width: calc(100% - 95px);
              $width: calc(100% - 95px);
              height: 102px;
              overflow-x: hidden;
              overflow-y: hidden;
              //   padding-right: calc(100vw - 100% + 1rem);
              &:hover {
                overflow-y: auto;
                overflow-y: overlay;
              }
              .realTimeMonitoring-detailLeft {
                width: 45%;
                padding: 0 10px;
                float: left;
                p {
                  position: relative;
                  color: #5e86b4;
                  margin-bottom: 0;
                  line-height: 25px;
                  white-space: nowrap;
                  span {
                    color: #000;
                  }
                }
              }
              .realTimeMonitoring-detailLeftes {
                width: 100%;
                padding: 7px 10px;
                .realTimeMonitoring-nongdu:hover {
                  height: 63px;
                  overflow-y: scroll;
                }
                p {
                  position: relative;
                  color: #5e86b4;
                  margin-bottom: 0;
                  line-height: 25px;
                  white-space: nowrap;
                  span {
                    color: #000;
                  }
                }
              }
              .realTimeMonitoring-detailRight {
                width: 50%;
                padding: 0 10px 0 0;
                float: left;
                p {
                  border-left: 1px solid #e5edff;
                  padding-left: 10px;
                  position: relative;
                  color: #5e86b4;
                  margin-bottom: 0;
                  line-height: 25px;
                  white-space: nowrap;
                  span {
                    color: #000;
                  }
                }
              }
            }
            .realTimeMonitoring-icon1 {
              width: 89px;
              margin-right: 15px;
              height: 89px;
              text-align: center;
              background: url('/static/img/assets/img/icon-box2.png') no-repeat;
              background-size: contain;
              .realTimeMonitoring-state {
                display: inline-block;
                margin-top: 6px;
                width: 54px;
                height: 56px;
                background: url('/static/img/assets/img/xieloudian.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-name {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -5px;
                font-weight: bold;
                background: url('/static/img/assets/img/zhengchang.png')
                  no-repeat;
              }
              .realTimeMonitoring-state-name-tingyong {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -10px;
                font-weight: bold;
                background: url('/static/img/assets/img/tingyong.png') no-repeat;
              }
              .realTimeMonitoring-state-name-lixian {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -6px;
                font-weight: bold;
                background: url('/static/img/assets/img/lixian.png') no-repeat;
              }
              .realTimeMonitoring-state-name-baojing {
                width: 61px;
                height: 19px;
                line-height: 19px;
                margin: 0 auto;
                padding-left: 22px;
                color: #fff;
                font-size: 12px;
                margin-top: -6px;
                font-weight: bold;
                background: url('/static/img/assets/img/baojing.png') no-repeat;
              }
            }
          }
        }
        li:nth-child(3n-1) {
          margin: 15px 1% 0 1%;
        }
      }
    }
  }
}
</style>
<style>
/* .el-tooltip__popper.is-dark {
  background: #122447;
  box-shadow: 0px 5px 20px 0px rgba(42, 60, 114, 0.43);
  opacity: 0.6;
  border-radius: 3px;
  border: 1px solid #303133;
} */
</style>
