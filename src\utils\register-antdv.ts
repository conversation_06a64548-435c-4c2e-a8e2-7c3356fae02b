import Vue from "vue";
//导入AntdUI
import {
  <PERSON><PERSON><PERSON><PERSON>b,
  <PERSON>u,
  Layout,
  Icon,
  ConfigProvider,
  Tree,
  Collapse,
} from "ant-design-vue";
import "ant-design-vue/dist/antd.css";

const components: any = [
  ConfigProvider,
  Breadcrumb,
  Breadcrumb.Item,
  Breadcrumb.Separator,
  Menu,
  Menu.Item,
  Menu.ItemGroup,
  Menu.SubMenu,
  Menu.Divider,
  Layout,
  Layout.Header,
  Layout.Sider,
  Layout.Content,
  Tree,
  Tree.DirectoryTree,
  Tree.TreeNode,
  Icon,
  Collapse,
  Collapse.Panel,
];

export default function AntdV(): void {
  for (const component of components) {
    Vue.component(component.name, component);
  }
}
