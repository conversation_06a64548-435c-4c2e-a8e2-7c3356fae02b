<template>
  <div class="enterpriseManagement">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box"
                    @click="goToRunning">
                <a-icon type="home"
                        theme="filled"
                        class="icon" /> 承包商管理
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="main-center">
        <EquipmentFacilities ref="equipmentFacilities"
                             :entInfoData="entInfoData"></EquipmentFacilities>
      </div>
    </div>
  </div>
</template>
<script>
import { getEnt } from '@/api/dailySafety'
import EquipmentFacilities from './equipmentFacilities'
// import VideoAbnormal from "./videoAbnormal";
import { createNamespacedHelpers } from 'vuex'
const { mapState: mapStateLogin } = createNamespacedHelpers('login')
export default {
  name: 'safetyTraining',
  components: {
    EquipmentFacilities
    // VideoAbnormal,
  },
  data() {
    return {
      activeName: this.$route.query.enterpId || 'equipmentFacilities',
      entInfoData: this.$store.state.login.enterData
    }
  },
  methods: {
    goToRunning() {
      this.$router.push({
        path: `/dailySafetyManagement/contractorManagement`
      })
    },
    handleClick(tab, event) {
      try {
        var enterpId = this.enterData.enterpId
      } catch (error) {
        var enterpId = ''
      }
      //   if (tab.name == "videoAbnormal") {
      //     this.$refs.videoAbnormal.getIotMontoringDataList(enterpId);
      //   } else if (tab.name == "equipmentFacilities") {
      //     this.$refs.equipmentFacilities.getIotMontoringDataList(enterpId);
      //   }
    },
    getEntId() {
      try {
        var enterpId = this.enterData.enterpId
      } catch (error) {
        var enterpId = ''
      }
      // console.log(enterpId);
      //   this.$nextTick(() => {
      //     this.$refs.equipmentFacilities.getIotMontoringDataList(enterpId);
      //     // this.$refs.videoAbnormal.getIotMontoringDataList(enterpId);
      //   });
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getEntId()
  },
  computed: {
    ...mapStateLogin({
      enterData: state => state.enterData
    })
  }
}
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
}
/deep/ .el-tabs__header {
  margin: 0;
}
// .main-center /deep/.el-tabs__nav-wrap::after {
//       position: static !important;
//   }
</style>
