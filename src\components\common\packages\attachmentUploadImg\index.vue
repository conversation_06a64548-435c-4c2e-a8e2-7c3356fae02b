<template>
  <div>
    <div v-if="!editabled || uploadList.length > 0">
      <div class="font"></div>
      <!-- upload: uploadList.length >= this.limit上传文件达到限制文件数隐藏上传按钮 -->
      <el-upload class="imgUploadWrap"
                 :class="{ upload: uploadList.length >= limitFile }"
                 :limit="limitFile"
                 :on-exceed="exceed"
                 :on-preview="handlePictureCardPreview"
                 :file-list="uploadList"
                 :before-upload="beforeFileName"
                 :action="uploadAddress"
                 :on-error="showError"
                 :headers="header"
                 list-type="picture-card"
                 :on-remove="fileRemove"
                 :on-success="uploadSuccess"
                 :disabled="editabled"
                 v-if="!editabled">
        <i class="el-icon-upload"></i>
        <div class="el-upload--picture-tips-word">
          限制上传{{ fileTips[type] || "图片,EXCEL,WORD,PDF,文本类,音视频文件，OFD文件" }}，文件大小不能超过50M，且只能上传{{ limit || 5 }}个文件
        </div>
        <div class="slot_img"
             v-if="
            !/(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/i.test(
              scope.file.name
            )
          "
             v-loading="scope.file.status === 'uploading'"
             element-loading-background="rgba(255, 253, 253, 0.8)"
             element-loading-text="正在上传"
             :title="scope.file.name"
             slot="file"
             slot-scope="scope">
          <img v-if="/(.*)\.(ppt|pptx)$/i.test(scope.file.name)"
               :src="require('./ppt.png')"
               alt />
          <img v-else-if="/(.*)\.(doc|docx)$/i.test(scope.file.name)"
               :src="require('./word.png')"
               alt />
          <img v-else-if="/(.*)\.(xlsx|xls)$/i.test(scope.file.name)"
               :src="require('./excel.png')"
               alt />
          <img v-else-if="/(.*)\.(txt)$/i.test(scope.file.name)"
               :src="require('./txt.png')"
               alt />
          <img v-else-if="/(.*)\.(pdf)$/i.test(scope.file.name)"
               :src="require('./PDF.png')"
               alt />
          <img v-else-if="
              /(.*)\.(mp4|avi|mp3|mov|rmvb|rm|3gp|flv)$/i.test(scope.file.name)
            "
               :src="require('./video.png')"
               alt />
          <img v-else
               :src="require('./file.png')"
               alt />
          <span class="slot_name">{{ scope.file.name }}</span>
          <i class="el-icon-close"></i>
          <span class="el-upload-list__item-actions">
            <span v-if="
                /(.*)\.(mp4|avi|mp3|mov|rmvb|rm|3gp|flv)$/i.test(
                  scope.file.name
                ) || type === 'word'
              "
                  @click="handlePictureCardPreview(scope.file, scope)">
              <span class="el-upload-list__item-preview">
                <i class="el-icon-zoom-in"></i>
              </span>
            </span>
            <span v-else
                  @click="download(scope.file)">
              <span class="el-upload-list__item-preview">
                <i class="el-icon-download"></i>
              </span>
            </span>
            <span v-if="!editabled"
                  @click="handleRemove(scope.file)"
                  class="el-upload-list__item-delete">
              <i class="el-icon-delete"></i>
            </span>
          </span>
        </div>
      </el-upload>
      <div v-else
           style="display: flex; align-items: center; justify-content: flex-start">
        <div class="slot_file"
             :title="file.name"
             v-for="(file, index) in attachmentlist"
             :key="index">
          <div class="backgroundImg">
            <img :src="file.url"
                 alt=""
                 v-if="
                /(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/i.test(
                  file.name
                )
              " />
            <img v-else-if="/(.*)\.(ppt|pptx)$/i.test(file.name)"
                 :src="require('./ppt.png')"
                 alt />
            <img v-else-if="/(.*)\.(doc|docx)$/i.test(file.name)"
                 :src="require('./word.png')"
                 alt />
            <img v-else-if="/(.*)\.(xlsx|xls)$/i.test(file.name)"
                 :src="require('./excel.png')"
                 alt />
            <img v-else-if="/(.*)\.(txt)$/i.test(file.name)"
                 :src="require('./txt.png')"
                 alt />
            <img v-else-if="/(.*)\.(pdf)$/i.test(file.name)"
                 :src="require('./PDF.png')"
                 alt />
            <img v-else-if="
                /(.*)\.(mp4|avi|mp3|mov|rmvb|rm|3gp|flv)$/i.test(file.name)
              "
                 :src="require('./video.png')"
                 alt />
            <img v-else
                 :src="require('./file.png')"
                 alt />
            <div class="slot_name"
                 v-if="
                !/(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/i.test(
                  file.name
                )
              ">
              <i class="el-icon-tickets"
                 style="margin-right: 0px; color: #909399"></i>
              <span>{{ file.name }}</span>
            </div>
            <div class="el-upload-list__item-hover">
              <span class="el-upload-list__item-preview"
                    v-if="
                  /(.*)\.(jpg|bmp|gif|ico|pcx|jpeg|tif|png|raw|tga)$/i.test(
                    file.name
                  )
                "
                    @click="handlePictureCardPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span class="el-upload-list__item-preview"
                    @click="download(file)">
                <i class="el-icon-download"></i>
              </span>
            </div>
          </div>
        </div>
      </div>
      <el-dialog :title="'查看图片'"
                 :visible.sync="dialogVisible"
                 :destroy-on-close="true"
                 :close-on-click-modal="false"
                 append-to-body>
        <el-image style="height: 60vh; width: 100%"
                  fit="scale-down"
                  :src="dialogImageUrl"
                  :preview-src-list="[dialogImageUrl]"
                  alt></el-image>
      </el-dialog>

      <el-dialog :title="'查看视频'"
                 :visible.sync="dialogVisibleVideo"
                 :close-on-click-modal="false"
                 :destroy-on-close="true"
                 append-to-body>
        <video controls
               autoplay
               style="height: 30rem; width: 100%">
          <source :src="dialogImageUrl"
                  type="video/mp4" />
        </video>
      </el-dialog>

      <el-dialog :title="'查看音频'"
                 :visible.sync="dialogVisibleAudio"
                 :destroy-on-close="true"
                 :close-on-click-modal="false"
                 append-to-body>
        <audio :src="dialogImageUrl"
               controls
               preload>
          您的浏览器不支持 audio 标签。
        </audio>
      </el-dialog>
    </div>
    <div v-else
         style="width: 100%; text-align: center">暂无数据</div>
  </div>
</template>

<script lang="ts">
import {
  Component,
  Vue,
  Prop,
  Emit,
  Watch,
  Model
} from 'vue-property-decorator'
import Options from 'vue-class-component'
import downloadFuc, { Attachmentdownload } from '@/api/download/download.js'
import { mapState } from 'vuex'
@Component({
  name: 'CAAttachmentUpload',
  computed: {
    ...mapState({
      user: (state: any) => state.login.user
    })
  }
})
// @Options({
//
// })
export default class AttachmentUpload extends Vue {
  @Prop() private type: any // 附件类型
  @Prop() private flieid: any // 附件类型
  @Prop() private attachmentlist!: any // 已上传附件数组
  @Prop() private limit: any // 限制上传数量
  @Prop() private editabled: any // 附件是否可编辑  true 不可编辑  false可编辑
  @Model('change') private value: any // 双向绑定附件ID
  // 上传文件地址
  private uploadAddress = '/gapi/gemp-file/api/attachment/upload/v1'
  private dialogVisible: boolean = false
  private dialogVisibleVideo: boolean = false
  private dialogVisibleAudio: boolean = false
  private dialogImageUrl = ''

  private fileTips = {
    img: '图片',
    audio: '音频',
    video: '音视频',
    excel: 'excel文件',
    txt: '文本',
    pdf: 'pdf文档',
    word: 'docx,doc文档',
    office: 'EXCEL,WORD,PDF,文本',
    exVideo: '图片,EXCEL,WORD,PDF,文本',
    wordPdftxt: 'WORD,PDF,文本',
    all: '图片,EXCEL,WORD,PDF,文本,音视频文件,OFD文件'
  }

  private filesExgeps: any = {
    img: /(.*)\.(jpg|bmp|gif|ico|jpeg|tif|png)$/i,
    video: /(.*)\.(mp4|avi|mov|rmvb|rm|3gp|flv)$/i,
    audio: /(.*)\.(mp3|wav)$/i,
    excel: /(.*)\.(xls|xlsx)$/i,
    word: /(.*)\.(docx|doc)$/i,
    txt: /(.*)\.(txt)$/i,
    pdf: /(.*)\.(pdf)$/i,
    office: /(.*)\.(docx|doc|xls|xlsx|txt|pdf|ppt|pptx)$/i,
    exVideo: /(.*)\.(jpg|bmp|gif|ico|jpeg|tif|png|docx|doc|xls|xlsx|txt|pdf)$/i,
    wordPdftxt: /(.*)\.(txt|docx|doc|pdf)$/i,
    all: /(.*)\.(jpg|bmp|gif|ico|jpeg|tif|png|docx|doc|xls|xlsx|txt|pdf|mp4|avi|mp3|mov|rmvb|rm|3gp|flv|wps|ofd)$/i
  }

  // 上传文件正则
  private filesExgep: RegExp = /(jpg|bmp|gif|ico|jpeg|tif|png|docx|doc|xls|xlsx|txt|pdf)/
  // private filesExgep: RegExp = /(jpg|bmp|gif|ico|jpeg|tif|png)/;

  private header = {
    token: this['$store'].state.login.token
  }

  private limitFile = 5

  // 绑定组件的附件列表
  private privateFileList: any = []

  // 回传附件信息
  private uploadList: any[] = []

  // private isFirst: boolean = true;

  // @Emit("change")
  public uploadId(uploadList: any) {
    let idArr: any[string] = []
    uploadList.forEach((item: any, index: number) => {
      if (item.response) {
        idArr.push(item.response.attachId)
      } else {
        idArr.push(item.attachId)
      }
    })

    this.$emit('change', idArr)
  }

  public created() {
    if (this.flieid) {
      this.queryAttachmentInfoByIdFun(this.flieid)
    }
    // 限制上传文件个数
    this.limitFile = this.limit
    // nginx代理开发
    this.filesExgep = this.type
      ? this.filesExgeps[this.type]
      : this.filesExgeps.all
    this.header.token = this.user.token_type + ' ' + this.user.access_token
    console.log(this.header)
  }
  @Watch('attachmentlist', { immediate: true })
  public watchAttachmentlist(val: any) {
    this.limitFile = this.limit
    if (!val) {
      this.privateFileList = []
    } else {
      this.attachmentlist.forEach((item, index) => {
        if (!item.url.includes('http://' + this.BASE_URL)) {
          item.url = 'http://' + this.BASE_URL + item.url
        }
      })
      this.privateFileList = this.attachmentlist
      this.uploadList = this.attachmentlist
    }
  }
  public queryAttachmentInfoByIdFun(id) {
    if (!id) {
      this.attachmentlist = []
      // this.list = [];
      return false
    }
  }

  public handlePictureCardPreview(file: any) {
    if (this.filesExgeps.video.test(file.name)) {
      this.dialogImageUrl = file.url
      this.dialogVisibleVideo = true
      return
    }
    if (this.filesExgeps.audio.test(file.name)) {
      // this.dialogImageUrl = getRootPath() + '/upload/' + file.path;
      this.dialogVisibleAudio = true
      return
    }
    if (this.filesExgeps.word.test(file.name)) {
      this.$emit('handleFile', file)
      return
    }
    this.dialogImageUrl = file.url
    this.dialogVisible = true
  }

  /**
   * 文件上传的规则限定
   * @param val
   */
  public beforeFileName(val: any) {
    // debugger;
    let flag = !this.filesExgep.test(val.name)
    if (flag) {
      this['$message']({ type: 'error', message: '文件格式错误' })
      return false
    }
    if (val.name.length > 50) {
      this['$message']({ type: 'error', message: '文件名称不能超过50字符' })
      return false
    }
    let isLt10M = val.size / 1024 / 1024 < 50
    if (!isLt10M) {
      this['$message'].error('文件大小不能超过50M!')
      return false
    }
    if (this.filesChange(val)) {
      this['$message']({
        message: val.name + '文件已存在',
        type: 'warning'
      })
      return false
    }
  }

  // 文件上传失败回调
  public showError(err: any, file: any, fileList: any) {
    // hideLoading();
    this['$message']({
      type: 'error',
      message: '文件上传失败'
    })
  }

  // 文件上传成功回调
  /*
   * 修改当文件大小超出限制时,做出提示和删除页面的效果
   * 修改file-list的绑定变量
   */
  public uploadSuccess(response: any, file: any, fileList: any) {  
    this.$emit('listImg',response)
    // hideLoading();
    if (response.status == 200) {
      console.log(file)
      //
      // this.isFirst = false;
      this.uploadList = fileList
      let newFile: any = {}
      if (file.response) {
        newFile = file.response
      }
      this.privateFileList.push(newFile)
      this.uploadList = this.privateFileList
      // this.uploadList = this.privateFileList;
      // this.uploadSuccessCall(fileList);
      this.uploadId(fileList)
    } else {
      this['$message']({
        type: 'error',
        message: response.msg
      })
      fileList.pop()
    }
  }

  // 文件超出限制回调
  public exceed(file: any, fileList: any) {
    this['$message']({
      type: 'error',
      message: `只能上传${this.limitFile}个文件`
    })
  }

  // 组件自带功能文件删除
  public fileRemove(file: any, fileList: any) {
    this.handleRemove(file)
    // this.isFirst = false;
    this.uploadList = fileList
    this.privateFileList = fileList
    // this.uploadSuccessCall(this.privateFileList);
    this.uploadId(this.privateFileList)
  }

  // 自定义删除文件功能
  /**
   * 修改file-list绑定变量
   * @param file
   */
  public handleRemove(file: any) {
    // console.log(file, this.privateFileList);
    this.$emit('handleFile', { ...file, remove: true })
    // this.isFirst = false;
    this.privateFileList.forEach((item: any, index: any) => {
      if (file.response) {
        if (file.response.uid === item.uid) {
          this.privateFileList.splice(index, 1)
        }
      }
      if (file.uid === item.uid) {
        this.privateFileList.splice(index, 1)
      }
    })
    // this.uploadSuccessCall(this.privateFileList);
    this.uploadId(this.privateFileList)
  }

  /**
   * 下载文件方法
   * @param url
   */
  public download(file: any) {
    let par = {
      fileId: file.attachId || file.response.attachId
    }
    Attachmentdownload(par).then((res: any) => {
      console.log(res,'特殊作业管理下载')
      downloadFuc(res)
    })
  }

  /**
   * 过滤上传重复的文件
   * @param file, fileList
   */
  public filesChange(file: any): boolean {
    //判断文件名是否重复
    let res: any = this.uploadList.find(item => item.name == file.name)
    // console.log(Boolean(res));
    return Boolean(res)
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-upload-list__item-name {
  margin-right: 0;
}
/deep/ .el-image__preview {
  cursor: -moz-zoom-in;
  cursor: -webkit-zoom-in;
  cursor: zoom-in;
}
/deep/ .el-upload--picture-tips-word {
  font-size: 12px;
  color: #7d7676;
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
}

/deep/ .el-upload--picture-card {
  display: inline-flex !important;
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
  -ms-flex-direction: column !important;
  flex-direction: column !important;
  -webkit-box-pack: center !important;
  -ms-flex-pack: center !important;
  justify-content: center !important;
  -webkit-box-align: center !important;
  -ms-flex-align: center !important;
  align-items: center !important;
  line-height: normal !important;
  padding: 0 0.5rem !important;
  // width:auto !important;
}

.slot_img,
.slot_file {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .slot_name {
    font-size: 12px;
    display: block;
    width: 100%;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    cursor: default;
  }
  .backgroundImg {
    //overflow: hidden;
    background-color: #fff;
    border: 1px solid #c0ccda;
    border-radius: 6px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 148px;
    height: 148px;
    margin: 0 8px 8px 0;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center center;
    position: relative;
    img {
      width: 100%;
      height: 100%;
      vertical-align: middle;
      border-style: none;
      object-fit: scale-down;
    }

    .el-upload-list__item-hover {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      text-align: center;
      color: #fff;
      opacity: 0;
      font-size: 20px;
      display: flex;
      justify-content: center;
      align-items: center;
      & > * {
        margin: 0 10px;
        cursor: pointer;
      }
    }
    .el-upload-list__item-hover:hover {
      opacity: 1;
      background-color: rgba(0, 0, 0, 0.5);
      -webkit-transition: opacity 0.3s;
      transition: opacity 0.3s;
    }
  }
}

.font {
  color: #d25757;
}

.imgUploadWrap {
  text-align: left;
}
</style>
