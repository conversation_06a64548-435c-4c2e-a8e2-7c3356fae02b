<template>
  <div
    class="videoOnlineMonitoring"
    id="videoOnlineMonitoring"
    ref="videoOnlineMonitoring"
  >
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box">
                <a-icon type="home" theme="filled" class="icon" /> 视频在线监控
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="videoInspection">
        <div class="video-left">
          <div class="videoLeft-top">
            <div class="list-search">
              <el-input
                v-model.trim="enterpName"
                size="mini"
                placeholder="请输入企业名称"
                class="input"
                clearable
                style="max-width: 260px; margin-right: 10px"
                @input="changeInput"
              ></el-input>
              <el-button type="primary" size="mini" @click="search"
                >查询</el-button
              >
            </div>
            <div class="video-list" v-loading="loading">
              <el-tree
                :data="newAllData"
                accordion
                @node-click="handleNodeClick"
                icon-class="el-icon-caret-right"
              >
                <span
                  :title="node.label"
                  class="custom-tree-node"
                  slot-scope="{ node, data }"
                >
                  <span>
                    <i v-if="data.type === '1'" class="el-icon-folder"></i>
                    <i
                      v-if="data.type === '2'"
                      class="el-icon-office-building"
                    ></i>
                    <i
                      v-if="data.type === '3'"
                      class="el-icon-video-camera"
                    ></i>
                    {{ node.label }}
                  </span>
                </span>
              </el-tree>
            </div>
          </div>
        </div>

        <div class="video-right">
          <div class="video-box" id="video-box">
            <!-- <div id="playWind"></div> -->
            <div id="ws-real-player"></div>
            <div class="items" id="playWind_items" style="display:none;">
              <div class="items_button">
                <div class="button" @click="StopRealPlayAll()">
                  关闭所有预览
                </div>
                <div class="button" @click="CapturePicture('JPEG')">抓图</div>
                <div class="button" @click="fullSreen()">全屏</div>
              </div>

              <div class="fenping_box">
                <img
                  src="../../../../../static/img/fenping1.png"
                  class="fenping"
                  @click="arrangeWindow(1)"
                />
                <img
                  src="../../../../../static/img/fenping2.png"
                  class="fenping"
                  @click="arrangeWindow(2)"
                />
                <img
                  src="../../../../../static/img/fenping3.png"
                  class="fenping"
                  @click="arrangeWindow(3)"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getOnlineVideoData, getRealmonitorNew } from "@/api/riskAssessment";
import { getVideoH5VideoH5 } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import WSPlayer from '@/utils/WSPlayer/WSPlayer';
import ICC from '@/utils/icc';
export default {
  components: {},
  data() {
    return {
      tableCheck: true,
      newAllData: [],
      enterpName: "",
      loading: false,
      iWind: 0,
      MaxIWind: 0,
      oPlugin: "",
      previewUrl: "",
      splitScreenValue: "",
      videoCheck: true,
      cameraIndexCode: "",
      resizeObserver: null,
      nowtime: new Date().getTime(),
      selectedKeys: "",
      iWndIndex: [],
      realPlayer: null
    };
  },
  methods: {
    //视频运行列表
    getOnlineVideoDataList(type) {
      this.loading = true;
      getOnlineVideoData({
        enterpName: this.enterpName,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          //   this.tableData = res.data.data.records;
          //   this.NetWork = false;
          this.newAllData = res.data.data;
          this.setMenus(this.newAllData);
          if (type != "search") {
            this.allData = this.newAllData;
          }
        }
      });
    },
    changeInput(val) {
      if (this.enterpName == "") {
        this.newAllData = this.allData;
      }
    },
    setMenus(arr) {
      /**
       * 利用递归替换key值
       * title替换orgName
       * key替换orgCode
       */
      var keyMap = {
        name: "label",
        // id: "key",
      };

      for (var i = 0; i < arr.length; i++) {
        // console.log(arr[i].children);
        if (arr[i].children <= 0) {
          delete arr[i].children;
          // console.log(arr[i]);
        }
        // delete arr[i].isLeaf;
        var obj = arr[i];
        for (var key in obj) {
          var newKey = keyMap[key];
          //   console.log(newKey);
          if (newKey) {
            obj[newKey] = obj[key];
            if (obj.children) {
              this.setMenus(obj.children);
            }
          }
        }
      }
    },
    /**
     * 手动双击事件
     * 层级为3才触发事件
     * selectedKeys
     * [0] cameraIndexCode
     * [1] 层级
     */
    // onSelect(selectedKeys, info) {
    //   // console.log(selectedKeys[0].split(",")[0]);

    //   if (
    //     this.selectedKeys === selectedKeys[0].split(",")[0] &&
    //     new Date().getTime() - this.nowtime < 500
    //   ) {
    //     if (selectedKeys[0].split(",")[1] == "3") {
    //       this.cameraIndexCode = selectedKeys[0].split(",")[0];
    //       $(`#canvas_animation${this.iWind}`).show();
    //       let canvasEl = document.getElementById(
    //         `canvas_animation${this.iWind}`
    //       );
    //       let wd = canvasEl.getAttribute("width");
    //       let ht = canvasEl.getAttribute("height");
    //       this.canvasAnimation(`canvas_animation${this.iWind}`, wd, ht);
    //       this.getVideo(this.cameraIndexCode);
    //     }
    //   } else {
    //     this.nowtime = new Date().getTime();
    //     console.log("click");
    //     this.selectedKeys = selectedKeys[0].split(",")[0];
    //   }
    // },
    handleNodeClick(data, node, el) {
      // console.log(this.selectedKeys, data.id);

      if (
        this.selectedKeys === data.id &&
        new Date().getTime() - this.nowtime < 500
      ) {
        // console.log("dbclick");
        if (data.type == "3") {
          this.cameraIndexCode = data.id;
          $(`#canvas_animation${this.iWind}`).show();
          let canvasEl = document.getElementById(
            `canvas_animation${this.iWind}`
          );
          let wd = canvasEl.getAttribute("width");
          let ht = canvasEl.getAttribute("height");
          this.canvasAnimation(`canvas_animation${this.iWind}`, wd, ht);
          // this.getVideo(this.cameraIndexCode);
          this.realPlayNew(this.cameraIndexCode);
        }
      } else {
        // console.log("click");
        this.nowtime = new Date().getTime();
        this.selectedKeys = data.id;
      }
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId 
     */
    realPlayNew(channelId) {
      ICC.getRealmonitor({
          channelId: channelId,
          // dataType: '3', //视频类型：1=视频, 2=音频, 3=音视频
          streamType: '2' //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
      }).then((data) => {
          this.realPlayer.playReal({
              rtspURL: data.rtspUrl, // string | array[string]
              decodeMode: 'canvas', // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
              channelId: channelId, // 可选，用来标记当前视频播放的通道id
          })
      })
    },
    search() {
      this.getOnlineVideoDataList("search");
    },
    getVideo() {
      this.videoCheck = false;
      getVideoH5VideoH5(this.cameraIndexCode).then((res) => {
        this.previewUrl = res.data.data;
        this.realplay(this.iWind);
        if (this.MaxIWind > this.iWind) {
          this.selectWnd(++this.iWind);
        } else {
          this.selectWnd(this.iWind);
        }
      });
    },
    //加载动画
    canvasAnimation(elName, wd, ht) {
      // console.log(elName, wd, ht);
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(255, 204, 0,1)";
        ctx.lineWidth = 3;
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        if (deg1 < 360) {
          if (deg1 < 180) {
            index *= 1.08;
          } else {
            index /= 1.08;
          }
          deg1 += index;
        }
        if (deg1 >= 360) {
          deg1 = 0;
        }
        ctx.stroke();
        ctx.beginPath();
        if (flag) {
          opa -= 0.02;
          if (opa < 0.2) {
            flag = false;
          }
        } else {
          opa += 0.02;
          if (opa > 1) {
            flag = true;
          }
        }
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgba(255, 204, 0," + opa + ")";
        ctx.fillText("正在取流...", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    //错误动画
    canvasError(elName, wd, ht) {
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("取流失败", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    // var szWebsocketSessionID = "4cc81824281f214eceb5"; //设备直连取流uuid， 流媒体取流不需要该参数
    // var szToken = "";
    // var iWind = 0; //窗口索引
    initPlugin() {
      const THIS = this;
      this.oPlugin.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {
          //插件选中窗口回调
          THIS.iWind = iWndIndex;
          // console.log(iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
          //插件错误回调
          console.error(
            `window-${iWndIndex}, errorCode: ${iErrorCode}`,
            oError
          );
          let canvasEl = document.getElementById(
            `canvas_animation${iWndIndex}`
          );
          let wd = canvasEl.getAttribute("width");
          let ht = canvasEl.getAttribute("height");
          THIS.canvasError(`canvas_animation${iWndIndex}`, wd, ht);
          THIS.iWndIndex[iWndIndex] = false;
        },
        windowEventOver: function (iWndIndex) {
          //鼠标移过回调
          if (THIS.iWndIndex[iWndIndex]) {
            $("#stopBtn" + iWndIndex).show();
            $("#stopBtn" + iWndIndex)
              .children()
              .eq(0)
              .click(() => {
                THIS.stopBtn(iWndIndex);
                THIS.iWndIndex[iWndIndex] = null;
              });
          }
        },
        windowEventOut: function (iWndIndex) {
          //鼠标移出回调
          //console.log(iWndIndex);
          $("#stopBtn" + iWndIndex).hide();
        },
        windowEventUp: function (iWndIndex) {
          //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {
          //全屏切换回调
          console.log(bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
          //首帧显示回调
          console.log(iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {
          //性能不足回调
        },
      });
      this.oPlugin
        .JS_SetOptions({
          bSupportSound: false, //是否支持音频，默认支持
          bSupporDoubleClickFull: false, //是否双击窗口全屏，默认支持
          // bOnlySupportMSE: true, //只支持MSE
          // bOnlySupportJSDecoder: true  //只支持JSDecoder
        })
        .then(function () {
          console.log("JS_SetOptions");
        });
    },

    getVersion() {
      this.oPlugin.JS_GetPluginVersion().then(function (szVersion) {
        console.log(szVersion);
      });
    },
    Destroy() {
      this.oPlugin.JS_DestroyWorker().then(function () {
        console.log("destroyWorker success");
      });
    },
    SetSecretKey() {
      var secretKey = document.getElementById("secretKey").value;
      this.oPlugin.JS_SetSecretKey(iWind, secretKey).then(
        function () {
          console.log("JS_SetSecretKey success");
        },
        function () {
          console.log("JS_SetSecretKey failed");
        }
      );
    },

    realplay(iWind) {
      this.videoCheck = true;
      const THIS = this;
      THIS.iWndIndex[THIS.iWind] = true;
      this.oPlugin
        .JS_Play(
          THIS.previewUrl,
          {
            playURL: THIS.previewUrl,
            mode: 0,
            session: "", //定制设备
            token: "",
          },
          iWind
        )
        .then(
          function (res) {
            console.log("realplay success");
            $(`#canvas_animation${iWind}`).hide();
          },
          function () {
            THIS.iWndIndex[iWind] = false;
            let canvasEl = document.getElementById(`canvas_animation${iWind}`);
            let wd = canvasEl.getAttribute("width");
            let ht = canvasEl.getAttribute("height");
            THIS.canvasError(`canvas_animation${iWind}`, wd, ht);
            console.log("realplay failed");
            THIS.$message.error("取流异常");
          }
        );
    },
    stopBtn(i) {
      this.oPlugin.JS_Stop(i).then(
        () => {
          console.log("stop success");
          $(`#canvas_animation${i}`).show();
          this.canvasDef(
            `canvas_animation${i}`,
            $(`#canvas_animation${i}`).parent().width(),
            $(`#canvas_animation${i}`).parent().height()
          );
          $("#stopBtn" + i).hide();
        },
        function (e) {
          console.error("stop failed", e);
        }
      );
    },
    arrangeWindow(i) {
      this.MaxIWind = i * i - 1;
      this.oPlugin.JS_ArrangeWindow(i).then(() => {
        console.log("JS_ArrangeWindow success");
        // console.log(this.iWndIndex.length);
        for (var i = 0; i < this.iWndIndex.length; i++) {
          // console.log(this.iWndIndex.length);
          $(`#canvas_animation${i}`)[0].width = $(`#canvas_animation${i}`)
            .parent()
            .width();
          $(`#canvas_animation${i}`)[0].height = $(`#canvas_animation${i}`)
            .parent()
            .height();
          if (this.iWndIndex[i] == true) {
            this.canvasAnimation(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          } else if (this.iWndIndex[i] == false) {
            this.canvasError(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        }
      });
    },
    selectWnd(iWind) {
      this.oPlugin.JS_SelectWnd(iWind).then(
        function () {
          console.log("JS_SelectWnd success");
        },
        function () {
          console.log("JS_SelectWnd failed");
        }
      );
    },
    CapturePicture(szType) {
      const timestamp = new Date();
      this.oPlugin
        .JS_CapturePicture(this.iWind, `img-${timestamp}`, szType)
        .then(
          function () {
            console.log("CapturePicture success");
          },
          function () {
            console.log("CapturePicture failed");
          }
        );
    },
    //关闭全部预览
    StopRealPlayAll() {
      this.oPlugin.JS_StopRealPlayAll().then(
        () => {
          console.log("JS_StopRealPlayAll success");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        },
        () => {
          console.log("JS_StopRealPlayAll failed");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        }
      );
    },
    canvasDef(elName, wd, ht) {
      var deg1 = 0;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    dateFormat(oDate, fmt) {
      var o = {
        "M+": oDate.getMonth() + 1, //月份
        "d+": oDate.getDate(), //日
        "h+": oDate.getHours(), //小时
        "m+": oDate.getMinutes(), //分
        "s+": oDate.getSeconds(), //秒
        "q+": Math.floor((oDate.getMonth() + 3) / 3), //季度
        S: oDate.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (oDate.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    fullSreen() {
      this.oPlugin.JS_FullScreenDisplay(true).then(
        function () {
          console.log("JS_FullScreenDisplay success");
        },
        function () {
          console.log("JS_FullScreenDisplay failed");
        }
      );
    },
    getVideoId(monitornum, index) {
      this.active = index;
      this.cameraIndexCode = monitornum;
      this.getVideo();
    },
    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
    setVideoSize() {
      var playWind = document.getElementById("playWind_items");
      this.oPlugin.JS_Resize({ iWidth: playWind.scrollWidth }).then(
        () => {},
        (err) => {
          console.info("JS_Resize failed");
          // do you want...
        }
      );
    },
    initializationVideo() {
      const THIS = this;
      function getScript(url, fn) {
        if ("string" === typeof url) {
          url = [url]; //如果不是数组带个套
        }
        var ok = 0; //加载成功几个js
        var len = url.length; //一共几个js
        var head = document.getElementsByTagName("head").item(0);
        var js = null;
        var _url;
        var create = function (url) {
          //创建js
          var oScript = null;
          oScript = document.createElement("script");
          oScript.type = "text/javascript";
          oScript.src = url;
          head.appendChild(oScript);
          return oScript;
        };
        for (var i = 0; i < len; i++) {
          _url = url[i];
          js = create(_url); //创建js
          fn &&
            (js.onload = function () {
              if (++ok >= len) {
                //如果加载完所有的js则执行回调
                fn();
              }
            });
        }
      }
      //var szBrowserVersion = "";
      //var iBrowserVersion = -1;
      var aScript = [];
      var szUserAgent = navigator.userAgent.toLowerCase();
      // if (szUserAgent.match(/chrome\/([\d.]+)/) || szUserAgent.match(/Firefox\/([\d.]+)/)) {
      //szBrowserVersion = szUserAgent.match(/chrome\/([\d.]+)/)[1];
      //iBrowserVersion = parseInt(szBrowserVersion.split(".")[0], 10);
      if (
        szUserAgent.indexOf("win64") > -1 ||
        szUserAgent.indexOf("x64") > -1
      ) {
        aScript = ["../../../../static/h5player.min.js"];
      } else {
        aScript = ["../../../../static/h5player.min.js"];
      }
      // }
      var playWind = document.getElementById("playWind_items");
      // console.log(playWind.scrollWidth);
      getScript(aScript, function () {
        //初始化插件
        //初始化插件
        THIS.oPlugin = new JSPlugin({
          szId: "playWind",
          iWidth: playWind.scrollWidth,
          // iHeight: 500,
          iMaxSplit: 3,
          iCurrentSplit: 1,
          szBasePath: "../../../../static/",
          oStyle: {
            border: "#343434",
            borderSelect: "#FFCC00",
            background: "#000",
          },
          openDebug: false,
        });
        THIS.initPlugin();
        //初始化播放器大小
        THIS.setVideoSize();
        THIS.resizeObserver = new ResizeObserver((entries) => {
          // console.log(entries);
          THIS.$nextTick(() => {
            THIS.$refs.videoOnlineMonitoring.style.width =
              entries[0].target.offsetWidth;
            THIS.setVideoSize();
          });
        });

        THIS.resizeObserver.observe(
          document.getElementById("videoOnlineMonitoring")
        );
        //初始化canvas动画
        for (var i = 0; i < $(".parent-wnd").children().length; i++) {
          THIS.iWndIndex[i] = null;
          $("#playWind_playCanvas" + i).before(
            `<canvas id="canvas_animation${i}" width="${$(
              "#playWind_playCanvas" + i
            ).width()}" height="${$(
              "#playWind_playCanvas" + i
            ).height()}"></canvas>`
          );
          $("#canvas_animation" + i).css({
            transform: "scale(0.5) translate(-50%, -50%)",
            "transform-origin": "0 0",
            position: "absolute",
            top: "50%",
            left: "50%",
          });
          $("#canvas_draw" + i).css({
            cursor: "default",
          });
          $("#playWindow" + i)
            .after(`<div id="stopBtn${i}" style="width:100%;height: 35px;background: rgba(0,0,0,0.4);position: absolute;top:0;left:0;z-index: 9999999;display:none;cursor: default;">
          <i class="el-icon-close"  style="float: right;line-height:35px;margin-right:10px;font-size:18px;color:#eee;cursor: pointer;"></i>
        </div>`);
        }
      });
    },
  },
  async created() {
     await ICC.init();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    const THIS = this;
    //初始化播放器
    // this.initializationVideo();

    this.getOnlineVideoDataList();

    // window.onresize = () => {
    //   THIS.setVideoSize();
    // };
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // 初始化平台信息获取
      let serverAdress = sessionStorage.getItem('videoApi');
      // if (process.env.NODE_ENV === 'development') {
      //     serverAdress = '************:2443';
      // } else {
      //     serverAdress = '***********:9100';
      // }
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    if (!this.realPlayer) {
      this.realPlayer = new WSPlayer({
          el: 'ws-real-player', // 必传
          type: 'real', // real | record
          serverIp: serverAdress,
          num: 4,
          showControl: true,
      })
    }
  },
  beforeDestroy() {
    // this.StopRealPlayAll();
    // 离开页面删除检测器和所有侦听器
    // this.resizeObserver.disconnect();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tree {
  background: rgba(255, 255, 255, 0) !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
/deep/ .el-tree-node__content {
  height: 35px;
}

/deep/ .el-tree-node__content:hover {
  background: #1890ff !important;
  color: #fff !important;
}
/deep/ .el-tree-node__expand-icon {
  color: #606266;
}
/deep/ .el-tree-node__content:hover .el-tree-node__expand-icon {
  color: #fff;
}
/deep/ .el-tree-node__content:hover .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}
/deep/ .el-tree-node__expand-icon.is-leaf {
  color: transparent;
  cursor: default;
}

/deep/ .el-tree-node:focus,
.is-current:focus,
.is-focusable:focus {
  // color: #fff;
  color: #606266;
  & > .el-tree-node__content {
    background: rgba(255, 255, 255, 0);
  }
}
/deep/ .el-tree-node__content:active {
  background: #1890ff !important;
  color: #fff;
}
.videoOnlineMonitoring {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    .video-left {
      float: left;
      width: 20%;
      margin-right: 1%;
      margin-top: 15px;
      .videoLeft-top {
        background: #daefff;
        .list-search {
          padding-top: 15px;
          display: flex;
          justify-content: space-between;
          padding-left: 8px;
          padding-right: 8px;
          padding-bottom: 5px;
        }
        .video-list {
          height: calc(100vh - 195px);
          margin-top: 2px;
          padding-bottom: 2px;
          border-radius: 4px;
          overflow-y: auto;
          overflow-y: overlay;
        }
      }
    }
    .video-right {
      float: right;
      position: relative;
      width: 78.5%;
      .video-box {
        margin-top: 15px;
        border: 1px solid #ddd;
        height: calc(100vh - 145px);
        width: 100%;
        & > .items {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #2e2e2e;
          padding-top: 10px;
          padding-bottom: 10px;
          padding: 0 20px;
          height: 50px;
          .items_button {
            width: 60%;
            height: 30px;
            display: flex;
            align-items: center;
            font-size: 12px;
          }
          .button {
            color: #fff;
            border-radius: 5px;
            padding: 5px 10px;
            margin-right: 20px;
            cursor: pointer;
            background-color: #777;
          }
          .fenping_box {
            width: 120px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .fenping {
              width: 25px;
              height: 25px;
            }
          }
        }
      }
      #playWind {
        height: calc(100vh - 195px);
      }
      #ws-real-player {
        height: calc(100vh - 150px);
      }
    }
  }
}
</style>

