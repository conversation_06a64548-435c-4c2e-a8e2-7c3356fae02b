<template>
          <iframe class="iframe" src="https://yyzc-meshproxy.hbsis.gov.cn:31443/bgmesh/fiddler/3856/service/65ce72a4-2d09-44bb-9eb6-7199ae3c7929/0/?apaasToken=TElKSUFTSFVBSV8zODU2XzIxMDlfNzc3YTU4NGEwYzg5NGU0YzkyZWM2NDEwMzNhOTVhNWQ=" frameborder="0"></iframe>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
export default {
    //import引入的组件
    name: "Main",
    components: {
    },
  };
</script>

<style lang="scss" scoped>
   .iframe{
        width: 100%;
        height: 100vh;
    }
</style>