import axios from "axios";
import qs from "qs";

// 企业安全承诺重大危险源列表
export const getStructureData = (data) => {
  return axios({
    method: "get",
    url: "/admin/org/tree",
    data: qs.stringify(data),
  });
};
export const getUserData = (data) => {
  return axios({
    method: "get",
    url: `/admin/org/list/${data.parentCode}?current=${data.current}&records=&size=${data.size}`,
  });
};

export const saveOrgAdd = (data) => {
  return axios({
    method: "post",
    url: `/admin/org/add`,
    data: qs.stringify(data),
  });
};

export const getDistrictTree = (data) => {
  return axios({
    method: "get",
    url: `/admin/district/tree`,
  });
};

export const getOrgList = (data) => {
  return axios({
    method: "get",
    url: `/admin/district/list/${data.distParentCode}?current=${data.current}&size=${data.size}`,
  });
};

//企业经济类型列表
export const getEconomicType = (data) => {
  return axios({
    method: "post",
    url: `/gemp-user/api/gemp/user/org/enterprise/economicType/v1`,
    data: qs.stringify(data),
  });
};

export const getOrgAdd = (data) => {
  return axios({
    method: "post",
    url: `/admin/org/add`,
    data: qs.stringify(data),
  });
};
export const getOrgUpdata = (data) => {
  return axios({
    method: "post",
    url: `/admin/org/update`,
    data: qs.stringify(data),
  });
};
export const getOrgInfo = (data) => {
  return axios({
    method: "get",
    url: `/admin/org/info/${data}`,
  });
};
export const delOrgCode = (data) => {
  return axios({
    method: "get",
    url: `/admin/org/del/${data}`,
  });
};

export const getDistrictList = (data) => {
  return axios({
    method: "get",
    url: `/admin/district/list/${data.distParentCode}?current=${data.current}&size=${data.size}`,
  });
};
export const getDistrictInfo = (data) => {
  return axios({
    method: "get",
    url: `/admin/district/info/${data}`,
  });
};
export const getDistrictAdd = (data) => {
  return axios({
    method: "post",
    url: `/admin/district/add`,
    data: qs.stringify(data),
  });
};
export const getDistrictUpdate = (data) => {
  return axios({
    method: "post",
    url: `/admin/district/update`,
    data: qs.stringify(data),
  });
};
export const getDistrictDel = (data) => {
  return axios({
    method: "post",
    url: `/admin/district/del/${data}`,
  });
};

//企业安全评价新增
export const getCompanyProjectAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyProject/add/v1",
    data: data,
  });
};
//企业安全评价更新
export const getCompanyProjectUpdata = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyProject/update/v1",
    data: data,
  });
};
export const getCompanyProjectPageList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyProject/pageList/v1",
    data: data,
  });
};

export const getCompanyProjectId = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyProject/id/v1",
    data: data,
  });
};

export const getCompanyProjectDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyProject/delete/v1",
    data: data,
  });
};
export const getCompanyCertType = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/type/list/v1",
    data: data,
  });
};
//新增证照信息
export const getCompanyCertSave = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/save/v1",
    data: data,
  });
};

export const getCompanyCertUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/update/v1",
    data: data,
  });
};
//分页查询证照信息;
export const getCompanyCertList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/list/v1",
    data: data,
  });
};
export const standardEvaluation = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/standardEvaluation/v1",
    data: data,
  });
};



//根据id查询证照信息;
export const getCompanyCertFindOne = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/findOne/v1",
    data: data,
  });
};

//根据id删除证照信息
export const getCompanyCertDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/delete/v1",
    data: data,
  });
};
export const getSafety = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/safety/warning/id/v1",
    data: data,
  });
};
export const delSafety = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/safety/warning/delete/v1",
    data: data,
  });
};
// 许可证信息列表
export const getCertList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/companyCert/list/v2",
    data: data,
  });
};
