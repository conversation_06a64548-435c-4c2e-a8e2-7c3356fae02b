/* 辰安科技
// 作者：代万重
// <EMAIL>
// 2020.4.16
*/
import CAButton from "./button/index.vue";
import CARadioGroup from "./radioGroup";
import CARadio from "./radioGroup/radio";
import CAEditor from "../Editor";
import CAPagination from "./Pagination";
import CAFileUpload from "./FileUpload";
import CAImageUpload from "./ImageUpload";
import CAAttachmentUpload from "./attachmentUpload";
import EgisMap from "./EgisMap";
import CASelect from "./CASelect";
// 所有组件列表
const components = [
  CAButton,
  CARadioGroup,
  CARadio,
  CAEditor,
  CAPagination,
  CAFileUpload,
  CAImageUpload,
  CAAttachmentUpload,
  EgisMap,
  CASelect,
];
const install = function (Vue) {
  // 遍历并注册所有组件
  components.map((component) => {
    Vue.component(component.name, component);
  });
};
// 检测是否为vue环境
if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
}
export default {
  install,
};
