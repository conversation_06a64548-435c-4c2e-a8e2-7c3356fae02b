export const manList = [
  {
    name: "主要负责人学历和资质",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content:
      "按照主要负责人是否具备专业和学历要求，是否取得安全生产考核合格证明赋分",
    mark: "专业和学历都不符或不全 0  未取得合格证明  0专业和学历均满足 1 取得合格证明 1",
  },
  {
    name: "专职安全管理人员学历和资质",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content:
      "按照专职安全管理人员是否具备专业和学历要求，是否取得安全生产考核合格证明赋分",
    mark: "专业和学历都不符或不全 0  未取得合格证明  0专业和学历均满足 1 取得合格证明 1",
  },
  {
    name: "注册工程师配备情况",
     value: "1",
    score: "1",
    progeress: "10.5",
    status: "0",
    coefficient: "0.022",
    content: "按照企业是否配备足量的注册安全工程师赋分",
    mark: "X＜安全生产管理人员总数的15% 0, X≥安全生产管理人员总数的15% 1",
  },
  {
    name: "分管安全、技术、工艺、设备人员学历和专业",
    value: "3",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照分管安全、技术、工艺、设备人员是否具备专业和学历要求赋分",
    mark: "四类人员专业和学历均不符 0,四类人员的专业和学历符合一半 1,四类人员的专业和学历均符合 3",
  },
  {
    name: "特种作业人员资质",
     value: "1",
    score: "1",
    progeress: "10.5",
    status: "0",
    coefficient: "0.022",
    content: "按照特种作业人员是否取得特种作业证书赋分",
    mark: "持证率＜100% 0,持证率=100% 1",
  },
  {
    name: "两类重点人员学历和专业",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content:
      "按照两类重点人员（危化品管理人员与危险化学品运输人员）是否具备学历和专业赋分",
    mark: "两类重点人员符合学历和专业均不符合或不全   0 ,两类重点人员符合学历和专业均符合 1",
  },
];

export const managementList = [
  {
    name: "全员安全生产责任制情况",
     value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业是否上传全员责任制和年度责任状签订赋分",
    mark: "全员责任制和年度责任状签订均未上传或两种上传不全  0,全员责任制和年度责任状签订均上传 1",
  },
  {
    name: "规章制度制定情况",
     value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业是否上传规章制度情况赋分",
    mark: "规章制度未上传或上传不全   0,规章制度均按要求上传 1",
  },
  {
    name: "岗位操作规程",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业是否上传岗位操作规程情况赋分",
    mark: "岗位操作规程未上传   0,岗位操作规程已上传 1",
  },
  {
    name: "标准化情况",
    value: "10",
    score: "6",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照标准化等级情况（一级、二级、三级、未评级）赋分",
    mark: "一级   10,二级   8,三级   5,未评级 0",
  },
  {
    name: "应急预案编制情况",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业是否上传应急预案赋分",
    mark: "应急预案未上传   0,应急预案已上传 1",
  },
  {
    name: "应急演练情况",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业是否上传应急演练情况赋分（上下半年至少一次）",
    mark: "每半年未进行一次   0,每半年进行一次 1",
  },
  {
    name: "双重预防机制运行情况",
    value: "10",
    score: "10",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业双重预防机制运行评分（优良中差）赋分",
    mark: "优   10,良   7,中   4,差   0",
  },
  {
    name: "风险事件情况",
    value: "3",
    score: "3",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业风险事件情况（红橙黄蓝）赋分",
    mark: "蓝   3,黄   2,橙   1,红   0",
  },
  {
    name: "工单事件情况",
    value: "2",
    score: "2",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业工单事件等级（重大、较大、一般、较低）赋分",
    mark: "无          2,一般或较低   1,较大或重大   0",
  },
  {
    name: "工单处置情况",
    value: "2",
    score: "2",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业工单处置完成情况赋分",
    mark: "已处置    2,未处置    0",
  },
];
export const thingsList = [
  {
    name: "重大危险源情况",
    value: "2",
    score: "2",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业重大危险源等级与数量（一级、二级、三级、四级、无）赋分",
    mark: "无等级      2四级或三级   1二级或一级   0",
  },
  {
    name: "重点监管的危化品工艺数量",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业重点监管危险化工工艺（数量）情况赋分",
    mark: "危险化工工艺数≤2      1，危险化工工艺数＞2      0 ",
  },
  {
    name: "重点监管的危化品数量",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "按照企业重点监管危险化学品数量情况赋分",
    mark: "危化品数量≤3  1,危化品数量＞3     0",
  },
  {
    name: "老旧装置情况",
    value: "3",
    score: "3",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业是否有老旧装置、老旧装置整改完成情况赋分",
    mark: "无老旧装置改造  3,有老旧装置改造  0",
  },
  {
    name: "淘汰落后工艺和装备设施情况",
    value: "3",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业是否涉及淘汰落后工艺设备、是否完成改造赋分",
    mark: "未涉及淘汰落后工艺设备   3,涉及淘汰落后工艺设备   0",
  },
  {
    name: "精细化工企业反应风险评估情况",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业是否上传精细化工反应风险评估报告赋分",
    mark: "精细化工反应风险评估报告已上传   1,精细化工反应风险评估报告未上传   0",
  },
];

export const environmentList = [
  {
    name: "企业项目建设情况",
    value: "5",
    score: "5",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业是否有在建或者改造项目情况赋分",
    mark: "无在建或者改造项目     5,存在在建或者改造项目   0",
  },
  {
    name: "企业检维修和特殊作业情况",
    value: "10",
    score: "8",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业风险承诺判定风险情况（高、中、低）情况赋分",
    mark: "低    10,中    5 ,高    0",
  },
  {
    name: "企业周边环境情况",
    value: "1",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业是否位于化工园区情况赋分",
    mark: "不位于化工园区   1 ,位于化工园区     0 ",
  },
];

export const supervisionList = [
  {
    name: "监督执法发现重大安全生产事故隐患",
    value: "5",
    score: "3",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据上级部门执法检查发现重大隐患赋分",
    mark: "执法检查未发现重大隐患   5,执法检查发现重大隐患     0",
  },
  {
    name: "行政处罚情况",
    value: "5",
    score: "5",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据上级部门执法处罚情况予以赋分",
    mark: "不存在执法处罚情况   5,存在执法处罚情况     0",
  },
  {
    name: "系统巡察反馈情况",
    value: "2",
    score: "2",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据省风险隐患监测预警系统巡察反馈隐患情况赋分",
    mark: "巡察无反馈隐患情况   2,巡察有反馈隐患情况  0",
  },
  {
    name: "重点专项治理工作落实情况",
    value: "5",
    score: "3",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据重点专项治理工作落实情况予以赋分",
    mark: "落实重点专项治理工作     5,未落实重点专项治理工作   0",
  },
];

export const performanceList = [
  {
    name: "事故情况情况",
    value: "5",
    score: "4",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业发生事故情况（特别重大、重大、较大、一般）赋分",
    mark: "不存在事故          5,一般事故            3,较大事故            1 ",
  },
  {
    name: "上级检查通报情况",
    value: "5",
    score: "3",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据企业在各级考核督察中被作为典型案例通报情况赋分",
    mark: "未被当做典型   5,被当做典型     0",
  },
  {
    name: "要素数据维护情况",
    value: "3",
    score: "1",
    progeress: "10.5",
    status: "1",
    coefficient: "0.022",
    content: "根据核查情况赋分",
    mark: "按要求更新数据     3,未按要求更新数据   0",
  },
];
