<template>
  <div class="notification">
    <el-dialog
      title="警示通报"
      :visible.sync="show"
      width="1000px"
      @close="closeBoolean()"
      top="10vh"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="div1">
        <div class="table">
          <ul class="container">
            <li class="lang">
              <div class="l">报告标题</div>
              <div class="r">
                <el-input
                  v-model.trim="title"
                  maxlength="50"
                  show-word-limit
                  clearable
                ></el-input>
              </div>
            </li>
            <li class="lang">
              <div class="l">通报对象</div>
              <div class="r">
                <!-- distRole:  省级：0/地市：1/区县：2 -->
                <el-checkbox-group v-model="checkList">
                  <el-checkbox label="1" v-if="user.distRole == 0"
                    >地市级</el-checkbox
                  >
                  <el-checkbox label="2" v-if="user.distRole <= 1"
                    >区县级</el-checkbox
                  >
                  <el-checkbox label="3" v-if="park">园区</el-checkbox>
                  <el-checkbox label="4">企业</el-checkbox>
                </el-checkbox-group>
              </div>
            </li>
            <li class="lang">
              <div class="l">接收单位</div>
              <div class="r" ref="Unit">
                {{
                  checkList.indexOf("1") != "-1"
                    ? cities + "应急管理局；"
                    : null
                }}{{
                  checkList.indexOf("2") != "-1"
                    ? county + "应急管理局；"
                    : null
                }}{{ checkList.indexOf("3") != "-1" ? park + "；" : null
                }}{{ checkList.indexOf("4") != "-1" ? ent + "；" : null }}
              </div>
            </li>
            <li class="lang">
              <div class="l">报送内容</div>
              <div class="r" style="position: relative">
                <el-input
                  style="padding: 0 0 0 0"
                  name=""
                  cols="5"
                  type="textarea"
                  resize="none"
                  class="textarea"
                  step
                  :autosize="{ minRows: 2, maxRows: 12 }"
                  id=""
                  placeholder="请输入报送内容"
                  v-model.trim="text"
                  maxlength="500"
                  show-word-limit
                  clearable
                ></el-input>

                <!-- preData.redWarningTime -->
                <!-- {{preData.redWarningTime}}  ---'1111' -->
                <div
                  style="color: yellow"
                  v-if="preData.yellowWarningTime"
                  class="toFenXianStyle"
                  @click="toFenXian"
                >
                  <img
                    style="width: 80px; position: absolute; left: 0"
                    src="/static/img/assets/img/huang.gif"
                  />风险动态
                </div>
                <div
                  style="color: orange"
                  v-if="preData.orangeWarningTime"
                  class="toFenXianStyle"
                  @click="toFenXian"
                >
                  <img
                    style="width: 80px; position: absolute; left: 0"
                    src="/static/img/assets/img/cheng.gif"
                  />风险动态
                </div>
                <div
                  v-if="preData.redWarningTime"
                  class="toFenXianStyle"
                  @click="toFenXian"
                >
                  <img
                    style="width: 80px; position: absolute; left: 0"
                    src="/static/img/assets/img/hong.gif"
                  />风险动态
                </div>
              </div>
            </li>
            <li class="lang bottom">
              <div class="l">发送单位</div>
              <div class="r">{{ user.org_name }}</div>
            </li>
          </ul>

          <el-button class="submit" size="medium" @click="closeBoolean()"
            >取消</el-button
          >
          <el-button
            class="submit"
            size="medium"
            type="primary"
            @click="addSubmit()"
            >提交</el-button
          >
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  postCimEarlyWarningWarningPush,
  postEnterpriseWarningNoticeAdd,
  getWarningNoticeList,
} from "@/api/riskAssessment";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  data() {
    return {
      show: false,
      checkList: [],
      text: "",
      nowtime: new Date(new Date()).Format("yy年MM月dd日 hh时mm分"),
      cities: "",
      county: "",
      park: "",
      ent: "",
      companyCode: "",
      preData: {},
      title: "安全生产风险警示通报",
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      userPark: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
  },
  //方法集合
  methods: {
    toFenXian() {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", this.companyCode);
    },
    closeBoolean(val) {
      this.show = val;
      this.cities = "";
      this.county = "";
      this.park = "";
      this.ent = "";
      this.checkList = [];
    },
    submit() {},
    getData(entObj) {
      console.log(entObj);
      this.park = entObj.parkName;
      this.cities = entObj.parentName;
      this.county = entObj.distName;
      this.ent = entObj.companyName;
      this.companyCode = entObj.companyCode;
      this.id = entObj.id;
      var time = "";
      var color = "";
      if (entObj.redWarningTime) {
        time = entObj.redWarningTime;
        color = "红";
      } else if (entObj.orangeWarningTime) {
        time = entObj.orangeWarningTime;
        color = "橙";
      } else {
        time = entObj.yellowWarningTime;
        color = "黄";
      }
      //  this.cities +
      this.text =
        new Date(time).Format("yy年MM月dd日 hh时mm分") +
        "，" +
        this.county +
        this.ent +
        "风险预警等级已达到" +
        color +
        "色预警，截至" +
        this.nowtime +
        "仍未消警，按照有关规定组织核查，并将处置情况及时上报，详情请登录武汉市危险化学品安全生产风险监测预警系统查询";
    },
    addSubmit() {
      if (this.checkList.length <= 0) {
        return this.$message.error("请勾选通报对象");
      } else if (this.text.length <= 0) {
        return this.$message.error("请输入通报内容");
      } else if (this.title.length <= 0) {
        return this.$message.error("请输入通报标题");
      }
      var unit = this.$refs.Unit.innerText.split("；").toString(",");
      unit = unit.substring(0, unit.length - 1);
      postEnterpriseWarningNoticeAdd({
        //企业id
        // companyCode: this.companyCode,
        //通报内容
        notificationContent: this.text,
        //创建人
        // createUser: this.user,
        //通报单位
        notificationObject: this.checkList.toString(","),
        receivingUnit: unit,
        sendingUnit: this.user.org_name,
        //标题
        title: this.title,
        //预警ID
        warningId: this.id,
      }).then((res) => {
        this.$message.success(res.data.data);
        this.show = false;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
/deep/ .el-textarea__inner {
  padding: 10px 80px 10px 10px;
}
.toFenXianStyle {
  text-align: center;
  width: 80px;
  height: 30px;
  line-height: 30px;
  border-radius: 5px;
  // border: 1px solid red;
  color: red;
  position: absolute;
  bottom: 30px;
  right: 16px;
  cursor: pointer;
}
.notification {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
