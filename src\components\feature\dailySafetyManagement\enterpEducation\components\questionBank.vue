<!-- 题库信息 -->
<template>
  <div class="question-bank">
    <el-form
      :model="testInfo"
      :rules="rules"
      ref="questionForm"
      size="small"
      label-width="100px"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="考试科目:" prop="examCourse">
            <el-select
              v-model="testInfo.examCourse"
              placeholder="请选择考试科目"
              :disabled="disabled" clearable
              style="width: 100%"
            >
              <el-option
                v-for="item in subjectList"
                :key="item"
                :value="item"
                :label="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="抽题标签:" prop="labelName">
            <el-select
              v-model="testInfo.labelName"
              placeholder="请选择抽题标签"
               clearable
              :disabled="disabled"
              style="width: 100%"
            >
              <el-option
                v-for="item in examinationTypes"
                :key="item"
                :value="item"
                :label="item"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单选数量:" prop="singleNum">
            <el-input
              v-model.trim="testInfo.singleNum"
              :disabled="disabled" clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="单选分值:" prop="singleFraction">
            <el-input
              v-model.trim="testInfo.singleFraction"
              :disabled="disabled" clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="单选总分:">
            <div class="score-content">
              <span class="score-box">{{ radioTotalScore }}</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="多选数量:" prop="multipleNum">
            <el-input
              v-model.trim="testInfo.multipleNum"
              :disabled="disabled" clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="多选分值:" prop="multipleFraction">
            <el-input
              v-model.trim="testInfo.multipleFraction"
              :disabled="disabled" 
              clearable
              placeholder="请输入"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="多选总分:">
            <div class="score-content">
              <span class="score-box">{{ checkboxTotalScore }}</span>
              <span>注:多选漏选不得分</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="总分值:">
            <div class="score-content">
              <span class="score-box">{{ totalScore }}</span>
              <span class="warning" v-if="totalScore != testInfo.fractionCount">差{{ testInfo.fractionCount - totalScore }}分</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
<script>
export default {
  model: {
    prop: "testInfo",
    event: "change",
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    testInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
    examinationTypes:{
      type: Array,
      default: () => {
        return [];
      },
    },
    subjectList: {
      type: Array,
      default: () => {
        return [];
      },
    },
  },
  data() {
    const numberValid = [
      {
        required: true,
        trigger: "blur",
        message: "请输入数字",
        pattern: /^[1-9]?\d$/,
      },
    ];
    return {
      // examinationTypes: [
      //   { value: "危化行业", label: "危化行业" },
      //   { value: "安全生产", label: "安全生产" },
      // ],
      // // 考试科目
      // subjectList: [
      //   { label: "科目一", value: "1" },
      //   { label: "科目二", value: "2" },
      // ],
      rules: {
        title: [{ required: true, message: "请输入考试名称", trigger: "blur" }],
        singleNum: numberValid,
        multipleNum: numberValid,
        singleFraction: numberValid,
        multipleFraction: numberValid,
        score: [
          {
            required: true,
            trigger: "blur",
            message: "请输入数字",
            pattern: /^[1-9]?\d$/,
          },
        ],
        labelName: [
          {
            required: true,
            message: "请输入培训主题",
            trigger: "change",
          },
        ],
      },
    };
  },
  computed: {
    // 单选总分
    radioTotalScore() {
      return (
        Number(this.testInfo.singleNum) * Number(this.testInfo.singleFraction)
      );
    },
    checkboxTotalScore() {
      return (
        Number(this.testInfo.multipleNum) *
        Number(this.testInfo.multipleFraction)
      );
    },
    totalScore() {
      return this.radioTotalScore + this.checkboxTotalScore;
    },
  },
  methods: {
    updateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.questionForm.validate((valid) => {
          if (valid) {
            let multipleCount = Number(this.testInfo.multipleNum)* Number(this.testInfo.multipleFraction);
            let singleCount = Number(this.testInfo.singleNum)* Number(this.testInfo.singleFraction);
            this.$emit("change", {...this.testInfo,fractionCount:multipleCount+singleCount,singleCount,multipleCount});
            resolve(valid);
          } else {
            reject(false);
            return false;
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.question-bank {
  height: 400px;
  overflow: auto;
  padding: 30px 20px 20px;
  box-sizing: border-box;
}
.score-content {
  display: flex;
  align-items: center;

  .score-box {
    width: 60px;
    height: 32px;
    border: 1px solid #e4e7ed;
    border-radius: 3px;
    background: #f5f7fa;
    color: #c0c4cc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px;

  } 
    
  .warning{
        color: red;
    }
}
</style>
