<template>
  <div class="report-h5">
    <!-- 步骤1: 企业验证 -->
    <div v-if="currentStep === 1" class="verify-step">
      <div class="title">企业身份验证</div>
      <el-form
        :model="verifyForm"
        :rules="verifyRules"
        ref="verifyForm"
        label-width="100px"
      >
        <el-form-item label="企业编码" prop="enterpriseCode">
          <el-input
            v-model.trim="verifyForm.enterpriseCode"
            placeholder="请输入信用编码"
          ></el-input>
        </el-form-item>
        <el-button type="primary" @click="verifyEnterprise">验证</el-button>
      </el-form>
    </div>

    <!-- 步骤2: 信息填写/附件上传 -->
    <div v-if="currentStep === 2" class="info-step">
      <div class="enterprise-info">
        <div class="info-header">
          <h3>企业基本信息</h3>
          <el-button
            v-if="isFromList"
            type="text"
            icon="el-icon-back"
            @click="goBack"
            >返回</el-button
          >
        </div>
        <div class="info-item">
          <span class="label">企业名称：</span>
          <span>{{ enterpriseInfo.enterpname }}</span>
        </div>
        <div class="info-item">
          <span class="label">企业地址：</span>
          <span>{{ enterpriseInfo.addressRegistry }}</span>
        </div>
        <div class="info-item">
          <span class="label">状态：</span>
          <span v-if="isSubmit">已提交</span>
          <span v-else>未提交</span>
        </div>
      </div>

      <div class="operation-btns">
        <el-button
          v-if="!isSubmit"
          type="primary"
          @click="showInstructionsDialog"
          >{{ isEdit ? "编辑表单" : "填写表单" }}</el-button
        >
        <el-button type="primary" @click="handleExport" v-if="isCustomized"
          >导出</el-button
        >
        <el-button
          v-if="!isSubmit && isCustomized"
          type="primary"
          :disabled="filesSubmitted"
          @click="showUploadDialog"
        >
          上传附件
        </el-button>
        <el-button type="primary" @click="handleSub" v-if="!isCustomized"
          >提交</el-button
        >
        <el-button v-if="isSubmit" type="primary" @click="viewHistoryRecord">
          历史填报记录
        </el-button>
      </div>

      <!-- 附件上传弹窗 -->
      <el-dialog
        title="上传附件"
        :visible.sync="uploadDialogVisible"
        width="500px"
        :close-on-click-modal="false"
      >
        <AttachmentUpload
          :attachmentlist="fileList"
          :limit="1"
          type="wordPdftxt"
          v-bind="{}"
          :editabled="false"
        ></AttachmentUpload>
        <div slot="footer">
          <el-button @click="uploadDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitFiles">确 定</el-button>
        </div>
      </el-dialog>

      <!-- 填报须知弹窗 -->
      <el-dialog
        title="填报须知"
        :visible.sync="instructionsDialogVisible"
        width="700px"
        :close-on-click-modal="false"
        :show-close="false"
        class="instructions-dialog"
        @opened="checkInstructionsScrollable"
      >
        <div
          class="instructions-content"
          ref="instructionsContent"
          @scroll="handleInstructionsScroll"
        >
          <h3>填报须知</h3>
          <p>
            1.本申请书申请类别中，企业首次申请办理危险化学品经营许可的勾选"首次申请"；已取得危险化学品经营许可的不带有储存设施的经营企业变更经营场所的、带有储存设施的经营企业变更储存场所的、仓储企业异地重建的、经营方式发生变化的、许可范围发生变化的勾选"重新申请"；经营许可证有限期到期后需要继续从事危险化学品经营活动的，勾选"延期申请"；申请延期的同时需要变更企业名称、主要负责人、注册地址或者危险化学品储存设施及其监控措施的，勾选"延期并变更"。
          </p>
          <p>
            2.本申请书中的"企业名称"、"企业住所"、"企业类型"和"地址"等栏目填写全称，与营业执照一致。"从业人员"包含主要负责人、安全管理人员、其他从业人员。
          </p>
          <p>
            3.本申请书中的"经营场所"、"储存场所"、"经营种类"、"经营方式"等栏目中有"□"栏为选择项，请按实际情况在对应"□"上划"√"。
          </p>
          <p>
            4.本申请书中"储存设施（储存场所）"、"安全评价"、"变更事项"等结合实际填写，不涉及相关内容的填"不涉及"。
          </p>
          <p>
            5.本申请书中的"经营种类"从"剧毒化学品"、"易制毒化学品"、"易制爆化学品"、"成品油"、"其他危险化学品"中按实际勾选。
          </p>
          <p>
            6.本申请书中的经营方式从"无储存经营"、"有储存经营（不构成重大危险源）"、"有储存经营（构成重大危险源）
            "、"仓储经营"、"加油站"、"危险化学品商店"中按实际选择。
          </p>
          <p>7.本申请书"品名"为《危险化学品目录》规定的品名。</p>
          <div class="instructions-bottom-spacer"></div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeInstructionsDialog">取 消</el-button>
          <el-button
            type="primary"
            :disabled="!hasReadToBottom"
            @click="proceedToForm"
            >{{ hasReadToBottom ? "确认并继续" : "请阅读完整内容" }}</el-button
          >
          <div v-if="!hasReadToBottom" class="read-tip">
            <i class="el-icon-info"></i>
            <span>请滚动阅读完整的填报须知内容</span>
          </div>
        </div>
      </el-dialog>
    </div>

    <!-- 步骤3: 表单填写 -->
    <div
      v-show="currentStep === 3"
      class="form-step"
      :class="{ 'form-readonly': isViewingHistory }"
    >
      <div class="form-header">
        <el-button icon="el-icon-back" @click="backToMain">返回</el-button>
        <h3>
          {{
            isEdit
              ? isViewingHistory
                ? "查看历史填报信息"
                : "编辑表单信息"
              : "填写表单信息"
          }}
        </h3>
        <el-button type="primary" @click="submitForm" v-if="!isViewingHistory"
          >保存</el-button
        >
      </div>

      <div class="form-content">
        <v-form-render
          :form-json="formJson"
          :form-data="formData"
          :option-data="optionData"
          ref="vFormRender"
        ></v-form-render>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getReportDesignById,
  addReportGatherContent,
  getReportByEnterpriseCode,
  updateReportGatherContent,
  exportWord,
  getReportGatherContentById,
  getEnterpriseInfoList,
} from "@/api/smartReport";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import { getEnterpriseInfo } from "@/api/entList";
import { getChemicalName } from "@/api/smartReport";
import { loginMock } from "@/api/login";
export default {
  props: {
    isFromList: {
      type: Boolean,
      default: false,
    },
  },
  components: {
    AttachmentUpload,
  },
  data() {
    return {
      currentStep: this.isFromList ? 2 : 1,
      verifyForm: {
        enterpriseCode: "",
      },
      verifyRules: {
        enterpriseCode: [
          { required: true, message: "请输入企业编码", trigger: "blur" },
        ],
      },
      enterpriseInfo: {},
      formJson: {},
      formData: {},
      optionData: {},
      formDialogVisible: false,
      uploadDialogVisible: false,
      fileList: [],
      formSubmitted: false,
      filesSubmitted: false,
      isEdit: false,
      gatherContentDTO: {},
      isSubmit: false,
      gatherId: "",
      isViewingHistory: false,
      originalFormData: null,
      gatherName: "",
      // 填报须知相关
      instructionsDialogVisible: false,
      hasReadToBottom: false,
      // 模糊查询相关
      mockList: [],
      currentDropdown: null,
      // 跟踪手动输入的化学品（key: productName字段名, value: true表示手动输入）
      manualInputChemicals: {},
      delete: (widget) => {
        this.$confirm("确认删除该化学品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          const list = this.formJson.widgetList;
          // 获取化学品列表
          const widgetList =
            list[list.length - 1].cols[5].widgetList[0].tabs[0].widgetList;

          // 如果只有一种化学品，不允许删除
          if (widgetList.length <= 1) {
            this.$message.warning("至少需要保留一种化学品");
            return;
          }

          // 获取要删除的化学品对象
          const targetWidget =
            widget.$parent.$parent.$parent.$parent.$parent.$parent.$parent
              .widget;

          // 找到要删除的化学品在列表中的索引
          const index = widgetList.findIndex(
            (item) => item.id === targetWidget.id
          );

          if (index !== -1) {
            // 获取要删除的化学品序号
            let chemNumber = null;
            const match = targetWidget.options.label.match(/第(\d+)种/);
            if (match && match[1]) {
              chemNumber = parseInt(match[1]);
            }

            // 从列表中删除该化学品
            widgetList.splice(index, 1);

            // 如果找到了化学品序号，删除formData中对应的数据
            if (chemNumber !== null) {
              // 删除formData中与该化学品相关的所有字段
              const fieldsToDelete = [
                `productName${chemNumber}`,
                `operateType${chemNumber}`,
                `salesVolume${chemNumber}`,
                `storage${chemNumber}`,
                `storageNumber${chemNumber}`,
                `designNumber${chemNumber}`,
                `storeroomNumber${chemNumber}`,
                `roomDesign${chemNumber}`,
              ];

              // 从 formData 中删除这些字段
              fieldsToDelete.forEach((field) => {
                if (this.formData.hasOwnProperty(field)) {
                  delete this.formData[field];
                }
              });

              console.log(`已删除第${chemNumber}种化学品的数据`);
            }

            // 更新表单
            const temp = { ...(await this.$refs.vFormRender.getFormData()) };
            this.$refs.vFormRender.setFormJson(this.formJson);
            this.$refs.vFormRender.setFormData(temp);
            this.$message.success("已删除化学品");
          } else {
            this.$message.error("未找到要删除的化学品");
          }
        });
      },
    };
  },
  computed: {
    canSubmit() {
      return (this.isEdit || this.formSubmitted) && this.filesSubmitted;
    },
    // 是否定制化
    isCustomized() {
      return this.gatherName.includes("危险化学品经营许可申请书");
    },
  },
  methods: {
    // 将节流改为防抖
    debounce(fn, delay = 300) {
      return (...args) => {
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer);
        }
        this.debounceTimer = setTimeout(() => {
          fn.apply(this, args);
          this.debounceTimer = null;
        }, delay);
      };
    },

    // 实际的回调处理函数
    async handleCallback(val, widget) {
      // 清除之前可能存在的下拉框
      this.removeDropdown();

      // 如果输入值为空，直接返回
      if (!val) {
        return;
      }

      // 从 widget 对象中获取字段名
      const fieldName =
        widget && widget.field.options && widget.field.options.name;

      // 如果是化学品名称字段，标记为手动输入
      if (fieldName && fieldName.startsWith("productName")) {
        this.manualInputChemicals[fieldName] = true;
        this.updateChemicalTitle(fieldName);
      }

      const res = await getChemicalName({
        productName: val,
      });
      this.mockList = res.data.data;

      // 创建下拉框
      if (widget && widget.$el && this.mockList.length > 0) {
        const inputEl = widget.$el;
        const rect = inputEl.getBoundingClientRect();

        // 更新下拉框位置的函数
        const updateDropdownPosition = () => {
          const newRect = inputEl.getBoundingClientRect();
          if (this.currentDropdown) {
            this.currentDropdown.style.left = `${newRect.left + 80}px`;
            this.currentDropdown.style.top = `${newRect.bottom}px`;
          }
        };

        // 创建下拉框元素
        const dropdown = document.createElement("div");
        dropdown.className = "dynamic-dropdown";
        dropdown.style.position = "fixed";
        // 设置初始位置
        dropdown.style.left = `${rect.left + 80}px`;
        dropdown.style.top = `${rect.bottom}px`;
        dropdown.style.width = `${rect.width - 80}px`;
        dropdown.style.zIndex = "1000";
        dropdown.style.backgroundColor = "#fff";
        dropdown.style.border = "1px solid #ccc";
        dropdown.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
        dropdown.style.maxHeight = "150px";
        dropdown.style.overflowY = "auto";

        // 添加滚动事件监听
        window.addEventListener("scroll", updateDropdownPosition);
        const formContent = document.querySelector(".form-content");
        if (formContent) {
          formContent.addEventListener("scroll", updateDropdownPosition);
        }

        // 创建选项列表
        if (this.mockList && this.mockList.length > 0) {
          const ul = document.createElement("ul");
          ul.style.listStyleType = "none";
          ul.style.padding = "0";
          ul.style.margin = "0";

          this.mockList.forEach((option) => {
            const li = document.createElement("li");
            li.textContent = option.productName;
            li.style.padding = "8px";
            li.style.cursor = "pointer";

            // 鼠标悬停效果
            li.addEventListener("mouseover", () => {
              li.style.backgroundColor = "#f0f0f0";
            });
            li.addEventListener("mouseout", () => {
              li.style.backgroundColor = "";
            });

            // 点击选项时的处理
            li.addEventListener("click", () => {
              // 设置输入框的值
              if (widget.setValue) {
                widget.setValue(option.productName);
                const operateTypeWidget = widget.$parent.$children[1];
                operateTypeWidget.setValue(option.operateType);

                // 标记为非手动输入（通过下拉选择）
                const fieldName =
                  widget && widget.options && widget.options.name;
                if (fieldName && fieldName.startsWith("productName")) {
                  this.manualInputChemicals[fieldName] = false;
                  this.updateChemicalTitle(fieldName);
                }
              }
              // 移除下拉框
              this.removeDropdown();
            });

            ul.appendChild(li);
          });

          dropdown.appendChild(ul);
        }

        document.body.appendChild(dropdown);
        this.currentDropdown = dropdown;

        // 在组件销毁时移除事件监听
        this.$once("hook:beforeDestroy", () => {
          window.removeEventListener("scroll", updateDropdownPosition);
          if (formContent) {
            formContent.removeEventListener("scroll", updateDropdownPosition);
          }
        });
      }
    },

    // 将 callBack 改为使用防抖版本
    callBack(val, widget) {
      this.debounce(this.handleCallback)(val, widget);
    },

    // 更新化学品标题，添加或移除自定义标注
    updateChemicalTitle(productNameField) {
      try {
        const match = productNameField.match(/productName(\d+)/);
        if (!match) return;

        const chemNumber = parseInt(match[1]);
        const isManual = this.manualInputChemicals[productNameField];

        const list = this.formJson.widgetList;
        const widgetList =
          list[list.length - 1].cols[5].widgetList[0].tabs[0].widgetList;

        const chemWidget = widgetList.find((widget) => {
          const labelMatch = widget.options.label.match(/第(\d+)种/);
          return labelMatch && parseInt(labelMatch[1]) === chemNumber;
        });

        if (chemWidget) {
          const baseLabel = `第${chemNumber}种`;

          if (isManual) {
            if (!chemWidget.options.label.includes("⚠")) {
              chemWidget.options.label = baseLabel + " ⚠";
            }
          } else {
            chemWidget.options.label = baseLabel;
          }

          this.$nextTick(() => {
            if (this.$refs.vFormRender) {
              this.$refs.vFormRender.setFormJson(this.formJson);
            }
          });
        }
      } catch (error) {
        console.error("更新化学品标题失败:", error);
      }
    },

    // 检查化学品是否在数据库中存在
    async checkChemicalInDatabase(productName) {
      try {
        console.log(`调用接口查询化学品: ${productName}`);
        const res = await getChemicalName({
          productName: productName,
        });

        console.log(`接口返回结果:`, res.data);
        const chemicals = res.data.data || [];
        const hasData = chemicals.length > 0;
        console.log(
          `化学品 ${productName} 查询结果: ${
            hasData ? "在数据库中" : "不在数据库中"
          }`
        );
        return hasData;
      } catch (error) {
        console.error(`查询化学品 ${productName} 失败:`, error);
        return false;
      }
    },

    // 显示填报须知弹窗
    showInstructionsDialog() {
      // 只有在填写危险化学品经营许可申请书时才显示填报须知
      if (this.isCustomized) {
        this.instructionsDialogVisible = true;
        this.hasReadToBottom = false;

        // 在弹窗显示后，重置滚动位置并检查是否需要滚动
        this.$nextTick(() => {
          if (this.$refs.instructionsContent) {
            this.$refs.instructionsContent.scrollTop = 0;
          }
          // 检查是否需要滚动，如果不需要则直接允许确定
          this.checkInstructionsScrollable();
        });
      } else {
        // 如果不是危险化学品经营许可申请书，直接进入表单
        this.currentStep = 3;
      }
    },

    // 处理填报须知滚动事件
    handleInstructionsScroll(e) {
      const element = e.target;
      // 检查是否滚动到底部（考虑一些容差）
      const isAtBottom =
        element.scrollHeight - element.scrollTop <= element.clientHeight + 10;

      if (isAtBottom) {
        this.hasReadToBottom = true;
      }
    },

    // 检查填报须知是否需要滚动
    checkInstructionsScrollable() {
      // 使用 setTimeout 确保 DOM 完全渲染后再检查
      setTimeout(() => {
        const element = this.$refs.instructionsContent;
        if (element) {
          // 如果内容高度小于等于容器高度，说明不需要滚动，直接允许确定
          const needsScroll = element.scrollHeight > element.clientHeight;

          if (!needsScroll) {
            this.hasReadToBottom = true;
          }
        }
      }, 100); // 延迟100ms确保渲染完成
    },

    // 关闭填报须知弹窗
    closeInstructionsDialog() {
      this.instructionsDialogVisible = false;
    },

    // 确认并进入表单
    proceedToForm() {
      this.instructionsDialogVisible = false;
      this.currentStep = 3;
    },
    // 新增化学品
    async sava() {
      const list = this.formJson.widgetList;
      // 获取化学品列表
      const widgetList =
        list[list.length - 1].cols[5].widgetList[0].tabs[0].widgetList;

      // 获取第一个化学品对象作为模板
      const firstChem = widgetList[0];

      // 查找当前化学品的序号
      let maxNum = 1;

      for (let i = 0; i < widgetList.length; i++) {
        const widget = widgetList[i];
        // 从标签中提取序号，如"第一种"中的"1"
        const match = widget.options.label.match(/第(\d+)种/);
        if (match && parseInt(match[1]) > maxNum) {
          maxNum = parseInt(match[1]);
        }
      }

      // 新的序号
      const newNum = maxNum + 1;

      // 创建新的化学品对象
      const newChem = this.createNewChem(firstChem, newNum);

      // 将新的化学品对象添加到列表中
      widgetList.push(newChem);
      // 更新表单
      const temp = { ...(await this.$refs.vFormRender.getFormData()) };
      // 更新表单
      this.$refs.vFormRender.setFormJson(this.formJson);
      this.$refs.vFormRender.setFormData(temp);
      this.$message.success(`已添加第${newNum}种化学品`);
    },

    // 创建新的化学品对象
    createNewChem(sourceChem, newNum) {
      // 深拷贝源对象
      const newChem = JSON.parse(JSON.stringify(sourceChem));

      // 更新label
      newChem.options.label = `第${newNum}种`;
      // 生成新的随机ID
      newChem.id = `card${this.generateRandomId()}`;

      // 更新widgetList中的每个组件
      for (let i = 0; i < newChem.widgetList.length; i++) {
        const widget = newChem.widgetList[i];

        // 更新name属性，将数字部分替换为新的序号
        if (widget.options && widget.options.name) {
          widget.options.name = widget.options.name.replace(/\d+$/, newNum);

          // 如果是 productName 字段，初始化为非手动输入状态
          if (widget.options.name.startsWith("productName")) {
            this.manualInputChemicals[widget.options.name] = false;
          }
        }

        // 生成新的随机ID
        widget.id = `${widget.type}${this.generateRandomId()}`;

        // 如果是容器类型，递归处理其子组件
        if (widget.type === "grid" && widget.cols) {
          for (let j = 0; j < widget.cols.length; j++) {
            const col = widget.cols[j];
            // 更新col的ID
            col.id = `grid-col-${this.generateRandomId()}`;
            if (col.options && col.options.name) {
              col.options.name = `gridCol${this.generateRandomId()}`;
            }

            // 处理col中的widgetList
            if (col.widgetList && col.widgetList.length > 0) {
              for (let k = 0; k < col.widgetList.length; k++) {
                const subWidget = col.widgetList[k];
                // 更新name属性
                if (subWidget.options && subWidget.options.name) {
                  subWidget.options.name = subWidget.options.name.replace(
                    /\d+$/,
                    newNum
                  );

                  // 如果是 productName 字段，初始化为非手动输入状态
                  if (subWidget.options.name.startsWith("productName")) {
                    this.manualInputChemicals[subWidget.options.name] = false;
                  }
                }
                // 更新onClick属性，确保删除按钮的事件处理正确
                if (subWidget.options && subWidget.options.onClick) {
                  // 保持原有的onClick事件
                }
                // 生成新的随机ID
                subWidget.id = `${subWidget.type}${this.generateRandomId()}`;
              }
            }
          }
        }
      }

      return newChem;
    },

    // 生成5位随机数字ID
    generateRandomId() {
      return Math.floor(10000 + Math.random() * 90000);
    },
    // 修改 removeDropdown 方法
    removeDropdown() {
      if (
        this.currentDropdown &&
        document.body.contains(this.currentDropdown)
      ) {
        // 移除所有相关的事件监听器
        window.removeEventListener("scroll", this.updateDropdownPosition);
        const formContent = document.querySelector(".form-content");
        if (formContent) {
          formContent.removeEventListener(
            "scroll",
            this.updateDropdownPosition
          );
        }

        document.body.removeChild(this.currentDropdown);
        this.currentDropdown = null;
      }
    },
    // 验证企业编码
    async verifyEnterprise() {
      try {
        if (!this.isFromList) {
          await this.$refs.verifyForm.validate();
        }
        const enterpriseRes = await loginMock(this.verifyForm.enterpriseCode);
        if (enterpriseRes.data.code === 0) {
          this.$store.state.login.user = {};
          this.$store.state.login.user.token_type = "bearer";
          this.$store.state.login.user.access_token =
            enterpriseRes.data?.data?.token?.split(" ")[1] || "";
        }
        const res = await getEnterpriseInfo(this.verifyForm.enterpriseCode);

        if (res.data.data) {
          this.enterpriseInfo = res.data.data;
          await this.getReportContent();
          if (!this.isEdit) {
            this.formData = {
              ...this.formData,
              enterpriseName: this.enterpriseInfo.enterpname,
              enterpriseAddress: this.enterpriseInfo.addressRegistry,
              socialCreditCode: this.enterpriseInfo.entcreditcode,
              legalPeople: this.enterpriseInfo.legalrepper,
              legalPeoplePhone: this.enterpriseInfo.legalrepptel,
            };
          }
          this.currentStep = 2;
          await this.getReportDesignById();
        } else {
          this.$message.error(res.data.msg || "企业验证失败");
        }
      } catch (error) {
        console.error("验证失败:", error);
      }
    },

    // 获取表单设计数据
    async getReportDesignById() {
      try {
        const res = await getReportDesignById({
          id: this.isFromList ? this.gatherId : this.$route.query.id,
        });
        if (res.data.status === 200 && res.data.data.textContent) {
          this.formJson = JSON.parse(res.data.data.textContent);
          this.gatherName = res.data.data.gatherName;
          this.addUploadTokenToForm();
          if (this.isCustomized) {
            this.$refs.vFormRender.callback = this.callBack;
            this.$refs.vFormRender.sava = this.sava;
            this.$refs.vFormRender.delete = this.delete;
          }

          const temp = { ...(await this.$refs.vFormRender.getFormData()) };
          this.$refs.vFormRender.setFormJson(this.formJson);
          this.$refs.vFormRender.setFormData(temp);

          console.log("表单加载完成，检查条件:", {
            isEdit: this.isEdit,
            isCustomized: this.isCustomized,
            gatherName: this.gatherName,
          });

          if (this.isEdit && this.isCustomized) {
            console.log("编辑模式，开始调整化学品数量并验证");
            const contentJson = JSON.parse(this.gatherContentDTO.contentJson);
            this.$nextTick(() => {
              if (this.formJson && this.formJson.widgetList) {
                this.adjustChemicalItems(contentJson);
              }
            });
          }
        }
      } catch (error) {
        console.error("获取表单失败:", error);
        this.$message.error("获取表单失败");
      }
    },

    // 处理文件下载
    addUploadTokenToForm() {
      const processWidget = (widget) => {
        // 处理当前组件
        if (widget.type === "picture-upload" || widget.type === "file-upload") {
          widget.options.uploadURL = "/gapi/gemp-file/api/attachment/upload/v1";
          widget.options.onBeforeUpload = `var currentWidget = this;
   var token = "${
     this.$store.state.login.user.token_type +
     " " +
     this.$store.state.login.user.access_token
   }";
   currentWidget.setUploadHeader('token', token);
   delete currentWidget.uploadData.key`;
          widget.options.onFileDownload = `
          let par = {
      fileId: file.attachId || file.response.attachId
    }
    this.$Attachmentdownload(par).then((res) => {
      console.log(res,'特殊作业管理下载')
      this.$downloadFuc(res)
    })`;
        }

        // 递归处理所有可能的子组件结构
        const processChildren = (widget) => {
          // 处理常规widgetList
          if (widget.widgetList) {
            widget.widgetList.forEach((child) => processWidget(child));
          }
          // 处理表格rows结构
          if (widget.rows) {
            widget.rows.forEach((row) => {
              if (row.cols) {
                row.cols.forEach((col) => {
                  if (col.widgetList) {
                    col.widgetList.forEach((child) => processWidget(child));
                  }
                });
              }
            });
          }
          if (widget.cols) {
            widget.cols.forEach((col) => {
              if (col.widgetList) {
                col.widgetList.forEach((child) => processWidget(child));
              }
            });
          }
        };

        processChildren(widget);
      };

      if (this.formJson?.widgetList) {
        this.formJson.widgetList.forEach((widget) => processWidget(widget));
      }
    },

    // 获取报表内容
    async getReportContent() {
      try {
        // 查询企业填报记录
        const reportRes = await getReportByEnterpriseCode({
          enterpId: this.enterpriseInfo.enterpid,
          gatherId: this.isFromList ? this.gatherId : this.$route.query.id,
        });
        if (reportRes.data.status === 200 && reportRes.data.data) {
          // 有填报记录，进入编辑模式
          this.isEdit = true;
          this.gatherContentDTO = reportRes.data.data.gatherContentDTO;
          if (this.gatherContentDTO.isReturn == "1") {
            this.isSubmit = false;
          } else if (this.gatherContentDTO.isReturn == "0") {
            this.isSubmit = true;
          }
          // 解析内容JSON
          const contentJson = JSON.parse(this.gatherContentDTO.contentJson);
          this.formData = {
            ...this.formData,
            ...contentJson,
          };
        } else {
          // 无填报记录，进入新增模式
          this.isEdit = false;
        }
      } catch (error) {
        console.error("获取报表内容失败:", error);
        this.$message.error("获取报表内容失败");
      }
    },

    // 显示上传弹窗
    showUploadDialog() {
      this.uploadDialogVisible = true;
    },
    handleSub() {
      //
    },
    // 上传成功回调
    handleUploadSuccess(response, file) {
      if (response.status === 200) {
        this.$message.success("上传成功");
        this.fileList.push({
          name: file.name,
          url: response.data.url,
          uid: file.uid,
        });
      } else {
        this.$message.error(response.msg || "上传失败");
      }
    },

    // 上传失败回调
    handleUploadError(err) {
      this.$message.error("上传失败：" + err.message);
    },

    // 文件超出个数限制
    handleExceed() {
      this.$message.warning("最多只能上传5个文件");
    },

    // 移除文件
    handleRemove(file) {
      const index = this.fileList.findIndex((item) => item.uid === file.uid);
      if (index > -1) {
        this.fileList.splice(index, 1);
      }
    },

    // 提交附件
    async submitFiles() {
      if (this.fileList.length > 0) {
        const contentJson = JSON.parse(this.gatherContentDTO.contentJson);
        await updateReportGatherContent({
          id: this.gatherContentDTO.id,
          reportAttachIdList: this.fileList,
          contentJson: this.gatherContentDTO.contentJson,
          gatherId: this.isFromList ? this.gatherId : this.$route.query.id,
          director: contentJson.mainPeople,
          phone: contentJson.mainPeoplePhone,
          enterpriseName: contentJson.enterpriseName,
          enterpriseAddress: contentJson.enterpriseAddress,
          enterpId: this.enterpriseInfo.enterpid,
        });
        this.filesSubmitted = true;
        this.uploadDialogVisible = false;
        this.getReportContent();
        this.$message.success("附件上传成功");
      } else {
        this.$message.warning("请先上传附件");
      }
    },

    // 提交表单
    async submitForm() {
      try {
        // 获取表单数据
        const formData = await this.$refs.vFormRender.getFormData();

        // 处理化学品数据
        const contentJson = this.isCustomized
          ? this.processFormData(formData)
          : formData;
        const params = {
          contentJson: JSON.stringify(contentJson),
          gatherId: this.isFromList ? this.gatherId : this.$route.query.id,
          director: contentJson.mainPeople || this.enterpriseInfo.respper,
          phone:
            contentJson.mainPeoplePhone || this.enterpriseInfo.responsiblePhone,
          enterpriseName:
            contentJson.enterpriseName || this.enterpriseInfo.enterpname,
          enterpriseAddress:
            contentJson.enterpriseAddress ||
            this.enterpriseInfo.addressRegistry,
          enterpId: this.enterpriseInfo.enterpid,
        };

        let res;
        if (this.isEdit) {
          // 编辑模式需要添加ID
          params.id = this.gatherContentDTO.id;
          res = await updateReportGatherContent(params);
        } else {
          res = await addReportGatherContent(params);
        }

        if (res.data.status === 200) {
          this.$message.success("保存成功");
          // 如果是新增，保存后切换为编辑模式
          if (!this.isEdit) {
            this.isEdit = true;
          }
          this.getReportContent();
          this.currentStep = 2;
          this.formSubmitted = true;
        } else {
          this.$message.error(res.data.msg || "保存失败");
        }
      } catch (error) {
        console.error("保存失败:", error);
        this.$message.error(error);
      }
    },

    // 处理表单数据，确保所有化学品数据都被正确收集
    processFormData(formData) {
      // 先使用当前的formData作为基础
      const result = { ...formData };
      const list = this.formJson.widgetList;
      // 获取化学品列表
      const widgetList =
        list[list.length - 1].cols[5].widgetList[0].tabs[0].widgetList;

      // 遍历所有化学品表单
      for (let i = 0; i < widgetList.length; i++) {
        const chemWidget = widgetList[i];

        // 获取化学品序号
        let chemNumber = null;
        const match = chemWidget.options.label.match(/第(\d+)种/);
        if (match && match[1]) {
          chemNumber = parseInt(match[1]);
        } else {
          continue; // 如果无法获取序号，跳过该化学品
        }

        // 遍历化学品表单中的所有输入字段
        for (let j = 0; j < chemWidget.widgetList.length; j++) {
          const field = chemWidget.widgetList[j];

          // 只处理输入类型的字段
          if (field.type === "input" && field.options && field.options.name) {
            const fieldName = field.options.name;

            // 如果该字段在this.formData中有值，但在formData中没有，则使用this.formData中的值
            if (this.formData[fieldName] && !formData[fieldName]) {
              result[fieldName] = this.formData[fieldName];
            }
          }
        }
      }

      return result;
    },

    // 导出
    async handleExport() {
      const response = await exportWord({
        id: this.gatherContentDTO.id,
      });
      const blob = new Blob([response.data], {
        type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // 推荐使用新版 docx 格式
      });
      // 获取今天的时间
      let day = new Date();
      day.setTime(day.getTime());
      let timestamp =
        day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
      const filename = this.gatherName + timestamp + ".docx"; // 修改后缀为 docx
      // 下载文件
      const link = document.createElement("a");
      link.href = URL.createObjectURL(blob);
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      window.setTimeout(function () {
        URL.revokeObjectURL(blob);
        document.body.removeChild(link);
      }, 0);
    },

    // 初始化数据
    async initData(data) {
      if (this.isFromList) {
        this.gatherId = data.id;
        // 如果是从列表页来的，直接使用当前用户的企业信息
        this.verifyForm.enterpriseCode =
          this.$store.state.login.enterData.entcreditCode;
        this.verifyEnterprise();
      }
    },

    // 返回
    goBack() {
      this.$emit("goBack");
    },

    // 查看历史填报记录
    viewHistoryRecord() {
      // 保存当前表单数据，以便返回时恢复
      this.originalFormData = JSON.parse(JSON.stringify(this.formData));
      // 设置为查看历史模式
      this.isViewingHistory = true;
      // 跳转到表单页面但是只读模式
      this.currentStep = 3;
    },

    // 从表单页返回主页
    backToMain() {
      // 如果是查看历史模式，需要重置表单数据
      if (this.isViewingHistory && this.originalFormData) {
        // 恢复原来的表单数据
        this.formData = this.originalFormData;
        this.isViewingHistory = false;
      }
      // 返回主页
      this.currentStep = 2;
    },

    // 根据内容调整化学品数量
    async adjustChemicalItems(contentJson) {
      try {
        const list = this.formJson.widgetList;
        // 获取化学品列表
        const widgetList =
          list[list.length - 1].cols[5].widgetList[0].tabs[0].widgetList;

        // 如果没有化学品列表，直接返回
        if (!widgetList || widgetList.length === 0) {
          console.warn("未找到化学品列表");
          return;
        }

        // 获取第一个化学品作为模板
        const templateChem = widgetList[0];

        // 计算需要的化学品数量
        let chemCount = 1; // 默认至少有一种

        // 遍历contentJson中的属性，查找化学品相关字段
        // 如productName1, productName2, productName3...
        const productNamePattern = /^productName(\d+)$/;

        for (const key in contentJson) {
          const match = key.match(productNamePattern);
          if (match && match[1]) {
            const num = parseInt(match[1]);
            if (num > chemCount) {
              chemCount = num;
            }
          }
        }

        console.log(`检测到${chemCount}种化学品`);

        // 当前表单中的化学品数量
        const currentCount = widgetList.length;

        // 如果需要的化学品数量大于当前数量，添加新的化学品
        if (chemCount > currentCount) {
          for (let i = currentCount + 1; i <= chemCount; i++) {
            const newChem = this.createNewChem(templateChem, i);
            widgetList.push(newChem);
          }
          const temp = { ...(await this.$refs.vFormRender.getFormData()) };
          this.$refs.vFormRender.setFormJson(this.formJson);
          this.$refs.vFormRender.setFormData(temp);
          console.log(`已添加${chemCount - currentCount}种化学品以匹配数据`);
        }

        // 回填数据后，立即验证每个化学品是否在数据库中
        console.log("开始验证回填的化学品数据...");
        for (let i = 1; i <= chemCount; i++) {
          const productNameKey = `productName${i}`;
          const productName = contentJson[productNameKey];

          if (productName) {
            console.log(`验证化学品: ${productNameKey} = ${productName}`);

            try {
              const isInDatabase = await this.checkChemicalInDatabase(
                productName
              );
              this.manualInputChemicals[productNameKey] = !isInDatabase;

              if (!isInDatabase) {
                console.log(`${productName} 不在数据库中，标记为手动输入`);
                this.updateChemicalTitle(productNameKey);
              } else {
                console.log(`${productName} 在数据库中`);
              }
            } catch (error) {
              console.error(`验证化学品 ${productName} 失败:`, error);
              this.manualInputChemicals[productNameKey] = true;
              this.updateChemicalTitle(productNameKey);
            }
          }
        }
      } catch (error) {
        console.error("调整化学品数量时出错:", error);
      }
    },
  },
  mounted() {},
  beforeDestroy() {},
};
</script>

<style lang="scss" scoped>
.report-h5 {
  width: 100%;
  min-height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  background-color: #f5f7fa;

  .verify-step {
    max-width: 500px;
    margin: 0 auto;
    padding: 20px;
    background: #fff;
    border-radius: 4px;

    .title {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      margin-bottom: 20px;
    }
  }

  .info-step {
    .enterprise-info {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      margin-bottom: 20px;

      .info-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #eee;

        h3 {
          margin: 0;
        }
      }

      .info-item {
        margin-bottom: 10px;

        .label {
          color: #666;
          margin-right: 10px;
        }
      }
    }

    .operation-btns {
      background: #fff;
      padding: 20px;
      border-radius: 4px;
      text-align: center;
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 10px;

      .el-button {
        margin: 5px;
        flex: 0 0 auto;
        min-width: 100px;

        @media screen and (max-width: 480px) {
          margin: 3px;
          min-width: 80px;
          font-size: 12px;
          padding: 8px 12px;
        }
      }
    }
  }

  .form-step {
    background: #fff;
    border-radius: 4px;

    .form-header {
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }

    .form-content {
      padding: 10px;
      min-height: calc(100vh - 180px);
      // overflow-y: auto;
    }

    // 只读模式样式
    &.form-readonly {
      ::v-deep {
        // 禁用所有输入框
        .el-input__inner,
        .el-textarea__inner {
          background-color: #f5f7fa !important;
          border-color: #e4e7ed !important;
          color: #606266 !important;
          cursor: not-allowed !important;
        }

        // 禁用所有按钮和可点击元素
        .el-radio__input,
        .el-checkbox__input,
        .el-switch,
        .el-slider__button-wrapper {
          pointer-events: none !important;
        }

        // 禁用下拉框
        .el-select .el-input.is-disabled .el-input__inner {
          background-color: #f5f7fa !important;
          border-color: #e4e7ed !important;
        }

        // 禁用日期选择器
        .el-date-editor.is-disabled {
          background-color: #f5f7fa !important;
        }

        // 禁用文件上传
        .el-upload {
          pointer-events: none !important;
        }

        // 禁用所有可点击和可输入元素
        input,
        textarea,
        select,
        .el-input,
        .el-select,
        .el-date-editor,
        .el-cascader,
        .el-time-picker,
        .el-color-picker {
          pointer-events: none !important;
        }

        // 禁用单选框组和复选框组
        .el-radio-group,
        .el-checkbox-group {
          pointer-events: none !important;

          .el-radio,
          .el-checkbox {
            cursor: not-allowed !important;

            .el-radio__input,
            .el-checkbox__input {
              cursor: not-allowed !important;

              .el-radio__inner,
              .el-checkbox__inner {
                background-color: #f5f7fa !important;
                border-color: #e4e7ed !important;
                cursor: not-allowed !important;
              }
            }

            .el-radio__label,
            .el-checkbox__label {
              color: #909399 !important;
              cursor: not-allowed !important;
            }
          }

          // 保持选中状态的样式
          .el-radio.is-checked,
          .el-checkbox.is-checked {
            .el-radio__input.is-checked .el-radio__inner,
            .el-checkbox__input.is-checked .el-checkbox__inner {
              background-color: #409eff !important;
              border-color: #409eff !important;
            }

            .el-radio__label,
            .el-checkbox__label {
              color: #606266 !important;
            }
          }
        }
      }
    }
  }
}

::v-deep .el-dialog__body {
  padding: 10px 20px;
}

::v-deep .el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
}

/* 填报须知弹窗样式 */
.instructions-dialog {
  ::v-deep .el-dialog {
    width: 90% !important;
    max-width: 700px;
    margin: 5vh auto !important;

    @media screen and (max-width: 480px) {
      width: 95% !important;
      margin: 3vh auto !important;
    }
  }

  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    max-height: 80vh;

    @media screen and (max-width: 480px) {
      padding: 10px;
    }
  }

  .instructions-content {
    max-height: 60vh;
    overflow-y: auto;
    padding: 0 10px;

    @media screen and (max-width: 480px) {
      max-height: 50vh;
      padding: 0 5px;
    }

    h3 {
      text-align: center;
      margin-bottom: 20px;

      @media screen and (max-width: 480px) {
        margin-bottom: 15px;
        font-size: 16px;
      }
    }

    p {
      text-indent: 2em;
      line-height: 1.8;
      margin-bottom: 15px;
      text-align: justify;

      @media screen and (max-width: 480px) {
        text-indent: 1em;
        line-height: 1.6;
        margin-bottom: 10px;
        font-size: 14px;
      }
    }

    .instructions-bottom-spacer {
      height: 20px;

      @media screen and (max-width: 480px) {
        height: 10px;
      }
    }
  }

  ::v-deep .dialog-footer {
    text-align: center;

    .el-button {
      @media screen and (max-width: 480px) {
        padding: 8px 15px;
        font-size: 13px;
      }
    }
  }
}

// 添加全局移动端适配样式
@media screen and (max-width: 480px) {
  .report-h5 {
    padding: 10px;

    .verify-step {
      padding: 15px;
    }

    .info-step {
      .enterprise-info {
        padding: 15px;

        .info-header {
          margin-bottom: 10px;
          padding-bottom: 8px;

          h3 {
            font-size: 16px;
          }
        }
      }
    }

    .form-step {
      .form-header {
        padding: 10px 15px;

        h3 {
          font-size: 14px;
        }

        .el-button {
          padding: 7px 12px;
          font-size: 12px;
        }
      }

      .form-content {
        padding: 10px;
      }
    }
  }

  ::v-deep .el-dialog {
    width: 95% !important;
    margin: 10vh auto !important;
  }

  .read-tip {
    margin-top: 10px;
    color: #909399;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      margin-right: 4px;
      color: #e6a23c;
    }
  }
}
</style>

<!-- 全局样式，不使用 scoped -->
<style lang="scss">
/* 移动端适配样式 */
@media screen and (max-width: 480px) {
  .el-message-box {
    max-width: 90% !important;

    .el-message-box__header {
      padding: 10px;

      .el-message-box__title {
        font-size: 16px;
      }
    }

    .el-message-box__content {
      padding: 10px;
      font-size: 14px;
    }

    .el-message-box__btns {
      padding: 5px 10px 10px;

      button {
        padding: 7px 12px;
        font-size: 12px;
        min-width: 60px;
        margin-left: 5px;
      }
    }
  }
}
</style>
