<template>
  <div class="report-detail">
    <div class="newEnterprise">
      <div class="newEnterpriseL">
        <div class="titH2">
          <span>{{ enterpriseName }}</span>
        </div>
        <div class="titCon">
          <span>法人：{{ name ? name : " " }}</span>
          &nbsp;&nbsp;&nbsp;&nbsp;<span>{{ tel ? tel : " " }}</span>
          &nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;
          <span>经营：</span>
          <span class="ent"></span>
          <span v-if="ent == '01'">生产</span>
          <span v-else-if="ent == '02'">经营</span>
          <span v-else-if="ent == '03'">使用</span>
          <span v-else-if="ent == '04'">第一类非药品易制毒</span>
          <span v-else></span>
          &nbsp;&nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;&nbsp;
          <span>地址：{{ address ? address : " " }}</span>
        </div>
      </div>
      <!-- <div class="newEnterpriseR">
          <div :class="['safeStatus' + riskGrade]"></div>
        </div> -->
    </div>
    <el-tabs v-model="activeName" @tab-click="handleTab" v-loading="tabLoading">
      <el-tab-pane label="企业基本信息" name="first">
        <BaseInfo></BaseInfo>
      </el-tab-pane>
      <el-tab-pane label="两重点一重大" name="second">
        <FocusOnAndMajor ref="FocusOnAndMajor"></FocusOnAndMajor>
      </el-tab-pane>
      <el-tab-pane label="安全生产基本信息" name="third">
        <SafetyInformation
          style="height: 480px"
          :enterpriseId="enterpriseInfo.id"
          ref="safetyInformation"
        ></SafetyInformation>
      </el-tab-pane>
      <el-tab-pane label="动态感知" name="fourth">
        <DynamicPerception></DynamicPerception>
      </el-tab-pane>

      <el-tab-pane label="画像指数信息" name="fifth"> </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import BaseInfo from "./components/baseInfo.vue";
import IndustyInfo from "./components/industyInfo";
import safeProduction from "./components/safeProduction.vue";
import FocusOnAndMajor from "./components/FocusOnAndMajor.vue";
import DynamicPerception from "./components/dynamicPerception.vue";
import SafetyInformation from "../../enterpriseManagement/safetyInformation/index.vue";
import { reportDetail, riskReportInfo } from "./mock";
export default {
  name: "enterprisePortraitReportDetail",
  components: {
    BaseInfo,
    IndustyInfo,
    safeProduction,
    FocusOnAndMajor,
    DynamicPerception,
    SafetyInformation,
  },
  props: {
    enterpriseInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      enterpriseName: "武汉有机实业有限公司",
      name: "张三",
      tel: "1354456834",
      address: "武汉市青山区",
      ent: "01",
      riskGrade: "武汉市青山区",
      tabLoading: false,
      activeName: "first",
      reportInfo: {},
      riskReportInfo: riskReportInfo,
    };
  },
  mounted() {
    this.reportInfo = reportDetail.data;
  },
  methods: {
    handleTab(tab) {
      this.activeName = tab.name;
    },
  },
};
</script>
<style lang="scss" scoped>
.report-detail {
  width: 100%;
  .newEnterprise {
    background: url("../../../../../static/img/riskbg.png") no-repeat top;
    background-size: 100% 100%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 0 0 20px;
    color: #fff;
    min-height: 100px;
    .titH2 span {
      color: #fff;
      text-align: left;
      font-size: 20px;
      font-weight: bold;
      display: inline-block;
      padding: 0 0 5px;
    }
    button.el-button.el-tooltip.botton.item.el-button--text {
      text-align: left;
    }
    .safeStatusnull,
    .safeStatus {
      background: url("../../../../../static/img/蓝-低风险.png") no-repeat top
        right;
      height: 100px;
      width: 159px;
      background-size: 100%;
    }
    .safeStatus1 {
      background: url("../../../../../static/img/红-重大风险.png") no-repeat top
        right;
      height: 100px;
      width: 159px;
      background-size: 100%;
    }
    .safeStatus2 {
      background: url("../../../../../static/img/橙-较大风险.png") no-repeat top
        right;
      height: 100px;
      width: 159px;
      background-size: 100%;
    }
    .safeStatus3 {
      background: url("../../../../../static/img/黄-一般风险.png") no-repeat top
        right;
      height: 100px;
      width: 159px;
      background-size: 100%;
    }
    .safeStatus4 {
      background: url("../../../../../static/img/蓝-低风险.png") no-repeat top
        right;
      height: 100px;
      width: 159px;
      background-size: 100%;
    }
  }
}
</style>
