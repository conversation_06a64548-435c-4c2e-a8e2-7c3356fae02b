import axios from "axios";
import qs from "qs";

//重大危险源下拉数据
export const getDengerListData = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/danger/list/' + data.enterpid,
    params: data,
  });
};

//设备下拉列表
export const getEquipmentData = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/equipment/list/' + data.dangerid,
    params: data,
  });
};
//视频设备下拉列表
export const getVideoData = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/video/list/' + data.enterpid,
    params: data,
  });
};
//申报原因下拉列表
export const getReasonData = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/reason',
    params: data,
  });
};

//上传
export const uploadApi = data => {
  return axios({
    method: "post",
    headers: {
      'Content-Type': 'multipart/form-data' //改这里就好了
    },
    url: '/file/upload',
    data: data,
  });
};
//获取entid
export const getEnt = data => {
  return axios({
    method: "get",
    url: '/enterprise/information/ent/info',
    params: data,
  });
};
//设备/视频新增/编辑
export const addOrEdit = data => {
  return axios({
    method: "post",
    url: '/enterprise/abnormalEquipment/save',
    data: data,
  });
};
//异常设备列表
export const getAbnormalList = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/list',
    params: data,
  });
};
//详情
export const getAbnormalDetail = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/details/' + data.id,
    params: data,
  });
};
//审核
export const examine = data => {
  return axios({
    method: "post",
    url: '/enterprise/abnormalEquipment/examine',
    data: data,
  });
};
//删除

export const getAbnormalEquipmentDel = data => {
  return axios({
    method: "get",
    url: '/enterprise/abnormalEquipment/del/' + data,
  })
}
//恢复启用
export const postAbnormalEquipmentResumeEnable = data => {
  return axios({
    method: "post",
    url: '/enterprise/abnormalEquipment/resumeEnable/' + data,
  })
}
//设备/视频导出
export const getEquipmentExportExcel = table => {
  return axios({
    method: "post",
    url: "/enterprise/abnormalEquipment/list/exportExcel",
    data: { ...table },
    responseType: "arraybuffer"
  })
};

//在线抽查获取行业
export const getIndustry = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/getIndustry',
    data: data,
  });
};
//在线抽查获取规模
export const getScale = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/getScale',
    data: data,
  });
};
//在线抽查获取重点监管危化品
export const getHazarchem = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/getHazarchem',
    data: data,
  });
};
//在线抽查获取重点监管危化工艺
export const getKnoRegprocess = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/getKnoRegprocess',
    data: data,
  });
};
//在线抽查列表
export const getSpotCheck = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/getSpotCheck?current=' + data.current + '&size=' + data.size,
    data: data
  });
};
//加入抽查
export const addSpotCheck = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/addSpotCheck',
    data: data,
  });
};
//在线抽查-历史抽查记录列表
export const querySpotCheckHis = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/querySpotCheckHis?current=' + data.current + '&size=' + data.size,
    data: data
  });
};
//在线抽查-历史抽查记录下发
export const issueSpotCheck = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/issueSpotCheck',
    data: data
  });
};
//在线抽查-历史抽查记录删除
export const deleteSpotCheckById = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/deleteSpotCheckById?id=' + data.id,
    data: data
  });
};
//在线抽查-反馈详情
export const spotCheckHisDetail = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/spotCheckHisDetail?id=' + data.id,
    data: data
  });
};
//在线抽查-一键抽查
export const oneClick = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/oneClick',
    data: data
  });
};
//企业反馈
export const addSpotCheckes = data => {
  return axios({
    method: "post",
    url: '/enterprise/onlineSpotCheck/feedBackSpotCheckHis',
    data: data,
  });
};
//导出
export const exportSpotCheckHis = data => {
  return axios({
    method: "post",
    url: "/enterprise/onlineSpotCheck/exportSpotCheckHis",
    data: data,
    responseType: "arraybuffer"
  })
};
export const getParkList = data => {
  return axios({
    method: "post",
    url: '/admin/org/park/list',
    data: data,
  });
};