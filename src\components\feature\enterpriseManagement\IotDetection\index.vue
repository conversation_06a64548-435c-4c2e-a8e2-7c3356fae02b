<template>
  <div class="lot">
    <div class="header">
      <div class="operation">
        <div class="inputBox">
          <el-input
            v-model.trim="dangerName"
            placeholder="请输入重大危险源/设备名称"
            class="input"
            size="mini"
            clearable
            @clear="clearDangerName(event)"
          ></el-input>
          <el-select
            v-model="sensortypeCode"
            size="mini"
            placeholder="请选择设备类型"
            clearable
            @clear="clearSensortypeCode($event)"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="state"
            size="mini"
            placeholder="请选择当前状态"
            clearable
            @clear="clearState($event)"
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-date-picker
            v-model="value1"
            size="mini"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
          >
          </el-date-picker>

          <el-button type="primary" size="mini" @click="search">查询</el-button>
        </div>
        <CA-button
          type="primary"
          plain
          class="export"
          size="mini"
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        v-loading="loading"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        @row-click="goEnt"
        ref="multipleTable"
        @selection-change="handleSelectionChange"
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="40"
          fixed="left"
          align="center"
        >
        </el-table-column>
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="dangerName"
          label="重大危险源名称"
          min-width="250"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="sensortypeCode"
          label="设备类型"
          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.sensortypeCode == 'G0'">储罐</span>
            <span v-else-if="scope.row.sensortypeCode == 'Q0'">泄漏点</span>
            <span v-else-if="scope.row.sensortypeCode == 'P0'">装置</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="monitorname"
          label="设备名称"
          min-width="200"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="monitemkey"
          label="指标类型"
          width="80"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="targetName"
          label="指标名称"
          width="150"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="monvalue"
          label="报警值"
          align="center"
          width="80"
        >
          <template slot-scope="scope">
            <div
              @mouseenter="showTip(scope.row.warningId)"
              @mouseleave="tipData = {}"
            >
              <el-tooltip effect="dark" placement="right">
                <div slot="content">
                  <div
                    v-loading="tipLoading"
                    element-loading-spinner="el-icon-loading"
                    element-loading-background="rgba(0, 0, 0, 0.8)"
                  >
                    <span>监测指标：{{ scope.row.targetName }}</span
                    ><br />
                    <span>一级上限：{{ tipData.up1 | numToFixed(2) }}</span
                    ><br />
                    <span>一级下限：{{ tipData.down1 | numToFixed(2) }}</span
                    ><br />
                    <span>二级上限：{{ tipData.up2 | numToFixed(2) }}</span
                    ><br />
                    <span>二级下限：{{ tipData.down2 | numToFixed(2) }}</span
                    ><br />
                    <span
                      >仪表量程上限：{{ tipData.rangeUp | numToFixed(2) }}</span
                    ><br />
                    <span
                      >仪表量程下限：{{
                        tipData.rangeDown | numToFixed(2)
                      }}</span
                    ><br />
                  </div>
                </div>
                <el-button type="text">{{ scope.row.monvalue }}</el-button>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="warningType"
          label="报警类型"
          align="center"
          width="80"
        >
        </el-table-column>
        <el-table-column
          prop="warningTime"
          label="报警时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="alarmHandelTime"
          label="消警时间"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column
          prop="warningDuration"
          label="报警时长"
          align="center"
          width="160"
        >
        </el-table-column>
        <el-table-column label="当前状态" align="center" fixed="right">
          <template slot-scope="scope">
            <span>{{ scope.row.state == 0 ? "已消警" : "未消警" }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { getLot, lotExportExcel, getMonitoringMonvalue } from "@/api/entList";
import { parseTime } from "@/utils/index";
export default {
  //import引入的组件
  name: "lot",
  components: {},
  data() {
    return {
      currentPage: 1,
      dangerName: "",
      selection: [],
      options: [
        {
          value: "G0",
          label: "储罐",
        },
        {
          value: "Q0",
          label: "泄漏点",
        },
        {
          value: "P0",
          label: "装置",
        },
      ],
      options1: [
        {
          value: "0",
          label: "已消警",
        },
        {
          value: "1",
          label: "未消警",
        },
      ],
      sensortypeCode: "",
      state: "",
      value1: "",
      starTime: "",
      endTime: "",
      total: "",
      tableData: [],
      loading: true,
      enterpriseId: "",
      tipData: {},
      tipLoading: false,
    };
  },
  watch: {
    enterpriseId: {
      handler(newVal, oldVal) {
        this.currentPage = 1;
      },
      deep: true,
      immediate: true,
    },
  },
  filters: {
    numToFixed(val) {
      if (val) {
        return Number(val).toFixed(2);
      } else {
        return val;
      }
    },
  },
  //方法集合
  methods: {
    showTip(id) {
      this.tipLoading = true;
      getMonitoringMonvalue(id).then((res) => {
        this.tipLoading = false;
        this.tipData = res.data.data;
      });
    },
    goEnt(val) {
      console.log(val);
      let bool = true;
      this.$emit("entBool", bool);
    },
    getData(id) {
      this.loading = true;
      this.enterpriseId = id;
      getLot({
        enterpriseId: this.enterpriseId,
        //  enterpriseId:'17acd1e0e',
        current: this.currentPage,
        dangerName: this.dangerName,
        sensortypeCode: this.sensortypeCode,
        state: this.state,
        startTime: this.starTime,
        endTime: this.endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.total = res.data.data.total;
          this.tableData = res.data.data.records;
        }
      });
    },

    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    search() {
      this.currentPage = 1;
      this.getData(this.enterpriseId);
    },
    clearSensortypeCode(e) {
      this.sensortypeCode = "";
    },
    clearState(e) {
      this.state = "";
    },
    clearDangerName(e) {
      this.dangerName = "";
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData(this.enterpriseId);
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId;
      }
    },
    // 导出
    exportExcel() {
      lotExportExcel({
        enterpId: this.enterpriseId,
        current: this.currentPage,
        dangerName: this.dangerName,
        sensortypeCode: this.sensortypeCode,
        state: this.state,
        startTime: this.starTime,
        endTime: this.endTime,
        ids: this.selection,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "物联监测" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.lot {
  background-color: #fff;
  // padding-top: 15px;
  .header {
    .title {
      margin-left: 20px;
      font-weight: 600;
    }
    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        width: 1020px;
        display: flex;
        justify-content: space-between;
        .input {
          width: 180px;
        }
      }
    }
    .export {
      margin-right: 20px;
    }
  }
  .table {
    width: 100%;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
