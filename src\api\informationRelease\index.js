import axios from "axios";
//根据用户查询所在政府和企业机构树
export const orgTreeByorgcode = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/org/trees/all/v1",
    data: data,
  });
};
export const orgTreeUser = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/org/id/v1",
    data: data,
  });
};
//通知公告
export const getNoticeList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/notice/notice/list/v1",
    data: data,
  });
};

//新增模板
export const getTempleAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/temple/add/v1",
    data: data,
  });
};
//修改模板
export const getTempleUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/temple/update/v1",
    data: data,
  });
};
//查询全部模板
export const getTempleList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/temple/find/v1",
    data: data,
  });
};
export const getTempleListAll = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/temple/list/v1",
    data: data,
  });
};
//根据主键ID查询模板
export const getTemple = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/temple/tmpltCode/v1",
    data: data,
  });
};
// 根据主键ID删除模板
export const delTemple = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/temple/delete/v1",
    data: data,
  });
};

//安全警示条件查询
export const listSafetys = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/safety/warning/list/v1",
    data: data,
  });
};
//安全警示新增
export const addSafety = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/safety/warning/save/v1",
    data: data,
  });
};
//新增通知公告;
export const addNotice = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/notice/notice/add/v1",
    data: data,
  });
};

export const getNotice = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/notice/notice/detail/v1",
    data: data,
  });
};
// 修改通知公告
export const updateNotice = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/notice/edit/v1",
    method: "post",
    data: data,
  });
};
//删除通知公告
export const delNotice = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/notice/delete/v1",
    method: "post",
    data: data,
  });
};
//批量发布通知公告
export const batchUpdateGeNoticeNoticeByIds = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/notice/send/batch/v1",
    method: "post",
    data: data,
  });
};
//批量删除通知公告
export const noticeDeleteBatch = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/notice/delete/batch/v1",
    method: "post",
    data: data,
  });
};


//
//根据用户查询所在政府和企业机构树
export const superviserTree = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/org/trees/superviser/search/v1",
    data: data,
  });
};

//事故警示
export const noticeEvent = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/event/page/v1",
    method: "post",
    data: data,
  });
};
//事故警示
export const noticeAdd = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/event/save/v1",
    method: "post",
    data: data,
  });
};

//事故警示
export const noticeUpdate = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/event/update/v1",
    method: "post",
    data: data,
  });
};

export const noticeDelete = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/event/delete/v1?id=" + data.id,
    method: "post",
    data: data,
  });
};

export const noticeId = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/notice/event/id/v1?id=" + data.id,
    method: "post",
    data: data,
  });
};

//事故类型与事故等级列表
export const eventCode = (data) => {
  return axios({
    url: "/gemp-chemical/api/gemp/event/code/v1",
    method: "post",
    data: data,
  });
};








