<template>
  <div class="riskWarning" v-loading="loadingBox">
    <div class="header">
      <div class="title">危险化学品企业情况</div>
      <!-- 更多 -->
      <div
        class="more"
        @click="toUrl('/gardenEnterpriseManagement/entManagement')"
      >
        更多
      </div>
    </div>

    <div class="enterprise-content">
      <div
        class="item clickable"
        v-for="(item, index) in displayEnterpriseList"
        :key="index"
        @click="handleEnterpriseClick(item, index)"
        @mouseenter="handleMouseEnter(item, index, $event)"
        @mouseleave="handleMouseLeave(index)"
      >
        <div class="item-label">{{ item.label }}企业</div>
        <div class="item-count">{{ item.count }}</div>
        <div class="item-percent">{{ item.percent }}</div>
      </div>
    </div>

    <!-- 悬浮提示框 -->
    <div
      v-if="hoverTooltipVisible"
      class="hover-tooltip"
      :style="{
        position: 'fixed',
        left: hoverTooltipPosition.x + 'px',
        top: hoverTooltipPosition.y + 'px',
        zIndex: 9999,
      }"
      @mouseenter="isMouseOverTooltip = true"
      @mouseleave="handleTooltipMouseLeave"
    >
      <div class="tooltip-content">
        <div class="tooltip-title">详细分类统计</div>
        <div
          class="tooltip-item"
          v-for="(item, index) in categorySubList"
          :key="index"
          @click="showCategoryEnterpriseList(item)"
        >
          <div class="tooltip-item-label">{{ item.label }}</div>
          <div class="tooltip-item-count">{{ item.count }}家</div>
        </div>
      </div>
    </div>
    <div class="center-content">
      <div class="center-left">
        <div class="header">
          <div class="title">重大危险源情况</div>
          <div>
            <span
              @click="changeDangerType(1)"
              :class="{ active: dangerType == 1 }"
              >区域</span
            >
            <span
              @click="changeDangerType(2)"
              :class="{ active: dangerType == 2 }"
              >等级</span
            >
          </div>
        </div>
        <div class="left-content">
          <div class="area-list" v-if="dangerType == 1">
            <div
              class="area-item"
              v-for="(item, index) in areaList"
              :key="index"
            >
              <span class="area-name">{{ item.areaName }}</span>
              <span class="area-num">{{ item.total }}处</span>
            </div>
          </div>
          <div class="area-list" v-else>
            <!-- 等级展示内容 -->
            <div
              class="area-item"
              v-for="(item, index) in levelList"
              :key="index"
            >
              <span class="area-name">{{ item.name }}</span>
              <span class="area-num">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="center-right">
        <div class="header">
          <div class="title">重点监管工艺</div>
          <div>
            <span
              @click="changeProcessType(1)"
              :class="{ active: processType == 1 }"
              >企业</span
            >
            <span
              @click="changeProcessType(2)"
              :class="{ active: processType == 2 }"
              >工艺</span
            >
          </div>
        </div>
        <div class="right-content">
          <div v-if="processType == 1">
            <div class="process-table">
              <div class="table-body">
                <div
                  class="table-row"
                  v-for="(item, index) in processListOne"
                  :key="index"
                >
                  <div class="table-cell">{{ item.enterName }}</div>
                  <div class="table-cell">{{ item.total }}种</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else>
            <div class="process-table">
              <div class="table-body">
                <div
                  class="table-row"
                  v-for="(item, index) in processListTwo"
                  :key="index"
                >
                  <div class="table-cell">{{ item.processname }}</div>
                  <div class="table-cell">
                    <span
                      class="link"
                      style="color: #3977ea; cursor: pointer"
                      @click="showEnterpriseList(item)"
                    >
                      {{ item.total }}家
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- <div class="warning-order">
      <div>预警工单情况</div>
      <div class="show-more" @click="toUrl('/earlyWarningDisposal/hiddenRisk')">
        更多
      </div>
    </div> -->
    <!-- <div class="warning-container">
      <div class="card-list">
        <div class="card-item" v-for="(item, index) in warningOrderList" :key="index">
          <div class="card-item-header">
            <div class="card-item-header-left" @click="viewWarningDetail(item)">{{ item.disposeUnitName }}</div>
            <div class="card-item-header-right">{{ item.createTime }}</div>
          </div>
          <div class="card-item-content">
            <div class="card-item-content-level">
              <span v-if="item.eventLevel=='1'">风险等级: <span style="color:red">红</span></span>
              <span v-if="item.eventLevel=='2'">风险等级: <span style="color:red">橙 </span></span>
              <span v-if="item.eventLevel=='3'">风险等级: <span style="color:red">黄 </span></span>
              <span v-if="item.eventLevel=='4'">风险等级: <span style="color:red">蓝</span></span>
            </div>
            <div class="card-item-content-description">
              需处理的预警信息: {{ item.eventDescription }}
            </div>
            <div style="text-align: right">
              <el-button type="primary" size="mini" @click="viewWarningDetail(item)">查看</el-button>
            </div>
          </div>
        </div>
      </div>
    </div> -->
    <!-- <div class="warning-title">
      <img
        style="height: 20px"
        src="../../../../../static/img/assets/img/title-two.png"
        alt=""
      />
      预警企业数
      <div class="warning-num" @click="openDialog()">
        <span
          style="
            font-family: PingFang SC;
            font-weight: bold;
            font-size: 26px;
            color: #333333;
          "
          >{{ box.warningTotalNum }}</span
        ><span style="color: #999999; margin-left: 5px">家</span>
      </div>
    </div>
    <div class="warning-content">
      <div class="warning-box box-red-bg">
        <div class="red-content">红色预警</div>
        <div class="warning-numRed" @click="warn('1')">
          {{ box.redWarningNum }}
        </div>
      </div>
      <div class="warning-box box-orange-bg">
        <div class="orange-content">橙色预警</div>
        <div class="warning-numOrange" @click="warn('2')">
          {{ box.orangeWarningNum }}
        </div>
      </div>
      <div class="warning-box box-yellow-bg">
        <div class="yellow-content">黄色预警</div>
        <div class="warning-numYellow" @click="warn('3')">
          {{ box.yellowWarningNum }}
        </div>
      </div>
    </div> -->
    <!-- <div class="container">
      <div class="left">
        <div class="title">预警企业数</div>
        <div class="num" @click="openDialog()">{{ box.warningTotalNum }}</div>
      </div>
      <div class="right">
        <div>
          <img src="../../../../../static/img/red_icon.png" alt="" />
          <div class="title">红色预警</div>
          <div class="numRed" @click="warn('1')">{{ box.redWarningNum }}</div>
        </div>
        <div>
          <img src="../../../../../static/img/orange_icon.png" alt="" />
          <div class="title">橙色预警</div>
          <div class="numOrange" @click="warn('2')">
            {{ box.orangeWarningNum }}
          </div>
        </div>
        <div>
          <img src="../../../../../static/img/yellow_icon.png" alt="" />
          <div class="title">黄色预警</div>
          <div class="numYellow" @click="warn('3')">
            {{ box.yellowWarningNum }}
          </div>
        </div>
      </div>
    </div> -->
    <!-- <div class="echarts" v-show="showEchart" id="riskWarningEcharts"></div>
    <div class="null" v-show="!showEchart"></div> -->
    <div class="dialog">
      <el-dialog
        title="风险动态预警"
        :visible.sync="show"
        width="1350px"
        top="10vh"
        @close="handleClose"
        :key="demoKey"
        v-dialogDrag
        :close-on-click-modal="false"
      >
        <el-scrollbar class="diagHeight">
          <div class="seach-part">
            <div class="l">
              <el-cascader
                size="mini"
                placeholder="请选择行政区划"
                :options="district"
                v-model="districtVal"
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                clearable
                :show-all-levels="true"
                v-if="isShowDist"
              ></el-cascader>
              <el-select
                v-model="rank"
                size="mini"
                placeholder="请选择最高预警等级"
                clearable
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-select
                v-model="warnStatus"
                size="mini"
                placeholder="请选择预警状态"
                clearable
              >
                <el-option
                  v-for="item in options1"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-date-picker
                v-model="date"
                size="mini"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="searchTime"
                unlink-panels
                clearable
              >
              </el-date-picker>
              <el-button type="primary" size="mini" @click="getDialogData()"
                >查询</el-button
              >
              <!-- <CA-button type="primary" size="mini" plain @click="exportExcel()"
              >导出</CA-button
            > -->
            </div>
          </div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <el-table-column type="selection" width="50" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="单位名称"
                min-width="180"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    @click="goEnt(row)"
                    style="color: #3977ea; cursor: pointer"
                  >
                    {{ row.companyName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="isShowDist == true ? '行政区划' : '归属园区'"
                width="120"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="isShowDist == true">{{ row.distName }}</span>
                  <span v-else>{{ park.parkName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="最高预警等级" width="120" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.areaName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="触发黄色预警时间"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.yellowWarningTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="触发橙色预警时间"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.orangeWarningTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="触发红色预警时间"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.redWarningTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="消警时间" min-width="110" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.endTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="消警状态" width="90" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.warnStatus }}
                    <!-- {{ row.warnStatus == 0 ? "未消警" : "已消警" }} -->
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <el-button
                      v-if="row.warnStatus == '0' && user.user_type == 'gov'"
                      type="text"
                      @click="NotificationBool(row)"
                      >通报</el-button
                    ><el-button type="text" @click="WarnBool(row)"
                      >详情</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.size"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </el-scrollbar>
      </el-dialog>
    </div>
    <warningOrder
      v-if="showWarningOrder"
      :showWarningOrder="showWarningOrder"
      :warningOrderItem="warningOrderItem"
      :key="demoKey"
      @close="showWarningOrder = false"
    ></warningOrder>
    <el-dialog
      :title="currentProcessName + ' 关联企业列表'"
      :visible.sync="showEnterpriseDialog"
      width="500px"
    >
      <el-table :data="enterpriseListByProcess" style="width: 100%">
        <el-table-column prop="enterpname" label="企业名称" />
      </el-table>
    </el-dialog>

    <!-- 企业列表弹窗 -->
    <el-dialog
      :title="enterpriseListDialogTitle"
      :visible.sync="enterpriseListDialogVisible"
      width="1000px"
      top="5vh"
      :close-on-click-modal="false"
      append-to-body
    >
      <el-table
        style="width: 100%; min-height: 600px"
        :data="enterpriseListData"
        v-loading="enterpriseListLoading"
        border
        stripe
      >
        <el-table-column
          prop="enterpName"
          label="企业名称"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column prop="districtName" label="行政区划" width="150" />
        <el-table-column
          prop="enterpriseTypeName"
          label="企业类型"
          width="100"
        />
        <el-table-column label="操作" width="80" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="goEnt(scope.row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div
        class="pagination-container"
        style="margin-top: 20px; text-align: center"
      >
        <el-pagination
          @size-change="handleEnterpriseListSizeChange"
          @current-change="handleEnterpriseListCurrentChange"
          :current-page="enterpriseListPagination.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="enterpriseListPagination.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="enterpriseListPagination.total"
        >
        </el-pagination>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEnterpriseListDialog">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  postCimEarlyWarningPushList,
  postCimEarlyWarningWorkLedgerWarningDetails,
  cimEarlyWarningCount,
  cimEarlyWarningExportExcel,
  getWarningOrderList,
  getDangerChemicalList,
  getImportantProcessList,
  getMajorHazardByArea,
  getMajorHazardByLevel,
  getImportantProcessCompany,
} from "@/api/riskAssessment";
import { getEnt } from "@/api/dailySafety";
import { getEnterpriseList } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
import warningOrder from "./warningOrder";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  components: {
    warningOrder,
  },
  data() {
    return {
      totalCount: 0,
      enterpriseList: [],
      enterpriseFullData: [], // 存储完整的企业数据（包含subDTOs）
      // 第二个分类弹窗相关数据
      categoryDialogVisible: false,
      categoryDialogTitle: "",
      categorySubList: [], // 动态从接口数据中获取
      // 企业列表弹窗相关数据
      enterpriseListDialogVisible: false,
      enterpriseListDialogTitle: "",
      enterpriseListData: [],
      enterpriseListLoading: false,
      // 企业列表分页数据
      enterpriseListPagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      currentManagementType: "", // 当前查询的管理类型
      // 悬浮提示框相关数据
      hoverTooltipVisible: false,
      hoverTooltipPosition: { x: 0, y: 0 },
      isMouseOverTooltip: false, // 鼠标是否在提示框上
      showWarningOrder: false,
      warningOrderItem: {},
      warningOrderList: [],
      demoKey: 0,
      box: {},
      echartData: [],
      currentPage: 1,
      show: false,
      level: [
        {
          label: "一级",
          value: "1",
        },
        {
          label: "二级",
          value: "2",
        },
        {
          label: "三级",
          value: "3",
        },
        {
          label: "四级",
          value: "4",
        },
      ],
      levelVal: ["1", "2", "3", "4"],
      input: "",
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      value: "",
      tableData: {},
      districtLoading: false,
      loading: false,
      loadingBox: false,
      selection: [],
      warnStatus: "",
      rank: "",
      date: [
        new Date(new Date().toLocaleDateString()).getTime(),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      startDate: "",
      endDate: "",
      showEchart: true,
      options1: [
        {
          value: "1",
          label: "已消警",
        },
        {
          value: "0",
          label: "未消警",
        },
      ],
      options: [
        {
          value: "1",
          label: "红色预警",
        },
        {
          value: "2",
          label: "橙色预警",
        },
        {
          value: "3",
          label: "黄色预警",
        },
      ],
      dangerType: 1, // 1: 区域, 2: 等级
      areaList: [],
      levelList: [],
      processType: 1, // 1: 企业, 2: 工艺
      processListOne: [],
      processListTwo: [],
      showEnterpriseDialog: false,
      enterpriseListByProcess: [],
      currentProcessName: "",
    };
  },
  //方法集合
  methods: {
    // 鼠标移入处理
    handleMouseEnter(item, index, event) {
      // 只有第二个分类才显示悬浮提示
      if (index === 2) {
        // 获取鼠标位置
        const rect = event.target.getBoundingClientRect();
        this.hoverTooltipPosition = {
          x: rect.right + 10, // 在元素右侧10px处显示
          y: rect.top + rect.height / 2 - 100, // 垂直居中
        };

        // 获取子分类数据
        this.getCategorySubData(item);
        this.hoverTooltipVisible = true;
      }
    },
    // 鼠标移出处理
    handleMouseLeave(index) {
      // 只有第二个分类才处理鼠标移出
      if (index === 2) {
        // 延迟隐藏，给用户时间移动到提示框
        setTimeout(() => {
          if (!this.isMouseOverTooltip) {
            this.hoverTooltipVisible = false;
          }
        }, 100);
      }
    },
    // 提示框鼠标移出处理
    handleTooltipMouseLeave() {
      this.isMouseOverTooltip = false;
      this.hoverTooltipVisible = false;
    },
    // 处理企业分类点击事件
    handleEnterpriseClick(item, index) {
      if (index === 2) {
        // 第二个分类：不再直接显示弹窗，改为鼠标悬停显示
        return;
      } else {
        // 其他分类：跳转到企业管理页面
        this.$router.push({
          path: "/gardenEnterpriseManagement/entManagement",
          query: {
            enterpriseType: item.value || item.id, // 企业类型ID
            fromDashboard: true, // 标记来源于看板
          },
        });
      }
    },
    // 显示分类详情弹窗
    showCategoryDialog(item) {
      this.categoryDialogTitle = `${item.label}企业详细分类`;
      // 这里可以调用接口获取具体的子分类数据
      // 暂时使用模拟数据
      this.getCategorySubData(item);
      this.categoryDialogVisible = true;
    },
    // 获取子分类数据（从接口返回的subDTOs中获取）
    getCategorySubData(item) {
      // 从完整数据中查找对应的项目
      const fullDataItem = this.enterpriseFullData.find(
        (fullItem) => fullItem.id === item.id
      );

      if (
        fullDataItem &&
        fullDataItem.subDTOs &&
        fullDataItem.subDTOs.length > 0
      ) {
        // 使用接口返回的subDTOs数据
        this.categorySubList = fullDataItem.subDTOs.map((subItem) => ({
          id: subItem.id,
          label: subItem.label,
          count: subItem.count,
          type: subItem.id, // 使用id作为type
        }));
      } else {
        // 如果没有子分类数据，设置为空数组
        this.categorySubList = [];
      }
    },
    // 关闭分类弹窗
    closeCategoryDialog() {
      this.categoryDialogVisible = false;
    },
    // 显示分类企业列表
    showCategoryEnterpriseList(categoryItem) {
      this.enterpriseListDialogTitle = `${categoryItem.label} - 企业列表`;
      this.enterpriseListLoading = true;
      this.enterpriseListDialogVisible = true;

      // 保存当前查询的管理类型
      this.currentManagementType = categoryItem.id || categoryItem.type;

      // 重置分页数据
      this.enterpriseListPagination.current = 1;
      this.enterpriseListPagination.total = 0;

      // 调用真实接口获取企业数据
      this.getEnterpriseDataByType(this.currentManagementType);
    },
    // 根据类型获取企业数据
    getEnterpriseDataByType(managementType) {
      const params = {
        distCode: 420100,
        enterpName: "",
        enterpriseType: "",
        // level: "1,2,3,4",
        current: this.enterpriseListPagination.current,
        processIds: "",
        hazChemName: "",
        size: this.enterpriseListPagination.size,
        managementType: managementType, // 传入小类编码
      };

      getEnterpriseList(params)
        .then((res) => {
          if (res.status === 200 && res.data && res.data.data) {
            this.enterpriseListData = res.data.data.records || [];
            this.enterpriseListPagination.total = res.data.data.total || 0;
          } else {
            this.enterpriseListData = [];
            this.enterpriseListPagination.total = 0;
            console.warn("获取企业列表失败:", res);
          }
          this.enterpriseListLoading = false;
        })
        .catch((error) => {
          console.error("获取企业列表出错:", error);
          this.enterpriseListData = [];
          this.enterpriseListPagination.total = 0;
          this.enterpriseListLoading = false;
        });
    },
    // 关闭企业列表弹窗
    closeEnterpriseListDialog() {
      this.enterpriseListDialogVisible = false;
      this.enterpriseListData = [];
      this.currentManagementType = "";
      // 重置分页数据
      this.enterpriseListPagination.current = 1;
      this.enterpriseListPagination.total = 0;
    },

    // 企业列表分页大小改变
    handleEnterpriseListSizeChange(size) {
      this.enterpriseListPagination.size = size;
      this.enterpriseListPagination.current = 1; // 重置到第一页
      this.enterpriseListLoading = true;
      this.getEnterpriseDataByType(this.currentManagementType);
    },

    // 企业列表当前页改变
    handleEnterpriseListCurrentChange(current) {
      this.enterpriseListPagination.current = current;
      this.enterpriseListLoading = true;
      this.getEnterpriseDataByType(this.currentManagementType);
    },

    changeDangerType(val) {
      this.dangerType = val;
    },
    changeProcessType(val) {
      this.processType = val;
    },
    viewWarningDetail(item) {
      console.log(item);
      this.warningOrderItem = item;
      this.showWarningOrder = true;
    },
    getWarningOrderList() {
      getWarningOrderList({}).then((res) => {
        this.warningOrderList = res.data.data;
      });
    },
    getDangerChemicalList() {
      const params = {
        distCode: 420100,
        level: "1,2,3,4",
      };
      getDangerChemicalList(params).then((res) => {
        this.totalCount = res.data.data.totalCount;

        // 处理显示用的企业列表
        this.enterpriseList = res.data.data.dtos.map((item) => ({
          ...item,
          label: item.label.slice(0, 2), // 截取前两位用于显示
        }));
      });
      getDangerChemicalList({ distCode: 420100 }).then((res) => {
        this.enterpriseFullData = res.data.data.dtos;
        // 计算市级看板总数（不带level参数的数据总数）
        this.cityLevelTotalCount = res.data.data.totalCount || 0;
      });
    },
    getImportantProcessList(type) {
      const params = {
        paraType: type,
      };
      getImportantProcessList(params).then((res) => {
        if (type == 1) {
          this.processListOne = res.data.data;
        } else {
          this.processListTwo = res.data.data;
        }
      });
    },
    getMajorHazardByArea() {
      const params = {
        distCode: "420100",
        current: 1,
        size: 100,
      };
      getMajorHazardByArea(params).then((res) => {
        res.data.data.shift(); // 去除第一个元素
        this.areaList = res.data.data;
      });
    },
    getMajorHazardByLevel() {
      const params = {
        distCode: "420100",
        current: 1,
        size: 100,
      };
      getMajorHazardByLevel(params).then((res) => {
        const dataAll = res.data.data[0];
        this.levelList = [];
        this.levelList.push(
          {
            name: "一级",
            value: dataAll.levelOneMajorHazardSource,
          },
          {
            name: "二级",
            value: dataAll.levelTwoMajorHazardSource,
          },
          {
            name: "三级",
            value: dataAll.levelThreeMajorHazardSource,
          },
          {
            name: "四级",
            value: dataAll.levelFourMajorHazardSource,
          }
        );
      });
    },
    exportExcel() {},
    openDialog() {
      this.show = true;
      this.levelVal = ["1", "2", "3", "4"];
      this.currentPage = 1;
      this.getDialogData();
      this.rank = "";
    },
    WarnBool() {
      this.$router.push("/riskAssessment/riskEarlyWarningPush");
    },
    toUrl(url) {
      this.$router.push(url);
    },
    warn(type) {
      this.rank = type;
      this.show = true;
      this.levelVal = ["1", "2", "3", "4"];
      this.currentPage = 1;
      this.getDialogData();
    },
    handleClose() {
      // this.$emit("close")
      this.tableData = [];
      this.currentPage = 1;
      this.levelVal = ["1", "2", "3", "4"];
      this.warnStatus = "";
      this.rank = "";
      this.districtVal = this.$store.state.login.userDistCode;
      this.date = [
        new Date(new Date().toLocaleDateString()).getTime(),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ];
      // 初始化弹窗位置
      this.demoKey =
        "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    },
    getDialogData() {
      if (this.$store.state.login.user.user_type == "ent") {
        getEnt({}).then((res) => {
          if (res.data.code == 0) {
            // this.enterpid= res.data.data.enterpId;
            this.getDialogDataes(res.data.data.enterpId);
          }
        });
      } else {
        this.getDialogDataes();
      }
    },
    getDialogDataes(id) {
      this.loading = true;
      postCimEarlyWarningPushList({
        // companyName: "string",
        companyCode: id,
        distCode: this.districtVal,
        startDate: new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        endDate: new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss"),
        //
        // parkId: "string",
        rank: this.rank,
        warnStatus: this.warnStatus,
        size: 10,
        current: this.currentPage,
      }).then((res) => {
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
    handleCurrentChange() {
      this.currentPage = 1;
      this.getDialogData();
    },
    select(selection, row) {
      this.selection = [];
      // console.log(selection);
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },
    searchTime(value) {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd hh:mm:ss");
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit(
        "controler/updateEntId",
        row.companyCode || row.enterpId
      );
    },
    getEcharts() {
      var chartDom = document.getElementById("riskWarningEcharts");
      var myChart = this.$echarts.init(chartDom);
      var option = {};
      var color = [];
      this.echartData.map((item, index) => {
        color[index] = item.color;
      });
      option = {
        tooltip: {
          formatter: "{b}：{c}个 ({d}%)",
        },
        series: [
          {
            name: "12",
            type: "pie",
            radius: ["30%", "60%"],
            data: this.echartData,
            color: color,
          },
        ],
      };
      option && myChart.setOption(option);
    },
    //设置echart大小
    setEchart(eleName) {
      var echartsWarp = document.getElementById(eleName);
      var resizeWorldMapContainer = function () {
        //用于使chart自适应高度和宽度,通过窗体高宽计算容器高宽
        echartsWarp.style.width = "100%";
        echartsWarp.style.height = "60%";
        // console.log(echartsWarp.style.height);
      };
      resizeWorldMapContainer(); //设置容器高宽
      var myChart = this.$echarts.init(echartsWarp);
      window.onresize = function () {
        //用于使chart自适应高度和宽度
        resizeWorldMapContainer(); //重置容器高宽
        myChart.resize();
      };
    },
    getData() {
      this.loadingBox = true;
      // console.log(this.box );
      cimEarlyWarningCount({
        distCode: this.districtVal,
        startDate: new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        endDate: new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss"),
      })
        .then((res) => {
          this.box = res.data.data;
          var data = [
            {
              value: this.box.redWarningNum,
              name: "红色预警",
              color: "#f86767",
            },
            {
              value: this.box.orangeWarningNum,
              name: "橙色预警",
              color: "#ffaa71",
            },
            {
              value: this.box.yellowWarningNum,
              name: "黄色预警",
              color: "#f1d650",
            },
          ];
          this.echartData = [];
          JSON.parse(JSON.stringify(data)).map((item, index) => {
            // console.log(item)
            if (item.value != 0) {
              this.echartData.push(item);
            }
          });
          // console.log(this.echartData);
          if (this.echartData.length > 0) {
            this.showEchart = true;
            this.getEcharts();
            // setTimeout(() => {
            this.setEchart("riskWarningEcharts");
            // },200);
          } else {
            this.showEchart = false;
          }
        })
        .finally(() => {
          this.loadingBox = false;
        });
    },
    showEnterpriseList(item) {
      this.currentProcessName = item.processname;
      // 请求接口查询具体企业
      const params = {
        processid: item.processid,
      };
      getImportantProcessCompany(params).then((res) => {
        this.enterpriseListByProcess = res.data.data;
      });
      this.showEnterpriseDialog = true;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    // this.getDialogData();
    // 获取预警工单列表
    this.getWarningOrderList();
    // 获取危险化学品企业情况
    this.getDangerChemicalList();
    // 获取重点监管工艺
    this.getImportantProcessList("1");
    this.getImportantProcessList("2");
    // 获取重大危险源情况(按区域)
    this.getMajorHazardByArea();
    // 获取重大危险源情况(按等级)
    this.getMajorHazardByLevel();
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    // 显示前三个分类的企业列表（市级看板逻辑）
    displayEnterpriseList() {
      // 确保数据存在且长度足够
      if (!this.enterpriseFullData || this.enterpriseFullData.length < 3) {
        return [];
      }

      // 使用第二次不带level参数的请求返回的数据，只显示前三个分类
      const data = this.enterpriseFullData.slice(0, 3).map((item) => ({
        ...item,
        label: item.label.slice(0, 2), // 截取前两位用于显示
      }));

      // 交换第二个和第三个元素的位置
      [data[1], data[2]] = [data[2], data[1]];
      return data;
    },
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-scrollbar__wrap {
  overflow-x: auto;
}
.diagHeight {
  height: 70vh;
}
.riskWarning {
  width: 100%;
  height: 100%;
  border: 1px solid #d8e0ee;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  overflow: hidden;
  background-color: #fff;
  z-index: 1;
  .header {
    width: 95%;
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .warning-title {
    width: 95%;
    height: 52px;
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    line-height: 54px;
    display: flex;
    // justify-content: space-between;
    align-items: center; /* 垂直居中对齐 */
    // margin-left: 15px; /* 与容器左边的距离 */
    .warning-num {
      margin-left: auto; /* 将元素推向右侧 */
      cursor: pointer; /* 鼠标悬停时显示指针手势 */
    }
  }
  .warning-title img {
    margin-right: 8px; /* 图片与文本之间的间距 */
  }
  .warning-content {
    width: 100%;
    display: flex; /* 启用 Flexbox 布局 */
    justify-content: space-between; /* 子元素之间的间距均匀分布 */
    padding: 0 20px; /* 根据需要添加内边距 */
    .warning-box {
      width: 115px;
      height: 72px;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      // background: #FFFAF9;
      border-radius: 6px;
      // border: 1px solid #FFECEA;
    }
  }
  .enterprise-count {
    color: #333333;
    margin: 5px 0px;
    padding-left: 15px;
    // 居左
    width: 100%;
    display: flex;
    justify-content: flex-start;
  }
  .enterprise-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;

    .item {
      flex: 1;
      padding: 10px;
      margin: 0 5px;
      border-radius: 6px;
      transition: all 0.3s ease;

      // 可点击状态样式
      &.clickable {
        cursor: pointer;

        &:hover {
          background: #e3f2fd;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
    .item-label,
    .item-count {
      text-align: center;
    }
    .item-percent {
      text-align: center;
    }
  }
  .center-content {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 15px;
  }
  // .warning-content div {
  //   display: flex; /* 启用 Flexbox 布局 */
  //   align-items: center; /* 垂直居中对齐 */
  //   // width: 30%;
  //   flex-direction: column; /* 子元素垂直排列 */
  //   text-align: center; /* 文本居中 */
  // }
  .warning-order {
    width: 95%;
    height: 52px;
    color: #333333;
    line-height: 54px;
    display: flex;
    justify-content: space-between;
    .show-more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .warning-container {
    width: 95%;
    height: 300px;
    // border:1px solid #d8e0ee;
  }
  .card-list {
    width: 100%;
    height: 100%;
    overflow: auto;
    .card-item {
      width: 98%; /* 卡片宽度 */
      height: 150px; /* 卡片高度 */
      background-color: #ffffff; /* 背景色 */
      border: 1px solid #e0e0e0; /* 边框样式 */
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* 阴影效果 */
      padding: 10px; /* 内边距 */
      margin: 10px 0px 10px 0px; /* 外边距 */
      transition: all 0.3s ease; /* 过渡动画 */
      .card-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .card-item-header-left {
          font-size: 15px;
          font-weight: 600;
          color: #3977ea;
          // 下划线
          text-decoration: underline;
          cursor: pointer;
          margin-bottom: 8px;
        }
      }
      .card-item-content {
        .card-item-content-level {
          margin-bottom: 4px;
        }
        .card-item-content-description {
          margin-bottom: 4px;
        }
      }
    }
  }
  .box-red-bg {
    background: url(../../../../../static/img/assets/img/red.png) no-repeat
      center center #fffaf9;
    background-size: 100% 100%;
    // background-clip: border-box;
    // background-size: cover;
    // background-clip: border-box;
  }
  .box-orange-bg {
    background: url(../../../../../static/img/assets/img/orange.png) no-repeat
      center center #fffaf9;
    background-size: 100% 100%;
  }
  .box-yellow-bg {
    background: url(../../../../../static/img/assets/img/yellow.png) no-repeat
      center center #fffaf9;
    background-size: 100% 100%;
  }
  .red-content,
  .orange-content,
  .yellow-content {
    width: 100px;
    font-family: PingFang SC;
    font-weight: 500;
    font-size: 16px;
    color: #333333;
    line-height: 12px;
    display: flex;
    margin-top: 20px;
  }

  .warning-numRed {
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 24px;
    color: #fe4242;
    line-height: 12px;
  }
  .warning-numOrange {
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 24px;
    color: #fe8a42;
    line-height: 12px;
  }
  .warning-numYellow {
    font-family: PingFang SC;
    font-weight: bold;
    font-size: 24px;
    color: #e6c644;
    line-height: 12px;
  }
  .warning-numRed,
  .warning-numOrange,
  .warning-numYellow {
    margin-top: 15px;
    width: 100px;
    display: flex;
    font-size: 20px;
    cursor: pointer; /* 鼠标悬停时显示指针手势 */
  }
  .container {
    width: 95%;
    height: 105px;
    margin-top: 13px;
    border: 1px solid #d8e0ee;
    display: flex;
    box-shadow: 0px 0px 3px 0.01px #d8e0ee;
    .left {
      background: url(../../../../../static/img/entNumBg.png) no-repeat;
      background-size: cover;
      width: 110px;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        color: #333333;
        font-size: 14px;
      }
      .num {
        color: #5e86b4;
        font-size: 28px;
        text-decoration: underline;
        cursor: pointer;
        font-weight: 600;
      }
    }
    .right {
      width: calc(100% - 110px);
      height: 100%;
      padding-top: 20px;
      > div {
        width: 50%;
        height: 40%;
        float: left;
        display: flex;
        align-items: center;
        cursor: pointer;
        img {
          width: 16px;
          height: 19px;
          margin-left: 8%;
          margin-right: 8%;
        }
        .title {
          color: #666666;
          font-size: 14px;
          // margin-left: 10%;
          margin-right: 7%;
        }
        .numRed {
          color: #f86767;
          font-size: 18px;
          text-decoration: underline;
          font-weight: 600;
        }
        .numOrange {
          color: #ffaa71;
          font-size: 18px;
          text-decoration: underline;
          font-weight: 600;
        }
        .numYellow {
          color: #f1d650;
          font-size: 18px;
          text-decoration: underline;
          font-weight: 600;
        }
      }
    }
  }
  .echarts {
    width: 100%;
    height: 60%;
  }
  .dialog {
    .inputBox {
      width: 900px;
      display: flex;
      justify-content: flex-start;

      .input {
        width: 200px;
      }
      > * {
        margin-right: 15px;
      }
    }
    .table {
      margin-top: 10px;
    }
    .pagination {
      display: flex;
      justify-content: flex-end;
      margin-top: 10px;
    }
  }
  .null {
    width: 330px;
    height: 210px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    // margin-left: 50%;
    // transform: translateX(-50%);
    margin: 0 auto;
    margin-top: 45px;
  }
  .center-left {
    margin-right: 10px;
    width: 100%;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;

      .title {
        color: #3b4046;
        font-size: 18px;
        font-weight: 600;
      }

      span {
        // margin-left: 15px;
        font-size: 14px;
        color: #666;
        cursor: pointer;
        padding: 2px 4px;
        border-radius: 4px;

        &.active {
          color: #3977ea;
          background: rgba(57, 119, 234, 0.1);
        }
      }
    }

    .left-content {
      height: 240px;
      width: 100%;
      overflow-y: scroll;
      .area-list {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .area-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #f5f7fa;
          border-radius: 4px;

          .area-name {
            font-size: 14px;
            color: #333;
          }

          .area-num {
            font-size: 14px;
            color: #3977ea;
            font-weight: 500;
          }
        }
      }
    }
  }
  .center-right {
    width: 100%;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    span {
      // margin-left: 15px;
      font-size: 14px;
      color: #666;
      cursor: pointer;
      padding: 2px 4px;
      border-radius: 4px;

      &.active {
        color: #3977ea;
        background: rgba(57, 119, 234, 0.1);
      }
    }
    .right-content {
      height: 240px;
      width: 100%;
      overflow: auto;

      .process-table {
        width: 100%;

        .table-header {
          display: flex;
          background: #f5f7fa;
          padding: 12px 0;
          border-radius: 4px 4px 0 0;
          font-weight: bold;
          color: #333;
          font-size: 14px;

          .header-item {
            flex: 1;
            text-align: center;

            &:first-child {
              flex: 1.5;
            }
          }
        }

        .table-body {
          .table-row {
            display: flex;
            padding: 12px 0;
            border-bottom: 1px solid #ebeef5;

            &:last-child {
              border-bottom: none;
            }

            .table-cell {
              flex: 1;
              text-align: center;
              font-size: 14px;
              color: #666;

              &:first-child {
                flex: 1.5;
                color: #333;
              }

              &:nth-child(2) {
                color: #3977ea;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}

// 悬浮提示框样式
.hover-tooltip {
  .tooltip-content {
    background: #ffffff;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px;
    min-width: 280px;

    .tooltip-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;
    }

    .tooltip-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 12px;
      margin-bottom: 4px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: #e3f2fd;
        transform: translateX(2px);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .tooltip-item-label {
        font-size: 13px;
        color: #333;
        flex: 1;
      }

      .tooltip-item-count {
        font-size: 14px;
        font-weight: bold;
        color: #409eff;
        background: #f0f8ff;
        padding: 2px 8px;
        border-radius: 12px;
        border: 1px solid #409eff;
      }
    }
  }
}

// 分类详情弹窗样式 - 使用深度选择器
::v-deep .category-dialog-content {
  .category-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 18px 24px;
    margin-bottom: 12px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-radius: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    cursor: pointer;

    &:hover {
      background: linear-gradient(135deg, #e3f2fd 0%, #f0f8ff 100%);
      border-color: #409eff;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .category-item-label {
      font-size: 15px;
      font-weight: 500;
      color: #333;
      flex: 1;
      line-height: 1.4;
    }

    .category-item-count {
      font-size: 20px;
      font-weight: bold;
      color: #409eff;
      background: #f0f8ff;
      padding: 8px 16px;
      border-radius: 20px;
      border: 2px solid #409eff;
      min-width: 80px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.1);
    }
  }
}
</style>
