<template>
  <div class="dailySafetyManagement">
    <component
      :is="
        $route.name === 'dailySafetyManagement'
          ? 'equipmentAbnormalDeclaration'
          : $route.name
      "
    ></component>
  </div>
</template>

<script>
import lightningWarning from "@/components/feature/earlyWarningDisposal/lightningWarning";
import iotMontoringAlarm from "@/components/feature/riskAssessment/iotMontoringAlarm";
export default {
  //import引入的组件
  name: "dailySafetyManagement",
  components: {
    equipmentAbnormalDeclaration: () =>
      import("./equipmentAbnormalDeclaration"),
    enterpEducation: () => import("./enterpEducation"),
    checkOnline: () => import("./checkOnline"),
    patrolOnline: () => import("./patrolOnline"),
    safetyTraining: () => import("./safetyTraining"),
    informationRelease: () => import("./informationRelease"),
    contractorManagement: () => import("./contractorManagement"),
    specialWork: () => import("./specialWork"),
    threeDataReview: () => import("./threeDataReview"),
    handleProcedures: () => import("./threeDataReview/handleProcedures"),
    pdfSignature: () => import("./threeDataReview/pdfSignature"),
    otherInspections: () => import("./otherInspections"),
    lightningWarning,
    iotMontoringAlarm,
  },
};
</script>
<style lang="scss" scoped>
.dailySafetyManagement {
  height: 100%;
}
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
