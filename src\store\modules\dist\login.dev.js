"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
// import { stat } from "fs"
// initial state
var state = {
  // 视频监控模块数据
  user: {},
  // token: "bearer e7a41b76-673f-429e-a35d-adb9c333f540"
  token: ""
}; // getters

var getters = {
  getUser: function getUser(state) {
    return state.user;
  },
  getToken: function getToken(state) {
    return state.token;
  }
}; // actions

var actions = {
  setUser: function setUser(_ref, maptype) {
    var state = _ref.state,
        commit = _ref.commit;
    commit("updataUser", maptype);
  },
  setToekn: function setToekn(_ref2, maptype) {
    var state = _ref2.state,
        commit = _ref2.commit;
    commit("updataToken", maptype);
  }
}; // mutations

var mutations = {
  updataUser: function updataUser(state, val) {
    state.user = val;
  },
  updataToken: function updataToken(state, val) {
    state.token = val;
  }
};
var _default = {
  namespaced: true,
  state: state,
  getters: getters,
  actions: actions,
  mutations: mutations
};
exports["default"] = _default;