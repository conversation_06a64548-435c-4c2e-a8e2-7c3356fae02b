<template>
  <div class="historyList">
    <el-dialog
      title="历史承诺"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="1350px"
      top="5vh"      
      :close-on-click-modal="false"
      
    >
    <div style="height: 60vh; overflow: auto">
      <el-table :data="tableData.records" style="width: 100%;" @row-click="showDialog" v-loading="loading" :header-cell-style="{background:'#F1F6FF',color:'#333'}"
        border>
        <el-table-column label="日期" align="center">
          <template slot-scope="scope">
            <span style="color: #3977EA;cursor: pointer;">{{
              getTime(scope.row.commiteDate)
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="风险等级" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.riskGrade == 1">高风险</span>
            <span v-if="scope.row.riskGrade == 2">较大风险</span>
            <span v-if="scope.row.riskGrade == 3">一般风险</span>
            <span v-if="scope.row.riskGrade == 4">低风险</span>
          </template>
        </el-table-column>
        <el-table-column prop="unitsNumber" label="生产装置套数" align="center">
        </el-table-column>
        <el-table-column prop="runNumber" label="运行套数" align="center">
        </el-table-column>
        <el-table-column prop="parkNumber" label="停车套数" align="center">
        </el-table-column>
        <el-table-column label="是否处于试生产期" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.trialProduction == 1 ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="是否处于开停车状态" width="180" align="center">
          <template slot-scope="scope">
            <span>{{ scope.row.openParking == 1 ? "是" : "否" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="commiteDate"
          label="上报时间"
          width="180"
          align="center"
        >
        </el-table-column>
      </el-table>
    </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="tableData.size"
          layout="total, prev, pager, next"
          :total="tableData.total"
          background
        >
        </el-pagination>
      </div>
      <History  ref="History"></History>
    </el-dialog>
  </div>
</template>

<script>
import History from "./history";
import { getHistoryPromise } from "@/api/entList";
export default {
  //import引入的组件
  components: { History },
  data() {
    return {
      show: false,
      currentPage: 1,
      total: "",
      tableData: {},
      enterpriseId:"",
      loading:false
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {   
      this.currentPage=1
      this.show = val;
    },
    getData(enterpriseId) {
      this.enterpriseId= enterpriseId
      this.loading = true;
      getHistoryPromise({
        enterpriseId: this.enterpriseId,
        current: this.currentPage,
      }).then((res) => {
        if (res.data.code == 0) {
          this.tableData = res.data.data;
        }
        this.loading = false;
      });
    },
    getTime(time) {
      let ti = time.split(" ");
      return ti[0];
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData(this.enterpriseId);
    },
    showDialog(row, column, event) {
      this.$refs.History.closeBoolean(true);
      this.$refs.History.getData(row.id);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.historyList {
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
body .el-table th.gutter{
    display: table-cell!important;
}
</style>