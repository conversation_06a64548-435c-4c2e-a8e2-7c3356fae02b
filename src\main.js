// The Vue build version to load with the `import` command
// (runtime-only or standalone) has been set in webpack.base.conf with an alias.
import Vue from "vue";
import App from "./App";
import router from "./router";
import store from "./store";
//导入拖拽功能模块
/**
 * 在dialog标签上使用v-dialogDrag
 */
import "./utils/directives";
// 添加被动事件监听器来阻止'touchstart'事件
// import "default-passive-events";
//导入Echarts
import echarts from "echarts";
import { setEchart, scrollToError, Format } from "./utils/index";
//导入自定义UI组件
import install from "./components/common/packages/index";
Vue.use(install);
//导入AntdV
import AntdV from "./utils/register-antdv";
Vue.use(AntdV);
//导入ElementUI
// import ElementUI from "./utils/register-element-ui";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
Vue.use(ElementUI);
import VForm from "vform-builds"; //引入VForm库
import "vform-builds/dist/VFormDesigner.css"; //引入VForm样式

// 让VForm可以访问到全局函数
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js"; // 假设这些函数位于此路径
Vue.prototype.$Attachmentdownload = Attachmentdownload;
Vue.prototype.$downloadFuc = downloadFuc;

// 初始化VForm时，告诉它如何找到这些函数
Vue.use(VForm, {
  globalFunctions: {
    Attachmentdownload: () => Vue.prototype.$Attachmentdownload,
    downloadFuc: () => Vue.prototype.$downloadFuc,
  },
});

Vue.config.productionTip = false;
import * as VueQuillEditor from "vue-quill-editor"; //引入富文本
import "quill/dist/quill.core.css";
import "quill/dist/quill.snow.css";
import "quill/dist/quill.bubble.css";
Vue.use(VueQuillEditor);

import {
  parseTime,
  resetForm,
  addDateRange,
  selectDictLabel,
  selectDictLabels,
  download,
  handleTree,
} from "@/utils/ruoyi";
Vue.prototype.parseTime = parseTime;
Vue.prototype.resetForm = resetForm;
Vue.prototype.addDateRange = addDateRange;
Vue.prototype.selectDictLabel = selectDictLabel;
Vue.prototype.selectDictLabels = selectDictLabels;
Vue.prototype.download = download;
Vue.prototype.handleTree = handleTree;
// 环境的切换
if (process.env.NODE_ENV === "development") {
  Vue.config.devtools = true; //生产环境中需要设置为false
} else if (process.env.NODE_ENV === "debug") {
  Vue.config.devtools = true; //生产环境中需要设置为false
} else if (process.env.NODE_ENV === "production") {
  Vue.config.devtools = false; //生产环境中需要设置为false
}

Vue.config.productionTip = false; //阻止vue启动时生成生产消息

Vue.prototype.$echarts = echarts;
Vue.prototype.$setEchart = setEchart;
Vue.prototype.$scrollToError = scrollToError;
Date.prototype.Format = Format;
//导出基路径
const config = require("../config");
var BASE_URL = config.BASE_URL;
console.log(
  window.location.port,
  "aaaa------------------window.location.portwindow"
);
if (window.location.port == "30003") {
  //企业端
  BASE_URL = "yyzc.hbsis.gov.cn:30003";
} else if (window.location.port == "31443") {
  BASE_URL = "yyzc-whjcyj.hbsis.gov.cn:31443";
}
Vue.prototype.BASE_URL = BASE_URL;

// 创建 Vue 实例
const app = new Vue({
  el: "#app",
  router,
  store,
  components: { App },
  template: "<App/>",
});

// 添加全局消息监听器
window.addEventListener("message", function (event) {
  if (event.data.type === "selectEnterprise") {
    const enterpId = event.data.enterpId;
    console.log("收到选择企业消息:", enterpId);
    // 使用 app.$router 而不是 this.$router
    app.$router.push(
      "/gardenEnterpriseManagement/entManagement?showDetail=true&id=" + enterpId
    );
    // 将企业ID存储到 Vuex 中，这样所有组件都能访问到
    store.commit("SET_ENTERPRISE_ID", enterpId);
  }

  // 监听辅助监管模式消息
  if (event.data.type === "AUXILIARY_SUPERVISION_MODE") {
    console.log("收到辅助监管模式消息:", event.data);

    // 设置辅助监管模式标识
    store.commit("controler/SET_AUXILIARY_MODE", true);

    console.log("已启用辅助监管模式");
  }

  // 监听菜单还原消息
  if (event.data.type === "RESTORE_NORMAL_MODE") {
    console.log("收到菜单还原消息:", event.data);

    // 关闭辅助监管模式
    store.commit("controler/SET_AUXILIARY_MODE", false);

    console.log("已还原到正常模式");
  }
});
