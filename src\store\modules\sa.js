// import { stat } from "fs"

// initial state
const state = {
    // 超级管理员授权管理-side-菜单列表
    SAMenuListData: {},
    SAPrivListData: {},
    SARoleListData: {tabCon:false}
  };
  const getters = {
    getSAMenuListData: state => {
      return state.SAMenuListData;
    },
    getSAPrivListData: state => {
      return state.SAPrivListData;
    },
    getSARoleListData: state => {
      return state.SARoleListData;
    }
  };
  const actions = {
    //   setMapType({ state, commit }, maptype) {
    //     commit("updateMapType", maptype);
    //   }
  };
  const mutations = {
    updateSAMenuListData(state, val) {
      state.SAMenuListData = val;
    },
    updateSAPrivListData(state, val) {
      state.SAPrivListData = val;
    },
    updateSARoleListData(state, val) {
      state.SARoleListData = val;
    }
  };
  export default {
    namespaced: true,
    state,
    getters,
    actions,
    mutations
  };
  