<template>
  <div class="table">
    <el-dialog
      title="修改密码规则"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableRow"
        ref="tableRow"
        v-loading="loadingRow"
        label-width="150px"
      >
        <div class="inputBox">
          <el-form-item label="规则表达式" prop="rulePwdCode" class="lable">
            <el-input
              v-model.trim="tableRow.rulePwdCode"
              placeholder="系统名称"
              label="系统名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="规则描述" prop="rulePwdName" class="lable">
            <el-input
              v-model.trim="tableRow.rulePwdName"
              placeholder="系统编码"
              label="系统编码"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="系统访问地址" prop="systemUrl" class="lable">
            <el-radio-group v-model="tableRow.isSelected">
              <el-radio label="false">关闭</el-radio>
              <el-radio label="true">启用</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <el-button
          size="default"
          type="primary"
          class="commit"
          @click="handleSaveTable"
          >保存</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveMenmenuByMenuId,
  sameMenuFlagSystemCodes,
  savePwdRule,
  getPwdRuleEditRule,
} from "../../../api/user";
import { pwdRules } from "../../../api/rules";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loadingRow: false,
      tableRow: {},
      checkedKeys: [],
      rules: pwdRules,
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
    },
    clearTable() {
      this.tableRow = {};
    },
    handleSaveTable() {
      // this.loading = true;
      this.$refs["tableRow"].validate((valid) => {
        if (valid) {
          savePwdRule({ ...this.tableRow })
            .then((res) => {
              // this.loading = false;
              this.tableVisible = false;
              this.$message({
                message: res.data.msg,
                type: "success",
              });
              this.$parent.fatherMethod();
            })
            .catch((e) => {
              // this.loading = false;
              console.log(e, "请求错误");
            });
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    getData(row) {
      this.tableRow = row;
    },
    onCheck(checkedKeys, e) {
      // console.log(checkedKeys, e)
      let key = JSON.stringify(checkedKeys);
      key = key.split('"').join("");
      key = key.split("[").join("");
      key = key.split("]").join("");
      // console.log(key);
      this.tableRow.belongSystemCode = key;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
