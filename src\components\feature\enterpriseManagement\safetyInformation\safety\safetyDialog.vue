<template>
  <el-dialog
    title="安全管理人员信息"
    :visible.sync="show"
    width="1000px"
    @close="closeBoolean()"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <div class="table">
      <ul class="container">
        <li>
          <div class="l">专（兼）职</div>
          <div class="r">{{ safetyOrg.isFullTime=='1'?'专职':'兼职' }}</div>
        </li>
        <li class="">
          <div class="l">类型</div>
          <div class="r">{{ safetyOrg.expertType }}</div>
        </li>
        <li>
          <div class="l">姓名</div>
          <div class="r">{{ safetyOrg.personName }}</div>
        </li>
        <li class="">
          <div class="l">手机号码</div>
          <div class="r">{{ safetyOrg.phone }}</div>
        </li>
        <li class="">
          <div class="l">职务</div>
          <div class="r">{{ safetyOrg.duties }}</div>
        </li>
        <li>
          <div class="l">职责</div>
          <div class="r">{{ safetyOrg.duty }}</div>
        </li>
        <li>
          <div class="l">证书名称</div>
          <div class="r">{{ safetyOrg.certificateType }}</div>
        </li>
        <li>
          <div class="l">证书编号</div>
          <div class="r">{{ safetyOrg.certificateNo }}</div>
        </li>
        <li class="">
          <div class="l">发证时间</div>
          <div class="r">{{ safetyOrg.issuanceTime }}</div>
        </li>
        <li class="">
          <div class="l">证书有效期</div>
          <div class="r">{{ safetyOrg.validityPeriod }}</div>
        </li>
        <li class="">
          <div class="l">发证机关</div>
          <div class="r">{{ safetyOrg.licenceIssuingAuthority }}</div>
        </li>
        <li class="">
          <div class="l">证书附件</div>
          <!-- <div class="r">{{ safetyOrg.attachmentFiles }}</div> -->
        </li>
        <li class="lang bottom">
          <div class="l">所属安全生产管理机构</div>
          <div class="r">{{ safetyOrg.forgId }}</div>
        </li>
  
      </ul>
    </div>
  </el-dialog>
</template>

<script>
import { detailSafetOrgList } from "@/api/safetyInfomation";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    entObj: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      safetyOrg: {},  
    //   safetyOrg: {
    //     personId: '',
    // personName: '',
    // orgName: '',
    // orgHead: '',
    // duties: '',
    // duty: '',
    // phone: '',
    // telephone: '',
    // certificateType: '',
    // operateCertificateName: '',
    // certificateNo: '',
    // issuanceTime: null,
    // validityPeriod: null,
    // licenceIssuingAuthority: '',
    // attachmentId: '',
    // attachmentFiles: [],
    // specialOperationPositions: '',
    // isFullTime: '',
    // expertType: '',
    // personType: '',
    // companyCode: '',
    // forgId: '',
    // trainDataList:[],
    //   },
    };
  },
  created() {
    this.getData()
  },
  methods: {
    closeBoolean() {
      this.$emit("closeBoolean");
    },
    async getData() {
    await  detailSafetOrgList(this.entObj.id ).then(res => {
      if (res.data.status == 200) {
        this.safetyOrg = res.data.data;
      }
      })
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  height: 100%;
  overflow: auto;
  .container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    .bottom {
      border-bottom: 1px solid rgb(231, 231, 231);
    }

    li {
      list-style-type: none;
      width: 50%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      // border-right: 1px solid rgb(231, 231, 231);
      border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 48%;
        padding: 1%;
        word-break: break-all;
      }
    }
    li:nth-of-type(2n + 0) {
      list-style-type: none;
      width: 50%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 40%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 60%;
        word-break: break-all;
      }
    }
    .lang {
      list-style-type: none;
      width: 100%;
      display: flex;
      border-top: 1px solid #eaedf2;
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 25%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 58%;
        padding: 1%;
        word-break: break-all;
      }
    }
    .liLine {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .bottom:nth-of-type(3n + 0) {
      border-right: 1px solid rgb(231, 231, 231);
    }
  }
}
</style>
