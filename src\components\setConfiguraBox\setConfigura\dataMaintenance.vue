<template>
  <div class="enterpriseManagement">
    <el-dialog
      title="组织及人员信息维护"
      :visible.sync="show"
      width="1100px"
      @close="closeBoolean()"
      top="10vh"
      :close-on-click-modal="false"
      v-dialog-drag
    >
      <div>
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px !important"
        >
          <el-form-item label="配置Id" prop="configKey">
            <el-input
              v-model.trim="form.configKey"
              maxlength="30"
              placeholder="配置Id"
            />
          </el-form-item>

          <el-form-item label="配置值" prop="configValue" style="width: 100%">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="300"
              show-word-limit
              placeholder="请输入配置值"
              v-model.trim="form.configValue"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="配置描述"
            prop="configComment"
            style="width: 100%"
          >
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="200"
              show-word-limit
              placeholder="请输入配置描述"
              v-model.trim="form.configComment"
            ></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" style="display: flex; justify-content: center">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { configAdd } from "@/api/user";
export default {
  name: "dataMaintenance",
  data() {
    return {
      form: {
        configKey: "",
        configValue: "",
        configComment: "",
      },
      show: false,
      rules: {
        configKey: [
          { required: true, message: "请输入配置Id", trigger: "blur" },
        ],
        configValue: [
          { required: true, message: "请输入配置值", trigger: "blur" },
        ],
        configComment: [
          { required: true, message: "请输入配置描述", trigger: "blur" },
        ],
      },
    };
  },

  mounted() {},

  methods: {
    // 表单重置
    cancel(){
      this.show=false;
      this.reset()      
    },
    submitForm(){
      configAdd(this.form).then((response) => {
                if (response.data.status === 200) {
                  this.$message.success("保存成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.$message.error("保存失败");
                }
              });
    },
    reset() {
      this.form = {
        configKey: '',
        configValue: '',
        configComment: '',
      };
      if (this.$refs["form"] != undefined) {
        this.$refs["form"].resetFields();
      }
    },
    closeBoolean(val) {
     this.show = val;
      if (!val) {
        this.showAddEdit = false;
        this.reset();
      }
    },
    cancleFun() {
      this.show = false;
      this.showAddEdit = false;
      this.reset();
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tree-node > .el-tree-node__children {
  overflow: inherit !important;
}
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  /deep/.el-input-group__append {
    cursor: pointer !important;
  }
}
.el-form-item {
  margin-bottom: 10px;
}
.duty-list {
  display: flex;
  justify-content: start;
  width: 100%;
  height: 70vh;
  padding-top: 0.1rem;
}

.duty-list-left {
  width: 22.5rem;
  height: 100%;
}

.duty-list-right {
  width: calc(100% - 22.5rem);
  overflow-y: scroll;
  overflow-x: hidden;
  height: 100%;
  /* background: rgba(233, 233, 233, 1) */
}
.tab {
  padding-left: 28px;
  padding-right: 20px;
  padding-top: 0.5rem;
  button {
    span {
      font-size: 16px !important;
    }
  }
}
.list_contain {
  display: flex;
  // justify-content: space-between;
  //   height: 72vh;
  height: 100%;
  width: 100%;
  flex-direction: column;
}
.list_top {
  // height: calc(100% - 240px);
  display: flex;
}
.list_bottom_add {
  .form_title {
    font-size: 20px;
    line-height: 35px;
    padding-bottom: 5px;
    border-bottom: 1px solid #d7d7d7;
    margin-bottom: 15px;
  }
}
.list_left {
  padding-top: 0.5rem;
  width: 100%;
}
.buttonWrap {
  display: flex;
  margin-top: 10px; /*no*/
  padding: 0 28px;
  button {
    flex: 1;
    display: inline-block;
    padding-left: 5px !important;
    padding-right: 10px !important;
    margin-right: 5px !important;
    span {
      font-size: 16px;
    }
  }
}
.person_info_span {
  width: 85px;
  //   margin-bottom: 4px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.maillist-title {
  display: inline-block;
  line-height: 40px;
  font-size: 22px;
}
.right_filter {
  float: right;
}

.el-scrollbar {
  height: 100% !important;
}
.el-dialog .search_tree {
  height: 100%;
}
.search_tree {
  position: relative;
  height: 91.5%;
  box-sizing: border-box;
}

.tabs_list {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 122;
  padding: 0.625rem 4px;
  padding-top: 0;
  margin-top: 0.5rem;
  position: absolute;
  height: 16.5rem;
  width: 100%;

  .filter_list {
    li {
      cursor: pointer;
      color: #000;
      font-size: 16px;
      padding: 0.625rem 1rem;
      line-height: 1rem;
      &:hover {
        background: #66b1ff;
        color: #fff;
      }

      &.active {
        background: #66b1ff;
        color: #fff;
      }
    }
  }
  .filter_list_content {
    display: inline-block;
    // width: 100%;
    line-height: 18px;
    // font-size: 1rem;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
}

.search_slide_searchKey {
  position: relative;
  height: 3rem;
  line-height: 3rem;
  background: #f5f5f7;
  border: 1px solid #e3e3e5;
  padding: 0 0.5rem;
  border-bottom: none;
}

.search_slide_input {
  // padding: 0 0.625rem;
  height: 100%;
  padding-top: 0.5rem;
  padding-left: 28px;
  padding-right: 20px;
}

.search_slide {
  position: relative;
  transition: all 0.5s;
  // top: 0.5rem;
  width: 100%;
  // height: calc(100% - 2.7rem);
  // height: 93.5%;
  // height: calc(100% - 80px);
  height: 88%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e3e3e5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 11;
  padding-left: 0.2rem;
  padding-bottom: 0.2rem;
  border-top: none;
  .controllab {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
  }
}

.popper__arrow {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
  -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: 2.5rem;
  left: 50%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #ebeef5;
  z-index: 100;

  &:after {
    content: "";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
  }
}

.headertitle {
  background: #f5f5f6;
  padding-left: 30px;
  line-height: 2.1rem;
  font-size: 1rem;
  cursor: pointer;
}
.navtitle {
  background: #fff;
  // border-bottom: 1px solid rgb(228, 231, 237)
}

.tree_icon {
  color: #c0c4cc;
  font-size: 18px;
  padding: 6px;
}
.headerdrops {
  margin-left: 1.6rem;
  margin-bottom: 3px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #c0c4cc;
}
.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #cee5ff;
  color: #4a7dff;
}
</style>
