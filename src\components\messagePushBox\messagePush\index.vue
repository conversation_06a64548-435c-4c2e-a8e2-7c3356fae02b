<template>
  <div class="messagePush">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane label="预警推送规则" name="Warn"
        ><div class="Warn"><Warn></Warn></div
      ></el-tab-pane>
      <!-- <el-tab-pane label="短信推送配置" name="SMS"
        ><div class="SMS"><SMS></SMS></div
      ></el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script>
import SMS from "./SMSPush";
import Warn from "./warnPush";
export default {
  //import引入的组件
  components: {
    SMS,
    Warn,
  },
  data() {
    return {
      activeName: "Warn",
    };
  },
  //方法集合
  methods: {
    handleClick(tab, event) {
      console.log(tab, event);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.messagePush {
}
</style>