<!-- 执法统计 -->
<template>
    <div class="law-statistics">
        <div class="statistic-title">
            <div class="item-title">执法情况分析</div>
            <div class="query-box">
                <el-radio-group v-model="rightQuery.radio" size="small" style="margin-bottom:0!important">
                    <el-radio-button :label="1">本周</el-radio-button>
                    <el-radio-button :label="2">本月</el-radio-button>
                    <el-radio-button :label="3">本年</el-radio-button>
                </el-radio-group>
                <el-date-picker v-model="value1" size="small" type="daterange" value-format="yyyy-MM-dd"
                    range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" @change="searchTime"
                    unlink-panels style="width: 340px" :picker-options="{
                        disabledDate: (time) => {
                            return (
                                time.getTime() >
                                new Date(new Date().Format('yyyy-MM-dd 23:59:59')).getTime()
                            );
                        },
                    }"></el-date-picker>
            </div>
        </div>
        <el-card class="statistic-card">
            <div class="statistic-container">
                <div class="sum-box">
                    <img src="../../../../../../static/img/lev1.png" alt="">
                    <div class="sum-item">
                        <div class="sum-title">今日总数</div>
                        <div class="sum-title"><span class="sum-value">500</span> 个</div>
                    </div>
                </div>
                <div class="total-count">
                    <div class="total-item" v-for="(item, index) in statistic" :key="index">
                        <div class="total-item-title">{{ item.name }}</div>
                        <div class="total-item-value">{{ item.value }}</div>
                    </div>

                </div>
            </div>
        </el-card>
        <div class="statistic-title">
            <div class="query-box">
                <el-button type="primary" size="small">类别</el-button>
                <el-radio-group v-model="classify" size="small" style="margin-bottom:0!important">
                    <el-radio-button :label="1">执法检查次数(次)</el-radio-button>
                    <el-radio-button :label="2">参与执法人(人)</el-radio-button>
                    <el-radio-button :label="3">出具文书数量(份)</el-radio-button>
                    <el-radio-button :label="4">行政处罚罚款金额(万元)</el-radio-button>
                </el-radio-group>
            </div>
        </div>
        <div id="typeBarChart" class="classify-bar"></div>
        <div class="bottom-container">
            <div class="bottom-item">
                <div class="item-title">行业分布分析</div>
                <div class="classify-pie" id="typeroseChart"></div>
            </div>
            <div class="bottom-item">
                <div class="item-title">执法文书类型分析</div>
                <div class="classify-pie" id="typepieChart"></div>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    data() {
        return {
            rightQuery: {
                radio: 1,
                startTime: '',
                endTime: '',
            },
            value1: '',
            classify: 1, //类别
            statistic: [
                { name: '检查机构', value: 30 },
                { name: '被检查危化品企业', value: 45 },
                { name: '执法文书数量', value: 123 },
                { name: '被执行企业数量', value: 36 },
                { name: '复查企业', value: 68 },
            ],
            barChart: null,
            typeroseChart: null,
            pieChart: null,

        }
    },
    mounted() {
        this.initBar();
        this.initPie();
        this.initTyperose();
    },
    beforeDestroy() {
        this.barChart.dispose();
        this.barChart = null;
    },
    methods: {
        searchTime(value) {
            if (value) {
                let date1 = new Date(value[0]);
                let dataTime1 = parseTime(date1, '{y}-{m}-{d}');
                let date2 = new Date(value[1]);
                let dataTime2 = parseTime(date2, '{y}-{m}-{d}')
                this.rightQuery.startTime = dataTime1;
                this.rightQuery.endTime = dataTime2;
            } else {
                this.value1 = '';
                this.rightQuery.startTime = '';
                this.rightQuery.endTime = '';
            }
        },
        initBar() {
            this.barChart = this.$echarts.init(document.getElementById('typeBarChart'));
            const xData = ['武汉市市辖区', '武汉市江岸区', '武汉市江汉区', '武汉市硚口区', '武汉市汉阳区', '武汉市武昌区', '武汉市青山区', '武汉市洪山区', '武汉市东西湖区', '武汉市汉南区', '武汉市蔡甸区', '武汉市江夏区', '武汉市黄陂区', '武汉市新洲区', '东湖新技术开发区', '东湖生态旅游风景区', '武汉经济技术开发区']
            const yData = [23, 45, 56, 78, 90, 100, 16, 80, 75, 42, 30, 42, 26, 35, 65, 75, 35]
            let option = {
                tooltip: {
                },
                grid: {
                    top: '30',
                    right: '8%',
                    left: '5%',
                    bottom: '20',
                    containLabel: true

                },
                xAxis: {
                    type: 'category',
                    data: xData,
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value'
                },
                series: [
                    {
                        name: '',
                        type: 'bar',
                        barWidth: '30%',
                        label: {
                            normal: {
                                show: true,
                                position: 'top',
                            }
                        },
                        itemStyle: {
                            normal: {
                                color: '#409EFF'
                            }
                        },
                        data: yData
                    },

                ]
            }
            this.barChart.setOption(option);
        },
        initPie() {
            this.pieChart = this.$echarts.init(document.getElementById('typepieChart'));
            const data = [
                { value: 35, name: '类型一' },
                { value: 30, name: '类型二' },
                { value: 234, name: '类型三' },
                { value: 135, name: '类型四' },
                { value: 158, name: '类型五' },
            ]
            const total = data.reduce((a, b) => a + b.value, 0) || 0;
            let option = {
                title: {
                    text: `{a|执法文书总数} \n {b|${total}}个`,
                    left: '32.5%',
                    top: '40%',
                    align: 'center',
                    textStyle: {
                        fontSize: 14,
                        color: '#000',
                        rich: {
                            a: {
                                fontSize: 14,
                                color: '#000',
                            },
                            b: {
                                fontSize: 18,
                                fontWeight: 'bold',
                                color: '#000',
                                align: 'center',
                                padding: [8, 0],
                            }
                        }

                    },

                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c} ({d}%)'
                },
                legend: {
                    orient: 'vertical',
                    right: '15%',
                    top: 'center',
                    formatter: function (name) {
                        return name + '  ' + data.find(item => item.name === name).value + '  ' + (data.find(item => item.name === name).value / total * 100).toFixed(2) + '%'
                    },
                    textStyle: {
                        color: '#000',
                        fontSize: 14,
                        rich: {
                            name: {
                                fontSize: 14,
                                color: '#000',
                            },
                            value: {
                                fontSize: 14,
                                color: '#000',
                            },
                            percent: {
                                fontSize: 14,
                                color: '#000',
                            }
                        }
                    },
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        radius: ['45%', '70%'],
                        center: ['40%', '50%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '30',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: data
                    }
                ]
            }
            this.pieChart.setOption(option);
        },
        initTyperose() {
            let list = [
                {
                    "name": "生产",
                    "value": "29"
                },
                {
                    "name": "经营",
                    "value": "41"
                },
                {
                    "name": "使用（使用许可）",
                    "value": "5"
                },
                {
                    "name": "化工（不发使用许可）",
                    "value": "14"
                },
                {
                    "name": "医药",
                    "value": "33"
                },
                {
                    "name": "其他",
                    "value": "26"
                }
            ]
            this.typeroseChart = this.$echarts.init(document.getElementById('typeroseChart'));
            let option = {
                title: {},
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c} ({d}%)'
                },
                legend: {
                    right: '20%',
                    top: 'center',
                    orient: 'vertical'
                },
                toolbox: {
                    show: true
                },
                series: [
                    {
                        name: '',
                        type: 'pie',
                        center: ['35%', '50%'],
                        roseType: 'radius',
                        itemStyle: {
                            borderRadius: 5
                        },
                        label: {
                            show: false
                        },
                        labelLine: {
                            show: false
                        },
                        emphasis: {
                            label: {
                                show: false
                            }
                        },
                        data: list
                    }
                ]
            };
            this.typeroseChart.setOption(option);
        }
    }
}
</script>

<style lang="scss" scoped>
.law-statistics {
    .statistic-title {
        display: flex;
        justify-content: space-between;
    }

    .statistic-card {
        margin: 10px 0;
    }

    .statistic-container {
        display: flex;
        height: 100px;

        .sum-box {
            width: 240px;
            border: 1px solid #e6e6e6;
            border-radius: 3px;
            display: flex;
            align-items: center;
            padding-left: 20px;


            .sum-item {
                margin-left: 20px;
            }

            .sum-title {
                display: flex;
                align-items: center;
                font-size: 16px;

                .sum-value {
                    font-size: 24px;
                    color: red;
                    font-weight: 500;
                    margin-right: 10px;
                }
            }


        }

        .total-count {
            flex: 1;
            display: flex;
            justify-content: space-around;
            align-items: center;

            .total-item {
                flex: 1;
                text-align: center;
                font-size: 16px;

                .total-item-value {
                    font-size: 24px;
                    font-weight: 500;
                }
            }
        }
    }

    .classify-bar {
        height: 240px;
        width: 100%;
    }

    .bottom-container {
        width: 100%;
        display: flex;

        .bottom-item {
            width: 50%;

            .classify-pie {
                width: 100%;
                height: 240px;
            }
        }

    }

    .item-title {
        position: relative;
        padding: 12px;

        &::before {
            position: absolute;
            left: 0;
            top: 50%;
            content: '';
            width: 9px;
            height: 10px;
            transform: translateY(-50%);
            background: url("../../../../../../static/img/lan_icon.png") no-repeat center;
            background-size: 100% 100%;
        }

    }
}
</style>