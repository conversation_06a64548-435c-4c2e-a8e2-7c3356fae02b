<template>
  <div class="historyList">
    <el-dialog
      :title="titleName"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="1350px"
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist"
          ></el-cascader>
          <!-- <el-select
            v-model="majorHazardLevel"
            placeholder="危险源等级"
            size="mini"
            clearable
            @clear="clearMa"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <el-input
            placeholder="请输入企业名称"
            v-model.trim="entName"
            size="mini"
            clearable
            @clear="clearEntName"
          >
          </el-input>
          <el-button type="primary" size="mini" class="btn" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="companyName"
          label="单位名称"
          width="300"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row, column, $index, store }">
            <span @click="goEnt(row)" style="color: #3977ea; cursor: pointer">{{
              row.companyName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="areaName" label="区划" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.parentName + "-" + row.distName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="报警次数" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.alarmNum }}</span>
          </template>
        </el-table-column>
        <el-table-column label="消警次数" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.clearedAlarmNum }}</span>
          </template>
        </el-table-column>
        <el-table-column label="未消警次数" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.notClearedAlarmNum }}</span>
          </template>
        </el-table-column>
        <el-table-column label="消警率" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ row.clearedAlarmRate + "%" }}</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getAlarmAnalysisCompanyNotClearedAlarm,
  getAlarmAnalysisCompanyNotClearedAlarmExportExcel,
} from "@/api/workingAcc";
import { getDistrictUser } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {},
  name: "NotClearedAlarm",
  data() {
    return {
      show: false,
      //  districtLoading: false,
      type: "",
      currentPage: 1,
      entName: "",
      loading: true,
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      majorHazardLevel: "",
      district: [],
      distCode: "",
      tableData: [],
      total: 0,
      areaName: "",
      titleName: "",
      startDate: "",
      endDate: "",
      selection:[]
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist
    }),
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.majorHazardLevel = "";
      // console.log(this.majorHazardLevel);
      this.show = val;
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then((res) => {
                let child = res.data.data;
        // debugger;
        if (child.children.length > 0) {
          for (let j = 0; j < child.children.length; j++) {
            if (child.children[j].children.length > 0) {
              for (let z = 0; z < child.children[j].children.length; z++) {
                if (child.children[j].children[z].children.length < 1) {
                  //判断children的数组长度
                  child.children[j].children[z].children = undefined;
                }
              }
            } else {
              child.children[j].children = undefined;
            }
          }
        } else {
          child.children = undefined;
        }
        this.district = [child];
      });
    },
    getEntData(distCode, type, areaName, startDate, endDate) {
      this.distCode = distCode;
      this.loading = true;
      this.type = type;
      this.areaName = areaName;
      this.titleName = this.areaName + "报警统计分析-消警企业数列表";
      this.startDate = startDate;
      this.endDate = endDate;
      getAlarmAnalysisCompanyNotClearedAlarm({
        distCode: this.distCode,
        entName: this.entName,
        startDate: startDate,
        endDate: endDate,
        type: type,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    exportExcel() {
      let list = [...this.selection];
      // console.log(list);
      getAlarmAnalysisCompanyNotClearedAlarmExportExcel({
        distCode: this.distCode,
        startDate: this.startDate,
        distCodeList: list.length <= 1 ? null : list,
        endDate: this.endDate,
        isContainParent: true,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "报警统计分析-消警企业数列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    // 排序
    changeTableSort(column) {
      var fieldName = column.prop;
      var sortingType = column.order;
      var data = [...this.tableData];
      var shiftTotal = [];
      if (data[0].areaCode == this.$store.state.login.userDistCode) {
        shiftTotal = data.shift();
      } else if ((this.areaCode = data[0].areaCode)) {
        shiftTotal = data.shift();
      }
      //按照降序排序
      if (sortingType == "descending") {
        data = data.sort((a, b) => b[fieldName] - a[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        data = data.sort((a, b) => a[fieldName] - b[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      } else {
        this.getSafetyList();
      }
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getEntData();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getEntData(
        this.distCode,
        this.type,
        this.areaName,
        this.startDate,
        this.endDate
      );
    },
    search() {
      this.currentPage = 1;
      this.getEntData(
        this.distCode,
        this.type,
        this.areaName,
        this.startDate,
        this.endDate
      );
    },
    clearDis() {
      this.distCode = "";
    },
    clearMa() {
      this.majorHazardLevel = "";
    },
    clearEntName() {
      this.entName = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.historyList {
  .seach-part {
    font-weight: 600;
    // padding: 0 10px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 500px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      >.btn{
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>