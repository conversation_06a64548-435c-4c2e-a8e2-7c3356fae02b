!function(t){var e={};function r(a){if(e[a])return e[a].exports;var i=e[a]={i:a,l:!1,exports:{}};return t[a].call(i.exports,i,i.exports,r),i.l=!0,i.exports}r.m=t,r.c=e,r.d=function(t,e,a){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:a})},r.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var a=Object.create(null);if(r.r(a),Object.defineProperty(a,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(a,i,function(e){return t[e]}.bind(null,i));return a},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=0)}([function(t,e,r){"use strict";r.r(e);var a={log:function(){},error:function(){},count:function(){},info:function(){}};(function(){function t(){}t.createFromElementId=function(e){for(var r=document.getElementById(e),a="",i=r.firstChild;i;)3===i.nodeType&&(a+=i.textContent),i=i.nextSibling;var n=new t;return n.type=r.type,n.source=a,n},t.createFromSource=function(e,r){var a=new t;return a.type=e,a.source=r,a}})(),function(){function t(t){this.gl=t,this.program=this.gl.createProgram()}t.prototype={attach:function(t){this.gl.attachShader(this.program,t.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(t){return this.gl.getAttribLocation(this.program,t)},setMatrixUniform:function(t,e){var r=this.gl.getUniformLocation(this.program,t);this.gl.uniformMatrix4fv(r,!1,e)}}}(),function(){var t=null;function e(t,e,r){this.gl=t,this.size=e,this.texture=t.createTexture(),t.bindTexture(t.TEXTURE_2D,this.texture),this.format=r||t.LUMINANCE,t.texImage2D(t.TEXTURE_2D,0,this.format,e.w,e.h,0,this.format,t.UNSIGNED_BYTE,null),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MAG_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(t.TEXTURE_2D,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE)}e.prototype={fill:function(t,e){var r=this.gl;r.bindTexture(r.TEXTURE_2D,this.texture),e?r.texSubImage2D(r.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,r.UNSIGNED_BYTE,t):r.texImage2D(r.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,r.UNSIGNED_BYTE,t)},bind:function(e,r,a){var i=this.gl;t||(t=[i.TEXTURE0,i.TEXTURE1,i.TEXTURE2]),i.activeTexture(t[e]),i.bindTexture(i.TEXTURE_2D,this.texture),i.uniform1i(i.getUniformLocation(r.program,a),e)}}}();function i(){var t=0,e=0,r=null;function a(){t=360,e=240,r=null,this._length=0,this.head=null,this.tail=null,this.curIdx=0}return a.prototype={push:function(t,a,i,n,o,s){var u=new VideoBufferNode(t,a,i,n,o,s);return this._length>0?(this.tail.next=u,u.previous=this.tail,this.tail=u):(this.head=u,this.tail=u),this._length+=1,null!==r&&this._length>=e&&r(),u},pop:function(){var t=null;return this._length>1&&(t=this.head,this.head=this.head.next,null!==this.head?this.head.previous=null:this.tail=null,this._length-=1),t},setMaxLength:function(e){(t=e)>360?t=360:t<30&&(t=30)},setBUFFERING:function(t){(e=t)>240?e=240:e<6&&(e=6)},setBufferFullCallback:function(t){r=t},searchTimestamp:function(t){var e=this.head,r=this._length,a=1;if(0===r||t<=0||null===e)throw new Error("Failure: non-existent node in this list.");for(;null!==e&&(e.timeStamp.timestamp!==t.timestamp||e.timeStamp.timestamp_usec!==t.timestamp_usec);)e=e.next,a++;return r<a?e=null:this.curIdx=a,e},findIFrame:function(t){var e=this.head,r=this._length,a=1;if(0===r)throw new Error("Failure: non-existent node in this list.");for(;a<this.curIdx;)e=e.next,a++;if(!0===t)for(;"I"!==e.frameType;)e=e.next,a++;else for(;"I"!==e.frameType;)e=e.previous,a--;return r<a?e=null:this.curIdx=a,e}},new a}var n=function(){var t,e,r,i,n,o,s,u,f,l=null,c=new Uint8Array,p=!1;function m(){l=Module._OpenDecoder(0,0,0),m.prototype.setIsFirstFrame(!1)}return m.prototype={init:function(){a.log("H264 Decoder init")},setOutputSize:function(t){u!=1.5*t&&(u=1.5*t,f=Module._malloc(u),c=new Uint8Array(Module.HEAPU8.buffer,f,u))},decode:function(a,f){if(t=Date.now(),e=new Uint8Array(a),c.set(e),r=Module._FrameAlloc(),Module._DecodeFrame(l,c.byteOffset,a.byteLength,u,r),i=Date.now()-t,o=Module._getYLength(r),n=Module._getHeight(r),!m.prototype.isFirstFrame())return m.prototype.setIsFirstFrame(!0),{firstFrame:!0};if(o>0&&n>0){t=Date.now();var p=new Uint8Array(c);return s={data:p,option:{yaddr:Module._getY(r),uaddr:Module._getU(r),vaddr:Module._getV(r),ylen:o,height:n,beforeDecoding:t},width:o,height:n,codecType:"h264",decodingTime:i,frameType:f},Module._FrameFree(r),s}},setIsFirstFrame:function(t){p=t},isFirstFrame:function(){return p},free:function(){Module._free(f),f=null}},new m},o=new function(){var t=[],e={};function r(){for(var r in t)t[r]=[r.charCodeAt(0),r.charCodeAt(1),r.charCodeAt(2),r.charCodeAt(3)];e.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]),e.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),e.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSC=e.STCO=e.STTS,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),e.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),e.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}t={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};var i=function(t){for(var e=8,r=Array.prototype.slice.call(arguments,1),a=0;a<r.length;a++)e+=r[a].byteLength;var i=new Uint8Array(e),n=0;for(i[n++]=e>>>24&255,i[n++]=e>>>16&255,i[n++]=e>>>8&255,i[n++]=255&e,i.set(t,n),n+=4,a=0;a<r.length;a++)i.set(r[a],n),n+=r[a].byteLength;return i},n=function(e){return i(t.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),function(e){var r=e.config,a=r.length,n=new Uint8Array([0,0,0,0,3,23+a,0,1,0,4,15+a,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([a]).concat(r).concat([6,1,2]));return i(t.esds,n)}(e))},o=function(r){return"audio"===r.type?i(t.stsd,e.STSD_PREFIX,n(r)):i(t.stsd,e.STSD_PREFIX,function(e){var r=e.sps||[],a=e.pps||[],n=[],o=[],s=0;for(s=0;s<r.length;s++)n.push((65280&r[s].byteLength)>>>8),n.push(255&r[s].byteLength),n=n.concat(Array.prototype.slice.call(r[s]));for(s=0;s<a.length;s++)o.push((65280&a[s].byteLength)>>>8),o.push(255&a[s].byteLength),o=o.concat(Array.prototype.slice.call(a[s]));return i(t.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),i(t.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([r.length]).concat(n).concat([a.length]).concat(o))))}(r))},s=function(r){var a=null;return a="audio"===r.type?i(t.smhd,e.SMHD):i(t.vmhd,e.VMHD),i(t.minf,a,i(t.dinf,i(t.dref,e.DREF)),function(r){return i(t.stbl,o(r),i(t.stts,e.STTS),i(t.stsc,e.STSC),i(t.stsz,e.STSZ),i(t.stco,e.STCO))}(r))},u=function(r){return i(t.mdia,function(e){var r=e.timescale,a=e.duration;return i(t.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,a>>>24&255,a>>>16&255,a>>>8&255,255&a,85,196,0,0]))}(r),function(r){var a=null;return a="audio"===r.type?e.HDLR_AUDIO:e.HDLR_VIDEO,i(t.hdlr,a)}(r),s(r))},f=function(e){return i(t.trak,function(e){var r=e.id,a=e.duration,n=e.width,o=e.height;return i(t.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,a>>>24&255,a>>>16&255,a>>>8&255,255&a,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,n>>>8&255,255&n,0,0,o>>>8&255,255&o,0,0]))}(e),u(e))},l=function(e){return i(t.mvex,function(e){var r=e.id,a=new Uint8Array([0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return i(t.trex,a)}(e))},c=function(e){var r,n,o=(r=e.timescale,n=e.duration,a.log("mvhd:  timescale: "+r+"  duration: "+n),i(t.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))),s=f(e),u=l(e);return i(t.moov,o,s,u)},p=function(e,r){return"audio"===e.type?audioTrun(e,r):function(e,r){var a,n=null,o=null,s=0,u=r;if(null===(a=e.samples||[])[0].frameDuration)for(u+=24+4*a.length,n=trunHeader(a,u),s=0;s<a.length;s++)o=a[s],n=n.concat([(4278190080&o.size)>>>24,(16711680&o.size)>>>16,(65280&o.size)>>>8,255&o.size]);else for(n=function(t,e){return[0,0,3,5,(4278190080&t.length)>>>24,(16711680&t.length)>>>16,(65280&t.length)>>>8,255&t.length,(4278190080&e)>>>24,(16711680&e)>>>16,(65280&e)>>>8,255&e,0,0,0,0]}(a,u+=24+4*a.length+4*a.length),s=0;s<a.length;s++)o=a[s],n=n.concat([(4278190080&o.frameDuration)>>>24,(16711680&o.frameDuration)>>>16,(65280&o.frameDuration)>>>8,255&o.frameDuration,(4278190080&o.size)>>>24,(16711680&o.size)>>>16,(65280&o.size)>>>8,255&o.size]);return i(t.trun,new Uint8Array(n))}(e,r)},m=function(e,r){return i(t.moof,function(e){var r=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return i(t.mfhd,r)}(e),function(e){var r,a,n;return r=i(t.tfhd,new Uint8Array([0,2,0,0,0,0,0,1])),a=i(t.tfdt,new Uint8Array([0,0,0,0,e.baseMediaDecodeTime>>>24&255,e.baseMediaDecodeTime>>>16&255,e.baseMediaDecodeTime>>>8&255,255&e.baseMediaDecodeTime])),n=p(e,72),i(t.traf,r,a,n)}(r))};return r.prototype={initSegment:function(r){var n=i(t.ftyp,e.FTYP);a.log(r);var o=c(r),s=new Uint8Array(n.byteLength+o.byteLength);return s.set(n,0),s.set(o,n.byteLength),s},mediaSegment:function(e,r,a,n){var o=m(e,r),s=function(e){return i(t.mdat,e)}(a),u=null;return(u=new Uint8Array(o.byteLength+s.byteLength)).set(o),u.set(s,o.byteLength),u}},new r},s=function(){this.map={}};s.prototype={put:function(t,e){this.map[t]=e},get:function(t){return this.map[t]},containsKey:function(t){return t in this.map},containsValue:function(t){for(var e in this.map)if(this.map[e]===t)return!0;return!1},isEmpty:function(t){return 0===this.size()},clear:function(){for(var t in this.map)delete this.map[t]},remove:function(t){delete this.map[t]},keys:function(){var t=new Array;for(var e in this.map)t.push(e);return t},values:function(){var t=new Array;for(var e in this.map)t.push(this.map[e]);return t},size:function(){var t=0;for(var e in this.map)t++;return t}};var u=s;function f(){var t=7,e=7,r=2,a=3,i=4,n=5,o=8,s=16,f=32,l=255,c=0,p=null;function m(){c=0,p=new u}function d(r,i){var n=i,o=c+n>>a;return n=c+i&t,r[o]>>e-(n&e)&1}function _(t,e){var r=c>>a,i=8*(r+1)-c;if(i<8)for(var n=0;n<3;n++){var o=t[r+n];o=0==n?o>>i<<i:2==n?o&255>>8-i|1<<i:0,t.set([o],r+n)}else t.set([0],r),t.set([1],r+1)}function h(t,e){var r=0;if(1===e)r=d(t,0);else for(var a=0;a<e;a++)r=(r<<1)+d(t,a);return c+=e,r}function g(t,e){for(var a=0,i=0,n=e;c+n<8*t.length&&!d(t,n++);)a++;if(0===a)return c+=1,0;i=1<<a;for(var o=a-1;o>=0;o--,n++)i|=d(t,n)<<o;return c+=a*r+1,i-1}function v(t,e){var a=g(t,e);return 1&a?(a+1)/r:-a/r}function y(t){p.put("cpb_cnt_minus1",g(t,0)),p.put("bit_rate_scale",h(t,i)),p.put("cpb_size_scale",h(t,i));for(var e=p.get("cpb_cnt_minus1"),r=new Array(e),a=new Array(e),o=new Array(e),s=0;s<=e;s++)r[s]=g(t,0),a[s]=g(t,0),o[s]=h(t,1);p.put("bit_rate_value_minus1",r),p.put("cpb_size_value_minus1",a),p.put("cbr_flag",o),p.put("initial_cpb_removal_delay_length_minus1",h(t,n)),p.put("cpb_removal_delay_length_minus1",h(t,n)),p.put("dpb_output_delay_length_minus1",h(t,n)),p.put("time_offset_length",h(t,n))}return m.prototype={parse:function(t){c=0,p.clear(),p.put("forbidden_zero_bit",h(t,1)),p.put("nal_ref_idc",h(t,r)),p.put("nal_unit_type",h(t,n)),p.put("profile_idc",h(t,o)),p.put("profile_compatibility",h(t,o)),p.put("level_idc",h(t,o)),p.put("seq_parameter_set_id",g(t,0));var e=p.get("profile_idc");if((100===e||110===e||122===e||244===e||44===e||83===e||86===e||118===e||128===e||138===e||139===e||134===e)&&(p.put("chroma_format_idc",g(t,0)),p.get("chroma_format_idc")===a&&p.put("separate_colour_plane_flag",h(t,1)),p.put("bit_depth_luma_minus8",g(t,0)),p.put("bit_depth_chroma_minus8",g(t,0)),p.put("qpprime_y_zero_transform_bypass_flag",h(t,1)),p.put("seq_scaling_matrix_present_flag",h(t,1)),p.get("seq_scaling_matrix_present_flag"))){for(var i=p.get("chroma_format_idc")!==a?o:12,u=new Array(i),m=0;m<i;m++)if(u[m]=h(t,1),u[m])for(var d=m<6?s:64,b=8,D=8,T=0;T<d;T++)D&&(D=(b+v(t,0)+256)%256),b=0===D?b:D;p.put("seq_scaling_list_present_flag",u)}if(p.put("log2_max_frame_num_minus4",g(t,0)),p.put("pic_order_cnt_type",g(t,0)),0===p.get("pic_order_cnt_type"))p.put("log2_max_pic_order_cnt_lsb_minus4",g(t,0));else if(1===p.get("pic_order_cnt_type")){p.put("delta_pic_order_always_zero_flag",h(t,1)),p.put("offset_for_non_ref_pic",v(t,0)),p.put("offset_for_top_to_bottom_field",v(t,0)),p.put("num_ref_frames_in_pic_order_cnt_cycle",g(t,0));for(var S=0;S<p.get("num_ref_frames_in_pic_order_cnt_cycle");S++)p.put("num_ref_frames_in_pic_order_cnt_cycle",v(t,0))}return p.put("num_ref_frames",g(t,0)),p.put("gaps_in_frame_num_value_allowed_flag",h(t,1)),p.put("pic_width_in_mbs_minus1",g(t,0)),p.put("pic_height_in_map_units_minus1",g(t,0)),p.put("frame_mbs_only_flag",h(t,1)),0===p.get("frame_mbs_only_flag")&&p.put("mb_adaptive_frame_field_flag",h(t,1)),p.put("direct_8x8_interence_flag",h(t,1)),p.put("frame_cropping_flag",h(t,1)),1===p.get("frame_cropping_flag")&&(p.put("frame_cropping_rect_left_offset",g(t,0)),p.put("frame_cropping_rect_right_offset",g(t,0)),p.put("frame_cropping_rect_top_offset",g(t,0)),p.put("frame_cropping_rect_bottom_offset",g(t,0))),p.put("vui_parameters_present_flag",h(t,1)),p.get("vui_parameters_present_flag")&&function(t){p.put("aspect_ratio_info_present_flag",h(t,1)),p.get("aspect_ratio_info_present_flag")&&(p.put("aspect_ratio_idc",h(t,o)),p.get("aspect_ratio_idc")===l&&(_(t),p.put("sar_width",h(t,s)),_(t),p.put("sar_height",h(t,s)))),p.put("overscan_info_present_flag",h(t,1)),p.get("overscan_info_present_flag")&&p.put("overscan_appropriate_flag",h(t,1)),p.put("video_signal_type_present_flag",h(t,1)),p.get("video_signal_type_present_flag")&&(p.put("video_format",h(t,a)),p.put("video_full_range_flag",h(t,1)),p.put("colour_description_present_flag",h(t,1)),p.get("colour_description_present_flag")&&(p.put("colour_primaries",h(t,o)),p.put("transfer_characteristics",h(t,o)),p.put("matrix_coefficients",h(t,o)))),p.put("chroma_loc_info_present_flag",h(t,1)),p.get("chroma_loc_info_present_flag")&&(p.put("chroma_sample_loc_type_top_field",g(t,0)),p.put("chroma_sample_loc_type_bottom_field",g(t,0))),p.put("timing_info_present_flag",h(t,1)),p.get("timing_info_present_flag")&&(p.put("num_units_in_tick",h(t,f)),p.put("time_scale",h(t,f)),p.put("fixed_frame_rate_flag",h(t,1))),p.put("nal_hrd_parameters_present_flag",h(t,1)),p.get("nal_hrd_parameters_present_flag")&&y(t),p.put("vcl_hrd_parameters_present_flag",h(t,1)),p.get("vcl_hrd_parameters_present_flag")&&y(t),(p.get("nal_hrd_parameters_present_flag")||p.get("vcl_hrd_parameters_present_flag"))&&p.put("low_delay_hrd_flag",h(t,1)),p.put("pic_struct_present_flag",h(t,1)),p.put("bitstream_restriction_flag",h(t,1)),p.get("bitstream_restriction_flag")&&(p.put("motion_vectors_over_pic_boundaries_flag",h(t,1)),p.put("max_bytes_per_pic_denom",g(t,0)),p.put("max_bits_per_mb_denom",g(t,0)))}(t),!0},getSizeInfo:function(){var t=0,e=0;0===p.get("chroma_format_idc")?t=e=0:1===p.get("chroma_format_idc")?t=e=r:p.get("chroma_format_idc")===r?(t=r,e=1):p.get("chroma_format_idc")===a&&(0===p.get("separate_colour_plane_flag")?t=e=1:1===p.get("separate_colour_plane_flag")&&(t=e=0));var i=p.get("pic_width_in_mbs_minus1")+1,n=p.get("pic_height_in_map_units_minus1")+1,o=(r-p.get("frame_mbs_only_flag"))*n,u=0,f=0,l=0,c=0;1===p.get("frame_cropping_flag")&&(u=p.get("frame_cropping_rect_left_offset"),f=p.get("frame_cropping_rect_right_offset"),l=p.get("frame_cropping_rect_top_offset"),c=p.get("frame_cropping_rect_bottom_offset"));var m=i*s*(o*s);return{width:i*s-t*(u+f),height:o*s-e*(r-p.get("frame_mbs_only_flag"))*(l+c),decodeSize:m}},getSpsValue:function(t){return p.get(t)},getCodecInfo:function(){return p.get("profile_idc").toString(s)+(p.get("profile_compatibility")<15?"0"+p.get("profile_compatibility").toString(s):p.get("profile_compatibility").toString(s))+p.get("level_idc").toString(s)}},new m}var l=function(){var t=0,e=0,r=!1,s=(new Uint8Array(1048576),new Uint8Array(["0x00","0x00","0x00","0x01"]),new f),u=0,l=null,c=null,p=0,m=!1,d=0,_={frameData:null,timeStamp:null,initSegmentData:null,mediaSample:null,dropPercent:0,dropCount:0,codecInfo:"",playback:!1},h={timestamp:null,timezone:null},g={},v=null,y=null,b=!1,D=!1,T=0,S=0,w=0,C=0,I=!1,F=0,M=null,k=null,x="",A=null,L=0,R=0,E={width:0,height:0},B=null,P=!1;function U(t){t!==x&&("video"===t?x="video":(x="canvas",m=!0,d=0,_.frameData.firstFrame=!0))}function O(t,e,r){var a="";return t*e>921600&&!1===r?(a="video",P&&L>0&&L<=3&&(a="canvas")):a="canvas",a}function z(){this.decoder=new n,this.firstDiffTime=0,this.firstTime=0,this.lastMSW=0}return z.prototype={setReturnCallback:function(t){this.rtpReturnCallback=t},setBufferfullCallback:function(t){null!==this.videoBufferList&&this.videoBufferList.setBufferFullCallback(t)},getVideoBuffer:function(t){if(null!==this.videoBufferList)return this.videoBufferList.searchNodeAt(t)},clearBuffer:function(){null!==this.videoBufferList&&this.videoBufferList.clear()},findCurrent:function(){null!==this.videoBufferList&&this.videoBufferList.searchTimestamp(this.getTimeStamp())},setTimeStamp:function(t){this.timeData=t},getTimeStamp:function(){return this.timeData},ntohl:function(t){return(t[0]<<24)+(t[1]<<16)+(t[2]<<8)+t[3]>>>0},appendBuffer:function(t,e,r){if(r+e.length>=t.length){var a=new Uint8Array(t.length+1048576);a.set(t,0),t=a}return t.set(e,r),t},getFramerate:function(){return L},setGovLength:function(t){A=t},getGovLength:function(){return A},setDecodingTime:function(t){this.decodingTime=t},getDropPercent:function(){return 0},getDropCount:function(){return 0},initStartTime:function(){this.firstDiffTime=0,this.calcGov=0},setCheckDelay:function(t){this.checkDelay=t},init:function(t){b=!1,r=!1,x=t,this.decoder.setIsFirstFrame(!1),this.videoBufferList=new i,this.firstDiffTime=0,this.checkDelay=!0,this.timeData=null},setFramerate:function(t){0<t&&"undefined"!==typeof t&&(L=t,null!==this.videoBufferList&&(this.videoBufferList.setMaxLength(6*L),this.videoBufferList.setBUFFERING(4*L)))},parseRTPData:function(i,n,f,A,L){var P=null,z={},j=(n[19]<<24)+(n[18]<<16)+(n[17]<<8)+n[16]>>>0,N=Date.UTC("20"+(j>>>26),(j>>>22&15)-1,j>>>17&31,j>>>12&31,j>>>6&63,63&j)/1e3;if(L.timeStampmsw,N+=(new Date).getTimezoneOffset()/60*3600,x||253!==n[4]||(B=0!==n[5],x=O(L.width,L.height,B)),""!==x){if(0==this.firstTime)this.firstTime=N,this.lastMSW=0,k=(n[21]<<8)+n[20],h={timestamp:this.firstTime,timestamp_usec:0};else{var W,G=(n[21]<<8)+n[20];W=G>k?G-k:G+65535-k,this.lastMSW+=W,N>this.firstTime&&(this.lastMSW-=1e3),this.firstTime=N,h={timestamp:N,timestamp_usec:this.lastMSW},k=G}0!==this.getFramerate()&&"undefined"!==typeof this.getFramerate()||"undefined"===typeof this.getTimeStamp()||(this.setFramerate(Math.round(1e3/((h.timestamp-this.getTimeStamp().timestamp===0?0:1e3)+(h.timestamp_usec-this.getTimeStamp().timestamp_usec)))),a.log("setFramerate"+Math.round(1e3/((h.timestamp-this.getTimeStamp().timestamp===0?0:1e3)+(h.timestamp_usec-this.getTimeStamp().timestamp_usec))))),this.setTimeStamp(h);for(var V=n[22],H=n.subarray(24+V,n.length-8),X=n.subarray(n.length-8,n.length),Y=(X[7],X[6],X[5],X[4],[]),q=0;q<=H.length;)if(0==H[q])if(0==H[q+1])if(1==H[q+2]){if(Y.push(q),5==(31&H[q+=3])||1==(31&H[q]))break}else 0==H[q+2]?q++:q+=3;else q+=2;else q+=1;var J="P";t=(n[21]<<8)+n[20];var Z=0;for(q=0;q<Y.length;q++)switch(P=H.subarray(Y[q]+3,Y[q+1]),31&H[Y[q]+3]){default:break;case 1:J="P",Z=Y[q]-1;break;case 5:J="I",Z=Y[q]-1;break;case 28:break;case 7:s.parse(P);var K=L;u=s.getSizeInfo().decodeSize,null!==l&&null!==c&&l.width===K.width&&l.height===K.height&&c===s.getCodecInfo()||(b=!1,l=K,c=s.getCodecInfo(),this.decoder.setIsFirstFrame(!1)),w=T=K.width,C=S=K.height,v=P,E.width==K.width&&E.height==K.height||(0!=E.width?(E.width=K.width,E.height=K.height,z.resolution=E,z.resolution.decodeMode=O(E.width,E.height,B),z.resolution.encodeMode="h264"):(E.width=K.width,E.height=K.height,z.decodeStart=E,z.decodeStart.decodeMode=x,z.decodeStart.encodeMode="h264"));break;case 8:y=P;break;case 6:case 9:}if(f&&!1===m&&(z.backupData={stream:new Uint8Array(H),frameType:J,width:w,height:C,codecType:"h264"},null!==h.timestamp&&"undefined"!==typeof h.timestamp?z.backupData.timestamp_usec=h.timestamp_usec:z.backupData.timestamp=(t/90).toFixed(0)),"canvas"===x){var Q=1e3*h.timestamp+h.timestamp_usec;if(0==this.firstDiffTime?(p=0,this.firstDiffTime=Date.now()-Q,a.log("firstDiff: "+R)):(Q-M<0&&(this.firstDiffTime=p+(Date.now()-Q).toFixed(0)),(p=Date.now()-Q-this.firstDiffTime)<0&&(this.firstDiffTime=0,p=0),p>8e3&&(z.error={errorCode:101},this.rtpReturnCallback(z))),M=Q,e!==u&&(this.decoder.free(),e=u,this.decoder.setOutputSize(e)),!0===m&&"P"===J)return;!0===m&&(m=!1),"I"===J&&d<2&&d++,_.frameData=null,!0===f&&!0===r||(_.frameData=this.decoder.decode(H)),_.timeStamp=null,h=null===h.timestamp?this.getTimeStamp():h,_.timeStamp=h}else{var $=null;if(b?_.initSegmentData=null:(b=!0,L={id:1,width:T,height:S,type:"video",profileIdc:s.getSpsValue("profile_idc"),profileCompatibility:0,levelIdc:s.getSpsValue("level_idc"),sps:[v],pps:[y],timescale:1e3,fps:this.getFramerate()},a.log(JSON.stringify(L)),_.initSegmentData=o.initSegment(L),_.codecInfo=s.getCodecInfo()),Z||a.log("11111111111111111111111111111111111111111"),"I"===J){var tt=Z;$=H.subarray(tt,H.length)}else $=H.subarray(Z,H.length);var et=$.length-4;$[0]=(4278190080&et)>>>24,$[1]=(16711680&et)>>>16,$[2]=(65280&et)>>>8,$[3]=255&et;var rt=this.getFramerate(),at={duration:Math.round(1/rt*1e3),size:$.length,frame_time_stamp:null,frameDuration:null};if(r)at.frame_time_stamp=t,_.frameData=new Uint8Array($),_.mediaSample=at;else{if(!1===f){if(at.frame_time_stamp=1e3*h.timestamp+h.timestamp_usec-R,!1===D)at.frame_time_stamp=0,R=1e3*h.timestamp+h.timestamp_usec,at.frameDuration=0,g=at,D=!0;else{var it=g.frame_time_stamp,nt=at.frame_time_stamp;at.frameDuration=Math.abs(nt-it),at.frameDuration>3e3&&(at.frameDuration=0),g=at}_.frameData=new Uint8Array($),_.mediaSample=at}h=null===h.timestamp?this.getTimeStamp():h,_.timeStamp=h}}var ot=w*C;!0===r&&(NaN.toFixed(0),ot>786432?(U("video"),z.decodeMode="video"):(U("canvas"),z.decodeMode="canvas"));try{_.playback=r,_.frameData.frameIndex=L.frameIndex,z.decodedData=_}catch(t){}if(!0===I)return"I"===J&&F++,2===F&&(F=0,I=!1),void a.info("H264Session::stop");this.rtpReturnCallback(z)}},findIFrame:function(){if(null!==this.videoBufferList){var t=this.videoBufferList.findIFrame();if(null===t||"undefined"===typeof t)return!1;var e={};return this.setTimeStamp(t.timeStamp),e.frameData=this.decoder.decode(t.buffer),e.timeStamp=t.timeStamp,e}},setInitSegment:function(){b=!1,l=null,c=null},setLessRate:function(t){P=t}},new z};var c=function(){var t,e,r,i,n,o,s,u,f,l=null,c=new Uint8Array,p=!1;function m(){l=Module._OpenDecoder(1,0,0),m.prototype.setIsFirstFrame(!1)}return m.prototype={init:function(){a.log("H265 Decoder init")},setOutputSize:function(t){u!=1.5*t&&(u=1.5*t,f=Module._malloc(u),c=new Uint8Array(Module.HEAPU8.buffer,f,u))},decode:function(a,f){if(t=Date.now(),e=new Uint8Array(a),c.set(e),r=Module._FrameAlloc(),Module._DecodeFrame(l,c.byteOffset,a.byteLength,u,r),i=Date.now()-t,o=Module._getYLength(r),n=Module._getHeight(r),!m.prototype.isFirstFrame())return m.prototype.setIsFirstFrame(!0),{firstFrame:!0};if(o>0&&n>0){t=Date.now();var p=new Uint8Array(c);return s={data:p,option:{yaddr:Module._getY(r),uaddr:Module._getU(r),vaddr:Module._getV(r),ylen:o,height:n,beforeDecoding:t},width:o,height:n,codecType:"h265",decodingTime:i,frameType:f},Module._FrameFree(r),s}},setIsFirstFrame:function(t){p=t},isFirstFrame:function(){return p},free:function(){Module._free(f),f=null}},new m};function p(){var t=null,e=null,r=0,a=0,i=0,n=0,o=0;function s(){0,t=new u}function f(){return 0==i&&(n=function(){if(a>=r)return 0;var t=e[a++];return 0==t?(o++,a<r&&2==o&&3==e[a]&&(a++,o=0)):o=0,t}(),i=8),n>>--i&1}function l(t,e){for(var r=0;e>0;)r<<=1,r|=f(),e--;return r}function c(t,e){for(var i=0;a<r&&0==f();)i++;return l(0,i)+((1<<i)-1)}return s.prototype={parse:function(e){e,0,t.clear(),t.put("forbidden_zero_bit",l(0,1)),t.put("nal_unit_type",l(0,6)),t.put("nuh_layer_id",l(0,6)),t.put("nuh_temporal_id_plus1",l(0,3)),t.put("sps_video_parameter_set_id",l(0,4)),0===t.get("nuh_layer_id")?t.put("sps_max_sub_layers_minus1",l(0,3)):t.put("sps_ext_or_max_sub_layers_minus1",l(0,3));var r=0!==t.get("nuh_layer_id")&&7===t.get("sps_ext_or_max_sub_layers_minus1");return r||(t.put("sps_max_sub_layers_minus1",l(0,1)),function(e,r){if(e){t.put("general_profile_space",l(0,2)),t.put("general_tier_flag",l(0,1)),t.put("general_profile_idc",l(0,5));for(var a=new Array(32),i=0;i<32;i++)a[i]=l(0,1);t.put("general_progressive_source_flag",l(0,1)),t.put("general_interlaced_source_flag",l(0,1)),t.put("general_non_packed_constraint_flag",l(0,1)),t.put("general_frame_only_constraint_flag",l(0,1));var n=t.get("general_profile_idc");4===n||a[4]||5===n||a[5]||6===n||a[6]||7===n||a[7]||8===n||a[8]||9===n||a[9]||10===n||a[10]?(t.put("general_max_12bit_constraint_flag",l(0,1)),t.put("general_max_10bit_constraint_flag",l(0,1)),t.put("general_max_8bit_constraint_flag",l(0,1)),t.put("general_max_422chroma_constraint_flag",l(0,1)),t.put("general_max_420chroma_constraint_flag",l(0,1)),t.put("general_max_monochrome_constraint_flag",l(0,1)),t.put("general_intra_constraint_flag",l(0,1)),t.put("general_one_picture_only_constraint_flag",l(0,1)),t.put("general_lower_bit_rate_constraint_flag",l(0,1)),5===n||a[5]||9===n||a[9]||10===n||a[10]?(t.put("general_max_14bit_constraint_flag",l(0,1)),t.put("general_reserved_zero_33bits",l(0,33))):t.put("general_reserved_zero_34bits",l(0,34))):t.put("general_reserved_zero_43bits",l(0,43)),n>=1&&n<=5||a[1]||a[2]||a[3]||a[4]||a[5]||a[9]?t.put("general_inbld_flag",l(0,1)):t.put("general_reserved_zero_bit",l(0,1))}t.put("general_level_idc",l(0,8));var o=new Array(r),s=new Array(r);for(_=0;_<r;_++)o[_]=l(0,1),s[_]=l(0,1);var u=new Array(8),f=new Array(r),c=new Array(r),p=new Array(r),m=[],d=new Array(r);if(r>0)for(var _=r;_<8;_++)u[_]=l(0,2);for(_=0;_<r;_++){if(o[_]){for(c[_]=l(0,2),p[_]=l(0,1),f[_]=l(0,5),i=0;i<32;i++)m[_][i]=l(0,1);t.put("sub_layer_progressive_source_flag",l(0,1)),t.put("sub_layer_interlaced_source_flag",l(0,1)),t.put("sub_layer_non_packed_constraint_flag",l(0,1)),t.put("sub_layer_frame_only_constraint_flag",l(0,1)),4===f[_]||m[_][4]||5===f[_]||m[_][5]||6===f[_]||m[_][6]||7===f[_]||m[_][7]||8===f[_]||m[_][8]||9===f[_]||m[_][9]||10===f[_]||m[_][10]?(t.put("sub_layer_max_12bit_constraint_flag",l(0,1)),t.put("sub_layer_max_10bit_constraint_flag",l(0,1)),t.put("sub_layer_max_8bit_constraint_flag",l(0,1)),t.put("sub_layer_max_422chroma_constraint_flag",l(0,1)),t.put("sub_layer_max_420chroma_constraint_flag",l(0,1)),t.put("sub_layer_max_monochrome_constraint_flag",l(0,1)),t.put("sub_layer_intra_constraint_flag",l(0,1)),t.put("sub_layer_one_picture_only_constraint_flag",l(0,1)),t.put("sub_layer_lower_bit_rate_constraint_flag",l(0,1)),5===f[_]||m[_][5]?(t.put("sub_layer_max_14bit_constraint_flag",l(0,1)),t.put("sub_layer_lower_bit_rate_constraint_flag",l(0,1)),d[_]=l(0,33)):d[_]=l(0,34)):t.put("sub_layer_reserved_zero_43bits",l(0,43)),f[_]>=1&&f[_]<=5||9==f[_]||m[1]||m[2]||m[3]||m[4]||m[5]||m[9]?t.put("sub_layer_inbld_flag",l(0,1)):t.put("sub_layer_reserved_zero_bit",l(0,1))}s[_]&&t.put("sub_layer_level_idc",l(0,8))}}(1,t.get("sps_max_sub_layers_minus1"))),l(0,84),t.put("sps_seq_parameter_set_id",c()),r?(t.put("update_rep_format_flag",l(0,1)),t.get("update_rep_format_flag")&&t.put("sps_rep_format_idx",l(0,8))):(t.put("chroma_format_idc",c()),3===t.get("chroma_format_idc")&&t.put("separate_colour_plane_flag",l(0,1)),t.put("pic_width_in_luma_samples",c()),t.put("pic_height_in_luma_samples",c()),t.put("conformance_window_flag",l(0,1)),t.get("conformance_window_flag")&&(t.put("conf_win_left_offset",c()),t.put("conf_win_right_offset",c()),t.put("conf_win_top_offset",c()),t.put("conf_win_bottom_offset",c()))),!0},parse2:function(s){var u=s.length;if(s,e=s,r=s.length,a=0,i=0,n=0,o=0,0,t.clear(),u<20)return!1;l(0,16),l(0,4);var f=l(0,3);if(t.put("sps_max_sub_layers_minus1",f),f>6)return!1;l(0,1),l(0,2),l(0,1);l(0,5);l(0,32),l(0,1),l(0,1),l(0,1),l(0,1),l(0,43),l(0,1),t.put("general_level_idc",l(0,8));for(var p=[],m=[],d=0;d<f;d++)p[d]=l(0,1),m[d]=l(0,1);if(f>0)for(d=f;d<8;d++)l(0,2);for(d=0;d<f;d++)p[d]&&(l(0,2),l(0,1),l(0,5),l(0,32),l(0,1),l(0,1),l(0,1),l(0,1),l(0,44)),m[d]&&l(0,8);var _=c();if(t.put("sps_seq_parameter_set_id",_),_>15)return!1;var h=c();return t.put("chroma_format_idc",h),!(_>3)&&(3==h&&l(0,1),t.put("pic_width_in_luma_samples",c()),t.put("pic_height_in_luma_samples",c()),l(0,1)&&(c(),c(),c(),c()),c()==c())},getSizeInfo:function(){var e=t.get("pic_width_in_luma_samples"),r=t.get("pic_height_in_luma_samples");if(t.get("conformance_window_flag")){var a=t.get("chroma_format_idc"),i=t.get("separate_colour_plane_flag");"undefined"===typeof i&&(i=0);var n=1!==a&&2!==a||0!==i?1:2,o=1===a&&0===i?2:1;e-=n*t.get("conf_win_right_offset")+n*t.get("conf_win_left_offset"),r-=o*t.get("conf_win_bottom_offset")+o*t.get("conf_win_top_offset")}return{width:e,height:r,decodeSize:e*r}},getSpsValue:function(e){return t.get(e)}},new s}var m=function(){var t,e=0,r=0,n=0,o=0,s=new p,u={frameData:null,timeStamp:null},f={timestamp:null,timezone:null},l=0,m=0,d=null,_=0,h={width:0,height:0},g=0,v=0;function y(){this.decoder=c(),this.firstTime=0,this.lastMSW=0}return y.prototype={setReturnCallback:function(t){this.rtpReturnCallback=t},setBufferfullCallback:function(t){null!==this.videoBufferList&&this.videoBufferList.setBufferFullCallback(t)},getVideoBuffer:function(t){if(null!==this.videoBufferList)return this.videoBufferList.searchNodeAt(t)},clearBuffer:function(){null!==this.videoBufferList&&this.videoBufferList.clear()},findCurrent:function(){null!==this.videoBufferList&&this.videoBufferList.searchTimestamp(this.getTimeStamp())},ntohl:function(t){return(t[0]<<24)+(t[1]<<16)+(t[2]<<8)+t[3]>>>0},appendBuffer:function(t,e,r){if(r+e.length>=t.length){var a=new Uint8Array(t.length+1048576);a.set(t,0),t=a}return t.set(e,r),t},setGovLength:function(t){d=t},getGovLength:function(){return d},setDecodingTime:function(t){this.decodingTime=t},getDropPercent:function(){return 0},getDropCount:function(){return 0},initStartTime:function(){this.firstDiffTime=0,this.calcGov=0},setCheckDelay:function(t){this.checkDelay=t},init:function(){this.decoder.setIsFirstFrame(!1),this.videoBufferList=new i,this.firstDiffTime=0,this.checkDelay=!0,this.timeData=null},parseRTPData:function(i,c,p,d,_){var y=null,b={},D=(c[19]<<24)+(c[18]<<16)+(c[17]<<8)+c[16]>>>0,T=Date.UTC("20"+(D>>>26),(D>>>22&15)-1,D>>>17&31,D>>>12&31,D>>>6&63,63&D)/1e3;if(T+=(new Date).getTimezoneOffset()/60*3600,0==this.firstTime)this.firstTime=T,this.lastMSW=0,r=(c[21]<<8)+c[20],f={timestamp:this.firstTime,timestamp_usec:0};else{var S,w=(c[21]<<8)+c[20];S=w>r?w-r:w+65535-r,this.lastMSW+=S,T>this.firstTime&&(this.lastMSW-=1e3),this.firstTime=T,f={timestamp:T,timestamp_usec:this.lastMSW},r=w}0!==this.getFramerate()&&"undefined"!==typeof this.getFramerate()||"undefined"===typeof this.getTimeStamp()||(this.setFramerate(Math.round(1e3/((f.timestamp-this.getTimeStamp().timestamp===0?0:1e3)+(f.timestamp_usec-this.getTimeStamp().timestamp_usec)))),a.log("setFramerate"+Math.round(1e3/((f.timestamp-this.getTimeStamp().timestamp===0?0:1e3)+(f.timestamp_usec-this.getTimeStamp().timestamp_usec))))),this.setTimeStamp(f);var C=c[22];t=c.subarray(24+C,c.length-8),e=(c[21]<<8)+c[20];for(var I=[],F=0;F<=t.length;)if(0==t[F])if(0==t[F+1])if(1==t[F+2]){if(I.push(F),5==(31&t[F+=3])||1==(31&t[F]))break}else 0==t[F+2]?F++:F+=3;else F+=2;else F+=1;var M="P";for(F=0;F<I.length;F++)switch(y=t.subarray(I[F]+3,I[F+1]),t[I[F]+3]>>1&63){default:break;case 33:M="I",s.parse2(y);var k=_;o=s.getSizeInfo().decodeSize,l=k.width,m=k.height,h.width==k.width&&h.height==k.height||(0!=h.width?(h.width=k.width,h.height=k.height,b.resolution=h,b.resolution.decodeMode="canvas",b.resolution.encodeMode="h265"):(h.width=k.width,h.height=k.height,b.decodeStart=h,b.decodeStart.decodeMode="canvas",b.decodeStart.encodeMode="h265"))}var x=1e3*f.timestamp+f.timestamp_usec;0==this.firstDiffTime?(g=0,this.firstDiffTime=Date.now()-x,a.log("firstDiff: 0")):(x-v<0&&(this.firstDiffTime=g+(Date.now()-x).toFixed(0)),(g=Date.now()-x-this.firstDiffTime)<0&&(this.firstDiffTime=0,g=0),g>8e3&&(b.error={errorCode:101},this.rtpReturnCallback(b))),v=x,u.frameData=null,n!==o&&(this.decoder.free(),n=o,this.decoder.setOutputSize(n)),u.frameData=this.decoder.decode(t),u.frameData.frameType=M,u.frameData.frameIndex=_.frameIndex,u.timeStamp=null,f=null===f.timestamp?this.getTimeStamp():f,u.timeStamp=f,p&&(b.backupData={stream:t,frameType:M,width:l,height:m,codecType:"h265"},null!==f.timestamp&&"undefined"!==typeof f.timestamp?b.backupData.timestamp_usec=f.timestamp_usec:b.backupData.timestamp=(e/90).toFixed(0)),b.decodedData=u,this.rtpReturnCallback(b)},findIFrame:function(){if(null!==this.videoBufferList){var t=this.videoBufferList.findIFrame();if(null===t||"undefined"===typeof t)return!1;var e={};return this.setTimeStamp(t.timeStamp),e.frameData=this.decoder.decode(t.buffer),e.timeStamp=t.timeStamp,e}},getFramerate:function(){return _},setFramerate:function(t){0<t&&"undefined"!==typeof t&&(_=t,null!==this.videoBufferList&&(this.videoBufferList.setMaxLength(6*_),this.videoBufferList.setBUFFERING(4*_)))},getTimeStamp:function(){return this.timeData},setTimeStamp:function(t){this.timeData=t}},new y};var d=function(){var t,e,r=!1;function i(){a.log("MJPEG Decoder")}return i.prototype={setIsFirstFrame:function(t){r=t},isFirstFrame:function(){return r},setResolution:function(r,a){t=r,e=a},decode:function(r,a){return i.prototype.isFirstFrame()?{data:r,width:t,height:e,codecType:"mjpeg"}:(i.prototype.setIsFirstFrame(!0),{firstFrame:!0})}},new i},_=function(){var t=0,e=0,r={frameData:null,timeStamp:null},n={timestamp:null,timezone:null};var o=0,s=0,u=0,f=0,l=null,c=null,p=null,m=0,_=0,h=0,g={width:0,height:0};function v(){this.decoder=new d,this.firstTime=0,this.lastMSW=0}return v.prototype={init:function(){this.decoder.setIsFirstFrame(!1),this.videoBufferList=new i,this.timeData=null},parseRTPData:function(i,p,m,d,v){var y=new Uint8Array(1048576);t=v.width,e=v.height,l={};var b=p[22],D=p.subarray(24+b,p.length-8);c=D.length;var T=(p[19]<<24)+(p[18]<<16)+(p[17]<<8)+p[16]>>>0,S=Date.UTC("20"+(T>>>26),(T>>>22&15)-1,T>>>17&31,T>>>12&31,T>>>6&63,63&T)/1e3;if(S-=28800,0==this.firstTime)this.firstTime=S,this.lastMSW=0,f=(p[21]<<8)+p[20],n={timestamp:this.firstTime,timestamp_usec:0};else{var w,C=(p[21]<<8)+p[20];w=C>f?C-f:C+65535-f,this.lastMSW+=w,S>this.firstTime&&(this.lastMSW-=1e3),this.firstTime=S,n={timestamp:S,timestamp_usec:this.lastMSW},f=C}0!==this.getFramerate()&&"undefined"!==typeof this.getFramerate()||"undefined"===typeof this.getTimeStamp()||this.setFramerate(Math.round(1e3/((n.timestamp-this.getTimeStamp().timestamp===0?0:1e3)+(n.timestamp_usec-this.getTimeStamp().timestamp_usec)))),this.setTimeStamp(n),u=(p[21]<<8)+p[20],s=c,(y=this.appendBuffer(y,D,o))[(o+=c)+s-2]=255,y[o+s-1]=217,g.width==t&&g.height==e||(0!=g.width?(g.width=t,g.height=e,l.resolution=g,l.resolution.decodeMode="canvas",l.resolution.encodeMode="mjpeg"):(g.width=t,g.height=e,l.decodeStart=g,l.decodeStart.decodeMode="canvas",l.decodeStart.encodeMode="mjpeg"));var I=1e3*n.timestamp+n.timestamp_usec;0==this.firstDiffTime?(_=0,this.firstDiffTime=Date.now()-I,a.log("firstDiff: "+this.firstTime)):(I-h<0&&(this.firstDiffTime=_+(Date.now()-I).toFixed(0)),(_=Date.now()-I-this.firstDiffTime)<0&&(this.firstDiffTime=0,_=0),_>8e3&&(l.error={errorCode:101},this.rtpReturnCallback(l))),h=I,r.frameData=null,this.decoder.setResolution(t,e),r.frameData=this.decoder.decode(y.subarray(0,o)),r.timeStamp=null,n=null===n.timestamp?this.getTimeStamp():n,r.timeStamp=n,!0===m&&(l.backupData={stream:y.subarray(0,o),width:t,height:e,codecType:"mjpeg"},null!==n.timestamp&&"undefined"!==typeof n.timestamp?l.backupData.timestamp_usec=n.timestamp_usec:l.backupData.timestamp=(u/90).toFixed(0)),o=0,r.playback=!1,l.decodedData=r,this.rtpReturnCallback(l)},getVideoBuffer:function(t){if(null!==this.videoBufferList)return this.videoBufferList.searchNodeAt(t)},clearBuffer:function(){null!==this.videoBufferList&&this.videoBufferList.clear()},findCurrent:function(){null!==this.videoBufferList&&this.videoBufferList.searchTimestamp(this.getTimeStamp())},findIFrame:function(){null!==this.videoBufferList&&this.videoBufferList.findIFrame()},SetRtpInterlevedID:function(t){this.interleavedID=t},setTimeStamp:function(t){this.timeData=t},getTimeStamp:function(){return this.timeData},getRTPPacket:function(t,e){},calculatePacketTime:function(t){},ntohl:function(t){return(t[0]<<24)+(t[1]<<16)+(t[2]<<8)+t[3]>>>0},appendBuffer:function(t,e,r){if(r+e.length>=t.length){var a=new Uint8Array(t.length+1048576);a.set(t,0),t=a}return t.set(e,r),t},setFramerate:function(t){0<t&&"undefined"!==typeof t&&(m=t,null!==this.videoBufferList&&(this.videoBufferList.setMaxLength(6*m),this.videoBufferList.setBUFFERING(4*m)))},getFramerate:function(){return m},setReturnCallback:function(t){this.rtpReturnCallback=t},setBufferfullCallback:function(t){null!==this.videoBufferList&&this.videoBufferList.setBufferFullCallback(t)},setGovLength:function(t){p=t},getGovLength:function(){return p},setDecodingTime:function(t){this.decodingTime=t},getDropPercent:function(){return 0},getDropCount:function(){return 0},initStartTime:function(){this.firstDiffTime=0,this.calcGov=0},setCheckDelay:function(t){this.checkDelay=t}},new v},h=function(){var t,e=null,r={1:"VideoSynopsis",2:"TrafficGate",3:"ElectronicPolice",4:"SinglePtzParking",5:"PtzParking",6:"Traffic",7:"Normal",8:"Prison",9:"ATM",10:"MetroIVS",11:"FaceDetection",12:"FaceRecognition",13:"NumberStat",14:"HeatMap",15:"VideoDiagnosis",16:"VideoEnhance",17:"SmokeFireDetect",18:"VehicleAnalyse",19:"PersonFeature",20:"SDFaceDetect",21:"HeatMapPlan",22:"ATMFD",23:"SCR",24:"NumberStatPlan",25:"CourseRecord",26:"Highway",27:"City",28:"LeTrack",29:"ObjectStruct",30:"Stereo",31:"StereoPc",32:"HumanDetect",33:"SDPedestrain",34:"FaceAnalysis",35:"FaceAttribute",36:"FacePicAnalyse",37:"SDEP",38:"XRayDetect",39:"ObjectDetect",40:"CrowdDistriMap",41:"StereoBehavior"};function i(){this.firstTime=0,this.lastMSW=0}function n(t){for(var e=[].slice.call(t),r="",a=0;a<e.length;a++)r+=String.fromCharCode(e[a]);return decodeURIComponent(escape(r))}function o(t){var e={result:!0,type:0};return e.params=JSON.parse(n(t)),e}function s(t){var e={result:!1},a=0,i=(t[a+1]<<8)+t[a];if(1!==i&&2!==i)return e;e.result=!0,e.type=5,e.params=null;var n=t[a+=2];if(0===n)return e;var o=t[a+=1];a+=1,e.params={},e.params.coordinate=128&o?8192:1024,e.params.isTrack=!!(127&o),e.params.object=[];for(var s=0;s<n;s++){var u={};u.objectId=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a],a+=4,u.operateType=t[a];var f=t[a+=1];a+=1,u.objectType=t[a];var l=t[a+=1];a+=1,a+=1,a+=1,u.classID=r[t[a]],a+=1,u.subType=t[a],a+=1,l>0&&(u.fatherId=[]);for(var c=0;c<l;c++)u.fatherId.push((t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a]),a+=4;f>0&&(u.track=[]);for(var p=0;p<f;p++){var m=(t[a+1]<<8)+t[a],d=(t[(a+=2)+1]<<8)+t[a],_=(t[(a+=2)+1]<<8)+t[a],h=(t[(a+=2)+1]<<8)+t[a];a+=2,u.track.push([m-_,d-h,m+_,d+h])}e.params.object.push(u)}return e}function u(t){for(var e={result:!1,type:20,params:[]},r=(t[0],t.length),a=0,i=t.slice(4),o=function(){var t={};t.objectId=(i[a+3]<<24)+(i[a+2]<<16)+(i[a+1]<<8)+(i[a+0]<<0),a+=4,t.result=!0,t.params={},t.custom=(i[a+1]<<8)+i[a],a+=2,t.objectStatus=i[a];var e=i[a+=1];a+=1,t.params.object=[];for(var r=null,o=0;o<e;o++){switch(i[a]){case 1:r=c(i.slice(a));break;case 2:r=l(i.slice(a));break;case 3:r=f(i.slice(a));break;case 4:r=p(i.slice(a))}t.params.object.push(r.info),a+=r.offset}1!=t.objectStatus&&3!=t.objectStatus||(t.params=null),0==e&&(t.params=null);var s=(i[a+1]<<8)+i[a];a+=2;var u=n(i.slice(a,a+s));return t.appendInfo=String.fromCharCode.apply(null,i.slice(a,a+s)),a+=s,t.appendInfo=u,t};a<r-4;)e.params.push(o());return e}function f(t){var e=0,r={type:t[0]};e+=1,r.pointCount=t[e],e+=1,r.lineWidth=t[e],e+=1,r.strokeStyle=t[e],e+=1,r.borderColor=[t[e+1],t[e+2],t[e+3],t[e]],r.borderColorType="RGBA",e+=4,r.fillColor=[t[e+1],t[e+2],t[e+3],t[e]],r.fillColorType="RGBA",e+=4,r.coordinate=[];for(var a=0;a<r.pointCount;a++){var i=(t[e+1]<<8)+t[e],n=(t[(e+=2)+1]<<8)+t[e];e+=2,r.coordinate.push([i,n])}return{info:r,offset:e}}function l(t){var e=0,r={type:t[0]};e+=1,r.pointCount=t[e],e+=1,r.lineWidth=t[e],e+=1,r.strokeStyle=t[e],e+=1,r.lineColor=[t[e+1],t[e+2],t[e+3],t[e]],r.lineColorType="RGBA",e+=4,r.coordinate=[];for(var a=0;a<r.pointCount;a++){var i=(t[e+1]<<8)+t[e],n=(t[(e+=2)+1]<<8)+t[e];e+=2,r.coordinate.push([i,n])}return{info:r,offset:e}}function c(t){var e=0,r={type:t[0]};e+=1,r.lineWidth=t[e],e+=1,r.strokeStyle=t[e],e+=1,e+=1,r.radius=(t[e+1]<<8)+t[e],e+=2;var a=(t[(e+=2)+1]<<8)+t[e],i=(t[(e+=2)+1]<<8)+t[e];return e+=2,r.coordinate=[a,i],r.borderColor=[t[e+1],t[e+2],t[e+3],t[e]],r.borderColorType="RGBA",e+=4,r.fillColor=[t[e+1],t[e+2],t[e+3],t[e]],r.fillColorType="RGBA",{info:r,offset:e+=4}}function p(t){var e=0,r={type:t[0]};e+=1,r.encodeType=t[e],e+=1;var a=(t[(e+=2)+1]<<8)+t[e],i=(t[(e+=2)+1]<<8)+t[e];return e+=2,r.coordinate=[a,i],r.fontColor=[t[e+1],t[e+2],t[e+3],t[e]],e+=4,r.colorType="RGBA",r.fontSize=t[e],e+=1,r.textAlign=t[e],e+=1,r.textLength=(t[e+1]<<8)+t[e],e+=2,r.content=String.fromCharCode.apply(null,t.slice(e,e+r.textLength)),{info:r,offset:e+=r.textLength}}function m(t,e){e.hasOwnProperty("attribute80")||(e.attribute80=[]);var r=1,a=t[r];r+=1;var i={color:{}};i.color.valid=t[r],r+=1,i.carModel=t[r],r+=1,i.color.red=t[r],r+=1,i.color.green=t[r],r+=1,i.color.blue=t[r],r+=1,i.color.alpha=t[r],r+=1,i.brand=(t[r+1]<<8)+t[r],r+=2,i.subBrand=(t[r+1]<<8)+t[r],r+=2,i.year=(t[r+1]<<8)+t[r],r+=2,i.reliability=t[r],r+=1;var n=(t[(r+=1)+1]<<8)+t[r],o=(t[(r+=2)+1]<<8)+t[r],s=(t[(r+=2)+1]<<8)+t[r],u=(t[(r+=2)+1]<<8)+t[r];return r+=2,i.windowPosition=[n-s,o-u,n+s,o+u],e.attribute80.push(i),a}function d(t,e){e.hasOwnProperty("attribute81")||(e.attribute81=[]);var r={},a=1,i=t[a],n=(t[(a+=1)+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a];return a+=2,r.mainPosition=[n-s,o-u,n+s,o+u],n=(t[a+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],a+=2,r.coPosition=[n-s,o-u,n+s,o+u],r.mainSafetyBelt=t[a]>>2&3,r.coSafetyBelt=3&t[a],a+=1,r.mainSunvisor=t[a]>>2&3,r.coSunvisor=3&t[a],a+=1,e.attribute81.push(r),i}function _(t,e){e.hasOwnProperty("attribute82")||(e.attribute82=[]);var r={},a=1,i=t[a];return a+=1,r.plateEncode=t[a],a+=1,r.plateInfoLen=t[a],a+=1,r.plateInfo=t.subarray(a,a+r.plateInfoLen),e.attribute82.push(r),i}function h(t,e){e.hasOwnProperty("attribute83")||(e.attribute83=[]);var r={},a=1,i=t[a];return a+=1,r.color={},r.color.valid=t[a],a+=1,r.color.red=t[a],a+=1,r.color.green=t[a],a+=1,r.color.blue=t[a],a+=1,r.color.alpha=t[a],a+=1,r.country=String.fromCharCode.apply(null,t.subarray(a,a+4)),a+=4,r.plateType=(t[a+1]<<8)+t[a],a+=2,a+=1,r.plateWidth=(t[a+1]<<8)+t[a],e.attribute83.push(r),i}function g(t,e){e.hasOwnProperty("attribute84")||(e.attribute84=[]);var r={},a=1,i=t[a];a+=1,r.fatherCount=t[a],a+=1,r.trackCount=t[a],a+=1,r.trackType=t[a],a+=1,a+=3,r.fatherCount>0&&(r.fatherID=[]);for(var n=0;n<r.fatherCount;n++)r.fatherID.push((t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a]),a+=4;r.trackCount>0&&(r.track=[]);for(var o=0;o<r.trackCount;o++){var s=(t[a+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],f=(t[(a+=2)+1]<<8)+t[a],l=(t[(a+=2)+1]<<8)+t[a];a+=2,r.track.push([s-f,u-l,s+f,u+l])}return e.attribute84.push(r),i}function v(t,e){e.hasOwnProperty("attribute85")||(e.attribute85=[]);var r={},a=1,i=t[a];a+=1,r.colorSpace=t[a],a+=1,r.mainColorCount=t[a],a+=1,r.mainColorCount>0&&(r.mainColorInfo=[]);for(var n=0;n<r.mainColorCount;n++){var o={},s=(t[a+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],f=(t[(a+=2)+1]<<8)+t[a],l=(t[(a+=2)+1]<<8)+t[a];a+=2,o.rect=[s-f,u-l,s+f,u+l],o.color=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a],a+=4,r.mainColorInfo.push(o)}return e.attribute85.push(r),i}function y(t,e){e.hasOwnProperty("attribute86")||(e.attribute86=[]);var r={},a=1,i=t[a];return a+=1,a+=1,r.speedType=t[a],a+=1,r.speed=t[a+1]<<8+t[a],a+=2,r.speedX=t[a+1]<<8+t[a],a+=2,r.speedY=(t[a+1]<<8)+t[a],e.attribute86.push(r),i}function b(t,e){e.hasOwnProperty("attribute87")||(e.attribute87=[]);var r={},a=1,i=t[a];a+=1;var n=(t[(a+=2)+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a];return r.track=[[n-s,o-u,n+s,o+u]],e.attribute87.push(r),i}function D(t,e){e.hasOwnProperty("attribute88")||(e.attribute88=[]);var r={},a=1;return a+=1,r.age=t[a],a+=1,r.sex=t[a],a+=1,r.face=t[a],a+=1,r.glass=t[a],a+=1,r.hat=t[a],a+=1,r.call=t[a],a+=1,r.backpack=t[a],a+=1,r.umbrella=t[a],a+=1,r.height=t[a],a+=1,r.mask=t[a]>>2&3,r.beard=3&t[a],e.attribute88.push(r),len}function T(t,e){e.hasOwnProperty("attribute89")||(e.attribute89=[]);var r={},a=1,i=t[a];a+=1,r.yawAngle=parseInt((t[a+1]<<8)+t[a]),a+=2,r.rollAngle=parseInt((t[a+1]<<8)+t[a]),a+=2,r.pitchAngle=parseInt((t[a+1]<<8)+t[a]);var n=(t[(a+=2)+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a];a+=2,r.lEyePos=[n,o],n=(t[a+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],a+=2,r.rEyePos=[n,o],n=(t[a+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],a+=2,r.nosePos=[n,o],n=(t[a+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],a+=2,r.lMouthPos=[n,o],n=(t[a+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],a+=2,r.rMouthPos=[n,o];var s=t[a];a+=3,s>0&&(r.featurePos=[]);for(var u=0;u<s;u++)n=(t[a+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],a+=2,r.featurePos.push([n,o]);return e.attribute89.push(r),i}function S(t,e){e.hasOwnProperty("attribute8C")||(e.attribute8C=[]);var r={},a=1,i=t[a];a+=1,r.hangingCount=t[a],a+=1,r.tissueCount=t[a],a+=1,r.sunVisorCount=t[a],a+=1,r.annualInspectionCount=t[a],a+=1,a+=6,r.hangingCount>0&&(r.hangingCount=[]);for(var n=0;n<r.hangingCount;n++){var o=(t[a+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],f=(t[(a+=2)+1]<<8)+t[a];a+=2,r.hangingPos.push([o-u,s-f,o+u,s+f])}for(r.tissueCount>0&&(r.tissueCount=[]),n=0;n<r.tissueCount;n++)o=(t[a+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],f=(t[(a+=2)+1]<<8)+t[a],a+=2,r.tissueCount.push([o-u,s-f,o+u,s+f]);for(r.sunVisorCount>0&&(r.sunVisorCount=[]),n=0;n<r.tissueCount;n++)o=(t[a+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],f=(t[(a+=2)+1]<<8)+t[a],a+=2,r.sunVisorCount.push([o-u,s-f,o+u,s+f]);for(r.annualInspectionCount>0&&(r.annualInspectionCount=[]),n=0;n<r.tissueCount;n++)o=(t[a+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a],f=(t[(a+=2)+1]<<8)+t[a],a+=2,r.annualInspectionCount.push([o-u,s-f,o+u,s+f]);return e.attribute8C.push(r),i}function w(t,e){e.hasOwnProperty("attribute8E")||(e.attribute8E=[]);var r={},a=1,i=t[a];a+=1,r.nameCodecFormat=t[a];var n=t[a+=1];return a+=1,r.name=String.fromCharCode.apply(null,t.subarray(a,n)),e.attribute8E.push(r),i}function C(t,e){for(var r={128:m,129:d,130:_,131:h,132:g,133:v,134:y,135:b,136:D,137:T,140:S,142:w},a=0,i=t[a];a<t.length;){var n=t.subarray(a,t.length);a+=r[i].call(null,n,e)}}function I(t,e){e.hasOwnProperty("vehicleObject")||(e.vehicleObject=[]);var r={},a=0;r.type=t[a],a+=1;var i=(t[(a+=1)+1]<<8)+t[a];a+=2,r.objectId=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a];var n=(t[(a+=4)+1]<<8)+t[a],o=(t[(a+=2)+1]<<8)+t[a],s=(t[(a+=2)+1]<<8)+t[a],u=(t[(a+=2)+1]<<8)+t[a];return a+=2,r.track=[[n-s,o-u,n+s,o+u]],r.valid=t[a],a+=1,r.operateType=t[a],a+=1,a+=2,C(t.subarray(a,i),r),e.vehicleObject.push(r),i}function F(t,e){e.hasOwnProperty("faceObject")||(e.faceObject=[]);var r={},a=0;r.type=t[a],a+=1;var i=(t[(a+=1)+1]<<8)+t[a];return i<12?0:(a+=2,r.objectId=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a],a+=4,r.version=t[a],a+=1,a+=3,r.faceData=t.subarray(a,i),e.faceObject.push(r),i)}function M(t,e){e.hasOwnProperty("commonObject")||(e.commonObject=[]);var r={},a=0;r.type=t[a],a+=1;var i=(t[(a+=1)+1]<<8)+t[a];return a+=2,r.objectId=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a],a+=4,r.operateType=t[a],a+=1,a+=3,C(t.subarray(a,i),r),e.commonObject.push(r),i}function k(t,e,r){var a=0,i=(t[a+3]<<24)+(t[a+2]<<16)+(t[a+1]<<8)+t[a],n=t[a+=4];if(a+=1,a+=3,0==n)return a;e.groupId=i,e.object={};for(var o=0;o<n;o++){var s=t[a],u=t.subarray(a,t.length),f=0;switch(s){case 2:case 5:f=I(u,e.object);break;case 15:f=F(u,e.object);break;default:f=M(u,e.object)}if(0==f)return 0;a+=f}return r(e),a}function x(t,e,r){var a={coordinate:8192};if(e.length<32)return!1;var i=4;a.classID=t;var n=e[i];if(0==n)return!0;a.groupCount=n,i+=1,i+=7,a.cameral=[];for(var o=0;o<20;o++)a.cameral.push(e[i+o]);i+=20;for(var s=0;s<a.groupCount;s++){var u=k(e.subarray(i,e.length),JSON.parse(JSON.stringify(a)),r);if(u<=0)break;i+=u}}return i.prototype={init:function(){a.log("init")},parseRTPData:function(a,i,n,f,l,c){var p=(i[19]<<24)+(i[18]<<16)+(i[17]<<8)+i[16]>>>0,m=Date.UTC("20"+(p>>26),(p>>22&15)-1,p>>17&31,p>>12&31,p>>6&63,63&p)/1e3;if(m-=28800,0==this.firstTime)this.firstTime=m,this.lastMSW=0,e=(i[21]<<8)+i[20],t={timestamp:this.firstTime,timestamp_usec:0};else{var d,_=(i[21]<<8)+i[20];d=_>=e?_-e:_+65535-e,this.lastMSW+=d,m>this.firstTime&&(this.lastMSW-=1e3),this.firstTime=m,t={timestamp:m,timestamp_usec:this.lastMSW},e=_}!function(e,a,i,n){var f=a[22],l=a.subarray(24+f,a.length-8);switch(e){case 0:i({ivsDraw:o(l),timeStamp:t,channel:n});break;case 5:i({ivsDraw:s(l),timeStamp:t,channel:n});break;case 6:break;case 14:var c=[];if(function(t,e){for(var a=t.length,i=0;i+4<a;){var n=t[i],o=(t[i+1],(t[i+3]<<8)+t[i+2]),s=t.subarray(i,o);if(i+=o,161!==n&&!x(r[n-64],s,e))break}}(l,function(t){c.push(t)}),c.length){var p={result:!1,type:14,params:c};i({ivsDraw:p,timeStamp:t,channel:n})}break;case 20:i({ivsDraw:u(l),timeStamp:t,channel:n})}}(i[5],i,this.rtpReturnCallback,c)},setBufferfullCallback:function(){},setReturnCallback:function(t){this.rtpReturnCallback=t}},new i};({isWasm:!1}).isWasm?importScripts("./ffmpegwasm.js"):importScripts("./ffmpegasm.js"),addEventListener("message",function(t){var e=t.data;switch(M=t.data.channelId,e.type){case"sdpInfo":v=e.data,0,function(t){g=[],T=!1;for(var e=0;e<t.sdpInfo.length;e++)y=null,b=t.decodeMode,"H264"===t.sdpInfo[e].codecName||"RAW"===t.sdpInfo[e].codecName&&t.mp4Codec&&"H264"===t.mp4Codec.VideoCodec?(null===w&&(w=l()),(y=w).init(t.decodeMode),y.setFramerate(t.sdpInfo[e].Framerate),y.setGovLength(t.govLength),y.setCheckDelay(t.checkDelay),y.setLessRate(t.lessRateCanvas)):"H265"===t.sdpInfo[e].codecName||"RAW"===t.sdpInfo[e].codecName&&t.mp4Codec&&"H265"===t.mp4Codec.VideoCodec?(null===C&&(C=m()),(y=C).init(),y.setFramerate(t.sdpInfo[e].Framerate),y.setGovLength(t.govLength),y.setCheckDelay(t.checkDelay)):"JPEG"===t.sdpInfo[e].codecName?(null===I&&(I=_()),(y=I).init(),y.setFramerate(t.sdpInfo[e].Framerate)):"stream-assist-frame"===t.sdpInfo[e].codecName&&(a.log(t.sdpInfo[e]),null===F&&(F=h()),(y=F).init()),"undefined"!==typeof t.sdpInfo[e].Framerate&&t.sdpInfo[e].Framerate,null!==y&&(y.setBufferfullCallback(x),y.setReturnCallback(A),S=t.sdpInfo[e].RtpInterlevedID,g[S]=y)}(v);break;case"MediaData":if(!0===T){!function(t){S=t.data.rtspInterleave[1],"undefined"!==typeof g[S]&&g[S].bufferingRtpData(t.data.rtspInterleave,t.data.header,t.data.payload)}(e);break}S=e.data.rtspInterleave[1],"undefined"!==typeof g[S]&&g[S].parseRTPData(e.data.rtspInterleave,e.data.payload,D,k,e.info,e.channel);break;case"initStartTime":g[S].initStartTime();break;case"end":L("end")}},!1),Module.onRuntimeInitialized=function(){Module._RegisterAll(),L("WorkerReady")};var g=[],v=null,y=null,b="",D=!1,T=!1,S=-1,w=null,C=null,I=null,F=null,M=null,k=1;function x(){g[S].findCurrent(),L("stepPlay","BufferFull")}function A(t){var e=null;if(null===t||"undefined"===typeof t)return e=null,void null;if("undefined"!==typeof t.error?(L("error",t.error),e=t.decodedData):(e=t.decodedData,null!==t.decodeMode&&"undefined"!==typeof t.decodeMode&&(b=t.decodeMode,L("setVideoTagMode",t.decodeMode))),null!=t.decodeStart&&(L("DecodeStart",t.decodeStart),b=t.decodeStart.decodeMode),null!==e&&"undefined"!==typeof e)if(void 0!==e.frameData&&null!==e.frameData&&"canvas"===b){!0===e.frameData.firstFrame&&L("firstFrame",e.frameData.firstFrame);var r={bufferIdx:e.frameData.bufferIdx,width:e.frameData.width,height:e.frameData.height,codecType:e.frameData.codecType,frameType:e.frameData.frameType,timeStamp:null,frameIndex:e.frameData.frameIndex};null!==e.timeStamp&&"undefined"!==typeof e.timeStamp&&(r.timeStamp=e.timeStamp),L("videoInfo",r),"undefined"!==typeof e.frameData.data&&null!==e.frameData.data&&L("canvasRender",e.frameData.data,e.frameData.option)}else if(null!==e.frameData&&"video"===b){null!==e.initSegmentData&&(L("codecInfo",e.codecInfo),L("initSegment",e.initSegmentData));r={codecType:e.frameData.codecType,frameIndex:e.frameData.frameIndex};"undefined"!==typeof e.frameData.width&&(r.width=e.frameData.width,r.height=e.frameData.height),L("videoInfo",r),L("videoTimeStamp",e.timeStamp),e.frameData.length>0&&(L("mediaSample",e.mediaSample),L("videoRender",e.frameData))}else L("drop",t.decodedData);null!=t.resolution&&L("MSEResolutionChanged",t.resolution),null!=t.ivsDraw&&L("ivsDraw",t)}function L(t,e,r){var a={type:t,data:e,channelId:M,option:r};"canvasRender"===t?postMessage(a,[e.buffer]):postMessage(a)}}]);