<template>
  <div>
    <el-dialog title="编辑预案信息"
      :visible.sync="show"
      width="1200px"
      @close="closeBoolean()"
      top="10vh"
      :close-on-click-modal="false"
      v-dialog-drag>
      <div class="flex-full planTable">
        <div class="flex-full list-contain">
          <div class="flex-header">
            <el-radio-group v-model="checkPage">
              <el-radio-button label="1">基本信息</el-radio-button>
              <el-radio-button label="2" :disabled="defaultdisabled">预案内容</el-radio-button>
            </el-radio-group>
            <div>
              <!-- <el-button @click="goback" icon="el-icon-arrow-left">返回</el-button> -->
            </div>
          </div>
          <div class="flex-full"
            style="margin-top: 10px;max-height: 60vh;overflow: auto;"
            v-if="checkPage == '1'">
            <!-- <iams-form ref="simpleTable" v-model="formdata" :mapinfo="formdata.mapInfo" :gisshow="true"
            @attachdata="getUplaodData"></iams-form> -->
            <div class="table_box">
              <el-scrollbar>
                <el-form label-position="right"
                  :model="listParams"
                  :rules="rules"
                  ref="ruleForm"
                  label-width="120px"
                  class="demo-ruleForm">
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="planName"
                                    label="预案名称:">
                        <el-input v-model.trim="listParams.planName" maxlength="20"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="planVersion"
                                    label="预案文号:">
                        <el-input v-model.trim="listParams.planVersion"
                                  maxlength="10"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="planTypeCode"
                                    label="预案类型:">
                        <!-- <el-cascader placeholder="请选择预案类型"
                          v-model="listParams.planTypeCode"
                          :options="planTypeData">
                        </el-cascader> -->
                        <el-col :span="listParams.planTypeSuperCode!='65000'&&listParams.planTypeSuperCode!='69000'?12:24"
                                style="padding-right: 3px">
                          <el-select v-on:change="indexSelect"
                                     v-model="listParams.planTypeSuperCode">
                            <el-option v-for="options in planTypeSuperList"
                                       :label="options.label"
                                       :value="options.id"
                                       :key="options.id"></el-option>
                          </el-select>
                        </el-col>
                        <el-col :span="listParams.planTypeSuperCode!='65000'&&listParams.planTypeSuperCode!='69000'?12:0">
                          <el-select v-model="listParams.planTypeCode">
                            <el-option v-for="options in planTypeList"
                            :label="options.label"
                            :value="options.id"
                            :key="options.id"></el-option>
                          </el-select>
                        </el-col>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="planLevel"
                                    label="预案级别:">
                        <el-select v-model="listParams.planLevel">
                          <el-option v-for="options in planLevelList"
                          :label="options.name"
                          :value="options.value"
                          :key="options.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="establishOrgCode" label="编制单位:">
                        <el-input v-model.trim="listParams.establishOrgName" v-if="roleInfo.user_type === 'ent'" disabled></el-input>
                        <el-cascader v-else placeholder="请选择编制单位" v-model="listParams.establishOrgCode"
                          :options="establishOrgCodeData"
                          @change="handleChangeEstablishOrgCode"
                          :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                        </el-cascader>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="publishOrgCode" label="发布单位:">
                        <el-input v-model.trim="listParams.publishOrgName" v-if="roleInfo.user_type === 'ent'" disabled></el-input>
                        <el-cascader v-else placeholder="请选择发布单位"
                          v-model="listParams.publishOrgCode"
                          :options="establishOrgCodeData"
                          @change="handleChangePublishOrgCode" 
                          :props="{ disabled: 'virtualNode', value: 'id', checkStrictly: true }">
                        </el-cascader>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="eventTypeCode" label="事件类型:">
                        <el-cascader placeholder="请选择事件类型"
                          v-model="listParams.eventTypeCode"
                          size="medium"
                          :options="eventTypeCodeData"
                          @change="handleChangeEventTypeCode">
                        </el-cascader>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="classCode"
                                    label="预案密级:">
                        <el-select v-model="listParams.classCode">
                          <el-option v-for="options in classCodeList"
                                     :label="options.name"
                                     :value="options.value"
                                     :key="options.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="contactName"
                                    label="联系人:">
                        <el-input v-model.trim="listParams.contactName"
                                  maxlength="16"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="contactPhone"
                                    label="联系电话:">
                        <el-input v-model.trim="listParams.contactPhone"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item prop="publishTime"
                                    label="发布日期:">
                        <el-date-picker type='date'
                                        v-model="listParams.publishTime"
                                        :editable="false"
                                        style="width: 100%"
                                        placeholder="请选择日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item prop="endTime"
                                    label="下次修订日期:">
                        <el-date-picker type='date'
                                        v-model="listParams.endTime"
                                        :editable="false"
                                        style="width: 100%"
                                        placeholder="请选择日期"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"></el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="12">
                      <el-form-item label="涉及危化品:">
                        <el-select v-model.trim="listParams.msdsId"
                                   filterable
                                   placeholder="请输入涉及危化品"
                                   remote
                                   clearable
                                   @change="currentSel"
                                   reserve-keyword
                                   :remote-method="Tolikesearch">
                          <el-option v-for="item in msdsTitleList"
                                     :key="item.msdsid"
                                     :label="item.msdstitle"
                                     :value="item.msdsid">
                          </el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item prop="attachmentList"
                                    label="附件:">
                        <!-- <iams-upload ref="upload" v-model="listParams.attachmentList" :limit=5>
                            </iams-upload> -->
                        <AttachmentUpload :attachmentlist="listParams.attachmentList"
                                          :limit="1"
                                          type="office"
                                          v-bind="{}"></AttachmentUpload>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row>
                    <el-col :span="24">
                      <el-form-item style="margin-left: 0;text-align: center;">
                        <el-button v-show="checkPage==1"
                                   type="primary"
                                   icon="el-icon-check"
                                   @click.native="submit">保存</el-button>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-scrollbar>
            </div>
          </div>
          <div class="flex-full planContentTable"
               style="margin-top: 10px;max-height: 60vh;overflow: auto;"
               v-if="checkPage == '2'">
            <div class="flex-full" style="max-height: 60vh;overflow: auto;">
              <div class="plan-btn">
                <!--<el-button class="el-icon-plus" type="success" @click="addChapters">新增章节</el-button>-->
                <el-button @click="lookAll"
                           icon="el-icon-arrow-left"
                           style="position: absolute;right: 20px;top: 85px;">查看全部章节</el-button>
                <!--<el-button class="el-icon-arrow-left " @click="go('/planManagement/planList')">返回</el-button>-->
                <el-button type="primary"
                           class="el-icon-check"
                           style="position: absolute;right: 180px;top: 85px;"
                           @click="planEditAdd(1)"
                           v-show="editcontentshow">保存
                </el-button>
                <!--<el-button type="primary" class="el-icon-check" @click="planNodeAdd" v-show="addcontentshow">保存-->
                <!--</el-button>-->
              </div>
              <el-row class="edit_plan" style="max-height: 60vh;overflow: auto;">
                <!-- 查看预案内容 -->
                <el-col :span="7"
                        class="edit_plan">
                  <div class="chapterModule">
                    <div class="operaFun">
                      <el-input v-model.trim="chapterName"
                                class="chapterNodeInput"
                                placeholder="可用章节标题定位"
                                @keyup.enter.native="searchHighLight"
                                clearable
                                maxlength="10"
                                show-word-limit>
                        <el-button slot="append"
                                   @click="searchHighLight"
                                   icon="el-icon-search"></el-button>
                      </el-input>
                      <el-button @click="addTree(0)"
                                 size="mini"
                                 type="success"
                                 plain
                                 class="chapterModuleBtn"
                                 style="margin-left: 10px;">新增章节</el-button>
                      <el-button @click="addTree(1)"
                                 size="mini"
                                 type="success"
                                 plain
                                 class="chapterModuleBtn">
                        添加子章节</el-button>
                      <el-button @click="editTree"
                                 size="mini"
                                 type="primary"
                                 plain
                                 class="chapterModuleBtn">编辑
                      </el-button>
                      <el-button @click="delectTree"
                                 size="mini"
                                 type="danger"
                                 plain
                                 class="chapterModuleBtn">删除
                      </el-button>
                      <!--<el-button @click="sortupTree" size="mini" type="primary" plain :class="temp.style.chapterModuleBtn">上移</el-button>-->
                      <!--<el-button @click="sortdownTree" size="mini" type="primary" plain :class="temp.style.chapterModuleBtn">下移</el-button>-->
                    </div>
                    <el-scrollbar style="height: 434px!important;">
                      <el-tree :data="nodeList"
                        :expand-on-click-node="false"
                        :props="defaultProps"
                        :empty-text="emptyText"
                        render-after-expand
                        @node-click="handleNodeClick"
                        :filter-node-method="searchHighLight"
                        highlight-current
                        :default-expanded-keys="expanded"
                        node-key="nodeId"
                        ref="chapterTree"
                        default-expand-all
                        class="chapterTree">
                        <span slot-scope="scope"
                          :class="scope.data.class">{{scope.node.label}}</span>
                      </el-tree>
                      <!--<search-tree listtype="getPlanTypeTree" @change="getOrg" :defaultchecked="defaultchecked" :configtree="configtree" placeholder="可用章节标题定位">-->
                      <!--</search-tree>-->
                    </el-scrollbar>
                  </div>
                </el-col>
                <el-col :span="17"
                  id="editPlan"
                  v-show="detailshow"
                  class="edit_plan"
                  ref="content"
                  style="margin-top: 10px;padding-left: 20px;">
                  <el-scrollbar style="height: 100%!important;">
                    <div v-for="(item,index) in nodeList"
                      :key="item.nodeId"
                      class="edit_plan_note"
                      v-show="nodeList.length>0">
                      <div class="chapter_1">
                        <h2>{{item.nodeName}}</h2>
                        <div class="chapter_content" v-html="item.content"></div>
                        <template v-if="item.childrenList && item.childrenList.length > 0">
                          <div class="chapter_2" v-for="child in item.childrenList" :key="child.nodeId">
                            <h3>{{child.nodeName}}</h3>
                            <div class="chapter_content" v-html="child.content"></div>
                            <template v-if="child.childrenList && child.childrenList.length > 0">
                              <div class="chapter_3" v-for="list in child.childrenList" :key="nodeId">
                                <h3>{{list.nodeName}}</h3>
                                <div class="chapter_content" v-html="list.content"></div>
                              </div>
                            </template>
                          </div>
                        </template>
                      </div>
                    </div>
                  </el-scrollbar>
                </el-col>
                <!-- 编辑 -->
                <el-col :span="16"
                        :offset=1
                        id="addChapter"
                        v-show="editcontentshow"
                        style="margin-left: 0;width: 68.5%;float:right">
                  <el-form :model="nodeParm"
                           :rules="rules"
                           ref="nodeParm"
                           label-width="0px">
                    <el-row>
                      <el-col :span=24>
                        <div class="search_filter">
                          <div class="toolbar_txt">
                            <el-page-header title="" content="章节内容"></el-page-header>
                          </div>
                        </div>
                        <!-- <el-form-item label="修改章节名称:" prop="nodeName">
                          <el-input v-model.trim="nodeParm.nodeName"  show-word-limit maxlength="20" required class="plan_node" ></el-input>
                          </el-form-item> -->
                        <!-- <el-form-item label="" prop="content"> -->
                        <div class="length-text">
                          <quill-editor v-model.trim="nodeParm.content"
                            ref="content"
                            :content="nodeParm.content"
                            :options="editorOption"
                            @change="onEditorChange($event)">
                          </quill-editor>
                          <div :class="showLength? 'redWordNumber':'wordNumber'">{{TiLength}}/10000</div>
                          <!--<div class="wordNumber" v-if="showLength">字符数已达上限</div>-->
                        </div>
                        <!-- </el-form-item> -->
                      </el-col>
                    </el-row>
                  </el-form>
                </el-col>
                <!-- 新增 -->
                <el-col :span="17"
                        :offset=1
                        v-show="addcontentshow">
                  <el-form :model="addplanContent"
                           :rules="rules"
                           ref="addplanContent"
                           label-width="180px">
                    <el-row>
                      <el-col :span=20>
                        <el-form-item label="新增章节名称:"
                                      prop="nodeName">
                          <el-input v-model.trim="addplanContent.nodeName"
                                    required
                                    show-word-limit
                                    maxlength="20"
                                    class="plan_node"></el-input>
                        </el-form-item>
                        <el-form-item label="新增章节内容:"
                                      prop="content">
                          <quill-editor v-model.trim="addplanContent.content"
                                        ref="content"
                                        :content="addplanContent.content"
                                        :options="editorOption"
                                        @change="onEditorChange($event)">
                          </quill-editor>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>

      </div>
    </el-dialog>
    <el-dialog width="50%"
               :title="dialogConfigGroup.tilteName"
               :visible.sync="dialogConfigGroup.viewDialog"
               v-if="dialogConfigGroup.viewDialog"
               :close-on-click-modal="false"
               :destroy-on-close="true">
      <el-row class="chapterNode">
        <el-col :span="19"
                :offset=1>
          <el-form label-width="180px">
            <el-row>
              <el-col :span=20>
                <el-form-item label="章节题目:"
                              prop="nodeName"
                              class="nodeNameTemp">
                  <span class="requiredLogo">*</span>
                  <el-input v-model.trim="nodeNameTemp"
                            class="chapterNode"
                            maxlength="25"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-col>
      </el-row>
      <div class="btn">
        <el-button @click.native="cancel"
                   type="warning"
                   icon="el-icon-close">关闭</el-button>
        <el-button @click.native="determineEdit"
                   type="primary"
                   icon="el-icon-check">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
import {
  getPlanType,
  getPlanList,
  getEstablishOrgCodeTreeData,
  getEventTypeCodData,
  getExpertTypeData,
  addPlanData,
  editPlanData,
  deletePlanData,
  getPlanTypeChild,
  getAllChapter,
  addChapter,
  viewChapterCont,
  deleteChapter,
  modifyChapter
} from '@/api/mergencyResources'
import { getDangerIdIsNotNullListData } from '@/api/equipmentAndFacilities'
import { detailPlanData } from '@/api/mergencyResources'
import iamsCombobox from '@/components/common/packages/iamsCombobox'
export default {
  //import引入的组件
  name: 'editPlan',
  components: {
    AttachmentUpload,
    iamsCombobox
  },
  props: ['rules', 'establishOrgCodeData', 'eventTypeCodeData', 'planTypeData', 'roleInfo'],
  data() {
    return {
      isAddorEdit:'',
      show: false,
      planTypeSuperList: [],
      planTypeList: [],
      planLevelList: [],
      classCodeList: [],
      checkPage: '1',
      submitShow: true,
      defaultdisabled: false,
      planId: '',
      nodeId: '',
      editcontentshow: false,
      addcontentshow: false,
      detailshow: true,
      stepData: [],
      editorContent: null,
      toolOptions: [
        ['bold', 'italic', 'underline', 'strike'],
        ['blockquote', 'code-block'],
        [{ header: 1 }, { header: 2 }],
        [{ list: 'ordered' }, { list: 'bullet' }],
        [{ script: 'sub' }, { script: 'super' }],
        [{ indent: '-1' }, { indent: '+1' }],
        [{ direction: 'rtl' }],
        [{ size: ['small', false, 'large', 'huge'] }],
        [{ header: [1, 2, 3, 4, 5, 6, false] }],
        [{ color: [] }, { background: [] }],
        [{ font: [] }],
        [{ align: [] }],
        ['clean'],
        ['image'] //上传图片、上传视频
      ],
      editorOption: {
        placeholder: '请输入文本...',
        modules: {
          toolbar: {
            container: this.toolOptions // 工具栏选项
          }
        }
      },
      showAll: true,
      planContent: {
        comments: '', //备用字段
        content: '', //预案内容
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        nodeId: '', //节点id
        nodeName: '', //节点名称
        planId: '', //预案id
        sort: 0, //排序
        updateBy: '', //更新人
        updateTime: '' //($date-time)更新时间
      },
      pickerOptionsToday: {},
      pickerOptions: {},
      addplanContent: {
        comments: '', //备用字段
        content: '', //预案内容
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        nodeId: '', //节点id
        nodeName: '', //节点名称
        planId: '', //预案id
        sort: 0, //排序
        updateBy: '', //更新人
        updateTime: '' //($date-time)更新时间
      },
      listParams: {
        attachmentList: [],
        checkStatus: '', //备案状态，建字典项（2不通过0未审核1审核通过）
        classCode: '', //预案密级，建字典项（CONFIDENTIAL、SECRET、LIMIT、OPEN、OTHE）
        comments: '', //备注
        contactName: '', //联系人
        contactPhone: '', //联系人电话
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        dutiesMeasures: '', //职责与措施
        endTime: '', //($date-time)适用结束时间
        establishOrgCode: '', //编制单位编码
        establishOrgName: '', //编制单位名称
        eventTypeCode: '', //事件类型
        eventTypeCodeList: [],
         eventTypeCodeListNew: [],
        notes: '', //编制或修订说明 （0,1）
        planCategory: '', //预案种类，预案大类GENERAL，SPECIAL，DEPARTMENT，COM
        planFlag: '1', //预案标志（0：备案，1：预案）
        planId: '', //预案id
        planLevel: '', //预案级别，COUNTRY、PROVINCE、CITY、ARER、INDUSTRY、COM
        planName: '', //预案名称
        planResponseValue: '', //可选响应级别(1、2、3、4级)，给默认值4级
        planSubCategory: '', //预案子类，GENERAL，SPECIAL，DEPARTMENT，COM
        planTypeCode: '', //预案类型
        planTypeSuperCode: '', //父节点备案类型
        planVersion: '', //预案版本
        publishOrgCode: '', //发布单位编码
        publishOrgName: '', //发布单位名称
        publishTime: '', //($date-time)发布时间
        reportOrgCode: '', //上报单位编码
        reportOrgName: '', //上报单位名称
        startTime: '', //($date-time)适用开始时间
        updateBy: '', //更新人
        updateTime: '', //($date-time)更新时间
        versionFlag: '', //发布还是修订(0发布,1修订)
        planStatus: '' //备案状态
      },
      flag: false,
      eventTypeCodeList: [],
      showLength: false,
      TiLength: 0,
      emptyText: '暂无数据',
      expanded: [1],
      parameter: {
        eventId: '',
        id: '',
        planId: '',
        planStatus: '',
        primaryFlag: '',
        resLevel: ''
      },
      nodeList: [],
      nodeOneList: [],
      defaultProps: {
        children: 'childrenList',
        label: 'nodeName'
      },
      chapterName: '',
      switchIsShow: true,
      currentChapterInfo: {}, // 当前选中节点内容
      currentNodeStructureInfo: {},
      dialogConfigGroup: {
        viewDialog: false, //弹框是否显示
        templateName: '', //弹框组件名
        tilteName: '添加章节信息' //标题头
      },
      nodeParm: {
        comments: '', //备用字段
        content: '', //预案内容
        createBy: '', //创建人
        createTime: '', //($date-time)创建时间
        nodeId: '', //节点id
        nodeName: '', //节点名称
        parentId: 0, // 上级节点id,没有传0
        planId: '', //预案id
        // sort: 0, //排序
        updateBy: '', //更新人
        updateTime: '' //($date-time)更新时间
      },
      isEdit: false,
      nodeNameTemp: '',
      msdsTitleList: []
    }
  },
  //方法集合
  methods: {
    getPlanId(val) {
      this.planId = val;
    },
    closeBoolean(val) {
      this.show = Boolean(val);
      this.$forceUpdate();
      console.log('+++++++++closeBoolean----->', this.rules);
    },
    //获取select option选中的值
    currentSel(selVal) {
      this.listParams.msdsId = selVal
      this.msdsTitleList.forEach(item => {
        if (item.msdsId === selVal) {
          this.listParams.msdsTitle = item.msdstitle
        }
      })
    },
    //搜索发起请求 传入值为当前select输入的值
    Tolikesearch(query) {
      getDangerIdIsNotNullListData({ msdstitle: query }).then(res => {
        if (res.data.status === 200) {
          console.log(res)
          this.msdsTitleList = res.data.data.list
        }
      })
    },
    handleChangeEstablishOrgCode(value) {
      if (value.length > 0) {
        this.listParams.establishOrgCode = value[value.length - 1]
      } else {
        this.listParams.establishOrgCode = ''
      }
    },
    handleChangeEventTypeCode(value) {
      if (value.length > 0) {
        this.listParams.eventTypeCode = value
      } else {
        this.listParams.eventTypeCode = ''
      }
    },
    handleChangePublishOrgCode(value) {
      if (value.length > 0) {
        this.listParams.publishOrgCode = value[value.length - 1]
      } else {
        this.listParams.publishOrgCode = ''
      }
    },
    getPublishhInfo(item) {
      this.listParams.publishOrgCode = item.label
    },
    indexSelect() {
      this.listParams.planTypeCode = ''
      if (this.listParams.planTypeSuperCode == '65000') {
        this.listParams.planTypeCode = '65000'
      } else if (this.listParams.planTypeSuperCode == '69000') {
        this.listParams.planTypeCode = '69000'
      }
      let parentCode = this.listParams.planTypeSuperCode
    },
    //保存方法
    submit() {
      let parms = this.listParams
      // 格式化时间
      if (
        this.listParams['endTime'] &&
        this.listParams['endTime'].length > 10
      ) {
        this.listParams['endTime'] = this.listParams['endTime'].slice(0, 10)
      }
      if (
        this.listParams['publishTime'] &&
        this.listParams['publishTime'].length > 10
      ) {
        this.listParams['publishTime'] = this.listParams['publishTime'].slice(
          0,
          10
        )
      }
      parms = this.listParams
      parms.createTime = ''
      parms.updateTime = ''
      this.$refs.ruleForm['validate'](valid => {
        if (valid) {
          editPlanData({
            ...parms,
             eventTypeCode:this.listParams.eventTypeCode[this.listParams.eventTypeCode.length-1],
            eventTypeCodeList:this.listParams.eventTypeCode,
          }).then(res => {
            if (res.data.status === 200) {
              this.$message.success(res.data.data || '操作成功!');
              this.$emit('updatePlan');
              this.closeBoolean(false);
            } else {
              this.$message.error(res.data.data || '编辑失败!');
            }
          })
        }
      })
    },
    // 获取附件列表
    getUplaodData(val) {
      this.$set(this.listParams, 'attachmentList', val)
    },
    // 企业端获取企业端orgCode
    getOrgCode() {
      // 应急这边企业进入也是要锁死编制单位
      // let enterprise = this.store.getters['plan/getEnterPrise'];
      // if (JSON.stringify(enterprise) !== '{}') {
      this.flag = true
      // }
    }, //获取预案类型
    getPlanTypeList() {
      getPlanType({}).then(res => {
        if (res.data.status == 200) {
          this.planTypeSuperList = res.data.data.treeData
        }
        // this.datas = this.data;
        this.planTypeSuperList.forEach(item => {
          item.value = item.id
          if (item.children && item.children.length > 0) {
            item.children.forEach(items => {
              items.value = items.id
            })
          }
        })
      })
    },
    //获取预案级别
    getPlanLevel() {
      getExpertTypeData({ dicCode: 'PLAN_LEVEL' }).then(res => {
        if (res.data.status == 200) {
          this.planLevelList = res.data.data
        }
      })
    },
    //获取预案密级列表
    getclassCode() {
      getExpertTypeData({ dicCode: 'PLAN_CLASS_CODE' }).then(res => {
        if (res.data.status == 200) {
          this.classCodeList = res.data.data
        }
      })
    },
    //   获取预案详情
    async planGetById(parms) {
      detailPlanData({
        planId: parms
      }).then(res => {
        if (res.data.status == 200) {
          this.eventTypeCodeList = res.data.data.eventTypeCodeList
          if (!Array.isArray(res.data.data.attachmentList)) {
            res.data.data.attachmentList = []
          }
          if (res.data.data.planTypeCode == '65000') {
            res.data.data.planTypeSuperCode = '65000'
          } else if (res.data.data.planTypeCode == '69000') {
            res.data.data.planTypeSuperCode = '69000'
          } else {
            let parentCode = res.data.data.planTypeSuperCode
            getPlanTypeChild({
              keyWord: '',
              parentCode: parentCode,
              nowPage: 1,
              pageSize: 100
            }).then(child => {
              if (child.status == 200) {
                this.planTypeList = child.data.data
              }
            })
          }
          this.listParams = res.data.data;
          this.listParams.eventTypeCode = res.data.data.eventTypeCodeList;
          this.Tolikesearch(this.listParams.msdsTitle)
        } else {
          this.$message.error('数据请求失败!')
        }
      })
    },
    // 预案内容的节点
    stepList(parms) {
      this.planId = parms
      // this.http.PlanRequest.planNodeListGet(parms).then((res) => {
      //     if (res.status == 200) {
      //         this.stepData = res.data;
      //     }
      // });
    },
    // 修改
    planEdit() {
      this.$refs.planContent['validate'](valid => {
        if (valid) {
          let parms = this.planContent
          parms.planId = this.planId
          // this.http.PlanRequest.planNodeEdit(parms).then((res) => {
          //     if (res.status == 200) {
          //         this.$message('操作成功');
          //         // this.planContent = new EmergencyPlanNodeAddDTO();
          //         this.stepList(this.planId);
          //     } else {
          //         this.$message('编辑失败');
          //     }
          // });
        }
      })
    },
    // 新增
    planNodeAdd() {
      /*if (
        this.addplanContent.nodeName.length < 1 ||
        this.addplanContent.content.length < 1
        ) {
        this.$message("章节名称或章节内容不能为空");
        return false;
        }*/
      this.$refs.addplanContent['validate'](valid => {
        if (valid) {
          let parms = this.addplanContent
          parms.planId = this.planId
          parms.sort = this.stepData.length
          parms.comments = ''
          // this.http.PlanRequest.planNodeAdd(parms).then((res) => {
          //     if (res.status == 200) {
          //         this.$message.success('操作成功');
          //         // this.addplanContent = new EmergencyPlanNodeAddDTO();
          //         this.stepList(this.planId);
          //     } else {
          //         this.$message(res.msg);
          //     }
          // });
        }
      })
    },
    // 点击新增章节
    addChapters() {
      this.addplanContent = {
        comments: '',
        content: '',
        createBy: '',
        createTime: '',
        nodeId: '',
        nodeName: '',
        planId: '',
        sort: 0,
        updateBy: '',
        updateTime: ''
      }
      this.addcontentshow = true
      this.editcontentshow = false
      this.detailshow = false
    },
    // 获取单个节点信息
    handleStepClick(name, sort, id, content) {
      this.editcontentshow = true
      this.addcontentshow = false
      this.detailshow = false
      this.$set(this.planContent, 'sort', sort)
      this.$set(this.planContent, 'nodeId', id)
      this.$set(this.planContent, 'nodeName', name)
      this.$set(this.planContent, 'content', content)
    },
    // 查看全部章节
    lookAll() {
      this.detailshow = true
      this.editcontentshow = false
      this.addcontentshow = false
      this.chapterName = ''
      // this.nodeParmInit();
      this.getEmergencyTree()
    },
    onEditorChange(event) {
      // console.log(this.$refs.content['quill'].getContents())
      // event.quill.deleteText(7000,1);
      if (this.nodeParm.content == '') {
        this.TiLength = 0
        this.showLength = false
      } else {
        this.TiLength = event.quill.container.innerText.length
        if (this.TiLength > 10000) {
          this.showLength = true
        } else {
          this.showLength = false
        }
      }
    },
    searchHighLight() {
      this.detailshow = true
      this.editcontentshow = false
      this.addcontentshow = false
    },
    // 节点列表查询
    getEmergencyTree() {
      getAllChapter({id: this.planId}).then(res => {
        if (res.data.status === 200) {
          this.nodeList = res.data.data;
          this.editcontentshow = false;
          this.currentChapterInfo = {};
        } else {
          this.$message.error(res.data.msg || '获取节点列表失败');
        }
      })
    },
    // 点击当前章节信息
    handleNodeClick(data, structureInfo) {
      console.log('点击当前章节信息----->', data, structureInfo)
      this.currentChapterInfo = data;
      this.currentChapterInfo.level = structureInfo.level
      viewChapterCont({planId: data.nodeId}).then(res => {
        if (res.data.status === 200) {
          // this.editcontentshow = true;
          this.detailshow = false;
          this.editcontentshow = true;
          this.nodeParm = res.data.data;
        } else {
          this.$message.warning(res.data.msg || '请求章节内容出错~');
        }
      })
    },
    nodeParmInit() {
      this.nodeParm.comments = '' //备用字段
      this.nodeParm.content = '' //预案内容
      this.nodeParm.createBy = '' //创建人
      this.nodeParm.createTime = '' //($date-time)创建时间
      this.nodeParm.nodeId = '' //节点id
      this.nodeParm.nodeName = '' //节点名称
      this.nodeParm.parentId = 0 // 上级节点id,没有传0
      this.nodeParm.planId = '' //预案id
      this.nodeParm.updateBy = '' //更新人
      this.nodeParm.updateTime = '' //($date-time)更新时间
    },
    // 添加节点\子节点
    addTree(type) {      
      this.isAddorEdit=1
      this.isEdit = false
      this.nodeNameTemp = ''
      if (type === 0) {
        this.dialogConfigGroup['tilteName'] = '添加章节信息'
        this.nodeParm.parentId = 0
        this.nodeParm.nodeId = ''
        this.dialogConfigGroup['viewDialog'] = true
      } else {
        if (this.currentChapterInfo && this.currentChapterInfo.nodeId) {
          if (this.currentChapterInfo.level === 3) {
            this.$message.warning('当前章节下无法继续添加子章节')
          } else {
            this.dialogConfigGroup['tilteName'] = '添加子章节信息'
            this.nodeParm.parentId = this.currentChapterInfo.nodeId
            this.nodeParm.nodeId = ''
            this.dialogConfigGroup['viewDialog'] = true
          }
        } else {
          this.$message.warning('请先选择父节点')
        }
      }
    },
    // 编辑节点
    editTree() {      
      this.isEdit = true
      this.isAddorEdit=0
      if (
        JSON.stringify(this.currentChapterInfo) === '{}' ||
        !this.currentChapterInfo
      ) {
        if (this.messageInfo) this.messageInfo.close()
        this.messageInfo = this.$message.warning('请选择章节')
      } else {
        this.nodeNameTemp = this.currentChapterInfo.nodeName
        this.nodeParm.parentId = this.currentChapterInfo.parentId
        // this.nodeParm.nodeName = this.currentChapterInfo.nodeName;
        this.dialogConfigGroup['tilteName'] = '编辑章节信息'
        this.dialogConfigGroup['viewDialog'] = true
      }
    },
    // 预案编辑保存
    planEditAdd() {    
      if (!this.showLength) {
        this.isEdit = true
        this.nodeNameTemp = this.currentChapterInfo.nodeName
        this.nodeParm.parentId = this.currentChapterInfo.parentId
        this.nodeParm.nodeName = this.currentChapterInfo.nodeName
        this.determine()
        this.detailshow = true
        this.editcontentshow = false
        this.addcontentshow = false
      } else {
        this.$message.error('章节内容超出最大字符数！')
      }
    },
    // 保存-新增章节题目
    determine() {
    if (this.nodeNameTemp === '') {
        this.$message.warning('请输入章节题目')
        return false
      }
      this.nodeParm.nodeName = this.nodeNameTemp
      let parms = this.nodeParm
      parms.planId = this.planId
      parms.sort = null
      if (this.isEdit) {
        parms.nodeId = this.currentChapterInfo.nodeId
      } else {
        parms.content = ''
      }      
      addChapter(parms).then(res => {
        if (res.data.status === 200) {
          this.$message.success(res.data.msg || '新增成功');
          this.dialogConfigGroup.viewDialog = false;
          this.getEmergencyTree();
          this.lookAll();
        } else {
          this.$message.error(res.data.msg || '新增失败');
        }
      })
    },
    determineEdit() {      
      console.log(this.isAddorEdit)
      if (this.nodeNameTemp === '') {
        this.$message.warning('请输入章节题目')
        return false
      }
      this.nodeParm.nodeName = this.nodeNameTemp
      let parms = this.nodeParm
      parms.planId = this.planId
      parms.sort = null
      if (this.isEdit) {
        parms.nodeId = this.currentChapterInfo.nodeId
      } else {
        parms.content = ''
      }  
      if(this.isAddorEdit==0){
         modifyChapter(parms).then(res => {
          if (res.data.status === 200) {
            this.$message.success(res.data.msg || '修改成功');
            this.dialogConfigGroup.viewDialog = false;
            this.getEmergencyTree();
            this.lookAll();
          } else {
            this.$message.error(res.data.msg || '修改失败');
          }
       })
      }else{
        addChapter(parms).then(res => {
        if (res.data.status === 200) {
          this.$message.success(res.data.msg || '新增成功');
          this.dialogConfigGroup.viewDialog = false;
          this.getEmergencyTree();
          this.lookAll();
        } else {
          this.$message.error(res.data.msg || '新增失败');
        }
      })
      }    
     
    },
    // 取消
    cancel() {
      this.dialogConfigGroup['viewDialog'] = false
      // this.lookAll();
      // this.currentChapterInfo = {};
    },
    // 删除
    delectTree() {
      if (
        JSON.stringify(this.currentChapterInfo) === '{}' ||
        !this.currentChapterInfo
      ) {
        if (this.messageInfo) this.messageInfo.close()
        this.messageInfo = this.$message.warning('请选择章节')
      } else {
        this.$confirm('是否确认删除', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
            deleteChapter({
              id: this.currentChapterInfo.nodeId,
              planId: this.currentChapterInfo.planId
            }).then(res => {
              if (res.data.status === 200) {
                this.$message.success(res.data.msg || '删除成功')
                this.getEmergencyTree();
                this.currentChapterInfo = {};
                this.lookAll();
              } else {
                this.$message.error(res.data.msg || '删除失败')
              }
            })
            // this.http.PlanRequest.planNodeDeleteById(id).then((res) => {
            //     if (res.status == 200) {
            //         this.currentChapterInfo = {};
            //         this.$message.success('操作成功');
            //         this.getEmergencyTree();
            //         this.lookAll();
            //     } else {
            //         this.currentChapterInfo = {};
            //         this.$message(res.msg);
            //     }
            // });
          })
          .catch(() => {
          })
      }
    },
    // 上移
    sortupTree() {
      if (
        JSON.stringify(this.currentChapterInfo) === '{}' ||
        !this.currentChapterInfo
      ) {
        if (this.messageInfo) this.messageInfo.close()
        this.messageInfo = this.$message.warning('请选择章节')
      } else {
        let parms = {
          nodeId: this.currentChapterInfo.nodeId,
          parentId: this.currentChapterInfo.parentId,
          planId: this.currentChapterInfo.planId,
          upOrDown: 0
        }
        // this.http.PlanRequest.getEmergencySort(parms).then((res) => {
        //     if (res.status == 200) {
        //         this.currentChapterInfo = {};
        //         this.$message.success('操作成功');
        //         this.getEmergencyTree();
        //         this.lookAll();
        //     } else {
        //         this.currentChapterInfo = {};
        //         this.$message(res.msg);
        //     }
        // });
      }
    },
    // 下移
    sortdownTree() {
      if (
        JSON.stringify(this.currentChapterInfo) === '{}' ||
        !this.currentChapterInfo
      ) {
        if (this.messageInfo) this.messageInfo.close()
        this.messageInfo = this.$message.warning('请选择章节')
      } else {
        let parms = {
          nodeId: this.currentChapterInfo.nodeId,
          parentId: this.currentChapterInfo.parentId,
          planId: this.currentChapterInfo.planId,
          upOrDown: 1
        }
        // this.http.PlanRequest.getEmergencySort(parms).then((res) => {
        //     if (res.status == 200) {
        //         this.currentChapterInfo = {};
        //         this.$message.success('操作成功');
        //         this.getEmergencyTree();
        //         this.lookAll();
        //     } else {
        //         this.currentChapterInfo = {};
        //         this.$message(res.msg);
        //     }
        // });
      }
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.getPlanType()
    this.getPlanLevel()
    this.getclassCode()
    // this.getEmergencyTree();
    // this.getOrgCode();
  }
}
</script>
<style lang="scss" scoped>
.edit_plan {
  // display: flex;
}
.edit_plan_note {
  margin-bottom: 2rem;

  > h2 {
    height: 3rem;
    line-height: 3rem;
  }
  > h3 {
    height: 3rem;
    line-height: 3rem;
  }
  > h4 {
    height: 3rem;
    line-height: 3rem;
  }

  > div {
    line-height: 1.5rem;
  }
}
.table_box {
  height: 95%;
  form {
    padding-right: 1rem;
  }
}
.recordCon {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 140px;
  overflow-y: auto;
  ul {
    display: flex;
    flex-wrap: wrap;
    li {
      display: inline;
      background-color: #f2f2f2;
      padding: 0px 10px;
      margin: 5px;
      border-radius: 4px;
      font-size: 12px;
      .recordConName {
        margin-right: 10px;
      }
      .close {
        cursor: pointer;
      }
      span {
        padding: 0;
      }
    }
  }
}
.filingList {
  display: flex;
  height: 550px;
  .filingListMain {
    width: calc(100% - 400px);
    .filingListMainScrollbar {
      height: 400px !important;
    }
    .search_input {
      width: 300px;
      right: 18rem;
      .el-input-group__append {
        padding: 0.5rem 1rem;
        margin-left: 0px;
      }
    }
    .btn {
      border-top: 1px solid #e4e4e4;
      padding-top: 20px;
      text-align: center;
      margin-top: 10px;
    }
  }
}
.chapterModule {
  border: 1px solid #e4e4e4;
  margin-top: 10px;
  .operaFun {
    border-bottom: 1px solid #e4e4e4;
    .chapterNodeInput {
      padding: 10px 15px;
      background-color: #f2f2f2;
      /deep/.el-input__suffix {
        right: 17px;
      }
    }
    .chapterModuleBtn {
      margin-right: -5px;
      margin-top: 10px;
      margin-bottom: 10px;
    }
  }
}
.btn {
  text-align: center;
}
.nodeNameTemp {
  position: relative;
  .requiredLogo {
    position: absolute;
    color: red;
    font-size: 1rem;
    left: -5rem;
    top: 0.1rem;
  }
}
.nodata {
  width: 6rem;
  position: absolute;
  top: 12rem;
  height: 6rem;
  // background-image: url(../../../assets/image/nodata.png);
  background-repeat: no-repeat;
  background-size: 100% 100%;
  left: 50%;
  transform: translate(-50%, -50%);
}
/deep/.el-page-header__left .el-icon-back {
  display: none!important;
}
/deep/.quill-editor {
  height: 460px;
}
.redWordNumber, .wordNumber {
  margin-top: 46px;
  text-align: right;
}
.edit_plan_note {
  .chapter_content {
    margin-left: 10px;
  }
  .chapter_2 {
    margin-left: 10px;
  }
  .chapter_3 {
    margin-left: 20px;
  }
}
</style>