<template>
  <el-dialog title="分享" :visible="visible" width="1260px" @close="closeBoolean" :destroy-on-close="true"
    :close-on-click-modal="false">
    <div class="share-container">
      <div class="menu-container">
        <div class="tab-title">通讯录</div>
        <div class="tree-container">
          <div class="list-search">
            <el-input v-model.trim="enterpName" size="mini" placeholder="请输入企业名称" class="input" clearable
              style="width: 140px; margin-left: 10px"></el-input>
            <el-button parentCode="primary" size="mini" @click="handleSearch">查询</el-button>
          </div>
          <div class="video-list" v-loading="loading">
            <a-directory-tree multiple show-checkbox default-expand-all @select="onSelect" @expand="onExpand"
              style="padding: 0 10px">
              <a-tree-node :key="item.id + ',' + item.parentCode" v-for="item in newAllData" :title="item.label">
                <a-tree-node v-if="item.children.length > 0" :key="subItem.id + ',' + subItem.parentCode"
                  v-for="subItem in item.children" :title="subItem.label">
                  <a-tree-node v-if="subItem.children.length > 0" :key="subItems.id + ',' + subItems.parentCode"
                    v-for="subItems in subItem.children" :title="subItems.label">
                    <a-tree-node v-if="subItems.children.length > 0" :key="subItemd.id + ',' + subItemd.parentCode"
                      v-for="subItemd in subItems.children" :title="subItemd.label">
                      <a-tree-node v-if="subItemd.children.length > 0" :key="subItemed.id + ',' + subItemed.parentCode"
                        v-for="subItemed in subItemd.children" :title="subItemed.label"></a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-tree-node>
              </a-tree-node>
            </a-directory-tree>
          </div>
        </div>
      </div>
      <div class="table-container">
        <div class="tab-title">
          人员信息
          <el-input placeholder="请输入内容" v-model="keyWord" size="mini" class="input" clearable style="width: 240px">
            <el-button slot="append" icon="el-icon-search"></el-button>
          </el-input>
        </div>
        <el-table :data="tableData" v-loading="tableLoading"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }" border height="100%" ref="multipleTable"
          @selection-change="tableSelect">
          <el-table-column type="selection" width="45" fixed="left" align="center">
          </el-table-column>
          <!-- <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column> -->
          <el-table-column prop="typeName" label="姓名" align="center">
          </el-table-column>
          <el-table-column prop="levelName" label="职务" align="center">
          </el-table-column>
          <el-table-column prop="levelName" label="单位" align="center">
          </el-table-column>
          <el-table-column prop="levelName" label="单位电话" align="center">
          </el-table-column>
          <el-table-column prop="levelName" label="手机号" align="center">
          </el-table-column>
        </el-table>
        <el-pagination @current-change="handleCurrentChange" :current-page.sync="searchData.nowPage"
          :page-size="searchData.pageSize" background class="pagination" size="mini" layout="total, prev, pager, next"
          :total="total" v-if="total != 0">
        </el-pagination>
      </div>
      <div class="checked-container">
        <div class="tab-title">
          已选择联系人
          <el-button size="mini" @click="closeAll">清空</el-button>
        </div>
        <div class="transfer-container">
          <el-tag v-for="(tag,index) in checkedList" class="tagItem" :key="tag.id" @close="closeChecked(tag,index)" closable>
            {{ tag.companyName }}
          </el-tag>
        </div>
      </div>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button type="primary" size="small" @click="handleSubmit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getAccidentListData, shareAccident } from "@/api/accidentManagement";
import { getMailListTreeData } from "@/api/mailList";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },

    accidentIds: {
      type: [String, Array],
      default: () => [],
    },
  },
  components: {},
  data() {
    return {
      activeName: "department",
      targetKeys: [],
      newAllData: [],
      enterpName: "",
      enterpId: "",
      keyWord: "",
      loading: false,
      tableLoading: false,
      transferDataSource: [],
      searchData: {
        orgCode: "",
        keyWord: "",
        nowPage: 1,
        pageSize: 6,
      },
      total: 0,
      tableData: [],
      checkedList: [], //选中的数据
      tableChecked : [],
    };
  },
  computed: {},
  created() {
    this.getOrgGroupList();
    // this.getDataList();
  },
  methods: {
    async handleSubmit() {
      await shareAccident({
        eventInfoId: this.accidentIds,
        userId: this.checkedList.map((item) => item.id), //企业id
      }).then((res) => {
        if (res.code == 200) {
          this.$message.success("分享成功");
          this.closeBoolean(false);
        }
      });
      this.closeBoolean(false);
    },
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    handleSearch() {
      this.getOnlineVideoDataList();
    },
    onSelect(selectedKeys, info) {
      this.enterpId = selectedKeys[0].split(",")[0];
      this.searchData.orgCode = this.enterpId;
      this.getDataList();
    },
    onExpand() { },
    tableSelect(val) {
       this.checkedList = val;
    },
    closeChecked(val,index) {
      this.$refs.multipleTable.toggleRowSelection(val, false);
      this.checkedList.splice(index,1)
      // this.$refs.multipleTable.setCurrentRow(row)
    },
    closeAll() {
      this.$refs.multipleTable.clearSelection();
    },
    handleCurrentChange(val) {
      this.searchData.nowPage = val;
      this.getDataList();
    },
    //视频运行列表
    getDataList() {
      const params = { ...this.searchData };
      this.tableLoading = true;
      getAccidentListData({
        districtcode: this.$store.state.login.userDistCode, //行政区划代码
        enterpname: this.enterpName,
        nowPage: params.nowPage,
        pageSize: params.pageSize,
      })
        .then((res) => {
          if (res.data.status == 200) {
            this.tableData = res.data.data.list;
            this.total = Number(res.data.data.total);
          }
        })
        .finally(() => {
          this.tableLoading = false;
        });
    },
    //机构树
    getOrgGroupList() {
      getMailListTreeData({
        type: 2,
      }).then((res) => {
        if (res.data.status == 200) {
          this.newAllData = res.data.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 15px;
}

.footer {
  text-align: center;
  padding-top: 20px;
}

.share-container {
  height: 450px;
  display: flex;
  box-sizing: border-box;

  .menu-container {
    width: 240px;
    height: 100%;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .tree-container {
      height: calc(100% - 30px);
      background: #daefff;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      .list-search {
        padding-top: 5px;
      }

      .video-list {
        flex: 1;
        padding: 10px 0;
        border-radius: 4px;
        overflow-y: auto;
        overflow-x: hidden;
        box-sizing: border-box;
      }
    }
  }

  .table-container {
    flex: 1;
    margin: 0 15px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .pagination {
      text-align: right;
    }
  }

  .checked-container {
    width: 180px;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;

    .transfer-container {
      height: 100%;
      border: 1px solid #daefff;
      overflow-y: auto;
      padding: 15px;

      .tagItem {
        margin: 0 12px 5px 0;
      }
    }
  }

  .tab-title {
    // border-left: 5px solid #409eff;
    padding-left: 5px;
    margin-bottom: 5px;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
