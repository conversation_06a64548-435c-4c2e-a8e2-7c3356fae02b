<template>
  <el-dialog
    title="分值与评级设置"
    :visible="visible"
    @close="closeBoolean(false)"
    width="600px"
    top="5vh"
    :close-on-click-modal="true"
  >
    <div class="setting-container">
      <el-form
        :model="subConfig"
        :rules="rules"
        ref="ruleForm"
        label-width="140px"
        size="small"
      >
        <div class="form-title">一级梯度 {{`${mainSetting.levelName}(${mainSetting.levelCode})`}}-u1</div>
        <el-row>
          <el-col :span="16">
            <!-- <el-form-item label="二级梯度名称：" prop="name">
              <el-input
                v-model.trim="subConfig.name"
                maxlength="100"
                clearable
              />
            </el-form-item> -->
            <el-form-item label="二级梯度名称：" prop="subRuleCode">
              <el-select
                v-model="subConfig.subRuleCode"
                size="small"
                placeholder="请选择取值接口"
                :clearable="true"
                @change="selectRuleCode"
              >
                <el-option
                  v-for="item in configList"
                  :key="item.subRuleCode"
                  :label="item.subRuleName"
                  :value="item.subRuleCode"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="规则描述：" prop="remark">
              <el-input
                v-model.trim="subConfig.remark"
                maxlength="100"
                readonly
              />
            </el-form-item>
            <!-- <el-form-item label="权重：" prop="management">
              <el-input
                v-model.trim="subConfig.management"
                maxlength="100"
                clearable
              />
            </el-form-item> -->
            <el-form-item label="分值：" prop="score">
              <el-input
                v-model.number="subConfig.score"
                readonly
                clearable
              />
            </el-form-item>
            <el-form-item label="系数调整：" prop="coefficient">
              <el-input
                v-model="subConfig.coefficient"
                type="number"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态：" prop="switchFlag" label-width="80px">
              <el-switch v-model="subConfig.switchFlag" active-value="1" inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button type="primary" size="small" @click="handleSubmit"
        >确认</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  subConfigList,
  findByMain,
  updateSubRule,
  addSubRule,
} from "@/api/enterprisePortrait";
import main from "vuex-along";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    mainSetting: {
      // 一级配置
      type: Object,
      default: () => {
        return {};
      },
    },
    subSetting: {
      // 二级配置
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      subConfig: {
        coefficient: 0,
        mainId: "",
        name: "",
        planId: "",
        remark: "",
        score: 0,
        subRuleCode: "",
        subRuleName: "",
        switchFlag: "",
      },
      configList: [],
      rules: {
        subRuleCode: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        subRuleCode: [
          {
            required: true,
            message: "",
          },
        ],
      },
    };
  },
  created() {
    if (!this.subSetting.id) {
      this.subConfig.mainId = this.mainSetting.id;
      this.subConfig.planId = this.mainSetting.planId;
    } else {
      this.subConfig = this.subSetting;
    }
    this.getConfigList();
  },
  methods: {
    selectRuleCode(val) {
      const item = this.configList.find((item) => item.subRuleCode == val);   
      this.subConfig.subRuleName = item.subRuleName;
      this.subConfig.score = item.defaultScore;
      this.subConfig.remark = item.remark;
    },
    // 查询二级方案详情
    getDetail() {
      findByMain({ id: this.subSetting.id }).then((res) => {
        if (res.data.status == 200) {
          this.subConfig = res.data.data;
        }
      });
    },
    // 获取配置列表
    getConfigList() {
      subConfigList({ mainRuleCode: this.mainSetting.levelCode }).then((res) => {
        if (res.data.status == 200) {
          this.configList = res.data.data;
        }
      });
    },
    closeBoolean(val) {
      this.$emit("close", val);
    },
    // 提交
    handleSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.subConfig.id) {
            updateSubRule(this.subConfig).then((res) => {
              if (res.data.status == 200) {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.$emit("submit")
              }else {
                this.$message({
                  message: res.data.msg,
                  type: "error",
                });
              }
            });
          } else {
            addSubRule(this.subConfig).then((res) => {
              if (res.data.status == 200) {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.$emit("submit")
              } else {
                this.$message({
                  message: res.data.msg,
                  type: "error",
                });
              }
            });
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-container {
  padding: 0 20px;

  .form-title {
    font-size: 16px;
    border-bottom: 1xp solid #d23c3c;
    margin-bottom: 16px;
  }
}

.footer {
  text-align: center;
}
</style>
