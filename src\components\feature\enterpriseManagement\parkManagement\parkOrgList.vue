<template>
  <div class="enterpriseList">
    <div class="header">
      <div class="title">园区管理</div>
      <div class="operation">
        <div class="inputBox">
          <el-cascader
            size="small"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtVal"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist"
            style="width: 220px"
          ></el-cascader>
          <!-- <el-select
            v-model="entTypeVal"
            size="small"
            placeholder="请选择企业类型"
            :clearable="true"
          >
            <el-option
              v-for="item in entType"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <!-- <el-select
            v-model="levelVal"
            size="small"
            placeholder="请选择重大危险源企业"
            :clearable="true"
            multiple
            style="width: 300px"
          >
            <el-option
              v-for="item in level"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <!-- <el-select
            v-model="auditStatus"
            size="small"
            placeholder="请选择审核状态"
            :clearable="true"
            style="width: 180px"
          >
            <el-option
              v-for="item in auditStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select> -->
          <el-input
            v-model.trim="parkName"
            size="small"
            placeholder="请输入园区名称"
            class="input"
            clearable
          ></el-input>
          <el-button type="primary" size="small" @click="searches()"
            >查询</el-button
          >
        </div>
        <CA-button
          type="primary"
          size="small"
          class="export"
          plain
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table
        :data="tableData.records"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        @select="select"
        @select-all="select"
      >
        <!-- <el-table-column
          type="selection"
          width="55"
          fixed="left"
          align="center"
        >
        </el-table-column> -->
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="parkName"
          label="园区名称"
          width="400"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span
              @click="goEnt(scope.row)"
              style="color: rgb(57, 119, 234)"
              class="parkName"
            >
              {{ scope.row.parkName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="districtName"
          label="行政区划"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="intoEnterNum"
          label="在园企业数"
          align="center"
        >
        <template slot-scope="scope">
            <span
              @click="goEntList(scope.row)"
              style="color: rgb(57, 119, 234)"
              class="parkName"
            >
              {{ scope.row.intoEnterNum }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="respPersonName"
          label="园区负责人"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="respPersonTel"
          label="负责人联系电话"
          align="center"
        >
        </el-table-column>

      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="tableData.size"
        layout="total, prev, pager, next"
        background
        :total="tableData.total"
      >
      </el-pagination>
    </div>
    <!-- <DialogCheckDetails
      :parkId="parkId"
      :show="dialogCheckDetailsShow"
      @closeBoolean="closeBoolean"
      @getParentList="search"
      ref="dialogCheckDetails"
    />
    <DialogInformationCheck
      :parkId="parkId"
      :show="dialogInformationCheckShow"
      @closeBoolean="closeBoolean"
      ref="dialogInformationCheck"
    /> -->

   </div>
</template>

<script>
import { getEnterpriseList, InformationExportexcel,enterExportExcel,yuanquDonwLoad } from "@/api/entList";
import {
  parkQueryPageList,
  parkQueryEnterPageList,
} from "@/api/riskAssessment";
import { getEnt } from "@/api/dailySafety";
// import DialogCheckDetails from "./dialogCheckDetails.vue";
// import DialogInformationCheck from "./dialogInformationCheck.vue";

import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  name: "enterpriseList",
  components: {
    // DialogCheckDetails,
    // DialogInformationCheck,
  },
  data() {
    return {
      currentPage: 1,
      parkId: "",
      entType: [
        {
          label: "生产",
          value: "01",
        },
        {
          label: "经营",
          value: "02",
        },
        {
          label: "使用",
          value: "03",
        },
        {
          label: "第一类非药品类易制毒",
          value: "04",
        },
      ],
      entTypeVal: "",
      level: [
        {
          label: "一级",
          value: "1",
        },
        {
          label: "二级",
          value: "2",
        },
        {
          label: "三级",
          value: "3",
        },
        {
          label: "四级",
          value: "4",
        },
      ],
      levelVal: ["1", "2", "3", "4"],
      auditStatusList: [
        {
          label: "审核通过",
          value: "1",
        },
        {
          label: "审核不通过",
          value: "2",
        },
        {
          label: "待审核",
          value: "3",
        },
      ],
      auditStatus: "",
      input: "",
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      value: "",
      tableData: null,
      districtLoading: false,
      parkName: "",
      loading: false,
      selection: [],
      tabName: "",
      entLevel: "",
      dialogCheckDetailsShow: false,
      dialogInformationCheckShow: false,
    };
  },
  //方法集合
  methods: {
    setTag() {
      const data = {
        parkName: this.parkName,
        districtVal: this.districtVal,
        levelVal: this.levelVal,
        entTypeVal: this.entTypeVal,
        auditStatus: this.auditStatus,
      };
      // console.log(data);
      this.$store.commit("controler/updateEntListControler", data);
    },
    /**  *
     * * @param {string} data dialogCheckDetailsShow，dialogInformationCheckShow
     * */
    closeBoolean(data) {
      this[data.name] = data.boolean;
    },
    goEnt(val) {
      let bool = true;
      this.$emit("entBool", bool, "basicInformationTab");
      let id = val.parkId;
      let codeParkId=val.codeParkId
      this.$emit("entId", {id:id,codeParkId:codeParkId});
    },
    goEntList(val) {
      let bool = true;
      this.$emit("entBoolList", bool, "parkEnterList");
      let id = val.parkId;
      let codeParkId=val.codeParkId
      this.$emit("entId", {id:id,codeParkId:codeParkId});
    },


    // 导出
    exportExcel() {
      yuanquDonwLoad({
        parkName: this.parkName,
        distCode: this.districtVal,
        current: this.currentPage,
        size:this.tableData.size,
        levelList: [],
        parkId: '',
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "园区列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].parkId;
      }
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.search();
    },
    //获取列表数据
    getData() {
     this.search();
    },
    handleClick(parkId, name) {
      this.$emit("goTag", parkId, name);
    },
    handleClickCheck(parkId, name) {
      this[name] = true;
      this.parkId = parkId;
      if (name === "dialogInformationCheckShow") {
        this.$nextTick(() => {
          this.$refs.dialogInformationCheck.getData();
        });
      }
    },
    //查询
    searches() {
      this.currentPage = 1;
      this.search();
    },
    search() {
      this.loading = true;
      parkQueryPageList({
        // distCode: this.districtVal,
        distCode: this.districtVal,
        levelList: [],
        parkName: this.parkName,
        parkId: '',
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        this.tableData = res.data.data;
        this.loading = false;
      });
    },

    // //根据需求设计，存入临时缓存浏览器未关闭，保持数据状态
    // changeDistrict() {
    //   const data = {
    //     parkName: this.parkName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    // changeEntTypeVal() {
    //   // sessionStorage.setItem("entTypeVal", this.entTypeVal);
    //   const data = {
    //     parkName: this.parkName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    // changeLevelVal() {
    //   // sessionStorage.setItem("levelVal", this.levelVal.join(","));
    //   const data = {
    //     parkName: this.parkName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    // changeEnterpName() {
    //   // sessionStorage.setItem("parkName", this.parkName);
    //   const data = {
    //     parkName: this.parkName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    getSessionStorage() {
      //如果企业列表筛选条件的行政区划代码存在的时候，才给当前页面继承上次选择的行政区划代码
      if (this.entListControler.districtVal) {
        this.districtVal = this.entListControler.districtVal;
      }
      this.entTypeVal = this.entListControler.entTypeVal;
      //必填项
      if (this.entListControler.levelVal == null) {
        this.$store.commit("controler/updateEntListControler", {
          parkName: this.parkName,
          districtVal: this.districtVal,
          levelVal: ["1", "2", "3", "4"],
          entTypeVal: this.entTypeVal,
          auditStatus: this.auditStatus,
        });
      } else {
        this.levelVal = this.entListControler.levelVal;
      }
      this.parkName = this.entListControler.parkName;
      this.auditStatus = this.entListControler.auditStatus;
    },
  },
  mounted() {
    this.getData();
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      entModelName: (state) => state.entModelName,
      entListControler: (state) => state.entListControler,
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    $attrs: {
      handler(newVal, oldVal) {
        console.log(newVal);
      },
    },
    entModelName: {
      handler(newVal, oldVal) {
        this.entModelName = this.tabName;
      },
      deep: true,
      // immediate: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .parkName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
        }
      }
    }
    .export {
      // margin-right: 20px;
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
