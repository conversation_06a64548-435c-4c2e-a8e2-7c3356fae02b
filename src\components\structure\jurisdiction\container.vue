<template>
  <div class="container">
    <div class="header">
      <div class="title">权限列表</div>
      <div v-if="$store.state.sa.SAPrivListData.menuId != ''">
        <el-radio-group size="small" v-model="mode">
          <el-radio-button label="权限信息"></el-radio-button>
          <el-radio-button label="控制url信息"></el-radio-button>
        </el-radio-group>
      </div>
      <div>
        <el-button
          type="primary"
          class="newMenu"
          @click="newMenu()"
          icon="plus"
          v-if="showFlag"
        >
          <span>添加权限</span>
        </el-button>
        <el-button
          type="primary"
          class="newMenu"
          @click="newMenus()"
          icon="plus"
          v-else
        >
          <span>添加url</span>
        </el-button>
      </div>
    </div>
    <div v-if="showFlag">
      <div class="body" v-loading="loading">
        <el-table
          :data="tableData.records"
          style="width: 100%; color: rgb(101, 101, 101)"
        >
          <el-table-column prop="privCode" label="权限编码"> </el-table-column>
          <el-table-column prop="privName" label="权限名称"> </el-table-column>
          <el-table-column prop="menuUrl" label="权限类型">
            <template slot-scope="{ row, column, $index, store }">
              <span v-if="row.privType == '1'">模块点</span>
              <span v-else-if="row.privType == '2'">功能点</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleEdit(scope.$index, scope.row)"
                ><i class="el-icon-edit"></i
              ></el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDelete(scope.$index, scope.row)"
                ><i class="el-icon-delete"></i
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
          :current-page.sync="currentPage3"
          :page-size="tableData.pageSize"
          layout="prev, pager, next, jumper"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
    </div>
    <div v-else>
      <div class="body" v-loading="loadings">
        <el-table
          :data="tableDatas.records"
          style="width: 100%; color: rgb(101, 101, 101)"
        >
          <el-table-column prop="url" label="权限名称"> </el-table-column>
          <el-table-column prop="url" label="权限名称" v-if="showFlag">
          </el-table-column>
          <el-table-column prop="url" label="权限名称" v-if="showFlag">
          </el-table-column>
          <el-table-column prop="url" label="权限名称" v-if="showFlag">
          </el-table-column>
          <el-table-column prop="url" label="权限名称" v-if="showFlag">
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button
                size="small"
                type="primary"
                @click="handleEdits(scope.$index, scope.row)"
                ><i class="el-icon-edit"></i
              ></el-button>
              <el-button
                size="small"
                type="danger"
                @click="handleDeletes(scope.$index, scope.row)"
                ><i class="el-icon-delete"></i
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          @prev-click="handleCurrentChange"
          @next-click="handleCurrentChange"
          :current-page.sync="currentPage4"
          :page-size="tableDatas.pageSize"
          layout="prev, pager, next, jumper"
          :total="tableDatas.total"
        >
        </el-pagination>
      </div>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
      :showTree="showTree"
    ></DialogTable>
    <DialogTables
      ref="childs"
      :dialogTableVisibles="dialogTableVisibles"
      :showTrees="showTrees"
    ></DialogTables>
  </div>
</template>

<script>
import {
  getPrivList,
  getPrivUrlList,
  deleteByPrivId,
  isDeleteByPrivId,
  deleteByPrivIdUrl,
} from "../../../api/jurisdiction";
import DialogTable from "./table";
import DialogTables from "./urltable";
import { mapState, mapGetters } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {
    DialogTable,
    DialogTables,
  },
  data() {
    return {
      dialogTableVisible: true,
      dialogTableVisibles: true,
      showTree: true,
      showTrees: true,
      currentPage3: 1,
      currentPage4: 1,
      tableData: {},
      tableDatas: {},
      loading: false,
      loadings: false,
      mode: "权限信息",
      showFlag: true,
    };
  },
  computed: {
    listenMenuList() {
      return this.$store.state.sa.SAPrivListData;
    },
  },
  //方法集合
  methods: {
    //打开编辑弹框
    handleEdit(index, row) {
      this.dialogTableVisible = true;
      this.showTree = false;
      this.$refs.child.parentMsg(this.dialogTableVisible, this.showTree);
      this.$refs.child.getData(row.id);
    },
    handleEdits(index, row) {
      this.dialogTableVisibles = true;
      this.showTrees = false;
      this.$refs.childs.parentMsg(this.dialogTableVisibles, this.showTrees);
      this.$refs.childs.getData(row.id);
    },
    handleDelete(index, row) {
      this.$confirm("确定要删除选择的数据吗？", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteByPrivId({ privId: row.id })
            .then((res) => {
              if (res.data.code == 0) {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.getMenuData(this.$store.state.sa.SAPrivListData, 1);
                this.$emit("pf");
              }
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    handleDeletes(index, row) {
      this.$confirm("确定要删除选择的数据吗？", "删除", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteByPrivIdUrl({ id: row.id })
            .then((res) => {
              if (res.data.code == 0) {
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.getMenuData(this.$store.state.sa.SAPrivListData, 1);
                this.$emit("pf");
              }
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    fatherMethod() {
      this.getMenuData(this.$store.state.sa.SAPrivListData, 1);
      this.$emit("pf");
    },
    //新建菜单
    newMenu() {
      this.dialogTableVisible = true;
      this.showTree = true;
      this.$refs.child.parentMsg(this.dialogTableVisible, this.showTree);
      this.$refs.child.clearTable();
      this.$refs.child.getTree(this.$store.state.sa.SAPrivListData.systemCode);
    },
    newMenus() {
      this.dialogTableVisibles = true;
      this.showTrees = true;
      this.$refs.childs.parentMsg(this.dialogTableVisibles, this.showTrees);
      this.$refs.childs.clearTable();
      this.$refs.childs.getTree(this.$store.state.sa.SAPrivListData.systemCode);
    },
    //获取菜单信息列表
    getMenuData(val, pageNo) {
      console.log(val);
      if (this.showFlag) {
        this.loading = true;
        getPrivList({
          systemCode: val.systemCode,
          privId: val.menuId || "",
          pageNo: pageNo || 1,
          pageSize: 8,
        })
          .then((res) => {
            this.loading = false;
            this.tableData = res.data.data;
          })
          .catch((e) => {
            this.loading = false;
            console.log(e, "请求错误");
          });
      } else {
        this.loadings = true;
        getPrivUrlList({
          systemCode: val.systemCode,
          privId: val.menuId || "",
          pageNo: pageNo || 1,
          pageSize: 8,
        })
          .then((res) => {
            // console.log(res.data.data);
            this.loadings = false;
            this.tableDatas = res.data.data;
          })
          .catch((e) => {
            this.loadings = false;
            console.log(e, "请求错误");
          });
      }
    },

    handleSizeChange() {},
    handleCurrentChange(data) {
      this.getMenuData(this.$store.state.sa.SAPrivListData, data);
    },
    bus() {
      var vm = this;
      // 用$on事件来接收参数
      Bus.$on("SAPrivListData", (data) => {
        this.getMenuData(data);
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.bus();
  },
  watch: {
    mode(newValue, oldValue) {
      if (newValue == "权限信息") {
        this.showFlag = true;
      } else if (newValue == "控制url信息") {
        this.showFlag = false;
      }
      this.getMenuData(this.$store.state.sa.SAPrivListData, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 15px;

  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .newMenu {
      width: 100px;
      height: 38px;
      padding: 10px;
      color: #fff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .title {
      font-weight: 900;
      font-size: 16px;
      color: #000;
    }
  }
  .pagination {
    float: right;
    margin-top: 30px;
  }
}
</style>
