<template>
  <div class="patrolOnline">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              实时监测信息
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.name"
      >
        <keep-alive :include="item.name">
          <component :is="item.name" :ref="item.name"></component>
        </keep-alive>
      </el-tab-pane>
    </el-tabs>
    <!-- <iframe src="http://*************:36080/freelogin/guizhou/" seamless align="center" frameborder="0" width="100%" height="100%"></iframe> -->
  </div>
</template>

<script>
import videoOnlineMonitoring from "./videoOnlineMonitoring";
import IOTmonitoring from "../IOTmonitoring/IOTmonitoring";
import videoPlayback from "./videoPlayback";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  name: "",
  components: {
    videoOnlineMonitoring,
    IOTmonitoring,
    videoPlayback,
  },
  data() {
    return {
      activeName: "videoPlayback",
      tabs: [
        // {
        //   label: "监测联网分析",
        //   name: "videoOnlineMonitoring",
        // },
        {
          label: "视频播放",
          name: "videoPlayback",
        },
        {
          label: "在线动态监测",
          name: "IOTmonitoring",
        },
      ],
    };
  },
  created() {},
  mounted() {
    this.initalize();
  },
  computed: {
    ...mapStateLogin({
      user: (state) => state.user,
    }),
    ...mapStateControler({
      entPatroName: (state) => state.entPatroName,
    }),
  },
  //   watch: {
  //     entPatroName: {
  //       handler(newVal, oldVal) {
  //         this.activeName = this.entPatroName;
  //       },
  //       deep: true,
  //       // immediate: true,
  //     },
  //   },
  methods: {
    initalize() {
      this.$nextTick(() => {
        this.handleClick();
      });
    },
    fatherMethod() {
      // debugger
      this.activeName = "HistoryPatrolCheck";
      this.$refs.HistoryPatrolCheck[0].onceShowSpotCheck();
    },
    // handleClick(tab, event) {
    //   return new Promise((resolve, reject) => {
    //     console.log(this.$refs[this.activeName]);
    //     resolve(this.$refs[this.activeName][0].getList());
    //   });
    // },
    handleClick(tab, event) {
      console.log(this.activeName);
      // 检查组件是否存在并且有getList方法
      if (this.$refs[this.activeName] && this.$refs[this.activeName][0]) {
        const component = this.$refs[this.activeName][0];
        if (typeof component.getList === "function") {
          component.getList();
        } else {
          console.log(`组件 ${this.activeName} 没有 getList 方法`);
        }
      } else {
        console.log(`组件 ${this.activeName} 未找到`);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.patrolOnline {
  height: 100%;
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }
}
</style>
