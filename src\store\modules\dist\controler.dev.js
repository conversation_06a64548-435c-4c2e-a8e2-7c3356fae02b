"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var state = {
  vuexEntId: ""
};
var getters = {
  getEntId: function getEntId(state) {
    return state.vuexEntId;
  }
};
var actions = {//   setMapType({ state, commit }, maptype) {
  //     commit("updateMapType", maptype);
  //   }
};
var mutations = {
  updateEntId: function updateEntId(state, val) {
    console.log(state);
    state.vuexEntId = val;
  }
};
var _default = {
  namespaced: true,
  state: state,
  getters: getters,
  actions: actions,
  mutations: mutations
};
exports["default"] = _default;