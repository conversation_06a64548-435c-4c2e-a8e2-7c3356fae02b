import axios from "axios";
import qs from "qs";

//通讯录-机构树查询
export const getMailListTreeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/org/maillistTree/v1",
    data: data,
  });
};
//通讯录-机构树查询人员
export const getMailListTreeDataForPerson = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/list/v1",
    data: data,
  });
};
export const personlitv1 = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/group/person/list/v1",
    data: data,
  });
};
//通讯录-获取通讯录组数据
export const getGroupListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/group/groupList/v1",
    data: data,
  });
};
export const pushMessageFn = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/pushStatus/v1",
    data: data,
  });
};

//通讯录-获取通讯录组数据查询人员
export const getSelPersonByOrgGroupData = (data) => {
  return axios({
    method: "post",
    url: "/gemp-user/api/gemp/user/maillist/group/person/list/v1",
    data: data,
  });
};
//通讯录添加/修改分组
export const addOrgGroup = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/group/add/v1",
    data: data,
  });
};
//通讯录删除分组
export const deleteOrgGroup = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/group/delete/v1?id=" + data.id,
    data: data,
  });
};
//新增通讯录人员
export const addPerson = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/add/v1",
    data: data,
  });
};
//编辑通讯录人员
export const editPerson = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/update/v1",
    data: data,
  });
};
//删除通讯录人员
export const deletePersons = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/delete/v1",
    data: data,
  });
};
//删除通讯录人员
export const movePersonsFromGroup = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/group/person/delete/v1",
    data: data,
  });
};
//上移/下移/置顶/置底
export const sortPersons = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/sort/v1",
    data: data,
  });
};
//通讯录人员详情
export const personDetail = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/personNew/personId/v1",
    data: data,
  });
};
//通讯录字典表
export const getZidainData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-dic/api/dic/code/v1",
    data: data,
  });
};

// 新增人员到通讯录，人员与分组关联
export const addToAddressList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/maillist/group/person/add/v1",
    data: data,
  });
};

//根据短链接获取完整链接
export const shortCodeRestore = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/shortCode/restore/v1",
    data: data,
  });
};

//根据orgCode查询组织机构(包括企业机构)
export const getByParentCode = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/org/list/org/byParentCode/v1",
    data: data,
  });
};

//根据orgCode新增机构危化业务权限
export const dangerPrivilegeAdd = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/manage/org/dangerPrivilege/add/v1",
    data: data,
  });
};
//根据orgCode移除机构危化业务权限
export const dangerPrivilegeRemove = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/manage/org/dangerPrivilege/remove/v1",
    data: data,
  });
};
//修改组织机构信息
export const dangerPrivilegeModify = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/manage/org/modify/v1",
    data: data,
  });
};

//根据orgCode查询组织机构(包括企业机构)
export const userOrgPage = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-user/api/gemp/user/org/page/v1",
    data: data,
  });
};

//修改组织机构信息
export const getenterpriseByOrg = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
      token: data.token,
    },
    url: "/gemp-user/api/gemp/user/org/list/enterprise/site/v2",
  });
};
