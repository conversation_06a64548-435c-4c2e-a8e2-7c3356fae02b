import axios from "axios";
import qs from "qs";

//安全培训列表
export const getSafetyTrainingData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/enterprise/safetyTraining/list/v1',
      data: data
    });
};
//安全培训新增
export const addSafetyTrainingData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/enterprise/safetyTraining/add/v1',
      data: data
    });
};
//安全培训修改
export const editSafetyTrainingData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/enterprise/safetyTraining/update/v1',
      data: data
    });
};
//安全培训详情
export const detailSafetyTrainingData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/enterprise/safetyTraining/id/v1',
      data: data
    });
};
//安全培训删除
export const deleteSafetyTrainingData = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/enterprise/safetyTraining/delete/v1',
      data: data
    });
};
//安全培训导出
export const exportSafetyTraining = data => {
    return axios({
      method: "post",
      headers: {
        "Content-Type": "application/json"
      },
      url: '/gemp-chemical/api/gemp/enterprise/safetyTraining/export/v1',
      data: data,
      responseType: "arraybuffer"
    });
};
