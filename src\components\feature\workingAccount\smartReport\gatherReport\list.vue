<template>
  <div class="safety-check-list">
    <!-- 修改页面标题样式 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              采集报表管理
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 其他内容保持不变 -->
    <div class="content">
      <!-- 头部工具栏 -->
      <div class="tool-bar" v-if="!isEnterpriseUser">
        <div class="left">
          <el-button-group>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAdd"
            >
              新增报表
            </el-button>
          </el-button-group>
        </div>
        <div class="right">
          <el-form :inline="true" :model="searchForm">
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入报表名称"
                size="mini"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="mini" @click="handleSearch">
                搜索
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 报表列表 -->
      <el-table :data="tableData" border style="width: 100%">
        <!-- 添加序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center" />

        <el-table-column prop="gatherName" label="报表名称" min-width="200">
          <template slot-scope="scope">
            <span @click="goReport(scope.row)" class="report-name">
              {{ scope.row.gatherName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="updateTime" label="更新时间" width="180" />
        <el-table-column
          label="操作"
          width="200"
          fixed="right"
          v-if="!isEnterpriseUser"
        >
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              size="mini"
              class="delete-btn"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import FieldSettingsDialog from "../components/FieldSettingsDialog.vue";
import { getReportDesignList, deleteReportDesign } from "@/api/smartReport";

export default {
  name: "CountReportList",
  components: {
    FieldSettingsDialog,
  },
  props: {
    isEnterpriseUser: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      searchForm: {
        keyword: "",
      },
      tableData: [],
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      fieldSettingsVisible: false,
    };
  },
  methods: {
    // 新增报表
    handleAdd() {
      this.$emit("goAdd");
    },

    // 编辑报表
    handleEdit(row) {
      this.$emit("goEdit", {
        id: row.id,
        gatherName: row.gatherName,
      });
    },

    // 删除报表
    async handleDelete(row) {
      try {
        await this.$confirm("确认删除该报表?", "提示", {
          type: "warning",
        });

        await deleteReportDesign({ id: row.id }).then((res) => {
          if (res.data.status === 200) {
            this.$message.success("删除成功");
            this.getList();
          } else {
            this.$message.error(res.data.msg || "删除失败");
          }
        });
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败:", error);
          this.$message.error("删除失败");
        }
      }
    },

    // 保存字段设置
    handleFieldSettingsSave(settings) {
      // TODO: 调用保存接口
      this.$message.success("创建成功");
      this.getList();
    },

    // 获取列表数据
    async getList() {
      try {
        const params = {
          nowPage: this.page.current,
          pageSize: this.page.size,
          reportName: this.searchForm.keyword,
        };

        const res = await getReportDesignList(params);
        if (res.data.status === 200) {
          const { list, total } = res.data.data;

          // 转换数据结构
          this.tableData = list.map((item) => ({
            id: item.id,
            gatherName: item.gatherName,
            createTime: item.createDate,
            updateTime: item.updateDate,
            // 其他需要的字段...
          }));

          this.page.total = total;
        } else {
          this.$message.error(res.data.msg || "获取列表失败");
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$message.error("获取列表失败");
      }
    },

    // 搜索
    handleSearch() {
      this.page.current = 1;
      this.getList();
    },

    // 分页处理
    handleSizeChange(val) {
      this.page.size = val;
      this.getList();
    },

    handleCurrentChange(val) {
      this.page.current = val;
      this.getList();
    },

    // 修改跳转方法
    goReport(row) {
      if (!this.isEnterpriseUser) {
        this.$emit("goEnterpInfo", {
          id: row.id,
          gatherName: row.gatherName,
        });
      } else {
        this.$emit("goReportH5", {
          id: row.id,
        });
      }
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.safety-check-list {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;
  }

  .tool-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }

  .report-name {
    color: #3977ea;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
