<template>
  <div class="safety-check-list">
    <!-- 修改页面标题样式 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              数据填报管理
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 其他内容保持不变 -->
    <div class="content">
      <!-- 头部工具栏 -->
      <div class="tool-bar">
        <div class="left">
          <!-- <el-button-group>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-refresh"
              @click="getTableList"
            >
              刷新
            </el-button>
          </el-button-group> -->
        </div>
        <div class="right">
          <el-form :inline="true" :model="searchForm">
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入表名关键字"
                size="mini"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="mini" @click="handleSearch">
                搜索
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 数据表列表 -->
      <el-table
        :data="filteredTableList"
        border
        style="width: 100%"
        v-loading="tableLoading"
      >
        <!-- 添加序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center" />

        <el-table-column prop="reportNameCn" label="表名" min-width="200">
          <template slot-scope="scope">
            <span @click="handleDataEntry(scope.row)" class="table-name">
              {{ scope.row.reportNameCn }}
            </span>
          </template>
        </el-table-column>

        <el-table-column
          prop="reportNameEn"
          label="表标识"
          min-width="150"
          show-overflow-tooltip
        />

        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="handleDataEntry(scope.row)"
            >
              数据填报
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getTableName } from "@/api/smartReport";

export default {
  name: "DataEntryList",
  data() {
    return {
      searchForm: {
        keyword: "",
      },
      tableList: [],
      tableLoading: false,
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
    };
  },
  computed: {
    // 过滤后的表格列表
    filteredTableList() {
      if (!this.searchForm.keyword) {
        return this.tableList;
      }

      const keyword = this.searchForm.keyword.toLowerCase();
      return this.tableList.filter(
        (item) =>
          (item.reportNameCn &&
            item.reportNameCn.toLowerCase().includes(keyword)) ||
          (item.reportNameEn &&
            item.reportNameEn.toLowerCase().includes(keyword))
      );
    },
  },
  methods: {
    // 获取数据表列表
    async getTableList() {
      this.tableLoading = true;
      try {
        const res = await getTableName();
        if (res.status === 200) {
          this.tableList =
            res.data.data.filter(
              (item) => item.reportNameEn !== "cim_risk_accident_record"
            ) || [];
        } else {
          this.$message.error(res.msg || "获取数据表列表失败");
          this.tableList = [];
        }
      } catch (error) {
        console.error("获取数据表列表失败:", error);
        this.$message.error("获取数据表列表失败，请稍后重试");
        this.tableList = [];
      } finally {
        this.tableLoading = false;
      }
    },

    // 搜索处理
    handleSearch() {
      this.page.current = 1;
      // 搜索逻辑在computed中处理，这里可以添加其他逻辑
    },

    // 分页处理
    handleSizeChange(val) {
      this.page.size = val;
      this.getTableList();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.getTableList();
    },

    // 数据填报
    handleDataEntry(table) {
      this.$emit("goDetail", table);
    },

    // 格式化日期
    formatDate(dateStr) {
      if (!dateStr) return "-";

      try {
        const date = new Date(dateStr);
        return date.toLocaleDateString("zh-CN", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        });
      } catch (error) {
        return "-";
      }
    },
  },
  created() {
    this.getTableList();
  },
};
</script>

<style lang="scss" scoped>
.safety-check-list {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;
  }

  .tool-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }

  .table-name {
    color: #3977ea;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
