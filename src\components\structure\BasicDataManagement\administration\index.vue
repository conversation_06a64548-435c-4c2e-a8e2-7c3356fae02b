<template>
  <div class="administration">
    <div class="tree-box">
      <h2>组织机构</h2>

      <a-directory-tree
        multiple
        default-expand-all
        @select="onSelect"
        style="padding: 0 10px; height: 100%; overflow-x: scroll"
      >
        <a-tree-node
          :key="item.orgCode"
          v-for="item in newAllData"
          :title="item.orgName"
        >
          <a-tree-node
            :key="subItem.orgCode"
            v-for="subItem in item.children"
            :title="subItem.orgName"
          >
            <a-tree-node
              :key="childSubItems.orgCode"
              v-for="childSubItems in subItem.children"
              :title="childSubItems.orgName"
            >
              <a-tree-node
                :key="childSubItems_item.orgCode"
                v-for="childSubItems_item in childSubItems.children"
                :title="childSubItems_item.orgName"
              ></a-tree-node>
            </a-tree-node>
          </a-tree-node>
        </a-tree-node>
      </a-directory-tree>
    </div>
    <div class="user-table">
      <div class="header">
        <div class="title">机构列表</div>
        <div>
          <el-button
            type="primary"
            icon="plus"
            :style="{ marginTop: '9px', marginRight: '20px' }"
            @click="addInfo()"
          >
            新建机构
          </el-button>
        </div>
      </div>
      <div class="body" style="padding: 0 10px">
        <el-table
          v-loading="loading"
          style="
            width: 100%;
            min-height: 500px;
            margin-left: 50%;
            transform: translateX(-50%);
          "
          :default-sort="{ prop: 'date', order: 'descending' }"
          ref="multipleTable"
          :data="tableData"
        >
          <el-table-column type="index" label="序号" width="50">
          </el-table-column>
          <el-table-column prop="orgName" label="机构名称" width="150">
          </el-table-column>
          <el-table-column prop="orgDuty" label="机构职责" width="150">
          </el-table-column>
          <el-table-column prop="dutytel" label="联系电话" width="150">
          </el-table-column>
          <el-table-column prop="address" label="地址" width="220">
          </el-table-column>
          <el-table-column prop="updateTime" label="修改时间" width="150">
          </el-table-column>
          <el-table-column width="150" label="操作" fixed="right">
            <template slot-scope="{ row, column, $index, store }">
              <el-tooltip
                class="item"
                effect="dark"
                content="编辑"
                placement="top-start"
              >
                <el-button
                  type="primary"
                  icon="el-icon-edit"
                  size="mini"
                  @click="editUser(row.orgCode)"
                ></el-button>
              </el-tooltip>
              <el-tooltip
                class="item"
                effect="dark"
                content="删除"
                placement="top-start"
              >
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  @click="deleteOrg(row)"
                  size="mini"
                ></el-button>
              </el-tooltip>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.pageNo"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.pageSize"
          layout="total, prev, pager, next"
          :total="page.total"
          v-if="page.total > 0"
          style="float: right; padding-top: 15px"
        >
        </el-pagination>
      </div>
    </div>
    <EditUser
      ref="childs"
      :edVisible="edVisible"
      :rowData="rowData"
      :orgCode="orgCode"
      @ed-callback="edUser"
    ></EditUser>
  </div>
</template>

<script>
import EditUser from "./edit";
import {
  getUserData,
  getStructureData,
  delOrgCode,
} from "@/api/BasicDataManagement";
export default {
  name: "administration",
  components: {
    EditUser,
  },
  data() {
    return {
      allData: [],
      newAllData: [],
      tableData: [],
      page: {
        pageNo: 1,
        pageSize: 10,
        total: 0,
      },
      orgCode: "-1",
      showFlag: 0,
      keyWords: "",
      dialogTableVisible: false,
      loading: false,
      edVisible: false,
      srVisible: false,
      rowData: {},
    };
  },
  created() {},
  mounted() {
    this.getStructure();
    this.getUser();
  },
  methods: {
    onSelect(selectedKeys, info) {
      this.orgCode = selectedKeys[0];
      this.getUser();
    },
    getStructure() {
      getStructureData()
        .then((data) => {
          if (data.data.code == 0) {
            this.newAllData = data.data.data;
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    getUser() {
      this.loading = true;
      getUserData({
        parentCode: this.orgCode || "-1",
        current: this.page.pageNo,
        size: this.page.pageSize,
      })
        .then((data) => {
          if (data.data.code == 0) {
            this.loading = false;
            let listData = data.data.data.records;
            this.page.total = data.data.data.total;
            this.tableData = listData;
          } else {
            this.loading = false;
            this.tableData = [];
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //新建机构
    addInfo() {
      this.dialogTableVisible = true;
      this.$refs.childs.parentMsg(this.dialogTableVisible, "-1", false);
      this.$refs.childs.close();
    },
    /**
     * 切换分页尺寸
     * @param val
     */
    handleSizeChange(val) {
      this.page.pageSize = val;
      this.getUser();
      this.$refs.multipleTable.clearSelection();
    },
    /**
     * 切换当前页
     * @param val
     */
    handleCurrentChange(val) {
      this.page.pageNo = val;
      this.getUser();
      this.$refs.multipleTable.clearSelection();
    },
    deleteOrg(row) {
      this.$confirm("确定要删除该机构吗？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          console.log(row.orgCode);
          delOrgCode(row.orgCode)
            .then(() => {
              this.getUser();
              this.$message({
                type: "success",
                message: "删除成功!",
              });
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    editUser(orgCode, index) {
      this.edVisible = true;
      this.rowData = orgCode;
      this.$refs.childs.parentMsg(this.edVisible, orgCode, true);
    },
    edUser() {
      this.edVisible = false;
      this.timeres = new Date().getTime();
      this.getUser();
    },
  },
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style lang="scss" scoped>
.administration {
  overflow: hidden;
}
.administration > div {
  display: inline-block;
}
.tree-box {
  width: 23%;
  min-height: 100%;
  height: 85vh;
  overflow: hidden;
  background: #fff;
  h2 {
    line-height: 34px;
    font-size: 18px;
    padding: 8px 16px;
  }
}
.ant-tree-title {
  width: 167px;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}

.user-table {
  //   height: 85vh;
  min-height: 85vh;
  width: calc(77% - 16px);
  float: right;
  background-color: #fff;
  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 50px;
    .newMenu {
      width: 90px;
      height: 38px;
      padding: 10px;
      background: #53a8e8;
      color: #fff;
      text-align: center;
      border-color: #53a8e8;
      border-radius: 4px;
    }
    .title {
      font-weight: 900;
      font-size: 16px;
      color: #000;
      padding: 0 20px;
      line-height: 50px;
    }
  }
}
</style>

<style>
.ant-tree-title {
  width: 187px;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  word-break: break-all;
}
.el-table th > .cell {
  text-align: center;
}

.el-table .cell {
  text-align: center;
}
.cell .el-button {
  padding: 12px;
}
.cell .el-button + .el-button {
  margin-left: 0px;
}
.body .el-table td,
.el-table th {
  padding: 4px 0;
}
</style>
