<template>
  <div class="app-container">
    <div class="searchDia">
      <el-form ref="form" label-width="100px !important">
        <el-form-item label="事故企业周边" prop="" style="width:25%">
          <el-select
            v-model="radius"
            placeholder="请选择距离"
            clearable
             @clear="clearRadius()"
           style="width:120px;"
          >
            <el-option
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.label"
              :value="item.value"              
            />
          </el-select>
        </el-form-item>

        <el-form-item label="范围内的" prop="">
          <el-select
            v-model="enterpriseTypes"
            multiple
            placeholder="请选择企业类型"
            clearable
            @clear="clearEnterpriseTypes()"
            style="width: 100%"
          >
            <el-option
              v-for="(item, index) in entType"
              :key="index"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>


        <el-form-item label="关键字" prop="">
            <!-- <el-input v-model="enterpname" placeholder=""  /> -->
            <el-autocomplete
            popper-class="my-autocomplete"
            v-model="enterpname"
            :fetch-suggestions="querySearch"
            placeholder="请输入企业名称"
            clearable
            @clear="clearSensororgCode()"
            @select="handleSelect"           
            style="width: 200"
          >
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete>
          </el-form-item>


        <div class="serchBtn">
          <el-button
            type="primary"
            plain          
            size="small"
            @click="clickSearch"
            >查找</el-button
          >
        </div>
      </el-form>
    </div>
    <!-- <div class="title">发布渠道</div> -->
    <!-- <el-table :data="channelList" stripe class="pdTable" v-loading="loading"> -->
    <!-- <el-table-column
        label="序号"
        align="center"
        type="index"
        width="60"
      ></el-table-column> -->
    <!-- <el-table-column
        label="渠道名称"
        align="center"
        width="125"
        prop="channelName"
      /> -->

    <!-- <el-table-column label="发布对象" align="center">
        <template slot-scope="scope"> -->
    <div class="manCheckedCon">
      <!-- {{checkLists}} -->
      <div class="placeholderDiv" v-if="checkLists.length < 1">
        请选择发布对象
      </div>
      <div v-else class="placeholderDiv">
        <el-tag
          :disabled="disabled"
          class="tagItem"
          v-for="(tag, index) in checkLists"
          :key="index"
          :closable="closable"
          size="small"
          @close="tagCloses(index)"
          >{{ tag.orgName }}</el-tag
        >
      </div>
    </div>
    <div>
      <el-row class="manCheckedBox" v-if="!disabled">
        <el-col :span="24" v-if="leftOrgDataSys.length > 0">
          <div class="leftTree">
            <!--                  <el-scrollbar style="" class="auto-scrollbar">-->
            <el-tree
              :data="leftOrgDataSys"
              :props="defaultProps"
              :highlight-current="true"
              :default-expand-all="false"
              :expand-on-click-node="false"
            >
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span>{{ node.label }}</span>
                <!-- v-if="!data.children || data.children == null || data.children.length === 0" -->
                <span>
                  <el-button
                    type="text"
                    :disabled="data.label == '政务侧' || data.label == '企业侧'"
                    size="mini"
                    @click="
                      () =>
                        handleNodeClickSys({
                          dispOrder: data.dispOrder,
                          fax: data.fax,
                          id: data.id,
                          label: data.label,
                          orgCode: data.orgCode,
                          orgDuty: data.orgDuty,
                          orgName: data.orgName,
                          orgType: data.orgType,
                          parentCode: data.parentCode,
                          person: data.person,
                        })
                    "
                  >
                    加入发布对象
                  </el-button>
                </span>
              </span>
            </el-tree>
            <!--                  </el-scrollbar>-->
          </div>
        </el-col>
        <!--              <el-col :span="12">-->
        <!--                <div class="rightTree" v-loading="rightTreeLoading">-->
        <!--                  <el-scrollbar style="height: 20vh" class="auto-scrollbar">-->
        <!--                    <el-checkbox-group-->
        <!--                      v-model="checkLists"-->
        <!--                      class="treeCheckBox"-->
        <!--                    >-->
        <!--                      <el-checkbox-->
        <!--                        :label="item.orgName + ':' + item.id"-->
        <!--                        v-for="(item, index) in rightManLists"-->
        <!--                        :key="index"-->
        <!--                        :name="item.id"-->
        <!--                        @change="joinerChanges"-->
        <!--                        >{{ item.orgName }}</el-checkbox-->
        <!--                      >-->
        <!--                    </el-checkbox-group>-->
        <!--                                        <div v-else>暂无企业信息</div>-->
        <!--                  </el-scrollbar>-->
        <!--                </div>-->
        <!--              </el-col>-->
      </el-row>
    </div>
    <!-- </template>
      </el-table-column> -->

    <!--      <el-table-column label="应用" align="center" width="120">-->
    <!--        <template slot-scope="scope">-->
    <!--          <el-switch-->
    <!--            v-model="scope.row.isApply"-->
    <!--            active-color="#13ce66"-->
    <!--            inactive-color="#95a3bf"-->
    <!--            :disabled="disabled"-->
    <!--          ></el-switch>-->
    <!--        </template>-->
    <!--      </el-table-column>-->
    <!-- </el-table> -->
  </div>
</template>

<script>
import {
  orgTreeByorgcode,
  superviserTree,
} from "../../../../../api/informationRelease";
import { getOrgTreesN } from "../../../../../api/user";
import { getSearchArr } from "@/api/entList.js";
const demochannelList = [
  { channelName: "接收单位", publishObj: "", isApply: false, code: "1" },
  // { channelName: "短信", publishObj: "", isApply: false, code: "2" },
  // { channelName: "应急广播", publishObj: "", isApply: false, code: "3" }
  //   { channelName: "微信公众号", publishObj: "", isApply: false, code: '4' },
  //   { channelName: "APP", publishObj: "", isApply: false, code: '5' },
];
export default {
  name: "Safety",
  components: {},
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    nextSearchForm:{
      type:Object,
      default:{}
    },
    //
    checkLists: {
      type: Array,
      default: [],
    },
    chanList: {
      type: Array,
      default: [],
    },
    closable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      radius: "", //事故企业周边
      enterpriseTypes: "", //企业类型
      enterpname:'',
      statusList: [
        { label: "10公里", value: "10" },
        { label: "50公里", value: "50" },
        { label: "100公里", value: "100" },
      ],
      entType: [
        {
          label: "生产",
          value: "01",
        },
        {
          label: "经营",
          value: "02",
        },
        {
          label: "使用",
          value: "03",
        },
        {
          label: "第一类非药品类易制毒",
          value: "04",
        },
      ],
      defaultProps: {
        children: "children",
        label: "label",
      },
      leftOrgData: [], //  短信的组织树
      leftOrgDataSys: [], //系统消息的组织树
      rightManList: [], //与会人员右边的列表
      rightManLists: [],
      rightTreeLoading: true,
      rightTreeLoadings: true,
      checkList: [],
      // checkLists: [],
      // 遮罩层
      loading: true,
    };
  },
  computed: {
    channelList() {
      if (this.chanList.length > 0) {
        console.log("进来了");

        if (this.chanList[0].publishObj) {
          if (this.chanList[0].publishObj.constructor == Array) {
            if (this.chanList[0].publishObj.length > 0) {
              this.checkLists = [];
              this.chanList[0].publishObj.forEach((item) => {
                this.checkLists.push(item.name + ":" + item.code);
              });
            }
          }
        }
        if (this.chanList[1].publishObj) {
          if (this.chanList[1].publishObj.constructor == Array) {
            if (this.chanList[1].publishObj.length > 0) {
              this.checkList = [];
              this.chanList[1].publishObj.forEach((item) => {
                this.checkList.push(item.name + ":" + item.code);
              });
            }
          }
        }
        return this.chanList;
      } else {
        this.checkLists = [];
        this.checkList = [];
        this.$forceUpdate();
        // demochannelList[2].publishObj = ''
        return demochannelList;
      }
    },
  },

  created() {
    this.orgTreeByorgcodeFun(0, "");
    this.sysOrgTreeByorgcodeFun();
  },
  methods: {
    //自动输入
    clearSensororgCode() {
      this.enterpname = "";
    },
    clearEnterpriseTypes(){
      this.enterpriseTypes=[]
    },
    clearRadius() {
      this.radius = "";
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {    
      this.enterpname = item.enterpName;
    },
    handleSelectDialogFn(item) {     
      // this.form.unitId = item.enterpId;
      // this.form.unitName = item.enterpName;
      // getInformationBasicInfo(item.enterpId).then((res) => {        
      //   this.form.distCode = res.data.data.enterprise.districtCode; //行政区划代码
      //   this.form.districtName = res.data.data.enterprise.districtName;
      //   this.nextSearchForm={
      //     latitude:res.data.data.enterprise.latitude,
      //     longitude:res.data.data.enterprise.longitude
      //   }
      //   //latitude,longitude
      // });
    },
    // 自动输入end
    sysCheckChange(e, e1) {
      console.log(e1);
      let _array = [];
      e1.checkedNodes.forEach((item) => {
        _array.push(item.label + ":" + item.orgCode);
      });

      this.checkLists = _array;
      this.joinerChanges();
    },

    // 根据orgcode获取人员列表
    personListFun(orgCode) {
      personList({
        keyWord: "",
        newQueryType: "1",
        nowPage: 1,
        orgCode: orgCode,
        pageSize: 10000,
        personIds: "",
        queryType: "",
        updateTime: "",
      })
        .then((res) => {
          let _data = res.data.list;
          this.rightManList = _data;
          this.rightTreeLoading = false;
        })
        .catch((err) => {
          this.rightTreeLoading = false;
        });
    },

    handleNodeClick(data) {
      let _index = this.leftOrgData.findIndex((item) => item.id == data.id);
      orgTreeByorgcode({
        orgCode: data.id,
      })
        .then((res) => {
          if (res.data.length > 0) {
            this.leftOrgData[_index].children = [...res.data];
          }
        })
        .catch((err) => {
          console.log(err);
        });
      this.personListFun(data.id);
    },

    handleNodeClickSys(data) {
      let repeatData = (this.checkLists || []).find(
        (item) => item.orgCode === data.orgCode
      );
      if (repeatData && Object.keys(repeatData).length > 0) {
        this.$message.warning("该对象已选择~");
      } else {
        this.checkLists.push(data);
        this.$emit("selectList", this.checkLists);
      }
    },

    // 查询系统消息的组织树
    sysOrgTreeByorgcodeFun() {},

    // 根据当前用户的orgCode查询组织机构树
    clickSearch() {
      this.orgTreeByorgcodeFun();
    },
    orgTreeByorgcodeFun() {
      this.loading = true;
      // orgTreeByorgcode()
      //   .then((res) => {
      //     // this.leftOrgDataSys = this.handleSysData(res.data.data);
      //     this.leftOrgDataSys = res.data.data;
      //     this.loading = false;
      //     console.log('leftOrgDataSys++++++++++++++++++++++++++++++++++++++', this.leftOrgDataSys)
      //     // this.$forceUpdate();
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //   });
      var param = {
        radius: this.radius,
        enterpriseTypes: this.enterpriseTypes || [],
        latitude:this.nextSearchForm.latitude || '',
        longitude:this.nextSearchForm.longitude || '',
        enterpname:this.enterpname,
      };
      superviserTree(param)
        .then((res) => {
          // this.leftOrgDataSys = this.handleSysData(res.data.data);
          this.leftOrgDataSys = res.data.data;
          this.loading = false;
          console.log(
            "leftOrgDataSys++++++++++++++++++++++++++++++++++++++",
            this.leftOrgDataSys
          );
          // this.$forceUpdate();
        })
        .catch((err) => {
          console.log(err);
        });
    },

    tagCloses(e) {
      this.checkLists.splice(e, 1);
      this.$emit("selectList", this.checkLists);
      // let _array = [];
      // if (this.checkLists.length > 0) {
      //   this.checkLists.forEach((item) => {
      //     _array.push({
      //       code: item.split(":")[1],
      //       name: item.split(":")[0],
      //     });
      //   });
      // }
      // this.channelList[0].publishObj = JSON.stringify(_array);
      //   this.form.recvUserStrs = JSON.stringify(_array);
    },

    // 人员的change事件
    joinerChange(e) {
      let _array = [];
      this.checkList.forEach((item) => {
        _array.push({
          code: item.split(":")[1],
          name: item.split(":")[0],
          phone: item.split(":")[2],
        });
      });
      this.channelList[1].publishObj = JSON.stringify(_array);

      //   this.channelList[1].publishObj = _arrays.join(',');
    },
    joinerChanges(e) {
      let _array = [];
      this.checkLists.forEach((item) => {
        _array.push({
          code: item.split(":")[1],
          name: item.split(":")[0],
        });
      });
      this.channelList[0].publishObj = JSON.stringify(_array);
    },
    change(selVal) {},
    getList() {
      this.loading = true;
    },

    /** 修改按钮操作 */
    getTempleDetails(row) {
      const tmpltCode = row.tmpltCode;
      getTemple(tmpltCode).then((response) => {
        this.open = false;
        this.$emit("choosedTemplateInfo", response.data);
      });
    },

    rowClick(e) {
      this.getTempleDetails(e);
    },
  },
};
</script>

<style lang="scss" scoped>
.searchDia {
  margin: 20px 0 0 0;
  .el-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .el-form-item {
      width: 33%;
    }
  }
  /deep/ .el-form-item {
    margin: 0 0 0 0;
  }
  .serchBtn {
    margin: 0 0 0 10px;
  }
}
.tableItem {
  cursor: pointer;
  &:hover {
    color: #1890ff;
  }
}

.pdTable {
  width: 100%;
  table {
    width: 100% !important;
  }
}
.title {
  font-size: 16px;
  color: #082754;
  margin: 20px 0 0;
  border-bottom: 2px solid #46a0fb;
  padding-bottom: 12px;
}
.manCheckedCon {
  min-height: 36px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  padding: 6px 0;
  display: flex;
  align-items: center;
  margin: 10px 0 0 0;
}
.placeholderDiv {
  color: #c0c4cc;
  margin-left: 15px;
  display: flex;
  flex-wrap: wrap;
}
.manCheckedBox {
  margin-top: 10px;
}
.tagItem {
  margin-right: 6px !important;
  margin-bottom: 6px;
}
.leftTree {
  //width: 300px;
  height: 300px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  overflow: auto;
  padding: 6px 0;
  // margin-right: 30px;
  .auto-scrollbar {
    width: 100% !important;
  }
}

.rightTree {
  border-radius: 4px;
  // margin-left: 40px;
  width: 230px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  padding: 6px;
  .treeCheckBox {
    display: flex;
    flex-direction: column;
    text-align: left;
  }
}
.app-container .el-dialog {
  overflow-y: none !important;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
