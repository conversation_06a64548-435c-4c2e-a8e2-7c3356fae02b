<template>
  <div>  
    <div v-if="isMobile">
      <!-- h5端 -->
      <reviewH5></reviewH5>
    </div>
    <div v-else>
      <pcdetail></pcdetail>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import Sign from './sign.vue';
import { shortCodeRestore } from "@/api/mailList";
import axios from "axios";
import pcdetail from "./PcDetail.vue";
import reviewH5 from "./reviewH5.vue";

export default {
  name: 'Review',
  components: {
    pcdetail,
    reviewH5
  },
  data() {
    return {     
    }
  },
 
  computed: {
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
  },
  
   mounted() {   
    //  debugger;
    console.log("ismobile----->", this.isMobile, "");   
  },
 
}
</script>

