declare module "*.vue" {
  import Vue from "vue";
  export default Vue;
}
declare module "vue/types/vue" {
  import VueRouter, { Route } from "vue-router";
  interface Vue {
    $router: VueRouter; // 这表示this下有这个东西
    $route: Route;
    $http: any;
    $Message: any;
    $Modal: any;
  }
}
declare module "vue-property-decorator";
declare module "vue-class-component";
declare module "webpack";
declare module "webpack-merge";
declare module "copy-webpack-plugin";
declare module "html-webpack-plugin";
declare module "friendly-errors-webpack-plugin";
// declare module '@/api/entList';
declare module "vue-loader/lib/plugin";
declare module "mini-css-extract-plugin";
declare module "progress-bar-webpack-plugin";
declare module "qs";
declare module "extract-text-webpack-plugin";
