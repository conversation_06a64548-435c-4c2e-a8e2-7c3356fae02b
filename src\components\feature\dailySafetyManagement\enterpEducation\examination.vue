<template>
  <div class="education-training">
    <div class="examination-container" v-if="isExam">
      <div class="search-container">
        <!-- <el-input
          v-model.trim="searchParams.examName"
          size="small"
          placeholder="请输入企业名称"
          class="input"
          clearable
        ></el-input> -->
        <el-input
          v-model.trim="searchParams.examName"
          size="small"
          placeholder="请输入考试名称"
          class="input"
          clearable
        ></el-input>
        <el-button type="primary" size="small" @click="handleSearch"
          >查询</el-button
        >
        <el-button type="primary" size="small" @click="handleEdit('add')"
          >新增</el-button
        >
      </div>
      <div class="table-container">
        <el-table
          :data="tableData"
          :header-cell-style="headerCellStyle"
          border
          v-loading="loading"
          style="width: 100%"
          ref="multipleTable"
        >
          <el-table-column type="index" label="序号" width="55" align="center">
          </el-table-column>
          <el-table-column
            prop="examName"
            label="考试名称"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            prop="examTime"
            label="考试时长"
            width="80"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="topicType"
            label="题目类型"
            width="120"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="examCourse"
            label="考试科目"
            width="120"
            align="center"
          />
          <el-table-column
            prop="singleCount"
            label="单选题总分"
            width="100"
            align="center"
          />
          <el-table-column
            prop="multipleCount"
            label="多选题总分"
            width="100"
            align="center"
          />
          <el-table-column
            prop="fractionCount"
            label="总分值"
            width="80"
            align="center"
          />
          <el-table-column prop="status" label="状态" width="80" align="center">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status == '1' ? 'success' : 'danger'">{{
                scope.row.status == "1" ? "已启用" : "未启用"
              }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column
            prop="qrCode"
            label="二维码"
            width="80"
            align="center"
          >
            <template slot-scope="scope">
              <span class="icon-qrcode" @click="handleQrCode(scope.row)">
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="report"
            label="成绩报表"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleRecord(scope.row)"
                >查看</el-button
              >
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="240"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                type="text"
                size="small"
                @click="handleEdit('view', scope.row)"
                >查看</el-button
              >
              <!-- <el-button type="text" size="small" @click="handleEdit('edit', scope.row)">编辑</el-button> -->
              <el-button
                type="text"
                size="small"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
              <el-button
                type="text"
                size="small"
                @click="handleStart(scope.row)"
                >{{ scope.row.status == "0" ? "启动" : "禁用" }}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page.sync="searchParams.current"
            :page-size="searchParams.size"
            layout="total, prev, pager, next"
            background
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
      <!-- 考卷详情 -->
      <ExaminationDialog
        v-if="showDialog"
        :visible="showDialog"
        :educationItem="educationItem"
        :dialogType="dialogType"
        @closeBoolean="showDialog = false"
        @refresh="handleSubmit"
      />
      <qrCodeDialog
        v-if="showQrCode"
        :visible="showQrCode"
        :educationItem="educationItem"
        @closeBoolean="showQrCode = false"
      />
    </div>
    <!-- 考试记录 -->
    <ExamRecords v-else :recordId="recordId" @back="isExam = true" />
  </div>
</template>

<script>
import {
  examinationPage,
  enableExamination,
  examinationDelete,
} from "@/api/enterpEducation";
import ExaminationDialog from "./components/examinationDialog.vue";
import ExamRecords from "./ExamRecords.vue";
import qrCodeDialog from "./components/qrCodeDialog.vue";
import { mapState } from "vuex";
export default {
  components: {
    ExaminationDialog,
    ExamRecords,
    qrCodeDialog,
  },
  data() {
    return {
      loading: false,
      searchParams: {
        examName: "",
        examCourse: "",
        current: 1,
        size: 10,
      },
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      total: 0,
      tableData: [],
      educationItem: {},
      showDialog: false,
      dialogType: "add",
      isExam: true,
      recordId: "",
      showQrCode: false,
    };
  },
  computed: {
    ...mapState({
      token: (state) => state.login.token,
    }),
  },
  mounted() {
    this.getData();
  },
  methods: {
    handleSearch() {
      this.searchParams.nowPage = 1;
      this.getData();
    },
    handleCurrentChange(val) {
      this.searchParams.current = val;
      this.getData();
    },
    handleRecord(record) {
      this.recordId = record.id;
      console.log(record.id);
      this.isExam = false;
    },
    // 启用禁用
    handleStart(row) {
      this.$confirm("确定要启用吗？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          const params = {
            id: row.id,
            status: row.status == "0" ? "1" : "0",
          };
          enableExamination(params).then((res) => {
            if (res.data.status == 200) {
              this.$message({
                type: "success",
                message: "启用成功!",
              });
              this.getData();
            }
          });
        })
        .catch(() => {});
    },
    // 删除
    handleDelete(row) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          examinationDelete(row.id).then((res) => {
            if (res.data.status == 200) {
              this.$message({
                type: "success",
                message: "删除成功!",
              });
              this.getData();
            }
          });
        })
        .catch(() => {});
    },
    // 编辑 查看
    handleEdit(type, row) {
      this.dialogType = type;
      this.showDialog = true;
      if (row) {
        this.educationItem = row;
      }
    },
    handleSubmit() {
      this.showDialog = false;
      this.educationItem = {};
      this.getData();
    },
    handleQrCode(row) {
      this.showQrCode = true;
      this.educationItem = row;
    },
    async getData() {
      this.tableData = [];
      this.loading = true;
      const params = { ...this.searchParams };
      await examinationPage(params)
        .then((res) => {
          this.tableData = res.data.data.list;
          this.total = Number(res.data.data.total);
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.education-training {
  .search-container {
    min-width: 650px;
    display: flex;
    justify-content: flex-start;
    margin-bottom: 20px;

    .input {
      width: 200px;
    }

    > * {
      margin-right: 15px;
    }
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
