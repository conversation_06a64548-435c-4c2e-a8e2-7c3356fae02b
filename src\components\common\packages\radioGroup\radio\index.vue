<template>
  <div class="CA-radio">
    <div class="left" ref="box">
      <div
        class="right"
        :style="isChecked ? `background-color:${bgColorActive}` : ''"
        @click="handleClick"
        v-show="!isChecked"
      >
        <img :style="label.style" :src="label.src" alt="" />
      </div>
      <transition :name="isChecked ? 'el-fade-in-linear' : null">
        <div
          class="right"
          :style="isChecked ? `background-color:${bgColorActive}` : ''"
          v-show="isChecked"
        >
          <img :style="label.style" :src="labelTwo.src" alt="" />
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
export default {
  //import引入的组件
  name: "CARadio",
  components: {},
  data() {
    return {
      isChecked: false,
    };
  },
  props: {
    label: {
      type: Object,
    },
    labelTwo: {
      type: Object,
    },
    value: {
      type: String,
    },
    bgColorActive: {
      type: String,
      default: "#409eff",
    },
  },
  //方法集合
  methods: {
    checkIsActive(value) {
      if (this.value === value) {
        this.isChecked = true;
      } else {
        this.isChecked = false;
      }
    },
    handleClick() {
      this.isChecked = !this.isChecked;
      if (this.isChecked) {
        this.$parent.$emit("radioChange", this.value);
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style scoped lang="scss">
$height: 33px;
$width: 33px;
// .opacity-enter-active {
//   animation: opacity-in 0.2s;
// }
// .opacity-leave-active {
//   animation: opacity-in 0.2s reverse;
// }
// @keyframes opacity-in {
//   0% {
//     opacity: 0;
//   }
//   30% {
//     opacity: 0.3;
//   }
//   70% {
//     opacity: 0.7;
//   }
//   100% {
//     opacity: 1;
//   }
// }
.CA-radio {
  height: $height;
  width: $width;
  display: table-cell;
  vertical-align: center;
  .left {
    height: $height;
    width: $width;
    border-radius: 50%;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .left:hover {
    cursor: pointer;
  }
  .right {
    height: $height;
    width: $width;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .box-click {
    // background-color: #409eff;
    // border: 0.5px solid #cccccc;
  }
}
</style>