import axios from "axios";
import qs from "qs";
import store from "../store"; 
  
// 雷电预警列表
export const getLightWarningData = (data) => {
    return axios({
      method: "post",
      url: "/enterprise/lightningWarning/warningList",
      data: data,
    });
};
// 预警详情
export const lightningWarningDetail = (id) => {
  return axios({
    method: "post",
    url: '/enterprise/lightningWarning/warningData?crashNumber='+ id,
  
  });
};
// 设备详情
export const lightningWarningDeviceDetail = (id) => {
  return axios({
    method: "post",
    url: '/enterprise/lightningWarning/warningDevice?id='+ id,
 
  });
};
// 雷电预警导出
export const lightningWarningExport = table => {
  return axios({
    method: "post",
    url: '/enterprise/lightningWarning/exportExcel',
    data: { ...table },
    responseType: "arraybuffer",
  });
};

// 风险隐患预警列表
export const getRiskWarningData = (data) => {
  return axios({
    method: "post",
    url: "/gemp-chemical/api/gemp/riskMonitorWorkorder/page/v1",
    data: data,
  });
}
// 风险隐患预警详情
export const riskWarningDetail = (id) => {
  return axios({
    method: "post",
    url: `/gemp-chemical/api/gemp/riskMonitorWorkorder/id/v1?id=${id}`,
  });
}
