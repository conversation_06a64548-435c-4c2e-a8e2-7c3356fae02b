<template>
  <div>
    <!-- {{isEnterprise}}
    {{enterpriseId + '----------enterpriseId'}} -->
    <erpriseList
      v-show="isEnterprise === 'gov'"
      @entBool="entBool"
      @entBoolList="entBoolList"
      @entId="entId"
      ref="gov"
    ></erpriseList>
    <Management
      v-show="isEnterprise === 'park'"
      ref="ent"
      :parkId="enterpriseId"
      @type="type"
    >
    </Management>
  </div>
</template>

<script>
import { mapState } from "vuex";
import entManagement from "./parkManagement.vue";
import enterpriseList from "./parkManagement/parkOrgList";
import { getMapData } from "@/api/mergencyResources";
export default {
  name: "entAndGov",
  components: {
    Management: entManagement,
    erpriseList: enterpriseList,
  },
  data() {
    return {
      isEnterprise: "",
      active: "",
      enterpriseId: "",
      MapData: [],
    };
  },
  methods: {
    type(type) {
      this.isEnterprise = type;
    },
    entBool(type, name) {
      this.isEnterprise = "park";
      this.active = name; //parkOrgList传过来的parkEnterList
      this.$store.commit("login/updataParkActiveName", "basicInformationTab");
      this.$refs.ent.activeName='basicInformationTab'
    },
    entBoolList(type, name) {
      this.isEnterprise = "park";
      this.active = name; //parkOrgList传过来的parkEnterList
      this.$store.commit("login/updataParkActiveName", "parkEnterList");
      this.$refs.ent.activeName='parkEnterList'
    },
    // goTag(parkId, name) {   //视频跳转
    //   this.active = name //废弃，不通过这个设置选项卡
    //   if(name='videoInspection'){
    //     this.$store.commit("login/updataActiveName", "videoInspection");
    //   }else if(name='realTimeMonitoring'){
    //      this.$store.commit("login/updataActiveName", "realTimeMonitoring");
    //   }

    //   this.enterpriseId = parkId
    //   console.log(parkId)
    //   this.isEnterprise = 'ent'
    //   this.$nextTick(() => {
    //     this.$refs.ent.getData()
    //   })
    // },
    async getUrl(codeParkId) {
      let that=this
      var params = {
        id: codeParkId,
      };
      var MapDataRes = await getMapData(params);
      console.log(MapDataRes, "MapDataRes================================>");
      if (MapDataRes.status == 200 && MapDataRes.data.data) {
        console.log(that)
        that.MapData = MapDataRes.data.data;
        that.$refs.ent.MapData = that.MapData;
        console.log(that.$refs.ent)
      }
    },
    entId(id) {
      this.enterpriseId = id.id;
      let codeParkId = id.codeParkId;
      this.getUrl(codeParkId);
      this.$nextTick(() => {
        this.$refs.ent.getData();
      });
    },
  },
  create() {
    console.log(this.enterpriseId);
  },

  computed: {
    ...mapState({
      vuexUser: (state) => state.login.user,
      enterData: (state) => state.login.enterData,
      parkId: (state) => state.controler.vuexEntId,
    }),
  },
  watch: {
    vuexUser: {
      handler(newVal, oldVal) {
        if (newVal.user_type === "park") {
          this.isEnterprise = "park";
          this.enterpriseId = newVal.park_id;
          this.$nextTick(() => {
            this.$refs.ent.getData();
          });
        } else {
          this.isEnterprise = "gov";
          this.$nextTick(() => {
            this.$refs.gov.getData();
          });
        }
      },
      deep: true,
      immediate: true,
    },
    // enterData: {
    //   handler(newVal, oldVal) {
    //     if (newVal) {
    //       this.enterpriseId = newVal.parkId;
    //     }
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    // parkId: {
    //   handler(newVal, oldVal) {
    //     if (newVal) {
    //       this.entId(newVal)
    //       this.enterpriseId = newVal.parkId;
    //     }
    //   },
    //   deep: true,
    //   immediate: true
    // }
  },
};
</script>

<style scoped></style>
