<template>
  <div class="riskDy">
    <!-- <div class="risk-boxone"></div> -->
    <div class="risk-boxtwo">
      <!-- <h2>实时风险状态</h2> -->
      <div id="myChart" :style="{ width: '400px', height: '288px' }"></div>
    </div>
    <!-- <div class="risk-boxthree">
              <div id="myCharts" :style="{width: '570px', height: '230px'}"></div>
          </div> -->

    <!-- <div class="riskDy-bottom">
          <div class="risk-boone"></div>
          <div class="risk-botwo"></div>
      </div> -->
  </div>
</template>

<script>
// const echarts = require('echarts');
import {
  queryDistCodeCount, //区域查询风险等级企业统计
  getPreDistrict
} from "@/api/riskAssessment";
export default {
  //import引入的组件
  name: "riskDynamics",
  components: {},
  props:{
    distCodePro:[Number,String]
  },
  watch:{
    distCodePro:{
      handler(newVal,old){
        this.getEchartData(newVal)
      }
    }
  },

  data() {
    return {
      currentName:'',
      ajaxData: [],
      dataLink: [
        {
          source: "引发因素",
          target: "人为因素",
          value: 66,
        },
        {
          source: "引发因素",
          target: "自然因素",
          value: 88,
        },
        {
          source: "引发因素",
          target: "xx因素",
          value: 53,
        },
        {
          source: "引发因素",
          target: "yy因素",
          value: 44,
        },
        {
          source: "xx因素",
          target: "xx分析",
          value: 42,
        },
        {
          source: "yy因素",
          target: "yy分析",
          value: 67,
        },
        {
          source: "自然因素",
          target: "用户分析",
          value: 12,
        },
        {
          source: "自然因素",
          target: "话题分析",
          value: 18,
        },
        {
          source: "自然因素",
          target: "评论分析",
          value: 50,
        },
        {
          source: "人为因素",
          target: "图书分析",
          value: 23,
        },
        {
          source: "人为因素",
          target: "借阅分析",
          value: 19,
        },
        {
          source: "人为因素",
          target: "借阅排行",
          value: 6,
        },
        {
          source: "人为因素",
          target: "图书收录",
          value: 8,
        },
      ],
      dataSerise: [
        {
          name: "引发因素",
          symbolSize: 50,
          //   draggable: true,
          value: 0,
          category: 0,
          itemStyle: {
            normal: {
              borderColor: "#04f2a7",
              borderWidth: 4,
              shadowBlur: 10,
              shadowColor: "#04f2a7",
              color: "#001c43",
            },
          },
        },
      ],
      xdata: [
        "B4",
        "B3",
        "C4",
        "A1",
        "E2",
        "C1",
        "D1",
        "A3",
        "E3",
        "A2",
        "D2",
        "C2",
        "D3",
        "D4",
        "B2",
        "A4",
        "C3",
        "E1",
        "B1",
        "B2",
        "A4",
        "C3",
        "E1",
        "B1",
      ], //x轴
      dataArr: [
        400, 300, 200, 131.14, 500, 131.14, 100, 131.14, 500, 131.14, 100, 200,
        500, 700, 131.14, 400, 600, 131.14, 100, 131.14, 400, 600, 131.14, 300,
      ],
      dashedArr: [
        150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150, 150,
        150, 150, 150, 150, 150, 150, 150, 150, 150, 150,
      ],
      titleName: "红色风险",
      titleNames: "当前风险",
    };
  },
  //方法集合
  methods: {
    async getEchartData(districtId) {

        var preD = await getPreDistrict({
          districtId: districtId,
        });
        var currentName = preD.data.data.districtName;

      queryDistCodeCount({
        distCode: districtId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.ajaxData = res.data.data;
          this.drawLine(this.ajaxData,currentName);
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
    async drawLine(ajaxData,currentName) {
      var that=this;
     
      var datas = [
        {
          name: currentName,
          symbolSize: 80,
          // value: '',
          category: 0,
          itemStyle: {
            normal: {
              // borderColor: '#82dffe',
              // borderWidth: 4,
              // shadowBlur: 10,
              // shadowColor: '#04f2a7',
              color: "#001c43",
            },
          },
        },
        {
          name: "重大风险",
          symbolSize: 60,
          value: "0",
          category: 0,
          itemStyle: {
            normal: {
              // borderColor: '#ff0000',
              // borderWidth: 4,
              // shadowBlur: 10,
              // shadowColor: '#ff0000',
              // color: '#001c43',
              // backgroundColor:"#ff0000"
              color: "#f67854",
              // shadowBlur: 10,
              // shadowColor: "#333",
            },
          },
        },
        {
          name: "较大风险",
          symbolSize: 60,
          value: "0",
          itemStyle: {
            normal: {
              color: "#f78e48",
              // shadowBlur: 10,
              // shadowColor: "#333",
            },
          },
          category: 0,
        },
        {
          name: "一般风险",
          symbolSize: 60,
          value: "0",
          itemStyle: {
            normal: {
              color: "#fcc35b",
              // shadowBlur: 10,
              // shadowColor: "#333",
            },
          },
          category: 0,
        },
        {
          name: "低风险",
          symbolSize: 60,
          value: "0",
          category: 0,
          itemStyle: {
            normal: {
              color: "#477efa",
              // shadowBlur: 10,
              // shadowColor: "#333",
            },
          },
        },
      ];

      datas.forEach((item) => {
        var obj = ajaxData.find((el) => item.name == el.riskLevelName);
        item.obj = obj;
      });

      datas.forEach((item) => {
        item.value = item.obj ? item.obj.riskCount : "";
      });

      var linkmes = [
        {
          source: currentName,
          target: "重大风险",
        },
        {
          source:  currentName,
          target: "较大风险",
        },
        {
          source:  currentName,
          target: "一般风险",
        },
        {
          source:  currentName,
          target: "低风险",
        },
      ]; //连接的信息

      // 基于准备好的dom，初始化echarts实例
      let myChart = this.$echarts.init(document.getElementById("myChart"));

      var option = {
        // backgroundColor: '#1a4377',
        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: "quinticInOut",
        color: ["#83e0ff", "#45f5ce", "#b158ff"],
        series: [
          {
            type: "graph",
            layout: "force",
            force: {
              repulsion: 1000,
              edgeLength: 80,
            },
            roam: true,
            label: {
              normal: {
                show: true,
              },
            },
            data: datas,
            links: linkmes,
            lineStyle: {
              normal: {
                opacity: 0.2,
                width: 2,
                curveness: 0.3,
              },
            },
            categories: [
              {
                name: "0",
              },
              {
                name: "1",
              },
              {
                name: "2",
              },
            ],
          },
        ],
      };
      // debugger;
      myChart.setOption(option);
      myChart.on("click", function (params) {   
        if (params.name != null) {
          var qiYe = params.data.obj
            ? params.data.obj.cimRiskCompanyDTOList
            : [];

          if (params.data.obj==null) {
            that.$router.push({
              path: "/gardenEnterpriseManagement/entManagement",
            });
            that.$store.commit("login/updataActiveName", "riskDynamics");
            that.$store.commit(
              "controler/updateEntId",
              params.data.enterpId
            );
          } else {
            if (params.name == "一般风险") {
              //更新节点
              qiYe.forEach((item) => {
                datas.push({
                  name: item.enterpName,
                  enterpId: item.enterpId,
                  symbolSize: 30,
                  category: 0,
                  itemStyle: {
                    normal: {
                      color: "#fcc35b",
                      // shadowBlur: 10,
                      // shadowColor: '#333'
                    },
                  },
                });

                //更新边的关系
                linkmes.push({
                  source: item.enterpName,
                  target: "一般风险",
                });
              });

              //重新画图
              myChart.setOption({
                series: [
                  {
                    data: datas,
                    links: linkmes,
                  },
                ],
              });
            } else if (params.name == "低风险") {
              //更新节点
              qiYe.forEach((item) => {
                datas.push({
                  name: item.enterpName,
                  enterpId: item.enterpId,
                  symbolSize: 30,
                  category: 0,
                  itemStyle: {
                    normal: {
                      color: "#447bf9",
                      // shadowBlur: 0,
                      // shadowColor: '#447bf9'
                    },
                  },
                });

                //更新边的关系
                linkmes.push({
                  source: item.enterpName,
                  target: "低风险",
                });
              });

              //重新画图
              myChart.setOption({
                series: [
                  {
                    data: datas,
                    links: linkmes,
                  },
                ],
              });
            } else if (params.name == "重大风险") {
              //
              //更新节点
              qiYe.forEach((item) => {
                datas.push({
                  name: item.enterpName,
                  enterpId: item.enterpId,
                  symbolSize: 30,
                  category: 0,
                  itemStyle: {
                    normal: {
                      color: "#e86e4d",
                      // shadowBlur: 0,
                      // shadowColor: '#e86e4d'
                    },
                  },
                });

                //更新边的关系
                linkmes.push({
                  source: item.enterpName,
                  target: "重大风险",
                });
              });

              //重新画图
              myChart.setOption({
                series: [
                  {
                    data: datas,
                    links: linkmes,
                  },
                ],
              });
            } else if (params.name == "较大风险") {
              //更新节点
              qiYe.forEach((item) => {
                datas.push({
                  name: item.enterpName,
                  enterpId: item.enterpId,
                  symbolSize: 30,
                  category: 0,
                  itemStyle: {
                    normal: {
                      color: "#f99a51",
                      // shadowBlur: 0,
                      // shadowColor: '#f99a51'
                    },
                  },
                });

                //更新边的关系
                linkmes.push({
                  source: item.enterpName,
                  target: "较大风险",
                });
              });

              //重新画图
              myChart.setOption({
                series: [
                  {
                    data: datas,
                    links: linkmes,
                  },
                ],
              });
            }
          }
        }
      });
    },
    //生命周期 - 挂载完成（可以访问DOM元素）
  },
  mounted() {
    // debugger;
    this.getEchartData(this.$store.state.login.userDistCode);
    // this.initCanv();
  },
};
</script>
<style lang="scss" scoped>
.riskDy {
}
</style>