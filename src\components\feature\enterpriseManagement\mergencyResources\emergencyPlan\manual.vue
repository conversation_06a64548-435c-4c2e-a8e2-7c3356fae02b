<template>
  <div class="historyList">
    <el-dialog title="工作手册信息"
               :visible.sync="show"
               width="1200px"
               @close="closeBoolean()"
               top="10vh"
               :close-on-click-modal="false"
               v-dialog-drag>
      <div class="seach-part">
        <div class="l">
          <span v-if="roleInfo.user_type == 'gov'">
             <el-button type="primary"  v-if='isFlag!=1'
                     size="medium"
                     class="btn"
                     @click="addManual">新增工作手册</el-button>
          </span>
          <el-button type="primary" v-else
                     size="medium"
                     class="btn"
                     @click="addManual">新增工作手册</el-button>
        </div>
      </div>
      <el-table :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                v-loading="loading"
                :data="tableData"
                style="width: 100%"
                border>
        <el-table-column prop="handbookName"
                         label="手册名称"
                         align="center"
                         :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column prop="verno"
                         label="手册版本号"
                         align="center" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="编制单位"
                         align="center"
                         prop="orgName" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="发布单位"
                         align="center"
                         prop="publicOrgName" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="负责人"
                         align="center"
                         prop="respper" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="办公电话"
                         align="center"
                         prop="officePhone" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="操作"
                         align="center">
          <template slot-scope="scope">
           
           <span v-if="roleInfo.user_type == 'gov'">
               <span @click="edit(scope.row)"  v-if='isFlag!=1'
                  style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">编辑</span>
           </span>
           <span @click="edit(scope.row)"  v-else
                  style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">编辑</span>

            <span v-if="roleInfo.user_type == 'gov'">
               <span @click="deleter(scope.row)" v-if='isFlag!=1'
                  style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">删除</span>
            </span>
          
            <span @click="deleter(scope.row) " v-else
                  style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">删除</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
                       :current-page.sync="currentPage"
                       background
                       layout="total, prev, pager, next"
                       :total="total">
        </el-pagination>
      </div>
    </el-dialog>
    <!-- 新增编辑查看 -->
    <el-dialog :title="title"
               :visible.sync="open"
               width="900px"
               :close-on-click-modal="false"
               :append-to-body="false">
      <el-form ref="form"
               :model="form"
               :rules="rules"
               :disabled="disabled"
               label-width="150px"
               :hide-required-asterisk="disabled">
        <div class="form_item">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="手册名称"
                              prop="handbookName">
                  <el-input v-model.trim="form.handbookName"
                            placeholder="请输入手册名称"
                            maxlength="50" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手册版本号">
                  <el-input v-model.trim="form.verno"
                  maxlength="20"
                            placeholder="请输入手册版本号" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="编制单位">
                  <el-input :disabled="true"
                            v-model.trim="form.orgName"
                            placeholder="请输入编制单位" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发布单位">
                  <el-input :disabled="true"
                            v-model.trim="form.publicOrgName"
                            placeholder="请输入发布单位" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="负责人">
                  <el-input v-model.trim="form.respper"
                  maxlength="20"
                            placeholder="请输入负责人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="办公电话" prop="officePhone">
                  <el-input v-model.trim="form.officePhone"
                 
                            placeholder="请输入办公电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="移动电话" prop="mobilePhone">
                  <el-input v-model.trim="form.mobilePhone"
                 
                            placeholder="请输入移动电话" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="住宅电话" prop="homePhone">
                  <el-input v-model.trim="form.homePhone"
                   
                            placeholder="请输入住宅电话" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="附件">
                  <!-- <file-cell :filelist="form.attachmentList"></file-cell> -->
                  <AttachmentUpload :attachmentlist="form.attachmentList"
                                    :limit="1"
                                    type="office"
                                    v-bind="{}"
                                    :editabled="disabled"></AttachmentUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="!disabled"
           slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHandbookList,
  detailPlanData,
  addHandbookList,
  editHandbookList,
  detailhandbookList,
  deletehandbookList
} from '@/api/mergencyResources'
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
export default {
  //import引入的组件
  name: 'manual',
  components: {
    AttachmentUpload
  },
  data() {
    return {
      isFlag:'',
      disabled: false,
      show: false,
      total: 0,
      planId: '',
      currentPage: 1,
      size: 10,
      loading: false,
      open: false,
      title: '新增工作手册',
      tableData: [],
      form: {
        handbookName: '',
        orgcode: '',
        orgName: '',
        respper: '',
        mobilePhone: '',
        verno: '',
        publicOrgcode: '',
        publicOrgName: '',
        officePhone: '',
        homePhone: '',
        attachmentList: [],
        sourceDeptcode: ''
      },
      rules: {
        handbookName: [
          { required: true, message: '请输入工作手册名称', trigger: 'blur' }
        ],
        officePhone: [
          {
            // required: true,
            // message: '请输入办公电话',
            // trigger: 'blur'
          },
          {pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,message: '请输入正确的电话号码'}
        ],
        mobilePhone: [
          {
            // required: true,
            // message: '请输入移动电话',
            // trigger: 'blur'
          },
          {pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,message: '请输入正确的移动电话'}
        ],
        //homePhone
        homePhone: [
          {
            // required: true,
            // message: '请输入住宅电话',
            // trigger: 'blur'
          },
          {pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/,message: '请输入正确的电话号码'}
        ],
        



      },
      roleInfo: {}
    }
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val
    },
    getList(id) {
      this.planId = id
      this.loading = true
      getHandbookList({
        nowPage: this.currentPage,
        pageSize: this.size,
        planId: id
      }).then(res => {
        if (res.data.status == 200) {
          this.loading = false
          this.tableData = res.data.data.list
          this.total = res.data.data.total
        }
      })
    },
    getDetaildata(id,getListsourceDeptFlag) {    
      this.isFlag=getListsourceDeptFlag
      console.log(getListsourceDeptFlag,'getListsourceDeptFlag')
      detailPlanData({
        planId: id
      }).then(res => {
        if (res.data.status == 200) {
          // this.form = res.data.data;
          this.form.publicOrgcode = res.data.data.publishOrgCode
          this.form.publicOrgName = res.data.data.publishOrgName
          this.form.orgcode = res.data.data.establishOrgCode
          this.form.orgName = res.data.data.establishOrgName
          this.form.sourceDeptcode = res.data.data.sourceDeptCode;
          console.log(this.form,'接口赋值')
        }
      })
    },
    addManual() {
      this.form.handbookName=''
      this.form.respper=''
      this.form.mobilePhone=''
      this.form.verno=''
      this.form.officePhone=''
      this.form.homePhone=''
      this.form.attachmentList=[]
      this.open = true;
      console.log('新增前清空888888888888888888', this.form)
    },
    edit(row) {
      this.reset()
      this.open = true
      this.disabled = false
      this.title = '编辑工作手册'
      detailhandbookList({
        id: row.handbookId
      }).then(res => {
        if (res.data.status == 200) {
          this.$set(this, 'form', res.data.data)
          console.log('888888888888888888', this.form)
        }
      })
    },
    reset() {     
      this.form = {
        handbookName: '',
        orgcode: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_code : this.form.orgcode,
        orgName: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_name : this.form.orgName,
        respper: '',
        mobilePhone: '',
        verno: '',
        publicOrgcode: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_code : this.form.publicOrgcode,
        publicOrgName: this.roleInfo.user_type === 'ent' ? this.roleInfo.org_name : this.form.publicOrgName,
        officePhone: '',
        homePhone: '',
        attachmentList: [],
        sourceDeptcode:this.form.sourceDeptcode
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
        // this.form.sourceDeptcode=this.sourceDeptcode
      }
    },
    submitForm() {
      this.$refs.form.validate(flag => {
        if (flag) {
          if (this.form.handbookId) {
            editHandbookList(this.form).then(res => {
              if (res.data.status == 200) {
                this.$message.success('操作成功')
                this.open = false
                this.reset()
                this.getList(this.planId)
              } else {
                this.$message.error(res.data.msg)
              }
            })
          } else {
            this.form.planId = this.planId           
            addHandbookList(this.form).then(res => {
              if (res.data.status == 200) {
                this.$message.success('操作成功')
                this.open = false
                this.reset()
                this.getList(this.planId)
              } else {
                this.$message.error(res.data.msg)
              }
            })
          }
        }
      })
    },
    deleter(row) {
      const id = row.handbookId
      this.$confirm('是否确认删除该工作手册信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deletehandbookList({
            planHandbookId: id
          }).then(res => {
            if (res.data.status == 200) {
              this.$message.success('删除成功')
                if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getList(this.planId)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    cancel() {
      this.open = false
      this.reset()
    },
    handleSizeChange() {},
    handleCurrentChange(val) {
      this.currentPage = val
      this.getList(this.planId)
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {   
    // console.log(111)
    this.roleInfo = JSON.parse(sessionStorage.getItem('VueX_local')).root.login.user || {}
    if (this.roleInfo.user_type === 'ent') { // 企业用户编制单位、发布单位为自己单位
      this.form.orgCode = this.roleInfo.org_code;
      this.form.publishOrgCode = this.roleInfo.org_code;
      this.form.orgName = this.roleInfo.org_name;
      this.form.publicOrgName = this.roleInfo.org_name;
    }
  }
}
</script>
<style lang="scss" scoped>
.historyList {
  .seach-part {
    font-weight: 600;
    // padding: 0 10px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 500px;
      display: flex;
      justify-content: space-between;
      > div {
        margin-right: 15px;
        width: 200px;
      }
      > .btn {
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>