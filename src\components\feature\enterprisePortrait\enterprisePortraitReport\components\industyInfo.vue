<!--行业分类信息-->
<template>
  <div class="table">
        <ul class="container">
          <li><span class="li-left">企业隶属行政区域</span><span class="value">{{ industyInfo.distStr }}</span></li>
          <li><span class="li-left">企业规模</span><span class="value">{{ industyInfo.scaleStr }}</span></li>
          <li v-for="item in industyInfo.indusTypeDTOList" :key="item"><span class="li-left">国民经济分类（主营）</span>
            <span class="value">{{ item.indusTypeStr }}</span></li>
      </ul>
      <div class="row">
        <div class="previewItemTwoTitle">主要工艺流程</div>
      </div>
      <div class="rowbusiAdd">
        <div
          class="busiAddrItem"
          v-for="(item, index) in busiAddrList"
          :key="index"
          @click="searchFlowchart(index)"
        >
          <h4 :title="item.name">{{ item.name }}</h4>
          <div class="busiAddrItemContent">
            <div class="imgdemo"></div>
          </div>
        </div>
      </div>
  </div>
</template>

<script>
import {reportDetail,changePreview} from "../mock";
export default {
  components:{},
  data() {
    return {
      previewInfo:reportDetail.data,
      busiAddrList:[],
      changePreviewInfo: changePreview,
      industyInfo:{}
    };
  },
  mounted() {
    this.getData()
  },
  methods:{
    getData(){
      this.busiAddrList = JSON.parse(reportDetail.data.enterpriseInfoDTO.htmlText);
      this.industyInfo = reportDetail.data.enterpriseInfoDTO;
    },
    // 查看流程图
    searchFlowchart(index) {
      const activeIndex = index;
      //this.$router.push({ path: '/statistics/Flowserch', query: {enterpriseId: this.priseId,type:this.$route.query.type,activeIndex,cornumber:this.$route.query.cornumber} })
    },
    getIndusTypeDTOList(index){
      const that = this;
      let keyItem = null;
      let text = {
        secondVal: ''
      }
      this.changePreviewInfo.some(item => {
        if(item.fieldName === 'indusTypeDTOList'){
          keyItem = item;
        }
      });
      if(keyItem&&(keyItem.secondVal instanceof Array)&&this.changePreviewInfo.length>0) {
        if(!keyItem.secondVal[index]) return;
        let flag = false;
        if(keyItem.secondVal[index].indusTypeStr === keyItem.firstVal[index].indusTypeStr) {
          flag = true;
        }
        if(flag) {
          return false;
        } else {
          text.secondVal = keyItem.secondVal[index].indusTypeStr;
        }
      } else {
        return false;
      }
      return text;
    },
    // 判断是否变更
    ischangeMethods(key,index) {
      const that = this;
      let keyItem = {}
      if(key instanceof Array) {
        let content = [];
        let obj = {
          secondVal:'',
        }
        key.forEach(itemkey=>{
          that.changePreviewInfo.some(item => {
            if(item.fieldName === itemkey){
              content.push(item);
            }
          });
        })
        if(content.length === 1) {
          content.forEach(item=>{
            if(item.fieldName==='aboveDesignatedTwo') {
              obj.secondVal= this.previewInfo.aboveDesignated + this.getGrade(item.secondVal);
              keyItem = obj
            } else {
              let secondValText = ''
              item.secondVal=== '1' ? secondValText= '规上企业' : secondValText= '规下企业'
              obj.secondVal= secondValText + this.getGrade(this.previewInfo.enterpriseInfoDTO.aboveDesignatedTwo);
              keyItem = obj
            }
          })
        } else if(content.length === 2) {
          let textone = '';
          let textTwo = '';
          content.forEach(item=>{
            if(item.fieldName==='aboveDesignatedTwo') {
              textone = this.getGrade(item.secondVal);
            } else {
              if(item.secondVal){
                textTwo =  item.secondVal=== '1' ? keyItem.secondVal = '规上企业' : keyItem.secondVal= '规下企业';
              } else {
                textTwo =  '';
              }
            }
          })
          obj.secondVal= textTwo + textone;
          keyItem = obj
        } else {
          return false
        }
        return keyItem;
      } else {
        this.changePreviewInfo.some(item => {
          if(item.fieldName === key){
            keyItem = item;
          }
        });
        let flag = this.changePreviewInfo.some(item => {
          if(item.fieldName === key){
            keyItem = item;
            return true
          }
        });
        if(key === 'startTime'&&keyItem&&keyItem.secondVal) {
          keyItem.secondVal=this.format(new Date(keyItem.secondVal))
        }
        if(flag) {
          return keyItem;
        } else {
          return false;
        }
      }
    },
    getGrade(item) {
      let text = '';
      switch (item) {
        case '1':
          text = '（大型）'
          break;
        case '2':
          text = '（中型）'
          break;
        case '3':
          text = '（小型）'
          break;
        case '4':
          text = '（微型）'
          break;
        default:
          break;
      }
      return text;
    },
    format(Date){
      let Y = Date.getFullYear();
      let M = Date.getMonth() + 1;
      M = M < 10 ? '0' + M : M;// 不够两位补充0
      let D = Date.getDate();
      D = D < 10 ? '0' + D : D;
      let H = Date.getHours();
      H = H < 10 ? '0' + H : H;
      let Mi = Date.getMinutes();
      Mi = Mi < 10 ? '0' + Mi : Mi;
      let S = Date.getSeconds();
      S = S < 10 ? '0' + S : S;
      // const name = Y + '-' + M + '-' + D + ' ' + H + ':' + Mi + ':' + S;
      const name = Y + '-' + M + '-' + D
      return name.includes('NaN') ? '' : name
    },
  }
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  height: 460px;
  overflow: auto;
  .container{
    width: 100%;
    li{
      min-height: 40px;
      display: flex;
      align-items: center;
      color: #444;
      font-size: 14px;
      border: 1px solid rgb(231, 231, 231);

      & ~ li{
        border-top: none;
      }
      .li-left {
            width: 180px;
            min-height: 40px;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
            border-right: 1px solid rgb(231, 231, 231);
          }
      .value{
        padding-left: 10px;
        color: #000;
        word-break: break-all;
      }
    }
  }
  .title {
  width: 100%;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 18px;
  display: flex;
  justify-content: space-between;
}
    .tabTitle {
      font-size: 18px;
      color: #49445f;
      font-weight: bold;
      font-family: "Microsoft Ya Hei";
    }

  .tabTitle-line {
    border-radius: 2px;
    border-bottom: 4px solid #415ef0;
    display: inline-block;
    line-height: 32px;
  }
  .previewItemTwoTitle {
    font-size: 16px;
    font-family: "Microsoft Ya Hei";
    font-stretch: normal;
    line-height: 40px;
    letter-spacing: 0px;
    color: #1b8bec;
  }
  .rowbusiAdd{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    width: 100%;
    min-height: 48px;
    padding: 0px 10px;
  }
  .busiAddrItem {
    width: 240px;
    height: 185px;
    color: #444;
    margin-right: 22px;
    margin-bottom: 18px;
    position: relative;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-size:100% 100% ;
    cursor: pointer;
    &:nth-of-type(5n){
      margin-right: 0;
    }
    h4 {
      width: calc(100% - 2px);
      margin: 1px auto;
      line-height: 40px;
      background-color: #f3f8fa;
      border-radius: 4px 4px 0 0;
      padding-left: 10px;
      padding-right: 52px;
      font-size: 16px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
    }
    .busiAddrItemContent {
      height: 148px;
      padding: 10px;
      .imgdemo {
        margin:  0 auto;
        cursor: pointer;
        width: 237px;
        height: 140px;
        /*background: url("@{imgUrl}/imgdemo.png") no-repeat;*/
      }
    }
    &.addBusiAddrBox {
      /*background: url("@{imgUrl}/item-add-bg.png") no-repeat;*/
      .operationBtn {
        position: absolute;
        right: 10px;
        top: 0;
        line-height: 50px;
        font-size: 16px;
        span {
          cursor: pointer;
          &:nth-of-type(1){
            color: #ff5555;
            margin-right: 10px;
          }
          &:nth-of-type(2){
            color: #086dd4;
          }
        }
      }
      .nameAndCode {
        margin: 5px 0;
      }
      .address {
        margin-top: 5px;
      }
    }
  }

}
</style>
