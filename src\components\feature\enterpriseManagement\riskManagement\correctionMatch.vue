<template>
  <el-dialog
    title="校正系统匹配表"
    :visible.sync="iscorrectionMatch"
    width="800px"
    top="20vh"
    @close='CloseClear'
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
     :destroy-on-close='false'
  >
    <div>
      <div class="hs_box">
        <h2>当前危险化学品：{{ preData.title }}</h2>

        <button
          @click="addFn"
          type="button"
          class="el-button el-button--primary el-button--small"
          v-if="StorageTank.length < 1"
        >
          <!----><!----><span>添加</span>
        </button>
      </div>
<!-- {{StorageTank}} -->
      <div class="M_box">
        <el-scrollbar class="linShiHeight">
          <el-table
            :data="StorageTank"
            style="width: 100%"
            :highlight-current-row="false"
            border
          >
            <!-- <el-table-column prop="storageTankNUM" label=" " width="90">
                  <template slot-scope="scope">
                    储罐{{ scope.$index + 1 }}
                  </template>
                </el-table-column> -->
            <el-table-column prop="title" label="危险类别" width="140">
              
              <template slot-scope="scope">
               <el-select
                  @input="MatchFn(scope.row.chemicalId, scope.row)"
                  v-model="scope.row.chemicalId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in eOption"
                    :key="item.chemicalId"
                    :label="item.title"
                    :value="item.chemicalId"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="casNo" label="符号" width="100">
              <!-- <template slot-scope="scope">
              <div class="tableInputFelxBox">
                <div class="tableInputFelxBox_left">
                  <el-input type="text" v-model.trim="scope.row.casNo" />
                </div>
              </div>
            </template> -->
            </el-table-column>
            <el-table-column prop="note" label="说明描述" width="/">
              <template slot-scope="scope">
                <div class="tableInputFelxBox">
                  <div class="tableInputFelxBox_left">
                    <span
                      ><el-input
                        type="textarea"
                        :rows="2"
                        v-model.trim="scope.row.note"
                    /></span>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="correction" label="临界量/t" width="100">
              <!-- <template slot-scope="scope">
              <div class="tableInputFelxBox">
                <div class="tableInputFelxBox_left">
                  <el-input type="text" v-model.trim="scope.row.correction" />
                </div>
              </div>
            </template> -->
            </el-table-column>
            <!-- <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <div class="deteleStyle" @click="deteleFn(index)">删除</div>
            </template>
          </el-table-column> -->
          </el-table>
        </el-scrollbar>
      </div>
    </div>

    <div slot="footer" style="display: flex; justify-content: center">
      <!-- <el-button size="mini">取 消</el-button> -->

      <el-button size="mini" type="primary" @click="submitMatch()"
        >提 交</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { generalChemicalsIndex } from "@/api/riskAssessment";
export default {
  data() {
    return {
      eOption: [],
      preData: {},
      correction: "",
      iscorrectionMatch: false, //临界量匹配表弹框
      StorageTank: [
        {
          title: "",
          casNo: "",
          note: "",
          correction: "",

             alias:"",
          chemicalId:"",
          createTime:"",
          dataType:"",
          deleteFlag:"",         
        },
      ],
    };
  },
  watch: {
    // radioTop(newVal, oldVal) {
    //   if (newVal == "fixed") {
    //     this.isTab = true;
    //   } else {
    //     this.isTab = false;
    //   }
    // },
  },
  methods: {
    CloseClear(){
    //  this.StorageTank=[]
    },
    submitMatch() {     
      var data=this.StorageTank;    
      this.$emit("submitMatch1", data);
      this.iscorrectionMatch = false;
    },
    MatchFn(id, row) { 
      console.log(id,'选择id',row)     
      if (this.eOption.length > 0) {
        this.eOption.forEach((item) => {
          if (item.chemicalId == id) {
           
            this.correction = item.correction;
            row.note = item.note;
            row.correction = item.correction;
            row.casNo = item.casNo;
            row.chemicalId=item.chemicalId;
            row.title=item.title;
            row.alias=item.alias;
            row.createTime=item.createTime;
            row.dataType=item.dataType;
            row.deleteFlag=item.deleteFlag;
          }
        });
      }      
    },
    initData(row, index) {
      this.preData = row;
      this.rowIndex = index;
     console.log(row.correctionValueList,'这是回填')
     this.StorageTank = row.correctionValueList || [];
     this.StorageTank.rowIndex = index;     
      generalChemicalsIndex().then((res) => {
        if (res.data.status === 200) {
          this.eOption = res.data.data || [];
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //无对应数据弹框
    popNoData(val) {
      this.iscorrectionMatch = val;
    },
    addFn() {
      let params = {
        title: "",
        casNo: "",
        note: "",
        correction: "",
           alias:"",
          chemicalId:"",
          createTime:"",
          dataType:"",
          deleteFlag:"",
      };
      this.StorageTank.push(params);
    },
    deteleFn(val) {
      this.StorageTank.splice(val, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-tab-pane {
  height: 500px;
  overflow: auto;
  // height: calc(100%- 200px);
}
/deep/ .el-date-editor.el-input {
  width: auto;
}
.hs_box {
  display: flex;
  justify-content: space-between;
  margin: 0 0 10px 0;
}
.M_box {
  .m_tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 10px 0;
  }
}
</style>