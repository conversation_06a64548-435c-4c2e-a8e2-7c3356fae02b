<template>
  
    <el-dialog
      title="
重大危险源详情"
      :visible.sync="show"
      width="1350px"
      top="5vh"
      height="70vh"
      @close="closeBoolean(false)"
      :destroy-on-close="true"
      append-to-body
      :close-on-click-modal="false"
    >
    <div class="hazards" v-loading="loading">
      <div class="dialog">
        <div class="div1">
          <div class="title">重大危险源</div>
          <div class="table">
            <ul class="container">
              <li>
                <div class="l">企业名称</div>
                <div class="r">{{ dangerDetails.enterpName }}</div>
              </li>
              <li>
                <div class="l">企业编码</div>
                <div class="r">{{ dangerDetails.enterpId }}</div>
              </li>
              <li class="liLine">
                <div class="l">* 统一社会信用代码</div>
                <div class="r">{{ dangerDetails.entcreditCode }}</div>
              </li>
              <li>
                <div class="l">* 重大危险源名称</div>
                <div class="r">{{ dangerDetails.dangerName }}</div>
              </li>
              <li>
                <div class="l">重大危险源编码</div>
                <div class="r">{{ dangerDetails.dangerId }}</div>
              </li>
              <li class="liLine">
                <div class="l">* 重大危险源等级</div>
                <div class="r" v-if="dangerDetails.level == null"></div>
                <div class="r" v-if="dangerDetails.level == 1">一级</div>
                <div class="r" v-if="dangerDetails.level == 2">二级</div>
                <div class="r" v-if="dangerDetails.level == 3">三级</div>
                <div class="r" v-if="dangerDetails.level == 4">四级</div>
              </li>
              <li>
                <div class="l">* R值</div>
                <div class="r">{{ dangerDetails.rvalue }}</div>
              </li>
              <li class="lang">
                <div class="l">* 重大危险源分类</div>
                <div class="r">{{ dangerDetails.hazardClassify }}</div>
                <!-- <div class="r" v-if="dangerDetails.hazardClassify == 0">罐区</div>
                <div class="r" v-else-if="dangerDetails.hazardClassify == 1">装置</div>
                <div class="r" v-else-if="dangerDetails.hazardClassify == 2">库区</div>
                <div class="r" v-else> </div> -->
              </li>
              <li>
                <div class="l">* 是否配备紧急停车系统（ESD）</div>
                <div class="r" v-if="dangerDetails.isEmergencyStop != null">
                  {{ dangerDetails.isEmergencyStop == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li>
                <div class="l">* 是否配备化工自动化仪表控制系统</div>
                <div class="r" v-if="dangerDetails.isChemicalSystem != null">
                  {{ dangerDetails.isChemicalSystem == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li class="liLine">
                <div class="l">配备的控制系统类型</div>
                <div class="r" v-if="dangerDetails.controlSystemType == '01'">集散控制系统（DCS）</div>
                <div class="r" v-else-if="dangerDetails.controlSystemType == '02'">可编程逻辑器件系统（PLC）</div>
                <div class="r" v-else> </div>
              </li>
              <li>
                <div class="l">* 是否配备独立安全仪表（SIS）</div>
                <div class="r" v-if="dangerDetails.isSafeInstrument != null">
                  {{ dangerDetails.isSafeInstrument == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li>
                <div class="l">* 是否完善监控设施</div>
                <div class="r" v-if="dangerDetails.isMonitorFacility != null">
                  {{ dangerDetails.isMonitorFacility == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li class="liLine">
                <div class="l">* 是否在城区</div>
                <div class="r" v-if="dangerDetails.isUrbanArea != null">
                  {{ dangerDetails.isUrbanArea == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li>
                <div class="l">* 投用日期</div>
                <div class="r">{{ dangerDetails.useTime }}</div>
              </li>
              <li>
                <div class="l">经度</div>
                <div class="r">{{ dangerDetails.longitude }}</div>
              </li>
              <li class="liLine">
                <div class="l">纬度</div>
                <div class="r">{{ dangerDetails.latitude }}</div>
              </li>
              <li class="bottom">
                <div class="l">* 是否涉及毒性气体、液化气体、剧毒液体</div>
                <div class="r" v-if="dangerDetails.isInvolvingToxic != null">
                  {{ dangerDetails.isInvolvingToxic == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li class="bottom">
                <div class="l">* 周边防护目标最近距离（米）</div>
                <div class="r">{{ dangerDetails.littledistance }}</div>
              </li>
              <li class="liLine bottom">
                <div class="l">* 外边界500米范围人数估算</div>
                <div class="r">{{ dangerDetails.people500M }}</div>
              </li>
            </ul>
          </div>
        </div>


          <div class="div1">
          <div class="title">主要负责人</div>
            <div class="table">
            <ul class="container">
               <li>
                <div class="l">主要负责人</div>
                <div class="r">{{dangerDetails.principal}}</div>
              </li>
              <li>
                <div class="l">主要负责人联系方式</div>
                <div class="r">{{dangerDetails.principalContact}}</div>
              </li>
              <li>
                <div class="l">主要负责人职务</div>
                <div class="r">{{dangerDetails.principalPost}}</div>
              </li>
              <li class="">
                <div class="l">技术负责人</div>
                <div class="r">{{dangerDetails.technician}}</div>
              </li>
                <li class="">
                <div class="l">技术负责人联系方式</div>
                <div class="r">{{dangerDetails.technicianContact}}</div>
              </li>
                <li class="">
                <div class="l">技术负责人职务</div>
                <div class="r">{{dangerDetails.technicianPost}}</div>
              </li>



              <li class="bottom">
                <div class="l">操作负责人</div>
                <div class="r">{{dangerDetails.operator}}</div>
              </li>
                <li class="bottom">
                <div class="l">操作负责人联系方式</div>
                <div class="r">{{dangerDetails.operatorContact}}</div>
              </li>
                <li class="bottom">
                <div class="l">操作负责人职务</div>
                <div class="r">{{dangerDetails.operatorPost}}</div>
              </li>


      
            </ul>
          </div>

        </div>






        <div class="div2">
          <div class="title">危险化学品</div>
          <div class="table" v-if="hazarchemSaiSi.length>0">
            <ul class="container header">
              <li>* 危险化学品名称</li>
              <li>化学品别名</li>
              <li>CAS号</li>
              <li>化学品属性</li>
              <li>* 设计储存量(吨/标方)</li>
              <li class="liLine">年产量/消耗量(吨/标方)</li>
            </ul>
            <ul
              class="container"
              v-for="(item, index) in hazarchemSaiSi"
              :key="index"
            >
              <li>{{ item.hazarchemName }}</li>
              <li>{{ item.chemicalAlias }}</li>
              <li>{{ item.casNo }}</li>
              <!-- <li>{{ item.hazarchemProperty }}</li> -->
              <li v-if="item.hazarchemProperty == 1">产品</li>
              <li v-else-if="item.hazarchemProperty == 2">中间产品</li>
              <li v-else-if="item.hazarchemProperty == 4">原料</li>
              <li v-else></li>
              <li>{{ item.storageNum }}</li>
              <li class="liLine">{{ item.annualUsageMeal }}</li>
            </ul>
          </div>
          <div class="table" v-else>
            <ul class="container header">
              <li>* 危险化学品名称</li>
              <li>化学品别名</li>
              <li>CAS号</li>
              <li>化学品属性</li>
              <li>* 设计储存量(吨/标方)</li>
              <li class="liLine">年产量/消耗量(吨/标方)</li>
            </ul>
            <ul
              class="container"
            >
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <!-- <li></li>
              <li></li>
              <li></li> -->
              <li class="liLine"></li>
            </ul>
          </div>
        </div>


      


      </div>
      </div>
    </el-dialog>
  
</template>

<script>
import { getInformationInfoDanger } from "@/api/entList";
export default {
  //import引入的组件
  name: "hazards",
  components: {},
  data() {
    return { show: false, dangerDetails: {}, hazarchemSaiSi: {} ,loading:false};
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val;
       this.dangerDetails = {};
        this.hazarchemSaiSi = {};
    },
    getData(id) {
      this.loading = true;
      getInformationInfoDanger(id).then((res) => {
        this.dangerDetails = res.data.data.dangerDetails;
        this.hazarchemSaiSi = res.data.data.hazarchemSaiSi;
        this.loading = false;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.dialog {
  height: 70vh;
  overflow: auto;
}
.hazards {
  overflow: auto;
  color: #000;

  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 66.6%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 24.9%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 74%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: center;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
  .div2 {
    margin-top: 20px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      font-size: 18px;
    }
    .table {
      .header {
        color: #60627a;
        background: rgb(242, 246, 255);
        font-weight: 900;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 16.6%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;

          align-items: center;
        }
        .liLine {
          list-style-type: none;
          width: 17%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
}
</style>