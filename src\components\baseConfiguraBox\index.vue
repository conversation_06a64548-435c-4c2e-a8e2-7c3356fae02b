<template>
  <div class="baseConfiguraBox">
    <component
      :is="$route.name == 'baseConfiguraBox' ? 'administration' : $route.name"
    ></component>
  </div>
</template>

<script>
import administration from "./BasicDataManagement/administration";
import organization from "./BasicDataManagement/organization";
import organizationMange from "./BasicDataManagement/organizationMange/organizationMange.vue";
export default {
  //import引入的组件
  components: {
    organization,
    administration,
    organizationMange
  },
  data() {
    return {
      componentStr: "",
    };
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss" scoped>
.structure {
  margin-top: 15px;
}
</style>