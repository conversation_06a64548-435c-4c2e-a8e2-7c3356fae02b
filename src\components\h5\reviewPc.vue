<template>
  <div>
    <div>
      <!-- h5端 -->
      <review></review>
    </div>
    <div>
      <PcDetail></PcDetail>
    </div>
  </div>
</template>

<script>
import PcDetail from "./PcDetail.vue";
import review from "./review.vue";

export default {
  name: "Review",
  components: {
    PcDetail,
    review,
  },
  data() {
    // return {
    //   isMobile: "",
    // };
  },
  computed: {
    isMobileTo() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
  },
  mounted() {
    console.log("isMobileTo----->", this.isMobileTo);
  },
  computed: {},
};
</script>

