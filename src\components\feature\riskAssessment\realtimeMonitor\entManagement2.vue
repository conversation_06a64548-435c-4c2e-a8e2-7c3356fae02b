<template>
  <div class="enterpriseManagement" v-loading="loading">
    <div class="header">
      <div class="breadcrumb" v-if="showTab && vuexUser.user_type !== 'ent'">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span @click="goEnt()">
              <a-icon type="home" theme="filled" class="icon" /> 企业管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item v-if="isEnterprise === 'ent'">
            <a-icon
              v-if="!showTab"
              type="home"
              theme="filled"
              class="icon"
              style="margin-right: 10px"
            />企业详情</a-breadcrumb-item
          >
        </a-breadcrumb>
      </div>
      <div class="enterprise">
        <div class="enterpriseL">
          <div
            class="Grade"
            v-if="safeStatus == '1'"
            style="background: #f65959"
          >
            重大风险
          </div>
          <div
            class="Grade"
            v-if="safeStatus == '2'"
            style="background: #f98e2f"
          >
            较大风险
          </div>
          <div
            class="Grade"
            v-if="safeStatus == '3'"
            style="background: #f9c22f"
          >
            一般风险
          </div>
          <div
            class="Grade"
            v-if="safeStatus == '4'"
            style="background: #2f8ef9"
          >
            低风险
          </div>
          <div class="name">
            <el-tooltip
              class="item"
              effect="dark"
              :content="enterpriseName"
              placement="top-start"
            >
              <el-button type="text" class="botton"
                >{{ enterpriseName }}
              </el-button>
            </el-tooltip>
          </div>
        </div>
        <div class="enterpriseR">
          <div>
            <div class="ent"></div>
            <div v-if="ent == '01'">生产</div>
            <div v-else-if="ent == '02'">经营</div>
            <div v-else-if="ent == '03'">使用</div>
            <div v-else-if="ent == '04'">第一类非药品易制毒</div>
            <div v-else></div>
          </div>
          <div>
            <div class="address"></div>
            <div>{{ address ? address : " " }}</div>
          </div>
          <div>
            <div class="name"></div>
            <div>{{ name ? name : " " }}</div>
          </div>
          <div>
            <div class="tel"></div>
            <div>{{ tel ? tel : " " }}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="tabs"> 
      <el-tabs v-model="activeName" @tab-click="handleClick" :key="key">
        <!-- 根据2021.3.16 沟通结果暂时隐藏风险动态 -->
        <!-- <el-tab-pane label="风险动态" name="riskDynamics">
            <RiskDynamics></RiskDynamics>
        </el-tab-pane> -->
        <!-- <el-tab-pane v-if="level"
                     label="视频监控"
                     name="videoInspection">
          <VideoInspection ref="videoInspection"
                           :showVideo="showVideo"
                           :key="videoInspectionKey"></VideoInspection>
        </el-tab-pane> -->
        <el-tab-pane v-if="level" label="物联监测" name="realTimeMonitoring">
          <RealTimeMonitoring ref="realTimeMonitoring"></RealTimeMonitoring>
        </el-tab-pane>
        <!-- <el-tab-pane v-if="level" label="三维视图" name="threeDimensionalView"
          ><ThreeDimensionalView></ThreeDimensionalView
        ></el-tab-pane> -->
        <!-- <el-tab-pane v-if="level"
                     label="安全承诺"
                     name="safetyCommitment">
          <SafetyCommitment ref="safetyCommitment"
                            :name="name" />
        </el-tab-pane>
        <el-tab-pane label="基础信息"
                     name="basicInformationTab">
          <BasicInformation :level="level"
                            ref="basicInformation"></BasicInformation>
        </el-tab-pane>
        <el-tab-pane v-if="level"
                     label="物联报警"
                     name="IotDetection">
          <Lot ref="IotDetection"></Lot>
        </el-tab-pane>
        <el-tab-pane v-if="level"
                     label="报警分析"
                     name="alarmStatistical">
          <Alarm ref="alarmStatistical"></Alarm>
        </el-tab-pane>
        <el-tab-pane v-if="level"
                     label="设备设施"
                     name="equipmentAndFacilities">
          <EquipmentAndFacilities ref="equipmentAndFacilities"></EquipmentAndFacilities>
        </el-tab-pane>
        <el-tab-pane v-if="level"
                     label="监测设备"
                     name="monitoringEquipment">
          <MonitoringEquipment ref="monitoringEquipment"></MonitoringEquipment>
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>
<script>
// import RiskDynamics from './riskDynamics/index'
// import VideoInspection from './videoInspection/index'  //视频
// import SafetyCommitment from './safetyCommitment/index'
// import Lot from './IotDetection/index'
// import BasicInformation from './basicInformation/index'
import RealTimeMonitoring from './realTimeMonitoring/index'  //物联
// import ThreeDimensionalView from './threeDimensionalView/index'
// import Alarm from './alarmStatistical/index'
// import EquipmentAndFacilities from './equipmentAndFacilities/index'
// import MonitoringEquipment from './monitoringEquipment/index'
import { getInformationInfo } from '@/api/entList'
import { createNamespacedHelpers, mapState } from 'vuex'
const { mapState: mapStateControler } = createNamespacedHelpers('controler')
const { mapState: mapStateLogin } = createNamespacedHelpers('login')
export default {
  name: 'entManagement',
  components: {
    // RiskDynamics,
    // VideoInspection,
    // SafetyCommitment,
    // Lot,
    // BasicInformation,
    RealTimeMonitoring,
    // ThreeDimensionalView,
    // Alarm,
    // EquipmentAndFacilities,
    // MonitoringEquipment
  },
  props: ['activeF', 'enterpId'],
  data() {
    return {
      key: '',
      activeName: 'realTimeMonitoring',  //realTimeMonitoring
      enterpriseName: '',
      isEnterprise: '',
      showVideo: false,
      enterpriseId: this.enterpId,
      ent: '',
      address: '',
      name: '',
      tel: '',
      level: 1,
      loading: false,
      active: 'realTimeMonitoring',
      safeStatus: '',
      showTab: true,
      videoInspectionKey: '',
      routerName: ''
    }
  },
  methods: {
    handleClick(tab, event) {
      // console.log(tab.name == "IotDetection");
      this.showVideo = tab.name === 'realTimeMonitoring'
      switch (tab.name) {
        case 'basicInformationTab':
          this.$nextTick(() => {
            this.$refs.basicInformation.getData(this.enterpriseId)
          })
          break
        case 'safetyCommitment':
          this.$nextTick(() => {
            // console.log(this.$refs.safetyCommitment);
            this.$refs.safetyCommitment.getData(this.enterpriseId)
          })
          break
        case 'alarmStatistical':
          this.$nextTick(() => {
            this.$refs.alarmStatistical.getData(this.enterpriseId)
            this.$refs.alarmStatistical.getTotalData(this.enterpriseId)
            this.$refs.alarmStatistical.getTime()
          })
          break
        case 'IotDetection':
          this.$nextTick(() => {
            this.$refs.IotDetection.getData(this.enterpriseId)
          })
          break
        case 'realTimeMonitoring':
          this.$nextTick(() => {
            this.$refs.realTimeMonitoring.getData(this.enterpriseId)
            this.$refs.realTimeMonitoring.getSelectData(this.enterpriseId)
            this.$refs.realTimeMonitoring.getStateData(this.enterpriseId)
          })

          break
        case 'videoInspection':
          this.videoInspectionKey = new Date() + 'key'
          this.$nextTick(() => {
            this.$refs.videoInspection.getData(this.enterpriseId)
            this.$refs.videoInspection.getVideoNum(this.enterpriseId)
            this.$refs.videoInspection.initVideoNew()
            // this.$refs.videoInspection.initVideo()
            // this.$refs.videoInspection.initializationVideo();
          })
          break
        case 'monitoringEquipment':
          this.$nextTick(() => {
            this.$refs.monitoringEquipment.getData(this.enterpriseId)
          })
          break
        case 'equipmentAndFacilities':
          this.$nextTick(() => {
            this.$refs.equipmentAndFacilities.getData(this.enterpriseId)
          })
          break
        default:
          break
      }
    },
    goEnt() {
      this.enterpriseName = ''
      this.tel = ''
      this.address = ''
      this.ent = ''
      this.name = ''
      this.level = ''
      this.safeStatus = ''
      this.key = new Date().getTime() + '_key'
      this.$emit('type', 'gov')
    },
    getData() {
      this.active = 'realTimeMonitoring'
      // this.enterpriseId = this.enterpId
      if (this.vuexUser.user_type === 'ent') {
        const res = this.enterData
        this.setTab(res)
      } else {
        this.getInformationInfoFun()
      }
    },
    setTab(res) {
      this.enterpriseName = res.enterpName
      this.tel = res.respTel
      this.address = res.districtName
      this.ent = res.enterpriseType
      this.name = res.respper
      this.level = res.level
      this.safeStatus = res.safeStatus
      this.$nextTick(() => {
        if (this.level === null) {
         this.activeName = 'realTimeMonitoring'
          this.$refs.basicInformation.getData(this.enterpriseId)
        } else {
          this.showVideo = this.active === 'realTimeMonitoring'

          console.log(this.active,'执行了啥1111111111111111111111111');
          switch ('realTimeMonitoring') {
            case 'basicInformationTab':
              this.activeName = this.active
              this.$refs.basicInformation.getData(this.enterpriseId)
              break
            case 'safetyCommitment':
              this.activeName = this.active
              this.$refs.safetyCommitment.getData(this.enterpriseId)
              break
            case 'alarmStatistical':
              this.activeName = this.active
              this.$refs.alarmStatistical.getData(this.enterpriseId)
              this.$refs.alarmStatistical.getTotalData(this.enterpriseId)
              break
            case 'IotDetection':
              this.activeName = this.active
              this.$refs.IotDetection.getData(this.enterpriseId)
              break
            case 'realTimeMonitoring':              
              this.activeName = 'realTimeMonitoring'
              this.$nextTick(() => {
                  var tab={
                 name:'realTimeMonitoring'}
              
              this.handleClick(tab)
              this.$refs.realTimeMonitoring.getData(this.enterpriseId)
              this.$refs.realTimeMonitoring.getSelectData(this.enterpriseId)
              this.$refs.realTimeMonitoring.getStateData(this.enterpriseId)
              })
              break
            case 'monitoringEquipment':
              this.activeName = this.active
              this.$refs.monitoringEquipment.getData()
              break
            case 'equipmentAndFacilities':
              this.activeName = this.active
              this.$refs.equipmentAndFacilities.getData()
              break
            case 'videoInspection':
              console.log(this.active)
              this.videoInspectionKey = new Date() + 'key'
              this.activeName = this.active
              this.$nextTick(() => {
                this.$refs.videoInspection.getData(this.enterpriseId)
                this.$refs.videoInspection.getVideoNum(this.enterpriseId)
                this.$refs.videoInspection.initVideoNew()               
                // this.$refs.videoInspection.initializationVideo();
              })
              break
            case '':
              this.activeName = 'realTimeMonitoring'
              this.isEnterprise = 'ent'
              this.$refs.basicInformation.getData(this.enterpriseId)
              break
            default:
              this.activeName = 'this.active'
              this.isEnterprise = 'ent'
              this.$refs.basicInformation.getData(this.enterpriseId)
              break
          }
        }
        setTimeout(() => {
          this.loading = false
        }, 500)
      })
    },
    getInformationInfoFun() {
      //正式服一定要放开
      this.loading = true
      getInformationInfo(this.enterpriseId).then(res => {
        this.setTab(res.data.data)
      })
    }
  },
  computed: {
    ...mapStateControler({
      entModelName: state => state.entModelName
    }),
    ...mapStateLogin({
      vuexUser: state => state.user
    }),
    ...mapState({
      enterData: state => state.login.enterData
    })
  },
  watch: {
    enterData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.enterpriseId = newVal.enterpId
        }
      },
      deep: true,
      immediate: true
    },
    enterpId: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.enterpriseId = newVal
        }
      },
      deep: true,
      immediate: true
    },
    entModelName: {
      handler(newVal, from) {
        this.showTab = !newVal
      },
      deep: true,
      immediate: true
    }
  },
  created(){
    console.log(this.$store.state,'获取tab状态');
    this.activeName='realTimeMonitoring'
  },
  destroyed(){
    // this.$store.commit("login/updataActiveName", 'basicInformationTab');
  }
}
</script>
<style lang="scss" scoped>
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
    .enterprise {
      display: flex;
      justify-content: space-between;
    }
    .enterpriseL {
      // width: 500px;
      height: 40px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      // margin-left: 10px;
      .Grade {
        color: #fff;
        padding: 0px 5px;
        margin-right: 10px;
        font-size: 14px;
        float: right;
        width: 93px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 4px;
      }
      .name {
        .botton {
          height: 42px;
          font-size: 20px;
          flex-wrap: 600;
          color: #000;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
          text-align: left;
        }
      }
    }
    .enterpriseR {
      display: flex;
      justify-content: space-around;
      align-items: center;
      width: 650px;
      height: 40px;
      margin-right: 15px;
      background: #f3f8fd;
      border: 1px solid #f1f5fa;
      border-radius: 20px;
      & > div {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25%;
        border-right: 1px #cfdee9 solid;
        // height: 20px;
      }
      & > div:nth-last-of-type(1) {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25%;
        border-right: 0px #cfdee9 solid;
      }
      .ent {
        background: url("../../../../../static/icon/jy.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .address {
        background: url("../../../../../static/icon/address.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .name {
        background: url("../../../../../static/icon/name.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
      .tel {
        background: url("../../../../../static/icon/tel.png") no-repeat;
        width: 13px;
        height: 13px;
        background-size: 13px 13px;
        margin-right: 5px;
      }
    }
  }
  .tabs {
    padding-top: 10px;
    background-color: #fff;
  }
}
</style>
