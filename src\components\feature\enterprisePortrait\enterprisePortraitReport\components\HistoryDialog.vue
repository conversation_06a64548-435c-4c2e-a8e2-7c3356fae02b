<!-- 企业报告 -->
<template>
  <el-dialog
    title="历史报告"
    :visible="visible"
    @close="closeBoolean(false)"
    width="1300px"
    top="5vh"
    v-dialog-drag
    :close-on-click-modal="true"
  >
    <div class="dialog-container">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        ref="multipleTable"
      >
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="enterpName"
          label="企业名称"
          min-width="200"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span style="color: rgb(57, 119, 234)" class="enterpName">
              {{ scope.row.enterpName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="报告时间"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="reportFlagName"
          label="报告状态"
          align="center"
          width="120"
        >
        </el-table-column>
        <el-table-column
          prop="address"
          label="操作"
          align="center"
          width="270"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button @click="handleView(scope.row)" type="text" size="small">
              查看
            </el-button>
            <el-button
              @click="handleReport(scope.row)"
              type="text"
              size="small"
            >
              {{ scope.row.reportFlag == 0 ? "生成报告" : "导出" }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <EnterpriseDialog v-if="enterpriseVisible" :visible.sync="enterpriseVisible" :enterpriseInfo="enterpItem" @close="closeEnterprise"></EnterpriseDialog>
  </el-dialog>
</template>

<script>
import { getReportHistory } from "@/api/entList.js";
import EnterpriseDialog from "../components/enterpriseDialog.vue";
export default {
  components: { EnterpriseDialog },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    enterpriseInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      tableData: [],
      enterpItem: {},
      enterpriseVisible: false,
    };
  },
  mounted() {
    const params = {
      enterpId: this.enterpriseInfo.enterpId,
      nowPage: 1,
      pageSize: 1000,
    };
    // 获取历史列表
    getReportHistory(params).then((res) => {
      this.tableData = res.data.data.list;
    });
  },
  methods: {
    handleView(row) {
      this.enterpItem = row;
      this.enterpriseVisible = true;
    },
    handleReport(row) {
      const link = document.createElement("a");
      link.href = row.reportList[0].urlOuterNet;
      link.download = row.reportList[0].name;
      link.style.display = "none";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    closeBoolean(boolean) {
      this.$emit("close", boolean);
    },
    handleCancle() {
      this.closeBoolean(false);
    },
    closeEnterprise(boolean) {
      this.enterpriseVisible = boolean;
      this.enterpItem = {}
    },
  },
};
</script>

<style lang="scss" scoped></style>
