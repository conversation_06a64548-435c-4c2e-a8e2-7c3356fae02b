<template>
  <div class="map-container">
    <div id="mars3dContainer"></div>
    <div class="toolbar" v-if="map">
      <el-button-group>
        <el-button
          size="small"
          :type="drawingMode === '1' ? 'primary' : 'default'"
          @click="startDraw('1')"
          v-if="type === '1'"
          >点状标绘</el-button
        >
        <el-button
          size="small"
          :type="drawingMode === '2' ? 'primary' : 'default'"
          @click="startDraw('2')"
          v-if="type === '2'"
          >线状标绘</el-button
        >
        <el-button
          size="small"
          :type="drawingMode === '3' ? 'primary' : 'default'"
          @click="startDraw('3')"
          v-if="type === '3'"
          >面状标绘</el-button
        >
        <el-button size="small" type="danger" @click="clearDraw"
          >清除</el-button
        >
      </el-button-group>
      <el-button-group style="margin-left: 10px">
        <el-button
          size="small"
          :type="showImageLayer ? 'primary' : 'default'"
          @click="toggleImageLayer"
        >
          {{ showImageLayer ? "隐藏图层" : "显示图层" }}
        </el-button>
      </el-button-group>
    </div>
  </div>
</template>

<script>
let factoryImage = require("/static/map/factoryImage.json");

export default {
  name: "PlotMap",
  props: {
    // 标绘类型：1-点状, 2-面状
    type: {
      type: String,
      default: "1",
    },
    plotStyle: {
      type: Object,
      default: () => ({}),
    },
    resourceId: {
      type: String,
      default: "",
    },
    enterpriseId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      map: null,
      drawingMode: "", // 当前标绘模式
      graphicLayer: null, // 标绘图层
      showImageLayer: true, // 控制图层显示状态
      currentImageLayer: null, // 保存当前图层引用
    };
  },
  mounted() {
    this.initMap();
  },
  watch: {
    // 监听 resourceId 变化，切换图层
    resourceId: {
      handler(newVal) {
        if (this.map) {
          // 移除旧图层
          this.removeImage();
          // 添加新图层
          if (newVal) {
            this.addImage(this.showImageLayer, this.enterpriseId, newVal);
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // 初始化地图
    initMap() {
      // 创建地图
      this.map = new mars3d.Map("mars3dContainer", {
        scene: {
          center: { lat: 31.83, lng: 117.22, alt: 1000 },
          cameraViewType: "2D",
          globe: {
            enableLighting: false,
          },
        },
        control: {
          baseLayerPicker: false,
          sceneModePicker: false,
          navigationHelpButton: false,
          fullscreenButton: false,
          // fullscreenElement: document.getElementById("mars3dContainer"),
        },
        basemaps: [
          {
            name: "天地图电子",
            type: "tdt",
            layer: "vec_d",
            show: true,
          },
        ],
      });

      // 创建矢量图层
      this.graphicLayer = new mars3d.layer.GraphicLayer();
      this.map.addLayer(this.graphicLayer);

      // 绑定标绘完成事件
      this.graphicLayer.on(mars3d.EventType.drawCreated, this.onDrawCreated);

      // 初始添加图层
      if (this.resourceId) {
        this.addImage(this.showImageLayer, this.enterpriseId, this.resourceId);
      }
    },

    // 获取绘制样式
    getDrawStyle() {
      return {
        billboard: {
          image: this.plotStyle.image || "/static/img/position/位置.png",
          pixelOffsetY: -Number(this.plotStyle.image?.height) / 2 || -14,
          horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: mars3d.Cesium.VerticalOrigin.TOP,
          // scaleByDistance: true,
          // scaleByDistance_near: 10,
          // scaleByDistance_far: 10000,
          // scaleByDistance_farValue: 0.1,
          visibleDepth: true,
          clampToGround: true,
        },
        polyline: {
          color: this.plotStyle.borderColor || "#3388ff",
          width: this.plotStyle.borderWidth || 2,
        },
        polygon: {
          color: this.plotStyle.fillColor || "#3388ff",
          opacity: this.plotStyle.fillColor ? 0.6 : 0,
          outline: true,
          outlineColor: this.plotStyle.borderColor || "#ffffff",
          outlineWidth: this.plotStyle.borderWidth || 2,
        },
      };
    },

    // 开始标绘
    startDraw(type) {
      this.clearDraw();
      this.drawingMode = type;

      const style = this.getDrawStyle();
      this.graphicLayer.startDraw({
        type: this.plotStyle.type,
        style: style[this.plotStyle.type],
      });
    },

    // 清除标绘
    clearDraw() {
      if (this.graphicLayer) {
        this.drawingMode = "";
        this.graphicLayer.stopDraw();
        this.graphicLayer.clear();
      }
    },

    // 标绘完成事件
    onDrawCreated(e) {
      if (e.graphic) {
        const geojson = e.graphic.toGeoJSON();
        this.$emit("plotComplete", geojson);
      }
    },

    // 修改添加图层方法
    addImage(show, id, type) {
      let factory = null;
      if (id && factoryImage) {
        factory = factoryImage[id];
      }
      if (!factory || !factory.children) return;

      const key = "factoryImage_" + factory.key + type;
      let imageLayer = this.map.getLayerById(key);

      if (!imageLayer) {
        imageLayer = new mars3d.layer.ImageLayer({
          id: key,
          url: `/static/img/factoryPlan/${factory.key}/${factory.key}_${type}.png`,
          rectangle: factory.rectangle,
          zIndex: 20,
          flyTo: true,
          show,
        });
        this.map.addLayer(imageLayer);
      } else {
        imageLayer.show = show;
      }

      // 保存当前图层引用
      this.currentImageLayer = imageLayer;
    },

    // 移除图层方法
    removeImage() {
      if (this.currentImageLayer) {
        this.map.removeLayer(this.currentImageLayer);
        this.currentImageLayer = null;
      }
    },

    // 切换图层显示/隐藏
    toggleImageLayer() {
      this.showImageLayer = !this.showImageLayer;
      if (this.currentImageLayer) {
        this.currentImageLayer.show = this.showImageLayer;
      }
    },

    // 绘制已有的图形
    drawExistingPlot(geojson) {
      this.clearDraw();

      const style = this.getDrawStyle();

      const graphic = mars3d.Util.geoJsonToGraphics(geojson, {
        style: style[this.plotStyle.type],
      })[0];
      // 添加到图层
      this.graphicLayer.addGraphic(graphic);

      // 定位到图形
      this.graphicLayer.flyTo();
    },
  },
  beforeDestroy() {
    this.removeImage();
    // 销毁地图
    if (this.map) {
      this.map.destroy();
      this.map = null;
    }
  },
  // watch: {
  //   // 监听标绘类型变化
  //   plotType: {
  //     handler(newType) {
  //       if (newType === "point") {
  //         this.startDraw("point");
  //       } else if (newType === "line") {
  //         this.startDraw("polyline");
  //       } else if (newType === "polygon") {
  //         this.startDraw("polygon");
  //       }
  //     },
  //     immediate: true,
  //   },
  // },
};
</script>

<style lang="scss" scoped>
.map-container {
  width: 100%;
  height: 400px;
  position: relative;

  #mars3dContainer {
    width: 100%;
    height: 100%;
  }

  .toolbar {
    position: absolute;
    bottom: 0px;
    left: 5px;
    z-index: 1;
    display: flex;
    gap: 10px;
  }
}
</style>
