<template>
  <div class="riskDy">
    <div class="topTit">
      风险单元和风险点：<span>{{risktopData.riskUnintCount}}</span>个风险单元， <span>{{risktopData.riskUnitPointCount}}</span>项风险点
    </div>
    <div class="riskBox" v-loading>
      <div
        :class="['riskItem', 'riskItem' + item.riskUnitRank]"
        v-for="(item, index) in riskData"
        :key="index"
      >
        <div class="riskItemTop">
          <div class="title">{{ item.riskUnitName }}</div>
          <div :class="['riskNnum', 'status' + item.riskUnitRank]">
            <span>{{ item.riskLevelName }}</span>
            <p>{{ item.riskUnitValue }}</p>
          </div>
        </div>
        <ul>
          <li v-for="el of item.cimRiskUnitPointDTOList" :key="el.id" @click="clickItem(el)">
            <span :class="['iconSquer', 'iconSquer' + item.riskUnitRank]"></span
            >{{ el.riskPointName }}<span class="iconArrow"></span>
          </li>
        </ul>
      </div>
    </div>

    <el-dialog
      title="氯化工艺单元中毒事故风险点"
      :visible.sync="dialogRisk"
      width="1460px"
      top="50px"    
      :close-on-click-modal="false"
    >
      <div class="riskDialogBox">
        <div class="riskDialogTop">
          <div class="dialogL">固有风险-五高风险因子计量</div>
          <div class="dialogR">风险动态指标调控</div>
        </div>

        <div class="dialogCon">
          <div class="dialogConL">
            <div class="item">
              <div class="itemTit">高风险设备设施-坝体H1</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">高风险设物-氨气</div>
              <ul>
                <li>危险指数特征值(M)：1.7</li>
                <li>存量：320标方</li>
                <li>年使用量：280标方</li>
                <li>物理特性：有毒，可燃，易爆</li>
              </ul>
            </div>

            <div class="item">
              <div class="itemTit">高风险场所：生产装置区域</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>

            <div class="item">
              <div class="itemTit">高风险工艺：光电及光气化工艺</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>

            <div class="item">
              <div class="itemTit">高风险作业：受限空间作业</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>
          </div>
          <div class="dialogConC">
            <div class="centerL">
              <div class="center1">
                高风险设备设施-坝体H1
                <div>1.2</div>
              </div>
              <div class="center2">
                高风险作业K2
                <div>1.2</div>
              </div>
              <div class="center3">
                <div>5.0</div>
                高风险物品（能量）M
              </div>
              <div class="center4">
                <div>9.0</div>
                高风险场所E
              </div>
              <div class="center5">
                <div>1.0</div>
                高风险工艺K1
              </div>
              <div class="center6">
                <div>131.1</div>
                初始风险H
              </div>
              <div class="center7">指标管控率G<span>1.4</span></div>
              <div class="center71">
                <div>91.8</div>
                固有风险H
              </div>
            </div>

            <div class="centerC">
              <div class="center10">
                <div class="num">131.1</div>
                橙色风险
              </div>
              <div class="center101">
                <div>最终实现</div>
                安全风险值R
              </div>
            </div>
            <div class="centerR">
              <div class="center11">
                重大隐患
                <div class="num">1.0</div>
              </div>
              <div class="center12">
                特殊时期
                <div class="num">1.0</div>
              </div>
              <div class="center13">
                <div class="num">2.0</div>
                动态调控指标D
              </div>
              <div class="center14">
                <div class="num">0.0</div>
                自然环境指标
              </div>
              <div class="center15">
                <div class="num">1.0</div>
                互联网事故指标
              </div>
            </div>

            <div class="centerBottom">
              <div class="centerBottomItem1">
                <div>二级</div>
                <div>安全标准化等级</div>
              </div>
              <div class="centerBottomItem2">
                <div>1.43</div>
                <div>固有风险指标调控-管控频率G</div>
              </div>
              <div class="centerBottomItem3">
                <div>70</div>
                <div>初始安全标准化评分</div>
              </div>
            </div>
          </div>
          <div class="dialogConR">
            <div class="item">
              <div class="itemTit">高风险监测特征指标修改系数（K4）</div>
              <ul>
                <li>风险修正系数：1.0</li>
                <li>压力报警：12</li>
                <li>液位报警：--</li>
                <li>有毒气体：--</li>
                <li>可燃气体：--</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">事故隐患直指标修正指数（K4）</div>
              <ul>
                <li>风险修正系数：1.0</li>
                <li>压力报警：12</li>
                <li>一般隐患：00</li>
                <li>重大隐患：00</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">特殊时期指数修正</div>
              <ul>
                <li>风险修正系数：1.0</li>
                <li>时间：2021年1月24日</li>
                <li>湖北省第十三届人民代表大会</li>
                <li>第五次会议</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">高风险监测特征指标修改系数（K4）</div>
              <ul>
                <li>高危风险物联网指数修正</li>
                <li>风险修正系数：1.0</li>
                <li>时间：2021年1月24日</li>
                <li>类型：事故</li>
                <li>湖北省第十三届人民代表大会</li>
                <li>第五次会议</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// const echarts = require('echarts');
import {
  queryRiskCompanyInfo, //
} from "@/api/riskAssessment";
export default {
  //import引入的组件
  name: "riskDynamics",
  components: {},
  data() {
    return {
      loading:false,
      dialogRisk: false, //弹窗
      risktopData:{},
      riskData: [
        {
          title: "氯化工艺",
          level: "1",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "2",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "3",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "4",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "1",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "2",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "3",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
        {
          title: "氯化工艺",
          level: "4",
          levelNum: "122.1",
          sub: [
            { subTitle: "氯化工艺", id: 1 },
            { subTitle: "火灾爆炸事故", id: 2 },
          ],
        },
      ],
    };
  },
  filters: {
    filterLevel(val) {
      var str = "";
      if (val == 1) {
        str = "较大风险";
      } else if (val == 2) {
        str = "一般风险";
      } else if (val == 3) {
        str = "重大风险";
      } else if (val == 4) {
        str = "低风险";
      }
      return str;
    },
  },
  //方法集合
  methods: {
    clickItem() {
      this.dialogRisk = true;
    },    
    getData(enterpriseId){
       this.loading = true;
      queryRiskCompanyInfo({
        enterId: enterpriseId,
      }).then((res) => {
        if (res.data.status === 200) {         
          this.loading = false;
          this.riskData = res.data.data.cimRiskUnitDTOList;
          this.risktopData=res.data.data
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0;
}
.riskDialogBox {
  background: #0c1534;
  padding: 0 20px;
  .riskDialogTop {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #fff;
    padding: 10px 0 10px 0;
    .dialogL {
      background: url("../../../../../static/img/riskTopL.png") no-repeat bottom
        left;
      padding: 0 0 8px;
      width: 48%;
    }
    .dialogR {
      background: url("../../../../../static/img/riskTopR.png") no-repeat bottom
        right;
      padding: 0 0 8px;
      width: 48%;
      text-align: right;
    }
  }
  .dialogCon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialogConL {
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitL.png") no-repeat
          bottom left;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #808089;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
    .dialogConC {
      background: url("../../../../../static/img/riskDialog.png") no-repeat
        center center;
      width: 960px;
      height: 508px;
      background-size: 100%;
      position: relative;
    }
    .dialogConR {
      text-align: right;
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitR.png") no-repeat
          bottom right;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #808089;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
  }
  .dialogConC {
  }
  .centerL {
    font-size: 12px;
    text-align: center;
    color: #fff;
  }
  .center1 {
    //高风险设备设施-坝体H1
    position: absolute;
    top: 22px;
    left: 66px;
  }
  .center2 {
    // 高风险作业K2
    position: absolute;
    left: 222px;
    top: 23px;
  }
  .center3 {
    //高风险物品（能量）M
    position: absolute;
    left: 2px;
    top: 163px;
  }
  .center4 {
    position: absolute;
    left: 96px;
    top: 280px;
  }
  .center5 {
    left: 216px;
    top: 280px;
    position: absolute;
  }
  .center6 {
    //初始风险H
    left: 284px;
    top: 147px;
    position: absolute;
  }
  .center7 {
    //指标管控率G
    position: absolute;
    left: 226px;
    top: 116px;
    transform: rotate(-56deg);
  }
  .center71 {
    //  固有风险H
    position: absolute;
    left: 167px;
    top: 155px;
    .center71 > div {
      margin: 0 0 25px 0;
      font-size: 16px;
      font-weight: bold;
    }
  }
  .center8 {
  }
  .center9 {
  }
  .center1 {
  }
  .centerC {
    font-size: 12px;
    text-align: center;
    color: #fff;
  }
  .center10 {
    //橙色风险
    position: absolute;
    color: #ff9d47;
    left: 397px;
    top: 96px;
    width: 135px;
    height: 135px;
    border-radius: 50%;
    font-size: 16px;
    background: url("../../../../../static/img/中间的圆-橙色.png") no-repeat
      center;
    background-size: 100%;
    .num {
      margin: 44px 0 0 0;
      font-size: 18px;
      font-weight: bold;
    }
  }
  .center101 {
    //最终实现安全风险值R
    position: absolute;
    left: 425px;
    top: 283px;
  }
  .centerR {
    color: #fff;
    font-size: 12px;
    text-align: center;
  }
  .center11 {
      position: absolute;
         right: 260px;
    top: 25px;
      background: url("../../../../../static/img/risk01.png") no-repeat center;
      width: 80px;
      height: 69px;
      padding:20px 0 0 0;
      color:#ea6544;
      >div{

      }
    }
  .center12 {
    position: absolute;
    right: 100px;
    top: 23px;
  }
  .center13 {
    position: absolute;
    right: 147px;
    top: 160px;
    > div {
      margin: 0px 0 28px 0;
    }
  }
  .center14 {
    position: absolute;
    right: 15px;
    top: 161px;
  }
  .center15 {
    position: absolute;
    right: 85px;
    top: 280px;
  }
  .center16 {
  }
  .centerBottomItem1{
   position: absolute;
    bottom: 50px;
    left: 160px;
    text-align: center;
    color: #fff;
    width: 198px;
  }
  .centerBottomItem2{
    position: absolute;
    bottom: 50px;
    left: 362px;
    text-align: center;
    color: #fff;
    width: 218px;
  }
  .centerBottomItem3{
      position: absolute;
    bottom: 50px;
    left: 594px;
    text-align: center;
    color: #fff;
    width: 191px;
  }
}

</style>