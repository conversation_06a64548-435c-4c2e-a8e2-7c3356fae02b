<template>
  <div class="table">
    <el-dialog
      title="编辑用户"
      :visible.sync="edUserVisible"
      :modal="true"
      :destroy-on-close="true"
      @close="close"
      width="600px"
      :close-on-click-modal="false"
    >
      <div class="container">
        <el-form
          :model="ruleForm"
          :rules="rules"
          status-icon
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="机构名称" prop="orgName">
            <el-input v-model.trim="ruleForm.orgName"></el-input>
          </el-form-item>
          <el-form-item label="机构职责" prop="orgDuty">
            <el-input v-model.trim="ruleForm.orgDuty"></el-input>
          </el-form-item>
          <el-form-item label="联系电话" prop="dutytel">
            <el-input v-model.trim="ruleForm.dutytel"></el-input>
          </el-form-item>
          <el-form-item label="传真" prop="fax">
            <el-input v-model.trim="ruleForm.fax"></el-input>
          </el-form-item>
          <el-form-item label="地址" prop="address">
            <el-input v-model.trim="ruleForm.address"></el-input>
          </el-form-item>
          <el-form-item label="机构类型" prop="orgTypeCode">
            <el-select v-model="ruleForm.orgTypeCode" placeholder="机构类型">
              <el-option label="省应急厅" :value="1"></el-option>
              <el-option label="市应急厅" :value="2"></el-option>
              <el-option label="管委会" :value="3"></el-option>
              <el-option label="企业" :value="4"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排序" prop="disporder">
            <el-input label="排序" v-model.trim="ruleForm.disporder"></el-input>
          </el-form-item>
          <el-form-item label="行政区划" prop="districtName">
            <el-select
              v-model="ruleForm.districtName"
              @visible-change="onSelects"
              placeholder="行政区划"
            >
              <el-option
                v-for="(item, index) in option.records"
                :key="index"
                :label="item.distName"
                :value="item.distCode"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-button
          size="default"
          :loading="submitting"
          type="primary"
          class="commit"
          @click="updataBool ? updataForm('ruleForm') : submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getOrgList,
  getOrgAdd,
  getOrgInfo,
  getOrgUpdata,
} from "@/api/BasicDataManagement";
export default {
  //import引入的组件
  props: {
    edVisible: {
      type: Boolean,
      default: false,
    },
  },
  computed: {},
  data() {
    return {
      edUserVisible: false,
      submitting: false,
      ruleForm: {
        orgName: "",
        orgDuty: "",
        dutytel: "",
        fax: "",
        address: "",
        orgTypeCode: "",
        disporder: "",
        districtCode: "",
      },
      orgCode: "",
      password: "",
      confirmPsd: "",
      selectedRoleIds: "",
      rules: {
        orgName: [
          { required: true, trigger: "blur", message: "请输入机构名称" },
        ],
      },
      option: [],
      allTreeData: [],
      treeData: [],
      updataBool: false,
    };
  },
  //方法集合
  methods: {
    //关闭时清空from表单
    close() {
      this.ruleForm = {
        orgName: "",
        orgDuty: "",
        dutytel: "",
        fax: "",
        address: "",
        orgTypeCode: "",
        disporder: "",
        districtCode: "",
      };
      console.log(this.ruleForm);
    },
    /**
     * 父组件发送的消息
     * @param val dialog显示或隐藏
     * @param orgCode 机构代码
     * @param updataBool 是否新建或修改
     */
    parentMsg(val, orgCode, updataBool) {
      this.edUserVisible = val;
      this.orgCode = orgCode;
      this.updataBool = updataBool;
      if (orgCode != "-1") {
        this.getFromData();
      }
    },
    getRowData() {
      this.ruleForm = this.rowData;
      this.ruleForm.confirmPsd = this.rowData.password;
    },
    onSelects(selectedKeys, info) {
      if (selectedKeys == true) {
        getOrgList({
          distParentCode: "420000",
          current: 1,
          size: 99,
        }).then((res) => {
          this.option = res.data.data;
          console.log(res.data.data);
        });
      }
      // console.log('selected', selectedKeys, info);
    },
    onChecks(checkedKeys, info) {
      if (checkedKeys) {
        this.selectedRoleIds = checkedKeys.join(",");
      } else {
        this.selectedRoleIds = "";
      }
    },
    getFromData() {
      getOrgInfo(this.orgCode).then((res) => {
        this.ruleForm = res.data.data;
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.submitting) return;
          this.submitting = true;
          getOrgAdd({ ...this.ruleForm, parentCode: this.orgCode })
            .then((data) => {
              if (data.data.code == 0) {
                this.submitting = false;
                this.$message({
                  type: "success",
                  message: data.data.msg,
                });
                this.edUserVisible = false;
                this.$refs.ruleForm.resetFields();
                this.$emit("ed-callback");
              } else {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: "success",
                });
              }
            })
            .catch((e) => {
              this.submitting = false;
              // this.$message({
              //     type: 'info',
              //     message: '接口报错'
              // });
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    updataForm(formName) {
      if (this.ruleForm.orgTypeCode >= 2) {
        this.ruleForm.orgTypeCode = 2;
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.submitting) return;
          this.submitting = true;
          getOrgUpdata({ ...this.ruleForm })
            .then((data) => {
              if (data.data.code == 0) {
                this.submitting = false;
                this.$message({
                  type: "success",
                  message: data.data.data,
                });
                this.edUserVisible = false;
                this.$refs.ruleForm.resetFields();
                this.$emit("ed-callback");
              } else {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: "success",
                });
              }
            })
            .catch((e) => {
              this.submitting = false;
              // this.$message({
              //     type: 'info',
              //     message: '接口报错'
              // });
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    /**
     * 点击关闭图标或者遮罩回调
     * @param done
     */
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(() => {
          done();
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.getStructureTreeData();
  },
  watch: {
    rowData() {
      this.getRowData();
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-select {
  width: 80%;
}
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    .input {
      width: 60%;
    }
    .el-select,
    .radio-box {
      width: 60%;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .el-input {
    width: 80%;
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
