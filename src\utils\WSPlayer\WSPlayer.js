import CONSTANT from './CONSTANT'
import RealPlayerItem from './RealPlayer'
import RecordPlayerItem from './RecordPlayer'
import WSPlayerManager from './WSPlayerManager'
import utils from './utils'
import BASE_URL_ip from '../../../static/ip.js'

/* ---------------- WSPlayer ---------------- */
var BASE_URL=""
if(window.location.port=="30003"){ //企业端
    BASE_URL="yyzc.hbsis.gov.cn:30003"
}else if(window.location.port=="31443"){
    BASE_URL="yyzc-whjcyj.hbsis.gov.cn:31443"
}else{
    BASE_URL=BASE_URL_ip.BASE_URL
}

const defaultConfig = {
    num: 4,
    showControl: true,
    type: 'real'
}
class WSPlayer {
    static version = "1.0.0"
    /**
     * 构造函数
     * @param {String} options.el 必传 播放器domId
     * @param {String} options.type  必传 类型 real | record
     * @param {String} options.serverIp 必传 服务器IP
     * @param {number} options.num 播放器数量 default 4
     * @param {boolean} options.showControl 显示播放器控制栏 default true
     */
    constructor(options) {
        if (!options.el || !options.type || !options.serverIp) {
            console.error(`el, type, serverIp 为必传参数，请校验入参`)
            return false
        }
        let isHttps = location.protocol == 'https:'
        // 协议判断
        this.protocol = isHttps ? 'wss' : 'ws'
        // 服务器IP
        this.serverIp = options.serverIp ? options.serverIp : location.hostname
        this.options = Object.assign({}, defaultConfig, options)
        this.$el = $('#' + options.el)
        this.width = this.$el.attr('width')
        this.height = this.$el.attr('height')
        this.$el.height(`${this.height}px`)
        this.$el.width(`${this.width}px`)
        this.$el.addClass(`ws-player`)
        // 添加player-wrapper
        this.$el.append(`<div class="player-wrapper"></div>`)
        this.$wrapper = $('.player-wrapper', this.$el)
        this.playerList = []
        this.playerAdapter = "selfAdaption"; // selfAdaption 自适应  stretching 拉伸
        /**
         * method、data: 方法名、数据以及作用如下：
         * clickRecordTimeLine：点击时间轴    data：timeStamp时间戳
         * selectWindowChanged：切换窗口回调    data：index选择的窗口索引
         * windowNumChanged：显示的窗口数量变化回调    data：num显示的窗口数量
         * statusChanged：视频状态改变回调    data：{status：视频状态，windowIndex：状态变化的窗口索引}
         */
        this.sendMessage = options.receiveMessageFromWSPlayer || function(method, data) {}; // 给项目发送数据
        $(this.$el).attr('inited', true)
        this.eventListner = {
            selectChange: null,
        }
        let {isVersionCompliance, browserType, errorCode} = utils.checkBrowser();
        switch (options.type) {
            case 'real':
                this.wsPort = CONSTANT.websocketPorts.realmonitor
                if (this.options.showControl) {
                    this.__addRealControl()
                } else {
                    this.$wrapper.addClass('nocontrol')
                }
                // 设置服务器websocektUrl地址
                // this.wsURL = `wss://${this.serverIp}/${this.wsPort}`
                console.log(BASE_URL,'我是2测试视频的BASE_URL')
                this.wsURL = `${this.protocol}://${BASE_URL}/${this.wsPort}`
                for (let i of [0, 1, 2, 3]) {
                    let realPlayerItem = new RealPlayerItem({
                        wrapperDomId: options.el,
                        index: i,
                        wsPlayer: this
                    });
                    this.playerList.push(realPlayerItem);
                    // 如果浏览器版本低，则需要进行提示
                    if(!isVersionCompliance) {
                        realPlayerItem.setStatus("error", {errorCode})
                    }
                }
                break;
            case 'record':
                for (let i of [0, 1, 2, 3]) {
                    let recordPlayerItem = new RecordPlayerItem({
                        wrapperDomId: options.el,
                        index: i,
                        wsPlayer: this
                    });
                    this.playerList.push(recordPlayerItem)
                    // 如果浏览器版本低，则需要进行提示
                    if(!isVersionCompliance) {
                        recordPlayerItem.setStatus("error", {errorCode})
                    }
                }
                this.wsPort = CONSTANT.websocketPorts.playback
                // 设置服务器websocektUrl地址
                // this.wsURL = `wss://${this.serverIp}/${this.wsPort}`
                console.log(BASE_URL,'视频3勒')
                this.wsURL = `${this.protocol}://${BASE_URL}/${this.wsPort}`
                if (this.options.showControl) {
                    this.__addRealControl()
                } else {
                    this.$wrapper.addClass('nocontrol')
                }
                break;
            default:
                break;
        }
        this.setSelectIndex(0)
        this.setPlayerNum(options.num)
        this.bindUpdatePlayerWindow = this.__updatePlayerWindow.bind(this);
        // 浏览器窗口事件：改变分辨率
        window.addEventListener( 'resize', this.bindUpdatePlayerWindow)
        if(!window.WSPlayerManager) {
            window.wsPlayerManager = new WSPlayerManager();
        }
    }

    /**
     * 添加事件监听
     * @param {String} eventType
     * @param {Function} callback
     */
    addListener(eventType, callback) {
        if (typeof eventType !== 'string' || !eventType) {
            console.warn("添加的监听需要为字符串！")
            return
        }
        this.eventListner[eventType] = callback
    }
    /**
     * 播放实时视频
     * @param {*} options.rtspURL String | array
     * @param {*} options.decodeMode 可选参数 video | canvas
     * @param {*} options.wsURL 可选参数
     * @param {*} options.channelId 可选参数, 用来标记当前播放的视频通道
     */
    playReal(opt) {
        if (!opt.rtspURL) {
            console.error("播放实时视频需要传入rtspURL")
            return
        }
        // 多个rtsp地址时，按照播放器顺序，选择播放器进行播放
        if (opt.rtspURL instanceof Array) {
            opt.rtspURL.forEach((item, i) => {
                if (i < 4) {
                    this.playReal(Object.assign({}, opt, {
                        rtspURL: item
                    }))
                }
            })
        } else {
            opt.wsURL = this.__getWSUrl(opt.rtspURL)
            opt.playerAdapter = this.playerAdapter;
            let player = this.playerList[this.selectIndex]
            player.init(opt)
            if (this.showNum > 1) {
                this.setSelectIndex((this.selectIndex + 1) % this.showNum)
            }
        }
    }
    /**
     * 播放录像
     * @param {String} options.decodeMode 可选参数 video | canvas
     * @param {String} options.wsURL 可选参数
     * @param {Function} options.recordSource 2=设备，3=中心
     * recordSource == 2 设备录像，按照时间方式播放
     * @param {String} options.rtspURL String
     * @param {Number | String} options.startTime 开始时间 时间戳或者'2021-09-18 15:40:00'格式的时间字符串
     * @param {Number | String} options.endTime 结束时间 时间戳或者'2021-09-18 15:40:00'格式的时间字符串
     * @param {Function} options.reload 重新拉流的回调函数，用于时间回放，返回promise
     * reload(newStarTime, endTime).then(newRtspUrl => { play continue})
     * recordSource == 3 中心录像，按照文件方式播放
     * @param {Function} options.RecordFiles 文件列表
     * @param {Function} options.getRtsp 文件列表
     * getRtsp(file).then(newRtspUrl => { play continue})
     */
    playRecord(opt) {
        let player = this.playerList[this.selectIndex]
        console.log(player,'player')
        opt.wsURL = this.__getWSUrl(opt.rtspURL)
        console.log(opt.wsURL,'opt.wsURL')
        opt.playerAdapter = this.playerAdapter;
        console.log(opt.playerAdapter,'opt.playerAdapter')
        player.init(opt)
        if (this.showNum > 1) {
            this.setSelectIndex((this.selectIndex + 1) % this.showNum)
        }
    }

    /**
     * 播放
     */
    play() {
        let player = this.playerList[this.selectIndex]
        player.status === "pause" && player.play()
    }
    /**
     * 暂停播放
     */
    pause() {
        let player = this.playerList[this.selectIndex]
        player.status === "playing" && player.pause()
    }
    /**
     * 倍速播放
     * @param {Number} speed 倍速
     */
    playSpeed(speed) {
        if (this.options.type === 'real') {
            console.warn('实时预览不支持倍速播放')
            return
        }
        let player = this.playerList[this.selectIndex]
        player.playSpeed(speed)
    }
    /**
     * 时间跳转
     * @param {*} time
     */
    playByTime(time) {
        if (this.options.type === 'real') {
            console.warn('实时预览不支持时间设置')
            return
        }
        let player = this.playerList[this.selectIndex]
        player.playByTime(time)
    }
    /**
     * 设置选中的播放器
     * @param {*} index
     */
    setSelectIndex(index) {
        if (this.selectIndex === index) {
            return
        }
        // 如果选中的窗口没有在播放或者暂停，则需要清空时间轴
        if(!["playing", "pause"].includes((this.playerList[index] || {}).status)) {
            this.setTimeLine([]);
        }
        this.sendMessage("selectWindowChanged", index);
        this.selectIndex = index
        this.__triggerEvent('selectChange', [this.selectIndex, this.playerList[this.selectIndex].options])
        this.playerList.forEach((item, i) => {
            if (i === index) {
                item.$el.removeClass('unselected').addClass('selected')
            } else {
                item.$el.removeClass('selected').addClass('unselected')
            }
            // 更新播放声音的窗口
            this.__updateVoice(item, i === index);
        })
    }
    /**
     * 控制视频播放器数量
     * @param {*} number
     * @param {*} index
     */
    setPlayerNum(number, index) {
        if (this.showNum === number) {
            return
        }
        this.showNum = number
        switch (number) {
            case 1:
                this.$el.addClass('fullplayer')
                break;
            default:
                this.$el.removeClass('fullplayer')
                break;
        }
        setTimeout(() => {
            this.__updatePlayerWindow();
        }, 200)
    }

    /**
     * 控制播放器是否自适应
     * @param playerAdapter
     */
    setPlayerAdapter(playerAdapter) {
        if(this.playerAdapter === playerAdapter) {
            return;
        }
        this.playerAdapter = playerAdapter
        this.__updatePlayerWindow();
    }

    /**
     * 录像回放中的时间轴
     * @param timeList: [{startTime: 1650067200, endTime: 1650070800, isImportant: false}, {startTime: 1650085200, endTime: 1650093971, isImportant: true}]
     * 开始时间、结束时间、是否重要
     */
    setTimeLine(timeList) {
        // this.timeList = JSON.parse('[{"startTime":"1650556800","endTime":"1650556860","isImportant":false},{"startTime":"1650556800","endTime":"1650556801","isImportant":false},{"startTime":"1650556800","endTime":"1650556832","isImportant":false},{"startTime":"1650556800","endTime":"1650556983","isImportant":false},{"startTime":"1650556800","endTime":"1650558101","isImportant":false},{"startTime":"1650556801","endTime":"1650557637","isImportant":false},{"startTime":"1650556860","endTime":"1650556867","isImportant":false},{"startTime":"1650558107","endTime":"1650560310","isImportant":false},{"startTime":"1650560368","endTime":"1650561340","isImportant":false},{"startTime":"1650561346","endTime":"1650562507","isImportant":false},{"startTime":"1650562529","endTime":"1650564579","isImportant":false},{"startTime":"1650564585","endTime":"1650567600","isImportant":false},{"startTime":"1650567600","endTime":"1650567819","isImportant":false},{"startTime":"1650567825","endTime":"1650571058","isImportant":false},{"startTime":"1650571065","endTime":"1650574298","isImportant":false},{"startTime":"1650574304","endTime":"1650577537","isImportant":false},{"startTime":"1650577543","endTime":"1650580777","isImportant":false},{"startTime":"1650580783","endTime":"1650584016","isImportant":false},{"startTime":"1650584023","endTime":"1650587255","isImportant":false},{"startTime":"1650587263","endTime":"1650589200","isImportant":false},{"startTime":"1650589200","endTime":"1650590285","isImportant":false},{"startTime":"1650590285","endTime":"1650590320","isImportant":false},{"startTime":"1650590308","endTime":"1650590495","isImportant":false},{"startTime":"1650590502","endTime":"1650592122","isImportant":false},{"startTime":"1650592109","endTime":"1650593440","isImportant":false},{"startTime":"1650593428","endTime":"1650593735","isImportant":false},{"startTime":"1650593741","endTime":"1650594881","isImportant":false},{"startTime":"1650594868","endTime":"1650596974","isImportant":false},{"startTime":"1650596981","endTime":"1650600000","isImportant":false},{"startTime":"1650600000","endTime":"1650600214","isImportant":false},{"startTime":"1650600221","endTime":"1650603453","isImportant":false},{"startTime":"1650603460","endTime":"1650606692","isImportant":false},{"startTime":"1650606699","endTime":"1650609931","isImportant":false},{"startTime":"1650609938","endTime":"1650611187","isImportant":false}]')
        this.timeList = timeList;
        this.__setTimeRecordArea(timeList);
    }

    // 关闭所有播放器
    close() {
        // 关闭所有播放器的同时也清空时间轴
        this.setTimeLine([])
        this.playerList.forEach(item => {
            item.close()
        })
        // 取消浏览器窗口事件监听
        window.removeEventListener("resize", this.bindUpdatePlayerWindow);
    }

    /* ----------------------- 内部方法 -----------------------*/
    /**
     * 添加实时播放控制栏
     */
    __addRealControl() {
        this.$el.append(`
            <div class="ws-control">
                <div class="flex">
                    <div class="ws-ctrl-icon full-screen-icon" title="全屏"></div>
                    <div class="ws-ctrl-icon one-screen-icon" title="单屏"></div>
                    <div class="ws-ctrl-icon four-screen-icon" title="四分屏"></div>
                    <div class="ws-ctrl-btn self-adaption">自适应</div>
                    <div class="ws-ctrl-btn stretching">拉伸</div>
                    <div class="ws-ctrl-btn close-all-video">一键关闭</div>
                </div>
            </div>
        `)

        $('.full-screen-icon', this.$el).click(() => {
            let target = this.$el[0]
            if (target.requestFullscreen) {
                target.requestFullscreen();
            } else if (target.webkitRequestFullscreen) {
                target.webkitRequestFullscreen();
            } else if (target.mozRequestFullScreen) {
                target.mozRequestFullScreen();
            } else if (target.msRequestFullscreen) {
                target.msRequestFullscreen();
            }
        })
        $('.one-screen-icon', this.$el).click(() => {
            this.setPlayerNum(1, this.selectIndex)
        })
        $('.four-screen-icon', this.$el).click(() => {
            this.setPlayerNum(4)
        })
        $('.self-adaption', this.$el).click(() => {
            this.setPlayerAdapter("selfAdaption")
        })
        $('.stretching', this.$el).click(() => {
            this.setPlayerAdapter("stretching")
        })
        $('.close-all-video', this.$el).click(() => {
            this.close()
        })
        // $('.start-local-record', this.$el).click(() => {
        //     this.startLocalRecord()
        // })
        // $('.stop-local-record', this.$el).click(() => {
        //     this.stopLocalRecord()
        // })
    }
    /**
     * 添加录像回放控制栏
     */
    __addRecordControl() {
        this.$el.append(`
            <div class="ws-control ws-record-control">
                <div class="ws-timeline">
                    <div class="ws-timeline-group"></div>
                    <div class="ws-timeline-group"></div>
                </div>
            </div>
        `)
        let wsTimeGroup1 = $(this.$el[0].getElementsByClassName("ws-timeline-group")[0]);
        let wsTimeGroup2 = $(this.$el[0].getElementsByClassName("ws-timeline-group")[1]);
        // 添加时间间隔
        new Array(49).fill(1).forEach((item, index) => {
            let className = `ws-time-space ${index % 4 ? "" : "ws-time-space-long"}`
            wsTimeGroup1.append(`<span class="${className}"></span>`)
        })
        // 添加时间点
        new Array(13).fill(1).forEach((item, index) => {
            wsTimeGroup2.append(`<span class="ws-time-point">${`${index * 2}:00`.padStart(5, "0")}</span>`)
        })
        $(".ws-record-control").mouseenter(e => {
            $(".ws-record-control").append("<div id='ws-cursor'><div class='ws-cursor-time'><span></span></div></div>")
        })
        // 添加鼠标移入事件
        $(".ws-record-control").mousemove(e => {
            let width = $(".ws-record-control").width();
            let layerX = e.clientX - $(".ws-record-control")[0].getBoundingClientRect().left
            let date = new Date((layerX / width * 24 * 60 * 60 - 8 * 60 * 60) * 1000);
            let hours = `${date.getHours()}`.padStart(2, "0")
            let minutes = `${date.getMinutes()}`.padStart(2, "0")
            let seconds = `${date.getSeconds()}`.padStart(2, "0")
            let time = `${hours}:${minutes}:${seconds}`;
            $("#ws-cursor").css("left", layerX);
            $("#ws-cursor span").text(time);
        })
        $(".ws-record-control").mouseleave(e => {
            $("#ws-cursor").remove();
        })
        // 点击某个时间点进行播放
        $(".ws-record-control").click(e => {
            // 只有选中的播放器在播放或者暂停状态，才能根据时间轴选择播放
            if(["playing", "pause"].includes((this.playerList[this.selectIndex] || {}).status)) {
                let width = $(".ws-record-control").width();
                let layerX = e.clientX - $(".ws-record-control")[0].getBoundingClientRect().left
                let timeStamp = parseInt(layerX / width * 24 * 60 * 60, 10);
                // 计算所选时间点是否有录像，如果有则返回时间点，否则返回空字符串
                let time = new Date(this.timeList[0].startTime * 1000).setHours(0, 0, 0) / 1000 + timeStamp;
                if(!this.timeList.some(timeItem => {
                    if(time >= timeItem.startTime && time < timeItem.endTime) {
                        this.sendMessage("clickRecordTimeLine", timeStamp);
                        return true
                    }
                })) {
                    this.sendMessage("clickRecordTimeLine", "");
                }
            }
        })
    }

    /**
     * 设置有录像的区域
     * @param timeList: [{startTime: xxx, endTime: xxx, isImportant: false}]
     * startTime 和 endTime 都是秒级时间戳
     * @private
     */
    __setTimeRecordArea(timeList = []) {
        if(timeList.length) {
            // 先清空时间轴
            $(".ws-record-area").remove();
            timeList.forEach(timeItem => {
                let boxWidth = $(".ws-record-control").width();
                // 录像区域长度
                let width = (timeItem.endTime - timeItem.startTime) * boxWidth / (24 * 60 * 60);
                let date = new Date(timeItem.startTime * 1000);
                let hours = date.getHours();
                let minutes = date.getMinutes();
                let seconds = date.getSeconds();
                let left = (hours * 3600 + minutes * 60 + seconds) / (24 * 3600) * boxWidth;
                let style = `width: ${width}px; left: ${left}px`;
                let className = `ws-record-area ${timeItem.isImportant ? "ws-record-area-red" : "ws-record-area-blue"}`
                $(".ws-record-control").append(`<div class="${className}" style="${style}"></div>`);
            })
        } else {
            $(".ws-record-area").remove();
        }
    }

    /**
     * 根据RTSP地址获取wsUrl
     * @param {String} rtspUrl
     */
    __getWSUrl(rtspUrl) {
        // debugger
        let ip = rtspUrl.match(/\d{1,3}(\.\d{1,3}){3}/g)[0]
        if (!ip) {
            ip = rtspUrl.split('//')[1].split(':')[0]
        }
        return `${this.wsURL}?serverIp=${ip}`
        //return `ws://*************:9320/playback-websocket?serverIp=${ip}`
        //return 'ws://*************:9320/playback-websocket?serverIp=*************'
        // return `ws://**************:8090/realmonitor-websocket?serverIp=************`  //实时视频        
        //return `ws://**************:8090/playback-websocket?serverIp=************`  //回放
        // return `${this.wsURL}?serverIp=${this.serverIp.split(':')[0]}`
    }
    /**
     * 事件触发
     * @param {String} eventType
     * @param {Args}  参数
     */
    __triggerEvent(eventType, args) {
        if (this.eventListner[eventType] && typeof this.eventListner[eventType] === 'function') {
            this.eventListner[eventType].call(this, ...args)
        }
    }
    /**
     * 更新播放器窗口
     */
    __updatePlayerWindow() {
        this.playerList.forEach(item => {
            item.updateAdapter(this.playerAdapter);
        })
        this.setTimeLine(this.timeList);
    }

    /**
     * 控制声音播放
     * @param playerWrapper
     * @param chooseFlag：当前的窗口是否被选中
     * @private
     */
    __updateVoice(playerWrapper, chooseFlag) {
        if(!chooseFlag) {
            // 关闭未被选中的窗口的声音，但是播放声音的图标保留显示
            playerWrapper.player && playerWrapper.player.setAudioVolume(0);
        } else if($('.audio-icon', playerWrapper.$el).hasClass("on")) {
            // 若窗口被选中，且声音被开启，就播放声音
            playerWrapper.player.setAudioVolume(1);
        }
    }
}

export {
    WSPlayer
}

export default WSPlayer
