/**
 * Mars3D平台插件,结合mapv可视化功能插件  mars3d-mapv
 *
 * 版本信息：v3.4.0
 * 编译日期：2022-07-14 15:06:29
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2022-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mapv || require('mapv')), (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mapv', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-mapv"] = {}, global.mapv, global.mars3d));
})(this, (function (exports, mapv, mars3d) { 
'use strict';var _0x52e6a5=_0x4f85;(function(_0x2920cf,_0x2af2c0){var _0xae1779=_0x4f85,_0xc6730c=_0x2920cf();while(!![]){try{var _0x1a6581=parseInt(_0xae1779(0x1f7))/0x1+parseInt(_0xae1779(0x257))/0x2*(-parseInt(_0xae1779(0x28d))/0x3)+parseInt(_0xae1779(0x1dd))/0x4*(parseInt(_0xae1779(0x26b))/0x5)+-parseInt(_0xae1779(0x23d))/0x6*(-parseInt(_0xae1779(0x27b))/0x7)+parseInt(_0xae1779(0x1db))/0x8+parseInt(_0xae1779(0x216))/0x9+-parseInt(_0xae1779(0x220))/0xa;if(_0x1a6581===_0x2af2c0)break;else _0xc6730c['push'](_0xc6730c['shift']());}catch(_0x4c2e54){_0xc6730c['push'](_0xc6730c['shift']());}}}(_0x49ad,0x30bf9));function _interopNamespace(_0x3c5fdb){var _0x439436=_0x4f85;if(_0x3c5fdb&&_0x3c5fdb[_0x439436(0x209)])return _0x3c5fdb;var _0x5c6ef1=Object[_0x439436(0x207)](null);return _0x3c5fdb&&Object[_0x439436(0x1fe)](_0x3c5fdb)['forEach'](function(_0xec7b56){var _0x20de7b=_0x439436;if(_0xec7b56!==_0x20de7b(0x25b)){var _0x8b751b=Object[_0x20de7b(0x218)](_0x3c5fdb,_0xec7b56);Object[_0x20de7b(0x1d6)](_0x5c6ef1,_0xec7b56,_0x8b751b['get']?_0x8b751b:{'enumerable':!![],'get':function(){return _0x3c5fdb[_0xec7b56];}});}}),_0x5c6ef1[_0x439436(0x25b)]=_0x3c5fdb,_0x5c6ef1;}function _0x4f85(_0x360a93,_0x59d7cd){var _0x49ad6d=_0x49ad();return _0x4f85=function(_0x4f85e1,_0x518cdf){_0x4f85e1=_0x4f85e1-0x1d5;var _0x372d26=_0x49ad6d[_0x4f85e1];if(_0x4f85['cZsNsJ']===undefined){var _0x4cbf5b=function(_0x3c5fdb){var _0x5c6ef1='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0xec7b56='',_0x8b751b='';for(var _0x5f1dd0=0x0,_0x50a812,_0x52e351,_0x1a465d=0x0;_0x52e351=_0x3c5fdb['charAt'](_0x1a465d++);~_0x52e351&&(_0x50a812=_0x5f1dd0%0x4?_0x50a812*0x40+_0x52e351:_0x52e351,_0x5f1dd0++%0x4)?_0xec7b56+=String['fromCharCode'](0xff&_0x50a812>>(-0x2*_0x5f1dd0&0x6)):0x0){_0x52e351=_0x5c6ef1['indexOf'](_0x52e351);}for(var _0x23130c=0x0,_0x55709c=_0xec7b56['length'];_0x23130c<_0x55709c;_0x23130c++){_0x8b751b+='%'+('00'+_0xec7b56['charCodeAt'](_0x23130c)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x8b751b);};_0x4f85['nmJlat']=_0x4cbf5b,_0x360a93=arguments,_0x4f85['cZsNsJ']=!![];}var _0x27cfb3=_0x49ad6d[0x0],_0x3b93d4=_0x4f85e1+_0x27cfb3,_0x4bb209=_0x360a93[_0x3b93d4];return!_0x4bb209?(_0x372d26=_0x4f85['nmJlat'](_0x372d26),_0x360a93[_0x3b93d4]=_0x372d26):_0x372d26=_0x4bb209,_0x372d26;},_0x4f85(_0x360a93,_0x59d7cd);}var mapv__namespace=_interopNamespace(mapv),mars3d__namespace=_interopNamespace(mars3d);function ownKeys(_0x5f1dd0,_0x50a812){var _0x1a2e1f=_0x4f85,_0x52e351=Object[_0x1a2e1f(0x1fe)](_0x5f1dd0);if(Object[_0x1a2e1f(0x26c)]){var _0x1a465d=Object['getOwnPropertySymbols'](_0x5f1dd0);_0x50a812&&(_0x1a465d=_0x1a465d[_0x1a2e1f(0x208)](function(_0x23130c){var _0xbac2f1=_0x1a2e1f;return Object[_0xbac2f1(0x218)](_0x5f1dd0,_0x23130c)[_0xbac2f1(0x266)];})),_0x52e351['push']['apply'](_0x52e351,_0x1a465d);}return _0x52e351;}function _objectSpread2(_0x55709c){var _0x3054fc=_0x4f85;for(var _0x25c054=0x1;_0x25c054<arguments[_0x3054fc(0x22b)];_0x25c054++){var _0x48bd84=null!=arguments[_0x25c054]?arguments[_0x25c054]:{};_0x25c054%0x2?ownKeys(Object(_0x48bd84),!0x0)[_0x3054fc(0x22f)](function(_0x1e6662){_defineProperty(_0x55709c,_0x1e6662,_0x48bd84[_0x1e6662]);}):Object[_0x3054fc(0x239)]?Object['defineProperties'](_0x55709c,Object[_0x3054fc(0x239)](_0x48bd84)):ownKeys(Object(_0x48bd84))['forEach'](function(_0x305212){var _0x4c73ca=_0x3054fc;Object[_0x4c73ca(0x1d6)](_0x55709c,_0x305212,Object[_0x4c73ca(0x218)](_0x48bd84,_0x305212));});}return _0x55709c;}function _classCallCheck(_0x344d28,_0x502ac1){if(!(_0x344d28 instanceof _0x502ac1))throw new TypeError('Cannot\x20call\x20a\x20class\x20as\x20a\x20function');}function _defineProperties(_0x31c557,_0x9c5a0a){var _0x2f8b88=_0x4f85;for(var _0x37a3ec=0x0;_0x37a3ec<_0x9c5a0a['length'];_0x37a3ec++){var _0x2ea727=_0x9c5a0a[_0x37a3ec];_0x2ea727[_0x2f8b88(0x266)]=_0x2ea727['enumerable']||![],_0x2ea727[_0x2f8b88(0x1ff)]=!![];if(_0x2f8b88(0x212)in _0x2ea727)_0x2ea727[_0x2f8b88(0x28f)]=!![];Object[_0x2f8b88(0x1d6)](_0x31c557,_0x2ea727[_0x2f8b88(0x28b)],_0x2ea727);}}function _createClass(_0x520860,_0x3fa2ea,_0x5c27d0){var _0x5977b1=_0x4f85;if(_0x3fa2ea)_defineProperties(_0x520860[_0x5977b1(0x270)],_0x3fa2ea);if(_0x5c27d0)_defineProperties(_0x520860,_0x5c27d0);return Object[_0x5977b1(0x1d6)](_0x520860,'prototype',{'writable':![]}),_0x520860;}function _defineProperty(_0x41179b,_0x4c613a,_0x1b23a9){var _0x210ca4=_0x4f85;return _0x4c613a in _0x41179b?Object[_0x210ca4(0x1d6)](_0x41179b,_0x4c613a,{'value':_0x1b23a9,'enumerable':!![],'configurable':!![],'writable':!![]}):_0x41179b[_0x4c613a]=_0x1b23a9,_0x41179b;}function _0x49ad(){var _0xc0bd6f=['D2LUzg93ug9ZAxrPB24','x2fKzgvKsg9VAW','zM9YrwfJAa','rxzLBNruExbL','C3rVCa','BgvMDa','y2fUDMfZ','Dg9W','C2nHBgu','C3rVCefUAwfTyxrPB24','CMvNAxn0zxi','DxbKyxrLq2fSBgjHy2S','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9YCW','txvSDgLqB2X5z29U','C2nLBMu','y2fSBa','nK5PEw9NCW','BM9Uzq','x3bVAw50zxjfDMvUDhm','CMvZDg9Yzq','u3vWzxiGzxHWCMvZC2LVBIbTDxn0igvPDgHLCIbIzsbUDwXSig9YigeGzNvUy3rPB24','CMvTB3zLqwXSrgf0yq','CMvTB3zL','BwfWDKzPEgvKsgvPz2H0','B2jQzwn0','ywrKqw5PBwf0B3jfDMvUDa','qMfZzuXHEwvY','x3nLDe9WDgLVBNniB29R','y2fTzxjHtw92zvn0yxj0','x29UtwfWq2XPy2S','BwfWDKrLChrOvgvZDa','y29UC3rYDwn0B3i','Cg9ZAxrPB24','ug9PBNq','z2v0uhjVDg90ExbLt2y','yw5PBwf0Aw9U','rg9TvxrPBa','Aw5PDerLDMLJzvbPEgvSuMf0Aw8','zhjHDW','rgf0yvnLDa','DhjHAwXZ','Bw91C2vTB3zL','mLj0DvbMDG','vxrPBa','x2nHy2HLx2v2zw50','B3b0Aw9UCW','zgvMyxvSDa','y2XPy2S','y2XLyxi','ywjZB2X1Dgu','tgLUzvn0CMLUzW','Bw91C2vnB3zL','Aw5PDa','twfWvKXHEwvY','x2nHBNzHC1vWzgf0zq','C2L6zq','z2v0uMvJDgfUz2XL','zw51BwvYywjSzq','yMXVy2S','BwfWDG','y29UC3rYDwn0','x2nVB3jKAw5HDgvZ','mJCWALz5Afjp','z2v0t3DUuhjVCgvYDhLtEw1IB2XZ','y2fUDMfZtgf5zxi','yxv0BW','CgfYzw50rwXLBwvUDa','ChjVDg90ExbL','DMfSDwvpzG','rwXSAxbZB2LKywXpy2nSDwrLCG','Cg9ZDfjLBMrLCG','x29Utw92zvn0yxj0rxzLBNq','CMvTB3zLq2HPBgq','yw5PBwf0B3jnB3zLzw5KrxzLBNq','x3jLC2v0','x21HCfzszw5KzxjLCG','y2XPy2TfDMvUDa','Ew1PBG','mtuZotyZnNz0tLf5CG','Cg9PBNrLCKv2zw50CW','u2nLBMvnB2rL','ChjVy2vZC0rHDge','y29UDgfPBMvY','Dw5IAw5KrxzLBNq','Ew1HEa','C3rLCa','y2fYDgvZAwfUvg9dyw52yxndB29YzgLUyxrLCW','rMvHDhvYzunVBgXLy3rPB24','y2XLyxjeyxrH','zgvWDgHuzxn0','yMfPzhvnyxbmyxLLCG','BwfW','uMvJDgfUz2XL','DgLTzq','A2v5','zgv2AwnLugL4zwXsyxrPBW','otu5odq3wufmu0jc','q2fYDg9NCMfWAgLJ','D3jPDgfIBgu','zhjHD0nVBNrLEhq','B2zM','x3jLBw92zwriB29R','zMLSBfn0EwXL','zgvMAw5LuhjVCgvYDhK','x21HCa','DgHPCYbOyxnUj3qGyMvLBIbPBML0AwfSAxnLzcaTihn1CgvYkcKGAgfZBID0igjLzw4Gy2fSBgvK','x3nOB3DiB29R','DxbKyxrLrgf0yq','mJu3mJKYog9YA1DlyW','C2HHBq','mtK1mdHcBe52whe','AxngB3jTyxq','BwfWDKf1Dg9izwLNAhq','C2v0wKLUzgv4','C3r5Bgu','z2v0','CMvUzgvY','y2fTzxjH','y29Uy2f0','Eg1HEa','AgvPz2H0','Eg1PBG','Aw5PDefUAw1HDg9Y','C2v0uhjVDg90ExbLt2y','rgvYAxzLzcbJB25ZDhj1y3rVCNmGBwf5ig9UBhKGCMv0DxjUig9IAMvJDcbVCIb1BMrLzMLUzwq','DhLWzq','C3rLChm','C3rHCNq','yxjNq2HLy2S','zNvUy3rPB24','y29UDgv4Da','6k+35BYv5ywLig1HChyG5BQtia','zML4zwrizwLNAhq','z2vVBwv0CNK','x2nYzwf0zunHBNzHCW','x29UtwfWtw91C2vnB3zL','mZCWnZe4uwrZDM9x','yMLUzev2zw50','Bgf5zxi','y2XLyxjszwn0','Bw91C2veB3DU','ug9SEwDVBG','zgf0yvnLDa','A2v5CW','y29UzMLNDxjHyMXL','AxnfBMfIBgvKvgLTzq','y2fTzxjHtw92zuvUza','CMvTB3zLrgf0yq','zgvZDhjVEq','mhb4','EKLUzgv4','D2LKDgG','y3jLyxrL','zMLSDgvY','x19LC01VzhvSzq','z2XVyMu','x3nPEMu','DhjHBNnMzxjdB29YzgLUyxrL','q2fYDgvZAwfUmW','zwXSAxbZB2LK','zNjVBurLz3jLzxm','x2rHDge','Dw5KzwzPBMvK','DMfSDwu','x2rHDgfdywnOzq','ywrKrgf0yq','AxnqB2LUDfzPC2LIBgu','mJaZndqXnhD2rMLLAa','Aw5PDerHDgfsyw5Nzq','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9Y','Bwv0Ag9KCW','zgf0yq','z2v0sgvPz2H0','Cg9ZAxrPB25xqW','zgLZCgXHEq','yw5PBwf0B3i','y2XHBxbuB0DYB3vUza','odGYmdy0mhbLExj4sG','yMLUza','C3rLChnsyw5Nzq','x29Utw92zuvUzev2zw50','zgvZDgLUyxrPB24TB3v0','Bgf5zxjPza','z2v0q29UDgv4Da','z2v0rgf0yq','Bw9Kzq','x19WCM90B19F','ChvZAa','BgvUz3rO','DxbKyxrL'];_0x49ad=function(){return _0xc0bd6f;};return _0x49ad();}function _inherits(_0x28d9f6,_0x30200b){var _0x49e543=_0x4f85;if(typeof _0x30200b!=='function'&&_0x30200b!==null)throw new TypeError(_0x49e543(0x241));_0x28d9f6[_0x49e543(0x270)]=Object[_0x49e543(0x207)](_0x30200b&&_0x30200b[_0x49e543(0x270)],{'constructor':{'value':_0x28d9f6,'writable':!![],'configurable':!![]}}),Object[_0x49e543(0x1d6)](_0x28d9f6,'prototype',{'writable':![]});if(_0x30200b)_setPrototypeOf(_0x28d9f6,_0x30200b);}function _getPrototypeOf(_0xa261f5){var _0x4f8d58=_0x4f85;return _getPrototypeOf=Object[_0x4f8d58(0x1ea)]?Object['getPrototypeOf']:function _0x2e5864(_0x2be829){var _0x55b92a=_0x4f8d58;return _0x2be829[_0x55b92a(0x229)]||Object[_0x55b92a(0x24f)](_0x2be829);},_getPrototypeOf(_0xa261f5);}function _setPrototypeOf(_0x5d4615,_0x57b36a){var _0x33ca8b=_0x4f85;return _setPrototypeOf=Object[_0x33ca8b(0x1ea)]||function _0x578f88(_0x4026fd,_0x1c4bc0){return _0x4026fd['__proto__']=_0x1c4bc0,_0x4026fd;},_setPrototypeOf(_0x5d4615,_0x57b36a);}function _isNativeReflectConstruct(){var _0x41a33d=_0x4f85;if(typeof Reflect==='undefined'||!Reflect[_0x41a33d(0x269)])return![];if(Reflect[_0x41a33d(0x269)][_0x41a33d(0x1dc)])return![];if(typeof Proxy==='function')return!![];try{return Boolean[_0x41a33d(0x270)][_0x41a33d(0x271)]['call'](Reflect[_0x41a33d(0x269)](Boolean,[],function(){})),!![];}catch(_0x1d582a){return![];}}function _assertThisInitialized(_0x13264b){var _0x1bec9d=_0x4f85;if(_0x13264b===void 0x0)throw new ReferenceError(_0x1bec9d(0x1d8));return _0x13264b;}function _possibleConstructorReturn(_0x368ec5,_0x546bc1){var _0x2ff1b2=_0x4f85;if(_0x546bc1&&(typeof _0x546bc1===_0x2ff1b2(0x245)||typeof _0x546bc1===_0x2ff1b2(0x1f0)))return _0x546bc1;else{if(_0x546bc1!==void 0x0)throw new TypeError(_0x2ff1b2(0x1eb));}return _assertThisInitialized(_0x368ec5);}function _createSuper(_0xc143e3){var _0xeeb030=_isNativeReflectConstruct();return function _0x4d9979(){var _0x546e1d=_0x4f85,_0x44ba4e=_getPrototypeOf(_0xc143e3),_0x1fd14d;if(_0xeeb030){var _0x2a3f20=_getPrototypeOf(this)[_0x546e1d(0x24c)];_0x1fd14d=Reflect['construct'](_0x44ba4e,arguments,_0x2a3f20);}else _0x1fd14d=_0x44ba4e['apply'](this,arguments);return _possibleConstructorReturn(this,_0x1fd14d);};}function _superPropBase(_0x1b8619,_0x2bbb97){var _0x105ad5=_0x4f85;while(!Object['prototype']['hasOwnProperty'][_0x105ad5(0x23c)](_0x1b8619,_0x2bbb97)){_0x1b8619=_getPrototypeOf(_0x1b8619);if(_0x1b8619===null)break;}return _0x1b8619;}function _get(){var _0x286f6f=_0x4f85;return typeof Reflect!==_0x286f6f(0x211)&&Reflect[_0x286f6f(0x1e2)]?_get=Reflect['get']:_get=function _0x3b02fd(_0x289b6b,_0xbc019a,_0x1f0384){var _0x4aeb38=_0x286f6f,_0x424d3c=_superPropBase(_0x289b6b,_0xbc019a);if(!_0x424d3c)return;var _0x4c4b36=Object[_0x4aeb38(0x218)](_0x424d3c,_0xbc019a);if(_0x4c4b36['get'])return _0x4c4b36['get'][_0x4aeb38(0x23c)](arguments[_0x4aeb38(0x22b)]<0x3?_0x289b6b:_0x1f0384);return _0x4c4b36[_0x4aeb38(0x212)];},_get['apply'](this,arguments);}var Cesium$1=mars3d__namespace['Cesium'],baiduMapLayer=mapv__namespace?mapv__namespace[_0x52e6a5(0x287)]:null,BaseLayer$1=baiduMapLayer?baiduMapLayer[_0x52e6a5(0x229)]:Function,MapVRenderer=function(_0x43cd3f){var _0x1e25cd=_0x52e6a5;_inherits(_0x1a8b70,_0x43cd3f);var _0x16b980=_createSuper(_0x1a8b70);function _0x1a8b70(_0x569702,_0x25e631,_0x1905ce,_0x4df257){var _0x45ac7e=_0x4f85,_0x4b2e03;_classCallCheck(this,_0x1a8b70),_0x4b2e03=_0x16b980[_0x45ac7e(0x23c)](this,_0x569702,_0x25e631,_0x1905ce);if(!BaseLayer$1)return _possibleConstructorReturn(_0x4b2e03);return _0x4b2e03[_0x45ac7e(0x288)]=_0x569702,_0x4b2e03['scene']=_0x569702[_0x45ac7e(0x23b)],_0x4b2e03[_0x45ac7e(0x1fd)]=_0x25e631,_0x1905ce=_0x1905ce||{},_0x4b2e03[_0x45ac7e(0x261)](_0x1905ce),_0x4b2e03[_0x45ac7e(0x1ef)](_0x1905ce),_0x4b2e03['initDevicePixelRatio'](),_0x4b2e03[_0x45ac7e(0x26d)]=_0x4df257,_0x4b2e03[_0x45ac7e(0x236)]=!0x1,_0x4b2e03[_0x45ac7e(0x250)]=_0x1905ce['animation'],_0x4b2e03;}return _createClass(_0x1a8b70,[{'key':'initDevicePixelRatio','value':function _0x3e4857(){var _0xa54ee0=_0x4f85;this[_0xa54ee0(0x28c)]=window[_0xa54ee0(0x28c)]||0x1;}},{'key':_0x1e25cd(0x246),'value':function _0xcf8d8(){}},{'key':'animatorMovestartEvent','value':function _0xd12128(){var _0x785506=_0x1e25cd,_0x5bf51d=this['options'][_0x785506(0x250)];this[_0x785506(0x200)]()&&this[_0x785506(0x21e)]&&(this[_0x785506(0x1ed)][_0x785506(0x282)]=_0x5bf51d[_0x785506(0x222)][_0x785506(0x1ee)]);}},{'key':'animatorMoveendEvent','value':function _0x51d87a(){var _0x579f44=_0x1e25cd;this[_0x579f44(0x200)]()&&this['animator'];}},{'key':'getContext','value':function _0x54dc02(){var _0x4d12c2=_0x1e25cd;return this['canvasLayer'][_0x4d12c2(0x233)]['getContext'](this[_0x4d12c2(0x1f1)]);}},{'key':_0x1e25cd(0x261),'value':function _0x56965d(_0x4a1ae8){var _0x28e08f=_0x1e25cd;this[_0x28e08f(0x25a)]=_0x4a1ae8,this[_0x28e08f(0x217)](_0x4a1ae8),this[_0x28e08f(0x1f1)]=this[_0x28e08f(0x25a)][_0x28e08f(0x1f1)]||'2d',this['options'][_0x28e08f(0x205)]&&this[_0x28e08f(0x26d)]&&this['canvasLayer'][_0x28e08f(0x1e0)]&&this[_0x28e08f(0x26d)]['setZIndex'](this[_0x28e08f(0x25a)][_0x28e08f(0x205)]),this[_0x28e08f(0x1e9)]();}},{'key':_0x1e25cd(0x263),'value':function _0xe91e9a(_0x563c67){var _0x3f4547=_0x1e25cd,_0x210043=this[_0x3f4547(0x23b)];if(this['canvasLayer']&&!this['stopAniamation']){var _0x7889df=this[_0x3f4547(0x25a)][_0x3f4547(0x250)],_0x4ff9da=this['getContext']();if(this[_0x3f4547(0x200)]()){if(void 0x0===_0x563c67)return void this['clear'](_0x4ff9da);this[_0x3f4547(0x1f1)]==='2d'&&(_0x4ff9da['save'](),_0x4ff9da['globalCompositeOperation']=_0x3f4547(0x224),_0x4ff9da[_0x3f4547(0x1d5)]='rgba(0,\x200,\x200,\x20.1)',_0x4ff9da['fillRect'](0x0,0x0,_0x4ff9da[_0x3f4547(0x233)][_0x3f4547(0x206)],_0x4ff9da[_0x3f4547(0x233)][_0x3f4547(0x1e7)]),_0x4ff9da[_0x3f4547(0x240)]());}else this['clear'](_0x4ff9da);if(this[_0x3f4547(0x1f1)]==='2d')for(var _0x5acc10 in this[_0x3f4547(0x25a)]){_0x4ff9da[_0x5acc10]=this['options'][_0x5acc10];}else _0x4ff9da['clear'](_0x4ff9da['COLOR_BUFFER_BIT']);var _0x228dec={'transferCoordinate':function _0x3ff306(_0x506f44){var _0x22cfad=_0x3f4547,_0x382879=null,_0x3a5b44=_0x210043[_0x22cfad(0x244)];_0x210043[_0x22cfad(0x1df)]&&(_0x3a5b44=_0x210043[_0x22cfad(0x20a)][_0x22cfad(0x21b)](Cesium$1[_0x22cfad(0x28e)][_0x22cfad(0x20f)](_0x506f44[0x0],_0x506f44[0x1])));var _0x1e1970=Cesium$1[_0x22cfad(0x20d)]['fromDegrees'](_0x506f44[0x0],_0x506f44[0x1],_0x3a5b44);if(!_0x1e1970)return _0x382879;var _0x5e7c91=_0x210043['cartesianToCanvasCoordinates'](_0x1e1970);if(!_0x5e7c91)return _0x382879;if(_0x210043[_0x22cfad(0x24b)]&&_0x210043[_0x22cfad(0x228)]===Cesium$1[_0x22cfad(0x27d)]['SCENE3D']){var _0x12af78=new Cesium$1[(_0x22cfad(0x272))](_0x210043[_0x22cfad(0x20a)][_0x22cfad(0x20e)],_0x210043[_0x22cfad(0x1e4)][_0x22cfad(0x21c)]),_0xbfd889=_0x12af78[_0x22cfad(0x215)](_0x1e1970);if(!_0xbfd889)return _0x382879;}return[_0x5e7c91['x'],_0x5e7c91['y']];}};void 0x0!==_0x563c67&&(_0x228dec[_0x3f4547(0x208)]=function(_0x2a2601){var _0x3fe7dd=_0x3f4547,_0x590da9=_0x7889df[_0x3fe7dd(0x255)]||0xa;return!!(_0x563c67&&_0x2a2601[_0x3fe7dd(0x28a)]>_0x563c67-_0x590da9&&_0x2a2601[_0x3fe7dd(0x28a)]<_0x563c67);});var _0x1a8fc8=this['dataSet']['get'](_0x228dec);this[_0x3f4547(0x27e)](_0x1a8fc8),this[_0x3f4547(0x25a)]['unit']==='m'&&this['options'][_0x3f4547(0x264)],this[_0x3f4547(0x25a)][_0x3f4547(0x20b)]=this[_0x3f4547(0x25a)][_0x3f4547(0x264)];var _0x6b4e64=_0x210043[_0x3f4547(0x283)](Cesium$1[_0x3f4547(0x20d)][_0x3f4547(0x20f)](0x0,0x0));if(!_0x6b4e64)return;this[_0x3f4547(0x290)](_0x4ff9da,new mapv__namespace['DataSet'](_0x1a8fc8),this[_0x3f4547(0x25a)],_0x6b4e64),this[_0x3f4547(0x25a)][_0x3f4547(0x238)]&&this[_0x3f4547(0x25a)][_0x3f4547(0x238)](_0x563c67);}}},{'key':_0x1e25cd(0x1da),'value':function _0x5c6184(_0x3a3da6,_0x22b741){var _0x3dd34a=_0x1e25cd,_0x56ada=_0x3a3da6;_0x56ada&&_0x56ada[_0x3dd34a(0x1e2)]&&(_0x56ada=_0x56ada[_0x3dd34a(0x1e2)]()),void 0x0!==_0x56ada&&this[_0x3dd34a(0x1fd)]['set'](_0x56ada),_get(_getPrototypeOf(_0x1a8b70['prototype']),'update',this)[_0x3dd34a(0x23c)](this,{'options':_0x22b741});}},{'key':'addData','value':function _0x525c08(_0x57eb87,_0x3291be){var _0x39f2a5=_0x1e25cd,_0x18eb35=_0x57eb87;_0x57eb87&&_0x57eb87[_0x39f2a5(0x1e2)]&&(_0x18eb35=_0x57eb87[_0x39f2a5(0x1e2)]()),this[_0x39f2a5(0x1fd)]['add'](_0x18eb35),this['update']({'options':_0x3291be});}},{'key':'getData','value':function _0x4539ed(){var _0x29e3f8=_0x1e25cd;return this[_0x29e3f8(0x1fd)];}},{'key':_0x1e25cd(0x202),'value':function _0x267286(_0x4f7972){var _0x29c664=_0x1e25cd;if(this[_0x29c664(0x1fd)]){var _0x125c65=this[_0x29c664(0x1fd)][_0x29c664(0x1e2)]({'filter':function _0x453937(_0x1fc5a2){return _0x4f7972==null||typeof _0x4f7972!=='function'||!_0x4f7972(_0x1fc5a2);}});this[_0x29c664(0x1fd)]['set'](_0x125c65),this[_0x29c664(0x22c)]({'options':null});}}},{'key':_0x1e25cd(0x285),'value':function _0x4fb073(){var _0x209b80=_0x1e25cd;this['dataSet']&&this[_0x209b80(0x1fd)][_0x209b80(0x25d)](),this[_0x209b80(0x22c)]({'options':null});}},{'key':_0x1e25cd(0x253),'value':function _0x2ac9c6(){var _0x2dace6=_0x1e25cd;this['canvasLayer'][_0x2dace6(0x253)]();}},{'key':'clear','value':function _0x1d12ff(_0x3958fe){var _0x305bb6=_0x1e25cd;_0x3958fe&&_0x3958fe['clearRect']&&_0x3958fe[_0x305bb6(0x1fa)](0x0,0x0,_0x3958fe[_0x305bb6(0x233)][_0x305bb6(0x206)],_0x3958fe[_0x305bb6(0x233)]['height']);}},{'key':_0x1e25cd(0x203),'value':function _0x240801(){var _0xa1aecb=_0x1e25cd;this[_0xa1aecb(0x25d)](this[_0xa1aecb(0x226)]()),this[_0xa1aecb(0x285)](),this[_0xa1aecb(0x21e)]&&this[_0xa1aecb(0x21e)][_0xa1aecb(0x231)](),this[_0xa1aecb(0x21e)]=null,this[_0xa1aecb(0x26d)]=null;}}]),_0x1a8b70;}(BaseLayer$1);if(mapv__namespace!==null&&mapv__namespace!==void 0x0&&mapv__namespace[_0x52e6a5(0x254)])mapv__namespace[_0x52e6a5(0x254)][_0x52e6a5(0x270)][_0x52e6a5(0x20c)]=function(_0x4850c7,_0x202e19,_0x564bf8,_0x1b041f){var _0x2f5971=_0x52e6a5;_0x1b041f=_0x1b041f||_0x2f5971(0x26a),_0x564bf8=_0x564bf8||'coordinates';for(var _0xb68eea=0x0;_0xb68eea<_0x4850c7[_0x2f5971(0x22b)];_0xb68eea++){var _0x306daa=_0x4850c7[_0xb68eea][_0x2f5971(0x1f4)],_0x2ea46d=_0x306daa[_0x564bf8];switch(_0x306daa[_0x2f5971(0x1ec)]){case _0x2f5971(0x24e):{var _0x316da8=_0x202e19(_0x2ea46d);_0x316da8?_0x306daa[_0x1b041f]=_0x316da8:_0x306daa[_0x1b041f]=[-0x3e7,-0x3e7];}break;case _0x2f5971(0x25f):{var _0x59f442=[];for(var _0x3a3be9=0x0;_0x3a3be9<_0x2ea46d['length'];_0x3a3be9++){var _0x231d62=_0x202e19(_0x2ea46d[_0x3a3be9]);_0x231d62&&_0x59f442[_0x2f5971(0x22a)](_0x231d62);}_0x306daa[_0x1b041f]=_0x59f442;}break;case'MultiLineString':case _0x2f5971(0x1fc):{var _0x429afe=_0x1c248e(_0x2ea46d);_0x306daa[_0x1b041f]=_0x429afe;}break;case _0x2f5971(0x23a):{var _0x3a9741=[];for(var _0xb1a0b9=0x0;_0xb1a0b9<_0x2ea46d[_0x2f5971(0x22b)];_0xb1a0b9++){var _0x54dddb=_0x1c248e(_0x2ea46d[_0xb1a0b9]);_0x54dddb[_0x2f5971(0x22b)]>0x0&&_0x3a9741[_0x2f5971(0x22a)](_0x54dddb);}_0x306daa[_0x1b041f]=_0x3a9741;}break;}}function _0x1c248e(_0x5eef1f){var _0x4cd041=_0x2f5971,_0x3e69b9=[];for(var _0x2a5918=0x0;_0x2a5918<_0x5eef1f['length'];_0x2a5918++){var _0x3973ec=_0x5eef1f[_0x2a5918],_0x3ebe3b=[];for(var _0x4cfb00=0x0;_0x4cfb00<_0x3973ec[_0x4cd041(0x22b)];_0x4cfb00++){var _0x32b68a=_0x202e19(_0x3973ec[_0x4cfb00]);_0x32b68a&&_0x3ebe3b[_0x4cd041(0x22a)](_0x32b68a);}_0x3ebe3b[_0x4cd041(0x22b)]>0x0&&_0x3e69b9[_0x4cd041(0x22a)](_0x3ebe3b);}return _0x3e69b9;}return _0x4850c7;};else throw new Error(_0x52e6a5(0x1f2));var Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace['layer'][_0x52e6a5(0x247)],divId=0x0,MapVLayer=function(_0x5b5e48){var _0x11f4d0=_0x52e6a5;_inherits(_0x5cfe90,_0x5b5e48);var _0x2f5274=_createSuper(_0x5cfe90);function _0x5cfe90(_0x58c344,_0x539872){var _0x199892=_0x4f85,_0x57ace9;return _classCallCheck(this,_0x5cfe90),_0x57ace9=_0x2f5274['call'](this,_0x58c344),_0x57ace9[_0x199892(0x23f)]=_0x57ace9[_0x199892(0x25a)][_0x199892(0x27c)],_0x57ace9[_0x199892(0x1fd)]=_0x539872||new mapv__namespace[(_0x199892(0x254))](_0x58c344[_0x199892(0x21a)]),_0x57ace9[_0x199892(0x233)]=null,_0x57ace9;}return _createClass(_0x5cfe90,[{'key':_0x11f4d0(0x27c),'get':function _0x301244(){var _0x4e3a6c=_0x11f4d0;return this[_0x4e3a6c(0x23f)];},'set':function _0x441085(_0x345380){var _0x5631d8=_0x11f4d0;this[_0x5631d8(0x23f)]=_0x345380,this['canvas']&&(_0x345380?this[_0x5631d8(0x233)][_0x5631d8(0x1e1)][_0x5631d8(0x27c)]='all':this[_0x5631d8(0x233)]['style']['pointerEvents']=_0x5631d8(0x23e));}},{'key':_0x11f4d0(0x1d9),'value':function _0x356499(_0x22586d){var _0x20aaf9=_0x11f4d0;_0x22586d?this[_0x20aaf9(0x233)][_0x20aaf9(0x1e1)][_0x20aaf9(0x21d)]=_0x20aaf9(0x267):this[_0x20aaf9(0x233)][_0x20aaf9(0x1e1)][_0x20aaf9(0x21d)]=_0x20aaf9(0x23e);}},{'key':'_mountedHook','value':function _0x369b1a(){var _0x5e66bb=_0x11f4d0,_0x33e286,_0x2c4b91,_0x3e88e5;this[_0x5e66bb(0x1d7)][_0x5e66bb(0x23b)][_0x5e66bb(0x24b)]=(_0x33e286=this[_0x5e66bb(0x25a)][_0x5e66bb(0x286)])!==null&&_0x33e286!==void 0x0?_0x33e286:!![],this['_map'][_0x5e66bb(0x23b)][_0x5e66bb(0x1df)]=(_0x2c4b91=this[_0x5e66bb(0x25a)][_0x5e66bb(0x21f)])!==null&&_0x2c4b91!==void 0x0?_0x2c4b91:![],this[_0x5e66bb(0x1d7)]['scene']['mapvFixedHeight']=(_0x3e88e5=this[_0x5e66bb(0x25a)][_0x5e66bb(0x1f3)])!==null&&_0x3e88e5!==void 0x0?_0x3e88e5:0x0;}},{'key':'_addedHook','value':function _0xd9142c(){var _0x1d487b=_0x11f4d0;this['dataSet']&&(!this[_0x1d487b(0x1fd)][_0x1d487b(0x210)]||this[_0x1d487b(0x1fd)][_0x1d487b(0x210)]['length']===0x0)&&(this['dataSet'][_0x1d487b(0x210)]=[][_0x1d487b(0x1e5)](this[_0x1d487b(0x1fd)][_0x1d487b(0x213)])),this[_0x1d487b(0x278)]=new MapVRenderer(this['_map'],this[_0x1d487b(0x1fd)],this[_0x1d487b(0x25a)],this),this['initDevicePixelRatio'](),this[_0x1d487b(0x233)]=this[_0x1d487b(0x1f5)](),this[_0x1d487b(0x1e3)]=this[_0x1d487b(0x1e3)][_0x1d487b(0x221)](this),this[_0x1d487b(0x1f8)](),this[_0x1d487b(0x277)]();}},{'key':_0x11f4d0(0x292),'value':function _0x73ce82(){var _0x5a57e4=_0x11f4d0;this[_0x5a57e4(0x280)](),this[_0x5a57e4(0x278)]&&(this[_0x5a57e4(0x278)][_0x5a57e4(0x203)](),this['_mapVRenderer']=null),this[_0x5a57e4(0x233)][_0x5a57e4(0x26f)][_0x5a57e4(0x275)](this[_0x5a57e4(0x233)]);}},{'key':_0x11f4d0(0x252),'value':function _0x12e175(){var _0x16ca3d=_0x11f4d0;this['devicePixelRatio']=window[_0x16ca3d(0x28c)]||0x1;}},{'key':_0x11f4d0(0x1f8),'value':function _0x291098(){var _0x41c2e3=_0x11f4d0,_0x414dd5,_0x1bb530,_0x363878,_0x251461;this[_0x41c2e3(0x1d7)]['on'](mars3d__namespace[_0x41c2e3(0x230)][_0x41c2e3(0x1fb)],this['_onMoveStartEvent'],this),this[_0x41c2e3(0x1d7)]['on'](mars3d__namespace['EventType'][_0x41c2e3(0x249)],this[_0x41c2e3(0x274)],this),this[_0x41c2e3(0x1d7)]['on'](mars3d__namespace['EventType'][_0x41c2e3(0x201)],this[_0x41c2e3(0x223)],this),(_0x414dd5=this[_0x41c2e3(0x25a)])!==null&&_0x414dd5!==void 0x0&&(_0x1bb530=_0x414dd5[_0x41c2e3(0x219)])!==null&&_0x1bb530!==void 0x0&&_0x1bb530['click']&&this[_0x41c2e3(0x1d7)]['on'](mars3d__namespace['EventType']['click'],this[_0x41c2e3(0x24a)],this),(_0x363878=this['options'])!==null&&_0x363878!==void 0x0&&(_0x251461=_0x363878['methods'])!==null&&_0x251461!==void 0x0&&_0x251461[_0x41c2e3(0x256)]&&this['_map']['on'](mars3d__namespace[_0x41c2e3(0x230)][_0x41c2e3(0x260)],this[_0x41c2e3(0x1f6)],this);}},{'key':'unbindEvent','value':function _0x27a80f(){var _0x5a75bb=_0x11f4d0,_0x4c474b,_0x16fd5c,_0x24e628,_0x307fb4;this[_0x5a75bb(0x1d7)]['off'](mars3d__namespace[_0x5a75bb(0x230)][_0x5a75bb(0x1fb)],this[_0x5a75bb(0x274)],this),this[_0x5a75bb(0x1d7)][_0x5a75bb(0x291)](mars3d__namespace['EventType']['cameraMoveStart'],this['_onMoveStartEvent'],this),this[_0x5a75bb(0x1d7)][_0x5a75bb(0x291)](mars3d__namespace['EventType'][_0x5a75bb(0x201)],this[_0x5a75bb(0x223)],this),this['_map'][_0x5a75bb(0x291)](mars3d__namespace[_0x5a75bb(0x230)]['postRender'],this[_0x5a75bb(0x277)],this),(_0x4c474b=this[_0x5a75bb(0x25a)])!==null&&_0x4c474b!==void 0x0&&(_0x16fd5c=_0x4c474b[_0x5a75bb(0x219)])!==null&&_0x16fd5c!==void 0x0&&_0x16fd5c[_0x5a75bb(0x25c)]&&this['_map']['off'](mars3d__namespace[_0x5a75bb(0x230)][_0x5a75bb(0x25c)],this['_onMapClick'],this),(_0x24e628=this['options'])!==null&&_0x24e628!==void 0x0&&(_0x307fb4=_0x24e628[_0x5a75bb(0x219)])!==null&&_0x307fb4!==void 0x0&&_0x307fb4[_0x5a75bb(0x256)]&&this[_0x5a75bb(0x1d7)][_0x5a75bb(0x291)](mars3d__namespace['EventType'][_0x5a75bb(0x260)],this[_0x5a75bb(0x1f6)],this);}},{'key':_0x11f4d0(0x274),'value':function _0x3c8bf6(){var _0x165646=_0x11f4d0;this['_mapVRenderer']&&(this[_0x165646(0x278)]['animatorMovestartEvent'](),this[_0x165646(0x1d7)][_0x165646(0x291)](mars3d__namespace['EventType'][_0x165646(0x273)],this[_0x165646(0x277)],this),this[_0x165646(0x1d7)]['on'](mars3d__namespace['EventType'][_0x165646(0x273)],this[_0x165646(0x277)],this));}},{'key':_0x11f4d0(0x223),'value':function _0x215f1f(){var _0x289504=_0x11f4d0;this[_0x289504(0x278)]&&(this[_0x289504(0x1d7)][_0x289504(0x291)](mars3d__namespace['EventType']['postRender'],this[_0x289504(0x277)],this),this[_0x289504(0x278)][_0x289504(0x276)](),this[_0x289504(0x277)]());}},{'key':_0x11f4d0(0x248),'value':function _0x463936(_0x3bf210){var _0x9702a5=_0x11f4d0;this[_0x9702a5(0x292)](),this[_0x9702a5(0x22e)]();}},{'key':'addData','value':function _0x247ffc(_0x3c9226){var _0x1cb367=_0x11f4d0;this[_0x1cb367(0x278)]&&this[_0x1cb367(0x278)][_0x1cb367(0x214)](_0x3c9226,this[_0x1cb367(0x25a)]);}},{'key':_0x11f4d0(0x1da),'value':function _0x1cb9d2(_0x2660ef){var _0x5791f1=_0x11f4d0;this[_0x5791f1(0x278)]&&this[_0x5791f1(0x278)][_0x5791f1(0x1da)](_0x2660ef,this[_0x5791f1(0x25a)]);}},{'key':_0x11f4d0(0x227),'value':function _0x376f23(){var _0x4861a1=_0x11f4d0;return this[_0x4861a1(0x278)]&&(this[_0x4861a1(0x1fd)]=this[_0x4861a1(0x278)][_0x4861a1(0x227)]()),this['dataSet'];}},{'key':_0x11f4d0(0x202),'value':function _0x123173(_0x579cf4){var _0x57717c=_0x11f4d0;this[_0x57717c(0x278)]&&this['_mapVRenderer'][_0x57717c(0x202)](_0x579cf4);}},{'key':_0x11f4d0(0x242),'value':function _0x1e91b2(){var _0x3362ce=_0x11f4d0;this[_0x3362ce(0x278)]&&this[_0x3362ce(0x278)][_0x3362ce(0x285)]();}},{'key':_0x11f4d0(0x1f5),'value':function _0x4d1412(){var _0x90bbfb=_0x11f4d0,_0x8cb30c=mars3d__namespace[_0x90bbfb(0x251)][_0x90bbfb(0x207)](_0x90bbfb(0x233),'mars3d-mapv',this['_map'][_0x90bbfb(0x27f)]);_0x8cb30c['id']=this[_0x90bbfb(0x25a)][_0x90bbfb(0x225)]||_0x90bbfb(0x268)+divId++,_0x8cb30c['style'][_0x90bbfb(0x24d)]='absolute',_0x8cb30c[_0x90bbfb(0x1e1)][_0x90bbfb(0x234)]=_0x90bbfb(0x204),_0x8cb30c[_0x90bbfb(0x1e1)][_0x90bbfb(0x232)]=_0x90bbfb(0x204),_0x8cb30c[_0x90bbfb(0x1e1)]['pointerEvents']=this['_pointerEvents']?_0x90bbfb(0x26e):'none',_0x8cb30c[_0x90bbfb(0x1e1)][_0x90bbfb(0x205)]=this[_0x90bbfb(0x25a)][_0x90bbfb(0x205)]||0x64,_0x8cb30c[_0x90bbfb(0x206)]=parseInt(this[_0x90bbfb(0x1d7)][_0x90bbfb(0x233)]['width']),_0x8cb30c[_0x90bbfb(0x1e7)]=parseInt(this[_0x90bbfb(0x1d7)]['canvas']['height']),_0x8cb30c[_0x90bbfb(0x1e1)][_0x90bbfb(0x206)]=this[_0x90bbfb(0x1d7)]['canvas'][_0x90bbfb(0x1e1)][_0x90bbfb(0x206)],_0x8cb30c[_0x90bbfb(0x1e1)][_0x90bbfb(0x1e7)]=this[_0x90bbfb(0x1d7)]['canvas'][_0x90bbfb(0x1e1)][_0x90bbfb(0x1e7)];if(this[_0x90bbfb(0x25a)][_0x90bbfb(0x1f1)]==='2d'){var _0x51b199=this[_0x90bbfb(0x28c)];_0x8cb30c[_0x90bbfb(0x226)](this[_0x90bbfb(0x25a)]['context'])[_0x90bbfb(0x235)](_0x51b199,_0x51b199);}return _0x8cb30c;}},{'key':'_reset','value':function _0x1b8f48(){var _0x3a64e2=_0x11f4d0;this['resize'](),this[_0x3a64e2(0x1e3)]();}},{'key':_0x11f4d0(0x253),'value':function _0x44e2b9(){var _0x44d14d=_0x11f4d0;this[_0x44d14d(0x277)]();}},{'key':_0x11f4d0(0x243),'value':function _0x5f00d7(){var _0xf13d0e=_0x11f4d0;this['_mapVRenderer']&&(this[_0xf13d0e(0x278)]['destroy'](),this[_0xf13d0e(0x278)]=null),this[_0xf13d0e(0x233)][_0xf13d0e(0x26f)][_0xf13d0e(0x275)](this[_0xf13d0e(0x233)]);}},{'key':_0x11f4d0(0x1e3),'value':function _0x11c166(){var _0x438ec2=_0x11f4d0;this[_0x438ec2(0x278)]['_canvasUpdate']();}},{'key':'resize','value':function _0x494c7a(){var _0x194b1b=_0x11f4d0;if(this[_0x194b1b(0x233)]){var _0x54910b=this[_0x194b1b(0x233)];_0x54910b[_0x194b1b(0x1e1)][_0x194b1b(0x24d)]=_0x194b1b(0x25e),_0x54910b['style']['top']='0px',_0x54910b[_0x194b1b(0x1e1)]['left']=_0x194b1b(0x204),_0x54910b['width']=parseInt(this[_0x194b1b(0x1d7)][_0x194b1b(0x233)][_0x194b1b(0x206)]),_0x54910b[_0x194b1b(0x1e7)]=parseInt(this[_0x194b1b(0x1d7)]['canvas']['height']),_0x54910b[_0x194b1b(0x1e1)][_0x194b1b(0x206)]=this[_0x194b1b(0x1d7)][_0x194b1b(0x233)][_0x194b1b(0x1e1)][_0x194b1b(0x206)],_0x54910b[_0x194b1b(0x1e1)][_0x194b1b(0x1e7)]=this[_0x194b1b(0x1d7)][_0x194b1b(0x233)]['style']['height'];}}},{'key':_0x11f4d0(0x265),'value':function _0x58de51(_0x460938){var _0x44e170=_0x11f4d0;if(!this[_0x44e170(0x1fd)]||!this[_0x44e170(0x1fd)][_0x44e170(0x210)])return;var _0x147efc=mars3d__namespace[_0x44e170(0x258)]['getExtentByGeoJSON']({'type':_0x44e170(0x284),'features':this[_0x44e170(0x1fd)][_0x44e170(0x210)]});if(!_0x147efc)return;return _0x460938!==null&&_0x460938!==void 0x0&&_0x460938[_0x44e170(0x1de)]?_0x147efc:Cesium[_0x44e170(0x289)][_0x44e170(0x20f)](_0x147efc[_0x44e170(0x1e8)],_0x147efc[_0x44e170(0x27a)],_0x147efc[_0x44e170(0x1e6)],_0x147efc[_0x44e170(0x281)]);}},{'key':_0x11f4d0(0x24a),'value':function _0x41a1b2(_0x390294){var _0x143037=_0x11f4d0;this[_0x143037(0x259)]=_0x390294,this['_mapVRenderer']&&this['_mapVRenderer'][_0x143037(0x279)](_0x390294[_0x143037(0x22d)],_0x390294);}},{'key':_0x11f4d0(0x1f6),'value':function _0x1e2cb0(_0x435d39){var _0x1d30a0=_0x11f4d0;this[_0x1d30a0(0x259)]=_0x435d39,this['_mapVRenderer']&&this[_0x1d30a0(0x278)]['mousemoveEvent'](_0x435d39[_0x1d30a0(0x22d)],_0x435d39);}},{'key':'on','value':function _0x39e88a(_0x2b5fd3,_0x1075e5,_0x430294){var _0x1bfe47=_0x11f4d0,_0x2e57ca=this;this['options'][_0x1bfe47(0x219)]=this[_0x1bfe47(0x25a)][_0x1bfe47(0x219)]||{};if(_0x2b5fd3===mars3d__namespace['EventType'][_0x1bfe47(0x25c)])this['options']['methods'][_0x1bfe47(0x25c)]=function(_0x5c68f2){var _0x37ef00=_0x1bfe47;_0x5c68f2&&_0x1075e5[_0x37ef00(0x221)](_0x430294)(_objectSpread2(_objectSpread2({},_0x2e57ca['_cache_event']),{},{'layer':_0x2e57ca,'data':_0x5c68f2}));},this[_0x1bfe47(0x1d7)]['on'](mars3d__namespace['EventType'][_0x1bfe47(0x25c)],this['_onMapClick'],this);else _0x2b5fd3===mars3d__namespace[_0x1bfe47(0x230)][_0x1bfe47(0x260)]&&(this[_0x1bfe47(0x25a)]['methods']['mousemove']=function(_0x42808c){_0x42808c&&_0x1075e5['bind'](_0x430294)(_objectSpread2(_objectSpread2({},_0x2e57ca['_cache_event']),{},{'layer':_0x2e57ca,'data':_0x42808c}));},this[_0x1bfe47(0x1d7)]['on'](mars3d__namespace[_0x1bfe47(0x230)][_0x1bfe47(0x260)],this[_0x1bfe47(0x1f6)],this));return this;}},{'key':'off','value':function _0x21497f(_0x41d54b,_0x173160){var _0xe28384=_0x11f4d0;if(_0x41d54b==='click'){var _0x13434e;this[_0xe28384(0x1d7)][_0xe28384(0x291)](_0x41d54b,this[_0xe28384(0x24a)],this),(_0x13434e=this[_0xe28384(0x25a)][_0xe28384(0x219)])!==null&&_0x13434e!==void 0x0&&_0x13434e[_0xe28384(0x256)]&&delete this['options'][_0xe28384(0x219)]['click'];}else{if(_0x41d54b==='mouseMove'){var _0x128330;this[_0xe28384(0x1d7)]['off'](_0x41d54b,this[_0xe28384(0x1f6)],this),(_0x128330=this[_0xe28384(0x25a)][_0xe28384(0x219)])!==null&&_0x128330!==void 0x0&&_0x128330[_0xe28384(0x256)]&&delete this[_0xe28384(0x25a)][_0xe28384(0x219)][_0xe28384(0x256)];}}return this;}}]),_0x5cfe90;}(BaseLayer);mars3d__namespace['LayerUtil'][_0x52e6a5(0x237)](_0x52e6a5(0x268),MapVLayer),mars3d__namespace[_0x52e6a5(0x1f9)][_0x52e6a5(0x262)]=MapVLayer,exports['MapVLayer']=MapVLayer,Object[_0x52e6a5(0x1fe)](mapv)[_0x52e6a5(0x22f)](function(_0x5e9f52){var _0x5511bc=_0x52e6a5;if(_0x5e9f52!==_0x5511bc(0x25b)&&!exports['hasOwnProperty'](_0x5e9f52))Object[_0x5511bc(0x1d6)](exports,_0x5e9f52,{'enumerable':!![],'get':function(){return mapv[_0x5e9f52];}});}),Object[_0x52e6a5(0x1d6)](exports,_0x52e6a5(0x209),{'value':!![]});
}));
