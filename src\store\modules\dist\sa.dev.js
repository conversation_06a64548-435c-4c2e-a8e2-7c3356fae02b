"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
// import { stat } from "fs"
// initial state
var state = {
  // 超级管理员授权管理-side-菜单列表
  SAMenuListData: {},
  SAPrivListData: {},
  SARoleListData: {
    tabCon: false
  }
};
var getters = {
  getSAMenuListData: function getSAMenuListData(state) {
    return state.SAMenuListData;
  },
  getSAPrivListData: function getSAPrivListData(state) {
    return state.SAPrivListData;
  },
  getSARoleListData: function getSARoleListData(state) {
    return state.SARoleListData;
  }
};
var actions = {//   setMapType({ state, commit }, maptype) {
  //     commit("updateMapType", maptype);
  //   }
};
var mutations = {
  updateSAMenuListData: function updateSAMenuListData(state, val) {
    state.SAMenuListData = val;
  },
  updateSAPrivListData: function updateSAPrivListData(state, val) {
    state.SAPrivListData = val;
  },
  updateSARoleListData: function updateSARoleListData(state, val) {
    state.SARoleListData = val;
  }
};
var _default = {
  namespaced: true,
  state: state,
  getters: getters,
  actions: actions,
  mutations: mutations
};
exports["default"] = _default;