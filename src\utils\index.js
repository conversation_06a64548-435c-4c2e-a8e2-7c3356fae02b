/**
 * Created by ji<PERSON><PERSON><PERSON> on 16/11/18.
 */

export function parseTime(time, cFormat) {
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (("" + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
  return time_str;
}

export function formatTime(time, option) {
  // time = +time * 1000
  const d = new Date(time);
  const now = Date.now();

  // const diff = (now - d) / 1000

  // if (diff < 30) {
  //     return '刚刚'
  // } else if (diff < 3600) {
  //     // less 1 hour
  //     return Math.ceil(diff / 60) + '分钟前'
  // } else if (diff < 3600 * 24) {
  //     return Math.ceil(diff / 3600) + '小时前'
  // } else if (diff < 3600 * 24 * 2) {
  //     return '1天前'
  // }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getFullYear() +
      "年" +
      (parseInt(d.getMonth()) + 1) +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

export function dateFormat(fmt, date) {
  let ret;
  const opt = {
    "Y+": date.getFullYear().toString(), // 年
    "m+": (date.getMonth() + 1).toString(), // 月
    "d+": date.getDate().toString(), // 日
    "H+": date.getHours().toString(), // 时
    "M+": date.getMinutes().toString(), // 分
    "S+": date.getSeconds().toString(), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };
  for (let k in opt) {
    ret = new RegExp("(" + k + ")").exec(fmt);
    if (ret) {
      fmt = fmt.replace(
        ret[1],
        ret[1].length == 1 ? opt[k] : opt[k].padStart(ret[1].length, "0")
      );
    }
  }
  return fmt;
}

export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    //throw new Error('error arguments', 'shallowClone');
    console.log("深克隆失败");
    return source;
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    //遍历到最后一层是否为该数据类型
    if (source[keys] && typeof source[keys] === "object") {
      //递归
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * 函数节流
 * @param idle 空闲时间，单位毫秒
 * @param action 请求关联函数，实际应用需要调用的函数
 * @returns {Function} 返回客户调用函数
 */
export function debounce(idle, action) {
  let last;
  return function () {
    const args = arguments;
    last && window.clearTimeout(last);
    last = window.setTimeout(() => {
      action.apply(this, args);
    }, idle);
  };
}
//设置echarts随着窗口的大小而改变
export function setEchart(eleName, w, h) {
  var echartsWarp = document.getElementById(eleName);
  var resizeWorldMapContainer = function () {
    //用于使chart自适应高度和宽度,通过窗体高宽计算容器高宽
    echartsWarp.style.width = window.innerWidth - w + "px";
    echartsWarp.style.height = window.innerHeight - h + "px";
  };
  resizeWorldMapContainer(); //设置容器高宽
  var myChart = this.$echarts.init(echartsWarp);
  window.onresize = function () {
    //用于使chart自适应高度和宽度
    resizeWorldMapContainer(); //重置容器高宽
    myChart.resize();
  };
}
export function scrollToError() {
  setTimeout(() => {
    const isError = document.getElementsByClassName("is-error");
    isError[0].querySelector("input").focus();
  }, 100);
  return false;
}
export function Format(fmt) {
  var o = {
    "M+": this.getMonth() + 1, // 月份
    "d+": this.getDate(), // 日
    "h+": this.getHours(), // 小时
    "m+": this.getMinutes(), // 分
    "s+": this.getSeconds(), // 秒
    "q+": Math.floor((this.getMonth() + 3) / 3), // 季度
    S: this.getMilliseconds(), // 毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, this.getFullYear() + "");
  for (var k in o)
    if (new RegExp("(" + k + ")").test(fmt))
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
  return fmt;
}
