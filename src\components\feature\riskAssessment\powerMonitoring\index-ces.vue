<template>
  <div class="riskDynamic">
    <header>
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" /> 风险动态研判
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </header>

    <div class="riskDynamicBox">
      <div class="riskDynamicL">
        <h3>湖北省</h3>
        <div class="riskDynamicLevel">
          <div
            class="riskDynamicLevelItem"
            v-for="(item, index) of LevelData"
            :key="index"
          >
            <div :class="['icon', 'icon' + item.level]"></div>
            <div class="itemcon">
              <p>{{ item.name }}</p>
              <div>
                <span>{{ item.num }}</span
                >家
              </div>
            </div>
          </div>
        </div>
        <div class="map">
          <img src="../../../../../static/img/map.png" />
        </div>
      </div>
      <div class="riskDynamicR">
        <div class="titlList">较大风险及以上企业清单</div>
        <div class="jiaoList">
          <div class="header">
            <div class="order"><span>序号</span></div>
            <div class="title">企业名称</div>
            <div class="ping">风险评分</div>
            <div class="level">风险等级</div>
          </div>
          <div class="jiaoItem" v-for="(el, index) of jiaoData" :key="index">
            <div class="order">
              <span>{{ index + 1 }}</span>
            </div>
            <div class="title">{{ el.title }}</div>
            <div class="ping">{{ el.ping }}</div>
            <div class="level">{{ el.level }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  data() {
    return {
      LevelData: [
        {
          name: "重点风险",
          num: 1,
          level: 1,
        },
        {
          name: "较大风险",
          num: 2,
          level: 2,
        },
        {
          name: "一般风险",
          num: 3,
          level: 3,
        },
        {
          name: "低风险",
          num: 4,
          level: 4,
        },
      ],
      jiaoData: [
        {
          title: "武汉恒基达鑫国际化工仓储有限公司",
          ping: "87",
          level: "重大",
        },
        {
          title: "武汉恒基达鑫国际化工仓储有限公司",
          ping: "87",
          level: "重大",
        },
        {
          title: "武汉恒基达鑫国际化工仓储有限公司",
          ping: "87",
          level: "重大",
        },
        {
          title: "武汉恒基达鑫国际化工仓储有限公司",
          ping: "87",
          level: "重大",
        },
      ],
    };
  },
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.riskDynamic {
  header {
    background-color: #fff;
    overflow: hidden;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  .riskDynamicBox {
    display: flex;
    .riskDynamicL {
      width: calc(100% - 400px);
      padding: 0 50px 0 0;
      box-sizing: border-box;
      .map img {
        width: 100%;
      }
      .riskDynamicLevel {
        display: flex;
        justify-content: space-between;
        .riskDynamicLevelItem {
          display: flex;
          width: 23%;
          background: #f7f8fa;
          align-items: center;
          padding: 20px 10px 20px 20px;
          .itemcon {
            padding: 0 0 0 20px;
            p {
              margin: 0 0 5px 0;
              font-size: 18px;
            }
            span {
              font-size: 18px;
            }
          }
        }

        .icon {
          width: 40px;
          height: 40px;
        }
        .icon1 {
          background: url("../../../../../static/img/lev1.png") no-repeat center;
        }
        .icon2 {
          background: url("../../../../../static/img/lev2.png") no-repeat center;
        }
        .icon3 {
          background: url("../../../../../static/img/lev3.png") no-repeat center;
        }
        .icon4 {
          background: url("../../../../../static/img/lev4.png") no-repeat center;
        }
      }
    }
    .riskDynamicR {
      width: 400px;
      // border: 1px solid red;
    }
  }
  .jiaoList {
    .header {
      display: flex;
      height: 40px;
      line-height: 40px;
      background: #f7f8fa;
    }
    .jiaoItem {
      display: flex;
      justify-content: space-between;
    }
    .order {
      width: 10%;
    }
    .title {
      width: 50%;
    }
    .ping,
    .level {
      width: 20%;
      text-align: center;
    }
    .jiaoItem {
       border-bottom: 1px solid #d9d9d9;
       padding:10px 0;
      .order {
        span {
          min-width: 26px;
          height: 26px;
          border-radius: 3px;
          background: #ebf1fd;
          color: #326eff;
          display: inline-block;
          text-align: center;
          line-height: 26px;
        }
      }
      .title {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .titlList {
    font-size: 16px;
    color: #252525;
    position: relative;
    padding: 0 0 0 15px;
  }
  .titlList::before {
    position: absolute;
    left: 0;
    top: 0;
    content: "";
    height: 20px;
    width: 4px;
    background: #3977ea;
  }
}
</style>