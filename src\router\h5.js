import Vue from 'vue'
import VueRouter from 'vue-router'

Vue.use(VueRouter)

// H5页面路由配置 - 只包含必要的路由
const routes = [
  {
    path: '/',
    redirect: '/reportH5'
  },
  {
    path: '/reportH5',
    name: 'reportH5',
    component: () => import(
      /* webpackChunkName: "h5-report" */ 
      '@/components/feature/workingAccount/smartReport/gatherReport/reportH5.vue'
    ),
    meta: {
      title: '智能报告H5',
      keepAlive: false
    }
  }
]

const router = new VueRouter({
  mode: 'hash', // H5使用hash模式更稳定
  routes
})

export default router
