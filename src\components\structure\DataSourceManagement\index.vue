<template>
  <div class="DataSourceManagement">
    <div class="header">
      <el-button class="addBtn" type="primary" @click="showDialog"
        >新增</el-button
      >
    </div>
    <div class="container">
      <div class="table">
        <el-table
          ref="multipleTable"
          :data="tableData.records"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column>
          <el-table-column label="名称" width="180" align="center">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column
            prop="url"
            label="jdbcUrl"
            width="220"
            align="center"
          >
            <template slot-scope="{ row, column, $index, store }">
              <el-tooltip
                class="item"
                effect="dark"
                :content="row.url"
                placement="top-start"
              >
                <span
                  style="
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    word-break: break-all;
                    display: inline-block;
                    width: 200px;
                  "
                  >{{ row.url }}</span
                >
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column
            prop="username"
            label="用户名"
            width="120"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="password"
            width="220"
            label="密码"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="createDate"
            label="创建时间"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="updateDate"
            label="更新时间"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
            align="center"
            show-overflow-tooltip
            fixed="right"
            width="140"
          >
            <template slot-scope="scope"
              ><el-button size="mini" type="text" @click="showDialog(scope)"
                >编辑</el-button
              >
              <el-button size="mini" type="text" @click="del(scope)"
                >删除</el-button
              ></template
            >
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="tableData.size"
          layout="total, prev, pager, next"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="from"
        class="dialog"
        :model="from"
        :rules="rules"
        label-width="80px"
      >
        <!-- :model="form" -->
        <el-form-item label="名称:" prop="name">
          <el-input
            class="input"
            v-model.trim="from.name"
            placeholder="请输入名称"
          ></el-input>
        </el-form-item>
        <el-form-item label="jdbcUrl:" prop="url">
          <el-input
            class="input"
            v-model.trim="from.url"
            placeholder="请输入jdbcUrl"
          ></el-input>
        </el-form-item>
        <el-form-item label="用户名:" prop="username">
          <el-input
            class="input"
            v-model.trim="from.username"
            placeholder="请输入用户名"
          ></el-input>
        </el-form-item>
        <el-form-item label="密码:" prop="password">
          <el-input
            class="input"
            v-model.trim="from.password"
            placeholder="请输入密码"
          ></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" @click="save('form')">保存</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getGenDsconf, saveDsconf, deleteDsconf } from "@/api/gen.js";
export default {
  //import引入的组件
  components: {},
  data() {
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入名称"));
      } else {
        // 检查是否包含小写字母和下划线
        var hasLowerCase = /[a-z]/.test(value);
        var hasUnderscore = /_/.test(value);
        if (value && (!hasLowerCase || !hasUnderscore)) {
          callback(new Error("数据源名称不合法, 组名_数据源名形式"));
        } else {
          callback();
        }
      }
    };
    return {
      tableData: {},
      currentPage: 1,
      dialogVisible: false,
      from: {
        name: "",
        url: "",
        username: "",
        password: "",
      },
      rules: {
        name: [{ validator: validatePass, trigger: "blur" }],
        url: [{ required: true, message: "请输入jdbcUrl", trigger: "blur" }],
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    };
  },
  //方法集合
  methods: {
    getData() {
      getGenDsconf({ current: 1, size: 20 }).then((res) => {
        this.tableData = res.data.data;
      });
    },
    showDialog(data) {
      this.dialogVisible = true;
      if (data.row) {
        this.from = data.row;
        this.from.password = "";
      } else {
        this.from = {
          name: "",
          url: "",
          username: "",
          password: "",
        };
      }
    },
    del(data) {
      this.$confirm("确定要删除选择的数据吗？", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteDsconf(data.row.id)
            .then((res) => {
              this.$message({
                message: "操作成功",
                type: "success",
              });
              this.getData();
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    save(from) {
      this.$refs["from"].validate((valid) => {
        if (valid) {
          saveDsconf(this.from).then((res) => {
            console.log(res);
            if (res.data.data) {
              this.submitting = false;
              this.$message({
                type: "success",
                message: "操作成功",
              });
              this.dialogVisible = false;
              this.getData();
            } else {
              this.submitting = false;
              this.$message({
                type: "info",
                message: "添加失败，数据源不可用",
              });
            }
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
};
</script>
<style lang="scss" scoped>
.DataSourceManagement {
  background-color: #fff;
  .header {
    .addBtn {
      margin: 20px 30px;
    }
  }
  .container {
    .table {
      padding: 0 30px;
    }
    .pagination {
      display: flex;
      justify-content: flex-end;
    }
  }
  .dialog {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 400px;
    .input {
      width: 400px;
    }
  }
}
</style>
