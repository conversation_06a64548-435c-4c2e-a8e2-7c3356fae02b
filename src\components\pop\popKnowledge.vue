<template>
  <div class="popKnowledge">
    <el-dialog
      @close="closeFun()"
      title="知识库"
      :visible.sync="open"
      width="800px"
      v-if="open"
      append-to-body
      top="5vh"
      :close-on-click-modal="false"
    >
      <div class="dialogInfo">
        <div class="topTit">
          <p>快速匹配</p>

          <div class="audioBox">
            <el-autocomplete
              popper-class="my-autocomplete"
              v-model="queryParams.msdstitle"
              :fetch-suggestions="querySearch"
              placeholder="请输入物质关键词"
              clearable
              ref="forcusTitle"
              @clear="clearSensororgCode()"
              @select="handleSelect"
              style="width: 400px"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.msdstitle }}</div>
              </template>
            </el-autocomplete>
            <div class="audio-wrap">
              <img
                v-show="startRecord"
                src="../../../static/img/videogif.gif"
                alt
                class="voicegif"
              />
              <img
                class="voiceBtn"
                v-show="!startRecord && !analysic"
                src="../../../static/img/voice.png"
              />
              <div class="scanner" v-show="!startRecord && !analysic">
                <div>{{ audiotext }}</div>
              </div>
            </div>

            <div class="aduio-controll-btn">
              <el-button type="primary" @click="startRecordFn">录制</el-button>
              <el-button type="primary" @click="recStop">结束</el-button>
            </div>
          </div>
        </div>

        <div class="tit">危害性</div>
        <div class="con">{{ huaData.toxicology || "暂无危害性" }}</div>
        <div class="tit">处置措施建议</div>
        <div class="con">{{ huaData.transport || "暂无处置措施建议" }}</div>
      </div>
      
    </el-dialog>
  </div>
</template>

<script>
import { knowledgeFindById, msdstitles, audioToText } from "@/api/entList.js";
export default {
  name: "popKnowledge",
  components: {},
  props: {
    closable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      rec: null,
      analysic: false,
      audiotext: "",
      startRecord: false,
      startVoice: true, // 语音开始状态
      enterVoice: false, // 语音录入状态
      analysisVoice: false, // 语音分析状态
      arr: [1, 2],
      huaData: {},

      queryParams: {
        msdsid: "",
        msdstitle: "",
      },
      loading: true,
      open: false,
    };
  },
  watch: {},
  created() {},
  methods: {
    closeFun() {
      this.queryParams.msdstitle = "";
    },
    //自动输入
    clearSensororgCode() {
      this.queryParams.msdstitle = "";
      this.queryParams.msdsid = "";
      this.huaData = {};
      this.arr = [];
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      var param = {
        msdstitle: keyWord,
        nowPage: 0,
        pageSize: 500,
      };
      knowledgeFindById(param)
        .then((res) => {
          if (res.data.status == 200) {
            this.$refs.forcusTitle.focus();
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.msdsid = item.msdsid;
      this.queryParams.msdstitle = item.msdstitle;
      this.huaData = item;
      //  var param={
      //    "msdstitle": item.msdstitle,
      //     "nowPage": 0,
      //     "pageSize": 500
      // }
      // msdstitles(param).then(res => {
      //   if (res.data.status == 200) {
      //   }
      // })
    },
    //自动输入end
    getData(row, col) {},

    startRecordFn() {
      this.startRecord = true;
      this.recOpen(this.recStart);
    },

    /**开始录音**/
    recStart() {
      //打开了录音后才能进行start、stop调用
      this.rec.start();
      this.audiotext = "";
      this.analysic = false;
    },

    /**结束录音**/
    recStop() {
      const vm = this;
      this.startRecord = false;
      this.analysic = true;
      this.loading = true;
      // vm.isVoiceAnalysis = true;
      this.rec.stop(
        (blob, duration) => {
          console.log(
            blob,
            (window.URL || webkitURL).createObjectURL(blob),
            "时长:" + duration + "ms"
          );
          this.rec.close();
          //释放录音资源，当然可以不释放，后面可以连续调用start；但不释放时系统或浏览器会一直提示在录音，最佳操作是录完就close掉
          this.rec = null;
          //已经拿到blob文件对象想干嘛就干嘛：立即播放、上传

          /*** 【立即播放例子】 ***/
          let audio = document.createElement("audio");
          audio.controls = false;
          document.body.appendChild(audio);
          //简单利用URL生成播放地址，注意不用了时需要revokeObjectURL，否则霸占内存
          audio.src = (window.URL || webkitURL).createObjectURL(blob);

          // audio.play();

          let form = new FormData();
          form.append("file", blob, "recorder.wav");
          //和普通form表单并无二致，后端接收到upfile参数的文件，文件名为recorder.mp3
          audioToText(form).then((res) => {
            this.analysic = false;
            // this.audiotext = res.data.data;
            if (res.data.status == 200) {
              var text = res.data.data;
              text = text.replace(/，|,|。|\.|？/g, "");
              this.queryParams.msdstitle = text;
              this.querySearch(text);              
            } else {
              this.$message.error("未解析到数据");
            }
            // if (res.data.data?.length > 0) {
            //   this.audiotext = res.data.data[0].audioText;
            //   let command = res.data.data[0].url;
            //   this.loading = false;
            //   let parseType = res.data.data[0].parseType;
            //   let parseValue = res.data.data[0].parseValue;

            //   // if (!parseType) {
            //   //   switch (command) {
            //   //     case "5":
            //   //       // 常态页面
            //   //       this.messsageBus.emit("audioController", 0);
            //   //       break;
            //   //     case "6":
            //   //       // 会商研判页面页面
            //   //       if (this.$store.state.eventModule.eventid) {
            //   //         this.messsageBus.emit("audioController", 1);
            //   //       } else {
            //   //         this.$message.error("请选择相关事件");
            //   //       }
            //   //       break;
            //   //     case "7":
            //   //       // 指挥调度页面
            //   //       if (this.$store.state.eventModule.eventid) {
            //   //         this.messsageBus.emit("audioController", 2);
            //   //       } else {
            //   //         this.$message.error("请选择相关事件");
            //   //       }
            //   //       break;
            //   //     case "4":
            //   //       // 通讯录
            //   //       this.messsageBus.emit("openAddress", true);
            //   //       break;
            //   //     case "3":
            //   //       // 协同会商
            //   //       this.messsageBus.emit("openSynergyConsultation", true);
            //   //       break;
            //   //     case "2":
            //   //       // 测绘
            //   //       this.messsageBus.emit("openPlot", true);
            //   //       break;
            //   //     case "1":
            //   //       // 音视频通信
            //   //       if (!this.showCommunicationManage) {
            //   //         this.showCommunicationManage = true;
            //   //       }
            //   //       break;
            //   //     default:
            //   //     // this.$message.error('解析错误');
            //   //   }
            //   // } else {
            //   //   let phone = parseValue.split("_");
            //   //   if (parseType == 1) {
            //   //     // 电话解析

            //   //     if (phone[1]) {
            //   //       // this.messsageBus.emit('openPanel', {
            //   //       //   show: true,
            //   //       //   telNumber: phone[1],
            //   //       // });
            //   //       util.telCall(phone[1]);
            //   //     }
            //   //   } else {
            //   //     // 视频解析
            //   //     onlienPawnServer
            //   //       .getOnlinepawnListSearch(
            //   //         3,
            //   //         `DEVICE_PHONE,DEVICE_BODY_WORN_CAMERA,DEVICE_UAV,DEVICE_28181_CONTROL_BALL,DEVICE_28181_SINGLE,DEVICE_BDS_CAR,DEVICE_LTE,DEVICE_HELMET,DEVICE_OUT_SIP`,
            //   //         "",
            //   //         "ONLINE"
            //   //       )
            //   //       .then((res) => {
            //   //         let equipmentArray = (res.data.data.records || []).map(
            //   //           (el) => {
            //   //             return {
            //   //               ...el,
            //   //               PINYIN: pinyin(el.name, { toneType: "none" }),
            //   //             };
            //   //           }
            //   //         );
            //   //         equipmentArray.map((el) => {
            //   //           if (
            //   //             pinyin(phone[0], { toneType: "none" }) == el.PINYIN
            //   //           ) {
            //   //             if (el.status === "ONLINE") {
            //   //               if (el.deviceSubType === "DEVICE_LTE") {
            //   //                 util.individualCall(el.deviceNo, "", "");
            //   //               } else {
            //   //                 util.individualCall(el.no, "", "");
            //   //               }
            //   //             }
            //   //           }
            //   //         });
            //   //       });
            //   //   }
            //   // }
            // } else {
            //   this.$message.error("未解析到数据");
            // }
            // this.audiotext = '';
            this.loading = false;
            this.analysic = false;
          });
          // .catch((e) => {
          //   this.$message.error("未能解析到相关数据");
          //   this.audiotext = "";
          //   this.loading = false;
          //   this.analysic = false;
          // });
        },
        function (msg) {
          console.log("录音失败:" + msg);
          console.log(vm, "this");
          // (vm as any).$message.error('录音失败:' + msg);
          vm.audiotext = "";
          vm.loading = false;
          vm.analysic = false;
          vm.rec.close(); //可以通过stop方法的第3个参数来自动调用close
          vm.rec = null;
        }
      );
    },

    /**调用open打开录音请求好录音权限**/
    recOpen(success) {
      //一般在显示出录音按钮或相关的录音界面时进行此方法调用，后面用户点击开始录音时就能畅通无阻了
      this.rec = Recorder({
        type: "wav",
        sampleRate: 16000,
        bitRate: 16,
        //mp3格式，指定采样率hz、比特率kbps，其他参数使用默认配置；注意：是数字的参数必须提供数字，不要用字符串；需要使用的type类型，需提前把格式支持文件加载进来，比如使用wav格式需要提前加载wav.js编码引擎
        onProcess: function (
          buffers,
          powerLevel,
          bufferDuration,
          bufferSampleRate,
          newBufferIdx,
          asyncEnd
        ) {
          //录音实时回调，大约1秒调用12次本回调
          //可利用extensions/waveview.js扩展实时绘制波形
          //可利用extensions/sonic.js扩展实时变速变调，此扩展计算量巨大，onProcess需要返回true开启异步模式
        },
      });
      //var dialog=createDelayDialog(); 我们可以选择性的弹一个对话框：为了防止移动端浏览器存在第三种情况：用户忽略，并且（或者国产系统UC系）浏览器没有任何回调，此处demo省略了弹窗的代码
      this.rec.open(
        function () {
          //打开麦克风授权获得相关资源
          //dialog&&dialog.Cancel(); 如果开启了弹框，此处需要取消
          //rec.start() 此处可以立即开始录音，但不建议这样编写，因为open是一个延迟漫长的操作，通过两次用户操作来分别调用open和start是推荐的最佳流程

          success && success();
        },
        function (msg, isUserNotAllow) {
          //用户拒绝未授权或不支持
          //dialog&&dialog.Cancel(); 如果开启了弹框，此处需要取消
          console.log(
            (isUserNotAllow ? "UserNotAllow，" : "") + "无法录音:" + msg
          );
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.dialogInfo {
  max-height: 600px;
  overflow-y: scroll;
  .topTit {
    margin: 0 0 10px 0;
    > p {
      color: #3977ea;
      font-size: 22px;
    }
  }
  .tit {
    padding: 10px 0;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    color: #000;
    font-size: 20px;
  }
  .con {
    padding: 0 0 20px 0;
    font-size: 16px;
  }
  .audioBox{
    display: flex;
  }
  .audio-wrap{
    width:40px;
    margin:0 10px;
    img{
      height: 40px;
    width:40px;   
    }
    
  }
}
</style>