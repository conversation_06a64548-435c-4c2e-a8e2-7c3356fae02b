<template>
  <div class="notification">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span
              class="icon-box"
              @click="
                toUrl(
                  user.user_type == 'gov'
                    ? '/workbench/superviseWorkbench'
                    : '/workbench/enterWorkbench'
                )
              "
            >
              <a-icon type="home" theme="filled" class="icon" /> 工作台
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item> 通知中心 </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="tabs">
      <el-tabs v-model="type" @tab-click="handleClick">
        <el-tab-pane label="全部推送" name="0"></el-tab-pane>
        <el-tab-pane label="警示通报" name="1"></el-tab-pane>
        <el-tab-pane label="风险预警" name="2"></el-tab-pane>
        <el-tab-pane label="物联报警" v-if="$store.state.login.user.user_type == 'ent'" name="5"></el-tab-pane>
        <el-tab-pane label="视频分析告警" v-if="$store.state.login.user.user_type == 'ent'" name="6"></el-tab-pane>
        <el-tab-pane label="信息审核" name="3"></el-tab-pane>
        <el-tab-pane label="系统通知" name="4"></el-tab-pane>
      </el-tabs>
    </div>
    <el-button
      type="primary"
      size="mini"
      class="more readMore"
      plain
      @click="readNews"
      ><a-icon type="read" style="margin-right: 7px" />标记已读</el-button
    >
    <CA-button
      type="primary"
      size="mini"
      class="more"
      plain
      @click="allReadMsg('-1')"
      ><a-icon type="read" style="margin-right: 7px" />全部已读</CA-button
    >
    <div class="container">
      <el-table
        :data="tableData.records"
        style="width: 100%"
        v-loading="loading"
        ref="tableList"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        @select="select"
        @select-all="select"
        @selection-change	='selectChange'
        :row-style="{ cursor: 'pointer' }"
        @row-click="rowClick"
      >
        <el-table-column type="selection" width="55" align="center">
        </el-table-column>
        <el-table-column prop="type" width="60" label="类型" align="center">
          <template slot-scope="scope">
            <div>
              <div v-if="scope.row.type == 1" class="red">通报</div>
              <div v-else-if="scope.row.type == 2" class="orange">风险</div>
              <div v-else-if="scope.row.type == 3" class="blue">审核</div>
              <div v-else-if="scope.row.type == 4" class="green">通知</div>
               <div v-else-if="scope.row.type == 5" class="yellow">通知</div>
               <div v-else-if="scope.row.type == 6" class="pink">视频</div>
              <div v-else></div>
            </div>
          </template>
        </el-table-column>
        <el-table-column width="40" align="center">
          <template slot-scope="{ row, $index }">
            <div :class="row.id != colOpen ? 'msgImgOff' : 'msgImgOn'">
              <img
                src='../../../../static/img/msgOff.png'
                style="margin-top:3px;"
                v-show="row.state == 0"
                />
                <img
                src='../../../../static/img/msgOn.png'
                style="margin-top:1px"
                v-show="row.state == 1"
                />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="message"
          min-width="480"
          max-width="680"
          label="推送内容"
          label-class-name="labelCenter"
        >
          <template slot-scope="{ row, $index }">
            <!-- <div
              :class="row.id != colOpen ? 'tableColumn' : 'tableColumnActive'"
              :style="row.state == 0 ?'color:#333;': 'color:#848484;'"
            >
              {{ row.message }}
            </div> -->
            <div v-if="row.id == colOpen" class="tableColumnActive" :style="row.state == 0 ?'color:#333;': 'color:#848484;'">
              {{ row.message }}
              <p @click="goEntPatro" v-if="(user.distRole =='1' && row.functionType == '1' && $store.state.login.user.user_type == 'gov') || (user.distRole == '2' || row.functionType == '1' && $store.state.login.user.user_type == 'gov')">
                  点击"<span style="color:#409EFF">这里</span>"进入政府巡查反馈
              </p>
              <p @click="goEntCheck" v-if="$store.state.login.user.user_type == 'ent' && row.functionType == '2'">
                  点击"<span style="color:#409EFF">这里</span>"进入在线抽查反馈
              </p>
            </div>
            <div v-else class="tableColumn" :style="row.state == 0 ?'color:#333;': 'color:#848484;'">
              {{ row.message }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="creatTime"
          label="推送时间"
          width="180"
          align="center"
        >
          <template slot-scope="{ row, $index }">
            <div>
              {{ row.creatTime }}
            </div>
          </template>
        </el-table-column>
        <el-table-column width="35" align="center" fixed="right">
          <template slot-scope="{ row, $index }">
            <i
              :class="
                row.id != colOpen
                  ? 'el-icon-arrow-right tableColumnIcon'
                  : ' el-icon-arrow-right tableColumnIconActive'
              "
            ></i>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="tableData.size"
        layout="total, prev, pager, next"
        background
        :total="tableData.total"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { getMessageList, postMessageRead } from "@/api/workbench";
import { createNamespacedHelpers } from "vuex";
import Bus from "@/utils/bus";


const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {},
  data() {
    return {
      show: false,
      type: "0",
      tableData: {},
      currentPage: 1,
      collapsetypes: 1,
      loading: false,
      colOpen: "",
      selection: [],
      jobnumberKey: "",
      selectList:[]
    };
  },
  methods: {
    selectChange(val){
      this.selectList = val;
    },

    rowClick(row, column, event) {
      row.id != this.colOpen
        ? this.collapse(row.id, row.state)
        : (this.colOpen = "");
    },
    collapse(id, state) {
      this.colOpen = id;
      //如果状态是未读才调用
      if (state === "0") {
        this.readMsg(id);
      }
    },
    toUrl(url) {
      this.$router.push(url);
    },
    handleClick(tab, event) {
      this.colOpen = '';
      //改变地址栏参数
      this.toUrl("/notification?type=" + this.type);
      // this.getData();
    },
    getData(id) {
      if (!id) this.loading = true;
      getMessageList({
        type: this.type == "0" ? null : this.type,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (!id) this.loading = false;
        this.tableData = res.data.data;
      });
    },
    allReadMsg(all) {
      this.readMsg(this.selection.toString(","), all);
    },
    readNews(){
      let ids = this.selectList.map((el)=>{
        return el.id
      });
      if(ids.length>0){
        postMessageRead({
        ids:ids.join(',')
      }).then((res)=>{
        if (res.data.code === 0) {
          this.getData();
          this.$refs['tableList'].clearSelection();
          Bus.$emit("readNews");
        };
      })
      }
      
    },

    readMsg(id, all) {
      // console.log(id);
      postMessageRead({ ids: all === "-1" ? "-1" : id }).then((res) => {
        if (res.data.code === 0) {
          this.getData(id);
        }
      });
    },
    handleCurrentChange() {
      this.getData();
    },
    //收集列表id
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    goEntPatro() {
      this.$router.push({ path: "/dailySafetyManagement/patrolOnline" });
      this.$store.commit("controler/updateEntPatroName", "InspectFeedback");
    },
    goEntCheck(){
      this.$router.push({ path: "/dailySafetyManagement/checkOnline" });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
  },
  watch: {
    //实时监听路由参数改变选项卡
    $route: {
      handler(newVal, oldVal) {
        this.type = this.$route.query.type;
        this.getData();
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-collapse {
  border-top: 0;
  border-bottom: 0;
}
/deep/ .el-collapse-item__wrap {
  border-bottom: 0;
}
/deep/ .labelCenter {
  text-align: center;
}
.notification {
  position: relative;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  > .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .container {
    .red {
      font-size: 12px;
      color: #f93737;
      background: #ffd3d3;
      opacity: 0.79;
      border-radius: 3px;
      width: 40px;
      height: 22px;
      text-align: center;
      line-height: 22px;
    }
    .orange {
      font-size: 12px;
      color: #f9a037;
      background: #ffe5c1;
      opacity: 0.79;
      border-radius: 3px;
      width: 40px;
      height: 22px;
      text-align: center;
      line-height: 22px;
    }
    .blue {
      font-size: 12px;
      color: #3977ea;
      background: #d5e8ff;
      opacity: 0.79;
      border-radius: 3px;
      width: 40px;
      height: 22px;
      text-align: center;
      line-height: 22px;
    }
    .green {
      font-size: 12px;
      color: #5bb35f;
      background: #d1f1b2;
      opacity: 0.79;
      border-radius: 3px;
      width: 40px;
      height: 22px;
      text-align: center;
      line-height: 22px;
    }
    .yellow{
      font-size: 12px;
      color: #ffbf00;
      background: rgb(255, 249, 219);
      // opacity: 0.79;
      border-radius: 3px;
      width: 40px;
      height: 22px;
      text-align: center;
      line-height: 22px;
    }
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
  .tableColumn {
    height: 20px;
    width: 98%;
    line-height: 23px;
    text-align: left;
    letter-spacing: 1px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    transition: 0.3s;
  }
  .tableColumnActive {
    line-height: 23px;
    text-align: left;
    letter-spacing: 1px;
    height: 110px;
    transition: 0.3s;
    width: 98%;
  }
  .tableColumnIcon {
    display: inline-block;
    // width: 12px;
    // height: 7px;
    background-size: cover;
    vertical-align: middle;
    transition: 0.3s;
    transform: rotate(0);
    line-height: 23px;
  }
  .tableColumnIconActive {
    display: inline-block;
    // width: 12px;
    // height: 7px;
    vertical-align: middle;
    transform: rotate(90deg);
    -ms-transform: rotate(90deg); /* IE 9 */
    -webkit-transform: rotate(90deg); /* Safari and Chrome */
    transition: 0.3s;
    line-height: 23px;
  }
  .more {
    cursor: pointer;
    position: absolute;
    z-index: 2000;
    right: 5px;
    top: 40px;
    &.readMore{
      right: 145px;
    }
  }
  .msgImgOn {
    height: 108px;
    transition: 0.3s;
    img {
      vertical-align: top;
    }
  }
  .msgImgOff {
    height: 20px;
    transition: 0.3s;
    margin-top: 2px;
    img {
      vertical-align: top;
    }
  }
}
</style>

