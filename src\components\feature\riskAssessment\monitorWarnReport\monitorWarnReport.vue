<template>
  <div class="monitorWarnReport">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              监测预警报告
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div>
      <div class="activeBody">
        <div class="activeHeader">
          <el-tabs v-model="activeTabClass" @tab-click="handleClickActiveTab()">
            <el-tab-pane label="日报" name="dayReport">
              <dayReport               
                ref="dayReport"              
              ></dayReport>
            </el-tab-pane>
            <el-tab-pane label="周报" name="weekReport">
              <weekReport ref="weekReport"></weekReport>
            </el-tab-pane>
             <el-tab-pane label="月报" name="monthReport">
              <monthReport ref="monthReport"></monthReport>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from "vuex";
import dayReport from './dayReport.vue'
import weekReport from './weekReport.vue'
import monthReport from './monthReport.vue'
export default {
  name: "monitorWarnReport",
  components: {
    dayReport,
    weekReport,
    monthReport
  },
  data() {
    return {
      activeTabClass:'dayReport'
    };
  },
  methods: {
    handleClickActiveTab(){

    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {

  },
  computed: {},
  watch: {},
};
</script>
<style lang="scss" scoped>
</style>
