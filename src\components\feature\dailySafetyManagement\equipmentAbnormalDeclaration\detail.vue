<template>
  <div class="notification">
    <el-dialog
      :title="title"
      :visible.sync="show"
      width="1000px"
      @close="closeBoolean()"
      top="5vh"
      v-dialog-drag
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="div1">
        <div class="table">
          <ul class="container">
            <li class="lang">
              <div class="l">企业名称</div>
              <div class="r">
                  {{detailData.enterpName}}
              </div>
            </li>
            <li class="lang">
              <div class="l">统一社会信用代码</div>
              <div class="r">
                {{detailData.entcreditcode}}
              </div>
            </li>
            <li class="lang">
              <div class="l">报备时间</div>
              <div class="r">{{detailData.creatTime}}</div>
            </li>
            <li class="lang">
              <div class="l">所属重大危险源</div>
              <div class="r">{{detailData.dangerName}}</div>
            </li>
            <li class="lang">
              <div class="l">设备名称</div>
              <div class="r">{{detailData.equipment}}</div>
            </li>
            <li class="lang">
              <div class="l">报备原因</div>
              <div class="r" v-if="detailData.declareReason == '0'">离线</div>
              <div class="r" v-else-if="detailData.declareReason == '1'">停产</div>
            </li>
            <li class="lang">
              <div class="l">报备说明</div>
              <div class="r">{{detailData.declareExplain}}</div>
            </li>
            <li class="lang">
              <div class="l">状态开始时间</div>
              <div class="r">{{detailData.endTime}}</div>
            </li>
            <li class="lang">
              <div class="l">状态结束时间</div>
              <div class="r">{{detailData.startTime}}</div>
            </li>
            <li class="lang">
              <div class="l">审核状态</div>
              <div class="r">
                  <span v-if="detailData.state == 0">待审核</span>
                  <span v-else-if="detailData.state == 1">审核通过</span>
                  <span v-else-if="detailData.state == 2">审核驳回</span>
              </div>
            </li>
            <li class="lang">
              <div class="l">审核说明</div>
              <div class="r">{{detailData.examineExplain}}</div>
            </li>
            <li class="lang">
              <div class="l">提交时间</div>
              <div class="r">{{detailData.examineTime}}</div>
            </li>
            <li class="lang">
              <div class="l">审核人</div>
              <div class="r">{{detailData.examineBy}}</div>
            </li>
            <li :class="detailData.enclosure?'lang':'lang bottom'">
              <div class="l">审核单位</div>
              <div class="r">{{detailData.affiliatedUnits}}</div>
            </li>
            <li class="lang bottom" v-if="detailData.enclosure">
              <div class="l">附件</div>
              <div class="r" >
                  <el-image fit="scale-down" :src="detailData.enclosure" :preview-src-list="[detailData.enclosure]" style="position: relative;width: 60px;left: 15px;top: 0px;" ></el-image>
              </div>
              <!-- <div class="r" v-else>暂无附件</div> -->
            </li>
          </ul>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAbnormalDetail } from "@/api/dailySafety";
export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  data() {
    return {
      detailData: {},
      show:false,
      title:'',
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.detailData = {};
      this.show = val;
    },
    getData(id,type) {
      this.title = "异常报备信息详情"
        // if(type == '0'){
        //     this.title="设备异常报备信息详情"
        // }else if(type == '1'){
        //     this.title="视频异常报备信息详情"
        // }
      getAbnormalDetail({
          id:id
      }).then((res) => {
        this.detailData = res.data.data;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
/deep/ .el-image__preview{
  cursor: -moz-zoom-in;
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
}
.notification {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength{
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    height: 600px;
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>