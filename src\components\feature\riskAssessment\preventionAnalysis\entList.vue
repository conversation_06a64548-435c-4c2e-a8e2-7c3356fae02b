<template>
  <div class="analysis-page">
    <div class="statistic-box">
      <div class="statistic-total">
        <img
          class="statisitc-icon"
          src="../../../../../static/img/icon-danger.png"
          alt=""
        />
        <div class="card-item">
          <div class="card-title">隐患总数</div>
          <div class="card-number">{{ majorData.pitfallCount }}</div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.rectified }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 已整改
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.notRectified }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 未整改
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.slippage }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 已逾期
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.rectificationRate }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 整改率
          </div>
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-item">
          <div class="card-title">重大隐患</div>
          <div class="card-number">{{ dangerData.pitfallCount }}</div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 已整改
            {{ dangerData.rectified }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 未整改
            {{ dangerData.notRectified }}
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 已逾期
            {{ dangerData.slippage }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 整改率
            {{ dangerData.rectificationRate }}
          </div>
        </div>
      </div>
      <div class="statistic-card card-yellow">
        <div class="card-item">
          <div class="card-title">一般隐患</div>
          <div class="card-number">{{ normalData.pitfallCount }}</div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            已整改 {{ normalData.rectified }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            未整改 {{ normalData.notRectified }}
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            已逾期 {{ normalData.slippage }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            整改率 {{ normalData.rectificationRate }}
          </div>
        </div>
      </div>
      <div>
        <!-- <el-button type="primary" size="small" @click="handleBack"
          >返回</el-button
        > -->
      </div>
    </div>
    <div class="search-box">
      <el-select v-model="params.hazardLevel" clearable multiple size="small" placeholder="隐患等级">
        <el-option
          v-for="item in levelOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-option>
      </el-select>
      <el-select v-model="params.administerType" multiple clearable size="small" placeholder="治理的类别">
        <el-option
          v-for="item in governanceOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-option>
        
      </el-select>
      <el-select v-model="params.hazardSource" multiple clearable size="small" placeholder="隐患来源">
        <el-option
          v-for="item in dangerOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-option>
      </el-select>
      <el-select v-model="params.hazardStatus" multiple clearable size="small" placeholder="隐患状态">
        <el-option
          v-for="item in statusOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-option>
      </el-select>
      <el-select v-model="params.hazardCategory" multiple clearable size="small" placeholder="隐患类别">
        <el-option
          v-for="item in categoryOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-option>
      </el-select>
      <el-select v-model="params.hazardType" multiple clearable size="small"  placeholder="隐患类型">
        <el-option
          v-for="item in typeOptions"
          :label="item.label"
          :value="item.value"
          :key="item.value"
        ></el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="handleSearch"
        >查询</el-button>
    </div>
    <el-table
      border
      v-loading="loading"
      style="width: 100%"
      :data="tableData"
      :header-cell-style="{
        textAlign: 'center',
        color: 'rgb(51, 51, 51)',
        backgroundColor: 'rgb(242, 246, 255)',
      }"
    >
      <el-table-column type="index" label="序号" width="60"> </el-table-column>
      <!-- <el-table-column prop="qymc" label="公司名称" width="160">
      </el-table-column> -->
     
      <el-table-column
        prop="yhmc"
        label="隐患名称"
        min-width="160"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="yyfx"
        label="风险对象分析"
        min-width="160"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column prop="yhlb" label="隐患类别" width="160">
        <template slot-scope="scope">
           <span>{{ scope.row.yhlb | categoryFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="yhly" label="隐患来源" width="160">
        <template slot-scope="scope"><span>{{ scope.row.yhly | sourceFilter }}</span></template>
      </el-table-column>
      <el-table-column prop="yhdj" label="隐患等级" width="120">
        <template slot-scope="scope">
          <span>{{ scope.row.yhdj | levelFilter }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="zllx" label="治理类型" width="120">
        <template slot-scope="scope"><span>{{
          scope.row.zllx == 0 ? "即查即改" : "限期整改"
        }}</span></template>
      </el-table-column>
      <el-table-column prop="djsj" label="登记时间" width="180">
      </el-table-column>
      <el-table-column prop="zgzrr" label="整改负责人" width="120">
      </el-table-column>
      <el-table-column prop="yhzt" label="隐患状态" width="120">
        <template slot-scope="scope"><span>{{ scope.row.yhzt | statusLabel }}</span></template>
      </el-table-column>
      <el-table-column prop="yhzlqx" label="隐患治理期限" width="180">
      </el-table-column>
      <el-table-column prop="yhlx" label="隐患类型" width="120">
        <template slot-scope="scope"><span>{{ scope.row.yhlx | typeFilter}}</span></template>
      </el-table-column>
      <!-- <el-table-column prop="yhpcjlid" label="隐患排查记录"> </el-table-column> -->
      <el-table-column label="操作" width="120px" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetails(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="params.nowPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>
    <prevention-dialog v-if="detailVisible" :visible="detailVisible" :warinInfo="detailData" @closeBoolean="closeBoolean"></prevention-dialog>
  </div>
</template>
<script>
import {
  preventionAnalysisId,
  preventionAnalysisPage,
} from "@/api/riskAssessment";
import PreventionDialog from "./components/preventionDialog.vue";
import { dangerOptions, statusOptions , typeOptions, levelOptions,categoryOptions,governanceOptions } from './const'
export default {
  name: "preventiveAnalysis",
  components: {PreventionDialog},
  props: {
    enterpriseCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      value1: null,
      dangerOptions,
      typeOptions,
      statusOptions,
      categoryOptions,
      levelOptions,
      governanceOptions,
      tableData: [],
      total: 0,
      loading: false,
      params: {
        nowPage: 1,
        pageSize: 10,
        enterpriseCode: "",
        startTime: null,
        endTime: null,
        administerType: [],
        hazardLevel: [],
        hazardSource: [],
        hazardStatus: [],
        hazardType: [],
        hazardCategory: [],
      },
      typeCode: null,
      majorData: {
        pitfallCount: 0, //总数
        rectified: 0, //已整改
        slippage: 0, //预期
        notRectified: 0, //未整改
        rectificationRate: 0, //整改率
      },
      dangerData: {
        pitfallCount: 0, //总数
        rectified: 0, //已整改
        slippage: 0, //预期
        notRectified: 0, //未整改
        rectificationRate: 0, //整改率
      },
      normalData: {
        pitfallCount: 0, //总数
        rectified: 0, //已整改
        slippage: 0, //预期
        notRectified: 0, //未整改
        rectificationRate: 0, //整改率
      },
      detailVisible: false,
      detailData: {},
     
    };
  },
  filters: {
    statusLabel(value) {
      const item = statusOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    levelFilter(value) {
      const item = levelOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    typeFilter(value){
      const item = typeOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    sourceFilter(value){
      const item = dangerOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    categoryFilter(value){
      const item = categoryOptions.find((item) => item.value == value);
      return item ? item.label : value;
    }
  },
  created() {
    this.getData();
  },
  methods: {
    searchTime(value) {
      if (value) {
        this.params.startTime = value[0];
        this.params.endTime = value[1];
      } else {
        this.params.startTime = null;
        this.params.endTime = null;
      }
    },
    handleSearch() {
      this.params.nowPage = 1;
      this.getData();
    },
    handleSizeChange(val) {
      this.params.pageSize = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.params.nowPage = val;
      this.getData();
    },
    handleDetails(row) {
      this.detailData = row;
      this.detailVisible = true;
    },
    closeBoolean(){
      this.detailVisible = false;
      this.detailData = {};
    },
    handleBack() {
      this.$emit("return");
    },
    getCount() {
      const params = { ...this.params, enterpriseCode: this.enterpriseCode };
      preventionAnalysisPage(params).then((res) => {
        if (res.data.status == 200) {
          this.majorData = res.data.data.dtoCount2;
          this.dangerData = res.data.data.dtoCount1;
          this.normalData = res.data.data.dtoCount0;
        }
      });
    },
    getData() {
      const params = { ...this.params, enterpriseCode: this.enterpriseCode };
      this.loading = true;
      preventionAnalysisId(params).then((res) => {
        if (res.data.status == 200) {
          this.tableData = res.data.data.pageResult.list;
          this.total = res.data.data.pageResult.total;
          this.majorData = res.data.data.dtoCount2;
          this.dangerData = res.data.data.dtoCount1;
          this.normalData = res.data.data.dtoCount0;
          // this.tableData = res.data.data.dtoList;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.analysis-page {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .search-box {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: rgb(242, 246, 255);
    & > * {
      margin-right: 16px;
    }
  }
  .pagination {
    padding: 10px;
    text-align: right;
  }
  .statistic-box {
    padding-top: 5px;
    display: flex;
    height: 120px;
    background-color: rgb(242, 246, 255);
    margin-bottom: 10px;
    .statistic-card {
      width: 400px;
      height: 105px;
      background: url("../../../../../static/img/card-bg-red.png") no-repeat
        center center;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-right: 10px;
      .card-item {
        color: #000;
        flex: 1;
        .card-title {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
        }
        .card-number {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
          color: #fe5552;
        }
        .card-content {
          display: flex;
          align-items: center;
          text-align: left;
          height: 28px;
          img {
            margin-right: 5px;
          }
        }
      }
    }
    .card-yellow {
      background: url("../../../../../static/img/card-bg-yellow.png") no-repeat
        center center;
      background-size: 100% 100%;
    }
    .statistic-total {
      width: 481px;
      height: 105px;
      display: flex;
      align-items: center;
      .statisitc-icon {
        width: 60px;
        height: 60px;
        margin: 0 10px;
      }
      .card-item {
        width: 150px;
        .card-content {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          img {
            margin-right: 5px;
          }
        }
        .card-title {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
        }
        .card-number {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
          color: #1d7ee7;
        }
      }
    }
  }
}
</style>
