<template>
  <div class="safety-information">
      <div class="left-list">
        <div slot="header" class="clearfix title">
          <span>目录</span>
        </div>
        <!-- <el-scrollbar style="height: 100%; width: 100%"> -->
        <div class="tab-list">
          <div
            class="tab-item"
            v-for="(item, index) in routerList"
            :key="index"
            @click="selectLeftListBtn(item, index)"
            :class="{ 'active': activeKey == item.key }"
          >
            <span>{{ item.label }}</span>
          </div>
          <a class="not-data" v-if="!routerList.length">开发中</a>
        </div>
        <!-- </el-scrollbar> -->
      </div>
      <div class="right-content">
          <safetyOrgList :companyCode="enterpriseId" v-if="activeKey == 'safetOrg'"></safetyOrgList>
          <safetyList :companyCode="enterpriseId" v-else-if="activeKey == 'safety'"></safetyList>
          <specialList :companyCode="enterpriseId" v-else-if="activeKey == 'special'"></specialList>
          <equipList :companyCode="enterpriseId" v-else-if="activeKey == 'equip'"></equipList>
          <personList :companyCode="enterpriseId" v-else-if="activeKey == 'person'"></personList>
      </div>
  </div>
</template>

<script>
import safetyOrgList from "./safetyOrg/safetyOrgList.vue";
import safetyList from "./safety/safetyList.vue";
import specialList from "./special/specialList.vue";
import equipList from "./equip/equipList.vue";
import personList from "./person/personList.vue";
export default {
  name: "safetyInformation",
  components: { safetyOrgList, safetyList, specialList, equipList, personList },
  props: {enterpriseId: { type: String, default: "" }},
  data() {
    return {
      routerList: [
        {
          label: "安全管理机构",
          key: "safetOrg",
        },
        {
          label: "企业负责人",
          key: "person",
        },
        {
          label: "安全管理人员",
          key: "safety",
        },
        {
          label: "特种作业人员",
          key: "special",
        },
        {
          label: "特种设备作业人员",
          key: "equip",
        },
      ],
      selectRouterItem: {},
      activeKey: "safetOrg",
      isEnterprise: "ent",
      enterpriseId: "",
      active: "",
      loading: false,
    };
  },
  methods: {
    selectLeftListBtn(item, index) {
      this.activeKey = item.key;
      this.selectRouterItem = item;
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-information {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;
  .left-list {
    width: 180px!important;
    margin-right: 20px;
    border: #f5f5f6 1px solid;
    .title {
      height: 47px;
      background: #f5f5f6;
      line-height: 47px;
      overflow: hidden;
      color: #534d6a;
      font-size: 18px;
      font-weight: 700;
      padding: 0 20px 0 20px;
    }
    .tab-list {
      width: 100%;
      .tab-item {
        width: 100%;
        height: 48px;
        line-height: 48px;
        font-size: 14px;
        padding-left: 20px;
        white-space: nowrap;
        overflow: hidden;
        position: relative;
        text-overflow: ellipsis;
        cursor: pointer;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-transition: 0.3s;
        transition: 0.3s;
        color: #534c6a;
        &:hover {
          background: rgba(0, 0, 0, 0.01);
          color: #4a7dff;
        }
        &.active {
          background: #d4e8ff;
          color: #4a7dff;
        }
      }
    }
  }
  .right-content {
    // flex: 1;
    width: calc(100% - 200px);
    display: flex;
   
    box-sizing: border-box;
    .tab-content{
      flex: 1;
      overflow: hidden;
    }
  }
}
</style>
