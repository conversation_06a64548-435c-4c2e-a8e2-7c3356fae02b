<template>
  <el-dialog
    title="风险单元事故风险类型配置"
    :visible.sync="isRiskPoinConfigure"
    width="1200px"
    top="20vh"
    @close="CloseClear"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    :destroy-on-close="false"
  >
    <div class="h_box">
      <div class="M_box">
        <el-scrollbar class="linShiHeight">
          <div v-if="StorageTank">
            <el-table
              :data="StorageTank"
              style="width: 100%"
              :highlight-current-row="false"
              border
            >
              <el-table-column
                prop="unitName"
                label="重大危险源"
                width="100"
                :show-overflow-tooltip="true"
              ></el-table-column>
              <el-table-column
                prop="riskTypeName"
                label="设备类别"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="deviceName"
                label="设备名称"
                width="/"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="deviceId"
                label="设备名称编号"
                width="/"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="deviceInfo"
                label="设备描述"
                width="/"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="correction"
                label="配置事故风险类型"
                width="300"
              >
                <template slot-scope="scope">
                  <!-- {{scope.row.checkedLists}} -->
                  <div class="selectBox">
                    <!-- <div class="all">
                      <el-checkbox
                        :indeterminate="scope.row.isIndeterminate"
                        v-model="scope.row.checkAll"
                        @change="
                          (val) => {
                            handleCheckAllChange(val, scope.row);
                          }
                        "
                        >全选</el-checkbox
                      >
                    </div> -->
                    <el-checkbox-group
                      v-model="scope.row.pointList"
                      @change="
                        (val) => {
                          handleCheckedCitiesChange(val, scope.row);
                        }
                      "
                    >
                      <el-checkbox
                        v-for="city in scope.row.pointListData"
                        :label="city.id"
                        :key="city.id"
                        >{{ city.label }}</el-checkbox
                      >
                    </el-checkbox-group>

                
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <div slot="footer" style="display: flex; justify-content: center">
      <!-- <el-button size="mini">取 消</el-button> -->

      <el-button size="mini" type="primary" @click="submitMatch()"
        >提 交</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  generalChemicalsIndex,
  unitDeviceFindList,
  modelUpdateDevice,
  riskPointList,
} from "@/api/riskAssessment";
export default {
  data() {
    return {
      // checkAll: false,
      checkedLists: [],
      isIndeterminate: true,
      eOption: [],
      preData: {},
      correction: "",
      isRiskPoinConfigure: false, //临界量匹配表弹框
      StorageTank: [
        // {
        //   title: "",
        //   casNo: "111",
        //   note: "",
        //   correction: "",
        //   // list: ["火灾", "爆炸", "中毒"],
        //   pointList:[
        //     { "id": "100032", "parentId": null, "label": "储罐火灾事故", "parentLabel": null, "disabled": false, "children": null, "orgTypeCode": 0, "leaf": false, "virtualNode": false },
        //     { "id": "100033", "parentId": null, "label": "储罐爆炸事故", "parentLabel": null, "disabled": false, "children": null, "orgTypeCode": 0, "leaf": false, "virtualNode": false },
        //     { "id": "100034", "parentId": null, "label": "储罐中毒事故", "parentLabel": null, "disabled": false, "children": null, "orgTypeCode": 0, "leaf": false, "virtualNode": false } ],
        //   checkedLists: [],
        //   checkAll: false,
        //   isIndeterminate: false,
        //   alias: "",
        //   chemicalId: "",
        //   createTime: "",
        //   dataType: "",
        //   deleteFlag: "",
        // },
      ],
    };
  },
  watch: {
    // radioTop(newVal, oldVal) {
    //   if (newVal == "fixed") {
    //     this.isTab = true;
    //   } else {
    //     this.isTab = false;
    //   }
    // },
  },
  methods: {
    handleCheckAllChange(val, row) {
      //全选
      var ary = [];
      row.pointList.forEach((el) => {
        ary.push(el.id);
      });

      row.pointList = val ? ary : [];
      row.isIndeterminate = false;
    },
    handleCheckedCitiesChange(value, row) {
      let checkedCount = value.length;
      row.checkAll = checkedCount === row.pointListData.length;
      row.isIndeterminate =
        checkedCount > 0 && checkedCount < row.pointListData.length;
    },
    CloseClear() {
      //  this.StorageTank=[]
    },
    submitMatch() {
      var dataOne = this.StorageTank;  
      // dataOne.forEach(el=>{
      //   el.pointList=el.checkedLists
      // })  
      // dataOne.forEach((item) => {
      //   item.pointList.forEach((el) => {
      //     let _index = item.checkedLists.findIndex((c) => c == el.id);
      //     if (_index > -1) {
      //       el.leaf = true;
      //     }
      //   });
      // });

      modelUpdateDevice(dataOne).then((res) => {
        if (res.data.status == 200) {
          this.$message({
            message: res.data.data,
            type: "success",
          });
          this.isRiskPoinConfigure = false;
          this.$emit('refash')
        } else {
          this.$message.error(res.data.msg);
        }
      });

     
    },

    // initData(row, index) {
    //   this.preData = row;
    //   this.rowIndex = index;   
    //   this.StorageTank = row.correctionValueList || [];
    //   this.StorageTank.rowIndex = index;
    //   generalChemicalsIndex().then((res) => {
    //     if (res.data.status === 200) {
    //       this.eOption = res.data.data || [];
    //     } else {
    //       // this.$message.error(res.msg);
    //     }
    //   });
    // },
    //无对应数据弹框
    async getData(val, item) {
      this.isRiskPoinConfigure = val;
      var resData = await unitDeviceFindList({
        cimRiskUnitId: item.riskUnitId,
        cimRiskUnitType: item.riskUnitType,
        enterpId: item.enterpId,
        riskPointId: "",
      });
      if (resData.status == 200) {
      // this.StorageTank = resData.data.data;
        // var obj = {};
        // this.$set(this, "StorageTank", resData.data.data);
        var _this=this;
        _this.StorageTank=resData.data.data
        console.log(
          _this.StorageTank,
          "StorageTankStorageTankStorageTankStorageTankStorageTankStorageTank"
        );
        if (_this.StorageTank.length > 0) {
          riskPointList({
            riskType: item.riskUnitType,
          }).then((res) => {
            if (res.status == 200) {
              if (res.data.data.length > 0) {
                _this.StorageTank.forEach((el,i) => {
                  _this.$set(el, "pointListData", res.data.data);
                  _this.$set(el, "pointList", _this.StorageTank[i].pointList || []);               
                  _this.$set(el, "isIndeterminate", false);
                  _this.$set(el, "checkAll", false);
                  // el.pointListData=res.data.data
                  // el.isIndeterminate=false
                  // el.checkAll=false
                  // el.pointList=[]
                });
              }

              console.log(this.StorageTank,'StorageTankthisStorageTankthisStorageTankthis最后')

              // this.checkedAttrs = val2.map(item => {
              //     return item.id
              // })
            } else {
            }
          });
        }
      }
      // console.log(this.StorageTank, "StorageTankStorageTankStorageTank");

      // this.StorageTank=dataAry
    },

    unitDeviceFindListFn(item) {
      unitDeviceFindList({
        cimRiskUnitId: item.riskUnitId,
        cimRiskUnitType: item.riskUnitType,
        enterpId: item.enterpId,
        riskPointId: "",
      }).then((res) => {});
    },

    addFn() {
      let params = {
        title: "",
        casNo: "",
        note: "",
        correction: "",
        alias: "",
        chemicalId: "",
        createTime: "",
        dataType: "",
        deleteFlag: "",
      };
      this.StorageTank.push(params);
    },
    deteleFn(val) {
      this.StorageTank.splice(val, 1);
    },
  },
};
</script>
<style lang="scss" scoped>
.h_box {
  height: 500px;
  overflow-y: auto;
}
/deep/ .el-checkbox {
  margin-right: 10px;
}
/deep/ .el-checkbox:last-child {
  margin-right: 0;
}
.selectBox {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: left;
  .all {
    margin: 0 20px 0 0;
  }
}
.el-checkbox-group {
  // display: flex;
}
</style>