<template>
  <div class="focuscompany">
    <div class="header">
      <div class="title">重点关注企业</div>
      <div class="radio">
        <el-radio-group size="small" @change="setMode" v-model="modeOne">
          <el-radio-button label="0">今日</el-radio-button>
          <el-radio-button label="1">本周</el-radio-button>
          <!-- <el-radio-button label="3">本月</el-radio-button> -->
        </el-radio-group>
      </div>
      <div class="more" @click="toUrl('/earlyWarningDisposal/hiddenRisk')">
        更多
      </div>
    </div>
    <div class="table-top">
      <!-- <el-tabs
            v-model="activeName"
            size="mini"
            @tab-click="handleClick"
          >
            <el-tab-pane label="全部" name="0"></el-tab-pane>
            <el-tab-pane label="重大风险" name="1"></el-tab-pane>
            <el-tab-pane label="较大风险" name="2"></el-tab-pane>
            <el-tab-pane label="未消警次数Top" name="3"></el-tab-pane>
            <el-tab-pane
              v-if="mode == '2'"
              label="最大报警时长"
              name="4"
            ></el-tab-pane>
          </el-tabs> -->
      <div class="radio">
        <el-radio-group size="mini" v-model="activeName" @change="handleClick">
          <el-radio-button label="0">全部</el-radio-button>
          <el-radio-button label="1">较大风险</el-radio-button>
          <el-radio-button label="2">重大风险</el-radio-button>
          <!-- <el-radio-button label="3">未消警次数Top</el-radio-button>
          <el-radio-button label="4">最大报警时长</el-radio-button> -->
        </el-radio-group>
      </div>
    </div>
    <div v-loading="loading" class="table-bottom">
      <div class="table" v-if="tableData.length > 0">
        <div v-for="(item, index) in tableData" :key="index" class="list">
          <div
            class="icon"
            :style="{
              background: bgIcon(index + 1) + 'no-repeat center center',
            }"
          >
            {{ index + 1 }}
          </div>
          <span class="text" @click="goEnt(item)">{{
            item.companyName || item.enterName
          }}</span>
        </div>
      </div>
      <div class="null" v-else></div>
    </div>
  </div>
</template>
<script>
import {
  // getFocuscompanyPageList,
  // getFocuscompanyFocusTypePageList,
  getFocuscompanyFocusOn,
  getFocuscompanyExportExcel,
  getCompanyRiskLevelDetail,
} from "@/api/workingAcc";
import { getRiskWarningData } from "@/api/earlyWarningDisposal";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  name: "focuscompany",
  data() {
    return {
      TrendChartBool: false,
      modeOne: "0", //默认7天
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      level: ["1", "2", "3", "4"],
      tableData: [],
      mode: "1",
      showtable: true,
      district: [],
      distCode: this.$store.state.login.userDistCode,
      areaName: "",
      loading: false,
      selection: [],
      companyName: "",
      activeName: "0",
      greaterRisk: [],
      majorRisks: [],
      tableDataAll: [],
    };
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  methods: {
    bgIcon(index) {
      var url = "";
      switch (index) {
        case 1:
          url = "url(../../../../../static/img/hong_icon.png)";
          break;
        case 2:
          url = "url(../../../../../static/img/cheng_icon.png)";
          break;
        case 3:
          url = "url(../../../../../static/img/huang_icon.png)";
          break;

        default:
          url = "url(../../../../../static/img/lan_icon.png)";
          break;
      }
      return url;
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit(
        "controler/updateEntId",
        row.companyCode || row.enterpId
      );
    },
    toUrl(url) {
      this.$router.push(url);
    },
    setMode(val) {
      this.modeOne = val;
      this.activeName = "0";
      this.majorRisks = [];
      this.greaterRisk = [];
      this.tableDataAll = [];
      this.getData();
    },
    handleClick() {
      switch (this.activeName) {
        case "0":
          // 全部
          this.tableData = this.tableDataAll;
          break;
        case "1":
          // 较大风险
          this.tableData = this.greaterRisk;
          break;
        case "2":
          // 重大风险
          this.tableData = this.majorRisks;
          break;
        default:
          break;
      }
    },
    async getData() {
      this.loading = true;

      try {
        // 一次性获取重大风险和较大风险企业数据
        const [riskData, warningData] = await Promise.all([
          // 获取重大风险和较大风险企业
          getCompanyRiskLevelDetail({
            enterName: "",
            districtCode: this.distCode,
            eventLevel: ["D", "C"], // D是重大风险，C是较大风险
            nowPage: 1,
            pageSize: 1000,
          }),
          // 获取风险预警数据
          getRiskWarningData({
            districtCode: this.distCode,
            nowPage: 1,
            pageSize: 1000,
            eventLevel: [],
            // status: "2",
          }),
        ]);

        // 处理数据并取交集
        this.processIntersectionData(riskData, warningData);
      } catch (error) {
        console.error("获取数据失败:", error);
        this.$message.error("获取数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 处理两个接口数据的交集
    processIntersectionData(riskData, warningData) {
      // 重置数据
      this.majorRisks = [];
      this.greaterRisk = [];
      this.tableDataAll = [];

      // 获取预警数据中的企业列表（用于取交集）
      let warningCompanies = [];
      if (warningData && warningData.data.status === 200) {
        const records = warningData.data.data.list || [];
        warningCompanies = records.map((item) => ({
          companyCode: item.currNodeCode.split("_")[0], // 取下划线前的部分
          companyName: item.currNodeName,
        }));
      }

      // 处理风险数据
      if (riskData && riskData.data.status === 200) {
        const allRiskCompanies = riskData.data.data.list || [];

        // 取交集：只保留在预警数据中也存在的企业
        const riskIntersection = allRiskCompanies.filter((company) =>
          warningCompanies.some(
            (warning) =>
              warning.companyCode === company.enterpId ||
              warning.companyName === company.enterName
          )
        );

        // 根据风险等级分类
        this.majorRisks = riskIntersection.filter(
          (company) => company.rank === "D"
        ); // 重大风险
        this.greaterRisk = riskIntersection.filter(
          (company) => company.rank === "C"
        ); // 较大风险
      }

      // 合并所有数据作为全部数据
      this.tableDataAll = [...this.majorRisks, ...this.greaterRisk];

      console.log("最终数据:", {
        majorRisks: this.majorRisks,
        greaterRisk: this.greaterRisk,
        tableDataAll: this.tableDataAll,
      });

      // 根据当前选中的tab显示对应数据
      this.handleClick();
    },
    // 导出
    exportExcel() {
      let exportExcelType = null;
      this.activeName == "0"
        ? (exportExcelType = "1")
        : (exportExcelType = "2");
      getFocuscompanyExportExcel({
        distCode: this.distCode,
        companyCodeList:
          [...this.selection].length <= 1 ? null : [...this.selection],
        dateType: this.mode,
        //全部传1 //其他传0
        exportType: exportExcelType,
        focusType: exportExcelType === "2" ? this.activeName : null,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "重点关注企业" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].enterpId || selection[i].companyCode;
      }
    },
    goToSafety() {
      this.distCode = this.$store.state.login.userDistCode;
      this.getData();
    },
    search() {
      this.currentPage = 1;
      this.getData();
    },
    gotoTrendAnalysis() {
      this.TrendChartBool = true;
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData();
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data;
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
};
</script>
<style lang="scss" scoped>
/deep/.el-radio-button--mini .el-radio-button__inner {
  padding: 7px 8px;
}
/deep/ .el-tabs__nav-wrap::after {
  background-color: #fff;
}
.focuscompany {
  width: 100%;
  height: 100%;
  border: 1px solid #d8e0ee;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  .icon {
    color: #6f81b5;
    font-size: 15px;
    background-size: 100% 100% !important;
  }
  .header {
    width: 95%;
    height: 40px;
    display: flex;
    justify-content: space-between;

    margin: 0 auto;
    margin-top: 13px;
    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .seach-part {
    font-weight: 600;
    // padding: 0 10px;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin: 0 auto;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      // width: 550px;
      display: flex;
      justify-content: space-between;
      > * {
        margin-right: 15px;
      }
    }
  }
  .table-top {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    height: 50px;
    width: 95%;
    margin: 0 auto;
    margin-top: 10px;
  }
  .table-bottom {
    height: calc(100% - 120px);
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .table {
    width: 95%;
    margin: 0 auto;
    height: 100%;
    overflow-y: auto;
    .list {
      display: flex;
      margin-bottom: 20px;
      .icon {
        min-width: 24px;
        height: 28px;
        line-height: 28px;
        color: #fff;
        text-align: center;
        margin-right: 10px;
        margin-left: 3px;
      }
      .text {
        color: #3b4046;
        font-size: 16px;
        color: #3977ea;
        cursor: pointer;
        text-decoration: underline;
      }
    }
  }
  .null {
    width: 350px;
    height: 80px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    // margin-top: 30px;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
