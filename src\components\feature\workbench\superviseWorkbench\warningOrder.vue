<template>
  <div class="warning-order">
    <el-dialog :title="title" :visible.sync="showWarningOrder" width="60%" @close="closeDialog">
      <el-form
        ref="warningOrderDetail"
        :model="warningOrderDetail"
        label-width="130px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单编号">
              <el-input
                v-model="warningOrderDetail.id"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="风险事件等级">
              <el-input
                v-model="warningOrderDetail.eventLevel"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="风险事件详情">
              <el-input
                v-model="warningOrderDetail.eventDescription"
                type="textarea"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="风险事件研判">
              <el-input
                v-model="warningOrderDetail.riskDecide"
                type="textarea"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="交办单位">
              <el-input
                v-model="warningOrderDetail.assignmentUnitName"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="交办时间">
              <el-input
                v-model="warningOrderDetail.assignmentTime"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事件工单反馈期限">
              <el-input
                v-model="warningOrderDetail.feedbackTime"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件处置单位">
              <el-input
                v-model="warningOrderDetail.disposeUnitName"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="处置责任人">
              <el-input
                v-model="warningOrderDetail.eventDisposeUser"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="事件处理期限">
              <el-input
                v-model="warningOrderDetail.eventDisposeDeadline"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="事件处理结果">
              <el-input
                v-model="warningOrderDetail.eventDisposeResult"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer" style="text-align: center">
        <el-button @click="showWarningOrder = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getWarningOrderDetail } from "@/api/riskAssessment";
export default {
  name: "WarningOrder",
  components: {},
  props: {
    showWarningOrder: {
      type: Boolean,
      default: false,
    },
    warningOrderItem: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      title: "",
      warningOrderDetail: {},
    };
  },
  created() {},
  mounted() {
    //获取详情数据
    this.getDetail();
  },
  methods: {
    getDetail() {
      //获取详情数据
      const params = {
        id: this.warningOrderItem.id,
      };
      getWarningOrderDetail(params).then((res) => {
        if (res.data.status == 200) {
          this.title = res.data.data.assignmentUnitName + '处置工单';
          this.warningOrderDetail = res.data.data;
          // 处理一下warningOrderDetail的eventLevel
          if (this.warningOrderDetail.eventLevel == 1) {
            this.warningOrderDetail.eventLevel = "红";
          } else if (this.warningOrderDetail.eventLevel == 2) {
            this.warningOrderDetail.eventLevel = "黄";
          } else if (this.warningOrderDetail.eventLevel == 3){
            this.warningOrderDetail.eventLevel = "橙";
          } else if (this.warningOrderDetail.eventLevel == 4) {
            this.warningOrderDetail.eventLevel = "蓝";
          }
        }
      });
    },
    closeDialog() {
      this.$emit("close");
    }
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__header{
  height:54px !important;
  line-height: 20px !important;
}
</style>
