<template>
  <div class="warningAndNotification" v-loading="loading">
    <div class="header">
      <el-cascader
        v-show="this.$store.state.login.user.user_type == 'gov'"
        size="mini"
        placeholder="请选择地市/区县"
        :options="district"
        v-model="districtVal"
        :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
        clearable
        @change="handleChange"
        :show-all-levels="true"
        style="width: 190px"
      ></el-cascader>
      <!-- <el-input
        placeholder="请输入企业名称"
        style="width: 190px"
        size="mini"
        clearable
        v-model.trim="enterName"
      ></el-input> -->
      <el-select
        v-model="rank"
        placeholder="请选择最高预警等级"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in options"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>

      <el-select
        v-model="warnStatus"
        placeholder="请选择预警状态"
        style="width: 190px"
        size="mini"
        clearable
        @clear="clearValue2"
      >
        <el-option
          v-for="(item, index) in options2"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      
      <el-select
        v-model="isFeedback"
        placeholder="请选择反馈标识"
        style="width: 190px"
        size="mini"
        clearable
        @clear="clearValue"
      >
        <el-option
          v-for="(item, index) in options3"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getData()">查询</el-button>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      style="width: 100%"
    >
      <el-table-column type="index" width="80" label="序号"></el-table-column>
      <el-table-column prop="companyName" label="企业名称" min-width="200" show-overflow-tooltip>
        <template slot-scope="{row}">
        <span type="text" style="color: #3977ea; cursor: pointer;" @click="goEnt(row)">{{row.companyName}}</span>
      </template>
      </el-table-column>
      <el-table-column prop="distName" label="行政区划" width="180"> </el-table-column>
      <el-table-column prop="highestRankLevel" label="最高预警等级" width="130">
      </el-table-column>
      <el-table-column prop="yellowWarningTime" label="触发黄色预警时间" width="140">
      </el-table-column>
      <el-table-column prop="orangeWarningTime" label="触发橙色预警时间" width="140">
      </el-table-column>
      <el-table-column prop="redWarningTime" label="触发红色预警时间" width="140">
      </el-table-column>
      <el-table-column prop="warnStatus" label="消警状态" width="100">
      </el-table-column>

      <el-table-column prop="notifyStatus" label="是否通报" width="100">
      </el-table-column>
      <el-table-column prop="isFeedBack" label="企业反馈状态" width="130">
      </el-table-column>
    </el-table>
    <div class="pagination">
            <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>

    </div>

  </div>
</template>

<script>
import {postOnlinePatrolQueryEarlyWarningAndNotificationDetails,postCimEarlyWarningPushList} from "@/api/riskAssessment"

import {mapState} from "vuex"
export default {
  data() {
    return {
      tableData: [],
      district: this.$store.state.controler.district,
      options: [
        { label: "红色预警", value: "1" },
        { label: "橙色预警", value: "2" },
        { label: "黄色预警", value: "3" },
      ],
      options2: [
        { label: "已消警", value: "1" },
        { label: "未消警", value: "0" },
      ],
       options3: [
        { label: "已反馈", value: "1" },
        { label: "未反馈", value: "0" },
      ],
      currentPage:1,
      total: 0,
      rank:"",
      enterName:"",
      districtVal:this.districtProps,
      loading:false,
      isFeedback:"",
      warnStatus:""
    };
  },
  props:['districtProps'],
  watch:{
        districtProps:{
      handler(newVal,oldVal){
        this.districtVal = newVal;
      },
            immediate:true,
      deep:true
    },
  },
    computed:{
    ...mapState({
      // userDistCode:'login/userDistCode'
      userDistCode:state=>state.login.userDistCode
    })
  },
  methods: {
    clearValue(){
      this.isFeedback=''
    },
    clearValue2(){
      this.warnStatus=''
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
        closeTable(){
      this.tableData = [];
      this.currentPage = 1;
      this.total = 0;
      this.rank="";
      this.enterName="";
      this.isFeedback= "";
      this.warnStatus=""
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
     handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.getData();
    },
    getData(){
      this.loading = true;
      postCimEarlyWarningPushList(
        {
        // distParentCode:this.userDistCode,
        // distCode:this.districtVal,
        // current: this.currentPage,
        // size: 10,
        // // distCode:this.districtVal,
        // enterName: this.enterName,
        // rank:this.rank,
        // isFeedback:this.isFeedback
        // companyCode: id?id.toString():'',
        distCode: this.districtVal,
        startDate:'',
        endDate:'',
        rank: this.rank,
        warnStatus: this.warnStatus,
        feedbackFlag:this.isFeedback,
        pageSize: 10,
        nowPage: this.currentPage,
      }
      ).then(res=>{
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
        this.loading = false;
      })
    }
  },
};
</script>

<style lang="scss" scoped>
.warningAndNotification {
  .header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    & > * {
      margin-right: 20px;
    }
  }
  .pagination{
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
  }
}
</style>