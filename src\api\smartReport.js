import axios from "axios";
import qs from "qs";
// 获取表名称
export const getTableName = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getTable/v1",
    data: data,
  });
};
// 获取表字段
export const getTableFields = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getTableField/v1",
    data: qs.stringify(data),
  });
};
// 新增报表
export const addReport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/addReportBase/v1",
    data: data,
  });
};

// 分页查询报表
export const getReportList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/findReportBasePage/v1",
    data: data,
  });
};

// 根据ID查询报表
export const getReportById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getReportBaseById/v1",
    data: qs.stringify(data),
  });
};
// 删除报表
export const deleteReport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/deleteReportBaseById/v1",
    data: qs.stringify(data),
  });
};

// 查询报表数据
export const getReportData = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getReportBase/v1",
    data: data,
  });
};

// 导出报表
export const exportReport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

// 新增表设计
export const addReportDesign = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/addReportGather/v1",
    data: data,
  });
};

// 分页查询设计表
export const getReportDesignList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/findReportGatherPage/v1",
    data: data,
  });
};

// 根据ID查询设计表
export const getReportDesignById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getReportGatherById/v1",
    data: qs.stringify(data),
  });
};

//编辑设计表
export const editReportDesign = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/updateReportGather/v1",
    data: data,
  });
};

//删除设计表
export const deleteReportDesign = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/deleteReportGatherById/v1",
    data: qs.stringify(data),
  });
};

// 获取企业信息列表
export const getEnterpriseInfoList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/findReportGatherContentPage/v1",
    data: data,
  });
};

// 填写提交报表信息
export const addReportGatherContent = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/addReportGatherContent/v1",
    data: data,
  });
};

//修改报表信息
export const updateReportGatherContent = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/updateReportGatherContent/v1",
    data: data,
  });
};

// 根据企业编码查询报表
export const getReportByEnterpriseCode = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getReportGatherContentByEnterpId/v1",
    data: qs.stringify(data),
  });
};

// 根据id查询报表
export const getReportGatherContentById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getReportGatherContentById/v1",
    data: qs.stringify(data),
  });
};

// 退回报表
export const returnReportGatherContent = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/updateIsReturn/v1",
    data: qs.stringify(data),
  });
};

// 删除报表
export const deleteReportGatherContent = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/deleteReportGatherContentById/v1",
    data: qs.stringify(data),
  });
};

// 导出word
export const exportWord = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/exportWord/v1",
    data: qs.stringify(data),
    responseType: "arraybuffer",
  });
};

// 模糊查询危化品名称
export const getChemicalName = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/getProductName/v1",
    data: qs.stringify(data),
  });
};

// 报表分享
export const shareReport = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/share/v1",
    data: qs.stringify(data, {
      arrayFormat: "repeat", // 使用 repeat 格式处理数组
      indices: false, // 不使用索引
    }),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
};

// 报表分享
export const shareReportGather = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/report/shareReportGather/v1",
    data: qs.stringify(data, {
      arrayFormat: "repeat", // 使用 repeat 格式处理数组
      indices: false, // 不使用索引
    }),
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
  });
};

// ==================== 事故查处表 (event_punishment_info) 相关接口 ====================

// 新增事故查处记录
export const addEventPunishment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/eventPunishment/add/v1",
    data: data,
  });
};

// 分页查询事故查处记录
export const getEventPunishmentPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/eventPunishment/findPage/v1",
    data: data,
  });
};

// 根据ID查询事故查处记录
export const getEventPunishmentById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/eventPunishment/findOne/v1",
    data: data,
  });
};

// 编辑事故查处记录
export const updateEventPunishment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/eventPunishment/update/v1",
    data: data,
  });
};

// 删除事故查处记录
export const deleteEventPunishment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/eventPunishment/delete/v1",
    data: data,
  });
};

// ==================== 行政处罚表 (admin_punishment_info) 相关接口 ====================

// 新增行政处罚记录
export const addAdminPunishment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/adminPunishment/add/v1",
    data: data,
  });
};

// 分页查询行政处罚记录
export const getAdminPunishmentPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/adminPunishment/findPage/v1",
    data: data,
  });
};

// 根据ID查询行政处罚记录
export const getAdminPunishmentById = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/adminPunishment/findOne/v1",
    data: data,
  });
};

// 编辑行政处罚记录
export const updateAdminPunishment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/adminPunishment/update/v1",
    data: data,
  });
};

// 删除行政处罚记录
export const deleteAdminPunishment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-data/api/gemp/adminPunishment/delete/v1",
    data: data,
  });
};
