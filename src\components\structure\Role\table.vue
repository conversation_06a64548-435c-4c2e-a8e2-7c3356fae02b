<template>
  <div class="table">
    <el-dialog
      title="添加角色组"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableData"
        ref="tableData"
        v-loading="loading"
        label-width="150px"
      >
        <div class="inputBox">
          <el-form-item label="角色组名称" prop="groupName" class="lable">
            <el-input
              v-model.trim="tableData.groupName"
              placeholder="角色组名称"
              label="角色组名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="序号" prop="orderNum" class="lable">
            <el-input
              v-model.trim="tableData.orderNum"
              placeholder="序号"
              label="序号"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <el-button
          size="default"
          type="primary"
          class="commit"
          @click="handleSaveTable"
          >保存</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveMenuTable,
  //   saveMenmenuByMenuId,
  //   querySystemTreeNoSelf,
  sameMenuFlagSystemCodes,
} from "../../../api/user";
import { menuRules } from "../../../api/rules";
import { getSaveRole, saveRoleByGroupId } from "../../../api/role";
import { mapState } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  name: "DialogTable",
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loading: false,
      tableData: {
        groupName: "",
        orderNum: "",
      },
      checkedKeys: [],
      rules: menuRules,
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
      console.log(this.$store.state.sa.SARoleListData);
    },
    clearTable() {
      this.tableData = {};
    },
    handleSaveTable() {
      // this.loading = true;
      console.log(this.$store.state.sa.SARoleListData.systemCode);
      this.tableData.systemCode =
        this.$store.state.sa.SARoleListData.systemCode;
      this.tableData.parentId =
        this.$store.state.sa.SARoleListData.menuId || -1;
      this.$refs["tableData"].validate((valid) => {
        if (valid) {
          if (this.tableData.id) {
            getSaveRole({
              orderNum: this.tableData.orderNum,
              groupName: this.tableData.groupName,
              systemCode: this.tableData.systemCode,
              parentId: this.tableData.parentId,
              id: this.tableData.id,
            }).then((res) => {
              if (res.data.code == 0) {
                // this.submitting = false;
                this.$message({
                  type: "success",
                  message: "修改成功!",
                });
                this.tableVisible = false;
                this.$parent.fatherMethod();
              } else {
                this.$message({
                  type: "info",
                  message: res.data.msg,
                });
                return;
              }
            });
          } else {
            getSaveRole({
              orderNum: this.tableData.orderNum,
              groupName: this.tableData.groupName,
              systemCode: this.tableData.systemCode,
              parentId: this.tableData.parentId,
            }).then((res) => {
              if (res.data.code == 0) {
                // this.submitting = false;
                this.$message({
                  type: "success",
                  message: "保存成功!",
                });
                this.tableVisible = false;
                this.$parent.fatherMethod();
              } else {
                this.$message({
                  type: "info",
                  message: res.data.msg,
                });
                return;
              }
            });
          }
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    getData(menuId) {
      this.loading = true;
      saveRoleByGroupId({ id: menuId })
        .then((res) => {
          this.loading = false;
          // console.log(res);
          this.tableData = res.data.data;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    // getTree(data) {
    //   querySystemTreeNoSelf({ systemCode: data })
    //     .then((res) => {
    //       let data = res.data.data;
    //       for (let i = 0; i < data.length; i++) {
    //         data[i].title = data[i].systemName;
    //         data[i].key = data[i].systemCode;
    //       }
    //       // console.log(data);
    //       this.treeData = data;
    //     })
    //     .catch((e) => {
    //       console.log(e, "请求错误");
    //     });
    // },
    getTreeData(data) {
      sameMenuFlagSystemCodes({ menuId: data })
        .then((res) => {
          let data = res.data.data;
          // console.log(data);
          this.checkedKeys = data.split(",");
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    onCheck(checkedKeys, e) {
      // console.log(checkedKeys, e)
      let key = JSON.stringify(checkedKeys);
      key = key.split('"').join("");
      key = key.split("[").join("");
      key = key.split("]").join("");
      // console.log(key);
      this.tableData.belongSystemCode = key;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
