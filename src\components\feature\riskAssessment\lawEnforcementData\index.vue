<!-- 执法数据分析 -->
<template>
  <div class="law-enforcement-data">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, index) in tabs"
        :key="index"
        :label="item.label"
        :name="item.name"
      >
        <keep-alive :include="item.name">
          <component :is="item.name" :ref="item.name"></component>
        </keep-alive>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import LawEnforcementList from "./lawEnforcementList/index.vue";
import LawEnforcementRecommend from "./lawEnforcementRecommend/index.vue";
import LawEnforcementStatistics from "./lawEnforcementStatistics/index.vue";
export default {
  components: {
    LawEnforcementStatistics,
    LawEnforcementList,
    LawEnforcementRecommend
  },
  data() {
    return {
      activeName: "LawEnforcementStatistics",
      tabs: [
        { label: "执法统计", name: "LawEnforcementStatistics" },
        { label: "执法列表", name: "LawEnforcementList" },
        { label: "执法辅助推荐", name: "LawEnforcementRecommend" }
      ]
    }
  },
  methods: {
    handleClick() {
     
    }
  }

}
</script>
<style lang="scss" scoped>
.law-enforcement-data {
  height: 100%;
  width: 100%;
  background: #fff;
}
</style>

