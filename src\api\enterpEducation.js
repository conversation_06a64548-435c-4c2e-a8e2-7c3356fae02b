import axios from "@/utils/http";
import qs from "qs";

// 考题管理----------------------------------
// 试题列表
export const questionsPage = (data) => {
  return axios({
    method: "post",
    headers: {
      token: "",
    },
    url: "/gemp-general/api/gemp/general/trainingSubjectData/questionsPage/v1",
    data: data,
  });
};

// 查看考题
export const questionsDetail = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/viewQuestions/v1?id=" +
      id,
  });
};
// 删除考题
export const questionsDelete = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/deleteQuestions/v1?id=" +
      id,
  });
};
// 考生管理------------------------------------
// 试卷列表
export const examinationPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/examinationPage/v1",
    data: data,
  });
};
// 删除试卷
export const examinationDelete = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/deleteExamination/v1?id=" +
      id,
  });
};
// 查看试卷
export const examinationDetail = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/viewExamination/v1?id=" +
      id,
  });
};
// 添加试卷
export const examinationAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/addExamination/v1",
    data: data,
  });
};

// 获取随机的考题集合
export const randomQuestionsList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/randomQuestionsList/v1",
    data: data,
  });
};

// 随机更换单个考题
export const randomQuestions = (data) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/randomQuestions/v1",
    data: data,
  });
};
// 启动/停用试卷
export const enableExamination = (data) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/startExamination/v1?id=" +
      data.id +
      "&status=" +
      data.status,
  });
};
// 考试记录--------------------------------------------------
// 考试记录列表
export const recordPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/recordPage/v1",
    data: data,
  });
};
// 查看考生考试记录
export const viewRecord = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/viewRecord/v1?id=" + id,
  });
};

// 删除考试记录
export const deleteRecord = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/deleteRecord/v1?id=" + id,
  });
};

// 查看答题记录
export const viewAnswerRecord = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/examinationQuestions/v1?id=" +
      id,
  });
};

// 考试科目
export const findByCourseNo = () => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/findByCourseNo/v1",
  });
};
//  题目标签集合
export const findByLabelName = () => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/findByLabelName/v1",
  });
};

// 培训列表
export const trainDataPage = (params) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/trainDataPage/v1",
    data: params,
  });
};

// 新增培训
export const addTrainData = (params) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/addTrainData/v1",
    data: params,
  });
};
// 编辑培训
export const updateTrainData = (params) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/updateTrainData/v1",
    data: params,
  });
};
// 删除培训
export const deleteTrainData = (id) => {
  return axios({
    method: "post",
    url:
      "/gemp-general/api/gemp/general/trainingSubjectData/deleteTrainData/v1?id=" +
      id,
  });
};
// 获取培训详情
export const getTrainData = (id) => {
  return axios({
    method: "post",
    url: "/gemp-general/api/gemp/general/trainingSubjectData/viewTrainData/v1?id=" + id,
  });
};
