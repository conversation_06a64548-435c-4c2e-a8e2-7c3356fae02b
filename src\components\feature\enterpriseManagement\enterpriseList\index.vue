<template>
  <div class="enterpriseList">
    <div class="header">
      <div class="titTop">
        <div class="title">企业列表</div>
        <el-radio-group v-model="radio1" size="small">
          <el-radio-button label="0">场所</el-radio-button>
          <el-radio-button label="1">主体</el-radio-button>
        </el-radio-group>
      </div>

      <div class="operation">
        <div class="inputBox">
          <el-cascader
            size="small"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtVal"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist"
            style="width: 220px"
          ></el-cascader>
          <el-cascader
            v-if="radio1 == 0"
            v-model="entTypeVal"
            :options="entTypeTreeData"
            :props="{
              value: 'id',
              label: 'enterpriseType',
              children: 'children',
              checkStrictly: true,
              emitPath: false,
            }"
            placeholder="请选择企业类型"
            clearable
            size="small"
            style="width: 300px"
          />
          <el-select
            v-model="levelVal"
            size="small"
            placeholder="请选择重大危险源企业"
            :clearable="true"
            multiple
            style="width: 300px"
          >
            <el-option
              v-for="item in level"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="auditStatus"
            size="small"
            placeholder="请选择审核状态"
            :clearable="true"
            style="width: 180px"
            v-if="radio1 == 0"
          >
            <el-option
              v-for="item in auditStatusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="enterpName"
            size="small"
            placeholder="请输入企业名称"
            class="input"
            clearable
          ></el-input>
          <el-select
            v-if="radio1 == 0"
            v-model="processIds"
            placeholder="请选择工艺"
            size="small"
            :clearable="true"
            style="width: 200px"
          >
            <el-option
              v-for="item in processIdsOption"
              :key="item.processid"
              :label="item.processname"
              :value="item.processid"
            >
            </el-option>
          </el-select>

          <div class="item" v-if="radio1 == 0">
            <el-input
              v-model.trim="hazChemName"
              style="width: 300px"
              size="small"
              placeholder="请输入危化品关键字"
              class="input"
              clearable
            ></el-input>
          </div>

          <el-button type="primary" size="small" @click="searches()"
            >查询</el-button
          >
        </div>
        <el-button
          v-if="radio1 == 0 && isShowMark"
          type="primary"
          size="small"
          @click="openTypeDialog"
          :disabled="selection.length === 0"
          style="margin-right: 10px"
          >标记</el-button
        >
        <CA-button
          v-if="radio1 == 0"
          type="primary"
          size="small"
          class="export"
          plain
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
    </div>
    <template v-if="radio1 == 0">
      <div class="table" v-loading="loading">
        <el-table
          :data="tableData ? tableData.records : []"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          border
          style="width: 100%"
          ref="multipleTable"
          @select="select"
          @select-all="select"
        >
          <el-table-column
            type="selection"
            width="55"
            fixed="left"
            align="center"
          >
          </el-table-column>
          <el-table-column type="index" label="序号" width="55" align="center">
          </el-table-column>
          <el-table-column
            prop="enterpName"
            label="企业名称"
            width="340"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span
                @click="goEnt(scope.row)"
                style="color: rgb(57, 119, 234)"
                class="enterpName"
              >
                {{ scope.row.enterpName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="districtName"
            :label="isShowDist == true ? '行政区划' : '归属园区'"
            align="center"
            width="120"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="isShowDist == true">{{
                scope.row.districtName
              }}</span>
              <span v-else>{{ park.parkName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="enterpriseTypeName"
            label="企业类型"
            width="180"
            align="center"
          >
            <!-- <template slot-scope="scope">
              <span v-if="scope.row.enterpriseType == null">/</span>
              <span v-else-if="scope.row.enterpriseType == '01'">生产</span>
              <span v-else-if="scope.row.enterpriseType == '02'">经营</span>
              <span v-else-if="scope.row.enterpriseType == '03'">使用</span>
              <span v-else-if="scope.row.enterpriseType == '04'"
                >第一类非药品类易制毒</span
              >
              <span v-else> </span>
            </template> -->
          </el-table-column>
          <el-table-column
            prop="managementTypeName"
            label="企业小类"
            width="180"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="level"
            label="企业等级"
            align="center"
            min-width="100"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.level == 1">一级</span>
              <span v-else-if="scope.row.level == 2">二级</span>
              <span v-else-if="scope.row.level == 3">三级</span>
              <span v-else-if="scope.row.level == 4">四级</span>
              <span v-else></span>
            </template>
          </el-table-column>
          <el-table-column
            prop="respper"
            label="企业负责人"
            align="center"
            min-width="100"
          >
          </el-table-column>
          <el-table-column
            prop="respTel"
            label="负责人手机"
            align="center"
            min-width="120"
          >
          </el-table-column>
          <!-- <el-table-column
          prop="status"
          label="经营状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.status == '0'">正常</span>
            <span v-else-if="scope.row.status == '1'">停产</span>
            <span v-else-if="scope.row.status == '2'">涉密</span>
            <span v-else> </span>
          </template>
        </el-table-column> -->
          <el-table-column
            prop="status"
            label="审核状态"
            width="100"
            align="center"
          >
            <template slot-scope="scope">
              <span
                v-if="scope.row.auditStatus == '1'"
                @click="
                  handleClickCheck(
                    scope.row.enterpId,
                    'dialogInformationCheckShow'
                  )
                "
              >
                审核通过
              </span>
              <span
                v-else-if="scope.row.auditStatus == '2'"
                @click="
                  handleClickCheck(
                    scope.row.enterpId,
                    'dialogInformationCheckShow'
                  )
                "
                >审核不通过</span
              >
              <span
                v-else-if="scope.row.auditStatus == '3'"
                @click="
                  handleClickCheck(
                    scope.row.enterpId,
                    'dialogInformationCheckShow'
                  )
                "
                >待审核</span
              >
              <span v-else> </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
            align="center"
            min-width="260"
            fixed="right"
          >
            <template slot-scope="scope">
              <div class="icon_box">
                <div
                  @click="
                    handleClickCheck(
                      scope.row.enterpId,
                      'dialogCheckDetailsShow'
                    )
                  "
                  type="text"
                  size="mini"
                  class="icon"
                  v-if="scope.row.auditStatus === '3'"
                >
                  <div class="icon">
                    <i class="iconfont icon-user2"></i>
                    <span>审核</span>
                  </div>
                </div>
                <!-- <div
                v-if="scope.row.level"
                @click="handleClick(scope.row.enterpId, 'threeDimensionalView')"
                type="text"
                size="mini"
                class="icon"
                ><div class="icon">
                  <i class="iconfont">&#xe826;</i>
                  <span>三维</span>
                </div></div
              > -->
                <div
                  v-if="scope.row.level"
                  @click="handleClick(scope.row.enterpId, 'videoInspection')"
                  type="text"
                  size="mini"
                  class="icon"
                >
                  <div class="icon">
                    <i class="el-icon-video-camera"></i>
                    <span>视频</span>
                  </div>
                </div>
                <div
                  v-if="scope.row.level"
                  @click="handleClick(scope.row.enterpId, 'realTimeMonitoring')"
                  type="text"
                  size="mini"
                  class="icon"
                >
                  <div class="icon">
                    <i class="el-icon-aim"></i>
                    <span style="color: #3977ea">监测</span>
                  </div>
                </div>
                <div
                  v-if="scope.row.level == null"
                  @click="handleClick(scope.row.enterpId, 'basicInformation')"
                  type="text"
                  size="mini"
                >
                  <div class="icon">
                    <i class="el-icon-office-building"></i>
                    <span style="color: #3977ea">企业信息</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="tableData ? tableData.size : 10"
          layout="total, prev, pager, next"
          background
          :total="tableData ? tableData.total : 0"
        >
        </el-pagination>
      </div>
    </template>

    <div v-show="radio1 == 1">
      <div class="table" v-loading="loading">
        <el-table
          :data="tableDataPace ? tableDataPace.list : []"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          border
          style="width: 100%"
          ref="multipleTable"
          @select="select"
          @select-all="select"
        >
          <el-table-column
            type="selection"
            width="55"
            fixed="left"
            align="center"
          >
          </el-table-column>
          <el-table-column type="index" label="序号" width="55" align="center">
          </el-table-column>
          <el-table-column
            prop="mainEnterpName"
            label="企业名称"
            min-width="340"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <!-- <el-table-column
            prop="mainDistrictName"
            label="行政区划"
            align="center"
            width="250"
            :show-overflow-tooltip="true"
          >
          </el-table-column> -->

          <!-- districtName -->

          <el-table-column
            prop="mainDistrictName"
            :label="isShowDist == true ? '行政区划' : '归属园区'"
            align="center"
            width="120"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <span v-if="isShowDist == true">{{
                scope.row.mainDistrictName
              }}</span>
              <span v-else>{{ park.parkName }}</span>
            </template>
          </el-table-column>

          <el-table-column
            prop="mainCreditCode"
            label="统一信用代码"
            align="center"
            width="250"
          >
          </el-table-column>
          <el-table-column
            prop="mainLegalRepper"
            label="法人"
            align="center"
            width="250"
          >
          </el-table-column>

          <el-table-column type="expand" label="展开" width="250">
            <template slot-scope="props">
              <el-table
                :data="props.row ? props.row.enterpriseDTOs : []"
                size="mini"
                class="subTable"
                style="width: 92%; margin: 15px auto"
              >
                <el-table-column
                  prop="enterpname"
                  label="单位名称"
                  width="300"
                  align="center"
                  :show-overflow-tooltip="true"
                >
                  <template #default="{ row }">
                    <span
                      @click="goEntSpace(row)"
                      style="color: #3977ea; cursor: pointer"
                      >{{ row.enterpname }}</span
                    >
                  </template>
                </el-table-column>

                <el-table-column
                  prop="districtName"
                  :label="isShowDist == true ? '行政区划' : '归属园区'"
                  align="center"
                  width="120"
                  :show-overflow-tooltip="true"
                >
                  <template slot-scope="scope">
                    <span v-if="isShowDist == true">{{
                      scope.row.districtName
                    }}</span>
                    <span v-else>{{ park.parkName }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="enterpriseTypeName"
                  label="企业类型"
                  width="180"
                  align="center"
                >
                  <!-- <template slot-scope="scope">
              <span v-if="scope.row.enterprisetype == null">/</span>
              <span v-else-if="scope.row.enterpriseType == '01'">生产</span>
              <span v-else-if="scope.row.enterpriseType == '02'">经营</span>
              <span v-else-if="scope.row.enterpriseType == '03'">使用</span>
              <span v-else-if="scope.row.enterpriseType == '04'"
                >第一类非药品类易制毒</span
              >
              <span v-else> </span>
            </template> -->
                </el-table-column>
                <el-table-column
                  prop="dangerLevelName"
                  label="企业等级"
                  align="center"
                  min-width="100"
                >
                  <!-- <template slot-scope="scope">
              <span v-if="scope.row.level == 1">一级</span>
              <span v-else-if="scope.row.level == 2">二级</span>
              <span v-else-if="scope.row.level == 3">三级</span>
              <span v-else-if="scope.row.level == 4">四级</span>
              <span v-else></span>
            </template> -->
                </el-table-column>
                <el-table-column
                  prop="respper"
                  label="企业负责人"
                  align="center"
                  min-width="100"
                >
                </el-table-column>
                <el-table-column
                  prop="resptel"
                  label="负责人手机"
                  align="center"
                  min-width="120"
                >
                </el-table-column>
                <!-- <el-table-column
          prop="status"
          label="经营状态"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.status == '0'">正常</span>
            <span v-else-if="scope.row.status == '1'">停产</span>
            <span v-else-if="scope.row.status == '2'">涉密</span>
            <span v-else> </span>
          </template>
        </el-table-column> -->
                <el-table-column
                  prop="status"
                  label="审核状态"
                  width="100"
                  align="center"
                >
                  <template slot-scope="scope">
                    <span
                      v-if="scope.row.auditstatus == '1'"
                      @click="
                        handleClickCheck(
                          scope.row.enterpid,
                          'dialogInformationCheckShow'
                        )
                      "
                    >
                      审核通过
                    </span>
                    <span
                      v-else-if="scope.row.auditstatus == '2'"
                      @click="
                        handleClickCheck(
                          scope.row.enterpid,
                          'dialogInformationCheckShow'
                        )
                      "
                      >审核不通过</span
                    >
                    <span
                      v-else-if="scope.row.auditstatus == '3'"
                      @click="
                        handleClickCheck(
                          scope.row.enterpid,
                          'dialogInformationCheckShow'
                        )
                      "
                      >待审核</span
                    >
                    <span v-else> </span>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="address"
                  label="操作"
                  align="center"
                  min-width="260"
                  fixed="right"
                >
                  <template slot-scope="scope">
                    <div class="icon_box">
                      <div
                        @click="
                          handleClickCheck(
                            scope.row.enterpid,
                            'dialogCheckDetailsShow'
                          )
                        "
                        type="text"
                        size="mini"
                        class="icon"
                        v-if="scope.row.auditStatus === '3'"
                      >
                        <div class="icon">
                          <i class="iconfont icon-user2"></i>
                          <span>审核</span>
                        </div>
                      </div>
                      <!-- <div
                v-if="scope.row.level"
                @click="handleClick(scope.row.enterpId, 'threeDimensionalView')"
                type="text"
                size="mini"
                class="icon"
                ><div class="icon">
                  <i class="iconfont">&#xe826;</i>
                  <span>三维</span>
                </div></div
              > -->
                      <div
                        v-if="scope.row.dangerLevelCode"
                        @click="
                          handleClick(scope.row.enterpid, 'videoInspection')
                        "
                        type="text"
                        size="mini"
                        class="icon"
                      >
                        <div class="icon">
                          <i class="el-icon-video-camera"></i>
                          <span>视频</span>
                        </div>
                      </div>
                      <div
                        v-if="scope.row.dangerLevelCode"
                        @click="
                          handleClick(scope.row.enterpid, 'realTimeMonitoring')
                        "
                        type="text"
                        size="mini"
                        class="icon"
                      >
                        <div class="icon">
                          <i class="el-icon-aim"></i>
                          <span style="color: #3977ea">监测</span>
                        </div>
                      </div>
                      <div
                        v-if="scope.row.dangerLevelCode == null"
                        @click="
                          handleClick(scope.row.enterpid, 'basicInformation')
                        "
                        type="text"
                        size="mini"
                      >
                        <div class="icon">
                          <i class="el-icon-office-building"></i>
                          <span style="color: #3977ea">企业信息</span>
                        </div>
                      </div>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChangePace"
          :current-page.sync="currentPagePace"
          :page-size="tableDataPace ? tableDataPace.size : 10"
          layout="total, prev, pager, next"
          background
          :total="tableDataPace ? tableDataPace.total : 0"
        >
        </el-pagination>
      </div>
    </div>

    <DialogCheckDetails
      :enterpId="enterpId"
      :show="dialogCheckDetailsShow"
      @closeBoolean="closeBoolean"
      @getParentList="search"
      ref="dialogCheckDetails"
      @refresh="refresh"
    />
    <DialogInformationCheck
      :enterpId="enterpId"
      :show="dialogInformationCheckShow"
      @closeBoolean="closeBoolean"
      ref="dialogInformationCheck"
    />

    <!-- 标记企业小类弹窗 -->
    <el-dialog
      title="标记企业小类"
      :visible.sync="typeDialogVisible"
      width="500px"
      :close-on-click-modal="false"
    >
      <div class="type-dialog-content">
        <p class="selected-count">已选择 {{ selection.length }} 家企业</p>

        <el-form :model="typeForm" label-width="100px">
          <el-form-item label="企业类型:" required>
            <el-cascader
              v-model="typeForm.typeCode"
              :options="entTypeTreeData"
              :props="{
                value: 'id',
                label: 'enterpriseType',
                children: 'children',
                checkStrictly: true,
                emitPath: false,
              }"
              placeholder="请选择企业类型"
              clearable
              style="width: 100%"
            />
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="typeDialogVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmUpdateType"
          :loading="updateTypeLoading"
          :disabled="!typeForm.typeCode"
        >
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getEnterpriseList,
  InformationExportexcel,
  getKnowledgeListData,
  enterpriseUserMain,
  enterpriseType,
  updateEnterpriseType,
} from "@/api/entList";
import { getEnt } from "@/api/dailySafety";
import DialogCheckDetails from "./dialogCheckDetails.vue";
import DialogInformationCheck from "./dialogInformationCheck.vue";

import { createNamespacedHelpers } from "vuex";
import Template from "../../dailySafetyManagement/informationRelease/component/Template.vue";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");

export default {
  //import引入的组件
  name: "enterpriseList",
  components: {
    DialogCheckDetails,
    DialogInformationCheck,
    Template,
  },
  data() {
    return {
      radio1: "0",
      processIds: "",
      hazChemName: "",
      processIdsOption: [],
      currentPage: 1,
      currentPagePace: 1,
      enterpId: "",
      entType: [], // 企业类型原始列表
      entTypeTreeData: [], // 企业类型树形数据
      entTypeVal: "", // 选中的企业类型（可能是父级或子级）

      // 标记企业小类弹窗相关
      typeDialogVisible: false,
      typeForm: {
        typeCode: "",
      },
      updateTypeLoading: false, // 更新企业类型加载状态
      level: [
        {
          label: "一级",
          value: "1",
        },
        {
          label: "二级",
          value: "2",
        },
        {
          label: "三级",
          value: "3",
        },
        {
          label: "四级",
          value: "4",
        },
      ],
      levelVal: ["1", "2", "3", "4"],
      auditStatusList: [
        {
          label: "审核通过",
          value: "1",
        },
        {
          label: "审核不通过",
          value: "2",
        },
        {
          label: "待审核",
          value: "3",
        },
      ],
      auditStatus: "",
      input: "",
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      value: "",
      tableData: null,
      tableDataPace: null,
      districtLoading: false,
      enterpName: "",
      loading: false,
      selection: [],
      tabName: "",
      entLevel: "",
      dialogCheckDetailsShow: false,
      dialogInformationCheckShow: false,
    };
  },
  //方法集合
  methods: {
    getSelect() {
      getKnowledgeListData({
        processid: "",
        processname: "",
      }).then((res) => {
        if (res.data.status === 200) {
          //processIdsOption
          this.processIdsOption = res.data.data;
        }
      });
    },
    setTag() {
      const data = {
        enterpName: this.enterpName,
        districtVal: this.districtVal,
        levelVal: this.levelVal,
        entTypeVal: this.entTypeVal,
        auditStatus: this.auditStatus,
      };
      // console.log(data);
      this.$store.commit("controler/updateEntListControler", data);
    },
    /**  *
     * * @param {string} data dialogCheckDetailsShow，dialogInformationCheckShow
     * */
    closeBoolean(data) {
      this[data.name] = data.boolean;
    },
    refresh() {
      this.search();
    },
    goEnt(val) {
      let bool = true;
      this.$emit("entBool", bool, "videoInspection");
      let id = val.enterpId;
      this.$emit("entId", id);
    },
    goEntSpace(val) {
      let bool = true;
      this.$emit("entBool", bool, "videoInspection");
      let id = val.enterpid;
      this.$emit("entId", id);
    },
    // 导出
    exportExcel() {
      // 处理企业类型参数
      const { entTypeVal, managementType } = this.getEnterpriseTypeParams();

      InformationExportexcel({
        distCode: this.districtVal,
        enterpName: this.enterpName,
        enterpriseType: entTypeVal,
        managementType: managementType,
        level: this.levelVal.toString(),
        current: this.currentPage,
        ids: this.selection,
        processIds: this.processIds,
        hazChemName: this.hazChemName,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].enterpId;
      }
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.search();
    },
    handleCurrentChangePace(val) {
      this.currentPagePace = val;
      this.search();
    },

    //获取列表数据
    getData() {
      // if (this.$store.state.login.user.user_type === "ent") {
      //   getEnt({}).then((res) => {
      //     if (res.data.code == 0) {
      //       let bool = true;
      //       this.$emit("entBool", bool);
      //       let id = res.data.data.enterpId;
      //       this.$emit("entId", id);
      //       //点击
      //       if (this.entModelName) {
      //         this.tabName = this.entModelName;
      //       } else if (res.data.data.level) {
      //         this.tabName = "videoInspection";
      //       } else {
      //         this.tabName = "basicInformation";
      //       }
      //       this.$emit("goTag", id, this.tabName);
      //     }
      //   });
      // } else {
      this.currentPage = 1;
      this.search();
      // }
    },
    handleClick(enterpId, name) {
      this.$emit("goTag", enterpId, name);
    },
    handleClickCheck(enterpId, name) {
      this[name] = true;
      this.enterpId = enterpId;
      // dialogCheckDetailsShow
      if (name === "dialogInformationCheckShow") {
        this.$nextTick(() => {
          this.$refs.dialogInformationCheck.getData();
        });
      }
    },
    //查询
    searches() {
      if (this.radio1 == 0) {
        this.currentPage = 1;
      } else {
        this.currentPagePace = 1;
      }

      const data = {
        enterpName: this.enterpName,
        districtVal: this.districtVal,
        levelVal: this.levelVal,
        entTypeVal: this.entTypeVal,
        auditStatus: this.auditStatus,
      };
      this.$store.commit("controler/updateEntListControler", data);
      this.search();
    },
    search() {
      this.loading = true;
      if (this.radio1 == 0) {
        // 处理企业类型参数
        const { entTypeVal, managementType } = this.getEnterpriseTypeParams();

        getEnterpriseList({
          distCode: this.districtVal,
          enterpName: this.enterpName,
          enterpriseType: entTypeVal,
          managementType: managementType,
          auditStatus: this.auditStatus,
          level: this.levelVal.toString(),
          current: this.currentPage,
          processIds: this.processIds,
          hazChemName: this.hazChemName,
          size: 10,
        }).then((res) => {
          this.tableData = res.data.data;
          this.loading = false;
        });
      } else {
        enterpriseUserMain({
          districtcode: this.districtVal,
          dangerLevel: this.levelVal.toString(),
          enterpName: this.enterpName,
          nowPage: this.currentPagePace,
          entcreditcode: "",
          pageSize: 10,
        }).then((res) => {
          this.tableDataPace = res.data.data;
          this.loading = false;
        });
      }
    },
    // //根据需求设计，存入临时缓存浏览器未关闭，保持数据状态
    // changeDistrict() {
    //   const data = {
    //     enterpName: this.enterpName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    // changeEntTypeVal() {
    //   // sessionStorage.setItem("entTypeVal", this.entTypeVal);
    //   const data = {
    //     enterpName: this.enterpName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    // changeLevelVal() {
    //   // sessionStorage.setItem("levelVal", this.levelVal.join(","));
    //   const data = {
    //     enterpName: this.enterpName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    // changeEnterpName() {
    //   // sessionStorage.setItem("enterpName", this.enterpName);
    //   const data = {
    //     enterpName: this.enterpName,
    //     districtVal: this.districtVal,
    //     levelVal: this.levelVal,
    //     entTypeVal: this.entTypeVal,
    //   };
    //   this.$store.commit("controler/updateEntListControler", data);
    // },
    getSessionStorage() {
      //如果企业列表筛选条件的行政区划代码存在的时候，才给当前页面继承上次选择的行政区划代码
      if (this.entListControler.districtVal) {
        this.districtVal = this.entListControler.districtVal;
      }
      this.entTypeVal = this.entListControler.entTypeVal || "";

      //必填项
      if (this.entListControler.levelVal == null) {
        this.$store.commit("controler/updateEntListControler", {
          enterpName: this.enterpName,
          districtVal: this.districtVal,
          levelVal: ["1", "2", "3", "4"],
          entTypeVal: this.entTypeVal,
          auditStatus: this.auditStatus,
        });
      } else {
        this.levelVal = this.entListControler.levelVal;
      }
      this.enterpName = this.entListControler.enterpName;
      this.auditStatus = this.entListControler.auditStatus;
    },
    async getEnterpriseType() {
      try {
        // 获取父级企业类型
        const parentRes = await enterpriseType({});
        this.entType = parentRes.data.data || [];

        // 构建树形数据
        const treeData = [];

        for (const parentType of this.entType) {
          const treeNode = {
            id: parentType.id,
            enterpriseType: parentType.enterpriseType,
          };

          // 只有当类型ID等于"02"时才获取子类
          if (parentType.id === "02") {
            try {
              const childRes = await enterpriseType({
                parentId: parentType.id,
              });
              const children = childRes.data.data || [];
              if (children.length > 0) {
                // 有子节点时，设置children属性
                treeNode.children = children.map((child) => ({
                  id: child.id,
                  enterpriseType: child.enterpriseType,
                  // 第二层不设置children属性，表示不可展开
                }));
              }
              // 如果没有子节点，不设置children属性
            } catch (error) {
              console.error(`获取企业类型 ${parentType.id} 的子类失败:`, error);
            }
          }
          // 其他企业类型不设置children属性，表示不可展开

          treeData.push(treeNode);
        }

        this.entTypeTreeData = treeData;
        console.log("企业类型树形数据:", this.entTypeTreeData);
      } catch (error) {
        console.error("获取企业类型失败:", error);
        this.$message.error("获取企业类型失败");
      }
    },

    // 打开标记企业小类弹窗
    openTypeDialog() {
      if (this.selection.length === 0) {
        this.$message.warning("请先选择要标记的企业");
        return;
      }

      this.typeForm = {
        typeCode: "",
      };
      this.typeDialogVisible = true;
    },

    // 确认更新企业类型
    async confirmUpdateType() {
      if (!this.typeForm.typeCode) {
        this.$message.warning("请选择企业小类");
        return;
      }

      this.updateTypeLoading = true;
      try {
        const res = await updateEnterpriseType({
          enterpIds: this.selection,
          typeCode: this.typeForm.typeCode,
        });

        if (res.status === 200) {
          this.$message.success("企业小类标记成功");
          this.typeDialogVisible = false;
          this.search(); // 刷新列表
        } else {
          this.$message.error(res.message || "标记失败");
        }
      } catch (error) {
        console.error("标记企业小类失败:", error);
        this.$message.error("标记失败");
      } finally {
        this.updateTypeLoading = false;
      }
    },

    // 处理企业类型参数逻辑
    getEnterpriseTypeParams() {
      if (!this.entTypeVal) {
        return {
          entTypeVal: "",
          managementType: "",
        };
      }

      // 判断选中的是否为经营企业的子类
      const isManagementSubType = this.isManagementSubType(this.entTypeVal);

      let result;
      if (isManagementSubType) {
        // 选择经营企业的子类时：managementType传小类值，entTypeVal传父级ID
        result = {
          entTypeVal: "02", // 传经营企业的父级ID
          managementType: this.entTypeVal, // 传小类值
        };
        console.log(`选择了经营企业子类: ${this.entTypeVal}, 传参:`, result);
      } else {
        // 选择父节点大类时：保持原来的传参，managementType传空
        result = {
          entTypeVal: this.entTypeVal,
          managementType: "",
        };
        console.log(`选择了企业大类: ${this.entTypeVal}, 传参:`, result);
      }

      return result;
    },

    // 判断选中的是否为经营企业的子类
    isManagementSubType(selectedValue) {
      if (!selectedValue || !this.entTypeTreeData) {
        return false;
      }

      // 查找经营企业（ID="02"）的子类
      const managementType = this.entTypeTreeData.find(
        (item) => item.id === "02"
      );
      if (!managementType || !managementType.children) {
        return false;
      }

      // 检查选中值是否在经营企业的子类中
      return managementType.children.some(
        (child) => child.id === selectedValue
      );
    },
    // 清除看板筛选
    clearDashboardFilter() {
      // 清除企业类型筛选
      this.entTypeVal = "";
      // 移除URL中的看板参数
      this.$router.replace({
        path: this.$route.path,
        query: {},
      });
      // 重新搜索
      this.search();
    },
  },

  mounted() {
    this.getSessionStorage();
    this.getData();
    this.getSelect();
    this.getEnterpriseType();

    // 处理从看板跳转过来的参数
    if (this.$route.query && this.$route.query.fromDashboard) {
      this.$nextTick(() => {
        // 设置企业类型筛选条件
        if (this.$route.query.enterpriseType) {
          // 这里需要根据传入的企业类型ID找到对应的父级和子级
          this.entTypeVal = this.$route.query.enterpriseType;
          // TODO: 可能需要根据子级ID反向查找父级ID
        }
        // 自动执行搜索
        this.search();
      });
    }

    if (this.$route.query && this.$route.query.showDetail) {
      this.$nextTick(() => {
        let bool = true;
        this.$emit("entBool", bool, "videoInspection");
        this.$emit("entId", this.$route.query.id);
      });
    }
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
      isShowMark: (state) =>
        state.user.roles?.includes("cab763a1e14d4da49abcdd069be4602c"),
    }),
    ...mapStateControler({
      entModelName: (state) => state.entModelName,
      entListControler: (state) => state.entListControler,
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    // 监听路由参数变化
    "$route.query": {
      handler(newQuery, oldQuery) {
        // 如果是从看板跳转过来的新请求
        if (newQuery.fromDashboard && newQuery.enterpriseType) {
          // 避免重复处理相同的参数
          if (oldQuery.enterpriseType !== newQuery.enterpriseType) {
            this.entTypeVal = newQuery.enterpriseType;
            // TODO: 可能需要根据子级ID反向查找父级ID
            this.search();
          }
        }
      },
      deep: true,
      immediate: false,
    },
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    $attrs: {
      handler(newVal, oldVal) {
        console.log(newVal);
      },
    },
    entModelName: {
      handler(newVal, oldVal) {
        this.entModelName = this.tabName;
      },
      deep: true,
      // immediate: true,
    },
    radio1: {
      handler(newVal) {
        this.enterpName = "";
        this.levelVal = this.levelVal;
        this.search();
      },
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .subTable.el-table td.el-table__cell,
/deep/ .subTable.el-table th.el-table__cell.is-leaf,
/deep/ .el-table--border .subTable.el-table__cell {
  border-bottom: 0;
  border-right: 0;
}
/deep/ .el-table--border .subTable {
  border-left: 1px solid #ebeef5;
  border-right: 1px solid #ebeef5;
  border-top: 1px solid #ebeef5;
}
.titTop {
  display: flex;
  justify-content: space-between;
  /deep/ .el-radio-group {
    margin: 0 0 0 0;
  }
}
.enterpriseList {
  background-color: #fff;
  .icon {
    display: flex;
    align-items: center;
    justify-content: space-around;
    cursor: pointer;
    color: rgb(57, 119, 234);
    i {
      margin-right: 2px;
      font-size: 16px;
    }
  }
  .icon_box {
    display: flex;
    align-items: center;
    justify-content: space-around;
  }
  .enterpName {
    overflow: hidden;
    text-align: center;
    white-space: nowrap;
    text-overflow: ellipsis;
    -o-text-overflow: ellipsis;
    cursor: pointer;
    color: #333;
  }
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
    }
    .operation {
      // margin-left: 20px;
      margin-top: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        min-width: 1150px;
        display: flex;
        justify-content: flex-start;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
        }
      }
    }
    .export {
      // margin-right: 20px;
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

// 标记企业小类弹窗样式
.type-dialog-content {
  .selected-count {
    color: #409eff;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
  }
}
</style>
