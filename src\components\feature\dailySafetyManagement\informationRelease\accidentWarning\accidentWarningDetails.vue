<template>
  <div class="accidentWarningDetails">
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
      top="10vh"
      :close-on-click-modal="false"
    >
      <div style="height: 67vh;overflow: auto;" class="dialogInfo">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px !important"
          class="formSearch"
        >
          <el-form-item label="事故单位名称" prop="" >
            <el-input v-model.trim="form.unitName" maxlength="30" placeholder="" disabled />
          </el-form-item>

          <el-form-item label="所属行政区划" prop="" >
            <el-input v-model.trim="form.distName" maxlength="30" placeholder="" disabled/>
          </el-form-item>

          <el-form-item label="事故类型" prop="">
            <el-select
              v-model="form.typeName"
              placeholder="事故类型"
              clearable
              style="width: 100%"
              disabled
            >
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="事故时间" prop="">
            <el-input
              v-model.trim="form.time"
              maxlength="30"
              placeholder="请输入标题"
              disabled
            />
          </el-form-item>

          <el-form-item label="事故介绍" prop="" style="width: 100%">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="80"
              placeholder="请输入事故介绍"
              v-model.trim="form.description"
              disabled
            ></el-input>
          </el-form-item>

          <el-form-item label="附件" prop="attachmentList" style="width: 100%" class="newAttachment">
            <AttachmentUpload
              :attachmentlist="form.attachmentList"
              :limit="5"
              type="office"
              class="newAttachment"
              v-bind="{}"
              :editabled="true"
            ></AttachmentUpload>
          </el-form-item>

          <el-form-item :label="alreadyHank" prop="" style="width: 100%">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="80"
              placeholder=""
              v-model.trim="form.haveReadName"
              disabled
            ></el-input>
          </el-form-item>

          <el-form-item :label="noHank" prop="" style="width: 100%">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="80"
              placeholder=""
              v-model.trim="form.didNotReadName"
              disabled
            ></el-input>
          </el-form-item>
        </el-form>

        <!-- <div class="btnbox">
          <el-button
            v-if="roleInfo.user_type !== 'ent'"
            type="primary"
            @click="chooseTemplate()"
            icon="el-icon-tickets"
            size="small"
            >选择模板</el-button
          >
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  noticeId
} from "../../../../../api/informationRelease";
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
export default {
  name: "accidentWarningDetails",
  components: {AttachmentUpload},
  props: {
    closable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      noHank:'',
      alreadyHank:'',
      // 表单参数
      form: {
        unitId: "",
        unitName: "",
        orgDTOs: [],
        time: "",
        rcveUnit: "",
        attachmentList: [],
        typeCode: "",
        status: "",
        distCode: "",
        districtName: "",
        id: "",
        description: "",
        didNotReadName:'',//未阅人，未接收单位
        haveReadName:"",//已阅人，接收单位
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全预警表格数据
      safetyList: [],
      // 弹出层标题
      title: "",
      tieles: "",
      // 是否显示弹出层
      open: false,
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      statusList: [
        { label: "全部", value: "-1" },
        { label: "已发布", value: "1" },
        { label: "待发布", value: "0" },
      ],
    };
  },
  created() {},
  methods: {
    getData(row, col) {
       noticeId({ id: row.id }).then((response) => {         
        this.form = response.data.data; 
         var noHankData=  response.data.data.haveRead?response.data.data.haveRead:'0'
         this.alreadyHank=`已查收单位(${noHankData}家)` //didNotRead
          var noHankData=response.data.data.didNotRead?response.data.data.didNotRead:'0'
         this.noHank=`未查收单位(${noHankData}家)`   
      });
    },
  },
};
</script>

<style lang="scss" scoped>

/deep/ .el-form {
  display: flex;
  flex-wrap: wrap;
}
/deep/ .el-form-item {
  width: 50%;
}
.accidentWarningDetails {
}
/deep/ .newAttachment > div >div{
  flex-wrap: wrap;
}
/deep/ .slot_file{
  width:30%!important;
}
</style>