<template>
  <div class="enterpriseManagement">
    <div v-show="!TrendChartBool">
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span @click="goToSafety"
                ><a-icon type="home" theme="filled" class="icon" />
                智能识别报警统计
              </span>
            </a-breadcrumb-item>
            <a-breadcrumb-item v-if="showBreak">{{
              districtName
            }}</a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <el-date-picker
            v-model="date"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="getDate"
            unlink-panels
            size="mini"
          >
          </el-date-picker>
          <el-button type="primary" size="mini" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
        <!-- <CA-button type="primary" size="mini" plain @click="gotoTrendAnalysis"
          ><a-icon type="bar-chart" class="chart-icon" />企业分析</CA-button
        > -->
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>视频智能分析报警统计</h2>
          <CA-RadioGroup
            class="radio"
            v-model="mode"
            backgroundColor="#F1F6FF"
            border="1px solid rgba(57, 119, 234, 0.2)"
          >
            <CA-radio
              :label="{
                src: '../../../static/img/liebiao_icon.png',
                style: 'width:15px;height:15px',
              }"
              :labelTwo="{
                src: '../../../static/img/liebiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
              value="统计"
              bgColorActive="#409eff"
            >
            </CA-radio>
            <CA-radio
              :label="{
                src: '../../../static/img/tubiao_icon.png',
                style: 'width:15px;height:15px',
              }"
              :labelTwo="{
                src: '../../../static/img/tubiao_icon_hover.png',
                style: 'width:15px;height:15px',
              }"
              bgColorActive="#409eff"
              value="图表"
            >
            </CA-radio>
          </CA-RadioGroup>
        </div>
        <div v-show="showtable">
          <div class="table">
            <el-table
              :data="tableData"
              v-loading="loading"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              @sort-change="changeTableSort"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <el-table-column type="selection" width="50" align="center">
              </el-table-column>
              <el-table-column label="行政区划" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <div
                      v-if="
                        row.districtCode == 429004 ||
                        row.districtCode == 429005 ||
                        row.districtCode == 429006 ||
                        row.districtCode == 429021
                      "
                    >
                      {{ row.districtName }}
                    </div>
                    <div v-else>
                      <span
                        v-if="$index != 0 && !showBreak && isXiaZuan"
                        @click="xiaZuan(row.districtCode, row.districtName)"
                        style="color: #3977ea; cursor: pointer"
                        >{{ row.districtName }}</span
                      >
                      <span v-else>{{ row.districtName }}</span>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="告警企业（个）" align="center">
                <el-table-column
                  label="告警企业数量"
                  sortable="custom"
                  prop="companyAlarmNum"
                  align="center"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <span
                      v-if="row.companyAlarmNum || tableData.length == 1"
                      @click="
                        openDialogAlarmDetailsList(
                          row.districtCode,
                          0,
                          row.districtName
                        )
                      "
                      style="color: #3977ea; cursor: pointer"
                      >{{ row.companyAlarmNum }}</span
                    >
                    <span v-else>{{ row.companyAlarmNum }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="告警解除企业数量"
                  sortable="custom"
                  prop="companyAlarmClearedNum"
                  align="center"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <span
                      v-if="row.companyAlarmClearedNum || tableData.length == 1"
                      @click="
                        openDialogNotClearedAlarm(
                          row.districtCode,
                          0,
                          row.districtName
                        )
                      "
                      style="color: #3977ea; cursor: pointer"
                      >{{ row.companyAlarmClearedNum }}</span
                    >
                    <span v-else>{{ row.companyAlarmClearedNum }}</span>
                  </template>
                </el-table-column>
                <el-table-column
                  label="告警企业解除率"
                  sortable="custom"
                  prop="companyAlarmClearedRate"
                  align="center"
                >
                  <template slot-scope="{ row, column, $index, store }">
                    <span>{{ row.companyAlarmClearedRate }}</span>
                  </template>
                </el-table-column>
              </el-table-column>
              <el-table-column label="告警次数（次）" align="center">
                <el-table-column
                  label="总告警数量"
                  sortable="custom"
                  prop="totalAlarmNum"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="总告警解除数量"
                  sortable="custom"
                  prop="totalAlarmClearedNum"
                  align="center"
                >
                </el-table-column>
                <el-table-column
                  label="总告警解除率"
                  sortable="custom"
                  prop="totalAlarmClearedRate"
                  align="center"
                >
                  <!-- <template slot-scope="{ row, column, $index, store }">
                    <span>{{ row.clearedAlarmRate }}</span>
                  </template> -->
                </el-table-column>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <!-- <el-pagination
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page.sync="currentPage"
                    :page-size="100"
                    layout="total, prev, pager, next"
                    :total="1000"
                >
                </el-pagination> -->
          </div>
        </div>
        <div v-show="!showtable">
          <div id="myCharted"></div>
        </div>
      </div>
    </div>
    <!-- <TrendChart
      v-show="TrendChartBool"
      @RunningState="TrendChartFun"
      ref="TrendChart"
    ></TrendChart> -->
    <!-- <NotClearedAlarm ref="NotClearedAlarm"></NotClearedAlarm> -->
    <AlarmDetailsList ref="AlarmDetailsList"></AlarmDetailsList>
  </div>
</template>
<script>
// import NotClearedAlarm from "./NotClearedAlarm";
import AlarmDetailsList from "./AlarmDetailsList";
// import TrendChart from "./trendChart";
import { alarmDistrictExport, alarmDistrictCount } from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  components: {
    // NotClearedAlarm:() => import("./NotClearedAlarm"),
    // TrendChart:() => import("./trendChart"),
    AlarmDetailsList: () => import("./AlarmDetailsList"),
  },
  data() {
    return {
      tableData: [],
      level: ["1", "2", "3", "4"],
      tableData: [],
      mode: "统计",
      showtable: true,
      currentPage: 1,
      total: 0,
      widthBox: 1400,
      distCode: this.$store.state.login.userDistCode,
      showBreak: false,
      districtName: "",
      loading: false,
      selection: [],
      districtName: "",
      TrendChartBool: false,
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (144 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      endData: "",
      statrData: "",
      echartDist: [],
      alarm: [],
      companyAlarmClearedNum: [],
      clearedAlarmRate: [],
    };
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
  methods: {
    getEnRiskList() {
      this.loading = true;
      alarmDistrictCount({
        districtCode: this.distCode,
        startTime:
          this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        endTime:
          this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss"),
        // size: 25,
        // current: this.currentPage,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data;
          // this.total = res.data.data.total;
          if (this.$store.state.login.user.user_type == "park") {
            for (let i = 0; i < this.tableData.length; i++) {
              //园区 ["告警企业数alarm", "消警企业数companyAlarmClearedNum", "企业消警率clearedAlarmRate"]
              this.echartDist[0] = this.tableData[i].districtName;
              this.alarm[0] = this.tableData[i].companyAlarmNum;
              this.companyAlarmClearedNum[0] =
                this.tableData[i].companyAlarmClearedNum;
              this.clearedAlarmRate[0] =
                this.tableData[i].companyAlarmClearedRate;
            }
          } else {
            for (let i = 1; i < this.tableData.length; i++) {
              //地市
              this.echartDist[i - 1] = this.tableData[i].districtName;
              this.alarm[i - 1] = this.tableData[i].companyAlarmNum;
              this.companyAlarmClearedNum[i - 1] =
                this.tableData[i].companyAlarmClearedNum;
              this.clearedAlarmRate[i - 1] =
                this.tableData[i].companyAlarmClearedRate;
            }
          }
        }
      });
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      this.getEnRiskList();
    },
    getDate() {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd hh:mm:ss");
    },
    // 导出
    exportExcel() {
      //let list = [this.distCode, ...this.selection];
      alarmDistrictExport({
        districtCode: this.distCode,
        // districtCodes: list.length<=1?null:list,
        districtCodes: this.selection.length <= 0 ? [] : this.selection,
        // districtCode:this.selection.length <= 0 ? '' : this.selection.join(','),
        startTime:
          this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        endTime:
          this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss"),
        // isContainParent: list.length<=1?true:false,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "视频分析报警地区统计" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].districtCode;
      }
    },

    xiaZuan(districtCode, districtName) {
      this.districtName = districtName;
      this.showBreak = true;
      this.distCode = districtCode;
      this.getEnRiskList();
    },
    goToSafety() {
      this.showBreak = false;
      this.TrendChartBool = false;
      //   this.level = "";
      this.distCode = this.$store.state.login.userDistCode;
      this.getEnRiskList();
    },
    // 排序
    changeTableSort(column) {
      console.log(column);
      var fieldName = column.prop;
      var sortingType = column.order;
      var data = [...this.tableData];
      var shiftTotal = [];
      if (data[0].districtCode == this.$store.state.login.userDistCode) {
        shiftTotal = data.shift();
      } else if ((this.districtCode = data[0].districtCode)) {
        shiftTotal = data.shift();
      }
      //按照降序排序
      if (sortingType == "descending") {
        data = data.sort((a, b) => b[fieldName] - a[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        data = data.sort((a, b) => a[fieldName] - b[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      } else {
        this.getEnRiskList();
      }
    },
    openDialogNotClearedAlarm(distCode, type, districtName) {
      this.$refs.NotClearedAlarm.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.NotClearedAlarm.getEntData(
        distCode,
        type,
        districtName,
        this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss")
      );
      this.$refs.NotClearedAlarm.getDistrict();
    },
    openDialogAlarmDetailsList(distCode, type, districtName) {
      this.$refs.AlarmDetailsList.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.AlarmDetailsList.getEntData(
        distCode,
        type,
        districtName,
        this.startDate || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss"),
        this.endDate || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss")
      );
      this.$refs.AlarmDetailsList.getDistrict();
    },
    search() {
      this.currentPage = 1;
      this.getEnRiskList();
    },
    TrendChartFun(data) {
      // console.log(data);
      this.TrendChartBool = data;
      this.getEnRiskList();
    },
    // gotoTrendAnalysis() {
    //   this.TrendChartBool = true;
    //   this.$nextTick(() => {
    //     this.$refs.TrendChart.getData();
    //   });
    // },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    drawLine() {
      let myChart = this.$echarts.init(document.getElementById("myCharted"));
      myChart.setOption({
        tooltip: {
          trigger: "axis",
          padding: 0,
          enterable: true,
          transitionDuration: 1,
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
          // formatter: '{b0}<br/>{a0}: {c0}<br />{a1}: {c1}<br />{a2}: {c2}%'
          formatter: function (params) {
            var tipHtml = "";
            tipHtml =
              "<div style='padding:5px 10px'>" +
              '<div style="width:100%;height:auto;;padding:5px">' +
              '<div style=";color:#fff;font-size:14px;">' +
              params[0].axisValue +
              "</div>" +
              '<div><i style="display:inline-block;width:10px;height:10px;background:linear-gradient(#458EF3,#89D8FE);border-radius:50%;margin-right:3px">' +
              "</i><span>报警企业数：" +
              params[0].value +
              "</span></div>" +
              '<div><i style="display:inline-block;width:10px;height:10px;background:linear-gradient(rgba(250,180,101,0.8),rgba(250,180,101,1));border-radius:50%;margin-right:3px">' +
              "</i><span>未消警企业数：" +
              params[1].value +
              "</span></div>" +
              '<div><i style="display:inline-block;width:10px;height:10px;background:#2fca95;border-radius:50%;margin-right:3px">' +
              "</i><span>企业消警率：" +
              params[2].value +
              "</span></div>" +
              "</div>" +
              "</div>";
            return tipHtml;
          },
        },
        grid: {
          x: 45,
          y: 65,
          x2: 50,
          y2: 80,
          borderWidth: 1,
        },
        legend: {
          data: ["告警企业数", "消警企业数", "企业消警率"],
          left: "center",
          top: 14,
          icon: "circle",
          itemWidth: 10,
          selectedMode: false,
          orient: "horizontal",
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: [
          {
            type: "category",
            data: this.echartDist,
            axisLabel: {
              interval: 0,
              rotate: 40,
              textStyle: {
                color: "#545C65",
              },
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              lineStyle: {
                color: "#F2F4F8",
              },
            },
            splitArea: {
              show: true,
            },
          },
        ],
        yAxis: [
          {
            name: "企业数（个）",
            splitLine: {
              show: false,
            },
            splitArea: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
            },
          },
          {
            name: "消警率（%）",
            // "max": 1,
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            axisLabel: {
              interval: 0,
              textStyle: {
                color: "#545C65",
              },
              formatter: function (value, index) {
                // console.log(value,index)
                var numStr = value.toString();
                var arr = numStr.split(".");
                if (arr[1] && arr[1].length > 0) {
                  // console.log(numStr)
                  return Number(numStr).toFixed(2);
                } else {
                  return value;
                }
              },
            },
          },
        ],
        series: [
          {
            name: "告警企业数", //
            type: "bar",
            yAxisIndex: 0,
            barWidth: "14",
            data: this.alarm,
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  type: "linear",
                  global: false,
                  colorStops: [
                    {
                      offset: 0,
                      color: "#458EF3",
                    },
                    {
                      offset: 1,
                      color: "#89D8FE",
                    },
                  ],
                },
                barBorderRadius: [14, 14, 0, 0],
              },
            },
          },
          {
            name: "消警企业数",
            type: "bar",
            yAxisIndex: 0,
            symbolSize: [14, "100%"],
            barWidth: "14",
            barGap: "60%",
            data: this.companyAlarmClearedNum,
            label: {
              show: true,
              position: "top",
              distance: 20,
              verticalAlign: "middle",
              textStyle: {
                color: "black",
                fontSize: 12,
              },
            },
            itemStyle: {
              normal: {
                color: {
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  type: "linear",
                  global: false,
                  colorStops: [
                    {
                      offset: 0,
                      color: "rgba(250,180,101,0.8)",
                    },
                    {
                      offset: 1,
                      color: "rgba(250,180,101,1)",
                    },
                  ],
                },
                barBorderRadius: [14, 14, 0, 0],
              },
            },
          },
          {
            name: "企业消警率",
            type: "line",
            data: this.clearedAlarmRate,
            yAxisIndex: 1,
            smooth: true,
            symbolSize: 0,
            lineStyle: {
              color: "#2fca95",
              width: 2,
            },
            itemStyle: {
              normal: {
                color: "#2fca95",
              },
            },
            // markLine: {
            //   label: {
            //     show: true,
            //     position: "end",
            //   },
            //   lineStyle: {
            //     color: "#2fca95",
            //   },
            //   data: [
            //     {
            //       name: "平均值",
            //       yAxis: 52.54,
            //     },
            //   ],
            // },
          },
        ],
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getEnRiskList();
    this.$setEchart("myCharted", 250, 250);
    this.$nextTick(function () {
      this.drawLine();
    });
  },
  watch: {
    mode(newValue, oldValue) {
      if (newValue == "统计") {
        this.showtable = true;
      } else {
        this.showtable = false;
      }
    },
    //观察option的变化
    tableData: {
      handler(newVal, oldVal) {
        this.drawLine();
      },
      deep: true,
      // immediate: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      width: 500px;
      display: flex;
      justify-content: space-between;
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // padding: 10px 0;
      margin-bottom: 10px;
      height: 40px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
        float: left;
      }
      .radio {
        float: right;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
