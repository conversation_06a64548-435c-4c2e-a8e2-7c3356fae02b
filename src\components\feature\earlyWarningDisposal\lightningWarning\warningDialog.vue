<template>
  <div>
    <el-dialog
      title="雷电预警信息"
      :visible.sync="show"
      width="500px"
      @close="closeBoolean()"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="container">
        <div class="waing-box" :class="['bg-'+warnInfo.levelCode]" >{{ warnInfo.levelCode | levelfilter }}</div>
        <ul>
          <li>
            <div class="l">预警时间开始</div>
            <div class="r">{{ warnInfo.beginTime }}</div>
          </li>
          <li>
            <div class="l">预警结束时间</div>
            <div class="r">{{ warnInfo.endTime }}</div>
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { lightningWarningDetail } from "@/api/earlyWarningDisposal";

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    entObj: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      warnInfo:{}
    };
  },
  filters: {
    levelfilter(val) {
      if (val == 1) {
        return "红色预警";
      } else if (val == 2) {
        return "橙色预警";
      } else if (val == 3) {
        return "黄色预警";
      }
    },
  },
  mounted() {
    this.getData();
  },
  methods: {
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    getData() {
      lightningWarningDetail(this.entObj.crashNumber).then(
        (res) => {
          if (res.data.code == 0) {
            this.warnInfo = res.data.data;
            // this.warnInfo.levelCode = 2;
          }
        }
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.waing-box {
  width: 160px;
  height: 60px;
  background: #bddd3d;
  margin: auto;
  border-radius: 8px;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  margin-bottom: 30px;
}
.bg-1{
  display: flex;
  background: #e7381d;
}
.bg-2{
  display: flex;
  background: #ffa500;
}
.bg-3{
  display: flex;
  background: #ffd700;
}
ul {
  width: 100%;
 
  li {
    list-style-type: none;

    display: flex;
    align-items: center;

    overflow: hidden;
    min-height: 40px;
    .red {
      color: red;
    }
    .l {
      width: 50%;
      min-height: 40px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 10px;
      color: #60627a;
    }

    .r {
      width: 48%;
      padding: 1%;
      word-break: break-all;
    }
  }

  .lang {
    list-style-type: none;
    width: 50%;
    display: flex;
    overflow: hidden;
    min-height: 40px;
    .red {
      color: red;
    }
    .l {
      width: 24.9%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 5px 10px;
      color: #60627a;
    }
    .r {
      width: 73.3%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      text-align: left;
      padding: 0px 10px;
      word-break: break-all;
    }
  }
}
</style>
