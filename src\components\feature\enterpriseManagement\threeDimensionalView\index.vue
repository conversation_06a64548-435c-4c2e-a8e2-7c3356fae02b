<template>
  <div class="threeDimensionalView">
    <!-- {{MapDataTo}} -->
    <!-- <div class="shebei-list">
      <div class="shebei-item">
        <h2>
          <span class="real-title">甲醇罐区（420610062001）</span
          ><span class="real-leav">二级</span>
        </h2>
        <ul>
          <li>
            <i class="qiye" @click="processBool"></i>3020-T-101A(碳九原料)
          </li>
          <li>
            <i class="qiti" @click="processBool"></i
            >1.5万吨/年产碳九1#树脂装置R-101
          </li>
          <li>
            <i class="shexiangtou" @click="processBool"></i
            >3020-T-101A(碳九原料)
          </li>
        </ul>
      </div>
      <div class="shebei-item">
        <h2>
          <span class="real-title">甲醇罐区（420610062001）</span
          ><span class="real-leav">二级</span>
        </h2>
        <ul>
          <li>
            <i class="cangku" @click="processBool"></i
            >1.5万吨/年产碳九1#树脂装置R-101
          </li>
          <li>
            <i class="cuguang" @click="processBool"></i>3020-T-101A(碳九原料)
          </li>
          <li>
            <i class="zhuangzhi" @click="processBool"></i
            >1.5万吨/年产碳九1#树脂装置R-101
          </li>
        </ul>
      </div>
    </div> -->
    <div class="map-main">
      <div id="map">
        <div id="toolTip"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getMapData } from "@/api/mergencyResources";
// const echarts = require('echarts');
export default {
  //import引入的组件
  // props:['MapDataTo'],
  name: "threeDimensionalView",
  data() {
    return {};
  },
  //方法集合
  methods: {
    loadMapToCenter(arr) {
      let viewer = new Cesium.Viewer("map", {
        //4326 瓦片
        imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
          // url: "http://***********/cloudapi/service/api/egis/base/v1/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=kd&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&",
          url: "http://************:590/service/api/egis/base/v1/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=kd&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&Authorization=Basic Mjg1MjRkOGM2NTg0NDYzMGEzNDI3MjcwYzlhMTYzMjM6ODRiYzE3NjUwYmIwNDQ5MWFhODQ3NWI5Y2JlM2QxYzQ=",
          subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6"],
          format: "tiles",
          tileMatrixSetID: "c",
          tilingScheme: new Cesium.GeographicTilingScheme(),
          tileMatrixLabels: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
          ],
          layer: "tdtImgLayer",
          style: "default",
          show: false,
        }),
        terrainProvider: new Cesium.CesiumTerrainProvider({
          url: "http://223.75.53.90:8010/terrain_tiles/HB",
        }),
        animation: false,
        timeline: false,
        baseLayerPicker: false,
        sceneModePicker: false,
        fullscreenButton: false,
        homeButton: false,
        geocoder: false,
        navigationHelpButton: false,
        infoBox: false,
        navigationInstructionsInitiallyVisible: false,
      });
      viewer.imageryLayers.addImageryProvider(
        new Cesium.WebMapTileServiceImageryProvider({
          // url: "http://***********/cloudapi/service/api/egis/base/v1/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&",
          url: "http://************:590/service/api/egis/base/v1/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=cia&tileMatrixSet=c&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&Authorization=Basic Mjg1MjRkOGM2NTg0NDYzMGEzNDI3MjcwYzlhMTYzMjM6ODRiYzE3NjUwYmIwNDQ5MWFhODQ3NWI5Y2JlM2QxYzQ=",
          subdomains: ["t0", "t1", "t2", "t3", "t4", "t5", "t6"],
          format: "tiles",
          tileMatrixSetID: "c",
          tilingScheme: new Cesium.GeographicTilingScheme(),
          tileMatrixLabels: [
            "1",
            "2",
            "3",
            "4",
            "5",
            "6",
            "7",
            "8",
            "9",
            "10",
            "11",
            "12",
            "13",
            "14",
            "15",
            "16",
            "17",
            "18",
            "19",
          ],
          layer: "tdtImgAnnoLayer",
          style: "default",
          show: false,
        })
      );
      //home定位到中国范围
      let initialPosition = new Cesium.Cartesian3.fromDegrees(
        108.845808,
        34.214282,
        10000000
      );
      let homeCameraView = {
        destination: initialPosition,
      };
      viewer.scene.camera.setView(homeCameraView);
      viewer.scene.postProcessStages.fxaa.enabled = false; //关闭抗锯齿
      //图层饱和度
      viewer.imageryLayers.get(0).saturation = 1.2;
      // {x: 115.231363, y: 29.73685}
      //viewer.scene.globe.depthTestAgainstTerrain = true;
      // let tileurl = "http://223.75.53.90:8010/3dtiles/wh/tileset.json";
      // let tileurl = "/ht/bgmesh/fiddler/3608/service/76a4352c-d267-40a4-821c-24b093411f6b/0/tileset.json?apaasToken=TElKSUFTSFVBSV8zNjA4XzE4MzhfYTRiYjYyNTNiYzA1NDE5Nzg3NmE3MjE2YzZlNDUyYzE%3D";

      //       [
      //     {
      //         "id": "2c9287cd7e925bf5017e9716b5580064",
      //         "cascUrl": "/ht/bgmesh/fiddler/3608/service/76a4352c-d267-40a4-821c-24b093411f6b/0/tileset.json",
      //         "cascAuthorization": "TElKSUFTSFVBSV8zNjA4XzE4MzhfYTRiYjYyNTNiYzA1NDE5Nzg3NmE3MjE2YzZlNDUyYzE=",
      //         "cascType": "3d_map_park",
      //         "createTime": "2023-06-25 09:31:00",
      //         "relationId": "4206000003"
      //     },
      //     {
      //         "id": "2c9287cd7e925bf5017e9716b5580065",
      //         "cascUrl": "/ht/bgmesh/fiddler/3607/service/96d2d79c-f8d5-4e40-85f5-62a84e872678/0/tileset.json",
      //         "cascAuthorization": "TElKSUFTSFVBSV8zNjA3XzE4MzlfNWFmYjJmNTA0OGNlNGQzMWI4YWRlYjE0ZDdmNGY1ZjU=",
      //         "cascType": "3d_map_park",
      //         "createTime": "2023-06-25 09:31:00",
      //         "relationId": "4206000003"
      //     }
      // ]

      // debugger
      // let titleUrlData=this.getUrl()
      console.log(arr, "我执行了吗？？？=============");
      arr.forEach((data) => {
        var token = data.cascAuthorization.split("=")[0];
        var cascUrl = data.cascUrl;
        let currentModel = new Cesium.Cesium3DTileset({
          url: `${cascUrl}?apaasToken=${token}`,
          maximumScreenSpaceError: 20,
          maximumMemoryUsage: 1024,
          skipLevelOfDetail: true,
          baseScreenSpaceError: 1024,
          skipScreenSpaceErrorFactor: 16,
          skipLevels: 20,
        });
        viewer.scene.primitives.add(currentModel);
        viewer.flyTo(currentModel);
      });

      //初始化鼠标事件
      let handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
      //定义标注点对象
      let PointPrimitive = (function () {
        function _(position, text, img) {
          this.options = {
            name: "MeasurePoint",
            position: position,
            billboard: {
              image: img,
              scale: 0.2,
              //heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
            label: {
              text: text,
              font: "18px sans-serif",
              fillColor: Cesium.Color.GOLD,
              style: Cesium.LabelStyle.FILL_AND_OUTLINE,
              outlineWidth: 2,
              verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
              pixelOffset: new Cesium.Cartesian2(0, -15),
              disableDepthTestDistance: Number.POSITIVE_INFINITY,
            },
          };
          this.img = img;
          this.position = position;
          this.text = text;
          this._init();
        }

        _.prototype._init = function () {
          var _self = this;
          var _update = function () {
            return _self.position;
          };
          var _text = function () {
            return _self.text;
          };
          var _img = function () {
            return _self.img;
          };
          //实时更新
          this.options.position = new Cesium.CallbackProperty(_update, false);
          this.options.label.text = new Cesium.CallbackProperty(_text, false);
          this.options.billboard.image = new Cesium.CallbackProperty(
            _img,
            false
          );
          viewer.entities.add(this.options);
        };
        return _;
      })();

      var egPoint = {
        x: -1975518.9344807558,
        y: 5008428.224536709,
        z: 3408357.1151631265,
      };
      var egPoint1 = {
        x: -1975475.9869762145,
        y: 5008467.012210143,
        z: 3408319.8155963174,
      };
      var egPoint2 = {
        x: -1975480.5627230185,
        y: 5008397.892620272,
        z: 3408448.4530744622,
      };
      var egPoint3 = {
        x: -1975059.0610458052,
        y: 5008649.068072618,
        z: 3408336.1241120035,
      };
      // new PointPrimitive(
      //   egPoint,
      //   "这是一个点",
      //   "http://************:9699/Examples/examples/img/point.png"
      // );
      // new PointPrimitive(
      //   egPoint1,
      //   "这又是一个点",
      //   "http://************:9699/Examples/examples/img/DivPoint/fire.png"
      // );
      // new PointPrimitive(
      //   egPoint2,
      //   "这还是一个点",
      //   "http://************:9699/Examples/examples/img/point.png"
      // );
      // new PointPrimitive(
      //   egPoint3,
      //   "这还是一个点",
      //   "http://************:9699/Examples/examples/img/point.png"
      // );
    },
    //清除鼠标事件
    clearHandler() {
      if (handler) {
        handler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_CLICK);
        handler.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
        handler.removeInputAction(Cesium.ScreenSpaceEventType.RIGHT_CLICK);
        handler = null;
      }
    },
    processBool() {
      console.log(123);
    },
    setpoint() {
      var that = this;
      var tooltip = document.getElementById("toolTip");
      tooltip.style.display = "block";
      //监听移动事件
      handler.setInputAction(function (movement) {
        tooltip.style.left = movement.endPosition.x + 20 + "px";
        tooltip.style.top = movement.endPosition.y + "px";
        tooltip.innerHTML = "单击开始，右键结束";
      }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
      var positon = null,
        point,
        text = "";
      handler.setInputAction(function (movement) {
        tooltip.style.display = "none";
        //cartesian = viewer.scene.pickPosition(movement.position);
        //点击的经纬度和高程
        let obj = that.lonLan(movement.position);
        if (Cesium.defined(obj.position)) {
          positon = obj.position;
          text = "经度:" + obj.lon + ",纬度" + obj.lat + ",高程:" + obj.height;
          if (!Cesium.defined(point)) {
            point = new PointPrimitive(positon, text, "map/images/point.png"); //
          } else {
            point.position = positon;
            point.text = text;
            console.log(point);
          }
        }
      }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
      handler.setInputAction(function (movement) {
        that.clearHandler();
      }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
    },
    async getUrl() {
      var params = {
        id: "420210014",
      };
      var MapData = await getMapData(params);
      console.log(MapData, "MapData================================>");
      return viewObj, MapData.data;
      // if(MapData.status==200 && MapData.data.length > 0){
      //   this.addModelTileSet(viewObj,MapData.data);
      // }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // debugger
    // var arr=[]
    // this.loadMapToCenter(arr);
  },
};
</script>
<style lang="scss" scoped>
.threeDimensionalView {
  display: flex;
  // height: 500px;
  .shebei-list {
    width: 25%;
    height: calc(100vh - 245px);
    margin-right: 15px;
    background: rgba(238, 243, 249, 0);
    border: 1px solid #d8e0ee;
    box-shadow: 0px 0px 8px 0px rgba(121, 163, 241, 0.3);
    border-radius: 4px 4px 0px 0px;
    //    border: 1px solid #ddd;
    .shebei-item {
      padding: 0 10px;
      //    h2{
      //        color: #fff;
      //        font-size: 14px;
      //        height: 35px;
      //        line-height: 35px;
      //        background-color: rgb(80, 172, 189);
      //        margin: 0;
      //        padding: 0 15px;
      //        position: relative;
      //    }
      > h2 {
        color: #000;
        font-size: 14px;
        background: url("/static/img/assets/img/wxybg.png") no-repeat center
          center;
        //  background-size: cover;
        background-size: 100%;
        height: 52px;
        width: 100%;
        margin: 0;
        position: relative;
        .real-title {
          position: absolute;
          left: 15px;
          top: 20px;
        }
        .real-leav {
          position: absolute;
          right: 2.5%;
          font-size: 12px;
          top: 13px;
          color: #fff;
          display: inline-block;
          width: 8%;
          text-align: center;
          height: 20px;
        }
      }
      ul {
        padding: 0;
        li {
          line-height: 44px;
          height: 44px;
          list-style-type: none;
          font-size: 14px;
          font-weight: 400;
          color: #3b4046;
          i {
            display: inline-block;
            width: 26px;
            height: 26px;
            vertical-align: middle;
            margin-right: 12px;
            position: relative;
            top: -3px;
          }
          .qiti {
            background: url("/static/img/assets/img/qiti-list.png") no-repeat
              center center;
            background-size: cover;
          }
          .qiye {
            background: url("/static/img/assets/img/qiye-list.png") no-repeat
              center center;
            background-size: cover;
          }
          .shexiangtou {
            background: url("/static/img/assets/img/shexiangtou-list.png")
              no-repeat center center;
            background-size: cover;
          }
          .cuguang {
            background: url("/static/img/assets/img/cuguang-list.png") no-repeat
              center center;
            background-size: cover;
          }
          .cangku {
            background: url("/static/img/assets/img/cangku-list.png") no-repeat
              center center;
            background-size: cover;
          }
          .zhuangzhi {
            background: url("/static/img/assets/img/zhuangzhi-list.png")
              no-repeat center center;
            background-size: cover;
          }
        }
      }
    }
  }
  .map-main {
    width: 100%;
    height: calc(100vh - 245px);
    border: 1px solid #ddd;
  }
}
#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
}
#toolTip {
  display: none;
  position: absolute;
  padding: 7px;
  background: #4c4c41;
  top: 0px;
  left: 0px;

  color: white;
  font-size: 13px;
  border-radius: 8px;
  z-index: 20;
}
// /deep/.cesium-viewer-bottom{display: none;}
</style>
<style>
.cesium-viewer-bottom {
  display: none;
}
</style>
