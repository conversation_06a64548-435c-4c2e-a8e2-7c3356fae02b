import axios from "axios";
import qs from "qs";

//监测对象类型
export const getSensorBbjectListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/objectList/v1",
    data: data,
  });
};

//传感器列表
export const getSensorListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/findAll/v1",
    data: data,
  });
};
//传感器新增
export const addSensorData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/add/v1",
    data: data,
  });
};
//传感器修改
export const editSensorData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/update/v1",
    data: data,
  });
};
//传感器详情
export const detailSensorData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/id/v1?id=" + data.id,
    data: data,
  });
};
//传感器删除
export const deleteSensorData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/batch/delete/v1",
    data: data,
  });
};
//传感器监测设备类型
export const getSensorTypeData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/deviceList/v1",
    data: data,
  });
};
//传感器监测项
export const getDeviceinfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/itemList/v1",
    data: data,
  });
};
//传感器导出
export const exportDeviceinfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/gemp/deviceinfo/export/v1",
    data: data,
    responseType: "blob",
  });
};

//监测设备列表
export const getCamerainfoListData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/findAll/v1",
    data: data,
  });
};
// 危险源关联视频列表
export const findCamerainfoList = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/findList/v1",
    data: data,
  });
};
//监测设备新增
export const addCamerainfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/add/v1",
    data: data,
  });
};
//监测设备修改
export const editCamerainfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/update/v1",
    data: data,
  });
};
//监测设备详情
export const detailCamerainfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/id/v1?id=" + data.id,
    data: data,
  });
};
//监测设备删除
export const deleteCamerainfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/batch/delete/v1",
    data: data,
  });
};
//监测设备导出
export const exportCamerainfoData = (data) => {
  return axios({
    method: "post",
    headers: {
      "Content-Type": "application/json",
    },
    url: "/gemp-chemical/api/gemp/monitor/camerainfo/export/v1",
    data: data,
    responseType: "arraybuffer",
  });
};
