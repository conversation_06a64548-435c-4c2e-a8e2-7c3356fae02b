.ws-player {
  position: relative;
  background-color: #000000;
  display: flex;
  justify-content: center;
}
.ws-player .player-wrapper {
  position: relative;
  width: 100%;
  height: calc(100% - 40px);
  overflow: hidden;
}
.ws-player .player-wrapper.nocontrol {
  height: 100%;
}
.ws-player .wsplayer-item {
  display: flex;
  align-items: center;
  justify-content: center;
  position: absolute;
  width: calc(50% - 2px);
  height: calc(50% - 2px);
}
.ws-player .wsplayer-item-0 {
  top: 0;
  left: 0;
}
.ws-player .wsplayer-item-1 {
  top: 0;
  right: 0;
}
.ws-player .wsplayer-item-2 {
  bottom: 0;
  left: 0;
}
.ws-player .wsplayer-item-3 {
  bottom: 0;
  right: 0;
}
.ws-player .wsplayer-item.selected {
  border: 1px solid #009cff;
  transition: all cubic-bezier(0.19, 1, 0.22, 1) .3s;
}
.ws-player .wsplayer-item.unselected {
  border: 1px solid #161A1E;
}
.ws-player.fullplayer .player-wrapper .wsplayer-item.selected {
  width: calc(100% - 2px);
  height: calc(100% - 2px);
}
.ws-player.fullplayer .player-wrapper .wsplayer-item.unselected {
  display: none;
}
.ws-player .kind-stream-canvas {
  width: 100%;
  height: 100%;
}
.ws-player .full-content {
  width: 100%;
  height: 100%;
}
.ws-player .default-status {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  z-index: 10;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  user-select: none;
}
.ws-player .player-control {
  position: absolute;
  width: 100%;
  background: rgba(0,0,0, .8);
  visibility: hidden;
  user-select: none;
}
.ws-player .top-control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 35;
  top: 0;
  height: 30px;
  color: #fff;
}
.ws-player .record-control-bar {
  display: none;
  z-index: 20;
  bottom: 0;
  height: 39px;
  visibility: visible;
  color: #fff;
}
.ws-player .stream-info {
  white-space: nowrap;
}
.ws-player .record-control-right,
.ws-player .record-control-left {
  height: 34px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.ws-player .record-control-left {
  float: left;
}
.ws-player .record-control-right {
  float: right;
}
.ws-player .wsplayer-progress-bar {
  position: relative;
  height: 5px;
  margin: 0 10px;
  cursor: pointer;
}
.ws-player .progress-bar_background {
  position: absolute;
  background: rgba(231, 231, 231, 0.3);
  height: 100%;
  width: 100%;
}
.ws-player .progress-bar_light {
  position: absolute;
  height: 100%;
  background: #459DF5;
  z-index: 2;
}
.ws-player .progress-bar_hover_light {
  position: absolute;
  cursor: pointer;
  background: rgba(231, 231, 231, 0.7);
  height: 100%;
}
.ws-player .error {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 30;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  user-select: none;
  color: rgba(255,255,255, .8);
  font-size: 30px;
}
.ws-player .play-pause-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 30;
  width: 100%;
  height: 100%;
  visibility: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, .3);
}
.ws-player .center-play-icon {
  width: 60px;
  height: 60px;
  background-image: url(./icon/play-n.png);
}
.ws-player .center-play-icon:hover {
  background-image: url(./icon/play-h.png);
}
.ws-player .opt-icons {
  display: flex;
}
.ws-player .opt-icon {
  width: 20px;
  height: 20px;
  margin: 0 10px;
}
.ws-player .audio-icon.off {
  background-image: url(./icon/AudioOffNormal.png);
}
.ws-player .audio-icon.off:hover {
  background-image: url(./icon/AudioOffHover.png);
}
.ws-player .audio-icon.on {
  background-image: url(./icon/AudioOnPress.png);
}
.ws-player .capture-icon {
  background-image: url(./icon/SnapNormal.png);
}
.ws-player .capture-icon:hover {
  background-image: url(./icon/SnapHover.png);
}
.ws-player .close-icon {
  background-image: url(./icon/CloseNormal.png);
}
.ws-player .close-icon:hover {
  background-image: url(./icon/CloseHover.png);
}
.ws-player .play-icon.play {
  background-image: url(./icon/pause-n.png);
}
.ws-player .play-icon.play:hover {
  background-image: url(./icon/pause-h.png);
}
.ws-player .play-icon.pause {
  background-image: url(./icon/start-n.png);
}
.ws-player .play-icon.pause:hover {
  background-image: url(./icon/start-h.png);
}
.ws-player .record-icon {
  background-image: url(./icon/RecordNormal.png);
}
.ws-player .record-icon:hover {
  background-image: url(./icon/RecordHover.png);
}
.ws-player .record-icon.recording {
  background-image: url(./icon/Recording.png);
  background-repeat: no-repeat;
  background-size: 20px 20px;
}


.ws-player .ws-control {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  width: 100%;
  background: #4C5054;
}
.ws-player .ws-record-control{
  height: 60px;
  cursor: pointer;
  background-image: linear-gradient(0deg, #445160 0%, #1c2834 100%);
}
.ws-player .ws-timeline{
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  z-index: 10;
}
.ws-player .ws-timeline-group{
  width: 100%;
  height: 20px;
  display: flex;
  justify-content: space-between;
}
.ws-player .ws-time-space{
  height: 10px;
  border-left: 1px solid #000;
}
.ws-player .ws-time-space-long{
  height: 20px;
}
.ws-player .ws-time-point{
  color: #CCC;
  width: 0;
  position: relative;
  left: -20px;
  top: 0;
}
.ws-player .ws-time-point:first-child{
  left: 0;
}
.ws-player .ws-time-point:last-child{
  left: -40px;
}
.ws-player #ws-cursor{
  height: 40px;
  border-left: 1px solid pink;
  position: absolute;
  top: 0;
  z-index: 20;
}
.ws-player .ws-cursor-time{
  position: relative;
}
.ws-player .ws-cursor-time span{
  position: absolute;
  top: 40px;
  left: -32px;
  color: #CCC;
}
.ws-player .ws-record-area{
  position: absolute;
  height: 100%;
}
.ws-player .ws-record-area-red{
  background-image: linear-gradient(0deg,
  rgba(251, 121, 101, 0.28) 0%,
  #b52c2c 100%);
  opacity: 0.8;
}
.ws-player .ws-record-area-blue{
  background-image: linear-gradient(0deg,
  rgba(77, 201, 233, 0.28) 0%,
  #1c79f4 100%);
  opacity: 0.8;
}
.ws-player .ws-ctrl-icon {
  width: 21px;
  height: 20px;
  margin: 0 10px;
  background-repeat: no-repeat;
  background-position: center;
}
.ws-player .full-screen-icon {
  background-image: url(./icon/fullscreen.svg);
}
.ws-player .full-screen-icon:hover {
  background-image: url(./icon/fullscreen-hover.svg);
}
.ws-player .one-screen-icon {
  background-image: url(./icon/tip_1-n.png);
}
.ws-player .one-screen-icon:hover {
  background-image: url(./icon/tip_1-h.png);
}
.ws-player .four-screen-icon {
  background-image: url(./icon/tip_4-n.png);
}
.ws-player .four-screen-icon.active {
  background-image: url(./icon/tip_4-p.png);
}
.ws-player .four-screen-icon:hover {
  background-image: url(./icon/tip_4-h.png);
}

.ws-player .flex {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ws-player .ws-ctrl-btn{
  border: 1px solid #000;
  background: #FFF;
  margin-right: 10px;
  padding: 0 10px;
  cursor: pointer;
}

/* spinner 动画 */
@keyframes spinner-line-fade-more {
  0%, 100% {
    opacity: 0; /* minimum opacity */
  }
  1% {
    opacity: 1;
  }
}

@keyframes spinner-line-fade-quick {
  0%, 39%, 100% {
    opacity: 0.25; /* minimum opacity */
  }
  40% {
    opacity: 1;
  }
}

@keyframes spinner-line-fade-default {
  0%, 100% {
    opacity: 0.22; /* minimum opacity */
  }
  1% {
    opacity: 1;
  }
}

@keyframes spinner-line-shrink {
  0%, 25%, 100% {
    /* minimum scale and opacity */
    transform: scale(0.5);
    opacity: 0.25;
  }
  26% {
    transform: scale(1);
    opacity: 1;
  }
}

.stream-type{
  display: flex;
  background: #FFF;
  color: #000;
  border-radius: 2px;
  font-size: 14px;
  cursor: pointer;
}

.stream-type .stream-type-select{
  background: #1A78EA;
  color: #FFF;
}

.stream-type .stream-type-item{
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #000;
}

.stream-type .stream-type-item:last-child{
  border-right: 0;
}
