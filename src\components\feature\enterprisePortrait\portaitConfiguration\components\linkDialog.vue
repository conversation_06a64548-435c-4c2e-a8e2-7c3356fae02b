<template>
  <el-dialog
    title="关联企业"
    :visible="visible"
    width="1060px"
    @close="closeBoolean"
    :destroy-on-close="true"
    :close-on-click-modal="false"
  >
    <div class="share-container">
      <div class="search-container">
        <el-input
          v-model="searchName"
          placeholder="请输入企业名称"
          clearable
          @clear="handleSearchClear"
          @input="handleSearchInput"
          prefix-icon="el-icon-search"
          n
          size="small"
          style="width: 250px; margin-bottom: 10px"
        ></el-input>
      </div>
      <el-transfer
        v-model="selectedList"
        :data="enterList"
        :titles="['企业列表', '选中企业']"
        :props="{
          key: 'enterpId',
          label: 'enterpName',
          disabled: 'isDisabled',
        }"
        @change="handleChange"
        :filter-method="filterMethod"
        filter-placeholder="请输入企业名称"
        style="width: 100%"
      ></el-transfer>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button type="primary" size="small" @click="handleSubmit"
        >确定</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import {
  getRelationList,
  addRelation,
  getPortraitInfoEnterpRelation,
} from "@/api/enterprisePortrait";

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    planId: {
      type: String,
      default: () => "",
    },
  },
  components: {},
  data() {
    return {
      enterList: [],
      selectedList: [],
      searchName: "",
      searchTimeout: null,
    };
  },
  computed: {},
  created() {
    this.getEntData();
    this.getRelationData();
  },
  methods: {
    async handleSubmit() {
      await addRelation({
        planId: this.planId,
        enterpIds: this.selectedList, //企业id
      }).then((res) => {
        if (res.data.status == 200) {
          this.$message.success("分享成功");
          this.closeBoolean(false);
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    //获取关联企业列表
    getRelationData() {
      const params = {
        id: this.planId,
      };
      getRelationList(params).then((res) => {
        if (res.data.status == 200) {
          this.selectedList = res.data.data.map((item) => item.enterpId);
          console.log(this.selectedList);
        }
      });
    },
    // 获取企业列表
    getEntData() {
      const params = {
        distCode: this.$store.state.login.userDistCode,
        current: 1,
        size: 999,
        enterpName: this.searchName || "",
      };
      getPortraitInfoEnterpRelation(params).then((res) => {
        if (res.data.code == 0) {
          // 处理企业列表，标记已关联其他plan的企业为不可选
          this.enterList = res.data.data.records.map((item) => {
            const isLinked = item.planId && item.planId !== this.planId;
            return {
              ...item,
              isDisabled: isLinked,
              // 为已关联企业添加标记信息
              enterpName: isLinked
                ? `${item.enterpName} (已关联其他方案)`
                : item.enterpName,
              originalName: item.enterpName, // 保存原始名称用于搜索
            };
          });
        }
      });
    },

    // 处理搜索输入
    handleSearchInput() {
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      this.searchTimeout = setTimeout(() => {
        this.getEntData();
      }, 300);
    },

    // 清空搜索
    handleSearchClear() {
      this.searchName = "";
      this.getEntData();
    },

    // 过滤方法
    filterMethod(query, item) {
      if (!query) return true;
      // 使用原始名称或当前显示名称进行搜索
      const searchText = (item.originalName || item.enterpName).toLowerCase();
      return searchText.indexOf(query.toLowerCase()) > -1;
    },

    // 处理选择变化
    handleChange(value, direction, movedKeys) {
      // 当用户尝试选择已禁用的企业时，可以在这里添加提示
      if (direction === "right") {
        const disabledItems = this.enterList.filter(
          (item) => item.isDisabled && movedKeys.includes(item.enterpId)
        );

        if (disabledItems.length > 0) {
          this.$message.warning("有企业已被其他方案关联，无法选择");
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding-top: 10px;
  padding-bottom: 15px;
}

.footer {
  text-align: center;
  padding-top: 20px;
}

.share-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
}

.search-container {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 10px;
}

::v-deep .el-transfer-panel {
  width: 400px;
}

::v-deep .el-checkbox-group.el-transfer-panel__list {
  height: 400px;
}

::v-deep .el-checkbox__input.is-disabled .el-checkbox__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  cursor: not-allowed;
}

::v-deep .el-checkbox__input.is-disabled + span.el-checkbox__label {
  color: #c0c4cc;
  cursor: not-allowed;
}
</style>
