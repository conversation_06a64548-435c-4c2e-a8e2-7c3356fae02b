<template>
  <div class="system" v-loading="loading">
    <div class="header">
      <el-cascader
        v-show="this.$store.state.login.user.user_type == 'gov'"
        size="mini"
        placeholder="请选择地市/区县"
        :options="district"
        v-model="districtVal"
        :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
        clearable
        @change="handleChange"
        :show-all-levels="true"
        style="width: 190px"
      ></el-cascader>
      <el-input
        placeholder="请输入企业名称"
        style="width: 190px"
        size="mini"
        clearable
        v-model.trim="enterpriseName"
      ></el-input>
      <el-select
        v-model="isOnline"
        placeholder="请选择是否在线"
        style="width: 190px"
        size="mini"
        clearable
        @change="isOnline === '1' ? (isReported = '1') : (isReported = '')"
      >
        <el-option
          v-for="(item, index) in options1"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="isReported"
        :placeholder="
          isOnline === '1' || isOnline === '' || isOnline === null  || isOnline === undefined? '请选择是否报备' : ''
        "
        style="width: 190px"
        size="mini"
        clearable
        :disabled="isOnline === '0'"
      >
        <el-option
          v-for="(item, index) in options2"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getData()">查询</el-button>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      style="width: 100%"
      :height="500"
    >
      <el-table-column type="index" width="80" label="序号"></el-table-column>
      <el-table-column
        prop="distName"
        label="区划"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        label="企业名称"
        width="270"
        show-overflow-tooltip
      >
      <template slot-scope="{row}">
        <span type="text" style="color: #3977ea; cursor: pointer;" @click="goEnt(row)">{{row.enterName}}</span>
      </template>
      </el-table-column>
      <el-table-column prop="address" label="是否在线" width="100">
        <template slot-scope="{ row }">
          <span>{{ row.isOnline == 0 ? "在线" : "离线" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="offlineStartTime" label="离线时间" width="160">
      </el-table-column>
      <el-table-column prop="offlineDuration" label="离线时长" width="150">
      </el-table-column>
      <el-table-column
        prop="plannedLaunchTime"
        label="计划上线时间"
        width="150"
      >
      </el-table-column>
      <el-table-column prop="offlineReason" label="离线原因"> </el-table-column>
      <el-table-column prop="address" label="附件" width="80">
        <template slot-scope="scope">
          <el-button
            type="text"
            :disabled="scope.row.enclosure ? false : true"
            @click="lookImg(scope.row.enclosure)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
    <LookImg ref="lookImg"></LookImg>
  </div>
</template>

<script>
import { getOnlinePatrolQuerySysDetails } from "@/api/riskAssessment";
import LookImg from "./lookImg";
export default {
  components: {
    LookImg,
  },
  data() {
    return {
      tableData: [],
      district: this.$store.state.controler.district,
      options1: [
        { label: "在线", value: "0" },
        { label: "离线", value: "1" },
      ],
      options2: [
        { label: "离线已报备", value: "1" },
        { label: "停产已报备", value: "3" },
        { label: "未报备", value: "2" },
      ],
      currentPage: 1,
      total: 0,
      enterpriseName: "",
      isOnline: "",
      isReported: "",
      districtVal: this.districtProps,
      loading: false,
    };
  },
  props: ["districtProps", "isOnlineProps", "isReportedProps"],
  methods: {
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterPid);
    },
    closeTable() {
      this.tableData = [];
      this.currentPage = 1;
      this.total = 0;
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.getData();
    },
    lookImg(img, type) {
      this.$refs.lookImg.closeBoolean(true);
      this.$refs.lookImg.getData(img, "0");
    },
    getData() {
      // console.log(this);
      this.loading = true;
      getOnlinePatrolQuerySysDetails({
        distParentCode: this.$store.state.login.userDistCode,
        current: this.currentPage,
        size: 10,
        distCode: this.districtProps,
        enterpriseName: this.enterpriseName,
        isOnline: this.isOnline,
        isReported: this.isReported,
      }).then((res) => {
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
  },
  //   computed:{
  //       ...mapStateLogin({
  //     userDistCode: (state) => state.userDistCode,
  //   }),
  // },
  watch: {
    districtProps: {
      handler(newVal, oldVal) {
        this.districtVal = newVal;
      },
      immediate: true,
      deep: true,
    },
    isOnlineProps: {
      handler(newVal, oldVal) {
        // console.log(newVal);
        this.isOnline = newVal;
      },
      immediate: true,
      deep: true,
    },
    isReportedProps: {
      handler(newVal, oldVal) {
        // console.log(newVal);
        this.isReported = newVal;
      },
      immediate: true,
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.system {
  .header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    & > * {
      margin-right: 20px;
    }
  }
  .pagination {
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>