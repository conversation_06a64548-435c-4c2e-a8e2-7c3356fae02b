{"presets": [["@babel/preset-env", {"modules": false, "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8", "ios >= 10", "android >= 6"]}}]], "plugins": ["dynamic-import-node", ["component", {"libraryName": "element-ui", "styleLibraryName": "theme-chalk"}], "transform-vue-jsx", ["@babel/plugin-transform-runtime", {"corejs": 2}], ["@babel/plugin-proposal-decorators", {"legacy": true}]], "env": {"development": {"plugins": ["dynamic-import-node"]}}}