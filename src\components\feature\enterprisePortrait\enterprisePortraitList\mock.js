export const mockData = {
    "code": 200,
    "msg": "ok",
    "data": {
        "id": "5d093886238a45bb9fbc5ef7d26fe6be",
        "uuitNo": "1803",
        "enterpriseName": "测试企业1803xjj",
        "reportTime": "2024-10-06 10:26:07",
        "aboveDesignated": "规下企业",
        "addr": "无锡市-惠山区-测试企业1803xjj",
        "legalPerson": "测试企业1803xjj",
        "phone": "***********",
        "indusType": "[制造业——C - 酒、饮料和精制茶制造业——15 - 酒的制造——151 - 酒精制造——1511, 采矿业——B - 非金属矿采选业——10 - 土砂石开采——101 - 石灰石、石膏开采——1011, 制造业——C - 医药制造业——27 - 化学药品原料药制造——271, 采矿业——B - 有色金属矿采选业——09 - 常用有色金属矿采选——091 - 镁矿采选——0917]",
        "businessScope": "测试企业1803xjj",
        "majorProduct": "测试企业1803xjj",
        "craftName": "[{\"nodes\":[{\"shape\":\"rect\",\"nodeType\":0,\"x\":325,\"y\":200,\"id\":\"0c5ed683\",\"label\":\"测试企业1803xjj\",\"account\":[]}],\"edges\":[],\"name\":\"测试企业1803xjj\",\"isCollapse\":false,\"id\":0}]",
        "totalFullMark": "102",
        "totalScore": "83",
        "manFullMark": "12",
        "manScore": "11",
        "thingsFullMark": "12",
        "thingsScore": "10",
        "managementFullMark": "32",
        "managementScore": "28",
        "environmentFullMark": "16",
        "environmentScore": "14",
        "supervisionFullMark": "17",
        "supervisionScore": "12",
        "performanceFullMark": "13",
        "performanceScore": "8",
        "isDelete": "0",
        "manDTO": {
            "id": "8de1d133f18d4301b546dbd67af4241e",
            "uuitNo": "1803",
            "staffNumFullMark": "2.5",
            "staffNumScore": "0.25",
            "staffNumDetails": "从业人员总数=50",
            "staffNumRule": "计算规则：20/100*人数",
            "personneldensityFullMark": "2.5",
            "personneldensityScore": "0",
            "personnelDensityDetails": "人员密集度=0",
            "personnelDensityRule": "计算规则：取值=0",
            "registeredEngineerFullMark": "8",
            "registeredEngineerScore": "0",
            "registeredEngineerDetails": "注册安全工程师比例=0%",
            "registeredEngineerRule": "计算规则：取值=0",
            "fullTimeStaffFullMark": "35",
            "fullTimeStaffScore": "10.5",
            "fullTimeStaffDetails": "专职安全管理人员比例=0%",
            "fullTimeStaffRule": "计算规则：（20/1*（1-实际比例*100）+80）*0.3",
            "partTimeStaffFullMark": "15",
            "partTimeStaffScore": "4.5",
            "partTimeStaffDetails": "兼职安全管理人员比例=0%",
            "partTimeStaffRule": "计算规则：（20/1*（1-实际比例*100）+80）*0.3",
            "employmentInjuryInsuranceFullMark": "6",
            "employmentInjuryInsuranceScore": "4.94",
            "employmentInjuryInsuranceDetails": "工伤保险人数比例=44%",
            "employmentInjuryInsuranceRule": "计算规则：20/50*（50-实际比例*100）+80",
            "externalPersonnelNumFullMark": "6",
            "externalPersonnelNumScore": "6",
            "externalPersonnelNumDetails": "外用工人员比例=20%",
            "externalPersonnelNumRule": "计算规则：取值=100",
            "specialWorkStaffFullMark": "10",
            "specialWorkStaffScore": "0",
            "specialWorkStaffDetails": "特种作业人数持证率=0%（非重点行业领域）",
            "specialWorkStaffRule": "计算规则：取值=0",
            "enterprisePrincipalFullMark": "10",
            "enterprisePrincipalScore": "3",
            "enterprisePrincipalDetails": "企业主要负责人安全生产资格证书=无(非重点行业领域)",
            "enterprisePrincipalRule": "计算规则：取值=30",
            "isSafetyDirectorFullMark": "20",
            "isSafetyDirectorScore": "0",
            "isSafetyDirectorDetails": "企业是否有安全总监=无（非重点行业领域）",
            "isSafetyDirectorRule": "计算规则：取值=0",
            "bkzbFullMark": "15",
            "bkzbScore": "11.7",
            "bkzbDetails": "本科及以上学历占比=11%",
            "bkzbRule": "计算规则：60/30*（40-实际占比*100）+20",
            "safetyManagerPrincipalFullMark": "19",
            "safetyManagerPrincipalScore": "19",
            "safetyManagerPrincipalDetails": "企业安全负责人安全生产资格证书=无",
            "safetyManagerPrincipalRule": "计算规则：取值=100",
            "isDelete": "0"
        },
        "managementDTO": {
            "id": "f320ac46de344dd094d0d10ad87ef44a",
            "uuitNo": "1803",
            "isDelete": "0",
            "standardizationConstructionFullMark": "20",
            "standardizationConstructionScore": "0",
            "standardizationConstructionDetails": "是否开展安全生产标准化建设=是",
            "standardizationConstructionRule": "计算规则：取值=0",
            "standardizationGradeFullMark": "38",
            "standardizationGradeScore": "38",
            "standardizationGradeDetails": "达标等级=未开展安全生产标准化建设",
            "standardizationGradeRule": "计算规则：取值=100",
            "managementOrgFullMark": "18",
            "managementOrgScore": "0",
            "managementOrgDetails": "安全管理部门名称=测试企业1803xjj",
            "managementOrgRule": "计算规则：取值=0",
            "employmentInjuryInsuranceFullMark": "16",
            "employmentInjuryInsuranceScore": "1.6",
            "employmentInjuryInsuranceDetails": "是否投保安全生产责任保险=否",
            "employmentInjuryInsuranceRule": "计算规则：取值=10",
            "perCapitaInvestmentFullMark": "75",
            "perCapitaInvestmentScore": "0",
            "perCapitaInvestmentDetails": "人均安全生产投入费用=24000000 元",
            "perCapitaInvestmentRule": "计算规则：取值=0",
            "perCapitaOutputFullMark": "19",
            "perCapitaOutputScore": "0",
            "perCapitaOutputDetails": "人均产值=700000000 元",
            "perCapitaOutputRule": "计算规则：取值=0",
            "enterpriseInspectionFullMark": "45",
            "enterpriseInspectionScore": "45",
            "enterpriseInspectionDetails": "季度检查次数=0",
            "enterpriseInspectionRule": "计算规则：取值=100",
            "reportMessageFullMark": "21",
            "reportMessageScore": "21",
            "reportMessageDetails": "上报信息是否完整=不完整",
            "reportMessageRule": "计算规则：取值=100",
            "noticeBoardFullMark": "11",
            "noticeBoardScore": "11",
            "noticeBoardDeteils": "公告栏生成=未生成",
            "noticeBoardRule": "计算规则：取值=100",
            "warningBoardFullMark": "9",
            "warningBoardScore": "9",
            "warningBoardDeteils": "警示牌生成=未生成",
            "warningBoardRule": "计算规则：取值=100",
            "fourColorFigureFullMark": "18",
            "fourColorFigureScore": "18",
            "fourColorFigureDeteils": "企业风险四色图生成=未生成",
            "fourColorFigureRule": "计算规则：取值=100"
        },
        "environmentDTO": {
            "id": "433959e5a3a84cd6be3dd23274b05d21",
            "uuitNo": "1803",
            "isDelete": "0",
            "keyIndustriesFullMark": "285",
            "keyIndustriesScore": "285",
            "keyIndustriesDetails": "高危的企业或粉尘涉爆企业中的铝镁金属粉尘或单班作业30人以上的其他粉尘企业",
            "keyIndustriesRule": "计算规则：取值=100",
            "commonLargerRiskNumFullMark": "9",
            "commonLargerRiskNumScore": "0",
            "commonLargerRiskNumDetails": "辨识较大风险数量（一般）=0",
            "commonLargerRiskNumRule": "计算规则：20/5*实际风险数量",
            "emphasisLargerRiskNumFullMark": "13",
            "emphasisLargerRiskNumScore": "2.08",
            "emphasisLargerRiskNumDetails": "辨识较大风险数量（重点）=4",
            "emphasisLargerRiskNumRule": "计算规则：20/5*实际风险数量",
            "greatRiskNumFullMark": "14",
            "greatRiskNumScore": "2.8",
            "greatRiskNumDetails": "辨识重大风险数量=1",
            "greatRiskNumRule": "计算规则：取值=20",
            "notReportLargerRiskFullMark": "33",
            "notReportLargerRiskScore": "0",
            "notReportLargerRiskDetails": "不属于重点行业领域，上报风险条数=4",
            "notReportLargerRiskRule": "计算规则：取值=0",
            "reportToPunishedFullMark": "80",
            "reportToPunishedScore": "0",
            "reportToPunishedDetails": "是否未按照140令如实报告受到处罚=否",
            "reportToPunishedRule": "计算规则：取值=0"
        },
        "supervisionDTO": {
            "id": "48a113365fb64c6eb0de2d03bcf99b3e",
            "uuitNo": "1803",
            "isDelete": "0",
            "generalHazardFullMark": "7",
            "generalHazardScore": "3.5",
            "generalHazardDetails": "平均每次检查的一般隐患数量=未开展执法检查的企业",
            "generalHazardRule": "计算规则：取值=50",
            "greatHazardFullMark": "17",
            "greatHazardScore": "8.5",
            "greatHazardDetails": "平均每次检查的重大隐患数量=未开展执法检查的企业",
            "greatHazardRule": "计算规则：取值=50",
            "penaltyAmountFullMark": "3",
            "penaltyAmountScore": "0.6",
            "penaltyAmountDetails": "近12个月内被处罚金额=未开展执法检查的企业",
            "penaltyAmountRule": "计算规则：取值=20",
            "initiateThePunishmentFullMark": "5",
            "initiateThePunishmentScore": "1.5",
            "initiateThePunishmentDetails": "近12个月内被立案处罚=未开展执法检查的企业",
            "initiateThePunishmentRule": "计算规则：取值=30",
            "punishedJointlyFullMark": "4",
            "punishedJointlyScore": "0",
            "punishedJointlyDetails": "是否被被联合惩戒=否",
            "punishedJointlyRule": "计算规则：取值=0",
            "enterTheBlacklistFullMark": "7",
            "enterTheBlacklistScore": "0",
            "enterTheBlacklistDetails": "是否进入黑名单=否",
            "enterTheBlacklistRule": "计算规则：取值=0",
            "executionCohesionFullMark": "3",
            "executionCohesionScore": "0",
            "executionCohesionDetails": "是否行刑衔接=否",
            "executionCohesionRule": "计算规则：取值=0",
            "administrativeArbitrarinessFullMark": "2",
            "administrativeArbitrarinessScore": "0",
            "administrativeArbitrarinessDetails": "行政强制=否",
            "administrativeArbitrarinessRule": "计算规则：取值=0"
        },
        "performanceDTO": {
            "id": "ff6d96c9ddd245b5bc55185b0cd5e280",
            "uuitNo": "1803",
            "isDelete": "0",
            "accidentInformationFullMark": "70",
            "accidentInformationScore": "0",
            "accidentInformationDetails": "事故信息情况=近三年未发生事故",
            "accidentInformationRule": "计算规则：取值=0",
            "eventInformationFullMark": "9",
            "eventInformationScore": "0",
            "eventInformationDetails": "事件信息=上一年没有工伤保险赔付记录",
            "eventInformationRule": "计算规则：取值=0"
        },
        "province": "320000",
        "provinceName": "江苏省",
        "city": "320600",
        "cityName": "南通市",
        "county": "320612",
        "countyName": "通州区",
        "villages": "320612002",
        "villagesName": "兴东街道",
        "supervisionLarge": "B,A",
        "supervisionName": "矿山企业,化工医药企业"
    },
    "susscess": true
}