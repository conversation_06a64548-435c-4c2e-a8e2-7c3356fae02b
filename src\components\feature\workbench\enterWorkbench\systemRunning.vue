<template>
  <div>
    <div class="work-heard">
      <h2>系统运行状态</h2>
      <span @click="goEnt">更多</span>
    </div>
    <div class="running-list">
      <div class="wulian-item" v-loading="loading1">
        <h2>物联监测统计</h2>
        <div class="wulian-main">
          <div class="wulian-left">
            <div class="percentloop">
              <div class="circle-left">
                <div ref="leftcontent"></div>
              </div>
              <div class="circle-right">
                <div ref="rightcontent"></div>
              </div>
              <div class="number">{{ percent + "%" }}</div>
            </div>
            <p>系统在线率</p>
          </div>
          <div class="wulian-right">
            <div>
              <p>
                报警次数
                <span
                  v-if="
                    !companyAlarminfoData.alarmNum ||
                    companyAlarminfoData.alarmNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span @click="openDialog(enterpId, '0')" v-else>{{
                  companyAlarminfoData.alarmNum
                }}</span>
              </p>
              <p>
                接入指标
                <span
                  v-if="
                    !companyAlarminfoData.linkedNum ||
                    companyAlarminfoData.linkedNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span v-else @click="clickZhiBiao(enterpId, '')">{{
                  companyAlarminfoData.linkedNum
                }}</span>
              </p>
            </div>
            <div>
              <p>
                未消警次数
                <span
                  v-if="
                    !companyAlarminfoData.notClearedNum ||
                    companyAlarminfoData.notClearedNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span @click="openDialog(enterpId, '1')" v-else>{{
                  companyAlarminfoData.notClearedNum
                }}</span>
              </p>
              <p>
                离线指标
                <span
                  v-if="
                    !companyAlarminfoData.offLineNum ||
                    companyAlarminfoData.offLineNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span @click="clickZhiBiao(enterpId, '1')" v-else>{{
                  companyAlarminfoData.offLineNum
                }}</span>
              </p>
            </div>
            <div>
              <p>
                消警率
                <span style="text-decoration: auto"
                  >{{ companyAlarminfoData.clearedAlarmRate }}%</span
                >
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="video-item" v-loading="loading2">
        <h2>视频监测统计</h2>
        <div class="wulian-main">
          <div class="wulian-left">
            <div class="percentloop">
              <div class="circle-left">
                <div ref="leftcontents"></div>
              </div>
              <div class="circle-right">
                <div ref="rightcontents"></div>
              </div>
              <div class="number">{{ percents + "%" }}</div>
            </div>
            <p>视频在线率</p>
          </div>
          <div class="wulian-right">
            <div>
              <p>
                报警次数
                <span
                  v-if="
                    !companyVideoinfoData.alarmNum ||
                    companyVideoinfoData.alarmNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span v-else>{{ companyVideoinfoData.alarmNum }}</span>
              </p>
              <p>
                接入点位
                <span
                  v-if="
                    !companyVideoinfoData.linkedNum ||
                    companyVideoinfoData.linkedNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span @click="clickDainWei(enterpId, '')" v-else>{{
                  companyVideoinfoData.linkedNum
                }}</span>
              </p>
            </div>
            <div>
              <p>
                未消警次数
                <span
                  v-if="
                    !companyVideoinfoData.notClearedNum ||
                    companyVideoinfoData.notClearedNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span v-else>{{ companyVideoinfoData.notClearedNum }}</span>
              </p>
              <p>
                在线点位
                <span
                  v-if="
                    !companyVideoinfoData.onLineNum ||
                    companyVideoinfoData.onLineNum == 0
                  "
                  style="text-decoration: auto"
                  >0</span
                ><span @click="clickDainWei(enterpId, '1')" v-else>{{
                  companyVideoinfoData.onLineNum
                }}</span>
              </p>
            </div>
            <div>
              <p>
                消警率
                <span style="text-decoration: auto"
                  >{{
                    companyVideoinfoData.clearedAlarmRate
                      ? companyVideoinfoData.clearedAlarmRate
                      : 0
                  }}%</span
                >
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <AlarmNum ref="alarmNum"></AlarmNum>
    <ZhiBiaoNum ref="zhiBiaoNum"></ZhiBiaoNum>
    <DianWeiNum ref="dianWeiNum"></DianWeiNum>
  </div>
</template>
<script>
import { getCompanyAlarminfo, getCompanyVideoInfo } from "@/api/workbench";
import { parseTime } from "@/utils/index";
import AlarmNum from "./alarmNum";
import ZhiBiaoNum from "./zhiBiaoNum";
import DianWeiNum from "./dianWeiNum";
export default {
  components: {
    AlarmNum,
    ZhiBiaoNum,
    DianWeiNum,
  },
  data() {
    return {
      loading1: true,
      percent: 0,
      initDeg: 0,
      timeId: null,
      animationing: false,
      speed: 1,
      percentNum: 0,
      companyAlarminfoData: {},

      loading2: true,
      percents: 0,
      initDegs: 0,
      timeIds: null,
      animationings: false,
      speeds: 1,
      percentNums: 0,
      companyVideoinfoData: {},
      enterpId: "",
    };
  },
  methods: {
    getData(id) {
      this.enterpId = id;
      getCompanyAlarminfo({
        companyCode: id,
        startDate: parseTime(
          new Date(new Date(new Date().toLocaleDateString()).getTime()),
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
        endDate: parseTime(
          new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
              24 * 60 * 60 * 1000 -
              1
          ),
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading2 = false;
          this.percentNum = Number(res.data.data.onlineQuality);
          this.companyAlarminfoData = res.data.data;
        }
      });

      getCompanyVideoInfo({
        companyCode: id,
        startDate: parseTime(
          new Date(new Date(new Date().toLocaleDateString()).getTime()),
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
        endDate: parseTime(
          new Date(
            new Date(new Date().toLocaleDateString()).getTime() +
              24 * 60 * 60 * 1000 -
              1
          ),
          "{y}-{m}-{d} {h}:{i}:{s}"
        ),
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading1 = false;
          this.percentNums = Number(res.data.data.onlineQuality);
          this.companyVideoinfoData = res.data.data;
        }
      });
    },
    transformToDeg(percent) {
      let deg = 0;
      if (percent >= 100) {
        deg = 360;
      } else {
        deg = parseInt((360 * percent) / 100);
      }
      return deg;
    },
    transformToPercent(deg) {
      let percent = 0;
      if (deg >= 360) {
        percent = 100;
      } else {
        percent = parseInt((100 * deg) / 360);
      }
      return percent;
    },
    rotateLeft(deg) {
      // 大于180时，执行的动画
      this.$nextTick(() => {
        this.$refs.leftcontent.style.transform =
          "rotate(" + (deg - 180) + "deg)";
      });
    },
    rotateRight(deg) {
      // 小于180时，执行的动画
      this.$nextTick(() => {
        this.$refs.rightcontent.style.transform = "rotate(" + deg + "deg)";
      });
    },
    rotateLefts(deg) {
      // 大于180时，执行的动画
      this.$nextTick(() => {
        this.$refs.leftcontents.style.transform =
          "rotate(" + (deg - 180) + "deg)";
      });
    },

    rotateRights(deg) {
      // 小于180时，执行的动画
      this.$nextTick(() => {
        this.$refs.rightcontents.style.transform = "rotate(" + deg + "deg)";
      });
    },
    goRotate(deg) {
      this.animationing = true;
      this.timeId = setInterval(() => {
        if (deg > this.initDeg) {
          // 递增动画
          this.initDeg += Number(this.speed);
          if (this.initDeg >= 180) {
            this.rotateLeft(this.initDeg);
            this.rotateRight(180); // 为避免前后两次传入的百分比转换为度数后的值不为步距的整数，可能出现的左右转动不到位的情况。
          } else {
            this.rotateRight(this.initDeg);
          }
        } else {
          // 递减动画
          this.initDeg -= Number(this.speed);
          if (this.initDeg >= 180) {
            this.rotateLeft(this.initDeg);
          } else {
            this.rotateLeft(180); // 为避免前后两次传入的百分比转换为度数后的值不为步距的整数，可能出现的左右转动不到位的情况。
            this.rotateRight(this.initDeg);
          }
        }
        this.percent = this.transformToPercent(this.initDeg); // 百分比数据滚动动画
        const remainer = Number(deg) - this.initDeg;
        if (Math.abs(remainer) < this.speed) {
          this.initDeg += remainer;
          if (this.initDeg > 180) {
            this.rotateLeft(deg);
          } else {
            this.rotateRight(deg);
          }
          this.animationFinished();
        }
      }, 10);
    },
    goRotates(deg) {
      this.animationings = true;
      this.timeIds = setInterval(() => {
        if (deg > this.initDegs) {
          // 递增动画
          this.initDegs += Number(this.speeds);
          if (this.initDegs >= 180) {
            this.rotateLefts(this.initDegs);
            this.rotateRights(180); // 为避免前后两次传入的百分比转换为度数后的值不为步距的整数，可能出现的左右转动不到位的情况。
          } else {
            this.rotateRights(this.initDegs);
          }
        } else {
          // 递减动画
          this.initDegs -= Number(this.speeds);
          if (this.initDegs >= 180) {
            this.rotateLefts(this.initDegs);
          } else {
            this.rotateLefts(180); // 为避免前后两次传入的百分比转换为度数后的值不为步距的整数，可能出现的左右转动不到位的情况。
            this.rotateRights(this.initDegs);
          }
        }
        this.percents = this.transformToPercent(this.initDegs); // 百分比数据滚动动画
        const remainer = Number(deg) - this.initDegs;
        if (Math.abs(remainer) < this.speeds) {
          this.initDegs += remainer;
          if (this.initDegs > 180) {
            this.rotateLefts(deg);
          } else {
            this.rotateRights(deg);
          }
          this.animationFinisheds();
        }
      }, 10);
    },
    animationFinished() {
      this.percent = this.percentNum; // 百分比数据滚动动画
      this.animationing = false;
      clearInterval(this.timeId);
      this.$emit("animationFinished"); // 动画完成的回调
    },
    animationFinisheds() {
      this.percents = this.percentNums; // 百分比数据滚动动画
      this.animationings = false;
      clearInterval(this.timeIds);
      this.$emit("animationFinisheds"); // 动画完成的回调
    },
    openDialog(enterpId, type) {
      this.$refs.alarmNum.closeBoolean(true);
      this.$refs.alarmNum.getData(enterpId, type);
    },
    clickDainWei(enterpId, type) {
      this.$refs.dianWeiNum.closeBoolean(true);
      this.$refs.dianWeiNum.getData(enterpId, type);
    },
    clickZhiBiao(enterpId, type) {
      this.$refs.zhiBiaoNum.closeBoolean(true);
      this.$refs.zhiBiaoNum.getData(enterpId, type);
    },
    goEnt() {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("login/updataActiveName", 'monitoringEquipment');
      this.$store.commit("controler/updateEntId", this.enterpId);
      this.$store.commit("controler/updateEntModelName", "realTimeMonitoring");
    },
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {
    this.goRotate(this.transformToDeg(this.percentNum));
    this.goRotates(this.transformToDeg(this.percentNums));
  },
  watch: {
    percentNum: function (val) {
      if (this.animationing) return;
      this.goRotate(this.transformToDeg(val));
    },
    percentNums: function (val) {
      if (this.animationings) return;
      this.goRotates(this.transformToDeg(val));
    },
  },
};
</script>
<style lang="scss" scoped>
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
    cursor: pointer;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.running-list {
  width: 92%;
  margin: 0 auto;
  > div {
    width: 100%;
    height: 170px;
    background: url("/static/img/assets/img/runningBg.png") center center
      no-repeat;
    background-size: 100% 100%;
    margin-top: 5px;
  }
  .wulian-item,
  .video-item {
    position: relative;
    h2 {
      position: absolute;
      font-size: 16px;
      color: #fff;
      top: 6%;
      left: 7%;
    }
    .wulian-main {
      .wulian-left {
        width: 40%;
        float: left;
        height: 90px;
        margin-top: 60px;
        // padding: 15px 0;
        border-right: 1px solid #cce7fe;
        .percentloop {
          position: relative;
          width: 70px;
          height: 70px;
          border-radius: 50%;
          overflow: hidden;
          margin: 0 auto;
          .circle-left,
          .circle-right {
            position: absolute;
            top: 0;
            left: 0;
            width: 50%;
            height: 100%;
            background-color: #9cc6fa;
            overflow: hidden;
            & > div {
              width: 100%;
              height: 100%;
              background-color: #c8e7ff;
              transform-origin: right center;
              /*transition: all .5s linear;*/
            }
          }
          .circle-right {
            left: 50%;
            & > div {
              transform-origin: left center;
            }
          }
          .number {
            position: absolute;
            top: 9%;
            bottom: 9%;
            left: 9%;
            right: 9%;
            background-color: #edf7ff;
            border-radius: 50%;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
          }
        }
        p {
          font-size: 16px;
          color: #545c65;
          text-align: center;
          margin-top: 5px;
        }
      }
      .wulian-right {
        width: calc(60% - 21px);
        float: left;
        height: 90px;
        margin-top: 60px;
        margin-left: 20px;
        // padding: 15px 0;
        // border-left: 1px solid #CCE7FE;
        div {
          p {
            display: inline-block;
            width: 49%;
            font-size: 12px;
            span {
              display: inline-block;
              margin-left: 5px;
              font-size: 16px;
              color: #545c65;
              font-weight: bold;
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
}
</style>
