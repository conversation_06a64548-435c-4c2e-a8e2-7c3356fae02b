<template>
    <div class="index-card">
        <div class="card-head flex-sb">
            <el-tooltip class="left" :content="item.subRuleName" placement="top-start" effect="dark">
                <span>{{ item.subRuleName }}</span>
            </el-tooltip>
            <div class="right">{{ item.score }}分</div>
        </div>
        <div class="index-card-content">
            <div class="card-body flex-sb">
                <div class="card-body-left">权重</div>
                <el-progress class="progress"  :percentage="Math.floor(item.weight)"></el-progress>
            </div>
            <div class="card-body flex-sb">
                <div class="card-body-left">系数</div>
                <div class="card-body-right">{{ item.coefficient }}</div>
            </div>
            <div class="card-body flex-sb">
                <div class="card-body-left">状态</div>
                <div :class="item.switchFlag == '1' ? 'card-status' : 'default-status'">{{ item.switchFlag == '1' ? '开启' : '关闭'
                    }}</div>
            </div>
        </div>
        <div class="active-box">
            <div class="active-item" @click="handleDelete">删除</div>
            <div class="active-item" @click="handleEdit">编辑</div>
            <div class="active-item" @click="handleStart">{{item.switchFlag == '0' ? '开启' : '关闭'}}</div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        item: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            config: {
                name: '从业人员',
                value: '29.8',
                progeress: '10.5',
                status: '1',
                coefficient: '0.022',

            }
        }
    },
    methods:{
        handleDelete() {
            this.$emit('delete')
        },
        handleEdit() {
            this.$emit('edit')
        },
        handleStart() {
            this.$emit('start')
        }
    }
}
</script>
<style lang="scss" scoped>
.index-card {
    position: relative;
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #E7EFFB;
    margin: 0 auto;
    padding: 10px;
    margin-bottom: 10px;

    .card-head {
        height: 28px;
        font-family: PingFang SC;
        font-weight: 800;
        font-size: 18px;
        color: #333333;

        .left {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .card-body {
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 16px;
        color: #666666;
        height: 28px;
        margin-top: 8px;

        .progress {
            flex: 1;
            margin-left: 16px;
            margin-right: -10px;
        }

        .card-status {
            color: #27C68F;
            font-size: 15px;
        }

        .default-status {
            font-size: 15px;
            color: #999999;
        }
    }

    .active-box {
        display: none;
    }

    &:hover {
        border: 1px solid #196DCA;
        .active-box {
            display: block;
            position: absolute;
            left: 0;
            bottom: 0;
            width: 100%;
            border-radius: 4px;
            color: #FFFFFF;
            display: flex;
            align-items: center;

            .active-item {
                flex: 1;
                height: 40px;
                line-height: 40px;
                background: rgba(25, 109, 202, 0.9);
                text-align: center;
                cursor: pointer;

                &:hover {
                    background: rgba(25, 109, 202, 0.8);
                }
            }
        }
    }
}

.flex-sb {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
}
</style>