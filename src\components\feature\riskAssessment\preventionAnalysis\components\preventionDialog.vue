<template>
  <el-dialog
    title="隐患详情"
    :visible.sync="visible"
    width="1150px"
    @close="closeBoolean()"
    :close-on-click-modal="false"
    append-to-body
  >
    <div class="chemicals" v-loading="loading">
      <div class="div1">
        <div class="table">
          <ul class="container">
            <li >
              <div class="l">隐患名称</div>
              <div class="r">{{ warinInfo.yhmc }}</div>
            </li>
            <li class="liLine">
              <div class="l">隐患类别</div>
              <div class="r">{{ warinInfo.yhlb| categoryFilter }}</div>
            </li>
            <li >
              <div class="l">企业名称</div>
              <div class="r">{{ warinInfo.qymc }}</div>
            </li>
            <li class="liLine">
              <div class="l">企业编码</div>
              <div class="r">{{ warinInfo.qybm }}</div>
            </li>
          
            <li >
              <div class="l">风险对象分析</div>
              <div class="r">{{ warinInfo.yyfx }}</div>
            </li>
            <li class="liLine">
              <div class="l">隐患来源</div>
              <div class="r">{{ warinInfo.yhly | sourceFilter }}</div>
            </li>
            <li>
              <div class="l">隐患等级</div>
              <div class="r">{{ warinInfo.yhdj | levelFilter }}</div>
            </li>
            <li class="liLine">
              <div class="l">治理类型</div>
              <div class="r" v-if="warinInfo.zllx == 0">即查即改</div>
              <div class="r" v-else-if="warinInfo.zllx == 1">限期整改</div>
             
            </li>
            <li >
              <div class="l"> 登记时间</div>
              <div class="r">{{ warinInfo.djsj }}</div>
            </li>
            <li class="liLine">
              <div class="l">整改负责人</div>
              <div class="r">
                {{ warinInfo.zgzrr }}
              </div>
            </li>
            <li>
              <div class="l">隐患状态</div>
              <div class="r">{{ warinInfo.yhzt | statusLabel }}</div>
            </li>
            <li class="liLine">
              <div class="l">隐患治理期限</div>
              <div class="r">
                {{ warinInfo.yhzlqx }}
              </div>
            </li>
            <li class="bottom lang">
              <div class="l">隐患类型</div>
              <div class="r">
                {{ warinInfo.yhlx| typeFilter}}
              </div>
            </li>
      
            <!-- <li class="bottom">
              <div class="l">年生产能力(标方)</div>
              <div class="r">{{ warinInfo.annualProductionCapacity }}</div>
            </li>
            <li class="bottom">
              <div class="l">* 设计储量(标方)</div>
              <div class="r">{{ warinInfo.storageNum }}</div>
            </li>
            <li class="liLine bottom">
              <div class="l">年使用量(标方)</div>
              <div class="r">{{ warinInfo.annualUsageMeal }}</div>
            </li> -->
          </ul>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  dangerOptions,
  statusOptions,
  typeOptions,
  levelOptions,
  categoryOptions,
  governanceOptions,
} from "../const";

export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  props: {
    warinInfo: {
      type: Object,
      default: () => {},
    },
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return { show: false, table: {}, loading: false };
  },
  filters: {
    statusLabel(value) {
      const item = statusOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    levelFilter(value) {
      const item = levelOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    typeFilter(value) {
      const item = typeOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    sourceFilter(value) {
      const item = dangerOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
    categoryFilter(value) {
      const item = categoryOptions.find((item) => item.value == value);
      return item ? item.label : value;
    },
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.chemicals {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    height: 400px;
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 49.8%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 99.6%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 24.9%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 74%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: center;
          }
        }
        .liLine {
          list-style-type: none;
          width: 49.8%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
