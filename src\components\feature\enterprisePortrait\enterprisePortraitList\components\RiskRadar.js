export default function getRistRenderOption(opts,indicatorMaxData = null) {
  // 增加缩放因子到1.4倍
  const scaleFactor = 1.4;
  const scaledMaxData = indicatorMaxData ? indicatorMaxData.map(max => max * scaleFactor) : null;
  
  const option = {
    title:{
    },
    radar: {
      center:['50%','50%'],
      // 减小雷达图半径，给数据留出更多空间
      radius:'75%',
      startAngle: 180,
      name: { // 指示器
        textStyle: {
            fontSize: 15,
            color: '#b0b0b0'
        },
        formatter:(value,indicator)=>{
          // return value 
        },
      },
      // 增加分割线数量，使显示更精细
      splitNumber: 6,
      // 添加轴线样式
      axisLine: {
        lineStyle: {
          color: '#ccc',
          width: 1
        }
      },
      // 添加分割线样式
      splitLine: {
        lineStyle: {
          color: '#ccc',
          width: 1
        }
      },
      splitArea: {
        show: false,
        areaStyle: { color: '#fff' }
      },  
      indicator: [
        { name: '人Man', max: scaledMaxData[0] },
        { name: '物(机、料)', max: scaledMaxData[1] },
        { name: '管(法)', max: scaledMaxData[2] },
        { name: '环(境)', max: scaledMaxData[3] },
        { name: '监(监管执法)', max: scaledMaxData[4] },
        { name: '绩(安全绩效)', max: scaledMaxData[5] },
      ]
    },
    series: [
      {
        type: 'radar',
        symbol:'none',
        // 添加数据标签配置
        label: {
          show: false
        },
        // 添加区域样式
        areaStyle: {
          opacity: 0.1
        },
        // 添加线条样式
        lineStyle: {
          width: 2
        },
        data: opts
      },
    ],
    // 添加动画配置
    animation: true,
    animationDuration: 1000,
    animationEasing: 'cubicInOut'
  };
  return option
}