<template>
  <el-dialog title="查看答题记录" :visible="visible" @close="closeBoolean(false)" width="750px" top="5vh"
    :close-on-click-modal="true">
    <div class="examination-dialog">
      <div class="score-box" v-if="answerRecord.singleMap.length>0">
        <div class="dialog-title">单选题</div>
        <el-tag class="score-item" size="small" :type="item.selectedAnswer==item.answer ? 'success' : 'danger'"
          v-for="(item, index) in answerRecord.singleMap" :key="index">{{ item.selectedAnswer }}</el-tag>
          <span class="judge-box">得分 {{ answerRecord.record.singleCount }} 分</span>
      </div>
      <div class="examination-item" v-for="(item,index) in answerRecord.singleMap" :key="item.id">
        <div class="question-box">
          <div class="question-item">
            <span class="index">{{ index+1 }}.</span> {{ item.content }}
          </div>
          <div class="question-item"> {{ item.options }}</div>
          <div class="question-item">
            <span class="index">考生答案：{{ item.selectedAnswer }}</span>
            <span class="judge">考题判定：{{ item.judge }}</span>
          </div>
          <div class="question-item"> <span class="index">答案：{{ item.answer }}</span></div>
          <div class="question-item"><span class="index">解析：{{ item.analysis }}</span>
          </div>
        </div>
      </div>
      <div class="score-box" v-if="answerRecord.multipleMap.length>0">
        <div class="dialog-title">多选题</div>
        <el-tag class="score-item" size="small" :type="item.selectedAnswer==item.answer ? 'success' : 'danger'"
          v-for="(item, index) in answerRecord.multipleMap" :key="index">{{ item.selectedAnswer }}</el-tag>
        <span class="judge-box">得分 {{ answerRecord.record.multipleCount }} 分</span>
      </div>
      <div class="examination-item" v-for="(item,index) in answerRecord.multipleMap" :key="item.id">
        <div class="question-box">
          <div class="question-item">
            <span class="index">{{ index+1 }}.</span> {{ item.content }}
          </div>
          <div class="question-item"> {{ item.options }}</div>
          <div class="question-item">
            <span class="index">考生答案：{{ item.selectedAnswer }}</span>
            <span class="judge">考题判定：{{ item.judge }}</span>
          </div>
          <div class="question-item"> <span class="index">答案：{{ item.answer }}</span></div>
          <div class="question-item"><span class="index">解析：{{ item.analysis }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="footer" slot="footer">
      <!-- <el-button size="small" @click="closeBoolean">取消</el-button> -->
      <el-button type="primary" size="small" @click="closeBoolean">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { viewAnswerRecord } from "@/api/enterpEducation";
export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    educationItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      answerRecord: {
        record: {},
        multipleMap: [],
        singleMap: [],
      },
    };
  },
  created() {
    this.getRecordById();
  },
  methods: {
    async getRecordById() {
      await viewAnswerRecord(this.educationItem.id).then((res) => {
        if (res.data.status === 200) {
          this.answerRecord = res.data.data;
        }
      });
    },
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.examination-dialog {
  height: 400px;
  overflow: auto;
  padding-right: 10px;
  box-sizing: border-box;

  .dialog-title {
    font-weight: 550;
    margin-bottom: 10px;
  }

  .examination-item {
    box-sizing: border-box;
    padding-left: 20px;
    .question-box {
      margin-bottom: 10px;
      .question-item{
        line-height: 30px;
        .index{
          font-weight: 550;
        }
        .judge {
          font-weight: 550;
          margin-left: 50px;
        }
      }
    }

  }

  .score-box {
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;

    .score-item {
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .judge-box{
      margin-left: 50px;
    }
  }
}

.footer {
  text-align: center;
}
</style>
