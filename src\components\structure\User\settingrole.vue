<template>
  <div class="table">
    <el-dialog
      title="角色配置"
      :visible.sync="edUserVisible"
      :modal="true"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      width="600px"
    >
      <div class="container">
        <a-tree
          v-model="checkedKeys"
          checkable
          :tree-data="treeData"
          @select="onSelects"
          @check="onChecks"
        />
        <el-button
          size="default"
          :loading="submitting"
          type="primary"
          class="commit"
          @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getStructureTree, getRoleIds, saveRoleIds } from "@/api/user";
export default {
  //import引入的组件
  props: {
    srVisible: {
      type: Boolean,
      default: false,
    },
    rowData: {
      type: Object,
    },
    orgCode: {
      type: String,
    },
  },
  computed: {},
  data() {
    return {
      edUserVisible: false,
      submitting: false,
      selectedRoleIds: "",
      allTreeData: [],
      treeData: [],
      checkedKeys: [],
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.edUserVisible = val;
    },
    getRowData() {
      this.ruleForm = this.rowData;
    },
    onSelects(selectedKeys, info) {},
    onChecks(checkedKeys, info) {
      if (checkedKeys) {
        this.selectedRoleIds = checkedKeys.join(",");
      } else {
        this.selectedRoleIds = "";
      }
    },
    getStructureTreeData() {
      getStructureTree()
        .then((data) => {
          if (data.data.code == 0) {
            this.treeData = data.data.data;
            this.treeData.forEach((item) => {
              item.key = item.id;
              item.title = item.systemName;
              if (item.children.length > 0) {
                item.children.forEach((items) => {
                  items.key = items.id;
                  items.title = items.name;
                  if (items.children.length > 0) {
                    items.children.forEach((itemed) => {
                      itemed.key = itemed.id;
                      itemed.title = itemed.name;
                    });
                  }
                });
              }
            });
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    getRoleIdsData(id) {
      getRoleIds({ id: id }).then((data) => {
        if (data.data.code == 0) {
          this.checkedKeys = data.data.data;
        }
      });
    },
    submitForm() {
      this.submitting = true;
      saveRoleIds({
        userId: this.rowData.userId,
        roleIds: this.selectedRoleIds,
      })
        .then((data) => {
          if (data.data.code == 0) {
            this.submitting = false;
            this.$message({
              type: "success",
              message: data.data.msg,
            });
            this.edUserVisible = false;
            this.$emit("sr-callback");
          } else {
            this.submitting = false;
            this.$message({
              type: "info",
              message: "success",
            });
          }
        })
        .catch((e) => {
          this.submitting = false;
          this.$message({
            type: "info",
            message: "接口报错",
          });
        });
    },
    /**
     * 点击关闭图标或者遮罩回调
     * @param done
     */
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(() => {
          done();
        })
        .catch(() => {});
    },
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getStructureTreeData();
    //   this.getRoleIdsData();
  },
  watch: {
    rowData() {
      this.getRowData();
    },
  },
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    .input {
      width: 60%;
    }
    .el-select,
    .radio-box {
      width: 60%;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .el-input {
    width: 80%;
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
