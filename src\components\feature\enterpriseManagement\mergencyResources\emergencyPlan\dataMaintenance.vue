<template>
  <div class="enterpriseManagement">
    <div class="duty-list padding_28">
      <div class="duty-list-left">
        <el-row class="tab">
          <el-col :span="12">
            <el-button @click="typeClick(1, true)"
                       size="small"
                       :type="treeType == 1?'primary':''"
                       style="width: 100%;">政务侧</el-button>
          </el-col>
          <el-col :span="12">
            <el-button @click="typeClick(2, true)"
                       size="small"
                       :type="treeType == 2?'primary':''"
                       style="width: 100%;">企业侧
            </el-button>
          </el-col>
        </el-row>
        <div class="search_tree">
          <div class="search_slide_input">
            <div class="search_slide_searchKey">
              <el-input placeholder="输入关键字进行过滤"
                        v-model.trim="filterText"
                        @change="(value) => {$refs.orgDragTreeRef.filter(value)}"
                        @keyup.enter.native="() => {$refs.orgDragTreeRef.filter(filterText)}">
                <i slot="append"
                   class="el-icon-search"
                   @click="() => {$refs.orgDragTreeRef.filter(filterText)}"></i>
              </el-input>
            </div>
            <div class="search_slide"
                 style="opacity:1">
              <el-scrollbar id="mainheight">
                <div class="headertitle">
                  <slot name="header"></slot>
                </div>
                <div class="navtitle">
                  <slot name="nav"></slot>
                </div>
                <!-- <el-tree :data="treeData" ref="orgDragTreeRef" node-key="orgCode" :current-node-key="defaultcheckeds"
                                    :empty-text="'暂无数据'" highlight-current :props="defaultProps" @node-drag-start="handleDragStart"
                                    @node-drag-enter="handleDragEnter" @node-drag-leave="handleDragLeave" @node-drag-over="handleDragOver"
                                    @node-drag-end="handleDragEnd" @node-drop="handleDrop" draggable :allow-drop="allowDrop" :allow-drag="allowDrag"
                                    :filter-node-method="filterNode" @node-click="onSelOrg" @node-contextmenu="nodeContextMenu"
                                    :default-expanded-keys="[defaultcheckeds]">
                                    <span class="custom-tree-node" slot-scope="{ node, data }">
                                        <span v-if="curSelNode.label != node.label || curSelNode.label == node.label && !curSelNode.editable"
                                            :title="node.label" class="nodeLabel">{{ node.label }}</span>
                                        <el-input size="mini" v-if="curSelNode.label == node.label && curSelNode.editable" v-model.trim="curSelNode.orgName"
                                            autofocus style="width: 100%;border-color:#5587FF;color: #5587FF;" @blur="editCurNode(curSelNode)"
                                            @keyup.enter.native="$event.target.blur" @click.stop.native="renameNodeInputFocus(node)" :maxlength="20">
                                        </el-input>
                                        <el-input size="mini" v-if="curSelNode.orgCode == data.parentCode && curSelNode.addChild && !data.orgCode"
                                            v-model.trim="addNodeOrgName" autofocus style="width: 100%;" @blur="addCurNodeChild(addNodeOrgName)"
                                            @keyup.enter.native="$event.target.blur" @click.stop.native="addChildNodeInputFocus(node)" :maxlength="20">
                                        </el-input>
                                    </span>
                                </el-tree> -->
                <el-tree class="filter-tree"
                         :data="treeData"
                         v-loading="loadingTree"
                         :props="defaultProps"
                         default-expand-all
                         :filter-node-method="filterNode"
                         @node-click="onSelOrg"
                         ref="orgDragTreeRef">
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </div>

      </div>
      <div class="duty-list-right flex-full">
        <div class="search_filter padding_0">
          <div class="maillist-title">人员信息</div>
          <div class="right_filter">
            <!-- <el-button type="primary" icon="el-icon-mobile-phone" @click="addressCall(null)">自定义拨号
                        </el-button> -->
            <el-button type="primary"
                       @click="openOrgUserOperateDialog" v-if='edVisible'>新增</el-button>
            <el-input placeholder="可按姓名、职务、办公电话过滤"
                      v-model.trim="searchData.key"
                      class="search_input"
                      clearable
                      @keyup.enter.native="searchByName"
                      style="width: 300px;"
                      maxlength="11">
              <i slot="append"
                 @click="searchByName"
                 class="el-icon-search"></i>
            </el-input>
          </div>
        </div>
        <div class="list_contain">
          <!-- 表 -->
          <div class="list_top">
            <div class="list_left flex-full">
              <!-- <list-table :propdata="propData" @tablecallback="tablecallback" unsortable="true"
                        @handleselection="handleSelectionData" :defaultselectfalse="defaultselect" rowkey="personId"></list-table> -->

              <div class="table">
                <el-table :data="personData"
                          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                          border
                          v-loading="loadingTable"
                          style="width: 100%"
                          ref="multipleTable">
                  <el-table-column align="center">
                    <template slot-scope="scope">
                      <el-radio v-model="selectRadio"
                                :label="scope.row.personId"
                                @change.native.stop.prevent="changeRedio(scope.row)">&nbsp;</el-radio>
                    </template>
                  </el-table-column>
                  <el-table-column prop="personName"
                                   label="姓名"
                                   align="center"
                                   :show-overflow-tooltip="true">
                  </el-table-column>
                  <el-table-column prop="post"
                                   label="职务"
                                   align="center">
                  </el-table-column>
                  <el-table-column prop="company"
                                   label="人员单位"
                                   align="center"
                                   :show-overflow-tooltip="true">
                  </el-table-column>
                  <el-table-column prop="phone"
                                   label="办公电话"
                                   align="center"
                                   min-width="100">
                  </el-table-column>
                  <el-table-column prop="telephone"
                                   label="手机"
                                   align="center">
                  </el-table-column>
                  <el-table-column label="操作"
                                   align="center">
                    <template slot-scope="scope">
                      <span v-if='edVisible' style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;"
                            @click="editPersons(scope.row,true)">编辑</span>
                      <span style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;"
                            @click="lookPerson(scope.row,false)">查看</span>
                      <span v-if='edVisible' @click="deletePerson(scope.row,false)"
                            style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">删除</span>
                      <el-dropdown v-if='edVisible'>
                        <span class="el-dropdown-link"
                              style="color: rgb(57, 119, 234);margin-right: 10px;cursor: pointer;">
                          更多
                        </span>
                        <el-dropdown-menu slot="dropdown">
                          <el-dropdown-item style="color: rgb(57, 119, 234);cursor: pointer;"
                                            @click.native="topBtnClick(scope.row)">置顶</el-dropdown-item>
                          <el-dropdown-item style="color: rgb(57, 119, 234);cursor: pointer;"
                                            @click.native="upNodeBtnClick(scope.row)">上移</el-dropdown-item>
                          <el-dropdown-item style="color: rgb(57, 119, 234);cursor: pointer;"
                                            @click.native="downNodeBtnClick(scope.row)">下移</el-dropdown-item>
                          <el-dropdown-item style="color: rgb(57, 119, 234);cursor: pointer;"
                                            @click.native="lastBtnClick(scope.row)">置底</el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </template>
                  </el-table-column>
                </el-table>
                <el-button type="primary"
                           icon="el-icon-check"
                           style="position: absolute;margin-top: 20px;"
                           v-show="showSubmit"
                           @click="submitSelect">确定已选</el-button>
              </div>
              <div class="pagination">
                <el-pagination @current-change="handleCurrentChange"
                               :current-page.sync="currentPage"
                               :page-size="size"
                               layout="total, prev, pager, next"
                               background
                               :total="total">
                </el-pagination>
              </div>

            </div>
          </div>
          <!-- 新增编辑查看 -->
          <div class="list_bottom_add"
               v-if="showAddEdit">
            <div class="form_title">{{personTitle}}</div>
            <el-form :model="ruleForm"
                     :rules="rules"
                     ref="ruleForm"
                     label-width="120px"
                     class="demo-ruleForm"
                     :disabled="isDisabled"
                     :hide-required-asterisk="isDisabled">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="姓名"
                                prop="personName">
                    <el-input v-model.trim="ruleForm.personName"
                              size="mini"
                              placeholder="请输入姓名"
                              maxlength="15" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="职务"                  
                                prop="post">
                    <el-input v-model.trim="ruleForm.post"
                     maxlength="15"
                              size="mini"
                              placeholder="请输入职务" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="所属单位">
                    <el-input v-model.trim="ruleForm.company"
                              size="mini"
                              :disabled="true"
                              placeholder="请输入所属单位"
                              maxlength="15" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="所属处（科）室">
                    <el-input v-model.trim="ruleForm.department"
                              size="mini"
                              maxlength="15"
                              placeholder="请输入所属单位" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="民族">
                    <el-select v-model="ruleForm.nation"
                               size="mini"
                               placeholder="请选择民族"
                               clearable>
                      <el-option v-for="item in minzuData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="性别">
                    <el-select size="mini"
                               v-model="ruleForm.sex"
                               placeholder="请选择性别">
                      <el-option label="男"
                                 value="1"></el-option>
                      <el-option label="女"
                                 value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="学历">
                    <el-select v-model="ruleForm.education"
                               size="mini"
                               placeholder="请选择学历"
                               clearable>
                      <el-option v-for="item in xueliData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="出生日期">
                    <el-date-picker v-model="ruleForm.birthday"
                                    type="date"
                                    size="mini"
                                    placeholder="选择日期">
                    </el-date-picker>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="学历证书编号">
                    <el-input v-model.trim="ruleForm.educationCode"
                              size="mini"
                              maxlength="15"
                              placeholder="请输入学历证书编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="专业">
                    <el-select v-model="ruleForm.profession"
                               size="mini"
                               placeholder="请选择专业"
                               clearable>
                      <el-option v-for="item in zhuanyeData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="办公电话" prop="phone">
                    <el-input v-model.trim="ruleForm.phone"
                              size="mini"
                              placeholder="请输入办公电话" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="联系电话"
                                prop="telephone">
                    <el-input v-model.trim="ruleForm.telephone"
                              size="mini"
                              placeholder="请输入联系电话" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="传真号码" prop="fax">
                    <el-input v-model.trim="ruleForm.fax"
                              size="mini"
                              placeholder="请输入传真号码" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                </el-col>
              </el-row>
              <!-- <el-row :gutter="20">
                            <el-form-item>
                                <el-button type="primary">保存</el-button>
                                <el-button>关闭</el-button>
                            </el-form-item>
                        </el-row> -->
              <el-col :span="24"
                      style="text-align: center;"
                      v-if="!isDisabled">
                <el-button size="small"
                           icon="el-icon-close"
                           plain
                           @click="cancleFun">关闭</el-button>
                <el-button type="primary"
                           @click="addorEditPersonSubmit"
                           icon="el-icon-check"
                           size="small">保存</el-button>
              </el-col>
            </el-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getMailListTreeData,
  getMailListTreeDataForPerson,
  addPerson,
  editPerson,
  getZidainData,
  personDetail,
  deletePersons,
  sortPersons
} from '@/api/mailList'
export default {
  name: 'dataMaintenance',
  props:{
    edVisible: {
      type: Boolean,
    },
  },
  data() {
    return {
      selectRadio: false,
      templateSelection: {},
      showSubmit: false,
      treeData: [],
      personTitle: '新增人员',
      showAddEdit: false,
      loadingTable: false,
      loadingTree: true,
      isDisabled: false,
      show: false,
      treeType: 1,
      filterText: '',
      total: 2,
      currentPage: 1,
      size: 5,
      personData: [],
      orgCode: '',
      searchData: {
        key: '',
        orgCode: ''
      },
      minzuData: [],
      xueliData: [],
      zhuanyeData: [],
      // 机构树选择node节点
      curSelNode: {},
      defaultcheckeds: '',
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      ruleForm: {
        personName: '',
        company: '',
        department: '',
        post: '',
        telephone: '',
        sex: '',
        educationCode: '',
        profession: '',
        phone: '',
        fax: '',
        nation: '',
        education: '',
        birthday: ''
      },
      rules: {
        personName: [
          { required: true, message: '请输入姓名', trigger: 'blur' }
        ],
        post: [{ required: true, message: '请输入职务', trigger: 'change' }],
         phone: [
          { message: "请输入办公电话", trigger: "blur" },
          { pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/, message: "请输入正确的办公电话" },
        ],

        telephone: [
          { required: true, message: "请输入联系电话", trigger: "blur" },
          {
            pattern: /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/,
            message: "请输入有效的电话号码",
          },
        ],
        fax: [
          {  message: "请输入传真", trigger: "blur" },
          { pattern: /^(\d{3,4})?(-){0,1}\d{7,8}$/, message: "请输入正确的传真号码" },
        ],
      }
    }
  },

  mounted() {
    this.getOrgGroupList()
    // this.orgType = JSON.parse(sessionStorage.getItem("role")).orgType;
    this.orgType = this.$store.state.login.user.user_type == 'gov' ? 1 : 0
  },

  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
    show(val) {
      if (!val) {
        this.showAddEdit = false
        this.reset()
      }
    }
  },

  methods: {
    changeRedio(row) {
      console.log(row)
      this.templateSelection = row
      if (this.templateSelection.personId) {
        this.showSubmit = true
      } else {
        this.showSubmit = false
      }
    },
    submitSelect() {
      this.$emit('close', this.templateSelection.personId)
    },
    getZidian() {
      this.getZidianMinzu()
      this.getZidianZhuanye()
      this.getZidianXueli()
    },
    emitOrgChange(curSelNode) {
      return curSelNode
    },
    getZidianMinzu() {
      getZidainData({
        dicCode: 'nation'
      }).then(res => {
        if (res.data.status == 200) {
          this.minzuData = res.data.data
        }
      })
    },
    getZidianZhuanye() {
      getZidainData({
        dicCode: 'EDUCATION_PROFESSION'
      }).then(res => {
        if (res.data.status == 200) {
          this.zhuanyeData = res.data.data
        }
      })
    },
    getZidianXueli() {
      getZidainData({
        dicCode: 'education'
      }).then(res => {
        if (res.data.status == 200) {
          this.xueliData = res.data.data
        }
      })
    },
    getOrgGroupList() {
      this.loadingTree = true
      getMailListTreeData({
        type: this.treeType
      }).then(res => {
        if (res.data.status == 200) {
          this.loadingTree = false
          this.treeData = res.data.data
          this.defaultchecked = res.data.data[0].orgCode
          this.curSelNode = res.data.data[0]
          this.$nextTick(() => {
            // this.$refs.orgDragTreeRef['setCurrentKey'](this.defaultchecked)
            // if (isFirst) {
            //     return;
            // }
            this.$nextTick(() => {
              const currentNode = document.getElementsByClassName('is-current')
              currentNode[currentNode.length - 1]['click']()
            })
          })
          this.emitOrgChange(this.curSelNode)
          this.personInfo(res.data.data[0].orgCode)
          this.orgCode = res.data.data[0].orgCode
        }
      })
    },
    onSelOrg(item) {
      this.currentPage = 1
      this.searchData.key = ''
      this.orgCode = item.orgCode
      this.reset()
      this.ruleForm.company = item.orgName
      this.showAddEdit = false
      this.personInfo(item.orgCode)
    },
    personInfo(orgCode) {
      this.searchData.orgCode = orgCode
      this.loadingTable = true
      getMailListTreeDataForPerson({
        key: this.searchData.key,
        nowPage: this.currentPage,
        orgCode: orgCode,
        pageSize: this.size
      }).then(res => {
        if (res.data.status == 200) {
          this.loadingTable = false
          this.personData = res.data.data.list
          this.total = res.data.data.total
        }
      })
    },
    // 树类型切换
    typeClick(type, isFirst) {
      this.treeType = type
      this.treeData = []
      this.loadingTree = true
      getMailListTreeData({
        type: this.treeType
      }).then(res => {
        if (res.data.status == 200) {
          this.loadingTree = false
          this.treeData = res.data.data
          // this.defaultchecked = this.defaultchecked;
          this.defaultchecked = res.data.data[0].orgCode
          this.curSelNode = res.data.data[0]
          this.$nextTick(() => {
            // this.$refs.orgDragTreeRef['setCurrentKey'](this.defaultchecked)
            if (isFirst) {
              return
            }
            this.$nextTick(() => {
              const currentNode = document.getElementsByClassName('is-current')
              currentNode[currentNode.length - 1]['click']()
            })
          })
          this.emitOrgChange(this.curSelNode)
          this.orgCode = res.data.data[0].orgCode
          this.personInfo(res.data.data[0].orgCode)
        } else {
          this.$message.error(res.data.msg)
        }
      })
    },
    // 表单重置
    reset() {
      this.ruleForm = {
        safetyTrainingCode: null,
        organizer: null,
        orgName: null,
        trainingName: null,
        trainingForm: null,
        noOfPtcpt: null,
        startTime: null,
        trainingEndTime: null,
        trainingStTime: null,
        endTime: null,
        trainingExps: null,
        trainer: null,
        ptcpt: null,
        trainingSummary: null,
        pinCharge: null,
        phone: null,
        createUser: null,
        createTime: null,
        updateUser: null,
        updateTime: null,
        instCode: null,
        trainingPhoto: null,
        list: []
      }
      if (this.$refs['ruleForm'] != undefined) {
        this.$refs['ruleForm'].resetFields()
      }
    },
    closeBoolean(val) {
      this.show = val
      if (!val) {
        this.showAddEdit = false
        this.reset()
      }
    },
    cancleFun() {
      this.show = false
      this.showAddEdit = false
      this.reset()
    },
    addorEditPersonSubmit() {
      this.$refs['ruleForm'].validate(flag => {
        if (flag) {
          this.ruleForm.orgCode = this.orgCode
          if (this.treeType == 1) {
            this.ruleForm.orgType = 1
          } else if (this.treeType == 2) {
            this.ruleForm.orgType = 2
          }
          if (this.ruleForm.personId) {
            editPerson(this.ruleForm).then(res => {
              if (res.data.status == 200) {
                this.$refs['ruleForm'].resetFields()
                this.showAddEdit = false
                this.reset()
                this.personInfo(this.orgCode)
              }
            })
          } else
            addPerson(this.ruleForm).then(res => {
              if (res.data.status == 200) {
                this.$refs['ruleForm'].resetFields()
                this.showAddEdit = false
                this.reset()
                this.personInfo(this.orgCode)
              }
            })
          // this.ruleForm.birthday=this.ruleForm.birthday+ ' 00:00:00';
        }
      })
    },
    editPersons(row) {
      this.showAddEdit = true
      this.isDisabled = false
      this.personTitle = '编辑人员'
      this.reset()
      personDetail({ personId: row.personId }).then(res => {
        if (res.data.status == 200) {
          // this.$refs['ruleForm'].resetFields();
          // this.showAddEdit = false;
          // this.personInfo(this.orgCode);
          this.ruleForm = res.data.data
        }
      })
    },
    lookPerson(row) {
      this.showAddEdit = true
      this.isDisabled = true
      this.personTitle = '查看人员'
      this.reset()
      personDetail({ personId: row.personId }).then(res => {
        if (res.data.status == 200) {
          // this.$refs['ruleForm'].resetFields();
          // this.showAddEdit = false;
          // this.personInfo(this.orgCode);
          this.ruleForm = res.data.data
        }
      })
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },
    openaddPersonToOrgToGroup() {
      // if (!this.activePhone.length) {
      // this.$message.warning('请选择人员');
      // return;
      // }
      // this.popoverOrgGroup.visible = true;
      // this.getOrgGroupList();
    },
    sendmessage(val) {
      // if (this.activePhone.length === 0) {
      //   this.$message.warning('请选择人员');
      //   return;
      // }
      // // 发送短信sms-modal组件
      // (this.$refs as any).smsModalRef.open(this.activePhone);
      // return;
      // let datar = []
      // if (!!val)
      // datar.push(val.rowVal)
      // else {
      // if (!this.activePhone.length) {
      //     if (this.messageDom)
      //     this.messageDom.close()
      //     return this.messageDom = this.$message.warning('请选择人员')
      // }
      // datar = this.activePhone
      // }
      // let data = {
      // viewDialog: true,
      // templateName: 'duty-address-book-send',
      // tilteName: '发送短信',
      // model: true,
      // escap: true,
      // close: true
      // }
      // // console.log(datar)
      // this.$set(this.parentData, 'data', datar)
      // this.$set(this, 'dialogConfig', data)
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val
      this.personInfo(this.searchData.orgCode)
    },
    // 上移按钮点击事件
    upNodeBtnClick(row) {
      if (this.orgType == '2') {
        this.$message.warning('当前节点无子节点暂不支持移动')
        return
      }
      let params = {
        // orgCode: this.curSelNode.orgCode,
        // parentCode: this.curSelNode.parentCode,
        id: row.personId,
        upDownType: 'up' // up:上移，down：下移
      }
      this.orgMoveFun(params)
    },
    // 下移按钮点击事件
    downNodeBtnClick(row) {
      if (this.orgType == '2') {
        this.$message.warning('当前节点无子节点暂不支持移动')
        return
      }
      let params = {
        id: row.personId,
        upDownType: 'down' // up:上移，down：下移
      }
      this.orgMoveFun(params)
    },
    // 置顶按钮点击事件
    topBtnClick(row) {
      if (this.orgType == '2') {
        this.$message.warning('当前节点无子节点暂不支持移动')
        return
      }
      let params = {
        id: row.personId,
        upDownType: 'top' // up:上移，down：下移
      }
      this.orgMoveFun(params)
    },
    // 置底按钮点击事件
    lastBtnClick(row) {
      if (this.orgType == '2') {
        this.$message.warning('当前节点无子节点暂不支持移动')
        return
      }
      let params = {
        id: row.personId,
        upDownType: 'last' // up:上移，down：下移
      }
      this.orgMoveFun(params)
    },
    // 机构树上移/下移请求方法
    orgMoveFun(params) {
      // this.$confirm('请确认是否移动?', '提示', {
      //   confirmButtonText: '确定',
      //   cancelButtonText: '取消',
      //   confirmButtonClass: 'confirmButtonClass',
      //   cancelButtonClass: 'confirmButtonClass',
      // }).then(() => {
      sortPersons(params)
        .then(res => {
          if (res.data.status == 200) {
            this.personInfo(this.searchData.orgCode)
          } else {
            this.$message({
              type: 'error',
              message: res.data.msg ? res.data.msg : '移动失败'
            })
          }
          // });
        })
        .finally(() => {
          this.$nextTick(() => {
            document
              .getElementsByClassName('el-button--info')[0]
              ['blur']()(document)
              .getElementsByClassName('el-button--info')[1]
              ['blur']()
          })
        })
    },
    searchByName() {
      this.currentPage = 1
      this.personInfo(this.searchData.orgCode)
    },
    // 打开机构树和人员的增删改页面
    openOrgUserOperateDialog() {
      this.showAddEdit = true
      this.isDisabled = false
    },
    // 添加人员到通讯录机构树中
    addPersonToOrg() {
      if (!this.searchData.orgCode) {
        this.$message.warning('请先选择机构！')
        return
      }
      this.$refs.addUserRef['open']({ ...this.selOrgInfo, id: '' })
    },
    // 删除人员-通讯录机构树中
    deletePerson(data) {
      this.$confirm('确认删除该数据？', '提示', {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        confirmButtonClass: 'confirmButtonClass',
        cancelButtonClass: 'confirmButtonClass'
      }).then(() => {
        deletePersons({ personId: data.personId }).then(res => {
          if (res.data.status == 200) {
            this.personInfo(this.orgCode)
          } else {
            this.$message({
              type: 'error',
              message: res.msg ? res.msg : '删除失败'
            })
          }
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  /deep/.el-input-group__append {
    cursor: pointer !important;
  }
}
.el-form-item {
  margin-bottom: 10px;
}
.duty-list {
  display: flex;
  justify-content: start;
  width: 100%;
  height: 70vh;
  padding-top: 0.1rem;
}

.duty-list-left {
  width: 22.5rem;
  height: 100%;
}

.duty-list-right {
  width: calc(100% - 22.5rem);
  overflow-y: scroll;
  overflow-x: hidden;
  height: 100%;
  /* background: rgba(233, 233, 233, 1) */
}
.tab {
  padding-left: 28px;
  padding-right: 20px;
  padding-top: 0.5rem;
  button {
    span {
      font-size: 16px !important;
    }
  }
}
.list_contain {
  display: flex;
  // justify-content: space-between;
  //   height: 72vh;
  height: 100%;
  width: 100%;
  flex-direction: column;
}
.list_top {
  height: calc(100% - 240px);
  display: flex;
}
.list_bottom_add {
  .form_title {
    font-size: 20px;
    line-height: 35px;
    padding-bottom: 5px;
    border-bottom: 1px solid #d7d7d7;
    margin-bottom: 15px;
  }
}
.list_left {
  padding-top: 0.5rem;
  width: 100%;
}
.buttonWrap {
  display: flex;
  margin-top: 10px; /*no*/
  padding: 0 28px;
  button {
    flex: 1;
    display: inline-block;
    padding-left: 5px !important;
    padding-right: 10px !important;
    margin-right: 5px !important;
    span {
      font-size: 16px;
    }
  }
}
.person_info_span {
  width: 85px;
  //   margin-bottom: 4px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.maillist-title {
  display: inline-block;
  line-height: 40px;
  font-size: 22px;
}
.right_filter {
  float: right;
}

.el-scrollbar {
  height: 100% !important;
}
.search_tree {
  position: relative;
  height: 90%;
  box-sizing: border-box;
}

.tabs_list {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 122;
  padding: 0.625rem 4px;
  padding-top: 0;
  margin-top: 0.5rem;
  position: absolute;
  height: 16.5rem;
  width: 100%;

  .filter_list {
    li {
      cursor: pointer;
      color: #000;
      font-size: 16px;
      padding: 0.625rem 1rem;
      line-height: 1rem;
      &:hover {
        background: #66b1ff;
        color: #fff;
      }

      &.active {
        background: #66b1ff;
        color: #fff;
      }
    }
  }
  .filter_list_content {
    display: inline-block;
    // width: 100%;
    line-height: 18px;
    // font-size: 1rem;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
}

.search_slide_searchKey {
  position: relative;
  height: 3rem;
  line-height: 3rem;
  background: #f5f5f7;
  border: 1px solid #e3e3e5;
  padding: 0 0.5rem;
  border-bottom: none;
}

.search_slide_input {
  // padding: 0 0.625rem;
  height: 100%;
  padding-top: 0.5rem;
  padding-left: 28px;
  padding-right: 20px;
}

.search_slide {
  position: relative;
  transition: all 0.5s;
  // top: 0.5rem;
  width: 100%;
  // height: calc(100% - 2.7rem);
  // height: 93.5%;
  // height: calc(100% - 80px);
  height: 88%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e3e3e5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 11;
  padding-left: 0.2rem;
  padding-bottom: 0.2rem;
  border-top: none;
  .controllab {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
  }
}

.popper__arrow {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
  -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: 2.5rem;
  left: 50%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #ebeef5;
  z-index: 100;

  &:after {
    content: '';
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
  }
}

.headertitle {
  background: #f5f5f6;
  padding-left: 30px;
  line-height: 2.1rem;
  font-size: 1rem;
  cursor: pointer;
}
.navtitle {
  background: #fff;
  // border-bottom: 1px solid rgb(228, 231, 237)
}

.tree_icon {
  color: #c0c4cc;
  font-size: 18px;
  padding: 6px;
}
.headerdrops {
  margin-left: 1.6rem;
  margin-bottom: 3px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #c0c4cc;
}
.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #cee5ff;
  color: #4a7dff;
}
</style>
