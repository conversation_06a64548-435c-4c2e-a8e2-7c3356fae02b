import axios from "axios";
// 分页查询企业画像评分方案
export const getPortraitPlanPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/findPage/v1",
    data: data,
  });
};
// 根据id查看企业画像评分方案
export const getPortraitPlanByid = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/findPlan/v1",
    data: data,
  });
};
// 新增企业画像评分方案
export const getPortraitPlanAdd = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/addPlan/v1",
    data: data,
  });
};
// 编辑企业画像评分方案

export const getPortraitPlanUpdate = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/updatePlan/v1",
    data: data,
  });
};
// 根据id删除企业画像评分方案
export const getPortraitPlanDelete = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/deletePlan/v1",
    data: data,
  });
};
// 根据id发布企业画像评分方案

export const getPublishPlan = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/publishPlan/v1",
    data: data,
  });
};
// 企业画像评分方案关联企业列表

export const getRelationList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/relationList/v1",
    data: data,
  });
};
// 企业画像评分方案修改关联企业
export const addRelation = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/plan/addRelation/v1",
    data: data,
  });
};

//新增二级规则信息
export const addSubRule = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/rule/sub/addSubRule/v1",
    data: data,
  });
};
//编辑二级规则信息
export const updateSubRule = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/rule/sub/updateSubRule/v1",
    data: data,
  });
};

//根据id删除二级规则信息
export const deleteSubRule = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/rule/sub/deleteSubRule/v1",
    data: data,
  });
};
//根据一级规则代码查询二级规则配置  mainRuleCode
export const subConfigList = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/rule/sub/configList/v1",
    data: data,
  });
};
//根据一级规则id查询二级规则信息列表 mainId
export const findByMain = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/rule/sub/findByMain/v1",
    data: data,
  });
};
// 企业画像当前评分分页列表
export const getPortraitInfoPage = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/info/latest/page/v1",
    data: data,
  });
};

// 根据enterpId查询企业画像当前评分详情
export const getPortraitInfoEnterp = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/info/enterp/find/v1",
    data: data,
  });
};

// 根据enterpId查询企业画像当前评分分析
export const getPortraitInfoEnterpAnalysis = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/info/enterp/analysis/v1",
    data: data,
  });
};

// 获取关联企业列表
export const getPortraitInfoEnterpRelation = (data) => {
  return axios({
    method: "get",
    url: `/enterprise/information/selPortraitRelList?distCode=${data.distCode}&current=${data.current}&size=${data.size}&enterpName=${data.enterpName}`,
  });
};
// 企业画像历史指标
export const getPortraitInfoHistory = (data) => {
  return axios({
    method: "post",
    url: "/gemp-model/api/gemp/model/portrait/info/enterp/page/v1",
    data: data,
  });
}
