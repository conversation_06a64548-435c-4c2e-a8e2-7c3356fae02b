<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span>
              <a-icon type="home" theme="filled" class="icon" /> 配置管理
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="duty-list padding_28">
      <div class="duty-list-left">
        <el-row class="tab"> </el-row>
        <el-row class="tab">
          <el-col :span="8">
            <el-popover
              placement="right"
              width="350"
              v-model="orgGroupInfo.addVisible"
              @show="openAddOrgGroupFun"
            >
              <el-form
                :model="orgGroupInfo"
                :rules="orgGroupInfo.addGroupNameRules"
                label-width="60px"
                ref="orgGroupAddFormRef"
              >
                <el-form-item label="名称" prop="label">
                  <el-input
                    v-model.trim="orgGroupInfo.label"
                    :maxlength="20"
                    :autofocus="true"
                    placeholder="请输入名称"
                  ></el-input>
                </el-form-item>
              </el-form>
              <div style="text-align: center">
                <el-button
                  size="mini"
                  @click="closeAddOrgGroupFun"
                  icon="el-icon-close"
                  >关闭</el-button
                >
                <el-button
                  type="primary"
                  size="mini"
                  @click="addOrgGroupFun"
                  icon="el-icon-check"
                  >确定</el-button
                >
              </div>
              <el-button
                slot="reference"
                size="small"
                type="success"
                plain
                icon="el-icon-plus"
                >新增</el-button
              >
            </el-popover>
          </el-col>
          <el-col :span="8">
            <el-popover
              placement="right"
              width="350"
              v-model="orgGroupInfo.editVisible"
              @show="openOrgGroupEditFun"
            >
              <el-form
                :model="orgGroupInfo"
                :rules="orgGroupInfo.groupNameRules"
                label-width="60px"
                ref="orgGroupEditFormRef"
              >
                <el-form-item label="名称" prop="label">
                  <el-input
                    v-model.trim="orgGroupInfo.label"
                    :autofocus="true"
                    placeholder="请输入名称"
                    :maxlength="20"
                  >
                  </el-input>
                </el-form-item>
              </el-form>
              <div style="text-align: center">
                <el-button
                  size="mini"
                  @click="closeAddOrgGroupFun"
                  icon="el-icon-close"
                  >关闭</el-button
                >
                <el-button
                  type="primary"
                  size="mini"
                  @click="editOrgGroupFun"
                  icon="el-icon-check"
                  >确定</el-button
                >
              </div>
              <el-button
                slot="reference"
                size="small"
                type="primary"
                plain
                icon="el-icon-edit"
                >编辑</el-button
              >
            </el-popover>
          </el-col>
          <el-col :span="8">
            <el-button
              @click="deleteFun($event)"
              size="small"
              type="warning"
              plain
              icon="el-icon-delete"
              >删除</el-button
            >
          </el-col>
        </el-row>

        <div class="search_tree">
          <div class="search_slide_input">
            <!-- <div class="search_slide_searchKey">
            
              <el-input
                v-model.trim="filterText"
                placeholder="请输入需要查询的名称"
                clearable
              >
              </el-input>
              <el-button
                slot="append"
                @click="searchLeft"
                icon="el-icon-search"
              ></el-button>
            </div> -->
            <div class="search_slide" style="opacity:1'">
              <el-scrollbar id="mainheight">
                <div class="headertitle">
                  <slot name="header"></slot>
                </div>
                <div class="navtitle">
                  <slot name="nav"></slot>
                </div>
                <el-tree
                  :expand-on-click-node="false"
                  :empty-text="emptyText"
                  render-after-expand
                  highlight-current
                  :default-expanded-keys="expanded"
                  node-key="nodeId"
                  class="filter-tree"
                  :data="treeData"
                  v-loading="loadingTree"
                  :props="defaultProps"
                  default-expand-all
                  :filter-node-method="filterNode"
                  @node-click="handleNodeClick"
                  ref="tree"
                >
                  <span slot-scope="scope" :class="scope.data.class">{{
                    scope.node.label
                  }}</span>
                </el-tree>
              </el-scrollbar>
            </div>
          </div>
        </div>
      </div>
      <div class="duty-list-right flex-full">
        <div class="search_filter padding_0">
          <div class="maillist-title">配置管理</div>
          <div class="right_filter">
            <!-- <el-button type="primary" icon="el-icon-mobile-phone" @click="addressCall(null)">自定义拨号
                    </el-button> -->
            <el-button type="" @click="refashBtn" :disabled="btnDisabled"
              >刷新缓存</el-button
            >
            <el-button type="primary" @click="openOrgUserOperateDialog('add')"
              >新增配置</el-button
            >
            <el-input
              placeholder="请输入配置ID查询"
              v-model.trim="searchData.configKey"
              class="search_input"
              clearable
              @keyup.enter.native="searchByName"
              style="width: 300px"
            >
              <i slot="append" @click="searchByName" class="el-icon-search"></i>
            </el-input>
          </div>
        </div>
        <div class="list_contain">
          <!-- 表 -->
          <div class="list_top">
            <div class="list_left flex-full">
              <!-- <list-table :propdata="propData" @tablecallback="tablecallback" unsortable="true"
                    @handleselection="handleSelectionData" :defaultselectfalse="defaultselect" rowkey="personId"></list-table> -->

              <div class="table">
                <el-table
                  :data="personData"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                  border
                  v-loading="loadingTable"
                  style="width: 100%"
                  ref="multipleTable"
                  @selection-change="handleSelectionChange"
                >
                  <!-- <el-table-column
                    type="selection"
                    width="55"
                    fixed="left"
                    align="center"
                  >
                  </el-table-column> -->
                  <el-table-column
                    prop="configKey"
                    label="配置ID"
                    align="center"
                    :show-overflow-tooltip="true"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="configValue"
                    label="配置值"
                    align="center"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="configComment"
                    label="配置描述"
                    align="center"
                    :show-overflow-tooltip="true"
                  >
                  </el-table-column>

                  <el-table-column label="操作" align="center">
                    <template slot-scope="scope">
                      <span
                        style="
                          color: rgb(57, 119, 234);
                          margin-right: 10px;
                          cursor: pointer;
                        "
                        @click="openOrgUserOperateDialog('edit', scope.row)"
                        >编辑</span
                      >

                      <span
                        style="
                          color: rgb(57, 119, 234);
                          margin-right: 10px;
                          cursor: pointer;
                        "
                        @click="deletePersonFromOrgGroup(scope.row)"
                        >删除</span
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="pagination">
                <el-pagination
                  @current-change="handleCurrentChange"
                  :current-page.sync="currentPage"
                  :page-size="size"
                  v-show="total > 0"
                  layout="total, prev, pager, next"
                  background
                  :total="total"
                >
                </el-pagination>
              </div>
            </div>
          </div>
          <!-- 已选联系人 -->
        </div>
        <!-- 发送短信/电话会议 机构树新增和编辑弹框-->
        <!-- <el-dialog top='4vh' width="80% " :title="dialogConfig.tilteName " :visible.sync="dialogConfig.viewDialog "
            :close-on-click-modal="false" :close-on-press-escape="dialogConfig.escap " :show-close="dialogConfig.close "
            v-if="dialogConfig.viewDialog " @close='closeDialog' modal-append-to-body>
            <component :is="dialogConfig.templateName " :propdata="parentData " @dialogcallback="closeDialogCall ">
            </component>
            </el-dialog> -->

        <!-- 云会议-->
        <!-- <el-dialog top='4vh' width="80% " title="云会议 " :visible.sync="cloudConfig.viewDialog " :close-on-click-modal="false"
            :close-on-press-escape="false " :show-close="false " v-if="cloudConfig.viewDialog ">
            <cloudference-dialog :propdata="cloudConfig.data " :people="activePhone " @dialogcallback="closeDialogCall ">
            </cloudference-dialog>
            </el-dialog> -->

        <!-- 拨号盘 -->
        <!-- <el-dialog title="拨打电话 " width="388px " :visible.sync="phonePanelVisible " :before-close="closePhone "
            :close-on-click-modal="false" :close-on-press-escape="false " v-if="phonePanelVisible ">
            <phone-panel :callnumber="callnumber "></phone-panel>
            </el-dialog>
            <add-user ref="addUserRef " @ok="addUserToOrgCall "></add-user>
            <sms-modal ref="smsModalRef " :allowchange="false " :disabledperson="true "></sms-modal> -->
        <!-- <el-dialog
          title="选择子节点 "
          :visible.sync="popoverOrgGroup.visible"
          :close-on-click-modal="false"
          :close-on-press-escape="false"
          v-if="popoverOrgGroup.visible"
        >
          <el-form
            :model="popoverOrgGroup"
            :rules="popoverOrgGroup.groupIdRules"
          >
            <el-form-item label="子节点：" prop="groupId">
              <el-select
                v-model="popoverOrgGroup.groupId"
                placeholder="请选择子节点"
                :popper-append-to-body="false"
              >
                <el-option
                  :label="item.label"
                  :value="item.groupId"
                  v-for="(item, index) in addressList"
                  :key="index"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div slot="footer" style="text-align: center">
            <el-button
              size="mini "
              icon="el-icon-close "
              @click="popoverOrgGroup.visible = false"
              >关闭</el-button
            >
            <el-button
              type="primary "
              size="mini "
              icon="el-icon-check "
              @click="addPersonToOrgToGroup"
              >确定</el-button
            >
          </div>
        </el-dialog> -->
      </div>
    </div>
    <DataMaintenance
      @refreshPreInfo="refreshPreInfoFn"
      ref="dataMaintenance"
    ></DataMaintenance>
    <el-dialog
      :title="title"
      :visible.sync="show"
      width="1100px"
      @close="cancel()"
      top="10vh"
      :close-on-click-modal="false"
      v-dialog-drag
    >
      <div>
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px !important"
        >
          <el-form-item label="配置Id" prop="configKey">
            <el-input
              v-model.trim="form.configKey"
              maxlength="30"
              placeholder="配置Id"
            />
          </el-form-item>

          <el-form-item label="配置值" prop="configValue" style="width: 100%">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="300"
              show-word-limit
              placeholder="请输入配置值"
              v-model.trim="form.configValue"
            ></el-input>
          </el-form-item>

          <el-form-item
            label="配置描述"
            prop="configComment"
            style="width: 100%"
          >
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="150"
              show-word-limit
              placeholder="请输入配置描述"
              v-model.trim="form.configComment"
            ></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" style="display: flex; justify-content: center">
          <el-button @click="cancel">取 消</el-button>
          <el-button type="primary" @click="submitForm()">保存</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DataMaintenance from "./dataMaintenance";
import {
  getConfigClassify,
  configModify,
  configDelete,
  configSearch,
  configAdd,
  configDeletePage,
  configRefresh,
} from "@/api/user";

export default {
  name: "setConfigura",
  components: {
    DataMaintenance,
  },
  data() {
    return {
      btnDisabled: false,
      title: "",
      orgGroupInfo: {
        addVisible: false,
        label: "",
        label: "",
        sort: 0,
        editVisible: false,
        addGroupNameRules: {
          label: [
            { required: true, message: "请输入名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "名称最大长度为20",
              trigger: "blur",
            },
          ],
        },
        groupNameRules: {
          label: [
            { required: true, message: "请输入名称", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "名称最大长度为20",
              trigger: "blur",
            },
          ],
        },
      },
      defaultprops: {
        children: "children",
        label: "orgName",
      },
      searchData: {
        configKey: "",
        orgCode: "",
        groupId: "",
        nowPage: 1,
        pageSize: 5,
      },
      treeData: [],
      treeDataList: [],
      // dialogConfig: {
      //   viewDialog: false, //弹框是否显示
      //   templateName: "", //弹框组件名
      //   tilteName: "", //标题头
      //   model: true,
      //   escap: true,
      //   close: true,
      // },
      treeType: 3,
      filterText: "",
      defaultProps: {
        children: "children",
        label: "label",
      },
      total: 0,
      orgType: "",
      currentPage: 1,
      size: 5,
      personData: [],
      loadingTable: false,
      loadingTree: true,
      selOrgGroupInfo: {
        parentId: "",
        id: "",
        label: "",
      },
      activePhone: [],
      popoverOrgGroup: {
        visible: false,
        groupId: "",
        groupIdRules: {
          groupId: [
            {
              required: true,
              message: "请选择子节点",
              trigger: "blur",
            },
          ],
        },
      },
      addressList: [], // 通讯录
      //弹框
      form: {
        configKey: "",
        configValue: "",
        configComment: "",
      },
      show: false,
      rules: {
        configKey: [
          { required: true, message: "请输入配置Id", trigger: "blur" },
        ],
        configValue: [
          { required: true, message: "请输入配置值", trigger: "blur" },
        ],
        configComment: [
          { required: true, message: "请输入配置描述", trigger: "blur" },
        ],
      },
    };
  },

  mounted() {
    this.getOrgGroupListes();
  },

  // watch: {
  //   filterText(val) {
  //     this.$refs.tree.filter(val);
  //   },
  // },

  methods: {
    // 弹框
    // 打开机构树和人员的增删改页面
    editInfo(row) {},
    openOrgUserOperateDialog(type, row) {
      if (type == "add") {
        if (this.selOrgGroupInfo.parentId == "") {
          this.$message.error("请选中一个节点");
        } else {
          this.show = true;
          this.title = "新增";
        }
      } else if (type == "edit") {
        this.show = true;
        this.title = "编辑";
        const rowData = Object.assign({}, row);
        this.form = rowData;
      }
    },
    submitForm() {
      var param;
      if (this.title == "新增") {
        param = {
          classId: this.selOrgGroupInfo.id,
          configComment: this.form.configComment,
          configKey: this.form.configKey,
          configValue: this.form.configValue,
          downloadurl: "",
          id: "",
          parentId: this.selOrgGroupInfo.parentId,
          updateTime: "",
          versioncode: "",
        };
      } else {
        param = this.form;
      }

      this.$refs["form"].validate((valid) => {
        if (valid) {
          configAdd(param).then((response) => {
            if (response.data.status === 200) {
              this.$message.success("保存成功");
              this.show = false;
              this.personInfo();
            } else {
              this.$message.error("保存失败");
            }
          });
        }
      });
    },
    reset() {
      this.form = {
        configKey: "",
        configValue: "",
        configComment: "",
      };
      if (this.$refs["form"] != undefined) {
        this.$refs["form"].resetFields();
      }
    },
    cancel() {
      this.show = false;
      this.reset();
    },
    // 弹框end

    //单击
    handleNodeClick(item) {
      // this.selectData = item;
      this.currentPage = 1;
      this.searchData.configKey = "";

      this.selOrgGroupInfo = item;
      this.personInfo();
    },
    refashBtn() {
      var that = this;
      configRefresh().then((res) => {
        if (res.data.status == 200) {
          this.$message.success(res.data.data);
          if(this.selOrgGroupInfo.id!=''){
            this.personInfo();
          }          
          this.btnDisabled = true;
        }
        setTimeout(function () {
          that.btnDisabled = false;
        }, 1000 * 10);
      });
    },
    //查询
    personInfo() {
      this.loadingTable = true;
      configSearch({
        configKey: this.searchData.configKey,
        classId: this.selOrgGroupInfo.id,
        listOrder: {},
        // id: "",
        nowPage: this.currentPage,
        pageSize: this.size,
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTable = false;
          this.personData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    handleSelectionChange(val) {
      this.activePhone = val;
    },

    sendmessage(val) {
      // if (this.activePhone.length === 0) {
      //   this.$message.warning('请选择人员');
      //   return;
      // }
      // // 发送短信sms-modal组件
      // (this.$refs as any).smsModalRef.open(this.activePhone);
      // return;
      // let datar = []
      // if (!!val)
      // datar.push(val.rowVal)
      // else {
      // if (!this.activePhone.length) {
      //     if (this.messageDom)
      //     this.messageDom.close()
      //     return this.messageDom = this.$message.warning('请选择人员')
      // }
      // datar = this.activePhone
      // }
      // let data = {
      // viewDialog: true,
      // templateName: 'duty-address-book-send',
      // tilteName: '发送短信',
      // model: true,
      // escap: true,
      // close: true
      // }
      // // console.log(datar)
      // this.$set(this.parentData, 'data', datar)
      // this.$set(this, 'dialogConfig', data)
    },
    //翻页
    handleCurrentChange(val) {
      this.currentPage = val;
      this.personInfo();
      // this.search();
    },
    refreshPreInfoFn() {
      this.personInfo();
    },
    // 树类型切换
    typeClick(type, isFirst) {
      this.filterText = "";
      this.searchData.configKey = "";
      this.treeType = type;
      // if (this.$refs.searchtree) {
      // this.$refs.searchtree.slideValue = '';
      // }
      this.treeData = [];

      this.getOrgGroupListes(isFirst);
    },
    searchLeft() {
      if (this.filterText == "") {
        this.treeData = this.treeDataList;
      } else {
        var list = this.treeDataList.filter((e) => {
          return e.label == this.filterText;
        });
        this.treeData = list;
      }

      console.log(this.treeData, "");
    },
    // 获取子节点数据
    getOrgGroupListes(isFirst) {
      this.loadingTree = true;
      getConfigClassify().then((res) => {
        if (res.data.status == 200) {
          this.loadingTree = false;
          // res.data.data = res.data.data.map((it) => {
          //   return {
          //     ...it,
          //     orgName: it.label,
          //     groupId: it.groupId,
          //     label: it.label,
          //     orgCode: it.orgCode, //
          //     label: it.label,
          //   };
          // });
          this.treeData = res.data.data;
          this.treeDataList = res.data.data;
        } else {
          // this.popoverOrgGroup.groupList = [];
          // this.$message.error(res.msg);
        }
      });
    },

    searchByName() {
      this.currentPage = 1;
      this.personInfo();
    },
    openAddOrgGroupFun() {
      // if (this.selOrgGroupInfo.parentId == "") {
      //   this.$message.error("请选中一个节点");
      // } else {
      //   this.$refs.orgGroupAddFormRef["resetFields"]();
      // }
      // this.orgGroupInfo.label = '';
    },

    // 根据通讯组-添加-关闭按钮点击事件
    closeAddOrgGroupFun() {
      this.orgGroupInfo.label = "";
      this.orgGroupInfo.sort = 0;
      this.orgGroupInfo.label = "";
      this.orgGroupInfo.addVisible = false;
      this.orgGroupInfo.editVisible = false;
      this.$refs.orgGroupAddFormRef.resetFields();
      this.$refs.orgGroupEditFormRef.resetFields();
    },
    // 根据通讯组-添加
    addOrgGroupFun() {
      this.$refs.orgGroupAddFormRef.validate((flag) => {
        if (flag) {
          let params = {
            parentId:
              this.selOrgGroupInfo.id == "" ? "0" : this.selOrgGroupInfo.id,
            label: this.orgGroupInfo.label,
            id: "",
          };
          configModify(params).then((res) => {
            if (res.data.status == 200) {
              this.$refs.orgGroupAddFormRef.resetFields();
              this.orgGroupInfo.addVisible = false;
              this.getOrgGroupListes();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    openOrgGroupEditFun() {
      this.$refs.orgGroupEditFormRef["resetFields"]();
      if (!this.selOrgGroupInfo.parentId) {
        this.orgGroupInfo.editVisible = false;
        this.$message.warning("请先选择子节点");
        return;
      }
      this.orgGroupInfo = {
        ...this.orgGroupInfo,
        ...this.selOrgGroupInfo,
      };
      this.orgGroupInfo.editVisible = true;
    },
    // 根据通讯组-编辑
    editOrgGroupFun() {
      if (!this.selOrgGroupInfo.parentId) {
        this.$message.warning("请先选择子节点");
        return;
      }
      this.$refs.orgGroupEditFormRef.validate((flag) => {
        if (!flag) {
          return;
        }
        this.selOrgGroupInfo.label = this.orgGroupInfo.label;
        let params = {
          ...this.selOrgGroupInfo,
        };
        configModify(params).then((res) => {
          if (res.data.status == 200) {
            this.orgGroupInfo.editVisible = false;
            this.getOrgGroupListes();
            return;
          } else {
            this.$message.error(res.data.msg);
          }
        });
      });
    },
    // 根据通讯组-删除
    deleteFun($event) {
      if (!this.selOrgGroupInfo.parentId) {
        this.$message.warning("请先选择子节点");
        return;
      }
      this.$confirm("确认删除该数据？", "提示", { type: "warning" })
        .then(() => {
          let params = {
            id: this.selOrgGroupInfo.id,
          };
          configDelete(params).then((res) => {
            if (res.data.status == 200) {
              this.getOrgGroupListes();
              return;
            }
            this.$message.error(res.msg);
          });
        })
        .catch(() => {})
        .finally(() => {
          this.$nextTick(() => {
            document.getElementsByClassName("el-button--warning")[0]["blur"]();
          });
        });
    },
    /**
     * 删除已选人员
     * author by liuwenlei
     */
    cancelSelectPhone(val, index) {
      let data = {
        groupId: val.groupId,
        personId: val.personId,
      };
      this.$confirm("是否取消当前已选联系人?", "提示", {
        type: "warning",
      })
        .then(() => {
          this.activePhone.splice(index, 1);
          // this.defaultselect = val
          this.$refs.multipleTable.toggleRowSelection(this.personData[index]);
        })
        .catch(() => {});
    },
    // 添加人员到通讯录机构树中
    addPersonToOrg() {
      if (!this.searchData.orgCode) {
        this.$message.warning("请先选择机构！");
        return;
      }
      this.$refs.addUserRef["open"]({ ...this.selOrgInfo, id: "" });
    },
    // 编辑人员-通讯录机构树中
    editPerson(data) {
      this.$refs.addUserRef["open"]({ ...data.rowVal });
    },
    // 删除人员-通讯录机构树中
    deletePerson(data) {
      this.$confirm("确认删除该数据？", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmButtonClass",
        cancelButtonClass: "confirmButtonClass",
      }).then(() => {
        this.http.AddressBookRequest.deletePersonNewtoOrg(data.rowVal.id).then(
          (res) => {
            if (res.status == 200) {
              this.getListData();
            } else {
              this.$message({
                type: "error",
                message: res.msg ? res.msg : "删除失败",
              });
            }
          }
        );
      });
    },
    // 从子节点中删除人员
    deletePersonFromOrgGroup(data) {
      console.log("从子节点中删除人员------->", data);
      this.$confirm("确认删除该数据？", "提示", {
        type: "warning",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        confirmButtonClass: "confirmButtonClass",
        cancelButtonClass: "confirmButtonClass",
      }).then(() => {
        let params = {
          id: data.id,
        };
        configDeletePage(params).then((res) => {
          if (res.data.status == 200) {
            this.$message.success(res.data.msg || "删除成功");
            this.personInfo();
          } else {
            this.$message({
              type: "error",
              message: res.data.msg ? res.data.msg : "删除失败",
            });
          }
        });
      });
    },

    // 添加人员到通讯录机构树中回调
    addUserToOrgCall(callData) {
      this.getListData();
    },
  },
};
</script>
<style lang="scss" scoped>
/* PC或中大型笔记本设备 desktop */
@media all and (min-width: 1680px) {
  .botton {
    width: 680px !important;
  }
}
/* 中小型笔记本或大平板 laptop */
@media all and (min-width: 1440px) and (max-width: 1680px) {
  .botton {
    width: 580px !important;
  }
}
/* 中型平板或小型笔记本 tablet */
@media all and (min-width: 1024px) and (max-width: 1440px) {
  .botton {
    width: 380px !important;
  }
}
/* 手机或小平板 phone */
@media all and (max-width: 1024px) {
  .botton {
    width: 180px !important;
  }
}
.enterpriseManagement {
  background-color: #fff;
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    // margin-bottom: 20px;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      padding-bottom: 10px;
      border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
      color: #4f5b69;
    }
  }
  /deep/.el-input-group__append {
    cursor: pointer !important;
  }
}
.duty-list {
  display: flex;
  justify-content: start;
  width: 100%;
  height: 77vh;
  padding-top: 0.1rem;
}

.duty-list-left {
  width: 30%;
  height: 100%;
}

.duty-list-right {
  width: 70%;
  height: 100%;
  /* background: rgba(233, 233, 233, 1) */
}
.tab {
  padding-left: 28px;
  padding-right: 20px;
  padding-top: 0.5rem;
  button {
    span {
      font-size: 16px !important;
    }
  }
}
.list_contain {
  display: flex;
  // justify-content: space-between;
  //   height: 72vh;
  height: 100%;
  width: 100%;
  flex-direction: column;
}
.list_top {
  height: calc(100% - 198px);
  display: flex;
}
.list_left {
  padding-top: 0.5rem;
  width: 100%;
}
.list_bottom_box {
  height: 198px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  // justify-content: space-between;
  .bottom_list {
    height: calc(100% - 50px);
    border: 1px solid #dfdfe3;

    .bottom_concant_list {
      width: 100%;
      height: calc(100% - 2rem);

      .bottom_flex_group {
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        padding-left: 1rem;

        > div {
          width: 25%;
          height: 80px;
          border-radius: 4px;
          margin-top: 0.875rem;
          padding-right: 0.75rem;
          position: relative;
          overflow: hidden;
          .concat_inner {
            width: 100%;
            height: 100%;
            padding: 14px;
            display: flex;
            justify-content: space-between;
            border: 1px solid #dfdfe3;
            background: #fafafa;
            > img {
              // width:66px;
              display: inline-block;
            }

            > .cancat_info {
              width: calc(100% - 52px);
              box-sizing: border-box;
              padding-left: 14px;
              font-size: 16px;
              display: flex;
              flex-direction: column;
              justify-content: space-around;

              .person_title {
                color: #49445f;
                width: 100%;
                line-height: 1rem;
                margin-top: 0.5rem;

                > p {
                  width: 100%;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                  // display: -webkit-box;
                  // -webkit-line-clamp: 2;
                  // -webkit-box-orient: vertical;
                }
              }

              .person_info {
                margin-top: 4px;
                margin-bottom: 6px;
                color: #9999b0;
                font-size: 12px;
                word-break: keep-all;
              }
            }
          }

          i {
            position: absolute;
            right: 0.75rem;
            top: 5px;
            cursor: pointer;

            &::before {
              color: #4a7dff !important;
            }
          }
        }
      }
    }

    .bottom_title {
      height: 2rem;
      line-height: 2rem;
      background: #f3f3f3;
      padding: 0 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .bottom_title_add_button {
      cursor: pointer;
    }

    .bottom_contain {
      height: calc(100% - 2rem);
    }
  }

  .bottom_btn_group {
    height: 38px;
    display: flex;
    justify-content: space-between;
    background: #f3f3f5;
    border: 1px solid #dfdfe3;
    border-radius: 4px;
    align-items: center;

    > div {
      flex: 1;
      text-align: center;
      cursor: pointer;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      &:not(:last-child) {
        border-right: 1px solid #dfdfe3;
      }

      &:hover {
        color: #66b1ff;
      }
    }
  }
}
.person_info_span {
  width: 85px;
  //   margin-bottom: 4px;
  display: inline-block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.maillist-title {
  display: inline-block;
  line-height: 40px;
  font-size: 22px;
}
.right_filter {
  float: right;
}

.el-scrollbar {
  height: 100% !important;
}
.search_tree {
  position: relative;
  height: 100%;
  box-sizing: border-box;
}

.tabs_list {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 122;
  padding: 0.625rem 4px;
  padding-top: 0;
  margin-top: 0.5rem;
  position: absolute;
  height: 16.5rem;
  width: 100%;

  .filter_list {
    li {
      cursor: pointer;
      color: #000;
      font-size: 16px;
      padding: 0.625rem 1rem;
      line-height: 1rem;
      &:hover {
        background: #66b1ff;
        color: #fff;
      }

      &.active {
        background: #66b1ff;
        color: #fff;
      }
    }
  }
  .filter_list_content {
    display: inline-block;
    // width: 100%;
    line-height: 18px;
    // font-size: 1rem;
    // white-space: nowrap;
    // overflow: hidden;
    // text-overflow: ellipsis;
  }
}

.search_slide_searchKey {
  position: relative;
  height: 40px;
  line-height: 40px;
  background: #f5f5f7;
  border: 1px solid #e3e3e5;
  padding: 0 0.5rem;
  border-bottom: none;
  display: flex;
  padding: 10px;
  box-sizing: content-box;
}

.search_slide_input {
  // padding: 0 0.625rem;
  height: 100%;
  padding-top: 0.5rem;
  padding-left: 28px;
  padding-right: 20px;
}

.search_slide {
  position: relative;
  transition: all 0.5s;
  // top: 0.5rem;
  width: 100%;
  // height: calc(100% - 2.7rem);
  // height: 93.5%;
  // height: calc(100% - 80px);
  height: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e3e3e5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 11;
  padding-left: 0.2rem;
  padding-bottom: 0.2rem;
  border-top: none;
  .controllab {
    border-bottom: 1px solid #e4e7ed;
    display: flex;
    justify-content: space-between;
  }
}

.popper__arrow {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 6px;
  -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
  top: 2.5rem;
  left: 50%;
  margin-right: 3px;
  border-top-width: 0;
  border-bottom-color: #ebeef5;
  z-index: 100;

  &:after {
    content: "";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    top: 1px;
    margin-left: -6px;
    border-top-width: 0;
    border-bottom-color: #fff;
  }
}

.headertitle {
  background: #f5f5f6;
  padding-left: 30px;
  line-height: 2.1rem;
  font-size: 1rem;
  cursor: pointer;
}
.navtitle {
  background: #fff;
  // border-bottom: 1px solid rgb(228, 231, 237)
}

.tree_icon {
  color: #c0c4cc;
  font-size: 18px;
  padding: 6px;
}
.headerdrops {
  margin-left: 1.6rem;
  margin-bottom: 3px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #c0c4cc;
}
.pagination {
  margin-top: 30px;
  padding-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}
</style>
<style>
.el-tree--highlight-current .el-tree-node.is-current > .el-tree-node__content {
  background-color: #cee5ff;
  color: #4a7dff;
}
</style>
