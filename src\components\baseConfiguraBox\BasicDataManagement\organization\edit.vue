<template>
  <div class="table">
    <el-dialog
      :title="title"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      :destroy-on-close="true"
      width="800px"
      :close-on-click-modal="false"
    >
      <div class="container">
        <el-form
          :model="ruleForm"
          status-icon
          :rules="rules"
          ref="ruleForm"
          label-width="120px"
          class="demo-ruleForm"
        >
          <el-form-item label="编码" prop="distCode">
            <el-input
              v-model.trim="ruleForm.distCode"
              :disabled="disabled"
              placeholder="编码"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="名称" prop="distName">
            <el-input
              v-model.trim="ruleForm.distName"
              placeholder="名称"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="简称" prop="distShortname">
            <el-input
              v-model.trim="ruleForm.distShortname"
              placeholder="简称"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="全拼" prop="distSpellingFull">
            <el-input
              v-model.trim="ruleForm.distSpellingFull"
              placeholder="全拼"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="经度" prop="distLongitude">
            <el-input
              v-model.trim="ruleForm.distLongitude"
              placeholder="经度"
            ></el-input>
          </el-form-item>
          <el-form-item label="纬度" prop="distLatitude">
            <el-input
              v-model.trim="ruleForm.distLatitude"
              placeholder="纬度"
            ></el-input>
          </el-form-item>
          <el-form-item label="简拼" prop="distSpellingShort">
            <el-input
              v-model.trim="ruleForm.distSpellingShort"
              placeholder="简拼"
              maxlength="20"
            ></el-input>
          </el-form-item>
          <el-form-item label="租户" prop="distTenantId">
            <el-input
              v-model.trim="ruleForm.distTenantId"
              placeholder="租户"
              maxlength="30"
            ></el-input>
          </el-form-item>
          <el-form-item label="排序" prop="distSortnum">
            <el-input
              v-model.trim="ruleForm.distSortnum"
              placeholder="排序"
              maxlength="10"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="distNotes">
            <el-input
              v-model.trim="ruleForm.distNotes"
              placeholder="备注"
              maxlength="100"
            ></el-input>
          </el-form-item>
          <el-form-item label="是否叶子节点" prop="isLeaf">
            <el-select v-model="ruleForm.isLeaf" placeholder="请选择">
              <el-option label="否" :value="0"></el-option>
              <el-option label="是" :value="1"></el-option>
            </el-select>
          </el-form-item>
        </el-form>
        <el-button
          size="default"
          :loading="submitting"
          type="primary"
          class="commit"
          @click="submitForm('ruleForm')"
          >保存</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDistrictInfo,
  getDistrictAdd,
  getDistrictUpdate,
} from "@/api/BasicDataManagement";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
    orgCode: {
      type: String,
    },
  },
  data() {
    var distLongitudeFn = (rule, value, callback) => {
     
      // const reg =
      //   /^-?((0|[1-9]\d?|1[1-7]\d)(\.\d{1,7})?|180(\.0{1,7})?)?$/;
      // if (value) {
      //   if (!reg.test(value)) {
      //     callback(new Error("经度不符合规范：经度整数部分为0-180,小数部分为0-6位！"));
      //   }
      //   callback();
      // }
      var reg = /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/
      if(!value){
        callback(new Error("请填写经度！"));
      } else {
        if(!reg.test(value)){
          callback(new Error("经度不符合规范：经度整数部分为0-180,小数部分为0-6位！"));
        }
        callback();
      }
    };
    var distLatitudeFn = (rule, value, callback) => {
      var reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/;
      // const reg =  /^-?((0|[1-8]\d|)(\.\d{1,7})?|90(\.0{1,7})?)?$/;
      // if(value){
      //    if (!reg.test(value)) {
      //     callback(new Error("纬度不符合规范：纬度整数部分为0-90,小数部分为0-6位！"));
      //   }
      //   callback();
      // }
      if(!value){
        callback(new Error("请填写纬度！"));
      } else {
        if(!reg.test(value)){
          callback(new Error("纬度不符合规范：纬度整数部分为0-90,小数部分为0到6位!"));
        }
        callback();
      }
    };
     var isNum = (rule, value, callback) => {   
       var reg=/(^[1-9]\d*$)/   
      if(value){
         if(!reg.test(value)){
            callback(new Error("排序请输入数字类型"));
         }  
         callback();   
      } else{
        callback(new Error("排序请输入数字类型"));
      }
    };
    
    return {
      disabled: false,
      title: "",
      tableVisible: false,
      submitting: false,
      ruleForm: {
        distCode:"",
        distName:"",
        distShortname:"",
        distTenantId:"",
        isLeaf:"",
        distLongitude:"",
        distLatitude:"",
        distSortnum:''
      },
      checkedKeys: [],
      rules: {
        distCode: [
          { trigger: "blur", required: true, message: "编码不能为空" },
        ],
        distName: [
          { trigger: "blur", required: true, message: "名称不能为空" },
        ],
        distShortname: [
          { trigger: "blur", required: true, message: "简拼不能为空" },
        ],
        distTenantId: [
          { trigger: "blur", required: true, message: "租户不能为空" },
        ],
        isLeaf: [
          {
            trigger: "change",
            required: true,
            message: "是否叶子节点不能为空",
          },
        ],

        distLongitude: [{ validator: distLongitudeFn, trigger: "blur",required:true  }],
        distLatitude: [{ validator: distLatitudeFn, trigger: "blur",required:true  }],
         distSortnum: [{ validator: isNum, trigger: "blur",required:true }],
      },
      allTreeData: [],
      treeData: [],
      updataBool: false,
      distParentCode: "",
    };
  },
  //方法集合
  methods: {
    /**
     * 父组件发过来的消息
     * @param updataBool 更新或新建
     */
    parentMsg(val, distCode, updataBool, title) {
      this.title = title;
      if (title == "编辑") {
        this.disabled = true;
      } else {
        this.disabled = false;
      }
      this.tableVisible = val;
      this.distParentCode = this.orgCode;
      if (updataBool == false) {
        this.ruleForm = {};
      } else {
        this.getDistrictInfoData(distCode);
      }
    },
    onSelects(selectedKeys, info) {
      //   console.log('selected', selectedKeys, info);
    },
    onChecks(checkedKeys, info) {
      // console.log(checkedKeys)
      if (checkedKeys) {
        this.ruleForm.selectedRoleIds = checkedKeys.join(",");
      } else {
        this.ruleForm.selectedRoleIds = "";
      }
    },
    getDistrictInfoData(distCode) {
      //this.orgCode
      getDistrictInfo(distCode).then((res) => {
        this.ruleForm = res.data.data;
      });
    },
    submitForm(formName) {
      debugger;
      this.$refs[formName].validate((valid) => {
        console.log(valid,'valud')
        if (valid) {
         
          if (this.submitting) return;
          this.submitting = true;
        
          if (this.title == "新增") {
            getDistrictAdd({
              ...this.ruleForm,
              distParentCode: this.distParentCode,
            })
              .then((data) => {
                if (data.data.code == 0) {
                  this.submitting = false;
                  this.$message({
                    type: "success",
                    message: data.data.msg || data.data.data,
                  });
                  this.tableVisible = false;
                  this.$refs.ruleForm.resetFields();
                  this.$emit("add-callback");
                } else {
                  this.submitting = false;
                  this.$message({
                    type: "info",
                    message: data.data.msg,
                  });
                }
              })
              .catch((e) => {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: "接口报错",
                });
              });
          } else {
            getDistrictUpdate({
              ...this.ruleForm,
              distParentCode: this.distParentCode,
            })
              .then((data) => {
                if (data.data.code == 0) {
                  this.submitting = false;
                  this.$message({
                    type: "success",
                    message: data.data.msg || data.data.data,
                  });
                  this.tableVisible = false;
                  this.$refs.ruleForm.resetFields();
                  this.$emit("add-callback");
                } else {
                  this.submitting = false;
                  this.$message({
                    type: "info",
                    message: data.data.msg,
                  });
                }
              })
              .catch((e) => {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: "接口报错",
                });
              });
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    updataForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.submitting) return;
          this.submitting = true;
          getDistrictUpdate({
            ...this.ruleForm,
            distParentCode: this.distParentCode,
          })
            .then((data) => {
              if (data.data.code == 0) {
                this.submitting = false;
                this.$message({
                  type: "success",
                  message: data.data.data,
                });
                this.tableVisible = false;
                this.$refs.ruleForm.resetFields();
                this.$emit("add-callback");
              } else {
                this.submitting = false;
                this.$message({
                  type: "info",
                  message: data.data.msg,
                });
              }
            })
            .catch((e) => {
              this.submitting = false;
              this.$message({
                type: "info",
                message: "接口报错",
              });
            });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    /**
     * 点击关闭图标或者遮罩回调
     * @param done
     */
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then(() => {
          done();
        })
        .catch(() => {});
    },
    clearTable() {
      this.ruleForm = {};
      this.treeData = [];
      this.submitting = false;
      if (this.$refs["ruleForm"] !== undefined) {
        this.$refs["ruleForm"].resetFields();
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
/deep/ .el-select {
  width: 80%;
}
.table {
  overflow: hidden;
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    .input {
      width: 60%;
    }
    .el-select,
    .radio-box {
      width: 60%;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .el-form-item {
    margin-bottom: 22px;
  }
  .el-input {
    width: 80%;
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
