<template>
  <div class="table">
    <el-dialog
      title="修改系统信息"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableData"
        ref="tableData"
        v-loading="loading"
        label-width="150px"
      >
        <div class="inputBox">
          <el-form-item label="系统名称" prop="systemName" class="lable">
            <el-input
              v-model.trim="tableData.systemName"
              placeholder="系统名称"
              label="系统名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="系统编码" prop="systemCode" class="lable">
            <el-input
              v-model.trim="tableData.systemCode"
              :disabled="systemCodeBool"
              placeholder="系统编码"
              label="系统编码"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="系统访问地址" prop="systemUrl" class="lable">
            <el-input
              v-model.trim="tableData.systemUrl"
              placeholder="系统访问地址"
              label="系统访问地址"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="系统菜单图片" prop="iconUrl" class="lable">
            <el-input
              v-model.trim="tableData.iconUrl"
              placeholder="系统菜单图片"
              label="系统菜单图片"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <!-- <div class="inputBox">
          <el-form-item label="所属组件名称" prop="moduleName" class="lable">
            <el-input
              v-model="tableData.moduleName"
              placeholder="所属组件名称"
              label="所属组件名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div> -->
        <div class="inputBox">
          <el-form-item label="序号" prop="orderNum" class="lable">
            <el-input
              v-model.trim="tableData.orderNum"
              placeholder="序号"
              label="序号"
              class="input"
              oninput="value=value.replace(/[^\d]/g,'')"
            ></el-input>
          </el-form-item>
        </div>
        <el-button
          size="default"
          type="primary"
          class="commit"
          @click="handleSaveTable"
          >保存</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveMenmenuByMenuId,
  sameMenuFlagSystemCodes,
  saveSystem,
} from "../../../api/user";
import { systemRules } from "../../../api/rules";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loading: false,
      tableData: {
        systemId: "",
        systemName: "",
        systemCode: "",
        systemUrl: "",
        iconUrl: "",
        orderNum: "",
      },
      checkedKeys: [],
      rules: systemRules,
      parentId: "",
      systemCodeBool: false,
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
    },
    clearTable() {
      this.tableData = {};
    },
    handleSaveTable() {
      // this.loading = true;
      this.$refs["tableData"].validate((valid) => {
        if (valid) {
          saveSystem({ ...this.tableData })
            .then((res) => {
              // this.loading = false;
              this.tableVisible = false;
              this.$message({
                message: res.data.message,
                type: "success",
              });
              this.$parent.fatherMethod();
            })
            .catch((e) => {
              this.loading = false;
              console.log(e, "请求错误");
            });
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    getData(data, type) {
      this.tableData = data;
      //系统编码状态
      this.systemCodeBool = type;
    },
    onCheck(checkedKeys, e) {
      // console.log(checkedKeys, e)
      let key = JSON.stringify(checkedKeys);
      key = key.split('"').join("");
      key = key.split("[").join("");
      key = key.split("]").join("");
      // console.log(key);
      this.tableData.belongSystemCode = key;
    },
    // bus() {
    //   var vm = this;
    //   // 用$on事件来接收参数
    //   Bus.$on("SAMenuListData", (data) => {
    //     vm.parentId=data.menuId;

    //   });
    //   console.log(vm.parentId);
    // },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
