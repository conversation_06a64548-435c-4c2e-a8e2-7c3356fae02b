<template>
  <div class="app-container">
    <div class="title">发布渠道</div>
    <!-- {{channelList}} -->
    <!-- {{checkLists}} -->
    <el-table :data="channelList" stripe class="pdTable" v-loading="loading">
      <el-table-column
        label="序号"
        align="center"
        type="index"
        width="60"
      ></el-table-column>
      <el-table-column
        label="渠道名称"
        align="center"
        width="125"
        prop="channelName"
      />

      <el-table-column label="发布对象" align="center">
        <template slot-scope="scope">
          <div>
            <!-- {{checkLists}} -->
            <div class="manCheckedCon">
              <div class="placeholderDiv" v-if="checkLists.length < 1">
                请选择发布对象
              </div>
              <div v-else class="placeholderDiv">
                <el-tag
                  :disabled="disabled"
                  class="tagItem"
                  v-for="(tag, index) in checkLists"
                  :key="index"
                  :closable="closable"
                  size="small"
                  @close="tagCloses(index)"
                  >{{ tag.orgName }}</el-tag
                >
              </div>
            </div>
            <el-row class="manCheckedBox" v-if="!disabled">
              <el-col :span="24" v-if="leftOrgDataSys.length > 0">
                <div class="leftTree">
                  <!--                  <el-scrollbar style="" class="auto-scrollbar">-->
                  <el-tree
                    :data="leftOrgDataSys"
                    :props="defaultProps"
                    :highlight-current="true"
                    :default-expand-all="false"
                    :expand-on-click-node="false"
                  >
                    <span class="custom-tree-node" slot-scope="{ node, data }">
                      <span>{{ node.label }}</span>
                      <!-- v-if="!data.children || data.children == null || data.children.length === 0" -->
                      <span>
                        <el-button
                        :disabled="data.label == '政务侧' || data.label == '企业侧'"
                          type="text"
                          size="mini"
                          @click="
                            () =>
                              handleNodeClickSys({
                                dispOrder: data.dispOrder,
                                fax: data.fax,
                                id: data.id,
                                label: data.label,
                                orgCode: data.orgCode,
                                orgDuty: data.orgDuty,
                                orgName: data.orgName,
                                orgType: data.orgType,
                                parentCode: data.parentCode,
                                person: data.person,
                              })
                          "
                        >
                          加入发布对象
                        </el-button>
                      </span>
                    </span>
                  </el-tree>
                  <!--                  </el-scrollbar>-->
                </div>
              </el-col>
              <!--              <el-col :span="12">-->
              <!--                <div class="rightTree" v-loading="rightTreeLoading">-->
              <!--                  <el-scrollbar style="height: 20vh" class="auto-scrollbar">-->
              <!--                    <el-checkbox-group-->
              <!--                      v-model="checkLists"-->
              <!--                      class="treeCheckBox"-->
              <!--                    >-->
              <!--                      <el-checkbox-->
              <!--                        :label="item.orgName + ':' + item.id"-->
              <!--                        v-for="(item, index) in rightManLists"-->
              <!--                        :key="index"-->
              <!--                        :name="item.id"-->
              <!--                        @change="joinerChanges"-->
              <!--                        >{{ item.orgName }}</el-checkbox-->
              <!--                      >-->
              <!--                    </el-checkbox-group>-->
              <!--                                        <div v-else>暂无企业信息</div>-->
              <!--                  </el-scrollbar>-->
              <!--                </div>-->
              <!--              </el-col>-->
            </el-row>
          </div>
        </template>
      </el-table-column>

      <!--      <el-table-column label="应用" align="center" width="120">-->
      <!--        <template slot-scope="scope">-->
      <!--          <el-switch-->
      <!--            v-model="scope.row.isApply"-->
      <!--            active-color="#13ce66"-->
      <!--            inactive-color="#95a3bf"-->
      <!--            :disabled="disabled"-->
      <!--          ></el-switch>-->
      <!--        </template>-->
      <!--      </el-table-column>-->
    </el-table>
  </div>
</template>

<script>
import { orgTreeByorgcode } from "../../../../../api/informationRelease";
import { getOrgTreesN } from '../../../../../api/user'

const demochannelList = [
  { channelName: "系统消息", publishObj: "", isApply: false, code: "1" },
  // { channelName: "短信", publishObj: "", isApply: false, code: "2" },
  // { channelName: "应急广播", publishObj: "", isApply: false, code: "3" }
  //   { channelName: "微信公众号", publishObj: "", isApply: false, code: '4' },
  //   { channelName: "APP", publishObj: "", isApply: false, code: '5' },
];
export default {
  name: "Safety",
  components: {},
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },

    checkLists: {
      type: Array,
      default: [],
    },

    chanList: {
      type: Array,
      default: [],
    },
   
    closable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      defaultProps: {
        children: "children",
        label: "label",
      },
      leftOrgData: [], //  短信的组织树
      leftOrgDataSys: [], //系统消息的组织树
      rightManList: [], //与会人员右边的列表
      rightManLists: [],
      rightTreeLoading: true,
      rightTreeLoadings: true,
      checkList: [],
      checkLists: [],
      // 遮罩层
      loading: true,
    };
  },
  computed: {
    channelList() {
      if (this.chanList.length > 0) {
        console.log("进来了");
        if (this.chanList[0].publishObj) {
          if (this.chanList[0].publishObj.constructor == Array) {
            if (this.chanList[0].publishObj.length > 0) {
              this.checkLists = [];
              this.chanList[0].publishObj.forEach((item) => {
                this.checkLists.push(item.name + ":" + item.code);
              });
            }
          }
        }
        if (this.chanList[1].publishObj) {
          if (this.chanList[1].publishObj.constructor == Array) {
            if (this.chanList[1].publishObj.length > 0) {
              this.checkList = [];
              this.chanList[1].publishObj.forEach((item) => {
                this.checkList.push(item.name + ":" + item.code);
              });
            }
          }
        }
        return this.chanList;
      } else {
        this.checkLists = [];
        this.checkList = [];
        this.$forceUpdate();
        // demochannelList[2].publishObj = ''
        return demochannelList;
      }
    },
  },

  created() {
    this.orgTreeByorgcodeFun(0, "");
    this.sysOrgTreeByorgcodeFun();
  },
  methods: {
    sysCheckChange(e, e1) {
      console.log(e1);
      let _array = [];
      e1.checkedNodes.forEach((item) => {
        _array.push(item.label + ":" + item.orgCode);
      });

      this.checkLists = _array;
      this.joinerChanges();
    },

    // 根据orgcode获取人员列表
    personListFun(orgCode) {
      personList({
        keyWord: "",
        newQueryType: "1",
        nowPage: 1,
        orgCode: orgCode,
        pageSize: 10000,
        personIds: "",
        queryType: "",
        updateTime: "",
      })
        .then((res) => {
          let _data = res.data.list;
          this.rightManList = _data;
          this.rightTreeLoading = false;
        })
        .catch((err) => {
          this.rightTreeLoading = false;
        });
    },

    handleNodeClick(data) {
      let _index = this.leftOrgData.findIndex((item) => item.id == data.id);
      orgTreeByorgcode({
        orgCode: data.id,
      })
        .then((res) => {
          if (res.data.length > 0) {
            this.leftOrgData[_index].children = [...res.data];
          }
        })
        .catch((err) => {
          console.log(err);
        });
      this.personListFun(data.id);
    },

    handleNodeClickSys(data) {
       let repeatData = (this.checkLists || []).find(
        (item) => item.orgCode === data.orgCode
      );
      if (repeatData && Object.keys(repeatData).length > 0) {
        this.$message.warning('该对象已选择~');
      } else {
        this.checkLists.push(data);
        this.$emit("selectList", this.checkLists);
      }
    },

    // 查询系统消息的组织树
    sysOrgTreeByorgcodeFun() {},

    // 根据当前用户的orgCode查询组织机构树
    orgTreeByorgcodeFun() {
      this.loading = true;
      // orgTreeByorgcode()
      //   .then((res) => {
      //     // this.leftOrgDataSys = this.handleSysData(res.data.data);
      //     this.leftOrgDataSys = res.data.data;
      //     this.loading = false;
      //     console.log('leftOrgDataSys++++++++++++++++++++++++++++++++++++++', this.leftOrgDataSys)
      //     // this.$forceUpdate();
      //   })
      //   .catch((err) => {
      //     console.log(err);
      //   });
      getOrgTreesN()
        .then((res) => {
          // this.leftOrgDataSys = this.handleSysData(res.data.data);
          this.leftOrgDataSys = res.data.data;
          this.loading = false;
          console.log('leftOrgDataSys++++++++++++++++++++++++++++++++++++++', this.leftOrgDataSys)
          // this.$forceUpdate();
        })
        .catch((err) => {
          console.log(err);
        });
    },

    tagCloses(e) {
      // console.log('aaaa')
      this.checkLists.splice(e, 1);
       this.$emit("selectList", this.checkLists);
      // console.log(this.checkLists)
     
      // let _array = [];
      // if (this.checkLists.length > 0) {
      //   this.checkLists.forEach((item) => {
      //     _array.push({
      //       code: item.orgCode,
      //       name: item.orgName,
      //     });
      //   });
      // }
      // this.channelList[0].publishObj = JSON.stringify(_array);
        // this.form.recvUserStrs = JSON.stringify(_array);
    },

    // 人员的change事件
    joinerChange(e) {
      let _array = [];
      this.checkList.forEach((item) => {
        _array.push({
          code: item.split(":")[1],
          name: item.split(":")[0],
          phone: item.split(":")[2],
        });
      });
      this.channelList[1].publishObj = JSON.stringify(_array);

      //   this.channelList[1].publishObj = _arrays.join(',');
    },
    joinerChanges(e) {
      let _array = [];
      this.checkLists.forEach((item) => {
        _array.push({
          code: item.split(":")[1],
          name: item.split(":")[0],
        });
      });
      this.channelList[0].publishObj = JSON.stringify(_array);
    },
    change(selVal) {},
    getList() {
      this.loading = true;
    },

    /** 修改按钮操作 */
    getTempleDetails(row) {
      const tmpltCode = row.tmpltCode;
      getTemple(tmpltCode).then((response) => {
        this.open = false;
        this.$emit("choosedTemplateInfo", response.data);
      });
    },

    rowClick(e) {
      this.getTempleDetails(e);
    },
  },
};
</script>

<style lang="scss" scoped>
.tableItem {
  cursor: pointer;
  &:hover {
    color: #1890ff;
  }
}

.pdTable {
  width: 100%;
  table {
    width: 100% !important;
  }
}
.title {
  font-size: 16px;
  color: #082754;
  margin: 20px 0 0;
  border-bottom: 2px solid #46a0fb;
  padding-bottom: 12px;
}
.manCheckedCon {
  min-height: 36px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  padding: 6px 0;
  display: flex;
  align-items: center;
}
.placeholderDiv {
  color: #c0c4cc;
  margin-left: 15px;
  display: flex;
  flex-wrap: wrap;
}
.manCheckedBox {
  margin-top: 10px;
}
.tagItem {
  margin-right: 6px !important;
  margin-bottom: 6px;
}
.leftTree {
  //width: 300px;
  height: 180px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  overflow: auto;
  padding: 6px 0;
  // margin-right: 30px;
  .auto-scrollbar {
    width: 100% !important;
  }
}

.rightTree {
  border-radius: 4px;
  // margin-left: 40px;
  width: 230px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  padding: 6px;
  .treeCheckBox {
    display: flex;
    flex-direction: column;
    text-align: left;
  }
}
.app-container .el-dialog {
  overflow-y: none !important;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
</style>
