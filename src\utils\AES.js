// import CryptoJS from 'crypto-js';
 
// export default {
//     //随机生成指定数量的16进制key
//     generatekey(num) {
//         let library = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
//         let key = "";
//         for (var i = 0; i < num; i++) {
//             let randomPoz = Math.floor(Math.random() * library.length);
//             key += library.substring(randomPoz, randomPoz + 1);
//         }
//         return key;
//     },
    
//     //加密
//     encrypt(word, keyStr) {
//         keyStr = keyStr ? keyStr : 'abcdsxyzhkj12345'; //判断是否存在ksy，不存在就用定义好的key
//         var key = CryptoJS.enc.Utf8.parse(keyStr);
//         var srcs = CryptoJS.enc.Utf8.parse(word);
//         var encrypted = CryptoJS.AES.encrypt(srcs, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
//         return encrypted.toString();
//     },
//     //解密
//     decrypt(word, keyStr) {
//         keyStr = keyStr ? keyStr : 'abcdsxyzhkj12345';
//         var key = CryptoJS.enc.Utf8.parse(keyStr);
//         var decrypt = CryptoJS.AES.decrypt(word, key, { mode: CryptoJS.mode.ECB, padding: CryptoJS.pad.Pkcs7 });
//         return CryptoJS.enc.Utf8.stringify(decrypt).toString();
//     }
 
// }

import CryptoJS from "crypto-js";

export default {
  // 加密
  encrypt(word, keyStr, ivStr) {
    keyStr = keyStr ? keyStr : "absoietlj32fai12";
    ivStr = ivStr ? ivStr : "absoietlj32fai12";
    let key = CryptoJS.enc.Utf8.parse(keyStr);
    let iv = CryptoJS.enc.Utf8.parse(ivStr);
    let srcs = CryptoJS.enc.Utf8.parse(word);

    let encrypted = CryptoJS.AES.encrypt(srcs, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.ZeroPadding
    });
    return encrypted.toString();
  },
  // 解密
  decrypt(word, keyStr, ivStr) {
    keyStr = keyStr ? keyStr : "absoietlj32fai12";
    ivStr = ivStr ? ivStr : "absoietlj32fai12";
    var key = CryptoJS.enc.Utf8.parse(keyStr);
    let iv = CryptoJS.enc.Utf8.parse(ivStr);

    var decrypt = CryptoJS.AES.decrypt(word, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.ZeroPadding
    });
    return decrypt.toString(CryptoJS.enc.Utf8);
  }
};