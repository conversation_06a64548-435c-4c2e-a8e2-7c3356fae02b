<template>
  <div class="table">
    <el-dialog
      title="菜单配置"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :close-on-click-modal="false"
    >
      <a-tree
        v-model="checkedKeys"
        checkable
        :tree-data="treeData"
        @select="onSelect"
        @check="onCheck"
        multiple
      />
      <div class="btnlist">
        <!-- <el-button type="primary" @click="allSelect"> 全选 </el-button> -->
        <el-button type="primary" @click="clean"> 清空 </el-button>
        <el-button type="primary" @click="save"> 保存 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getMenuTreeData,
  getMenuTreeDataId,
  getSaveRoleMenu,
} from "../../../api/role";
import { mapState } from "vuex";
import Bus from "../../../utils/bus";
export default {
  name: "MenuEdit",
  //import引入的组件
  components: {},
  props: {
    dialogTableVisiblees: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      loading: false,
      checkedKeys: [],
      allIds: [],
      id: "",
      checkIds: [],
      test: [],
    };
  },
  watch: {
    checkedKeys: {
      handler(val, old) {
        console.log("onCheck", val);
      },
      deep: true,
      immediate: true,
    },
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
      // console.log(this.$store.state.sa.SARoleListData)
    },
    clean() {
      this.checkedKeys = [];
    },
    allSelect() {
      console.log(this.checkedKeys);
      // this.checkedKeys = this.allIds;
    },
    save() {
      getSaveRoleMenu({
        roleId: this.id,
        systemCode: this.$store.state.sa.SARoleListData.systemCode,
        menuIds: this.checkIds.join(","),
      }).then((res) => {
        if (res.data.code == 0) {
          // this.submitting = false;
          this.$message({
            type: "success",
            message: "修改成功!",
          });
          this.tableVisible = false;
          this.$parent.fatherMethod();
        } else {
          this.$message({
            type: "info",
            message: res.data.msg,
          });
          return;
        }
      });
    },
    getData(systemCode, menuId) {
      this.loading = true;
      this.id = menuId;
      getMenuTreeDataId({ systemCode: systemCode, id: menuId })
        .then((res) => {
          //   this.checkedKeys = res.data.data;
          this.checkedKeys = this.uniqueTree(res.data.data, this.test);
          console.log("checkedKeys", this.checkedKeys);
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    getTreeData(systemCode) {
      this.allIds = [];
      getMenuTreeData({ systemCode: systemCode })
        .then((res) => {
          this.treeData = res.data.data;
          this.treeData.forEach((item) => {
            item.key = item.id;
            item.title = item.menuName;
            this.allIds.push(item.key);
            if (item.children.length > 0) {
              item.children.forEach((items) => {
                items.key = items.id;
                items.title = items.menuName;
              });
            }
          });
          this.requestList(this.treeData);
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    onExpand(expandedKeys) {
      console.log("onExpand", expandedKeys);
      // if not set autoExpandParent to false, if children expanded, parent can not collapse.
      // or, you can remove all expanded children keys.
      this.expandedKeys = expandedKeys;
      this.autoExpandParent = false;
    },
    onCheck(checkedKeys, e) {
      // debugger;
      //   console.log("onCheck", checkedKeys,"e",e);
      this.checkedKeys = checkedKeys;
      this.checkIds = checkedKeys.concat(e.halfCheckedKeys);
      //   console.log(this.checkIds )
    },
    onSelect(selectedKeys, info) {
      console.log("onSelect", info);
      this.selectedKeys = selectedKeys;
    },
    requestList(data) {
      data &&
        data.map((item) => {
          if (item.children && item.children.length > 0) {
            this.requestList(item.children);
          } else {
            this.test.push(item.id);
          }
          return null;
        });
      return this.test;
    },
    uniqueTree(uniqueArr, Arr) {
      let uniqueChild = [];
      for (var i in Arr) {
        for (var k in uniqueArr) {
          if (uniqueArr[k] === Arr[i]) {
            uniqueChild.push(uniqueArr[k]);
          }
        }
      }
      return uniqueChild;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
.btnlist {
  text-align: center;
}
.el-dialog__body {
  padding-top: 0;
}
.ant-tree {
  height: 330px;
  overflow: auto;
}
</style>
