<template>
  <div class="safety-check-list">
    <!-- 修改页面标题样式 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              统计报表管理
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 其他内容保持不变 -->
    <div class="content">
      <!-- 头部工具栏 -->
      <div class="tool-bar">
        <div class="left">
          <el-button-group>
            <el-button
              type="primary"
              size="mini"
              icon="el-icon-plus"
              @click="handleAdd"
            >
              新增报表
            </el-button>
          </el-button-group>
        </div>
        <div class="right">
          <el-form :inline="true" :model="searchForm">
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入报表名称"
                size="mini"
                clearable
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="mini" @click="handleSearch">
                搜索
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 报表列表 -->
      <el-table :data="tableData" border style="width: 100%">
        <!-- 添加序号列 -->
        <el-table-column type="index" label="序号" width="60" align="center" />

        <el-table-column prop="reportName" label="报表名称" min-width="200">
          <template slot-scope="scope">
            <span @click="goReport(scope.row)" class="report-name">
              {{ scope.row.reportName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="创建时间" width="180" />
        <el-table-column prop="updateDate" label="更新时间" width="180" />
        <el-table-column label="操作" width="200" fixed="right">
          <template slot-scope="scope">
            <!-- <el-button type="text" size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button> -->
            <el-button
              type="text"
              size="mini"
              class="delete-btn"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 字段设置弹窗 -->
    <field-settings-dialog
      :visible.sync="fieldSettingsVisible"
      :all-fields="allFields"
      :table-list="tableList"
      :is-new-report="true"
      @save="handleFieldSettingsSave"
      @update:tableList="handleTableListChange"
    />
  </div>
</template>

<script>
import FieldSettingsDialog from "../components/FieldSettingsDialog.vue";
import {
  getTableName,
  getTableFields,
  addReport,
  getReportList,
  deleteReport,
} from "@/api/smartReport";

export default {
  name: "CountReportList",
  components: {
    FieldSettingsDialog,
  },
  data() {
    return {
      searchForm: {
        keyword: "",
      },
      tableData: [
        {
          id: "1",
          name: "2024年生产安全事故查处情况表",
          createTime: "2024-01-15 10:00:00",
          updateTime: "2024-01-15 10:00:00",
          fields: [],
        },
      ],
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      fieldSettingsVisible: false,
      allFields: [],
      tableList: [],
    };
  },
  methods: {
    // 新增报表
    async handleAdd() {
      const res = await getTableName();
      if (res.status === 200) {
        this.tableList = res.data.data;
      }

      this.fieldSettingsVisible = true;
    },

    async handleTableListChange(value) {
      const fields = await getTableFields({
        tableName: value,
      });
      if (fields.status === 200) {
        this.allFields = fields.data.data;
      }
    },

    // 编辑报表
    handleEdit(row) {
      //
    },

    // 删除报表
    handleDelete(row) {
      this.$confirm("确认删除该报表?", "提示", {
        type: "warning",
      }).then(() => {
        // TODO: 调用删除接口
        deleteReport({
          id: row.id,
        }).then((res) => {
          if (res.status === 200) {
            this.$message.success("删除成功");
            this.getList();
          }
        });
      });
    },

    // 保存字段设置
    async handleFieldSettingsSave(settings) {
      // TODO: 调用保存接口
      const params = {
        reportContentCn: settings.selectedLabels.join(","),
        reportContentEn: settings.selectedFields.join(","),
        reportName: settings.reportName,
        reportTitleEn: settings.selectedTable,
        reportTitleCn: settings.selectedTableLabel,
      };
      const res = await addReport(params);
      if (res.status === 200) {
        this.$message.success("创建成功");
        this.getList();
      }
    },

    // 获取列表数据
    getList() {
      getReportList({
        nowPage: this.page.current,
        pageSize: this.page.size,
        reportName: this.searchForm.keyword,
      }).then((res) => {
        this.tableData = res.data.data.list;
        this.page.total = res.data.data.total;
      });
    },

    // 搜索
    handleSearch() {
      this.page.current = 1;
      this.getList();
    },

    // 分页处理
    handleSizeChange(val) {
      this.page.size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.page.current = val;
      this.getList();
    },

    // 修改跳转方法
    goReport(row) {
      this.$emit("goDetail", row);
    },
  },
  created() {
    this.getList();
  },
};
</script>

<style lang="scss" scoped>
.safety-check-list {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;
  }

  .tool-bar {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
  }

  .delete-btn {
    color: #f56c6c;
  }

  .pagination {
    margin-top: 20px;
    text-align: right;
  }

  .report-name {
    color: #3977ea;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>
