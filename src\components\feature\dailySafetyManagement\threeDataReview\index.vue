<template>
  <div class="threeDataReview">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              "三同时"资料复查
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div>
      <div class="seach-part">
        <div class="l">
          <el-input
            v-model.trim="projectName"
            placeholder="请输入项目名称关键字"
            class="input"
            clearable
            @clear="clearKey"
            style="width: 200px"
            size="mini"
          ></el-input>
          <!-- {{approvalStatusData}} -->
          <el-select
            v-model="approvalStatus"
            placeholder="请选择当前状态"
            clearable
            size="mini"
          >
            <el-option
              v-for="item in approvalStatusData"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <!-- <el-date-picker
            v-model="datePickerValue"
            clearable
            type="date"
            size="mini"
            value-format="yyyy-MM-dd"
            placeholder="年/月日"
            style="width: 200px"
          /> -->
          <el-date-picker
            v-model="date"
            size="mini"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            value-format="yyyy-MM-dd HH:mm:ss"
            unlink-panels
            clearable
          >
          </el-date-picker>

          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>“三同时”专家审查意见复查列表</h2>
          <el-button
            type="primary"
            size="mini"
            @click="openDetails('新增“三同时”资料复查')"
            v-if="user.user_type === 'ent'"
            >新增
            <!--            -->
          </el-button>
        </div>
        <div v-loading="loading">
          <div class="table">
            <el-table
              :data="tableData"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              style="width: 100%"
            >
              <!-- <el-table-column prop="i"
                               type="selection"
                               width="45"
                               fixed="left"
                               align="center">
              </el-table-column> -->
              <el-table-column
                prop="i"
                type="index"
                label="序号"
                width="60"
                align="center"
              >
              </el-table-column>

              <el-table-column
                prop="projectName"
                label="项目名称"
                align="center"
                width="300"
                :show-overflow-tooltip="true"
              >
                <!-- <template slot-scope="scope">
                  <el-button
                    @click="goEnt(scope.row)"
                    type="text"
                    class="enterpName"
                  >
                    {{ scope.row.orgName }}
                  </el-button>
                </template> -->
              </el-table-column>
              <el-table-column
                prop="projectTypeName"
                label="项目类型"
                align="center"
                width="110"
              >
              </el-table-column>
              <el-table-column
                prop="reportTypeName"
                label="报告类型"
                align="center"
                min-width="150"
                :show-overflow-tooltip="true"
              >
              </el-table-column>

              <el-table-column
                prop="signAttachList"
                label="专家签字报告"
                align="center"
                width="110"
              >
                <template slot-scope="{ row }">
                  <el-button
                    type="text"
                    v-if="row.signAttachList && row.signAttachList.length > 0"
                    @click="openImg(row)"
                    >查看附件</el-button
                  >
                </template>
              </el-table-column>

              <el-table-column
                prop="approvalStatusName"
                label="当前阶段"
                align="center"
                width="155"
              >
                <!-- <template slot-scope="{ row }">
                  <span v-if="row.contractor == 1">是</span>
                  <span v-else-if="row.contractor == 0">否</span>
                  <span v-else></span>
                </template> -->
              </el-table-column>

              <el-table-column
                prop="applyTime"
                label="申请时间"
                align="center"
                width="160"
              >
              </el-table-column>

              <el-table-column
                prop="passTime"
                label="完成时间"
                align="center"
                width="160"
              >
              </el-table-column>

              <el-table-column
                prop="i"
                label="操作"
                align="center"
                width="220"
                fixed="right"
              >
                <!-- 1和4是可以编辑删除的，其他不行 -->
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    @click="openDetails('查看“三同时”资料复查', scope.row)"
                    v-if="user.user_type === 'ent'"
                    >查看
                  </el-button>
                  <!-- approvalStatus true:置灰-->
                  <el-button
                    type="text"
                    @click="openDetails('编辑“三同时”资料复查', scope.row)"
                    v-if="user.user_type === 'ent'"
                    :disabled="
                      scope.row.approvalStatus == 2 ||
                      scope.row.approvalStatus == 3 ||
                      scope.row.approvalStatus == 5
                    "
                    >编辑
                  </el-button>
                  <el-button
                    type="text"
                    @click="clickHand(scope.row)"
                    :disabled="user.isAdmin != 1"
                  >
                    办理
                  </el-button>
                  <!-- 监管端:办理（驳回、受理）企业端：-->
                  <!-- 1:未提交 2:待处理 3:处理中 4:驳回 5:完成  --  这个在状态才能判断 -->
                  <el-button
                    type="text"
                    @click="abnormalEquipmentDel(scope.row)"
                    v-if="user.user_type === 'ent'"
                    :disabled="
                      scope.row.approvalStatus == 2 ||
                      scope.row.approvalStatus == 3 ||
                      scope.row.approvalStatus == 5
                    "
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              background
              layout="total, prev, pager, next"
              :total="total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1000px"
      top="5vh"
      @close="closeDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogInfo" v-loading="loading">
        <!-- <div v-if="title == '查看“三同时”资料复查'">当前状态：审核中</div> -->
        <div class="titStatus" v-if="title != '新增“三同时”资料复查'">
          <span>当前状态 ：</span>
          <div class="rejectBtn">{{ status }}</div>
          <!-- <div class="processBtn">受理中</div> -->
        </div>
        <el-form ref="ruleForm" :model="form" size="medium" :rules="rules">
          <el-descriptions :column="2" border :labelStyle="labelStyle">
            <el-descriptions-item
              :span="2"
              v-if="form.rejectInfo && title == '查看“三同时”资料复查'"
            >
              <template slot="label"> 驳回原因： </template>
              <template>
                <div style="text-align: left">
                  {{ form.rejectInfo || "无" }}
                </div>
              </template>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">企业名称：</template>
              <el-form-item>
                <el-input disabled v-model.trim="form.orgName"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">行政区划：</template>
              <el-form-item>
                <el-input disabled v-model.trim="form.districtName"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"
                ><span class="redDot">*</span>项目名称：</template
              >
              <el-form-item prop="projectName">
                <el-input
                  v-model.trim="form.projectName"
                  :disabled="disabled"
                  placeholder="请输入项目名称"
                  maxlength="40"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>项目类型：
              </template>
              <el-form-item prop="projectType">
                <el-select
                  v-model="form.projectType"
                  placeholder="请先选择项目类型"
                  :disabled="disabled"
                  clearable
                  style="width: 100%"
                  :loading="remoteSmall"
                >
                  <el-option
                    v-for="item in projectTypeAry"
                    :key="item.id"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>报告类型：
              </template>
              <el-form-item prop="reportType">
                <el-select
                  v-model="form.reportType"
                  placeholder="请选择报告类型"
                  clearable
                  :disabled="disabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in reportTypeAry"
                    :key="item.value"
                    :label="item.name"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>审批级别：
              </template>
              <el-form-item prop="approvalLevel">
                <el-select
                  v-model="form.approvalLevel"
                  placeholder="请选择审批级别"
                  clearable
                  :disabled="disabled"
                  style="width: 100%"
                >
                  <el-option label="省级" value="0"></el-option>
                  <el-option label="市级" value="1"></el-option>
                  <el-option label="区级" value="2"></el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label"
                ><span class="redDot">*</span>项目负责人：</template
              >
              <el-form-item prop="principal">
                <el-input
                  v-model.trim="form.principal"
                  :disabled="disabled"
                  placeholder="请输入项目负责人"
                  maxlength="20"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>负责人联系方式：
              </template>
              <el-form-item prop="contact">
                <el-input
                  v-model.trim="form.contact"
                  placeholder="请输入负责人联系方式"
                  clearable
                  maxlength="20"
                  :disabled="disabled"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label">
                <span class="redDot">*</span>电子报告：
              </template>
              <el-form-item prop="reportAttachList" class="uploadBox">
                <AttachmentUpload
                  :attachmentlist="form.reportAttachList"
                  :limit="1"
                  type="pdf"
                  v-bind="{}"
                  :messageTip="messageTip"
                  :editabled="disabled"
                  @resBack="resBackReportAttachList"
                ></AttachmentUpload>
                <!-- <span class="tipInfo"
                  >请上传企业盖章后的电子报告PDF扫描件，文件大小不超过30M</span
                > -->
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label">
                <span class="redDot">*</span>专家意见表：
              </template>
              <el-form-item prop="commentAttachList" class="uploadBox">
                <AttachmentUpload
                  :attachmentlist="form.commentAttachList"
                  :limit="1"
                  type="pdf"
                  v-bind="{}"
                  :messageTip="messageTip2"
                  @resBack="resBackCommentAttachList"
                  :editabled="disabled"
                ></AttachmentUpload>
                <!-- <span class="tipInfo">请上传PDF扫描件，文件大小不超过30M</span> -->
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label">
                <span class="redDot">*</span>修订对照表：
              </template>
              <el-form-item prop="reviseAttachList" class="uploadBox">
                <AttachmentUpload
                  :attachmentlist="form.reviseAttachList"
                  :limit="1"
                  type="pdf"
                  v-bind="{}"
                  :messageTip="messageTip3"
                  @resBack="resBackReviseAttachList"
                  :editabled="disabled"
                ></AttachmentUpload>
                <!-- <span class="tipInfo"
                  >请上传企业修订对照表的PDF扫描件，文件大小不超过30M</span
                > -->
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label">
                <div style="width: 120px; display: inline-block">附件：</div>
              </template>
              <el-form-item prop="otherAttachList" class="uploadBox">
                <AttachmentUpload
                  :attachmentlist="form.otherAttachList"
                  :limit="10"
                  type="docxPdfZip"
                  v-bind="{}"
                  :editabled="disabled"
                  :messageTip="messageTip4"
                ></AttachmentUpload>
                <!-- <span class="tipInfo"
                  >请上传word、pdf等其他补充材料，最多上传5个，单个文件大小不超过30M</span
                > -->
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item :span="2">
              <template slot="label"> 备注： </template>
              <el-form-item prop="remark">
                <el-input
                  type="textarea"
                  placeholder="请输入备注"
                  v-model.trim="form.remark"
                  :rows="4"
                  maxlength="500"
                  :disabled="disabled"
                  show-word-limit
                >
                </el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
      <div slot="footer" style="display: flex; justify-content: center">
        <el-button
          size="mini"
          @click="closeDialog()"
          v-if="title != '查看“三同时”资料复查'"
          >取 消</el-button
        >

        <!-- 保存的相当于是个草稿 -->
        <el-button
          size="mini"
          @click="submit(1)"
          v-if="
            title === '编辑“三同时”资料复查' || title === '新增“三同时”资料复查'
          "
          >保 存</el-button
        >

        <!-- 提交以后就是不能编辑和删除了 -->
        <el-button
          size="mini"
          type="primary"
          @click="submit(2)"
          v-if="
            title === '编辑“三同时”资料复查' || title === '新增“三同时”资料复查'
          "
          >提 交</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="专家签字报告"
      :visible.sync="dialogImg"
      width="600px"
      top="5vh"
      @close="dialogFileSrc = []"
      :close-on-click-modal="false"
    >
      <AttachmentUpload
        :attachmentlist="dialogFileSrc"
        :limit="5"
        type="all"
        :editabled="true"
      ></AttachmentUpload>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
import {
  getCompanyProjectList, //列表查询
  getCompanyProjectFindById, //查询详情
  getCompanyProjectSave, //新增
  getCompanyProjectDelete, //删除
  getCompanyProjectUpdate, //修改
  getStatusList, //状态
  getCompanyProjectApply, //提交
  getCompanyParticularJobList,
} from "@/api/companyParticularJob";
import downloadFuc from "../../../../api/download/download";
import { getfindByEnterpId, getEnterpriseId } from "@/api/user";
import { getCompanyParticularJobTypeByParentCode } from "@/api/companyParticularJob";
import { getEnt } from "@/api/dailySafety";
import { getExpertTypeData } from "@/api/mergencyResources";
// import { parseTime } form "../../../../utils";
export default {
  name: "threeDataReview",
  components: {
    AttachmentUpload,
  },
  data() {
    return {
      messageTip: "请上传企业盖章后的电子报告pdf扫描件文件,大小不超过50M",
      messageTip2: "请上传pdf扫描件，文件大小不超过50M",
      messageTip3:
        "请上传企业修订对照表的pdf原始文件:(建议不使用扫描文件)，文件大小不超过50M",
      messageTip4:
        "请上传docx、pdf、zip、rar补充材料，最多上传10个，单个文件大小不超过50M",
      status: "",
      projectName: "", //查询- 项目名称
      approvalStatus: "", //查询-项目状态
      //  date: [
      //   new Date(new Date().toLocaleDateString()).getTime() -
      //     (720 * 60 * 60 * 1000 - 1),
      //   new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      // ],
      date: [],
      startTime: "",
      endTime: "",
      title: "", //弹框标题
      dialogVisible: false, //弹框显示
      projectTypeAry: [], //项目类型
      reportTypeAry: [], //报告类型
      disabled: true,
      approvalStatusData: [],
      labelStyle: {
        textAlign: "center",
        backgroundColor: "rgb(242, 246, 255)",
      },
      rules: {
        projectName: [
          { required: true, message: "项目名称不能为空", trigger: "blur" },
        ],
        projectType: [
          { required: true, message: "项目类型不能为空", trigger: "change" },
        ],
        reportType: [
          { required: true, message: "报告类型不能为空", trigger: "change" },
        ],
        approvalLevel: [
          { required: true, message: "审批级别不能为空", trigger: "change" },
        ],
        principal: [
          { required: true, message: "项目负责人不能为空", trigger: "blur" },
        ],
        //  contact: [
        //   { required: true, validator: checkPhone2, trigger: 'blur' }
        // ],
        contact: [
          {
            required: true,
            message: "负责人联系方式不能为空",
            trigger: "blur",
          },
          {
            pattern: /^1[3|4|5|6|7|8|9][0-9]{9}$/,
            message: "请输入正确的电话号码",
          },
        ],
        reportAttachList: [
          { required: true, message: "电子报告不能为空", trigger: "blur" },
        ],
        commentAttachList: [
          { required: true, message: "专家意见表不能为空", trigger: "blur" },
        ],
        reviseAttachList: [
          { required: true, message: "修订对照表不能为空", trigger: "blur" },
        ],
      },
      loading: false,
      btnLoading: false,
      selection: [],
      tableData: [],
      currentPage: 1,
      size: 10,
      total: 0,
      form: {
        projectType: "", //项目类型：
        reportType: "",
        projectName: "", //项目名称
        contact: "", //负责人联系方式：
        principal: "", //项目负责人：
        approvalLevel: "", //审批级别
        reportAttachList: [], //电子报告：
        commentAttachList: [], //专家意见表：
        reviseAttachList: [], //修订对照表：
        otherAttachList: [], //附件：
        remark: "", //备注：
        districtCode: "", //行政区划
        enterpId: "", //企业id
        infoId: "", //主键
        creditCode: "", //统一社会信用代码
        orgName: "",
        districtName: "",
        orgCode: "",
        districtCode: "",
      },
      dialogFileSrc: "",
      dialogImg: false,
      administrativeList: [],
      remoteSmall: false,
      remoteBig: false,
    };
    var checkPhone2 = (rule, value, callback) => {
      const phoneReg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
      if (!value) {
        return callback(new Error("负责人联系方式不能为空"));
      }
      setTimeout(() => {
        if (!Number.isInteger(+value)) {
          callback(new Error("请输入数字值"));
        } else {
          if (phoneReg.test(value)) {
            callback();
          } else {
            callback(new Error("联系电话格式不正确"));
          }
        }
      }, 100);
    };
  },
  methods: {
    resBackReportAttachList(res) {
      this.$refs["ruleForm"].clearValidate(["reportAttachList"]);
    },
    resBackCommentAttachList() {
      this.$refs["ruleForm"].clearValidate(["commentAttachList"]);
    },
    resBackReviseAttachList() {
      this.$refs["ruleForm"].clearValidate(["reviseAttachList"]);
    },
    searchTime(value) {
      //  value-format="yyyy-MM-dd HH:mm:00"
      // this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      // this.endDate = new Date(
      //   new Date(this.date[1].getTime() + 86399900)
      // ).Format("yy-MM-dd hh:mm:ss");
    },
    //信息复核状态列表下拉框
    serchStatusList() {
      getStatusList().then((res) => {
        if (res.data.status == 200) {
          this.approvalStatusData = res.data.data;
        }
      });
    },
    getSelectInt() {
      //值列表-项目类型
      getExpertTypeData({ dicCode: "PROJECT_TYPE" }).then((res) => {
        if (res.data.status == 200) {
          this.projectTypeAry = res.data.data;
        }
      }),
        //值列表-报告类型
        getExpertTypeData({ dicCode: "PROJECT_REPORT_TYPE" }).then((res) => {
          if (res.data.status == 200) {
            this.reportTypeAry = res.data.data;
          }
        });
    },

    // getCompanyParticularJobTypeFun() {
    //   this.remoteBig = true;
    //   getCompanyParticularJobType().then((res) => {
    //     this.remoteBig = false;
    //     this.ReportTypeOpt = res.data.data.map((item, index) => {
    //       return { id: item.id, label: item.label };
    //     });
    //     // console.log(this.ReportTypeOpt,'');
    //   });
    // },
    //打开文件
    openImg(img) {
      // img.signAttachList.forEach((item, index) => {
      //   if (!item.url.includes("http://***********:8090")) {
      //     item.url = "http://***********:8090" + item.url;
      //   }
      // });
      this.dialogFileSrc = img.signAttachList;
      this.dialogImg = true;
    },
    //根据code查找作业类型
    // jobBigTypeFilter(value) {
    //   let res = this.ReportTypeOpt.find((item, index) => {
    //     return value === item.value;
    //   });
    //   return res.label;
    // },

    //删除
    abnormalEquipmentDel(row) {
      this.$confirm("确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getCompanyProjectDelete({ infoId: row.infoId }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success(res.data.data);
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getData();
            } else {
              this.$message.error(res.data.data);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },

    // filterJobBigType(value, clock) {
    //   if (clock) {
    //     this.form.jobSmallType = "";
    //   }
    //   this.remoteSmall = true;
    //   getCompanyParticularJobTypeByParentCode({ parentCode: value }).then(
    //     (res) => {
    //       this.remoteSmall = false;
    //       this.jobSmallTypeOpt = res.data.data;
    //     }
    //   );
    // },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        // console.log(this.administrativeList, data)
        const administrative = this.administrativeList.find((item, index) => {
          return item.label === data.addressComponent.county;
        });
        if (administrative) {
          this.form.amnstDvsn = administrative.id;
        } else {
          this.form.amnstDvsn = "";
        }
        // console.log(administrative);
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        // this.form.dtlLocaltion = data.formatted_address
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
    //清空
    closeDialog() {
      this.form = {
        projectType: "", //项目类型：
        reportType: "",
        contact: "", //负责人联系方式：
        principal: "", //项目负责人：
        reportAttachList: [], //电子报告：
        commentAttachList: [], //专家意见表：
        reviseAttachList: [], //修订对照表：
        otherAttachList: [], //附件：
        remark: "", //备注：
        districtCode: "", //行政区划
        enterpId: "", //企业id
        infoId: "", //主键
        creditCode: "", //统一社会信用代码
        orgName: "",
        districtName: "",
        orgCode: "",
        districtCode: "",
      };
      this.dialogVisible = false;
    },

    submit(type) {
      //2:提交  1：保存
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          // this.form.jobType = this.form.jobBigType;
          if (type == 1) {
            if (this.title === "编辑“三同时”资料复查") {
              getCompanyProjectUpdate(this.form).then((res) => {
                if (res.data.status === 200) {
                  //this.$message.success(res.data.data);
                  this.$message.success("保存成功");
                  this.dialogVisible = false;
                  this.getData();
                } else {
                  this.$message.error(res.data.msg);
                }
              });
            } else if (this.title === "新增“三同时”资料复查") {
              getCompanyProjectSave(this.form).then((res) => {
                if (res.data.status === 200) {
                  // this.$message.success(res.data.data);
                  this.$message.success("保存成功");
                  this.dialogVisible = false;
                  this.getData();
                } else {
                  this.$message.error(res.data.msg);
                }
              });
            }
          } else if (type == 2) {
            getCompanyProjectApply(this.form).then((res) => {
              if (res.data.status === 200) {
                // this.$message.success(res.data.data);
                this.$message.success("提交成功");
                this.dialogVisible = false;
                this.getData();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        } else {
          this.$scrollToError();
          return false;
        }
      });
    },
    //获取企业id
    getEntData() {
      this.loading = true;
      getEnterpriseId({ id: this.user.org_code }).then((res) => {
        this.loading = false;
        this.form.orgName = res.data.data.qymc;
        this.form.districtName = res.data.data.districtName;
        this.form.districtCode = res.data.data.districtCode;
      });

      if (this.$store.state.login.user.user_type == "ent") {
        getEnt({}).then((resID) => {
          if (resID.data.code == 0) {
            this.form.enterpId = resID.data.data.enterpId;
            this.form.creditCode = resID.data.data.entcreditCode;
          }
        });
      } else {
      }
    },

    openDetails(title, row) {
      this.title = title;
      // this.getCompanyParticularJobTypeFun();
      switch (title) {
        case "新增“三同时”资料复查":
          this.disabled = false;
          this.form.orgName = this.user.org_name;
          this.form.orgCode = this.user.org_code;
          this.form.districtName = this.userDistCode;
          this.getEntData();
          // this.filterJobBigType("420000", false);
          break;
        case "查看“三同时”资料复查":
          this.disabled = true;
          this.loading = true;
          this.getCompanyParticularJobFun(row);
          break;
        case "编辑“三同时”资料复查":
          this.disabled = false;
          // this.form.district_code = this.userDistCode;
          this.getCompanyParticularJobFun(row);
          break;
        default:
          break;
      }
      this.dialogVisible = true;
    },
    clickHand(row) {
      this.$router.push({
        path: "/dailySafetyManagement/handleProcedures",
        query: {
          infoId: row.infoId,
          approvalStatus: row.approvalStatus,
          checkFlag: row.checkFlag,
        },
      });
    },
    //查看详情
    getCompanyParticularJobFun(row) {
      this.loading = true;
      getCompanyProjectFindById({ infoId: row.infoId }).then((res) => {
        this.form = res.data.data;
        this.status = res.data.data.approvalStatusName;
        this.form.orgName = res.data.data.enterpName;
        this.loading = false;
      });
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpid);
    },

    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {},
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData();
    },
    clearKey() {
      this.enterpName = "";
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    handleSelectionChange(val) {},
    handleClick() {},
    //列表查询
    getData() {
      this.loading = true;
      const dto = {
        projectName: this.projectName,
        approvalStatus: this.approvalStatus,
        // startTime:'',
        // endTime:'',
        startTime: this.date ? this.date[0] : "",
        endTime: this.date ? this.date[1] : "",
        nowPage: this.currentPage,
        pageSize: 10,
      };
      if (this.user.user_type === "ent") {
        // dto.orgCode = this.$store.state.login.user.org_code;
      } else {
        // dto.orgName = this.enterpName;
        // dto.districtCode = this.districtVal;
        dto.approvalLevel = this.user.distRole;
      }
      getCompanyProjectList(dto).then((res) => {
        //getCompanyParticularJobList   //getCompanyProjectList
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    this.getSelectInt();
    this.serchStatusList();
    // this.getCompanyParticularJobTypeFun();
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
      district: (state) => state.controler.district,
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {
    enterData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.form.jobName = newVal.enterpName;

          // this.form.orgCode = newVal.districtCode;
        }
      },
      deep: true,
      immediate: true,
    },
    "form.jobEndTime": {
      handler(newVal, oldVal) {
        if (newVal === null) {
          this.form.jobEndTime = "";
        }
      },
    },
    "form.jobStartTime": {
      handler(newVal, oldVal) {
        if (newVal === null) {
          this.form.jobStartTime = "";
        }
      },
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-form-item.uploadBox.el-form-item--medium {
  width: 820px;
}
/deep/ .slot_img,
/deep/ .slot_file {
  width: 200px;
}
.dialogInfo {
  .titStatus {
    display: flex;
    height: 50px;
    align-items: center;
    margin-bottom: 10px;
    margin-left: 133px;
    font-size: 16px;
    // margin-top: -15px;
  }
  .titStatus span {
    border-left: 4px solid #409eff;
    padding: 0 0 0 10px;
    color: #252525;
  }
  .rejectBtn {
    width: 74px;
    height: 30px;
    background: #f8e8e8;
    line-height: 30px;
    text-align: center;
    margin: 0 10px;
    color: #c43129;
    border-radius: 3px;
  }
  .processBtn {
    width: 74px;
    height: 30px;
    background: #eaf0ff;
    line-height: 30px;
    text-align: center;
    color: #3e76ff;
  }
}
/deep/ .el-descriptions .is-bordered .el-descriptions-item__cell {
  padding: 0 0 25px 0 !important;
  background-color: #fff !important;
  text-align: right !important;
  color: #252525;
}
/deep/ .el-form-item {
  margin-bottom: 0;
  margin-top: 0;
}

/deep/ .el-form-item__error {
  padding-top: 2px;
}
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
/deep/ .l .el-input {
  width: 150px;
}
/deep/ .el-image__preview {
  cursor: zoom-in;
}
/deep/ .el-descriptions .is-bordered .el-descriptions-item__cell {
  border: 0;
  padding: 12px 10px;
}
/deep/ .el-form-item.uploadBox.el-form-item--medium .el-form-item__content {
  text-align: left !important;
}
.redDot {
  color: red;
}

.threeDataReview {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }

  .icon {
    color: #6f81b5;
    font-size: 15px;
  }

  .seach-part {
    width: 100%;
    font-weight: 600;
    padding-bottom: 20px;
    margin-bottom: 0px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .l {
      display: flex;
      justify-content: flex-start;

      > * {
        margin-right: 15px;
      }
    }
  }

  .table-main {
    background: #fff;

    .table-top {
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
        float: left;
      }

      button {
        float: right;
        margin-top: 10px;
      }
    }

    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
.dialog {
  height: 65vh;
  overflow-y: scroll;
}
/deep/ .uploadBox .el-form-item__content > div {
  display: inline-block;
}
/deep/ .uploadBox .el-form-item__content > div .tipInfo {
  color: #ccc;
}
/deep/ .is-disabled.el-button--text {
  color: #ccc;
}
</style>
<style></style>
