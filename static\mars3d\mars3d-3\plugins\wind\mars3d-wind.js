/**
 * Mars3D平台插件,支持气象 风向图 功能插件  mars3d-wind
 *
 * 版本信息：v3.4.0
 * 编译日期：2022-07-15 14:35:43
 * 版权所有：Copyright by 火星科技  http://mars3d.cn
 * 使用单位：免费公开版 ，2022-06-01
 */
(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, (window.mars3d || require('mars3d'))) :
  typeof define === 'function' && define.amd ? define(['exports', 'mars3d'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global["mars3d-wind"] = {}, global.mars3d));
})(this, (function (exports, mars3d) { 
'use strict';var _0x2b2e19=_0x5b8f;(function(_0x107a11,_0x5bd76b){var _0x8ca6da=_0x5b8f,_0x438480=_0x107a11();while(!![]){try{var _0x305325=parseInt(_0x8ca6da(0x144))/0x1*(parseInt(_0x8ca6da(0x203))/0x2)+-parseInt(_0x8ca6da(0x16c))/0x3+parseInt(_0x8ca6da(0x1f6))/0x4*(-parseInt(_0x8ca6da(0xca))/0x5)+-parseInt(_0x8ca6da(0x1d6))/0x6+-parseInt(_0x8ca6da(0x130))/0x7*(parseInt(_0x8ca6da(0xc9))/0x8)+parseInt(_0x8ca6da(0x1c2))/0x9*(-parseInt(_0x8ca6da(0x102))/0xa)+parseInt(_0x8ca6da(0x103))/0xb*(parseInt(_0x8ca6da(0xd3))/0xc);if(_0x305325===_0x5bd76b)break;else _0x438480['push'](_0x438480['shift']());}catch(_0x5e224e){_0x438480['push'](_0x438480['shift']());}}}(_0x1c89,0x71b7d));function _0x5b8f(_0x2cd2dd,_0x1b4c1e){var _0x1c89d9=_0x1c89();return _0x5b8f=function(_0x5b8fe2,_0x4a0d9d){_0x5b8fe2=_0x5b8fe2-0xb4;var _0x48d849=_0x1c89d9[_0x5b8fe2];if(_0x5b8f['OCqmiu']===undefined){var _0x51dd56=function(_0x488861){var _0x4b3fbb='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0x3616b0='',_0x5b5ea6='';for(var _0x5c98e5=0x0,_0x2641de,_0x5da8c3,_0x5e5e5c=0x0;_0x5da8c3=_0x488861['charAt'](_0x5e5e5c++);~_0x5da8c3&&(_0x2641de=_0x5c98e5%0x4?_0x2641de*0x40+_0x5da8c3:_0x5da8c3,_0x5c98e5++%0x4)?_0x3616b0+=String['fromCharCode'](0xff&_0x2641de>>(-0x2*_0x5c98e5&0x6)):0x0){_0x5da8c3=_0x4b3fbb['indexOf'](_0x5da8c3);}for(var _0x491881=0x0,_0x3bc78a=_0x3616b0['length'];_0x491881<_0x3bc78a;_0x491881++){_0x5b5ea6+='%'+('00'+_0x3616b0['charCodeAt'](_0x491881)['toString'](0x10))['slice'](-0x2);}return decodeURIComponent(_0x5b5ea6);};_0x5b8f['HjTxDi']=_0x51dd56,_0x2cd2dd=arguments,_0x5b8f['OCqmiu']=!![];}var _0x4b30ed=_0x1c89d9[0x0],_0x2c629e=_0x5b8fe2+_0x4b30ed,_0x3dd03e=_0x2cd2dd[_0x2c629e];return!_0x3dd03e?(_0x48d849=_0x5b8f['HjTxDi'](_0x48d849),_0x2cd2dd[_0x2c629e]=_0x48d849):_0x48d849=_0x3dd03e,_0x48d849;},_0x5b8f(_0x2cd2dd,_0x1b4c1e);}function _interopNamespace(_0x488861){var _0x210a0d=_0x5b8f;if(_0x488861&&_0x488861[_0x210a0d(0x1d9)])return _0x488861;var _0x4b3fbb=Object[_0x210a0d(0x20c)](null);return _0x488861&&Object['keys'](_0x488861)[_0x210a0d(0x1b8)](function(_0x3616b0){var _0x1bd884=_0x210a0d;if(_0x3616b0!==_0x1bd884(0x17b)){var _0x5b5ea6=Object[_0x1bd884(0x124)](_0x488861,_0x3616b0);Object['defineProperty'](_0x4b3fbb,_0x3616b0,_0x5b5ea6[_0x1bd884(0x204)]?_0x5b5ea6:{'enumerable':!![],'get':function(){return _0x488861[_0x3616b0];}});}}),_0x4b3fbb['default']=_0x488861,_0x4b3fbb;}var mars3d__namespace=_interopNamespace(mars3d),Cesium$7=mars3d__namespace[_0x2b2e19(0x232)];function getU(_0x5c98e5,_0x2641de){var _0x435d1c=_0x2b2e19,_0x5da8c3=_0x5c98e5*Math[_0x435d1c(0x18b)](Cesium$7[_0x435d1c(0x12b)][_0x435d1c(0xdf)](_0x2641de));return _0x5da8c3;}function getV(_0x5e5e5c,_0x491881){var _0x21cab5=_0x2b2e19,_0x3bc78a=_0x5e5e5c*Math[_0x21cab5(0x212)](Cesium$7[_0x21cab5(0x12b)][_0x21cab5(0xdf)](_0x491881));return _0x3bc78a;}function getSpeed(_0x15a483,_0x185208){var _0x24719e=_0x2b2e19,_0x5e4210=Math[_0x24719e(0xbb)](Math[_0x24719e(0x119)](_0x15a483,0x2)+Math[_0x24719e(0x119)](_0x185208,0x2));return _0x5e4210;}function getDirection(_0x111858,_0x13c035){var _0x20857a=_0x2b2e19,_0x1da02b=Cesium$7[_0x20857a(0x12b)][_0x20857a(0x109)](Math[_0x20857a(0x111)](_0x13c035,_0x111858));return _0x1da02b+=_0x1da02b<0x0?0x168:0x0,_0x1da02b;}var WindUtil={'__proto__':null,'getU':getU,'getV':getV,'getSpeed':getSpeed,'getDirection':getDirection};function ownKeys(_0x34bbb5,_0xab5b5c){var _0x4f9b0c=_0x2b2e19,_0x267e63=Object[_0x4f9b0c(0x1ec)](_0x34bbb5);if(Object[_0x4f9b0c(0x161)]){var _0x2baffd=Object[_0x4f9b0c(0x161)](_0x34bbb5);_0xab5b5c&&(_0x2baffd=_0x2baffd[_0x4f9b0c(0x213)](function(_0xa7eec4){var _0x442981=_0x4f9b0c;return Object[_0x442981(0x124)](_0x34bbb5,_0xa7eec4)[_0x442981(0x1bf)];})),_0x267e63[_0x4f9b0c(0xb6)][_0x4f9b0c(0x140)](_0x267e63,_0x2baffd);}return _0x267e63;}function _objectSpread2(_0x5181ab){var _0x3c70bf=_0x2b2e19;for(var _0x2fa367=0x1;_0x2fa367<arguments[_0x3c70bf(0x1a7)];_0x2fa367++){var _0xac2d6a=null!=arguments[_0x2fa367]?arguments[_0x2fa367]:{};_0x2fa367%0x2?ownKeys(Object(_0xac2d6a),!0x0)[_0x3c70bf(0x1b8)](function(_0x25af02){_defineProperty(_0x5181ab,_0x25af02,_0xac2d6a[_0x25af02]);}):Object['getOwnPropertyDescriptors']?Object[_0x3c70bf(0xbc)](_0x5181ab,Object[_0x3c70bf(0x215)](_0xac2d6a)):ownKeys(Object(_0xac2d6a))[_0x3c70bf(0x1b8)](function(_0x2c01c7){var _0x3375a7=_0x3c70bf;Object[_0x3375a7(0x1e2)](_0x5181ab,_0x2c01c7,Object[_0x3375a7(0x124)](_0xac2d6a,_0x2c01c7));});}return _0x5181ab;}function _classCallCheck(_0x517338,_0x500ae1){var _0x25a3e7=_0x2b2e19;if(!(_0x517338 instanceof _0x500ae1))throw new TypeError(_0x25a3e7(0x184));}function _defineProperties(_0x420c84,_0x3d1d0f){var _0x56ce89=_0x2b2e19;for(var _0xc7e9ff=0x0;_0xc7e9ff<_0x3d1d0f[_0x56ce89(0x1a7)];_0xc7e9ff++){var _0xba0fed=_0x3d1d0f[_0xc7e9ff];_0xba0fed[_0x56ce89(0x1bf)]=_0xba0fed[_0x56ce89(0x1bf)]||![],_0xba0fed[_0x56ce89(0x134)]=!![];if(_0x56ce89(0x142)in _0xba0fed)_0xba0fed[_0x56ce89(0x104)]=!![];Object['defineProperty'](_0x420c84,_0xba0fed[_0x56ce89(0xf5)],_0xba0fed);}}function _createClass(_0x4d09ce,_0x142a1e,_0x1ffb6a){var _0x4bc9c2=_0x2b2e19;if(_0x142a1e)_defineProperties(_0x4d09ce[_0x4bc9c2(0x1b3)],_0x142a1e);if(_0x1ffb6a)_defineProperties(_0x4d09ce,_0x1ffb6a);return Object[_0x4bc9c2(0x1e2)](_0x4d09ce,_0x4bc9c2(0x1b3),{'writable':![]}),_0x4d09ce;}function _defineProperty(_0x39e476,_0x264887,_0x155ddf){return _0x264887 in _0x39e476?Object['defineProperty'](_0x39e476,_0x264887,{'value':_0x155ddf,'enumerable':!![],'configurable':!![],'writable':!![]}):_0x39e476[_0x264887]=_0x155ddf,_0x39e476;}function _0x1c89(){var _0x386b2c=['ywrKrxzLBNrmAxn0zw5LCG','sw52ywXPzcbHDhrLBxb0ihrVihnWCMvHzcbUB24TAxrLCMfIBguGAw5ZDgfUy2uUcKLUig9YzgvYihrVigjLigL0zxjHyMXLlcbUB24TyxjYyxKGB2jQzwn0CYbTDxn0igHHDMuGysbBu3LTyM9SlML0zxjHDg9YxsGPig1LDgHVzc4','CMf3uMvUzgvYu3rHDgu','y3jLyxrLugfYDgLJBgvZvgv4DhvYzxm','C3fYDa','zgvMAw5LuhjVCgvYDgLLCW','ywrKuhjPBwL0AxzLCW','y29UC3rYDwn0','uhjPBwL0AxzLq29SBgvJDgLVBG','AgvPz2H0','Bw91C2vvCa','y3jLyxrLq29TChv0Aw5NuhjPBwL0AxzLCW','y3vYCMvUDfrYywLSCW','z2v0ugL4zwXtAxPL','reLtqujmrv9htf9qt1njveLptL9mt0DFrevqveG','x3rVBwfW','u2HHzgvYuhjVz3jHBq','uKDc','mtzlCfPdv3u','nwnfvKvZAG','uMvJDgfUz2XL','Bwf4','vu5tsuDorurFqLLurq','zML4zwrizwLNAhq','Bw91C2vnB3zL','Dw5PzM9YBsbZyw1WBgvYmKqGy29SB3juywjSztSkcNzHCNLPBMCGzMXVyxqGC3bLzwroB3jTywXPEMf0Aw9UoWOkDM9PzcbTywLUkcKGEWOGicaGz2XFrNjHz0nVBg9Yid0GDgv4DhvYztjekgnVBg9YvgfIBguSihzLyZiOC3bLzwroB3jTywXPEMf0Aw9UlcaWlJaPktSkFq','qxjNDw1LBNrZ','CMvK','ody0y0fTAMPO','zwfZDa','zhjHD2LUz0j1zMzLCLDPzhrO','Ew1PBG','yMXLBMrPBMC','z2v0q29SB3juzxH0DxjL','Bgf0','zgvMAw5Lza','yxv0B0nSzwfY','D2HLzwW','teLorufs','ywrK','Dg9sywrPyw5Z','qebPDgvYyxrVCG','z2v0q29UDgv4Da','z2vVBwv0CNK','BgLNAhrLCG','DhjHAwXZ','yxjYyxK','CMvTB3zL','ChjLuMvUzgvY','x2nHBNjLzNjLC2G','zgf0yq','y29UDgfPBMvY','y3vYCMvUDfbHCNrPy2XLC1bVC2L0Aw9U','zNjVBurLz3jLzxm','x29Utw91C2vvCev2zw50','zNjHBwvIDwzMzxjZ','C3rYB2TLu3r5Bgu','zNjVBunZC0nVBg9Yu3rYAw5N','Cg9ZDfbYB2nLC3nPBMDtCgvLza','u2HHzgvYu291CMnL','v2LUzfv0AwW','BMv4DfrYywLSCW','A2v5','CMvZAxPL','BMv4DfbHCNrPy2XLC1nWzwvK','CMvNAxn0zxi','C2v0rgf0yq','Cg9ZAxrPB24','v01tx1vsta','Bw92zvrV','vMvYDgv4qxjYyxK','ugfZCW','BMv4DfbHCNrPy2XLC1bVC2L0Aw9U','x3bVAw50zxjfDMvUDhm','Dw5PzM9YBsbZyw1WBgvYmKqGDhjHAwXZq29SB3juzxH0DxjLoW0kDw5PzM9YBsbZyw1WBgvYmKqGDhjHAwXZrgvWDgHuzxH0DxjLoW0kdqP2yxj5Aw5NihzLyZiGDgv4DhvYzunVB3jKAw5HDgu7dqOncNzVAwqGBwfPBIGPihSncIaGicb2zwm0ihrYywLSC0nVBg9Yid0GDgv4DhvYztjekhrYywLSC0nVBg9Yvgv4DhvYzsWGDgv4DhvYzunVB3jKAw5HDguPoW0kicaGigzSB2f0ihrYywLSC0rLChrOid0GDgv4DhvYztjekhrYywLSC0rLChrOvgv4DhvYzsWGDgv4DhvYzunVB3jKAw5HDguPlNi7dqOGicaGzMXVyxqGz2XVyMvezxb0Aca9ign6Bv91BNbHy2Tezxb0AcH0zxH0DxjLmKqOy3PTx2DSB2jLrgvWDgHuzxH0DxjLlcb0zxH0DxjLq29VCMrPBMf0zsKPoW0kdqOGicaGAwyGkhrYywLSC0rLChrOidWGz2XVyMvezxb0AcKGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsb0CMfPBhndB2XVCJSncIaGicb9igvSC2uGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsb2zwm0kdaUmcK7dqOGicaGFq0kFq','mZe4odbdvfzPz0m','mtyZmdqYugXIA1bc','D3jPDgfIBgu','vfDpx1bj','Bw91C2vFzg93BG','u2v0','C2vNBwvUDhnezxb0Aa','Dg9ezwDYzwvZ','x29UtwfWx3bYzvjLBMrLCKv2zw50','CMfUzg9T','zNjVBunHy2HL','reLtqujmrv9mt0DFrevqveHFrLjbr01ftLrFv1jjveu','zNjHBwvuAw1L','x29UtwfWv2HLBgXfDMvUDa','C3rYAw5N','yxrHBJi','CgfYDgLJBgvZq29TChv0Aw5N','Dg9tDhjPBMC','y3jLyxrLuMvUzgvYAw5NrNjHBwvIDwzMzxjZ','ywDL','y2fUDMfZv2LUza','zgvZDgLUyxrPB24TAw4','Cg9ZDfbYB2nLC3nPBMDqB3nPDgLVBG','Cg93','vgv4DhvYzq','mhb4','ChjPBwL0AxzLCW','u2fTCgXLCG','y2fUDMfZuMvZAxPL','C3bLzwrgywn0B3i','y2fUDMfZv2LKDgG','Bwf4qwDL','DwrHDge','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','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9Y','DxbKyxrLu3bLzwq','DgXUzW','CMv2zxjZzvK','zhjHD1DPBMq','q2XLyxjdB21Tyw5K','DMvYDgv4u2HHzgvYu291CMnL','twf0Aa','z2v0uhjVDg90ExbLt2y','r2vVBwv0CNK','wKvstW','y2XHBxbuB0XHDgL0DwrLuMfUz2u','otiZm1vVBMDlwa','rNjHBwvIDwzMzxi','Bgf0uMfUz2u','D2LUzezPzwXK','y29UzMLNDxjHyMXL','C2v0t3b0Aw9UCW','Bw91C2veB3DU','Dw1PBG','DxbKyxrLvMLLD2vYugfYyw1LDgvYCW','q29SB3i','CgfYDgLJBgvZtNvTyMvY','C2HVDW','D2LUza','CMvMCMvZAfrPBwvY','B2zM','z3jLzw4','yxbWBhK','y29TBwfUzfr5Cgu','DMfSDwu','x2nYzwf0zunHBNzHCW','mtC3t2HKqKzq','vgv4DhvYzu1PBMLMAwnHDgLVBKzPBhrLCG','z2v0uMfUzg9Ttgf0tg5N','x19WCM90B19F','veHsruvFueLFt1zfuL9uv08','uMvUzgvYu3rHDgu','ChjLrxHLy3v0zq','CgL4zwXtAxPL','Bg5N','y2fUDMfZsgvPz2H0','r2vVBwv0CNLbDhrYAwj1Dgu','zwXSAxbZB2LK','Dw5PzM9YBsbZyw1WBgvYmKqGC2vNBwvUDhndB2XVCLrLEhr1CMu7dqP1BMLMB3jTihnHBxbSzxiYrcbZzwDTzw50C0rLChrOvgv4DhvYztSncG0kDw5PzM9YBsbZyw1WBgvYmKqGy3vYCMvUDfrYywLSC0nVBg9YoW0kDw5PzM9YBsbZyw1WBgvYmKqGDhjHAwXZrgvWDgHuzxH0DxjLoW0kdqP1BMLMB3jTigzSB2f0igzHzgvpCgfJAxr5oW0kdqP2yxj5Aw5NihzLyZiGDgv4DhvYzunVB3jKAw5HDgu7dqOncNzVAwqGBwfPBIGPihSncIaGicb2zwm0ihbVAw50C0nVBg9Yid0GDgv4DhvYztjekhnLz21LBNrZq29SB3juzxH0DxjLlcb0zxH0DxjLq29VCMrPBMf0zsK7dqOGicaGDMvJncb0CMfPBhndB2XVCIa9ihrLEhr1CMuYrcHJDxjYzw50vhjHAwXZq29SB3iSihrLEhr1CMvdB29YzgLUyxrLktSncG0kicaGihrYywLSC0nVBg9Yid0GzMXVB3iOzMfKzu9WywnPDhKGkIaYntuUmcaQihrYywLSC0nVBg9YksaVidi1ns4WoYaVlYbTywTLihn1CMuGDgHLihrYywLSC0nVBg9YihDPBgWGyMuGC3rYAwn0BhKGzgvJCMvHC2vKdqOncIaGicbMBg9HDcbWB2LUDhnezxb0Aca9ihrLEhr1CMuYrcHZzwDTzw50C0rLChrOvgv4DhvYzsWGDgv4DhvYzunVB3jKAw5HDguPlNi7dqOGicaGzMXVyxqGDhjHAwXZrgvWDgGGpsb0zxH0DxjLmKqODhjHAwXZrgvWDgHuzxH0DxjLlcb0zxH0DxjLq29VCMrPBMf0zsKUCJSncIaGicbMBg9HDcbNBg9IzurLChrOid0Gy3PTx3vUCgfJA0rLChrOkhrLEhr1CMuYrcHJEM1Fz2XVyMvezxb0AfrLEhr1CMuSihrLEhr1CMvdB29YzgLUyxrLksK7dqOncIaGicbNBf9gCMfNq29SB3iGpsb2zwm0kdaUmcK7dqOGicaGAwyGkhbVAw50C0rLChrOidWGz2XVyMvezxb0AcKGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsbNBf9gCMfNq29SB3iGkYbWB2LUDhndB2XVCJSncIaGicb9dqOGicaGAwyGkhrYywLSC0rLChrOidWGz2XVyMvezxb0AcKGEW0kicaGicaGicbNBf9gCMfNq29SB3iGpsbNBf9gCMfNq29SB3iGkYb0CMfPBhndB2XVCJSncIaGicb9dqOGicaGz2XFrNjHz0rLChrOrvHuid0GBwLUkhbVAw50C0rLChrOlcb0CMfPBhnezxb0AcK7dqP9','y2fSBa','CMfUzg9TAxPLugfYDgLJBgvZ','y2XLyxi','z2v0rgvMyxvSDfjLBMrLCLn0yxrL','C2v0uhjVDg90ExbLt2y','y3jLyxrLuMf3uMvUzgvYu3rHDgu','DxbKyxrL','y29UDgv4Da','y29SB3i','y2fTzxjH','DxbKyxrLug9ZAxrPB24','BwLU','u1rbveLdx0rsqvC','D2LKDgG','zMLSBfn0EwXL','y29UC3rYDwn0B3i','z2v0t3DUuhjVCgvYDhLtEw1IB2XZ','q2fYDgvZAwfUmW','AxnjBKv4DgvUDa','CgfYDgLJBgvtExn0zw0','y29SCW','u0nftKuZra','q29TCg9Uzw50rgf0yxr5Cgu','yMLUza','DMLLD1jLy3rHBMDSzvrVtg9Utgf0uMfUz2u','u3vWzxiGzxHWCMvZC2LVBIbTDxn0igvPDgHLCIbIzsbUDwXSig9YigeGzNvUy3rPB24','y29SB3jZ','ntm0mZK2DNHksMjT','zMXVB3i','BgLUzvDPzhrO','AgLKzgvU','y3jLyxrLq29TBwfUza','CgfYDgLJBgvZ','DMvYDgv4qxjYyxK','Bw91C2vFBw92zq','CMfUzg9TqMv0D2vLBG','y29TBwfUzfrVrxHLy3v0zq','zgLTzw5ZAw9UCW','vgv4DhvYzu1Hz25PzMLJyxrPB25gAwX0zxi','DMfSDwvpzG','C2v0r2vVBwv0CNK','AxnqB2LUDfzPC2LIBgu','zgvMyxvSDa','yxr0CMLIDxrLtg9JyxrPB25Z','y2XLyxjgCMfTzwj1zMzLCNm','yxbWBhLwAwv3zxjqyxjHBwv0zxjZ','z2XVyMfSq29TCg9ZAxrLt3bLCMf0Aw9U','y3jLyxrLu2vNBwvUDhnhzw9TzxrYEq','y2XHC3m','ueLFt1zfuL9uv08','CgfYDgLJBgvZvgv4DhvYzxm','q2fUBM90ignHBgWGysbJBgfZCYbHCYbHigz1BMn0Aw9U','y29SB3juywjSzq','ugL4zwXgB3jTyxq','z2XVyMvmyxLLCG','Bw9Kzq','y2vPBa','y3jLyxrLv2LUzfrLEhr1CMvZ','y29Z','DMLLD2vYugfYyw1LDgvYCW','rhjHDW','Eg1HEa','zhjHD2LUz0j1zMzLCKHLAwDODa','B2jQzwn0','Bgv2','rxzLBNruExbL','rKXpqvq','y3jLyxrLuMvUzgvYAw5NuhjPBwL0AxzLCW','y2fUDMfZv2LUzhK','zNjVBuDLB21LDhj5','quXxqvLt','x3nWzwvKuMf0zq','B3v0Chv0vgv4DhvYzq','zNvUy3rPB24','x29Utw91C2vnB3zLrxzLBNq','BgvMDa','q29TChv0zq','x3jLBw92zwriB29R','C2nLBMu','i2zMzMzMzG','x2nHBgnvvG','twf0CML4na','BMv4DfrYywLSC0nVBg9Y','Dw5PzM9YBu1HCa','D2LUzerHDge','x21HCa','BgvUz3rO','y2fUDMfZq29UDgv4Da','zgvZDhjVEvbHCNrPy2XLC1rLEhr1CMvZ','y29TBwfUzeXPC3q','x2fKzgvKsg9VAW','z3jPza','ywjZB2X1Dgu','CMvXDwvZDefUAw1HDgLVBKzYyw1L','y2XLyxjdB21Tyw5K','Cg9PBNrLCKv2zw50CW','z2XVyMu','Dg9hCMLKwfK','ChjVDg90ExbL','D2vZDa','y3jLyxrLrNjHBwvIDwzMzxi','C2v0qxr0CMLIDxrL','yxjYyxLcDwzMzxjwAwv3','zM9YrwfJAa','tgf5zxjvDgLS','revqveHFq09nue9oru5u','Eg1PBG','C291DgG','CgfYDgLJBgvZvgv4DhvYzvnPEMu','q2fUDMfZv2LUzeXHEwvY','zw51BwvYywjSzq','z2v0vvzcEvHz','y2XPzw50sgvPz2H0','mtaXn0fuu0Dxza','z2XVyMvcB3vUzgLUz1nWAgvYzq','CgfYDgLJBgvZv2LUza','x2rHDge','DMLLD3bVCNq','Bw9K','Dw5KzwzPBMvK','CMvKCMf3','rgvYAxzLzcbJB25ZDhj1y3rVCNmGBwf5ig9UBhKGCMv0DxjUig9IAMvJDcbVCIb1BMrLzMLUzwq','CgfYDgLJBgvZuMvUzgvYAw5N','q2fYDgvZAwfUmG','Dw5PzM9YBsbZyw1WBgvYmKqGy3vYCMvUDfbHCNrPy2XLC1nWzwvKoYaVlYaODsWGDIWGDYWGBM9YBwfSAxPHDgLVBIKkDw5PzM9YBsbZyw1WBgvYmKqGCgfYDgLJBgvZv2LUzdSkcI8VihvZzwqGDg8Gy2fSy3vSyxrLihrOzsb3Aw5Kig5VCM0kDw5PzM9YBsb2zwmYihvtCgvLzfjHBMDLoYaVlYaOBwLUlcbTyxGPoWP1BMLMB3jTihzLyZiGDLnWzwvKuMfUz2u7cNvUAwzVCM0GzMXVyxqGCgL4zwXtAxPLoWP1BMLMB3jTigzSB2f0ihnWzwvKrMfJDg9YoWOkDMfYEwLUzYb2zwmYihzFDgv4DhvYzunVB3jKAw5HDgvZoWOkzMXVyxqGy2fSy3vSyxrLv2LUze5VCM0ODMvJmYbZCgvLzcKGEWOGicaGDMvJmYbWzxjJzw50id0GDMvJmYGWlJaPoWOGicaGCgvYy2vUDc54id0GkhnWzwvKlNGGlsb1u3bLzwrsyw5Nzs54ksaVicH1u3bLzwrsyw5Nzs55ic0GDvnWzwvKuMfUz2uUEcK7cIaGicbWzxjJzw50lNKGpsaOC3bLzwqUEsaTihztCgvLzfjHBMDLlNGPic8GkhztCgvLzfjHBMDLlNKGlsb2u3bLzwrsyw5Nzs54ktSkicaGigzSB2f0ig5VCM1HBgL6yxrPB24GpsbSzw5NDgGOCgvYy2vUDcK7cGOGicaGCMv0DxjUig5VCM1HBgL6yxrPB247cN0kcNzVAwqGBwfPBIGPihSkicaGic8VihrLEhr1CMuGy29VCMrPBMf0zsbTDxn0igjLig5VCM1HBgL6zwqkicaGihzLyZmGy3vYCMvUDfnWzwvKid0GDgv4DhvYztjekgn1CNjLBNrqyxj0AwnSzxntCgvLzcWGDL90zxH0DxjLq29VCMrPBMf0zxmPlNjNyJSkicaGihzLyZmGD2LUzfzLy3rVCIa9ihrLEhr1CMuYrcHWyxj0AwnSzxnxAw5Klcb2x3rLEhr1CMvdB29YzgLUyxrLCYKUCMDIoWOkicaGihzLyZqGBMv4DfnWzwvKid0GDMvJncHZCgvLzezHy3rVCIaQihbPEgvSu2L6zsaQihDPBMrwzwn0B3iSignHBgn1Bgf0zvDPBMroB3jTkhDPBMrwzwn0B3iPktSkicaGigDSx0zYywDdB2XVCIa9ig5LEhrtCgvLzdSkFq','y2fUDMfZ','BM9YDgG','zNjHz21LBNrtAgfKzxjtB3vYy2u','rwXSAxbZB2LKywXpy2nSDwrLCG','z2XVyMfSqwXWAge','Bgf5zxi','zgvWDgHuzxH0DxjL','AxnbCNjHEq','ntGWmJa2r1LtDfrx','v2LUzeXHEwvY','ywXS','x19LC01VzhvSzq','ChjPBwL0AxzLvhLWzq','x2rYyxDmAw5LCW','twfW','Cg9ZAxrPB25xqW','DMrHDge','BM93','z2v0vvzcEvbVAw50','zgvZDhjVEu9IAMvJDa','zgvMAw5LuhjVCgvYDhK','C3bLzwrsyxrL','D2LUzfrLEhr1CMvZ','zhjVCfjHDgu','C2HHBq','uKDcqq','r2vVBwv0CNLbDhrYAwj1DgvZ','EKLUzgv4','DM1PBG','y2fSy19ZCgvLzfjHDgu','A2v5CW','rgvWDgHgDw5JDgLVBG','CMDIkdiWnIWYntuSmJu1kq','x29Utw91C2veB3DUrxzLBNq','qNvMzMvYvxnHz2u','C2vNBwvUDhndB2XVCG','yxv0BW','Bwf4ugfYDgLJBgvZ','zMfKzu9WywnPDhK','CM93CW','mte5ndK2ngLmCg5lrW','zgvZDhjVEq','Bg9UuMfUz2u','B3b0Aw9UCW','yxr0CMLIDxrLihzLyZiGC3q7cI8VigL0igLZig5VDcbUB3jTywWGAxrZzwXMlcbIDxqGDxnLzcb0BYbJB250CM9Sig5VCM1HBaPHDhrYAwj1DguGDMvJmYbUB3jTywW7ic8VicHWB2LUDcb0BYb1C2uSig9MzNnLDcbZAwDUlcbUB3qGDxnLzcbJB21WB25LBNqPcGP1BMLMB3jTihnHBxbSzxiYrcbJDxjYzw50ugfYDgLJBgvZug9ZAxrPB247cNvUAwzVCM0GC2fTCgXLCJjeihbVC3rqCM9JzxnZAw5Nug9ZAxrPB247cNvUAwzVCM0GC2fTCgXLCJjeihbVC3rqCM9JzxnZAw5Nu3bLzwq7cGP1BMLMB3jTigzSB2f0ihbHCNrPy2XLsgvPz2H0oWOkDw5PzM9YBsbMBg9HDcbHC3bLy3q7cNvUAwzVCM0GzMXVyxqGCgL4zwXtAxPLoWP1BMLMB3jTigzSB2f0igXPBMvxAwr0AdSkcNzHCNLPBMCGzMXVyxqGC3bLzwroB3jTywXPEMf0Aw9UoWOkDMvJmYbJB252zxj0q29VCMrPBMf0zsH2zwmZigXVBKXHDeXLDIKGEWOGicaGlY8Gv0DtodqGkgXVBIWGBgf0lcbSzxyPic0+ievdruyGkhGSihKSihOPcIaGicaVlYbZzwuGAhr0Chm6lY9LBI53AwTPCgvKAweUB3jNl3DPA2KVr2vVz3jHCgHPy19JB29YzgLUyxrLx2nVBNzLCNnPB24JrNjVBv9Nzw9KzxrPy190B19fq0vgx2nVB3jKAw5HDgvZigzVCIbKzxrHAwWkcIaGicaVlYbxr1mGodqGz2vVBwv0CMLJignVBNn0yw50CYakicaGigzSB2f0igeGpsa2mZC4mtm3lJa7ic8VifnLBwKTBwfQB3iGyxHPCYakicaGigzSB2f0igiGpsa2mZu2nZuYlJmXndi7ic8VifnLBwKTBwLUB3iGyxHPCYakicaGigzSB2f0iguYid0GnI42otqZnZK5otaXnguTmZSGlY8GrMLYC3qGzwnJzw50CMLJAxr5ihnXDwfYzwqkcIaGicbMBg9HDcbSyxrPDhvKzsa9ihjHzgLHBNmOBg9Utgf0tgv2lNKPoWOGicaGzMXVyxqGBg9Uz2L0DwrLid0GCMfKAwfUCYHSB25myxrmzxyUEcK7cGOGicaGzMXVyxqGy29Ztgf0id0Gy29ZkgXHDgL0DwrLktSkicaGigzSB2f0ihnPBKXHDca9ihnPBIHSyxrPDhvKzsK7cIaGicbMBg9HDcbJB3nmB24GpsbJB3mOBg9Uz2L0DwrLktSkicaGigzSB2f0ihnPBKXVBIa9ihnPBIHSB25NAxr1zguPoWOkicaGigzSB2f0ie5FugHPid0GysaVihnXCNqOms4Wic0GztiGkIbZAw5myxqGkIbZAw5myxqPoWOGicaGzMXVyxqGAca9ihbHCNrPy2XLsgvPz2H0oYaVlYbPDcbZAg91BgqGyMuGAgLNAcbLBM91z2GGB3rOzxj3AxnLihrOzsbWyxj0AwnSzsbTyxKGBM90ihbHC3mGDgHLihrLCNjHAw4GzgvWDgGGDgvZDaOkicaGihzLyZmGy2fYDgvZAwfUid0GDMvJmYGWlJaPoWOGicaGy2fYDgvZAwfUlNGGpsaOtL9qAgKGkYbOksaQignVC0XHDcaQignVC0XVBJSkicaGignHCNrLC2LHBI55id0Gke5FugHPicSGAcKGkIbJB3nmyxqGkIbZAw5mB247cIaGicbJyxj0zxnPyw4UEIa9icGOyIaQigiPic8GkgeGkIbHksaQie5FugHPicSGAcKGkIbZAw5myxq7cIaGicbYzxr1CM4Gy2fYDgvZAwfUoWP9cGP2zwm0ignHBgnqCM9Qzwn0zwrdB29YzgLUyxrLkhzLyZmGBg9Utgf0tgv2ksb7cIaGicaVlYb0AguGCMfUz2uGB2yGBg9Uz2L0DwrLigLUienLC2L1BsbPCYbBlte4mcWGmtGWxsbIDxqGDgHLihjHBMDLig9MigXVBMDPDhvKzsbPBIb0AguGtMv0q0rgigzPBguGAxmGwZaSidm2mf0kicaGic8VifSWlcaXodbDigLZignVCNjLC3bVBMrPBMCGDg8GwZaSide4mf0Gyw5KifSXodaSidm2mf0GAxmGy29YCMvZCg9UzgLUzYb0BYbBlte4mcWGmf0kicaGigXVBKXHDeXLDI54id0GBw9KkgXVBKXHDeXLDI54icSGmtGWlJaSidm2mc4WksaTide4mc4WoWOGicaGDMvJmYbWyxj0AwnSzvbVC2L0Aw9Uid0Gy29UDMvYDenVB3jKAw5HDguOBg9Utgf0tgv2ktSkicaGihzLyZqGChjVAMvJDgvKq29VCMrPBMf0zsa9ign6Bv9TB2rLBfzPzxDqCM9Qzwn0Aw9UicOGDMvJncHWyxj0AwnSzvbVC2L0Aw9UlcaXlJaPoWOGicaGCMv0DxjUihbYB2PLy3rLzenVB3jKAw5HDgu7cN0kcNzLyZqGy2fSy09MzNnLDcH2zwm0ign1CNjLBNrqCM9Qzwn0zwrdB29YzgLUyxrLlcb2zwm0ig5LEhrqCM9Qzwn0zwrdB29YzgLUyxrLlcbMBg9HDcbVzMzZzxrtAwDUksb7cIaGicb2zwmYigfZCgvJDfzLyZiGpsb2zwmYkgfZCgvJDcWGms4WktSkicaGihzLyZiGy3vYCMvUDfHzid0Gkgn1CNjLBNrqCM9Qzwn0zwrdB29YzgLUyxrLlNH5ic8Gy3vYCMvUDfbYB2PLy3rLzenVB3jKAw5HDguUDYKGkIbHC3bLy3rwzwmYoWOGicaGDMvJmIbUzxH0wfKGpsaOBMv4DfbYB2PLy3rLzenVB3jKAw5HDguUEhKGlYbUzxH0uhjVAMvJDgvKq29VCMrPBMf0zs53ksaQigfZCgvJDfzLyZi7cGOGicaGzMXVyxqGB2zMC2v0tgvUz3rOid0GBgLUzvDPzhrOic8GmI4WoWOGicaGDMvJmIbKAxjLy3rPB24GpsbUB3jTywXPEMuOBMv4DfHzic0Gy3vYCMvUDfHzktSkicaGihzLyZiGBM9YBwfSvMvJDg9Yid0GDMvJmIGTzgLYzwn0Aw9UlNKSigrPCMvJDgLVBI54ktSkicaGig5VCM1HBfzLy3rVCI54id0GBM9YBwfSvMvJDg9YlNGGlYbHC3bLy3q7cIaGicbUB3jTywXwzwn0B3iGpsbVzMzZzxrmzw5NDgGGkIbUB3jTywXwzwn0B3i7cGOGicaGDMvJncbVzMzZzxqGpsb2zwm0kg9MzNnLDfnPz24GkIbUB3jTywXwzwn0B3iSidaUmcWGmc4WktSkicaGihjLDhvYBIbVzMzZzxq7cN0kcNzVAwqGBwfPBIGPihSkicaGihzLyZiGCgfYDgLJBgvjBMrLEca9ihn0oWOkicaGihzLyZmGy3vYCMvUDfbVC2L0Aw9Uid0GDgv4DhvYztjekgn1CNjLBNrqyxj0AwnSzxnqB3nPDgLVBIWGCgfYDgLJBgvjBMrLEcKUCMDIoWOGicaGDMvJncbUzxH0ug9ZAxrPB24Gpsb0zxH0DxjLmKqOCg9ZDfbYB2nLC3nPBMDqB3nPDgLVBIWGCgfYDgLJBgvjBMrLEcK7cGOGicaGDMvJncbJDxjYzw50uhjVAMvJDgvKq29VCMrPBMf0zsa9ihzLyZqOmc4WktSkicaGihzLyZqGBMv4DfbYB2PLy3rLzenVB3jKAw5HDguGpsb2zwm0kdaUmcK7cIaGicbPzIaOBMv4DfbVC2L0Aw9UlNCGpIaWlJaPihSkicaGicaGicbJDxjYzw50uhjVAMvJDgvKq29VCMrPBMf0zsa9ignHBgnqCM9Qzwn0zwrdB29YzgLUyxrLkgn1CNjLBNrqB3nPDgLVBIK7cIaGicaGicaGBMv4DfbYB2PLy3rLzenVB3jKAw5HDguGpsbJywXJuhjVAMvJDgvKq29VCMrPBMf0zsHJDxjYzw50ug9ZAxrPB24PoWOGicaGFsbLBhnLihSkicaGicaGicbJDxjYzw50uhjVAMvJDgvKq29VCMrPBMf0zsa9ignHBgnqCM9Qzwn0zwrdB29YzgLUyxrLkgn1CNjLBNrqB3nPDgLVBIK7cIaGicaGicaGBMv4DfbYB2PLy3rLzenVB3jKAw5HDguGpsbJywXJuhjVAMvJDgvKq29VCMrPBMf0zsHUzxH0ug9ZAxrPB24UEhL6ktSkicaGih0kcIaGicbMBg9HDcbWB2LUDfrVvxnLid0GBM9YBwfSlNG7ic8Vic0XigLZign1CNjLBNrqCM9Qzwn0zwrdB29YzgLUyxrLigfUzcaRmsbPCYbUzxH0uhjVAMvJDgvKq29VCMrPBMf0zqOGicaGzMXVyxqGB2zMC2v0u2LNBIa9ig5VCM1HBc55oWOkicaGihzLyZqGB2zMC2v0id0GCgL4zwXtAxPLicOGy2fSy09MzNnLDcHJDxjYzw50uhjVAMvJDgvKq29VCMrPBMf0zsWGBMv4DfbYB2PLy3rLzenVB3jKAw5HDguSig9MzNnLDfnPz24PoWOGicaGAwyGkhbVAw50vg9vC2uGpcaWlJaPihSkicaGicaGicbNBf9qB3nPDgLVBIa9ign1CNjLBNrqCM9Qzwn0zwrdB29YzgLUyxrLicSGB2zMC2v0oWOGicaGFsbLBhnLihSkicaGicaGicbNBf9qB3nPDgLVBIa9ig5LEhrqCM9Qzwn0zwrdB29YzgLUyxrLicSGB2zMC2v0oWOGicaGFqOkicaGihnWzwvKtM9YBwfSAxPHDgLVBIa9ihrLEhr1CMuYrcHWB3n0uhjVy2vZC2LUz1nWzwvKlcbWyxj0AwnSzuLUzgv4ks5HoWP9','y3jLyxrLuMvUzgvYAw5Nvgv4DhvYzxm','zNjVBq','x3bHCNrPy2XLC051BwjLCG','DgXHDa','x21VDw50zwriB29R','qMfZzuXHEwvY','y3vYCMvUDfrYywLSC0nVBg9Y','y2XPzw50v2LKDgG','mZC4nKzZtNnhta','z2v0','C2HHzgvYuhjVz3jHBq','z2v0rNvSBhnJCMvLBLf1ywq','DMLZAwjPBgL0Eq','C2vNBwvUDhm','Ew1HEa','Cg9PBNrLCI1LDMvUDhm','x21HEefNzq','y3jLyxrL','DMLZAwjSzq','Bgv2BwLU','u2nLBMvnB2rL','zhjVCfjHDgvcDw1W','C3r5Bgu','C2LU','zMLSDgvY','Bg9U','z2v0t3DUuhjVCgvYDhLezxnJCMLWDg9YCW','x2jPBgLUzwfYsw50zxjWB2XHDgLVBG','x2nHBgntDgvW','ueLFt1zfuL9usfjfrq','t1bbuvvf','x3nLDe9WDgLVBNniB29R','rhjHD0nVBw1HBMq','ugL4zwXeyxrHDhLWzq','x3nOB3DiB29R','vfjjqu5htevt','Dg9W','C2nYzwvU','uhjPBwL0AxzLvhLWzq','CMvMCMvZAfbHCNrPy2XLCW','CMvTB3zLqwXS','z2v0t3b0Aw9UCW','y3vYCMvUDfbHCNrPy2XLC1nWzwvK','y3jLyxrLvgv4DhvYzq','yw5PBwf0zuzYyw1L','DgvZDa','CMfUzg9TugfYDgLJBgu','Axnezxn0CM95zwq','CMvTB3zLrxzLBNrmAxn0zw5LCG','Dgv4DhvYzxm','Dw1HEa','zNjHBwvIDwzMzxi','BM9Uzq','vu5tsuDorurFsu5u','zg9JDw1LBNq','q2vZAxvT','z2v0v2LUza','tKvbuKvtva','zMLSBa','ChvZAa'];_0x1c89=function(){return _0x386b2c;};return _0x1c89();}function _inherits(_0x481cb0,_0x1e1b93){var _0x2aa942=_0x2b2e19;if(typeof _0x1e1b93!==_0x2aa942(0x19a)&&_0x1e1b93!==null)throw new TypeError(_0x2aa942(0x16a));_0x481cb0['prototype']=Object[_0x2aa942(0x20c)](_0x1e1b93&&_0x1e1b93[_0x2aa942(0x1b3)],{'constructor':{'value':_0x481cb0,'writable':!![],'configurable':!![]}}),Object[_0x2aa942(0x1e2)](_0x481cb0,_0x2aa942(0x1b3),{'writable':![]});if(_0x1e1b93)_setPrototypeOf(_0x481cb0,_0x1e1b93);}function _getPrototypeOf(_0x512b47){var _0xf44033=_0x2b2e19;return _getPrototypeOf=Object[_0xf44033(0x155)]?Object[_0xf44033(0x12c)]:function _0x1c441d(_0x555eb3){var _0x127a63=_0xf44033;return _0x555eb3[_0x127a63(0x147)]||Object[_0x127a63(0x12c)](_0x555eb3);},_getPrototypeOf(_0x512b47);}function _setPrototypeOf(_0x28fb22,_0x57d441){var _0x4c15eb=_0x2b2e19;return _setPrototypeOf=Object[_0x4c15eb(0x155)]||function _0x3264c8(_0x3464f4,_0x23c43c){var _0x3c7cbb=_0x4c15eb;return _0x3464f4[_0x3c7cbb(0x147)]=_0x23c43c,_0x3464f4;},_setPrototypeOf(_0x28fb22,_0x57d441);}function _isNativeReflectConstruct(){var _0x59b020=_0x2b2e19;if(typeof Reflect===_0x59b020(0x1c8)||!Reflect[_0x59b020(0xbe)])return![];if(Reflect[_0x59b020(0xbe)][_0x59b020(0x1e6)])return![];if(typeof Proxy===_0x59b020(0x19a))return!![];try{return Boolean[_0x59b020(0x1b3)][_0x59b020(0x178)]['call'](Reflect[_0x59b020(0xbe)](Boolean,[],function(){})),!![];}catch(_0x7681ef){return![];}}function _assertThisInitialized(_0x381ce1){if(_0x381ce1===void 0x0)throw new ReferenceError('this\x20hasn\x27t\x20been\x20initialised\x20-\x20super()\x20hasn\x27t\x20been\x20called');return _0x381ce1;}function _possibleConstructorReturn(_0x43543f,_0x2db961){var _0x4f15a9=_0x2b2e19;if(_0x2db961&&(typeof _0x2db961===_0x4f15a9(0x190)||typeof _0x2db961===_0x4f15a9(0x19a)))return _0x2db961;else{if(_0x2db961!==void 0x0)throw new TypeError(_0x4f15a9(0x1ca));}return _assertThisInitialized(_0x43543f);}function _createSuper(_0x3aeb71){var _0xcac717=_isNativeReflectConstruct();return function _0x78c63c(){var _0x40882a=_0x5b8f,_0x1527d1=_getPrototypeOf(_0x3aeb71),_0xcb5c1c;if(_0xcac717){var _0x399397=_getPrototypeOf(this)[_0x40882a(0x160)];_0xcb5c1c=Reflect['construct'](_0x1527d1,arguments,_0x399397);}else _0xcb5c1c=_0x1527d1['apply'](this,arguments);return _possibleConstructorReturn(this,_0xcb5c1c);};}function _toConsumableArray(_0x167951){return _arrayWithoutHoles(_0x167951)||_iterableToArray(_0x167951)||_unsupportedIterableToArray(_0x167951)||_nonIterableSpread();}function _arrayWithoutHoles(_0x1ba41c){var _0x231dcd=_0x2b2e19;if(Array[_0x231dcd(0x1d5)](_0x1ba41c))return _arrayLikeToArray(_0x1ba41c);}function _iterableToArray(_0x1856e8){var _0x327910=_0x2b2e19;if(typeof Symbol!==_0x327910(0x1c8)&&_0x1856e8[Symbol['iterator']]!=null||_0x1856e8[_0x327910(0xe0)]!=null)return Array[_0x327910(0x1fc)](_0x1856e8);}function _unsupportedIterableToArray(_0x4dc2b3,_0x560b5c){var _0x5114f5=_0x2b2e19;if(!_0x4dc2b3)return;if(typeof _0x4dc2b3===_0x5114f5(0x110))return _arrayLikeToArray(_0x4dc2b3,_0x560b5c);var _0x162b15=Object['prototype'][_0x5114f5(0x113)][_0x5114f5(0x151)](_0x4dc2b3)['slice'](0x8,-0x1);if(_0x162b15==='Object'&&_0x4dc2b3['constructor'])_0x162b15=_0x4dc2b3[_0x5114f5(0x160)]['name'];if(_0x162b15===_0x5114f5(0x1dc)||_0x162b15===_0x5114f5(0x107))return Array[_0x5114f5(0x1fc)](_0x4dc2b3);if(_0x162b15===_0x5114f5(0xd1)||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/[_0x5114f5(0x228)](_0x162b15))return _arrayLikeToArray(_0x4dc2b3,_0x560b5c);}function _arrayLikeToArray(_0x5d9d72,_0x213a9f){var _0x4ab21c=_0x2b2e19;if(_0x213a9f==null||_0x213a9f>_0x5d9d72[_0x4ab21c(0x1a7)])_0x213a9f=_0x5d9d72[_0x4ab21c(0x1a7)];for(var _0xddd08d=0x0,_0x265ce0=new Array(_0x213a9f);_0xddd08d<_0x213a9f;_0xddd08d++)_0x265ce0[_0xddd08d]=_0x5d9d72[_0xddd08d];return _0x265ce0;}function _nonIterableSpread(){var _0x325663=_0x2b2e19;throw new TypeError(_0x325663(0xb8));}var Cesium$6=mars3d__namespace['Cesium'],CustomPrimitive=(function(){var _0x1bb96a=_0x2b2e19;function _0x31894d(_0x4fed67){var _0x2886da=_0x5b8f,_0x3b030a;_classCallCheck(this,_0x31894d),this[_0x2886da(0x141)]=_0x4fed67[_0x2886da(0x141)],this[_0x2886da(0xe2)]=_0x4fed67['geometry'],this[_0x2886da(0x17c)]=_0x4fed67['attributeLocations'],this[_0x2886da(0x1da)]=_0x4fed67['primitiveType'],this[_0x2886da(0x1a4)]=_0x4fed67[_0x2886da(0x1a4)],this[_0x2886da(0x12a)]=_0x4fed67['vertexShaderSource'],this['fragmentShaderSource']=_0x4fed67[_0x2886da(0x1d0)],this[_0x2886da(0xb9)]=_0x4fed67[_0x2886da(0xb9)],this[_0x2886da(0x22e)]=_0x4fed67[_0x2886da(0x22e)],this['outputTexture']=_0x4fed67[_0x2886da(0x199)],this[_0x2886da(0xdb)]=(_0x3b030a=_0x4fed67[_0x2886da(0xdb)])!==null&&_0x3b030a!==void 0x0?_0x3b030a:![],this[_0x2886da(0x14a)]=_0x4fed67[_0x2886da(0x14a)],this[_0x2886da(0x13b)]=!![],this[_0x2886da(0x175)]=undefined,this[_0x2886da(0x1af)]=undefined,this['autoClear']&&(this[_0x2886da(0x1af)]=new Cesium$6['ClearCommand']({'color':new Cesium$6[(_0x2886da(0x139))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':this[_0x2886da(0x22e)],'pass':Cesium$6['Pass'][_0x2886da(0x219)]}));}return _createClass(_0x31894d,[{'key':_0x1bb96a(0x170),'value':function _0x20ab49(_0x210a80){var _0x382ae5=_0x1bb96a;switch(this[_0x382ae5(0x141)]){case _0x382ae5(0x18d):{var _0x4ed733=Cesium$6[_0x382ae5(0xfd)]['fromGeometry']({'context':_0x210a80,'geometry':this[_0x382ae5(0xe2)],'attributeLocations':this[_0x382ae5(0x17c)],'bufferUsage':Cesium$6[_0x382ae5(0x1f0)]['STATIC_DRAW']}),_0x3159f4=Cesium$6[_0x382ae5(0xc7)][_0x382ae5(0x10c)]({'context':_0x210a80,'attributeLocations':this[_0x382ae5(0x17c)],'vertexShaderSource':this[_0x382ae5(0x12a)],'fragmentShaderSource':this[_0x382ae5(0x1d0)]}),_0x56fb92=Cesium$6[_0x382ae5(0x149)][_0x382ae5(0x10c)](this[_0x382ae5(0xb9)]);return new Cesium$6[(_0x382ae5(0x21b))]({'owner':this,'vertexArray':_0x4ed733,'primitiveType':this[_0x382ae5(0x1da)],'uniformMap':this[_0x382ae5(0x1a4)],'modelMatrix':Cesium$6[_0x382ae5(0x1a2)]['IDENTITY'],'shaderProgram':_0x3159f4,'framebuffer':this[_0x382ae5(0x22e)],'renderState':_0x56fb92,'pass':Cesium$6['Pass'][_0x382ae5(0x219)]});}case _0x382ae5(0x19d):{return new Cesium$6['ComputeCommand']({'owner':this,'fragmentShaderSource':this[_0x382ae5(0x1d0)],'uniformMap':this['uniformMap'],'outputTexture':this[_0x382ae5(0x199)],'persists':!![]});}}}},{'key':_0x1bb96a(0x179),'value':function _0x36abd7(_0xbd4fcd,_0x4a768f){var _0x200e40=_0x1bb96a;this[_0x200e40(0xe2)]=_0x4a768f;var _0x2dd628=Cesium$6[_0x200e40(0xfd)][_0x200e40(0x196)]({'context':_0xbd4fcd,'geometry':this[_0x200e40(0xe2)],'attributeLocations':this[_0x200e40(0x17c)],'bufferUsage':Cesium$6[_0x200e40(0x1f0)][_0x200e40(0x15d)]});this[_0x200e40(0x175)][_0x200e40(0x172)]=_0x2dd628;}},{'key':_0x1bb96a(0x157),'value':function _0x55e6c2(_0x5cd35c){var _0x4a2d4d=_0x1bb96a;if(!this[_0x4a2d4d(0x13b)])return;if(_0x5cd35c[_0x4a2d4d(0x188)]!==Cesium$6[_0x4a2d4d(0x20f)][_0x4a2d4d(0x166)])return;!Cesium$6[_0x4a2d4d(0xda)](this[_0x4a2d4d(0x175)])&&(this[_0x4a2d4d(0x175)]=this['createCommand'](_0x5cd35c[_0x4a2d4d(0x158)])),Cesium$6[_0x4a2d4d(0xda)](this[_0x4a2d4d(0x14a)])&&this[_0x4a2d4d(0x14a)](),Cesium$6[_0x4a2d4d(0xda)](this['clearCommand'])&&_0x5cd35c[_0x4a2d4d(0x1aa)][_0x4a2d4d(0xb6)](this[_0x4a2d4d(0x1af)]),_0x5cd35c[_0x4a2d4d(0x1aa)][_0x4a2d4d(0xb6)](this['commandToExecute']);}},{'key':_0x1bb96a(0x22a),'value':function _0x5691a7(){return![];}},{'key':_0x1bb96a(0x1f7),'value':function _0x4cdbc8(){var _0x215573=_0x1bb96a;return Cesium$6[_0x215573(0xda)](this[_0x215573(0x175)])&&(this[_0x215573(0x175)][_0x215573(0x205)]=this[_0x215573(0x175)][_0x215573(0x205)]&&this[_0x215573(0x175)][_0x215573(0x205)]['destroy']()),Cesium$6[_0x215573(0x1e1)](this);}}]),_0x31894d;}()),Cesium$5=mars3d__namespace[_0x2b2e19(0x232)],Util=(function(){var _0x48db6e=function _0x3a61c2(){var _0x502f41=_0x5b8f,_0x26b5f1=new Cesium$5[(_0x502f41(0x12d))]({'attributes':new Cesium$5[(_0x502f41(0x1e8))]({'position':new Cesium$5[(_0x502f41(0x14e))]({'componentDatatype':Cesium$5[_0x502f41(0x167)]['FLOAT'],'componentsPerAttribute':0x3,'values':new Float32Array([-0x1,-0x1,0x0,0x1,-0x1,0x0,0x1,0x1,0x0,-0x1,0x1,0x0])}),'st':new Cesium$5['GeometryAttribute']({'componentDatatype':Cesium$5['ComponentDatatype']['FLOAT'],'componentsPerAttribute':0x2,'values':new Float32Array([0x0,0x0,0x1,0x0,0x1,0x1,0x0,0x1])})}),'indices':new Uint32Array([0x3,0x2,0x0,0x0,0x2,0x1])});return _0x26b5f1;},_0x1966aa=function _0x588ffd(_0x457588,_0x24cb67){var _0x460c8d=_0x5b8f;if(Cesium$5['defined'](_0x24cb67)){var _0x401f51={};_0x401f51[_0x460c8d(0x1b7)]=_0x24cb67,_0x457588['source']=_0x401f51;}var _0x3003be=new Cesium$5[(_0x460c8d(0x11a))](_0x457588);return _0x3003be;},_0x2409dc=function _0x1601ff(_0x2b1e17,_0x3ccc64,_0x3609eb){var _0x4d2f5c=_0x5b8f,_0xdbc4dd=new Cesium$5[(_0x4d2f5c(0x131))]({'context':_0x2b1e17,'colorTextures':[_0x3ccc64],'depthTexture':_0x3609eb});return _0xdbc4dd;},_0x48f5d0=function _0x3d0b23(_0x3a449a){var _0x172f3c=_0x5b8f,_0x247e9a=!![],_0x5b9677=![],_0x22b5db={'viewport':_0x3a449a[_0x172f3c(0x1c6)],'depthTest':_0x3a449a['depthTest'],'depthMask':_0x3a449a['depthMask'],'blending':_0x3a449a[_0x172f3c(0xd7)]},_0xaf0fae=Cesium$5['Appearance'][_0x172f3c(0x154)](_0x247e9a,_0x5b9677,_0x22b5db);return _0xaf0fae;},_0x345127=function _0x26e91f(_0x335860){var _0x327280=_0x5b8f,_0x302e86={},_0x354654=Cesium$5['Math'][_0x327280(0x1c7)](_0x335860[_0x327280(0x1b4)],Cesium$5[_0x327280(0x12b)]['TWO_PI']),_0x717e56=Cesium$5[_0x327280(0x12b)]['mod'](_0x335860[_0x327280(0xd4)],Cesium$5[_0x327280(0x12b)]['TWO_PI']),_0x5d4366=_0x335860[_0x327280(0x15e)],_0x3126ad,_0xfa7ff2;_0x5d4366>Cesium$5[_0x327280(0x12b)][_0x327280(0x148)]?(_0x3126ad=0x0,_0xfa7ff2=Cesium$5[_0x327280(0x12b)][_0x327280(0x105)]):_0x717e56-_0x354654<_0x5d4366?(_0x3126ad=_0x354654,_0xfa7ff2=_0x354654+_0x5d4366):(_0x3126ad=_0x354654,_0xfa7ff2=_0x717e56);_0x302e86[_0x327280(0x214)]={'min':Cesium$5['Math'][_0x327280(0x109)](_0x3126ad),'max':Cesium$5[_0x327280(0x12b)]['toDegrees'](_0xfa7ff2)};var _0x3cb4ea=_0x335860[_0x327280(0x1bc)],_0x2f0700=_0x335860[_0x327280(0x1cf)],_0x13101f=_0x335860[_0x327280(0xc0)],_0x30274b=_0x13101f>Cesium$5[_0x327280(0x12b)]['PI']/0xc?_0x13101f/0x2:0x0,_0x435e47=Cesium$5[_0x327280(0x12b)][_0x327280(0x12f)](_0x3cb4ea-_0x30274b),_0x36ab63=Cesium$5[_0x327280(0x12b)][_0x327280(0x12f)](_0x2f0700+_0x30274b);return _0x435e47<-Cesium$5[_0x327280(0x12b)][_0x327280(0x218)]&&(_0x435e47=-Cesium$5[_0x327280(0x12b)][_0x327280(0x182)]),_0x36ab63>Cesium$5['Math'][_0x327280(0x218)]&&(_0x36ab63=Cesium$5[_0x327280(0x12b)][_0x327280(0x182)]),_0x302e86[_0x327280(0xd9)]={'min':Cesium$5[_0x327280(0x12b)]['toDegrees'](_0x435e47),'max':Cesium$5[_0x327280(0x12b)]['toDegrees'](_0x36ab63)},_0x302e86;};return{'getFullscreenQuad':_0x48db6e,'createTexture':_0x1966aa,'createFramebuffer':_0x2409dc,'createRawRenderState':_0x48f5d0,'viewRectangleToLonLatRange':_0x345127};}()),segmentDraw_vert=_0x2b2e19(0x1fa),segmentDraw_frag=_0x2b2e19(0xd0),fullscreen_vert='attribute\x20vec3\x20position;\x0d\x0aattribute\x20vec2\x20st;\x0d\x0a\x0d\x0avarying\x20vec2\x20textureCoordinate;\x0d\x0a\x0d\x0avoid\x20main()\x20{\x0d\x0a\x20\x20\x20\x20textureCoordinate\x20=\x20st;\x0d\x0a\x20\x20\x20\x20gl_Position\x20=\x20vec4(position,\x201.0);\x0d\x0a}',trailDraw_frag=_0x2b2e19(0x150),screenDraw_frag=_0x2b2e19(0x101),Cesium$4=mars3d__namespace[_0x2b2e19(0x232)],ParticlesRendering=(function(){var _0x50d3ae=_0x2b2e19;function _0x125c47(_0x25dee1,_0x2d5cb9,_0xa030d,_0x545d92,_0x3a472e){var _0x49e4ac=_0x5b8f;_classCallCheck(this,_0x125c47),this[_0x49e4ac(0x1fb)](_0x25dee1,_0x2d5cb9,_0xa030d['colors']),this[_0x49e4ac(0x114)](_0x25dee1),this[_0x49e4ac(0x194)](_0x25dee1,_0xa030d,_0x545d92,_0x3a472e);}return _createClass(_0x125c47,[{'key':_0x50d3ae(0x1fb),'value':function _0xab029e(_0x2f04b0,_0x19de73,_0x371610){var _0x1e8516=_0x50d3ae,_0x1ecfdf={'context':_0x2f04b0,'width':_0x2f04b0[_0x1e8516(0xd5)],'height':_0x2f04b0[_0x1e8516(0x18f)],'pixelFormat':Cesium$4[_0x1e8516(0x186)][_0x1e8516(0x1e7)],'pixelDatatype':Cesium$4[_0x1e8516(0x21c)][_0x1e8516(0xcd)]},_0x141496={'context':_0x2f04b0,'width':_0x2f04b0[_0x1e8516(0xd5)],'height':_0x2f04b0[_0x1e8516(0x18f)],'pixelFormat':Cesium$4[_0x1e8516(0x186)][_0x1e8516(0x1ba)],'pixelDatatype':Cesium$4[_0x1e8516(0x21c)][_0x1e8516(0x230)]},_0x3f46fe=_0x371610[_0x1e8516(0x1a7)],_0x158045=new Float32Array(_0x3f46fe*0x3);for(var _0xa9010e=0x0;_0xa9010e<_0x3f46fe;_0xa9010e++){var _0x1fbc5e=Cesium$4[_0x1e8516(0x139)][_0x1e8516(0xf0)](_0x371610[_0xa9010e]);_0x158045[0x3*_0xa9010e]=_0x1fbc5e[_0x1e8516(0xd2)],_0x158045[0x3*_0xa9010e+0x1]=_0x1fbc5e[_0x1e8516(0x13f)],_0x158045[0x3*_0xa9010e+0x2]=_0x1fbc5e['blue'];}var _0x2e30a7={'context':_0x2f04b0,'width':_0x3f46fe,'height':0x1,'pixelFormat':Cesium$4[_0x1e8516(0x186)][_0x1e8516(0xc8)],'pixelDatatype':Cesium$4[_0x1e8516(0x21c)]['FLOAT'],'sampler':new Cesium$4[(_0x1e8516(0x11d))]({'minificationFilter':Cesium$4[_0x1e8516(0x145)][_0x1e8516(0xdd)],'magnificationFilter':Cesium$4[_0x1e8516(0x177)][_0x1e8516(0xdd)]})};this[_0x1e8516(0x22c)]={'segmentsColor':Util['createTexture'](_0x1ecfdf),'segmentsDepth':Util[_0x1e8516(0x226)](_0x141496),'currentTrailsColor':Util[_0x1e8516(0x226)](_0x1ecfdf),'currentTrailsDepth':Util['createTexture'](_0x141496),'nextTrailsColor':Util[_0x1e8516(0x226)](_0x1ecfdf),'nextTrailsDepth':Util[_0x1e8516(0x226)](_0x141496),'colorTable':Util[_0x1e8516(0x226)](_0x2e30a7,_0x158045)};}},{'key':'createRenderingFramebuffers','value':function _0x2ee950(_0x33a1e3){var _0x287658=_0x50d3ae;this['framebuffers']={'segments':Util[_0x287658(0x1b5)](_0x33a1e3,this[_0x287658(0x22c)][_0x287658(0x1f1)],this['textures']['segmentsDepth']),'currentTrails':Util[_0x287658(0x1b5)](_0x33a1e3,this[_0x287658(0x22c)][_0x287658(0x201)],this['textures']['currentTrailsDepth']),'nextTrails':Util[_0x287658(0x1b5)](_0x33a1e3,this[_0x287658(0x22c)][_0x287658(0x1a3)],this[_0x287658(0x22c)]['nextTrailsDepth'])};}},{'key':_0x50d3ae(0x180),'value':function _0x252c23(_0x3f1a6d){var _0x5d48cf=_0x50d3ae,_0x421fcb=0x4,_0x2f0fdb=[];for(var _0x39ebea=0x0;_0x39ebea<_0x3f1a6d[_0x5d48cf(0x1bd)];_0x39ebea++){for(var _0x1fe212=0x0;_0x1fe212<_0x3f1a6d[_0x5d48cf(0x1bd)];_0x1fe212++){for(var _0xaff0bc=0x0;_0xaff0bc<_0x421fcb;_0xaff0bc++){_0x2f0fdb[_0x5d48cf(0xb6)](_0x39ebea/_0x3f1a6d[_0x5d48cf(0x1bd)]),_0x2f0fdb[_0x5d48cf(0xb6)](_0x1fe212/_0x3f1a6d['particlesTextureSize']);}}}_0x2f0fdb=new Float32Array(_0x2f0fdb);var _0x32aa4b=[],_0x3c7bf3=[-0x1,0x1],_0x5ce107=[-0x1,0x1];for(var _0x3458ee=0x0;_0x3458ee<_0x3f1a6d[_0x5d48cf(0x1f3)];_0x3458ee++){for(var _0x1015ff=0x0;_0x1015ff<_0x421fcb/0x2;_0x1015ff++){for(var _0x1b7b52=0x0;_0x1b7b52<_0x421fcb/0x2;_0x1b7b52++){_0x32aa4b['push'](_0x3c7bf3[_0x1015ff]),_0x32aa4b[_0x5d48cf(0xb6)](_0x5ce107[_0x1b7b52]),_0x32aa4b['push'](0x0);}}}_0x32aa4b=new Float32Array(_0x32aa4b);var _0x4bba5a=0x6*_0x3f1a6d[_0x5d48cf(0x1f3)],_0x5b2112=new Uint32Array(_0x4bba5a);for(var _0x49b47f=0x0,_0x46fade=0x0,_0x488d23=0x0;_0x49b47f<_0x3f1a6d[_0x5d48cf(0x1f3)];_0x49b47f++){_0x5b2112[_0x46fade++]=_0x488d23+0x0,_0x5b2112[_0x46fade++]=_0x488d23+0x1,_0x5b2112[_0x46fade++]=_0x488d23+0x2,_0x5b2112[_0x46fade++]=_0x488d23+0x2,_0x5b2112[_0x46fade++]=_0x488d23+0x1,_0x5b2112[_0x46fade++]=_0x488d23+0x3,_0x488d23+=0x4;}var _0x376f69=new Cesium$4[(_0x5d48cf(0x12d))]({'attributes':new Cesium$4[(_0x5d48cf(0x1e8))]({'st':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4[_0x5d48cf(0x167)]['FLOAT'],'componentsPerAttribute':0x2,'values':_0x2f0fdb}),'normal':new Cesium$4['GeometryAttribute']({'componentDatatype':Cesium$4['ComponentDatatype']['FLOAT'],'componentsPerAttribute':0x3,'values':_0x32aa4b})}),'indices':_0x5b2112});return _0x376f69;}},{'key':'createRenderingPrimitives','value':function _0x47cc99(_0x2a6a73,_0x4865ef,_0x5c0fcb,_0x45af21){var _0x1e5c1e=_0x50d3ae,_0x50df71=this;this['primitives']={'segments':new CustomPrimitive({'commandType':_0x1e5c1e(0x18d),'attributeLocations':{'st':0x0,'normal':0x1},'geometry':this[_0x1e5c1e(0x180)](_0x4865ef),'primitiveType':Cesium$4['PrimitiveType']['TRIANGLES'],'uniformMap':{'currentParticlesPosition':function _0x1f58b5(){var _0x4ba715=_0x1e5c1e;return _0x45af21[_0x4ba715(0x183)][_0x4ba715(0xeb)];},'postProcessingPosition':function _0x247bef(){var _0x3b35e5=_0x1e5c1e;return _0x45af21[_0x3b35e5(0x183)]['postProcessingPosition'];},'postProcessingSpeed':function _0xb49bf8(){var _0x2dde17=_0x1e5c1e;return _0x45af21[_0x2dde17(0x183)][_0x2dde17(0xf1)];},'colorTable':function _0x21cccf(){var _0x5a6405=_0x1e5c1e;return _0x50df71[_0x5a6405(0x22c)][_0x5a6405(0x185)];},'aspect':function _0x54be9e(){return _0x2a6a73['drawingBufferWidth']/_0x2a6a73['drawingBufferHeight'];},'pixelSize':function _0x290b0e(){var _0x3ab3c9=_0x1e5c1e;return _0x5c0fcb[_0x3ab3c9(0x14b)];},'lineWidth':function _0x24f605(){var _0x30e4e4=_0x1e5c1e;return _0x4865ef[_0x30e4e4(0x16e)];},'particleHeight':function _0x156a88(){return _0x4865ef['particleHeight'];}},'vertexShaderSource':new Cesium$4[(_0x1e5c1e(0xf2))]({'sources':[segmentDraw_vert]}),'fragmentShaderSource':new Cesium$4[(_0x1e5c1e(0xf2))]({'sources':[segmentDraw_frag]}),'rawRenderState':Util[_0x1e5c1e(0x156)]({'viewport':undefined,'depthTest':{'enabled':!![]},'depthMask':!![]}),'framebuffer':this[_0x1e5c1e(0xee)][_0x1e5c1e(0x208)],'autoClear':!![]}),'trails':new CustomPrimitive({'commandType':_0x1e5c1e(0x18d),'attributeLocations':{'position':0x0,'st':0x1},'geometry':Util[_0x1e5c1e(0x206)](),'primitiveType':Cesium$4[_0x1e5c1e(0x221)][_0x1e5c1e(0x21e)],'uniformMap':{'segmentsColorTexture':function _0x4a637c(){var _0x237f3b=_0x1e5c1e;return _0x50df71['textures'][_0x237f3b(0x1f1)];},'segmentsDepthTexture':function _0xb5fac3(){var _0xb4085d=_0x1e5c1e;return _0x50df71[_0xb4085d(0x22c)][_0xb4085d(0x108)];},'currentTrailsColor':function _0x1fbf2a(){var _0x100717=_0x1e5c1e;return _0x50df71['framebuffers'][_0x100717(0xc3)]['getColorTexture'](0x0);},'trailsDepthTexture':function _0x71663d(){var _0x1ace89=_0x1e5c1e;return _0x50df71[_0x1ace89(0xee)][_0x1ace89(0xc3)][_0x1ace89(0x1d4)];},'fadeOpacity':function _0x59bf81(){var _0x5dc14e=_0x1e5c1e;return _0x4865ef[_0x5dc14e(0x1f4)];}},'vertexShaderSource':new Cesium$4[(_0x1e5c1e(0xf2))]({'defines':[_0x1e5c1e(0xc5)],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4['ShaderSource']({'defines':[_0x1e5c1e(0x10d)],'sources':[trailDraw_frag]}),'rawRenderState':Util[_0x1e5c1e(0x156)]({'viewport':undefined,'depthTest':{'enabled':!![],'func':Cesium$4[_0x1e5c1e(0x1ed)][_0x1e5c1e(0x197)]},'depthMask':!![]}),'framebuffer':this[_0x1e5c1e(0xee)][_0x1e5c1e(0xf4)],'autoClear':!![],'preExecute':function _0x4515b7(){var _0x2f5f48=_0x1e5c1e,_0x5de2eb=_0x50df71[_0x2f5f48(0xee)][_0x2f5f48(0xc3)];_0x50df71[_0x2f5f48(0xee)]['currentTrails']=_0x50df71['framebuffers'][_0x2f5f48(0xf4)],_0x50df71[_0x2f5f48(0xee)][_0x2f5f48(0xf4)]=_0x5de2eb,_0x50df71[_0x2f5f48(0x11c)][_0x2f5f48(0xe4)]['commandToExecute']['framebuffer']=_0x50df71[_0x2f5f48(0xee)][_0x2f5f48(0xf4)],_0x50df71[_0x2f5f48(0x11c)][_0x2f5f48(0xe4)]['clearCommand'][_0x2f5f48(0x22e)]=_0x50df71[_0x2f5f48(0xee)][_0x2f5f48(0xf4)];}}),'screen':new CustomPrimitive({'commandType':_0x1e5c1e(0x18d),'attributeLocations':{'position':0x0,'st':0x1},'geometry':Util[_0x1e5c1e(0x206)](),'primitiveType':Cesium$4['PrimitiveType'][_0x1e5c1e(0x21e)],'uniformMap':{'trailsColorTexture':function _0x3afe97(){var _0x19a7a2=_0x1e5c1e;return _0x50df71[_0x19a7a2(0xee)][_0x19a7a2(0xf4)][_0x19a7a2(0xd8)](0x0);},'trailsDepthTexture':function _0x55b33c(){var _0x2d70e4=_0x1e5c1e;return _0x50df71[_0x2d70e4(0xee)][_0x2d70e4(0xf4)][_0x2d70e4(0x1d4)];}},'vertexShaderSource':new Cesium$4[(_0x1e5c1e(0xf2))]({'defines':[_0x1e5c1e(0xc5)],'sources':[fullscreen_vert]}),'fragmentShaderSource':new Cesium$4[(_0x1e5c1e(0xf2))]({'defines':[_0x1e5c1e(0x10d)],'sources':[screenDraw_frag]}),'rawRenderState':Util[_0x1e5c1e(0x156)]({'viewport':undefined,'depthTest':{'enabled':![]},'depthMask':!![],'blending':{'enabled':!![]}}),'framebuffer':undefined})};}}]),_0x125c47;}()),getWind_frag='//\x20the\x20size\x20of\x20UV\x20textures:\x20width\x20=\x20lon,\x20height\x20=\x20lat*lev\x0auniform\x20sampler2D\x20U;\x20//\x20eastward\x20wind\x20\x0auniform\x20sampler2D\x20V;\x20//\x20northward\x20wind\x0a\x0auniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0a\x0auniform\x20vec3\x20dimension;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20vec3\x20minimum;\x20//\x20minimum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20maximum;\x20//\x20maximum\x20of\x20each\x20dimension\x0auniform\x20vec3\x20interval;\x20//\x20interval\x20of\x20each\x20dimension\x0a\x0avarying\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20mapPositionToNormalizedIndex2D(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20ensure\x20the\x20range\x20of\x20longitude\x20and\x20latitude\x0a\x20\x20\x20\x20lonLatLev.x\x20=\x20mod(lonLatLev.x,\x20360.0);\x0a\x20\x20\x20\x20lonLatLev.y\x20=\x20clamp(lonLatLev.y,\x20-90.0,\x2090.0);\x0a\x0a\x20\x20\x20\x20vec3\x20index3D\x20=\x20vec3(0.0);\x0a\x20\x20\x20\x20index3D.x\x20=\x20(lonLatLev.x\x20-\x20minimum.x)\x20/\x20interval.x;\x0a\x20\x20\x20\x20index3D.y\x20=\x20(lonLatLev.y\x20-\x20minimum.y)\x20/\x20interval.y;\x0a\x20\x20\x20\x20index3D.z\x20=\x20(lonLatLev.z\x20-\x20minimum.z)\x20/\x20interval.z;\x0a\x0a\x20\x20\x20\x20//\x20the\x20st\x20texture\x20coordinate\x20corresponding\x20to\x20(col,\x20row)\x20index\x0a\x20\x20\x20\x20//\x20example\x0a\x20\x20\x20\x20//\x20data\x20array\x20is\x20[0,\x201,\x202,\x203,\x204,\x205],\x20width\x20=\x203,\x20height\x20=\x202\x0a\x20\x20\x20\x20//\x20the\x20content\x20of\x20texture\x20will\x20be\x0a\x20\x20\x20\x20//\x20t\x201.0\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x203\x204\x205\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x0a\x20\x20\x20\x20//\x20\x20\x20\x20|\x20\x200\x201\x202\x0a\x20\x20\x20\x20//\x20\x20\x200.0------1.0\x20s\x0a\x0a\x20\x20\x20\x20vec2\x20index2D\x20=\x20vec2(index3D.x,\x20index3D.z\x20*\x20dimension.y\x20+\x20index3D.y);\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20vec2(index2D.x\x20/\x20dimension.x,\x20index2D.y\x20/\x20(dimension.y\x20*\x20dimension.z));\x0a\x20\x20\x20\x20return\x20normalizedIndex2D;\x0a}\x0a\x0afloat\x20getWind(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20vec2\x20normalizedIndex2D\x20=\x20mapPositionToNormalizedIndex2D(lonLatLev);\x0a\x20\x20\x20\x20float\x20result\x20=\x20texture2D(windTexture,\x20normalizedIndex2D).r;\x0a\x20\x20\x20\x20return\x20result;\x0a}\x0a\x0aconst\x20mat4\x20kernelMatrix\x20=\x20mat4(\x0a\x20\x20\x20\x200.0,\x20-1.0,\x202.0,\x20-1.0,\x20//\x20first\x20column\x0a\x20\x20\x20\x202.0,\x200.0,\x20-5.0,\x203.0,\x20//\x20second\x20column\x0a\x20\x20\x20\x200.0,\x201.0,\x204.0,\x20-3.0,\x20//\x20third\x20column\x0a\x20\x20\x20\x200.0,\x200.0,\x20-1.0,\x201.0\x20//\x20fourth\x20column\x0a);\x0afloat\x20oneDimensionInterpolation(float\x20t,\x20float\x20p0,\x20float\x20p1,\x20float\x20p2,\x20float\x20p3)\x20{\x0a\x20\x20\x20\x20vec4\x20tVec4\x20=\x20vec4(1.0,\x20t,\x20t\x20*\x20t,\x20t\x20*\x20t\x20*\x20t);\x0a\x20\x20\x20\x20tVec4\x20=\x20tVec4\x20/\x202.0;\x0a\x20\x20\x20\x20vec4\x20pVec4\x20=\x20vec4(p0,\x20p1,\x20p2,\x20p3);\x0a\x20\x20\x20\x20return\x20dot((tVec4\x20*\x20kernelMatrix),\x20pVec4);\x0a}\x0a\x0afloat\x20calculateB(sampler2D\x20windTexture,\x20float\x20t,\x20float\x20lon,\x20float\x20lat,\x20float\x20lev)\x20{\x0a\x20\x20\x20\x20float\x20lon0\x20=\x20floor(lon)\x20-\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon1\x20=\x20floor(lon);\x0a\x20\x20\x20\x20float\x20lon2\x20=\x20floor(lon)\x20+\x201.0\x20*\x20interval.x;\x0a\x20\x20\x20\x20float\x20lon3\x20=\x20floor(lon)\x20+\x202.0\x20*\x20interval.x;\x0a\x0a\x20\x20\x20\x20float\x20p0\x20=\x20getWind(windTexture,\x20vec3(lon0,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p1\x20=\x20getWind(windTexture,\x20vec3(lon1,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p2\x20=\x20getWind(windTexture,\x20vec3(lon2,\x20lat,\x20lev));\x0a\x20\x20\x20\x20float\x20p3\x20=\x20getWind(windTexture,\x20vec3(lon3,\x20lat,\x20lev));\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(t,\x20p0,\x20p1,\x20p2,\x20p3);\x0a}\x0a\x0afloat\x20interpolateOneTexture(sampler2D\x20windTexture,\x20vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20float\x20lon\x20=\x20lonLatLev.x;\x0a\x20\x20\x20\x20float\x20lat\x20=\x20lonLatLev.y;\x0a\x20\x20\x20\x20float\x20lev\x20=\x20lonLatLev.z;\x0a\x0a\x20\x20\x20\x20float\x20lat0\x20=\x20floor(lat)\x20-\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat1\x20=\x20floor(lat);\x0a\x20\x20\x20\x20float\x20lat2\x20=\x20floor(lat)\x20+\x201.0\x20*\x20interval.y;\x0a\x20\x20\x20\x20float\x20lat3\x20=\x20floor(lat)\x20+\x202.0\x20*\x20interval.y;\x0a\x0a\x20\x20\x20\x20vec2\x20coefficient\x20=\x20lonLatLev.xy\x20-\x20floor(lonLatLev.xy);\x0a\x20\x20\x20\x20float\x20b0\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat0,\x20lev);\x0a\x20\x20\x20\x20float\x20b1\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat1,\x20lev);\x0a\x20\x20\x20\x20float\x20b2\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat2,\x20lev);\x0a\x20\x20\x20\x20float\x20b3\x20=\x20calculateB(windTexture,\x20coefficient.x,\x20lon,\x20lat3,\x20lev);\x0a\x0a\x20\x20\x20\x20return\x20oneDimensionInterpolation(coefficient.y,\x20b0,\x20b1,\x20b2,\x20b3);\x0a}\x0a\x0avec3\x20bicubic(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20https://en.wikipedia.org/wiki/Bicubic_interpolation#Bicubic_convolution_algorithm\x0a\x20\x20\x20\x20float\x20u\x20=\x20interpolateOneTexture(U,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20v\x20=\x20interpolateOneTexture(V,\x20lonLatLev);\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20return\x20vec3(u,\x20v,\x20w);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture2D(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20windVector\x20=\x20bicubic(lonLatLev);\x0a\x20\x20\x20\x20gl_FragColor\x20=\x20vec4(windVector,\x200.0);\x0a}',updateSpeed_frag=_0x2b2e19(0x1cd),updatePosition_frag='uniform\x20sampler2D\x20currentParticlesPosition;\x20//\x20(lon,\x20lat,\x20lev)\x0auniform\x20sampler2D\x20currentParticlesSpeed;\x20//\x20(u,\x20v,\x20w,\x20normalization)\x0a\x0avarying\x20vec2\x20v_textureCoordinates;\x0a\x0avec2\x20lengthOfLonLat(vec3\x20lonLatLev)\x20{\x0a\x20\x20\x20\x20//\x20unit\x20conversion:\x20meters\x20->\x20longitude\x20latitude\x20degrees\x0a\x20\x20\x20\x20//\x20see\x20https://en.wikipedia.org/wiki/Geographic_coordinate_system#Length_of_a_degree\x20for\x20detail\x0a\x0a\x20\x20\x20\x20//\x20Calculate\x20the\x20length\x20of\x20a\x20degree\x20of\x20latitude\x20and\x20longitude\x20in\x20meters\x0a\x20\x20\x20\x20float\x20latitude\x20=\x20radians(lonLatLev.y);\x0a\x0a\x20\x20\x20\x20float\x20term1\x20=\x20111132.92;\x0a\x20\x20\x20\x20float\x20term2\x20=\x20559.82\x20*\x20cos(2.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term3\x20=\x201.175\x20*\x20cos(4.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term4\x20=\x200.0023\x20*\x20cos(6.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20latLength\x20=\x20term1\x20-\x20term2\x20+\x20term3\x20-\x20term4;\x0a\x0a\x20\x20\x20\x20float\x20term5\x20=\x20111412.84\x20*\x20cos(latitude);\x0a\x20\x20\x20\x20float\x20term6\x20=\x2093.5\x20*\x20cos(3.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20term7\x20=\x200.118\x20*\x20cos(5.0\x20*\x20latitude);\x0a\x20\x20\x20\x20float\x20longLength\x20=\x20term5\x20-\x20term6\x20+\x20term7;\x0a\x0a\x20\x20\x20\x20return\x20vec2(longLength,\x20latLength);\x0a}\x0a\x0avoid\x20updatePosition(vec3\x20lonLatLev,\x20vec3\x20speed)\x20{\x0a\x20\x20\x20\x20vec2\x20lonLatLength\x20=\x20lengthOfLonLat(lonLatLev);\x0a\x20\x20\x20\x20float\x20u\x20=\x20speed.x\x20/\x20lonLatLength.x;\x0a\x20\x20\x20\x20float\x20v\x20=\x20speed.y\x20/\x20lonLatLength.y;\x0a\x20\x20\x20\x20float\x20w\x20=\x200.0;\x0a\x20\x20\x20\x20vec3\x20windVectorInLonLatLev\x20=\x20vec3(u,\x20v,\x20w);\x0a\x0a\x20\x20\x20\x20vec3\x20nextParticle\x20=\x20lonLatLev\x20+\x20windVectorInLonLatLev;\x0a\x0a\x20\x20\x20\x20gl_FragColor\x20=\x20vec4(nextParticle,\x200.0);\x0a}\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20//\x20texture\x20coordinate\x20must\x20be\x20normalized\x0a\x20\x20\x20\x20vec3\x20lonLatLev\x20=\x20texture2D(currentParticlesPosition,\x20v_textureCoordinates).rgb;\x0a\x20\x20\x20\x20vec3\x20speed\x20=\x20texture2D(currentParticlesSpeed,\x20v_textureCoordinates).rgb;\x0a\x0a\x20\x20\x20\x20updatePosition(lonLatLev,\x20speed);\x0a}',postProcessingPosition_frag=_0x2b2e19(0x123),postProcessingSpeed_frag='uniform\x20sampler2D\x20postProcessingPosition;\x0auniform\x20sampler2D\x20nextParticlesSpeed;\x0a\x0avarying\x20vec2\x20v_textureCoordinates;\x0a\x0avoid\x20main()\x20{\x0a\x20\x20\x20\x20vec4\x20randomParticle\x20=\x20texture2D(postProcessingPosition,\x20v_textureCoordinates);\x0a\x20\x20\x20\x20vec4\x20particleSpeed\x20=\x20texture2D(nextParticlesSpeed,\x20v_textureCoordinates);\x0a\x0a\x20\x20\x20\x20if\x20(randomParticle.a\x20>\x200.0)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_FragColor\x20=\x20vec4(0.0);\x0a\x20\x20\x20\x20}\x20else\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20gl_FragColor\x20=\x20particleSpeed;\x0a\x20\x20\x20\x20}\x0a}',Cesium$3=mars3d__namespace['Cesium'],ParticlesComputing=(function(){var _0x34e146=_0x2b2e19;function _0x15861c(_0x49acb2,_0x3ac8bb,_0x52eab5,_0x324ac0){var _0x39680b=_0x5b8f;_classCallCheck(this,_0x15861c),this[_0x39680b(0xe9)]=_0x3ac8bb,this[_0x39680b(0x18a)](_0x49acb2,_0x3ac8bb),this[_0x39680b(0xba)](_0x49acb2,_0x52eab5,_0x324ac0),this[_0x39680b(0xc2)](_0x3ac8bb,_0x52eab5,_0x324ac0);}return _createClass(_0x15861c,[{'key':'createWindTextures','value':function _0x5d8c0e(_0x1ac87b,_0x1fbcc2){var _0x37ec5d=_0x5b8f,_0x27d9bb={'context':_0x1ac87b,'width':_0x1fbcc2[_0x37ec5d(0x176)][_0x37ec5d(0x214)],'height':_0x1fbcc2[_0x37ec5d(0x176)]['lat']*(_0x1fbcc2[_0x37ec5d(0x176)][_0x37ec5d(0x191)]||0x1),'pixelFormat':Cesium$3['PixelFormat']['LUMINANCE'],'pixelDatatype':Cesium$3[_0x37ec5d(0x21c)]['FLOAT'],'flipY':![],'sampler':new Cesium$3[(_0x37ec5d(0x11d))]({'minificationFilter':Cesium$3[_0x37ec5d(0x145)][_0x37ec5d(0xb4)],'magnificationFilter':Cesium$3['TextureMagnificationFilter'][_0x37ec5d(0xb4)]})};this[_0x37ec5d(0x1e4)]={'U':Util[_0x37ec5d(0x226)](_0x27d9bb,_0x1fbcc2['U'][_0x37ec5d(0xe5)]),'V':Util[_0x37ec5d(0x226)](_0x27d9bb,_0x1fbcc2['V'][_0x37ec5d(0xe5)])};}},{'key':_0x34e146(0xba),'value':function _0x504bf3(_0x2b6b95,_0x659b6c,_0x2a68b7){var _0x31ac1b=_0x34e146,_0x3c901e={'context':_0x2b6b95,'width':_0x659b6c['particlesTextureSize'],'height':_0x659b6c[_0x31ac1b(0x1bd)],'pixelFormat':Cesium$3['PixelFormat'][_0x31ac1b(0x1e7)],'pixelDatatype':Cesium$3[_0x31ac1b(0x21c)][_0x31ac1b(0x193)],'flipY':![],'sampler':new Cesium$3[(_0x31ac1b(0x11d))]({'minificationFilter':Cesium$3[_0x31ac1b(0x145)][_0x31ac1b(0xb4)],'magnificationFilter':Cesium$3[_0x31ac1b(0x177)][_0x31ac1b(0xb4)]})},_0x2ff571=this[_0x31ac1b(0x152)](_0x659b6c[_0x31ac1b(0x1f3)],_0x2a68b7),_0xf16364=new Float32Array(0x4*_0x659b6c[_0x31ac1b(0x1f3)])[_0x31ac1b(0xb5)](0x0);this[_0x31ac1b(0x183)]={'particlesWind':Util[_0x31ac1b(0x226)](_0x3c901e),'currentParticlesPosition':Util['createTexture'](_0x3c901e,_0x2ff571),'nextParticlesPosition':Util['createTexture'](_0x3c901e,_0x2ff571),'currentParticlesSpeed':Util[_0x31ac1b(0x226)](_0x3c901e,_0xf16364),'nextParticlesSpeed':Util['createTexture'](_0x3c901e,_0xf16364),'postProcessingPosition':Util['createTexture'](_0x3c901e,_0x2ff571),'postProcessingSpeed':Util['createTexture'](_0x3c901e,_0xf16364)};}},{'key':_0x34e146(0x152),'value':function _0x4733ab(_0x5a0979,_0x444492){var _0x4499f3=_0x34e146,_0x30299a=new Float32Array(0x4*_0x5a0979);for(var _0x173afe=0x0;_0x173afe<_0x5a0979;_0x173afe++){_0x30299a[0x4*_0x173afe]=Cesium$3[_0x4499f3(0x12b)]['randomBetween'](_0x444492[_0x4499f3(0x1f8)]['x'],_0x444492[_0x4499f3(0x1f8)]['y']),_0x30299a[0x4*_0x173afe+0x1]=Cesium$3[_0x4499f3(0x12b)]['randomBetween'](_0x444492[_0x4499f3(0x132)]['x'],_0x444492[_0x4499f3(0x132)]['y']),_0x30299a[0x4*_0x173afe+0x2]=Cesium$3[_0x4499f3(0x12b)][_0x4499f3(0x174)](this[_0x4499f3(0xe9)][_0x4499f3(0x191)]['min'],this[_0x4499f3(0xe9)][_0x4499f3(0x191)][_0x4499f3(0xcc)]),_0x30299a[0x4*_0x173afe+0x3]=0x0;}return _0x30299a;}},{'key':_0x34e146(0x1a9),'value':function _0x2ee068(){var _0x1e44cf=_0x34e146,_0x1f557d=this;Object[_0x1e44cf(0x1ec)](this[_0x1e44cf(0x183)])[_0x1e44cf(0x1b8)](function(_0x3a9b48){_0x1f557d['particlesTextures'][_0x3a9b48]['destroy']();});}},{'key':_0x34e146(0xc2),'value':function _0x325fe0(_0x4590bc,_0x52628c,_0x5a46d4){var _0x4b6916=_0x34e146,_0x17171f=new Cesium$3[(_0x4b6916(0x162))](_0x4590bc[_0x4b6916(0x176)]['lon'],_0x4590bc[_0x4b6916(0x176)][_0x4b6916(0xd9)],_0x4590bc[_0x4b6916(0x176)][_0x4b6916(0x191)]),_0x4919a6=new Cesium$3[(_0x4b6916(0x162))](_0x4590bc[_0x4b6916(0x214)]['min'],_0x4590bc[_0x4b6916(0xd9)]['min'],_0x4590bc['lev'][_0x4b6916(0x15c)]),_0x5a359b=new Cesium$3[(_0x4b6916(0x162))](_0x4590bc[_0x4b6916(0x214)]['max'],_0x4590bc[_0x4b6916(0xd9)]['max'],_0x4590bc[_0x4b6916(0x191)][_0x4b6916(0xcc)]),_0x5e6ade=new Cesium$3[(_0x4b6916(0x162))]((_0x5a359b['x']-_0x4919a6['x'])/(_0x17171f['x']-0x1),(_0x5a359b['y']-_0x4919a6['y'])/(_0x17171f['y']-0x1),_0x17171f['z']>0x1?(_0x5a359b['z']-_0x4919a6['z'])/(_0x17171f['z']-0x1):0x1),_0x2b7043=new Cesium$3[(_0x4b6916(0x1cc))](_0x4590bc['U']['min'],_0x4590bc['U'][_0x4b6916(0xcc)]),_0xec605d=new Cesium$3['Cartesian2'](_0x4590bc['V']['min'],_0x4590bc['V']['max']),_0x49afd4=this;this[_0x4b6916(0x11c)]={'getWind':new CustomPrimitive({'commandType':_0x4b6916(0x19d),'uniformMap':{'U':function _0x5c0d81(){var _0xf809f7=_0x4b6916;return _0x49afd4[_0xf809f7(0x1e4)]['U'];},'V':function _0x3cf405(){var _0x5323b0=_0x4b6916;return _0x49afd4[_0x5323b0(0x1e4)]['V'];},'currentParticlesPosition':function _0x2715f9(){return _0x49afd4['particlesTextures']['currentParticlesPosition'];},'dimension':function _0x5bb44a(){return _0x17171f;},'minimum':function _0x53985e(){return _0x4919a6;},'maximum':function _0x592033(){return _0x5a359b;},'interval':function _0x22d5d5(){return _0x5e6ade;}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[getWind_frag]}),'outputTexture':this['particlesTextures'][_0x4b6916(0x1c4)],'preExecute':function _0x1b56c5(){var _0x514831=_0x4b6916;_0x49afd4[_0x514831(0x11c)]['getWind'][_0x514831(0x175)]['outputTexture']=_0x49afd4[_0x514831(0x183)][_0x514831(0x1c4)];}}),'updateSpeed':new CustomPrimitive({'commandType':_0x4b6916(0x19d),'uniformMap':{'currentParticlesSpeed':function _0x5d68f7(){var _0x1db5f4=_0x4b6916;return _0x49afd4[_0x1db5f4(0x183)][_0x1db5f4(0x225)];},'particlesWind':function _0x17d9ff(){var _0x3b12c9=_0x4b6916;return _0x49afd4[_0x3b12c9(0x183)]['particlesWind'];},'uSpeedRange':function _0x180a24(){return _0x2b7043;},'vSpeedRange':function _0x58d932(){return _0xec605d;},'pixelSize':function _0x435e73(){return _0x5a46d4['pixelSize'];},'speedFactor':function _0x359803(){var _0x22cd6f=_0x4b6916;return _0x52628c[_0x22cd6f(0x11f)];}},'fragmentShaderSource':new Cesium$3['ShaderSource']({'sources':[updateSpeed_frag]}),'outputTexture':this[_0x4b6916(0x183)][_0x4b6916(0xf7)],'preExecute':function _0x12487a(){var _0x12c5dd=_0x4b6916,_0x20ac0e=_0x49afd4[_0x12c5dd(0x183)][_0x12c5dd(0x225)];_0x49afd4['particlesTextures'][_0x12c5dd(0x225)]=_0x49afd4['particlesTextures'][_0x12c5dd(0xf1)],_0x49afd4['particlesTextures'][_0x12c5dd(0xf1)]=_0x20ac0e,_0x49afd4['primitives'][_0x12c5dd(0x125)]['commandToExecute'][_0x12c5dd(0x199)]=_0x49afd4['particlesTextures'][_0x12c5dd(0xf7)];}}),'updatePosition':new CustomPrimitive({'commandType':_0x4b6916(0x19d),'uniformMap':{'currentParticlesPosition':function _0x1f1f65(){return _0x49afd4['particlesTextures']['currentParticlesPosition'];},'currentParticlesSpeed':function _0x21ac68(){var _0x62c1bc=_0x4b6916;return _0x49afd4[_0x62c1bc(0x183)][_0x62c1bc(0x225)];}},'fragmentShaderSource':new Cesium$3[(_0x4b6916(0xf2))]({'sources':[updatePosition_frag]}),'outputTexture':this[_0x4b6916(0x183)][_0x4b6916(0xff)],'preExecute':function _0x134610(){var _0x15edd5=_0x4b6916,_0x5c5961=_0x49afd4[_0x15edd5(0x183)][_0x15edd5(0xeb)];_0x49afd4[_0x15edd5(0x183)][_0x15edd5(0xeb)]=_0x49afd4[_0x15edd5(0x183)][_0x15edd5(0x118)],_0x49afd4[_0x15edd5(0x183)][_0x15edd5(0x118)]=_0x5c5961,_0x49afd4['primitives'][_0x15edd5(0x15b)][_0x15edd5(0x175)][_0x15edd5(0x199)]=_0x49afd4['particlesTextures'][_0x15edd5(0xff)];}}),'postProcessingPosition':new CustomPrimitive({'commandType':_0x4b6916(0x19d),'uniformMap':{'nextParticlesPosition':function _0x5b631b(){return _0x49afd4['particlesTextures']['nextParticlesPosition'];},'nextParticlesSpeed':function _0x489223(){var _0x3fa3e6=_0x4b6916;return _0x49afd4[_0x3fa3e6(0x183)]['nextParticlesSpeed'];},'lonRange':function _0x5df0c4(){var _0x29ac10=_0x4b6916;return _0x5a46d4[_0x29ac10(0x1f8)];},'latRange':function _0x108436(){var _0x3cba67=_0x4b6916;return _0x5a46d4[_0x3cba67(0x132)];},'randomCoefficient':function _0x304637(){var _0x34702f=_0x4b6916,_0x1e1050=Math[_0x34702f(0x10b)]();return _0x1e1050;},'dropRate':function _0x28c3f7(){var _0x2cc71d=_0x4b6916;return _0x52628c[_0x2cc71d(0x1e5)];},'dropRateBump':function _0x12fa5c(){var _0x28b622=_0x4b6916;return _0x52628c[_0x28b622(0x210)];}},'fragmentShaderSource':new Cesium$3[(_0x4b6916(0xf2))]({'sources':[postProcessingPosition_frag]}),'outputTexture':this[_0x4b6916(0x183)][_0x4b6916(0x118)],'preExecute':function _0xd4ef64(){var _0x19e78f=_0x4b6916;_0x49afd4['primitives'][_0x19e78f(0x118)][_0x19e78f(0x175)][_0x19e78f(0x199)]=_0x49afd4[_0x19e78f(0x183)][_0x19e78f(0x118)];}}),'postProcessingSpeed':new CustomPrimitive({'commandType':_0x4b6916(0x19d),'uniformMap':{'postProcessingPosition':function _0x4b15dd(){var _0x18d24d=_0x4b6916;return _0x49afd4[_0x18d24d(0x183)]['postProcessingPosition'];},'nextParticlesSpeed':function _0x5a2f24(){var _0xef6c62=_0x4b6916;return _0x49afd4[_0xef6c62(0x183)]['nextParticlesSpeed'];}},'fragmentShaderSource':new Cesium$3[(_0x4b6916(0xf2))]({'sources':[postProcessingSpeed_frag]}),'outputTexture':this[_0x4b6916(0x183)][_0x4b6916(0xf1)],'preExecute':function _0x238a02(){var _0x4cddd2=_0x4b6916;_0x49afd4['primitives']['postProcessingSpeed'][_0x4cddd2(0x175)][_0x4cddd2(0x199)]=_0x49afd4[_0x4cddd2(0x183)][_0x4cddd2(0xf1)];}})};}}]),_0x15861c;}()),Cesium$2=mars3d__namespace['Cesium'],ParticleSystem=(function(){var _0x22a114=_0x2b2e19;function _0x33d68c(_0x1962c2,_0x3aa797,_0x4333d7,_0x34544d){var _0x31bd34=_0x5b8f;_classCallCheck(this,_0x33d68c),this['context']=_0x1962c2,_0x3aa797=_objectSpread2({},_0x3aa797);if(_0x3aa797[_0x31bd34(0x122)]&&_0x3aa797[_0x31bd34(0x1de)]){var _0x6eb7d9,_0x54ebe4,_0x1e5b50,_0x5a7eb7,_0x35a1e8,_0x526bd7;_0x3aa797['dimensions']={},_0x3aa797[_0x31bd34(0x176)][_0x31bd34(0x214)]=_0x3aa797[_0x31bd34(0x165)],_0x3aa797[_0x31bd34(0x176)]['lat']=_0x3aa797[_0x31bd34(0x1f5)],_0x3aa797[_0x31bd34(0x176)][_0x31bd34(0x191)]=_0x3aa797['lev']||0x1,_0x3aa797[_0x31bd34(0x214)]={},_0x3aa797['lon'][_0x31bd34(0x15c)]=_0x3aa797['xmin'],_0x3aa797[_0x31bd34(0x214)][_0x31bd34(0xcc)]=_0x3aa797[_0x31bd34(0x18e)],_0x3aa797[_0x31bd34(0xd9)]={},_0x3aa797[_0x31bd34(0xd9)][_0x31bd34(0x15c)]=_0x3aa797[_0x31bd34(0xd6)],_0x3aa797[_0x31bd34(0xd9)][_0x31bd34(0xcc)]=_0x3aa797[_0x31bd34(0x209)],_0x3aa797[_0x31bd34(0x191)]={},_0x3aa797['lev'][_0x31bd34(0x15c)]=(_0x6eb7d9=_0x3aa797[_0x31bd34(0x20e)])!==null&&_0x6eb7d9!==void 0x0?_0x6eb7d9:0x1,_0x3aa797[_0x31bd34(0x191)][_0x31bd34(0xcc)]=(_0x54ebe4=_0x3aa797['levmax'])!==null&&_0x54ebe4!==void 0x0?_0x54ebe4:0x1,_0x3aa797['U']={},_0x3aa797['U'][_0x31bd34(0xe5)]=new Float32Array(_0x3aa797[_0x31bd34(0x122)]),_0x3aa797['U']['min']=(_0x1e5b50=_0x3aa797[_0x31bd34(0x137)])!==null&&_0x1e5b50!==void 0x0?_0x1e5b50:Math['min'][_0x31bd34(0x140)](Math,_toConsumableArray(_0x3aa797[_0x31bd34(0x122)])),_0x3aa797['U']['max']=(_0x5a7eb7=_0x3aa797[_0x31bd34(0x22d)])!==null&&_0x5a7eb7!==void 0x0?_0x5a7eb7:Math[_0x31bd34(0xcc)][_0x31bd34(0x140)](Math,_toConsumableArray(_0x3aa797[_0x31bd34(0x122)])),_0x3aa797['V']={},_0x3aa797['V'][_0x31bd34(0xe5)]=new Float32Array(_0x3aa797[_0x31bd34(0x1de)]),_0x3aa797['V'][_0x31bd34(0x15c)]=(_0x35a1e8=_0x3aa797[_0x31bd34(0x1ea)])!==null&&_0x35a1e8!==void 0x0?_0x35a1e8:Math[_0x31bd34(0x15c)][_0x31bd34(0x140)](Math,_toConsumableArray(_0x3aa797['vdata'])),_0x3aa797['V'][_0x31bd34(0xcc)]=(_0x526bd7=_0x3aa797['vmax'])!==null&&_0x526bd7!==void 0x0?_0x526bd7:Math[_0x31bd34(0xcc)][_0x31bd34(0x140)](Math,_toConsumableArray(_0x3aa797['vdata']));}this[_0x31bd34(0xe9)]=_0x3aa797,this[_0x31bd34(0x1f9)]=_0x4333d7,this['viewerParameters']=_0x34544d,this[_0x31bd34(0x112)]=new ParticlesComputing(this['context'],this[_0x31bd34(0xe9)],this[_0x31bd34(0x1f9)],this[_0x31bd34(0x18c)]),this[_0x31bd34(0x1cb)]=new ParticlesRendering(this[_0x31bd34(0x158)],this[_0x31bd34(0xe9)],this[_0x31bd34(0x1f9)],this['viewerParameters'],this[_0x31bd34(0x112)]);}return _createClass(_0x33d68c,[{'key':_0x22a114(0x11e),'value':function _0x232f26(_0x19a758){var _0x48230a=_0x22a114,_0x48b809=this;this[_0x48230a(0x112)][_0x48230a(0x1a9)](),Object['keys'](this[_0x48230a(0x112)][_0x48230a(0x1e4)])[_0x48230a(0x1b8)](function(_0x413399){var _0xe87da0=_0x48230a;_0x48b809[_0xe87da0(0x112)]['windTextures'][_0x413399][_0xe87da0(0x1f7)]();}),this['particlesRendering'][_0x48230a(0x22c)]['colorTable']['destroy'](),Object['keys'](this[_0x48230a(0x1cb)][_0x48230a(0xee)])[_0x48230a(0x1b8)](function(_0x42d877){var _0x5411e2=_0x48230a;_0x48b809['particlesRendering'][_0x5411e2(0xee)][_0x42d877][_0x5411e2(0x1f7)]();}),this[_0x48230a(0x158)]=_0x19a758,this[_0x48230a(0x112)]=new ParticlesComputing(this['context'],this[_0x48230a(0xe9)],this[_0x48230a(0x1f9)],this[_0x48230a(0x18c)]),this[_0x48230a(0x1cb)]=new ParticlesRendering(this[_0x48230a(0x158)],this[_0x48230a(0xe9)],this['options'],this['viewerParameters'],this[_0x48230a(0x112)]);}},{'key':_0x22a114(0x17d),'value':function _0x1c6023(){var _0x4e8fb6=_0x22a114,_0x14ec22=this,_0x2b63c4=new Cesium$2[(_0x4e8fb6(0x129))]({'color':new Cesium$2[(_0x4e8fb6(0x139))](0x0,0x0,0x0,0x0),'depth':0x1,'framebuffer':undefined,'pass':Cesium$2[_0x4e8fb6(0xfe)][_0x4e8fb6(0x219)]});Object['keys'](this['particlesRendering'][_0x4e8fb6(0xee)])[_0x4e8fb6(0x1b8)](function(_0x44d23f){var _0x268bd8=_0x4e8fb6;_0x2b63c4['framebuffer']=_0x14ec22[_0x268bd8(0x1cb)][_0x268bd8(0xee)][_0x44d23f],_0x2b63c4['execute'](_0x14ec22['context']);});}},{'key':_0x22a114(0x222),'value':function _0x2905e5(_0x4f6d2f){var _0x193175=_0x22a114;this[_0x193175(0x17d)](),this[_0x193175(0x112)]['destroyParticlesTextures'](),this[_0x193175(0x112)]['createParticlesTextures'](this[_0x193175(0x158)],this[_0x193175(0x1f9)],this[_0x193175(0x18c)]);if(_0x4f6d2f){var _0x3c5f2f=this[_0x193175(0x1cb)][_0x193175(0x180)](this['options']);this[_0x193175(0x1cb)][_0x193175(0x11c)]['segments'][_0x193175(0xe2)]=_0x3c5f2f;var _0x615ee0=Cesium$2[_0x193175(0xfd)][_0x193175(0x196)]({'context':this[_0x193175(0x158)],'geometry':_0x3c5f2f,'attributeLocations':this[_0x193175(0x1cb)][_0x193175(0x11c)][_0x193175(0x208)][_0x193175(0x17c)],'bufferUsage':Cesium$2[_0x193175(0x1f0)][_0x193175(0x15d)]});this[_0x193175(0x1cb)][_0x193175(0x11c)][_0x193175(0x208)][_0x193175(0x175)]['vertexArray']=_0x615ee0;}}},{'key':_0x22a114(0x135),'value':function _0x50b468(_0x273994){var _0x5912e5=_0x22a114,_0x33d33a=this,_0x50ab97=![];this['options'][_0x5912e5(0x1f3)]!==_0x273994[_0x5912e5(0x1f3)]&&(_0x50ab97=!![]),Object['keys'](_0x273994)[_0x5912e5(0x1b8)](function(_0x4ebbf1){var _0x2398b4=_0x5912e5;_0x33d33a[_0x2398b4(0x1f9)][_0x4ebbf1]=_0x273994[_0x4ebbf1];}),this['refreshParticles'](_0x50ab97);}},{'key':_0x22a114(0x17e),'value':function _0x346dde(_0x5369e9){var _0x37934a=_0x22a114,_0x3b07a6=this;Object[_0x37934a(0x1ec)](_0x5369e9)[_0x37934a(0x1b8)](function(_0x4f19e4){var _0x55bd56=_0x37934a;_0x3b07a6[_0x55bd56(0x18c)][_0x4f19e4]=_0x5369e9[_0x4f19e4];}),this[_0x37934a(0x222)](![]);}},{'key':_0x22a114(0x1f7),'value':function _0xfecba1(){var _0x103f09=_0x22a114,_0x518799=this;clearTimeout(this['canrefresh']),this['particlesComputing'][_0x103f09(0x1a9)](),Object['keys'](this[_0x103f09(0x112)][_0x103f09(0x1e4)])[_0x103f09(0x1b8)](function(_0x5352e6){var _0x3facd2=_0x103f09;_0x518799[_0x3facd2(0x112)][_0x3facd2(0x1e4)][_0x5352e6][_0x3facd2(0x1f7)]();}),this[_0x103f09(0x1cb)]['textures']['colorTable'][_0x103f09(0x1f7)](),Object[_0x103f09(0x1ec)](this[_0x103f09(0x1cb)][_0x103f09(0xee)])[_0x103f09(0x1b8)](function(_0x40ab74){var _0x504860=_0x103f09;_0x518799['particlesRendering'][_0x504860(0xee)][_0x40ab74][_0x504860(0x1f7)]();});for(var _0x4bedac in this){delete this[_0x4bedac];}}}]),_0x33d68c;}()),Cesium$1=mars3d__namespace['Cesium'],BaseLayer$1=mars3d__namespace['layer'][_0x2b2e19(0x200)],DEF_OPTIONS={'particlesNumber':0x1000,'fixedHeight':0x0,'fadeOpacity':0.996,'dropRate':0.003,'dropRateBump':0.01,'speedFactor':0.5,'lineWidth':0x2,'colors':[_0x2b2e19(0x1ee)]},WindLayer=function(_0x3fd817){var _0x1f18e8=_0x2b2e19;_inherits(_0x3981cf,_0x3fd817);var _0x2e6190=_createSuper(_0x3981cf);function _0x3981cf(){var _0x4f118f=_0x5b8f,_0x5c7394,_0x26cf44=arguments[_0x4f118f(0x1a7)]>0x0&&arguments[0x0]!==undefined?arguments[0x0]:{};return _classCallCheck(this,_0x3981cf),_0x26cf44=_objectSpread2(_objectSpread2({},DEF_OPTIONS),_0x26cf44),_0x5c7394=_0x2e6190[_0x4f118f(0x151)](this,_0x26cf44),_0x5c7394[_0x4f118f(0x21a)](_0x26cf44),_0x5c7394;}return _createClass(_0x3981cf,[{'key':'layer','get':function _0x14ca00(){var _0x20b621=_0x5b8f;return this[_0x20b621(0x11c)];}},{'key':_0x1f18e8(0xe9),'get':function _0x4cd89a(){var _0x42aa2d=_0x1f18e8;return this[_0x42aa2d(0x1c5)];},'set':function _0x55ccbe(_0x511998){this['setData'](_0x511998);}},{'key':_0x1f18e8(0x16b),'get':function _0x2c22d8(){var _0x4a2fd1=_0x1f18e8;return this[_0x4a2fd1(0x1f9)][_0x4a2fd1(0x16b)];},'set':function _0x578c8f(_0x303777){var _0x58895a=_0x1f18e8;this['options'][_0x58895a(0x16b)]=_0x303777,this['particleSystem']&&this[_0x58895a(0x164)][_0x58895a(0x135)]({'colors':_0x303777}),this['resize']();}},{'key':_0x1f18e8(0x1ff),'value':function _0x5b7df5(){}},{'key':'_addedHook','value':function _0x405e57(){var _0xdbafaa=_0x1f18e8;this[_0xdbafaa(0x19f)]=this[_0xdbafaa(0x1a6)][_0xdbafaa(0x19f)],this[_0xdbafaa(0x15a)]=this[_0xdbafaa(0x1a6)][_0xdbafaa(0x15a)],this[_0xdbafaa(0x11c)]=new Cesium$1[(_0xdbafaa(0xbf))](),this[_0xdbafaa(0x1a6)]['scene'][_0xdbafaa(0x11c)][_0xdbafaa(0xde)](this[_0xdbafaa(0x11c)]),this[_0xdbafaa(0x18c)]={'lonRange':new Cesium$1[(_0xdbafaa(0x1cc))](),'latRange':new Cesium$1[(_0xdbafaa(0x1cc))](),'pixelSize':0x0},this['globeBoundingSphere']=new Cesium$1['BoundingSphere'](Cesium$1[_0xdbafaa(0x162)][_0xdbafaa(0x12e)],0.99*0x615299),this[_0xdbafaa(0x138)](),window['addEventListener'](_0xdbafaa(0xf6),this[_0xdbafaa(0xf6)][_0xdbafaa(0x168)](this),![]),this[_0xdbafaa(0x106)]=![],this['mouse_move']=![],this[_0xdbafaa(0x1a6)]['on'](mars3d__namespace['EventType']['wheel'],this['_onMapWhellEvent'],this),this['_map']['on'](mars3d__namespace[_0xdbafaa(0x192)]['mouseDown'],this['_onMouseDownEvent'],this),this[_0xdbafaa(0x1a6)]['on'](mars3d__namespace[_0xdbafaa(0x192)][_0xdbafaa(0xc1)],this[_0xdbafaa(0xed)],this),this[_0xdbafaa(0x1a6)]['on'](mars3d__namespace['EventType'][_0xdbafaa(0xcf)],this['_onMouseMoveEvent'],this),this[_0xdbafaa(0x1c5)]&&this['setData'](this[_0xdbafaa(0x1c5)]);}},{'key':_0x1f18e8(0x19e),'value':function _0x2f7c02(){var _0x5502e5=_0x1f18e8;window[_0x5502e5(0x22b)](_0x5502e5(0xf6),this[_0x5502e5(0xf6)]),this[_0x5502e5(0x1a6)][_0x5502e5(0x13e)](mars3d__namespace[_0x5502e5(0x192)][_0x5502e5(0xe7)],this['_onMap_preRenderEvent'],this),this['_map'][_0x5502e5(0x13e)](mars3d__namespace['EventType'][_0x5502e5(0xdc)],this[_0x5502e5(0x10f)],this),this[_0x5502e5(0x1a6)]['off'](mars3d__namespace['EventType'][_0x5502e5(0x136)],this[_0x5502e5(0x1ef)],this),this['_map'][_0x5502e5(0x13e)](mars3d__namespace[_0x5502e5(0x192)][_0x5502e5(0xc1)],this[_0x5502e5(0xed)],this),this[_0x5502e5(0x1a6)][_0x5502e5(0x13e)](mars3d__namespace[_0x5502e5(0x192)][_0x5502e5(0xcf)],this['_onMouseMoveEvent'],this),this['primitives'][_0x5502e5(0x223)](),this[_0x5502e5(0x1a6)][_0x5502e5(0x19f)][_0x5502e5(0x11c)][_0x5502e5(0xe6)](this[_0x5502e5(0x11c)]);}},{'key':'resize','value':function _0x9fca50(){var _0x4dd4f6=_0x1f18e8;if(!this[_0x4dd4f6(0x13b)]||!this[_0x4dd4f6(0x164)])return;this[_0x4dd4f6(0x11c)][_0x4dd4f6(0x13b)]=![],this['primitives'][_0x4dd4f6(0x223)](),this['_map']['once'](mars3d__namespace[_0x4dd4f6(0x192)][_0x4dd4f6(0xe7)],this['_onMap_preRenderEvent'],this);}},{'key':_0x1f18e8(0x10a),'value':function _0x5a6071(_0x51b78d){var _0x45ac8f=_0x1f18e8;this['particleSystem'][_0x45ac8f(0x11e)](this[_0x45ac8f(0x19f)]['context']),this[_0x45ac8f(0xbd)](),this['primitives'][_0x45ac8f(0x13b)]=!![];}},{'key':_0x1f18e8(0x10f),'value':function _0x58d563(_0x5867de){var _0x4343ef=_0x1f18e8,_0x3db85d=this;clearTimeout(this[_0x4343ef(0x13d)]);if(!this[_0x4343ef(0x13b)]||!this['particleSystem'])return;this[_0x4343ef(0x11c)]['show']=![],this[_0x4343ef(0x13d)]=setTimeout(function(){var _0x492726=_0x4343ef;if(!_0x3db85d[_0x492726(0x13b)])return;_0x3db85d[_0x492726(0x1c9)]();},0xc8);}},{'key':_0x1f18e8(0x1ef),'value':function _0x5931c4(_0x529f7b){var _0x4b5c8d=_0x1f18e8;this[_0x4b5c8d(0x106)]=!![];}},{'key':_0x1f18e8(0x19b),'value':function _0x4d77ed(_0x3d286c){var _0x110c15=_0x1f18e8;if(!this[_0x110c15(0x13b)]||!this['particleSystem'])return;this[_0x110c15(0x106)]&&(this[_0x110c15(0x11c)][_0x110c15(0x13b)]=![],this[_0x110c15(0x173)]=!![]);}},{'key':'_onMouseUpEvent','value':function _0x48ae34(_0x6fa45b){var _0x2c264d=_0x1f18e8;if(!this[_0x2c264d(0x13b)]||!this[_0x2c264d(0x164)])return;this[_0x2c264d(0x106)]&&this[_0x2c264d(0x173)]&&this[_0x2c264d(0x1c9)](),this['primitives'][_0x2c264d(0x13b)]=!![],this[_0x2c264d(0x106)]=![],this[_0x2c264d(0x173)]=![];}},{'key':_0x1f18e8(0x1c9),'value':function _0x34d2dc(){var _0x280060=_0x1f18e8;if(!this[_0x280060(0x1a6)]||!this[_0x280060(0x13b)])return;this[_0x280060(0x138)](),this[_0x280060(0x164)][_0x280060(0x17e)](this['viewerParameters']),this[_0x280060(0x11c)][_0x280060(0x13b)]=!![];}},{'key':_0x1f18e8(0xf9),'value':function _0x20e977(_0x1b04a9){var _0x33d9f5=_0x1f18e8;this[_0x33d9f5(0x1c5)]=_0x1b04a9,this[_0x33d9f5(0x164)]&&this[_0x33d9f5(0x164)][_0x33d9f5(0x1f7)](),this['particleSystem']=new ParticleSystem(this[_0x33d9f5(0x19f)][_0x33d9f5(0x158)],_0x1b04a9,this['getOptions'](),this[_0x33d9f5(0x18c)]),this[_0x33d9f5(0xbd)]();}},{'key':_0x1f18e8(0x21a),'value':function _0x15b673(_0x5a49c1){var _0x1465df=_0x1f18e8;if(_0x5a49c1)for(var _0x11ef7a in _0x5a49c1){this[_0x11ef7a]=_0x5a49c1[_0x11ef7a];}this[_0x1465df(0x164)]&&this[_0x1465df(0x164)][_0x1465df(0x135)](this[_0x1465df(0x224)]());}},{'key':'getOptions','value':function _0x54c3eb(){var _0x5ba673=_0x1f18e8,_0x21c96c=Math[_0x5ba673(0x189)](Math[_0x5ba673(0xbb)](this['particlesNumber']));return this[_0x5ba673(0x13a)]=_0x21c96c*_0x21c96c,{'particlesTextureSize':_0x21c96c,'maxParticles':this[_0x5ba673(0x13a)],'particleHeight':this['fixedHeight'],'fadeOpacity':this[_0x5ba673(0x1f4)],'dropRate':this[_0x5ba673(0x1e5)],'dropRateBump':this['dropRateBump'],'speedFactor':this[_0x5ba673(0x11f)],'lineWidth':this[_0x5ba673(0x16e)],'globeLayer':this[_0x5ba673(0x187)],'WMS_URL':this[_0x5ba673(0xfb)],'colors':this['colors']};}},{'key':'addPrimitives','value':function _0xd9c0ad(){var _0x21395d=_0x1f18e8;this[_0x21395d(0x11c)][_0x21395d(0xde)](this[_0x21395d(0x164)]['particlesComputing'][_0x21395d(0x11c)][_0x21395d(0x233)]),this['primitives'][_0x21395d(0xde)](this[_0x21395d(0x164)][_0x21395d(0x112)][_0x21395d(0x11c)][_0x21395d(0x125)]),this[_0x21395d(0x11c)][_0x21395d(0xde)](this[_0x21395d(0x164)]['particlesComputing'][_0x21395d(0x11c)][_0x21395d(0x15b)]),this[_0x21395d(0x11c)][_0x21395d(0xde)](this[_0x21395d(0x164)][_0x21395d(0x112)]['primitives'][_0x21395d(0x118)]),this[_0x21395d(0x11c)][_0x21395d(0xde)](this['particleSystem'][_0x21395d(0x112)][_0x21395d(0x11c)][_0x21395d(0xf1)]),this['primitives']['add'](this[_0x21395d(0x164)]['particlesRendering'][_0x21395d(0x11c)][_0x21395d(0x208)]),this[_0x21395d(0x11c)][_0x21395d(0xde)](this[_0x21395d(0x164)][_0x21395d(0x1cb)]['primitives'][_0x21395d(0xe4)]),this[_0x21395d(0x11c)][_0x21395d(0xde)](this[_0x21395d(0x164)][_0x21395d(0x1cb)]['primitives'][_0x21395d(0x220)]);}},{'key':'updateViewerParameters','value':function _0x2bb320(){var _0x5f47f8=_0x1f18e8,_0x472256=this[_0x5f47f8(0x15a)]['computeViewRectangle'](this[_0x5f47f8(0x19f)][_0x5f47f8(0x1b1)][_0x5f47f8(0x14f)]);if(!_0x472256){var _0x1445be=this[_0x5f47f8(0x1a6)]['getExtent']();_0x472256=Cesium$1[_0x5f47f8(0xcb)]['fromDegrees'](_0x1445be['xmin'],_0x1445be['ymin'],_0x1445be[_0x5f47f8(0x18e)],_0x1445be[_0x5f47f8(0x209)]);}var _0x5df86a=Util[_0x5f47f8(0x169)](_0x472256);this['viewerParameters']['lonRange']['x']=_0x5df86a[_0x5f47f8(0x214)]['min'],this[_0x5f47f8(0x18c)][_0x5f47f8(0x1f8)]['y']=_0x5df86a[_0x5f47f8(0x214)][_0x5f47f8(0xcc)],this['viewerParameters'][_0x5f47f8(0x132)]['x']=_0x5df86a['lat'][_0x5f47f8(0x15c)],this[_0x5f47f8(0x18c)][_0x5f47f8(0x132)]['y']=_0x5df86a[_0x5f47f8(0xd9)][_0x5f47f8(0xcc)];var _0x19a390=this[_0x5f47f8(0x15a)][_0x5f47f8(0xc4)](this[_0x5f47f8(0x1c3)],this[_0x5f47f8(0x19f)]['drawingBufferWidth'],this[_0x5f47f8(0x19f)][_0x5f47f8(0x18f)]);_0x19a390>0x0&&(this[_0x5f47f8(0x18c)]['pixelSize']=_0x19a390);}}]),_0x3981cf;}(BaseLayer$1);mars3d__namespace[_0x2b2e19(0x1b9)][_0x2b2e19(0xf8)](_0x2b2e19(0x13c),WindLayer),mars3d__namespace[_0x2b2e19(0x1d3)][_0x2b2e19(0x1d7)]=WindLayer;var CanvasParticle=_createClass(function CanvasParticle(){var _0x5dcebf=_0x2b2e19;_classCallCheck(this,CanvasParticle),this[_0x5dcebf(0x14c)]=null,this[_0x5dcebf(0xd9)]=null,this[_0x5dcebf(0x126)]=null,this['tlat']=null,this['age']=null;}),CanvasWindField=(function(){var _0x2863c2=_0x2b2e19;function _0x5ac9b5(_0x4fa23f,_0x1dc28e){var _0x59d3d4=_0x5b8f;_classCallCheck(this,_0x5ac9b5),this[_0x59d3d4(0x1f5)]=_0x4fa23f[_0x59d3d4(0x1f5)],this[_0x59d3d4(0x165)]=_0x4fa23f[_0x59d3d4(0x165)],this[_0x59d3d4(0x1bb)]=_0x4fa23f['xmin'],this[_0x59d3d4(0x18e)]=_0x4fa23f[_0x59d3d4(0x18e)],this[_0x59d3d4(0xd6)]=_0x4fa23f[_0x59d3d4(0xd6)],this[_0x59d3d4(0x209)]=_0x4fa23f[_0x59d3d4(0x209)],this['grid']=[];var _0x122358=_0x4fa23f['udata'],_0x147ae4=_0x4fa23f[_0x59d3d4(0x1de)],_0x57dab4=![];_0x122358[_0x59d3d4(0x1a7)]===this[_0x59d3d4(0x1f5)]&&_0x122358[0x0][_0x59d3d4(0x1a7)]===this[_0x59d3d4(0x165)]&&(_0x57dab4=!![]);var _0x430052=0x0,_0x10b1e6=null,_0x267860=null;for(var _0x417105=0x0;_0x417105<this['rows'];_0x417105++){_0x10b1e6=[];for(var _0x521b16=0x0;_0x521b16<this['cols'];_0x521b16++,_0x430052++){_0x57dab4?_0x267860=this[_0x59d3d4(0x1a1)](_0x122358[_0x417105][_0x521b16],_0x147ae4[_0x417105][_0x521b16]):_0x267860=this[_0x59d3d4(0x1a1)](_0x122358[_0x430052],_0x147ae4[_0x430052]),_0x10b1e6[_0x59d3d4(0xb6)](_0x267860);}this[_0x59d3d4(0x1ac)][_0x59d3d4(0xb6)](_0x10b1e6);}_0x1dc28e&&this['grid']['reverse']();}return _createClass(_0x5ac9b5,[{'key':_0x2863c2(0x1b2),'value':function _0x13c6b3(_0x32bda6,_0x4d3878){var _0x237ead=_0x2863c2,_0x481c14=(_0x32bda6-this[_0x237ead(0x1bb)])/(this[_0x237ead(0x18e)]-this[_0x237ead(0x1bb)])*(this[_0x237ead(0x165)]-0x1),_0x350340=(this[_0x237ead(0x209)]-_0x4d3878)/(this[_0x237ead(0x209)]-this[_0x237ead(0xd6)])*(this['rows']-0x1);return{'x':_0x481c14,'y':_0x350340};}},{'key':'getUVByXY','value':function _0x222dda(_0x1ca6f9,_0x1da484){var _0xa8fa2=_0x2863c2;if(_0x1ca6f9<0x0||_0x1ca6f9>=this[_0xa8fa2(0x165)]||_0x1da484>=this['rows'])return[0x0,0x0,0x0];var _0x320265=Math[_0xa8fa2(0x16d)](_0x1ca6f9),_0x47284c=Math['floor'](_0x1da484);if(_0x320265===_0x1ca6f9&&_0x47284c===_0x1da484)return this['grid'][_0x1da484][_0x1ca6f9];var _0x14dd89=_0x320265+0x1,_0x287d31=_0x47284c+0x1,_0x24f321=this[_0xa8fa2(0x1c0)](_0x320265,_0x47284c),_0x305d26=this['getUVByXY'](_0x14dd89,_0x47284c),_0x80a8bf=this[_0xa8fa2(0x1c0)](_0x320265,_0x287d31),_0x280dc4=this[_0xa8fa2(0x1c0)](_0x14dd89,_0x287d31),_0x27c8bc=null;try{_0x27c8bc=this[_0xa8fa2(0x216)](_0x1ca6f9-_0x320265,_0x1da484-_0x47284c,_0x24f321,_0x305d26,_0x80a8bf,_0x280dc4);}catch(_0x433fe7){console['log'](_0x1ca6f9,_0x1da484);}return _0x27c8bc;}},{'key':_0x2863c2(0x216),'value':function _0x258c02(_0x2fb4ff,_0x2769dd,_0x1d048f,_0xed0c74,_0x279ca3,_0x59496d){var _0x38326e=_0x2863c2,_0x2b0763=0x1-_0x2fb4ff,_0x3d848d=0x1-_0x2769dd,_0x153f3c=_0x2b0763*_0x3d848d,_0x52ee49=_0x2fb4ff*_0x3d848d,_0x461ca1=_0x2b0763*_0x2769dd,_0x39cbc3=_0x2fb4ff*_0x2769dd,_0x52c5ce=_0x1d048f[0x0]*_0x153f3c+_0xed0c74[0x0]*_0x52ee49+_0x279ca3[0x0]*_0x461ca1+_0x59496d[0x0]*_0x39cbc3,_0x3db3df=_0x1d048f[0x1]*_0x153f3c+_0xed0c74[0x1]*_0x52ee49+_0x279ca3[0x1]*_0x461ca1+_0x59496d[0x1]*_0x39cbc3;return this[_0x38326e(0x1a1)](_0x52c5ce,_0x3db3df);}},{'key':_0x2863c2(0x1a1),'value':function _0x1ad70d(_0xd1e64e,_0x439559){var _0x3f344c=_0x2863c2;return[+_0xd1e64e,+_0x439559,Math[_0x3f344c(0xbb)](_0xd1e64e*_0xd1e64e+_0x439559*_0x439559)];}},{'key':_0x2863c2(0x1e0),'value':function _0x2fc4c7(_0x36fd3e,_0x41f4ed){var _0x350df2=_0x2863c2;if(!this[_0x350df2(0x163)](_0x36fd3e,_0x41f4ed))return null;var _0x4a4bc5=this[_0x350df2(0x1b2)](_0x36fd3e,_0x41f4ed),_0x54fbcc=this[_0x350df2(0x1c0)](_0x4a4bc5['x'],_0x4a4bc5['y']);return _0x54fbcc;}},{'key':'isInExtent','value':function _0x55daff(_0x33d77e,_0x3d2e92){var _0x41226c=_0x2863c2;return _0x33d77e>=this[_0x41226c(0x1bb)]&&_0x33d77e<=this[_0x41226c(0x18e)]&&_0x3d2e92>=this['ymin']&&_0x3d2e92<=this['ymax']?!![]:![];}},{'key':_0x2863c2(0x146),'value':function _0x59c918(){var _0x16441d=_0x2863c2,_0x5ab707=fRandomByfloat(this[_0x16441d(0x1bb)],this[_0x16441d(0x18e)]),_0x273068=fRandomByfloat(this['ymin'],this['ymax']);return{'lat':_0x273068,'lng':_0x5ab707};}}]),_0x5ac9b5;}());function fRandomByfloat(_0x3f9e77,_0x485c8f){var _0x215b83=_0x2b2e19;return _0x3f9e77+Math[_0x215b83(0x10b)]()*(_0x485c8f-_0x3f9e77);}var Cesium=mars3d__namespace['Cesium'],BaseLayer=mars3d__namespace[_0x2b2e19(0x1d3)]['BaseLayer'],CanvasWindLayer=function(_0x47abe5){var _0x5d4a44=_0x2b2e19;_inherits(_0x3b5510,_0x47abe5);var _0x619e49=_createSuper(_0x3b5510);function _0x3b5510(){var _0x4d9e0a=_0x5b8f,_0x572bf9,_0x134168=arguments[_0x4d9e0a(0x1a7)]>0x0&&arguments[0x0]!==undefined?arguments[0x0]:{};return _classCallCheck(this,_0x3b5510),_0x572bf9=_0x619e49[_0x4d9e0a(0x151)](this,_0x134168),_0x572bf9[_0x4d9e0a(0x21a)](_0x134168),_0x572bf9[_0x4d9e0a(0x1ce)]=null,_0x572bf9;}return _createClass(_0x3b5510,[{'key':'_setOptionsHook','value':function _0x4dfe26(_0x150260){var _0x9f7e52=_0x5b8f,_0x142a57,_0x1ebe53,_0x399f25;this[_0x9f7e52(0x1eb)]=[0x0,0x0],this[_0x9f7e52(0x171)]=[],this[_0x9f7e52(0x1e3)]=_0x150260[_0x9f7e52(0x1e3)]||0x32,this[_0x9f7e52(0x1fd)]=_0x150260[_0x9f7e52(0x13a)]||0x1000,this[_0x9f7e52(0x20b)]=_0x150260[_0x9f7e52(0x121)]||0x78,this[_0x9f7e52(0x10e)]=0x3e8/(_0x150260['frameRate']||0xa),this['_pointerEvents']=(_0x142a57=this[_0x9f7e52(0x1f9)]['pointerEvents'])!==null&&_0x142a57!==void 0x0?_0x142a57:![],this[_0x9f7e52(0x159)]=_0x150260[_0x9f7e52(0x159)]||_0x9f7e52(0x1a0),this[_0x9f7e52(0x16e)]=_0x150260[_0x9f7e52(0x16e)]||0x1,this[_0x9f7e52(0xce)]=(_0x1ebe53=_0x150260[_0x9f7e52(0xce)])!==null&&_0x1ebe53!==void 0x0?_0x1ebe53:0x0,this[_0x9f7e52(0x127)]=(_0x399f25=_0x150260[_0x9f7e52(0x127)])!==null&&_0x399f25!==void 0x0?_0x399f25:![];}},{'key':'layer','get':function _0x3cac11(){return this['canvas'];}},{'key':_0x5d4a44(0x120),'get':function _0x37d24a(){var _0x51b8e5=_0x5d4a44;return this[_0x51b8e5(0x1a6)][_0x51b8e5(0x19f)][_0x51b8e5(0x1ce)][_0x51b8e5(0x202)];}},{'key':_0x5d4a44(0x14d),'get':function _0x40d888(){var _0x1e339f=_0x5d4a44;return this[_0x1e339f(0x1a6)][_0x1e339f(0x19f)]['canvas'][_0x1e339f(0x1c1)];}},{'key':'pointerEvents','get':function _0x3d2d4b(){var _0x31adf4=_0x5d4a44;return this[_0x31adf4(0x100)];},'set':function _0xa9ed53(_0x322ade){var _0x1d5661=_0x5d4a44;this[_0x1d5661(0x100)]=_0x322ade;if(!this['canvas'])return;_0x322ade?this['canvas']['style'][_0x1d5661(0x20a)]=_0x1d5661(0x1d8):this[_0x1d5661(0x1ce)]['style']['pointer-events']=_0x1d5661(0x22f);}},{'key':_0x5d4a44(0x1e3),'get':function _0x5af85c(){var _0xce366=_0x5d4a44;return this[_0xce366(0x198)];},'set':function _0x195489(_0x1771b0){var _0x39b7bd=_0x5d4a44;this[_0x39b7bd(0x198)]=(0x64-(_0x1771b0>0x63?0x63:_0x1771b0))*0x64,this[_0x39b7bd(0x217)]();}},{'key':_0x5d4a44(0x13a),'get':function _0xa791aa(){var _0x23a4d8=_0x5d4a44;return this[_0x23a4d8(0x1fd)];},'set':function _0x448d39(_0x51efc8){var _0x176f61=_0x5d4a44,_0x3191b9=this;this[_0x176f61(0x1fd)]=_0x51efc8,clearTimeout(this[_0x176f61(0xe8)]),this['_canrefresh']=setTimeout(function(){_0x3191b9['redraw']();},0x1f4);}},{'key':_0x5d4a44(0x121),'get':function _0x119a29(){return this['_maxAge'];},'set':function _0x2aa3bb(_0x4c5b3e){var _0x5b2e2f=_0x5d4a44,_0x303e9a=this;this[_0x5b2e2f(0x20b)]=_0x4c5b3e,clearTimeout(this[_0x5b2e2f(0xe8)]),this[_0x5b2e2f(0xe8)]=setTimeout(function(){var _0x325a7f=_0x5b2e2f;_0x303e9a[_0x325a7f(0x1c9)]();},0x1f4);}},{'key':_0x5d4a44(0xe9),'get':function _0x1d6471(){var _0x3018ac=_0x5d4a44;return this[_0x3018ac(0x1a5)];},'set':function _0x4a2806(_0x22ecc0){var _0x1e3fc3=_0x5d4a44;this[_0x1e3fc3(0xf9)](_0x22ecc0);}},{'key':_0x5d4a44(0x21d),'value':function _0x151b8e(_0x1efbf4){var _0x3e74cd=_0x5d4a44;_0x1efbf4?this[_0x3e74cd(0x1ab)]():(this[_0x3e74cd(0x1a5)]&&(this['options'][_0x3e74cd(0xe9)]=this['windData']),this[_0x3e74cd(0x19e)]());}},{'key':_0x5d4a44(0x1ff),'value':function _0x306054(){}},{'key':'_addedHook','value':function _0x43e53a(){var _0x124da5=_0x5d4a44;this[_0x124da5(0x1ce)]=this[_0x124da5(0x143)](),this[_0x124da5(0x1a8)]=this['canvas'][_0x124da5(0xe1)]('2d'),this['bindEvent'](),this['options'][_0x124da5(0xe9)]&&this[_0x124da5(0xf9)](this[_0x124da5(0x1f9)][_0x124da5(0xe9)]);}},{'key':_0x5d4a44(0x19e),'value':function _0x3eb26f(){var _0x3cd924=_0x5d4a44;this[_0x3cd924(0x153)](),this['unbindEvent'](),this[_0x3cd924(0x1ce)]&&(this['_map'][_0x3cd924(0xea)]['removeChild'](this[_0x3cd924(0x1ce)]),delete this[_0x3cd924(0x1ce)]);}},{'key':_0x5d4a44(0x143),'value':function _0x335d44(){var _0x504aec=_0x5d4a44,_0x54c89a=window[_0x504aec(0x231)]['createElement']('canvas');_0x54c89a[_0x504aec(0x211)][_0x504aec(0xfa)]=_0x504aec(0x1ad),_0x54c89a[_0x504aec(0x211)][_0x504aec(0x21f)]=_0x504aec(0x11b),_0x54c89a[_0x504aec(0x211)][_0x504aec(0x19c)]=_0x504aec(0x11b),_0x54c89a[_0x504aec(0x211)]['width']='100%',_0x54c89a['style'][_0x504aec(0xc0)]='100%',_0x54c89a[_0x504aec(0x211)][_0x504aec(0x1b0)]=this[_0x504aec(0x100)]?_0x504aec(0x1f2):_0x504aec(0x22f),_0x54c89a[_0x504aec(0x211)][_0x504aec(0x1e9)]=0xa,_0x54c89a[_0x504aec(0x1b6)]('id','canvasWindy'),_0x54c89a[_0x504aec(0x1b6)](_0x504aec(0x181),_0x504aec(0x195)),this[_0x504aec(0x1a6)]['container']['appendChild'](_0x54c89a);var _0x61422f=this[_0x504aec(0x1a6)][_0x504aec(0x19f)];return _0x54c89a[_0x504aec(0x15e)]=_0x61422f['canvas'][_0x504aec(0x202)],_0x54c89a[_0x504aec(0xc0)]=_0x61422f['canvas'][_0x504aec(0x1c1)],_0x54c89a;}},{'key':_0x5d4a44(0xf6),'value':function _0x2129db(){var _0x171e5b=_0x5d4a44;this[_0x171e5b(0x1ce)]&&(this[_0x171e5b(0x1ce)][_0x171e5b(0x15e)]=this[_0x171e5b(0x120)],this['canvas'][_0x171e5b(0xc0)]=this[_0x171e5b(0x14d)]);}},{'key':'bindEvent','value':function _0x423ded(){var _0x53ea0d=_0x5d4a44,_0x76351d=this,_0x452fac=Date[_0x53ea0d(0x1df)]();(function _0x335873(){var _0x1ba05f=_0x53ea0d;_0x76351d['animateFrame']=window[_0x1ba05f(0x1ae)](_0x335873);var _0x4a8c51=Date[_0x1ba05f(0x1df)](),_0x553a59=_0x4a8c51-_0x452fac;_0x553a59>_0x76351d[_0x1ba05f(0x10e)]&&(_0x452fac=_0x4a8c51-_0x553a59%_0x76351d[_0x1ba05f(0x10e)],_0x76351d[_0x1ba05f(0x157)]());}(),window[_0x53ea0d(0xb7)](_0x53ea0d(0xf6),this['resize']['bind'](this),![]),this[_0x53ea0d(0x106)]=![],this[_0x53ea0d(0x173)]=![],this['_map']['on'](mars3d__namespace[_0x53ea0d(0x192)][_0x53ea0d(0xdc)],this[_0x53ea0d(0x10f)],this),this[_0x53ea0d(0x1a6)]['on'](mars3d__namespace[_0x53ea0d(0x192)][_0x53ea0d(0x136)],this[_0x53ea0d(0x1ef)],this),this[_0x53ea0d(0x1a6)]['on'](mars3d__namespace[_0x53ea0d(0x192)]['mouseUp'],this[_0x53ea0d(0xed)],this));}},{'key':'unbindEvent','value':function _0x2c5663(){var _0xed6da7=_0x5d4a44;window['cancelAnimationFrame'](this[_0xed6da7(0x227)]),delete this[_0xed6da7(0x227)],window['removeEventListener'](_0xed6da7(0xf6),this[_0xed6da7(0xf6)]),this[_0xed6da7(0x1a6)][_0xed6da7(0x13e)](mars3d__namespace[_0xed6da7(0x192)]['wheel'],this['_onMapWhellEvent'],this),this['_map']['off'](mars3d__namespace['EventType'][_0xed6da7(0x136)],this[_0xed6da7(0x1ef)],this),this[_0xed6da7(0x1a6)][_0xed6da7(0x13e)](mars3d__namespace[_0xed6da7(0x192)][_0xed6da7(0xc1)],this[_0xed6da7(0xed)],this),this[_0xed6da7(0x1a6)][_0xed6da7(0x13e)](mars3d__namespace[_0xed6da7(0x192)][_0xed6da7(0xcf)],this[_0xed6da7(0x19b)],this);}},{'key':_0x5d4a44(0x10f),'value':function _0x35523f(_0x3c0571){var _0x158a97=_0x5d4a44,_0x587d1d=this;clearTimeout(this['refreshTimer']);if(!this[_0x158a97(0x13b)]||!this[_0x158a97(0x1ce)])return;this['canvas'][_0x158a97(0x211)][_0x158a97(0x207)]=_0x158a97(0x16f),this[_0x158a97(0x13d)]=setTimeout(function(){var _0x3af797=_0x158a97;if(!_0x587d1d[_0x3af797(0x13b)])return;_0x587d1d['redraw'](),_0x587d1d[_0x3af797(0x1ce)]['style'][_0x3af797(0x207)]=_0x3af797(0x20d);},0xc8);}},{'key':_0x5d4a44(0x1ef),'value':function _0x5149a7(_0x38a1fe){var _0x99db29=_0x5d4a44;this[_0x99db29(0x106)]=!![],this[_0x99db29(0x1a6)][_0x99db29(0x13e)](mars3d__namespace[_0x99db29(0x192)]['mouseMove'],this[_0x99db29(0x19b)],this),this[_0x99db29(0x1a6)]['on'](mars3d__namespace[_0x99db29(0x192)][_0x99db29(0xcf)],this['_onMouseMoveEvent'],this);}},{'key':_0x5d4a44(0x19b),'value':function _0x783865(_0x1f7942){var _0x19b70e=_0x5d4a44;if(!this[_0x19b70e(0x13b)]||!this['canvas'])return;this['mouse_down']&&(this[_0x19b70e(0x1ce)]['style']['visibility']=_0x19b70e(0x16f),this[_0x19b70e(0x173)]=!![]);}},{'key':_0x5d4a44(0xed),'value':function _0x1aac04(_0xcbf1ef){var _0x25a8ff=_0x5d4a44;if(!this[_0x25a8ff(0x13b)]||!this[_0x25a8ff(0x1ce)])return;this[_0x25a8ff(0x1a6)][_0x25a8ff(0x13e)](mars3d__namespace[_0x25a8ff(0x192)][_0x25a8ff(0xcf)],this[_0x25a8ff(0x19b)],this),this[_0x25a8ff(0x106)]&&this[_0x25a8ff(0x173)]&&this[_0x25a8ff(0x1c9)](),this[_0x25a8ff(0x1ce)][_0x25a8ff(0x211)][_0x25a8ff(0x207)]=_0x25a8ff(0x20d),this[_0x25a8ff(0x106)]=![],this[_0x25a8ff(0x173)]=![];}},{'key':_0x5d4a44(0x1c9),'value':function _0x15cd98(){var _0x190b5a=_0x5d4a44;if(!this['show']||!this[_0x190b5a(0x133)])return;this['particles']=[],this[_0x190b5a(0x128)]();}},{'key':_0x5d4a44(0xf9),'value':function _0x416d54(_0x1005f9){var _0xafe0ff=_0x5d4a44;this['clear'](),this[_0xafe0ff(0x1a5)]=_0x1005f9,this['windField']=new CanvasWindField(this[_0xafe0ff(0x1a5)],this[_0xafe0ff(0x127)]),this[_0xafe0ff(0x128)]();}},{'key':_0x5d4a44(0x128),'value':function _0x567c7e(){var _0x13c7e9=_0x5d4a44;this['_calcStep']();for(var _0x3a0559=0x0;_0x3a0559<this[_0x13c7e9(0x13a)];_0x3a0559++){var _0x32c802=this[_0x13c7e9(0x229)](new CanvasParticle());this[_0x13c7e9(0x171)][_0x13c7e9(0xb6)](_0x32c802);}this[_0x13c7e9(0x1a8)][_0x13c7e9(0x15f)]='rgba(0,\x200,\x200,\x200.97)',this['canvasContext'][_0x13c7e9(0x1d2)]=0.6,this['update']();}},{'key':_0x5d4a44(0x217),'value':function _0x420436(){var _0x4ec74a=_0x5d4a44;if(!this[_0x4ec74a(0x133)])return;this['calc_speedRate']=[(this[_0x4ec74a(0x133)][_0x4ec74a(0x18e)]-this[_0x4ec74a(0x133)][_0x4ec74a(0x1bb)])/this[_0x4ec74a(0x1e3)],(this[_0x4ec74a(0x133)][_0x4ec74a(0x209)]-this['windField']['ymin'])/this['speedRate']];}},{'key':_0x5d4a44(0x157),'value':function _0x303029(){var _0x2b38ae=_0x5d4a44,_0x1ff9b8=this;if(!this[_0x2b38ae(0x13b)]||this['particles'][_0x2b38ae(0x1a7)]<=0x0)return;var _0x5b876a=null,_0x9d6083=null,_0x3ef839=null;this[_0x2b38ae(0x171)][_0x2b38ae(0x1b8)](function(_0x4ad511){var _0x2e09de=_0x2b38ae;_0x4ad511[_0x2e09de(0x115)]<=0x0&&_0x1ff9b8[_0x2e09de(0x229)](_0x4ad511);if(_0x4ad511[_0x2e09de(0x115)]>0x0){var _0x1e7ddc=_0x4ad511[_0x2e09de(0x126)],_0x54f728=_0x4ad511[_0x2e09de(0x1fe)];_0x3ef839=_0x1ff9b8['windField']['getUVByPoint'](_0x1e7ddc,_0x54f728),_0x3ef839?(_0x5b876a=_0x1e7ddc+_0x1ff9b8['calc_speedRate'][0x0]*_0x3ef839[0x0],_0x9d6083=_0x54f728+_0x1ff9b8['calc_speedRate'][0x1]*_0x3ef839[0x1],_0x4ad511[_0x2e09de(0x14c)]=_0x1e7ddc,_0x4ad511[_0x2e09de(0xd9)]=_0x54f728,_0x4ad511[_0x2e09de(0x126)]=_0x5b876a,_0x4ad511[_0x2e09de(0x1fe)]=_0x9d6083,_0x4ad511[_0x2e09de(0x115)]--):_0x4ad511['age']=0x0;}}),this[_0x2b38ae(0x1db)]();}},{'key':_0x5d4a44(0xc6),'value':function _0x459def(_0x358c2e,_0x313c74,_0x27d864){var _0x46ec43=_0x5d4a44,_0x23b364=Cesium['Cartesian3'][_0x46ec43(0xec)](_0x358c2e,_0x313c74,this[_0x46ec43(0xce)]),_0x343608=this[_0x46ec43(0x1a6)]['scene'];if(_0x343608[_0x46ec43(0x188)]===Cesium['SceneMode'][_0x46ec43(0x166)]){var _0x3d87cc=new Cesium[(_0x46ec43(0x1d1))](_0x343608[_0x46ec43(0x1b1)][_0x46ec43(0x14f)],_0x343608[_0x46ec43(0x15a)][_0x46ec43(0x1dd)]),_0x428c4d=_0x3d87cc[_0x46ec43(0x17a)](_0x23b364);if(!_0x428c4d)return _0x27d864[_0x46ec43(0x115)]=0x0,null;}var _0x51cca7=Cesium['SceneTransforms']['wgs84ToWindowCoordinates'](this[_0x46ec43(0x1a6)][_0x46ec43(0x19f)],_0x23b364);return _0x51cca7?[_0x51cca7['x'],_0x51cca7['y']]:null;}},{'key':_0x5d4a44(0x1db),'value':function _0x36d7eb(){var _0x1acd49=_0x5d4a44,_0x10733a=this,_0xd4b3bc=this['particles'];this[_0x1acd49(0x1a8)][_0x1acd49(0x16e)]=_0x10733a['lineWidth'],this['canvasContext']['globalCompositeOperation']=_0x1acd49(0x117),this['canvasContext']['fillRect'](0x0,0x0,this['canvasWidth'],this[_0x1acd49(0x14d)]),this[_0x1acd49(0x1a8)][_0x1acd49(0x17f)]=_0x1acd49(0xe3),this[_0x1acd49(0x1a8)]['globalAlpha']=0.9,this[_0x1acd49(0x1a8)]['beginPath'](),this[_0x1acd49(0x1a8)][_0x1acd49(0xef)]=this[_0x1acd49(0x159)];var _0x5224dd=this[_0x1acd49(0x1a6)][_0x1acd49(0x19f)][_0x1acd49(0x188)]!==Cesium['SceneMode']['SCENE3D'];_0xd4b3bc[_0x1acd49(0x1b8)](function(_0x4eabc5){var _0x2b1145=_0x1acd49,_0x5f41e2=_0x10733a[_0x2b1145(0xc6)](_0x4eabc5[_0x2b1145(0x14c)],_0x4eabc5[_0x2b1145(0xd9)],_0x4eabc5),_0x32dfcf=_0x10733a[_0x2b1145(0xc6)](_0x4eabc5[_0x2b1145(0x126)],_0x4eabc5[_0x2b1145(0x1fe)],_0x4eabc5);if(_0x5f41e2!=null&&_0x32dfcf!=null){var _0x556b60=Math['abs'](_0x5f41e2[0x0]-_0x32dfcf[0x0]);if(_0x5224dd&&_0x556b60>=_0x10733a[_0x2b1145(0x120)]);else _0x10733a[_0x2b1145(0x1a8)][_0x2b1145(0xfc)](_0x5f41e2[0x0],_0x5f41e2[0x1]),_0x10733a[_0x2b1145(0x1a8)]['lineTo'](_0x32dfcf[0x0],_0x32dfcf[0x1]);}}),this[_0x1acd49(0x1a8)]['stroke']();}},{'key':_0x5d4a44(0x229),'value':function _0x27ed75(_0x28b8a2){var _0x4cfda9=_0x5d4a44,_0x10e04a,_0x3e320e;for(var _0xf0c4e1=0x0;_0xf0c4e1<0x1e;_0xf0c4e1++){_0x10e04a=this[_0x4cfda9(0x133)][_0x4cfda9(0x146)](),_0x3e320e=this[_0x4cfda9(0x133)][_0x4cfda9(0x1e0)](_0x10e04a[_0x4cfda9(0x14c)],_0x10e04a[_0x4cfda9(0xd9)]);if(_0x3e320e&&_0x3e320e[0x2]>0x0)break;}if(!_0x3e320e)return _0x28b8a2;var _0x3c6ff5=_0x10e04a['lng']+this[_0x4cfda9(0x1eb)][0x0]*_0x3e320e[0x0],_0x1e7cc6=_0x10e04a[_0x4cfda9(0xd9)]+this[_0x4cfda9(0x1eb)][0x1]*_0x3e320e[0x1];return _0x28b8a2[_0x4cfda9(0x14c)]=_0x10e04a['lng'],_0x28b8a2[_0x4cfda9(0xd9)]=_0x10e04a[_0x4cfda9(0xd9)],_0x28b8a2[_0x4cfda9(0x126)]=_0x3c6ff5,_0x28b8a2[_0x4cfda9(0x1fe)]=_0x1e7cc6,_0x28b8a2[_0x4cfda9(0x115)]=Math['round'](Math['random']()*this[_0x4cfda9(0x121)]),_0x28b8a2;}},{'key':_0x5d4a44(0x153),'value':function _0x35b8e8(){var _0x416ff1=_0x5d4a44;this[_0x416ff1(0x171)]=[],delete this[_0x416ff1(0x133)],delete this['windData'];}}]),_0x3b5510;}(BaseLayer);mars3d__namespace[_0x2b2e19(0x1b9)][_0x2b2e19(0xf8)](_0x2b2e19(0x116),CanvasWindLayer),mars3d__namespace[_0x2b2e19(0x1d3)][_0x2b2e19(0x1be)]=CanvasWindLayer,mars3d__namespace['WindUtil']=WindUtil,exports[_0x2b2e19(0x1be)]=CanvasWindLayer,exports[_0x2b2e19(0x1d7)]=WindLayer,exports[_0x2b2e19(0xf3)]=WindUtil,Object[_0x2b2e19(0x1e2)](exports,_0x2b2e19(0x1d9),{'value':!![]});
}));
