<template>
  <!-- 重大   -->
  <el-dialog
    title="风险单元"
    :visible.sync="iszhongDa"
    width="800px"
    class=""
    top="5vh"
    :close-on-click-modal="false"
  >
    <!-- text-align:center; -->
    <div class="iszhongDaCon">
      <!-- <img src="../../../../../static/img/riskManagementIcon/sgyh.png" /> -->
      <div class="hs_box">
        <h2>高危风险监测特征指标修正系数(k3)：</h2>
        <el-progress type="circle" :percentage="1.87"></el-progress>
      </div>

      <br/> <br/>

      <div class="fen_dong">
        <div class="fen_tit">
          <span>隐患级别</span>
          <span>数量</span>
          <span>更新时间</span>
        </div>

        <div class="feng_con">
          <div class="feng_item">
            <span>重大隐患</span>
            <div class="level_num">
              <el-input size="mini" v-model.trim="levelOneDate.num"></el-input>
            </div>
           <div class="updataTime">
              <el-date-picker
                v-model="levelOneDate.updata"
                type="date"
                size="mini"
                placeholder="选择日期"
              >
              </el-date-picker>
            </div>
          </div>
        </div>

        <div class="feng_con">
          <div class="feng_item">
            <span>一般隐患</span>
            <div class="level_num">
              <el-input size="mini" v-model.trim="levelTwoDate.num"></el-input>
            </div>
          <div class="updataTime">
              <el-date-picker
                v-model="levelTwoDate.updata"
                type="date"
                size="mini"
                placeholder="选择日期"
              >
              </el-date-picker>
            </div>
          </div>
        </div>

       
      </div>
    </div>

    <div slot="footer" style="display: flex; justify-content: center">
      <el-button size="mini">取 消</el-button>

      <el-button size="mini" type="primary" @click="submit(2)">提 交</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      iszhongDa: false,
       levelOneDate: {
        num: 0,
        xishu: 0.1,
        updata: "2022-01-11",
      },
      levelTwoDate: {
        num: 0,
        xishu: 0.1,
        updata: "2022-01-11",
      },
      levelThreeDate: {
        num: 0,
        xishu: 0.1,
        updata: "2022-01-11",
      },
    };
  },
};
</script>
<style scoped lang="scss">
.hs_box {
  display: flex;
  align-items: center;
  justify-content: center;
}
.fen_dong {
  .fen_tit {
    display: flex;
    height: 40px;
    background: #ccc;
    align-items: center;
    justify-content: center;
    text-align: center;
    > span {
      width: 33.33%;
    }
  }
  .feng_item {
    display: flex;
    text-align: center;
    align-items: center;
    margin: 10px 0;
    > span {
      width: 33.33%;
    }
    > div {
      width: 33.33%;
      padding: 0 10px;
      box-sizing: content-box;
    }
  }
}
</style>