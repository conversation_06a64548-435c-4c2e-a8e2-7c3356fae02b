<template>
  <div class="table">
    <el-dialog
      :title="title"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      @close="closeTable"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableData"
        ref="tableData"
        v-loading="loading"
        label-width="150px"
      >
        <div class="inputBox">
          <el-form-item label="菜单名称" prop="menuName" class="lable">
            <el-input
              v-model.trim="tableData.menuName"
              placeholder="菜单名称"
              label="菜单名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="菜单别名" prop="menuAlias" class="lable">
            <el-input
              v-model.trim="tableData.menuAlias"
              placeholder="菜单别名"
              label="菜单别名"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="菜单URL" prop="url" class="lable">
            <el-input
              v-model.trim="tableData.url"
              placeholder="菜单URL"
              label="菜单URL"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="菜单icon" prop="iconUrl" class="lable">
            <el-input
              v-model.trim="tableData.iconUrl"
              placeholder="菜单icon"
              label="菜单icon"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="所属组件名称" prop="moduleName" class="lable">
            <el-input
              v-model.trim="tableData.moduleName"
              placeholder="所属组件名称"
              label="所属组件名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="序号" prop="menuOrder" class="lable">
            <el-input
              v-model.trim="tableData.menuOrder"
              placeholder="序号"
              label="序号"
              class="input"
              oninput="value=value.replace(/[^\d]/g,'')"
            ></el-input>
          </el-form-item>
        </div>
        <!-- <div class="inputBox">
          <el-form-item label="同步到其他系统" prop="tenantId" class="lable">
            <el-input
              v-model="tableData.tenantId"
              placeholder="请输入搜索内容"
              label="同步到其他系统"
              class="input"
            ></el-input>
          </el-form-item>
        </div> -->
        <div class="tree">
          <a-tree
            checkable
            :tree-data="treeData"
            @check="onCheck"
            v-model="checkedKeys"
          >
          </a-tree>
        </div>
        <el-button
          size="default"
          type="primary"
          class="commit"
          @click="isSetData == true ? handleSaveTable() : handleSetTable()"
          >保存</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveMenuTable,
  saveMenmenuByMenuId,
  querySystemTreeNoSelf,
  setMenuTable,
} from "../../../api/user";
import { menuRules } from "../../../api/rules";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
    title:{
      type:String,
      default:""
    }
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loading: false,
      title:"",
      tableData: {
        // menuId: "",
        // menuName: "",
        // menuAlias: "",
        // menuUrl: "",
        // iconUrl: "",
        // parentId: "",
        // systemCode: "",
        // moduleName: "",
        // menuOrder: null,
        // menuFlag: "",
        // tenantId: "",
        // belongSystemCode: "",
      },
      checkedKeys: [],
      rules: menuRules,
      parentId: "",
      isSetData: true,
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
    },
    clearTable() {
      this.tableData = {};
    },
    closeTable() {
      this.checkedKeys = [];
    },
    handleSaveTable() {
      this.tableData.systemCode =
        this.$store.state.sa.SAMenuListData.systemCode;
      this.tableData.parentId = this.$store.state.sa.SAMenuListData.menuId;
      this.$refs["tableData"].validate((valid) => {
        if (valid) {
          //判断
          // if(this.checkedKeys.length>0){
          saveMenuTable({ ...this.tableData })
            .then((res) => {
              this.tableVisible = false;
              this.$message({
                message: res.data.msg,
                type: "success",
              });
              this.$parent.fatherMethod();
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
          // }else{
          //   this.$message.error("请选择同步到的模块");
          // }
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    handleSetTable() {
      // this.loading = true;
      this.tableData.systemCode =
        this.$store.state.sa.SAMenuListData.systemCode;
      this.tableData.parentId =
        this.$store.state.sa.SAMenuListData.menuId || "-1";
      this.$refs["tableData"].validate((valid) => {
        if (valid) {
          if (this.checkedKeys.length > 0) {
            setMenuTable({ ...this.tableData })
              .then((res) => {
                // this.loading = false;
                this.tableVisible = false;
                this.$message({
                  message: res.data.msg,
                  type: "success",
                });
                this.$parent.fatherMethod();
              })
              .catch((e) => {
                this.loading = false;
                console.log(e, "请求错误");
              });
          } else {
            this.$message.error("请选择同步到的模块");
          }
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    getData(id) {
      if (id === null) {
        this.isSetData = true;
      } else {
        this.isSetData = false;
        this.loading = true;
        saveMenmenuByMenuId(id)
          .then((res) => {
            this.loading = false;
            // console.log(res);
            this.tableData = res.data.data;
            // console.log(res.data.data.systemCodes)
            if (res.data.data.systemCodes) {
              this.checkedKeys = res.data.data.systemCodes;
            }
          })
          .catch((e) => {
            this.loading = false;
            console.log(e, "请求错误");
          });
      }
    },
    getTree(data) {
      querySystemTreeNoSelf(data)
        .then((res) => {
          let data = res.data.data;
          for (let i = 0; i < data.length; i++) {
            data[i].title = data[i].systemName;
            data[i].key = data[i].systemCode;
          }
          // console.log(data);
          this.treeData = data;
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    onCheck(checkedKeys, e) {
      console.log(checkedKeys, e);
      let key = JSON.stringify(checkedKeys);
      key = key.split('"').join("");
      key = key.split("[").join("");
      key = key.split("]").join("");
      // console.log(key);
      this.tableData.belongSystemCode = key;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
