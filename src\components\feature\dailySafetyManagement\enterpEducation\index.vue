<template>
  <div class="enterpEducation">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              企业教育培训
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <el-tabs v-model="activeName">
      <el-tab-pane label="教育培训" name="education">
        <education v-if="activeName === 'education'"></education>
      </el-tab-pane>
      <el-tab-pane label="考题管理" name="question">
        <question v-if="activeName === 'question'"></question>
      </el-tab-pane>
      <el-tab-pane label="考试管理" name="examination">
        <examination v-if="activeName === 'examination'"></examination>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script>
import education from "./education.vue";
import question from "./question.vue";
import examination from "./examination.vue";
export default {
  components: {
    education,
    question,
    examination,
  },
  data() {
    return {
      activeName: "education",
    };
  },
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {},
};
</script>
<style lang="scss" scoped>
.enterpEducation {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
}
</style>
