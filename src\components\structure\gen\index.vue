<template>
  <div class="gen">
    <div class="header">
      <el-select v-model="dsName" @change="getData" :clearable="true" placeholder="请选择">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.label"
          
        >
        </el-option>
      </el-select>
      <el-input
        v-model.trim="input"
        :clearable="true" 
        placeholder="请输入内容"
        class="input"
      ></el-input>
      <el-button type="primary" @click="serach">搜索</el-button>
      <el-button type="primary">批量生成</el-button>
    </div>
    <div class="container">
      <div class="table">
        <el-table
          ref="multipleTable"
          :data="tableData.records"
          tooltip-effect="dark"
          style="width: 100%"
        >
          <el-table-column type="selection" width="55" align="center">
          </el-table-column>
          <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column>
          <el-table-column label="表名称" width="180" align="center">
            <template slot-scope="scope">{{ scope.row.tableName }}</template>
          </el-table-column>
          <el-table-column
            prop="tableComment"
            label="表注释"
            width="180"
            align="center"
          >
          </el-table-column>
          <el-table-column
            prop="tableCollation"
            label="表编码"
            width="180"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="ENGINE"
            width="180"
            label="索引"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="createTime"
            label="创建时间"
            align="center"
            show-overflow-tooltip
          >
          </el-table-column>
          <el-table-column
            prop="address"
            label="操作"
            align="center"
            show-overflow-tooltip
          >
            <template slot-scope="scope"
              ><el-button size="mini" type="text" @click="showDialog(scope)"
                >生成</el-button
              ></template
            >
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          :page-size="tableData.size"
          layout="total, prev, pager, next"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
    </div>
    <el-dialog
      title="提示"
      :visible.sync="dialogVisible"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="form"
        class="dialog"

        label-width="80px"
      >
              <!-- :model="form" -->
        <el-form-item label="表名称:">
          <el-input
            class="input"
            label="表名称"
            v-model.trim="from.tableName"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item label="包名:">
          <el-input
            class="input"
            v-model.trim="from.packageName"
            placeholder="可为空，加载系统默认配置"
            label="包名"
          ></el-input>
        </el-form-item>
        <el-form-item label="作者:">
          <el-input
            class="input"
            v-model.trim="from.author"
            placeholder="可为空，加载系统默认配置"
            label="作者"
          ></el-input>
        </el-form-item>
        <el-form-item label="模块:">
          <el-input
            class="input"
            v-model.trim="from.moduleName"
            placeholder="可为空，加载系统默认配置"
            label="模块"
          ></el-input>
        </el-form-item>
        <el-form-item label="表前缀:">
          <el-input
            class="input"
            v-model.trim="from.tablePrefix"
            placeholder="可为空，加载系统默认配置"
            label="表前缀"
          ></el-input>
        </el-form-item>
        <el-form-item label="注释:">
          <el-input
            class="input"
            v-model.trim="from.comments"
            placeholder="可为空，加载系统默认配置"
            label="注释"
          ></el-input>
        </el-form-item>
        <div>
          <el-button type="primary" @click="downloadCode">下载</el-button>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getGenGenerator, getDsconfList ,getGeneratorCode} from "@/api/gen.js";
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      input: "",
      currentPage: 1,
      dialogVisible: false,
      dsName: "",
      options: [],
      tableData: [],
      multipleSelection: [],
      from:{},
    };
  },
  //方法集合
  methods: {
    showDialog(data) {
      this.dialogVisible = true;
      this.from = data.row;
      this.dsName = data.row.tableName;
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
        getGenGenerator({ current: val, size: 10 ,dsName:this.dsName}).then((res) => {
        this.tableData = res.data.data;
      });
    },
    getData() {
      getGenGenerator({ current: 1, size: 10 ,dsName:this.dsName}).then((res) => {
        this.tableData = res.data.data;
      });
    },
    serach(){
      getGenGenerator({ current: 1, size: 10 ,tableName:this.input}).then((res) => {
        this.tableData = res.data.data;
      });
    },
    getOptionData() {
      getDsconfList().then((res) => {
        let data = res.data.data;
        this.options = data.map((item, index) => {
          return { value: item.id,label: item.name };
        });
      });
    },
    downloadCode(){
      getGeneratorCode(this.from).then((res)=>{
        console.log(res);
      })
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    this.getOptionData();
  },
};
</script>
<style lang="scss" scoped>
.gen {
  .header {
    width: 100%;
    display: flex;
    justify-content: flex-start;
    padding: 20px 10px;
    background-color: #fff;
    .input {
      width: 200px;
      margin-left: 20px;
      margin-right: 20px;
    }
  }
  .container {
    background-color: #fff;
    .table {
      padding: 10px 30px;
    }
    .pagination {
      display: flex;
      justify-content: flex-end;
      padding: 20px 0;
    }
  }
  .dialog {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: space-around;
    height: 400px;
    .input {
      width: 400px;
    }
  }
}
</style>