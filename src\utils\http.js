/**axios封装 * 请求拦截、相应拦截、错误统一处理 */
import axios from "axios";
import { Message } from "element-ui";
import store from "../store/index";

//让ajax携带cookie
// axios.defaults.withCredentials=false;
// 请求超时时间
axios.defaults.timeout = 600000;
// post请求头
axios.defaults.headers.post["Content-Type"] =
  "application/x-www-form-urlencoded;charset=UTF-8";
// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    // 每次发送请求之前判断是否存在token，如果存在，则统一在http请求的header都加上token，不用每次请求都手动添加了
    // 即使本地存在token，也有可能token是过期的，所以在响应拦截器中要对返回状态进行判断
    if (config.url.includes("outer")) {
      return config;
    }
    if (config.url.indexOf("/auth/oauth/token") !== -1) {
      config.headers.Authorization = "Basic eXVuOnl1bg==";
    } else {
      //检测token存在哪个字段当中
      let token = "";
      try {
        if (store.state.login.user.access_token !== undefined) {
          token =
            store.state.login.user.token_type +
            " " +
            store.state.login.user.access_token;
        } else {
          token = store.state.login.user.token;
        }
      } catch (e) {
        token = store.state.login.user.token;
      }

      if (config.url.indexOf("/gemp-") !== -1) {
        //新接口需要在头部添加/gapi
        config.url = "/gapi" + config.url;
        // 只在没有设置 token 的情况下才添加
        if (!config?.headers?.token) {
          token && (config.headers.token = token);
        }
      } else {
        if (config.url.includes("/geocoder")) {
          return config;
        } else if (config.url.indexOf("/training") !== -1) {
          // 考试,教育培训添加token
          if (!config?.headers?.token) {
            token && (config.headers.token = token);
          }
        }
        token && (config.headers.Authorization = token);
      }
    }
    return config;
  },
  (error) => {
    return Promise.error(error);
  }
);
// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    if (response.status === 200) {
      //   if (response.data.code === 1 && response.data.data === null && response.data.msg === "没有权限") {
      if (response.data.code === 1 && response.data.msg === "没有权限") {
        // Message.closeAll();
        Message.error("登录过期，请重新登录");
        // setTimeout(() => {
        //   if (window.location.port == "30003") {
        //     window.location.href =
        //       "https://yyzc.hbsis.gov.cn:30003/admin/user/casLogin"; //企业
        //   } else {
        //     window.location.href =
        //       "https://yyzc-whjcyj.hbsis.gov.cn:31443/admin/user/oauth2Login"; //监管
        //   }
        // }, 3000);
      } else {
        return Promise.resolve(response);
      }
    } else {
      return Promise.reject(response);
    }
  },
  //服务器状态码不是200的情况
  (error) => {
    if (error.response.status) {
      switch (error.response.status) {
        // 401: 未登录
        // 未登录则跳转登录页面，并携带当前页面的路径
        // 在登录成功后返回当前页面，这一步需要在登录页操作。
        case 401:
          Message.error("登录过期，请重新登录");
          //清空登录信息
          // store.state.login = {
          //   user: {},
          //   token: "",
          //   userDistCode: "",
          //   isXiaZuan: true,
          //   park: {},
          //   isShowDist: true,
          //   enterData: {},
          // };
          // console.log(store.state.login);
          // setTimeout(() => {
          //   if (config.BASE_URL === "59.208.149.239:9999") {
          //     window.location.href = "https://59.208.147.57:1443/";
          //   } else {
          //     window.location.href =
          //       "http://www.hbsis.gov.cn/ZHPT_LOGIN_SERVER/login";
          //   }
          // }, 3000);
          break;
        // 403 token过期
        // 登录过期对用户进行提示
        // 清除本地token和清空vuex中token对象
        // 跳转登录页面
        case 403:
          Message.error("登录过期，请重新登录");
          // store.state.login = {
          //   user: {},
          //   token: "",
          //   userDistCode: "",
          //   isXiaZuan: true,
          //   park: {},
          //   isShowDist: true,
          //   enterData: {},
          // };
          // setTimeout(() => {
          //   if (config.BASE_URL === "59.208.149.239:9999") {
          //     window.location.href = "https://59.208.147.57:1443/";
          //   } else {
          //     window.location.href =
          //       "http://www.hbsis.gov.cn/ZHPT_LOGIN_SERVER/login";
          //   }
          // }, 3000);
          break;
        // 404请求不存在
        case 404:
          Message.closeAll();
          Message.error("404 NOT FOUND");
          break;
        case 426:
          Message.closeAll();
          Message.error("账号或密码错误");
          break;
        // 其他错误，直接抛出错误提示
        default:
          Message.closeAll();
          // 更换统一认证平台登录接口之后，登录失败返回200，所以单独判断登录失败的情况
          if (error.response.data.msg === "原密码错误") {
            Message.error("原密码错误");
          } else {
            Message.error("网络错误");
          }
          break;
      }
      return Promise.reject(error.response);
    }
  }
);
export default axios;
// /**  * get方法，对应get请求  * @param {String} url [请求的url地址]  * @param {Object} params [请求时携带的参数]  */
// export function get(url, params) {
//   return new Promise((resolve, reject) => {
//     axios
//       .get(url, {
//         params: params
//       })
//       .then(res => {
//         resolve(res.data);
//       })
//       .catch(err => {
//         reject(err.data);
//       });
//   }).catch(err => {});
// }
// /**  * post方法，对应post请求  * @param {String} url [请求的url地址]  * @param {Object} params [请求时携带的参数]  */
// export function post(url, params) {
//   return new Promise((resolve, reject) => {
//     axios.post(url, QS.stringify(params)).then(res => {
//         resolve(res);
//         console.log(res)
//       })
//       .catch(err => {
//         reject(err);
//       });
//   }).catch(err => {
//     console.log(err)
//   });
// }
