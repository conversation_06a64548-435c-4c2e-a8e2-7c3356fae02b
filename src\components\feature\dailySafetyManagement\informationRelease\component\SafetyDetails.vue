<template>
  <div class="app-container">
    <el-dialog :title="title" :visible.sync="open" width="1000px" append-to-body  :close-on-click-modal="false">
      <el-scrollbar :style="{ height: (orgType == 1 ? '66vh' : '66vh')  }" >
        <div class="cborder">
          <div class="flex cborder-bottom">
            <div class="cleftBox width-20 cborder-right">{{titles}}标题：</div>
            <div class="crightBox width-80">{{ form.earlyWarnTitle }}</div>
          </div>

          <div class="flex cborder-bottom">
            <div class="flex flex-1">
              <div class="cleftBox width-40 cborder-right">发布单位：</div>
              <div class="crightBox width-60">{{form.issueUnitName}}</div>
            </div>

         
          </div>


           <div class="flex cborder-bottom">
            <div class="flex flex-1">
              <div class="cleftBox width-40 cborder-right">发布对象：</div>
              <div class="crightBox width-60">{{orgDTOsAry}}</div>
            </div>

         
          </div>


  <div class="flex cborder-bottom">
          

            <div class="flex flex-1 cborder-bottom">
              <div class="cleftBox width-40 cborder-right">发布时间：</div>
              <div class="crightBox width-60">{{ form.releaseTime ? form.releaseTime : "--" }}</div>
            </div>
          </div>





          <div class="flex cborder-bottom">
            <div class="cleftBox width-20 cborder-right">{{titles}}内容：</div>
            <div class="crightBox width-80">{{ form.earlyWarnContent }}</div>
          </div>
        </div>

        <channel-list
          ref="channelList"
          :checkLists="chanList"
          :chanList="chanList"
          :disabled="true"
          v-if="orgType == 1"
          :closable="closable"
        ></channel-list>
      </el-scrollbar>
    </el-dialog>
  </div>
</template>

<script>

import ChannelList from "./ChannelList";

export default {
  name: "SafetyDetails",
  components: {
    ChannelList
  },
  props: {
    closable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      orgDTOsAry:[],
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全预警表格数据
      safetyList: [],
      // 弹出层标题
      title: "",
      tieles: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        earlyWarnTitle: null,
        earlyWarnStatus: null,
        typeOfEarlyWarn: "0"
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
      statusList: [
        { label: "全部", value: "-1" },
        { label: "已发布", value: "1" },
        { label: "待发布", value: "0" }
      ],
      chanList: [],
      orgType: null,
      titles: ""
    };
  },

  created() {},
  methods: {
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const warnCode = row.warnCode || this.ids;
      getSafety(warnCode).then(response => {        
        this.form = response.data;
       
        let _list = [];
        if (response.data.geReleaseChnList.length > 0) {
          response.data.geReleaseChnList.forEach(item => {
            _list.push({
              channelName:
                item.chntype == "1"
                  ? "系统消息"
                  : item.chntype == "2"
                  ? "短信"
                  : item.chntype == "3"
                  ? "应急广播"
                  : item.chntype == "4"
                  ? "微信公众号"
                  : "APP",
              publishObj: item.publishingObjects,
              isApply: item.aplctStatus == 1 ? false : true,
              code: item.chntype
            });
          });
          this.chanList = _list;
        } else {
          this.chanList = [];
        }
        this.open = true;
        this.title = "修改安全预警";
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.btnbox {
  display: flex;
  justify-content: flex-end;
}
.cborder-bottom {
  margin-bottom: 10px;
  .cborder-right {
    font-weight: 600;
    font-size: 16px;
  }
}
</style>