<template>
  <el-dialog
    :title="dialogInfo.title"
    :visible="visible"
    @close="closeBoolean"
    width="900px"
    top="5vh"
    :close-on-click-modal="true"
  >
    <div class="dialog" v-loading="loading">
      <el-form
        :model="accidentForm"
        :rules="rules"
        ref="ruleForm"
        label-width="150px"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="培训资料名称:" prop="trainName">
              <el-input
                v-model.trim="accidentForm.trainName"
                maxlength="100"
                clearable
                :disabled="dialogInfo.disable"
                placeholder="请输入培训资料名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="培训类型:" prop="trainType">
              <el-input
                v-model.trim="accidentForm.trainType"
                maxlength="100"
                clearable
                :disabled="dialogInfo.disable"
                placeholder="请输入培训类型"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="培训主题:" prop="trainTheme">
              <el-input
                v-model.trim="accidentForm.trainTheme"
                maxlength="100"
                clearable
                :disabled="dialogInfo.disable"
                placeholder="请输入培训主题"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="培训内容描述:" prop="trainContent">
              <el-input
                v-model.trim="accidentForm.trainContent"
                type="textarea"
                :rows="3"
                clearable
                placeholder="2000字以内"
                maxlength="2000"
                :disabled="dialogInfo.disable"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="培训范围:" prop="trainRanges">
              <!-- <el-select
                v-model="accidentForm.trainRanges"
                placeholder="请选择培训范围"
                clearable
                multiple
                :disabled="dialogInfo.disable"
                style="width: 100%"
                @focus="handleShare"
              >
                <el-option
                  v-for="item in hazarchemList"
                  :key="item.hazarchemId"
                  :value="item.hazarchemId"
                  :label="item.hazarchemName"
                >{{ item.hazarchemName }}</el-option>
              </el-select> -->
              <div class="range-box">
                <div class="content">
                  <el-tag
                    v-for="tag in accidentForm.trainDataUserDTOS"
                    class="tagItem"
                    :key="tag.userId"
                  >
                    {{ tag.userName }}
                  </el-tag>
                </div>
                <el-button type="text" @click="handleShare">选择范围</el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="上传附件:">
              <AttachmentUpload
                :attachmentlist="accidentForm.trainEnclosureList"
                :limit="1"
                type="office"
                v-bind="{}"
                :editabled="dialogInfo.disable"
              ></AttachmentUpload>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button
        type="primary"
        v-if="!dialogInfo.disable"
        size="small"
        @click="handleSubmit"
        >确定</el-button
      >
    </div>
    <shareDialog
      v-if="showCheck"
      :visible="showCheck"
      @closeBoolean="closeShare"
      @change="tableSelect"
    ></shareDialog>
  </el-dialog>
</template>

<script>
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import shareDialog from "./shareDialog.vue";
import {
  getTrainData,
  addTrainData,
  updateTrainData,
} from "@/api/enterpEducation";
export default {
  components: {
    AttachmentUpload,
    shareDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    dialogType: {
      type: String,
      default: "",
    },
    educationItem: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      loading: false,
      dialogInfo: {
        // 弹窗控制信息
        visible: false,
        title: "新增教育培训资料",
        disable: false,
      },
      hazarchemList: [],
      dangerSourceList: [],
      accidentForm: {
        trainContent: "",
        trainEnclosureList: [],
        trainName: "",
        trainRanges: [],
        trainTheme: "",
        trainType: "",
      },
      rules: {
        // 表单校验规则
        trainName: [
          { required: true, message: "请填写事件标题", trigger: "blur" },
          // { max: 255, message: "长度 255 个字符一下", trigger: "blur" },
        ],
        trainType: [
          {
            required: true,
            message: "请选择培训类型",
            trigger: "change",
          },
        ],
        trainTheme: [
          {
            required: true,
            message: "请输入培训主题",
            trigger: "blur",
          },
        ],
        trainContent: [
          { required: true, message: "请输入培训内容", trigger: "blur" },
        ],
      },
      showCheck: false,
    };
  },
  created() {
    this.dialogInfo.visible = this.visible;
    if (this.dialogType === "edit") {
      this.dialogInfo.title = "修改教育培训资料";
      this.getDetail();
    } else if (this.dialogType === "add") {
      this.dialogInfo.title = "新增教育培训资料";
    } else {
      this.dialogInfo.title = "查看教育培训资料";
      this.dialogInfo.disable = true;
      this.getDetail();
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dialogType === "add") {
            // const userId = this.hazarchemList.map(item => item.id).toString()
            addTrainData(this.accidentForm).then((res) => {
              if (res.data.status == "200") {
                this.$message.success("新增成功");
                this.$emit("closeBoolean", true);
              }
            });
          } else {
            updateTrainData(this.accidentForm).then((res) => {
              if (res.data.status == "200") {
                this.$message.success("修改成功");
                this.$emit("closeBoolean", true);
              }
            });
          }
        }
      });
    },
    closeBoolean() {
      this.$emit("closeBoolean", false);
    },
    async getDetail() {
      this.loading = true;
      await getTrainData(this.educationItem.id)
        .then((res) => {
          if (res.data.status == "200") {
            this.accidentForm = res.data.data;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleShare() {
      this.showCheck = true;
    },
    closeShare() {
      this.showCheck = false;
    },
    tableSelect(list) {
      this.hazarchemList = list.map((item) => {
        return {
          ...item,
          userId: item.id,
          userName: item.companyName,
        };
      });
      
      this.showCheck = false;
      this.$nextTick(() => {
        this.accidentForm.trainDataUserDTOS = this.hazarchemList;
      });
      this.accidentForm.trainDataUserDTOS = this.hazarchemList;
      console.log(this.accidentForm.trainDataUserDTOS);
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog {
  height: 500px;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 10px;
}

.range-box {
  min-height: 40px;
  display: flex;
  box-sizing: border-box;
  .content {
    flex: 1;
    border: 1px solid #dcdfe6;
    padding: 5px 10px;
    margin-right: 10px;
    line-height: 32px;
    border-radius: 5px;
    .tagItem {
      margin: 0 12px 5px 0;
    }
  }
}

.footer {
  padding-top: 10px;
  text-align: center;
}
</style>
