<template>

    <el-dialog
      title="
重大危险源列表"
      :visible.sync="show"
      width="1350px"
      top="5vh"
      height="70vh"
      @close="closeBoolean(false)"
      append-to-body
      :close-on-click-modal="false"
    >
    <div class="EquipmentList">
      <div class="dialog" v-loading="div1Loading">
        <div class="div1">
          <div class="title">
            <div class="l">
              <el-select
                v-model="value"
                placeholder="请选择"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
              <el-button type="primary" size="mini" @click="getData(id)"
                >查询</el-button
              >
            </div>
            <el-button type="primary" size="mini" @click="exportEquipList(id)">导出</el-button>
          </div>
            <!-- {{table.length}} -->
          <div class="table"  v-if="table.length>0">
            <ul class="container header">
              <li>序号</li>
              <li>所属重大危险源</li>
              <li>设备类型</li>
              <li>设备名称</li>
              <li class="liLine">监测指标</li>
            </ul>
          
            <ul class="container" v-for="(item, index) in table" :key="index">
              <li>{{ index + 1 }}</li>
              <li>{{ item.dangerName }}</li>
              <li v-if="item.sensortypeCode == 'G0'">储罐</li>
              <li v-else-if="item.sensortypeCode == 'Q0'">泄漏点</li>
              <li v-else-if="item.sensortypeCode == 'P0'">装置</li>
              <li v-else></li>
              <li>{{ item.monitorname }}</li>
              <!-- <li class="liLine"><u >{{item.targetNum}}</u></li> -->
              <li class="liLine">
                <el-button v-if='item.targetNum > 0' @click="openDiv2(item.sensormonId)" type="text">{{
                  item.targetNum
                }}</el-button>

                 <el-button v-else disabled type="text">{{
                  item.targetNum
                }}</el-button>

              </li>
            </ul>
          </div>
          <div class="table"  v-else>
            <ul class="container header">
              <li>序号</li>
              <li>所属重大危险源</li>
              <li>设备类型</li>
              <li>设备名称</li>
              <li class="liLine">监测指标</li>
            </ul>
            <div style="text-align:center;padding:40px 0;width:100%">暂无数据</div>
            <!-- <ul class="container"> 
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li class="liLine">
              </li>
            </ul> -->
          </div>
        </div>
      </div>

    <el-dialog
      title="
重大危险源列表详情"
      :visible.sync="showDiv2"
      width="1350px"
      top="5vh"
      height="70vh"
      @close="closeDiv2(false)"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="dialog" v-loading="div2Loading">
        <div class="div2">
          <!-- <div class="title">
          </div> -->
          <div class="table"  v-if="sensormon.length>0">
            <ul class="container header">
              <li>序号</li>
              <li>指标名称</li>
              <li>指标类型</li>
              <li>指标位号</li>
              <li>计量单位</li>
              <li>仪表量程下限</li>
              <li>仪表量程上限</li>
              <li>低低报</li>
              <li>低报</li>
              <li>高报</li>
              <li class="liLine">高高报</li>
            </ul>
            <ul
              class="container"
              v-for="(item, index) in sensormon"
              :key="index"
            >
              <li>{{ index + 1 }}</li>
              <li>{{ item.targetName }}</li>
              <li>{{ item.monitemkey }}</li>
              <li>{{ item.bitNo }}</li>
              <li>{{ item.unit }}</li>
              <li>{{ item.rangeLowValue == null ? " " : Number(item.rangeLowValue).toFixed(2) }}</li>
              <li>{{ item.rangeTopValue == null ? " " : Number(item.rangeTopValue).toFixed(2) }}</li>
              <li>
                {{ item.lowLowAlarmValue == null ? " " : Number(item.lowLowAlarmValue).toFixed(2) }}
              </li>
              <li>{{ item.lowAlarmValue == null ? " " : Number(item.lowAlarmValue).toFixed(2) }}</li>
              <li>
                {{ item.highAlarmValue == null ? " " : Number(item.highAlarmValue).toFixed(2) }}
              </li>
              <li class="liLine">
                {{
                  item.highHighAlarmValue == null ? " " : Number(item.highHighAlarmValue).toFixed(2)
                }}
              </li>
            </ul>
          </div>

          <div class="table" v-else>
            <ul class="container header">
              <li>序号</li>
              <li>指标名称</li>
              <li>指标类型</li>
              <li>指标位号</li>
              <li>计量单位</li>
              <li>仪表量程下限</li>
              <li>仪表量程上限</li>
              <li>低低报</li>
              <li>低报</li>
              <li>高报</li>
              <li class="liLine">高高报</li>
            </ul>
            <ul
              class="container"
            >
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li class="liLine">
              </li>
            </ul>
          </div>
        </div>
      </div>

    </el-dialog>
    </div>
  </el-dialog>
</template>

<script>
import {
  getInformationInfoDangerEquipmen,
  getInformationEquipmenAlarmvalue,
  exportInformationInfoDangerEquipmentExcel,
} from "@/api/entList";
export default {
  //import引入的组件
  name: "hazards",
  components: {},
  data() {
    return {
      show: false,
      showDiv2: false,
      options: [
        {
          value: "G0",
          label: "储罐",
        },
        {
          value: "Q0",
          label: "泄漏点",
        },
        {
          value: "P0",
          label: "装置",
        },
      ],
      value: "",
      table: {},
      div1Loading: false,
      div2Loading: false,
      sensormon: {},
      sensormonId: "",
      id: "",
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.value=''
      this.show = val;
      this.table = {};
    },
    closeDiv2(val) {
      this.showDiv2 = val;
      this.sensormon = {};
    },
    openDiv2(sensormonId) {
      this.showDiv2 = true;
      this.getListData(sensormonId);
    },
    getData(id) {
      this.id = id;
      this.div1Loading = true;
      getInformationInfoDangerEquipmen({
        id: this.id,
        sensortypecode: this.value,
      }).then((res) => {
        this.table = res.data.data;
        this.div1Loading = false;
      });
    },
    //导出危险源设备清单
    exportEquipList(id){
      this.id = id;    
      exportInformationInfoDangerEquipmentExcel({
        id: this.id,
        sensortypecode: this.value,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "重大危险源设备清单" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    getListData(sensormonId) {
      this.sensormonId = sensormonId;
      this.div2Loading = true;
      getInformationEquipmenAlarmvalue(this.sensormonId).then((res) => {
        this.sensormon = res.data.data;
        this.div2Loading = false;
      });
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.dialog {
  height: 70vh;
  overflow: auto;
}
.EquipmentList {
  overflow: auto;
  color: #606266;

  ul {
    padding-inline-start: 0px;
  }
  .div1 {

    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .l {
        width: 250px;
        display: flex;
        justify-content: space-between;
      }
    }
    .table {
      height: 63vh;
    overflow: auto;
      .header {
        background: rgb(242, 246, 255);
        font-weight: 900;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 20%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;

          align-items: center;
        }
        .liLine {
          list-style-type: none;
          width: 20%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }

}
.div2 {
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      .l {
        width: 250px;
        display: flex;
        justify-content: space-between;
      }
    }
    .table {
      .header {
        background: rgb(242, 246, 255);
        font-weight: 900;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 9.1%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;

          align-items: center;
        }
        .liLine {
          list-style-type: none;
          width: 9%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
          u {
            color: #1086e8;
            cursor: pointer;
          }
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
</style>
