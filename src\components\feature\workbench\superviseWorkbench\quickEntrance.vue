<template>
  <div style="height: 100%">
    <div class="securityCommitments2">
      <div class="header">
        <div class="title">快捷功能</div>
      </div>
      <div class="moreStyleBoxR">
        <!-- <p @click="equipmentAbnormalDeclarationFn">
          <img src="../../../../../static/img/assets/img/icon1.png" /><span
            class="words"
            >审核代办</span
          >
        </p> -->
        <!-- <p @click="hubeiFn">
          <img src="../../../../../static/img/assets/img/icon2.png" /><span
            class="words"
            >舆情系统</span
          >
        </p> -->
        <p @click="emergencyResourcesFn">
          <img src="../../../../../static/img/assets/img/icon3.png" /><span
            class="words"
            >应急资源</span
          >
        </p>
        <p @click="hazardManagementFn">
          <img src="../../../../../static/img/assets/img/icon4.png" /><span
            class="words"
            >隐患管理</span
          >
        </p>
        <!-- <p @click="monitorWarnReport">
          <img src="../../../../../static/img/assets/img/icon5.png" /><span
            class="words"
            >日报</span
          >
        </p> -->

        <p @click="bigModelFn">
          <img src="../../../../../static/img/assets/img/ai-img.png" /><span
            class="words"
            >大模型</span
          >
        </p>
      </div>
    </div>
  </div>
</template>

<script>
import { InformationExportexcel } from "@/api/entList";
import {
  getSafetyListData,
  getentArr,
  getSafetyPromiseExportPromiseDetailsToExcel,
  addUrge,
} from "@/api/workingAcc";
import dayjs from "dayjs";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      currentPage: 1,
      show: false,
      level: [
        {
          label: "一级",
          value: "1",
        },
        {
          label: "二级",
          value: "2",
        },
        {
          label: "三级",
          value: "3",
        },
        {
          label: "四级",
          value: "4",
        },
      ],
      options: [
        {
          value: "1",
          label: "一级",
        },
        {
          value: "2",
          label: "二级",
        },
        {
          value: "3",
          label: "三级",
        },
        {
          value: "4",
          label: "四级",
        },
      ],
      majorHazardLevel: ["1", "2", "3", "4"],
      input: "",
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      value: "",
      tableData: {},
      districtLoading: false,
      enterpName: "",
      loading: false,
      selection: [],
      SafetyListData: {},
      riskSum: "",
      risk: [],
      entName: "",
      title: "",
      loadingBox: false,
      demoKey: 0,
      submissionFlag: true,
      urgeLag: "",
    };
  },
  //方法集合
  methods: {
    //审核代办
    equipmentAbnormalDeclarationFn() {
      window.open(
        // "http://**************:8291/bi/whc_gsafety/app/%E9%A6%96%E9%A1%B5-%E7%9B%91%E7%AE%A1.app"
        "http://yyzc-yjtssbi.hbsis.gov.cn:8291/bi/whc_gsafety/app/%E9%A6%96%E9%A1%B5-%E7%9B%91%E7%AE%A1.app"
      );
    },
    //舆情系统
    hubeiFn() {
      window.open("https://ecdp.gsafety.com/ieds/freelogin/hubei/");
    },
    //信息发布
    informationReleaseFn() {
      this.$router.push({ path: "/dailySafetyManagement/informationRelease" });
    },
    //日报
    monitorWarnReport() {
      this.$router.push({ path: "/riskAssessment/monitorWarnReport" });
    },
    //应急资源
    emergencyResourcesFn() {
      this.$router.push({ path: "/gardenEnterpriseManagement/mergencyResources" });
    },
    //隐患管理
    hazardManagementFn(){
      // iframe传递给父组件
      window.top.postMessage({ action: "toHazardManagement" }, "*");
    },
    bigModelFn() {
      // iframe传递给父组件
      window.top.postMessage({ action: "changeMenu" }, "*");
    },
    //安全承诺分析
    addUrgeFn(entId) {
      let that = this;
      this.$confirm("确认督促该企业？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "error",
      })
        .then(() => {
          let param = {
            enterpId: entId,
            urgeDate: dayjs().format("YYYY-MM-DD"),
          };
          addUrge(param).then((res) => {
            if (res) {
              this.$message.success("已发送督促！");
              that.getEntData();
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    //dianMOFn
    dianMOFn() {
      this.$message({
        type: "info",
        message: "功能建设中",
      });
    },
    closeDialog() {
      this.currentPage = 1;
      this.entName = "";
      this.majorHazardLevel = ["1", "2", "3", "4"];
      this.tableData = {};
      this.districtVal = this.$store.state.login.userDistCode;
      this.demoKey =
        "demo-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
    },
    openDialog(type) {
      this.show = true;
      this.majorHazardLevel = ["1", "2", "3", "4"];
      this.currentPage = 1;
      this.type = type;
      this.getEntData();
    },
    toUrl(url) {
      this.$router.push(url);
    },
    search() {
      this.loadingBox = true;
      getSafetyListData({
        level: this.majorHazardLevel.join(","),
        current: this.currentPage,
        distCode: this.districtVal,
        size: 100,
      }).then((res) => {
        // console.log(res);
        if (res.data.code == 0) {
          this.loadingBox = false;
          this.SafetyListData = res.data.data.records[0];
          this.riskSum =
            res.data.data.records[0].riskFour +
            res.data.data.records[0].riskOne +
            res.data.data.records[0].riskThree +
            res.data.data.records[0].riskTwo;
          this.risk = [
            {
              value: res.data.data.records[0].riskOne,
              name: "高风险",
              color: "#f86767",
            },
            {
              value: res.data.data.records[0].riskTwo,
              name: "较大风险",
              color: "#ffaa71",
            },
            {
              value: res.data.data.records[0].riskThree,
              name: "一般风险",
              color: "#f1d650",
            },
            {
              value: res.data.data.records[0].riskFour,
              name: "低风险",
              color: "#499FFF",
            },
          ];
          // console.log(this.risk);
          this.risk.map((item, index) => {
            if (item.value == 0 || item.value == "" || item.value == null) {
              // console.log(item,index)
              this.risk.splice(index, 1);
            }
          });
          this.getEcharts();
          this.setEchart("securityCommitmentsEcharts");
        }
      });
    },
    handleCurrentChange() {
      this.getEntData();
    },
    select(selection, row) {
      this.selection = [];
      // console.log(selection);
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].entId;
      }
    },
    goEnt(row) {
      console.log(row);
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.entId);
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].entId;
      }
    },
    getEcharts() {
      var chartDom = document.getElementById("securityCommitmentsEcharts");
      var myChart = this.$echarts.init(chartDom);
      var option;
      var data = this.risk;
      var color = [];
      this.risk.map((item, index) => {
        color[index] = item.color;
      });
      option = {
        tooltip: {
          formatter: "{b}：{c}个 ({d}%)",
        },
        series: [
          {
            name: "",
            type: "pie",
            radius: ["80%", "60%"],
            data: data,
            // color: ["#f86767", "#ffaa71", "#f1d650", "#499FFF"],
            color: color,
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
              },
            },
          },
        ],
      };
      option && myChart.setOption(option);
    },

    getEntData() {
      this.loading = true;
      if (this.type == 0) {
        this.title = "安全承诺分析-应承诺企业列表";
      } else if (this.type == 1) {
        this.title = "安全承诺分析-重大风险企业列表";
      } else if (this.type == 2) {
        this.title = "安全承诺分析-较大风险企业列表";
      } else if (this.type == 3) {
        this.title = "安全承诺分析-一般风险企业列表";
      } else if (this.type == 4) {
        this.title = "安全承诺分析-较低风险企业列表";
      } else if (this.type == 5) {
        this.title = "安全承诺分析-未承诺企业列表";
      } else if (this.type == 6) {
        this.title = "安全承诺分析-已承诺企业列表";
      }
      getentArr({
        distCode: this.districtVal,
        entName: this.entName,
        majorHazardLevel: this.majorHazardLevel.join(","),
        type: this.type,
        current: this.currentPage,
        size: 10,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data;
        }
      });
    },
    exportExcel() {
      let list = [...this.selection];
      getSafetyPromiseExportPromiseDetailsToExcel({
        distCode: this.districtVal,
        entIdList: list.length <= 1 ? null : list,
        entName: this.entName,
        majorHazardLevel: this.majorHazardLevel.join(","),
        type: this.type,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "安全承诺趋势分析" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    //设置echart大小
    setEchart(eleName) {
      var echartsWarp = document.getElementById(eleName);
      var resizeWorldMapContainer = function () {
        //用于使chart自适应高度和宽度,通过窗体高宽计算容器高宽
        echartsWarp.style.width = "100%";
        echartsWarp.style.height = "60%";
      };
      resizeWorldMapContainer(); //设置容器高宽
      var myChart = this.$echarts.init(echartsWarp);
      window.onresize = function () {
        //用于使chart自适应高度和宽度
        resizeWorldMapContainer(); //重置容器高宽
        myChart.resize();
      };
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.search();
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.disabledStyle {
}
.securityCommitments2 {
  width: 100%;
  height: 100%;
  border: 1px solid #d8e0ee;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  position: relative;
  // margin: 15px 0 0 0;
  background-color: #fff;
  .header {
    width: 95%;
    display: flex;
    justify-content: space-between;
    margin-top: 13px;
    .title {
      margin-left: 5px;
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
    }
    .more {
      color: #3977ea;
      font-size: 14px;
      cursor: pointer;
    }
  }
  .moreStyleBoxR {
    display: flex;
    flex-direction: column;
    flex-wrap: wrap;
    width: 95%;
    margin-top: 13px;
    // justify-content: space-between;
    p {
      position: relative;
      font-size: 20px;
      cursor: pointer;
      width: 100%;
      height: 55px;
      text-align: center;
      margin: 0;
      // background: #3977ea;
      border-radius: 4px;
      border: 2px solid #ffffff;
      margin: 0 10px 10px 0;
      color: #fff;
    }
    p:hover {
      color: #f1d650;
    }
    p img {
      position: absolute;
      left: 15px;
      top: 10px;
      width: 30px;
      height: 30px;
    }
  }
}
.words {
  position: absolute;
  left: 50px;
  top: 10px;
  color: #333333;
  font-size: 16px;
}
</style>
