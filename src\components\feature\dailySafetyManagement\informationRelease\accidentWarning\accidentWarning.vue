<template>
  <div class="app-container">
    <div class="specification-from-box">
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist && showArea"
          ></el-cascader>

          <el-select
            v-model="queryParams.typeCode"
            placeholder="请选择事故类型"
            clearable
            size="small"
          >
            <el-option
              v-for="(item, index) in statusList"
              :key="index"
              :label="item.name"
              :value="item.code"
            />
          </el-select>

          <el-autocomplete
            popper-class="my-autocomplete"
            v-model="queryParams.unitName"
            :fetch-suggestions="querySearch"
            placeholder="请输入事故单位名称"
            clearable
            @clear="clearSensororgCode()"
            @select="handleSelect"
            size="mini"
            style="width: 200"
          >
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete>

          <el-date-picker
            v-model="queryParams.datePick"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd"
            unlink-panels
            clearable
          >
          </el-date-picker>
        </div>
        <div>
          <el-button type="primary" size="small" @click="handleQuery"
            >查询</el-button
          >
        </div>
        <!-- <el-form-item>
          <el-button size="small" type="primary" plain @click="openTemplate"
            >选择模板</el-button
          >
        </el-form-item> -->
      </div>
    </div>

    <div class="specification-table-head" id="abc">
      <div class="left">安全生产事故警示列表</div>
      <!-- 测试 -->
      <div>
        <el-button
          type="primary"
          v-if="roleInfo.user_type !== 'ent'"
          plain
          icon="el-icon-plus"
          size="small"
          @click="handleAdd"
          >新增</el-button
        >
      </div>
    </div>

    <el-table v-loading="loading" :data="safetyList">
      <el-table-column label="序号" align="center" type="index" width="70" />
      <el-table-column label="事故单位名称" align="center" prop="unitName">
        <template slot-scope="scope">
          <div class="title-hover">{{ scope.row.unitName }}</div>
        </template>
      </el-table-column>
      <!-- releaseTime -->
      <el-table-column label="所属行政区划" align="center" prop="distName">
        <!-- <template slot-scope="scope">
          <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
        </template> -->
      </el-table-column>
      <el-table-column label="事故类型" align="center" prop="typeName">
      </el-table-column>

      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <span>
            {{ scope.row.status == 0 ? "待发布" : "已发布" }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="事故时间" align="center" prop="time" width="180">
      </el-table-column>
      <!-- <el-table-column label="事故介绍" align="center" prop="description">
      </el-table-column> -->
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
        width="260"
      >
        <!-- 已发布：1 没有编辑和删除 -->
        <template slot-scope="scope">
          <el-button type="text" @click="handleView(scope.row)">查看</el-button>
          <el-button
            type="text"
            @click="handleUpdate(scope.row)"
            v-if="user.user_type != 'ent'"
            :disabled="scope.row.status == 1"
            >编辑</el-button
          >

          <!--          <el-button round size="mini" @click="cancelPublishFun(scope.row)"-->
          <!--            >取消发布</el-button-->
          <!--          >-->

          <el-button
            type="text"
            @click="handleDelete(scope.row)"
            v-if="user.user_type != 'ent'"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        :total="total"
        :page.sync="queryParams.nowPage"
        :limit.sync="queryParams.pageSize"
        @current-change="handleGetList"
        background
        layout="total, prev, pager, next"
      ></el-pagination>
    </div>

    <!-- 添加或修改事故警示对话框 -->
    <el-dialog
      class="addWarningBox"
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
      @close='closeDialog'
      top="10vh"
      :close-on-click-modal="false"
    >
      <div v-loading="dialogLoading" class="dialogInfo">
        <!-- <div class="topSearch">
          <div style="width: 400px; margin-right: 10px">           
            <el-autocomplete
              popper-class="my-autocomplete"
              v-model="form.unitName"
              :fetch-suggestions="querySearch"
              placeholder="请输入企业名称关键字"
              clearable
              @clear="clearSensororgCodeDialog()"
              @select="handleSelectDialogFn"
              size="medium"
              style="width: 100%"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.enterpName }}</div>
              </template>
            </el-autocomplete>
          </div>

          <div>
            <el-button type="primary" size="small">查询</el-button>
          </div>
        </div> -->

        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          label-width="150px !important"
        >
          <el-form-item label="事故单位名称" prop="unitName">
            <!-- <el-input
              v-model="form.unitName"
              maxlength="30"
              placeholder="事故单位名称"
              disabled
            /> -->

            <el-autocomplete
              popper-class="my-autocomplete"
              v-model="form.unitName"
              :fetch-suggestions="querySearch"
              placeholder="请输入企业名称关键字"
              clearable
              @clear="clearSensororgCodeDialog()"
              @select="handleSelectDialogFn"
              style="width: 100%"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.enterpName }}</div>
              </template>
            </el-autocomplete>
          </el-form-item>

          <el-form-item label="所属行政区划" prop="districtName">
            <el-input
              v-model.trim="form.districtName"
              maxlength="30"
              placeholder="所属行政区划"
              disabled
            />
          </el-form-item>

          <el-form-item label="事故类型" prop="typeCode">
            <el-select
              v-model="form.typeCode"
              placeholder="事故类型"
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="(item, index) in statusList"
                :key="index"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="事故时间" prop="time">
            <el-date-picker
              v-model="form.time"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="选择日期"
              style="width: 100%"
            >
              value-formt
            </el-date-picker>
          </el-form-item>

          <el-form-item label="事故介绍" prop="description" style="width: 100%">
            <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 10 }"
              resize="none"
              maxlength="1000"
              show-word-limit
              placeholder="请输入事故介绍"
              v-model.trim="form.description"
            ></el-input>
          </el-form-item>

          <!-- {{form.attachmentList}} -->

          <el-form-item
            label="附件"
            prop="attachmentList"
            class="newAttachment"
          >
            <AttachmentUpload
              :attachmentlist="form.attachmentList"
              :limit="5"
              type="office"
              v-bind="{}"
              class="newAttachment"
              @resBack='resBackAttachmentList'
            ></AttachmentUpload>
          </el-form-item>
        </el-form>

        <!-- <div class="btnbox">
          <el-button
            v-if="roleInfo.user_type !== 'ent'"
            type="primary"
            @click="chooseTemplate()"
            icon="el-icon-tickets"
            size="small"
            >选择模板</el-button
          >
        </div> -->
        <channel-list
          ref="channelList"
          :chanList="chanList"
          :checkLists="chanList"
          @selectList="selectList"
          :nextSearchForm='nextSearchForm'
        ></channel-list>
      </div>

      <div slot="footer" style="display: flex; justify-content: center">
        <el-button type="primary" @click="submitForm(1)">发布</el-button>
        <el-button type="primary" @click="submitForm(0)">保存草稿</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 详情弹窗 -->
    <accidentWarningDetails
      ref="accidentWarningDetails"
      :closable="closable"
    ></accidentWarningDetails>
  </div>
</template>

<script>
// import { mapState } from "vuex";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");

import {
  addSafety,
  noticeEvent,
  eventCode,
  noticeAdd,
  noticeDelete,
  noticeUpdate,
  noticeId,
} from "../../../../../api/informationRelease";
import { delSafety, getSafety } from "../../../../../api/BasicDataManagement";
import { getSearchArr } from "@/api/entList.js";
import { getInformationBasicInfo } from "@/api/entList";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";

const ChannelList = () => import("../component/ChannelListSearch");
const accidentWarningDetails = () => import("./accidentWarningDetails");
const checkConIsEmpty = (rule, value, callback) => {
  if (!value.trim()) {
    return callback(new Error("内容不能为空"));
  } else {
    callback();
  }
};

export default {
  name: "AccidentWarning",
  components: {
    ChannelList,
    accidentWarningDetails,
  },
  data() {
    return {
      nextSearchForm:{},
      showArea:true,
      district: this.$store.state.controler.district,
      distCode: this.$store.state.login.userDistCode, //行政区划代码
      queryParams: {
        orgCode: "",
        unitName: "",
        typeCode: "",
        datePick: [],
        nowPage: 1,
        pageSize: 10,        
      },
      listSelect: "",
      roleInfo: {},
      dialogLoading: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 安全预警表格数据
      safetyList: [],
      // 弹出层标题
      title: "",
      titles: "",
      // 是否显示弹出层
      open: false,

      // 表单参数
      form: {
        unitId: "",
        unitName: "",
        orgDTOs: [],
        time: "",
        rcveUnit: "",
        attachmentList: [],
        typeCode: "",
        status: "",
        distCode: "",
        districtName: "",
        id: "",
        description: "",
      },
      // 表单校验
      rules: {
        unitName: [
          {
            required: true,
            message: "事故单位名称不能为空",
            trigger: ["blur", "change"],
          },
          // {
          //   validator: checkConIsEmpty,
          //   trigger: ["blur", "change"],
          // },
        ],
        districtName: [
          {
            required: true,
            message: "所属行政区划不能为空",
            trigger: ["blur", "change"],
          },
          // {
          //   validator: checkConIsEmpty,
          //   trigger: ["blur", "change"],
          // },
        ],
        typeCode: [
          {
            required: true,
            message: "事故类型不能为空",
            trigger: ["blur", "change"],
          },
        ],
        time: [
          {
            required: true,
            message: "事故时间不能为空",
            trigger: ["blur", "change"],
          },
        ],

        description: [
          {
            required: true,
            message: "事故介绍不能为空",
            trigger: ["blur", "change"],
          },
        ],

        attachmentList: [
          {
            required: true,
            message: "附件不能为空",
            trigger: ["blur", "change"],
          },
        ],
      },
      statusList: [
        // { label: "已发布", value: "1" },
        // { label: "待发布", value: "0" },
      ],
      chanList: [],
      closable: true,
    };
  },

  created() {
     this.getList()
    this.roleInfo =
      JSON.parse(sessionStorage.getItem("VueX_local")).root.login.user || {};
  },
  computed: {
    // ...mapState({
    //   user: (state) => state.login.user,
    // }),
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
  methods: {
     resBackAttachmentList(){
      this.$refs['form'].clearValidate(['attachmentList']);
    },
    handleGetList(val) {
      this.queryParams.nowPage = val;
      this.getList();
    },
    //获取事故类型下拉
    eventCodeFn() {
      eventCode({}).then((response) => {
        if (response.data.status == 200) {
          this.statusList = response.data.data.typeCode;
        }
      });
    },

    //获取企业信息
    searchBaseInfo() {
      getInformationBasicInfo(this.queryParams.orgCode).then((res) => {});
    },
    //自动输入
    clearSensororgCode() {
      this.queryParams.orgCode = "";
    },
    clearSensororgCodeDialog() {
      this.form.unitId = "";
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.orgCode = item.enterpId;
      this.queryParams.unitName = item.enterpName;
    },
    handleSelectDialogFn(item) {     
      this.form.unitId = item.enterpId;
      this.form.unitName = item.enterpName;
      getInformationBasicInfo(item.enterpId).then((res) => {        
        this.form.distCode = res.data.data.enterprise.districtCode; //行政区划代码
        this.form.districtName = res.data.data.enterprise.districtName;
        this.nextSearchForm={
          latitude:res.data.data.enterprise.latitude,
          longitude:res.data.data.enterprise.longitude
        }
        //latitude,longitude
      });
    },
    // 自动输入end

    selectList(list) {
      this.form.instCode = list;
      this.listSelect = list;
      console.log(list, "发布对象");
    },

    chooseTemplate() {
      this.$refs.templateChoose.open = true;
      this.$nextTick(() => {
        this.$refs.templateChoose.getList();
      });
    },

    choosedTemplateInfo(e) {
      this.form.earlyWarnTitle = e.tmpltName;
      this.form.earlyWarnContent = e.tmpltContent;
    },

    /** 查询安全预警列表 */
    getList() {
      if (this.$store.state.login.user.user_type == "ent") {
        this.showArea = false;
      }
      var params = {
        distCode: this.distCode,
        startTime:
          this.queryParams.datePick.length > 0
            ? this.queryParams.datePick[0] + ' 00:00:00'
            : "",
        endTime:
          this.queryParams.datePick.length > 0
            ? this.queryParams.datePick[1]+' 23:59:59'
            : "",
        nowPage: this.queryParams.nowPage,
        pageSize: this.queryParams.pageSize,
        typeCode: this.queryParams.typeCode,
        unitName: this.queryParams.unitName,
      };

      this.loading = true;
      noticeEvent(params).then((response) => {
        this.loading = false;
        if (response.data.status == 200) {
          this.safetyList = response.data.data.list;
          this.total = response.data.data.total || 0;
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.$refs['channelList'].radius=''
      this.$refs['channelList'].enterpriseTypes=[]
      this.$refs['channelList'].enterpname=''
      this.reset();
    },
    closeDialog(){
      this.$refs['channelList'].radius=''
      this.$refs['channelList'].enterpriseTypes=[]
      this.$refs['channelList'].enterpname=''
    },
    // 表单重置
    reset() {    
      this.form = {
        unitId: "",
        unitName: "",
        orgDTOs: [],
        time: "",
        rcveUnit: "",
        attachmentList: [],
        typeCode: "",
        status: "",
        distCode: "",
        districtName: "",
        id: "",
        description: "",
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.nowPage = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.warnCode);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.resetForm("form");
      this.resetForm("queryForm");
      // this.$set(this.form, "", "");
      this.chanList = [];
      this.$nextTick(() => {
        this.open = true;
        this.title = "添加事故警示";
      });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      // debugger
      this.reset();
      this.resetForm("form");
      this.resetForm("queryForm");
      this.open = true;
      this.title = "修改事故警示";
      this.dialogLoading = true;
      const id = row.id;
      noticeId({ id: id }).then((response) => {
        this.dialogLoading = false;
        this.form = response.data.data;
        this.chanList = response.data.data.orgDTOs;
        this.form.districtName = response.data.data.distName;
      });
    },
    // 某个单元格被点击事件
    handleView(row, col) {
      this.$refs.accidentWarningDetails.open = true;
      this.$refs.accidentWarningDetails.title = "事故警示详情";
      this.closable = false;
      this.$refs.accidentWarningDetails.getData(row, col);
    },
    debounce(func, delay) {
      const context = this; // this指向发生变化，需要提出来
      const args = arguments;
      return (function () {
        if (context.timeout) {
          clearTimeout(context.timeout);
        }
        context.timeout = setTimeout(() => {
          func.apply(context, args);
        }, delay);
      })();
    },

    /** 提交按钮 */
    submitForm(type) {
      // debugger;
      // console.log(this.listSelect);
      var that = this;
      let orgAry = [];
      if (this.listSelect.length == 0) {
        //表示回填时候，没有做任何修改
        if (this.chanList.length > 0) {
          this.chanList.forEach((item, index) => {
            orgAry[index] = item.orgCode;
          });
        }
      }

      that.debounce(() => {
        let orgDTOsAry = [];
        if (this.listSelect.length > 0) {
          this.listSelect.forEach((item, index) => {
            orgDTOsAry[index] = item.orgCode;
          });
        }
        let orgDTOs = orgAry.concat(orgDTOsAry);

        if (orgDTOs.length == 0) {
          this.$message.error("请添加发布对象");
          return false;
        }

        this.form.orgDTOs = orgDTOs;
        this.form.rcveUnit = orgDTOs.toString();
        this.form.status = type;
        console.log(this.form, "事故警示列表");
        this.$refs["form"].validate((valid) => {
          if (valid) {
            if (this.title == "添加事故警示") {
              noticeAdd(this.form).then((response) => {
                if (response.data.status === 200) {
                  this.$message.success("保存成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.$message.error("保存失败");
                }
              });
            } else {
              noticeUpdate(this.form).then((response) => {
                if (response.data.status === 200) {
                  this.$message.success("修改成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.$message.error("修改失败");
                }
              });
            }
          }
        });
      }, 500);
    },

    // 取消发布的操作
    // cancelPublishFun(row) {
    //   updateStatus({
    //     warnCode: row.warnCode,
    //     earlyWarnStatus: "0",
    //   }).then((response) => {
    //     this.msgSuccess("取消成功");
    //     this.getList();
    //   });
    // },

    /** 删除按钮操作 */
    handleDelete(row) {
      const id = row.id;
      this.$confirm("确认删除该数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return noticeDelete({ id: id });
        })
        .then((res) => {
          if (res.data.status === 200) {
              if (this.safetyList.length === 1 && this.queryParams.nowPage !== 1) {
                this.queryParams.nowPage--;
              }
            this.getList();
            this.$message.success("删除成功");
          } else {
            this.$message.error("删除失败");
          }
        });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm("是否确认导出所有安全预警数据项?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(function () {
          return exportSafety(queryParams);
        })
        .then((response) => {
          this.download(response.msg);
        });
    },
  },
  mounted() {
    this.eventCodeFn();
  },
};
</script>

<style lang="scss" scoped>
.el-form-item__content > div {
  padding: 0;
}
.dialogInfo {
  height: 600px;
  padding: 30px 20px;
  overflow-y: scroll;
}
.addWarningBox {
  .topSearch {
    display: flex;
    width: 450px;
    margin: 0 auto;
  }
  .el-form {
    display: flex;
    flex-wrap: wrap;
  }
  .el-form-item {
    width: 50%;
  }
  /deep/ .el-dialog__body {
    overflow: auto;
  }
}

.specification-from-box {
  /deep/ .el-input {
    width: auto;
  }
}
.seach-part {
  display: flex;
  .l {
    margin-right: 10px;
  }
}

.btnbox {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}
/deep/ .el-form-item__content {
  line-height: 0 !important;
}
.el-form-item {
  margin-bottom: 0;
  margin-top: 20px;
}
.specification-table-head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  margin-top: 15px;
  .left {
    font-size: 18px;
    text-align: left;
    font-weight: 900;
  }
}
.pagination {
  display: flex;
  padding: 20px 0;
  justify-content: flex-end;
}
.abc {
  color: red;
  font-size: 20px;
}
</style>
