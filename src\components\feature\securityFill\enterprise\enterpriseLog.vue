<template>
  <div class="enterpriseLog">
    <div>
      <div class="header">企业现状</div>
      <div>
        <form action="" v-loading="loading">
          <!-- 企业现状开始 -->
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">生产装置套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.unitsNumber"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">运行套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.runNumber"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">停产套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.parkNumber"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">一级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.dangerLevelOne"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">二级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.dangerLevelTwe"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">三级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.dangerLevelThree"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">四级重大危险源</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.dangerLevelFour"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">试生产装置套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.dangerLevelFour"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">重点监管危险工艺</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.dangerMsds"
                  disabled
                ></el-input>
                <div class="yellow">/种</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否有承包商作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.contractor == 1 ? '是' : '否'"
                  disabled
                ></el-input>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否处于试生产期</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.trialProduction == 1 ? '是' : '否'"
                  disabled
                ></el-input>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否处于开停车状态</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="select"
                  :placeholder="entData.openParking == 1 ? '是' : '否'"
                  disabled
                />
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">是否开展中（扩）试</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="select"
                  :placeholder="entData.test == 1 ? '是' : '否'"
                  disabled
                />
              </div>
            </li>

            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">有无重大隐患</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="select"
                  :placeholder="entData.mhazards == 1 ? '有' : '无'"
                  disabled
                />
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">检维修套数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.fixNum"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">正在开停产装置数</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.onOffDevice"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <!-- 占位符开始 -->
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span><span class="title"></span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  placeholder="123"
                  disabled
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span><span class="title"></span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  placeholder="123"
                  disabled
                ></el-input>
                <div class="yellow">/种</div>
              </div>
            </li>
            <!-- 占位符结束 -->
          </ul>
          <!-- 企业现状结束 -->
          <!-- 作业风险开始 -->
          <div class="header">作业风险</div>
          <ul class="">
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">特殊动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.firesNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">一级动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.fire1Number"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">二级动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.fire2Number"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span
                ><span class="title">受限空间作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.spaceworkNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">盲板作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.blindplateNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">高处作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.highworkNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">吊装作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.liftingworkNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">临时用电作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.electricityworkNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">动土作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.soilworkNumber"
                  disabled
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span><span class="title">断路作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.roadworkNumber"
                  :disabled="true"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">检维修作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.inspectionNumber"
                  :disabled="true"
                ></el-input>
                <div class="yellow">/处</div>
              </div>
            </li>
            <li class="list back">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">二级动火作业</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.inspectionNumber"
                  :disabled="true"
                ></el-input>
                <div class="yellow">/套</div>
              </div>
            </li>
          </ul>
          <!-- 作业风险结束 -->
          <!-- 企业承诺开始 -->
          <div class="header">企业承诺</div>
          <ul>
            <li class="list textareaLi">
              <div class="titleBox textareaTitle">
                <span class="red">*</span>
                <span class="title">我司今日承诺</span>
              </div>
              <div class="textareaBox">
                <el-input
                  class="listTextarea"
                  :placeholder="entData.subject"
                  type="textarea"
                  :rows="2"
                  :autosize="{ minRows: 2 }"
                  :disabled="true"
                ></el-input>
              </div>
            </li>
          </ul>
          <ul>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">风险等级</span>
              </div>
              <div class="inputBox" v-if="entData.riskGrade == 1">
                <el-input
                  class="listInput"
                  placeholder="高风险"
                  disabled
                ></el-input>
              </div>

              <div class="inputBox" v-else-if="entData.riskGrade == 2">
                <el-input
                  class="listInput"
                  placeholder="较高风险"
                  disabled
                ></el-input>
              </div>

              <div class="inputBox" v-else-if="entData.riskGrade == 3">
                <el-input
                  class="listInput"
                  placeholder="一般风险"
                  disabled
                ></el-input>
              </div>

              <div class="inputBox" v-else>
                <el-input
                  class="listInput"
                  placeholder="低风险"
                  disabled
                ></el-input>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">承诺人</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.commitment"
                  disabled
                ></el-input>
              </div>
            </li>
            <li class="list">
              <div class="titleBox">
                <span class="red">*</span>
                <span class="title">承诺时间</span>
              </div>
              <div class="inputBox">
                <el-input
                  class="listInput"
                  :placeholder="entData.commiteDate"
                  disabled
                ></el-input>
              </div>
            </li>
          </ul>
          <!-- 企业承诺结束 -->
        </form>
        <div class="commitBox">
          <!-- <div class="commit"><i></i>保存</div>
          <div class="commit"><i></i>保存并上报</div> -->
          <div class="commit" @click="toPath('/enterprise')"><i></i>返回</div>
        </div>
        <!-- 占位符 -->
        <div class="placeholder"></div>
      </div>
    </div>
  </div>
</template>

<script>
import Bus from "../../../../utils/bus";
import { getEnterpriseDetail } from "@/api/reportedList";
export default {
    name:"enterpriseLog",
  //import引入的组件
  components: {},

  data() {
    return {
      input: "",
      unitsNumber: "",
      value: "",
      entData: {
        blindplateNumber: "",
        commiteDate: "",
        commiteDateStr: "",
        commitment: "",
        commitmentState: "",
        commitmentStateStr: "",
        companyCode: "",
        contractor: "",
        dangerLevelFour: "",
        dangerLevelOne: "",
        dangerLevelThree: "",
        dangerLevelTwe: "",
        dangerMsds: "",
        electricityworkNumber: "",
        extendstr1: "",
        fire1Number: "",
        fire2Number: "",
        firesNumber: "",
        fixNum: "",
        highworkNumber: "",
        id: "",
        inspectionNumber: "",
        levelRiskName: "",
        liftingworkNumber: "",
        mhazards: "",
        onOffDevice: "",
        openParking: "",
        parkNumber: "",
        riskGrade: "",
        roadworkNumber: "",
        runNumber: "",
        soilworkNumber: "",
        spaceworkNumber: "",
        subject: "*********",
        test: "",
        trialProduction: "",
        tryUnitsNumber: "",
        unitsNumber: ""
      },
      loading: false
    };
  },
  //方法集合
  methods: {
    toPath(path) {
      this.$router.push({ path: path });
    },
    //获取企业详情数据
    getEntdata(id) {
      let that = this;
      this.loading = true;
      getEnterpriseDetail({
        id: id
      })
        .then(data => {
          console.log(data.data.data);
          if (data.data.code == "success") {
            that.loading = false;
            that.entData = JSON.parse(JSON.stringify(data.data.data));
            console.log(that.entData);
          }
        })
        .catch(e => {
          console.log(e, "请求错误");
        });
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    //获取企业详情数据
    this.getEntdata(this.$route.params.id);
  },
  created() {}
};
</script>
<style lang="scss" scoped>
.enterpriseFill {
  margin: 15px;
  background-color: #fff;
  padding: 15px;
}
.col {
  display: flex;
  justify-content: space-between;
}
.header {
  font-size: 16px;
  line-height: 1;
  color: #656565;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 10px;
  width: 100%;
  text-align: left;
  margin-top: 20px;
  margin-bottom: 5px;
}
.titleBox {
  background-color: #f2f7f8;
  width: 158px;
  min-height: 55px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 10px;
  border-right: 1px solid #f0f0f0;
}
ul:nth-last-of-type(1) {
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #f0f0f0;
  height: 55px;
  overflow: hidden;
  padding: 0;
}
ul {
  display: flex;
  justify-content: space-between;
  border-top: 1px solid #f0f0f0;
  border-left: 1px solid #f0f0f0;
  border-right: 1px solid #f0f0f0;
  // height: 55px;
  overflow: hidden;
  padding: 0;
}
.list {
  display: flex;
  align-items: center;
  justify-content: space-between;

  width: 100%;
  .title {
    font-size: 16px;
    color: #222;
  }
}
.listInput {
  flex: 1;
  height: 36px;
}
.red {
  color: red;
  font-weight: 900;
  font-size: 16px;
  margin-right: 3px;
}
.yellow {
  background-color: #f1ac5a;
  color: #fff;
  padding: 14px 0;
  display: block;
  width: 55px;
  text-align: center;
}
.inputBox {
  padding: 0;
  display: flex;
  align-items: center;
  flex: 1;
  .listInput {
    margin-right: 10px;
    margin-left: 10px;
    height: 40px;
    flex: 1;
  }
  .select {
    margin-right: 10px;
    margin-left: 10px;
    height: 40px;
    flex: 1;
  }
}
.textareaBox {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
  .listTextarea {
    width: 95%;
  }
}
.textareaTitle {
  padding-top: 10px;
  padding-bottom: 10px;
}
.back {
  visibility: hidden;
}
.commitBox {
  width: 100%;
  height: 70px;
  justify-content: center;
  align-items: center;
  display: flex;
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  background-color: #fff;
  border-top: 1px solid #dfe0e0;
  .commit {
    color: #fff;
    background-color: #2892e2;
    border-color: #2892e2;
    text-align: center;
    display: inline-flex;
    align-items: center;
    border-radius: 4px;
    padding: 10px 20px;
    margin-left: 20px;
    font-weight: 900;
  }
}
.placeholder {
  width: 100%;
  height: 70px;
}
</style>
