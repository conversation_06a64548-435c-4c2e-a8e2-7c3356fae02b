<template>
  <div class="ThreeMeanwhile">
    <div class="header">
      <div style="display: flex; justify-content: flex-start">
        <div class="title">安全“三同时”</div>
      </div>
      <el-button
        icon="el-icon-plus"
        size="mini"
        type="success"
        @click="openDialog('新增安全“三同时”')"
        v-if="user.user_type === 'ent'"
        >新增
      </el-button>
    </div>
    <div class="table" v-loading="listLoading">
      <el-table
        :data="list"
        :header-cell-style="{
          textAlign: 'center',
          backgroundColor: 'rgb(242, 246, 255)',
        }"
        border
        style="width: 100%"
        height="50vh"
      >
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="企业名称" prop="orgCode" width="300px">
          <span>{{ orgName }}</span>
        </el-table-column>
        <el-table-column label="项目名称" prop="name"></el-table-column>
        <el-table-column
          label="项目类型"
          prop="peojectType"
          width="150px"
        ></el-table-column>
        <el-table-column
          label="材料上传种类数"
          prop="attachNum"
          width="150px"
        ></el-table-column>
        <el-table-column label="操作" width="200px">
          <template slot-scope="{ row }">
            <el-button
              type="text"
              @click="openDialog('安全“三同时”详情', row.id)"
              >详情
            </el-button>
            <el-button
              type="text"
              v-if="user.user_type === 'ent'"
              @click="openDialog('编辑安全“三同时”', row.id)"
              >编辑
            </el-button>
            <el-button
              type="text"
              v-if="user.user_type === 'ent'"
              @click="delForm(row.id)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        :current-page.sync="currentPage"
        :page-size="size"
        :total="total"
        background
        layout="total, prev, pager, next"
        @current-change="handleCurrentChange()"
      ></el-pagination>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="newAddVisible"
      append-to-body
      top="5vh"
      width="800px"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <div class="newAddVisible" v-loading="loading">
        <div class="box">
          <el-form
            label-position="right"
            label-width="200px"
            :model="form"
            ref="form"
          >
            <el-form-item
              :rules="[
                { required: true, message: '请输入项目名称', trigger: 'blur' },
              ]"
              label="项目名称："
              prop="name"
            >
              <el-input
                placeholder="请输入项目名称"
                :disabled="disabled"
                type="text"
                v-model.trim="form.name"
              ></el-input>
            </el-form-item>
            <el-form-item
              label="项目类型："
              :rules="[
                { required: true, message: '请选择项目类型', trigger: 'blur' },
              ]"
              prop="peojectType"
            >
              <el-select
                placeholder="请选择项目类型"
                :disabled="disabled"
                style="width: 100%"
                v-model="form.peojectType"
              >
                <el-option
                  v-for="item in options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="安全条件论证报告："
              prop="safetyConditionAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.safetyConditionAttachId"
                  :attachmentlist="form.safetyConditionAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item label="安全预评价报告：" prop="preEvaluationAttachId">
              <div>
                <AttachmentUpload
                  v-model="form.preEvaluationAttachId"
                  :attachmentlist="form.preEvaluationAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目安全设施设计："
              prop="facilityDesignAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.facilityDesignAttachId"
                  :attachmentlist="form.facilityDesignAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目安全验收评价报告："
              prop="evaluationAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.evaluationAttachId"
                  :attachmentlist="form.evaluationAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="安全生产条件和设施综合分析报告："
              prop="analysisAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.analysisAttachId"
                  :attachmentlist="form.analysisAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目安全设施设计审查报告："
              prop="reviewAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.reviewAttachId"
                  :attachmentlist="form.reviewAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目审批、核准或者备案的文件："
              prop="recordAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.recordAttachId"
                  :attachmentlist="form.recordAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目安全设施设计审查申请："
              prop="reviewApplicationAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.reviewApplicationAttachId"
                  :attachmentlist="form.reviewApplicationAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="设计单位的设计资质证明文件："
              prop="qualificationCertificateAttac"
            >
              <div>
                <AttachmentUpload
                  v-model="form.qualificationCertificateAttac"
                  :attachmentlist="form.qualificationCertificateAttacList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目初步设计报告及安全专篇："
              prop="safetyArticlesAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.safetyArticlesAttachId"
                  :attachmentlist="form.safetyArticlesAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="法律、行政法规、规章规定的其他文件资料："
              prop="lawAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.lawAttachId"
                  :attachmentlist="form.lawAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="安全设施竣工验收申请："
              prop="completeApplicationAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.completeApplicationAttachId"
                  :attachmentlist="form.completeApplicationAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="安全设施设计审查意见书（复印件）："
              prop="reviewOpinionAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.reviewOpinionAttachId"
                  :attachmentlist="form.reviewOpinionAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="施工单位的资质证明文件（复印件）："
              prop="constructionAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.constructionAttachId"
                  :attachmentlist="form.constructionAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="建设项目安全验收评价报告及其存在问题的整改确认材料："
              prop="rectifyConfirmAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.rectifyConfirmAttachId"
                  :attachmentlist="form.rectifyConfirmAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="安全生产管理机构设置或者安全生产管理人员配备情况："
              prop="managerAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.managerAttachId"
                  :attachmentlist="form.managerAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
            <el-form-item
              label="从业人员安全培训教育及资格情况："
              prop="educationAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.educationAttachId"
                  :attachmentlist="form.educationAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div> </el-form-item
            ><el-form-item
              label="安全设施试运行自查报告："
              prop="selfExaminationAttachId"
            >
              <div>
                <AttachmentUpload
                  v-model="form.selfExaminationAttachId"
                  :attachmentlist="form.selfExaminationAttachList"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div
        slot="footer"
        style="display: flex; justify-content: center"
        v-if="!disabled"
      >
        <el-button type="primary" @click="submit()" :loading="btnLoading"
          >提交</el-button
        >
        <el-button @click="closeDialog()">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="证书附件"
      :visible.sync="imgVisible"
      append-to-body
      top="5vh"
      width="800px"
      :close-on-click-modal="false"
    >
      <img :src="imgSrc" alt="" style="width: 100%; height: 100%" />
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { getCompanyProjectAdd } from "@/api/BasicDataManagement";
import AttachmentUpload from '@/components/common/packages/attachmentUpload'
import {
  getCompanyProjectPageList,
  getCompanyProjectId,
  getCompanyProjectDelete,
} from "../../../../api/BasicDataManagement";
export default {
  name: "ThreeMeanwhile",
  components: {
    AttachmentUpload
  },
  props: ["orgCode", "orgName"],
  data() {
    return {
      list: [],
      currentPage: 0,
      size: 10,
      total: 0,
      tableData: {},
      newAddVisible: false,
      imgVisible: false,
      disabled: true,
      title: "",
      options: [
        {
          value: "新建",
          label: "新建",
        },
        {
          value: "改建",
          label: "改建",
        },
        {
          value: "扩建",
          label: "扩建",
        },
      ],
      value: "",
      imgSrc: "",
      loading: false,
      listLoading: false,
      btnLoading: false,
      form: {
        analysisAttachId: "",
        analysisAttachList: [],
        completeApplicationAttachId: "",
        completeApplicationAttachList: [],
        constructionAttachId: "",
        constructionAttachList: [],
        educationAttachId: "",
        educationAttachList: [],
        evaluationAttachId: "",
        evaluationAttachList: [],
        facilityDesignAttachId: "",
        facilityDesignAttachList: [],
        id: "",
        lawAttachId: "",
        lawAttachList: [],
        managerAttachId: "",
        managerAttachList: [],
        name: "",
        orgCode: "",
        peojectType: "",
        peojectTypeName: "",
        preEvaluationAttachId: "",
        preEvaluationAttachList: [],
        qualificationCertificateAttac: "",
        qualificationCertificateAttacList: [],
        recordAttachId: "",
        recordAttachList: [],
        rectifyConfirmAttachId: "",
        rectifyConfirmAttachList: [],
        reviewApplicationAttachId: "",
        reviewApplicationAttachList: [],
        reviewAttachId: "",
        reviewAttachList: [],
        reviewOpinionAttachId: "",
        reviewOpinionAttachList: [],
        safetyArticlesAttachId: "",
        safetyArticlesAttachList: [],
        safetyConditionAttachId: "",
        safetyConditionAttachList: [],
        selfExaminationAttachId: "",
        selfExaminationAttachList: [],
        sysOrgCode: "",
      },
    };
  },

  mounted() {},

  methods: {
    getData(id) {
      this.listLoading = true;
      getCompanyProjectPageList({
        nowPage: this.currentPage,
        infoType: 2, //1对应安全评价，2对应三同时
        orgCode: this.orgCode,
        pageSize: 10,
      }).then((res) => {
        this.list = res.data.data.list;
        this.total = res.data.data.total;
        this.listLoading = false;
      });
    },
    handleCurrentChange() {
      this.getData();
    },
    closeDialog() {
      this.newAddVisible = false;
      this.$nextTick(() => {
        this.$refs["form"].resetFields();
        this.form = {
          analysisAttachId: "",
          analysisAttachList: [],
          completeApplicationAttachId: "",
          completeApplicationAttachList: [],
          constructionAttachId: "",
          constructionAttachList: [],
          educationAttachId: "",
          educationAttachList: [],
          evaluationAttachId: "",
          evaluationAttachList: [],
          facilityDesignAttachId: "",
          facilityDesignAttachList: [],
          id: "",
          lawAttachId: "",
          lawAttachList: [],
          managerAttachId: "",
          managerAttachList: [],
          name: "",
          orgCode: "",
          peojectType: "",
          peojectTypeName: "",
          preEvaluationAttachId: "",
          preEvaluationAttachList: [],
          qualificationCertificateAttac: "",
          qualificationCertificateAttacList: [],
          recordAttachId: "",
          recordAttachList: [],
          rectifyConfirmAttachId: "",
          rectifyConfirmAttachList: [],
          reviewApplicationAttachId: "",
          reviewApplicationAttachList: [],
          reviewAttachId: "",
          reviewAttachList: [],
          reviewOpinionAttachId: "",
          reviewOpinionAttachList: [],
          safetyArticlesAttachId: "",
          safetyArticlesAttachList: [],
          safetyConditionAttachId: "",
          safetyConditionAttachList: [],
          selfExaminationAttachId: "",
          selfExaminationAttachList: [],
          sysOrgCode: "",
        };
      });
    },
    delForm(id) {
      getCompanyProjectDelete({ id: id }).then((res) => {
        if (res.data.status == 200) {
          this.$message.success("删除成功");
          this.getData();
        } else {
          this.$message.error("删除失败");
        }
      });
    },

    submit() {
      console.log('aaaa测试')
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          this.form.orgCode = this.orgCode;
          this.form.infoType = 2; //1对应安全评价，2对应三同时
          getCompanyProjectAdd(this.form).then((res) => {
            this.btnLoading = false;
            if (res.data.status === 200) {
              this.$message.success(res.data.msg);
              this.getData();
              this.closeDialog();
            } else {
              this.$message.err(res.data.msg);
            }
          });
        } else {
          // console.log('error submit!!');
          return false;
        }
      });
    },
    openDialog(title, id) {
      this.title = title;
      switch (title) {
        case "新增安全“三同时”":
          this.disabled = false;
          break;
        case "安全“三同时”详情":
          this.disabled = true;
          this.loading = true;
          getCompanyProjectId({ id: id }).then((res) => {
            this.loading = false;
            if (res.data.status == 200) {
              this.form = res.data.data;
            }
          });
          break;
        case "编辑安全“三同时”":
          this.disabled = false;
          this.loading = true;
          getCompanyProjectId({ id: id }).then((res) => {
            this.loading = false;
            if (res.data.status == 200) {
              this.form = res.data.data;
            }
          });
          break;
        default:
          break;
      }
      this.newAddVisible = true;
    },
    openAttachment(img) {
      this.imgVisible = true;
      this.imgSrc = img;
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
    }),
  },
  watch: {},
};
</script>

<style lang="scss" scoped>
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
.ThreeMeanwhile {
  .header {
    display: flex;
    justify-content: space-between;

    .title {
      font-weight: 600;
      font-size: 18px;
      margin-right: 50px;
    }

    .legend {
      display: flex;
      align-items: center;
      margin-right: 5px;

      .red {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .point {
          background: red;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }

      .yellow {
        display: flex;
        align-items: center;

        .point {
          background: yellow;
          width: 15px;
          height: 15px;
          border-radius: 50%;
          margin-right: 5px;
        }
      }
    }
  }

  .table {
    margin-top: 15px;
  }

  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}

.newAddVisible {
  width: 100%;
  overflow-y: scroll;
  height: 60vh;

  .box {
    width: 90%;
    margin: 0 auto;
  }
}
</style>
