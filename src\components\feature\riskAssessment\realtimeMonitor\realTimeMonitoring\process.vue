<template>
  <div class="process">
    <el-dialog
      :title="`${processTitle}监测趋势图`"
      :visible.sync="processBoolean"
      width="950px"
      top="5vh"
      @open="open()"
      @close="closeBoolean()"
      :close-on-click-modal="false"
    >
      <div class="div1">
        <div class="div-heard">
            <div class="">
                <!-- <el-radio-group size="small" v-model="radio1">
                    <el-radio-button label="温度"></el-radio-button>
                    <el-radio-button label="压力"></el-radio-button>
                    <el-radio-button label="液位"></el-radio-button>
                    <el-radio-button label="磁翻板液位"></el-radio-button>
                </el-radio-group> -->
                <el-select v-model="targetCode" size="small"  placeholder="监测指标" @change='selectChang'>
                    <el-option
                        v-for="item in option1"
                        :key="item.targetCode"
                        :label="item.indexName"
                        :value="item.targetCode">
                    </el-option>
                </el-select>
            </div>
            <div class="select-data">
                <el-date-picker
                    v-model="value1"
                    size="small"
                    type="datetimerange"
                    value-format="timestamp"
                    format="yyyy-MM-dd HH:mm:ss"
                    range-separator="至"
                    start-placeholder="选择开始日期"
                    end-placeholder="选择结束日期"
                    :default-time="['00:00:00','23:59:59']"
                    unlink-panels
                    @change="searchTime">
                </el-date-picker>
            </div>
            <div class="seach-btn">
                <el-button type="primary" icon="el-icon-search" size="small" @click="search">查询</el-button>
            </div>
        </div>
        <div class="div-main">
            <div id="myCharted" :style="{width: '910px', height: '400px'}"></div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getEchartSelectData,getEchartData,getShangXia} from "@/api/entList"
import { parseTime} from '@/utils/index';
export default {
  //import引入的组件
  name: "process",
  components: {},
  props: {
    processBoolean: { type: Boolean, default: false },
  },
  data() {
    return { 
        value1:'',
        targetCode:'',
        option1:[],
        startTime:'',
        endTime:'',
        unit:'',
        yingShiData:[],
        legendData:['最高温度','一级上限','一级下限','二级上限','二级下限'],
        colorArr : ['#DB8905','#0575DB','#18D8F7','#FF0000','green','#000'],
        xAxisData :  [],
        data1 : [],
        // data2 : ['32', '40', '35', '45', '40', '32', '38', '40', '32'],
        up1 :[],
        up2 :[],
        down1 :[],
        down2 :[],
        legendDataName:'',
        processTitle: ''
    };
  },
  //方法集合
  methods: {
    closeBoolean() {
      this.$emit("close", false);
    },
    getSelectData(data){
      this.processTitle = data.equipmentName;
      this.equipCode = data.equipCode;
      getEchartSelectData({
         equipCode:this.equipCode,
      }).then((res)=>{
        if(res.data.code == 0){
            this.option1 = res.data.data;
            this.targetCode = this.option1[0].targetCode;
            this.legendData[0] = this.option1[0].indexName;
            this.legendDataName = this.option1[0].indexName;
            this.up1 = [this.option1[0].up1];
            this.up2 = [this.option1[0].up2];
            this.down1 = [this.option1[0].down1];
            this.down2 = [this.option1[0].down2];
            this.unit = this.option1[0].unit
            this.getYingShiData()
          }
      })
    },
    selectChang(params){
        this.getYingShiData()
        this.getShangXiaData(params)
    },
    getShangXiaData(params){
      getShangXia({
         targetCode:params,
      }).then((res)=>{
        if(res.data.code == 0){
            this.up1 = [res.data.data.up1];
            this.up2 = [res.data.data.up2];
            this.down1 = [res.data.data.down1];
            this.down2 = [res.data.data.down2];
            this.unit = res.data.data.unit;
            this.legendData[0] = res.data.data.indexName;
            this.legendDataName = res.data.data.indexName;
          }
      })
    },
    getYingShiData(){
      getEchartData({
        "start_absolute": this.startTime,
        "end_absolute": this.endTime, 
        "metrics": [{
            "name": "history_data",
            "tags": {
            "target_code": [this.targetCode],
            },
            "aggregators": [{
                "name": "avg",
                "sampling": {
                    "value": 30,
                    "unit": "minutes"
                }
            }]
        }]
      }
    // {'metrics': [{'aggregators': [{'name': 'avg', 'sampling': {'unit': 'minutes', 'value': 30}}], 'name': 'history_data', 'tags': {'target_code': ['420510055002Q0001QT001']}}], 'end_absolute': 1618037114000.0, 'start_absolute': 1554705914000.0}
      ).then((res)=>{
        //   console.log(res.data.queries[0].results[0].values)
          this.yingShiData = res.data.queries[0].results[0].values
        // if(res.data.code == 0){
        //     this.option1 = res.data.data;
        //     this.targetCode = this.option1[0].targetCode;
        //     this.getYingShiData()
        //   }
      })
    },
    searchTime(value){
        if(value){
            this.startTime = value[0];
            this.endTime = value[1];
        }else{
            // this.value1 = '';
            // this.startTime = '';
            // this.endTime = '';
        } 
    },
    search(){
        this.getYingShiData()
        this.getShangXiaData(this.targetCode)
    },
    getTime(){
        var timestamp = (new Date()).getTime();
        this.value1 = [timestamp-24*60*60*1000,timestamp]
        this.startTime = timestamp-24*60*60*1000;
        this.endTime = timestamp;
    },
    getYMDHMS (timestamp) {
      let time = new Date(timestamp)
      let year = time.getFullYear()
      let month = time.getMonth() + 1
      let date = time.getDate()
      let hours = time.getHours()
      let minute = time.getMinutes()
      let second = time.getSeconds()

      if (month < 10) { month = '0' + month }
      if (date < 10) { date = '0' + date }
      if (hours < 10) { hours = '0' + hours }
      if (minute < 10) { minute = '0' + minute }
      if (second < 10) { second = '0' + second }
      return year + '-' + month + '-' + date + ' ' + hours + ':' + minute + ':' + second
    },
    open() {
    //   this.$nextTick(() => {
    //     this.drawLinees()
    //   })
    },
    // searchTime(){},
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {
    yingShiData(newValue, oldValue) {
       let myChart = this.$echarts.init(document.getElementById('myCharted'));
       var option={
            backgroundColor:'#fff',
            tooltip: {
                trigger: 'axis',
    //             formatter(params){
    //                 // debugger;
    //        for(let i = 0; i < params.length; i++){
    //            return params[i].name +":"+params[i].data.value+"/"+params[i].data.date;
    //        }
            
    //    }
            },
            title: {
                text: '',
                textStyle: {
                    fontSize: 22,
                    fontWeight: 400,
                    color:'#ACCFFF'
                },
                left: 'center',
                top: '0'
            },
            legend: {
                top: '-10%',
                right:'0',
                itemGap:50,
                textStyle: {
                    color: '#000',
                    // color:this.colorArr,
                    fontSize: 14
                },
                data: this.legendData
            },
            color:this.colorArr,
            grid: {
                left: '2%',
                right: '5%',
                top: '15%',
                bottom: '13%',
                containLabel: true
            },
            dataZoom: [
                {
                    type: 'slider',//数据滑块
                    start:0,
                    minSpan:8,    //5min
                    // minSpan:16,   //10min
                    // minSpan:24,   //15min
                    // minSpan:50,   //30min
                    dataBackground:{
                        lineStyle:{
                            // color:'#95BC2F'
                        },
                        areaStyle:{
                            // color:'#95BC2F',
                            opacity:1,
                        }
                    },
                    // fillerColor:'rgba(255,255,255,.6)'
                },
                {
                    type:'inside'//使鼠标在图表中时滚轮可用
                }
            ],
            xAxis: [{
                show: true,
                type: 'category',
                splitLine: {
                    show: false
                },
                axisLabel: {
                    color: '#999',
                },
                data:this.xAxisData,
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#545C65',
                },
                axisLine: {
                    lineStyle: {
                        color: '#344B83',
                    }
                },
                boundaryGap: true,
            }],
            yAxis: [{
                    name:this.unit,
                    nameTextStyle:{
                        color: '#545C65', 
                        fontSize: 14
                    },
                    type: 'value',
                    splitLine: {
                        show: true
                    },
                    axisLabel: {
                        color: '#545C65',
                    },
                    axisLine: {
                        lineStyle: {
                            color: '#344B83',
                        }
                    },
                    axisTick: {
                        show: false
                    },
                    splitLine: {
                        show: true,
                        lineStyle: {
                            color: '#344B83',
                        }
                    }
                }
            ],
            series: [
                {
                    name: '最高温度',
                    type: 'line',
                    symbolSize: 0,
                    smooth: true,
                    data: this.data1,
                },
                // {
                //     name: '最低温度',
                //     type: 'line',
                //     symbolSize: 0,
                //     smooth: true,
                //     data: this.data2,
                // },
                {
                    name: '一级上限',
                    type: 'line',
                    symbolSize:0,
                    smooth: true,
                    data: this.up1,
                    markLine: {
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#0575DB'
                                },
                            }
                        },
                        data: [{
                            type: 'average',
                            name: '一级上限',
                            label: {
                                "position": "insideEndTop",
                                "formatter": "{b} {c}"
                            }
                        }]
                    }
                },{
                    name: '一级下限',
                    type: 'line',
                    data: this.down1,
                    symbolSize:0,
                    smooth: true,
                    markLine: {
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#18D8F7'
                                },
                            }
                        },
                        data: [{
                            type: 'average',
                            name: '一级下限',
                            label: {
                                "position": "insideEndTop",
                                "formatter": "{b} {c}"
                            }
                        }]
                    }
                },{
                    name: '二级上限',
                    type: 'line',
                    symbolSize:0,
                    data: this.up2,
                    smooth: true,
                    markLine: {
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: '#FF0000'
                                },
                            }
                        },
                        data: [{
                            type: 'average',
                            name: '二级上限',
                            label: {
                                "position": "insideEndTop",
                                "formatter": "{b} {c}"
                            }
                        }]
                    }
                },{
                    name: '二级下限',
                    type: 'line',
                    data: this.down2,
                    symbolSize:0,
                    smooth: true,
                    markLine: {
                        itemStyle: {
                            normal: {
                                lineStyle: {
                                    color: 'green'
                                },
                            }
                        },
                        data: [{
                            type: 'average',
                            name: '二级下限',
                            label: {
                                "position": "insideEndTop",
                                "formatter": "{b} {c}"
                            }
                        }]
                    }
                },
            ]
       }
        this.xAxisData =  [];
        this.data1 = [];
        // this.data2 = [],
       for (let i = 0; i < this.yingShiData.length; i++) {
        //   this.xAxisData.push(new Date(this.yingShiData[i][0]).Format("yy-MM-dd hh:mm:ss"))
          this.xAxisData.push(this.getYMDHMS(this.yingShiData[i][0]))
          this.data1.push(this.yingShiData[i][1])
       }
       option.series[0].data = this.data1;
       option.series[0].name = this.legendDataName;
       option.xAxis[0].data = this.xAxisData;
       myChart.setOption(option)
    }
  }
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  font-size: 12px;
}
.process {
  .div1{
      .div-heard{
          >div{
              display: inline-block;
          }
      }
  }
}
</style>