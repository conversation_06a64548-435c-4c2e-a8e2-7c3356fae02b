<template>
  <div>
    <div class="search-box" v-if="status===1">
        <div class="select-data">
            <el-date-picker
                v-model="value1"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="选择更新开始日期"
                end-placeholder="选择更新结束日期"
                unlink-panels
                @change="searchTime">
            </el-date-picker>
        </div>
        <!-- <div class="select-time">
            <el-select v-model="onTime" clearable placeholder="请选择" @clear="clearSh" >
                <el-option
                    v-for="item in options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
            </el-select>
        </div> -->
        <div class="seach-btn">
            <el-button type="primary" icon="el-icon-search" @click="search">查询</el-button>
        </div>
    </div>
    <div class="table-box" v-if="status===1">
        <div class="tab-header">
            <el-row>
                <h2>安全承诺</h2>
                <div class="btn-group">
                    <el-button type="primary" icon="el-icon-download">导出</el-button>
                </div>
            </el-row>
        </div>
        <el-table
            :row-key="getRowKeys"
            v-loading="loading"
            style="width: 100%;min-height:350px"
            :default-sort = "{prop: 'date', order: 'descending'}"
            ref="multipleTable" 
            :data="tableData" 
            @selection-change="handleSelectionChange"
        >
            <el-table-column
                type="selection"
                :reserve-selection="true"
                width="50">
            </el-table-column>
            <el-table-column
                label="日期"
            >
                <template slot-scope="{row, column, $index, store}">
                    <span class="linkBtn" @click="entList(row.commiteDate)">{{row.commiteDate}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="一级危险源企业数/未上报数">
                <template slot-scope="{row, column, $index, store}">
                    <span>{{row.levelOne}}</span><span class="oneColor">{{row.levelOneNoC}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="二级危险源企业数/未上报数">
                <template slot-scope="{row, column, $index, store}">
                    <span>{{row.levelTwo}}</span><span class="twoColor">{{row.levelTwoNoC}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="三级危险源企业数/未上报数">
                <template slot-scope="{row, column, $index, store}">
                    <span>{{row.levelThree}}</span><span class="threeColor">{{row.levelThreeNoC}}</span>
                </template>
            </el-table-column>
            <el-table-column
                label="四级危险源企业数/未上报数">
                <template slot-scope="{row, column, $index, store}">
                    <span>{{row.levelFour}}</span><span class="fourColor">{{row.levelFourNoC}}</span>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page.pageNo"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="page.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="page.total"
            v-if="page.total != 0">
        </el-pagination>
    </div>
    <!-- 已上报企业 -->
    <NotReportedEnterprise v-if="status===0" :commiteDate="commiteDate"></NotReportedEnterprise>
  </div>
</template>

<script>
import NotReportedEnterprise from './notReportedEnterprise';
import {getSecurityCommitmentList} from "@/api/reportedList";
import { parseTime} from '@/utils/index';
export default {
    name: 'notReport',
    components: {
        NotReportedEnterprise
    },
    data() {
      return {
        tableData:[],
        // options: [{
        //   value: '1',
        //   label: '准时上报'
        // }, {
        //   value: '2',
        //   label: '未准时上报'
        // }],
        loading: false,
        value1: '',
        starTime:'',
        endTime:'',
        isSources:true,
        page: {
            pageNo:1,
            pageSize:10,
            total: 100
        },
        status:1,
        commiteDate:''
      }
    },
    created() {
        if (this.$route.name == "notReportedEnterprise") {
            this.status = 0;
        } else {
            this.status = 1;
        }  
    },
    watch: {
        $route(newVal, oldVal) {
            if (newVal.name == "notReportedEnterprise") {
                this.status = 0;
            } else {
                this.status = 1;
            }
        },
    },
    mounted() {
        //获取安全承诺列表列表
        this.getCommitment();
    },
    methods: {
      // 获取安全承诺重大危险源列表
      getCommitment(){
        this.loading = true;
        getSecurityCommitmentList({
            pageNo: this.page.pageNo,
            pageSize: this.page.pageSize,
            starTime: this.starTime,
            endTime: this.endTime
        })
        .then(data => {
            if(data.data.code=="success"){
                this.loading = false;
                let listData = data.data.data.rows;
                this.page.total = data.data.data.total;
                this.tableData = listData;
            }
        })
        .catch(e => {
          console.log(e, "请求错误");
        });
      },
      search(){
        this.page.pageNo = 1
        this.page.pageSize = 10
        this.getCommitment();
      },
      searchTime(value){
        if(value){
            let date1 = new Date(value[0]);
            let dataTime1 = parseTime(date1, '{y}-{m}-{d}');
            let date2 = new Date(value[1]);
            let dataTime2 = parseTime(date2, '{y}-{m}-{d}')
            // this.value1 = dataTime1 +','+dataTime2
            this.starTime = dataTime1;
            this.endTime = dataTime2;
        }else{
            this.value1 = '';
            this.starTime = '';
            this.endTime = '';
        } 
      },
      /**
      * 切换分页尺寸
      * @param val
      */
      handleSizeChange(val) {
        this.page.pageSize = val;
        this.getCommitment();
        this.$refs.multipleTable.clearSelection();
      },
      /**
      * 切换当前页
      * @param val
      */
      handleCurrentChange(val) {
        this.page.pageNo = val;
        this.getCommitment();
        this.$refs.multipleTable.clearSelection();
      },
      getRowKeys(row) {
        return row.id
      },
      handleSelectionChange(val) {
        let vlength = val.length
        this.multipleSelection = val
        this.piLiangIds = [];
        this.multipleSelection.forEach(item => {
            this.piLiangIds.push(item.id)
        });
        this.checkAll = vlength === this.page.size
        this.isIndeterminate = vlength > 0 && vlength < this.page.size
     },
     entList(val){
         this.$router.push({ path: `/Home/notReportedEnterprise/${val}`});
         this.commiteDate = val
     }
    }
};
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.search-box{
    padding-bottom:10px;
    background: #fff;
    /* padding-top: 20px; */
    margin-bottom: 20px;
        padding: 0 20px 20px 20px;
}
.select-data{
   display: inline-block;
   margin-right: 15px;
}
.select-time{
    display: inline-block;
}
.seach-btn{
    display: inline-block;
    float: right;
}
.tab-header{
    padding: 15px 19px 15px 30px;
    border-bottom: 1px solid #f0f0f0;
    background: #fff;
}
.tab-header h2{
    display: inline-block;
    margin-top: 10px;
    font-size: 18px;
    color: #000;
}
.btn-group{
    float: right;
    display: inline-block;
    vertical-align: middle;
}
.table-box{
    /* padding: 0 20px 20px 20px; */
}
.oneColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: orangered;
    color: white;
    border-radius: 4em;
}
.twoColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: orange;
    color: white;
    border-radius: 4em;
}
.threeColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: yellowgreen;
    color: white;
    border-radius: 4em;
}
.fourColor{
    display: inline-block;
    margin-left: 30px;
    width: 50px;
    background-color: blue;
    color: white;
    border-radius: 4em;
}
.linkBtn{
    color: #2892e2;
    text-decoration: underline;
    cursor: pointer;
}
</style>

<style scoped>
.el-tabs__nav-wrap::after{
    height: 0px!important;
}

.el-tabs__nav-wrap {
    padding: 20px;
    background: #fff;
}
.el-tabs__header {
    margin: 0;
}

.el-table th > .cell {
    text-align: center;
}
 
.el-table .cell {
    text-align: center;
}  
.el-pagination {
    background: #fff;
    text-align: right;
    padding: 20px;
} 
.el-pagination .el-select .el-input ,.el-pagination__sizes{
    display: none!important;
}

</style>
