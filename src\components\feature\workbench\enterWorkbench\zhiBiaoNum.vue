<template>
  <div class="historyList">
    <el-dialog
      v-dialog-drag
      :title="titleName"
      :visible.sync="showeds"
      @close="closeBoolean(false)"
      width="1350px"
      top="5vh"
      :close-on-click-modal="false"
    >
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
      >
        <el-table-column type="index" label="序号" width="55" align="center"> </el-table-column>
        <el-table-column
          prop="dangerName"
          label="重大危险源名称"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          align="center"
        >
        </el-table-column>
        <el-table-column
          prop="targetName"
          label="指标名称"
          align="center"
        >
        </el-table-column>
        <el-table-column label="运行状态" align="center" >
          <template slot-scope="scope">
            <span>{{scope.row.state =='1' ? '离线' : '在线'}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="updateTime"
          label="数据最近上传时间"
          align="center"
        >
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getSelectCompanyTargetInfo } from "@/api/workbench";
import { parseTime} from '@/utils/index';
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      showeds: false,
      type: "",
      currentPage: 1,
      loading: true,
      tableData: [],
      total: 0,
      titleName: "",
      enterpId:'',
      type:'',
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.currentPage = 1;
      this.showeds = val;
    },
    getData(enterpId, type) {
      this.enterpId = enterpId;
      this.loading = true;
      this.type = type;
      if (type == '') {
        this.titleName ="接入指标数列表";
      } else if (type == '1') {
        this.titleName ="离线指标数列表";
      }
      getSelectCompanyTargetInfo({
         companyCode:enterpId,
         current:this.currentPage,
         onlineStatus:type,
         startDate:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime()), '{y}-{m}-{d} {h}:{i}:{s}'),
         endDate:parseTime(new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 60 * 1000 - 1), '{y}-{m}-{d} {h}:{i}:{s}')
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        }
      });
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
    showDialog() {
      this.$refs.History.closeBoolean(true);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getData(this.enterpId, this.type);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.historyList {
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 700px;
      display: flex;
      justify-content: space-between;
      > div{
        margin-right: 15px;
        width: 200px;
      }
      >.btn{
        margin-right: 15px;
      }

    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>