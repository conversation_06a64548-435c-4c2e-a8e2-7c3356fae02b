<template>
  <div class="videoAnalysis">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              用户操作日志
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div>
      <!-- {{districtCode}} -->
      <!-- v-if="isShowDist && showArea" -->
      <!-- {{$store.state.login.user.user_type +'------------------ '}}   -->

      <div class="seach-part">
        <div class="l">
          <el-input
            v-model="creator"
            size="mini"
            placeholder="请输入用户名"
            clearable
            style="width: 280px"
          >
          </el-input>

          <el-date-picker
            v-model="dateTime"
            size="mini"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"           
            @change="searchTime"
            unlink-panels
            clearable
           
            style="width: 380px"
            value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >

           <!-- value-format="yyyy-MM-dd HH:mm:ss" -->
          <!-- <el-button type="" size="mini" @click="exportExcel()">导出</el-button> -->
        </div>
      </div>

      <div class="table-top">
        <h2>用户操作日志列表</h2>
      </div>

      <div class="table-main">
        <div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                label="用户名"
                width="180"
                align="center"
                prop="creator"
              >
              </el-table-column>
              <el-table-column
                label="操作时间"
                width="180"
                align="center"
                prop="logTime"
              >
              </el-table-column>

              <el-table-column
                label="日志内容"
                align="center"
                prop="logContent"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                label="模块"
                width="200"
                align="center"
                prop="logTheme"
              >
              </el-table-column>
              <el-table-column
                label="操作内容"
                align="center"
                width="200"
                prop="operateContent"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                label="结果"
                width="200"
                align="center"
                prop="operateResult"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.pageSize"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- <alarmDeatil :detailAlarmDialog='detailAlarmDialog' @closeCenterDialog='closeCenterDialog'  ref="alarmDeatil"></alarmDeatil> -->
  </div>
</template>
<script>
import { getLogList,getLogListUser } from "@/api/user";
var dayjs = require("dayjs");


import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");

export default {
  components: {},
  data() {
    return {
      districtCode: this.$store.state.login.userDistCode, //行政区划代
      endTime: "",
      startTime: "",
      dateTime: [], //查询时间
      creator: "",
      total: "",
      loading: false,
      tableData: [],
      district: this.$store.state.controler.district,
      currentPage: 1,
      selection: [],
    };
  },

  // filters: {
  //   filterFeedbackTime(val) {
  //     // debugger
  //     return val ? dayjs(val).format("YYYY-MM-DD HH:ss") : "";
  //   },
  //   filterAlarmTime(val) {
  //     return val ? dayjs(val).format("YYYY-MM-DD HH:ss") : "";
  //   },
  // },

  methods: {   
    searchTime(value) {
      // debugger
      // if(!value){
      //   this.dateTime=[];
      //   // this.getData()
      // }
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    //查询列表
    getData() {
     this.loading = true;
      var sTime="";
      var eTime=""
      if(this.dateTime){
        sTime=this.dateTime.length > 0 ? this.dateTime[0] : "";
        eTime=this.dateTime.length > 0 ? this.dateTime[1] : ""
      }else{
        sTime=''
        eTime=''
      }
      const dto = {
        startTime: sTime,
        endTime: eTime,
        creator: this.creator,
        orgCode: "",
        nowPage: this.currentPage,
        pageSize: 10,
      };
      getLogListUser(dto).then((res) => {
        //getCompanyParticularJobList   //getCompanyProjectList
        this.tableData = res.data.data;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
    // 导出
    exportExcel() {
      videoAlarmExport({
        alarmId: "",
        alarmType: this.alarmId, //告警类型
        districtCode: this.districtCode,
        startTime: this.dateTime.length > 0 ? this.dateTime[0] : "",
        endTime: this.dateTime.length > 0 ? this.dateTime[1] : "",
        enterpId: this.queryParams.orgCode, //企业id
        feedbackStatus: this.feedbackStatus, //反馈状态
        pageSize: 0,
        nowPage: 0,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "日志管理" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },

    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleCurrentChange(val) {
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.videoAnalysis {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>

