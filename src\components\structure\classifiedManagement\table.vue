<template>
  <div class="table">
    <el-dialog
      title="修改密级"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="600px"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableData"
        ref="tableData"
        v-loading="loading"
        label-width="150px"
      >
        <div class="inputBox">
          <el-form-item label="密级名称" prop="securityName" class="lable">
            <el-input
              v-model.trim="tableData.securityName"
              placeholder="密级名称"
              label="密级名称"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <div class="inputBox">
          <el-form-item label="密级级别" prop="leve" class="lable">
            <el-input
              v-model.trim="tableData.leve"
              placeholder="密级级别"
              label="密级级别"
              class="input"
            ></el-input>
          </el-form-item>
        </div>
        <el-button
          size="default"
          type="primary"
          class="commit"
          @click="handleSaveTable"
          >保存</el-button
        >
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { saveSecurity } from "../../../api/user";
import { pwdRules } from "../../../api/rules";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loading: false,
      tableData: {},
      checkedKeys: [],
      rules: pwdRules,
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
    },
    clearTable() {
      this.tableData = {};
    },
    handleSaveTable() {
      // this.loading = true;
      this.$refs["tableData"].validate((valid) => {
        if (valid) {
          saveSecurity({ ...this.tableData })
            .then((res) => {
              // this.loading = false;
              this.tableVisible = false;
              this.$message({
                message: res.data.msg,
                type: "success",
              });
              this.$parent.fatherMethod();
            })
            .catch((e) => {
              this.loading = false;
              console.log(e, "请求错误");
            });
        } else {
          this.$message.error("请检查必填项");
          return false;
        }
      });
    },
    getData(row) {
      this.tableData = row;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>
