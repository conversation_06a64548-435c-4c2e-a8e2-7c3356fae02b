<template>
  <a-config-provider :locale="zh_CN">
    <div id="app">
      <router-view></router-view>
    </div>
  </a-config-provider>
</template>

<script>
import zh_CN from "ant-design-vue/lib/locale-provider/zh_CN";
import moment from "moment";
import "moment/locale/zh-cn";

moment.locale("zh-cn");
export default {
  name: "App",
  data() {
    return {
      zh_CN,
    };
  },
  mounted() {
  },
};
</script>

<style lang="scss">
@import url("./assets/icon/quanhui/style.css");
@import url("./assets/icon/qh/style.css");

* {
  margin: 0 0;
  padding: 0;
}
#app {
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* text-align: center; */
  /* color: #2c3e50; */
  background-color: #ffffff;
  font-family: "Avenir", Helvetica, Arial, sans-serif;
}
body {
  width: 100%;
  // line-height: 1.15 !important;
  scrollbar-base-color: transparent;
  scrollbar-darkshadow-color: transparent;
  scrollbar-face-color: transparent;
  scrollbar-highlight-color: transparent;
}
.el-textarea .el-input__count {
  background: transparent !important;
  font-size: 12px;
  bottom: 0 !important;
  height: 18px !important;
  line-height: 18px !important;
}
.el-textarea__inner {
  padding-top: 10px !important;
  padding-bottom: 10px !important;
}
/*全局滚动条样式*/
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background: #f0f0f0;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: rgba(120, 139, 199, 0.5);
}
body .el-dialog__body {
  // padding: 10px 20px 30px 20px;
}
body .el-dialog__header {
  background-color: #3977ea;
  padding-bottom: 14px;
  padding-top: 17px;
}
body .el-dialog__title {
  color: #ffffff;
}
body .el-dialog__headerbtn .el-dialog__close {
  color: #ffffff;
  font-size: 22px;
}
.el-popover {
  min-width: 0 !important;
}
//全屏按钮
// video::-webkit-media-controls-fullscreen-button {
//     display: none;
// }
.animated-0 {
  display: none !important;
}
video:-internal-media-controls-overflow-menu-list-item {
  display: none;
}
video:-internal-media-controls-overflow-button {
  display: none;
}
video::-webkit-internal-media-controls-overflow-menu-list-item {
  display: none;
}
// //播放按钮
video::-webkit-media-controls-play-button {
  display: none;
}
//进度条
video::-webkit-media-controls-timeline {
  display: none;
}
//观看的当前时间
video::-webkit-media-controls-current-time-display {
  display: none;
}
//剩余时间
video::-webkit-media-controls-time-remaining-display {
  display: none;
}
//音量按钮
video::-webkit-media-controls-mute-button {
  display: none;
}
video::-webkit-media-controls-toggle-closed-captions-button {
  display: none;
}
//音量的控制条
video::-webkit-media-controls-volume-slider {
  display: none;
}
.ant-menu-submenu-popup {
  z-index: 30000;
}
.ant-menu-item {
  text-align: center;
}
.el-dialog__wrapper {
  // overflow: hidden;
  overflow-x: auto;
  overflow-y: hidden;
}
.v-modal {
  display: none !important;
}
.ant-menu-vertical .ant-menu-item,
.ant-menu-vertical-left .ant-menu-item,
.ant-menu-vertical-right .ant-menu-item,
.ant-menu-inline .ant-menu-item,
.ant-menu-vertical .ant-menu-submenu-title,
.ant-menu-vertical-left .ant-menu-submenu-title,
.ant-menu-vertical-right .ant-menu-submenu-title,
.ant-menu-inline .ant-menu-submenu-title {
  margin-top: 0px;
}
#equipmentAndFacilitiesBox .el-table .el-table__cell {
  padding: 8px 0 !important;
}
.sign-dialog {
  .el-dialog__header {
    background-color: unset !important;
    .el-dialog__close {
      color: #999 !important;
    }
    .sign-header {
      .tips {
        color: #c7c7c7;
        font-size: 0.8em;
        margin-left: 0.5em;
      }
    }
  }
  .el-dialog__body {
    padding: 0.5em 1em !important;
    padding-bottom: 1.5em !important;
  }
}
// .el-cascader-panel .el-radio{
//   display:none!important;
// }

// 表单设计器高度
.form-render-wrapper {
  overflow: auto !important;
  height: 600px !important;
}
</style>
