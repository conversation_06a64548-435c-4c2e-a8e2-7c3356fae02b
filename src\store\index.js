/* global Vue: true,  Vuex : true */
import login from "./modules/login";
import controler from "./modules/controler";
import sa from "./modules/sa"
import portrait from "./modules/portrait";
import VueXAlong from "vuex-along";
import Vuex from "vuex";
import Vue from "vue";

Vue.use(Vuex);

export default new Vuex.Store({
  modules: {
    login,
    controler,
    sa,
    portrait
  },
  /*
    @name 存放在localStroage或者sessionStroage 中的名字
    @local 是否存放在local中  false 不存放 如果存放按照下面session的配置配
    @session 如果值不为false 那么可以传递对象 其中 当isFilter设置为true时， list 数组中的值就会被过滤调,这些值不会存放在seesion或者local中
   */
  plugins: [
    VueXAlong({
      name: "VueX_local",
      local: false,
      session: { list: ["controler"], isFilter: true },
      justSession: true
    })
  ],
  // strict: "debug"
});
