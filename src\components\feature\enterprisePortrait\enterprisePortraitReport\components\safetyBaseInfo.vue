<template>
  <div class="safe-baseinfo">
    <div class="table">
      <ul class="container">
        <li>
          <div class="l">企业主要负责人</div>
          <div class="r">{{ safeyInfo.managePerson }}</div>
        </li>
        <li class="">
          <div class="l">联系方式</div>
          <div class="r">{{ safeyInfo.managePersonTel }}</div>
        </li>
        <li class="">
          <div class="l">安全生产资格证书</div>
          <div class="r">{{ safeyInfo.managePersonCert }}</div>
        </li>
        <li>
          <div class="l">企业安全负责人</div>
          <div class="r">{{ safeyInfo.safetyPerson }}</div>
        </li>
        <li>
          <div class="l">联系方式</div>
          <div class="r">{{ safeyInfo.safetyPersonTel }}</div>
        </li>
        <li>
          <div class="l">安全生产资格证书</div>
          <div class="r">{{ safeyInfo.safetyPersonCert }}</div>
        </li>
        <li>
          <div class="l">从业人员</div>
          <div class="r">
            {{ safeyInfo.staffNum }}人（外用工{{
              safeyInfo.topicalStaffNum
            }}人）
          </div>
        </li>
        <li>
          <div class="l">从业人员本科占比</div>
          <div class="r">{{ safeyInfo.bkzb }}%</div>
        </li>
        <li>
          <div class="l">上一年度人员流动率</div>
          <div class="r">{{ safeyInfo.lastYearStuffTurnover }}%</div>
        </li>
        <li>
          <div class="l">安全管理部门名称</div>
          <div class="r">{{ safeyInfo.safetyDept }}</div>
        </li>
        <li>
          <div class="l">安全总监</div>
          <div class="r">{{ safeyInfo.aqzj === "1" ? "有" : "无" }}</div>
        </li>
        <li>
          <div class="l">注册安全工程师人数</div>
          <div class="r">{{ safeyInfo.safetyDeptNum }}人</div>
        </li>
        <li>
          <div class="l">工伤保险投保人数</div>
          <div class="r">{{ safeyInfo.insureNum }}人</div>
        </li>
        <li>
          <div class="l">工伤保险投保总金额</div>
          <div class="r">{{ safeyInfo.employmentInjuryInsurance }}(万元)</div>
        </li>
        <li>
          <div class="l">是否属于“厂中厂”</div>
          <div class="r">
            {{ safeyInfo.factoryInFactory === "1" ? "是" : "否" }}
          </div>
        </li>
        <li>
          <div class="l">安全管理人数</div>
          <div class="r">{{ safeyInfo.safetyNum }}人</div>
        </li>
        <li class="lang">
          <div class="l">安全管理人员数</div>
          <div class="r">
            总数{{ safeyInfo.safetyNum }}人，专职人员{{
              safeyInfo.fulltimeSafetyNum
            }}人，持证人员{{
              safeyInfo.fulltimeSafetyCertificateNum
            }}人，兼职人员{{ safeyInfo.parttimeSafetyNum }}人，持证人员{{
              safeyInfo.parttimeSafetyCertificateNum
            }}人
          </div>
        </li>

        <li class="bottom">
          <div class="l">特种作业人员持证情况</div>
          <div class="r">{{ safeyInfo.specialWorkCertificateNum }}本</div>
        </li>
        <li class="bottom">
          <div class="l">是否开展安全生产标准化建设</div>
          <div class="r">
            {{ safeyInfo.indusafetyBuild != "5" ? "有" : "无" }}
            {{
              safeyInfo.safetyBuild == "1"
                ? "一级"
                : safeyInfo.safetyBuild == "2"
                ? "二级"
                : safeyInfo.safetyBuild == "3"
                ? "三级"
                : safeyInfo.safetyBuild == "4"
                ? "小微"
                : safeyInfo.safetyBuild == "0"
                ? "未达标等级"
                : ""
            }}
          </div>
        </li>
        <li class="bottom">
          <div class="l">是否投保安全生产责任保险</div>
          <div class="r">
            {{ safeyInfo.ifInsure === "1" ? "有" : "无"
            }}{{ safeyInfo.insureMoney ? safeyInfo.insureMoney + "万" : "" }}
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { reportDetail, changePreview } from "../mock";
export default {
  name: "safeProduction",
  props: {
    previewInfo: {
      type: Object,
    },
  },
  data() {
    return {
      safeyInfo: reportDetail.data.safetyManageDTO,
    };
  },
};
</script>

<style lang="scss" scoped>
.safe-baseinfo {
  width: 100%;
  height: auto;
}
.table {
  width: 100%;
  .container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    .bottom {
      border-bottom: 1px solid rgb(231, 231, 231);
    }

    li {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      // border-right: 1px solid rgb(231, 231, 231);
      border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 48%;
        padding: 1%;
        word-break: break-all;
      }
    }
    li:nth-of-type(3n + 0) {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .lang {
      list-style-type: none;
      width: 66.6%;
      display: flex;
      border-top: 1px solid #eaedf2;
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 24.9%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 73.3%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        padding: 0px 10px;
        word-break: break-all;
      }
    }
    .liLine {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .bottom:nth-of-type(3n + 0) {
      border-right: 1px solid rgb(231, 231, 231);
    }
  }
}
</style>
