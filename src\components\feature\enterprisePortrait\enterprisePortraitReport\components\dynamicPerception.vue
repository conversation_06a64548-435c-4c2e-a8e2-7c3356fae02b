<template>
  <div class="dynamic-page">
    <div class="title">2025年2月1日-1月28日期间
        发生4起物联设备报警 其中重复报警的点位2个，平均销警时长0.01小时，
        当前企业的点位平均报警次数为0.07次，最大报警时长0.01,
        销警及时率100%</div>
    <el-table :data="danger" border :header-cell-style="headerCellStyle">
      <el-table-column label="序号" type="index" width="60"></el-table-column>
      <el-table-column
        label="重大危险源名称"
        prop="dangerName"
      ></el-table-column>
      <el-table-column label="设备类型" prop="dangerName"></el-table-column>
      <el-table-column label="设备名称" prop="dangerName"></el-table-column>
      <el-table-column label="指标类型" prop="rvalue"></el-table-column>
      <el-table-column label="指标名称" prop="rvalue"></el-table-column>
      <el-table-column label="报警值" prop="rvalue"></el-table-column>
      <el-table-column label="报警类型" prop="rvalue"></el-table-column>
      <el-table-column
        label="报警时间"
        width="160"
        prop="useTime"
      ></el-table-column>
      <el-table-column
        label="消警时间"
        width="160"
        prop="rvalue"
      ></el-table-column>
      <el-table-column label="报警时长" prop="rvalue"></el-table-column>
      <el-table-column
        label="当前状态"
        prop="rvalue"
        width="120"
      ></el-table-column>
    </el-table>
    <div class="operation-box">
      <el-date-picker
        v-model="dateRange"
        size="small"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="searchTime"
        clearable
      >
      </el-date-picker>
      <el-button type="primary" size="small" @click="getTableData"
        >查询</el-button
      >
      <el-button type="primary" size="small" @click="getTableData"
        >导出</el-button
      >
    </div>
    <el-table :data="hazarchem" border :header-cell-style="headerCellStyle">
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column
        label="重大危险源名称"
        prop="hazarchemName"
      ></el-table-column>
      <el-table-column label="报警次数" prop="chemicalAlias"></el-table-column>
      <el-table-column label="重复报警点检数" prop="casNo"></el-table-column>
      <el-table-column
        label="重复报警次数"
        prop="hazarchemProperty"
      ></el-table-column>
      <el-table-column label="平均报警时长" prop="storageNum"></el-table-column>
      <el-table-column
        label="点位平均报警次数"
        prop="annualUsageMeal"
      ></el-table-column>
      <el-table-column
        label="最大报警时长"
        prop="annualUsageMeal"
      ></el-table-column>
      <el-table-column
        label="平均报警率"
        prop="annualUsageMeal"
      ></el-table-column>
      <el-table-column
        label="消警处置率"
        prop="annualUsageMeal"
      ></el-table-column>
    </el-table>
  </div>
</template>
<script>
export default {
  name: "dynamicPerception",
  data() {
    return {
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      dateRange: null,
      hazarchem: [],
      danger: [],
    };
  },
  methods: {
    searchTime(value) {},
    getTableData() {},
  },
  mounted() {},
};
</script>
<style lang="scss" scoped>
.dynamic-page {
  height: 460px;
  .title{
    text-indent:2em;
    font-size: 14px;
    font-weight: 600;
  }
  .operation-box {
    margin: 10px 0;
  }
}
</style>
