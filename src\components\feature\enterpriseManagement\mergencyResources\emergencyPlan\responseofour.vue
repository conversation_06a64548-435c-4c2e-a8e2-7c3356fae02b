<template>
  <div class="flex-full planTable">
    <div class="flex-header">
      <el-radio-group v-model="checkPage">
        <el-radio-button label="1">基本信息</el-radio-button>
        <el-radio-button label="2" :disabled="isAdd">总指挥部</el-radio-button>
        <el-radio-button label="3" :disabled="isAdd"
          >现场指挥部</el-radio-button
        >
      </el-radio-group>
      <div style="float: right">
        <!-- <el-button icon="el-icon-arrow-left" @click.native="goback">返回</el-button> -->
        <el-button
          type="primary"
          icon="el-icon-check"
          @click.native="submit(checkPage)"
          v-show="isShow"
          >保存</el-button
        >
      </div>
    </div>
    <div class="flex-full" style="max-height: 60vh; overflow: auto">
      <div class="flex-full list-contain" v-if="checkPage == '1'">
        <!-- <iams-form ref="simpleTable" v-model="formdata" :mapinfo="formdata.mapInfo" :gisshow="true">
        </iams-form> -->
        <el-form
          :model="formdata"
          :rules="rules"
          ref="formdata"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="启动条件" prop="startCondition">
            <el-input v-model.trim="formdata.startCondition"></el-input>
          </el-form-item>
          <el-form-item label="响应行动" prop="responseAction">
            <el-input
              v-model.trim="formdata.responseAction"
              type="textarea"
              placeholder="最多可输入500字"
              maxlength="500"
              show-word-limit
              :autosize="{ minRows: 3, maxRows: 6 }"
            ></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div class="flex-full" v-if="checkPage == '2'">
        <el-scrollbar>
          <el-row class="command_centre">指挥中心</el-row>
          <el-row class="command_content">
            <!-- 指挥长 -->
            <el-row class="command_first">
              <el-col :span="3" class="command_title">指挥长</el-col>
              <el-col :span="21" class="command_data">
                <div>
                  <span v-if="selectedCommander.commanderId">
                    <span style="margin-right: 10px">
                      {{ selectedCommander.commander }}</span
                    >
                    <span> {{ selectedCommander.commanderTel }}</span>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="
                        commanderDeleteById(
                          selectedCommander.commanderId,
                          selectedCommander.commander
                        )
                      "
                    ></i>
                  </span>
                  <el-button plain @click="handleCommanderAdd(1)" v-else>
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 副指挥长 -->
            <el-row class="command_second">
              <el-col :span="3" class="command_title">副指挥长</el-col>
              <el-col :span="21" :offset="3" class="command_data">
                <div>
                  <span
                    v-for="(
                      assistantCommander, index
                    ) in assistantCommanderList"
                    :key="index"
                    class="surname"
                    v-if="assistantCommanderList.length > 0"
                  >
                    <span
                      class="name_add_limit"
                      :title="assistantCommander.commander"
                    >
                      {{ assistantCommander.commander }}
                    </span>
                    <span :title="assistantCommander.commanderTel">{{
                      assistantCommander.commanderTel
                    }}</span>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="
                        commanderDeleteById(
                          assistantCommander.commanderId,
                          assistantCommander.commander
                        )
                      "
                    ></i>
                  </span>
                  <el-button plain @click="handleAssistantCommanderAdd(1)">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 办公室成员 -->
            <el-row class="command_second">
              <el-col :span="3" class="command_title">办公室成员</el-col>
              <el-col :span="21" :offset="3" class="command_data">
                <div>
                  <span
                    v-for="(officeMember, index) in officeMemberList"
                    :key="index"
                    class="surname"
                    v-if="officeMemberList.length > 0"
                  >
                    <span
                      class="name_add_limit"
                      :title="officeMember.commander"
                      >{{ officeMember.commander }}</span
                    >
                    <span>{{ officeMember.commanderTel }}</span>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="
                        commanderDeleteById(
                          officeMember.commanderId,
                          officeMember.commander
                        )
                      "
                    ></i>
                  </span>
                  <el-button plain @click="handleOfficeMemberAdd(1)">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </el-row>
          <el-row class="command_centre">行动组</el-row>
          <el-row class="command_content">
            <el-row
              v-for="(team, index) in teamList"
              :key="index"
              class="command_second"
            >
              <el-col :span="3" class="command_title" style="height: 100%">
                <span style="font-size: 17px" :title="team.teamName">{{
                  team.teamName
                }}</span>
                <i
                  class="el-icon-circle-close"
                  style="cursor: pointer; color: #ff4747"
                  @click="teamDeleteById(team.teamId, team.teamName)"
                ></i>
              </el-col>
              <el-col :span="21" :offset="3" class="action_data">
                <el-row class="duty">
                  <span :title="team.teamDuty">{{ team.teamDuty }}</span>
                </el-row>
                <el-row
                  v-if="team.gempPlanDigitalTeamComDTOs"
                  class="action_right"
                  v-for="(item, index) in team.gempPlanDigitalTeamComDTOs"
                  :key="index"
                >
                  <el-col :span="6" class="action_title">
                    <i class="el-icon-s-custom"></i>
                    <span style="color: #ff4747" :title="item.comDuty">{{
                      item.comName
                    }}</span>
                    <i
                      class="el-icon-edit-outline"
                      style="cursor: pointer; color: #4c7cff"
                      @click="handleComEdit(team, item)"
                    ></i>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="comDeleteById(item.comId, item.comName)"
                    ></i>
                    <div class="parent_title">
                      {{
                        item.comParentName == "组织机构"
                          ? ""
                          : item.comParentName
                      }}
                    </div>
                  </el-col>
                  <el-col :span="2" class="online"></el-col>
                  <el-col :span="16">
                    <el-row class="plan-team-com">
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-user-solid"></i>
                        <span
                          style="cursor: pointer; color: #ff4747"
                          @click="handleComMember(item, 1)"
                          >主要领导:</span
                        >
                        <div class="team_com">
                          <span
                            @click="handleComMember(item, 1)"
                            :title="item.comLeaderName"
                          >
                            {{ item.comLeaderName }}</span
                          >
                        </div>
                      </el-col>
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-s-custom"></i> 职务:
                        <div class="team_com">
                          <span :title="item.comPersonPost">{{
                            item.comLeaderPost
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6" class="team_text">
                        <i class="el-icon-phone"></i> 电话:
                        <div class="team_com">
                          <span :title="item.comLeaderTel">{{
                            item.comLeaderTel
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row class="plan-team-com">
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-user-solid"></i>
                        <span
                          style="cursor: pointer; color: #ff4747"
                          @click="handleComMember(item, 2)"
                          >副领导:</span
                        >
                        <div class="team_com">
                          <span
                            @click="handleComMember(item, 2)"
                            :title="item.comDeputyLeaderName"
                          >
                            {{ item.comDeputyLeaderName }}</span
                          >
                        </div>
                      </el-col>
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-s-custom"></i> 职务:
                        <div class="team_com">
                          <span :title="item.comDeputyLeaderPost">{{
                            item.comDeputyLeaderPost
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6" class="team_text">
                        <i class="el-icon-phone"></i> 电话:
                        <div class="team_com">
                          <span :title="item.comDeputyLeaderTel">{{
                            item.comDeputyLeaderTel
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row class="plan-team-com">
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-user-solid"></i>
                        <span
                          style="cursor: pointer; color: #ff4747"
                          @click="handleComMember(item, 3)"
                          >联络员:</span
                        >
                        <div class="team_com">
                          <span
                            @click="handleComMember(item, 3)"
                            :title="item.comContractName"
                          >
                            {{ item.comContractName }}</span
                          >
                        </div>
                      </el-col>
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-s-custom"></i> 职务:
                        <div class="team_com">
                          <span :title="item.comContractPost">{{
                            item.comContractPost
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6" class="team_text">
                        <i class="el-icon-phone"></i> 电话:
                        <div class="team_com">
                          <span :title="item.comContractTel">{{
                            item.comContractTel
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <el-row class="team_btn">
                  <el-button plain @click="handleComAdd(team)">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </el-row>
              </el-col>
            </el-row>
            <el-row class="action_btn">
              <el-button plain @click="handleTeamAdd(1)">
                <i class="el-icon-plus"></i> 添加
              </el-button>
            </el-row>
          </el-row>
        </el-scrollbar>
      </div>
      <div class="flex-full" v-if="checkPage == '3'">
        <el-scrollbar>
          <el-row class="command_centre">指挥中心</el-row>
          <el-row class="command_content">
            <!-- 指挥长 -->
            <el-row class="command_first">
              <el-col :span="3" class="command_title">指挥长</el-col>
              <el-col :span="21" class="command_data">
                <div>
                  <span v-if="selectedCommander.commanderId">
                    <span style="margin-right: 10px">{{
                      selectedCommander.commander
                    }}</span>
                    <!-- <i class="el-icon-phone"> -->
                    <span>{{ selectedCommander.commanderTel }}</span>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="
                        commanderDeleteById(
                          selectedCommander.commanderId,
                          selectedCommander.commander
                        )
                      "
                    ></i>
                  </span>
                  <el-button plain @click="handleCommanderAdd(2)" v-else>
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 副指挥长 -->
            <el-row class="command_second">
              <el-col :span="3" class="command_title">副指挥长</el-col>
              <el-col :span="21" :offset="3" class="command_data">
                <div>
                  <span
                    v-for="(
                      assistantCommander, index
                    ) in assistantCommanderList"
                    :key="index"
                    class="surname"
                    v-if="assistantCommanderList.length > 0"
                  >
                    <span
                      class="name_add_limit"
                      :title="assistantCommander.commander"
                      >{{ assistantCommander.commander }}
                    </span>
                    <span :title="assistantCommander.commanderTel">
                      {{ assistantCommander.commanderTel }}</span
                    >
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="
                        commanderDeleteById(
                          assistantCommander.commanderId,
                          assistantCommander.commander
                        )
                      "
                    ></i>
                  </span>
                  <el-button plain @click="handleAssistantCommanderAdd(2)">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </div>
              </el-col>
            </el-row>
            <!-- 办公室成员 -->
            <el-row class="command_second">
              <el-col :span="3" class="command_title">办公室成员</el-col>
              <el-col :span="21" :offset="3" class="command_data">
                <div>
                  <span
                    v-for="(officeMember, index) in officeMemberList"
                    :key="index"
                    class="surname"
                    v-if="officeMemberList.length > 0"
                  >
                    <span
                      class="name_add_limit"
                      :title="officeMember.commander"
                      >{{ officeMember.commander }}</span
                    >
                    <span>{{ officeMember.commanderTel }}</span>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="
                        commanderDeleteById(
                          officeMember.commanderId,
                          officeMember.commander
                        )
                      "
                    ></i>
                  </span>
                  <el-button plain @click="handleOfficeMemberAdd(2)">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </div>
              </el-col>
            </el-row>
          </el-row>
          <!-- 预案小组 -->
          <el-row class="command_centre">行动组</el-row>
          <el-row class="command_content">
            <el-row
              v-for="(team, index) in teamList"
              :key="index"
              class="command_second"
            >
              <el-col :span="3" class="command_title" style="height: 100%">
                <span style="font-size: 17px" :title="team.teamName">{{
                  team.teamName
                }}</span>
                <i
                  class="el-icon-circle-close"
                  style="cursor: pointer; color: #ff4747"
                  @click="teamDeleteById(team.teamId, team.teamName)"
                ></i>
              </el-col>
              <el-col :span="21" :offset="3" class="action_data">
                <el-row class="duty">
                  <span :title="team.teamDuty">{{ team.teamDuty }}</span>
                </el-row>
                <el-row
                  v-if="team.gempPlanDigitalTeamComDTOs"
                  class="action_right"
                  v-for="(item, index) in team.gempPlanDigitalTeamComDTOs"
                  :key="index"
                >
                  <el-col :span="6" class="action_title" :title="item.comDuty">
                    <i class="el-icon-s-custom"></i>
                    <span style="color: #ff4747">{{ item.comName }}</span>
                    <i
                      class="el-icon-edit-outline"
                      style="cursor: pointer; color: #4c7cff"
                      @click="handleComEdit(team, item)"
                    ></i>
                    <i
                      class="el-icon-circle-close"
                      style="cursor: pointer; color: #ff4747"
                      @click="comDeleteById(item.comId)"
                    ></i>
                    <div class="parent_title">
                      {{
                        item.comParentName == "组织机构"
                          ? ""
                          : item.comParentName
                      }}
                    </div>
                  </el-col>
                  <el-col :span="2" class="online"></el-col>
                  <el-col :span="16">
                    <el-row class="plan-team-com">
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-user-solid"></i>
                        <span
                          style="cursor: pointer; color: #ff4747"
                          @click="handleComMember(item, 1)"
                          >主要领导:</span
                        >
                        <div class="team_com">
                          <span
                            @click="handleComMember(item, 1)"
                            :title="item.comLeaderName"
                          >
                            {{ item.comLeaderName }}</span
                          >
                        </div>
                      </el-col>
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-s-custom"></i> 职务:
                        <div class="team_com">
                          <span :title="item.comPersonPost">{{
                            item.comLeaderPost
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6" class="team_text">
                        <i class="el-icon-phone"></i> 电话:
                        <div class="team_com">
                          <span :title="item.comLeaderTel">{{
                            item.comLeaderTel
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row class="plan-team-com">
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-user-solid"></i>
                        <span
                          style="cursor: pointer; color: #ff4747"
                          @click="handleComMember(item, 2)"
                          >副领导:</span
                        >
                        <div class="team_com">
                          <span
                            @click="handleComMember(item, 2)"
                            :title="item.comDeputyLeaderName"
                          >
                            {{ item.comDeputyLeaderName }}</span
                          >
                        </div>
                      </el-col>
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-s-custom"></i> 职务:
                        <div class="team_com">
                          <span :title="item.comDeputyLeaderPost">{{
                            item.comDeputyLeaderPost
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6" class="team_text">
                        <i class="el-icon-phone"></i> 电话:
                        <div class="team_com">
                          <span :title="item.comDeputyLeaderTel">{{
                            item.comDeputyLeaderTel
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                    <el-row class="plan-team-com">
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-user-solid"></i>
                        <span
                          style="cursor: pointer; color: #ff4747"
                          @click="handleComMember(item, 3)"
                          >联络员:</span
                        >
                        <div class="team_com">
                          <span
                            @click="handleComMember(item, 3)"
                            :title="item.comContractName"
                          >
                            {{ item.comContractName }}</span
                          >
                        </div>
                      </el-col>
                      <el-col :span="7" class="team_text">
                        <i class="el-icon-s-custom"></i> 职务:
                        <div class="team_com">
                          <span :title="item.comContractPost">{{
                            item.comContractPost
                          }}</span>
                        </div>
                      </el-col>
                      <el-col :span="6" class="team_text">
                        <i class="el-icon-phone"></i> 电话:
                        <div class="team_com">
                          <span :title="item.comContractTel">{{
                            item.comContractTel
                          }}</span>
                        </div>
                      </el-col>
                    </el-row>
                  </el-col>
                </el-row>
                <el-row class="team_btn">
                  <el-button plain @click="handleComAdd(team)">
                    <i class="el-icon-plus"></i> 添加
                  </el-button>
                </el-row>
              </el-col>
            </el-row>
            <el-row class="action_btn">
              <el-button plain @click="handleTeamAdd(2)">
                <i class="el-icon-plus"></i> 添加
              </el-button>
            </el-row>
          </el-row>
        </el-scrollbar>
      </div>
      <!-- 添加指挥长副指挥长办公室成员弹框 -->
      <el-dialog
        :title="commanderDialogTitle"
        :visible.sync="commanderDialogVisible"
        width="75%"
        class="add-commander"
        :close-on-click-modal="false"
        custom-class="dia-people"
        append-to-body="true"
      >
        <!-- <plan-orgs v-if="commanderDialogVisible" :memberparams="memberParams" @close="colseCommanderDialog">
        </plan-orgs> -->
        <DataMaintenance
          v-if="commanderDialogVisible"
          :memberparams="memberParams"
          :edVisible='edVisible'
          @close="colseCommanderDialog"
        ></DataMaintenance>
      </el-dialog>
      <!-- 添加组成员弹框 -->
      <el-dialog
        :title="memberAddDialogTitle"
        :visible.sync="memberAddDialogVisible"
        :close-on-click-modal="false"
        append-to-body="true"
        @close='colseMemberAddDialog'
      >
        <!-- <plan-member-add v-if="memberAddDialogVisible" :memberaddparms="memberAddParms" @close="colseMemberAddDialog">
        </plan-member-add> -->
        <el-form
          :model="memberAddParms"
          :rules="rules"
          ref="memberAddParms"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item
            label="应急小组名称"
            prop="teamName"
            :rules="[
              {
                required: true,
                message: '请输入应急小组名称',
                trigger: 'blur',
              },
            ]"
          >
            <el-input v-model.trim="memberAddParms.teamName" disabled></el-input>
          </el-form-item>
          <el-form-item
            label="单位名称"
            prop="comId"
            :rules="[
              { required: true, message: '请输入单位名称', trigger: 'change' },
            ]"
          >
            <!-- <el-input v-model="memberAddParms.teamName"></el-input> -->
            <el-cascader
              placeholder="请选择单位名称"
              v-model="memberAddParms.comId"
              :options="establishOrgCodeData"
              @change="handleChangeEstablishOrgCode"
            >
            </el-cascader>
          </el-form-item>
          <el-form-item
            label="单位职责"
            prop="comDuty"
            :rules="[
              { required: true, message: '请输入单位职责', trigger: 'blur' },
            ]"
          >
            <el-input v-model.trim="memberAddParms.comDuty"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitMemberAddParms"
            >保 存</el-button
          >
          <el-button @click="colseMemberAddDialog">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 编辑组成员弹框 -->
      <el-dialog
        :title="memberEditDialogTitle"
        :visible.sync="memberEditDialogVisible"
        :close-on-click-modal="false"
        append-to-body="true"
        @close='colseMemberEditDialog'
      >
        <!-- <plan-member-edit v-if="memberEditDialogVisible" :membereditparms="memberEditParms"
            @close="colseMemberEditDialog"></plan-member-edit> -->
        <!-- ok -->
        <el-form
          :model="memberDetailParams"
          :rules="rules"
          ref="memberDetailParams"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item label="应急小组名称">
            <el-input
              :disabled="true"
              v-model="memberDetailParams.teamName"
            ></el-input>
          </el-form-item>
          <el-form-item label="单位名称">
            <el-input
              :disabled="true"
              v-model.trim="memberDetailParams.comName"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="单位职责"
            prop="comDuty"
            :rules="[
              { required: true, message: '请输入单位职责', trigger: 'blur' },
            ]"
          >
            <el-input v-model.trim="memberDetailParams.comDuty"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitMemberDetailParams"
            >保 存</el-button
          >
          <el-button @click="colseMemberEditDialog">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 添加领导联络员弹框 -->
      <el-dialog
        :title="comMemberAddDialogTitle"
        :visible.sync="comMemberAddDialogVisible"
        :close-on-click-modal="false"
        append-to-body="true"
      >
        <!-- <com-member-add v-if="comMemberAddDialogVisible" :commemberaddparms="comMemberAddParms"
            :selectpersonId="selectPersonId" @close="comMemberAddDialog"></com-member-add> -->
        <div class="table">
          <el-table
            :data="personData"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            v-loading="loadingTable"
            style="width: 100%"
            ref="multipleTable"
          >
            <el-table-column align="center">
              <template slot-scope="scope">
                <!-- {{ scope.row.selectRadio }} -->
                <el-radio
                  v-model="scope.row.selectRadio"
                  :label="scope.row.personId"
                  @change.native.stop.prevent="changeRedio(scope.row)"
                  >&nbsp;</el-radio
                >
              </template>
            </el-table-column>
            <el-table-column
              prop="personName"
              label="姓名"
              align="center"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column prop="post" label="职务" align="center">
            </el-table-column>
            <el-table-column
              prop="company"
              label="人员单位"
              align="center"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="phone"
              label="办公电话"
              align="center"
              min-width="100"
            >
            </el-table-column>
            <el-table-column prop="telephone" label="手机" align="center">
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination" style="float: right">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page.sync="nowPage"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            background
            :total="total"
          >
          </el-pagination>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitPerson">保 存</el-button>
          <el-button @click="comMemberAddDialog">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 添加组弹框 -->
      <el-dialog
        :title="teamAddDialogTitle"
        :visible.sync="teamAddDialogVisible"
        :close-on-click-modal="false"
        append-to-body="true"
        @close='colseTeamAddDialog'
        
      >
        <!-- <plan-team-add v-if="teamAddDialogVisible" :teamaddparms="teamAddParms" @close="colseTeamAddDialog">
        </plan-team-add> -->
        <el-form
          :model="formData"
          :rules="rules"
          ref="formData"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item
            label="应急小组名称"
            prop="teamName"
            :rules="[
              {
                required: true,
                message: '请输入应急小组名称',
                trigger: 'blur',
              },
            ]"
          >
            <el-input v-model.trim="formData.teamName"></el-input>
          </el-form-item>
          <el-form-item
            label="单位职责"
            prop="teamDuty"
            :rules="[
              { required: true, message: '请输入单位职责', trigger: 'blur' },
            ]"
          >
            <el-input v-model.trim="formData.teamDuty"></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForm">保 存</el-button>
          <el-button @click="colseTeamAddDialog">取 消</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getDigitalList,
  getEstablishOrgCodeTreeData,
  detailPlanData,
  addDigitalList,
  editDigitalList,
  detailDigitalList,
  commanderListGetList,
  teamAdd,
  commanderDeleteById,
  teamListGet,
  teamDeleteById,
  comDeleteById,
  comAdd,
  commanderAdd,
  comGetById,
  comEdit,
} from "@/api/mergencyResources";
import { getMailListTreeDataForPerson } from "@/api/mailList";
import DataMaintenance from "./dataMaintenance";
export default {
  //import引入的组件
  name: "responseofour",
  components: {
    DataMaintenance,
  },
  props:{
    edVisible: {
      type: Boolean,
    },
  },
  data() {
    return {
      personData: [],
      pageSize: 5,
      nowPage: 1,
      total: "",
      selectRadio: false,
      templateSelection: {},
      checkPage: "1",
      loadingTable: false,
      selectPersonId: "",
      planid: "",
      responseLevel: 4,
      isAdd: true,
      establishOrgCodeData: [],
      memberParams: {
        commandType: null,
        commanderType: null,
        planId: "",
        responseLevel: null,
      },
      memberDetailParams: {
        teamName: "",
        comDuty: "",
        comName: "",
        comOrgcode: "",
        sortSeq: "",
        comLeaderPersonId: "",
        comContractPersonId: "",
        comDeputyLeaderPersonId: "",
      },
      formdata: {
        createBy: "", //创建人
        createtime: "", //($date-time)创建时间
        id: "", //预案数字化基本信息id
        planId: "", //预案ID
        responseAction: "", //响应行动
        responseLevel: 4, //($int32)响应等级（1：一级响应；2：二级响应；3：三级响应；4：四级响应）
        startCondition: "", //启动条件
        updateBy: "", //修改人
        updatetime: "", //($date-time)修改时间
      },
      formData: {
        teamName: "",
        teamDuty: "",
        commandType: "", //($int32)1：总指挥部；2：现场指挥部
        planId: "",
        responseLevel: "", //($int32)响应等级（1：一级响应；2：二级响应；3：三级响应；4：四级响应）
        sortSeq: "",
        teamId: "", //应急小组ID
        teamName: "", //应急小组名
      },
      searchData: {
        planId: "",
        responseLevel: 0,
      },
      memberAddParms: {
        commandType: null,
        responseLevel: null,
        teamId: "",
        teamName: "",
        comId: "",
        comDuty: "",
      },
      memberEditParms: {
        commandType: null,
        responseLevel: null,
        teamId: "",
        teamName: "",
        comId: "",
      },
      teamAddParms: {
        commandType: null,
        planId: "",
        responseLevel: null,
      },
      comMemberAddParms: {
        comContractName: "",
        comContractPersonId: "",
        comContractPost: "",
        comContractTel: "",
        comDeputyLeaderName: "",
        comDeputyLeaderPersonId: "",
        comDeputyLeaderPost: "",
        comDeputyLeaderTel: "",
        comDuty: "",
        comId: "",
        comLeaderName: "",
        comLeaderPersonId: "",
        comLeaderPost: "",
        comLeaderTel: "",
        comName: "",
        comOrgcode: "",
        commandType: null,
        responseLevel: null,
        sortSeq: null,
        teamId: "",
        type: null,
      },
      commanderDialogVisible: false,
      commanderDialogTitle: "",
      memberAddDialogVisible: false,
      isShow: true,
      memberAddDialogTitle: "",
      memberEditDialogVisible: false,
      memberEditDialogTitle: "",
      comMemberAddDialogVisible: false,
      comMemberAddDialogTitle: "",
      teamAddDialogVisible: false,
      teamAddDialogTitle: "",
      selectedCommander: {
        commandType: null, //($int32)1：总指挥部；2：现场指挥部
        commander: "", //指挥长名称
        commanderId: "", //ID
        commanderTel: "", //指挥长手机号
        commanderType: null, //($int32)1：指挥长；2：副指挥长;3:办公室成员
        createBy: "", //创建人
        createtime: "", //($date-time)创建时间
        personId: "", //common_maillist_person表主键
        planId: "", //预案ID
        responseLevel: null, //($int32)响应等级（1：一级响应；2：二级响应；3：三级响应；4：四级响应）
        sortSeq: null, //($int32)排序，数值小的排前面
        updateBy: "", //修改人
        updatetime: "", //($date-time)修改时间
      },
      assistantCommanderList: [],
      officeMemberList: [],
      teamList: [],
      ruleForm: {
        startCondition: "",
        responseAction: "",
      },
      rules: {
        startCondition: [
          { required: true, message: "请输入启动条件", trigger: "blur" },
        ],
        responseAction: [
          { required: true, message: "请输入响应行动", trigger: "blur" },
        ],
      },
    };
  },
  //方法集合
  methods: {
    reset() {
      this.formData = {
        teamName: "",
        teamDuty: "",
        commandType: "", //($int32)1：总指挥部；2：现场指挥部
        planId: "",
        responseLevel: "", //($int32)响应等级（1：一级响应；2：二级响应；3：三级响应；4：四级响应）
        sortSeq: "",
        teamId: "", //应急小组ID
        teamName: "", //应急小组名
      };
      if (this.$refs["formData"]) {
        this.$refs["formData"].resetFields();        
      }
      this.formdata = {
        createBy: "", //创建人
        createtime: "", //($date-time)创建时间
        id: "", //预案数字化基本信息id
        planId: "", //预案ID
        responseAction: "", //响应行动
        responseLevel: 4, //($int32)响应等级（1：一级响应；2：二级响应；3：三级响应；4：四级响应）
        startCondition: "", //启动条件
        updateBy: "", //修改人
        updatetime: "", //($date-time)修改时间
      };
      if (this.$refs["formdata"]) {
        this.$refs["formdata"].resetFields();
      }
      this.memberAddParms = {
        commandType: null,
        responseLevel: null,
        teamId: "",
        teamName: "",
        comId: "",
        comDuty: "",
      };
      if (this.$refs["memberAddParms"]) {
        this.$refs["memberAddParms"].resetFields();
      }
      this.memberDetailParams = {
        teamName: "",
        comDuty: "",
        comName: "",
        comOrgcode: "",
        sortSeq: "",
        comLeaderPersonId: "",
        comContractPersonId: "",
        comDeputyLeaderPersonId: "",
      };
      if (this.$refs["memberDetailParams"]) {
        this.$refs["memberDetailParams"].resetFields();
      }
    },
    resetPage() {
      this.checkPage = "1";
    },
    handleCurrentChange(val) {
      // this.nowPage = val;
      // this.search();
    },
    changeRedio(row) {
     
      this.personData.forEach(item=>{
        if(item.personId!=row.personId){
          item.selectRadio='111'
        }
      })
      this.templateSelection = row;     
    },
    //获取预案数字化基本信息
    planDigitalGet(planId, responseLevel) {
      this.planid = planId;
      this.responseLevel = 4;
      this.formdata.planId = planId;
      this.formdata.responseLevel = responseLevel;
      this.searchData.planId = planId;
      this.searchData.responseLevel = responseLevel;
      detailDigitalList({
        planId: planId,
        responseLevel: responseLevel,
      }).then((res) => {
        if (res.data.status == 200) {
          if (res.data.msg == "查询成功") {
            this.isAdd = false;
            this.formdata = res.data.data;
          } else {
            this.isAdd = true;
          }
        } else {
          this.isAdd = true;
        }
      });
      //编制单位
      getEstablishOrgCodeTreeData().then((res) => {
        if (res.data.status == 200) {
          // debugger
          this.establishOrgCodeData = res.data.data[0].children;
          this.establishOrgCodeData.forEach((item) => {
            item.value = item.id;
            if (item.children && item.children.length > 0) {
              item.children.forEach((items) => {
                items.value = items.id;
                if (items.children && items.children.length > 0) {
                  items.children.forEach((itemed) => {
                    itemed.value = itemed.id;
                  });
                }
              });
            }
          });
        }
      });
    },
    handleChangeEstablishOrgCode(value) {
      if (value.length > 0) {
        this.memberAddParms.comId = value[value.length - 1];
      } else {
        this.memberAddParms.comId = "";
      }
    },
    //基本信息保存方法
    submit(e) {
      console.log(this.$refs);
      console.log(e);
      var _this = this;
      if (e == 1) {
        this.$refs["formdata"].validate((valid) => {
          console.log(valid);
          if (valid) {
            if (this.formdata.startCondition.trim() == "") {
              this.$message.warning("启动条件不能为空！");
              return false;
            }
            if (this.formdata.responseAction.trim() == "") {
              this.$message.warning("响应行为不能为空！");
              return false;
            }
            let parms = this.formdata;
            if (this.isAdd) {
              addDigitalList(parms).then((res) => {
                if (res.data.status == 200) {
                  this.$message.success("保存成功!");
                  // this.reset()
                  this.planDigitalGet(
                    _this.searchData.planId,
                    _this.searchData.responseLevel
                  );
                } else {
                  this.$message.error("保存失败!");
                }
              });
            } else {
              editDigitalList(parms).then((res) => {
                if (res.data.status == 200) {
                  this.$message.success("修改成功!");
                  // this.planDigitalGet(this.searchData);
                } else {
                  this.$message.error("修改失败!");
                }
              });
            }
          }
        });
        this.isShow = true;
      } else {
        this.isShow = false;
      }
    },
    submitForm() {
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          teamAdd({
            commandType: this.teamAddParms.commandType,
            planId: this.teamAddParms.planId,
            responseLevel: 4,
            sortSeq: 0,
            teamDuty: this.formData.teamDuty,
            teamId: "",
            teamName: this.formData.teamName,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("保存成功!");
              this.reset();
              this.teamAddDialogVisible = false;
              this.initPage(this.teamAddParms.commandType);
            } else {
              this.$message.error("保存失败!");
            }
          });
        }
      });
    },
    submitMemberAddParms() {
      this.$refs["memberAddParms"].validate((valid) => {
        if (valid) {
          console.log(this.memberAddParms);
          comAdd({
            comContractPersonId: "",
            comDuty: this.memberAddParms.comDuty,
            comId: "",
            comLeaderPersonId: "",
            comName: this.memberAddParms.comName,
            comOrgcode: this.memberAddParms.comId,
            commandType: this.memberAddParms.commandType,
            responseLevel: this.memberAddParms.responseLevel,
            sortSeq: 0,
            teamId: this.memberAddParms.teamId,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("保存成功!");
              this.initPage(this.memberAddParms.commandType);
              this.reset();
              this.memberAddDialogVisible = false;
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    submitMemberDetailParams() {
      this.$refs["memberDetailParams"].validate((valid) => {
        if (valid) {
          console.log(this.memberDetailParams);
          comEdit({
            comContractPersonId: this.memberDetailParams.comContractPersonId,
            comDeputyLeaderPersonId:
              this.memberDetailParams.comDeputyLeaderPersonId,
            comDuty: this.memberDetailParams.comDuty,
            comId: this.memberEditParms.comId,
            comLeaderPersonId: this.memberDetailParams.comLeaderPersonId,
            comName: this.memberDetailParams.comName,
            comOrgcode: this.memberDetailParams.comOrgcode,
            commandType: this.memberEditParms.commandType,
            responseLevel: this.memberEditParms.responseLevel,
            sortSeq: this.memberDetailParams.sortSeq,
            teamId: this.memberEditParms.teamId,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("保存成功!");
              this.initPage(this.memberEditParms.commandType);
              this.reset();
              this.memberEditDialogVisible = false;
            } else {
              this.$message.error("保存失败!");
            }
          });
        }
      });
    },
    submitPerson() {
      if (!this.templateSelection.personId) {
        this.$message.error("请先选择人员再保存！");
        return;
      }
      const userInfo = {
        dutyNumber: this.templateSelection.dutyNumber,
        orgName: this.templateSelection.orgCodeName,
        personId: this.templateSelection.personId,
        personJob: this.templateSelection.post,
        personName: this.templateSelection.personName,
        telNumber: this.templateSelection.telephone,
      };
      let personInfoDTOs = [];
      personInfoDTOs.push(userInfo);
      let params = {
        comDuty: this.comMemberAddParms.comDuty,
        comId: this.comMemberAddParms.comId,
        comName: this.comMemberAddParms.comName,
        comOrgcode: this.comMemberAddParms.comOrgcode,
        commandType: this.comMemberAddParms.commandType,
        responseLevel: this.comMemberAddParms.responseLevel,
        sortSeq: this.comMemberAddParms.sortSeq,
        teamId: this.comMemberAddParms.teamId,
        personInfoDTOs: personInfoDTOs,
      };
      if (this.comMemberAddParms.type == 1) {
        params.comContractPersonId = this.comMemberAddParms.comContractPersonId;
        params.comDeputyLeaderPersonId =
          this.comMemberAddParms.comDeputyLeaderPersonId;
        params.comLeaderPersonId = this.templateSelection.personId;
      }
      if (this.comMemberAddParms.type == 2) {
        params.comContractPersonId = this.comMemberAddParms.comContractPersonId;
        params.comDeputyLeaderPersonId = this.templateSelection.personId;
        params.comLeaderPersonId = this.comMemberAddParms.comLeaderPersonId;
      }
      if (this.comMemberAddParms.type == 3) {
        params.comContractPersonId = this.templateSelection.personId;
        params.comDeputyLeaderPersonId =
          this.comMemberAddParms.comDeputyLeaderPersonId;
        params.comLeaderPersonId = this.comMemberAddParms.comLeaderPersonId;
      }
      comEdit(params).then((res) => {
        if (res.data.status == 200) {
          this.$message.success("保存成功!");
          this.initPage(this.comMemberAddParms.commandType);
          this.reset();
          this.comMemberAddDialogVisible = false;
        } else {
          this.$message.error("保存失败!");
        }
      });
    },
    // 获取指挥长信息
    commanderListGet(parms) {
      commanderListGetList(parms).then((res) => {
        if (res.data.status == 200) {
          if (res.data.data.length > 0) {
            this.selectedCommander = res.data.data[0];
          } else {
            this.selectedCommander = {
              commandType: null,
              commander: "",
              commanderId: "",
              commanderTel: "",
              commanderType: null,
              createBy: "",
              createtime: "",
              personId: "",
              planId: "",
              responseLevel: null,
              sortSeq: null,
              updateBy: "",
              updatetime: "",
            };
          }
        } else {
          this.$message.error("数据获取失败!");
        }
      });
    },
    // 删除指挥长/副指挥长/办公室成员
    commanderDeleteById(id, commander) {
      this.$confirm(`确认删除该条数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          commanderDeleteById({ id: id }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("操作成功");
              if (this.checkPage === "2") {
                this.initPage(1);
              }
              if (this.checkPage === "3") {
                this.initPage(2);
              }
            } else {
              this.$message.warning("删除失败!");
            }
          });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },
    // 获取副指挥长信息
    assistantCommanderListGet(parms) {
      commanderListGetList(parms).then((res) => {
        if (res.data.status == 200) {
          if (res.data.data.length > 0) {
            this.assistantCommanderList = res.data.data;
          } else {
            this.assistantCommanderList = [];
          }
        } else {
          this.$message.error("数据获取失败!");
        }
      });
    },

    // 获取办公室成员信息
    officeMemberListGet(parms) {
      commanderListGetList(parms).then((res) => {
        if (res.data.status == 200) {
          if (res.data.data.length > 0) {
            this.officeMemberList = res.data.data;
          } else {
            this.officeMemberList = [];
          }
        } else {
          this.$message.error("数据获取失败!");
        }
      });
    },
    // 获取预案小组集合
    teamListGet(parms) {
      teamListGet(parms).then((res) => {
        if (res.data.status == 200) {
          if (res.data.data.length > 0) {
            this.teamList = res.data.data;
          } else {
            this.teamList = [];
          }
        } else {
          this.$message.error("数据获取失败!");
        }
      });
    },

    // 删除应急小组
    teamDeleteById(id, teamName) {
      this.$confirm(`确认删除该条数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          teamDeleteById({
            id: id,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("删除成功");
              if (this.checkPage === "2") {
                this.initPage(1);
              }
              if (this.checkPage === "3") {
                this.initPage(2);
              }
            } else {
              this.$message.error("删除失败！");
            }
          });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 删除应急小组成员
    comDeleteById(id, comName) {
      this.$confirm(`确认删除该条数据？`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          comDeleteById({
            id: id,
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("删除成功");
              if (this.checkPage === "2") {
                this.initPage(1);
              }
              if (this.checkPage === "3") {
                this.initPage(2);
              }
            } else {
              this.$message.error("删除失败！");
            }
          });
        })
        .catch(() => {
          this.$message.info("已取消删除");
        });
    },

    // 点击选择指挥长
    handleCommanderAdd(parms) {
      this.memberParams = {
        commandType: parms,
        commanderType: 1,
        planId: this.planid,
        responseLevel: 4,
      };
      this.commanderDialogVisible = true;
      if(this.edVisible){
        this.commanderDialogTitle = "新增指挥长";
      } else {
        this.commanderDialogTitle = "选择指挥长";
      }
     
    },

    // 点击选择副指挥长
    handleAssistantCommanderAdd(parms) {
      this.memberParams = {
        commandType: parms,
        commanderType: 2,
        planId: this.planid,
        responseLevel: 4,
      };
      // this.$set(this, 'memberParams', memberParams)
      this.commanderDialogVisible = true;
      if(this.edVisible){
        this.commanderDialogTitle = "新增副指挥长";
      } else {
        this.commanderDialogTitle = "选择副指挥长";
      }
    },

    // 点击选择办公室成员
    handleOfficeMemberAdd(params) {
      this.commanderDialogVisible = true;
      if(this.edVisible){
        this.commanderDialogTitle = "新增办公室成员";
      } else {
        this.commanderDialogTitle = "选择办公室成员";
      }
      this.memberParams = {
        commandType: params,
        commanderType: 3,
        planId: this.planid,
        responseLevel: 4,
      };
    },

    // 点击添加预案小组
    handleTeamAdd(parms) {
      this.teamAddDialogVisible = true;
      if(this.edVisible){
        this.teamAddDialogTitle = "新增组";
      } else {
        this.teamAddDialogTitle = "选择组";
      }
      this.teamAddParms = {
        commandType: parms,
        planId: this.planid,
        responseLevel: 4,
      };
    },

    // 点击添加预案小组成员
    handleComAdd(parms) {
       this.$nextTick(() => {
         this.$refs["memberAddParms"].resetFields(); 
       });
      this.memberAddDialogVisible = true;
      this.memberAddDialogTitle = "新增组成员";
      // if(this.edVisible){
      //   this.memberAddDialogTitle = "新增组成员";
      // } else {
      //   this.memberAddDialogTitle = "选择组成员";
      // }
      // this.memberAddDialogTitle = "新增组成员";
      this.memberAddParms = {
        commandType: parms.commandType,
        responseLevel: 4,
        teamId: parms.teamId,
        teamName: parms.teamName,
      };
    },

    // 点击选择领导联络员
    handleComMember(item, type) {
      this.comMemberAddDialogVisible = true;
      this.comMemberAddDialogTitle = "查看负责人";
      this.loadingTable = true;
      getMailListTreeDataForPerson({
        orgCode: item.comOrgcode,
        pageSize: this.pageSize,
        nowPage: this.nowPage,
        queryType: "1",
        keyWord: "",
      }).then((res) => {
        if (res.data.status == 200) {
          this.loadingTable = false;
          if (type == 1) {
            res.data.data.list.forEach((el) => {
              if (item.comLeaderPersonId == el.personId) {
                el.selectRadio = el.personId;
              }else{
                el.selectRadio='111'
              }
            });
          }else if(type == 2){
             res.data.data.list.forEach((el) => {
              if (item.comDeputyLeaderPersonId == el.personId) {
                el.selectRadio = el.personId;
              }else{
                el.selectRadio='111'
              }
            });
          }else if(type == 3){
              res.data.data.list.forEach((el) => {
              if (item.comContractPersonId == el.personId) {
                el.selectRadio = el.personId;
              }else{
                el.selectRadio='111'
              }
            });
          }
          this.personData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
      if (type == 1) {
        this.selectPersonId = item.comLeaderPersonId;
      } else if (type == 2) {
        this.selectPersonId = item.comDeputyLeaderPersonId;
      } else if (type == 3) {
        this.selectPersonId = item.comContractPersonId;
      }
      this.comMemberAddParms = {
        comContractName: item.comContractName,
        comContractPersonId: item.comContractPersonId,
        comContractPost: item.comContractPost,
        comContractTel: item.comContractTel,
        comDeputyLeaderName: item.comDeputyLeaderName,
        comDeputyLeaderPersonId: item.comDeputyLeaderPersonId,
        comDeputyLeaderPost: item.comDeputyLeaderPost,
        comDeputyLeaderTel: item.comDeputyLeaderTel,
        comDuty: item.comDuty,
        comId: item.comId,
        comLeaderName: item.comLeaderName,
        comLeaderPersonId: item.comLeaderPersonId,
        comLeaderPost: item.comLeaderPost,
        comLeaderTel: item.comLeaderTel,
        comName: item.comName,
        comOrgcode: item.comOrgcode,
        commandType: item.commandType,
        responseLevel: item.responseLevel,
        sortSeq: item.sortSeq,
        teamId: item.teamId,
        type: type,
      };
    },

    // 点击编辑预案小组成员
    handleComEdit(team, item) {
      this.memberEditDialogVisible = true;
      this.memberEditDialogTitle = "编辑组成员";
      comGetById({
        comId: item.comId,
      }).then((res) => {
        if (res.data.status == 200) {
          this.memberDetailParams = {
            teamName: team.teamName,
            comDuty: res.data.data.comDuty,
            comName: res.data.data.comName,
            comOrgcode: res.data.data.comOrgcode,
            sortSeq: res.data.data.sortSeq,
            comLeaderPersonId: res.data.data.comLeaderPersonId,
            comContractPersonId: res.data.data.comContractPersonId,
            comDeputyLeaderPersonId: res.data.data.comDeputyLeaderPersonId,
          };
        }
      });
      this.memberEditParms = {
        commandType: team.commandType,
        responseLevel: 4,
        teamId: team.teamId,
        teamName: team.teamName,
        comId: item.comId,
      };
    },
    initPage(parms) {
      // 获取指挥长信息
      let commander = {
        commandType: parms,
        commanderType: 1,
        planId: this.planid,
        responseLevel: 4,
      };
      this.commanderListGet(commander);
      // 获取副指挥长集合信息
      let assistantCommander = {
        commandType: parms,
        commanderType: 2,
        planId: this.planid,
        responseLevel: 4,
      };
      this.assistantCommanderListGet(assistantCommander);
      // 获取办公室成员集合
      let officeMember = {
        commandType: parms,
        commanderType: 3,
        planId: this.planid,
        responseLevel: 4,
      };
      this.officeMemberListGet(officeMember);
      // 获取预案小组集合信息
      let team = {
        commandType: parms,
        commanderType: null,
        planId: this.planid,
        responseLevel: 4,
      };
      this.teamListGet(team);
    },

    // 关闭新增指挥长副指挥长办公室成员弹框
    colseCommanderDialog(data) {
      this.commanderDialogVisible = false;
      if (data) {
        commanderAdd({
          commandType: this.memberParams.commandType,
          commanderId: "",
          commanderType: this.memberParams.commanderType,
          personId: data,
          planId: this.memberParams.planId,
          responseLevel: 4,
          sortSeq: 0,
        }).then((res) => {
          if (res.data.status == 200) {
            this.initPage(this.memberParams.commandType);
          }
        });
      }
    },

    // 关闭新增成员弹框
    colseMemberAddDialog() {
      this.memberAddDialogVisible = false;
      this.$refs["memberAddParms"].resetFields();
    },

    // 关闭编辑成员弹框
    colseMemberEditDialog() {
      this.memberEditDialogVisible = false;
      this.$refs["memberDetailParams"].resetFields();
    },

    // 关闭新增领导联络员弹框
    comMemberAddDialog() {
      this.comMemberAddDialogVisible = false;
    },

    // 关闭新增组弹框
    colseTeamAddDialog() {
      this.teamAddDialogVisible = false;
      this.$refs["formData"].resetFields();
    },
  },
  watch: {
    checkPage(val) {
      if (val === "1") {
        this.searchData.planId = this.planid;
        this.searchData.responseLevel = 4;
        this.planDigitalGet(this.planid, 4);
        this.isShow = true;
      }
      if (val === "2") {
        this.initPage(1);
        this.isShow = false;
      }
      if (val === "3") {
        this.initPage(2);
        this.isShow = false;
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.$nextTick(() => {
      // this.$refs["memberAddParms"].resetFields();      
      // this.$refs["comId"].resetFields()
    });
    // this.nowPage = this.$route.query.nowPage
    // this.searchData.planId = this.planid;
    // this.searchData.responseLevel = this.responselevel;
    // this.planDigitalGet(this.searchData);
    // this.on('save', (val) => {
    //   this.commanderDialogVisible = val[0];
    //   if (this.checkPage === '2') {
    //     this.initPage(1);
    //   }
    //   if (this.checkPage === '3') {
    //     this.initPage(2);
    //   }
    // });
  },
};
</script>
<style lang="scss" scoped>
.historyList {
  .command_centre {
    position: relative;
    height: 4rem;
    width: 100%;
    line-height: 4rem;
    padding: 0 2rem;
    background-color: #f3f3f5;
    // font: bold 1.5rem '黑体';
    font-size: 0.9rem;
    color: #3f63c3;
    font-weight: 600;

    &:before {
      content: "";
      position: absolute;
      top: 1rem;
      left: 0.1rem;
      display: block;
      width: 0.3rem;
      height: 1.9rem;
      background-color: #3f63c3;
    }
  }

  .command_content {
    border: 1px solid #dfdfe3;
    border-width: 1px 0 0 0;

    .command_first {
      min-height: 4.5rem;
      line-height: 4.5rem;
      border: 1px solid #dfdfe3;
      border-width: 0 0 1px 0;
      text-align: right;

      .command_title {
        padding: 0 1rem 0 0;
        font-size: 1rem;
        font-weight: bold;
        border: 1px solid #dfdfe3;
        border-width: 0 1px 0 0;
      }

      .command_data {
        padding: 0 0 0 1.5rem;
        text-align: left;
        // font-size: 14px;
      }
    }

    .command_second {
      min-height: 4.5rem;
      border: 1px solid #dfdfe3;
      border-width: 0 0 1px 0;
      overflow: hidden;

      .command_title {
        position: absolute;
        align-items: center;
        justify-content: flex-end;
        display: flex;
        height: 100%;
        padding: 0 1rem 0 0;
        border: 1px solid #dfdfe3;
        border-width: 0 1px 0 0;
        font-size: 1rem;
        font-weight: bold;
        text-align: right;

        span {
          overflow: hidden; //超出的文本隐藏
          text-overflow: ellipsis; //溢出用省略号显示
          white-space: nowrap; //溢出不换行
          height: 25px;
        }
      }

      .command_data {
        padding: 1rem 0 1rem 1.5rem;

        .surname {
          display: inline-block;
          height: 2rem;
          line-height: 2rem;
          padding: 0 15px;
          margin: 0.5rem 0;
          border: 1px solid #dfdfe3;
          border-width: 0 1px 0 0;

          span {
            display: inline-block;
            height: 35px;
            line-height: 49px;
          }

          &:nth-child(6n) {
            padding-left: 0;
          }

          &:nth-child(1) {
            padding: 0 0.75rem 0 0;
          }
        }
      }
    }
  }

  .action_btn {
    min-height: 5.5rem;
    line-height: 5.5rem;
    border: 1px solid #dfdfe3;
    border-width: 0 0 1px 0;
    text-align: center;
  }

  .team_btn {
    min-height: 5.5rem;
    line-height: 5.5rem;
    border: 1px solid #dfdfe3;
    border-width: 0 0 1px 0;
    padding: 0 0 0 2rem;
  }

  .action_data {
    font-size: 14px;
    line-height: 3rem;

    .duty {
      height: 4rem;
      padding: 1rem 0 0 2rem;
      border: 1px solid #dfdfe3;
      border-width: 0 0 1px 0;

      span {
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%;
        height: 4rem;
        padding-right: 1rem;
      }
    }

    .action_right {
      padding: 0.5rem 0 1rem 2rem;
      border: 1px solid #dfdfe3;
      border-width: 0 0 1px 0;

      &:last-child {
        border: 0;
      }

      .action_title {
        line-height: 2rem;
        font-size: 1rem;
        color: #599047;
        overflow: hidden;
        text-align: center;
        margin-top: 2.5rem;
        > span {
          width: 2rem;
          color: #000;
        }
      }

      .online {
        display: inline-block;
        margin: 0px;
        position: relative;
        left: 0;
        top: 1.2rem;
        height: 6.5rem;
        width: 2rem;
        border: 1px solid #dfdfe3;
        border-width: 1px 0 1px 1px;

        &:before {
          content: "";
          position: absolute;
          left: -114%;
          top: 50%;
          height: 1px;
          width: 4rem;
          border: 1px solid #dfdfe3;
          border-width: 0 0 1px 0;
        }
      }
    }
  }

  .team_com {
    display: inline;
  }

  .team_text {
    height: 3rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .parent_title {
    color: #909399;
    text-align: center;
    font-size: 0.6rem;
    height: 20px;
    line-height: 20px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>