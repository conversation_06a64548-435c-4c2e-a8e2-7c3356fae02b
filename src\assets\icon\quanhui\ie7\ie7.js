/* To avoid CSS expressions while still supporting IE 7 and IE 6, use this script */
/* The script tag referencing this file must be placed before the ending body tag. */

/* Use conditional comments in order to target IE 7 and older:
	<!--[if lt IE 8]><!-->
	<script src="ie7/ie7.js"></script>
	<!--<![endif]-->
*/

(function() {
	function addIcon(el, entity) {
		var html = el.innerHTML;
		el.innerHTML = '<span style="font-family: \'quanhui\'">' + entity + '</span>' + html;
	}
	var icons = {
		'icon-lock2': '&#xe986;',
		'icon-user2': '&#xe989;',
		'icon-shrinkPic3': '&#xe98a;',
		'icon-gas3': '&#xe98b;',
		'icon-pullleft': '&#xe98c;',
		'icon-pullRight': '&#xe98d;',
		'icon-morePic': '&#xe98e;',
		'icon-pulldown': '&#xe98f;',
		'icon-pullup': '&#xe990;',
		'icon-lastPage': '&#xe992;',
		'icon-firstPage': '&#xe993;',
		'icon-loginUser2': '&#xe995;',
		'icon-exitPic': '&#xe996;',
		'icon-setup': '&#xe997;',
		'icon-fold': '&#xe998;',
		'icon-unfold': '&#xe999;',
		'icon-firmCount': '&#xe9a4;',
		'icon-accident-analysis': '&#xe99a;',
		'icon-accident-management': '&#xe99b;',
		'icon-canlendar': '&#xe99c;',
		'icon-contingency-plan': '&#xe99d;',
		'icon-decision': '&#xe99e;',
		'icon-grid2': '&#xe9a0;',
		'icon-info2': '&#xe9a1;',
		'icon-person': '&#xe9a2;',
		'icon-Information-reception': '&#xe9a3;',
		'icon-uniE90F': '&#xe9a5;',
		'icon-home4': '&#xe9a7;',
		'icon-small-message': '&#xe9a8;',
		'icon-train': '&#xe9a9;',
		'icon-plan': '&#xe9aa;',
		'icon-search22': '&#xe9ac;',
		'icon-cross4': '&#xe9ad;',
		'icon-weater': '&#xe9ae;',
		'icon-talk2': '&#xe9af;',
		'icon-screen2': '&#xe9b0;',
		'icon-building': '&#xe9b1;',
		'icon-note': '&#xe9b2;',
		'icon-help': '&#xe9b3;',
		'icon-contacts2': '&#xe9b4;',
		'icon-emergencyResponse': '&#xe9b5;',
		'icon-situation': '&#xe9b6;',
		'icon-bookOpen2': '&#xe9bb;',
		'icon-picture': '&#xe9bc;',
		'icon-arrowSouth': '&#xe9be;',
		'icon-exit3': '&#xe9bf;',
		'icon-lock22': '&#xe9c1;',
		'icon-system': '&#xe9c5;',
		'icon-traffic': '&#xe9ca;',
		'icon-zhishengji': '&#xe9cb;',
		'icon-data': '&#xe9cc;',
		'icon-facsimile2': '&#xe9cd;',
		'icon-address-book-add': '&#xe921;',
		'icon-address-book-amplification': '&#xe922;',
		'icon-address-book-book': '&#xe923;',
		'icon-address-book-delete': '&#xe924;',
		'icon-address-book-edit': '&#xe925;',
		'icon-address-book-edit1': '&#xe926;',
		'icon-address-book-list': '&#xe927;',
		'icon-address-book-subtract': '&#xe928;',
		'icon-address-book-view': '&#xe929;',
		'icon-clock': '&#xe92a;',
		'icon-delete': '&#xe92b;',
		'icon-details': '&#xe92c;',
		'icon-download': '&#xe92d;',
		'icon-download1': '&#xe92e;',
		'icon-edit': '&#xe92f;',
		'icon-location': '&#xe930;',
		'icon-Notice': '&#xe931;',
		'icon-search_Search': '&#xe932;',
		'icon-Search-delete': '&#xe933;',
		'icon-mail': '&#xe934;',
		'icon-alarm': '&#xe935;',
		'icon-homepage': '&#xe936;',
		'icon-menu': '&#xe937;',
		'icon-enterprise': '&#xe900;',
		'icon-Alarm-history': '&#xe901;',
		'icon-business-configuration': '&#xe902;',
		'icon-correspondence': '&#xe903;',
		'icon-data-statistics': '&#xe904;',
		'icon-duty-allocation': '&#xe905;',
		'icon-duty-management': '&#xe906;',
		'icon-duty-statistics': '&#xe907;',
		'icon-Entry-exit-management': '&#xe908;',
		'icon-essential-information': '&#xe909;',
		'icon-expert': '&#xe90a;',
		'icon-fault-dimension-detection': '&#xe90b;',
		'icon-gas': '&#xe90c;',
		'icon-hazard-source': '&#xe90d;',
		'icon-holiday-management': '&#xe90e;',
		'icon-Information-filing': '&#xe90f;',
		'icon-Information-Delivery': '&#xe910;',
		'icon-knowledge-base': '&#xe911;',
		'icon-log-management': '&#xe912;',
		'icon-major-hazard-sources': '&#xe913;',
		'icon-Material-quipment': '&#xe914;',
		'icon-meteorological': '&#xe915;',
		'icon-monitoring-management': '&#xe916;',
		'icon-pipeline': '&#xe917;',
		'icon-production-place': '&#xe918;',
		'icon-release-strategy': '&#xe919;',
		'icon-rescue-team': '&#xe91a;',
		'icon-reserve-plan': '&#xe91b;',
		'icon-search': '&#xe91c;',
		'icon-sewage-monitoring': '&#xe91d;',
		'icon-Storage-tank': '&#xe91e;',
		'icon-Video-management': '&#xe91f;',
		'icon-warehouse': '&#xe920;',
		'icon-play_arrow': '&#xe037;',
		'icon-play_circle_outline': '&#xe039;',
		'icon-videocam': '&#xe04b;',
		'icon-volume_down2': '&#xe04e;',
		'icon-volume_up': '&#xe050;',
		'icon-reply_all': '&#xe15f;',
		'icon-desktop_windows': '&#xe30c;',
		'icon-laptop3': '&#xe31e;',
		'icon-laptop_chromebook': '&#xe31f;',
		'icon-phone_iphone': '&#xe325;',
		'icon-security': '&#xe32a;',
		'icon-speaker': '&#xe32d;',
		'icon-center_focus_strong': '&#xe3b4;',
		'icon-directions_walk': '&#xe536;',
		'icon-layers': '&#xe53b;',
		'icon-local_hospital': '&#xe548;',
		'icon-local_hotel': '&#xe549;',
		'icon-local_pharmacy': '&#xe550;',
		'icon-directions_run': '&#xe566;',
		'icon-add_location': '&#xe567;',
		'icon-feedback': '&#xe613;',
		'icon-sign-in': '&#xe61a;',
		'icon-sign-out': '&#xe61b;',
		'icon-sign-up': '&#xe61c;',
		'icon-sign-down': '&#xe61d;',
		'icon-teamManagement': '&#xe62c;',
		'icon-user-add': '&#xe62d;',
		'icon-rep-send': '&#xe62e;',
		'icon-airline_seat_flat': '&#xe63a;',
		'icon-sort-pic': '&#xe641;',
		'icon-lock_open': '&#xe898;',
		'icon-lock_outline': '&#xe899;',
		'icon-visibility': '&#xe8f4;',
		'icon-visibility_off': '&#xe8f5;',
		'icon-message': '&#xe938;',
		'icon-task': '&#xe939;',
		'icon-tools': '&#xe93a;',
		'icon-plotting': '&#xe93b;',
		'icon-screen': '&#xe93c;',
		'icon-name': '&#xe93d;',
		'icon-scene': '&#xe93e;',
		'icon-play': '&#xe93f;',
		'icon-next': '&#xe940;',
		'icon-back': '&#xe941;',
		'icon-pause': '&#xe942;',
		'icon-medical': '&#xe943;',
		'icon-talk': '&#xe944;',
		'icon-send': '&#xe945;',
		'icon-recive': '&#xe946;',
		'icon-sendmessages': '&#xe947;',
		'icon-run': '&#xe948;',
		'icon-stop': '&#xe949;',
		'icon-transend': '&#xe94a;',
		'icon-wolk': '&#xe94b;',
		'icon-user32': '&#xe94c;',
		'icon-leader4': '&#xe94d;',
		'icon-address': '&#xe94e;',
		'icon-author': '&#xe94f;',
		'icon-casePlan': '&#xe950;',
		'icon-workPlan': '&#xe951;',
		'icon-gas2': '&#xe952;',
		'icon-smallMessage': '&#xe953;',
		'icon-garden': '&#xe954;',
		'icon-coal': '&#xe955;',
		'icon-dangerous': '&#xe956;',
		'icon-search2': '&#xe957;',
		'icon-danger': '&#xe958;',
		'icon-cross': '&#xe959;',
		'icon-dataManage': '&#xe95a;',
		'icon-dataShare': '&#xe95b;',
		'icon-draft': '&#xe95c;',
		'icon-collection': '&#xe95d;',
		'icon-listEdit': '&#xe95e;',
		'icon-knowledge': '&#xe95f;',
		'icon-exercise': '&#xe960;',
		'icon-rescuePlay': '&#xe961;',
		'icon-firm': '&#xe962;',
		'icon-equip': '&#xe963;',
		'icon-grid': '&#xe964;',
		'icon-home': '&#xe965;',
		'icon-manage': '&#xe966;',
		'icon-keyCounty': '&#xe967;',
		'icon-sceneInfo': '&#xe968;',
		'icon-opinion': '&#xe969;',
		'icon-process': '&#xe96a;',
		'icon-reportInfo': '&#xe96b;',
		'icon-reportUnit': '&#xe96c;',
		'icon-resource': '&#xe96d;',
		'icon-authManage': '&#xe96e;',
		'icon-browse': '&#xe96f;',
		'icon-serSource': '&#xe970;',
		'icon-apperSite': '&#xe971;',
		'icon-document': '&#xe972;',
		'icon-allList': '&#xe973;',
		'icon-home3': '&#xe974;',
		'icon-matter': '&#xe975;',
		'icon-tel': '&#xe976;',
		'icon-capacity': '&#xe977;',
		'icon-injuries': '&#xe978;',
		'icon-taskPlan': '&#xe979;',
		'icon-loginUser': '&#xe97a;',
		'icon-yjManage': '&#xe97b;',
		'icon-exit': '&#xe97c;',
		'icon-facsimile': '&#xe97d;',
		'icon-contacts': '&#xe97e;',
		'icon-mobile': '&#xe97f;',
		'icon-bookOpen': '&#xe980;',
		'icon-rollPaper': '&#xe981;',
		'icon-del': '&#xe982;',
		'icon-up-load': '&#xe983;',
		'icon-upload': '&#xe984;',
		'icon-tradePic': '&#xe985;',
		'icon-folder-open': '&#xe987;',
		'icon-folder-plus': '&#xe988;',
		'icon-highTrain': '&#xe991;',
		'icon-specialPic': '&#xe994;',
		'icon-set': '&#xe99f;',
		'icon-trash2': '&#xe9a6;',
		'icon-textNote': '&#xe9ab;',
		'icon-download2': '&#xe9b7;',
		'icon-cancel': '&#xe9b8;',
		'icon-release': '&#xe9b9;',
		'icon-upload2': '&#xe9ba;',
		'icon-play2': '&#xe9bd;',
		'icon-modify': '&#xe9c0;',
		'icon-monitor': '&#xe9c2;',
		'icon-bubbles': '&#xe9c3;',
		'icon-bubbles2': '&#xe9c4;',
		'icon-online': '&#xe9c6;',
		'icon-bubbles3': '&#xe9c7;',
		'icon-bubbles4': '&#xe9c8;',
		'icon-location2': '&#xe9c9;',
		'icon-accAnalyse': '&#xe9cf;',
		'icon-shrinkLeft': '&#xe9d6;',
		'icon-shrinkRight': '&#xe9d7;',
		'icon-claSearch': '&#xe9e3;',
		'icon-tube': '&#xe9ed;',
		'icon-medical2': '&#xe9f2;',
		'icon-payTarget': '&#xe9f6;',
		'icon-exercise2': '&#xe9f9;',
		'icon-emerDuty': '&#xe9fc;',
		'icon-emerRes': '&#xe9fe;',
		'icon-notePic': '&#xea08;',
		'icon-umbrella': '&#xf0e9;',
		'icon-plus-square': '&#xf0fe;',
		'icon-wheelchair': '&#xf193;',
		'icon-pie-chart3': '&#xf200;',
		'icon-bus': '&#xf207;',
		'icon-ship': '&#xf21a;',
		'icon-subway': '&#xf239;',
		'icon-cheliangguanli': '&#xebcf;',
		'icon-anquanxian': '&#xebce;',
		'icon-chuanganqijiance': '&#xebcd;',
		'icon-chuanganqipeizhi': '&#xebcc;',
		'icon-dibiaoshui': '&#xebcb;',
		'icon-fengxian': '&#xebca;',
		'icon-guolu': '&#xebc9;',
		'icon-huanbaosheshi': '&#xebc8;',
		'icon-qitijiance': '&#xebc7;',
		'icon-jiancexiang': '&#xebc6;',
		'icon-wuzi': '&#xebc5;',
		'icon-yanliguandao': '&#xebc4;',
		'icon-yanlirongqi': '&#xebc3;',
		'icon-yiyuan': '&#xebc2;',
		'icon-home2': '&#xe9ce;',
		'icon-home22': '&#xe9d0;',
		'icon-home32': '&#xe9d1;',
		'icon-office': '&#xe9d2;',
		'icon-newspaper': '&#xe9d3;',
		'icon-pencil': '&#xe9d4;',
		'icon-pencil2': '&#xe9d5;',
		'icon-quill': '&#xe9d8;',
		'icon-pen': '&#xe9d9;',
		'icon-blog': '&#xe9da;',
		'icon-droplet': '&#xe9db;',
		'icon-paint-format': '&#xe9dc;',
		'icon-image': '&#xe9dd;',
		'icon-images': '&#xe9de;',
		'icon-camera': '&#xe9df;',
		'icon-headphones': '&#xe9e0;',
		'icon-music': '&#xe9e1;',
		'icon-play3': '&#xe9e2;',
		'icon-film': '&#xe9e4;',
		'icon-video-camera': '&#xe9e5;',
		'icon-dice': '&#xe9e6;',
		'icon-pacman': '&#xe9e7;',
		'icon-clubs': '&#xe9e8;',
		'icon-diamonds': '&#xe9e9;',
		'icon-bullhorn': '&#xe9ea;',
		'icon-connection': '&#xe9eb;',
		'icon-podcast': '&#xe9ec;',
		'icon-feed': '&#xe9ee;',
		'icon-mic': '&#xe9ef;',
		'icon-book': '&#xe9f0;',
		'icon-books': '&#xe9f1;',
		'icon-library': '&#xe9f3;',
		'icon-file-text': '&#xe9f4;',
		'icon-profile': '&#xe9f5;',
		'icon-file-empty': '&#xe9f7;',
		'icon-files-empty': '&#xe9f8;',
		'icon-file-text2': '&#xe9fa;',
		'icon-file-picture': '&#xe9fb;',
		'icon-file-music': '&#xe9fd;',
		'icon-file-play': '&#xe9ff;',
		'icon-file-video': '&#xea00;',
		'icon-file-zip': '&#xea01;',
		'icon-copy': '&#xea02;',
		'icon-paste': '&#xea03;',
		'icon-stack': '&#xea04;',
		'icon-folder': '&#xea05;',
		'icon-folder-open2': '&#xea06;',
		'icon-folder-plus2': '&#xea07;',
		'icon-folder-minus': '&#xea09;',
		'icon-folder-download': '&#xea0a;',
		'icon-folder-upload': '&#xea0b;',
		'icon-price-tag': '&#xea0c;',
		'icon-price-tags': '&#xea0d;',
		'icon-barcode': '&#xea0e;',
		'icon-qrcode': '&#xea0f;',
		'icon-ticket': '&#xea10;',
		'icon-cart': '&#xea11;',
		'icon-coin-dollar': '&#xea12;',
		'icon-coin-euro': '&#xea13;',
		'icon-coin-pound': '&#xea14;',
		'icon-coin-yen': '&#xea15;',
		'icon-credit-card': '&#xea16;',
		'icon-calculator': '&#xea17;',
		'icon-lifebuoy': '&#xea18;',
		'icon-phone': '&#xea19;',
		'icon-phone-hang-up': '&#xea1a;',
		'icon-address-book': '&#xea1b;',
		'icon-envelop': '&#xea1c;',
		'icon-pushpin': '&#xea1d;',
		'icon-location3': '&#xea1e;',
		'icon-location22': '&#xea1f;',
		'icon-compass': '&#xea20;',
		'icon-compass2': '&#xea21;',
		'icon-map': '&#xea22;',
		'icon-map2': '&#xea23;',
		'icon-history': '&#xea24;',
		'icon-clock2': '&#xea25;',
		'icon-clock22': '&#xea26;',
		'icon-alarm2': '&#xea27;',
		'icon-bell': '&#xea28;',
		'icon-stopwatch': '&#xea29;',
		'icon-calendar': '&#xea2a;',
		'icon-printer': '&#xea2b;',
		'icon-keyboard': '&#xea2c;',
		'icon-display': '&#xea2d;',
		'icon-laptop': '&#xea2e;',
		'icon-mobile2': '&#xea2f;',
		'icon-mobile22': '&#xea30;',
		'icon-tablet': '&#xea31;',
		'icon-tv': '&#xea32;',
		'icon-drawer': '&#xea33;',
		'icon-drawer2': '&#xea34;',
		'icon-box-add': '&#xea35;',
		'icon-box-remove': '&#xea36;',
		'icon-download3': '&#xea37;',
		'icon-upload3': '&#xea38;',
		'icon-floppy-disk': '&#xea39;',
		'icon-drive': '&#xea3a;',
		'icon-database': '&#xea3b;',
		'icon-undo': '&#xea3c;',
		'icon-redo': '&#xea3d;',
		'icon-undo2': '&#xea3e;',
		'icon-redo2': '&#xea3f;',
		'icon-forward': '&#xea40;',
		'icon-reply': '&#xea41;',
		'icon-bubble': '&#xea42;',
		'icon-bubbles5': '&#xea43;',
		'icon-bubbles22': '&#xea44;',
		'icon-bubble2': '&#xea45;',
		'icon-bubbles32': '&#xea46;',
		'icon-bubbles42': '&#xea47;',
		'icon-user': '&#xea48;',
		'icon-users': '&#xea49;',
		'icon-user-plus': '&#xea4a;',
		'icon-user-minus': '&#xea4b;',
		'icon-user-check': '&#xea4c;',
		'icon-user-tie': '&#xea4d;',
		'icon-quotes-left': '&#xea4e;',
		'icon-quotes-right': '&#xea4f;',
		'icon-hour-glass': '&#xea50;',
		'icon-spinner': '&#xea51;',
		'icon-spinner2': '&#xea52;',
		'icon-spinner3': '&#xea53;',
		'icon-spinner4': '&#xea54;',
		'icon-spinner5': '&#xea55;',
		'icon-spinner6': '&#xea56;',
		'icon-spinner7': '&#xea57;',
		'icon-spinner8': '&#xea58;',
		'icon-spinner9': '&#xea59;',
		'icon-spinner10': '&#xea5a;',
		'icon-spinner11': '&#xea5b;',
		'icon-binoculars': '&#xea5c;',
		'icon-search3': '&#xea5d;',
		'icon-zoom-in': '&#xea5e;',
		'icon-zoom-out': '&#xea5f;',
		'icon-enlarge': '&#xea60;',
		'icon-shrink': '&#xea61;',
		'icon-enlarge2': '&#xea62;',
		'icon-shrink2': '&#xea63;',
		'icon-key': '&#xea64;',
		'icon-key2': '&#xea65;',
		'icon-lock': '&#xea66;',
		'icon-unlocked': '&#xea67;',
		'icon-wrench': '&#xea68;',
		'icon-equalizer': '&#xea69;',
		'icon-equalizer2': '&#xea6a;',
		'icon-cog': '&#xea6b;',
		'icon-cogs': '&#xea6c;',
		'icon-hammer': '&#xea6d;',
		'icon-magic-wand': '&#xea6e;',
		'icon-aid-kit': '&#xea6f;',
		'icon-bug': '&#xea70;',
		'icon-pie-chart': '&#xea71;',
		'icon-stats-dots': '&#xea72;',
		'icon-stats-bars': '&#xea73;',
		'icon-stats-bars2': '&#xea74;',
		'icon-trophy': '&#xea75;',
		'icon-gift': '&#xea76;',
		'icon-glass': '&#xea77;',
		'icon-glass2': '&#xea78;',
		'icon-mug': '&#xea79;',
		'icon-spoon-knife': '&#xea7a;',
		'icon-leaf': '&#xea7b;',
		'icon-rocket': '&#xea7c;',
		'icon-meter': '&#xea7d;',
		'icon-meter2': '&#xea7e;',
		'icon-hammer2': '&#xea7f;',
		'icon-fire': '&#xea80;',
		'icon-lab': '&#xea81;',
		'icon-magnet': '&#xea82;',
		'icon-bin': '&#xea83;',
		'icon-bin2': '&#xea84;',
		'icon-briefcase': '&#xea85;',
		'icon-airplane': '&#xea86;',
		'icon-truck': '&#xea87;',
		'icon-road': '&#xea88;',
		'icon-accessibility': '&#xea89;',
		'icon-target': '&#xea8a;',
		'icon-shield': '&#xea8b;',
		'icon-power': '&#xea8c;',
		'icon-switch': '&#xea8d;',
		'icon-power-cord': '&#xea8e;',
		'icon-clipboard': '&#xea8f;',
		'icon-list-numbered': '&#xea90;',
		'icon-list': '&#xea91;',
		'icon-list2': '&#xea92;',
		'icon-tree': '&#xea93;',
		'icon-menu2': '&#xea94;',
		'icon-menu22': '&#xea95;',
		'icon-menu3': '&#xea96;',
		'icon-menu4': '&#xea97;',
		'icon-cloud': '&#xea98;',
		'icon-cloud-download': '&#xea99;',
		'icon-cloud-upload': '&#xea9a;',
		'icon-cloud-check': '&#xea9b;',
		'icon-download22': '&#xea9c;',
		'icon-upload22': '&#xea9d;',
		'icon-download32': '&#xea9e;',
		'icon-upload32': '&#xea9f;',
		'icon-sphere': '&#xeaa0;',
		'icon-earth': '&#xeaa1;',
		'icon-link': '&#xeaa2;',
		'icon-flag': '&#xeaa3;',
		'icon-attachment': '&#xeaa4;',
		'icon-eye': '&#xeaa5;',
		'icon-eye-plus': '&#xeaa6;',
		'icon-eye-minus': '&#xeaa7;',
		'icon-eye-blocked': '&#xeaa8;',
		'icon-bookmark': '&#xeaa9;',
		'icon-bookmarks': '&#xeaaa;',
		'icon-sun': '&#xeaab;',
		'icon-contrast': '&#xeaac;',
		'icon-brightness-contrast': '&#xeaad;',
		'icon-star-empty': '&#xeaae;',
		'icon-star-half': '&#xeaaf;',
		'icon-star-full': '&#xeab0;',
		'icon-heart': '&#xeab1;',
		'icon-heart-broken': '&#xeab2;',
		'icon-man': '&#xeab3;',
		'icon-woman': '&#xeab4;',
		'icon-man-woman': '&#xeab5;',
		'icon-happy': '&#xeab6;',
		'icon-happy2': '&#xeab7;',
		'icon-smile': '&#xeab8;',
		'icon-smile2': '&#xeab9;',
		'icon-tongue': '&#xeaba;',
		'icon-tongue2': '&#xeabb;',
		'icon-sad': '&#xeabc;',
		'icon-sad2': '&#xeabd;',
		'icon-wink': '&#xeabe;',
		'icon-wink2': '&#xeabf;',
		'icon-grin': '&#xeac0;',
		'icon-grin2': '&#xeac1;',
		'icon-cool': '&#xeac2;',
		'icon-cool2': '&#xeac3;',
		'icon-angry': '&#xeac4;',
		'icon-angry2': '&#xeac5;',
		'icon-evil': '&#xeac6;',
		'icon-evil2': '&#xeac7;',
		'icon-shocked': '&#xeac8;',
		'icon-shocked2': '&#xeac9;',
		'icon-baffled': '&#xeaca;',
		'icon-baffled2': '&#xeacb;',
		'icon-confused': '&#xeacc;',
		'icon-confused2': '&#xeacd;',
		'icon-neutral': '&#xeace;',
		'icon-neutral2': '&#xeacf;',
		'icon-hipster': '&#xead0;',
		'icon-hipster2': '&#xead1;',
		'icon-wondering': '&#xead2;',
		'icon-wondering2': '&#xead3;',
		'icon-sleepy': '&#xead4;',
		'icon-sleepy2': '&#xead5;',
		'icon-frustrated': '&#xead6;',
		'icon-frustrated2': '&#xead7;',
		'icon-crying': '&#xead8;',
		'icon-crying2': '&#xead9;',
		'icon-point-up': '&#xeada;',
		'icon-point-right': '&#xeadb;',
		'icon-point-down': '&#xeadc;',
		'icon-point-left': '&#xeadd;',
		'icon-warning': '&#xeade;',
		'icon-notification': '&#xeadf;',
		'icon-question': '&#xeae0;',
		'icon-plus': '&#xeae1;',
		'icon-minus': '&#xeae2;',
		'icon-info': '&#xeae3;',
		'icon-cancel-circle': '&#xeae4;',
		'icon-blocked': '&#xeae5;',
		'icon-cross2': '&#xeae6;',
		'icon-checkmark': '&#xeae7;',
		'icon-checkmark2': '&#xeae8;',
		'icon-spell-check': '&#xeae9;',
		'icon-enter': '&#xeaea;',
		'icon-exit2': '&#xeaeb;',
		'icon-play22': '&#xeaec;',
		'icon-pause2': '&#xeaed;',
		'icon-stop2': '&#xeaee;',
		'icon-previous': '&#xeaef;',
		'icon-next2': '&#xeaf0;',
		'icon-backward': '&#xeaf1;',
		'icon-forward2': '&#xeaf2;',
		'icon-play32': '&#xeaf3;',
		'icon-pause22': '&#xeaf4;',
		'icon-stop22': '&#xeaf5;',
		'icon-backward2': '&#xeaf6;',
		'icon-forward3': '&#xeaf7;',
		'icon-first': '&#xeaf8;',
		'icon-last': '&#xeaf9;',
		'icon-previous2': '&#xeafa;',
		'icon-next22': '&#xeafb;',
		'icon-eject': '&#xeafc;',
		'icon-volume-high': '&#xeafd;',
		'icon-volume-medium': '&#xeafe;',
		'icon-volume-low': '&#xeaff;',
		'icon-volume-mute': '&#xeb00;',
		'icon-volume-mute2': '&#xeb01;',
		'icon-volume-increase': '&#xeb02;',
		'icon-volume-decrease': '&#xeb03;',
		'icon-loop': '&#xeb04;',
		'icon-loop2': '&#xeb05;',
		'icon-infinite': '&#xeb06;',
		'icon-shuffle': '&#xeb07;',
		'icon-arrow-up-left': '&#xeb08;',
		'icon-arrow-up': '&#xeb09;',
		'icon-arrow-up-right': '&#xeb0a;',
		'icon-arrow-right': '&#xeb0b;',
		'icon-arrow-down-right': '&#xeb0c;',
		'icon-arrow-down': '&#xeb0d;',
		'icon-arrow-down-left': '&#xeb0e;',
		'icon-arrow-left': '&#xeb0f;',
		'icon-arrow-up-left2': '&#xeb10;',
		'icon-arrow-up2': '&#xeb11;',
		'icon-arrow-up-right2': '&#xeb12;',
		'icon-arrow-right2': '&#xeb13;',
		'icon-arrow-down-right2': '&#xeb14;',
		'icon-arrow-down2': '&#xeb15;',
		'icon-arrow-down-left2': '&#xeb16;',
		'icon-arrow-left2': '&#xeb17;',
		'icon-circle-up': '&#xeb18;',
		'icon-circle-right': '&#xeb19;',
		'icon-circle-down': '&#xeb1a;',
		'icon-circle-left': '&#xeb1b;',
		'icon-tab': '&#xeb1c;',
		'icon-move-up': '&#xeb1d;',
		'icon-move-down': '&#xeb1e;',
		'icon-sort-alpha-asc': '&#xeb1f;',
		'icon-sort-alpha-desc': '&#xeb20;',
		'icon-sort-numeric-asc': '&#xeb21;',
		'icon-sort-numberic-desc': '&#xeb22;',
		'icon-sort-amount-asc': '&#xeb23;',
		'icon-sort-amount-desc': '&#xeb24;',
		'icon-command': '&#xeb25;',
		'icon-shift': '&#xeb26;',
		'icon-ctrl': '&#xeb27;',
		'icon-opt': '&#xeb28;',
		'icon-checkbox-checked': '&#xeb29;',
		'icon-checkbox-unchecked': '&#xeb2a;',
		'icon-radio-checked': '&#xeb2b;',
		'icon-radio-checked2': '&#xeb2c;',
		'icon-radio-unchecked': '&#xeb2d;',
		'icon-crop': '&#xeb2e;',
		'icon-make-group': '&#xeb2f;',
		'icon-ungroup': '&#xeb30;',
		'icon-scissors': '&#xeb31;',
		'icon-filter': '&#xeb32;',
		'icon-font': '&#xeb33;',
		'icon-ligature': '&#xeb34;',
		'icon-ligature2': '&#xeb35;',
		'icon-text-height': '&#xeb36;',
		'icon-text-width': '&#xeb37;',
		'icon-font-size': '&#xeb38;',
		'icon-bold': '&#xeb39;',
		'icon-underline': '&#xeb3a;',
		'icon-italic': '&#xeb3b;',
		'icon-strikethrough': '&#xeb3c;',
		'icon-omega': '&#xeb3d;',
		'icon-sigma': '&#xeb3e;',
		'icon-page-break': '&#xeb3f;',
		'icon-superscript': '&#xeb40;',
		'icon-subscript': '&#xeb41;',
		'icon-superscript2': '&#xeb42;',
		'icon-subscript2': '&#xeb43;',
		'icon-text-color': '&#xeb44;',
		'icon-pagebreak': '&#xeb45;',
		'icon-clear-formatting': '&#xeb46;',
		'icon-table': '&#xeb47;',
		'icon-table2': '&#xeb48;',
		'icon-insert-template': '&#xeb49;',
		'icon-pilcrow': '&#xeb4a;',
		'icon-ltr': '&#xeb4b;',
		'icon-rtl': '&#xeb4c;',
		'icon-section': '&#xeb4d;',
		'icon-paragraph-left': '&#xeb4e;',
		'icon-paragraph-center': '&#xeb4f;',
		'icon-paragraph-right': '&#xeb50;',
		'icon-paragraph-justify': '&#xeb51;',
		'icon-indent-increase': '&#xeb52;',
		'icon-indent-decrease': '&#xeb53;',
		'icon-share': '&#xeb54;',
		'icon-new-tab': '&#xeb55;',
		'icon-embed': '&#xeb56;',
		'icon-embed2': '&#xeb57;',
		'icon-terminal': '&#xeb58;',
		'icon-share2': '&#xeb59;',
		'icon-mail2': '&#xeb5a;',
		'icon-mail22': '&#xeb5b;',
		'icon-mail3': '&#xeb5c;',
		'icon-mail4': '&#xeb5d;',
		'icon-amazon': '&#xeb5e;',
		'icon-google': '&#xeb5f;',
		'icon-google2': '&#xeb60;',
		'icon-google3': '&#xeb61;',
		'icon-google-plus': '&#xeb62;',
		'icon-google-plus2': '&#xeb63;',
		'icon-google-plus3': '&#xeb64;',
		'icon-hangouts': '&#xeb65;',
		'icon-google-drive': '&#xeb66;',
		'icon-facebook': '&#xeb67;',
		'icon-facebook2': '&#xeb68;',
		'icon-instagram': '&#xeb69;',
		'icon-whatsapp': '&#xeb6a;',
		'icon-spotify': '&#xeb6b;',
		'icon-telegram': '&#xeb6c;',
		'icon-twitter': '&#xeb6d;',
		'icon-vine': '&#xeb6e;',
		'icon-vk': '&#xeb6f;',
		'icon-renren': '&#xeb70;',
		'icon-sina-weibo': '&#xeb71;',
		'icon-rss': '&#xeb72;',
		'icon-rss2': '&#xeb73;',
		'icon-youtube': '&#xeb74;',
		'icon-youtube2': '&#xeb75;',
		'icon-twitch': '&#xeb76;',
		'icon-vimeo': '&#xeb77;',
		'icon-vimeo2': '&#xeb78;',
		'icon-lanyrd': '&#xeb79;',
		'icon-flickr': '&#xeb7a;',
		'icon-flickr2': '&#xeb7b;',
		'icon-flickr3': '&#xeb7c;',
		'icon-flickr4': '&#xeb7d;',
		'icon-dribbble': '&#xeb7e;',
		'icon-behance': '&#xeb7f;',
		'icon-behance2': '&#xeb80;',
		'icon-deviantart': '&#xeb81;',
		'icon-500px': '&#xeb82;',
		'icon-steam': '&#xeb83;',
		'icon-steam2': '&#xeb84;',
		'icon-dropbox': '&#xeb85;',
		'icon-onedrive': '&#xeb86;',
		'icon-github': '&#xeb87;',
		'icon-npm': '&#xeb88;',
		'icon-basecamp': '&#xeb89;',
		'icon-trello': '&#xeb8a;',
		'icon-wordpress': '&#xeb8b;',
		'icon-joomla': '&#xeb8c;',
		'icon-ello': '&#xeb8d;',
		'icon-blogger': '&#xeb8e;',
		'icon-blogger2': '&#xeb8f;',
		'icon-tumblr': '&#xeb90;',
		'icon-tumblr2': '&#xeb91;',
		'icon-yahoo': '&#xeb92;',
		'icon-yahoo2': '&#xeb93;',
		'icon-tux': '&#xeb94;',
		'icon-appleinc': '&#xeb95;',
		'icon-finder': '&#xeb96;',
		'icon-android': '&#xeb97;',
		'icon-windows': '&#xeb98;',
		'icon-windows8': '&#xeb99;',
		'icon-soundcloud': '&#xeb9a;',
		'icon-soundcloud2': '&#xeb9b;',
		'icon-skype': '&#xeb9c;',
		'icon-reddit': '&#xeb9d;',
		'icon-hackernews': '&#xeb9e;',
		'icon-wikipedia': '&#xeb9f;',
		'icon-linkedin': '&#xeba0;',
		'icon-linkedin2': '&#xeba1;',
		'icon-lastfm': '&#xeba2;',
		'icon-lastfm2': '&#xeba3;',
		'icon-delicious': '&#xeba4;',
		'icon-stumbleupon': '&#xeba5;',
		'icon-stumbleupon2': '&#xeba6;',
		'icon-stackoverflow': '&#xeba7;',
		'icon-pinterest': '&#xeba8;',
		'icon-pinterest2': '&#xeba9;',
		'icon-xing': '&#xebaa;',
		'icon-xing2': '&#xebab;',
		'icon-flattr': '&#xebac;',
		'icon-foursquare': '&#xebad;',
		'icon-yelp': '&#xebae;',
		'icon-paypal': '&#xebaf;',
		'icon-chrome': '&#xebb0;',
		'icon-firefox': '&#xebb1;',
		'icon-IE': '&#xebb2;',
		'icon-edge': '&#xebb3;',
		'icon-safari': '&#xebb4;',
		'icon-opera': '&#xebb5;',
		'icon-file-pdf': '&#xebb6;',
		'icon-file-openoffice': '&#xebb7;',
		'icon-file-word': '&#xebb8;',
		'icon-file-excel': '&#xebb9;',
		'icon-libreoffice': '&#xebba;',
		'icon-html-five': '&#xebbb;',
		'icon-html-five2': '&#xebbc;',
		'icon-css3': '&#xebbd;',
		'icon-git': '&#xebbe;',
		'icon-codepen': '&#xebbf;',
		'icon-svg': '&#xebc0;',
		'icon-IcoMoon': '&#xebc1;',
		'0': 0
		},
		els = document.getElementsByTagName('*'),
		i, c, el;
	for (i = 0; ; i += 1) {
		el = els[i];
		if(!el) {
			break;
		}
		c = el.className;
		c = c.match(/icon-[^\s'"]+/);
		if (c && icons[c[0]]) {
			addIcon(el, icons[c[0]]);
		}
	}
}());
