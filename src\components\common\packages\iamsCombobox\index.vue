<template>
  <div>
    <div class="search_slide_input checkbox_combobox">
      <!-- <el-input @focus="searchKey = false" @input="changeSearchStatus" readonly ref="input" :disabled='disabled'
            v-model.trim="slideValue" type="text"
            :suffix-icon="!disabled ? (showSlide?'el-icon--right el-icon-arrow-up':'el-icon--right el-icon-arrow-down') : ''"
            :placeholder="placeholder">
        </el-input> -->
      <el-input
        @focus="searchKey = false"
        placeholder="请选择"
        @input="changeSearchStatus"
        ref="input"
        v-model.trim="slideValue"
        type="text"
        :disabled="disabled"
      >
        <i
          slot="suffix"
          @click.stop="changeExpand"
          :class="[
            showSlide ? 'el-icon-arrow-up' : 'el-icon-arrow-down',
            slide_icon,
          ]"
        ></i>
      </el-input>
      <div class="search_slide" v-show="showSlide && !disabled">
        <div class="tabs tabsAdd" v-show="!searchKey" style="height: 294px">
          <el-scrollbar>
            <el-tree
              ref="tree"
              :default-checked-keys="
                typeof defaultchecked == 'string'
                  ? [defaultchecked]
                  : defaultchecked
              "
              :default-expanded-keys="expanded"
              :check-strictly="rootcheck"
              :data="treeData"
              node-key="id"
              show-checkbox
              :props="getDefaultProps"
              @check="handleNodeClick"
            >
            </el-tree>
            <!--<el-tree v-else ref="tree" class="lazy_tree"-->
            <!--:default-checked-keys="typeof(defaultchecked) == 'string'?[defaultchecked]:defaultchecked"-->
            <!--:default-expanded-keys="expanded"-->
            <!--:check-strictly="rootcheck" :data="treeData" node-key="id" :props="getDefaultProps"-->
            <!--&gt;-->
            <!--<span slot-scope="scope">-->
            <!--<i v-if="!scope.data.leaf" @click.stop="treeLoad(scope.data,scope.node)"-->
            <!--:class="[!!scope.node.expanded && scope.data.children.length >0 ?'el-icon-caret-bottom':'el-icon-caret-right',temp.style.tree_icon]"></i>-->
            <!--<i v-else style="width:30px;height:30px;display: inline-block;"></i>-->
            <!--<el-checkbox v-if="!scope.data.virtualNode" v-model="scope.data['checked']" @change.self="lazyHandleClick(scope.node)">{{scope.node.label}}</el-checkbox>-->
            <!--<span v-else style="font-size:14px">{{scope.node.label}}</span>-->
            <!--</span>-->
            <!--</el-tree>-->
          </el-scrollbar>
        </div>
        <el-tabs class="tabs" style="display: none">
          <!--<el-tab-pane label="常用">-->
          <!--<el-scrollbar>-->
          <!--<ul :class="temp.style.filter_list">-->
          <!--<li v-if="listData.length === 0" style="text-align: center;">暂无数据</li>-->
          <!--<li :class="index == commonSelectIndex?temp.style.active:''" v-for="item,index in listData"-->
          <!--@click="handleSelect(item,index)">{{item.label}}</li>-->
          <!--</ul>-->
          <!--</el-scrollbar>-->
          <!--</el-tab-pane>-->
          <el-tab-pane label="全部">
            <!--<el-scrollbar>-->
            <!--<el-tree v-if="!islazy" ref="tree"-->
            <!--:default-checked-keys="typeof(defaultchecked) == 'string'?[defaultchecked]:defaultchecked"-->
            <!--:check-strictly="rootcheck" :data="treeData" node-key="id" show-checkbox-->
            <!--:props="getDefaultProps" @check="handleNodeClick">-->
            <!--</el-tree>-->
            <!--<el-tree v-else ref="tree" class="lazy_tree"-->
            <!--:default-checked-keys="typeof(defaultchecked) == 'string'?[defaultchecked]:defaultchecked"-->
            <!--:check-strictly="rootcheck" :data="treeData" node-key="id" :props="getDefaultProps"-->
            <!--&gt;-->
            <!--<span slot-scope="scope">-->
            <!--<i v-if="!scope.data.leaf" @click.stop="treeLoad(scope.data,scope.node)"-->
            <!--:class="[!!scope.node.expanded && scope.data.children.length >0 ?'el-icon-caret-bottom':'el-icon-caret-right',temp.style.tree_icon]"></i>-->
            <!--<i v-else style="width:30px;height:30px;display: inline-block;"></i>-->
            <!--<el-checkbox v-if="!scope.data.virtualNode" v-model="scope.data['checked']" @change.self="lazyHandleClick(scope.node)">{{scope.node.label}}</el-checkbox>-->
            <!--<span v-else style="font-size:14px">{{scope.node.label}}</span>-->
            <!--</span>-->
            <!--</el-tree>-->
            <!--</el-scrollbar>-->
          </el-tab-pane>
        </el-tabs>
        <div class="tabs" v-show="searchKey" style="height: 294px">
          <el-scrollbar>
            <ul class="filter_list">
              <div style="text-align: center; height: 12px">
                <span v-if="searchList.length == 0"> 未搜索到数据 </span>
              </div>
              <!-- {{item.label}}-->
              <li
                class="index == commonSelectIndex?active:''"
                v-for="(item, index) in searchList"
                @click="handleSelect(item, index)"
              >
                {{ item.label
                }}{{ item.parentLabel ? "(" + item.parentLabel + ")" : "" }}
              </li>
            </ul>
          </el-scrollbar>
        </div>
        <div class="popper__arrow" style="left: 35px"></div>
      </div>
    </div>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
// import { queryAttachmentInfoById, downloadUrl } from '@/api/notice/notice'
import downloadFuc from "@/api/download/download.js";
export default {
  name: "iamsCombobox",
  props: {
    listtype: {
      type: String,
      default: "",
    },
    defaultchecked: {
      type: Array,
      default: () => [],
    },
    placeholder: {
      type: String,
      default: "",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    rootcheck: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    parentprop: {
      type: String,
      default: "",
    },
    lazymethod: {
      type: String,
      default: "",
    },
    configdata: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      //   uploadFileUrl: process.env.VUE_APP_BASE_API + '/common/uploadGeFile',
      //   headers: {
      //     Authorization: 'Bearer ' + getToken()
      //   },
      //   fileList: [],
      //   list: []
      slideValue: "",
      showSlide: false,
      commonSelectIndex: -1,
      listData: [],
      treeData: [],
      searchKey: false,
      searchList: [],
      lazyCheckNodes: [],
      getDefaultProps: {
        children: "children",
        label: "label",
      },
      expanded: [],
    };
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  watch: {
    value() {
      if (this.value) {
        this.queryAttachmentInfoByIdFun(this.value);
      } else {
        this.fileList = [];
        this.list = [];
      }
    },
  },
  created() {
    this.getTreeData(this.listtype);
    this.bind(this);
  },
  methods: {
    getTreeData(method) {
      // queryAttachmentInfoById({ attachmentId: id }).then((res) => {
      //     if (res.data.treeData[0].parentId == '43000') {
      //         res.data.treeData.forEach(function (item) {
      //             if (item.children != null) {
      //                 let parentLabel = item.label;
      //                 item.children.forEach(function (list) {
      //                     list.parentLabel = parentLabel;
      //                 });
      //             }
      //         });
      //     }
      //     let treeData = res.data.treeData || res.data;
      //     if (this.configdata && this.configdata['disabledorg']) {
      //         this.signTreeData(treeData);
      //     }
      //     this.expanded = [treeData[0].id];
      //     this.$set(this, 'treeData', treeData);
      //     this.listData = res.data.commonData || [];
      //     let that = this;
      //     if (this.defaultchecked && this.defaultchecked.length > 0) {
      //         if (typeof this.defaultchecked == 'string') {
      //         this.defaultInputVal(this.defaultchecked, this.treeData);
      //         } else {
      //         this.defaultCheckedMultiple(this.defaultchecked, this.treeData);
      //         }
      //     }
      // });
      this.treeData = [
        {
          label: "一级 1",
          children: [
            {
              label: "二级 1-1",
              children: [
                {
                  label: "三级 1-1-1",
                },
              ],
            },
          ],
        },
        {
          label: "一级 2",
          children: [
            {
              label: "二级 2-1",
              children: [
                {
                  label: "三级 2-1-1",
                },
              ],
            },
            {
              label: "二级 2-2",
              children: [
                {
                  label: "三级 2-2-1",
                },
              ],
            },
          ],
        },
        {
          label: "一级 3",
          children: [
            {
              label: "二级 3-1",
              children: [
                {
                  label: "三级 3-1-1",
                },
              ],
            },
            {
              label: "二级 3-2",
              children: [
                {
                  label: "三级 3-2-1",
                },
              ],
            },
          ],
        },
      ];
    },
    // 树组件点击回调
    handleNodeClick(item, node) {
      let checkArr = this.$refs.tree["getCheckedKeys"]();
      this.slideValue = "";
      if (this.rootcheck && !this.multiple) {
        this.slideValue = item.label;
        this.selectSlideChange(item.id);
        this.slideChange(item.id);
        if (checkArr.length > 1) {
          let arr = [];
          arr.push(item.id);
          this.$refs.tree["setCheckedKeys"](arr);
        }
      } else {
        let nodeArr = JSON.parse(JSON.stringify(node.checkedKeys));
        node.checkedNodes.forEach((item, index) => {
          if (!item.disabled) {
            this.slideValue += item.label;
            if (index < node.checkedNodes.length - 1) {
              this.slideValue += ",";
            }
          } else {
            nodeArr.splice(index, 1);
          }
        });
        this.selectSlideChange(nodeArr);
        this.slideChange(nodeArr);
      }
      // this.$refs.input['focus']();
      this.showSlide = !this.showSlide;
    },
    // 绑定全局点击
    bind(el) {
      function documentHandler(e) {
        try {
          let list = el.$el.getElementsByClassName("el-tab-pane")[0];
          if (el.$el.contains(e.target) && !list.contains(e.target)) {
            el.showSlide = true;
          } else {
            el.showSlide = false;
          }
        } catch (error) {}
      }
      document.addEventListener("click", documentHandler);
    },
    // 处理单选选中返回值回填
    defaultInputVal(valg, arr, flagArray) {
      for (let i = 0; i < arr.length; i++) {
        if (arr[i].id == val) {
          if (flagArray) {
            this.slideValue += arr[i].label + ",";
          } else {
            this.slideValue = arr[i].label;
          }
          break;
        } else if (arr[i].children) {
          if (flagArray) {
            this.defaultInputVal(val, arr[i].children, flagArray);
          } else {
            this.defaultInputVal(val, arr[i].children);
          }
        } else {
          continue;
        }
      }
    },
    // 多选选中值回填
    defaultCheckedMultiple(checkList, arr) {
      checkList.forEach((item) => {
        this.defaultInputVal(item, arr, true);
      });
      this.slideValue =
        this.slideValue.substring(this.slideValue.length - 1) == ","
          ? this.slideValue.substring(0, this.slideValue.length - 1)
          : this.slideValue;
      // this.slideValue=this.slideValue.substring(0,this.slideValue.length-1)
    },
    // 列表点击事件处理
    handleSelect(val, index) {
      this.commonSelectIndex = index;
      if (!this.multiple && this.rootcheck) {
        this.selectSlideChange(val.id);
        this.slideChange(val.id);
      } else {
        let arr = [];
        arr.push(val.id);
        this.selectSlideChange(arr);
        this.slideChange(arr);
      }
      this.slideValue = val.label;
      // this.$refs.input['focus']();
      this.$refs.tree["setCheckedKeys"]([val.id]);
    },
    // 搜索事件
    changeSearchStatus(e) {
      this.$refs.tree["setCheckedKeys"]([]);
      this.selectSlideChange("");
      this.slideChange("");
      if (this.slideValue != "") {
        this.searchList = [];
        let arr = this.treeDataFilter(this.slideValue, this.treeData);
        this.$set(this, "searchList", arr);
        this.searchKey = true;
      } else {
        this.searchKey = false;
      }
    },
    // 树形数据过滤器
    treeDataFilter(name, data) {
      let newArr = [];
      data.forEach((item) => {
        if (item.label.indexOf(name) > -1 && !item.children) {
          newArr.push(item);
        } else if (item.label.indexOf(name) > -1 && item.children) {
          newArr.push(item);
          newArr = newArr.concat(this.treeDataFilter(name, item.children));
        } else if (item.label.indexOf(name) == -1 && item.children) {
          newArr = newArr.concat(this.treeDataFilter(name, item.children));
        }
      });
      return newArr;
    },
    //行政区划节点不能选中方法
    signTreeData(data) {
      let arr = [];
      data.forEach((item) => {
        if (item.virtualNode == true) {
          item["disabled"] = true;
        }
        if (item.children && item.children.length > 0) {
          this.signTreeData(item.children);
        }
      });
      return arr;
    },
    // 机构懒加载
    treeLoad(data, node) {
      if (!!this.islazy) {
        if (this.$refs["tree"]["getNode"](data)["childNodes"].length == 0) {
          this.http.TreeNode.treeByCode(node.key).then((res) => {
            if (res.status == 200) {
              this.$refs["tree"]["updateKeyChildren"](node.key, res.data);
              node.expanded = true;
            }
          });
        } else {
          node.expanded = !node.expanded;
        }
      }
    },
    // 懒加载节点选中事件
    lazyHandleClick(data) {
      if (data.data.checked) {
        this.lazyCheckNodes.push(data.data.id);
        this.slideValue === ""
          ? (this.slideValue += data.data.label)
          : (this.slideValue += "," + data.data.label);
      } else {
        this.lazyCheckNodes.forEach((item, index) => {
          if (item === data.data.id) {
            this.lazyCheckNodes.splice(index, 1);
            let slideValueArr = this.slideValue.split(",");
            slideValueArr.forEach((cells, num) => {
              if (cells === data.data.label) {
                slideValueArr.splice(num, 1);
              }
            });
            this.slideValue = slideValueArr.join(",");
          }
        });
      }
      this.selectSlideChange(this.lazyCheckNodes);
    },
    changeExpand() {
      this.showSlide = !this.showSlide;
    },
    selectSlideChange(item) {
      return item;
    },
    slideChange(item) {
      return item;
    },
  },
};
</script>

<style scoped lang="scss">
.search_slide {
  position: absolute;
  transition: all 0.5s;
  top: 50px;
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  box-sizing: border-box;
  z-index: 11;

  .tabs {
    padding: 0 4px;
    .filter_list {
      li {
        cursor: pointer;
        color: #606266;
        font-size: 14px;
        padding-left: 18px;
        &:hover {
          background: #66b1ff;
          color: #fff;
        }

        &.active {
          background: #66b1ff;
          color: #fff;
        }
      }
    }
  }

  .popper__arrow {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 6px;
    -webkit-filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
    filter: drop-shadow(0 2px 12px rgba(0, 0, 0, 0.03));
    top: -6px;
    left: 50%;
    margin-right: 3px;
    border-top-width: 0;
    border-bottom-color: #ebeef5;

    &:after {
      content: "";
      position: absolute;
      display: block;
      width: 0;
      height: 0;
      border-color: transparent;
      border-style: solid;
      border-width: 6px;
      top: 1px;
      margin-left: -6px;
      border-top-width: 0;
      border-bottom-color: #fff;
    }
  }
}

.headerdrops {
  margin-left: 1.6rem;
  margin-bottom: 3px;
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #c0c4cc;
}

.tree_icon {
  color: #c0c4cc;
  font-size: 18px;
  padding: 6px;
}
</style>
