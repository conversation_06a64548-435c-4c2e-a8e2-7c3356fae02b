<template>
  <div class="video" v-loading="loading">
    <div class="header">
      <el-cascader
        v-show="this.$store.state.login.user.user_type == 'gov'"
        size="mini"
        placeholder="请选择地市/区县"
        :options="district"
        v-model="districtVal"
        :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
        clearable
        @change="handleChange"
        :show-all-levels="true"
        style="width: 190px"
      ></el-cascader>
      <el-input
        placeholder="请输入企业名称"
        style="width: 190px"
        size="mini"
        clearable
        v-model.trim="enterpriseName"
      ></el-input>
      <el-select
        v-model="isOnline"
        placeholder="请选择是否在线"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in options1"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="isReported"
        placeholder="请选择是否报备"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in options2"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getData()">查询</el-button>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      style="width: 100%"
      :height="500"
    >
      <el-table-column type="index" width="80" label="序号"></el-table-column>
      <el-table-column prop="distName" label="区划" width="180" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="enterName" label="企业名称" show-overflow-tooltip>
              <template slot-scope="{row}">
        <span type="text" style="color: #3977ea; cursor: pointer;" @click="goEnt(row)">{{row.enterName}}</span>
      </template>
      </el-table-column>
      <el-table-column prop="onlineCount" label="在线视频点位" width="120">
      </el-table-column>
      <el-table-column prop="offlineCount" label="离线视频点位" width="120">
      </el-table-column>
    </el-table>
    <div class="pagination">
            <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>

    </div>

  </div>
</template>

<script>
import { getOnlinePatrolQueryVideoDetails } from "@/api/riskAssessment";

export default {
  data() {
    return {
      tableData: [],
      district: this.$store.state.controler.district,
            options1: [
        { label: "在线", value: "0" },
        { label: "离线", value: "1" },
      ],
       options2: [
        { label: "已报备", value: "0" },
        { label: "未报备", value: "1" },
      ],
      value: "",
      currentPage:1,
      total: 0,
            enterpriseName: "",
      isOnline: "",
      isReported: "",
      districtVal:this.districtProps,
      loading:false
    };
  },
  props:["districtProps","isOnlineProps","isReportedProps"],
   watch:{
    districtProps:{
      handler(newVal,oldVal){
        this.districtVal = newVal;
      },
      immediate:true,
      deep:true
    },
    isOnlineProps:{
      handler(newVal,oldVal){
        this.isOnline = newVal;
      },
      immediate:true,
      deep:true
    },
    isReportedProps:{
      handler(newVal,oldVal){
        this.isReported = newVal;
      },
      immediate:true,
      deep:true
    }
  },
  methods: {
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterPid);
    },
    closeTable(){
      this.isReported = "";
      this.isOnline = "";
      this.districtVal = "";
      this.enterpriseName = "";
      this.currentPage = "";
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
     handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.getData()
    },
    getData() {
      this.loading = true;
      getOnlinePatrolQueryVideoDetails(
        {
          distParentCode: this.$store.state.login.userDistCode,
          current: this.currentPage,
          size: 10,
          distCode: this.districtVal,
          enterName: this.enterpriseName,
          isOnline: this.isOnline,
          isReported: this.isReported,
        }
      ).then((res) => {
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.video {
  .header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    & > * {
      margin-right: 20px;
    }
  }
  .pagination{
      height: 60px;
      display: flex;
      justify-content: flex-end;
      align-items: center;
  }
}
</style>