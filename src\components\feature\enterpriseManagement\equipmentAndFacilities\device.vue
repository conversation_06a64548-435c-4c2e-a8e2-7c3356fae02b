<template>
  <div id="device"
       class="device">
    <div class="header">
      <div class="operation">
        <h2 style="margin-bottom: 0">装置列表</h2>
        <div class="inputBox">
          <el-select v-model="hazardId" style="margin-right: 10px"
            size="small"
            clearable
            placeholder="全部重大危险源">
            <el-option v-for="item in dangerIdIsNotNullListDataList"
              :key="item.dangerid"
              :label="item.dangername"
              :value="item.dangerid" />
            </el-option>
          </el-select>
          <!-- <el-input v-model="productionUnitCode"
                    placeholder="请输入装置编号"
                    class="input"
                    size="small"
                    clearable
                    @clear="clearDangerName(event)"
                    style="margin-right: 10px"></el-input>
          <el-select v-model="typeOfProductionUnit"
                     size="small"
                     placeholder="请选择装置类型"
                     clearable
                     @clear="clearSensortypeCode($event)"
                     style="margin-right: 10px">
            <el-option v-for="item in tankTypeData"
                       :key="item.value"
                       :label="item.name"
                       :value="item.value">
            </el-option>
          </el-select> -->
          <el-input v-model.trim="nameOfProductionUnit"
                    placeholder="请输入装置名称"
                    class="input"
                    size="small"
                    clearable
                    @clear="clearState(event)"
                    style="margin-right: 10px"></el-input>
          <el-button size="small"
                     type="primary"
                     @click="search()">查询</el-button>
        </div>
        <div class="btnBox">
          <!--          <el-button-->
          <!--            size="mini"-->
          <!--            v-if="$store.state.login.user.user_type == 'ent'"-->
          <!--            type="primary"-->
          <!--            @click="addEdit"-->
          <!--            >新增</el-button-->
          <!--          >-->
          <CA-button class="export"
                     plain
                     size="small"
                     type="primary"
                     @click="exportExcel">导出
          </CA-button>
        </div>
      </div>
    </div>
    <div class="table"
         v-loading="loading">
      <el-table ref="multipleTable"
                :data="tableData"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
                height="530px">
        <!-- <el-table-column
          align="center"
          fixed="left"
          type="selection"
        >
        </el-table-column> -->
        <el-table-column align="center"
                         label="序号"
                         type="index"
                         width="55">
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
                         align="center"
                         label="所属重大危险源"
                         prop="sysGreatHazardName">
        </el-table-column>
        <el-table-column prop="nameOfProductionUnit"
                         label="装置名称"
                         align="center"
                         show-overflow-tooltip
                         width="120px">
        </el-table-column>
        <el-table-column prop="processName"
                         label="所属化工工艺"
                         show-overflow-tooltip
                         width="120px">
        </el-table-column>
        <el-table-column prop="runStateName"
                         label="运行状态"
                         align="center">
        </el-table-column>
        <el-table-column align="center"
                         label="最近检修日期">
          <template slot-scope="scope">
            <span style="color: rgb(57, 119, 234); cursor: pointer"
                  @click="enterDetail(scope.row)">{{ scope.row.lastMaintenanceTime ? new Date(scope.row.lastMaintenanceTime).Format("yyyy-MM-dd") : "" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center"
                         label="操作"
                         min-width="200px">
          <template slot-scope="scope">
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="view(scope.row)">详情</span>
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  v-if="$store.state.login.user.user_type === 'ent'"
                  @click="edit(scope.row)">编辑</span>
            <!--            v-if="$store.state.login.user.user_type === 'ent'"-->
            <!--            <span-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              @click="deleter(scope.row.id)"-->
            <!--              >删除</span-->
            <!--            >-->
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  v-if="$store.state.login.user.user_type === 'ent'"
                  @click="enterInput(scope.row)">录入</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination"
         v-if="total != 0">
      <el-pagination :current-page.sync="currentPage"
                     :total="total"
                     background
                      :page-size='size'
                     layout="total, prev, pager, next"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增编辑查看 -->
      <div  v-if='open'>
    <el-dialog :title="title"
              
               :visible.sync="open"
               width="1100px"
               :close-on-click-modal="false"
               :append-to-body="true">
      <el-form ref="form"
               :model="form"
               :rules="rules"
               label-width="150px">
        <div class="form_item">
          <!-- <h2 class="form_title">基本信息</h2> -->
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="所属重大危险源">
                  <el-select v-model="form.sysGreatHazard"
                             placeholder="请选择所属重大危险源"
                             disabled>
                    <el-option v-for="item in dangerIdIsNotNullListDataList"
                               :key="item.dangerid"
                               :label="item.dangername"
                               :value="item.dangerid" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="装置编码">
                  <el-input v-model.trim="form.productionUnitNo"
                            placeholder="请输入装置编码"
                            disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="装置名称">
                  <el-input v-model.trim="form.nameOfProductionUnit"
                            placeholder="请输入装置名称"
                            disabled />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="装置类型">
                  <!-- <el-input
                    v-model="form.typeOfProductionUnit"
                    placeholder="请输入装置类型"
                  /> -->
                  <el-select v-model="form.typeOfProductionUnit"
                             placeholder="请选择装置类型"
                             :disabled="dialogType === 'add'">
                    <el-option v-for="item in tankTypeData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="投入使用日期">
                  <el-date-picker v-model="form.dateOfCommissioning"
                                  value-format="yyyy-MM-dd"
                                  type="date"
                                  style="width: 190px"
                                  placeholder="选择投入使用日期"
                                  :disabled="dialogType === 'add'">
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="所属工艺">
                  <el-select v-model="form.processCode"
                             placeholder="请选择所属工艺"
                             :disabled="dialogType === 'add'">
                    <el-option v-for="item in knowledgeListData"
                               :key="item.processid"
                               :label="item.processname"
                               :value="item.processid" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="16">
                <el-form-item label="地址">
                  <el-input v-model.trim="form.addr"
                  maxlength="45"
                            :disabled="dialogType === 'add'"
                            placeholder="请选择地址" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="装置运行状态">
                  <el-select v-model="form.runState"
                             placeholder="请选择装置运行状态"
                             disabled>
                    <el-option v-for="item in pressuretypeData"
                               :key="item.value"
                               :label="item.name"
                               :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
              <el-form-item label="经度">
                  <el-input v-model.trim="form.longitude"                  
                  maxlength="10"
                            disabled
                            placeholder="请输入经度" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="纬度">
                  <el-input v-model.trim="form.latitude"
                  maxlength="10"
                            disabled
                            placeholder="请输入纬度" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="高程">
                  <el-input v-model.trim="form.altitude"
                  maxlength="5"
                            :disabled="dialogType === 'add'"
                            placeholder="请输入高程" >
                   <template slot="append">米</template>
                              </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <div class="map_height">
                  <el-form-item label="地图定位">
                    <!-- <egisMap
                      :islistener="true"
                      :isdetail="true"
                      ref="detailMap"
                      style="height: 220px"
                    ></egisMap> -->
                    <egisMap :islistener="false"
                      :isdetail="disabled"
                      ref="detailMap"
                      :datas="form"
                      style="height: 200px"
                      @mapCallback="mapcallback"></egisMap>
                  </el-form-item>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="安全仪表连锁回路总数" class="labelH">
                  <el-input v-model.trim="form.safetyLoopSum"
                            placeholder="请输入安全仪表连锁回路总数"
                            disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="安全仪表系统投用状态的指标编码" class="labelH">
                  <el-input v-model.trim="form.safetyUserStatusCode"
                            placeholder="请输入安全仪表系统投用状态的指标编码"
                            disabled />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="安全仪表连锁回路旁路数的指标编码" class="labelH">
                  <el-input v-model.trim="form.safetyLoopCode"
                            placeholder="请输入安全仪表连锁回路旁路数的指标编码"
                            disabled />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div v-if="dialogType === 'edit'"
           slot="footer"
           class="dialog-footer"
           style="display: flex; justify-content: flex-end;">
        <el-button type="primary"
                   @click="submitForm">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    </div>
    <el-dialog :append-to-body="true"
               :close-on-click-modal="false"
               :visible.sync="opens"
               title="录入检维修记录"
               width="900px">
      <el-form ref="formData"
               :model="formData"
               label-width="150px">
        <div class="form_item">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="dangerId" label="所属重大危险源">
                  <el-select v-model="formData.sysGreatHazard"
                             placeholder="请选择所属重大危险源"
                             @change="changeDanger"
                             style="width: 270px"
                             :disabled="true">
                    <el-option v-for="item in dangerIdIsNotNullListDataList"
                               :key="item.dangerid"
                               :label="item.dangername"
                               :value="item.dangerid" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="危险源编码">
                  <el-input v-model.trim="formData.sysGreatHazard"
                            :disabled="true"
                            placeholder="请输入危险源编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备名称"
                              prop="nameOfProductionUnit">
                  <el-input :disabled="true"
                            placeholder="请选择设备名称"
                            v-model.trim="formData.nameOfProductionUnit"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备编码">
                  <el-input :disabled="true"
                            v-model.trim="formData.productionUnitNo"
                            placeholder="请输入设备编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上次检维修日期">
                  <el-input :disabled="true"
                            v-model.trim="formData.lastMaintenanceTime"
                            style="width: 270px" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检维修日期"
                              prop="maintenanceTime"
                              :rules="{
                    required: true,
                    message: '检维修日期不能为空',
                    trigger: 'blur',
                  }">
                  <el-date-picker v-model="formData.maintenanceTime"
                    value-format="yyyy-MM-dd"
                    type="date"
                    placeholder="请输入检维修日期"
                    style="width: 270px">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作负责人"
                              prop="operator"
                              :rules="{
                    required: true,
                    message: '操作负责人不能为空',
                    trigger: 'blur',
                  }">
                  <el-input maxlength="30" v-model.trim="formData.operator"
                            placeholder="请输入操作负责人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式"
                              prop="contact"
                              :rules="[{
                    required: true,
                    message: '联系方式不能为空',
                    trigger: 'blur',
                  },
                   {
                    pattern: /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/,
                    message: '请输入有效的电话号码'
                   }]">
                  <el-input v-model.trim="formData.contact"
                            placeholder="请输入联系方式" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="更换备品清单"
                              prop="replacementContent"
                              :rules="{
                    required: true,
                    message: '更换备品清单不能为空',
                    trigger: 'blur',
                  }">
                  <el-input v-model.trim="formData.replacementContent"
                            :rows="4"
                            maxlength="500"
                            placeholder="请输入更换备品清单"
                            show-word-limit
                            type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="检维修内容"
                  prop="maintenanceContent"
                  :rules="{
                    required: true,
                    message: '检维修内容不能为空',
                    trigger: 'blur',
                  }">
                  <el-input v-model.trim="formData.maintenanceContent"
                    :rows="4"
                    maxlength="500"
                    placeholder="请输入检维修内容"
                    show-word-limit
                    type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button type="primary" @click="submit" :loading="btnLoading">保 存</el-button>
        <el-button @click="cancels">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 录入 -->
    <repairDialog ref="repairDialog" type="device"
      :form="formDataRepair"
      v-model="openes"
      :viewForm="formList"
      @handleCurrentChange="handleMaintenance"></repairDialog>
  </div>
</template>
<script>
import {
  getDeviceListData,
  addDeviceListData,
  editDeviceListData,
  datailDeviceListData,
  deleteDeviceListData,
  getDeviceListAllData,
  getDangerIdIsNotNullListDatas,
  maintenanceSave,
  getmaintenanceSave,
  getDangerIdIsNotNullTypeData,
  getKnowledgeListData,
  exportDeviceListData
} from '@/api/equipmentAndFacilities'
import repairDialog from './repairDialog'
import { getmaintenanceList } from '../../../../api/equipmentAndFacilities'

const mapConfig = require('@/assets/json/map.json')
export default {
  //import引入的组件
  name: 'device',
  components: {
    repairDialog
  },
  data() {
    var validatelongitude = (rule, value, callback) => {
      // 校验经度
      const reg = /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,9})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,9}|180)$/
      if (value !== '' && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error('经度整数部分为0-180,小数部分为0到9位!'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    var validatelatitude = (rule, value, callback) => {
      // 校验纬度
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,9}|90\.0{0,9}|[0-8]?\d{1}|90)$/
      if (value !== '' && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error('纬度整数部分为0-90,小数部分为0到9位!'))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      btnLoading: false,
      hazardId: '', // 重大危险源
      nameOfProductionUnit: '',
      typeOfProductionUnit: '',
      productionUnitCode: '',
      loading: false,
      open: false,
      opens: false,
      openes: false,
      isDanger: true,
      dangerIdIsNotNullListDataList: [],
      title: '编辑装置信息',
      dialogType: '', // add edit
      tankTypeData: [],
      pressuretypeData: [],
      temperaturTypeData: [],
      dangerIdIsNotNullListData: [],
      knowledgeListData: [],
      allShebeiNameData: [],
      formDataRepair: {},
      formList: {},
      currentPage: 1,
      size: 11,
      selection: [],
      total: 0,
      tableData: [],
      form: {
        sysGreatHazard: '',
        productionUnitCode: '',
        productionUnitNo:'',
        nameOfProductionUnit: '',
        typeOfProductionUnit: '',
        dateOfCommissioning: '',
        processCode: '',
        addr: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: '0',
        safetyLoopSum: '',
        safetyUserStatusCode: '',
        safetyLoopCode: '',
        runState: ''
      },
      formData: {
        contact: '',
        facilityId: '',
        dangerId: '',
        upmaintenanceTime: '',
        maintenanceContent: '',
        maintenanceId: '',
        maintenanceTime: '',
        operator: '',
        replacementContent: ''
      },
      rules: {
        longitude: [
          {
            required: true,
            validator: validatelongitude,
            trigger: ['change', 'blur']
          }
        ],
        latitude: [
          {
            required: true,
            validator: validatelatitude,
            trigger: ['change', 'blur']
          }
        ],
        altitude: [
          {
            required: true,
            pattern: /^\d{1,6}(\.\d{1,6})?$/,
            message: '请输入正确的高程',
            trigger: ['change', 'blur']
          }
        ]
      },
      enterpriseId: '',
      disabled: false,
      activeName: '1'
    }
  },
  //方法集合
  methods: {
    beforeClose(){
      this.open = false;
    },
    changeLongitude(val){
      console.log(val)
    },
    handleMaintenance(row) {
      this.getmaintenanceListFun(this.formList, row.currentPage)
    },
    getmaintenanceListFun(row, nowPage) {
      this.$nextTick(() => {
        this.$refs.repairDialog.loading = true
      })
      getmaintenanceList({
        facilityId: row.productionUnitCode,
        nowPage: nowPage || 1,
        pageSize: 10
      }).then(res => {
        this.$nextTick(() => {
          this.$refs.repairDialog.loading = false
          if (res.data.status === 200) {
            this.formDataRepair = res.data.data
          }
        })
      })
    },
    changeDanger(event, item) {
      if (event) {
        this.isDanger = false
        this.formData.dangerId = event
        this.getAllName(event)
      } else {
        this.isDanger = true
      }
    },
    getAllName(danderId) {
      getDeviceListAllData({
        hazardId: danderId
      }).then(res => {
        if (res.data.status == 200) {
          this.allShebeiNameData = res.data.data;
        }
      })
    },
    enterDetail(row) {
      this.openes = true
      this.formList = row
      // this.formList = {
      //   hazardIdName: row.sysGreatHazardName,
      //   hazardId: row.sysGreatHazard,
      //   tankName: row.typeOfProductionUnitName,
      //   tankNum: row.typeOfProductionUnit
      // }
      this.getmaintenanceListFun(row)
    },
    getData(id) {
      this.loading = true
      this.enterpriseId = id
      var orgCode = ''
      if (this.$store.state.login.user.user_type == 'ent') {
        orgCode = this.$store.state.login.user.org_code
      }
      getDeviceListData({
        nowPage: this.currentPage,
        orgCode: id,
        // orgCode: orgCode,
        pageSize: this.size,
        nameOfProductionUnit: this.nameOfProductionUnit,
        productionUnitCode: this.productionUnitCode,
        typeOfProductionUnit: this.typeOfProductionUnit,
        harzardId: this.hazardId, // 所属危险源
      }).then(res => {
        this.loading = false
        if (res.data.status == 200) {
          this.total = res.data.data.total
          this.tableData = res.data.data.list
        }
      })
    },
    //重大危险源
    getDangerIdIsNotNullListDataList(id) {
      console.log(id)
      getDangerIdIsNotNullListDatas({ enterpid: id }).then(res => {
        if (res.data.status == 200) {
          this.dangerIdIsNotNullListDataList = res.data.data
        }
      })
    },
    getDangerIdIsNotNullListDataType() {
      getDangerIdIsNotNullTypeData({
        dicCode: 'PRODUCTION_TYPE'
      }).then(res => {
        if (res.data.status === 200) {
          this.tankTypeData = res.data.data
        }
      })
      getDangerIdIsNotNullTypeData({
        dicCode: 'PRODUCTION_STATUS'
      }).then(res => {
        if (res.data.status === 200) {
          this.pressuretypeData = res.data.data
        }
      })
      getKnowledgeListData({
        processid: '',
        processname: ''
      }).then(res => {
        if (res.data.status === 200) {
          this.knowledgeListData = res.data.data
        }
      })
    },
    search() {
      this.currentPage = 1
      this.getData(this.enterpriseId)
    },
    clearSensortypeCode(e) {
      this.typeOfProductionUnit = ''
    },
    clearState(e) {
      this.nameOfProductionUnit = ''
    },
    clearDangerName(e) {
      this.productionUnitCode = ''
    },
    handleSelectionChange(val) {
      console.log(val)
    },
    handleClick() {
      console.log(123)
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getData(this.enterpriseId)
    },
    select(selection, row) {
      this.selection = []
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId
      }
    },
    reset() {
      this.form = {
        sysGreatHazard: '',
        productionUnitCode: '',
        productionUnitNo: '',
        nameOfProductionUnit: '',
        typeOfProductionUnit: '',
        dateOfCommissioning: '',
        processCode: '',
        addr: '',
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: '0',
        safetyLoopSum: '',
        safetyUserStatusCode: '',
        safetyLoopCode: '',
        runState: ''
      }
      if (this.$refs['form']) {
        this.$refs['form'].resetFields()
      }
    },
    resetes() {
      this.formData = {
        contact: '',
        facilityId: '',
        maintenanceContent: '',
        maintenanceId: '',
        maintenanceTime: '',
        operator: '',
        replacementContent: ''
      }
      if (this.$refs['formData']) {
        this.$refs['formData'].resetFields()
      }
    },
    addEdit() {
      this.reset()
      this.open = true
      this.disabled = false
    },
    view(row) {
      this.dialogType = 'add';
      this.reset()
      this.open = true
      this.disabled = true
      this.title = '查看装置信息'
      //   this.form = row;
      datailDeviceListData({ productionUnitCode: row.productionUnitCode }).then(
        res => {
          if (res.data.status == 200) {
            this.form = res.data.data
            if (!this.form.longitude || !this.form.latitude) {
              this.form.longitude = mapConfig.map.defaultExtent.center[0];
              this.form.latitude = mapConfig.map.defaultExtent.center[1];
            }
          }
        }
      )
    },
    edit(row) {
      this.dialogType = 'edit';
      this.reset()
      this.open = true
      this.disabled = false
      this.title = '编辑装置信息'
      //   this.form = row;
      datailDeviceListData({ productionUnitCode: row.productionUnitCode }).then(
        res => {
          if (res.data.status == 200) {
            this.form = res.data.data
            if (!this.form.longitude || !this.form.latitude) {
              this.form.longitude = mapConfig.map.defaultExtent.center[0];
              this.form.latitude = mapConfig.map.defaultExtent.center[1];
            }
          }
        }
      )
    },
    enterInput(row) {
      this.resetes()
      this.formData = row
      this.$set(this.formData, 'maintenanceTime', '')
      console.log(row)
      this.formData.hazardIds = row.instCode
      this.opens = true
    },

    deleter(row) {
      const id = row
      this.$confirm('是否确认删除该装置信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteDeviceListData({
            ids: [id]
          }).then(res => {
            if (res.data.status == 200) {
              this.$message.success('删除成功')
              this.getData(this.enterpriseId)
            } else {
              this.$message.error(res.data.msg)
            }
          })
        })
        .catch(() => {})
    },
    cancel() {
      this.open = false
      this.reset()
    },
    cancels() {
      this.opens = false
      this.resetes()
    },
    canceles() {
      this.openes = false
    },
    submitForm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          if (this.title === '编辑装置信息') {
            editDeviceListData(this.form).then(response => {
              if (response.data.status == 200) {
                this.open = false
                this.$message({
                  type: 'success',
                  message: response.data.msg || '修改成功'
                })
                this.reset()
                this.getData(this.enterpriseId)
              } else {
                this.$message.error(response.data.msg || '修改失败')
              }
            })
          } else {
            // this.form.orgCode = this.enterpriseId;
            this.form.orgCode = this.$store.state.login.user.org_code
            this.form.orgName = this.$store.state.login.user.org_name
            addDeviceListData(this.form).then(response => {
              if (response.data.status == 200) {
                this.open = false
                this.$message({
                  type: 'success',
                  message: response.data.msg || '新增成功'
                })
                this.reset()
                this.getData(this.enterpriseId)
              } else {
                this.$message.error(response.data.msg || '新增失败')
              }
            })
          }
        } else {
          return false
        }
      })
    },
    submit() {
      this.$refs['formData'].validate(valid => {
        if (valid) {
          this.btnLoading = true
          const {
            contact,
            productionUnitCode,         
            maintenanceContent,
            maintenanceId,
            maintenanceTime,
            operator,
            replacementContent
          } = this.formData
          maintenanceSave({
            contact,
            facilityId: productionUnitCode,
            maintenanceContent,
            maintenanceId,
            maintenanceTime,
            operator,
            replacementContent
          }).then(response => {
            this.btnLoading = false
            if (response.data.status == 200) {
              this.$message({
                type: 'success',
                message: '录入成功'
              })
              this.opens = false
              this.resetes()
              this.getData(this.enterpriseId)
            }
          })
        } else {
          return false
        }
      })
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6)
        this.form.latitude = data.location.lat.toFixed(6)
        this.form.altitude = data.location.altitude ? data.location.altitude.toFixed(6) : '0'
        this.form.addr = data.formatted_address
      } else {
        this.$message({
          message: '详情页面不可选点！',
          type: 'warning'
        })
      }
    },
    exportExcel() {
      exportDeviceListData({
        nowPage: this.currentPage,
        // orgCode: this.$store.state.login.user.org_code,
        orgCode: this.enterpriseId,
        pageSize: this.size,
        nameOfProductionUnit: this.nameOfProductionUnit,
        productionUnitCode: this.productionUnitCode,
        typeOfProductionUnit: this.typeOfProductionUnit,
        harzardId: this.hazardId, // 所属危险源
      }).then(response => {
        this.$message({
          message: '导出成功',
          type: 'success'
        })
        const blob = new Blob([response.data], {
          type: 'application/vnd.ms-excel'
        })
        //获取今天的时间
        let day = new Date()
        day.setTime(day.getTime())
        let timestamp =
          day.getFullYear() + '-' + (day.getMonth() + 1) + '-' + day.getDate()
        const filename = '装置' + timestamp + '.xls'
        //下载文件
        const link = document.createElement('a')
        link.href = URL.createObjectURL(blob)
        link.download = filename
        document.body.appendChild(link)
        link.click()
        window.setTimeout(function() {
          URL.revokeObjectURL(blob)
          document.body.removeChild(link)
        }, 0)
      })
    }
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getDangerIdIsNotNullListDataType()
  }
}
</script>
<style lang="scss" scoped>
/deep/ .labelH .el-form-item__label{
 line-height: inherit!important;
}
.device {
  background-color: #fff;
  padding-bottom: 50px;

  .header {
    .title {
      margin-left: 20px;
      font-weight: 600;
    }

    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      .inputBox {
        // width: 1020px;
        //width: 940px;
        //float: left;
        display: flex;
        align-items: center;
        .input {
          width: 160px;
        }
      }
      .btnBox {
        display: flex;
        & > * {
          margin-left: 10px;
        }
      }
    }

    .export {
      margin-right: 10px;
    }
  }

  .table {
    width: 100%;
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
