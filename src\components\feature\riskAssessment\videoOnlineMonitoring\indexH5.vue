<template>
  <div class="enterpriseManagement">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box">
                <a-icon type="home" theme="filled" class="icon" /> 视频在线监控
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="videoInspection">
        <div class="video-left">
          <div class="videoLeft-top">
            <div class="list-search">
              <el-input
                v-model.trim="enterpName"
                size="mini"
                placeholder="请输入企业名称"
                class="input"
                clearable
                style="max-width: 260px; margin-right: 10px"
                @input="changeInput"
              ></el-input>
              <el-button type="primary" size="mini" @click="search"
                >查询</el-button
              >
            </div>
            <div class="video-list" v-loading="loading">
              <a-directory-tree
                multiple
                default-expand-all
                @select="onSelect"
                @expand="onExpand"
                style="padding: 0 10px"
              >
                <a-tree-node
                  :key="item.id + ',' + item.type"
                  v-for="item in newAllData"
                  :title="item.name"
                >
                  <a-tree-node
                    v-if="item.children.length > 0"
                    :key="subItem.id + ',' + subItem.type"
                    v-for="subItem in item.children"
                    :title="subItem.name"
                  >
                    <a-tree-node
                      v-if="subItem.children.length > 0"
                      :key="subItems.id + ',' + subItems.type"
                      v-for="subItems in subItem.children"
                      :title="subItems.name"
                    >
                      <a-tree-node
                        v-if="subItems.children.length > 0"
                        :key="subItemd.id + ',' + subItemd.type"
                        v-for="subItemd in subItems.children"
                        :title="subItemd.name"
                      >
                        <a-tree-node
                          v-if="subItemd.children.length > 0"
                          :key="subItemed.id + ',' + subItemed.type"
                          v-for="subItemed in subItemd.children"
                          :title="subItemed.name"
                        ></a-tree-node>
                      </a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-tree-node>
              </a-directory-tree>
              <!-- <a-tree
                :multiple="false"
                default-expand-all
                @select="onSelect"
                @expand="onExpand"
                :tree-data="newAllData"
                style="padding: 0 10px"
              >
              </a-tree> -->
            </div>
          </div>
        </div>
        <div class="video-right">
          <div class="video-box" id="video-box">
            <!-- <div id="playWind"></div> -->
            <div id="ws-real-player"></div>
            <div class="items" id="playWind_items" style="display: none">
              <div class="items_button">
                <div class="button" @click="stop()">停止预览</div>
                <div class="button" @click="StopRealPlayAll()">
                  关闭所有预览
                </div>
                <div class="button" @click="CapturePicture('JPEG')">抓图</div>
                <div class="button" @click="fullSreen()">全屏</div>
              </div>

              <div class="fenping_box">
                <img
                  src="../../../../../static/img/fenping1.png"
                  class="fenping"
                  @click="arrangeWindow(1)"
                />
                <img
                  src="../../../../../static/img/fenping2.png"
                  class="fenping"
                  @click="arrangeWindow(2)"
                />
                <img
                  src="../../../../../static/img/fenping3.png"
                  class="fenping"
                  @click="arrangeWindow(3)"
                />
                <!-- <img
                  src="../../../../../static/img/fenping4.png"
                  class="fenping"
                  @click="arrangeWindow(4)"
                /> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getOnlineVideoData, getRealmonitorNew } from "@/api/riskAssessment";
import { getVideoH5VideoH5 } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import WSPlayer from "@/utils/WSPlayer/WSPlayer";
import ICC from "@/utils/icc";
export default {
  components: {},
  data() {
    return {
      tableCheck: true,
      newAllData: [],
      enterpName: "",
      loading: false,
      iWind: 0,
      MaxIWind: 1,
      oPlugin: "",
      previewUrl: "",
      splitScreenValue: "",
      videoCheck: true,
      cameraIndexCode: "",
      realPlayer: null,
    };
  },
  methods: {
    //视频运行列表
    getOnlineVideoDataList() {
      this.loading = true;
      getOnlineVideoData({
        enterpName: this.enterpName,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          //   this.tableData = res.data.data.records;
          //   this.NetWork = false;
          this.newAllData = res.data.data;
          this.setMenus(this.newAllData);
        }
      });
    },
    changeInput(val) {
      if (this.enterpName == "") {
        this.getOnlineVideoDataList();
      }
    },
    setMenus(arr) {
      /**
       * 利用递归替换key值
       * title替换orgName
       * key替换orgCode
       */
      var keyMap = {
        name: "title",
        id: "key",
      };
      for (var i = 0; i < arr.length; i++) {
        delete arr[i].isLeaf;
        var obj = arr[i];
        for (var key in obj) {
          var newKey = keyMap[key];
          //   console.log(newKey);
          if (newKey) {
            obj[newKey] = obj[key];
            if (obj.children.length > 0) {
              this.setMenus(obj.children);
            }
            // delete obj[key];
          }
        }
      }
    },
    onSelect(selectedKeys, info) {
      console.log(selectedKeys);
      if (selectedKeys[0].split(",")[1] == "3") {
        this.cameraIndexCode = selectedKeys[0].split(",")[0];
        // this.getVideo(this.cameraIndexCode);
        this.realPlayNew(this.cameraIndexCode);
      }
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId
     */
    realPlayNew(channelId) {
      ICC.getRealmonitor({
        channelId: channelId,
        // dataType: '3', //视频类型：1=视频, 2=音频, 3=音视频
        streamType: "2", //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
      }).then((data) => {
        this.realPlayer.playReal({
          rtspURL: data.rtspUrl, // string | array[string]
          decodeMode: "canvas", // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
          channelId: channelId, // 可选，用来标记当前视频播放的通道id
        });
      });
    },
    onExpand() {},
    search() {
      this.getOnlineVideoDataList();
    },
    getVideo() {
      this.videoCheck = false;
      getVideoH5VideoH5(this.cameraIndexCode).then((res) => {
        this.previewUrl = res.data.data;
        this.realplay();
        if (this.MaxIWind > this.iWind) {
          $("#playWind")
            .children()
            .eq(0)
            .children()
            .eq(this.iWind++)
            .find(".draw-window")[0].style.border = "1px solid #343434";
          $("#playWind")
            .children()
            .eq(0)
            .children()
            .eq(this.iWind)
            .find(".draw-window")[0].style.border =
            "1px solid rgb(255, 204, 0)";
        } else {
          $("#playWind")
            .children()
            .eq(0)
            .children()
            .eq(this.iWind)
            .find(".draw-window")[0].style.border =
            "1px solid rgb(255, 204, 0)";
        }
      });
    },
    // var szWebsocketSessionID = "4cc81824281f214eceb5"; //设备直连取流uuid， 流媒体取流不需要该参数
    // var szToken = "";
    // var iWind = 0; //窗口索引
    initPlugin() {
      this.oPlugin.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {
          //插件选中窗口回调
          this.iWind = iWndIndex;
          // console.log(iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
          //插件错误回调
          console.error(
            `window-${iWndIndex}, errorCode: ${iErrorCode}`,
            oError
          );
        },
        windowEventOver: function (iWndIndex) {
          //鼠标移过回调
          //console.log(iWndIndex);
        },
        windowEventOut: function (iWndIndex) {
          //鼠标移出回调
          //console.log(iWndIndex);
        },
        windowEventUp: function (iWndIndex) {
          //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {
          //全屏切换回调
          console.log(bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
          //首帧显示回调
          console.log(iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {
          //性能不足回调
        },
      });
      this.oPlugin
        .JS_SetOptions({
          // bSupportSound: false  //是否支持音频，默认支持
          // bSupporDoubleClickFull: false  //是否双击窗口全屏，默认支持
          // bOnlySupportMSE: true  //只支持MSE
          // bOnlySupportJSDecoder: true  //只支持JSDecoder
        })
        .then(function () {
          console.log("JS_SetOptions");
        });
    },

    getVersion() {
      this.oPlugin.JS_GetPluginVersion().then(function (szVersion) {
        console.log(szVersion);
      });
    },
    Destroy() {
      this.oPlugin.JS_DestroyWorker().then(function () {
        console.log("destroyWorker success");
      });
    },
    SetSecretKey() {
      var secretKey = document.getElementById("secretKey").value;
      this.oPlugin.JS_SetSecretKey(iWind, secretKey).then(
        function () {
          console.log("JS_SetSecretKey success");
        },
        function () {
          console.log("JS_SetSecretKey failed");
        }
      );
    },

    realplay() {
      this.videoCheck = true;
      var url = this.previewUrl; // + "?token=" + szToken;  //"ws://10.19.141.64:7314/EUrl/ybcwxHO"; 联网共享下该url和playurl是一样的
      let streamType = this.getRadioValue("streamType");
      // console.log(this.oPlugin);
      const THIS = this;
      this.oPlugin
        .JS_Play(
          url,
          {
            playURL: url,
            mode: parseInt(streamType),
            session: "4cc81824281f214eceb5", //定制设备
            token: "",
          },
          this.iWind
        )
        .then(
          function () {
            console.log("realplay success");
          },
          function () {
            console.log("realplay failed");
            THIS.$message.error("取流异常");
          }
        );
    },
    getRadioValue(radioName) {
      let value = "";
      document.getElementsByName(radioName).forEach((el) => {
        if (el.checked) {
          value = el.value;
        }
      });
      return value;
    },
    playback() {
      var url = document.getElementById("urlPlayback").value; //"ws://10.19.141.64:7314/EUrl/ybcwxHO";
      var szStartDate = document.getElementById("sDate").value;
      var szEndDate = document.getElementById("eDate").value;
      let streamType = this.getRadioValue("streamType");
      if (
        document.getElementById("urlPlayback").value &&
        szStartDate &&
        szEndDate
      ) {
        this.oPlugin
          .JS_Play(
            url,
            {
              // token: szToken,
              //流媒体
              playURL: url, //联网共享的取流url
              mode: parseInt(streamType),
              //proxy: "10.5.6.8",  //proxy后面的属性为代理的目标地址，根据实际情况配置，联网共享https下需要用到该参数
              //mode: "media",  //建立连接的url新增一个media节点, 联网共享https下需要用到该参数
            },
            this.iWind,
            szStartDate,
            szEndDate
          )
          .then(
            function () {
              console.log("playback success");
            },
            function () {
              console.log("playback failed");
            }
          );
      } else {
        return;
      }
    },
    playbackLocation() {
      var szStartDate = document.getElementById("sDate1").value;
      var szEndDate = document.getElementById("eDate1").value;
      this.oPlugin.JS_Seek(this.iWind, szStartDate, szEndDate).then(
        function () {
          console.log("playbackLocation success");
        },
        function () {
          console.log("playbackLocation failed");
        }
      );
    },
    stop() {
      this.oPlugin.JS_Stop(this.iWind).then(
        function () {
          console.log("stop success");
        },
        function (e) {
          console.error("stop failed", e);
        }
      );
    },
    arrangeWindow(i) {
      this.MaxIWind = i * i - 1;
      this.oPlugin.JS_ArrangeWindow(i).then(function () {
        console.log("JS_ArrangeWindow success");
      });
    },
    Pause() {
      this.oPlugin.JS_Pause(this.iWind).then(
        function () {
          console.log("Pause success");
        },
        function (e) {
          console.error("Pause failed", e);
        }
      );
    },
    Resume() {
      this.oPlugin.JS_Resume(this.iWind).then(
        function () {
          console.log("Resume success");
        },
        function (e) {
          console.error("Resume failed", e);
        }
      );
    },
    GetVolume() {
      this.oPlugin.JS_GetVolume(this.iWind).then(function (i) {
        console.log(i);
      });
    },
    CapturePicture(szType) {
      const timestamp = new Date();
      this.oPlugin
        .JS_CapturePicture(this.iWind, `img-${timestamp}`, szType)
        .then(
          function () {
            console.log("CapturePicture success");
          },
          function () {
            console.log("CapturePicture failed");
          }
        );
    },
    StopRealPlayAll() {
      this.oPlugin.JS_StopRealPlayAll().then(
        function () {
          console.log("JS_StopRealPlayAll success");
        },
        function () {
          console.log("JS_StopRealPlayAll failed");
        }
      );
    },
    dateFormat(oDate, fmt) {
      var o = {
        "M+": oDate.getMonth() + 1, //月份
        "d+": oDate.getDate(), //日
        "h+": oDate.getHours(), //小时
        "m+": oDate.getMinutes(), //分
        "s+": oDate.getSeconds(), //秒
        "q+": Math.floor((oDate.getMonth() + 3) / 3), //季度
        S: oDate.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (oDate.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    stopRecord() {
      this.oPlugin.JS_StopSave(this.iWind).then(
        function () {
          console.log("stopRecord success");
        },
        function () {
          console.log("stopRecord failed");
        }
      );
    },
    startTalk() {
      var talkurl = document.getElementById("talkurl").value;
      this.oPlugin.JS_StartTalk(talkurl).then(
        function () {
          console.log("startTalk success");
        },
        function () {
          console.log("startTalk failed");
        }
      );
    },
    fullSreen() {
      this.oPlugin.JS_FullScreenDisplay(true).then(
        function () {
          console.log("JS_FullScreenDisplay success");
        },
        function () {
          console.log("JS_FullScreenDisplay failed");
        }
      );
    },
    getVideoId(monitornum, index) {
      this.active = index;
      this.cameraIndexCode = monitornum;
      this.getVideo();
    },
    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
    setVideoSize() {
      var playWind = document.getElementById("playWind_items");
      this.oPlugin.JS_Resize({ iWidth: playWind.scrollWidth }).then(
        () => {
          console.info("JS_Resize success");
          // do you want...
        },
        (err) => {
          console.info("JS_Resize failed");
          // do you want...
        }
      );
    },
    initializationVideo() {
      const THIS = this;
      function getScript(url, fn) {
        if ("string" === typeof url) {
          url = [url]; //如果不是数组带个套
        }
        var ok = 0; //加载成功几个js
        var len = url.length; //一共几个js
        var head = document.getElementsByTagName("head").item(0);
        var js = null;
        var _url;
        var create = function (url) {
          //创建js
          var oScript = null;
          oScript = document.createElement("script");
          oScript.type = "text/javascript";
          oScript.src = url;
          head.appendChild(oScript);
          return oScript;
        };
        for (var i = 0; i < len; i++) {
          _url = url[i];
          js = create(_url); //创建js
          fn &&
            (js.onload = function () {
              if (++ok >= len) {
                //如果加载完所有的js则执行回调
                fn();
              }
            });
        }
      }
      //var szBrowserVersion = "";
      //var iBrowserVersion = -1;
      var aScript = [];
      var szUserAgent = navigator.userAgent.toLowerCase();
      // if (szUserAgent.match(/chrome\/([\d.]+)/) || szUserAgent.match(/Firefox\/([\d.]+)/)) {
      //szBrowserVersion = szUserAgent.match(/chrome\/([\d.]+)/)[1];
      //iBrowserVersion = parseInt(szBrowserVersion.split(".")[0], 10);
      if (
        szUserAgent.indexOf("win64") > -1 ||
        szUserAgent.indexOf("x64") > -1
      ) {
        aScript = ["../../../../../static/h5player.min.js"];
      } else {
        aScript = ["../../../../../static/h5player.min.js"];
      }
      // }
      var playWind = document.getElementById("playWind_items");
      // console.log(playWind.scrollWidth);
      getScript(aScript, function () {
        //初始化插件
        //初始化插件
        THIS.oPlugin = new JSPlugin({
          szId: "playWind",
          iWidth: playWind.scrollWidth,
          // iHeight: 500,
          iMaxSplit: 4,
          iCurrentSplit: 1,
          szBasePath: "../../../../../static/",
          oStyle: {
            border: "#343434",
            borderSelect: "#FFCC00",
            background: "#000",
          },
          openDebug: false,
        });
        THIS.initPlugin();
        //初始化播放器大小
        THIS.setVideoSize();
      });
    },
  },
  async created() {
    await ICC.init();
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    const THIS = this;
    this.getOnlineVideoDataList();
    //初始化播放器
    // this.initializationVideo();
    // window.onresize = () => {
    //   THIS.setVideoSize();
    // };
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // 初始化平台信息获取
    let serverAdress = sessionStorage.getItem("videoApi");
    // if (process.env.NODE_ENV === 'development') {
    //     serverAdress = '************:2443';
    // } else {
    //     serverAdress = '***********:9100';
    // }
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    if (!this.realPlayer) {
      this.realPlayer = new WSPlayer({
        el: "ws-real-player", // 必传
        type: "real", // real | record
        serverIp: serverAdress,
        num: 4,
        showControl: true,
      });
    }
  },
  beforeDestroy() {
    // this.StopRealPlayAll();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  min-width: 1200px;
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    .video-left {
      float: left;
      width: 18%;
      margin-right: 15px;
      margin-top: 15px;
      .videoLeft-top {
        background: #daefff;
        .list-search {
          padding-top: 15px;
          display: flex;
          justify-content: space-between;
          padding-left: 10px;
          padding-right: 10px;
          padding-bottom: 10px;
        }
        .video-list {
          height: calc(100vh - 195px);
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
    }
    .video-right {
      float: left;
      position: relative;
      width: 80%;
      .video-box {
        margin-top: 15px;
        border: 1px solid #ddd;
        height: calc(100vh - 145px);
        width: 100%;
        & > .items {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #2e2e2e;
          padding-top: 10px;
          padding-bottom: 10px;
          padding: 0 20px;
          height: 50px;
          .items_button {
            width: 60%;
            height: 30px;
            display: flex;
            align-items: center;
            font-size: 12px;
          }
          .button {
            color: #fff;
            border-radius: 5px;
            padding: 5px 10px;
            margin-right: 20px;
            cursor: pointer;
            background-color: #777;
          }
          .fenping_box {
            width: 150px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .fenping {
              width: 25px;
              height: 25px;
            }
          }
        }
      }
      #playWind {
        height: calc(100vh - 195px);
      }
      #ws-real-player {
        height: calc(100vh - 150px);
      }
    }
  }
}
</style>
