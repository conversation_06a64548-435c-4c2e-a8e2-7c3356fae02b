<template>
  <div class="echartDetail">
    <!-- {{ timeObj }} -->
    <div
      class="echarts"
      id="echartlR"
      style="width: 250px; height: 250px"
    ></div>
    <ul>
      <li v-for="(item, index) of chartListData" :key="index">
        <span class="squery" :style="{ background: item.color }"></span>
        {{ item.name }}<span class="value"> {{ item.value }} 家</span>
      </li>
    </ul>
  </div>
</template>
<script>
var dayjs = require("dayjs");
import { getElectricityPieChart } from "@/api/riskAssessment";
export default {
  props: {
    timeObj: Object,
  },
  data() {
    return {
      chartListData: [
        {
          name: "存在长期停产可能",
          value: "0",
          color: "#03d6ef",
          type: "shutDown",
        },
        {
          name: "存在紧急生产停车可能",
          value: "0",
          color: "#8b6fff",
          type: "urgencyStop",
        },
        {
          name: "存在超负荷生产可能",
          value: "0",
          color: "#dc91ff",
          type: "overload",
        },
        {
          name: "存在昼停夜开可能",
          value: "0",
          color: "#ffc618",
          type: "productNight",
        },
        {
          name: "存在明停暗开可能",
          value: "0",
          color: "#66b1ff",
          tppe: "productSecret",
        },
        {
          name: "存在试生产企业违规生产可能",
          value: "0",
          color: "#f76c41",
          type: "productIllegal",
        },
      ],
    };
  },
  watch: {
    timeObj(val, oldVal) {
      console.log(val, oldVal);
      this.chartData(val.startTime, val.endTime);
    },
  },
  methods: {
    initChart(chartData, noData) {
      var chartDom = document.getElementById("echartlR");
      //   var myChart = echarts.init(chartDom);
      var myChart = this.$echarts.init(chartDom);
      var option;
      var dataValue = [];
      // chartData.forEach(item=>{
      //   dataValue.push({
      //     value:item.value
      //   })
      // })

      if (noData) {
        dataValue = [];
      } else {
        dataValue = chartData;
      }

      option = {
        title: {
          show: noData, // 无数据时展示 title
          textStyle: {
            color: "#000000a6",
            fontSize: 16,
          },
          text: "暂无数据",
          left: "center",
          top: "center",
        },

        tooltip: {
          trigger: "item",
          formatter: "{b} : {c}",
        },
        legend: {
          left: "center",
          top: "bottom",
          show: false,
          // data: [
          //   "rose1",
          //   "rose2",
          //   "rose3",
          //   "rose4",
          //   "rose5",
          //   "rose6",
          //   "rose7",
          //   "rose8",
          // ],
        },
        toolbox: {
          show: false,
          feature: {
            mark: { show: true },
            dataView: { show: true, readOnly: false },
            restore: { show: true },
            saveAsImage: { show: true },
          },
        },
        series: [
          {
            // name: "Radius Mode",
            type: "pie",
            radius: [50, 80],
            center: ["50%", "50%"],
            roseType: "radius",
            color: [
              "#03d6ef",
              "#8b6fff",
              "#dc91ff",
              "#ffc618",
              "#66b1ff",
              ,
              "#f76c41",
            ], //设置对应块的数据
            // itemStyle: {
            //   borderRadius: 5,
            // },
            label: {
              show: false,
            },
            emphasis: {
              label: {
                show: false,
              },
            },
            data: dataValue,
            // data: chartData,
            // data: [
            //   { value: 40, name: "rose 1" },
            //   { value: 33, name: "rose 2" },
            //   { value: 28, name: "rose 3" },
            //   { value: 22, name: "rose 4" },
            //   { value: 20, name: "rose 5" },
            //   { value: 15, name: "rose 6" },
            //   { value: 12, name: "rose 7" },
            //   { value: 10, name: "rose 8" },
            // ],
          },
        ],
      };

      option && myChart.setOption(option);
    },

    chartData(startTime, endTime) {
      var parme = {
        districtCode: this.$store.state.login.userDistCode,
        endTime: endTime,
        startTime: startTime,
      };
      getElectricityPieChart(parme).then((res) => {
        // console.log(this.chartListData);
        // this.chartListData = [];
        if (res.data.status == 200) {
          var chartListObj = res.data.data;
          this.chartListData.forEach((item) => {
            for (var obj in chartListObj) {
              if (obj == item.type) {
                item.value = chartListObj[obj];
              }
            }
          });

          var noData = this.chartListData.every((item) => {
            return item.value == 0;
          });
          console.log(this.chartListData);
          this.initChart(this.chartListData, noData);
        }
      });
    },
  },
  mounted() {
    var startTime = dayjs().subtract(6, "day").format("YYYY-MM-DD");
    var endTime = dayjs().format("YYYY-MM-DD");
    this.chartData(startTime, endTime);
    // this.initChart();
  },
};
</script>
<style scoped lang='scss'>
.echartDetail {
  display: flex;
  justify-content: center;
  align-items: center;
  ul {
    // margin-left: -35px;
    li {
      list-style: none;
      padding: 5px 0 5px 20px;
      position: relative;
      .squery {
        position: absolute;
        left: 0;
        top: 50%;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-top: -5px;
      }
    }
  }
}
</style>