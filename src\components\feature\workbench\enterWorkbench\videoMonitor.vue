<template>
  <div>
    <div class="work-heard">
      <h2>视频监控</h2>
      <span @click="goEnt">更多</span>
    </div>
    <div class="video-select">
      <el-select
        v-model="cameraIndexCode"
        placeholder="选择设备"
        size="mini"
        style="width: 100%; margin-bottom: 8px"
        placement="bottom"
        @change="changeCode"
        :disabled="!videoCheck"
        clearable
        @clear="clearVideo()"
      >
        <el-option
          v-for="item in options"
          :key="item.monitornum"
          :label="item.monitorname"
          :value="item.monitornum"
        >
        </el-option>
      </el-select>
      <div class="video-box" id="video-box">
        <!-- 四路视频播放器 -->
        <multiVideoPlayer ref="multiVideoPlayer" />
        <div class="items" id="playWind_items" style="display: none">
          <div class="items_button">
            <!-- <div class="button" @click="stop()">停止预览</div> -->
            <!-- <div class="button" @click="StopRealPlayAll()">关闭所有预览</div> -->
            <div class="button" @click="CapturePicture('JPEG')">抓图</div>
            <div class="button" @click="fullSreen()">全屏</div>
          </div>

          <!-- <div class="fenping_box">
            <img
              src="../../../../../static/img/fenping1.png"
              class="fenping"
              @click="arrangeWindow(1)"
            />
            <img
              src="../../../../../static/img/fenping2.png"
              class="fenping"
              @click="arrangeWindow(2)"
            />
            <img
              src="../../../../../static/img/fenping3.png"
              class="fenping"
              @click="arrangeWindow(3)"
            />
            <img
              src="../../../../../static/img/fenping4.png"
              class="fenping"
              @click="arrangeWindow(4)"
            />
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getVideoPreviewUrl } from "@/api/riskAssessment";
import { getVideoList } from "@/api/entList";
import { getVideoH5VideoH5 } from "@/api/entList";
import multiVideoPlayer from "@/components/feature/riskAssessment/videoOnlineMonitoring/multiVideoPlayer.vue";
export default {
  components: { multiVideoPlayer },
  data() {
    return {
      enterpId: "",
      sign: "0",
      options: [],
      iWind: 0,
      MaxIWind: 0,
      oPlugin: "",
      previewUrl: "",
      splitScreenValue: "",
      videoCheck: true,
      cameraIndexCode: "",
      resizeObserver: null,
      nowtime: new Date().getTime(),
      selectedKeys: "",
      iWndIndex: [],
    };
  },
  methods: {
    clearVideo() {
      // this.StopRealPlayAll();
      this.cameraIndexCode = "";
    },
    goEnt() {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("login/updataActiveName", "videoInspection");
      this.$store.commit("controler/updateEntId", this.enterpId);
      this.$store.commit("controler/updateEntModelName", "videoInspection");
    },
    changeCode() {
      if (this.cameraIndexCode) {
        // $(`#canvas_animation${this.iWind}`).show();
        //   let canvasEl = document.getElementById(
        //     `canvas_animation${this.iWind}`
        //   );
        //   let wd = canvasEl.getAttribute("width");
        //   let ht = canvasEl.getAttribute("height");
        //   this.canvasAnimation(`canvas_animation${this.iWind}`, wd, ht);
        // this.getVideo();
        this.realPlayNew(this.cameraIndexCode);
      }
    },
    /**
     * 播放某通道的实时视频
     * @param {*} channelId
     */
    realPlayNew(channelId) {
      ICC.getRealmonitor({
        optional: "/admin/API/MTS/Video/StartVideo",
        channelId: channelId,
        dataType: "3", //视频类型：1=视频, 2=音频, 3=音视频
        streamType: "2", //码流类型：1=主码流, 2=辅码流 默认为1，使用辅码流 码率低更加流畅
        trackId: "",
      }).then((data) => {
        this.realPlayer.playReal({
          rtspURL: data.rtspUrl, // string | array[string]
          decodeMode: "canvas", // 解码方式，video | canvas, h264默认video播放，h265默认canvas播放
          channelId: channelId, // 可选，用来标记当前视频播放的通道id
        });
      });
    },
    getData(enterpriseId) {
      this.videoCheck = false;
      this.enterpId = enterpriseId;
      getVideoList({
        enterpId: this.enterpId,
        sign: this.sign,
      }).then((res) => {
        this.videoCheck = true;
        if (res.data.code == 0) {
          this.options = res.data.data;
        }
      });
    },
    getVideo() {
      this.videoCheck = false;
      getVideoH5VideoH5(this.cameraIndexCode).then((res) => {
        this.previewUrl = res.data.data;
        this.realplay();
      });
    },
    //加载动画
    canvasAnimation(elName, wd, ht) {
      // console.log(elName, wd, ht);
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(255, 204, 0,1)";
        ctx.lineWidth = 3;
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        if (deg1 < 360) {
          if (deg1 < 180) {
            index *= 1.08;
          } else {
            index /= 1.08;
          }
          deg1 += index;
        }
        if (deg1 >= 360) {
          deg1 = 0;
        }
        ctx.stroke();
        ctx.beginPath();
        if (flag) {
          opa -= 0.02;
          if (opa < 0.2) {
            flag = false;
          }
        } else {
          opa += 0.02;
          if (opa > 1) {
            flag = true;
          }
        }
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgba(255, 204, 0," + opa + ")";
        ctx.fillText("正在取流...", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    //错误动画
    canvasError(elName, wd, ht) {
      var deg1 = 0;
      var index = 1;
      var opa = 1;
      var flag = true;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("取流失败", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    //初始动画
    canvasDef(elName, wd, ht) {
      var deg1 = 0;
      var canvas = document.getElementById(elName);
      var ctx = canvas.getContext("2d");
      canvas.height = ht;
      canvas.width = wd;
      var centerX = canvas.width / 2;
      var centerY = canvas.height / 2;
      var PI = Math.PI;
      ctx.fillRect(0, 0, canvas.width, canvas.height);
      // ctx.translate(centerX,centerY);
      function arc() {
        ctx.fillStyle = "rgba(0,0,0,1)";
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        ctx.beginPath();
        ctx.strokeStyle = "rgba(48,128,236,1)";
        // 过渡
        ctx.arc(
          centerX,
          centerY,
          100,
          (PI * 3) / 2,
          (deg1 * PI) / 180 + (PI * 3) / 2
        );
        ctx.stroke();
        ctx.beginPath();
        ctx.font = "normal 26px Arial";
        ctx.textAlign = "center";
        ctx.fillStyle = "rgb(255, 204, 0)";
        ctx.fillText("", centerX, centerY + 5);
        window.requestAnimationFrame(arc);
      }

      window.requestAnimationFrame(arc);
    },
    initPlugin() {
      const THIS = this;
      this.oPlugin.JS_SetWindowControlCallback({
        windowEventSelect: function (iWndIndex) {
          //插件选中窗口回调
          THIS.iWind = iWndIndex;
          // console.log(iWndIndex);
        },
        pluginErrorHandler: function (iWndIndex, iErrorCode, oError) {
          //插件错误回调
          console.error(
            `window-${iWndIndex}, errorCode: ${iErrorCode}`,
            oError
          );
          let canvasEl = document.getElementById(
            `canvas_animation${iWndIndex}`
          );
          let wd = canvasEl.getAttribute("width");
          let ht = canvasEl.getAttribute("height");
          THIS.canvasError(`canvas_animation${iWndIndex}`, wd, ht);
          THIS.iWndIndex[iWndIndex] = false;
        },
        windowEventOver: function (iWndIndex) {
          //鼠标移过回调
          //console.log(iWndIndex);
          if (THIS.iWndIndex[iWndIndex]) {
            $("#stopBtn" + iWndIndex).show();
            $("#stopBtn" + iWndIndex)
              .children()
              .eq(0)
              .click(() => {
                THIS.stopBtn(iWndIndex);
                THIS.iWndIndex[iWndIndex] = null;
              });
          }
        },
        windowEventOut: function (iWndIndex) {
          //鼠标移出回调
          //console.log(iWndIndex);
          if (!THIS.iWndIndex[iWndIndex]) {
            $("#stopBtn" + iWndIndex).hide();
          }
        },
        windowEventUp: function (iWndIndex) {
          //鼠标mouseup事件回调
          //console.log(iWndIndex);
        },
        windowFullCcreenChange: function (bFull) {
          //全屏切换回调
          console.log(bFull);
        },
        firstFrameDisplay: function (iWndIndex, iWidth, iHeight) {
          //首帧显示回调
          console.log(iWndIndex, iWidth, iHeight);
        },
        performanceLack: function () {
          //性能不足回调
        },
      });
      this.oPlugin
        .JS_SetOptions({
          bSupportSound: false, //是否支持音频，默认支持
          bSupporDoubleClickFull: false, //是否双击窗口全屏，默认支持
          // bOnlySupportMSE: true  //只支持MSE
          // bOnlySupportJSDecoder: true  //只支持JSDecoder
        })
        .then(function () {
          console.log("JS_SetOptions");
        });
    },

    getVersion() {
      this.oPlugin.JS_GetPluginVersion().then(function (szVersion) {
        console.log(szVersion);
      });
    },
    Destroy() {
      this.oPlugin.JS_DestroyWorker().then(function () {
        console.log("destroyWorker success");
      });
    },
    SetSecretKey() {
      var secretKey = document.getElementById("secretKey").value;
      this.oPlugin.JS_SetSecretKey(iWind, secretKey).then(
        function () {
          console.log("JS_SetSecretKey success");
        },
        function () {
          console.log("JS_SetSecretKey failed");
        }
      );
    },

    realplay() {
      this.videoCheck = true;
      var url = this.previewUrl; // + "?token=" + szToken;  //"ws://10.19.141.64:7314/EUrl/ybcwxHO"; 联网共享下该url和playurl是一样的
      let streamType = this.getRadioValue("streamType");
      // console.log(this.oPlugin);
      const THIS = this;
      THIS.iWndIndex[THIS.iWind] = true;
      this.oPlugin
        .JS_Play(
          url,
          {
            playURL: url,
            mode: parseInt(streamType),
            session: "", //定制设备
            token: "",
          },
          this.iWind
        )
        .then(
          function () {
            console.log("realplay success");
            $(`#canvas_animation${THIS.iWind}`).hide();
          },
          function () {
            console.log("realplay failed");
            THIS.$message.error("取流异常");
          }
        );
    },
    getRadioValue(radioName) {
      let value = "";
      document.getElementsByName(radioName).forEach((el) => {
        if (el.checked) {
          value = el.value;
        }
      });
      return value;
    },
    playbackLocation() {
      var szStartDate = document.getElementById("sDate1").value;
      var szEndDate = document.getElementById("eDate1").value;
      this.oPlugin.JS_Seek(this.iWind, szStartDate, szEndDate).then(
        function () {
          console.log("playbackLocation success");
        },
        function () {
          console.log("playbackLocation failed");
        }
      );
    },
    arrangeWindow(i) {
      this.oPlugin.JS_ArrangeWindow(i).then(function () {
        console.log("JS_ArrangeWindow success");
        $(`#canvas_animation${i}`)[0].width = $(`#canvas_animation${i}`)
          .parent()
          .width();
        $(`#canvas_animation${i}`)[0].height = $(`#canvas_animation${i}`)
          .parent()
          .height();
      });
    },
    GetVolume() {
      this.oPlugin.JS_GetVolume(this.iWind).then(function (i) {
        console.log(i);
      });
    },
    CapturePicture(szType) {
      const timestamp = new Date();
      this.oPlugin
        .JS_CapturePicture(this.iWind, `img-${timestamp}`, szType)
        .then(
          function () {
            console.log("CapturePicture success");
          },
          function () {
            console.log("CapturePicture failed");
          }
        );
    },
    //关闭全部预览
    StopRealPlayAll() {
      this.oPlugin.JS_StopRealPlayAll().then(
        () => {
          console.log("JS_StopRealPlayAll success");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        },
        () => {
          console.log("JS_StopRealPlayAll failed");
          for (var i = 0; i < $(".parent-wnd").children().length; i++) {
            this.iWndIndex[i] = null;
            $(`#canvas_animation${i}`).show();
            this.canvasDef(
              `canvas_animation${i}`,
              $(`#canvas_animation${i}`).parent().width(),
              $(`#canvas_animation${i}`).parent().height()
            );
          }
        }
      );
    },
    dateFormat(oDate, fmt) {
      var o = {
        "M+": oDate.getMonth() + 1, //月份
        "d+": oDate.getDate(), //日
        "h+": oDate.getHours(), //小时
        "m+": oDate.getMinutes(), //分
        "s+": oDate.getSeconds(), //秒
        "q+": Math.floor((oDate.getMonth() + 3) / 3), //季度
        S: oDate.getMilliseconds(), //毫秒
      };
      if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(
          RegExp.$1,
          (oDate.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      }
      for (var k in o) {
        if (new RegExp("(" + k + ")").test(fmt)) {
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length == 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
        }
      }
      return fmt;
    },
    stopRecord() {
      this.oPlugin.JS_StopSave(this.iWind).then(
        function () {
          console.log("stopRecord success");
        },
        function () {
          console.log("stopRecord failed");
        }
      );
    },
    startTalk() {
      var talkurl = document.getElementById("talkurl").value;
      this.oPlugin.JS_StartTalk(talkurl).then(
        function () {
          console.log("startTalk success");
        },
        function () {
          console.log("startTalk failed");
        }
      );
    },
    fullSreen() {
      this.oPlugin.JS_FullScreenDisplay(true).then(
        function () {
          console.log("JS_FullScreenDisplay success");
        },
        function () {
          console.log("JS_FullScreenDisplay failed");
        }
      );
    },
    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
    setVideoSize() {
      var playWind = document.getElementById("playWind_items");
      this.oPlugin.JS_Resize({ iWidth: playWind.scrollWidth }).then(
        () => {
          console.info("JS_Resize success");
          // do you want...
        },
        (err) => {
          console.info("JS_Resize failed");
          // do you want...
        }
      );
    },
    initializationVideo() {
      const THIS = this;
      function getScript(url, fn) {
        if ("string" === typeof url) {
          url = [url]; //如果不是数组带个套
        }
        var ok = 0; //加载成功几个js
        var len = url.length; //一共几个js
        var head = document.getElementsByTagName("head").item(0);
        var js = null;
        var _url;
        var create = function (url) {
          //创建js
          var oScript = null;
          oScript = document.createElement("script");
          oScript.type = "text/javascript";
          oScript.src = url;
          head.appendChild(oScript);
          return oScript;
        };
        for (var i = 0; i < len; i++) {
          _url = url[i];
          js = create(_url); //创建js
          fn &&
            (js.onload = function () {
              if (++ok >= len) {
                //如果加载完所有的js则执行回调
                fn();
              }
            });
        }
      }
      //var szBrowserVersion = "";
      //var iBrowserVersion = -1;
      var aScript = [];
      var szUserAgent = navigator.userAgent.toLowerCase();
      // if (szUserAgent.match(/chrome\/([\d.]+)/) || szUserAgent.match(/Firefox\/([\d.]+)/)) {
      //szBrowserVersion = szUserAgent.match(/chrome\/([\d.]+)/)[1];
      //iBrowserVersion = parseInt(szBrowserVersion.split(".")[0], 10);
      if (
        szUserAgent.indexOf("win64") > -1 ||
        szUserAgent.indexOf("x64") > -1
      ) {
        aScript = ["../../../../../static/h5player.min.js"];
      } else {
        aScript = ["../../../../../static/h5player.min.js"];
      }
      // }
      var playWind = document.getElementById("playWind_items");
      // console.log(playWind.scrollWidth);
      getScript(aScript, function () {
        //初始化插件
        //初始化插件
        THIS.oPlugin = new JSPlugin({
          szId: "playWind",
          iWidth: playWind.scrollWidth,
          // iHeight: 500,
          iMaxSplit: 4,
          iCurrentSplit: 1,
          szBasePath: "../../../../../static/",
          oStyle: {
            border: "#343434",
            borderSelect: "#FFCC00",
            background: "#000",
          },
          openDebug: false,
        });
        THIS.initPlugin();
        //初始化播放器大小
        THIS.setVideoSize();
        for (var i = 0; i < $(".parent-wnd").children().length; i++) {
          THIS.iWndIndex[i] = null;
          $("#playWind_playCanvas" + i).before(
            `<canvas id="canvas_animation${i}" width="${$(
              "#playWind_playCanvas" + i
            ).width()}" height="${$(
              "#playWind_playCanvas" + i
            ).height()}"></canvas>`
          );
          $("#canvas_animation" + i).css({
            transform: "scale(0.5) translate(-50%, -50%)",
            "transform-origin": "0 0",
            position: "absolute",
            top: "50%",
            left: "50%",
            // zIndex: 200,
          });
          $("#canvas_draw" + i).css({
            cursor: "default",
          });
          $("#playWindow" + i)
            .after(`<div id="stopBtn${i}" style="width:100%;height: 35px;background: rgba(0,0,0,0.4);position: absolute;top:0;left:0;z-index: 9999999;display:none;cursor: default;">
          <i class="el-icon-close"  style="float: right;line-height:35px;margin-right:10px;font-size:18px;color:#eee;cursor: pointer;"></i>
        </div>`);
        }
      });
    },
    stopBtn(i) {
      this.oPlugin.JS_Stop(i).then(
        () => {
          console.log("stop success");
          this.cameraIndexCode = "";
          $(`#canvas_animation${i}`).show();
          this.canvasDef(
            `canvas_animation${i}`,
            $(`#canvas_animation${i}`).parent().width(),
            $(`#canvas_animation${i}`).parent().height()
          );
          $("#stopBtn" + i).hide();
        },
        function (e) {
          console.error("stop failed", e);
        }
      );
    },
  },
  async created() {
    await ICC.init();
  },
  //生命周期 - 挂载完成(可以访问DOM元素)
  mounted() {
    const THIS = this;
    //初始化播放器
    // this.initializationVideo();
    // window.onresize = () => {
    //   THIS.setVideoSize();
    // };
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    // 初始化平台信息获取
    let serverAdress = sessionStorage.getItem("videoApi");
    // if (process.env.NODE_ENV === 'development') {
    //     serverAdress = '************:2443';
    // } else {
    //     serverAdress = '***********:9100';
    // }
    // 构造播放器，构造播放器后，通过获取实时视频rtsp接口，获取到视频的rtsp地址
    if (!this.realPlayer) {
      this.realPlayer = new WSPlayer({
        el: "ws-real-player", // 必传
        type: "real", // real | record
        serverIp: serverAdress,
        num: 4,
        showControl: true,
      });
    }
  },
  beforeDestroy() {
    // this.StopRealPlayAll();
  },
};
</script>
<style lang="scss" scoped>
.work-heard {
  width: 92%;
  margin: 0 auto;
  height: 40px;
  position: relative;
  h2 {
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 0;
    color: #3b4046;
  }
  span {
    position: absolute;
    right: 0px;
    top: 10px;
    font-size: 14px;
    color: #3977ea;
    cursor: pointer;
  }
  p {
    width: 120px;
    position: absolute;
    left: 130px;
    top: 5px;
  }
}
.video-select {
  width: 92%;
  margin: 0 auto;
  .video-box {
    // width:calc(100% - 60px);
    width: 100%;
    height: 225px;
    & > .items {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #2e2e2e;
      padding-left: 10px;
      height: 35px;
      .items_button {
        width: 50%;
        height: 20px;
        display: flex;
        align-items: center;
        font-size: 12px;
      }
      .button {
        color: #fff;
        border-radius: 5px;
        padding: 2px 6px;
        margin-right: 15px;
        cursor: pointer;
        background-color: #777;
      }
      .fenping_box {
        width: 150px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .fenping {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  #playWind {
    height: 210px;
  }
  #ws-real-player {
    height: 250px;
  }
}
</style>
