<!-- 企业报告 -->
<template>
  <el-dialog
    title="企业报告"
    :visible="visible"
    @close="closeBoolean(false)"
    width="1500px"
    top="5vh"
    v-dialog-drag
    :key="dialogKey"
    :close-on-click-modal="true"
  >
    <template slot="title">
      <div class="enterprise-detail">
        <div class="enterprise-left">
          <div class="enterprise-name">
            {{ titleInfo.enterpName }}
            <span class="tag-common status">{{ titleInfo.businessType == 0 ? "正常生产" : "长期停产" }}</span>
          </div>
          <div class="enterprise-info">
            <div class="info">
              统一社会信用代码: {{ titleInfo.entcreditCode }}
            </div>
            <div class="info">经营场所地址: {{ titleInfo.businessAddress }}</div>
          </div>
        </div>
        <div class="enterprise-middle">
          <div>{{ titleInfo.score }}</div>
          <div class="index-text">指标分数</div>
        </div>
        <div class="enterprise-right">
          <div>企业画像报告 第146期</div>
          <div>
            <span class="tag-common">首次报告</span
            ><i class="el-icon-time"></i> {{ titleInfo.createDate }}
          </div>
        </div>
      </div>
    </template>
    <div class="dialog-container">
      <div class="tab-container">
        <el-tabs
          v-model="activeName"
          @tab-click="handleTab"
          v-loading="tabLoading"
        >
          <el-tab-pane label="企业基本信息" name="first">
            <BaseInfo :enterpId="enterpriseInfo.enterpId"></BaseInfo>
          </el-tab-pane>
          <!-- <el-tab-pane label="行业分类信息" name="second"> 
            <IndustyInfo></IndustyInfo>
          </el-tab-pane> -->
          <el-tab-pane label="两重点一重大" name="second">
          <FocusOnAndMajor ref="FocusOnAndMajor"></FocusOnAndMajor>
        </el-tab-pane>
          <el-tab-pane label="安全基本信息" name="third">
            <!-- <safeProduction></safeProduction> -->
            <SafetyInformation
            style="height: 480px;"
            :enterpriseId="enterpriseInfo.id"
            ref="safetyInformation"
          ></SafetyInformation>
          </el-tab-pane>
          <el-tab-pane label="动态感知" name="fourth">
          <DynamicPerception></DynamicPerception>  
          </el-tab-pane>

          <el-tab-pane label="画像指数信息" name="fifth">
            <div style="height: 480px;overflow-y: scroll;">
            <IndexAnalysis :enterpriseInfo="enterpriseInfo"> </IndexAnalysis></div> </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import BaseInfo from "./baseInfo.vue";
import IndustyInfo from "./industyInfo";
import safeProduction from "./safeProduction.vue";
import FocusOnAndMajor from "./FocusOnAndMajor.vue";
import DynamicPerception from "./dynamicPerception.vue";
import SafetyInformation from "../../../enterpriseManagement/safetyInformation/safety.vue";
import IndexAnalysis from "../../enterprisePortraitList/components/IndexAnalysis.vue";
import { reportDetail, riskReportInfo } from "../mock";
import { getInformationBasicInfo } from "@/api/entList.js";
import {
  getPortraitInfoEnterp,
} from "@/api/enterprisePortrait";
export default {
  components: {
    BaseInfo,
    IndustyInfo,
    safeProduction,
    FocusOnAndMajor,
    SafetyInformation,
    DynamicPerception,
    IndexAnalysis
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    enterpriseInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      titleInfo: {},
      dialogKey: "",
      tabLoading: false,
      activeName: "first",
      reportInfo: {},
      riskReportInfo: riskReportInfo,
    };
  },
  mounted() {
    this.reportInfo = reportDetail.data;
    // 根据企业id获取企业基本信息
    this.getEnterpriseInfo();
    // 根据企业id获取企业画像报告
    this.getEnterpAnalysis();
  },
  methods: {
     // 查询企业基本信息
    getEnterpriseInfo() {
      getInformationBasicInfo(this.enterpriseInfo.enterpId).then((res) => {
        if (res.status === 200) {
          this.$set(this.titleInfo, "enterpName", res.data.data.enterprise.enterpName || null);
          this.$set(this.titleInfo, "businessType", res.data.data.enterprise.businessType || null);
          this.$set(this.titleInfo, "entcreditCode", res.data.data.enterprise.entcreditCode || null);
          this.$set(this.titleInfo, "businessAddress", res.data.data.enterprise.businessAddress || null);
        }
      });
    },
    // 获取企业画像报告
    async getEnterpAnalysis() {
      await getPortraitInfoEnterp({
        enterpId: this.enterpriseInfo.enterpId,
      }).then((res) => {
        if (res.data.status == 200) {
          this.$set(this.titleInfo, "score", res.data.data.score || null);
          this.$set(this.titleInfo, "createDate", res.data.data.createDate || null);
        }
      });
    },
    handleTab(tab) {
      this.activeName = tab.name;
    },
    closeBoolean(boolean) {
      this.$emit("close", boolean);
    },
    handleSubmit() {
      this.closeBoolean(false);
    },
  },
};
</script>

<style lang="scss" scoped>
.dialog-container {
  // height: 550px;
}
.enterprise-detail {
  display: flex;
  align-items: center;
  color: #fff;
  .enterprise-left {
    flex: 1;
    .enterprise-name {
      font-size: 16px;
      font-weight: 600;
      .status {
        margin-left: 16px;
      }
    }
    .enterprise-info {
      margin-top: 8px;
      display: flex;
      align-items: center;
      .info {
        margin-right: 20px;
      }
    }
  }
  .enterprise-middle {
    width: 80px;
    margin: 0 20px;
    .index-text {
      font-size: 12px;
      font-weight: 600;
    }
  }
  .enterprise-right {
    width: 240px;
    border-left: 1px solid #ccc;
    padding-left: 20px;
  }
}
::v-deep .el-dialog__body {
  padding-top: 10px;
}
.tab-container {
  height: 540px;
}
.tag-common {
  display: inline-block;
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
  background: #a4e27a;
  font-size: 12px;
  font-weight: normal;
  border-radius: 5px;
  margin-right: 8px;
  border: 1px solid #fff;
  cursor: pointer;
}
</style>
