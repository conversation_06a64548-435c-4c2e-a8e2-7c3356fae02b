!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("mars3d")):"function"==typeof define&&define.amd?define(["exports","mars3d"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self)["mars3d-supermap"]={},e.mars3d)}(this,(function(e,t){"use strict";function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var o=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,o.get?o:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,t}var o=r(t);function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,o)}return r}function i(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach((function(t){u(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var r=0;r<t.length;r++){var o=t[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}function l(e,t,r){return t&&s(e.prototype,t),r&&s(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function u(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&f(e,t)}function p(e){return p=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},p(e)}function f(e,t){return f=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},f(e,t)}function y(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function h(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=p(e);if(t){var n=p(this).constructor;r=Reflect.construct(o,arguments,n)}else r=o.apply(this,arguments);return y(this,r)}}function v(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=p(e)););return e}function m(){return m="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(e,t,r){var o=v(e,t);if(o){var n=Object.getOwnPropertyDescriptor(o,t);return n.get?n.get.call(arguments.length<3?e:r):n.value}},m.apply(this,arguments)}var d=o.Cesium,_=function(e){c(r,e);var t=h(r);function r(){return a(this,r),t.apply(this,arguments)}return l(r,[{key:"layer",get:function(){return this._layerArr}},{key:"s3mOptions",get:function(){return this.options.s3mOptions},set:function(e){for(var t in e){var r=e[t];this.options.s3mOptions[t]=r,"transparentBackColor"===t?r=d.Color.fromCssColorString(r):"transparentBackColorTolerance"===t&&(r=Number(r));for(var o=0;o<this._layerArr.length;o++){var n=this._layerArr[o];null!=n&&(n[t]=r)}}}},{key:"_showHook",value:function(e){this.eachLayer((function(t){t.visible=e,t.show=e}),this)}},{key:"_mountedHook",value:function(){var e=this;if(!this._map.scene.open)throw new Error("请引入 超图版本Cesium库 或 超图S3M插件 ");(this.options.layername?this._map.scene.addS3MTilesLayerByScp(this.options.url,{name:this.options.layername,autoSetVie:this.options.flyTo,cullEnabled:this.options.cullEnabled}):this._map.scene.open(this.options.url,this.options.sceneName,{autoSetVie:this.options.flyTo})).then((function(t){Array.isArray(t)?e._layerArr=t:e._layerArr=[t];for(var r=0;r<e._layerArr.length;r++){var n=e._layerArr[r];if(n)try{e._initModelItem(n)}catch(e){o.Log.logError("s3m图层初始化出错",e)}}e._showHook(e.show),e.options.flyTo&&e.flyToByAnimationEnd(),e._readyPromise.resolve(e),e.fire(o.EventType.load,{layers:e._layerArr})}),(function(t){e._readyPromise&&e._readyPromise.reject(t)}))}},{key:"_initModelItem",value:function(e){var t,r;if(this.options.s3mOptions)for(var n in this.options.s3mOptions){var i=this.options.s3mOptions[n];e[n]="transparentBackColor"===n?d.Color.fromCssColorString(i):"transparentBackColorTolerance"===n?Number(i):i}this.options.highlight&&(e.selectedColor=o.Util.getColorByStyle(this.options.highlight)),null!==(t=this.options)&&void 0!==t&&null!==(r=t.position)&&void 0!==r&&r.alt&&(e.style3D.altitudeMode=d.HeightReference.NONE,e.style3D.bottomAltitude=this.options.position.alt,e.refresh&&e.refresh())}},{key:"_addedHook",value:function(){this._showHook(this.show)}},{key:"_removedHook",value:function(){this._showHook(!1)}},{key:"eachLayer",value:function(e,t){if(this._layerArr)return this._layerArr.forEach((function(r){e.call(t,r)})),this}},{key:"setOpacity",value:function(e){this.eachLayer((function(t){t.style3D.fillForeColor.alpha=e}),this)}},{key:"flyTo",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.options.center?this._map.setCameraView(this.options.center,e):this.options.extent?this._map.flyToExtent(this.options.extent,e):void 0}}]),r}(o.layer.BaseLayer);o.layer.S3MLayer=_,o.LayerUtil.register("supermap_s3m",_);var g=o.Cesium,b=function(e){c(r,e);var t=h(r);function r(){return a(this,r),t.apply(this,arguments)}return l(r,[{key:"_createImageryProvider",value:function(e){return O(e)}},{key:"_addedHook",value:function(){m(p(r.prototype),"_addedHook",this).call(this),g.defined(this.options.transparentBackColor)&&(this._imageryLayer.transparentBackColor=o.Util.getCesiumColor(this.options.transparentBackColor),this._imageryLayer.transparentBackColorTolerance=this.options.transparentBackColorTolerance)}}]),r}(o.layer.BaseTileLayer);function O(e){return(e=o.LayerUtil.converOptions(e)).url instanceof g.Resource&&(e.url=e.url.url),g.defined(e.transparentBackColor)&&(delete e.transparentBackColor,delete e.transparentBackColorTolerance),new g.SuperMapImageryProvider(e)}b.createImageryProvider=O,o.layer.SmImgLayer=b;var k="supermap_img";o.LayerUtil.register(k,b),o.LayerUtil.registerImageryProvider(k,O);var w=o.Cesium,P=function(e){c(r,e);var t=h(r);function r(){return a(this,r),t.apply(this,arguments)}return l(r,[{key:"layer",get:function(){return this._mvtLayer}},{key:"_mountedHook",value:function(){var e=this;this._mvtLayer=this._map.scene.addVectorTilesMap(this.options),this._mvtLayer.readyPromise.then((function(e){}));var t=this._map.scene,r=new w.ScreenSpaceEventHandler(t.canvas);r.setInputAction((function(r){if(e.show){var n=o.PointUtil.getCurrentMousePosition(t,r.position);e._mvtLayer.queryRenderedFeatures([n],{}).reduce((function(t,i){var a=i.feature.properties;if(a){var s=o.Util.getPopupForConfig(e.options,a),l={data:a,event:r};e._map.openPopup(n,s,l)}}))}}),w.ScreenSpaceEventType.LEFT_CLICK),this.handler=r}},{key:"_addedHook",value:function(){this._mvtLayer.show=!0}},{key:"_removedHook",value:function(){this._mvtLayer&&(this._mvtLayer.show=!1)}},{key:"setOpacity",value:function(e){this._mvtLayer&&(this._mvtLayer.alpha=parseFloat(e))}},{key:"flyTo",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this.options.center?this._map.setCameraView(this.options.center,e):this.options.extent?this._map.flyToExtent(this.options.extent,e):this._mvtLayer?this._map.camera.flyTo(i(i({},e),{},{destination:this._mvtLayer.rectangle})):Promise.resolve(!1)}}]),r}(o.layer.BaseLayer);o.layer.SmMvtLayer=P,o.LayerUtil.register("supermap_mvt",P),e.S3MLayer=_,e.SmImgLayer=b,e.SmMvtLayer=P,Object.defineProperty(e,"__esModule",{value:!0})}));
