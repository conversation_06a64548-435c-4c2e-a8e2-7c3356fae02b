<template>
  <div class="preventiveAnalysis">
    <div class="search-box">
      <span>时间</span>
      <el-date-picker
        v-model="value1"
        size="small"
        type="datetimerange"
        value-format="yyyy-MM-dd HH:mm:ss"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="searchTime"
        unlink-panels
        style="width: 370px"
        :picker-options="{
          disabledDate: (time) => {
            return (
              time.getTime() >
              new Date(new Date().Format('yyyy-MM-dd 23:59:59')).getTime()
            );
          },
        }"
      ></el-date-picker>
      <el-button type="primary" size="small" @click="handleSearch"
        >查询</el-button
      >
    </div>
    <div class="statistic-box">
      <div class="statistic-total">
        <img
          class="statisitc-icon"
          src="../../../../../static/img/icon-danger.png"
          alt=""
        />
        <div class="card-item">
          <div class="card-title">隐患总数</div>
          <div class="card-number">{{ majorData.pitfallCount }}</div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.rectified }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 已整改
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.notRectified }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 未整改
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.slippage }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 已逾期
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">{{ majorData.rectificationRate }}</div>
          <div class="card-content">
            <img src="../../../../../static/img/blue-point.png" alt="" /> 整改率
          </div>
        </div>
      </div>
      <div class="statistic-card">
        <div class="card-item">
          <div class="card-title">重大隐患</div>
          <div class="card-number">{{ dangerData.pitfallCount }}</div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 已整改
            {{ dangerData.rectified }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 未整改
            {{ dangerData.notRectified }}
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 已逾期
            {{ dangerData.slippage }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/red-point.png" alt="" /> 整改率
            {{ dangerData.rectificationRate }}
          </div>
        </div>
      </div>
      <div class="statistic-card card-yellow">
        <div class="card-item">
          <div class="card-title">一般隐患</div>
          <div class="card-number">{{ normalData.pitfallCount }}</div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            已整改 {{ normalData.rectified }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            未整改 {{ normalData.notRectified }}
          </div>
        </div>
        <div class="card-item">
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            已逾期 {{ normalData.slippage }}
          </div>
          <div class="card-content">
            <img src="../../../../../static/img/orange-point.png" alt="" />
            整改率 {{ normalData.rectificationRate }}
          </div>
        </div>
      </div>
    </div>
    <el-table
      :data="tableData"
      border
      v-loading="loading"
      style="width: 100%"
      :header-cell-style="{
        textAlign: 'center',
        color: 'rgb(51, 51, 51)',
        backgroundColor: 'rgb(242, 246, 255)',
      }"
    >
      <el-table-column type="index" label="序号" width="60"> </el-table-column>
      <el-table-column
        prop="qxmc"
        label="区域名称"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column prop="pitfallCount" label="隐患总数" width="160">
      </el-table-column>
      <el-table-column
        prop="pitfallCountMajor"
        label="重大隐患数量"
        width="160"
      >
      </el-table-column>
      <el-table-column
        prop="pitfallCountGeneral"
        label="一般隐患数量"
        width="160"
      >
      </el-table-column>
      <el-table-column prop="rectified" label="已整改数量" width="160">
      </el-table-column>
      <el-table-column prop="notRectified" label="未整改数量" width="160">
      </el-table-column>
      <el-table-column prop="expectNumber" label="预期数量" width="160">
      </el-table-column>
      <el-table-column label="操作" width="150px" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleDetails(scope.row)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="params.nowPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>
  </div>
</template>
<script>
import { preventionAnalysisAll } from "@/api/riskAssessment";
export default {
  name: "preventiveAnalysis",
  components: {},
  data() {
    return {
      value1: null,
      params: {
        nowPage: 1,
        pageSize: 10,
        startTime: null,
        endTime: null,
        administerType: [],
        enterpriseCode: "",
        hazardLevel: [],
        hazardSource: [],
        hazardStatus: [],
        hazardType: [],
      },
      total: 0,
      tableData: [],
      loading: false,
      showDetail: false,
      majorData: {
        pitfallCount: 0, //总数
        rectified: 0, //已整改
        slippage: 0, //预期
        notRectified: 0, //未整改
        rectificationRate: 0, //整改率
      },
      dangerData: {
        pitfallCount: 0, //总数
        rectified: 0, //已整改
        slippage: 0, //预期
        notRectified: 0, //未整改
        rectificationRate: 0, //整改率
      },
      normalData: {
        pitfallCount: 0, //总数
        rectified: 0, //已整改
        slippage: 0, //预期
        notRectified: 0, //未整改
        rectificationRate: 0, //整改率
      },
    };
  },
  created() {
    this.getData();
  },
  methods: {
    searchTime(value) {
      if (value) {
        this.params.startTime = value[0];
        this.params.endTime = value[1];
      } else {
        this.params.startTime = null;
        this.params.endTime = null;
      }
    },
    handleSearch() {
      this.params.nowPage = 1;
      this.getData();
    },
    handleSizeChange(val) {
      this.params.pageSize = val;
    },
    handleCurrentChange(val) {
      this.params.nowPage = val;
    },
    handleDetails(row) {
      this.$emit("detail", row);
    },
    getData() {
      this.loading = true;
      preventionAnalysisAll(this.params).then((res) => {
        if (res.data.status == 200) {
          this.tableData = res.data.data.dtoList;
          // this.total = res.data.data.total;
          this.majorData = res.data.data.dtoCount2;
          this.dangerData = res.data.data.dtoCount1;
          this.normalData = res.data.data.dtoCount0;
        }
      }).finally(() => {
        this.loading = false;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.preventiveAnalysis {
  .search-box {
    display: flex;
    align-items: center;
    padding: 10px;
    background-color: rgb(242, 246, 255);
    & > * {
      margin-right: 16px;
    }
  }
  .pagination {
    padding: 10px;
    text-align: right;
  }
  .statistic-box {
    display: flex;
    height: 120px;
    background-color: rgb(242, 246, 255);
    margin-bottom: 10px;
    .statistic-card {
      width: 431px;
      height: 105px;
      background: url("../../../../../static/img/card-bg-red.png") no-repeat
        center center;
      background-size: 100% 100%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-left: 10px;
      .card-item {
        color: #000;
        flex: 1;
        .card-title {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
        }
        .card-number {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
          color: #fe5552;
        }
        .card-content {
          display: flex;
          align-items: center;
          text-align: left;
          height: 28px;
          img {
            margin-right: 5px;
          }
        }
      }
    }
    .card-yellow {
      background: url("../../../../../static/img/card-bg-yellow.png") no-repeat
        center center;
      background-size: 100% 100%;
      .card-item {
        .card-number {
          color: #ff940a;
        }
      }
    }
    .statistic-total {
      width: 531px;
      height: 105px;
      display: flex;
      align-items: center;
      .statisitc-icon {
        width: 60px;
        height: 60px;
        margin: 0 16px;
      }
      .card-item {
        width: 150px;
        .card-content {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 28px;
          img {
            margin-right: 5px;
          }
        }
        .card-title {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
        }
        .card-number {
          font-size: 18px;
          font-weight: 600;
          text-align: center;
          color: #1d7ee7;
        }
      }
    }
  }
}
</style>
