<template>
  <div class="Risk-container">
    <div class="radar-box" ref="radar-box"></div>
    <!-- 图例 start -->
    <div class="legendList">
      <div
        class="legend-item"
        v-for="(item, index) in legendList"
        :key="item.name"
      >
        <span>
          <i :class="item.icon"></i>
          {{ item.name }}
        </span>
        <input
          type="checkbox"
          v-if="item.isDone"
          :value="item.name"
          v-model="check"
          style="margin-left: 5px"
          @change="handleChange($event, item.name, index)"
        />
      </div>
    </div>
    <!-- 图例 end -->
    <!-- 政策法规依据描述 start -->
    <div class="policy-desc" style="padding:10px;font-size:14px;color:#666;">
      政策法规依据：《中华人民共和国安全生产法》、《注册安全工程师管理规定》、国家安全生产监督管理总局令第11号《企业安全生产费用提取和使用管理办法》（财企〔2012〕16 号）
    </div>
    <!-- 政策法规依据描述 end -->

    <div class="total" @click.stop="handleTotalClick">
      <p
        :class="[
          { blue: activeClassName === 'blue' },
          { yellow: activeClassName === 'yellow' },
          { orange: activeClassName === 'orange' },
          { red: activeClassName === 'red' },
        ]"
      >
      {{ planData.score }}
      </p>
      <p>总体风险指数</p>
    </div>

    <div
      class="risk man"
    >
      <p>
        <span>{{ indicator[0].indicatorData.value }}</span
        ><small>分</small>
      </p>
      <p>{{ indicator[0].indicatorData.describe }}</p>
      <p>{{ indicator[0].indicatorData.describeEn }}</p>
    </div>
    <div
      class="risk things"
    >
      <p>
        <span>{{ indicator[1].indicatorData.value }}</span
        ><small>分</small>
      </p>
      <p>{{ indicator[1].indicatorData.describe }}</p>
      <p>{{ indicator[1].indicatorData.describeEn }}</p>
    </div>
    <div
      class="risk management"
    >
      <p>
        <span>{{ indicator[2].indicatorData.value }}</span
        ><small>分</small>
      </p>
      <p>{{ indicator[2].indicatorData.describe }}</p>
      <p>{{ indicator[2].indicatorData.describeEn }}</p>
    </div>
    <div
      class="risk environment"
    >
      <p>
        <span>{{ indicator[3].indicatorData.value }}</span
        ><small>分</small>
      </p>
      <p>{{ indicator[3].indicatorData.describe }}</p>
      <p>{{ indicator[3].indicatorData.describeEn }}</p>
    </div>
    <div
      class="risk supervision"
    >
      <p>
        <span>{{ indicator[4].indicatorData.value }}</span
        ><small>分</small>
      </p>
      <p>{{ indicator[4].indicatorData.describe }}</p>
      <p>{{ indicator[4].indicatorData.describeEn }}</p>
    </div>
    <div
      class="risk performance"
    >
      <p>
        <span>{{ indicator[5].indicatorData.value }}</span
        ><small>分</small>
      </p>
      <p>{{ indicator[5].indicatorData.describe }}</p>
      <p>{{ indicator[5].indicatorData.describeEn }}</p>
    </div>

    <!-- 文字添加边框 -->
    <svg width="0" height="0">
      <filter id="dilate">
        <feMorphology
          in="SourceAlpha"
          result="DILATED"
          operator="dilate"
          radius="3"
        ></feMorphology>
        <feFlood flood-color="#fff" flood-opacity="1" result="flood"></feFlood>
        <feComposite
          in="flood"
          in2="DILATED"
          operator="in"
          result="OUTLINE"
        ></feComposite>
        <feMerge>
          <feMergeNode in="OUTLINE" />
          <feMergeNode in="SourceGraphic" />
        </feMerge>
      </filter>
    </svg>
  </div>
</template>

<script>
import getRistRenderOption from "./RiskRadar.js";
const colorList = ["#415ef0", "#13c752", "#facd89", "#4ca2c2"];
export default {
  name: "RiskRadar",
  props: {
    enterpriseData: {
      // 企业得分数据
      type: Array,
    },
    sourceData: {
      // 所有的源数据
      type: Object,
    },
    index: {
      type: Number,
    },
    name: {
      type: String,
    },
    activeClassName: {
      type: String,
      default: "back",
    },
    planData: {
      type: Object,
      default: () => {},
    },
    legendInfo: {
      type: Object,
      default: () => {},
    },
  },
  computed: {
    indicatorMaxData() {
      // radar 指示器的最大值
      return [
        this.sourceData.manFullMark,
        this.sourceData.thingsFullMark,
        this.sourceData.managementFullMark,
        this.sourceData.environmentFullMark,
        this.sourceData.supervisionFullMark,
        this.sourceData.performanceFullMark,
      ];
    },
  },
  watch: {
    index: {
      handler() {
        let num = this.indicator.findIndex(
          (item) => item.mainCode.indexOf(this.name)
        );
        this.num = num;
        this.handleClick(num, this.name);
      },
    },
  },
  data() {
    return {
      num: 0,
      currentName: "人Man", // 指示器当前名称
      rotate: 0, // 初始角度
      uuitNo: "", // 行业最优质最高的企业id
      check: [],
      chartsData: [], // radar init 数据
      IndustryMeanData: [], //行业均值数据
      RegionalMeanData: [], // 区域均值
      bestQualityData: [], // 行业最优值
      bestQualityId: [], // 最优质id企业
      series: [],
      legendList: [
        // 图例
        { icon: "circle1", name: "企业得分", isDone: false },
        { icon: "circle2", name: "行业最优值", isDone: true },
        // {icon:'circle3',name:'政府目标值',isDone:true},
        { icon: "circle4", name: "行业均值", isDone: true },
        { icon: "circle5", name: "区域均值", isDone: true },
      ],
      indicator: [
        // 指示器
        {
          id: 1,
          mainCode: "man",
          indicatorData: {
            value: this.enterpriseData[0],
            describe: "人Man",
          },
          tooltipData: {
            describe: "人Man",
            describeEn: "",
            value1: this.enterpriseData[0],
            value2: this.sourceData.manFullMark, // 指数满分
          },
        },
        {
          id: 2,
          mainCode: "things",
          indicatorData: {
            describe: "物(机、料)Things",
            value: this.enterpriseData[1],
          },
          tooltipData: {
            describe: "物(机、料)",
            value1: this.enterpriseData[1],
            value2: this.sourceData.thingsFullMark,
          },
        },
        {
          id: 3,
          mainCode: "management",
          indicatorData: {
            describe: "管(法)",
            describeEn: "Management",
            value: this.enterpriseData[2],
          },
          tooltipData: {
            describe: "管(法)",
            value1: this.enterpriseData[2],
            value2: this.sourceData.managementFullMark,
          },
        },
        {
          id: 4,
          mainCode: "environment",
          indicatorData: {
            describe: "环(境)",
            describeEn: "Environment",
            value: this.enterpriseData[3],
          },
          tooltipData: {
            describe: "环(境)",
            value1: this.enterpriseData[3],
            value2: this.sourceData.environmentFullMark,
          },
        },
        {
          id: 5,
          mainCode: "supervision",
          indicatorData: {
            describe: "监(监管执法)",
            describeEn: "Supervision",
            value: this.enterpriseData[5],
          },
          tooltipData: {
            describe: "监(监管执法)",
            value1: this.enterpriseData[5],
            value2: this.sourceData.supervisionFullMark,
          },
        },
        {
          id: 6,
          mainCode: "performance",
          indicatorData: {
            describe: "绩(安全绩效)",
            describeEn: "Performance",
            value: this.enterpriseData[4],
          },
          tooltipData: {
            describe: "绩(安全绩效)",
            value1: this.enterpriseData[4],
            value2: this.sourceData.performanceFullMark,
          },
        },
      ],
    };
  },
  mounted() {
    console.log("初始化数据", this.enterpriseData);
    this.getRenderData();
    // this.getAllApiData()
  },
  methods: {
    async renderChart(dom, optionData) {
      await this.$nextTick();
      const chart = this.$echarts.init(this.$refs[dom]);
      const option = getRistRenderOption(optionData, this.indicatorMaxData);
      chart.resize();
      chart.clear();
      chart.setOption(option, true);
      // chart.off('click')
      // chart.on('click', (params) => {
      //   if(params.data.code === 'E'){
      //     this.isBody = true
      //     setTimeout(() => {
      //       this.isDetail = true
      //       this.getData(params.data.code)
      //     }, 500)
      //   }
      // })
    },
    async getRenderData() {
      this.chartsData = [
        {
          name: "bg",
          value: this.indicatorMaxData.map((item) => (item / 5) * 2),
          itemStyle: { normal: { color: "#fff" } },
          lineStyle: { normal: { color: "#ccc", width: 2, opacity: 1 } },
          areaStyle: {
            color: "#fff",
            opacity: 1,
          },
        },
        {
          name: "企业得分",
          value: this.enterpriseData,
          itemStyle: { normal: { color: "#415ef0" } },
          lineStyle: { normal: { color: "#415ef0", width: 2, opacity: 1 } },
          areaStyle: {
            color: "#415ef0",
            opacity: 0.1,
          },
        },
        {
          name: "企业得分circle",
          symbol: "circle",
          symbolSize: 10,
          value: [this.enterpriseData[0], 0, 0, 0, 0, 0],
          itemStyle: {
            normal: {
              color: "#415ef0",
            },
          },
          lineStyle: { normal: { color: "transparent" } },
        },
      ];
      this.renderChart("radar-box", this.chartsData);
    },
    async getAllApiData() {
      return
      const loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {
        // 行业均值
        const res1 = await this.$api.enterpriseAnalysis({
          area: false,
          supervisionLarge: this.sourceData.supervisionLarge.slice(0, 1),
        });
        // 区域均值
        const res2 = await this.$api.enterpriseAnalysis({
          area: true,
          supervisionLarge: this.sourceData.supervisionLarge.slice(0, 1),
        });
        // 最优值
        const res3 = await this.$api.enterprisOptimum({
          supervisionLarge: this.sourceData.supervisionLarge.slice(0, 1),
        });

        if (res1 && res1.code === 200) {
          this.IndustryMeanData = [
            res1.data.meanManScore,
            res1.data.meanThingsScore,
            res1.data.meanManagementScore,
            res1.data.meanEnvironmentScore,
            res1.data.meanSupervisionScore,
            res1.data.meanPerformanceScore,
          ];
        } else {
          console.log(res1.msg);
        }

        if (res2 && res2.code === 200) {
          this.RegionalMeanData = [
            res2.data.meanManScore,
            res2.data.meanThingsScore,
            res2.data.meanManagementScore,
            res2.data.meanEnvironmentScore,
            res2.data.meanSupervisionScore,
            res2.data.meanPerformanceScore,
          ];
        } else {
          console.log(res2.msg);
        }
        if (res3 && res3.code === 200) {
          this.bestQualityData = [
            res3.data.optimumManScore,
            res3.data.optimumThingsScore,
            res3.data.optimumManagementScore,
            res3.data.optimumEnvironmentScore,
            res3.data.optimumSupervisionScore,
            res3.data.optimumPerformanceScore,
          ];

          this.bestQualityId = [
            res3.data.manId,
            res3.data.thingsId,
            res3.data.managementId,
            res3.data.environmentId,
            res3.data.supervisionId,
            res3.data.performanceId,
          ];
        } else {
          console.log(res3.msg);
        }
        this.indicator = [
          {
            id: 1,
            indicatorData: {
              value: this.enterpriseData[0],
              describe: "人Man",
            },
            tooltipData: {
              describe: "人Man",
              describeEn: "", // 企业得分
              value1: this.enterpriseData[0],
              value2: this.sourceData.manFullMark, // 指数满分
              value3: res3.data.optimumManScore, // 行业最优值
              // value4: this.indicatorMaxData[0] / 5 *4,  // 政府目标值
              value5: res1.data.meanManScore, // 行业均值
              value6: res2.data.meanManScore, // 区域均值
            },
          },
          {
            id: 2,
            indicatorData: {
              value: this.enterpriseData[1],
              describe: "物(机、料)Things",
            },
            tooltipData: {
              describe: "物(机、料)",
              value1: this.enterpriseData[1],
              value2: this.sourceData.thingsFullMark,
              value3: res3.data.optimumThingsScore, // 行业最优值
              // value4: this.indicatorMaxData[1] / 5 *4,  // 政府目标值
              value5: res1.data.meanThingsScore, // 行业均值
              value6: res2.data.meanThingsScore, // 区域均值
            },
          },
          {
            id: 3,
            indicatorData: {
              value: this.enterpriseData[2],
              describe: "管(法)",
              describeEn: "Management",
            },
            tooltipData: {
              describe: "管(法)",
              value1: this.enterpriseData[2],
              value2: this.sourceData.managementFullMark,
              value3: res3.data.optimumManagementScore, // 行业最优值
              // value4: this.indicatorMaxData[2] / 5 *4,  // 政府目标值
              value5: res1.data.meanManagementScore, // 行业均值
              value6: res2.data.meanManagementScore, // 区域均值
            },
          },
          {
            id: 4,
            indicatorData: {
              value: this.enterpriseData[3],
              describe: "环(境)",
              describeEn: "Environment",
            },
            tooltipData: {
              describe: "环(境)",
              value1: this.enterpriseData[3],
              value2: this.sourceData.environmentFullMark,
              value3: res3.data.optimumEnvironmentScore, // 行业最优值
              // value4: this.indicatorMaxData[3] / 5 *4,  // 政府目标值
              value5: res1.data.meanEnvironmentScore, // 行业均值
              value6: res2.data.meanEnvironmentScore, // 区域均值
            },
          },
          {
            id: 5,
            indicatorData: {
              value: this.enterpriseData[4],
              describe: "监(监管执法)",
              describeEn: "Supervision",
            },
            tooltipData: {
              describe: "监(监管执法)",
              value1: this.enterpriseData[4],
              value2: this.sourceData.supervisionFullMark,
              value3: res3.data.optimumSupervisionScore, // 行业最优值
              // value4: this.indicatorMaxData[4] / 5 *4,  // 政府目标值
              value5: res1.data.meanSupervisionScore, // 行业均值
              value6: res2.data.meanSupervisionScore, // 区域均值
            },
          },
          {
            id: 6,
            indicatorData: {
              value: this.enterpriseData[5],
              describe: "绩(安全绩效)",
              describeEn: "Performance",
            },
            tooltipData: {
              describe: "绩(安全绩效)",
              value1: this.enterpriseData[5],
              value2: this.sourceData.performanceFullMark,
              value3: res3.data.optimumPerformanceScore, // 行业最优值
              // value4: this.indicatorMaxData[5] / 5 *4,  // 政府目标值
              value5: res1.data.meanPerformanceScore, // 行业均值
              value6: res2.data.meanPerformanceScore, // 区域均值
            },
          },
        ];
      } catch (err) {
        this.$message.error(err.message);
        console.log(err.message);
      } finally {
        loading.close();
      }
    },
    async handleChange(e, name, index) {
      let arr = [0, 0, 0, 0, 0, 0];
      // [1.如果是勾选状态就往 radar 添加数据]
      if (e.target.checked) {
        if (index === 1) {
          // 最优值
          this.series = this.bestQualityData;
        } else if (index === 2) {
          //行业目标值
          this.series = this.IndustryMeanData;
        } else if (index === 3) {
          //区域均值
          this.series = this.RegionalMeanData;
        }

        // 追加 radar 拐点
        if (this.currentName === "man") {
          arr = [this.series[0], 0, 0, 0, 0, 0];
        } else if (this.currentName === "things") {
          arr = [0, this.series[1], 0, 0, 0, 0];
        } else if (this.currentName === "management") {
          arr = [0, 0, this.series[2], 0, 0, 0];
        } else if (this.currentName === "environment") {
          arr = [0, 0, 0, this.series[3], 0, 0];
        } else if (this.currentName === "supervision") {
          arr = [0, 0, 0, 0, this.series[4], 0];
        } else if (this.currentName === "performance") {
          arr = [0, 0, 0, 0, 0, this.series[5]];
        }

        // 追加 radar图 系列数据
        this.chartsData.push(
          {
            name: name,
            value: this.series,
            itemStyle: { normal: { color: colorList[index] } },
            lineStyle: {
              normal: {
                color: colorList[index],
                width: 2,
                opacity: 1,
                type: "dashed",
              },
            },
          },
          {
            name: name + "circle",
            symbol: "circle",
            symbolSize: 10,
            value: arr,
            itemStyle: {
              normal: {
                color:
                  name === "行业最优值"
                    ? "#13c752"
                    : name === "行业均值"
                    ? "#facd89"
                    : name === "区域均值"
                    ? "#4ca2c2"
                    : "",
              },
            },
            lineStyle: { normal: { color: "transparent" } },
          }
        );
        this.renderChart("radar-box", this.chartsData);
      } else {
        // 2.[取消状态删除数据]
        this.chartsData = this.chartsData.filter((item) => {
          return item.name !== name;
        });
        this.chartsData = this.chartsData.filter((item) => {
          return item.name !== name + "circle";
        });
        this.renderChart("radar-box", this.chartsData);
      }
    },
    async handleClick(index, name) {
      this.currentName = name;
      let interimArr = [];
      // 1.【过滤掉带 circle数据】 保留的数据为 bg 和 真数据
      this.chartsData = this.chartsData.filter((item) => {
        return !item.name.includes("circle");
      });

      // 2.【增加拐点】 => 添加除bg之外的数据
      interimArr = this.chartsData
        .map((item) => {
          let arr = [0, 0, 0, 0, 0, 0];
          // arr.splice(index, 1, item.value[index] * 1)
          if (name === "人Man") {
            arr = [item.value[0], 0, 0, 0, 0, 0];
          } else if (name === "物(机、料)Things") {
            arr = [0, item.value[1], 0, 0, 0, 0];
          } else if (name === "管(法)") {
            arr = [0, 0, item.value[2], 0, 0, 0];
          } else if (name === "环(境)") {
            arr = [0, 0, 0, item.value[3], 0, 0];
          } else if (name === "监(监管执法)") {
            arr = [0, 0, 0, 0, item.value[4], 0];
          } else if (name === "绩(安全绩效)") {
            arr = [0, 0, 0, 0, 0, item.value[5]];
          }
          if (item.name !== "bg") {
            return {
              name: item.name + "circle",
              symbol: "circle",
              symbolSize: 10,
              value: arr,
              itemStyle: {
                normal: {
                  color:
                    item.name === "企业得分"
                      ? "#415ef0"
                      : item.name === "行业最优值"
                      ? "#13c752"
                      : item.name === "行业均值"
                      ? "#facd89"
                      : item.name === "区域均值"
                      ? "#4ca2c2"
                      : "",
                },
              },
              lineStyle: { normal: { color: "transparent" } },
            };
          }
        })
        .filter((item) => item);

      // 只更新chartsData，不做任何旋转或indicator顺序调整
      this.chartsData = [...this.chartsData, ...interimArr];
      this.renderChart("radar-box", this.chartsData);

      // 4. 【修改父元素数据 + 显示对应的左侧详情数据】
      if (name === "name") {
        this.num = 0;
      } else if (name === "things") {
        this.num = 1;
      } else if (name === "management") {
        this.num = 2;
      } else if (name === "environment") {
        this.num = 3;
      } else if (name === "supervision") {
        this.num = 4;
      } else if (name === "performance") {
        this.num = 5;
      }
      this.$emit("amendIsnow", this.num, name);
    },
    handleOptimal(name) {
      //跳到最优值分数最高的企业中
      if (name === "人Man") {
        this.uuitNo = this.bestQualityId[0];
      } else if (name === "物(机、料)Things") {
        this.uuitNo = this.bestQualityId[1];
      } else if (name === "管(法)") {
        this.uuitNo = this.bestQualityId[2];
      } else if (name === "环(境)") {
        this.uuitNo = this.bestQualityId[3];
      } else if (name === "监(监管执法)") {
        this.uuitNo = this.bestQualityId[4];
      } else if (name === "绩(安全绩效)") {
        this.uuitNo = this.bestQualityId[5];
      }
      this.$router.push({
        path: "/statistics/history",
        query: {
          uuitNo: this.uuitNo,
        },
      });
      this.$emit("resetData");
    },
    handleTotalClick() {
      console.log('点击总体风险指数'); // 添加调试日志
      this.$emit('showRiskDetail');
    },
  },
};
</script>

<style lang="scss" scoped>
$imgUrl: "../../../../../../static/portrait/";

@font-face {
  font-family: "Quartz-Regular";
  src: url("../../../../../../static/font/Quartz-Regular.ttf");
}
.Risk-container {
  height: 100%;
  display: flex;
  position: relative;
  flex-direction: column;
  justify-content: space-between;
  background: url("#{$imgUrl}/many/qianshe.png") no-repeat center;

  .radar-box {
    width: 100%;
    height: 460px;
    transition: all 1.5s;
  }

  .legendList {
    padding: 0 80px;
    z-index: 444;
    flex: 1;
    display: flex;
    justify-content: space-around;
    align-items: center;

    .legend-item {
      display: flex;
      align-items: center;
      margin-right: 10px;

      > span {
        color: #666;
        display: flex;
        align-items: center;
      }
    }

    .circle1 {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      margin-right: 5px;
      background-color: #415ef0;
    }

    .circle2 {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      margin-right: 5px;
      background-color: #13c752;
    }

    .circle3 {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      margin-right: 5px;
      background-color: #fe5d9d;
    }

    .circle4 {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      margin-right: 5px;
      background-color: #facd89;
    }

    .circle5 {
      width: 9px;
      height: 9px;
      border-radius: 50%;
      margin-right: 5px;
      background-color: #4ca2c2;
    }
  }

  .risk {
    z-index: 333;
    cursor: pointer;
    position: absolute;
    font-weight: 600;
    text-align: center;
    position: absolute;

    p {
      &:first-of-type {
        span {
          font-size: 24px;
          font-family: "Quartz-Regular";
        }
      }
    }
  }

  .total {
    position: absolute;
    text-align: center;
    font-weight: 600;
    top: 170px;
    left: 50%;
    transform: translate(-50%, 0);
    z-index: 999; // 提高层级，确保可以点击
    cursor: pointer;
    transition: transform 0.3s;
    padding: 10px; // 增加点击区域

    &:hover {
      transform: translate(-50%, 0) scale(1.05);
    }

    &::before {
      content: "";
      display: block;
      position: absolute;
      width: 116px;
      height: 101px;
      z-index: -1;
      background: url("#{$imgUrl}/solid/pic.png") no-repeat center;
      background-size: 100% 100%;
    }

    p {
      &:first-of-type {
        filter: url(#dilate);
        font-size: 32px;
        font-family: "Quartz-Regular";
      }

      &:nth-child(2) {
        font-size: 18px;
        filter: url(#dilate);
      }
    }
  }

  .man {
    top: 210px;
    left: 20px;
    z-index: 444;
  }

  .things {
    top: 380px;
    left: 110px;
  }

  .management {
    top: 380px;
    left: 450px;
  }

  .environment {
    top: 210px;
    left: 544px;
  }

  .supervision {
    top: 10px;
    left: 473px;
  }

  .performance {
    top: 10px;
    left: 126px;
  }

  .bgImg {
    position: absolute;
    top: 0;
    left: 0;
  }
}
p {
  margin-bottom: 0;
}
</style>
