//监管端 ip地址---------------- 正式地址
//const BASE_URL = "**************:9090";
//企业端端 ip地址
//const BASE_URL = "************:9080";
// debugger
// console.log(window.location)

//监管端测试服务器---------------测试地址  **************:8090

// let BASE_URL = "***********:8090"; //危化开发环境
let BASE_URL = "************:8990"; //危化生产环境
// let BASE_URL = "zhyj.yjj.wuhan.gov.cn:8993"; //危化互联网环境
const H5_URL = "https://zhyj.yjj.wuhan.gov.cn:8992/chemical-h5/"; //H5 考试h5链接

// let BASE_URL = "************:18990";   //工贸生产.环境
// let BASE_URL = "***********:8090"; //工贸开发环境

// let BASE_URL = "yyzc-whjcyj.hbsis.gov.cn:31443";
// if(window.location.port=="30003"){ //企业端
//   BASE_URL="yyzc.hbsis.gov.cn:30003"
// }
const serverIp = "************:2443";
//企业端端测试服务器 ip地址
//const BASE_URL = "**************:8099";

//************:9081
//const BASE_URL = "************:9081";
//武汉分公司测试 IP地址
//const BASE_URL = "***********:9999";
module.exports = {
  BASE_URL,
  serverIp,
  H5_URL,
};
