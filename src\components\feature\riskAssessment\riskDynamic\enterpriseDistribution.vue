<template>
  <div class="enterpriseDistribution">
    <div class="enterpriseDistributionBox">
      <div class="top">
        <div class="name">各区划企业分布数量</div>
        <span>单位家</span>
      </div>
      <div class="jiaoList">
        <div class="header">
          <div class="order"><span>区划</span></div>
          <div class="level1">一级</div>
          <div class="level2">二级</div>
          <div class="level3">三级</div>
          <div class="level4">四级</div>
        </div>
        <div class="jiaoItem" v-for="(el, index) of jiaoData" :key="index">
          <div class="order">
            <span>{{ el.title }}</span>
          </div>
          <div class="level1">{{ el.level1 }}</div>
          <div class="level2">{{ el.level2 }}</div>
          <div class="level3">{{ el.level3 }}</div>
          <div class="level4">{{ el.level4 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  data() {
    return {
      jiaoData: [
        {
          title: "武汉市",
          level1: "12",
          level2: "7",
          level3: "9",
          level4: "243",
        },
        {
          title: "仙桃市",
          level1: "12",
          level2: "7",
          level3: "9",
          level4: "243",
        },
        {
          title: "鄂州市",
          level1: "12",
          level2: "7",
          level3: "9",
          level4: "243",
        },
        {
          title: "恩施州",
          level1: "12",
          level2: "7",
          level3: "9",
          level4: "243",
        },
        {
          title: "随州市",
          level1: "12",
          level2: "7",
          level3: "9",
          level4: "243",
        },
        {
          title: "十堰市",
          level1: "12",
          level2: "7",
          level3: "9",
          level4: "243",
        },
        //  {
        //   title: "湖北兴发化工集团股份有限公司刘草坡化工厂",
        //   ping: "70",
        //   level: "较大",
        // },
      ],
    };
  },
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseDistribution {
   height: 288px;
  overflow: auto;
  .enterpriseDistributionBox .top{
    display: flex;
    justify-content: space-between;
    text-align: center;
    span{
      color:#ccc
    }
  }
  .jiaoList {
    text-align: center;
    height: 250px;
    overflow: auto;
    .header {
      display: flex;
      height: 40px;
      line-height: 40px;
      background: #f7f8fa;
      .level1,
      .level2,
      .level3,
      .level4{
        color: #000;
      }
    }

    .order {
      width: 20%;
    }
    .level1,
    .level2,
    .level3,
    .level4{
      width: 20%;
    }

    .jiaoItem {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #d9d9d9;
      padding: 10px 0;
    }
    .jiaoItem .level1 {
      color: #cb5f5c;
    }
    .jiaoItem .level2 {
      color: #faad14;
    }
    .jiaoItem .level3 {
      color: #f8d253;
    }
    .jiaoItem .level4 {
      color: #326eff;
    }
  }
}
</style>