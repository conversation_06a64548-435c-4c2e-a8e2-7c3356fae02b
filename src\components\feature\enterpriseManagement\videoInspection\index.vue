<template>
  <div class="videoInspection">
    <div class="video-left">
      <div class="videoLeft-top">
        <h2>设备监控列表</h2>
        <div class="video-list">
          <div class="video-item">
            <p
              v-for="(item, index) in videoData"
              :key="index"
              @click="getVideoId(item.monitornum, index)"
              :class="active == index ? 'activeP' : ''"
            >
              <i class="video-camera"></i>
              <span class="video-name">{{ item.monitorname }}</span>
              <span class="zaixian" v-if="item.onlinestatus == 1">在线</span>
              <span class="lixian" v-else-if="item.onlinestatus == 0"
                >离线</span
              >
            </p>
            <img
              v-if="videoData.length == 0"
              style="
                margin: 0 auto;
                display: block;
                margin-top: 30px;
                width: 70%;
              "
              src="/static/img/assets/img/noData.png"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="video-right">
      <el-radio-group
        size="small"
        v-model="sign"
        style="position: absolute; right: 0; top: 10px; z-index: 10"
      >
        <el-radio-button label="0"
          >全部({{ videoNumData.total }})</el-radio-button
        >
        <el-radio-button label="1"
          >在线({{ videoNumData.onlineNum }})</el-radio-button
        >
      </el-radio-group>
      <!-- 四路实时视频 -->
      <div class="video-content">
        <multiVideoPlayer ref="multiVideoPlayer" />
      </div>
    </div>
  </div>
</template>

<script>
import { getVideoList, getVideoNumData } from "@/api/entList";
import { getVideoPreviewUrl } from "@/api/riskAssessment";
import multiVideoPlayer from "@/components/feature/riskAssessment/videoOnlineMonitoring/multiVideoPlayer.vue";
export default {
  //import引入的组件
  name: "videoInspection",
  components: {
    multiVideoPlayer,
  },
  props: ["showVideo"],
  data() {
    return {
      //声明公用变量
      enterpriseId: "",
      sign: "0",
      videoData: [],
      videoNumData: {},
      active: 0,
    };
  },
  //方法集合
  methods: {
    getData(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoList({
        enterpId: this.enterpriseId,
        sign: this.sign,
      }).then((res) => {
        console.log("getVideoList+++++++++++++++000", res);
        if (res.data.code == 0) {
          this.videoData = res.data.data;
        }
      });
    },
    async getVideoId(monitornum, index) {
      this.active = index;
      const cameraId = monitornum;
      const cameraName = this.videoData[index].monitorname || "摄像头";

      console.log("选择摄像头:", { cameraId, cameraName });

      try {
        // 获取视频流URL
        const res = await getVideoPreviewUrl({
          channelId: cameraId,
          schema: 5, // 使用默认的流媒体协议
          subType: 1, // 实时预览
        });

        if (res.status === 200 && res.data?.data?.url) {
          const videoSource = {
            id: cameraId,
            name: cameraName,
            url: res.data.data.url,
            channelId: cameraId,
            expireTime: res.data.data.expireTime,
            schema: res.data.data.schema,
            status: "online",
          };

          // 添加到四路视频播放器
          this.$nextTick(() => {
            if (
              this.$refs.multiVideoPlayer &&
              typeof this.$refs.multiVideoPlayer.addVideoToChannel ===
                "function"
            ) {
              const channelIndex =
                this.$refs.multiVideoPlayer.addVideoToChannel(videoSource);
              if (channelIndex >= 0) {
                console.log(`视频已添加到通道 ${channelIndex + 1}`);
              } else {
                console.error("添加视频到多路播放器失败");
              }
            } else {
              console.error("多路播放器组件未准备好");
            }
          });
        } else {
          this.$message.error(
            "获取视频流失败：" + (res.data?.msg || "未知错误")
          );
        }
      } catch (error) {
        console.error("获取视频流失败:", error);
        this.$message.error("获取视频流失败：" + (error.message || "未知错误"));
      }
    },

    getVideoNum(enterpriseId) {
      this.enterpriseId = enterpriseId;
      getVideoNumData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.videoNumData = res.data.data;
        }
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeDestroy() {},
  //生命周期 - 销毁完成
  destroyed() {},
  //如果页面有keep-alive缓存功能，这个函数会触发
  activated() {},
  watch: {
    showVideo(val) {
      if (val) {
        // 组件显示时的逻辑
      }
    },
    sign() {
      this.getData(this.enterpriseId);
    },
  },
};
</script>

<style lang="scss" scoped>
.videoInspection {
  // padding: 15px 0;
  overflow: hidden;
  .video-left {
    float: left;
    width: 345px;
    margin-right: 25px;
    .videoLeft-top {
      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #3b4046;
        line-height: 32px;
      }
      .video-list {
        background: #daefff;
        height: calc(100vh - 285px);
        padding: 15px 0;
        border-radius: 4px;
        overflow-y: scroll;
        .activeP {
          background-color: rgba(0, 120, 255, 0.1);
          color: #3977ea;
        }
        p {
          display: flex;
          align-items: center;
          line-height: 50px;
          height: 50px;
          margin-bottom: 0;
          font-size: 14px;
          padding: 0 15px;
          color: #545c65;
          border-bottom: 1px solid #bdddf3;
          .video-camera {
            display: inline-block;
            width: 17px;
            height: 20px;
            background: url("/static/img/assets/img/camera.png") no-repeat
              center;
            background-size: cover;
            vertical-align: middle;
            margin-right: 14px;
          }
          span {
            float: right;
          }
          .video-name {
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .zaixian {
            color: #43be98;
          }
          .lixian {
            color: #999999;
          }
          .baojing {
            color: #f9646f;
          }
        }
      }
    }
  }
  .video-right {
    float: left;
    position: relative;
    width: calc(100% - 370px);

    .video-content {
      margin-top: 40px;
      border: 1px solid #ddd;
      height: calc(100vh - 285px);
      background: #000;
      border-radius: 4px;

      // 四路视频播放器样式
      ::v-deep .multi-video-player {
        width: 100%;
        height: 100%;
        border-radius: 4px;
        overflow: hidden;
      }
    }
  }
}
</style>
