<template>
  <div class="historyList">
    <el-dialog
      title="报警统计分析"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="70%"
      :close-on-click-modal="false"
      v-loading="loading"
      element-loading-text="加载中..."
      element-loading-spinner="el-icon-loading"
      element-loading-background="rgba(0, 0, 0, 0.8)"
    >
      <el-table
        :loading="loading"
        :border="true"
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        style="width: 100%"
      >
        <el-table-column prop="upperWD" label="温度超上限" align="center">
        </el-table-column>
        <el-table-column prop="lowerWD" label="温度超下限" align="center">
        </el-table-column>
        <el-table-column prop="upperYL" label="压力超上限" align="center">
        </el-table-column>
        <el-table-column prop="lowerYL" label="压力超下限" align="center">
        </el-table-column>
        <el-table-column prop="upperYW" label="液位超下限" align="center">
        </el-table-column>
        <el-table-column prop="lowerYW" label="液位超下限" align="center">
        </el-table-column>
        <el-table-column prop="gasKR" label="可燃气体" align="center">
        </el-table-column>
        <el-table-column prop="gasYD" label="有毒气体" align="center">
        </el-table-column>
        <el-table-column prop="lowerCL" label="储量超上限" align="center">
        </el-table-column>
        <el-table-column prop="upperCL" label="储量超下限" align="center">
        </el-table-column>

        <el-table-column prop="lowerDL" label="电流超上限" align="center">
        </el-table-column>
        <el-table-column prop="upperDL" label="电流超下限" align="center">
        </el-table-column>

        <el-table-column prop="lowerHL" label="含量超上限" align="center">
        </el-table-column>
        <el-table-column prop="upperHL" label="含量超下限" align="center">
        </el-table-column>

        <el-table-column prop="lowerWD" label="浓度超上限" align="center">
        </el-table-column>
        <el-table-column prop="upperWD" label="浓度超下限" align="center">
        </el-table-column>

        <el-table-column prop="lowerYC" label="压差超上限" align="center">
        </el-table-column>
        <el-table-column prop="upperYC" label="压差超下限" align="center">
        </el-table-column>
      </el-table>
      <div>
        <div class="pietotal"><p>总数</p><span>{{ totNum }}</span></div>
        <div
          id="myCharted"
          :style="{ width: '600px', height: '300px', margin: '0 auto 0' }"
        ></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getDetailData, getTotalDetailData } from "@/api/entList";
export default {
  //import引入的组件
  name: "statisticDetail",
  components: {},
  data() {
    return {
      totNum:0,
      show: false,
      currentPage: 1,
      tableData: [],
      echartData: [],
      loading: true,
      option: {
        title: {
          text: "",
          x: "center",
          y: "center",
          textStyle: {
            fontSize: 20,
          },
        },
        tooltip: {
          trigger: "item",
        },
        series: [
          {
            type: "pie",
            center: ["50%", "52%"],
            radius: ["30%", "45%"],
            clockwise: true,
            avoidLabelOverlap: true,
            // emphasis: {
            //   label: {
            //     show: true,
            //     fontSize: "40",
            //     fontWeight: "bold",
            //   },
            // },
            hoverOffset: 20,
            itemStyle: {
              normal: {
                color: function (params) {
                  return [
                    "#3977EA",
                    "#EA3939",
                    "#FA8733",
                    "#6092BD",
                    "#FAD233",
                    "#85C655",
                    "#936BD5",
                    "#FF9FF7",
                    "#000000",
                    "#00dcff",
                    "#676820",
                    "#3977EA",
                    "#EA3939",
                    "#FA8733",
                    "#6092BD",
                    "#FAD233",
                    "#85C655",
                    "#936BD5",
                    "#FF9FF7",
                    // "#000000",
                  ][params.dataIndex];
                },
              },
            },
            label: {
              show: true,
              position: "outside",
              formatter: "{a|{b}：{d}%}\n{hr|}",
              minAngle: 0,
              rich: {
                hr: {
                  backgroundColor: "t",
                  borderRadius: 3,
                  width: 3,
                  height: 3,
                  padding: [3, 3, 0, -12],
                },
                a: {
                  padding: [-30, 15, -20, 15],
                },
              },
            },
            labelLine: {
              // show:false,
              normal: {
                show: true,
                length: 20,
                length2: 30,
                lineStyle: {
                  width: 1,
                },
              },
            },
            data: {},
          },
        ],
      },
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val;
    },
    getDetail(dangerId, starTime, endTime) {
      getDetailData({
        dangerId: dangerId,
        startTime: starTime,
        endTime: endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.tableData = [];
          this.tableData.push(res.data.data);

          this.echartData = [
            {
              name: "温度超上限",
              value: res.data.data.upperWD,
            },
            {
              name: "温度超下限",
              value: res.data.data.lowerWD,
            },
            {
              name: "压力超上限",
              value: res.data.data.upperYL,
            },
            {
              name: "压力超下限",
              value: res.data.data.lowerYL,
            },
            {
              name: "液位超上限",
              value: res.data.data.upperYW,
            },
            {
              name: "液位超下限",
              value: res.data.data.lowerYW,
            },
            {
              name: "可燃气体",
              value: res.data.data.gasKR,
            },
            {
              name: "有毒气体",
              value: res.data.data.gasYD,
            },
            {
              name: "流量超上限",
              value: res.data.data.upperLL,
            },
            {
              name: "流量超下限",
              value: res.data.data.lowerLL,
            },
            {
              name: "电流超上限",
              value: res.data.data.lowerDL,
            },
            {
              name: "电流超下限",
              value: res.data.data.upperDL,
            },
            {
              name: "含量超上限",
              value: res.data.data.lowerHL,
            },
            {
              name: "含量超下限",
              value: res.data.data.upperHL,
            },
            {
              name: "浓度超上限",
              value: res.data.data.lowerWD,
            },
            {
              name: "浓度超下限",
              value: res.data.data.upperWD,
            },
            {
              name: "压差超上限",
              value: res.data.data.lowerYC,
            },
            {
              name: "压差超下限",
              value: res.data.data.upperYC,
            },
          ];
        }
      });
    },
    getTotalDetail(enterpriseId, starTime, endTime) {
      getTotalDetailData({
        enterpriseId: enterpriseId,
        startTime: starTime,
        endTime: endTime,
      }).then((res) => {
        if (res.data.code == 0) {
          this.tableData = [];
          this.tableData.push(res.data.data);
          this.echartData = [
            {
              name: "温度超上限",
              value: res.data.data.upperWD,
            },
            {
              name: "温度超下限",
              value: res.data.data.lowerWD,
            },
            {
              name: "压力超上限",
              value: res.data.data.upperYL,
            },
            {
              name: "压力超下限",
              value: res.data.data.lowerYL,
            },
            {
              name: "液位超上限",
              value: res.data.data.upperYW,
            },
            {
              name: "液位超下限",
              value: res.data.data.lowerYW,
            },
            {
              name: "可燃气体",
              value: res.data.data.gasKR,
            },
            {
              name: "有毒气体",
              value: res.data.data.gasYD,
            },
            {
              name: "流量超上限",
              value: res.data.data.upperLL,
            },
            {
              name: "流量超下限",
              value: res.data.data.lowerLL,
            },

            {
              name: "电流超上限",
              value: res.data.data.lowerDL,
            },
            {
              name: "电流超下限",
              value: res.data.data.upperDL,
            },
            {
              name: "含量超上限",
              value: res.data.data.lowerHL,
            },
            {
              name: "含量超下限",
              value: res.data.data.upperHL,
            },
            {
              name: "浓度超上限",
              value: res.data.data.lowerWD,
            },
            {
              name: "浓度超下限",
              value: res.data.data.upperWD,
            },
            {
              name: "压差超上限",
              value: res.data.data.lowerYC,
            },
            {
              name: "压差超下限",
              value: res.data.data.upperYC,
            },
          ];
        }
      });
    },
    drawLine() {
      let myChart = this.$echarts.init(document.getElementById("myCharted"));
      myChart.setOption(this.option, true);
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {
    //观察option的变化
    echartData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.totNum=0
          let ary = [];
          this.totNum = newVal.reduce((prev, next) => {
            return prev + Number(next.value);
          }, 0);
          if (newVal.every((el) => el.value == 0)) {
            ary = [
              {
                value: 0,
                name: "",
              },
            ];
            console.log(this.option, "ccccc");
            this.option.series[0].label.show = false;
            this.option.series[0].labelLine = false;
            this.option.series[0].itemStyle = {
              normal: {
                color: function (params) {
                  return ["#ccc"];
                },
              },
            };
          } else {
            // newVal.forEach(el=>{})
            ary = newVal.filter(el=>el.value!=0)
          }
          this.option.series[0].data = ary;
          this.drawLine();
        } else {
          this.totNum=0
          let ary = [];
          this.totNum = oldVal.reduce((prev, next) => {
            return prev + Number(next.value);
          }, 0);
          if (oldVal.every((el) => el.value == 0)) {
            ary = [
              {
                value: 0,
                name: "",
              },
            ];
            console.log(this.option, "ccccc");
            this.option.series[0].label.show = false;
            this.option.series[0].labelLine = false;
            this.option.series[0].itemStyle = {
              normal: {
                color: function (params) {
                  return ["#ccc"];
                },
              },
            };
          } else {
            ary =oldVal.filter(el=>el.value!=0)
          }
          this.option.series[0].data = ary;
          this.drawLine();
          // console.log(this.option.series[0].data, "bbbbbbbbbbbbbb");
        }
      },
      deep: true, //对象内部属性的监听，关键。
    },
  },
};
</script>
<style lang="scss" scoped>
.pietotal{
  position: absolute;
  top:50%;
  left:50%;
  z-index: 9999;
  text-align: center;
  margin-top:75px;
  margin-left:-20px;
  p{
    margin:0 0 5px 0;
    font-weight: bold;
    font-size: 18px;
  }
}
.historyList {
  .seach-part {
    font-weight: 600;
    padding: 0 10px;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      width: 915px;
      display: flex;
      justify-content: space-between;
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
