<template>
  <el-dialog
    title="分值与评级设置"
    :visible="visible"
    @close="closeBoolean(false)"
    width="500px"
    top="5vh"
    :close-on-click-modal="true"
  >
    <div class="setting-container">
      <el-form
        :model="planSetting"
        :rules="rules"
        ref="firstForm"
        label-width="auto"
        size="small"
      >
        <div class="form-title">一级梯度分值设置</div>
        <template v-for="(item, index) in planSetting.mainDTOs">
          <el-form-item
            :label="`${item.levelName}(${item.levelCode})-u${index+1}`"
            :prop="'mainDTOs.' + index + '.score'"
            :rules="[
              { required: true, message: '请输入数字', trigger: 'blur' },
            ]"
            :key="item.id"
          >
            <el-input v-model.number="item.score"  size="small" clearable />
          </el-form-item>
        </template>
        <div class="form-title">一级梯度分值评级设置</div>
        <el-form-item label="一级：" style="margin: 0">
          <el-row>
            <el-col :span="10">
              <el-form-item >
                <el-input
                  v-model.number="baseScore"
                  
                  clearable
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" style="text-align: center">--</el-col>
            <el-col :span="10">
              <el-form-item prop="rankD">
                <el-input
                  v-model.number="planSetting.rankD"
                  clearable
                  placeholder=""
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
       
        <el-form-item label="二级：" style="margin: 0">
          <el-row>
            <el-col :span="10">
              <el-form-item prop="rankD">
                <el-input
                  v-model.number="planSetting.rankD"
                  clearable 
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" style="text-align: center">--</el-col>
            <el-col :span="10">
              <el-form-item prop="rankC">
                <el-input
                  v-model.number="planSetting.rankC"
                  clearable 
                  placeholder=""
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
       
        <el-form-item label="三级：" style="margin: 0">
          <el-row>
            <el-col :span="10">
              <el-form-item prop="rankC">
                <el-input
                  v-model.number="planSetting.rankC"
                  clearable 
                  placeholder=""
                />
              </el-form-item>
            </el-col>
            <el-col :span="4" style="text-align: center">--</el-col>
            <el-col :span="10">
              <el-form-item prop="rankB">
                <el-input
                  v-model.number="planSetting.rankB"
                  clearable 
                  placeholder=""
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="四级：" style="margin: 0">
          <el-row>
            <el-col :span="10">
              <el-form-item prop="rankB">
                <el-input v-model.number="planSetting.rankB" clearable  placeholder="" />
              </el-form-item>
            </el-col>
            <el-col :span="4" style="text-align: center">--</el-col>
            <el-col :span="10">
              <el-form-item prop="rankA">
                <el-input
                  v-model.number="planSetting.rankA"
                  clearable 
                  placeholder=""
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button size="small" @click="closeBoolean">取消</el-button>
      <el-button type="primary" size="small" @click="handleSubmit"
        >确认</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { getPortraitPlanUpdate } from "@/api/enterprisePortrait";

export default {
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    firstSetting: {
      type: Object,
      default: () => {
        return {
          name: "危化企业方案",
          rankA: null,
          rankB: null,
          rankC: null,
          rankD: null,
          remark: null,
          mainDTOs: [],
        };
      },
    },
  },
  data() {
    return {
      baseScore: 0,
      planSetting:{
        name: "危化企业方案",
          rankA: null,
          rankB: null,
          rankC: null,
          rankD: null,
          remark: null,
          mainDTOs: [],
      },
      rules: {
        score: [
          {
            required: true,
            message: "请输入分数",
            trigger: "blur",
          },
        ],
        rankA: [{ required: true, message: "请输入分数", trigger: "blur" }],
        rankB: [{ required: true, message: "请输入分数", trigger: "blur" }],
        rankC: [{ required: true, message: "请输入分数", trigger: "blur" }],
        rankD: [{ required: true, message: "请输入分数", trigger: "blur" }],
      },
    };
  },
  created() {
    this.planSetting = {...this.firstSetting};
    this.getDetail();
  },
  methods: {
    getDetail() {},
    closeBoolean(val) {
      this.$emit("close", val);
    },
    handleSubmit() {
      this.$refs.firstForm.validate((valid) => {
        if (valid) {
          const total = this.planSetting.mainDTOs.reduce((pre, cur) => {
            return pre + cur.score;
          }, 0);
         const params = {
          ...this.planSetting,
          ruleMainAddDTOs:this.planSetting.mainDTOs ,
          total: total 
         }
         delete params.mainDTOs;
          getPortraitPlanUpdate(params).then((res) => {
            if (res.data.status == 200) {
              this.$emit("submit");
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.setting-container {
  padding: 0 20px;

  .form-title {
    font-size: 16px;
    height: 30px;
    border-bottom: #ededed solid 1px;
    margin-bottom: 16px;
  }
}

.footer {
  text-align: center;
}
</style>
