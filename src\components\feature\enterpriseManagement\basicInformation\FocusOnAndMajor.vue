<template>
  <div class="FocusOnAndMajor">
    <div style="justify-content: space-between; display: flex">
      <div>
        <el-card class="left-list" shadow="never" ref="leftBox">
          <div slot="header" class="clearfix title">
            <span>目录</span>
          </div>
          <div class="tab-list-btn">
            <el-scrollbar style="height: 100%; width: 100%">
              <ul>
                <li
                  v-for="(item, index) in routerList"
                  :key="index"
                  @click="selectLeftListBtn(item, index)"
                  :class="{ active: selectRouterItem === item }"
                  :title="item"
                >
                  <!-- <el-tooltip
                  class="item"
                  effect="light"
                  :content="item"
                  placement="right"
                >
                </el-tooltip> -->
                  <span>{{ item }}</span>
                </li>
              </ul>
            </el-scrollbar>
          </div>
        </el-card>
      </div>
      <div class="box" id="box">
        <div class="div">
          <div class="title">重点监管危化品</div>
          <el-table
            :data="hazarchem"
            border
            style="width: 100%"
            :header-cell-style="{
              textAlign: 'center',
              backgroundColor: 'rgb(242, 246, 255)',
            }"
          >
            <el-table-column
              type="index"
              label="序号"
              width="60"
            ></el-table-column>
            <el-table-column
              label="危化品中文名"
              prop="hazarchemName"
            ></el-table-column>
            <el-table-column
              label="别名"
              prop="chemicalAlias"
            ></el-table-column>
            <el-table-column label="CAS号" prop="casNo"></el-table-column>
            <el-table-column
              label="化学品属性"
              prop="hazarchemProperty"
            ></el-table-column>
            <el-table-column
              label="设计储存量(吨/标方)"
              prop="storageNum"
            ></el-table-column>
            <el-table-column
              label="年产量/消耗量(吨/标方)"
              prop="annualUsageMeal"
            ></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }">
                <el-button type="text" @click="chemicalsBool(row.hazarchemId)"
                  >详情</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="div" v-if="level">
          <div class="title">重点监管工艺</div>
          <el-table
            :data="regprocess"
            border
            style="width: 100%"
            :header-cell-style="{
              textAlign: 'center',
              backgroundColor: 'rgb(242, 246, 255)',
            }"
          >
            <el-table-column
              label="序号"
              type="index"
              width="60"
            ></el-table-column>
            <el-table-column
              label="工艺名称"
              prop="processName"
            ></el-table-column>
            <el-table-column
              label="装置名称"
              prop="deviceName"
            ></el-table-column>
            <el-table-column label="工艺危险性特征" prop="hazardFeature"
              >
              <!-- <template slot-scope="{ row }"
                ><div v-if="row.isTypicalProcess == 1">是</div>
                <div v-else-if="row.isTypicalProcess == 0">否</div>
                <div v-else></div> </template
            > -->
          </el-table-column>
            <el-table-column
              label="工艺装置地址"
              prop="processAddress"
            ></el-table-column>
            <el-table-column
              label="投用日期"
              prop="investmentTime"
            ></el-table-column>
            <el-table-column label="操作">
              <template slot-scope="{ row }"
                ><el-button type="text" @click="processBool(row.relationId)"
                  >详情</el-button
                ></template
              >
            </el-table-column>
          </el-table>
        </div>
        <div class="div" v-if="level" style="margin-bottom: 0">
          <div class="title">重大危险源</div>
          <el-table
            :data="danger"
            border
            style="width: 100%"
            :header-cell-style="{
              textAlign: 'center',
              backgroundColor: 'rgb(242, 246, 255)',
            }"
          >
            <el-table-column
              label="序号"
              type="index"
              width="60"
            ></el-table-column>
            <el-table-column
              label="重大危险源名称"
              prop="dangerName"
            ></el-table-column>
            <el-table-column label="R值" prop="rvalue"></el-table-column>
            <el-table-column label="危险源等级" prop="level">
              <template slot-scope="{ row }"
                ><div v-if="row.level == 1">一级重大危险源</div>
                <div v-else-if="row.level == 2">二级重大危险源</div>
                <div v-else-if="row.level == 3">三级重大危险源</div>
                <div v-else-if="row.level == 4">四级重大危险源</div>
                <div v-else></div
              ></template>
            </el-table-column>
            <el-table-column label="重大危险源分类" prop="hazardClassify">
              <!-- <template slot-scope="{ row }">
                <div v-if="row.hazardClassify == 0">罐区</div>
                <div v-else-if="row.hazardClassify == 1">装置</div>
                <div v-else-if="row.hazardClassify == 2">库区</div>
                <div v-else></div>
              </template> -->
            </el-table-column>
            <el-table-column label="投用日期" prop="useTime"></el-table-column>
            <el-table-column label="操作" width="160"
              ><template slot-scope="{ row }"
                ><el-button type="text" @click="hazardsBool(row.dangerId)"
                  >详情</el-button
                ><el-button type="text" @click="EquipmentListBool(row.dangerId)"
                  >设备清单</el-button
                ></template
              ></el-table-column
            >
          </el-table>
        </div>
      </div>
    </div>
    <!-- 危险工艺 -->
    <Process ref="Process"></Process>
    <!-- 危险化学品 -->
    <Chemicals ref="Chemicals"></Chemicals>
    <!-- 重大危险源详情 -->
    <Hazards ref="Hazards"></Hazards>
    <!-- 重大危险源列表 -->
    <EquipmentList ref="EquipmentList"></EquipmentList>
  </div>
</template>

<script>
import FillInformation from "./FillInformation";
import RegistrationInformation from "./RegistrationInformation";
import Process from "./process";
import Chemicals from "./chemicals";
import Hazards from "./hazards";
import EquipmentList from "./EquipmentList";
export default {
  name: "FocusOnAndMajor",
  components: {
    FillInformation,
    RegistrationInformation,
    Process,
    Chemicals,
    Hazards,
    EquipmentList,
  },
  data() {
    return {
      routerList: [],
      selectRouterItem: "重点监管危化品",
      enterprise: {},
      enterpriseId: {},
      danger: [],
      hazarchem: [],
      regprocess: [],
      loading: false,
      level: "",
      scrollBox: "",
      isClickNav: false // 标记是否是点击目录触发的滚动
    };
  },
  methods: {
    selectLeftListBtn(item, index) {
      this.isClickNav = true; // 标记为点击目录触发的滚动
      this.selectRouterItem = item;
      const divElements = document.querySelectorAll('#box .div');
      if (index >= 0 && index < divElements.length) {
        const targetElement = divElements[index];
        if (this.scrollBox && targetElement) {
          const targetRect = targetElement.getBoundingClientRect();
          const containerRect = this.scrollBox.getBoundingClientRect();
          const scrollTop = targetRect.top - containerRect.top + this.scrollBox.scrollTop;
          this.scrollBox.scrollTo({
            top: scrollTop,
            behavior: 'smooth'
          });
        }
      }
      // 适当增加延迟时间，确保滚动动画结束后再恢复标志位
      setTimeout(() => {
        this.isClickNav = false;
      }, 1000); 
    },
    getData(newVal) {
      this.enterprise = newVal.enterprise;
      this.enterpriseId = newVal.enterpriseId;
      this.danger = newVal.danger;
      this.hazarchem = newVal.hazarchem;
      this.regprocess = newVal.regprocess;
      this.level = newVal.level;
      if (this.level) {
        this.routerList = ["重点监管危化品", "重点监管工艺", "重大危险源"];
      } else {
        this.routerList = ["重点监管危化品"];
      }
      this.$nextTick(() => {
        this.scroll();
      });
    },
    scroll() {
      this.scrollBox = document.getElementById("box");
      const divElements = document.querySelectorAll('#box .div');
      const topArr = [];
      for (let i = 0; i < divElements.length; i++) {
        const rect = divElements[i].getBoundingClientRect();
        const containerRect = this.scrollBox.getBoundingClientRect();
        const offsetTop = rect.top - containerRect.top + this.scrollBox.scrollTop;
        topArr.push(offsetTop);
      }

      // 防抖函数
      const debounce = (func, delay) => {
        let timer = null;
        return function() {
          if (timer) clearTimeout(timer);
          timer = setTimeout(func, delay);
        };
      };

      const handleScroll = () => {
        if (this.isClickNav) return; // 如果是点击目录触发的滚动，不更新选中状态
        const currentOffsetTop = this.scrollBox.scrollTop;
        for (let i = topArr.length - 1; i >= 0; i--) {
          // 优化判断逻辑，添加一定的偏移量避免边界判断不准确
          if (currentOffsetTop + 50 >= topArr[i]) { 
            this.selectRouterItem = this.routerList[i];
            break;
          }
        }
      };

      this.scrollBox.addEventListener(
        "scroll",
        debounce(handleScroll, 100),
        true
      );
    },
    //填报信息
    FillInformationBool() {
      this.$refs.FillInformation.closeBoolean(true);
      this.$refs.FillInformation.getData(this.enterpriseId);
    },
    //登记信息
    RegistrationInformationBool() {
      this.$refs.RegistrationInformation.closeBoolean(true);
      this.$refs.RegistrationInformation.getData(this.enterpriseId);
    },
    //危险化工工艺
    processBool(relationId) {
      this.$refs.Process.closeBoolean(true);
      this.$refs.Process.getData(relationId);
    },
    //危险化学品
    chemicalsBool(id) {
      this.$refs.Chemicals.closeBoolean(true);
      this.$refs.Chemicals.getData(id);
    },
    //危险源
    hazardsBool(dangerId) {
      this.$refs.Hazards.closeBoolean(true);
      this.$refs.Hazards.getData(dangerId);
    },
    //设备清单
    EquipmentListBool(dangerId) {
      this.$refs.EquipmentList.closeBoolean(true);
      this.$refs.EquipmentList.getData(dangerId);
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-card__body {
  padding: 0;
}
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
.FocusOnAndMajor {
  height: 60vh;
  overflow: auto;

  .box {
    height: 60vh;
    overflow: auto;
    width: calc(100% - 200px);
  }
}
.side-hidden {
  .left-list {
    width: 60px;
    .title {
      padding: 0;
    }
    span {
      display: none;
    }
    .tab-list-btn {
      i {
        pointer-events: all;
      }
    }
  }
}
.left-list {
  width: 180px;
  height: 360px;
  /deep/ .el-card__header {
    padding: 0;
  }
  .title {
    height: 47px;
    background: #f5f5f6;
    padding: 0;
    line-height: 47px;
    padding: 0 19px;
    overflow: hidden;
    color: #534d6a;
    font-size: 18px;
    font-weight: 700;
    padding-right: 60px;
    position: relative;
    .select-btn {
      position: absolute;
      height: 40px;
      width: 40px;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      text-align: center;
      line-height: 40px;
      font-size: 26px;
      cursor: pointer;
      &:hover {
        opacity: 0.8;
      }
    }
  }
  /deep/.el-card__body {
    height: calc(100%);
  }
  .tab-list-btn {
    list-style: none;
    width: 100%;
    height: calc(100% - 50px);
    overflow: hidden;
    li {
      width: 100%;
      height: 48px;
      line-height: 48px;
      font-size: 14px;
      padding-left: 20px;
      white-space: nowrap;
      overflow: hidden;
      position: relative;
      text-overflow: ellipsis;
      cursor: pointer;
      user-select: none;
      transition: 0.3s;
      color: #534c6a;
      &:hover {
        background: rgba(0, 0, 0, 0.01);
        color: #4a7dff;
      }
      &.active {
        background: #d4e8ff;
        color: #4a7dff;
      }
      i {
        width: 40px;
        height: 40px;
        position: absolute;
        top: 50%;
        line-height: 40px;
        text-align: center;
        transform: translateY(-50%);
        left: 10px;
        font-size: 20px;
        pointer-events: none;
      }
    }
  }
}
.div {
  margin-bottom: 50px;
  ul {
    margin-block-end: 0;
  }
  .title {
    font-weight: 600;
    margin-bottom: 10px;
    font-size: 18px;
  }
}
</style>
