<template>
  <el-dialog
    title="特殊时期修正"
    :visible.sync="isamendment"
    width="1000px"
    top="10vh"
    @close="CloseClear"
    :close-on-click-modal="false"
    :modal-append-to-body="true"
    :append-to-body="true"
    :destroy-on-close="false"
  >
  

  <div class="">
    
    <el-scrollbar style="height:600px">
        <el-calendar v-model="calendarData">
      </el-calendar>
     <h2>法定节假日：{{ statutoryData.length > 0 ? "有" : "无" }}</h2>
    <div class="table">
      <el-table
        :data="statutoryData"
        v-loading="loading"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
      >
        <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
        <el-table-column type="index" label="序号" width="50" align="center">
        </el-table-column>
        <el-table-column
          prop="periodName"
          label="节日名称"
          align="center"
          min-width="100"
        >
        </el-table-column>
        <el-table-column
          prop="startTime"
          label="起始时间"
          align="center"
          min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="endTime"
          label="结束时间"
          align="center"
          min-width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>

        <el-table-column
          prop="districtName"
          label="生效区域"
          align="center"
          width="100"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
      </el-table>
    </div>
    <br /><br />

    <h2>重要活动状态：{{ tableData.length > 0 ? "有" : "无" }}</h2>
    <div class="table-main">
      <!-- <div class="table-top">
          <h2>特殊时期列表</h2>
        </div> -->
      <div>
        <div class="table">
          <el-table
            :data="tableData"
            v-loading="loading"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            style="width: 100%"
            ref="multipleTable"
          >
            <!-- <el-table-column
                type="selection"
                width="45"
                fixed="left"
                align="center"
              >
              </el-table-column> -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="periodName"
              label="活动名称"
              align="center"
              min-width="150"
            >
            </el-table-column>
            <el-table-column
              prop="startTime"
              label="起始时间"
              align="center"
              width="150"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column
              prop="endTime"
              label="结束时间"
              align="center"
              width="150"
              :show-overflow-tooltip="true"
            >
            </el-table-column>

            <el-table-column
              prop="districtName"
              label="生效区域"
              align="center"
              width="150"
              :show-overflow-tooltip="true"
            >
            </el-table-column>

            <el-table-column
              prop="draftFlagName"
              label="策略状态"
              align="center"
              width="100"
              :show-overflow-tooltip="true"
            >
            </el-table-column>
            <el-table-column label="操作" width="180" align="center">
              <template slot-scope="{ row }">
                <div class="tabButton">
                  <el-button type="text" @click="openDialog('read', row)"
                    >查看</el-button
                  >
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <!-- <div class="pagination">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page.sync="searchObj.nowPage"
            background
            layout="total, prev, pager, next"
            :total="searchObj.total"
            v-if="searchObj.total != 0"
          >
          </el-pagination>
        </div> -->
      </div>
    </div>
 

  </el-scrollbar>
    </div>

    <el-dialog
      :title="dialogInfo.title"
      :visible.sync="dialogInfo.visible"
      width="1100px"
      @close="closeDialog"
      v-if="dialogInfo.visible"
      :close-on-click-modal="false"
      :modal-append-to-body="true"
      :append-to-body="true"
    >
      <div class="dialog">
        <el-form
          :model="accidentForm"
          :rules="rules"
          ref="ruleForm"
          label-width="150px"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="行政区划:" prop="periodName">
                <el-input
                  v-model.trim="accidentForm.districtName"
                  maxlength="50"
                  placeholder="请输入行政区划"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
              <!-- <el-form-item label="行政区划:" prop="districtCode">
                <el-cascader
                  placeholder="请选择行政区划"
                  :options="district"
                  :disabled="dialogInfo.disable"
                  v-model="accidentForm.districtCode"
                  :props="{
                    checkStrictly: true,
                    value: 'distCode',
                    label: 'distName',
                    children: 'children',
                    emitPath: false,
                  }"
                  clearable
                  :show-all-levels="true"
                  style="width: 100%"
                ></el-cascader>
              </el-form-item> -->
            </el-col>
            <el-col :span="12">
              <el-form-item label="日期类型:" prop="periodType">
                <el-select
                  v-model="accidentForm.periodType"
                  placeholder="请输入日期类型"
                  style="width: 100%"
                  :disabled="dialogInfo.disable"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="活动名称:" prop="periodName">
                <el-input
                  v-model.trim="accidentForm.periodName"
                  maxlength="50"
                  placeholder="请输入活动名称"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="活动日期" prop="startTime">
                <el-date-picker
                  v-model="value1"
                  type="daterange"
                  style="width: 100%"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  @change="searchTime"
                  value-format="yyyy-MM-dd"
                  :disabled="dialogInfo.disable"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="是否启用:" prop="draftFlag">
                <el-select
                  v-model="accidentForm.draftFlag"
                  placeholder="请输入日期类型"
                  style="width: 100%"
                  :disabled="dialogInfo.disable"
                >
                  <el-option
                    v-for="item in draftFlagOption"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12"> </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="活动描述:" prop="periodNote">
                <el-input
                  v-model.trim="accidentForm.periodNote"
                  type="textarea"
                  :rows="5"
                  placeholder="2000字以内"
                  maxlength="2000"
                  :disabled="dialogInfo.disable"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import {
  findPeriod,
  addPeriod,
  deletePeriod,
  updatePeriod,
  findPeriodList
} from "@/api/riskAssessment";
var dayjs = require("dayjs");
export default {
  data() {
    return {
       calendarData: new Date(),
      statutoryData: [],
      district: this.$store.state.login.userDistCode,
      isamendment: false,
      tableData: [], // 表格数据
      loading: false, // 加载状态
      searchObj: {
        // 表格查询参数
        nowPage: 1,
        total: 0,
        pageSize: 10,
        keyword: "",
      },
      dateValue: "", // 时间选择
      typeList: [
        { name: "节假日", code: "0" },
        { name: "重大活动", code: "1" },
      ], // 特殊时期类型数据
      draftFlagOption: [
        { name: "未启用", code: "0" },
        { name: "启用", code: "1" },
      ],
      dialogInfo: {
        // 弹窗控制信息
        visible: false,
        title: "新增特殊时期信息",
        disable: false,
      },
      accidentForm: {
        startTime: "",
        endTime: "",
      },
    };
  },
  watch: {},
  methods: {
    // 打开弹窗
    openDialog(type, data) {
      const dataList = Object.assign({}, data);
      if (type === "read") {
        this.value1 = [];
        this.value1.push(dataList.startTime, dataList.endTime);
        this.accidentForm = dataList;
        this.dialogInfo.title = "特殊时期信息详情";
        this.dialogInfo.disable = true;
      }
      this.dialogInfo.visible = true;
    },
    CloseClear() {},
    getData(val,type) {
      this.isamendment = val;
      this.getSearch(type);
    },
    // 分页查询
    handleCurrentChange(val) {
      this.searchObj.nowPage = val;
      this.getSearch(1);
    },
    getSearch(type) {
       this.loading = true;  
      var params = 
        {
          "districtCode":this.$store.state.login.userDistCode,
          "draftFlag": "1",
          "endTime": dayjs(this.calendarData).format("YYYY-MM-DD") ,
          "id": "",
          "keywords": "",
          "nowPage": 0,
          "pageSize": 0,
          "periodType": type,
          "startTime": dayjs(this.calendarData).format("YYYY-MM-DD"),
        }
    
      findPeriodList(params).then((res) => {
        if (res.data.status === 200) {
          if(type==1){
            this.tableData = res.data.data;   
          }else{
            this.statutoryData=res.data.data;  
          } 
          this.loading = false;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
      // this.loading = true;
      // console.log(this.$store.state);
      // var params = {
      //   districtCode: this.$store.state.login.userDistCode,
      //   "endTime": dayjs(this.calendarData).format("YYYY-MM-DD") ,
      //   "startTime": dayjs(this.calendarData).format("YYYY-MM-DD"),
      // };
      // findPeriod(params).then((res) => {
      //   if (res.data.status === 200) {
      //     this.tableData = res.data.data.list;
      //     this.searchObj.nowPage = res.data.data.nowPage + 1;
      //     this.searchObj.total = res.data.data.total;
      //     this.loading = false;
      //   } else {
      //     this.$message({
      //       message: res.data.msg,
      //       type: "warning",
      //     });
      //   }
      // });
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-calendar-table .el-calendar-day{
  height: 30px;
}
/deep/ .el-calendar__header .el-calendar__button-group{
  display: none!important;
}
/deep/ .el-calendar-table td.is-today{
  background-color: #409EFF!important;
  color:#ccc!important
}
/deep/ .el-table td.el-table__cell div {
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.pagination {
  text-align: right;
  margin: 10px 0 0 0;
}
/deep/ .el-tab-pane {
  height: 500px;
  overflow: auto;
  // height: calc(100%- 200px);
}
/deep/ .el-date-editor.el-input {
  width: auto;
}
.hs_box {
  display: flex;
  justify-content: space-between;
  margin: 0 0 10px 0;
}
.M_box {
  .m_tit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 10px 0;
  }
}
</style>