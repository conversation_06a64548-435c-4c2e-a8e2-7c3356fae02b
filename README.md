# 依赖包若有问题，打开 node_modules.zip 文件到最下面有两个压缩文件 emap 和 vform 的，把文件拷贝到 node_modules 中

# hbsecondaryprojectfront

//2023-03-24
互联网登陆入口
https://yyzc.hbsis.gov.cn:30003/login 15072584537 123456a

互联网环境需要根据端口判断 baseurl
监管端：https://yyzc-whjcyj.hbsis.gov.cn:31443
企业端：https://yyzc.hbsis.gov.cn:30003
互联网环境需要注意
src\main.js
src\utils\WSPlayer\WSPlayer.js

互联网环境，工作台出始化视频，获取不到 videoApi,找到如下页面，手动设置
workbench\superviseWorkbench\video.vue

//登陆过期区分
测试环境-登陆过期跳转页面
监管端：http://**************:8090/admin/user/oauth2Login
企业端：http://**************:8099/admin/user/casLogin

正式环境-登陆过期跳转页面
监管端：http://**************:9999/admin/user/oauth2Login
企业端：http://************:9080/admin/user/casLogin

互联网-登陆过期跳转页面
监管端：https://yyzc-whjcyj.hbsis.gov.cn:31443/admin/user/oauth2Login 
企业端：https://yyzc.hbsis.gov.cn:30003/admin/user/casLogin

正式环境 屏蔽以下内容
src\components\feature\enterpriseManagement\basicInformation\index.vue
安全生产证照、安全生产标准化、安全评价管理

src\components\feature\enterpriseManagement\entManagement.vue
风险动态、设备设施、监测设备、三维

src\components\feature\workbench\superviseWorkbench\video.vue
视频 AI 报警、安全生产事故警示

src\components\feature\workbench\superviseWorkbench\securityCommitments.vue
信息发布、轮播点名、日报

> A Vue.js project
> build -------》》》webpack 配置文件
> config -------》》》vue 配置文件
> src -------》》》vue 主文件夹

       api    -------》》》接口与校验规则
       asstes -------》》》静态文件
       components ---》》》
                  common  ---》》》头部布局文件及自定义组件
                  feature ---》》》mis端文件
                  structure ---》》》 超级管理员页面
                  login.vue --------》》》 未接入统一认证平台时登录所用的页面
                  Zhome.vue --------》》》 主布局文件

router -------》》》路由文件
store -------》》》vuex
utils -------》》》第三方 js 文件与 vue_bus

接入统一认证平台的登录方式后在 Zhome.vue 里面进行登录判断
mounted() {
if (this.$route.query.token) {
      this.$store.state.login.user = {};
this.$store.state.login.user.token_type = "bearer";
      this.$store.state.login.user.access_token = this.$route.query.token;
this.Login();
} else {
//获取侧导航数据
this.getAside();
}
//webSokect
this.init();
}

## Build Setup

```bash
# install dependencies
npm install

# serve with hot reload at localhost:8083
npm run dev

# build for production with minification
npm run build

# build for production and view the bundle analyzer report
npm run build --report
```

For a detailed explanation on how things work, check out the [guide](http://vuejs-templates.github.io/webpack/) and [docs for vue-loader](http://vuejs.github.io/vue-loader).
node-gyp 在此解决方案中一次生成一个项目。若要启用并行生成，请添加“/m”开关。
在此解决方案中一次生成一个项目。若要启用并行生成，请添加“/m”开关。
MSBUILD : error MSB3428: 未能加载 Visual C++ 组件“VCBuild.exe”。要解决此问题，

1. 安装 .NET Framework 2.0 SDK；

2. 安装 Microsoft Visual Studio 2005；或

3. 如果将该组件安装到了 其他位置，请将其位置添加到系统 路径中。 [G:\nodejs\moviesite\node_modules\bcrypt\build\binding.sln]
   gyp ERR! build error

npm install --global --production windows-build-tools
