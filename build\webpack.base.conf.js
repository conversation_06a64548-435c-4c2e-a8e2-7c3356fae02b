// "use strict";
const path = require("path");
const utils = require("./utils");
const config = require("../config");
const vueLoaderConfig = require("./vue-loader.conf");
const VueLoaderPlugin = require("vue-loader/lib/plugin");
const webpack = require("webpack");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const ExtractTextPlugin = require("extract-text-webpack-plugin");
const HappyPack = require("happypack");
const os = require("os");

function systemInformation() {
  const G = 1024 * 1024 * 1024;
  console.log("系统版本：%s %s %s", os.type(), os.release(), os.arch());
  console.log("开机时长：%sh", (os.uptime() / 3600).toFixed(1));
  console.log("总内存：%s", `${(os.totalmem() / G).toFixed(2)}G`);
  console.log("可用内存：%s", `${(os.freemem() / G).toFixed(2)}G`);
  console.log("CPU：", os.cpus()[0].model, os.cpus().length + "线程处理器");
}
systemInformation();
// 优化线程池大小，避免过度并发 - 进一步减少线程数
const happyThreadPool = HappyPack.ThreadPool({
  size: Math.min(2, os.cpus().length - 2) || 1,
});
const ProgressPlugin = require("progress-bar-webpack-plugin");
function resolve(dir) {
  return path.join(__dirname, "..", dir);
}

module.exports = {
  mode: "development",
  context: path.resolve(__dirname, "../"),
  entry: {
    app: "./src/main.js",
  },
  // 启用缓存以提高构建速度 (webpack 4 使用 true)
  cache: true,
  output: {
    path: config.build.assetsRoot,
    filename: "[name].js",
    publicPath:
      process.env.NODE_ENV === "production"
        ? config.build.assetsPublicPath
        : config.dev.assetsPublicPath,
  },
  target: "web",
  resolve: {
    extensions: [
      ".js", // 最常用的放前面
      ".vue",
      ".json",
      ".ts",
      ".mjs",
      ".tsx",
      ".jsx",
      ".wasm",
      ".scss",
    ],
    alias: {
      vue$: "vue/dist/vue.esm.js",
      "@": resolve("src"),
      "/static": path.resolve(__dirname, "../static"),
      jquery: "jquery",
    },
    // 优化模块解析性能
    modules: [resolve("src"), "node_modules"],
    symlinks: false, // 禁用符号链接解析，提高性能
  },
  optimization: {
    //minimize: true, //[new UglifyJsPlugin({})]
    // minimize: true,
    splitChunks: {
      chunks: "all",
      cacheGroups: {
        vendors: {
          test: /[\\/]node_modules[\\/]/,
          name: "vendors",
          chunks: "all",
        },
        "async-vendors": {
          test: /[\\/]node_modules[\\/]/,
          minChunks: 2,
          chunks: "async",
          name: "async-vendors",
        },
        styles: {
          name: "styles",
          test: /\.(scss|css)$/,
          chunks: "all",
          minChunks: 1,
          reuseExistingChunk: true,
          enforce: true,
        },
      },
      // enforce: true,
    },
    runtimeChunk: { name: "runtime" },
  },
  // optimization: {
  //   minimize: true,
  //   splitChunks: {
  //     chunks: "async",
  //     cacheGroups: {
  //       vendors: {
  //         test: /[\\/]node_modules[\\/]/,
  //         name: "11chunk-vendors",
  //         chunks: "async",
  //         priority: 50,
  //       },
  //       src: {
  //         test: /[\\/]src[\\/]/,
  //         name: "12chunk-src",
  //         chunks: "async",
  //       },
  //       elementUI: {
  //         name: "13chunk-element-UI",
  //         test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
  //         chunks: "all",
  //       },
  //       // "ant-design-vue": {
  //       //   test: /[\\/]node_modules[\\/]_?_ant-design-vue@1.7.8@ant-design-vue(.*)/,
  //       //   name: "14chunk-ant-design-vue",
  //       //   chunks: "all",
  //       // },
  //       echarts: {
  //         test: /[\\/]node_modules[\\/]_?echarts(.*)/,
  //         name: "15chunk-echarts",
  //         chunks: "all",
  //       },
  //       quill: {
  //         test: /[\\/]node_modules[\\/]_?quill(.*)/,
  //         name: "17chunk-lib",
  //         chunks: "all",
  //       },
  //       jquery: {
  //         test: /[\\/]node_modules[\\/]_?jquery(.*)/,
  //         name: "17chunk-lib",
  //         chunks: "all",
  //       },
  //       zrender: {
  //         test: /[\\/]node_modules[\\/]_?zrender(.*)/,
  //         name: "17chunk-lib",
  //         chunks: "all",
  //       },
  //       "ant-design_icons": {
  //         test: /[\\/]node_modules[\\/]_?_@ant-design_icons@2.1.1@@ant-design(.*)/,
  //         name: "14chunk-ant-design-vue",
  //         chunks: "all",
  //       },
  //       moment: {
  //         test: /[\\/]node_modules[\\/]_?moment(.*)/,
  //         name: "20chunk-moment",
  //         chunks: "all",
  //       },
  //       "emap-2d": {
  //         test: /[\\/]node_modules[\\/]_?emap-2d(.*)/,
  //         name: "21chunk-emap-2d",
  //         chunks: "initial",
  //       },
  //       styles: {
  //         name: "22chunk-styles",
  //         test: /\.(scss|css|less)$/,
  //         chunks: "all",
  //       },
  //     },
  //   },
  //   runtimeChunk: { name: "runtime" },
  // },
  module: {
    rules: [
      {
        test: /\.vue$/,
        loader: "vue-loader?chaheDirectory=true",
        options: {
          vueLoaderConfig,
          loaders: {
            // css: "vue-style-loader!css-loader",
            // scss: "vue-style-loader!css-loader!sass-loader"
            // ts: [
            //   {
            //     loader: "ts-loader",
            //     options: {
            //       appendTsSuffixTo: [/\.vue$/]
            //     }
            //   }
            // ]
          },
          cacheDirectory: path.resolve(
            __dirname,
            "../node_modules/.cache/vue-loader"
          ),
          cacheIdentifier: "cache-loader:{version} {process.env.NODE_ENV}",
        },
        exclude: /node_modules/,
      },
      {
        test: /\.ts$/,
        exclude: /node_modules/,
        enforce: "pre",
        loader: "tslint-loader?chaheDirectory=true",
      },
      {
        test: /\.tsx?$/,
        loader: "ts-loader?chaheDirectory=true",
        exclude: /node_modules/,
        options: {
          appendTsSuffixTo: [/\.vue$/],
        },
      },
      {
        test: /\.js$/,
        // 把对 .js 文件的处理转交给 id 为 babel 的 HappyPack 实例
        use: ["happypack/loader?id=js"],
        exclude: /node_modules/,
        include: [resolve("src")], // 只处理src目录
      },
      {
        test: /\.(png|jpe?g|gif|webp|svg)(\?.*)?$/,
        loader: "url-loader?chaheDirectory=true",
        options: {
          limit: 4096,
          name: utils.assetsPath("img/[name].[hash:7].[ext]"),
        },
      },
      // {
      //   test: /\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/,
      //   loader: "url-loader",
      //   options: {
      //     limit: 1,
      //     name: utils.assetsPath("media/[name].[hash:7].[ext]"),
      //   },
      // },
      {
        test: /\.(woff2?|eot|ttf|otf)(\?.*)?$/,
        loader: "url-loader?chaheDirectory=true",
        options: {
          limit: 1,
          name: utils.assetsPath("fonts/[name].[hash:7].[ext]"),
        },
      },
    ],
  },
  node: {
    // prevent webpack from injecting useless setImmediate polyfill because Vue
    // source contains it (although only uses it if it's native).
    setImmediate: false,
    // prevent webpack from injecting mocks to Node native modules
    // that does not make sense for the client
    dgram: "empty",
    fs: "empty",
    net: "empty",
    tls: "empty",
    child_process: "empty",
  },
  plugins: [
    new HappyPack({
      // 用唯一的标识符id来代表当前的HappyPack 处理一类特定的文件
      id: "js",
      // 如何处理.js文件，用法和Loader配置是一样的
      loaders: [
        {
          loader: "babel-loader",
          options: {
            cacheDirectory: true, // 启用babel缓存
            cacheCompression: false, // 关闭缓存压缩以提高速度
          },
        },
      ],
      //共享进程池
      threadPool: happyThreadPool,
      //允许 HappyPack 输出日志 - 关闭以提高性能
      verbose: false,
    }),
    new webpack.ProvidePlugin({
      $: "jquery",
      jQuery: "jquery",
    }),
    new VueLoaderPlugin(), //vue-loader的伴生插件
    new ProgressPlugin(), // 显示打包进度
  ],
};
