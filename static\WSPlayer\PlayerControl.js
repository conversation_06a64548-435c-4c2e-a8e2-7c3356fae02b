var PlayerControl=function(e){var t={};function n(r){if(t[r])return t[r].exports;var i=t[r]={i:r,l:!1,exports:{}};return e[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}return n.m=e,n.c=t,n.d=function(e,t,r){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var i in e)n.d(r,i,function(t){return e[t]}.bind(null,i));return r},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="",n(n.s=3)}([function(e,t,n){"use strict";n.d(t,"h",function(){return r}),n.d(t,"e",function(){return d}),n.d(t,"c",function(){return c}),n.d(t,"b",function(){return h}),n.d(t,"d",function(){return f}),n.d(t,"a",function(){return u}),n.d(t,"g",function(){return s}),n.d(t,"f",function(){return l});var r={log:function(){},error:function(){},count:function(){},info:function(){}},i={Opera:"Opera",Chrome:"Chrome",Firefox:"Firefox",Edge:"Edge",IE:"IE",Safari:"Safari"};function a(){var e=navigator.userAgent;return e.includes("Edge")?i.Edge:e.includes("Firefox")?i.Firefox:e.includes("Chrome")?i.Chrome:e.includes("Safari")?i.Safari:e.includes("compatible")&&e.includes("MSIE")&&e.includes("Opera")?i.IE:e.includes("Opera")?i.Opera:""}function o(e){return navigator.userAgent.split(e)[1].split(".")[0].slice(1)}function s(){var e=a(),t=o(e),n=!1;switch(e){case i.Chrome:n=t>=104;break;default:n=0}return{bSupportH265MSE:n,browserType:e,browserVersion:t}}function l(){var e=a(),t=o(e),n=!1,r=0;switch(e){case i.Chrome:n=t>=91,r=701;break;case i.Firefox:n=t>=97,r=702;break;case i.Edge:n=t>=91,r=703;break;default:n=0}return{isVersionCompliance:n,browserType:e,errorCode:r}}function u(){var e=navigator.userAgent.toLowerCase(),t=navigator.appName,n=null;return"Microsoft Internet Explorer"===t||e.indexOf("trident")>-1||e.indexOf("edge/")>-1?(n="ie","Microsoft Internet Explorer"===t?(e=/msie ([0-9]{1,}[\.0-9]{0,})/.exec(e),n+=parseInt(e[1])):e.indexOf("trident")>-1?n+=11:e.indexOf("edge/")>-1&&(n="edge")):e.indexOf("safari")>-1?n=e.indexOf("chrome")>-1?"chrome":"safari":e.indexOf("firefox")>-1&&(n="firefox"),n}var c=function(){function e(){}return e.createFromElementId=function(t){for(var n=document.getElementById(t),r="",i=n.firstChild;i;)3===i.nodeType&&(r+=i.textContent),i=i.nextSibling;var a=new e;return a.type=n.type,a.source=r,a},e.createFromSource=function(t,n){var r=new e;return r.type=t,r.source=n,r},e}(),f=function(){return function(e,t){if("x-shader/x-fragment"===t.type)this.shader=e.createShader(e.FRAGMENT_SHADER);else{if("x-shader/x-vertex"!==t.type)return void error("Unknown shader type: "+t.type);this.shader=e.createShader(e.VERTEX_SHADER)}e.shaderSource(this.shader,t.source),e.compileShader(this.shader),e.getShaderParameter(this.shader,e.COMPILE_STATUS)||error("An error occurred compiling the shaders: "+e.getShaderInfoLog(this.shader))}}(),h=function(){function e(e){this.gl=e,this.program=this.gl.createProgram()}return e.prototype={attach:function(e){this.gl.attachShader(this.program,e.shader)},link:function(){this.gl.linkProgram(this.program)},use:function(){this.gl.useProgram(this.program)},getAttributeLocation:function(e){return this.gl.getAttribLocation(this.program,e)},setMatrixUniform:function(e,t){var n=this.gl.getUniformLocation(this.program,e);this.gl.uniformMatrix4fv(n,!1,t)}},e}(),d=function(){var e=null;function t(e,t,n){this.gl=e,this.size=t,this.texture=e.createTexture(),e.bindTexture(e.TEXTURE_2D,this.texture),this.format=n||e.LUMINANCE,e.texImage2D(e.TEXTURE_2D,0,this.format,t.w,t.h,0,this.format,e.UNSIGNED_BYTE,null),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MAG_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_MIN_FILTER,e.NEAREST),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_S,e.CLAMP_TO_EDGE),e.texParameteri(e.TEXTURE_2D,e.TEXTURE_WRAP_T,e.CLAMP_TO_EDGE)}return t.prototype={fill:function(e,t){var n=this.gl;n.bindTexture(n.TEXTURE_2D,this.texture),t?n.texSubImage2D(n.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,n.UNSIGNED_BYTE,e):n.texImage2D(n.TEXTURE_2D,0,this.format,this.size.w,this.size.h,0,this.format,n.UNSIGNED_BYTE,e)},bind:function(t,n,r){var i=this.gl;e||(e=[i.TEXTURE0,i.TEXTURE1,i.TEXTURE2]),i.activeTexture(e[t]),i.bindTexture(i.TEXTURE_2D,this.texture),i.uniform1i(i.getUniformLocation(n.program,r),t)}},t}()},,function(e,t){e.exports=function(){return new Worker("./static/WSPlayer/audioTalkWorker.js")}},function(e,t,n){"use strict";n.r(t);var r=function(e){var t=[],n={},r=e;function i(){for(var e in t)t[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)];0,1==r?n.FTYP=new Uint8Array([105,115,111,109,0,0,0,1,105,115,111,109,97,118,99,49]):2==r&&(n.FTYP=new Uint8Array([105,115,111,109,0,0,2,0,105,115,111,109,105,115,111,50,97,118,99,49,109,112,52,49])),n.STSD_PREFIX=new Uint8Array([0,0,0,0,0,0,0,1]),n.STTS=new Uint8Array([0,0,0,0,0,0,0,0]),n.STSC=n.STCO=n.STTS,n.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),n.HDLR_VIDEO=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),n.HDLR_AUDIO=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]),n.DREF=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),n.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0])}t={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],mvex:[],mvhd:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[],hev1:[],hvcC:[]};var a=function(e){for(var t=8,n=Array.prototype.slice.call(arguments,1),r=0;r<n.length;r++)t+=n[r].byteLength;var i=new Uint8Array(t),a=0;i[a++]=t>>>24&255,i[a++]=t>>>16&255,i[a++]=t>>>8&255,i[a++]=255&t,i.set(e,a),a+=4;for(r=0;r<n.length;r++)i.set(n[r],a),a+=n[r].byteLength;return i},o=function(e){return a(t.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,(65280&e.channelcount)>>8,255&e.channelcount,(65280&e.samplesize)>>8,255&e.samplesize,0,0,0,0,(65280&e.samplerate)>>8,255&e.samplerate,0,0]),function(e){var n=e.config,r=n.length,i=new Uint8Array([0,0,0,0,3,23+r,0,1,0,4,15+r,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([r]).concat(n).concat([6,1,2]));return a(t.esds,i)}(e))},s=function(e){return"audio"===e.type?a(t.stsd,n.STSD_PREFIX,o(e)):a(t.stsd,n.STSD_PREFIX,function(e){var n=e.vps||[],i=e.sps||[],o=e.pps||[],s=[],l=[],u=[],c=0;for(c=0;c<n.length;c++)s.push((65280&n[c].byteLength)>>>8),s.push(255&n[c].byteLength),s=s.concat(Array.prototype.slice.call(n[c]));for(c=0;c<i.length;c++)l.push((65280&i[c].byteLength)>>>8),l.push(255&i[c].byteLength),l=l.concat(Array.prototype.slice.call(i[c]));for(c=0;c<o.length;c++)u.push((65280&o[c].byteLength)>>>8),u.push(255&o[c].byteLength),u=u.concat(Array.prototype.slice.call(o[c]));return 1==r?a(t.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a(t.avcC,new Uint8Array([1,e.profileIdc,e.profileCompatibility,e.levelIdc,255].concat([i.length]).concat(l).concat([o.length]).concat(u)))):2==r?a(t.hev1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,(65280&e.width)>>8,255&e.width,(65280&e.height)>>8,255&e.height,0,72,0,0,0,72,0,0,0,0,0,0,0,1,19,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a(t.hvcC,new Uint8Array([1,e.general_profile_flag,(4278190080&e.general_profile_compatibility_flags)>>>24,(16711680&e.general_profile_compatibility_flags)>>>16,(65280&e.general_profile_compatibility_flags)>>>8,255&e.general_profile_compatibility_flags,(0xff0000000000&e.general_constraint_indicator_flags)>>>40,(0xff00000000&e.general_constraint_indicator_flags)>>>32,(4278190080&e.general_constraint_indicator_flags)>>>24,(16711680&e.general_constraint_indicator_flags)>>>16,(65280&e.general_constraint_indicator_flags)>>>8,255&e.general_constraint_indicator_flags,e.general_level_idc,240,0,252,252|e.chroma_format_idc,248|e.bitDepthLumaMinus8,248|e.bitDepthChromaMinus8,0,0,e.rate_layers_nested_length,3].concat([32,0,1]).concat(s).concat([33,0,1]).concat(l).concat([34,0,1]).concat(u)))):void 0}(e))},l=function(e){var r=null;return r="audio"===e.type?a(t.smhd,n.SMHD):a(t.vmhd,n.VMHD),a(t.minf,r,a(t.dinf,a(t.dref,n.DREF)),function(e){return a(t.stbl,s(e),a(t.stts,n.STTS),a(t.stsc,n.STSC),a(t.stsz,n.STSZ),a(t.stco,n.STCO))}(e))},u=function(e){return a(t.mdia,function(e){var n=e.timescale,r=e.duration;return a(t.mdhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,85,196,0,0]))}(e),function(e){var r=null;return r="audio"===e.type?n.HDLR_AUDIO:n.HDLR_VIDEO,a(t.hdlr,r)}(e),l(e))},c=function(e){return a(t.trak,function(e){var n=e.id,r=e.duration,i=e.width,o=e.height;return a(t.tkhd,new Uint8Array([0,0,0,7,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,0,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>>8&255,255&i,0,0,o>>>8&255,255&o,0,0]))}(e),u(e))},f=function(e){return a(t.mvex,function(e){var n=e.id,r=new Uint8Array([0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]);return a(t.trex,r)}(e))},h=function(e){var n,r,i=(n=e.timescale,r=e.duration,a(t.mvhd,new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,n>>>24&255,n>>>16&255,n>>>8&255,255&n,r>>>24&255,r>>>16&255,r>>>8&255,255&r,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]))),o=c(e),s=f(e);return a(t.moov,i,o,s)},d=function(e,n){return"audio"===e.type?audioTrun(e,n):function(e,n){var r,i=null,o=null,s=0,l=n;if(null===(r=e.samples||[])[0].frameDuration)for(l+=24+4*r.length,i=trunHeader(r,l),s=0;s<r.length;s++)o=r[s],i=i.concat([(4278190080&o.size)>>>24,(16711680&o.size)>>>16,(65280&o.size)>>>8,255&o.size]);else for(i=function(e,t){return[0,0,3,5,(4278190080&e.length)>>>24,(16711680&e.length)>>>16,(65280&e.length)>>>8,255&e.length,(4278190080&t)>>>24,(16711680&t)>>>16,(65280&t)>>>8,255&t,0,0,0,0]}(r,l+=24+4*r.length+4*r.length),s=0;s<r.length;s++)o=r[s],i=i.concat([(4278190080&o.frameDuration)>>>24,(16711680&o.frameDuration)>>>16,(65280&o.frameDuration)>>>8,255&o.frameDuration,(4278190080&o.size)>>>24,(16711680&o.size)>>>16,(65280&o.size)>>>8,255&o.size]);return a(t.trun,new Uint8Array(i))}(e,n)},p=function(e,n){return a(t.moof,function(e){var n=new Uint8Array([0,0,0,0,e>>>24&255,e>>>16&255,e>>>8&255,255&e]);return a(t.mfhd,n)}(e),function(e){var n,r,i;return n=a(t.tfhd,new Uint8Array([0,2,0,0,0,0,0,1])),r=a(t.tfdt,new Uint8Array([0,0,0,0,e.baseMediaDecodeTime>>>24&255,e.baseMediaDecodeTime>>>16&255,e.baseMediaDecodeTime>>>8&255,255&e.baseMediaDecodeTime])),i=d(e,72),a(t.traf,n,r,i)}(n))};return i.prototype={initSegment:function(e){var r=a(t.ftyp,n.FTYP),i=h(e),o=new Uint8Array(r.byteLength+i.byteLength);return o.set(r,0),o.set(i,r.byteLength),o},mediaSegment:function(e,n,r,i){var o=p(e,n),s=function(e){return a(t.mdat,e)}(r),l=null;return(l=new Uint8Array(o.byteLength+s.byteLength)).set(o),l.set(s,o.byteLength),l}},new i};function i(e){var t=7,n=7,r=2,i=3,o=4,s=5,l=8,u=16,c=32,f=255,h=0,d=null,p=e;function m(){h=0,d=new a}function g(e,r){var a=r,o=h+a>>i;return a=h+r&t,e[o]>>n-(a&n)&1}function v(e,t){var n=h>>i,r=8*(n+1)-h;if(r<8)for(var a=0;a<3;a++){var o=e[n+a];o=0==a?o>>r<<r:2==a?o&255>>8-r|1<<r:0,e.set([o],n+a)}else e.set([0],n),e.set([1],n+1)}function _(e,t){if(t<=25)var n=y(e,t);else n=y(e,16)<<t-16|y(e,t-16);return n}function y(e,t){var n=0;if(1===t)n=g(e,0);else for(var r=0;r<t;r++)n=(n<<1)+g(e,r);return h+=t,n}function b(e,t){for(var n=0,i=0,a=t;h+a<8*e.length&&!g(e,a++);)n++;if(0===n)return h+=1,0;i=1<<n;for(var o=n-1;o>=0;o--,a++)i|=g(e,a)<<o;return h+=n*r+1,i-1}function w(e,t){var n=b(e,t);return 1&n?(n+1)/r:-n/r}function S(e){d.put("cpb_cnt_minus1",b(e,0)),d.put("bit_rate_scale",y(e,o)),d.put("cpb_size_scale",y(e,o));for(var t=d.get("cpb_cnt_minus1"),n=new Array(t),r=new Array(t),i=new Array(t),a=0;a<=t;a++)n[a]=b(e,0),r[a]=b(e,0),i[a]=y(e,1);d.put("bit_rate_value_minus1",n),d.put("cpb_size_value_minus1",r),d.put("cbr_flag",i),d.put("initial_cpb_removal_delay_length_minus1",y(e,s)),d.put("cpb_removal_delay_length_minus1",y(e,s)),d.put("dpb_output_delay_length_minus1",y(e,s)),d.put("time_offset_length",y(e,s))}return m.prototype={parse:function(e){if(h=0,d.clear(),1==p){d.put("forbidden_zero_bit",y(e,1)),d.put("nal_ref_idc",y(e,r)),d.put("nal_unit_type",y(e,s)),d.put("profile_idc",y(e,l)),d.put("profile_compatibility",y(e,l)),d.put("level_idc",y(e,l)),d.put("seq_parameter_set_id",b(e,0));var t=d.get("profile_idc");if((100===t||110===t||122===t||244===t||44===t||83===t||86===t||118===t||128===t||138===t||139===t||134===t)&&(d.put("chroma_format_idc",b(e,0)),d.get("chroma_format_idc")===i&&d.put("separate_colour_plane_flag",y(e,1)),d.put("bit_depth_luma_minus8",b(e,0)),d.put("bit_depth_chroma_minus8",b(e,0)),d.put("qpprime_y_zero_transform_bypass_flag",y(e,1)),d.put("seq_scaling_matrix_present_flag",y(e,1)),d.get("seq_scaling_matrix_present_flag"))){for(var n=d.get("chroma_format_idc")!==i?l:12,a=new Array(n),o=0;o<n;o++)if(a[o]=y(e,1),a[o])for(var m=o<6?u:64,g=8,T=8,C=0;C<m;C++)T&&(T=(g+w(e,0)+256)%256),g=0===T?g:T;d.put("seq_scaling_list_present_flag",a)}if(d.put("log2_max_frame_num_minus4",b(e,0)),d.put("pic_order_cnt_type",b(e,0)),0===d.get("pic_order_cnt_type"))d.put("log2_max_pic_order_cnt_lsb_minus4",b(e,0));else if(1===d.get("pic_order_cnt_type")){d.put("delta_pic_order_always_zero_flag",y(e,1)),d.put("offset_for_non_ref_pic",w(e,0)),d.put("offset_for_top_to_bottom_field",w(e,0)),d.put("num_ref_frames_in_pic_order_cnt_cycle",b(e,0));for(var A=0;A<d.get("num_ref_frames_in_pic_order_cnt_cycle");A++)d.put("num_ref_frames_in_pic_order_cnt_cycle",w(e,0))}d.put("num_ref_frames",b(e,0)),d.put("gaps_in_frame_num_value_allowed_flag",y(e,1)),d.put("pic_width_in_mbs_minus1",b(e,0)),d.put("pic_height_in_map_units_minus1",b(e,0)),d.put("frame_mbs_only_flag",y(e,1)),0===d.get("frame_mbs_only_flag")&&d.put("mb_adaptive_frame_field_flag",y(e,1)),d.put("direct_8x8_interence_flag",y(e,1)),d.put("frame_cropping_flag",y(e,1)),1===d.get("frame_cropping_flag")&&(d.put("frame_cropping_rect_left_offset",b(e,0)),d.put("frame_cropping_rect_right_offset",b(e,0)),d.put("frame_cropping_rect_top_offset",b(e,0)),d.put("frame_cropping_rect_bottom_offset",b(e,0))),d.put("vui_parameters_present_flag",y(e,1)),d.get("vui_parameters_present_flag")&&function(e){d.put("aspect_ratio_info_present_flag",y(e,1)),d.get("aspect_ratio_info_present_flag")&&(d.put("aspect_ratio_idc",y(e,l)),d.get("aspect_ratio_idc")===f&&(v(e),d.put("sar_width",y(e,u)),v(e),d.put("sar_height",y(e,u)))),d.put("overscan_info_present_flag",y(e,1)),d.get("overscan_info_present_flag")&&d.put("overscan_appropriate_flag",y(e,1)),d.put("video_signal_type_present_flag",y(e,1)),d.get("video_signal_type_present_flag")&&(d.put("video_format",y(e,i)),d.put("video_full_range_flag",y(e,1)),d.put("colour_description_present_flag",y(e,1)),d.get("colour_description_present_flag")&&(d.put("colour_primaries",y(e,l)),d.put("transfer_characteristics",y(e,l)),d.put("matrix_coefficients",y(e,l)))),d.put("chroma_loc_info_present_flag",y(e,1)),d.get("chroma_loc_info_present_flag")&&(d.put("chroma_sample_loc_type_top_field",b(e,0)),d.put("chroma_sample_loc_type_bottom_field",b(e,0))),d.put("timing_info_present_flag",y(e,1)),d.get("timing_info_present_flag")&&(d.put("num_units_in_tick",y(e,c)),d.put("time_scale",y(e,c)),d.put("fixed_frame_rate_flag",y(e,1))),d.put("nal_hrd_parameters_present_flag",y(e,1)),d.get("nal_hrd_parameters_present_flag")&&S(e),d.put("vcl_hrd_parameters_present_flag",y(e,1)),d.get("vcl_hrd_parameters_present_flag")&&S(e),(d.get("nal_hrd_parameters_present_flag")||d.get("vcl_hrd_parameters_present_flag"))&&d.put("low_delay_hrd_flag",y(e,1)),d.put("pic_struct_present_flag",y(e,1)),d.put("bitstream_restriction_flag",y(e,1)),d.get("bitstream_restriction_flag")&&(d.put("motion_vectors_over_pic_boundaries_flag",y(e,1)),d.put("max_bytes_per_pic_denom",b(e,0)),d.put("max_bits_per_mb_denom",b(e,0)))}(e)}else if(2==p){var P=new ArrayBuffer(256),R=new Uint8Array(P);!function(e,t,n,r){for(var i=0,a=0;i+2<t&&a+2<r;++i)0==e[i]&&0==e[i+1]&&3==e[i+2]?(n[a++]=e[i++],n[a++]=e[i++]):n[a++]=e[i];for(;i<t&&a<r;)n[a++]=e[i++]}(e,e.length,R,256);var M=[],E=[];y(R,4);var x=y(R,3);for(d.put("temporalIdNested",y(R,1)),d.put("general_profile_space",y(R,2)),d.put("general_tier_flag",y(R,1)),d.put("general_profile_idc",y(R,5)),d.put("general_profile_compatibility_flags",_(R,32)),d.put("general_constraint_indicator_flags",(U=R,(I=48)<=32?_(U,I):_(U,I-32)<<32|_(U,32))),d.put("general_level_idc",y(R,8)),o=0;o<x&&o<MAX_SUB_LAYERS;o++)M[o]=y(R,1),E[o]=y(R,1);if(x>0)for(o=x;o<8;o++)y(R,2);for(o=0;o<x&&o<MAX_SUB_LAYERS;o++)E[o]&&y(R,8);b(R,0);d.put("chroma_format_idc",b(R,0));b(R,0),b(R,0);y(R,1),b(R,0),b(R,0),b(R,0),b(R,0),d.put("bitDepthLumaMinus8",b(R,0)+8),d.put("bitDepthChromaMinus8",b(R,0)+8),P=null,R=null}var U,I;return!0},getSizeInfo:function(){var e=0,t=0;0===d.get("chroma_format_idc")?e=t=0:1===d.get("chroma_format_idc")?e=t=r:d.get("chroma_format_idc")===r?(e=r,t=1):d.get("chroma_format_idc")===i&&(0===d.get("separate_colour_plane_flag")?e=t=1:1===d.get("separate_colour_plane_flag")&&(e=t=0));var n=d.get("pic_width_in_mbs_minus1")+1,a=d.get("pic_height_in_map_units_minus1")+1,o=(r-d.get("frame_mbs_only_flag"))*a,s=0,l=0,c=0,f=0;1===d.get("frame_cropping_flag")&&(s=d.get("frame_cropping_rect_left_offset"),l=d.get("frame_cropping_rect_right_offset"),c=d.get("frame_cropping_rect_top_offset"),f=d.get("frame_cropping_rect_bottom_offset"));var h=n*u*(o*u);return{width:n*u-e*(s+l),height:o*u-t*(r-d.get("frame_mbs_only_flag"))*(c+f),decodeSize:h}},getSpsValue:function(e){return d.get(e)},getCodecInfo:function(){return d.get("profile_idc").toString(u)+(d.get("profile_compatibility")<15?"0"+d.get("profile_compatibility").toString(u):d.get("profile_compatibility").toString(u))+d.get("level_idc").toString(u)}},new m}var a=function(){this.map={}};a.prototype={put:function(e,t){this.map[e]=t},get:function(e){return this.map[e]},containsKey:function(e){return e in this.map},containsValue:function(e){for(var t in this.map)if(this.map[t]===e)return!0;return!1},isEmpty:function(e){return 0===this.size()},clear:function(){for(var e in this.map)delete this.map[e]},remove:function(e){delete this.map[e]},keys:function(){var e=new Array;for(var t in this.map)e.push(t);return e},values:function(){var e=new Array;for(var t in this.map)e.push(this.map[t]);return e},size:function(){var e=0;for(var t in this.map)e++;return e}};var o=function(){var e=200,t=null,n=null,r=null,i=0,a=0,o=!1,s=0,l=0,u=null,c=!1,f=new Float32Array(8e4),h=0,d=null,p=0;function m(n,i){var o=i-s;if((o>e||o<0)&&(a=0,h=0,c=!0,null!==d&&d.stop()),a-t.currentTime<0&&(a=0),s=i,f=function(e,t,n){var r=e;return n+t.length>=r.length&&(r=new Float32Array(r.length+8e4)).set(r,0),r.set(t,n),r}(f,n,h),h+=n.length,!c){var l=0;if(h/n.length>1&&(null!==u&&(l=u*p),l>=h||null===u))return void(h=0);var m=null;(m=t.createBuffer(1,h-l,p)).getChannelData(0).set(f.subarray(l,h)),h=0,(d=t.createBufferSource()).buffer=m,d.connect(r),a||(a=t.currentTime+.1),d.start(a),a+=m.duration}}function g(){}return g.prototype={audioInit:function(e,i){if(a=0,null!==t)debug.info("Audio context already defined!");else try{return window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(t=new AudioContext).onstatechange=function(){"running"===t.state&&(o=!0)},p=i,n=t.createGain(),(r=t.createBiquadFilter()).connect(n),r.type="lowpass",r.frequency.value=4e3,r.gain.value=40,n.connect(t.destination),this.setVolume(e),!0}catch(e){return debug.error("Web Audio API is not supported in this web browser! : "+e),!1}},play:function(){this.setVolume(i)},stop:function(){i=0,n.gain.value=0,a=0,t=null},bufferAudio:function(e,t){o&&m(e,0)},setVolume:function(e){i=e;var t=e/1;t<=0?(n.gain.value=0,a=0):n.gain.value=t>=1?1:t},getVolume:function(){return i},Mute:function(e){if(e)n.gain.value=0,a=0;else{var t=i/1;t<=0?(n.gain.value=0,a=0):n.gain.value=t>=1?1:t}},terminate:function(){"closed"!==t.state&&(a=0,o=!1,t.close())},setBufferingFlag:function(e,t){"init"===t?l=e:c&&(0===e||"undefined"===typeof e||null===e?u=null:(u=e-l,l=0),c=!1)},getBufferingFlag:function(){return c},setInitVideoTimeStamp:function(e){l=e},getInitVideoTimeStamp:function(){return l},setSamplingRate:function(e){p=e}},new g},s=n(0),l=1e-6;function u(){}function c(){}function f(){}function h(){}u.prototype={e:function(e){return e<1||e>this.elements.length?null:this.elements[e-1]},dimensions:function(){return this.elements.length},modulus:function(){return Math.sqrt(this.dot(this))},eql:function(e){var t=this.elements.length,n=e.elements||e;if(t!=n.length)return!1;do{if(Math.abs(this.elements[t-1]-n[t-1])>l)return!1}while(--t);return!0},dup:function(){return u.create(this.elements)},map:function(e){var t=[];return this.each(function(n,r){t.push(e(n,r))}),u.create(t)},each:function(e){var t,n=this.elements.length,r=n;do{t=r-n,e(this.elements[t],t+1)}while(--n)},toUnitVector:function(){var e=this.modulus();return 0===e?this.dup():this.map(function(t){return t/e})},angleFrom:function(e){var t=e.elements||e,n=this.elements.length;if(n!=t.length)return null;var r=0,i=0,a=0;if(this.each(function(e,n){r+=e*t[n-1],i+=e*e,a+=t[n-1]*t[n-1]}),i=Math.sqrt(i),a=Math.sqrt(a),i*a===0)return null;var o=r/(i*a);return o<-1&&(o=-1),o>1&&(o=1),Math.acos(o)},isParallelTo:function(e){var t=this.angleFrom(e);return null===t?null:t<=l},isAntiparallelTo:function(e){var t=this.angleFrom(e);return null===t?null:Math.abs(t-Math.PI)<=l},isPerpendicularTo:function(e){var t=this.dot(e);return null===t?null:Math.abs(t)<=l},add:function(e){var t=e.elements||e;return this.elements.length!=t.length?null:this.map(function(e,n){return e+t[n-1]})},subtract:function(e){var t=e.elements||e;return this.elements.length!=t.length?null:this.map(function(e,n){return e-t[n-1]})},multiply:function(e){return this.map(function(t){return t*e})},x:function(e){return this.multiply(e)},dot:function(e){var t=e.elements||e,n=0,r=this.elements.length;if(r!=t.length)return null;do{n+=this.elements[r-1]*t[r-1]}while(--r);return n},cross:function(e){var t=e.elements||e;if(3!=this.elements.length||3!=t.length)return null;var n=this.elements;return u.create([n[1]*t[2]-n[2]*t[1],n[2]*t[0]-n[0]*t[2],n[0]*t[1]-n[1]*t[0]])},max:function(){var e,t=0,n=this.elements.length,r=n;do{e=r-n,Math.abs(this.elements[e])>Math.abs(t)&&(t=this.elements[e])}while(--n);return t},indexOf:function(e){var t,n=null,r=this.elements.length,i=r;do{t=i-r,null===n&&this.elements[t]==e&&(n=t+1)}while(--r);return n},toDiagonalMatrix:function(){return c.Diagonal(this.elements)},round:function(){return this.map(function(e){return Math.round(e)})},snapTo:function(e){return this.map(function(t){return Math.abs(t-e)<=l?e:t})},distanceFrom:function(e){if(e.anchor)return e.distanceFrom(this);var t=e.elements||e;if(t.length!=this.elements.length)return null;var n,r=0;return this.each(function(e,i){n=e-t[i-1],r+=n*n}),Math.sqrt(r)},liesOn:function(e){return e.contains(this)},liesIn:function(e){return e.contains(this)},rotate:function(e,t){var n,r,i,a,o;switch(this.elements.length){case 2:return 2!=(n=t.elements||t).length?null:(r=c.Rotation(e).elements,i=this.elements[0]-n[0],a=this.elements[1]-n[1],u.create([n[0]+r[0][0]*i+r[0][1]*a,n[1]+r[1][0]*i+r[1][1]*a]));case 3:if(!t.direction)return null;var s=t.pointClosestTo(this).elements;return r=c.Rotation(e,t.direction).elements,i=this.elements[0]-s[0],a=this.elements[1]-s[1],o=this.elements[2]-s[2],u.create([s[0]+r[0][0]*i+r[0][1]*a+r[0][2]*o,s[1]+r[1][0]*i+r[1][1]*a+r[1][2]*o,s[2]+r[2][0]*i+r[2][1]*a+r[2][2]*o]);default:return null}},reflectionIn:function(e){if(e.anchor){var t=this.elements.slice(),n=e.pointClosestTo(t).elements;return u.create([n[0]+(n[0]-t[0]),n[1]+(n[1]-t[1]),n[2]+(n[2]-(t[2]||0))])}var r=e.elements||e;return this.elements.length!=r.length?null:this.map(function(e,t){return r[t-1]+(r[t-1]-e)})},to3D:function(){var e=this.dup();switch(e.elements.length){case 3:break;case 2:e.elements.push(0);break;default:return null}return e},inspect:function(){return"["+this.elements.join(", ")+"]"},setElements:function(e){return this.elements=(e.elements||e).slice(),this}},u.create=function(e){return(new u).setElements(e)},u.i=u.create([1,0,0]),u.j=u.create([0,1,0]),u.k=u.create([0,0,1]),u.Random=function(e){var t=[];do{t.push(Math.random())}while(--e);return u.create(t)},u.Zero=function(e){var t=[];do{t.push(0)}while(--e);return u.create(t)},c.prototype={e:function(e,t){return e<1||e>this.elements.length||t<1||t>this.elements[0].length?null:this.elements[e-1][t-1]},row:function(e){return e>this.elements.length?null:u.create(this.elements[e-1])},col:function(e){if(e>this.elements[0].length)return null;var t,n=[],r=this.elements.length,i=r;do{t=i-r,n.push(this.elements[t][e-1])}while(--r);return u.create(n)},dimensions:function(){return{rows:this.elements.length,cols:this.elements[0].length}},rows:function(){return this.elements.length},cols:function(){return this.elements[0].length},eql:function(e){var t=e.elements||e;if("undefined"==typeof t[0][0]&&(t=c.create(t).elements),this.elements.length!=t.length||this.elements[0].length!=t[0].length)return!1;var n,r,i,a=this.elements.length,o=a,s=this.elements[0].length;do{n=o-a,r=s;do{if(i=s-r,Math.abs(this.elements[n][i]-t[n][i])>l)return!1}while(--r)}while(--a);return!0},dup:function(){return c.create(this.elements)},map:function(e){var t,n,r,i=[],a=this.elements.length,o=a,s=this.elements[0].length;do{n=s,i[t=o-a]=[];do{r=s-n,i[t][r]=e(this.elements[t][r],t+1,r+1)}while(--n)}while(--a);return c.create(i)},isSameSizeAs:function(e){var t=e.elements||e;return"undefined"==typeof t[0][0]&&(t=c.create(t).elements),this.elements.length==t.length&&this.elements[0].length==t[0].length},add:function(e){var t=e.elements||e;return"undefined"==typeof t[0][0]&&(t=c.create(t).elements),this.isSameSizeAs(t)?this.map(function(e,n,r){return e+t[n-1][r-1]}):null},subtract:function(e){var t=e.elements||e;return"undefined"==typeof t[0][0]&&(t=c.create(t).elements),this.isSameSizeAs(t)?this.map(function(e,n,r){return e-t[n-1][r-1]}):null},canMultiplyFromLeft:function(e){var t=e.elements||e;return"undefined"==typeof t[0][0]&&(t=c.create(t).elements),this.elements[0].length==t.length},multiply:function(e){if(!e.elements)return this.map(function(t){return t*e});var t=!!e.modulus;if("undefined"==typeof(p=e.elements||e)[0][0]&&(p=c.create(p).elements),!this.canMultiplyFromLeft(p))return null;var n,r,i,a,o,s,l=this.elements.length,u=l,f=p[0].length,h=this.elements[0].length,d=[];do{d[n=u-l]=[],r=f;do{i=f-r,a=0,o=h;do{s=h-o,a+=this.elements[n][s]*p[s][i]}while(--o);d[n][i]=a}while(--r)}while(--l);var p=c.create(d);return t?p.col(1):p},x:function(e){return this.multiply(e)},minor:function(e,t,n,r){var i,a,o,s=[],l=n,u=this.elements.length,f=this.elements[0].length;do{s[i=n-l]=[],a=r;do{o=r-a,s[i][o]=this.elements[(e+i-1)%u][(t+o-1)%f]}while(--a)}while(--l);return c.create(s)},transpose:function(){var e,t,n,r=this.elements.length,i=this.elements[0].length,a=[],o=i;do{a[e=i-o]=[],t=r;do{n=r-t,a[e][n]=this.elements[n][e]}while(--t)}while(--o);return c.create(a)},isSquare:function(){return this.elements.length==this.elements[0].length},max:function(){var e,t,n,r=0,i=this.elements.length,a=i,o=this.elements[0].length;do{e=a-i,t=o;do{n=o-t,Math.abs(this.elements[e][n])>Math.abs(r)&&(r=this.elements[e][n])}while(--t)}while(--i);return r},indexOf:function(e){var t,n,r,i=this.elements.length,a=i,o=this.elements[0].length;do{t=a-i,n=o;do{if(r=o-n,this.elements[t][r]==e)return{i:t+1,j:r+1}}while(--n)}while(--i);return null},diagonal:function(){if(!this.isSquare)return null;var e,t=[],n=this.elements.length,r=n;do{e=r-n,t.push(this.elements[e][e])}while(--n);return u.create(t)},toRightTriangular:function(){var e,t,n,r,i=this.dup(),a=this.elements.length,o=a,s=this.elements[0].length;do{if(t=o-a,0==i.elements[t][t])for(j=t+1;j<o;j++)if(0!=i.elements[j][t]){e=[],n=s;do{r=s-n,e.push(i.elements[t][r]+i.elements[j][r])}while(--n);i.elements[t]=e;break}if(0!=i.elements[t][t])for(j=t+1;j<o;j++){var l=i.elements[j][t]/i.elements[t][t];e=[],n=s;do{r=s-n,e.push(r<=t?0:i.elements[j][r]-i.elements[t][r]*l)}while(--n);i.elements[j]=e}}while(--a);return i},toUpperTriangular:function(){return this.toRightTriangular()},determinant:function(){if(!this.isSquare())return null;var e,t=this.toRightTriangular(),n=t.elements[0][0],r=t.elements.length-1,i=r;do{e=i-r+1,n*=t.elements[e][e]}while(--r);return n},det:function(){return this.determinant()},isSingular:function(){return this.isSquare()&&0===this.determinant()},trace:function(){if(!this.isSquare())return null;var e,t=this.elements[0][0],n=this.elements.length-1,r=n;do{e=r-n+1,t+=this.elements[e][e]}while(--n);return t},tr:function(){return this.trace()},rank:function(){var e,t,n,r=this.toRightTriangular(),i=0,a=this.elements.length,o=a,s=this.elements[0].length;do{e=o-a,t=s;do{if(n=s-t,Math.abs(r.elements[e][n])>l){i++;break}}while(--t)}while(--a);return i},rk:function(){return this.rank()},augment:function(e){var t=e.elements||e;"undefined"==typeof t[0][0]&&(t=c.create(t).elements);var n,r,i,a=this.dup(),o=a.elements[0].length,s=a.elements.length,l=s,u=t[0].length;if(s!=t.length)return null;do{n=l-s,r=u;do{i=u-r,a.elements[n][o+i]=t[n][i]}while(--r)}while(--s);return a},inverse:function(){if(!this.isSquare()||this.isSingular())return null;var e,t,n,r,i,a,o,s=this.elements.length,l=s,u=this.augment(c.I(s)).toRightTriangular(),f=u.elements[0].length,h=[];do{i=[],n=f,h[e=s-1]=[],a=u.elements[e][e];do{r=f-n,o=u.elements[e][r]/a,i.push(o),r>=l&&h[e].push(o)}while(--n);for(u.elements[e]=i,t=0;t<e;t++){i=[],n=f;do{r=f-n,i.push(u.elements[t][r]-u.elements[e][r]*u.elements[t][e])}while(--n);u.elements[t]=i}}while(--s);return c.create(h)},inv:function(){return this.inverse()},round:function(){return this.map(function(e){return Math.round(e)})},snapTo:function(e){return this.map(function(t){return Math.abs(t-e)<=l?e:t})},inspect:function(){var e,t=[],n=this.elements.length,r=n;do{e=r-n,t.push(u.create(this.elements[e]).inspect())}while(--n);return t.join("\n")},setElements:function(e){var t,n=e.elements||e;if("undefined"!=typeof n[0][0]){var r,i,a,o=n.length,s=o;this.elements=[];do{i=r=n[t=s-o].length,this.elements[t]=[];do{a=i-r,this.elements[t][a]=n[t][a]}while(--r)}while(--o);return this}var l=n.length,u=l;this.elements=[];do{t=u-l,this.elements.push([n[t]])}while(--l);return this}},c.create=function(e){return(new c).setElements(e)},c.I=function(e){var t,n,r,i=[],a=e;do{i[t=a-e]=[],n=a;do{r=a-n,i[t][r]=t==r?1:0}while(--n)}while(--e);return c.create(i)},c.Diagonal=function(e){var t,n=e.length,r=n,i=c.I(n);do{t=r-n,i.elements[t][t]=e[t]}while(--n);return i},c.Rotation=function(e,t){if(!t)return c.create([[Math.cos(e),-Math.sin(e)],[Math.sin(e),Math.cos(e)]]);var n=t.dup();if(3!=n.elements.length)return null;var r=n.modulus(),i=n.elements[0]/r,a=n.elements[1]/r,o=n.elements[2]/r,s=Math.sin(e),l=Math.cos(e),u=1-l;return c.create([[u*i*i+l,u*i*a-s*o,u*i*o+s*a],[u*i*a+s*o,u*a*a+l,u*a*o-s*i],[u*i*o-s*a,u*a*o+s*i,u*o*o+l]])},c.RotationX=function(e){var t=Math.cos(e),n=Math.sin(e);return c.create([[1,0,0],[0,t,-n],[0,n,t]])},c.RotationY=function(e){var t=Math.cos(e),n=Math.sin(e);return c.create([[t,0,n],[0,1,0],[-n,0,t]])},c.RotationZ=function(e){var t=Math.cos(e),n=Math.sin(e);return c.create([[t,-n,0],[n,t,0],[0,0,1]])},c.Random=function(e,t){return c.Zero(e,t).map(function(){return Math.random()})},c.Zero=function(e,t){var n,r,i,a=[],o=e;do{a[n=e-o]=[],r=t;do{i=t-r,a[n][i]=0}while(--r)}while(--o);return c.create(a)},f.prototype={eql:function(e){return this.isParallelTo(e)&&this.contains(e.anchor)},dup:function(){return f.create(this.anchor,this.direction)},translate:function(e){var t=e.elements||e;return f.create([this.anchor.elements[0]+t[0],this.anchor.elements[1]+t[1],this.anchor.elements[2]+(t[2]||0)],this.direction)},isParallelTo:function(e){if(e.normal)return e.isParallelTo(this);var t=this.direction.angleFrom(e.direction);return Math.abs(t)<=l||Math.abs(t-Math.PI)<=l},distanceFrom:function(e){if(e.normal)return e.distanceFrom(this);if(e.direction){if(this.isParallelTo(e))return this.distanceFrom(e.anchor);var t=this.direction.cross(e.direction).toUnitVector().elements,n=this.anchor.elements,r=e.anchor.elements;return Math.abs((n[0]-r[0])*t[0]+(n[1]-r[1])*t[1]+(n[2]-r[2])*t[2])}var i=e.elements||e,a=(n=this.anchor.elements,this.direction.elements),o=i[0]-n[0],s=i[1]-n[1],l=(i[2]||0)-n[2],u=Math.sqrt(o*o+s*s+l*l);if(0===u)return 0;var c=(o*a[0]+s*a[1]+l*a[2])/u,f=1-c*c;return Math.abs(u*Math.sqrt(f<0?0:f))},contains:function(e){var t=this.distanceFrom(e);return null!==t&&t<=l},liesIn:function(e){return e.contains(this)},intersects:function(e){return e.normal?e.intersects(this):!this.isParallelTo(e)&&this.distanceFrom(e)<=l},intersectionWith:function(e){if(e.normal)return e.intersectionWith(this);if(!this.intersects(e))return null;var t=this.anchor.elements,n=this.direction.elements,r=e.anchor.elements,i=e.direction.elements,a=n[0],o=n[1],s=n[2],l=i[0],c=i[1],f=i[2],h=t[0]-r[0],d=t[1]-r[1],p=t[2]-r[2],m=l*l+c*c+f*f,g=a*l+o*c+s*f,v=((-a*h-o*d-s*p)*m/(a*a+o*o+s*s)+g*(l*h+c*d+f*p))/(m-g*g);return u.create([t[0]+v*a,t[1]+v*o,t[2]+v*s])},pointClosestTo:function(e){if(e.direction){if(this.intersects(e))return this.intersectionWith(e);if(this.isParallelTo(e))return null;var t=this.direction.elements,n=e.direction.elements,r=t[0],i=t[1],a=t[2],o=n[0],s=n[1],l=n[2],c=a*o-r*l,f=r*s-i*o,d=i*l-a*s,p=u.create([c*l-f*s,f*o-d*l,d*s-c*o]);return(m=h.create(e.anchor,p)).intersectionWith(this)}var m=e.elements||e;if(this.contains(m))return u.create(m);var g=this.anchor.elements,v=(r=(t=this.direction.elements)[0],i=t[1],a=t[2],g[0]),_=g[1],y=g[2],b=(c=r*(m[1]-_)-i*(m[0]-v),f=i*((m[2]||0)-y)-a*(m[1]-_),d=a*(m[0]-v)-r*((m[2]||0)-y),u.create([i*c-a*d,a*f-r*c,r*d-i*f])),w=this.distanceFrom(m)/b.modulus();return u.create([m[0]+b.elements[0]*w,m[1]+b.elements[1]*w,(m[2]||0)+b.elements[2]*w])},rotate:function(e,t){"undefined"==typeof t.direction&&(t=f.create(t.to3D(),u.k));var n=c.Rotation(e,t.direction).elements,r=t.pointClosestTo(this.anchor).elements,i=this.anchor.elements,a=this.direction.elements,o=r[0],s=r[1],l=r[2],h=i[0]-o,d=i[1]-s,p=i[2]-l;return f.create([o+n[0][0]*h+n[0][1]*d+n[0][2]*p,s+n[1][0]*h+n[1][1]*d+n[1][2]*p,l+n[2][0]*h+n[2][1]*d+n[2][2]*p],[n[0][0]*a[0]+n[0][1]*a[1]+n[0][2]*a[2],n[1][0]*a[0]+n[1][1]*a[1]+n[1][2]*a[2],n[2][0]*a[0]+n[2][1]*a[1]+n[2][2]*a[2]])},reflectionIn:function(e){if(e.normal){var t=this.anchor.elements,n=this.direction.elements,r=t[0],i=t[1],a=t[2],o=n[0],s=n[1],l=n[2],u=this.anchor.reflectionIn(e).elements,c=r+o,h=i+s,d=a+l,p=e.pointClosestTo([c,h,d]).elements,m=[p[0]+(p[0]-c)-u[0],p[1]+(p[1]-h)-u[1],p[2]+(p[2]-d)-u[2]];return f.create(u,m)}if(e.direction)return this.rotate(Math.PI,e);var g=e.elements||e;return f.create(this.anchor.reflectionIn([g[0],g[1],g[2]||0]),this.direction)},setVectors:function(e,t){if(e=u.create(e),t=u.create(t),2==e.elements.length&&e.elements.push(0),2==t.elements.length&&t.elements.push(0),e.elements.length>3||t.elements.length>3)return null;var n=t.modulus();return 0===n?null:(this.anchor=e,this.direction=u.create([t.elements[0]/n,t.elements[1]/n,t.elements[2]/n]),this)}},f.create=function(e,t){return(new f).setVectors(e,t)},f.X=f.create(u.Zero(3),u.i),f.Y=f.create(u.Zero(3),u.j),f.Z=f.create(u.Zero(3),u.k),h.prototype={eql:function(e){return this.contains(e.anchor)&&this.isParallelTo(e)},dup:function(){return h.create(this.anchor,this.normal)},translate:function(e){var t=e.elements||e;return h.create([this.anchor.elements[0]+t[0],this.anchor.elements[1]+t[1],this.anchor.elements[2]+(t[2]||0)],this.normal)},isParallelTo:function(e){var t;return e.normal?(t=this.normal.angleFrom(e.normal),Math.abs(t)<=l||Math.abs(Math.PI-t)<=l):e.direction?this.normal.isPerpendicularTo(e.direction):null},isPerpendicularTo:function(e){var t=this.normal.angleFrom(e.normal);return Math.abs(Math.PI/2-t)<=l},distanceFrom:function(e){if(this.intersects(e)||this.contains(e))return 0;if(e.anchor){var t=this.anchor.elements,n=e.anchor.elements,r=this.normal.elements;return Math.abs((t[0]-n[0])*r[0]+(t[1]-n[1])*r[1]+(t[2]-n[2])*r[2])}var i=e.elements||e;t=this.anchor.elements,r=this.normal.elements;return Math.abs((t[0]-i[0])*r[0]+(t[1]-i[1])*r[1]+(t[2]-(i[2]||0))*r[2])},contains:function(e){if(e.normal)return null;if(e.direction)return this.contains(e.anchor)&&this.contains(e.anchor.add(e.direction));var t=e.elements||e,n=this.anchor.elements,r=this.normal.elements;return Math.abs(r[0]*(n[0]-t[0])+r[1]*(n[1]-t[1])+r[2]*(n[2]-(t[2]||0)))<=l},intersects:function(e){return"undefined"==typeof e.direction&&"undefined"==typeof e.normal?null:!this.isParallelTo(e)},intersectionWith:function(e){if(!this.intersects(e))return null;if(e.direction){var t=e.anchor.elements,n=e.direction.elements,r=this.anchor.elements,i=((o=this.normal.elements)[0]*(r[0]-t[0])+o[1]*(r[1]-t[1])+o[2]*(r[2]-t[2]))/(o[0]*n[0]+o[1]*n[1]+o[2]*n[2]);return u.create([t[0]+n[0]*i,t[1]+n[1]*i,t[2]+n[2]*i])}if(e.normal){for(var a=this.normal.cross(e.normal).toUnitVector(),o=this.normal.elements,s=(t=this.anchor.elements,e.normal.elements),l=e.anchor.elements,h=c.Zero(2,2),d=0;h.isSingular();)d++,h=c.create([[o[d%3],o[(d+1)%3]],[s[d%3],s[(d+1)%3]]]);for(var p=h.inverse().elements,m=o[0]*t[0]+o[1]*t[1]+o[2]*t[2],g=s[0]*l[0]+s[1]*l[1]+s[2]*l[2],v=[p[0][0]*m+p[0][1]*g,p[1][0]*m+p[1][1]*g],_=[],y=1;y<=3;y++)_.push(d==y?0:v[(y+(5-d)%3)%3]);return f.create(_,a)}},pointClosestTo:function(e){var t=e.elements||e,n=this.anchor.elements,r=this.normal.elements,i=(n[0]-t[0])*r[0]+(n[1]-t[1])*r[1]+(n[2]-(t[2]||0))*r[2];return u.create([t[0]+r[0]*i,t[1]+r[1]*i,(t[2]||0)+r[2]*i])},rotate:function(e,t){var n=c.Rotation(e,t.direction).elements,r=t.pointClosestTo(this.anchor).elements,i=this.anchor.elements,a=this.normal.elements,o=r[0],s=r[1],l=r[2],u=i[0]-o,f=i[1]-s,d=i[2]-l;return h.create([o+n[0][0]*u+n[0][1]*f+n[0][2]*d,s+n[1][0]*u+n[1][1]*f+n[1][2]*d,l+n[2][0]*u+n[2][1]*f+n[2][2]*d],[n[0][0]*a[0]+n[0][1]*a[1]+n[0][2]*a[2],n[1][0]*a[0]+n[1][1]*a[1]+n[1][2]*a[2],n[2][0]*a[0]+n[2][1]*a[1]+n[2][2]*a[2]])},reflectionIn:function(e){if(e.normal){var t=this.anchor.elements,n=this.normal.elements,r=t[0],i=t[1],a=t[2],o=n[0],s=n[1],l=n[2],u=this.anchor.reflectionIn(e).elements,c=r+o,f=i+s,d=a+l,p=e.pointClosestTo([c,f,d]).elements,m=[p[0]+(p[0]-c)-u[0],p[1]+(p[1]-f)-u[1],p[2]+(p[2]-d)-u[2]];return h.create(u,m)}if(e.direction)return this.rotate(Math.PI,e);var g=e.elements||e;return h.create(this.anchor.reflectionIn([g[0],g[1],g[2]||0]),this.normal)},setVectors:function(e,t,n){if(null===(e=(e=u.create(e)).to3D()))return null;if(null===(t=(t=u.create(t)).to3D()))return null;if("undefined"==typeof n)n=null;else if(null===(n=(n=u.create(n)).to3D()))return null;var r,i,a=e.elements[0],o=e.elements[1],s=e.elements[2],l=t.elements[0],c=t.elements[1],f=t.elements[2];if(null!==n){var h=n.elements[0],d=n.elements[1],p=n.elements[2];if(0===(i=(r=u.create([(c-o)*(p-s)-(f-s)*(d-o),(f-s)*(h-a)-(l-a)*(p-s),(l-a)*(d-o)-(c-o)*(h-a)])).modulus()))return null;r=u.create([r.elements[0]/i,r.elements[1]/i,r.elements[2]/i])}else{if(0===(i=Math.sqrt(l*l+c*c+f*f)))return null;r=u.create([t.elements[0]/i,t.elements[1]/i,t.elements[2]/i])}return this.anchor=e,this.normal=r,this}},c.Translation=function(e){var t;if(2===e.elements.length)return(t=c.I(3)).elements[2][0]=e.elements[0],t.elements[2][1]=e.elements[1],t;if(3===e.elements.length)return(t=c.I(4)).elements[0][3]=e.elements[0],t.elements[1][3]=e.elements[1],t.elements[2][3]=e.elements[2],t;throw"Invalid length for Translation"},c.prototype.flatten=function(){var e=[];if(0===this.elements.length)return[];for(var t=0;t<this.elements[0].length;t++)for(var n=0;n<this.elements.length;n++)e.push(this.elements[n][t]);return e},c.prototype.ensure4x4=function(){var e;if(4===this.elements.length&&4===this.elements[0].length)return this;if(this.elements.length>4||this.elements[0].length>4)return null;for(e=0;e<this.elements.length;e++)for(var t=this.elements[e].length;t<4;t++)e===t?this.elements[e].push(1):this.elements[e].push(0);for(e=this.elements.length;e<4;e++)0===e?this.elements.push([1,0,0,0]):1===e?this.elements.push([0,1,0,0]):2===e?this.elements.push([0,0,1,0]):3===e&&this.elements.push([0,0,0,1]);return this},c.prototype.make3x3=function(){return 4!==this.elements.length||4!==this.elements[0].length?null:c.create([[this.elements[0][0],this.elements[0][1],this.elements[0][2]],[this.elements[1][0],this.elements[1][1],this.elements[1][2]],[this.elements[2][0],this.elements[2][1],this.elements[2][2]]])},h.create=function(e,t,n){return(new h).setVectors(e,t,n)},h.XY=h.create(u.Zero(3),u.k),h.YZ=h.create(u.Zero(3),u.i),h.ZX=h.create(u.Zero(3),u.j),h.YX=h.XY,h.ZY=h.YZ,h.XZ=h.ZX;var d=u.create,p=c.create,m=(f.create,h.create,function(){function e(e,t,n){s.e.call(this,e,t,n)}return e.prototype=_(s.e,{fill:function(e,t){var n=this.gl;n.bindTexture(n.TEXTURE_2D,this.texture),t?n.texSubImage2D(n.TEXTURE_2D,0,0,0,this.size.w,this.size.h,this.format,n.UNSIGNED_BYTE,e):n.texImage2D(n.TEXTURE_2D,0,this.format,this.format,n.UNSIGNED_BYTE,e)}}),e}()),g=function(){var e=s.c.createFromSource("x-shader/x-vertex",y(["attribute vec3 aVertexPosition;","attribute vec2 aTextureCoord;","uniform mat4 uMVMatrix;","uniform mat4 uPMatrix;","varying highp vec2 vTextureCoord;","void main(void) {","  gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);","  vTextureCoord = aTextureCoord;","}"])),t=s.c.createFromSource("x-shader/x-fragment",y(["precision highp float;","varying highp vec2 vTextureCoord;","uniform sampler2D texture;","void main(void) {","  gl_FragColor = texture2D(texture, vTextureCoord);","}"]));function n(e,t,n){this.canvas=e,this.size=t,this.canvas.width=t.w,this.canvas.height=t.h,this.onInitWebGL(),this.onInitShaders(),function(){var e=[1,1,0,-1,1,0,1,-1,0,-1,-1,0],t=this.gl;this.quadVPBuffer=t.createBuffer(),t.bindBuffer(t.ARRAY_BUFFER,this.quadVPBuffer),t.bufferData(t.ARRAY_BUFFER,new Float32Array(e),t.STATIC_DRAW),this.quadVPBuffer.itemSize=3,this.quadVPBuffer.numItems=4;this.quadVTCBuffer=t.createBuffer(),t.bindBuffer(t.ARRAY_BUFFER,this.quadVTCBuffer),e=[1,0,0,0,1,1,0,1],t.bufferData(t.ARRAY_BUFFER,new Float32Array(e),t.STATIC_DRAW)}.call(this),n&&function(){var e=this.gl;this.framebuffer=e.createFramebuffer(),e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer),this.framebufferTexture=new s.e(this.gl,this.size,e.RGBA);var t=e.createRenderbuffer();e.bindRenderbuffer(e.RENDERBUFFER,t),e.renderbufferStorage(e.RENDERBUFFER,e.DEPTH_COMPONENT16,this.size.w,this.size.h),e.framebufferTexture2D(e.FRAMEBUFFER,e.COLOR_ATTACHMENT0,e.TEXTURE_2D,this.framebufferTexture.texture,0),e.framebufferRenderbuffer(e.FRAMEBUFFER,e.DEPTH_ATTACHMENT,e.RENDERBUFFER,t)}.call(this),this.onInitTextures(),function(){var e=this.gl;this.perspectiveMatrix=function(e,t,n,r){var i=n*Math.tan(e*Math.PI/360),a=-i;return function(e,t,n,r,i,a){return p([[2*i/(t-e),0,(t+e)/(t-e),0],[0,2*i/(r-n),(r+n)/(r-n),0],[0,0,-(a+i)/(a-i),-2*a*i/(a-i)],[0,0,-1,0]])}(a*t,i*t,a,i,n,r)}(45,1,.1,100),r.call(this),i.call(this,[0,0,-2.415]),e.bindBuffer(e.ARRAY_BUFFER,this.quadVPBuffer),e.vertexAttribPointer(this.vertexPositionAttribute,3,e.FLOAT,!1,0,0),e.bindBuffer(e.ARRAY_BUFFER,this.quadVTCBuffer),e.vertexAttribPointer(this.textureCoordAttribute,2,e.FLOAT,!1,0,0),this.onInitSceneTextures(),a.call(this),this.framebuffer&&e.bindFramebuffer(e.FRAMEBUFFER,this.framebuffer)}.call(this)}function r(){this.mvMatrix=c.I(4)}function i(e){(function(e){this.mvMatrix=this.mvMatrix.x(e)}).call(this,c.Translation(d([e[0],e[1],e[2]])).ensure4x4())}function a(){this.program.setMatrixUniform("uPMatrix",new Float32Array(this.perspectiveMatrix.flatten())),this.program.setMatrixUniform("uMVMatrix",new Float32Array(this.mvMatrix.flatten()))}return n.prototype={toString:function(){return"WebGLCanvas Size: "+this.size},checkLastError:function(e){var t=this.gl.getError();if(t!==this.gl.NO_ERROR){var n=this.glNames[t];n="undefined"!==typeof n?n+"("+t+")":"Unknown WebGL ENUM (0x"+value.toString(16)+")",e?s.h.log("WebGL Error: %s, %s",e,n):s.h.log("WebGL Error: %s",n),s.h.trace()}},onInitWebGL:function(){try{this.gl=this.canvas.getContext("webgl")}catch(e){s.h.log("inInitWebGL error = "+e)}if(this.gl||s.h.error("Unable to initialize WebGL. Your browser may not support it."),!this.glNames)for(var e in this.glNames={},this.gl)"number"===typeof this.gl[e]&&(this.glNames[this.gl[e]]=e)},onInitShaders:function(){this.program=new s.b(this.gl),this.program.attach(new s.d(this.gl,e)),this.program.attach(new s.d(this.gl,t)),this.program.link(),this.program.use(),this.vertexPositionAttribute=this.program.getAttributeLocation("aVertexPosition"),this.gl.enableVertexAttribArray(this.vertexPositionAttribute),this.textureCoordAttribute=this.program.getAttributeLocation("aTextureCoord"),this.gl.enableVertexAttribArray(this.textureCoordAttribute)},onInitTextures:function(){var e=this.gl;e.viewport(0,0,this.canvas.width,this.canvas.height),this.texture=new s.e(e,this.size,e.RGBA)},onInitSceneTextures:function(){this.texture.bind(0,this.program,"texture")},drawScene:function(){this.gl.drawArrays(this.gl.TRIANGLE_STRIP,0,4)},updateVertexArray:function(e){this.zoomScene(e)},readPixels:function(e){var t=this.gl;t.readPixels(0,0,this.size.w,this.size.h,t.RGBA,t.UNSIGNED_BYTE,e)},zoomScene:function(e){r.call(this),i.call(this,[e[0],e[1],e[2]]),a.call(this),this.drawScene()},setViewport:function(e,t){var n,r;s.h.log("toWidth="+e+",toHeight="+t),this.gl.drawingBufferWidth<e||this.gl.drawingBufferHeight<t?(n=this.gl.drawingBufferWidth,r=this.gl.drawingBufferHeight,this.canvas.width=n,this.canvas.height=r):(n=e,r=t),this.gl.viewport(0,0,n,r)},clearCanvas:function(){this.gl.clearColor(0,0,0,1),this.gl.clear(this.gl.DEPTH_BUFFER_BIT|this.gl.COLOR_BUFFER_BIT)}},n}(),v=(function(){function e(e,t){g.call(this,e,t)}e.prototype=_(g,{drawCanvas:function(e){this.texture.fill(e),this.drawScene()},onInitTextures:function(){var e=this.gl;this.setViewport(this.canvas.width,this.canvas.height),this.texture=new m(e,this.size,e.RGBA)},initCanvas:function(){this.gl.clear(this.gl.DEPTH_BUFFER_BIT|this.gl.COLOR_BUFFER_BIT)}})}(),function(){var e=s.c.createFromSource("x-shader/x-vertex",y(["attribute vec3 aVertexPosition;","attribute vec2 aTextureCoord;","uniform mat4 uMVMatrix;","uniform mat4 uPMatrix;","varying highp vec2 vTextureCoord;","void main(void) {","  gl_Position = uPMatrix * uMVMatrix * vec4(aVertexPosition, 1.0);","  vTextureCoord = aTextureCoord;","}"])),t=s.c.createFromSource("x-shader/x-fragment",y(["precision highp float;","varying highp vec2 vTextureCoord;","uniform sampler2D YTexture;","uniform sampler2D UTexture;","uniform sampler2D VTexture;","const mat4 YUV2RGB = mat4","("," 1.16438, 0.00000, 1.59603, -.87079,"," 1.16438, -.39176, -.81297, .52959,"," 1.16438, 2.01723, 0, -1.08139,"," 0, 0, 0, 1",");","void main(void) {"," gl_FragColor = vec4( texture2D(YTexture,  vTextureCoord).x, texture2D(UTexture, vTextureCoord).x, texture2D(VTexture, vTextureCoord).x, 1) * YUV2RGB;","}"]));function n(e,t){this.isPlayStart=!0,g.call(this,e,t)}return n.prototype=_(g,{onInitShaders:function(){this.program=new s.b(this.gl),this.program.attach(new s.d(this.gl,e)),this.program.attach(new s.d(this.gl,t)),this.program.link(),this.program.use(),this.vertexPositionAttribute=this.program.getAttributeLocation("aVertexPosition"),this.gl.enableVertexAttribArray(this.vertexPositionAttribute),this.textureCoordAttribute=this.program.getAttributeLocation("aTextureCoord"),this.gl.enableVertexAttribArray(this.textureCoordAttribute)},onInitTextures:function(){this.setViewport(this.size.w,this.size.h),this.YTexture=new s.e(this.gl,this.size),this.UTexture=new s.e(this.gl,this.size.getHalfSize()),this.VTexture=new s.e(this.gl,this.size.getHalfSize())},onInitSceneTextures:function(){this.YTexture.bind(0,this.program,"YTexture"),this.UTexture.bind(1,this.program,"UTexture"),this.VTexture.bind(2,this.program,"VTexture")},fillYUVTextures:function(e,t,n){this.YTexture.fill(e),this.UTexture.fill(t),this.VTexture.fill(n),this.drawScene()},drawCanvas:function(e,t,n){this.isPlayStart&&this.playStartCallback&&(this.playStartCallback(),this.isPlayStart=!1),this.YTexture.fill(e),this.UTexture.fill(t),this.VTexture.fill(n),this.drawScene()},updateVertexArray:function(e){this.zoomScene(e)},toString:function(){return"YUVCanvas Size: "+this.size},initCanvas:function(){this.gl.clear(this.gl.DEPTH_BUFFER_BIT|this.gl.COLOR_BUFFER_BIT)},setPlayStartCallback:function(e){this.playStartCallback=e}}),n}());function _(e,t){for(var n=Object.create(e.prototype),r=Object.keys(t),i=0;i<r.length;i++)n[r[i]]=t[r[i]];return n}function y(e){return e.join("\n")}var b=function(e,t){var n=t,r=e,i=null,a=null,o=0,s=0,l=0,u=0,c=!1,f=16.7,h=0,d=null,p=null,m=null,g=function(e){(function(e){this.buffer=e,this.previous=null,this.next=null}).call(this,e)},_=null,y=null,b=null;function w(){var e=n||15;function t(){this.first=null,this.size=0}return t.prototype={enqueue:function(t){this.size>=e&&this.clear();var n=new g(t);if(null===this.first)this.first=n;else{for(var r=this.first;null!==r.next;)r=r.next;r.next=n}return this.size+=1,n},dequeue:function(){var e=null;return null!==this.first&&(e=this.first,this.first=this.first.next,this.size-=1),e},clear:function(){for(var e=null;null!==this.first;)e=this.first,this.first=this.first.next,this.size-=1,e.buffer=null,e=null;this.size=0,this.first=null}},new t}function S(){_=new w,y=new w,b=new w,a=f,c=!1}var T=function e(t){if(!0===c){if(0===o||t-o<200)return 0===o&&(o=t),void(null!==_&&window.requestAnimationFrame(e));(u+=t-s)>l&&(d=_.dequeue(),p=y.dequeue(),m=b.dequeue(),null!==d&&null!==d.buffer&&d.buffer.length>0&&"undefined"!==typeof i&&(i.drawCanvas(d.buffer,p.buffer,m.buffer),delete d.buffer,delete p.buffer,delete m.buffer,d.buffer=null,d.previous=null,d.next=null,d=null,p.buffer=null,p.previous=null,p.next=null,p=null,m.buffer=null,m.previous=null,m.next=null,m=null,1)&&(l+=a)),u>1e3&&(l=0,u=0),s=t,window.requestAnimationFrame(e)}};function C(e,t){function n(e,t){n.prototype.w=e,n.prototype.h=t}return n.prototype={toString:function(){return"("+n.prototype.w+", "+n.prototype.h+")"},getHalfSize:function(){return new C(n.prototype.w>>>1,n.prototype.h>>>1)},length:function(){return n.prototype.w*n.prototype.h}},new n(e,t)}return S.prototype={draw:function(e,t,n){null!==_&&!0===c&&(document.hidden&&_.size>=15?(_.clear(),y.clear(),b.clear()):(_.enqueue(e),y.enqueue(t),b.enqueue(n)))},resize:function(e,t){this.stopRendering(),null!==_&&_.clear(),null!==y&&y.clear(),null!==b&&b.clear(),i&&(i=null);var n=new C(e,t);i=new v(r,n),n=null,this.startRendering()},initStartTime:function(){0===o&&this.startRendering()},startRendering:function(){0===o&&(c=!0,window.requestAnimationFrame(T))},pause:function(){c=!1},play:function(){c=!0},stopRendering:function(){c=!1,o=0},setFPS:function(e){"undefined"===typeof e?a=f:0===e?a=f:a=1e3/e,h=a},setFrameInterval:function(e){a=e*h},terminate:function(){c=!1,o=0,null!==_&&(_.clear(),_=null),null!==y&&(y.clear(),y=null),null!==b&&(b.clear(),b=null),i&&i.clearCanvas(),i=null},getVideoBufferQueueSize:function(){return _.size}},new S};var w=function(e){var t=null,n="",r=e,i=null,a=null,o=null,l=null,u=null,c=null,f=1,h={timestamp:0,timestamp_usec:0,timezone:0},d={timestamp:0,timestamp_usec:0,timezone:0},p=null,m=!1,g=null,v=null,_=null,y=!1,b=!0,w=0,S=!1,T=[],C=.5,A=null,P=null,R=null,M=0,E=0,x=!1,U=null,I="png",D=1,O=Object(s.a)(),k=null,L=0,F=0,B=0,N=8,V=null,z=!1,j=!1,q=[],Y={},G=25,H=.5;function W(){}function X(){K()}function Z(){var e=0;if(null!==g)for(e=0;e<g.length;e++)R.removeEventListener(g[e].type,g[e].function);if(null!==_)for(e=0;e<_.length;e++)P.removeEventListener(_[e].type,_[e].function);if(null!==v)for(e=0;e<v.length;e++)A.removeEventListener(v[e].type,v[e].function)}function K(){if(null===P||"ended"===P.readyState)return function(e){(_=[]).push({type:"sourceopen",function:X}),_.push({type:"error",function:re});for(var t=0;t<_.length;t++)e.addEventListener(_[t].type,_[t].function)}(P=new MediaSource),A.src=window.URL.createObjectURL(P),void s.h.log("videoMediaSource::appendInitSegment new MediaSource()");if(s.h.log("videoMediaSource::appendInitSegment start"),0===P.sourceBuffers.length){P.duration=0;var e=null;if(1==r?e='video/mp4;codecs="avc1.'+n+'"':2==r&&(e='video/mp4; codecs="hvc1.1.6.L30.B0"'),!MediaSource.isTypeSupported(e))return s.h.log("not support"+e),void(V&&V({errorCode:101}));!function(e){(g=[]).push({type:"error",function:ie}),g.push({type:"updateend",function:te}),g.push({type:"update",function:ne});for(var t=0;t<g.length;t++)e.addEventListener(g[t].type,g[t].function)}(R=P.addSourceBuffer(e))}var i=t();null!==i?(R.appendBuffer(i),s.h.log("videoMediaSource::appendInitSegment end, codecInfo = "+n)):P.endOfStream("network")}function Q(){A.paused&&(a(),y||z||A.play())}function J(){A.paused||b||(s.h.log("pause"),A.pause())}function $(){q.length&&function(e){if(null!==R&&"closed"!==P.readyState&&"ended"!==P.readyState)try{if(T.length>0)return s.h.count("1.segmentWaitDecode.length: "+T.length),T.push(e),void s.h.count("2.segmentWaitDecode.length: "+T.length);R.updating?(s.h.log("updating.........."),T.push(e)):(R.appendBuffer(e),z&&(Y.buffer=e))}catch(e){s.h.log("videoMediaSource::appendNextMediaSegment error >> initVideo"),T.length=0,V&&V({errorCode:101})}}(q.shift())}function ee(){if(null!==P)try{if(R&&R.buffered.length>0&&(function(){var e=1*R.buffered.start(R.buffered.length-1),t=1*R.buffered.end(R.buffered.length-1);t-e>60&&R.remove(e,t-10)}(),j&&!z||A.duration>H&&(A.currentTime=(A.duration-H).toFixed(3),H+=G<10?.5:.1),A&&A.duration-A.currentTime>N&&V&&V({errorCode:101}),S&&!m)){var e=1*R.buffered.start(R.buffered.length-1),t=1*R.buffered.end(R.buffered.length-1);if((0===A.currentTime?t-e:t-A.currentTime)>=C+.1){if(s.h.log("\u8df3\u79d2"),R.updating)return;var n=t-C;A.currentTime=n.toFixed(3)}}}catch(e){s.h.log("sourceBuffer has been removed")}}function te(){}function ne(){T.length>0&&(s.h.count("1. onSourceUpdate .segmentWaitDecode.length: "+T.length),R.updating||(s.h.count("2. onSourceUpdate .appendBuffer: "+T.length+"  "+T[0].length),R.appendBuffer(T[0]),T.shift()))}function re(){u()}function ie(){u()}function ae(){J(),V&&V({errorCode:101}),u()}function oe(){y=!0,b=!1,j=!0,s.h.log("playing "),x||(x=!0,l("PlayStart"))}function se(){y=!1,b=!0,s.h.log("\u6682\u505c\u64ad\u653e----------------------------------------------")}function le(){var e=parseInt(P.duration,10),t=parseInt(A.currentTime,10),n={timestamp:h.timestamp-f*(e-t+(1!==f?1:0)),timestamp_usec:0,timezone:h.timezone};0===t||isNaN(e)||!m&&Math.abs(e-t)>4&&1===f||A.paused||(null===p?(p=n,o(0,"currentTime")):(p.timestamp<=n.timestamp&&f>=1||p.timestamp>n.timestamp&&f<1)&&(p=n,++w>4&&o(n.timestamp,"currentTime")))}function ue(){Q(),ee()}function ce(){a()}function fe(){Q()}function he(){if(s.h.log("\u9700\u8981\u7f13\u51b2\u4e0b\u4e00\u5e27"),S=!1,0==E)M=Date.now(),E++;else{E++;var e=Date.now()-M;s.h.log("diffTime: "+e+"  Count: "+E),E>=5&&e<6e4&&C<=1.8&&(C+=.1,E=0,M=0,s.h.log("delay + 0.1 = "+C))}}function de(){s.h.log("Can play !")}function pe(){s.h.log("Can play without waiting"),S=!0}function me(){s.h.log("loadedmetadata")}function ge(e,t){for(var n=atob(e.substring("data:image/png;base64,".length)),r=new Uint8Array(n.length),i=0,a=n.length;i<a;++i)r[i]=n.charCodeAt(i);var o=new Blob([r.buffer],{type:"image/png"});ve(o,t+".png")}W.prototype={init:function(e){c=Object(s.a)(),s.h.log("videoMediaSource::init browserType = "+c),(A=e).autoplay="safari"!==c,A.controls=!1,A.preload="auto",function(e){(v=[]).push({type:"durationchange",function:ue}),v.push({type:"playing",function:oe}),v.push({type:"error",function:ae}),v.push({type:"pause",function:se}),v.push({type:"timeupdate",function:le}),v.push({type:"resize",function:ce}),v.push({type:"seeked",function:fe}),v.push({type:"waiting",function:he}),v.push({type:"canplaythrough",function:pe}),v.push({type:"canplay",function:de}),v.push({type:"loadedmetadata",function:me});for(var t=0;t<v.length;t++)e.addEventListener(v[t].type,v[t].function)}(A),K()},setInitSegmentFunc:function(e){t=e},getVideoElement:function(){return A},setCodecInfo:function(e){n=e},setMediaSegment:function(e){q.push(e),z||$()},capture:function(e,t){U&&clearInterval(U);var n=document.createElement("canvas");n.width=A.videoWidth,n.height=A.videoHeight,S||"edge"===O?(n.getContext("2d").drawImage(A,0,0,n.width,n.height),ge(n.toDataURL(),e)):U=setInterval(function(){S&&(n.getContext("2d").drawImage(A,0,0,n.width,n.height),ge(n.toDataURL(),e),clearInterval(U))},200)},getCapture:function(e,t,n){U&&clearInterval(U),D=n||1,I="png","jpg"!==t&&"jpeg"!==t||(I="jpeg");var r=document.createElement("canvas"),i=null;return r.width=A.videoWidth,r.height=A.videoHeight,S||"edge"===O?(r.getContext("2d").drawImage(A,0,0,r.width,r.height),i=r.toDataURL("image/"+I,D)):S&&(r.getContext("2d").drawImage(A,0,0,r.width,r.height),i=r.toDataURL("image/"+I,D)),i},setInitSegment:function(){K()},setTimeStamp:function(e,t){i=e},setVideoSizeCallback:function(e){a=e},setAudioStartCallback:function(e){o=e},setReStartMSECallback:function(e){u=e},getPlaybackTimeStamp:function(){return i},setSpeedPlay:function(e){f=e},setvideoTimeStamp:function(e){var t=Math.abs(h.timestamp-e.timestamp)>3;d.timestamp,!0===t&&(w=0,o((d=e).timestamp,"init"),0!==h.timestamp&&m&&(A.currentTime=P.duration-.1),p=null),h=e},pause:function(){z=!0,J()},play:function(){z=!1},setPlaybackFlag:function(e){m=e},setTimeStampInit:function(){p=null,d={timestamp:0,timestamp_usec:0,timezone:0}},close:function(){Z(),J()},setBeginDrawCallback:function(e){l=e},setErrorCallback:function(e){V=e},terminate:function(){null!==A&&(Z(),"open"===P.readyState&&(R&&P.removeSourceBuffer(R),P.endOfStream()),R=null,R=null,P=null,A=null,U&&(clearInterval(U),U=null),k&&(clearInterval(k),k=null),B=0,F=0,L=0)},getDuration:function(){return A.duration-A.currentTime},setFPS:function(e){e&&(G=e)},setRtspOver:function(){A.duration.toFixed(4)-0===A.currentTime.toFixed(4)-0||(L=parseInt(A.currentTime),F=parseInt(A.duration),k=setInterval(function(){L===parseInt(A.currentTime)&&F===parseInt(A.duration)?B++>10&&(k&&clearInterval(k),k=null):parseInt(A.currentTime)>=parseInt(A.duration)?(k&&clearInterval(k),k=null):(L=parseInt(A.currentTime),F=parseInt(A.duration),B=0)},150))},getVideoBufferQueueSize:function(){return q.length},playNextFrame:function(){$()},getCurFrameInfo:function(){return Y.src=function(){var e=document.createElement("canvas");return e.width=A.videoWidth,e.height=A.videoHeight,e.getContext("2d").drawImage(A,0,0,e.width,e.height),e.toDataURL()}(),Y}};var ve=function(e){var t=e.document,n=function(){return e.URL||e.webkitURL||e},r=t.createElementNS("http://www.w3.org/1999/xhtml","a"),i="download"in r,a=/constructor/i.test(e.HTMLElement),o=/CriOS\/[\d]+/.test(navigator.userAgent),s=function(t){(e.setImmediate||e.setTimeout)(function(){throw t},0)},l=function(e){setTimeout(function(){"string"===typeof e?n().revokeObjectURL(e):e.remove()},4e4)},u=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},c=function(t,c,f){f||(t=u(t));var h,d=this,p="application/octet-stream"===t.type,m=function(){!function(e,t,n){for(var r=(t=[].concat(t)).length;r--;){var i=e["on"+t[r]];if("function"===typeof i)try{i.call(e,n||e)}catch(e){s(e)}}}(d,"writestart progress write writeend".split(" "))};if(d.readyState=d.INIT,i)return h=n().createObjectURL(t),void setTimeout(function(){r.href=h,r.download=c,function(e){var t=new MouseEvent("click");e.dispatchEvent(t)}(r),m(),l(h),d.readyState=d.DONE});!function(){if((o||p&&a)&&e.FileReader){var r=new FileReader;return r.onloadend=function(){var t=o?r.result:r.result.replace(/^data:[^;]*;/,"data:attachment/file;");e.open(t,"_blank")||(e.location.href=t),t=void 0,d.readyState=d.DONE,m()},r.readAsDataURL(t),void(d.readyState=d.INIT)}h||(h=n().createObjectURL(t)),p?e.location.href=h:e.open(h,"_blank")||(e.location.href=h),d.readyState=d.DONE,m(),l(h)}()},f=c.prototype;return"undefined"!==typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=u(e)),navigator.msSaveOrOpenBlob(e,t)}:(f.readyState=f.INIT=0,f.WRITING=1,f.DONE=2,f.error=f.onwritestart=f.onprogress=f.onwrite=f.onabort=f.onerror=f.onwriteend=null,function(e,t,n){return new c(e,t||e.name||"download",n)})}(window);return new W},S=function(){var e=null,t=null,n=null,r=null,i=!1,a=null,o={audio:!0,video:!1},l=null;function u(){}return u.prototype={init:function(){if(void 0===e||null===e)try{window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(e=new AudioContext).onstatechange=function(){s.h.info("Audio Context State changed :: "+e.state)}}catch(e){return void s.h.error("Web Audio API is not supported in this web browser! : "+e)}},initAudioOut:function(){if(null!==t&&null!==n||(t=e.createGain(),(n=e.createScriptProcessor(4096,1,1)).onaudioprocess=function(e){if(null!==a){var t=e.inputBuffer.getChannelData(0);null!==l&&!0===i&&l(t)}},t.connect(n),n.connect(e.destination),r=e.sampleRate,t.gain.value=10),void 0===navigator.mediaDevices&&(navigator.mediaDevices={}),void 0===navigator.mediaDevices.getUserMedia&&(navigator.mediaDevices.getUserMedia=function(e,t,n){var r=navigator.getUserMedia||navigator.webkitGetUserMedia||navigator.mozGetUserMedia||navigator.msGetUserMedia;return r?new Promise(function(t,n){r.call(navigator,e,t,n)}):(n(),Promise.reject(new Error("getUserMedia is not implemented in this browser")))}),navigator.mediaDevices.getUserMedia)return navigator.mediaDevices.getUserMedia(o).then(function(n){a=n,e.createMediaStreamSource(n).connect(t)}).catch(function(e){s.h.error(e)}),i=!0,r;s.h.error("Cannot open local media stream! :: navigator.mediaDevices.getUserMedia is not defined!")},controlVolumnOut:function(e){var n=e/20*2;t.gain.value=n<=0?0:n>=10?10:n},stopAudioOut:function(){if(null!==a&&i)try{for(var e=a.getAudioTracks(),t=0,n=e.length;t<n;t++)e[t].stop();i=!1,a=null}catch(e){s.h.log(e)}},terminate:function(){this.stopAudioOut(),e.close(),t=null,n=null},setSendAudioTalkBufferCallback:function(e){l=e}},new u},T=n(2),C=n.n(T);var A=function(e,t){var n=null,r=null,i=null;function a(){}function o(e){var t={type:"getRtpData",data:e};n.postMessage(t)}function s(e){var t=e.data;switch(t.type){case"rtpData":i(t.data)}}return a.prototype={setSendAudioTalkCallback:function(e){i=e},talkInit:function(e,t){var i={type:"sdpInfo",data:{sdpInfo:e,aacCodecInfo:t,decodeMode:"canvas",mp4Codec:arguments.length>2&&void 0!==arguments[2]?arguments[2]:null}};try{window.AudioContext=window.AudioContext||window.webkitAudioContext||window.mozAudioContext||window.oAudioContext||window.msAudioContext,(n=new C.a).onmessage=s,null===r&&((r=new S).init(),r.setSendAudioTalkBufferCallback(o)),n.postMessage(i),i={type:"sampleRate",data:r.initAudioOut()},n.postMessage(i)}catch(e){return void debug.error("Web Audio API is not supported in this web browser! : "+e)}},stop:function(){r&&(r.terminate(),r=null),n&&(n.terminate(),n=null)}},new a},P=function(){var e=0,t=null,n=null,a=null,l=null,u=null,c=null,f=null,h=null,d=null,p=1,m="",g=!1,_=null,y=0,S={id:1,samples:null,baseMediaDecodeTime:0},T=0,C=null,P=4,R=0,M=0,E=0,x=1,U=null,I=0,D=null,O={timestamp:0,timestamp_usec:0},k=null,L=null,F=null,B=null,N=null,V=null,z=null,j=0,q=null,Y=null,G="",H=!1,W=null,X=!1,Z=!1,K=null,Q=null,J=!1;function $(){}var ee=null,te=null,ne=null,re=null,ie=null,ae=null,oe=null,se=0,le=0,ue=0,ce=0,fe=null,he=null,de=null,pe=null,me=0,ge=0,ve=null,_e=null,ye=null,be=null,we=0,Se=0,Te=null,Ce=null,Ae=0,Pe=!1,Re=!1;function Me(){(l=w(Ae)).setCodecInfo(m),l.setInitSegmentFunc(xe),l.init(n),l.setVideoSizeCallback(Ue),l.setBeginDrawCallback(k),l.setErrorCallback(B),l.setSpeedPlay(p),l.setFPS(y),l.setAudioStartCallback(Ee),l.setReStartMSECallback(Ie)}function Ee(e,t){}function xe(){return _}function Ue(){null!==F&&F(!1)}function Ie(){J=!0}function De(e,t){function n(e,t){n.prototype.w=e,n.prototype.h=t}return n.prototype={toString:function(){return"("+n.prototype.w+", "+n.prototype.h+")"},getHalfSize:function(){return new De(n.prototype.w>>>1,n.prototype.h>>>1)},length:function(){return n.prototype.w*n.prototype.h}},new n(e,t)}$.prototype={Init:function(r,i,a,o){var l=Object(s.f)(),u=l.bSupportMultiThread;l.browserType,l.errorCode,Re=u;var c=Object(s.g)(),f=c.bSupportH265MSE;c.browserTypeTmp,c.browserVersion,Pe=f,Module._PLAY_SetSupportH265MSE(f);var h=Module._malloc(1);return fe=new Uint8Array(Module.HEAPU8.buffer,h,1),Module._PLAY_GetFreePort(fe.byteOffset),e=fe[0],fe=null,Module._free(h),Module._PLAY_SetStreamOpenMode(e,o?1:0),Module._PLAY_OpenStream(e,0,0,10485760),Module._PLAY_Play(e,0),q=Module._malloc(5242880),Y=new Uint8Array(Module.HEAPU8.buffer,q,5242880),Te=new b(r,!0===o?500:15),t=r,n=i,e},InputData:function(t,n){if(Y)return Y.set(n),Module._PLAY_InputData(e,Y.byteOffset,n.length)},setSpeed:function(t){Module._PLAY_SetPlaySpeed(e,t)},Stop:function(){Module._PLAY_Stop(e),Module._PLAY_CloseStream(e),a&&(a.stop(),a=null),l&&(l.close(),l.terminate(),l=null),_=null,u=null,c=null,g=!1,Y=null,Module._free(q),he=null,de=null,pe=null,me=0,ge=0,ee=null,te=null,ne=null,re=null,ie=null,ae=null,_e=null,ye=null,be=0,ve&&(ve.stop(),ve=null,K=null,Q=null),Re?Ce=null:(Te.stopRendering(),Te.terminate(),Te=null)},setFrameData:function(e,t,n,a,s,v){de||(he=new ArrayBuffer(40),de=new Uint8Array(he),pe=new DataView(he)),de.set(Module.HEAPU8.subarray(v,v+40)),pe.getUint32(0,!0);var b=pe.getUint8(4),w=pe.getUint8(5),A=pe.getUint8(6),k=(pe.getUint8(7),pe.getUint16(30,!0)),L=pe.getUint8(32),F=pe.getUint8(33),B=pe.getUint8(34),q=pe.getUint8(35),Y=pe.getUint8(36),$=new Date(k,L-1,F,B,q,Y).getTime();if(O.timestamp=$,O.timestamp_usec=0,j<$&&(j=$,z&&z(j)),1==b){if(2==A||4==A||8==A?Ae=1:12==A&&(Ae=2),ce=pe.getUint16(12,!0),se=pe.getUint16(16,!0),le=pe.getUint16(18,!0),0==se||0==le)return;if(y=pe.getUint8(37),18==w||20==w?ue=1:0==w&&(ue=0),1!=Ae&&(2!=Ae||1!=Pe)||ue){var fe=pe.getUint16(20,!0);se==me&&le==ge||(me=se,ge=le,Re?this.resize(se,le):Te.resize(se,le),Ue={decodeMode:"canvas",width:se,height:le},[2,4,8].includes(A)?Ue.encodeMode="H264":12===A&&(Ue.encodeMode="H265"),N(Ue),ee=null,te=null,ne=null,re=null,ie=null,ae=null,ee=new ArrayBuffer(se*le),re=new Uint8Array(ee),te=new ArrayBuffer(se*le/4),ie=new Uint8Array(te),ne=new ArrayBuffer(se*le/4),ae=new Uint8Array(ne)),H&&(H=!1,function(e,t){var n=W.width,r=W.height,i=document.createElement("canvas");i.width=n,i.height=r;for(var a=i.getContext("2d"),o=0;o<e.length;o++)a.drawImage(e[o],0,0,n,r);for(var s=i.toDataURL(),l=atob(s.substring("data:image/png;base64,".length)),u=new Uint8Array(l.length),c=0,f=l.length;c<f;++c)u[c]=l.charCodeAt(c);var h=new Blob([u.buffer],{type:"image/png"});u=null,Oe(h,t+".png"),h=null}([W],G));var Ee=0;for(Ee=0;Ee<le;Ee++)re.set(Module.HEAPU8.subarray(t+Ee*fe,t+Ee*fe+se),Ee*se);for(Ee=0;Ee<le/2;Ee++)ie.set(Module.HEAPU8.subarray(n+Ee*fe/2,n+Ee*fe/2+se/2),Ee*se/2);for(Ee=0;Ee<le/2;Ee++)ae.set(Module.HEAPU8.subarray(a+Ee*fe/2,a+Ee*fe/2+se/2),Ee*se/2);Re?Ce&&Ce.drawCanvas(re,ie,ae):Te&&Te.draw(re,ie,ae)}else{!0===J&&0===w&&(l&&(l.close(),l.terminate(),l=null),_=null,u=null,c=null,g=!1,J=!1);var xe=t;if(ee=new ArrayBuffer(s),(oe=new Uint8Array(ee)).set(Module.HEAPU8.subarray(xe,xe+s)),null==u&&(u=new i(Ae)),null==c&&(c=new r(Ae)),function(e,t,n){for(var r=null,i=e.length,a=[],o=0;o<=i;)if(0==e[o])if(0==e[o+1])if(1==e[o+2]){if(a.push(o),o+=3,1==Ae){if(5==(31&e[o])||1==(31&e[o]))break}else if(2==Ae&&(38==(255&e[o])||2==(255&e[o])))break}else 0==e[o+2]?o++:o+=3;else o+=2;else o+=1;var s=0;if(1==Ae){for(var o=0;o<a.length;o++)switch(r=e.subarray(a[o]+3,a[o+1]),31&e[a[o]+3]){case 1:case 5:s=a[o]-1,D=e.subarray(s,e.length);break;case 7:u.parse(r),h=r;break;case 8:d=r}if(!g){g=!0;var l={id:1,width:se,height:le,type:"video",profileIdc:u.getSpsValue("profile_idc"),profileCompatibility:0,levelIdc:u.getSpsValue("level_idc"),sps:[h],pps:[d],timescale:1e3,fps:y};_=c.initSegment(l),m=u.getCodecInfo()}}else if(2==Ae){for(var o=0;o<a.length;o++)switch(r=e.subarray(a[o]+3,a[o+1]-1),255&e[a[o]+3]){case 2:case 38:s=a[o]-1,D=e.subarray(s,e.length);break;case 64:f=r;break;case 66:var p=e.subarray(a[o]+5,a[o+1]-1);u.parse(p),h=r;break;case 68:d=r}if(!g){g=!0;var v=u.getSpsValue("general_profile_space"),b=u.getSpsValue("general_tier_flag"),w=u.getSpsValue("general_profile_idc"),S=u.getSpsValue("temporalIdNested"),l=(u.getSpsValue("general_profile_compatibility_flags"),u.getSpsValue("general_constraint_indicator_flags"),{id:1,width:se,height:le,type:"video",general_profile_flag:v<<6|b<<5|w,general_profile_compatibility_flags:u.getSpsValue("general_profile_compatibility_flags"),general_constraint_indicator_flags:u.getSpsValue("general_constraint_indicator_flags"),general_level_idc:u.getSpsValue("general_level_idc"),chroma_format_idc:u.getSpsValue("chroma_format_idc"),bitDepthLumaMinus8:u.getSpsValue("bitDepthLumaMinus8"),bitDepthChromaMinus8:u.getSpsValue("bitDepthChromaMinus8"),rate_layers_nested_length:11|(1&S)<<2,vps:[f],sps:[h],pps:[d],timescale:1e3,fps:y});_=c.initSegment(l)}}}(oe),null==l){var Ue={decodeMode:"video",width:se,height:le};[2,4,8].includes(A)?Ue.encodeMode="H264":12===A&&(Ue.encodeMode="H265"),N(Ue),Me()}H&&(H=!1,l.capture(G)),l.setvideoTimeStamp(O),function(){if(null!=D){var e={duration:Math.round(1/y*1e3),size:D.length,frame_time_stamp:null,frameDuration:null};e.frameDuration=e.duration,null==S.samples&&(S.samples=new Array(P)),S.samples[R++]=e,M+=e.frameDuration,E+=e.frameDuration;var t=D.length-4;D[0]=(4278190080&t)>>>24,D[1]=(16711680&t)>>>16,D[2]=(65280&t)>>>8,D[3]=255&t;var n=new Uint8Array(D.length+T);if(0!==T&&n.set(C),n.set(D,T),T=(C=n).length,R%P===0&&0!==R){if(null!==S.samples[0].frameDuration&&(S.baseMediaDecodeTime=1===x?0:I,I=M),1==p)for(var r=S.samples.length,i=E/P,a=0;a<r;a++)S.samples[a].frameDuration=i;E=0,U=c.mediaSegment(x,S,C,S.baseMediaDecodeTime),x++,R=0,C=null,T=0,null!==l?l.setMediaSegment(U):!1===g&&(debug.log("workerManager::videoMS error!! recreate videoMS"),Me())}}}()}if(!ce&&X)return void V()}else if(2==b&&Z){K=pe.getUint32(24,!0),Q=pe.getUint8(28,!0),pe.getUint8(29,!0),be!=s&&(be=s,_e=null,ye=null,_e=new ArrayBuffer(s),ye=new Uint8Array(_e)),ye.set(Module.HEAPU8.subarray(t,t+s));for(var Ie=new Int16Array(ye.buffer,ye.byteOffset,ye.byteLength/Int16Array.BYTES_PER_ELEMENT),De=new Float32Array(Ie.length),ke=0;ke<Ie.length;ke++)De[ke]=Ie[ke]/Math.pow(2,15);K==we&&Q==Se||(we=K,Se=Q,ve&&(ve.stop(),ve=null),(ve=new o).setSamplingRate(K),null!==ve&&(ve.setInitVideoTimeStamp(0),ve.audioInit(1,K)||(ve.stop(),ve=null))),ve&&ve.bufferAudio(De,0),Ie=null,De=null}$=null},setCallback:function(e,t){switch(e){case"timeStamp":case"ResolutionChanged":break;case"audioTalk":L=t;break;case"stepRequest":case"metaEvent":case"videoMode":break;case"loadingBar":F=t;break;case"Error":B=t;break;case"PlayStart":k=t;break;case"DecodeStart":N=t;break;case"UpdateCanvas":case"FrameTypeChange":case"MSEResolutionChanged":case"audioChange":case"WorkerReady":break;case"FileOver":V=t;break;case"UpdatePlayingTime":z=t}},capture:function(e,t){G=e,H=!0,W=t},setFileOver:function(e){X=e,!ce&&X&&V()},mute:function(e){ve&&ve.Mute(e)},setPlayAudio:function(e){Z=e},setVolume:function(e){ve&&ve.setVolume(e)},talkInit:function(e,t){a||(a=new A),a.talkInit(e,t),a.setSendAudioTalkCallback(L)},resize:function(e,n){Ce&&(Ce=null);var r=new De(e,n);Ce=new v(t,r),r=null}};var Oe=function(e){var t=e.document,n=function(){return e.URL||e.webkitURL||e},r=t.createElementNS("http://www.w3.org/1999/xhtml","a"),i="download"in r,a=/constructor/i.test(e.HTMLElement),o=/CriOS\/[\d]+/.test(navigator.userAgent),s=function(t){(e.setImmediate||e.setTimeout)(function(){throw t},0)},l=function(e){setTimeout(function(){"string"===typeof e?n().revokeObjectURL(e):e.remove()},4e4)},u=function(e){return/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e},c=function(t,c,f){f||(t=u(t));var h,d=this,p="application/octet-stream"===t.type,m=function(){!function(e,t,n){for(var r=(t=[].concat(t)).length;r--;){var i=e["on"+t[r]];if("function"===typeof i)try{i.call(e,n||e)}catch(e){s(e)}}}(d,"writestart progress write writeend".split(" "))};if(d.readyState=d.INIT,i)return h=n().createObjectURL(t),void setTimeout(function(){r.href=h,r.download=c,r.dispatchEvent(new MouseEvent("click")),m(),l(h),d.readyState=d.DONE});!function(){if((o||p&&a)&&e.FileReader){var r=new FileReader;return r.onloadend=function(){var t=o?r.result:r.result.replace(/^data:[^;]*;/,"data:attachment/file;");e.open(t,"_blank")||(e.location.href=t),t=void 0,d.readyState=d.DONE,m()},r.readAsDataURL(t),void(d.readyState=d.INIT)}h||(h=n().createObjectURL(t)),p?e.location.href=h:e.open(h,"_blank")||(e.location.href=h),d.readyState=d.DONE,m(),l(h)}()},f=c.prototype;return"undefined"!==typeof navigator&&navigator.msSaveOrOpenBlob?function(e,t,n){return t=t||e.name||"download",n||(e=u(e)),navigator.msSaveOrOpenBlob(e,t)}:(f.readyState=f.INIT=0,f.WRITING=1,f.DONE=2,f.error=f.onwritestart=f.onprogress=f.onwrite=f.onabort=f.onerror=f.onwriteend=null,function(e,t,n){return new c(e,t||e.name||"download",n)})}(window);return new $};function R(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?R(Object(n),!0).forEach(function(t){E(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e,t){return t=(t=t.toLowerCase())[0].toUpperCase()+t.substr(1),Object.prototype.toString.call(e)==="[object "+t+"]"}function U(e,t,n){if(void 0===n&&(n=2),void 0===t&&(t=0),(e=e.toString()).length>=n)return e;var r=n-e.length;return new Array(r).fill(String(t)).join("")+e}function I(e,t){return void 0!==e&&e?(t=t||new Date,e=(e=(e=(e=(e=(e=e.replace(/y/gi,U(t.getFullYear()),0)).replace(/m/gi,U(t.getMonth()+1),0)).replace(/d/gi,U(t.getDate()),0)).replace(/h/gi,U(t.getHours()),0)).replace(/i/gi,U(t.getMinutes()),0)).replace(/s/gi,U(t.getSeconds()),0)):""}function D(e,t){var n=(e=e||{}).nameFormat||["ymd_his"];t=t||new Date;var r="";if(x(n,"string"))n=[n,{}];else{if(!x(n,"array"))return void function(e){throw new Error(e)}("name format must be string or array");x(n[0],"string")||(n[0]="ymd_his"),x(n[1],"object")||(n[1]={})}var i=n[0].split(/\{(?:[^{}]+)\}/),a=n[1];n[0].replace(/\{([^{}]*)\}/g,function(e,t,n){i.shift();r+=I(),r+=t in a?a[t]:e});var o=i.shift();return r+=I(o,t)}function O(e,t){this.name=e,this.allowUpDateName=!0,this.byteLength=0,this.options=t,this.startTime=(new Date).toLocaleString()}O.prototype.setEndTime=function(){this.endTime=(new Date).toLocaleString()},O.prototype.updateNameByStream=function(e,t){if(this.allowUpDateName){var n=new Uint8Array(t),r=(n[19]<<24)+(n[18]<<16)+(n[17]<<8)+n[16]>>>0,i="20"+(r>>26)+"/"+(r>>22&15)+"/"+(r>>17&31)+" "+(r>>12&31)+":"+(r>>6&63)+":"+(63&r);this.name=D(e,new Date(i)),this.allowUpDateName=!1,n=null}t=null};var k=new function(){var e={count:0,total:0,group:[]},t=function(){};return t.prototype.add=function(t){e.count++,e.total+=t.byteLength,e.group.push(t)},t.prototype.get=function(t){return t in e?e[t]:e},new t};var L=function(){var e=1048576,t=null,n=null,r=0,i=void 0,a=null;function o(){this.onMessage=function(){},this.postMessage=function(e){this.__onMessage(e)},this.__postMessage=function(e){this.onMessage(e)}}return o.prototype.__onMessage=function(e){var t=e;switch(t.type){case"init":this.init(t.options);break;case"addBuffer":this.addBuffer(t);break;case"close":this.close()}},o.prototype.init=function(t){this.fullSize=t.fullSize||1/0,this.singleSize=t.singleSize+20*e||520*e,i="init",this.limitOptions=Object.assign({limitBy:"fullSize"},t.limitOptions),this.nameOptions=Object.assign({namedBy:"date",nameFormat:["ymd_his",{}]},t.nameOptions)},o.prototype._malloc=function(e){t&&n&&(n=null,t=null),t=new ArrayBuffer(e),n=new DataView(t);var r=this.nameOptions,i="";switch(this.nameOptions.namedBy.toLowerCase()){case"date":i=D(r);break;default:i=D()}a=new O(i)},o.prototype._initVideoMem=function(){!t&&this.singleSize&&this._malloc(this.singleSize)},o.prototype.appendVideoBuf=function(t){var i=t.byteLength,a=r+i;if(a>this.singleSize-20*e)this.inNodePlace(),this.addBuffer({buffer:t});else{for(var o=r;o<a;o++)n.setUint8(o,t[o-r]);r=a,this.__postMessage({type:"pendding",size:r,total:this.singleSize})}},o.prototype.addBuffer=function(e){if("closed"!==i){var t=e.buffer;this._initVideoMem(),i="addBuffer";var n=t.length,a=r+n;k.get("total")+a>this.fullSize?this.close():this.appendVideoBuf(t)}},o.prototype.inNodePlace=function(){if("addBuffer"===i){i="download",a.updateNameByStream(this.nameOptions,t.slice(0,20)),a.byteLength=r,a.setEndTime(),k.add(a);var e=t.slice(0,r);if(this.reset(),this.__postMessage({type:"download",data:M(M({},a),{},{buffer:e})}),e=null,"count"===this.limitOptions.limitBy){var n=this.limitOptions.count;n&&n===k.get("count")&&this.close()}}},o.prototype.reset=function(){r=0,this._malloc(this.singleSize)},o.prototype.close=function(){this.inNodePlace(),"closed"!==i&&void 0!==i&&(i="closed",this.__postMessage({type:"closed",message:"record was closed"}),t=null,n=null)},new o},F=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(e){e=e;var r=null,i=n.isTalkService,a=6,o=null,l=null,u=0,c=0,f=!1,h=!1,d={OK:200,UNAUTHORIZED:401,NOTFOUND:404,INVALID_RANGE:457,NOTSERVICE:503,DISCONNECT:999},p=4e4,m="",g=[],v=1,_=null,y={},b="Options",w=null,S=null,T="",C=null,A={},R=t,M=null,E={},x=null,U=0,I=0,D=null,O=!1,k=!1,F=Symbol();function B(){}function N(e,t,n,r,a){var o="";switch(e){case"OPTIONS":case"TEARDOWN":case"GET_PARAMETER":case"SET_PARAMETERS":o=e+" "+R+" RTSP/1.0\r\nCSeq: "+v+"\r\n"+m+"\r\n";break;case"DESCRIBE":o=e+" "+R+" RTSP/1.0\r\nCSeq: "+v+"\r\n"+m,o+=i?"User-Agent: Dahua Rtsp Client/2.0\r\n\r\n":"\r\n",G(),H();break;case"SETUP":s.h.log("trackID: "+t),o=e+" "+R+"/trackID="+t+" RTSP/1.0\r\nCSeq: "+v+"\r\n"+m+"Transport: DH/AVP/TCP;unicast;interleaved="+2*t+"-"+(2*t+1)+"\r\n",o+=0!=S?"Session: "+S+"\r\n\r\n":"\r\n",G(),H();break;case"PLAY":o=e+" "+R+" RTSP/1.0\r\nCSeq: "+v+"\r\nSession: "+S+"\r\n",void 0!=r&&0!=r?(o+="Range: npt="+r+"-\r\n",o+=m):o+=m,o+=a?"Speed: "+a+"\r\n":"\r\n",G(),H();break;case"PAUSE":o=e+" "+R+" RTSP/1.0\r\nCSeq: "+v+"\r\nSession: "+S+"\r\n\r\n";break;case"SCALE":o="PLAY "+R+" RTSP/1.0\r\nCSeq: "+v+"\r\nSession: "+S+"\r\n",o+="Scale: "+r+"\r\n",o+=m+"\r\n"}return s.h.log(o),o}function V(e){!0===i&&function(e){null!==r&&r.readyState===WebSocket.OPEN&&r.send(e)}(e)}function z(e){s.h.log(e);var t,n=e.search("CSeq: ")+5;if(v=parseInt(e.slice(n,n+10))+1,(t=function(e){var t={},n=0,r=0,i=null,a=null;if(-1!==e.search("Content-Type: application/sdp")){var o=e.split("\r\n\r\n");a=o[0]}else a=e;var s=a.split("\r\n"),l=s[0].split(" ");if(l.length>2&&(t.ResponseCode=parseInt(l[1]),t.ResponseMessage=l[2]),t.ResponseCode===d.OK){for(n=1;n<s.length;n++)if("Public"===(i=s[n].split(":"))[0])t.MethodsSupported=i[1].split(",");else if("CSeq"===i[0])t.CSeq=parseInt(i[1]);else if("Content-Type"===i[0])t.ContentType=i[1],-1!==t.ContentType.search("application/sdp")&&(t.SDPData=X(e));else if("Content-Length"===i[0])t.ContentLength=parseInt(i[1]);else if("Content-Base"===i[0]){var u=s[n].search("Content-Base:");-1!==u&&(t.ContentBase=s[n].substr(u+13))}else if("Session"===i[0]){var c=i[1].split(";");t.SessionID=parseInt(c[0])}else if("Transport"===i[0]){var f=i[1].split(";");for(r=0;r<f.length;r++){var h=f[r].search("interleaved=");if(-1!==h){var p=f[r].substr(h+12),m=p.split("-");m.length>1&&(t.RtpInterlevedID=parseInt(m[0]),t.RtcpInterlevedID=parseInt(m[1]))}}}else if("RTP-Info"===i[0]){i[1]=s[n].substr(9);var g=i[1].split(",");for(t.RTPInfoList=[],r=0;r<g.length;r++){var v=g[r].split(";"),_={},y=0;for(y=0;y<v.length;y++){var b=v[y].search("url=");-1!==b&&(_.URL=v[y].substr(b+4)),-1!==(b=v[y].search("seq="))&&(_.Seq=parseInt(v[y].substr(b+4)))}t.RTPInfoList.push(_)}}}else if(t.ResponseCode===d.UNAUTHORIZED)for(n=1;n<s.length;n++)if("CSeq"===(i=s[n].split(":"))[0])t.CSeq=parseInt(i[1]);else if("WWW-Authenticate"===i[0]){var w=i[1].split(",");for(r=0;r<w.length;r++){var S=w[r].search("Digest realm=");if(-1!==S){var T=w[r].substr(S+13).split('"');t.Realm=T[1]}if(-1!==(S=w[r].search("nonce="))){var C=w[r].substr(S+6).split('"');t.Nonce=C[1]}}}return t}(e)).ResponseCode===d.UNAUTHORIZED&&""===m)!function(e){var t=E.username,n=E.passWord,r={Method:null,Realm:null,Nonce:null,Uri:null},i=null;r={Method:b.toUpperCase(),Realm:e.Realm,Nonce:e.Nonce,Uri:R},i=function(e,t,n,r,i,a){var o=null,s=null;return o=hex_md5(e+":"+r+":"+t).toLowerCase(),s=hex_md5(a+":"+n).toLowerCase(),hex_md5(o+":"+i+":"+s).toLowerCase()}(t,n,r.Uri,r.Realm,r.Nonce,r.Method),m='Authorization: Digest username="'+t+'", realm="'+r.Realm+'",',m+=' nonce="'+r.Nonce+'", uri="'+r.Uri+'", response="'+i+'"',m+="\r\n",j(N("OPTIONS",null))}(t);else if(t.ResponseCode===d.OK){if("Options"===b)return b="Describe",N("DESCRIBE",null);if("Describe"===b){y=X(e),"undefined"!==typeof t.ContentBase&&(y.ContentBase=t.ContentBase);var r=0;for(r=0;r<y.Sessions.length;r+=1){var a={};"JPEG"===y.Sessions[r].CodecMime||"H264"===y.Sessions[r].CodecMime||"H265"===y.Sessions[r].CodecMime||"H264-SVC"==y.Sessions[r].CodecMime?(a.codecName=y.Sessions[r].CodecMime,"H264-SVC"==y.Sessions[r].CodecMime&&(a.codecName="H264"),"H265"==y.Sessions[r].CodecMime&&B.prototype.setLiveMode("canvas"),a.trackID=y.Sessions[r].ControlURL,a.ClockFreq=y.Sessions[r].ClockFreq,a.Port=parseInt(y.Sessions[r].Port),"undefined"!==typeof y.Sessions[r].Framerate&&(a.Framerate=parseInt(y.Sessions[r].Framerate),M(a.Framerate)),g.push(a)):"PCMU"===y.Sessions[r].CodecMime||-1!==y.Sessions[r].CodecMime.search("G726-16")||-1!==y.Sessions[r].CodecMime.search("G726-24")||-1!==y.Sessions[r].CodecMime.search("G726-32")||-1!==y.Sessions[r].CodecMime.search("G726-40")||"PCMA"===y.Sessions[r].CodecMime?("PCMU"===y.Sessions[r].CodecMime?a.codecName="G.711Mu":"G726-16"===y.Sessions[r].CodecMime?a.codecName="G.726-16":"G726-24"===y.Sessions[r].CodecMime?a.codecName="G.726-24":"G726-32"===y.Sessions[r].CodecMime?a.codecName="G.726-32":"G726-40"===y.Sessions[r].CodecMime?a.codecName="G.726-40":"PCMA"===y.Sessions[r].CodecMime&&(a.codecName="G.711A"),a.trackID=y.Sessions[r].ControlURL,a.ClockFreq=y.Sessions[r].ClockFreq,a.Port=parseInt(y.Sessions[r].Port),a.Bitrate=parseInt(y.Sessions[r].Bitrate),a.TalkTransType=y.Sessions[r].TalkTransType,g.push(a)):"mpeg4-generic"===y.Sessions[r].CodecMime||"MPEG4-GENERIC"===y.Sessions[r].CodecMime?(a.codecName="mpeg4-generic",a.trackID=y.Sessions[r].ControlURL,a.ClockFreq=y.Sessions[r].ClockFreq,a.Port=parseInt(y.Sessions[r].Port),a.Bitrate=parseInt(y.Sessions[r].Bitrate),g.push(a)):"vnd.onvif.metadata"===y.Sessions[r].CodecMime?(a.codecName="MetaData",a.trackID=y.Sessions[r].ControlURL,a.ClockFreq=y.Sessions[r].ClockFreq,a.Port=parseInt(y.Sessions[r].Port),g.push(a)):"stream-assist-frame"===y.Sessions[r].CodecMime?(a.codecName="stream-assist-frame",a.trackID=y.Sessions[r].ControlURL,a.ClockFreq=y.Sessions[r].ClockFreq,a.Port=parseInt(y.Sessions[r].Port),g.push(a)):s.h.log("Unknown codec type:",y.Sessions[r].CodecMime,y.Sessions[r].ControlURL)}if(w=0,b="Setup",s.h.log(g),g[w]||i)return N("SETUP",i?0:g[w].trackID.split("=")[1]-0);_({symbol:F,errorCode:"407"})}else if("Setup"===b){if(S=t.SessionID,w<g.length)return g[w].RtpInterlevedID=t.RtpInterlevedID,g[w].RtcpInterlevedID=t.RtcpInterlevedID,(w+=1)!==g.length?N("SETUP",g[w].trackID.split("=")[1]-0):(b="Play",N("PLAY",null));if(i)return x.setCallback("audioTalk",V),x.talkInit(g,A),b="Play",N("PLAY",null);s.h.log("Unknown setup SDP index")}else"Play"===b?(S=t.SessionID,i||(clearInterval(C),C=setInterval(function(){return j(N("GET_PARAMETER",null))},p)),b="Playing"):"Playing"===b||s.h.log("unknown rtsp state:"+b)}else if(t.ResponseCode===d.NOTSERVICE){if("Setup"===b&&-1!==g[w].trackID.search("trackID=t"))return g[w].RtpInterlevedID=-1,g[w].RtcpInterlevedID=-1,w+=1,_({symbol:F,errorCode:"504",description:"Talk Service Unavilable",place:"RtspClient.js"}),w<g.length?N("SETUP",g[w].trackID):(b="Play",N("PLAY",null));_({symbol:F,errorCode:"503",description:"Service Unavilable"})}else if(t.ResponseCode===d.NOTFOUND){if("Describe"===b||"Options"===b)return void _({symbol:F,errorCode:404,description:"rtsp not found"})}else if(t.ResponseCode===d.INVALID_RANGE)return"backup"!==T&&"playback"!==T||_({symbol:F,errorCode:"457",description:"Invalid range"}),void s.h.log("RTP disconnection detect!!!")}function j(e){void 0!=e&&null!=e&&""!=e&&(null!==r&&r.readyState===WebSocket.OPEN?(!1===h&&-1!==e.search("DESCRIBE")&&(f=!0,h=!0),void 0!=e&&r.send(q(e))):s.h.log("ws\u672a\u8fde\u63a5"))}function q(e){for(var t=e.length,n=new Uint8Array(new ArrayBuffer(t)),r=0;r<t;r++)n[r]=e.charCodeAt(r);return n}function Y(e){var t=new Uint8Array,n=new Uint8Array(e.data);for((t=new Uint8Array(n.length)).set(n,0),u=t.length,U&&(clearTimeout(U),U=0),I&&(clearTimeout(I),I=0);u>0;)if(36!==t[0]){var r=String.fromCharCode.apply(null,t),i=null;(-1!==r.indexOf("OffLine:File Over")||-1!==r.indexOf("OffLine:Internal Error")||r.includes("is_session_end: true"))&&x.setFileOver(!0),-1!==r.indexOf("OffLine:KmsUnavailable")&&_({symbol:F,errorCode:203}),!0===f?(i=r.lastIndexOf("\r\n"),f=!1):i=r.search("\r\n\r\n");var s=r.search("RTSP");if(-1===s)return void(t=new Uint8Array);if(-1===i)return void(u=t.length);o=t.subarray(s,i+a),t=t.subarray(i+a),j(z(String.fromCharCode.apply(null,o))),u=t.length}else{if(l=t.subarray(0,a),!((c=l[2]<<24|l[3]<<16|l[4]<<8|l[5])+a<=t.length))return void(u=t.length);var h=t.subarray(a,c+a);O&&D.postMessage({type:"addBuffer",buffer:h}),W(l,h),t=t.subarray(c+a),u=t.length}}function G(){U&&clearTimeout(U),U=setTimeout(function(){_({symbol:F,errorCode:"407",description:"Request Timeout"})},3e4)}function H(){I&&clearTimeout(I),I=setTimeout(function(){_({symbol:F,errorCode:"408",description:"Short Request Timeout"})},3e3)}function W(e,t){x&&0===x.InputData(e,t)&&k&&W(e,t)}function X(e){var t={Sessions:[]},n=(-1!==e.search("Content-Type: application/sdp")?e.split("\r\n\r\n")[1]:e).split("\r\n"),r=0,i=!1;for(r=0;r<n.length;r++){var a=n[r].split("=");if(a.length>0)switch(a[0]){case"a":var o=a[1].split(":");if(o.length>1){if("control"===o[0]){var s=n[r].search("control:");!0===i?-1!==s&&(t.Sessions[t.Sessions.length-1].ControlURL=n[r].substr(s+8)):-1!==s&&(t.BaseURL=n[r].substr(s+8))}else if("rtpmap"===o[0]){var l=o[1].split(" ");t.Sessions[t.Sessions.length-1].PayloadType=l[0];var u=l[1].split("/");t.Sessions[t.Sessions.length-1].CodecMime=u[0],u.length>1&&(t.Sessions[t.Sessions.length-1].ClockFreq=u[1])}else if("framesize"===o[0]){var c=o[1].split(" ");if(c.length>1){var f=c[1].split("-");t.Sessions[t.Sessions.length-1].Width=f[0],t.Sessions[t.Sessions.length-1].Height=f[1]}}else if("framerate"===o[0])t.Sessions[t.Sessions.length-1].Framerate=o[1];else if("fmtp"===o[0]){var h=n[r].split(" ");if(h.length<2)continue;for(var d=1;d<h.length;d++){var p=h[d].split(";"),m=0;for(m=0;m<p.length;m++){var g=p[m].search("mode=");if(-1!==g&&(t.Sessions[t.Sessions.length-1].mode=p[m].substr(g+5)),-1!==(g=p[m].search("config="))&&(t.Sessions[t.Sessions.length-1].config=p[m].substr(g+7),A.config=t.Sessions[t.Sessions.length-1].config,A.clockFreq=t.Sessions[t.Sessions.length-1].ClockFreq,A.bitrate=t.Sessions[t.Sessions.length-1].Bitrate),-1!==(g=p[m].search("sprop-vps="))&&(t.Sessions[t.Sessions.length-1].VPS=p[m].substr(g+10)),-1!==(g=p[m].search("sprop-sps="))&&(t.Sessions[t.Sessions.length-1].SPS=p[m].substr(g+10)),-1!==(g=p[m].search("sprop-pps="))&&(t.Sessions[t.Sessions.length-1].PPS=p[m].substr(g+10)),-1!==(g=p[m].search("sprop-parameter-sets="))){var v=p[m].substr(g+21).split(",");v.length>1&&(t.Sessions[t.Sessions.length-1].SPS=v[0],t.Sessions[t.Sessions.length-1].PPS=v[1])}}}}}else 1===o.length&&("recvonly"===o[0]?t.Sessions[t.Sessions.length-1].TalkTransType="recvonly":"sendonly"===o[0]&&(t.Sessions[t.Sessions.length-1].TalkTransType="sendonly"));break;case"m":var _=a[1].split(" "),y={};y.Type=_[0],y.Port=_[1],y.Payload=_[3],t.Sessions.push(y),i=!0;break;case"b":if(!0===i){var b=a[1].split(":");t.Sessions[t.Sessions.length-1].Bitrate=b[1]}}}return t}return B.prototype={init:function(e,t,n,r){x||(x=new P),this.symbol=F,k=!!r,e&&(this.nPlayPort=x.Init(e,t,n,k))},connect:function(){r||((r=new WebSocket(e)).binaryType="arraybuffer",r.addEventListener("message",Y,!1),r.onopen=function(){var e="OPTIONS "+R+" RTSP/1.0\r\nCSeq: "+v+"\r\n";i&&(e+="User-Agent: Dahua Rtsp Client/2.0\r\n");var t=q(e+="\r\n");r.send(t)},r.onerror=function(e){_({symbol:F,errorCode:202,description:"Open WebSocket Error"})})},disconnect:function(){x&&(x.Stop(),x=null),D&&(D=null),j(N("TEARDOWN",null)),clearInterval(C),C=null,null!==r&&(r.onerror=null),null!==r&&r.readyState===WebSocket.OPEN&&(r.close(),r=null,S=null),D&&(D=null)},controlPlayer:function(e){if("PLAY_SPEED"===e.command)return j(N("PLAY",null,0,null,e.speed)),j(N("GET_PARAMETER",null)),void x.setSpeed(e.speed);var t="";switch(e.command,e.command){case"PLAY":if(b="Play",null!=e.range){t=N("PLAY",null,0,e.range);break}t=N("PLAY",null);break;case"PAUSE":if("PAUSE"===b)break;b="PAUSE",t=N("PAUSE",null);break;case"SCALE":t=N("SCALE",null,0,e.data);break;case"TEARDOWN":t=N("TEARDOWN",null);break;case"audioPlay":x&&x.setPlayAudio(!!e.data);break;case"volume":x&&x.setVolume(e.data);break;case"audioSamplingRate":break;case"startLocalRecord":D=new L,function(e){var t=parseInt(e)||500;D.postMessage({type:"init",options:{singleSize:1048576*t,nameOptions:{namedBy:"date",nameFormat:["ymd_his"]},limitOptions:{limitBy:"count",count:10}}}),D.onMessage=function(e){switch(e.type){case"pendding":break;case"download":!function(e,t){e=(e=(e||Date.now())+"").toLowerCase().split(".dav")[0];var n=new Blob([t]),r=document.createElement("a");r.href=URL.createObjectURL(n),r.download=e+".dav",r.click(),URL.revokeObjectURL(r.href),r=null,t=null}(e.data.name,e.data.buffer);break;case"closed":D=null,O=!1}}}(e.data),O=!0;break;case"stopLocalRecord":D.postMessage({type:"close"});break;default:s.h.log("\u672a\u77e5\u6307\u4ee4: "+e.command)}""!=t&&j(t)},setLiveMode:function(e){},setPlayMode:function(e){},setRTSPURL:function(e){R=e},setCallback:function(e,t){switch(e){case"GetFrameRate":M=t;break;default:x.setCallback(e,t)}"Error"===e&&(_=t)},setUserInfo:function(e,t){E.username=e,E.passWord=t},capture:function(e,t){x.capture(e,t)},setLessRate:function(e){},setFrameData:function(e,t,n,r,i,a){x&&x.setFrameData(e,t,n,r,i,a)}},new B}(e)};function B(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var V=function(e){this.wsURL=e.wsURL,this.rtspURL=e.rtspURL,this.isTalkService=e.isTalkService,this.isPlayback=e.isPlayback||!1,this.ws=null,this.decodeMode=e.decodeMode,this.lessRateCanvas=e.lessRateCanvas||!1,this.nPlayPort="",this.events=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?B(Object(n),!0).forEach(function(t){N(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):B(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}({ResolutionChanged:function(){},PlayStart:function(){},DecodeStart:function(){},UpdateCanvas:function(){},GetFrameRate:function(){},FrameTypeChange:function(){},Error:function(){},MSEResolutionChanged:function(){},audioChange:function(){},WorkerReady:function(){},IvsDraw:function(){},FileOver:function(){},Waiting:function(){},UpdatePlayingTime:function(){}},e.events),this.username=e.username,this.password=e.password};V.prototype={init:function(e,t,n){for(var r in this.ws=new F(this.wsURL,this.rtspURL),this.ws.init(e,t,n,this.isPlayback),this.nPlayPort=this.ws.nPlayPort,this.ws.setLiveMode(this.decodeMode),this.ws.setUserInfo(this.username,this.password),this.ws.setPlayMode(this.isPlayback),this.ws.setLessRate(this.lessRateCanvas),this.events)this.ws.setCallback(r,this.events[r]);this.events=null},connect:function(){this.ws&&this.ws.connect()},play:function(){this.controlPlayer("PLAY")},pause:function(){this.controlPlayer("PAUSE")},stop:function(){this.controlPlayer("TEARDOWN")},close:function(){this.ws&&(this.ws.disconnect(),this.ws=null)},playByTime:function(e){this.controlPlayer("PLAY","video",e)},playSpeed:function(e){this.controlPlayer("PAUSE");var t=.125;t=e<.25?.125:e>=.25&&e<.5?.25:e>=.5&&e<1?.5:e>=1&&e<2?1:e>=2&&e<4?2:e>=4&&e<8?4:e>=8&&e<16?8:16,this.controlPlayer("PLAY_SPEED",t)},playRewind:function(){},audioPlay:function(){this.controlPlayer("audioPlay","start")},audioStop:function(){this.controlPlayer("audioPlay","stop")},setAudioSamplingRate:function(e){this.controlPlayer("audioSamplingRate",e)},setAudioVolume:function(e){this.controlPlayer("audioPlay",e)},startLocalRecord:function(e){this.controlPlayer("startLocalRecord",e)},stopLocalRecord:function(){this.controlPlayer("stopLocalRecord")},controlPlayer:function(e,t,n){var r;r="video"===t?{command:e,range:n||0}:{command:e,data:t},"PLAY_SPEED"===e&&(r={command:e,speed:t}),this.ws&&this.ws.controlPlayer(r)},setPlayMode:function(e){this.ws&&this.ws.setLiveMode(e)},setPlayPath:function(e){this.ws&&this.ws.setRTSPURL(e)},capture:function(e,t){this.ws&&this.ws.capture(e,t)},setFrameData:function(e,t,n,r,i,a){this.ws&&this.ws.setFrameData(e,t,n,r,i,a)},talk:function(e){if("on"===e){for(var t in this.ws=new F(this.wsURL,this.rtspURL,{isTalkService:this.isTalkService}),this.ws.init(),this.events)this.ws.setCallback(t,this.events[t]);this.events=null,this.connect()}else this.close()},on:function(e,t){this.events[e]=t}};t.default=V}]).default;