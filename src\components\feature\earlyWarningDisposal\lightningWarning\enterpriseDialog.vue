<template>
  <div>
    <el-dialog
      title="设备信息"
      :visible.sync="show"
      width="1000px"
      @close="closeBoolean()"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="table">
        <ul class="container">
          <li>
            <div class="l">设备所属</div>
            <div class="r">{{ entObj.companyName }}</div>
          </li>
          <li class="">
            <div class="l">监控距离</div>
            <div class="r">{{ entInfo.radius }}</div>
          </li>
          <li class="">
            <div class="l">设备名称</div>
            <div class="r">{{ entInfo.equipName }}</div>
          </li>
          <li>
            <div class="l">设备编号</div>
            <div class="r">{{ entInfo.equipCode }}</div>
          </li> 
          <li>
            <div class="l">设备经度</div>
            <div class="r">{{ entInfo.longitude }}</div>
          </li>
          <li>
            <div class="l">设备纬度</div>
            <div class="r">{{ entInfo.latitude }}</div>
          </li>
          <li>
            <div class="l">创建人</div>
            <div class="r">{{ entInfo.createBy  }}</div>
          </li>
          <li>
            <div class="l">创建时间</div>
            <div class="r">{{ entInfo.createDate }}</div>
          </li>
          <li class="bottom">
            <div class="l">最后修改人</div>
            <div class="r">{{ entInfo.updateBy }}</div>
          </li>
          <li class="bottom">
            <div class="l">最后修改时间</div>
            <div class="r">{{ entInfo.updateDate }}</div>
          </li>
        </ul>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { lightningWarningDeviceDetail } from "@/api/earlyWarningDisposal";
export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    entObj: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      entInfo:{}
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    getData() {
      lightningWarningDeviceDetail(this.entObj.equipId).then((res) => {
        if (res.data.code == 0) {
          this.entInfo = res.data.data;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  width: 100%;
  height: 100%;
  overflow: auto;
  .container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    .bottom {
      border-bottom: 1px solid rgb(231, 231, 231);
    }

    li {
      list-style-type: none;
      width: 50%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      // border-right: 1px solid rgb(231, 231, 231);
      border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 48%;
        padding: 1%;
        word-break: break-all;
      }
    }
    li:nth-of-type(2n + 0) {
      list-style-type: none;
      width: 50%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .lang {
      list-style-type: none;
      width: 50%;
      display: flex;
      border-top: 1px solid #eaedf2;
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 24.9%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 73.3%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        padding: 0px 10px;
        word-break: break-all;
      }
    }
    .liLine {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .bottom:nth-of-type(3n + 0) {
      border-right: 1px solid rgb(231, 231, 231);
    }
  }
}
</style>
