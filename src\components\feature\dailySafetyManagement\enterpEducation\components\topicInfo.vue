<!-- 分值标准 -->
<template>
  <div class="topic-container" v-loading="loading">
    <div class="label-box">单选题
      <el-button type="text" size="small" :disabled="disabled" @click="handleReset">{{ '重新生成' }}</el-button>
    </div>
    <el-table :data="singleList" :header-cell-style="headerCellStyle" border style="width: 100%" ref="radioTable">
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column prop="content" label="题干内容" align="center" />
      <el-table-column prop="courseno" label="选项内容" align="center" />
      <el-table-column prop="answer" label="答案" width="120" align="center" />
      <el-table-column prop="analysis" label="题目解析" width="120" align="center" />
      <el-table-column prop="lablename" label="所属标签" width="120" align="center" />
      <el-table-column prop="usageNum" label="使用次数" width="120" align="center" />
      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" :disabled="disabled"
            @click="handleChange(scope.row, 'single', scope.$index)">随机更换</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="label-box">多选题</div>
    <el-table :data="multipleList" :header-cell-style="headerCellStyle" border style="width: 100%" ref="multiTable">
      <el-table-column type="index" label="序号" width="55" align="center" />
      <el-table-column prop="content" label="题干内容" align="center" />
      <el-table-column prop="courseno" label="选项内容" align="center" />
      <el-table-column prop="answer" label="答案" width="120" align="center" />
      <el-table-column prop="analysis" label="题目解析" width="120" align="center" />
      <el-table-column prop="lablename" label="所属标签" width="120" align="center" />
      <el-table-column prop="usageNum" label="使用次数" width="120" align="center" />
      <el-table-column label="操作" align="center" width="180" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" :disabled="disabled"
            @click="handleChange(scope.row, 'multi', scope.$index)">随机更换</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import { randomQuestionsList, randomQuestions } from "@/api/enterpEducation";

export default {
  model: {
    prop: "testInfo",
    event: "change",
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    testInfo: {
      type: Object,
      default: () => {},
    },
    singleData: {
      type: Array,
      default: () => []
    },
    multipleData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: false,
      headerCellStyle: { background: "#F1F6FF", color: "#333" },
      multipleList: [],
      singleList: []
    };
  },
  computed: {},
  created() {
    if (this.disabled) {
      this.singleList = this.singleData;
      this.multipleList = this.multipleData;
    } else {
       this.getRandom();
    }
  },
  beforeDestroy() {
    this.$nextTick(() => {
      this.singleList = this.singleData;
      this.multipleList = this.multipleData;
    })
   
  },
  methods: {
    // 报错数据
    updateForm() {
      return new Promise((resolve, reject) => {
        if (this.singleList.length == 0 || this.multipleList.length == 0) {
          this.$message({
            type: "warning",
            message: "请先保存题目",
          });
          reject(false);
        } else {
          let singles = this.singleList.map(item => item.id)
          let multiples = this.multipleList.map(item => item.id)
          let ids = [...singles, ...multiples]
          this.$emit("change", { ...this.testInfo, ids });
          resolve(true);
        }
      });
    },
    // 暂存数据
    handleSave() {
      this.$emit('update:singleData', this.singleList);
      this.$emit('update:multipleData', this.multipleList);
    },
    // 获取随机数据
    getRandom() {
      let singles = this.singleList.map(item => item.id);
      let multiples = this.multipleList.map(item => item.id);
      let ids = [...singles, ...multiples];
      const params = {
        ids: ids,
        labelName: this.testInfo.labelName,
        singleNum: this.testInfo.singleNum,
        multipleNum: this.testInfo.multipleNum,
        questionsType: this.testInfo.topicType,
      }
      this.loading = true
      randomQuestionsList(params).then(res => {
        if (res.data.status == 200) {
          this.singleList = []
          this.multipleList = []
          this.$nextTick(() => {
            this.singleList = res.data.data.singleList;
            this.multipleList = res.data.data.multipleList;
          })
          this.$message({
            type: "success",
            message: "重新生成成功!",
          })
        } else {
          this.$message({
            type: "warning",
            message: "重新生成失败",
          })
        }
      }).finally(() => {
        this.loading = false
      })

    },
    // 重新生成
    handleReset() {
      this.singleList = [];
      this.multipleList = [];
      this.getRandom();
    },
    // 获取随机题目
    async handleChange(row, type, index) {
      const that = this;
      let ids= [];
      if (type == 'single') {
        ids = that.singleList.map(item => item.id)
      } else {
        ids = that.multipleList.map(item => item.id)
      }
      const params = {
        ids: ids,
        labelName: that.testInfo.labelName,
        singleNum: type == 'single' ? 1 : 0,
        multipleNum: type == 'single' ? 0 : 1,
        questionsType: type == 'single' ? ['1'] : ['2'],
      }
      const res = await randomQuestions(params);
      if (res.data.status == 200) {
        if (type == 'single') {
          this.singleList.splice(index, 1, res.data.data[0])
        } else {
          that.multipleList.splice(index, 1, res.data.data[0])
        }
        that.$message({
          type: "success",
          message: "随机更换成功!",
        });
      }

    }
  },
};
</script>

<style lang="scss" scoped>
.topic-container {
  height: 400px;
  overflow: auto;
  padding: 10px 20px;
  box-sizing: border-box;

  .label-box {
    height: 40px;
    margin-right: 10px;
    font-weight: 550;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
