<template>
  <div>
    <div class="header">
      <div class="mainPoint">
        <i class="accAnalyse"></i>
        <!-- <div>武汉市危险化学品安全生产风险监测预警系统</div> -->
        <div>武汉市安全生产综合业务系统</div>
      </div>
      <div class="mainRight">
        <div
          class="mainBtnUl"
          v-if="$store.state.login.user.user_type !== 'ent'"
        >
          <el-autocomplete
            v-model="state"
            :fetch-suggestions="querySearch"
            placeholder="请输入企业名称/信用代码/企业编码查询"
            clearable
            @select="handleSelect"
          >
            <i
              class="el-icon-search el-input__icon"
              slot="prefix"
              style="cursor: default"
            >
            </i>
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete>
        </div>
        <el-tooltip
          class="item"
          effect="light"
          content="智能助手"
          placement="top-start"
        >
          <span
            style="font-size: 28px; margin-right: 10px"
            class="el-icon-reading"
            @click="isPopKnowledgeFn()"
          ></span>
        </el-tooltip>

        <el-popover
          placement="top-start"
          width="130"
          trigger="hover"
          v-if="$store.state.login.user.user_type === 'park' ? false : true"
        >
          <div style="min-height: 60px" v-loading="loadingMenu">
            <div
              v-for="(item, index) in listMenu"
              :key="index"
              class="menuContainer"
            >
              <div class="menu" @click="openNewUrl(item.url)">
                <a-icon class="menuIcon" :type="item.icon" /><span>{{
                  item.name
                }}</span>
              </div>
            </div>
          </div>
          <div slot="reference">
            <a-icon type="appstore" class="icon" />
          </div>
        </el-popover>
        <!-- <el-tooltip
        class="item"
        effect="light"
        content="三维视图"
        placement="top-start"
      >
        <span class="iconfont icon" @click="$message('该功能暂未上线')">&#xe826;</span>
      </el-tooltip> -->
        <el-tooltip
          class="item"
          effect="light"
          content="企业gis一张图"
          v-if="$store.state.login.user.user_type == 'ent'"
          placement="top-start"
        >
          <span class="iconfont icon" @click="largeScreen()">&#xe660;</span>
        </el-tooltip>

        <el-tooltip
          class="item"
          effect="light"
          content="园区gis一张图"
          v-else-if="$store.state.login.user.user_type == 'park'"
          placement="top-start"
        >
          <span class="iconfont icon" @click="largeScreen()">&#xe660;</span>
        </el-tooltip>

        <el-tooltip
          v-else
          class="item"
          effect="light"
          content="GIS一张图"
          placement="top-start"
        >
          <span class="iconfont icon" @click="largeScreen()">&#xe660;</span>
        </el-tooltip>

        <el-popover
          placement="top-start"
          title="技术支持"
          width="260"
          trigger="hover"
        >
          <p style="line-height: 30px">企业QQ群1:1051280042(已满)</p>
          <p style="line-height: 30px">企业QQ群2:694342599(已满)</p>
          <p style="line-height: 30px">企业QQ群3:786669499</p>
          <p style="line-height: 30px">监管QQ群:202343939</p>
          <i class="el-icon-service icon" slot="reference"></i>
        </el-popover>
        <el-popover
          placement="top-start"
          width="170"
          trigger="hover"
          transition="el-zoom-in-top"
        >
          <div class="msg">
            <div @click="toUrl('/notification?type=1')">
              <div>警示通报</div>
              <div>({{ msg.type1 }})</div>
            </div>
            <div @click="toUrl('/notification?type=2')">
              <div>风险预警</div>
              <div>({{ msg.type2 }})</div>
            </div>
            <div
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="toUrl('/notification?type=6')"
            >
              <div>视频告警</div>
              <div>({{ msg.type6 }})</div>
            </div>
            <div
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="toUrl('/notification?type=5')"
            >
              <div>物联报警</div>
              <div>({{ msg.type5 }})</div>
            </div>
            <div @click="toUrl('/notification?type=3')">
              <div>信息审核</div>
              <div>({{ msg.type3 }})</div>
            </div>
            <div @click="toUrl('/notification?type=4')">
              <div>系统消息</div>
              <div>({{ msg.type4 }})</div>
            </div>
          </div>
          <div slot="reference" @click="toUrl('/notification?type=0')">
            <el-badge :value="msg.total != 0 ? msg.total : null" class="icon">
              <i class="qh-gaojing"></i
            ></el-badge>
          </div>
        </el-popover>
        <div
          class="nameBox"
          @mousemove="showPopover(true, 0)"
          @mouseleave="showPopover(false, 300)"
        >
          <el-popover
            placement="top-start"
            width="340"
            trigger="hover"
            transition="el-zoom-in-top"
            popper-class="popover"
            :close-delay="200"
          >
            <div @mouseleave="showPopover(false, 300)" class="popoverBox">
              <p
                style="
                  line-height: 50px;
                  border-bottom: 1px solid #e9e9e9;
                  padding: 0 15px;
                  display: flex;
                "
              >
                <!-- <img class="iconName" src="/static/img/assets/img/person.png" alt="" />
               -->
                <span class="iconfont icon">&#xe622;</span>
                <span class="name" :title="$store.state.login.user.username">{{
                  $store.state.login.user.username
                }}</span>
              </p>
              <p
                style="
                  line-height: 50px;
                  padding: 0 15px;
                  width: 100%;
                  overflow: hidden;
                  white-space: nowrap;
                  text-overflow: ellipsis;
                  -o-text-overflow: ellipsis;
                "
              >
                所属单位：
                <el-tooltip :content="user.org_name" placement="top">
                  <span>{{ user.org_name }}</span>
                </el-tooltip>
              </p>
              <!-- <p
              style="
                line-height: 50px;
                padding: 6px 15px 12px 15px;
                height: 50px;
              "
            >
              <el-button
                size="small"
                type="primary"
                icon="el-icon-unlock"
                style="float: left; margin-top: 10px"
                @click="editPass"
                >修改密码</el-button
              >
              <el-button
                size="small"
                type="danger"
                icon="el-icon-switch-button"
                style="float: right; margin-top: 10px"
                @click="loginOut"
                >退出</el-button
              >
            </p> -->
            </div>
            <div slot="reference" class="icon_box">
              <!-- <img class="iconName" src="/static/img/assets/img/person.png" alt="" /> -->
              <span class="iconfont icon">&#xe622;</span>
              <i :class="ent ? 'iconPng' : 'iconPngActive'"></i>
            </div>
          </el-popover>
        </div>
      </div>
      <el-dialog
        title="修改密码"
        :visible.sync="dialogFormVisible"
        width="600px"
        :append-to-body="true"
        @close="closeDialog"
        :close-on-click-modal="false"
      >
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm">
          <el-form-item
            label="请输入原始密码"
            :label-width="formLabelWidth"
            prop="oldPass"
          >
            <el-input
              v-model.trim="ruleForm.oldPass"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="请输入新密码"
            :label-width="formLabelWidth"
            prop="newPass"
          >
            <el-input
              v-model.trim="ruleForm.newPass"
              autocomplete="off"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="请再次输入密码"
            :label-width="formLabelWidth"
            prop="commitPass"
          >
            <el-input
              v-model.trim="ruleForm.commitPass"
              autocomplete="off"
            ></el-input>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取 消</el-button>
          <!-- <el-button type="primary" @click="dialogFormVisible = false">确 定</el-button> -->
          <el-button type="primary" @click="configEdit('ruleForm')"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
    <popKnowledge ref="popKnowledge"></popKnowledge>
  </div>
</template>
<script>
import { getSearchArr, systemGetKey } from "@/api/entList.js";
import { editPsaa } from "@/api/login.js";
import { getAdminUserPlugInUrl } from "@/api/menu.js";
import { getMessageNum } from "@/api/workbench.js";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import popKnowledge from "@/components/pop/popKnowledge.vue";
require("@/utils/recorder.core.min");
const Recorder = require("@/utils/recorder.wav.min");
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js";
import Bus from "@/utils/bus";
import WS from "@/utils/Ws";

export default {
  components: { popKnowledge },
  data() {
    var validatePass = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入新密码"));
      } else {
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请再次输入密码"));
      } else if (value != this.ruleForm.newPass) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    var validateOldPass = (rule, value, callback) => {
      if (value == "") {
        callback(new Error("请输入原始密码"));
      } else {
        callback();
      }
    };
    return {
      path: `ws://${this.BASE_URL}/file/webSocket/`,
      listMenu: [],
      loadingMenu: false,
      socket: "",
      ent: false,
      msg: false,
      radioIschecked: "1",
      visible: false,
      ToHome: true,
      ToEnterprise: false,
      ToSupervision: false,
      ToSetting: false,
      iconHome: false,
      iconBell: false,
      badge: 12,
      state: "",
      dialogFormVisible: false,
      ruleForm: {
        oldPass: "",
        newPass: "",
        commitPass: "",
      },
      formLabelWidth: "150px",
      rules: {
        newPass: [{ validator: validatePass, trigger: "blur", required: true }],
        commitPass: [
          { validator: validatePass2, trigger: "blur", required: true },
        ],
        oldPass: [
          { validator: validateOldPass, trigger: "blur", required: true },
        ],
      },
      messageDef: "",
    };
  },
  props: {
    userName: String,
    isQiye: Boolean,
  },
  created() {},
  mounted() {
    this.getMenu();
    // this.getMessageDef();
    this.messageDef = setInterval(this.getMessageDef(), 30000);
    Bus.$on("readNews", () => {
      this.getMessageDef();
    });
    Bus.$on("newMessage", (data) => {
      this.msg = data;
    });
  },
  beforeDestroy() {
    // 销毁监听
    clearInterval(this.messageDef);
    this.socket.onclose = this.close;
  },
  methods: {
    isPopKnowledgeFn() {
      this.$refs.popKnowledge.open = true;
      this.$refs.popKnowledge.huaData = {};
    },
    largeScreen() {
      let isHttps = location.protocol == "https:"; //互联网环境
      const path = isHttps
        ? "https://" + this.BASE_URL
        : "http://" + this.BASE_URL;
      var tokenBig = this.$store.state.login.user.access_token;
      if (tokenBig) {
        if (this.$store.state.login.user.user_type == "ent") {
          var enterpId = this.$store.state.login.enterData.enterpId;
          window.open(
            path +
              "/egis/#/enterpriseInfo?type=ent&token=" +
              tokenBig +
              "&enterId=" +
              enterpId
          );
          // window.open(
          // "http://localhost:8778/#/enterpriseInfo?type=ent&token=" + tokenBig + '&enterId=' + enterpId
          // );
        } else {
          window.open(path + "/egis/#/home?token=" + tokenBig);
        }
      } else {
        this.$message("请先登录！");
      }
    },
    getMessageDef() {
      getMessageNum().then((res) => {
        this.msg = res.data.data;
      });
    },
    init() {
      if (typeof WebSocket === "undefined") {
        this.$message("您的浏览器不支持消息推送");
      } else {
        // 实例化socket
        // this.socket = new WebSocket(this.path + this.user.user_id);
        // // 监听socket连接
        // this.socket.onopen = this.open;
        // // 监听socket错误信息
        // this.socket.onerror = this.error;
        // // 监听socket消息
        // this.socket.onmessage = this.getMessage;
        let isHttps = location.protocol == "https:"; //互联网环境
        const path = isHttps
          ? "wss://" + this.BASE_URL + "/file/webSocket/"
          : "ws://" + this.BASE_URL + "/file/webSocket/";

        // `ws://${this.BASE_URL}/file/webSocket/`
        this.socket = new WS(path + this.user.user_id);
      }
    },
    open(msg) {
      console.log("socket连接成功");
    },
    error() {
      console.log("连接错误");
    },
    getMessage(msg) {
      this.msg = JSON.parse(msg.data);
      // console.log(this.msg);
    },
    send() {
      this.socket.send(params);
    },
    close() {
      console.log("socket已经关闭");
    },
    showPopover(bool, s) {
      setTimeout(() => {
        this.ent = bool;
      }, s);
    },
    showPopoverMsg(bool, s) {
      setTimeout(() => {
        this.msg = bool;
      }, s);
    },
    openUrl(url) {
      window.open(url);
    },
    toUrl(url) {
      this.$router.push(url);
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      console.log(item);
      this.state = item.enterpName;
      this.$router.push({
        name: "entManagement",
      });
      //存入vuex
      this.$store.commit("controler/updateEntId", item.enterpId);
    },
    //退出登陆
    loginOut() {
      // debugger;
      //清空所有的缓存
      window.localStorage.clear();
      window.sessionStorage.clear();
      //初始化vuex.login
      // this.$store.commit("login/updataUser", "");
      // this.$store.commit("login/updataUserDistCode", "");
      this.$store.state.login = {
        user: {},
        token: "",
        userDistCode: "",
        isXiaZuan: true,
        park: {},
        isShowDist: true,
        enterData: {},
      };
      this.$router.push({ name: "login" });
    },
    //修改密码
    editPass() {
      this.dialogFormVisible = true;
    },
    configEdit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          editPsaa({
            userId: this.$store.state.login.user.user_id,
            oldPassword: this.ruleForm.oldPass,
            newPassword: this.ruleForm.newPass,
          }).then((data) => {
            console.log(data);
            if (data.data.code == 0) {
              this.$message({
                type: "success",
                message: "修改成功",
              });
              this.dialogFormVisible = false;
            } else {
              this.$message({
                type: "info",
                message: "修改失败",
              });
            }
          });
        }
      });
    },
    closeDialog() {
      this.ruleForm = {};
    },
    getMenu() {
      this.loadingMenu = true;
      getAdminUserPlugInUrl()
        .then((res) => {
          // console.log(123);
          this.loadingMenu = false;
          this.listMenu = res.data.data;
          this.listMenu.push({
            icon: "cloud-download",
            name: "企业标准化",
            sign: "qyhbz",
            url: "downPdf",
          });
          res.data.data.forEach((item, index) => {
            if (item.sign == "yqtb") {
              this.listMenu.splice(index, 1);
            }
          });
          this.$store.commit("controler/updateUrlData", this.listMenu);
        })
        .catch((e) => {
          this.loadingMenu = false;
        });
    },
    async openNewUrl(url) {
      if (url == "downPdf") {
        var res = await systemGetKey({ key: "ent_standard_attach_id" });
        if (res.data.status == 200) {
          var configValue = res.data.data.configValue;
          let par = {
            fileId: configValue,
          };
          Attachmentdownload(par).then((res) => {
            downloadFuc(res);
          });
        }
      } else {
        window.open(url);
      }
    },
  },
  computed: {
    ...mapStateControler(["vuexEntId"]),
    ...mapStateLogin({
      user: (state) => state.user,
    }),
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
@import url("../../assets/css/nav.css");
/deep/ .el-popover__reference-wrapper {
  height: 52px;
  width: 22px;
}

.menuContainer {
  display: flex;
  justify-content: center;
  font-size: 14px;
  width: 100%;
  .menu {
    cursor: pointer;
    width: 100%;
    height: 35px;
    margin-top: 2.5px;
    margin-bottom: 2.5px;
    display: flex;
    justify-content: left;
    align-items: center;
    > .menuIcon {
      margin-right: 15px;
      font-size: 16px;
    }
    border-bottom: 1.5px solid #fff;
  }

  .menu:hover {
    color: #3d94ea;
    border-bottom: 1.5px solid #3d94ea;
  }
}
.menuContainer:nth-of-type(1) .menu {
  margin-top: 0px;
}

.header {
  background: url(/static/img/assets/img/top_bg.png) no-repeat center center;
  background-size: 100% 100%;
  position: absolute;
  z-index: 0;
  width: 100%;
  height: 140px;
  line-height: 68px;
  display: flex;
  justify-content: space-between;
}
.container {
  display: flex;
}
.mainPoint {
  font-size: 22px;
  color: white;
  margin-left: 20px;
  width: 40vw;
  height: 68px;
}
.accAnalyse {
  // display: inline-block;
  background: url(/static/img/assets/img/nationalEmblem.png) no-repeat center
    center;
  background-size: 100% 100%;
  width: 41px;
  height: 41px;
  position: relative;
  top: 10px;
  float: left;
  margin-right: 10px;
}
@media all and (max-width: 600px) {
  .mainPoint > div {
    display: none;
  }
}

.mainPoint > div {
  width: calc(100% - 61px);
  line-height: 68px;
  height: 68px;
  font-size: 24px;
  font-weight: bold;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  float: left;
}
.mainBtnUl {
  cursor: pointer;
  display: flex;
  font-size: 16px;
  text-align: center;
  margin-right: 20px;
}
.mainBtnLi {
  width: 130px;
  height: 60px;
  color: #ffffff;
}
.mainBtnLi:hover {
  color: yellow;
  width: 130px;
  height: 60px;
  background-color: rgba(13, 125, 220, 1);
}
.mainBtnLiActive {
  color: yellow;
  width: 130px;
  height: 60px;
  background-color: rgba(13, 125, 220, 1);
}
.mainRight {
  color: #ffffff;
  // font-size: 24px;
  cursor: pointer;
  //   width: 350px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-bottom: 65px;
}
.mainRight > img {
  margin: 0 15px;
}
.name {
  margin-left: 5px;
  font-size: 15px;
}
.nameBox {
  display: flex;
  align-items: center;
  margin-right: 15px;
  margin-left: 5px;
  // margin-right: 25px;
  // padding-left: 10px;
  //   width: 310px;
  cursor: pointer;
  font-size: 24px;
  // border-left: 0.5px solid rgba(223,231,250,1);
  span {
    // width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .icon_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  // &:hover {
  //   .iconPng {
  //     display: inline-block;
  //     width: 12px;
  //     height: 7px;
  //     background: url("/static/img/assets/img/zhankai.png") no-repeat center center;
  //     background-size: cover;
  //     margin-left: 9px;
  //     vertical-align: middle;

  //     transform: rotate(180deg);
  //     -ms-transform: rotate(180deg); /* IE 9 */
  //     -webkit-transform: rotate(180deg); /* Safari and Chrome */
  //     transition: 0.5s;
  //   }
  // }
}
.iconPng {
  display: inline-block;
  width: 12px;
  height: 7px;
  background: url("/static/img/assets/img/zhankai.png") no-repeat center center;
  background-size: cover;
  margin-left: 9px;
  vertical-align: middle;
  transition: 0.5s;
  transform: rotate(0);
}
.iconPngActive {
  display: inline-block;
  width: 12px;
  height: 7px;
  background: url("/static/img/assets/img/zhankai.png") no-repeat center center;
  background-size: cover;
  margin-left: 9px;
  vertical-align: middle;
  transform: rotate(180deg);
  -ms-transform: rotate(180deg); /* IE 9 */
  -webkit-transform: rotate(180deg); /* Safari and Chrome */
  transition: 0.5s;
}
.popoverBox {
  padding: 12px;
}
.icon {
  font-size: 25px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10px;
}
.msg {
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-around;
  width: 100%;
  height: 175px;
  > div {
    width: 100%;
    display: flex;
    justify-content: space-between;
    cursor: pointer;
    &:hover {
      color: rgb(57, 119, 234);
    }
  }
}
</style>
<style>
.mainBtnUl input {
  width: 310px;
  height: 40px;
  border-radius: 5px;
  background: #2e65d5;
  color: #fff;
  border: none;
}
.el-autocomplete-suggestion {
  width: 500px !important;
}
.popover {
  padding: 0;
}
</style>
