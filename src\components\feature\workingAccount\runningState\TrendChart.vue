<template>
  <div class="TrendChart">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span @click="gotoRunningState"
              ><a-icon type="home" theme="filled" class="icon" /> 运行状态分析
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>趋势分析</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="seach-part">
      <div class="l">
        <el-cascader
          size="mini"
          placeholder="请选择行政区划"
          :options="district"
          v-model="districtVal"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          @change="handleChange"
          :show-all-levels="true"
          style="width: 250px"
        ></el-cascader>
        <el-date-picker
          v-model="value1"
          size="mini"
          type="daterange"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="searchTime"
          unlink-panels
        >
        </el-date-picker>
        <el-button type="primary" size="mini" @click="seach">查询</el-button>
        <CA-button
          type="primary"
          size="mini"
          v-if="mode == '统计'"
          plain
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
      <!-- <el-button type="primary" size="mini">趋势分析</el-button>      -->
    </div>
    <div class="table-main">
      <div class="table-top">
        <div>
          <el-radio-group size="small" v-model="modees">
            <el-radio-button label="在线联网趋势"></el-radio-button>
            <el-radio-button label="视频运行趋势"></el-radio-button>
            <el-radio-button
              v-if="isShowDist && mode == '图表'"
              label="系统平均在线率"
            ></el-radio-button>
            <el-radio-button
              v-if="isShowDist && mode == '图表'"
              label="视频平均在线率"
            ></el-radio-button>

            <el-radio-button
              v-if="isShowDist && mode == '图表'"
              label="超量程趋势"
            ></el-radio-button>
          </el-radio-group>
        </div>
        <CA-RadioGroup
          class="radio"
          v-model="mode"
          v-if="tagsType ? true : false"
          backgroundColor="#F1F6FF"
          border="1px solid rgba(57, 119, 234, 0.2)"
        >
          <CA-Radio
            :label="{
              src: '../../../static/img/tubiao_icon.png',
              style: 'width:15px;height:15px',
            }"
            :labelTwo="{
              src: '../../../static/img/tubiao_icon_hover.png',
              style: 'width:15px;height:15px',
            }"
            bgColorActive="#409eff"
            value="图表"
          ></CA-Radio>
          <CA-Radio
            :label="{
              src: '../../../static/img/liebiao_icon.png',
              style: 'width:15px;height:15px',
            }"
            :labelTwo="{
              src: '../../../static/img/liebiao_icon_hover.png',
              style: 'width:15px;height:15px',
            }"
            value="统计"
            bgColorActive="#409eff"
          ></CA-Radio>
        </CA-RadioGroup>
      </div>
      <div v-show="showtable">
        <div class="table" v-if="!videoFlag">
          <el-table
            :data="tableData"
            v-loading="loading"
            style="width: 100%"
            ref="multipleTable"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            @selection-change="handleSelectionChange"
            :default-sort="{ prop: 'date', order: 'descending' }"
            @select="select"
            @select-all="select"
          >
            <el-table-column type="selection" width="50" align="center">
            </el-table-column>
            <el-table-column label="日期" align="center" prop="onlineDate">
            </el-table-column>
            <el-table-column label="行政区划" align="center" prop="areaName">
            </el-table-column>
            <el-table-column
              label="应接入企业数"
              align="center"
              prop="shouldLinkedNumber"
            >
            </el-table-column>
            <el-table-column
              label="已接入企业数"
              align="center"
              prop="linkedNumber"
            ></el-table-column>
            <el-table-column label="接入率" align="center" prop="linkedRate">
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.linkedRate + "%" }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="在线企业数"
              align="center"
              prop="onlineCompanyNum"
            ></el-table-column>
            <el-table-column
              label="离线企业数"
              align="center"
              prop="offlineCompanyNum"
            ></el-table-column>
            <el-table-column
              label="企业在线率"
              align="center"
              prop="onlineCompanyRate"
            >
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.onlineCompanyRate + "%" }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="接入指标数"
              align="center"
              prop="linkedTargerNumber"
            ></el-table-column>
            <el-table-column
              label="在线质量"
              align="center"
              prop="onlineQuality"
              v-if="showChartType != 1"
            >
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.onlineQuality + "%" }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="table" v-else>
          <el-table
            :data="tableDataes"
            v-loading="loading"
            style="width: 100%"
            ref="multipleTable"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            @selection-change="handleSelectionChange"
            :default-sort="{ prop: 'date', order: 'descending' }"
            @select="selectes"
            @select-all="selectes"
          >
            <el-table-column type="selection" width="50" align="center">
            </el-table-column>
            <el-table-column label="日期" align="center" prop="onlineDate">
            </el-table-column>
            <el-table-column label="行政区划" align="center" prop="areaName">
            </el-table-column>
            <el-table-column
              label="应接入企业数"
              align="center"
              prop="shouldAccessNum"
            >
            </el-table-column>
            <el-table-column
              label="已接入企业数"
              align="center"
              prop="yetAccessNum"
            ></el-table-column>
            <el-table-column label="接入率" align="center" prop="accessRate">
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.accessRate + "%" }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="在线企业数"
              align="center"
              prop="onlineNum"
            ></el-table-column>
            <el-table-column
              label="离线企业数"
              align="center"
              prop="offlineNum"
            ></el-table-column>
            <el-table-column
              label="企业在线率"
              align="center"
              prop="onlineRate"
            >
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.onlineRate + "%" }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="监控点位数"
              align="center"
              prop="monitorPoint"
            ></el-table-column>
            <el-table-column
              label="在线质量"
              align="center"
              prop="onlineQualityRate"
            >
              <template slot-scope="{ row, column, $index, store }">
                <div>
                  <span>{{ row.onlineQualityRate + "%" }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="pagination">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page.sync="current"
            layout="total, prev, pager, next"
            :page-size="size"
            :total="total"
            background
          >
          </el-pagination>
        </div>
      </div>
      <div v-show="!showtable" v-loading="echartDataLoading">
        <div v-show="showChartType == 1" id="echart"></div>
        <div v-show="showChartType == 2" id="myChartedes"></div>
        <div v-show="showChartType == 3" id="myCharteded"></div>
        <div v-show="showChartType == 4" id="myChartededes"></div>
        <div v-show="showChartType == 5" id="myChartLiangC"></div>
      </div>
    </div>
  </div>
</template>
<script>
// import {getRunningStateTrendList,getCommitRiskEchartData,getSaftyTrendList,getSafetyTrendExportExcel} from "@/api/workingAcc";
import {
  getRunningStateTrendList,
  getRunningStateVideoTrendList,
  getRunningStateTrendOnlineLv,
  getAverageVideoOnlineRate,
  integrityDataSearch,
  getExportVideoGroupDate,
  getExportCimRiskGroupDate,
} from "@/api/workingAcc";
import { getDistrictUser } from "@/api/entList";
import { parseTime } from "@/utils/index";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  data() {
    return {
      activeName: "first",
      tableData: [],
      tableDataes: [],
      mode: "图表",
      modees: "在线联网趋势",
      showChartType: 1,
      showtable: false,
      currentPage: 1,
      widthBox: 1100,
      value1: "",
      starTime: "",
      endTime: "",
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (168 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime(),
      ],
      districtVal: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      echartData1: [],
      echartDataLoading: false,
      echartData2: [],
      echartData3: [],
      echartData4: [],
      echartData5: [],
      loading: true,
      current: 1,
      size: 999,
      total: 0,
      videoFlag: false,
      tagsType: true,
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  methods: {
    gotoRunningState() {
      let data = false;
      this.$emit("RunningState", data);
      // this.$router.go(0);
    },
    handleChange(value) {
      if (value && value.length > 0) {
        this.distCode = value[value.length - 1];
      }
    },
    seach() {
      if (this.modees == "在线联网趋势") {
        this.getData();
        this.videoFlag = false;
        this.tagsType = true;
      } else if (this.modees == "视频运行趋势") {
        this.getDataes();
        this.videoFlag = true;
        this.tagsType = true;
      } else if (this.modees == "系统平均在线率") {
        this.getData();
        this.getOnlineLv();
        this.videoFlag = false;
        this.tagsType = false;
      } else if (this.modees == "视频平均在线率") {
        this.getDataes();
        this.averageVideoOnlineRate();
        this.videoFlag = true;
        this.tagsType = false;
      } else if (this.modees == "超量程趋势") {
        this.averageVideoOnlineRate5();
        this.tagsType = false;
        this.videoFlag = false;
      }
    },
    getData() {
      this.echartDataLoading = true;
      this.loading = true;
      getRunningStateTrendList({
        distCode: this.districtVal,
        sort: "ASC",
        startDate: this.starTime,
        endDate: this.endTime,
        current: this.current,
        size: this.size,
      }).then((res) => {
        this.loading = false;
        this.echartData1 = res.data.data.records;
        this.tableData = res.data.data.records;
        this.echartDataLoading = false;
        this.total = res.data.data.total;
        this.$setEchart("echart", 250, 250);
      });
    },
    getDataes() {
      this.echartDataLoading = true;
      this.loading = true;
      getRunningStateVideoTrendList({
        distCode: this.districtVal,
        sort: "DESC",
        startDate: this.starTime,
        endDate: this.endTime,
        current: this.current,
        size: this.size,
      }).then((res) => {
        this.loading = false;
        this.echartData2 = res.data.data.records;
        this.tableDataes = res.data.data.records;
        this.echartDataLoading = false;
        this.total = res.data.data.total;
      });
    },
    averageVideoOnlineRate() {
      this.echartDataLoading = true;
      getAverageVideoOnlineRate({
        distCode: this.districtVal,
        startDate: this.starTime,
        endDate: this.endTime,
      }).then((res) => {
        this.echartData4 = res.data.data.records;
        this.echartDataLoading = false;
      });
    },
    averageVideoOnlineRate5() {
      this.echartDataLoading = true;
      integrityDataSearch({
        distCode: this.districtVal,
        startDate: this.starTime,
        endDate: this.endTime,
      }).then((res) => {
        // debugger
        this.echartData5 = res.data.data;
        this.echartDataLoading = false;
      });
    },
    getOnlineLv() {
      this.echartDataLoading = true;
      getRunningStateTrendOnlineLv({
        distCode: this.districtVal * 1,
        startDate: this.starTime,
        endDate: this.endTime,
      }).then((res) => {
        this.echartData3 = res.data.data.records;
        this.echartDataLoading = false;
      });
    },
    // getSaftyTrendListData() {
    //   this.loading = true;
    //   getSaftyTrendList({
    //     distCode: this.districtVal,
    //     startTime: this.starTime,
    //     endTime: this.endTime,
    //   }).then((res) => {
    //     this.loading = false;
    //     this.tableData = res.data.data;
    //   });
    // },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    getTime() {
      this.value1 = [this.get_date(-6), this.get_date(0)];
      this.starTime = this.get_date(-6);
      this.endTime = this.get_date(0);
    },
    get_date(num) {
      var date = new Date(); //获取今天的时间
      var today =
        date.getFullYear() + "-" + (date.getMonth() + 1) + "-" + date.getDate();
      var new_date = new Date(date);
      new_date.setDate(date.getDate() + num);
      // num为正数时，获取后num天 ，为负数时，获取前num天，0表示今天。
      var new_day =
        new_date.getFullYear().toString() +
        "-" +
        (new_date.getMonth() + 1 >= 10
          ? new_date.getMonth() + 1
          : "0" + (new_date.getMonth() + 1)
        ).toString() +
        "-" +
        (new_date.getDate() >= 10
          ? new_date.getDate()
          : "0" + new_date.getDate()
        ).toString();
      return new_day;
    },
    // 导出
    exportExcel() {
      if (this.videoFlag) {
        getExportVideoGroupDate({
          distCode: this.districtVal,
          startDate: this.starTime,
          endDate: this.endTime,
          dateList: this.selection,
          sort: "DESC",
        }).then((response) => {
          // 处理返回的文件流
          // console.log(response);
          if (response.status == 200) {
            this.$message({
              message: "导出成功",
              type: "success",
            });
          } else {
            this.$message.error("导出失败");
          }
          const blob = new Blob([response.data], {
            type: "application/vnd.ms-excel",
          });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "运行状态分析-视频趋势运行列表" + timestamp + ".xls";
          //下载文件
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
          }, 0);
        });
      } else {
        getExportCimRiskGroupDate({
          distCode: this.districtVal,
          startDate: this.starTime,
          endDate: this.endTime,
          dateList: this.selection,
          sort: "DESC",
        }).then((response) => {
          // 处理返回的文件流
          // console.log(response);
          if (response.status == 200) {
            this.$message({
              message: "导出成功",
              type: "success",
            });
          } else {
            this.$message.error("导出失败");
          }
          const blob = new Blob([response.data], {
            type: "application/vnd.ms-excel",
          });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "运行状态分析-在线联网分析列表" + timestamp + ".xls";
          //下载文件
          const link = document.createElement("a");
          link.href = URL.createObjectURL(blob);
          link.download = filename;
          document.body.appendChild(link);
          link.click();
          window.setTimeout(function () {
            URL.revokeObjectURL(blob);
            document.body.removeChild(link);
          }, 0);
        });
      }
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].onlineDate;
      }
    },
    selectes(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].onlineDate;
      }
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      this.current = val;
      if (this.modees == "在线联网趋势") {
        this.getData();
        this.videoFlag = false;
      } else if (this.modees == "视频运行趋势") {
        this.getDataes();
        this.videoFlag = true;
      } else if (this.modees == "系统平均在线率") {
        this.getData();
        this.getOnlineLv();
        this.videoFlag = false;
      } else if (this.modees == "视频平均在线率") {
        this.getDataes();
        this.averageVideoOnlineRate();
        this.videoFlag = true;
      } else if (this.modees == "超量程趋势") {
        this.averageVideoOnlineRate5();
        this.videoFlag = false;
      }
    },
    handleCurrentChange(val) {
      this.current = val;
      this.getData();
    },
    formatterTip(params) {
      // console.log(params,'111111111111')
      //移除重复的数据
      // for (var i = 0; i < params.length; i++) {
      //   for (var j = params.length - 1; j > i; j--) {
      //     if (params[j].data == params[i].data) {
      //       params.splice(j, 1);
      //       break;
      //     }
      //   }
      // }

      let tipData=params.slice(-4)

      var tip = "";
      // console.log(tipData,'params00000000000-------00000')
      for (var i = 0; i < tipData.length; i++) {
        //这里是自己定义样式， params[i].marker 表示是否显示左边的那个小圆圈
        // if (params[i].value != 0) {
        // }
        // debugger
        if(tipData[i].seriesType == 'bar'){
          tip =
            tip +
            tipData[i].marker +
            tipData[i].seriesName +
            ":" +
            tipData[i].value + '个' +
            "<br/>";
        }else{
          tip =
            tip +
            tipData[i].marker +
            tipData[i].seriesName +
            ":" +
            tipData[i].value + "%" +
            "<br/>";
        }

      }

      return tip;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    mode(newValue, oldValue) {
      if (newValue == "统计") {
        this.showtable = true;
      } else {
        this.showtable = false;
      }
    },
    modees: {
      handler(newValue, oldValue) {
        // debugger;
        this.current = 1;
        if (newValue == "在线联网趋势") {
          this.tagsType = true;
          this.showChartType = 1;
          this.videoFlag = false;
          this.getData();
          this.$setEchart("echart", 250, 250);
        } else if (newValue == "视频运行趋势") {
          this.tagsType = true;
          this.showChartType = 2;
          this.videoFlag = true;
          this.getDataes();
          this.$nextTick(function () {
            this.$setEchart("myChartedes", 250, 250);
          });
        } else if (newValue == "系统平均在线率") {
          this.tagsType = false;
          this.showChartType = 3;
          this.videoFlag = false;
          this.getOnlineLv();
          this.$nextTick(function () {
            this.$setEchart("myCharteded", 250, 250);
          });
        } else if (newValue == "视频平均在线率") {
          this.tagsType = false;
          this.showChartType = 4;
          this.averageVideoOnlineRate();
          this.videoFlag = true;
          this.$nextTick(function () {
            this.$setEchart("myChartededes", 250, 250);
          });
        } else if (newValue == "超量程趋势") {
          //myChartLiangC
          this.tagsType = false;
          this.showChartType = 5;
          this.averageVideoOnlineRate5();
          this.videoFlag = false;
          this.$nextTick(function () {
            this.$setEchart("myChartLiangC", 250, 250);
          });
        }
        console.log(this.tagsType);
      },
      deep: true,
    },
    echartData1(newValue, oldValue) {
      let myChart = this.$echarts.init(document.getElementById("echart"));
      var option;
      var xAxisData = [];
      var chengnuoNum = [];
      var chengnuoLv = [];
      var option = {
        color: ["#1890ff", "#1f9"],
        grid: {
          top: "15%",
          right: "5%",
          left: "5%",
          bottom: "10%",
        },
        // tooltip: {
        //   trigger: "axis",
        //   extraCssText: "width:180px;height:75px;overflow: hidden;",
        //   formatter: function (a, b) {
        //     var str = { a } + { b };
        //     return str;
        //   },
        // },
        tooltip: {
          show: true,
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
          formatter: (params) => {
            console.log(params,'params000000000111111')
            // debugger
            // let html=''
            // console.log(params,'paramsparamsparamsparams')
            //   for(var i=0;i<params.length;i++){
            //     html =   params[i].name +
            //   "<br>" +
            //   params[i].marker +
            //   params[i].seriesName +
            //   ": " +
            //   params[i].value +
            //   " %" +
            //   "<br>" +
            //   params[i].marker +
            //   params[i].seriesName +
            //   ": " +
            //   params[1].value +
            //   " 个"
            //   }
            // console.log(params)
            let seriesName = "";
            let value = "";
            let marker = "";
            for (var i = 0; i < params.length; i++) {
              if (params[i].componentSubType == "bar") {
                seriesName = params[i].seriesName;
                value = params[i].value;
                marker = params[i].marker;
              }
            }
            return (
              params[params.length - 1].name +
              "<br>" +
              params[params.length - 1].marker +
              params[params.length - 1].seriesName +
              ": " +
              params[params.length - 1].value +
              " %" +
              "<br>" +
              marker +
              seriesName +
              ": " +
              value +
              " 个"
            );
          },
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        legend: {
          top: 5,
          left: 250,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 14,
            color: "#000",
            padding: [3, 8, 0, 2],
          },
          data: ["系统在线率%", "物联监测在线企业"],
        },

        xAxis: [
          {
            type: "category",
            data: [],
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              color: "#000",
              textStyle: {
                fontSize: 10,
              },
              fontFamily: "LCDEF",
            },
          },
        ],
        yAxis: [
          {
            name: "物联监测在线企业(个)",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            type: "value",
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#384267",
              },
            },
            axisLabel: {
              color: "#545C65",
              fontSize: "10",
            },
          },
          {
            type: "value",
            name: "系统在线率 (%)",
            position: "right",
            axisLabel: {
              formatter: "{value} %",
              color: "#545C65",
            },
            max: 100,
            splitLine: {
              show: false,
            },
            // axisPointer: {
            //   type: 'none',
            //   label: {
            //     backgroundColor: '#f00'
            //   }
            // },
            axisPointer: {
              show: true,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#545C65",
              },
            },
          },
        ],
        dataZoom: [
          {
            type: "slider", //数据滑块
            start: 0,
            minSpan: 8, //5min
            // minSpan:16,   //10min
            // minSpan:24,   //15min
            // minSpan:50,   //30min
            dataBackground: {
              lineStyle: {
                // color:'#95BC2F'
              },
              areaStyle: {
                // color:'#95BC2F',
                opacity: 1,
              },
            },
            // fillerColor:'rgba(255,255,255,.6)'
          },
          {
            type: "inside", //使鼠标在图表中时滚轮可用
          },
        ],
        series: [
          {
            type: "bar",
            name: "物联监测在线企业",
            barWidth: "20",
            data: [],
            itemStyle: {
              color: "#3299FF",
            },
            // itemStyle: {
            //   normal: {
            //     color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
            //       {
            //         offset: 0,
            //         color: "#42B7FD",
            //       },
            //       {
            //         offset: 1,
            //         color: "#3299FF",
            //       },
            //     ]),
            //   },
            // },
            // 取消鼠标悬停时的放大效果
            // emphasis: {
            //   scale: false,
            // },
          },
          {
            type: "line",
            name: "系统在线率",
            yAxisIndex: 1,
            itemStyle: {
              color: "#20C578",
            },
            symbolSize: 10,
            symbol: "circle",
            data: [],
          },
        ],
      };
      for (let i = 0; i < this.echartData1.length; i++) {
        xAxisData.push(this.echartData1[i].onlineDate);
        chengnuoNum.push(this.echartData1[i].onlineCompanyNum);
        chengnuoLv.push(this.echartData1[i].onlineCompanyRate);
      }
      option.xAxis[0].data = xAxisData;
      option.series[0].data = chengnuoNum;
      option.series[1].data = chengnuoLv;
      option && myChart.setOption(option);
      // console.log( option.series[0].data,' option.series[0].data')
    },

    echartData2(newValue, oldValue) {
      let myChart = this.$echarts.init(document.getElementById("myChartedes"));
      var option;
      var xAxisData = [];
      var chengnuoNum = [];
      var chengnuoLv = [];

      for (let i = 0; i < this.echartData2.length; i++) {
        xAxisData.push(this.echartData2[i].onlineDate);
        chengnuoNum.push(this.echartData2[i].onlineNum);
        chengnuoLv.push(this.echartData2[i].onlineRate);
      }
      // option.xAxis[0].data = xAxisData;
      // option.series[0].data = chengnuoNum;
      // option.series[1].data = chengnuoLv;

      var option = {
        color: ["#1890ff", "#1f9"],
        grid: {
          top: "15%",
          right: "5%",
          left: "5%",
          bottom: "10%",
        },
        // tooltip: {
        //   trigger: "axis",
        //   extraCssText: "width:180px;height:75px;overflow: hidden;",
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
          formatter: (params) => {
            // return (
            //   params[0].name +
            //   "<br>" +
            //   params[0].marker +
            //   params[0].seriesName +
            //   ": " +
            //   params[0].value +
            //   " %" +
            //   "<br>" +
            //   params[1].marker +
            //   params[1].seriesName +
            //   ": " +
            //   params[1].value +
            //   " 个"
            // );
            let seriesName = "";
            let value = "";
            let marker = "";
            for (var i = 0; i < params.length; i++) {
              if (params[i].componentSubType == "bar") {
                seriesName = params[i].seriesName;
                value = params[i].value;
                marker = params[i].marker;
              }
            }
            return (
              params[params.length - 1].name +
              "<br>" +
              params[params.length - 1].marker +
              params[params.length - 1].seriesName +
              ": " +
              params[params.length - 1].value +
              " %" +
              "<br>" +
              marker +
              seriesName +
              ": " +
              value +
              " 个"
            );
          },
        },

        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        legend: {
          top: 5,
          left: 250,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 14,
            color: "#000",
            padding: [3, 8, 0, 2],
          },
          data: ["视频在线率", "视频在线企业"],
        },
        xAxis: [
          {
            type: "category",
            data: xAxisData,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              color: "#000",
              textStyle: {
                fontSize: 10,
              },
              fontFamily: "LCDEF",
            },
          },
        ],
        yAxis: [
          {
            name: "视频在线企业(个)",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            type: "value",
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#384267",
              },
            },
            axisLabel: {
              color: "#545C65",
              fontSize: "10",
            },
          },
          {
            type: "value",
            name: "视频在线率 (%)",
            position: "right",
            axisLabel: {
              formatter: "{value} %",
              color: "#545C65",
            },
            max: 100,
            splitLine: {
              show: false,
            },
            axisPointer: {
              show: true,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#545C65",
              },
            },
          },
        ],
        dataZoom: [
          {
            type: "slider", //数据滑块
            start: 0,
            minSpan: 8, //5min
            // minSpan:16,   //10min
            // minSpan:24,   //15min
            // minSpan:50,   //30min
            dataBackground: {
              lineStyle: {
                // color:'#95BC2F'
              },
              areaStyle: {
                // color:'#95BC2F',
                opacity: 1,
              },
            },
            // fillerColor:'rgba(255,255,255,.6)'
          },
          {
            type: "inside", //使鼠标在图表中时滚轮可用
          },
        ],
        series: [
          {
            type: "bar",
            name: "视频在线企业",
            barWidth: "20",
            data: chengnuoNum,
            itemStyle: {
              color: "#3299FF",
            },
            // itemStyle: {
            //   normal: {
            //     color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
            //       {
            //         offset: 0,
            //         color: "#42B7FD",
            //       },
            //       {
            //         offset: 1,
            //         color: "#3299FF",
            //       },
            //     ]),
            //   },
            // },
          },
          {
            type: "line",
            name: "视频在线率",
            yAxisIndex: 1,
            itemStyle: {
              color: "#20C578",
            },
            symbolSize: 10,
            symbol: "circle",
            data: chengnuoLv,
          },
        ],
      };
      // for (let i = 0; i < this.echartData2.length; i++) {
      //   xAxisData.push(this.echartData2[i].onlineDate);
      //   chengnuoNum.push(this.echartData2[i].onlineNum);
      //   chengnuoLv.push(this.echartData2[i].onlineRate);
      // }
      // option.xAxis[0].data = xAxisData;
      // option.series[0].data = chengnuoNum;
      // option.series[1].data = chengnuoLv;
      option && myChart.setOption(option);
    },
    echartData3(newValue, oldValue) {
      let myChart = this.$echarts.init(document.getElementById("myCharteded"));
      var option;
      var color = ["#8B5CFF", "#00CA69"];
      var xAxisData = [];
      var yAxisData1 = [];
      const hexToRgba = (hex, opacity) => {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
          )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
        }
        return rgbaColor;
      };
      var option = {
        backgroundColor: "#fff",
        color: color,

        legend: {
          top: 20,
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let html = "";
            params.forEach((v) => {
              html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      color[v.componentIndex]
                    };"></span>
                    ${v.seriesName} ${v.name}
                    <span style="color:${
                      color[v.componentIndex]
                    };font-weight:700;font-size: 18px;margin-left:5px">${
                v.value
              }</span>
                    %`;
            });
            return html;
          },
          extraCssText:
            "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "#ffffff",
              shadowColor: "rgba(225,225,225,1)",
              shadowBlur: 5,
            },
          },
        },
        grid: {
          left: "3%",
          right: "3%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              interval: 0,
              formatter: function (value) {
                return value.length > 6 ? value.slice(0, 6) + "..." : value;
              },
              rotate: 40,
              textStyle: {
                color: "#333",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#D9D9D9",
              },
            },
            data: xAxisData,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "系统平均在线率（%）",
            axisLabel: {
              textStyle: {
                color: "#666",
              },
            },
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
              lineHeight: 40,
            },
            // 分割线
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#E9E9E9",
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            // name: "2018",
            name: "系统平均在线率",
            type: "line",
            smooth: true,
            symbolSize: 8,
            zlevel: 3,
            lineStyle: {
              normal: {
                color: color[0],
                shadowBlur: 3,
                shadowColor: hexToRgba(color[0], 0.5),
                shadowOffsetY: 8,
              },
            },
            symbol: "circle", //数据交叉点样式
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: hexToRgba(color[0], 0.3),
                    },
                    {
                      offset: 1,
                      color: hexToRgba(color[0], 0.1),
                    },
                  ],
                  false
                ),
                shadowColor: hexToRgba(color[0], 0.1),
                shadowBlur: 10,
              },
            },
            data: yAxisData1,
          },
        ],
      };
      for (let i = 0; i < this.echartData3.length; i++) {
        xAxisData.push(this.echartData3[i].areaName);
        yAxisData1.push(
          this.echartData3[i].averageOnlineRate
            ? this.echartData3[i].averageOnlineRate
            : 0
        );
      }
      option.xAxis.data = xAxisData;
      option.series[0].data = yAxisData1;
      option && myChart.setOption(option);
    },
    echartData4(newValue, oldValue) {
      let myChart = this.$echarts.init(
        document.getElementById("myChartededes")
      );
      var option;
      var color = ["#00CA69", "#8B5CFF"];
      var xAxisData = [];
      var yAxisData1 = [];
      const hexToRgba = (hex, opacity) => {
        let rgbaColor = "";
        let reg = /^#[\da-f]{6}$/i;
        if (reg.test(hex)) {
          rgbaColor = `rgba(${parseInt("0x" + hex.slice(1, 3))},${parseInt(
            "0x" + hex.slice(3, 5)
          )},${parseInt("0x" + hex.slice(5, 7))},${opacity})`;
        }
        return rgbaColor;
      };
      var option = {
        backgroundColor: "#fff",
        color: color,
        legend: {
          top: 20,
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        // tooltip: {
        //   trigger: "axis",
        //   axisPointer: {
        //     type: "shadow",
        //     shadowStyle: {
        //       color: "rgba(0,27,251,0.03)",
        //     },
        //   },
        //   textStyle: {
        //     color: "#fff",
        //   },
        //   formatter: (params) => {
        //     //debugger;
        //     return (
        //       params[0].name +
        //       "<br>" +
        //       params[0].marker +
        //       params[0].seriesName +
        //       ": " +
        //       params[0].value +
        //       " %" +
        //       "<br>" +
        //       params[1].marker +
        //       params[1].seriesName +
        //       ": " +
        //       params[1].value +
        //       " 个" +
        //       "<br>" +
        //       params[2].marker +
        //       params[2].seriesName +
        //       ": " +
        //       params[2].value +
        //       " %" +
        //       "<br>" +
        //       params[3].marker +
        //       params[3].seriesName +
        //       ": " +
        //       params[3].value +
        //       " 个"
        //     );
        //   },
        // },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            let html = "";
            params.forEach((v) => {
              html += `<div style="color: #666;font-size: 14px;line-height: 24px">
                    <span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
                      color[v.componentIndex]
                    };"></span>
                    ${v.seriesName} ${v.name}
                    <span style="color:${
                      color[v.componentIndex]
                    };font-weight:700;font-size: 18px;margin-left:5px">${
                v.value
              }</span>
                    %`;
            });
            return html;
          },
          extraCssText:
            "background: #fff; border-radius: 0;box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);color: #333;",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "#ffffff",
              shadowColor: "rgba(225,225,225,1)",
              shadowBlur: 5,
            },
          },
        },
        grid: {
          // top: 'middle',
          left: "3%",
          right: "4%",
          bottom: "3%",
          top: "10%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            axisLabel: {
              interval: 0,
              formatter: function (value) {
                return value.length > 6 ? value.slice(0, 6) + "..." : value;
              },
              rotate: 40,
              textStyle: {
                color: "#333",
              },
            },
            axisLine: {
              lineStyle: {
                color: "#D9D9D9",
              },
            },
            data: xAxisData,
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "视频平均在线率（%）",
            axisLabel: {
              textStyle: {
                color: "#666",
              },
            },
            nameTextStyle: {
              color: "#666",
              fontSize: 12,
              lineHeight: 40,
            },
            // 分割线
            splitLine: {
              lineStyle: {
                type: "dashed",
                color: "#E9E9E9",
              },
            },
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        ],
        series: [
          {
            // name: "2018",
            name: "视频平均在线率",
            type: "line",
            smooth: true,
            symbolSize: 8,
            zlevel: 3,
            lineStyle: {
              normal: {
                color: color[0],
                shadowBlur: 3,
                shadowColor: hexToRgba(color[0], 0.5),
                shadowOffsetY: 8,
              },
            },
            symbol: "circle", //数据交叉点样式
            areaStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: hexToRgba(color[0], 0.3),
                    },
                    {
                      offset: 1,
                      color: hexToRgba(color[0], 0.1),
                    },
                  ],
                  false
                ),
                shadowColor: hexToRgba(color[0], 0.1),
                shadowBlur: 10,
              },
            },
            data: yAxisData1,
          },
        ],
      };
      for (let i = 0; i < this.echartData4.length; i++) {
        xAxisData.push(this.echartData4[i].areaName);
        yAxisData1.push(
          this.echartData4[i].averageOnlineRate
            ? this.echartData4[i].averageOnlineRate
            : 0
        );
      }
      option.xAxis.data = xAxisData;
      option.series[0].data = yAxisData1;
      // console.log(option);
      option && myChart.setOption(option);
    },
    echartData5(newValue, oldValue) {
      let myChart = this.$echarts.init(
        document.getElementById("myChartLiangC")
      );
      var option;
      var xAxisData = [];
      var offLineNumData = []; //超过6小时未在线企业平均数量
      var offLineRateData = []; //超过6小时未在线企业平均率
      var outRangeNumData = []; //监测数据超量程企业平均数量
      var outRangeRateData = []; //监测数据超量程企业平均率
      for (let i = 0; i < this.echartData5.length; i++) {
        xAxisData.push(this.echartData5[i].countDate);
        offLineNumData.push(this.echartData5[i].offLineNum);
        offLineRateData.push(this.echartData5[i].offLineRate);
        outRangeNumData.push(this.echartData5[i].outRangeNum);
        outRangeRateData.push(this.echartData5[i].outRangeRate);
      }
      // option.xAxis[0].data = xAxisData;
      var option = {
        color: ["#1890ff", "#1f9"],
        grid: {
          top: "15%",
          right: "5%",
          left: "5%",
          bottom: "10%",
        },
        // tooltip: {
        //   trigger: "axis",
        //   extraCssText: "height:135px;overflow: hidden;",
        // },
        // tooltip: {
        //   trigger: "axis",
        //   // axisPointer: {
        //   //   type: "shadow", //line  shadow
        //   //   shadowStyle: {
        //   //     color: "rgba(0,27,251,0.03)",
        //   //   },
        //   // },
        //   // textStyle: {
        //   //   color: "#fff",
        //   // },
        //   formatter: (params) => {
        //     // debugger
        //     console.log(console.log(params, "paramsparamsparamsparams-----"));
        //     return this.formatterTip(params);
        //     // return (
        //     //   params[0].name +
        //     //   "<br>" +
        //     //   params[0].marker +
        //     //   params[0].seriesName +
        //     //   ": " +
        //     //   params[0].value +
        //     //   " %" +
        //     //   "<br>" +
        //     //   params[1].marker +
        //     //   params[1].seriesName +
        //     //   ": " +
        //     //   params[1].value +
        //     //   " 个" +
        //     //   "<br>" +
        //     //   params[2].marker +
        //     //   params[2].seriesName +
        //     //   ": " +
        //     //   params[2].value +
        //     //   " %" +
        //     //   "<br>" +
        //     //   params[3].marker +
        //     //   params[3].seriesName +
        //     //   ": " +
        //     //   params[3].value +
        //     //   " 个"
        //     // );
        //   },
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
            shadowStyle: {
              color: "rgba(0,27,251,0.03)",
            },
          },
          textStyle: {
            color: "#fff",
          },
          formatter: (params) => {
            // debugger
            console.log(params,'paramsparamsparams=======')
            return this.formatterTip(params);
            // return (
            //   params[0].name +
            //   "<br>" +
            //   params[0].marker +
            //   params[0].seriesName +
            //   ": " +
            //   params[0].value +
            //   " %" +
            //   "<br>" +
            //   params[1].marker +
            //   params[1].seriesName +
            //   ": " +
            //   params[1].value +
            //   " 个" +
            //   "<br>" +
            //   params[2].marker +
            //   params[2].seriesName +
            //   ": " +
            //   params[2].value +
            //   " %" +
            //   "<br>" +
            //   params[3].marker +
            //   params[3].seriesName +
            //   ": " +
            //   params[3].value +
            //   " 个"
            // );
          },
        },
        toolbox: {
          feature: {
            saveAsImage: {},
          },
        },
        legend: {
          top: 5,
          left: 250,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 14,
            color: "#000",
            padding: [3, 8, 0, 2],
          },
          data: [
            "超过6小时未在线企业平均比例",
            "超过6小时未在线企业平均数量",
            "监测数据超量程企业平均比例",
            "监测数据超量程企业平均数量",
          ],
        },

        xAxis: [
          {
            type: "category",
            data: xAxisData,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: true,
              color: "#000",
              textStyle: {
                fontSize: 10,
              },
              fontFamily: "LCDEF",
            },
          },
        ],
        yAxis: [
          {
            name: "平均数量",
            axisLine: {
              show: false,
              lineStyle: {
                color: "#545C65",
              },
            },
            type: "value",
            axisTick: {
              show: false,
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: "#384267",
              },
            },
            axisLabel: {
              color: "#545C65",
              fontSize: "10",
            },
          },

          {
            type: "value",
            name: "平均比例",
            position: "right",
            axisLabel: {
              formatter: "{value} %",
              color: "#545C65",
            },
            max: 100,
            splitLine: {
              show: false,
            },
            axisPointer: {
              show: true,
            },
            axisTick: {
              show: false,
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: "#545C65",
              },
            },
          },
        ],
        dataZoom: [
          {
            type: "slider", //数据滑块
            start: 0,
            minSpan: 8, //5min
            // minSpan:16,   //10min
            // minSpan:24,   //15min
            // minSpan:50,   //30min
            dataBackground: {
              lineStyle: {
                // color:'#95BC2F'
              },
              areaStyle: {
                // color:'#95BC2F',
                opacity: 1,
              },
            },
            // fillerColor:'rgba(255,255,255,.6)'
          },
          {
            type: "inside", //使鼠标在图表中时滚轮可用
          },
        ],
        series: [
          {
            type: "bar",
            name: "超过6小时未在线企业平均数量",
            barWidth: "20",
            data: offLineNumData,
            itemStyle: {
               color: "#42B7FD",
              // normal: {
              //   color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
              //     {
              //       offset: 0,
              //       color: "#42B7FD",
              //     },
              //     {
              //       offset: 1,
              //       color: "#3299FF",
              //     },
              //   ]),
              // },
            },
          },
          {
            type: "line",
            name: "超过6小时未在线企业平均比例",
            yAxisIndex: 1,
            itemStyle: {
              color: "#20C578",
            },
            symbolSize: 10,
            symbol: "circle",
            data: offLineRateData,
          },

          {
            type: "bar",
            name: "监测数据超量程企业平均数量",
            barWidth: "20",
            data: outRangeNumData,
            itemStyle: {
              color: "#ec7878",
              // normal: {
              //   color: new this.$echarts.graphic.LinearGradient(0, 1, 0, 0, [
              //     {
              //       offset: 0,
              //       color: "#ec7878",
              //     },
              //     {
              //       offset: 1,
              //       color: "#ec7878",
              //     },
              //   ]),
              // },
            },
          },

          {
            type: "line",
            name: "监测数据超量程企业平均比例",
            yAxisIndex: 1,
            itemStyle: {
              color: "#ffa656",
            },
            symbolSize: 10,
            symbol: "circle",
            data: outRangeRateData,
          },
        ],
      };

      // option.series[0].data = chengnuoNum;
      // option.series[1].data = chengnuoLv;
      option && myChart.setOption(option);
    },
  },
};
</script>
<style lang="scss" scoped>
.TrendChart {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-top: 10px;
    padding-bottom: 10px;
    margin-bottom: 20px;
    margin-bottom: 15px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      & > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      // height: 48px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
