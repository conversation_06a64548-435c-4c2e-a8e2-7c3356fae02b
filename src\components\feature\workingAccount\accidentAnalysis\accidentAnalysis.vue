<template>
  <div class="accidentAnalysis">
    <!-- <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 事故统计分析
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div> -->
    <!-- {{district}} -->

    <el-tabs v-model="activeTabClass" @tab-click="handleClickActiveTab()">
      <el-tab-pane label="统计图表" name="statisticsEchart">
        <statisticsEchart ref="statisticsEchart"></statisticsEchart>
      </el-tab-pane>
      <el-tab-pane label="统计列表" name="statisticsList">
        <statisticsList
          ref="statisticsList"
          :distCode="distCode"
          :dateTime="dateTime"
        ></statisticsList>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import statisticsEchart from "./statisticsEchart";
import statisticsList from "./statisticsList";

export default {
  name: "accidentAnalysis",
  components: {
    statisticsEchart,
    statisticsList,
  },
  data() {
    return {
      activeTabClass: "statisticsEchart",
      dateTime: [],
      district: this.$store.state.controler.district,
      distCode: this.$store.state.login.userDistCode,
    };
  },

  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      park: (state) => state.park,
      isShowDist: (state) => state.isShowDist,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },

  created() {},
  methods: {
    handleClickActiveTab() {},
    search() {
      if (this.activeTabClass == "statisticsList") {
        this.$refs.statisticsList.getDataes();
      } else {
      }
    },
  },

  mounted() {},
};
</script>

<style lang="scss" scoped>
.accidentAnalysis {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    // padding: 0 10px;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      // width: 550px;
      display: flex;
      justify-content: space-between;
      > * {
        margin-right: 15px;
      }
    }
  }
}
</style>
