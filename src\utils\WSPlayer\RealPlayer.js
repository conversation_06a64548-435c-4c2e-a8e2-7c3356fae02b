import PlayerItem from './PlayerItem'
import {Spinner} from './spin'
import CONSTANT from './CONSTANT'
const PlayerControl = window.PlayerControl;
/* ---------------- PlayerItem ---------------- */
class RealPlayerItem extends PlayerItem {
    /**
     * @param {*} opt.wrapperDomId 父级id
     * @param {*} opt.index 索引
     */
    constructor(opt) {
        super(opt)
        this.canvasId = `${this.domId}-livecanvas`
        this.videoId = `${this.domId}-liveVideo`
        this.initDom()
        this.defaultStatus = $('.default-status', this.$el)
        this.error = $('.error', this.$el)
        this.controller = $('.player-control', this.$el)
        this.initMouseEvent()
        /**
         * this.state 当前Player状态
         * created, ready, playing, pause, stop, closed, error
         */
        this.setStatus('created')
    }
    /**
     * 播放器模板
     */
    getTemplate() {
        let template = `
        <div id="${this.domId}" class="wsplayer-item wsplayer-item-${this.index} ${this.index === 0 ? 'selected' : 'unselected'}">
            <div class="full-content flex">
                <canvas id="${this.canvasId}" class="kind-stream-canvas" kind-channel-id="0" width="800" height="600"></canvas>
                <video id="${this.videoId}" class="kind-stream-canvas" kind-channel-id="0" muted style="display:none" width="800" height="600"></video>
            </div>
            <div class="default-status">
                <img src="./static/WSPlayer/icon/default.png" alt="">
            </div>
            <div class="player-control top-control-bar">
                <span class="stream-info"></span>
                <div class="opt-icons">
                    <!--<div class="stream-type">-->
                    <!--    <div title="主码流" stream-type="1" class="stream-type-item">主</div>-->
                    <!--    <div title="辅码流1" stream-type="2" class="stream-type-item">辅1</div>-->
                    <!--    <div title="辅码流2" stream-type="3" class="stream-type-item">辅2</div>-->
                    <!--</div>-->
                    <div class="opt-icon record-icon" title="录像"></div>
                    <div class="opt-icon audio-icon off"></div>
                    <div class="opt-icon capture-icon"></div>
                    <div class="opt-icon close-icon"></div>
                </div>
            </div>
            <div class="error">
                <div class="error-message"></div>
            </div>
        </div>
        `
        return template
    }
    /**
     * 事件监听
     */
    initMouseEvent() {
        super.initMouseEvent()
        this.hideTimer = null
        this.$el.on('mouseenter mousemove', (evt) => {
            // 非创建和关闭状态，显示状态条，可关闭视频
            if(!["created", "closed"].includes(this.status)) {
                this.setDomVisible($('.player-control', $(`#${this.domId}`)), true)
            }
            if (this.status === 'playing' || this.status === 'error') {
                this.hideTimer && clearTimeout(this.hideTimer)
            }
        })
        this.$el.on('mouseleave', (evt) => {
            this.hideTimer = setTimeout(() => {
                this.setDomVisible($('.player-control', $(`#${this.domId}`)), false)
            }, 300)
        })
        // 点击切换码流
        $('.stream-type-item', this.$el).click((e) => {
            let streamType = e.target.getAttribute("stream-type")
            // 码流发生了切换才进行码流切换操作
            if(this.streamType !== streamType) {
                // 通知业务层码流发生变化
                this.wsPlayer.sendMessage("changeStreamType", {channelId: this.options.channelId, streamType, windowIndex: this.index})
            }
        })
    }

    /**
     * 设置码流类型
     * @param streamType
     */
    setStreamType(streamType) {
        this.streamType = streamType;
        // 获取需要高亮的元素
        let target = $(".stream-type")[this.index].children[streamType - 1];
        // 给选中的码流进行高亮显示，同时将其他两个码流取消高亮
        $(target).addClass("stream-type-select").siblings().removeClass("stream-type-select")
    }
    /**
     * 设置状态，同时控制组件显示
     * created, playing, pause, stop, closed, error
     */
    setStatus(status, msg) {
        // 状态改变时，向外发送状态变动情况
        this.wsPlayer.sendMessage("statusChanged", {status, windowIndex: this.index});
        this.status = status
        switch (this.status) {
            case 'created':
            case 'closed':
                this.setDomVisible(this.defaultStatus, true)
                this.setDomVisible(this.error, false)
                this.setDomVisible(this.controller, false)
                this.videoElem.src = ''
                $('.audio-icon', this.$el).removeClass('on').addClass('off')
                break;
            case 'ready':
            case 'playing':
            case 'pause':
                this.setDomVisible(this.defaultStatus, false)
                this.setDomVisible(this.error, false)
                break;
            case 'error':
                this.setDomVisible(this.defaultStatus, false)
                $('.error-message', this.$el).text(CONSTANT.errorInfo[msg.errorCode] ? CONSTANT.errorInfo[msg.errorCode] : CONSTANT.errorInfo['defaultErrorMsg'])
                this.setDomVisible(this.error, true)
                break;
            default:
                break;
        }
    }
    /**
     * 初始化播放器
     * @param {*} options.rtspURL
     * @param {*} options.decodeMode 可选参数
     * @param {*} options.wsURL 可选参数
     * @param {*} options.streamType 码流类型
     * @param {*} options.channelId 通道id
     */
    init(options) {
        this.options = options
        if (this.player) {
            if(this.isAudioPlay) {
                // 正在播放则关闭声音
                $('.audio-icon', this.$el).removeClass('on').addClass('off')
            }
            this.close(true)
        }
        if (this.spinner) {
            this.spinner.stop()
        }
        this.spinner = new Spinner({
            color: '#ffffff'
        }).spin(this.$el[0])
        this.setStatus('ready')
        // this.setStreamType(options.streamType);

        this.createPlayer(options)
    }

    startPlay(options, e) {
        let self = this
        self.spinner.stop()
        if (e.decodeMode === 'video') {
            self.videoElem.style.display = '';
            self.canvasElem.style.display = 'none';
        } else {
            self.videoElem.style.display = 'none';
            self.canvasElem.style.display = '';
        }
        // 设置拉伸或者自适应
        self.updateAdapter(options.playerAdapter, e);
        // 若视频正在加载中，是没有宽高的
        $('.stream-info', $(`#${self.domId}`)).text(`${e.encodeMode ? `${e.encodeMode}, ` : ''}${e.width ? `${e.width}*` : ''}${e.height ? e.height : ''}`)
    }

    createPlayer(options) {
        let self = this
        this.player = new PlayerControl({
            videoElem: this.videoElem,
            canvasElem: this.canvasElem,
            wsURL: options.wsURL,
            rtspURL: options.rtspURL,
            events: {
                // 开始播放
                PlayStart: e => {
                    console.log(e);
                    self.setStatus('playing')
                },
                // 开始解码
                DecodeStart: e => {
                    console.log(e);
                    self.startPlay(options, e);
                },
                // 获取帧率
                GetFrameRate: e => {
                    console.log("GetFrameRate", e);
                    self.startPlay(options, e)
                },
                // 报错
                Error: e => {
                    // 由于error里有延迟任务，避免延迟任务触发期间切换视频导致延迟任务触发到新的视频里，加一个判断
                    // 每个视频的symbol都是唯一的
                    if(self.player.ws && e.symbol === self.player.ws.symbol) {
                        self.spinner.stop()
                        console.log('Error: ' + JSON.stringify(e))
                        self.setStatus('error', e)
                    }
                },
                // 录像文件播放结束
                FileOver: e => {
                    console.log('FileOver: ', e)
                }
            }
        });

        let newCanvas = this.copyCanvas();
        this.canvasElem.parentElement.appendChild(newCanvas)
        this.canvasElem.parentElement.removeChild(this.canvasElem);
        newCanvas.style.display = "none"
        this.canvasElem = newCanvas;
        const offscreen = this.canvasElem.transferControlToOffscreen();
        this.player.init(offscreen, this.videoElem);
        this.player.connect();
        window.wsPlayerManager.bindPlayer(this.player.nPlayPort, this.player);
    }

    /**
     * 更新播放器是自适应还是拉伸
     */
    updateAdapter(playerAdapter, e = {}) {
        // 视频流分辨率长宽比
        let ratio = e.width / e.height;
        // el：播放器节点
        let el = (e.decodeMode || this.decodeMode) === "video" ? this.videoElem : this.canvasElem;
        // 播放器父节点，根据父节点大小来进行缩放
        let elParent = el.parentNode;
        if(e.decodeMode) {
            this.decodeMode = e.decodeMode;
            // 将分辨率存储起来
            this.width = e.width;
            this.height = e.height;
        } else {
            ratio = this.width / this.height;
        }
        let width = "100%";
        let height = "100%";
        if(playerAdapter === "selfAdaption") {
            // 自适应
            let elParentHeight = elParent.offsetHeight;
            let elParentWidth = elParent.offsetWidth;
            let elRatio = elParentWidth / elParentHeight;
            if(ratio > elRatio) {
                height = `${elParentWidth / ratio}px`;
            } else if(ratio < elRatio) {
                width = `${elParentHeight * ratio}px`;
            }
            $(el).css({width , height, "object-fit": "contain"})
        } else {
            // 拉伸
            $(el).css({width, height, "object-fit": "fill"})
        }
    }
}
export default RealPlayerItem
