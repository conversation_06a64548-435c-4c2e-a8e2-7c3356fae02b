define(["./PrimitivePipeline-9bc85f23","./createTaskProcessorWorker","./Transforms-06c05e21","./Matrix2-e6265fb0","./RuntimeError-ac440aa5","./defaultValue-69ee94f4","./ComponentDatatype-a9820060","./WebGLConstants-f63312fc","./_commonjsHelpers-3aae1032-15991586","./combine-0259f56f","./GeometryAttribute-b7edcc35","./GeometryAttributes-1b4134a9","./GeometryPipeline-311a1f9e","./AttributeCompression-6e71d14f","./EncodedCartesian3-20914bf5","./IndexDatatype-1cbc8622","./IntersectionTests-94cb8698","./Plane-042297c7","./WebMercatorProjection-50f5da31"],(function(e,t,i,r,n,o,a,c,s,m,b,f,u,P,p,l,d,y,G){"use strict";return t((function(t,i){const r=e.PrimitivePipeline.unpackCombineGeometryParameters(t),n=e.PrimitivePipeline.combineGeometry(r);return e.PrimitivePipeline.packCombineGeometryResults(n,i)}))}));
