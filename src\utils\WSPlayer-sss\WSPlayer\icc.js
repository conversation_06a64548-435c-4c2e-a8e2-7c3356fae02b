import { Message } from "element-ui";
var token = "";
// 跟平台相关的业务JS
const ICC = {
 async init() {
      return new Promise((resolve,reject)=>{
        $.ajax({
            url: "/evo/evo-apigw/evo-oauth/oauth/token",
            type: "post",
            dataType: "json",
            data: {
              client_id: "zxht-icc",
              client_secret: "b0d31b70-9eee-43c6-964d-68997c2c5b7c",
              grant_type: "client_credentials",
            },
            headers: {
              "content-type": "application/x-www-form-urlencoded",
            },
            success(res) {
              console.log(res, "res");
              localStorage.setItem("accessToken", res.data.access_token);
              // 初始化服务信息
              var accessToken = localStorage.getItem("accessToken");
              console.log(accessToken, "accessToken");
              token = accessToken.split(":")[1];
             
              // $("#accessToken").val(localStorage.getItem('accessToken'))
              // if (process.env.NODE_ENV === "development") {
              //   $("#serverAdress").val(serverIp);
              // } else {
              //   $("#serverAdress").val(window.location.host.split(":")[0]);
              // }
              resolve(res.data.access_token);
              // 监听 accessToken变化
              window.addEventListener("storage", (evt) => {
                if (evt.key === "accessToken") {
                  accessToken = localStorage.getItem("accessToken");
                  token = accessToken.split(":")[1];
                  // $("#accessToken").val(localStorage.getItem('accessToken'))
                }
              });
            },
          });
      })
    
  },
  getStartTime() {
    return parseInt(
      new Date($("#startDate").val() + " " + $("#startTime").val()).getTime() /
        1000
    );
  },
  getEndTime() {
    return parseInt(
      new Date($("#endDate").val() + " " + $("#endTime").val()).getTime() / 1000
    );
  },
  /**
   * 请求实时视频流地址
   * @param opt.dataType 视频类型：1=视频, 2=音频, 3=音视频
   * @param opt.streamType //码流类型：1=主码流, 2=辅码流 使用辅码流 码率低更加流畅
   * @param opt.channelId 通道code
   */
  getRealmonitor(opt) {
    console.log(opt)
    var realmonitorParam = {
      clientType: "WINPC",
      clientMac: "30:9c:23:79:40:08",
      clientPushId: "",
      project: "PSDK",
      method: "MTS.Video.StartVideo",
      data: {
        optional: "/admin/API/MTS/Video/StartVideo",
        dataType: opt.dataType ? opt.dataType : "3", // 视频类型：1=视频, 2=音频, 3=音视频
        streamType: opt.streamType ? opt.streamType : "2", //码流类型：1=主码流, 2=辅码流 使用辅码流 码率低更加流畅
        channelId: opt.code||opt.channelCode||opt.channelId,
        trackId: "",
      },
    };
    return new Promise((resolve, reject) => {
      ICC.ajaxRequest({
        url: "/evo/evo-apigw/admin/API/MTS/Video/StartVideo",
        data: realmonitorParam,
        // cb:ICC.getRealmonitor(opt)
      })
        .then((res) => {
          res.data.rtspUrl = res.data.url + "?token=" + res.data.token;
          resolve(res.data);
        })
        .catch((res) => {
          if (res.code === 2016) {
            Message.error("该设备不存在~");
          } else {
            Message.error(res.errMsg);
          }
          reject(res.errMsg);
        });
    });
  },
  /**
   * 根据时间查询设备上的录像
   * @param opt.recordType 录像类型：1=一般录像，2=报警录像
   * @param opt.recordSource 录像来源：1=全部，2=设备，3=中心
   * @param opt.streamType 码流类型：1=主码流, 2=辅码流 使用辅码流 码率低更加流畅
   * @param opt.channelId 通道code
   * @param opt.startTime 开始时间
   * @param opt.endTime 结束时间
   */

  getRecordRtspByTime(opt) {
    var recordByTimeParam = {
      clientType: "WINPC",
      clientMac: "30:9c:23:79:40:08",
      clientPushId: "",
      project: "PSDK",
      method: "SS.Playback.StartPlaybackByTime",
      data: {
        nvrId: "",
        optional: "/admin/API/SS/Playback/StartPlaybackByTime",
        recordType: opt.recordType ? opt.recordType : "0", // 录像类型：1=一般录像，2=报警录像
        recordSource: opt.recordSource ? opt.recordSource : "1", // 录像来源：1=全部，2=设备，3=中心
        streamType: opt.streamType ? opt.streamType : "1", // 码流类型：1=主码流， 2=辅码流
        channelId: opt.channelId,
        startTime: opt.startTime ? opt.startTime : ICC.getStartTime(),
        endTime: opt.endTime ? opt.endTime : ICC.getEndTime(),
      },
    };
    return new Promise((resolve, reject) => {
      ICC.ajaxRequest({
        url: "/evo/evo-apigw/admin/API/SS/Playback/StartPlaybackByTime",
        data: recordByTimeParam,
      })
        .then((res) => {
          res.data.rtspUrl = res.data.url + "?token=" + res.data.token;
          resolve(res.data);
        })
        .catch((res) => {
          reject(res.errMsg);
        });
    });
  },

  /**
   * 根据文件形式回放录像
   * @param opt.recordType 录像类型：1=一般录像，2=报警录像
   * @param opt.streamType 码流类型：1=主码流, 2=辅码流 使用辅码流 码率低更加流畅
   * @param opt.recordSource 录像来源：1=全部，2=设备，3=中心
   * @param opt.channelId 通道code
   * @param opt.startTime 开始时间 timestamp到秒
   * @param opt.endTime 结束时间 timestamp到秒
   */

  getRecordRtspByFile(opt) {
    var byFileParam = {
      clientType: "WINPC",
      clientMac: "30:9c:23:79:40:08",
      clientPushId: "",
      project: "PSDK",
      method: "SS.Playback.StartPlaybackByFile",
      data: {
        ssId: "1001",
        optional: "/evo/evo-apigw/admin/API/SS/Playback/StartPlaybackByFile",
        startTime: opt.startTime ? opt.startTime : ICC.getStartTime(),
        endTime: opt.endTime ? opt.endTime : ICC.getEndTime(),
        fileName: "{fe69f729-9d4b-42d4-b6a0-56189aaa4e1e}",
        diskId: "1540852944-1540853395",
        nvrId: "",
        recordSource: opt.recordSource ? opt.recordSource : "3",
        channelId: opt.channelId,
        playbackMode: "0",
        streamId: "5875",
      },
    };
    return new Promise((resolve, reject) => {
      ICC.getRecords(opt)
        .then((data) => {
          if (data.length === 0) {
            // alert()
            reject("所选设备未查询到录像文件");
          }
          var rec = data[0];
          byFileParam.data = {
            ssId: rec.ssId,
            optional: "/evo/evo-apigw/admin/API/SS/Playback/StartPlaybackByFile",
            startTime: opt.startTime ? opt.startTime : ICC.getStartTime(),
            endTime: opt.endTime ? opt.endTime : ICC.getEndTime(),
            fileName: rec.recordName,
            diskId: rec.diskId,
            nvrId: "",
            recordSource: opt.recordSource ? opt.recordSource : "3",
            channelId: rec.channelId,
            playbackMode: "0",
            streamId: rec.streamId,
          };
          ICC.ajaxRequest({
            url: "/evo/evo-apigw/admin/API/SS/Playback/StartPlaybackByFile",
            data: byFileParam,
          })
            .then((res) => {
              res.data.rtspUrl = res.data.url + "?token=" + res.data.token;
              resolve(res.data);
            })
            .catch((res) => {
              reject(res.errMsg);
            });
        })
        .catch((err) => {
          reject(err);
        });
    });
  },
  /**
   * 初始化视频设备树
   * @param {*} options.el
   * @param {*} options.onClick
   */
  initVideoTree(options) {
    const zTreeSetting = {
      async: {
        autoParam: ["id=id", "checkStat"], //异步加载时需要自动提交父节点属性的参数
        contentType: "application/json", //Ajax 提交参数的数据类型
        dataType: "text", //Ajax 获取的数据类型
        enable: true, //设置 zTree 是否开启异步加载模式
        otherParam: {
          type: "001;00_1,00_3,00_5,00_7,00_21,00_35,00_36,00_45;1",
        }, //Ajax 请求提交的静态参数键值对
        type: "post", // Ajax 的 http 请求模式
        url: "/evo/evo-apigw//evo-brm/1.2.0/tree", //请求地址
        token: localStorage.accessToken,
        headers: {
          Authorization: localStorage.accessToken
            ? "bearer " + localStorage.accessToken
            : "",
          userId: "1",
          "user-client": localStorage.clientType,
        },
        dataFilter: (treeId, parentNode, res) => {
          if (res.code === "27001007") {
            ICC.refreshToken().then(() => {
              ICC.initVideoTree(options);
            });
            return false;
          }
          res.data.value.forEach((item) => {
            item.icon = `${item.iconType}.png`;
          });
          return res.data.value;
        },
      },
      view: {
        dblClickExpand: false,
        showLine: true,
        selectedMulti: false,
      },
      data: {
        simpleData: {
          enable: true,
          idKey: "id",
          pIdKey: "pId",
          rootPId: "root",
        },
      },
      callback: {
        beforeClick: function(treeId, treeNode) {
          var zTree = $.fn.zTree.getZTreeObj(options.el);
          if (treeNode.isParent) {
            zTree.expandNode(treeNode);
            return false;
          }
        },
        onAsyncSuccess: function(evt, treeId, treeNode) {
          var zTree = $.fn.zTree.getZTreeObj(treeId);
          if (!treeNode) {
            let rootNode = $.fn.zTree.getZTreeObj(treeId).getNodes()[0];
            zTree.expandNode(rootNode, true);
          }
        },
        onClick: options.onClick,
      },
    };
    $.fn.zTree.init($(`#${options.el}`), zTreeSetting);
  },
  /**
   *  查询中心录像上的录像文件信息
   * @param opt.recordType 录像类型：1=一般录像，2=报警录像
   * @param opt.streamType 码流类型：1=主码流, 2=辅码流 使用辅码流 码率低更加流畅
   * @param opt.recordSource 录像来源：1=全部，2=设备，3=中心
   * @param opt.channelId 通道code
   * @param opt.startTime 开始时间 timestamp到秒
   * @param opt.endTime 结束时间 timestamp到秒
   */
  getRecords(opt) {
    var param = {
      clientType: "WINPC",
      clientMac: "30:9c:23:79:40:08",
      clientPushId: "",
      project: "PSDK",
      method: "SS.Record.QueryRecords",
      data: {
        cardNo: "",
        optional: "/admin/API/SS/Record/QueryRecords",
        diskPath: "",
        startIndex: "",
        streamType: opt.streamType ? opt.streamType : "0", // 码流类型：1=主码流, 2=辅码流
        recordType: opt.recordType ? opt.recordType : "0", // 录像类型：0=全部，1=手动录像，2=报警录像，3=动态监测，4=视频丢失，5=视频遮挡，6=定时录像，7=全天候录像，8=文件录像转换
        recordSource: opt.recordSource ? opt.recordSource : "3", // 录像来源：1=全部，2=设备，3=中心
        endIndex: "",
        startTime: opt.startTime ? opt.startTime : ICC.getStartTime(),
        endTime: opt.endTime ? opt.endTime : ICC.getEndTime(),
        channelId: opt.channelId,
      },
    };
    return new Promise((resolve, reject) => {
      ICC.ajaxRequest({
        url: "/evo/evo-apigw/admin/API/SS/Record/QueryRecords",
        data: param,
      })
        .then((res) => {
          resolve(res.data.records);
        })
        .catch((res) => {
          reject(res.errMsg);
        });
    });
  },
  /**
   * 请求
   * @param {*} opt
   * @param {*} opt.url 接口地址
   * @param {*} opt.data 参数
   */
  ajaxRequest(opt,callback) {
    var accessToken = localStorage.getItem("accessToken");
    var userId = accessToken.split(":")[0];
    opt.headers = opt.headers ? opt.headers : {};
    return new Promise((resolve, reject) => {
      $.ajax({
        url: opt.url,
        type: opt.type ? opt.type : "post",
        dataType: opt.dataType ? opt.dataType : "json",
        data: JSON.stringify(opt.data),
        headers: Object.assign(
          {
            Authorization: "bearer " + accessToken,
            "User-Id": 1,
            "Content-Type": "application/json",
          },
          opt.headers
        ),
        contentType: "application/json;charset=utf-8",
        crossDomain: true,
        success: function(res) {
          if (res.code == 1000) {
            resolve(res);
          } else if (res.code == "27001007") {
            // token过期，刷新token
            ICC.refreshToken().then(() => {
              // token刷新后，重新请求
              ICC.ajaxRequest(opt).then((data) => {
                // 重新请求后，需要处理原始请求的resolve
                resolve(data);
              });
            }).catch((ex)=>{
              // console.log('刷新token失效，需要重新登录')
              ICC.init().then(() => {
                // token刷新后，重新请求
                ICC.ajaxRequest(opt).then((data) => {
                  // 重新请求后，需要处理原始请求的resolve
                  resolve(data);
                });
            })
          })
          } else {
            reject(res);
          }
        },
      });
    });
  },
  // 刷新token
  refreshToken() {
    var refreshToken = localStorage.getItem("refreshToken");
    var clientId = localStorage.getItem("checked")
      ? "web_client_remember"
      : "web_client";
    var value = {
      refresh_token: refreshToken,
      grant_type: "refresh_token",
      client_id: clientId,
      client_secret: "web_client",
    };
    var param = "";
    for (var i in value) {
      param += "&" + i + "=" + value[i];
    }
    param = param.slice(1);
    return new Promise((resolve, reject) => {
      $.ajax({
        url: "/evo/evo-apigw/evo-oauth/oauth/token",
        type: "post",
        dataType: "json",
        data: param,
        headers: {
          "content-type": "application/x-www-form-urlencoded",
        },
        success: function(res) {
          console.log(res);
          if (res.code === "0") {
            localStorage.setItem("accessToken", res.data.access_token);
            localStorage.setItem("refreshToken", res.data.refresh_token);
            localStorage.setItem(
              "expiresTime",
              new Date().getTime() + res.data.expires_in * 1000
            );
            resolve();
          } else {
            console.error(`refreshToken 失效，请登录平台`);
            // window.open(location.origin);
            // refreshToken();
            // ICC.refreshToken().then(() => {
            //   // token刷新后，重新请求
            //   ICC.ajaxRequest(opt).then((data) => {
            //     // 重新请求后，需要处理原始请求的resolve
            //     resolve(data);
            //   });
            // });
            reject();
          }
        },
      });
    });
  },
};
export default ICC;
