import axios from "axios";

export default function (obj, name) {
  // 兼容ie的文件下载方法
  let flag =
    window.navigator.userAgent.indexOf("Trident") > -1 &&
    window.navigator.userAgent.indexOf("rv:11.0") > -1;
  if (flag) {
    window.navigator.msSaveBlob(obj.blobStream, obj.filename);
    try {
    } catch (e) {
      console.log(e);
    }
  } else {
    // 谷歌的下载方法
    const blob = new Blob([obj.data], {
      type: "application/octet-stream;charset=utf-8 ",
    });
    let filename = "";
    if (name) {
      filename = name;
    } else {
      filename = decodeURIComponent(obj.headers.filename);
    }
    //下载文件
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    console.log(filename, "link.download");
    document.body.appendChild(link);
    link.click();
    window.setTimeout(function () {
      URL.revokeObjectURL(blob);
      document.body.removeChild(link);
    }, 0);
  }
}
//
export const Attachmentdownload = (data) => {
  return axios({
    method: "post",
    url: "/gemp-file/api/attachment/download/v1",
    data: data,
    responseType: "arraybuffer",
  });
};

export const findByFileIdAttachment = (data) => {
  return axios({
    method: "post",
    url: "/gemp-file/api/attachment/findByFileId/v1",
    data: data,
    // responseType: "arraybuffer",
  });
};
