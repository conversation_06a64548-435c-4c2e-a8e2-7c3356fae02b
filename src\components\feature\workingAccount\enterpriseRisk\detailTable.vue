<template>
  <div class="detailTable">
    <el-dialog
      :title="titleName"
      :visible.sync="show"
      @close="closeBoolean(false)"
      width="1350px"
      top="5vh"
      :destroy-on-close="true"
      :close-on-click-modal="false"
    >
      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="distCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"          
            :show-all-levels="true"
            v-if="isShowDist"
          ></el-cascader>
          <el-select
            v-model="majorHazardLevel"
            placeholder="危险源等级"
            size="mini"
            clearable
            @clear="clearMa"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            placeholder="请输入企业名称"
            v-model.trim="entName"
            size="mini"
            clearable
            @clear="clearEntName"
          >
          </el-input>
          <el-select
            v-model="type"
            placeholder="危险源等级"
            size="mini"        
          >
            <el-option
              v-for="item in risks"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button type="primary" size="mini"  class="btn" @click="search">查询</el-button>
          <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
      <el-table
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        border
        @select="select"
        @select-all="select"
      >
        <el-table-column
          type="selection"
          width="50"
          align="center"
        ></el-table-column>
        <el-table-column label="序号" width="50" align="center">
          <template slot-scope="{ row, column, $index, store }">
            <span>{{ $index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="enterpName"
          label="单位名称"
          width="300"
          align="center"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="{ row, column, $index, store }">
            <span @click="goEnt(row)" style="color: #3977ea; cursor: pointer">{{
              row.enterpName
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="districtName" label="区划" align="center">
        </el-table-column>
        <el-table-column
          prop="majorHazardLevelName"
          label="重大危险源企业等级"
          align="center"
        >
          <!-- <template slot-scope="scope">
            <span v-if="scope.row.majorHazardLevel == 1">一级重大危险源</span>
            <span v-if="scope.row.majorHazardLevel == 2">二级重大危险源</span>
            <span v-if="scope.row.majorHazardLevel == 3">三级重大危险源</span>
            <span v-if="scope.row.majorHazardLevel == 4">四级重大危险源</span>
          </template> -->
        </el-table-column>
        <el-table-column prop="riskLevelName" label="风险等级" align="center">
          <!-- <template slot-scope="scope">
            <span v-if="scope.row.riskGrade == 1">重大风险</span>
            <span v-if="scope.row.riskGrade == 2">较大风险</span>
            <span v-if="scope.row.riskGrade == 3">一般风险</span>
            <span v-if="scope.row.riskGrade == 4">低风险</span>
          </template> -->
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page.sync="currentPage"
          background
          layout="total, prev, pager, next"
          :total="total"
        >
        </el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCimRiskAnalysisCimRiskDetailsList,
  getCimRiskAnalysisExportPromiseDetailsToExcel,
} from "@/api/workingAcc";
import { getDistrictUser } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      show: false,
      //  districtLoading: false,
      type: "",
      currentPage: 1,
      entName: "",
      loading: true,
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      risks:[
        {
          value: 0,
          label: "所有风险",
        },
        {
          value: 1,
          label: "重大风险",
        },
        {
          value: 2,
          label: "较大风险",
        },
        {
          value: 3,
          label: "一般风险",
        },
        {
          value: 4,
          label: "低风险",
        },
      ],
      majorHazardLevel: "",
      district: [],
      distCode: "",
      tableData: [],
      total: 0,
      areaName: "",
      titleName: "",
      selection: [],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
      isShowDist: (state) => state.isShowDist
    }),
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.currentPage = 1;
      this.majorHazardLevel = "";
      this.show = val;
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.entId);
    },
    //获取行政区划
    getDistrict(val) {
      getDistrictUser().then((res) => {
        let child = res.data.data;
        // debugger;
        if (child.children.length > 0) {
          for (let j = 0; j < child.children.length; j++) {
            if (child.children[j].children.length > 0) {
              for (let z = 0; z < child.children[j].children.length; z++) {
                if (child.children[j].children[z].children.length < 1) {
                  //判断children的数组长度
                  child.children[j].children[z].children = undefined;
                }
              }
            } else {
              child.children[j].children = undefined;
            }
          }
        } else {
          child.children = undefined;
        }
        this.district = [child];
        this.districtLoading = false;
      });
    },
    getEntData(distCode, type, areaName, danger) {
      this.distCode = distCode;
      this.loading = true;
      this.type = type;
      this.areaName = areaName;
      if (type == 0) {
        this.titleName = this.areaName + "-企业风险分析-接入企业列表";
      } else if (type == 1) {
        this.titleName = this.areaName + "-企业风险分析-重大风险企业列表";
      } else if (type == 2) {
        this.titleName = this.areaName + "-企业风险分析-较大风险企业列表";
      } else if (type == 3) {
        this.titleName = this.areaName + "-企业风险分析-一般风险企业列表";
      } else if (type == 4) {
        this.titleName = this.areaName + "-企业风险分析-较低风险企业列表";
      }
      getCimRiskAnalysisCimRiskDetailsList({
        distCode: this.distCode,
        entName: this.entName,
        majorHazardLevel: this.majorHazardLevel,
        type: this.type,
        nowPage: this.currentPage,
        pageSize: 10,
        entIdList:[]
      }).then((res) => {
        if (res.data.status == 200) {
          this.loading = false;
          this.tableData = res.data.data.list;
          this.total = res.data.data.total;
        }
      });
    },
    exportExcel() {
      // console.log(list);
      getCimRiskAnalysisExportPromiseDetailsToExcel({
        distCode: this.distCode,
        entName: this.entName,
        majorHazardLevel: this.majorHazardLevel,
        type: this.type,
        nowPage: this.currentPage,
        pageSize: 10,
        entIdList:this.selection

         }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = this.titleName + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    // 排序
    changeTableSort(column) {
      var fieldName = column.prop;
      var sortingType = column.order;
      var data = [...this.tableData];
      var shiftTotal = [];
      if (data[0].areaCode == this.$store.state.login.userDistCode) {
        shiftTotal = data.shift();
      } else if ((this.areaCode = data[0].areaCode)) {
        shiftTotal = data.shift();
      }
      //按照降序排序
      if (sortingType == "descending") {
        data = data.sort((a, b) => b[fieldName] - a[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      }
      //按照升序排序
      else if (sortingType == "ascending") {
        data = data.sort((a, b) => a[fieldName] - b[fieldName]);
        this.tableData = data;
        this.tableData.unshift(shiftTotal);
      } else {
        this.getSafetyList();
      }
    },
    select(selection, row) {
      this.selection = selection.map(item => item.enterpId)
      
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.currentPage = val;
      this.getEntData();
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`);
      this.current = val;
      this.getEntData(this.distCode, this.type, this.areaName);
    },
    search() {
      this.currentPage = 1;
      this.getEntData(this.distCode, this.type, this.areaName);
    },
    clearDis() {
      this.distCode = "";
    },
    clearMa() {
      this.majorHazardLevel = "";
    },
    clearEntName() {
      this.entName = "";
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.detailTable {
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    .l {
      display: flex;
      justify-content: flex-start;
      > div {
        width: 200px;
      }
                  >* {
        margin-right: 15px;
      }
    }
  }
  .pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
<style>
</style>