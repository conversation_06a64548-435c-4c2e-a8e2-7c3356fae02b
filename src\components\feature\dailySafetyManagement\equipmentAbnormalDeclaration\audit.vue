<template>
  <div class="notification">
    <el-dialog
      :title="title"
      :visible.sync="show"
      width="1000px"
      @close="closeBoolean()"
      top="10vh"
      v-dialog-drag
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="div1">
        <div class="table">
          <ul class="container">
            <li class="lang">
              <div class="l">企业名称</div>
              <div class="r">
                  {{detailData.enterpName}}
              </div>
            </li>
            <li class="lang">
              <div class="l">统一社会信用代码</div>
              <div class="r">
                {{detailData.entcreditcode}}
              </div>
            </li>
            <li class="lang">
              <div class="l">提交时间</div>
              <div class="r">
                {{detailData.updateTime}}
              </div>
            </li>
            <li class="lang">
              <div class="l">所属重大危险源</div>
              <div class="r">{{detailData.dangerName}}</div>
            </li>
            <li class="lang">
              <div class="l">设备名称</div>
              <div class="r">{{detailData.equipment}}</div>
            </li>
            <li class="lang">
              <div class="l">申报原因</div>
              <!-- <div class="r" v-if="detailData.declareReason == '01'">故障检修</div>
              <div class="r" v-else-if="detailData.declareReason == '02'">设备损坏</div>
              <div class="r" v-else-if="detailData.declareReason == '03'">停工停产</div>
              <div class="r" v-else-if="detailData.declareReason == '04'">其它原因</div> -->
                  <div class="r" v-if="detailData.declareReason == '0'">离线</div>
              <div class="r" v-else-if="detailData.declareReason == '1'">停产</div>
            
            </li>
            <li class="lang">
              <div class="l">申报说明</div>
              <div class="r">{{detailData.declareExplain}}</div>
            </li>
            <li class="lang">
              <div class="l">状态开始时间</div>
              <div class="r">{{detailData.endTime}}</div>
            </li>
            <li class="lang">
              <div class="l">状态结束时间</div>
              <div class="r">{{detailData.startTime}}</div>
            </li>
            <li class="lang">
              <div class="l">审核意见</div>
              <div class="r">
                  <el-radio v-model="radio" label="1">通过</el-radio>
                  <el-radio v-model="radio" label="2">驳回</el-radio>
              </div>
            </li>
            <li class="lang bottom">
              <div class="l">审核说明</div>
              <div class="r">
                 <!-- :autosize="{ minRows: 2, maxRows: 12 }" -->
                <el-input
                  name=""
                  cols="10"                  
                  :autosize="{ minRows: 6, maxRows: 12 }"
                  type="textarea"
                  resize="none"
                  maxlength="500"
                  class="textarea"
                  step                 
                  placeholder="请输入审核说明"
                  v-model.trim="text"
                ></el-input>
                <div class="textLength">{{"还能输入："+textLength+"个字"}}</div>
              </div>
            </li>
          </ul>
 </div>

 <div class="buttonBox">
<el-button class="submit" size="medium" @click="closeBoolean()"
            >取消</el-button
          >
          <el-button
            class="submit"
            size="medium"
            type="primary"
            @click="submit()"
            >提交</el-button
          >
 </div>
          
       
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { examine,getAbnormalDetail } from "@/api/dailySafety";
export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  data() {
    return {
      show: false,
      radio:"1",
      text:"",
      textLength: 500,
      id:'',
      detailData:{},
      enterpId:'',
      title:'',
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val;
      this.radio='1';
      this.text=''
    },
    submit() {
        if(this.radio == '2'){
            if(!this.text){
               this.$message({
                    type: 'info',
                    message: '审核说明不能为空'
                });
                return
            }
            
        }
        examine({
            id:this.id,
            examineExplain:this.text,
            state:this.radio
        }).then((res) => {
            this.$message({
                    type: 'success',
                    message: '保存成功'
                });
                this.show = false;
                this.$parent.getIotMontoringDataList(this.enterpId);
        });
    },
    getData(id,enterpId,type) {
        if(type == '0'){
            this.title="异常报备信息审核"
        }else if(type == '1'){
            this.title="视频异常报备信息审核"
        }
        getAbnormalDetail({
          id:id
      }).then((res) => {
        this.detailData = res.data.data;
      });
      this.id = id;
      this.enterpId = enterpId;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {
    text: {
      handler(newVal, oldVal) {
        this.textLength = 500 - newVal.length;
      },
      deep:true,
      immediate:true
    },
  },
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.notification {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength{
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {   
    .table{
      height: 600px;
       overflow: auto;
    }
    .buttonBox{
      text-align: right;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>