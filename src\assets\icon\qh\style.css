@font-face {
  font-family: 'qh';
  src:  url('fonts/quanhui.eot?l40c8m');
  src:  url('fonts/quanhui.eot?l40c8m#iefix') format('embedded-opentype'),
    url('fonts/quanhui.ttf?l40c8m') format('truetype'),
    url('fonts/quanhui.woff?l40c8m') format('woff'),
    url('fonts/quanhui.svg?l40c8m#quanhui') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="qh-"], [class*=" qh-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'qh' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.qh-gaojing1:before {
  content: "\e900";
}
.qh-gaojing2:before {
  content: "\e901";
}
.qh-gaojing:before {
  content: "\e902";
}
.qh-qiehuan:before {
  content: "\e903";
}
.qh-fast_forward:before {
  content: "\e01f";
}
.qh-fast_rewind:before {
  content: "\e020";
}
.qh-widgets:before {
  content: "\e1bd";
}
.qh-center_focus_strong2:before {
  content: "\e3b5";
}
.qh-person_pin:before {
  content: "\e55a";
}
.qh-my_location2:before {
  content: "\e55d";
}
.qh-pin_drop:before {
  content: "\e55e";
}
.qh-power:before {
  content: "\e60b";
}
.qh-disposalReport:before {
  content: "\e60e";
}
.qh-affiliates:before {
  content: "\e61f";
}
.qh-sort-pic:before {
  content: "\e641";
}
.qh-lock_outline2:before {
  content: "\e89b";
}
.qh-open_in_new:before {
  content: "\e89e";
}
.qh-settings_backup_restore:before {
  content: "\e8ba";
}
.qh-zhedie:before {
  content: "\e904";
}
.qh-dingwei:before {
  content: "\e905";
}
.qh-weixian:before {
  content: "\e906";
}
.qh-xiaofangshuan:before {
  content: "\e907";
}
.qh-qiye:before {
  content: "\e908";
}
.qh-cangku:before {
  content: "\e909";
}
.qh-chuguan:before {
  content: "\e90a";
}
.qh-chuguan1:before {
  content: "\e90b";
}
.qh-chuguan2:before {
  content: "\e90c";
}
.qh-dianhua:before {
  content: "\e90d";
}
.qh-duanxin:before {
  content: "\e90e";
}
.qh-guanxian:before {
  content: "\e90f";
}
.qh-jiuyuandui:before {
  content: "\e910";
}
.qh-7:before {
  content: "\e911";
}
.qh-weixianyuan:before {
  content: "\e912";
}
.qh-zhuangbei:before {
  content: "\e913";
}
.qh-zhuangbei1:before {
  content: "\e914";
}
.qh-zhuanjia:before {
  content: "\e915";
}
.qh-zhuanjia1:before {
  content: "\e916";
}
.qh-airplan:before {
  content: "\e917";
}
.qh-casePlan:before {
  content: "\e918";
}
.qh-address:before {
  content: "\e919";
}
.qh-grade:before {
  content: "\e91a";
}
.qh-name:before {
  content: "\e91b";
}
.qh-workPlan:before {
  content: "\e91c";
}
.qh-type:before {
  content: "\e91d";
}
.qh-time:before {
  content: "\e91e";
}
.qh-allFax:before {
  content: "\e91f";
}
.qh-project:before {
  content: "\e920";
}
.qh-author:before {
  content: "\e921";
}
.qh-dutyPeople:before {
  content: "\e922";
}
.qh-arrowTop:before {
  content: "\e923";
}
.qh-report:before {
  content: "\e924";
}
.qh-contacts:before {
  content: "\e925";
}
.qh-minusPic:before {
  content: "\e926";
}
.qh-addPic:before {
  content: "\e927";
}
.qh-gas:before {
  content: "\e928";
}
.qh-picture:before {
  content: "\e929";
}
.qh-search:before {
  content: "\e92a";
}
.qh-smallMessage:before {
  content: "\e92b";
}
.qh-garden:before {
  content: "\e92c";
}
.qh-coal:before {
  content: "\e92d";
}
.qh-dangerous:before {
  content: "\e92e";
}
.qh-danger:before {
  content: "\e92f";
}
.qh-cross:before {
  content: "\e930";
}
.qh-eyeOpen:before {
  content: "\e931";
}
.qh-eyeClose:before {
  content: "\e932";
}
.qh-dataCount:before {
  content: "\e933";
}
.qh-dataManage:before {
  content: "\e934";
}
.qh-dataControl:before {
  content: "\e935";
}
.qh-structure:before {
  content: "\e936";
}
.qh-dutyManage:before {
  content: "\e937";
}
.qh-grid2:before {
  content: "\e938";
}
.qh-study:before {
  content: "\e939";
}
.qh-notice:before {
  content: "\e93a";
}
.qh-dataShare:before {
  content: "\e93b";
}
.qh-support:before {
  content: "\e93c";
}
.qh-draft:before {
  content: "\e93d";
}
.qh-collection:before {
  content: "\e93e";
}
.qh-icon6:before {
  content: "\e93f";
}
.qh-administrativeDivision:before {
  content: "\e940";
}
.qh-listEdit:before {
  content: "\e941";
}
.qh-rescueUnit:before {
  content: "\e942";
}
.qh-home:before {
  content: "\e943";
}
.qh-equip:before {
  content: "\e944";
}
.qh-icon8:before {
  content: "\e945";
}
.qh-home1:before {
  content: "\e946";
}
.qh-manage:before {
  content: "\e947";
}
.qh-zhishengji:before {
  content: "\e948";
}
.qh-infoReport:before {
  content: "\e949";
}
.qh-noCoal:before {
  content: "\e94a";
}
.qh-keyCounty:before {
  content: "\e94b";
}
.qh-laws:before {
  content: "\e94c";
}
.qh-layer1:before {
  content: "\e94d";
}
.qh-browse:before {
  content: "\e94e";
}
.qh-leader:before {
  content: "\e94f";
}
.qh-knowledge:before {
  content: "\e950";
}
.qh-exercise:before {
  content: "\e951";
}
.qh-rescuePlay:before {
  content: "\e952";
}
.qh-document:before {
  content: "\e953";
}
.qh-firm:before {
  content: "\e954";
}
.qh-allList:before {
  content: "\e955";
}
.qh-talk:before {
  content: "\e956";
}
.qh-respondInfo:before {
  content: "\e957";
}
.qh-commandCar:before {
  content: "\e958";
}
.qh-apperSite:before {
  content: "\e959";
}
.qh-contacts21:before {
  content: "\e95a";
}
.qh-picture2:before {
  content: "\e95b";
}
.qh-sceneInfo:before {
  content: "\e95c";
}
.qh-study2:before {
  content: "\e95d";
}
.qh-administrativeDivision2:before {
  content: "\e95e";
}
.qh-user322:before {
  content: "\e95f";
}
.qh-oilField:before {
  content: "\e960";
}
.qh-opinion:before {
  content: "\e961";
}
.qh-process:before {
  content: "\e962";
}
.qh-professor:before {
  content: "\e963";
}
.qh-property:before {
  content: "\e964";
}
.qh-asset:before {
  content: "\e965";
}
.qh-reporter:before {
  content: "\e966";
}
.qh-getLetter:before {
  content: "\e967";
}
.qh-getBin:before {
  content: "\e968";
}
.qh-reportInfo:before {
  content: "\e969";
}
.qh-reportUnit:before {
  content: "\e96a";
}
.qh-rescueTeam:before {
  content: "\e96b";
}
.qh-resource:before {
  content: "\e96c";
}
.qh-fileManage:before {
  content: "\e96d";
}
.qh-situation2:before {
  content: "\e96e";
}
.qh-emResponse:before {
  content: "\e96f";
}
.qh-serviceManagement:before {
  content: "\e970";
}
.qh-sendLetter:before {
  content: "\e971";
}
.qh-sendBin:before {
  content: "\e972";
}
.qh-train2:before {
  content: "\e973";
}
.qh-plan:before {
  content: "\e974";
}
.qh-safeTrend:before {
  content: "\e975";
}
.qh-searchPic:before {
  content: "\e976";
}
.qh-sendShort:before {
  content: "\e977";
}
.qh-contacts2:before {
  content: "\e978";
}
.qh-traffic:before {
  content: "\e979";
}
.qh-sentFax:before {
  content: "\e97a";
}
.qh-authManage:before {
  content: "\e97b";
}
.qh-serManage:before {
  content: "\e97c";
}
.qh-building:before {
  content: "\e97d";
}
.qh-serSource:before {
  content: "\e97e";
}
.qh-storeBox:before {
  content: "\e97f";
}
.qh-filePic:before {
  content: "\e980";
}
.qh-staffGauge:before {
  content: "\e981";
}
.qh-sendTrash:before {
  content: "\e982";
}
.qh-getTrash:before {
  content: "\e983";
}
.qh-draftMail:before {
  content: "\e984";
}
.qh-sendMail:before {
  content: "\e985";
}
.qh-getMail:before {
  content: "\e986";
}
.qh-infoSearch22:before {
  content: "\e987";
}
.qh-sendMessage:before {
  content: "\e988";
}
.qh-delete:before {
  content: "\e989";
}
.qh-warn:before {
  content: "\e98a";
}
.qh-trafficPic:before {
  content: "\e98b";
}
.qh-pencil3:before {
  content: "\e98c";
}
.qh-keyArea:before {
  content: "\e98d";
}
.qh-agentMan:before {
  content: "\e98e";
}
.qh-accessory:before {
  content: "\e98f";
}
.qh-infoBrief:before {
  content: "\e990";
}
.qh-cause:before {
  content: "\e991";
}
.qh-matter:before {
  content: "\e992";
}
.qh-webCam:before {
  content: "\e993";
}
.qh-tel:before {
  content: "\e994";
}
.qh-capacity:before {
  content: "\e995";
}
.qh-highTrain:before {
  content: "\e996";
}
.qh-video:before {
  content: "\e997";
}
.qh-modelSet:before {
  content: "\e998";
}
.qh-event1:before {
  content: "\e999";
}
.qh-title:before {
  content: "\e99a";
}
.qh-injuries:before {
  content: "\e99b";
}
.qh-workCase:before {
  content: "\e99c";
}
.qh-caseInfo:before {
  content: "\e99d";
}
.qh-taskPlan:before {
  content: "\e99e";
}
.qh-loginUser:before {
  content: "\e99f";
}
.qh-yjManage:before {
  content: "\e9a0";
}
.qh-exit:before {
  content: "\e9a1";
}
.qh-lock:before {
  content: "\e9a2";
}
.qh-building1:before {
  content: "\e9a3";
}
.qh-screen:before {
  content: "\e9a4";
}
.qh-talk1:before {
  content: "\e9a5";
}
.qh-facsimile:before {
  content: "\e9a6";
}
.qh-weater:before {
  content: "\e9a7";
}
.qh-help:before {
  content: "\e9a8";
}
.qh-user:before {
  content: "\e9a9";
}
.qh-buildingPic:before {
  content: "\e9aa";
}
.qh-mobile:before {
  content: "\e9ab";
}
.qh-bookOpen:before {
  content: "\e9ac";
}
.qh-balance:before {
  content: "\e9ad";
}
.qh-hammer:before {
  content: "\e9ae";
}
.qh-bookLine:before {
  content: "\e9af";
}
.qh-rollPaper:before {
  content: "\e9b0";
}
.qh-accCount:before {
  content: "\e9b1";
}
.qh-switchPic:before {
  content: "\e9b2";
}
.qh-ecoLoss:before {
  content: "\e9b3";
}
.qh-metal:before {
  content: "\e9b4";
}
.qh-feedBack:before {
  content: "\e9b5";
}
.qh-medicine:before {
  content: "\e9b6";
}
.qh-comPic:before {
  content: "\e9b7";
}
.qh-tradePic:before {
  content: "\e9b8";
}
.qh-fireBuid:before {
  content: "\e9b9";
}
.qh-danSource:before {
  content: "\e9ba";
}
.qh-oilSite:before {
  content: "\e9bb";
}
.qh-oilLine:before {
  content: "\e9bc";
}
.qh-cancel:before {
  content: "\e9bd";
}
.qh-release:before {
  content: "\e9be";
}
.qh-folderPic:before {
  content: "\e9bf";
}
.qh-team:before {
  content: "\e9c0";
}
.qh-agencyPic:before {
  content: "\e9c1";
}
.qh-trainSite:before {
  content: "\e9c2";
}
.qh-specialImg:before {
  content: "\e9c3";
}
.qh-specialPic:before {
  content: "\e9c4";
}
.qh-modify:before {
  content: "\e9c5";
}
.qh-rimSet:before {
  content: "\e9c6";
}
.qh-thingSet:before {
  content: "\e9c7";
}
.qh-deploy:before {
  content: "\e9c8";
}
.qh-starGrade:before {
  content: "\e9c9";
}
.qh-plot:before {
  content: "\e9ca";
}
.qh-arrowLeft:before {
  content: "\e9cb";
}
.qh-pullTop:before {
  content: "\e9cc";
}
.qh-measureGap:before {
  content: "\e9cd";
}
.qh-measure:before {
  content: "\e9ce";
}
.qh-linkAge:before {
  content: "\e9cf";
}
.qh-route:before {
  content: "\e9d0";
}
.qh-locate:before {
  content: "\e9d1";
}
.qh-makePic:before {
  content: "\e9d2";
}
.qh-fullMap:before {
  content: "\e9d3";
}
.qh-workBox:before {
  content: "\e9d4";
}
.qh-measureArea:before {
  content: "\e9d5";
}
.qh-visual:before {
  content: "\e9d6";
}
.qh-colliery:before {
  content: "\e9d7";
}
.qh-trash:before {
  content: "\e9d8";
}
.qh-comcar:before {
  content: "\e9d9";
}
.qh-experts:before {
  content: "\e9da";
}
.qh-army:before {
  content: "\e9db";
}
.qh-legend:before {
  content: "\e9dc";
}
.qh-equipImg:before {
  content: "\e9dd";
}
.qh-textNote:before {
  content: "\e9de";
}
.qh-mapOut:before {
  content: "\e9df";
}
.qh-bigsource:before {
  content: "\e9e0";
}
.qh-fireflower:before {
  content: "\e9e1";
}
.qh-pullLeft:before {
  content: "\e9e2";
}
.qh-return:before {
  content: "\e9e3";
}
.qh-arrowRight:before {
  content: "\e9e4";
}
.qh-fullScreen:before {
  content: "\e9e5";
}
.qh-unfold:before {
  content: "\e9e6";
}
.qh-fold:before {
  content: "\e9e7";
}
.qh-lock2:before {
  content: "\e9e8";
}
.qh-unlock:before {
  content: "\e9e9";
}
.qh-reTop:before {
  content: "\e9ea";
}
.qh-reDown:before {
  content: "\e9eb";
}
.qh-arrowDown:before {
  content: "\e9ec";
}
.qh-shut:before {
  content: "\e9ed";
}
.qh-selected:before {
  content: "\e9ee";
}
.qh-unselected:before {
  content: "\e9ef";
}
.qh-play:before {
  content: "\e9f0";
}
.qh-path:before {
  content: "\e9f1";
}
.qh-tube:before {
  content: "\e9f2";
}
.qh-dutyGroup:before {
  content: "\e9f3";
}
.qh-tower:before {
  content: "\e9f4";
}
.qh-channel:before {
  content: "\e9f5";
}
.qh-monitor:before {
  content: "\e9f6";
}
.qh-popList:before {
  content: "\e9f7";
}
.qh-schoolPic:before {
  content: "\e9f8";
}
.qh-listPic:before {
  content: "\e9f9";
}
.qh-bridge:before {
  content: "\e9fa";
}
.qh-ganged:before {
  content: "\e9fb";
}
.qh-online:before {
  content: "\e9fc";
}
.qh-surround:before {
  content: "\e9fd";
}
.qh-analysis:before {
  content: "\e9fe";
}
.qh-location:before {
  content: "\e9ff";
}
.qh-surAnalyse:before {
  content: "\ea00";
}
.qh-emerDuty:before {
  content: "\ea01";
}
.qh-emerDuty1:before {
  content: "\ea02";
}
.qh-resShare:before {
  content: "\ea03";
}
.qh-synmeet:before {
  content: "\ea04";
}
.qh-modAnalyse:before {
  content: "\ea05";
}
.qh-accAnalyse:before {
  content: "\ea06";
}
.qh-yuan:before {
  content: "\ea07";
}
.qh-duobian:before {
  content: "\ea08";
}
.qh-juxing:before {
  content: "\ea09";
}
.qh-line:before {
  content: "\ea0a";
}
.qh-dot:before {
  content: "\ea0b";
}
.qh-text:before {
  content: "\ea0c";
}
.qh-shrinkLeft:before {
  content: "\ea0d";
}
.qh-shrinkRight:before {
  content: "\ea0e";
}
.qh-tradeFirm:before {
  content: "\ea0f";
}
.qh-mapImg:before {
  content: "\ea10";
}
.qh-pullRight:before {
  content: "\ea11";
}
.qh-pullDown:before {
  content: "\ea12";
}
.qh-reRight:before {
  content: "\ea13";
}
.qh-scene:before {
  content: "\ea14";
}
.qh-sceneSet:before {
  content: "\ea15";
}
.qh-measure2:before {
  content: "\ea16";
}
.qh-fastSearch:before {
  content: "\ea17";
}
.qh-bufSearch:before {
  content: "\ea18";
}
.qh-claSearch:before {
  content: "\ea19";
}
.qh-speSearch:before {
  content: "\ea1a";
}
.qh-target:before {
  content: "\ea1b";
}
.qh-square:before {
  content: "\ea1c";
}
.qh-circle:before {
  content: "\ea1d";
}
.qh-map:before {
  content: "\ea1e";
}
.qh-track_changes:before {
  content: "\ea1f";
}
.qh-phonePic:before {
  content: "\ea20";
}
.qh-homePage:before {
  content: "\ea21";
}
.qh-display:before {
  content: "\ea22";
}
.qh-light:before {
  content: "\ea23";
}
.qh-medical:before {
  content: "\ea24";
}
.qh-cheer:before {
  content: "\ea25";
}
.qh-payTarget:before {
  content: "\ea26";
}
.qh-pwdPic:before {
  content: "\ea27";
}
.qh-equipPro:before {
  content: "\ea28";
}
.qh-exercise2:before {
  content: "\ea29";
}
.qh-keyArea2:before {
  content: "\ea2a";
}
.qh-emerAns:before {
  content: "\ea2b";
}
.qh-mine:before {
  content: "\ea2c";
}
.qh-emerRes:before {
  content: "\ea2d";
}
.qh-rightPic:before {
  content: "\ea2e";
}
.qh-infoPic:before {
  content: "\ea2f";
}
.qh-knodge:before {
  content: "\ea30";
}
.qh-disaster:before {
  content: "\ea31";
}
.qh-comSys:before {
  content: "\ea32";
}
.qh-officePic:before {
  content: "\ea33";
}
.qh-phoneNote:before {
  content: "\ea34";
}
.qh-faxPic:before {
  content: "\ea35";
}
.qh-notePic:before {
  content: "\ea36";
}
.qh-bigscreen:before {
  content: "\ea37";
}
