<template>
  <div class="table">
    <el-dialog
      title="查看"
      :visible.sync="tableVisible"
      :modal="true"
      :modal-append-to-body="false"
      width="800px"
      :close-on-click-modal="false"
    >
      <el-form
        class="container"
        :rules="rules"
        :model="tableRow"
        ref="tableRow"
        v-loading="loadingRow"
        label-width="150px"
      >
        <div class="inputBox">
          <div><span class="input-left">类型：{{tableRow.type == 0 ? '正常' : '异常'}}</span><span class="input-right">标题：{{tableRow.title}}</span></div>
        </div>
        <div class="inputBox">
          <div><span class="input-left">IP地址：{{tableRow.remoteAddr}}</span><span class="input-right">请求方式：{{tableRow.method}}</span></div>
        </div>
        <div class="inputBox">
          <div><span class="input-left">客户端：{{tableRow.serviceId}}</span><span class="input-right">请求时间：{{tableRow.time}}</span></div>
        </div>
        <div class="inputBox">
          <div><span class="input-left">创建时间：{{tableRow.createTime}}</span><span class="input-right">异常日志：{{tableRow.exception}}</span></div>
        </div>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  saveMenmenuByMenuId,
  sameMenuFlagSystemCodes,
  savePwdRule,
  getPwdRuleEditRule
} from "../../../api/user";
import { pwdRules } from "../../../api/rules";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {},
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tableVisible: false,
      treeData: [],
      input: "",
      loadingRow: false,
      tableRow: {},
      checkedKeys: [],
      rules: pwdRules,
    };
  },
  //方法集合
  methods: {
    parentMsg(val) {
      this.tableVisible = val;
    },
    clearTable() {
      this.tableRow = {};
    },
    getData(row) {
      this.tableRow = row;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.table {
  .container {
    height: 400px;
    overflow: auto;
  }
  .inputBox {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
    width: 100%;
    .input-left{
        display: inline-block;
        width: 300px;
    }
    .input-right{
        display: inline-block;
        width: 300px;
    }
    .input {
      width: 300px;
    }
    .label {
      text-align: right;
      width: 20%;
      margin-right: 5%;
      .red {
        color: red;
        margin-right: 3%;
      }
    }
  }
  .tree {
    width: 100%;
    display: flex;
    justify-content: center;
  }
  .commit {
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
}
</style>