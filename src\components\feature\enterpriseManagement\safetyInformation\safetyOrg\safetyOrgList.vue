<template>
  <div class="safetyOrgList">
    <div class="header">
      <div class="title">安全管理机构</div>
    </div>

    <el-table
      class="table"
      :data="tableData"
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      border
      v-loading="loading"
      height="100%"
      style="width: 100%"
    >
      <el-table-column type="index" label="序号" width="55" align="center">
      </el-table-column>
      <el-table-column
        prop="orgName"
        label="机构名称"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="orgHead"
        label="机构负责人"
        align="center"
        width="100"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="duties"
        label="职务"
        align="center"
        width="120"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="duty"
        label="职责"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="fullTimePersonnel"
        label="手机号码"
        align="center"
        width="120"
      >
      </el-table-column>
      <el-table-column
        prop="fullTimePersonnel"
        label="安全管理人数"
        align="center"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="specialJobPersonnel"
        label="特种作业人数"
        align="center"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        align="center"
        width="160"
      >
      </el-table-column>

      <el-table-column
        prop="address"
        label="操作"
        align="center"
        width="100"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button
            type="text"
            size="small"
            v-if="$store.state.login.user.user_type == 'ent'"
            @click="edit(scope.row)"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="small"
            v-if="$store.state.login.user.user_type == 'ent'"
            @click="deleter(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="params.nowPage"
        :page-size="params.pageSize"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <safetyOrgDialog
      v-if="detailVisible"
      :show.sync="detailVisible"
      :entObj.sync="safetyOrgInfo"
      @closeBoolean="closeBoolean"
    ></safetyOrgDialog>
  </div>
</template>

<script>
import { getSafetOrgList, detailSafetOrgList } from "@/api/safetyInfomation";
import safetyOrgDialog from "./safetyOrgDialog.vue";
export default {
  //import引入的组件
  name: "rescueTeam",
  components: {
    safetyOrgDialog,
  },
  props: {
    companyCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      tableData: [],
      loading: false,
      total: 0,
      detailVisible: false,
      safetyOrgInfo: {},
      params: {
        page: 1,
        pageSize: 10,
        personType: "safetyOrg",
        personName: "",
        phone: "",
        isFullTime: null,
        companyCode: this.companyCode,
        specialOperationPositions: null,
        specialEquipmentOperationPositions: null,
      },
    };
  },
  created() {},
  //方法集合
  methods: {
    handleCurrentChange(val) {
      this.params.nowPage = val;
      this.getList();
    },
    handleView(row) {
      this.safetyOrgInfo = row;
      this.detailVisible = true;
    },
    closeBoolean() {
      this.detailVisible = false;
      this.safetyOrgInfo = {};
    },
    //获取列表
    getList() {
      this.loading = true;
      getSafetOrgList(this.params)
        .then((res) => {
          if (res.data.status == 200) {
            this.tableData = res.data.data.list;
            this.total = Number(res.data.data.total);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    edit(row) {
      this.reset();
      this.open = true;
      this.disabled = false;
      this.title = "编辑救援队伍";
      const rowV = Object.assign({ orgname: row.orgName }, row);
      this.$set(this, "form", rowV);
    },
    deleter(row) {
      const id = row.teamId;
      this.$confirm("是否确认删除该救援队伍信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {})
        .catch(() => {});
    },
  },
  mounted() {
    this.getList();
  },
};
</script>
<style lang="scss" scoped>
.safetyOrgList {
  width: 100%;
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .header {
    margin-bottom: 10px;
    .title {
      font-size: 18px;
      font-family: Microsoft YaHei;
    }
  }
  .table {
    width: 100%;
    height: 100%;
  }
  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
