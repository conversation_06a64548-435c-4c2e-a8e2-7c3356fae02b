<template>
  <div class="echarts">
    <div style="width: calc(100%); height: 600px" ref="allMap"></div>
    <div class="mapChoose">
      <!-- {{currentName}} -->
      <span v-for="(item, index) in parentInfo" :key="item.code">
        <span class="title" @click="chooseArea(item, index)">{{
          item.cityName == "湖北省" ? "湖北省" : item.cityName
        }}</span>
        <span class="icon" v-show="index + 1 != parentInfo.length">></span>
      </span>
    </div>
  </div>
</template>

<script>
import echarts from "echarts";
import resize from "./mixins/resize";
import axios from "axios";
import {
  queryPageRiskPage,
  queryRiskAreaByDistCode, //区域查询企业风险  风险排名
  queryDistCodeCount, //区域查询风险等级企业统计
  getPreDistrict,
} from "@/api/riskAssessment";
import { DBService } from "vuex-along/lib/db";

export default {
  name: "allMap",
  // mixins: [resize],
  data() {
    return {
      // levelOne: levelOne,
      // qiye: qiye,
      option: {},
      currentName: "",
      parentCode: "",
      distCode: "",
      myCharts: null,
      pointData:[],
      geoJson: {
        features: [],
      },
      parentInfo: [],
      timeTitle: ["2016", "2017", "2018", "2019", "2020"],
         info: {  },
    };
  },
  async created() {
    var preD = await getPreDistrict({
      districtId: this.$store.state.login.userDistCode,
    });
    this.parentCode = preD.data.data.parentCode;
    this.currentName = preD.data.data.districtName;

    this.parentInfo = [
      {
        cityName: preD.data.data.districtName,
        code: this.$store.state.login.userDistCode,
      },
    ];
    //  getPreDistrict({
    //   districtId: this.$store.state.login.userDistCode,
    // }).then(res=>{
    //    this.parentCode = preD.data.data.parentCode;
    //    this.currentName = preD.data.data.districtName;
    // });
  },

  mounted() {
    this.getGeoJson(this.$store.state.login.userDistCode);
  },
  methods: {
    getGeoJsonPre(adcode, currentAdcode, isclick) {
      console.log("是否是地图点击", isclick);
      console.log("currentAdcode", currentAdcode);
      this.pointData = [];
      let that = this;
      AMapUI.loadUI(["geo/DistrictExplorer"], (DistrictExplorer) => {
        var districtExplorer = new DistrictExplorer();
        districtExplorer.loadAreaNode(adcode, function (error, areaNode) {
          if (isclick == "click") {
            that.getGeoJson2(currentAdcode, "click");
            //  that.geoJson.features = that.geoJson.features.filter(
            //   (item) => item.properties.adcode == currentAdcode
            // );
          } else {
            let Json = areaNode.getSubFeatures();
            that.geoJson.features = Json;
            that.geoJson.features = that.geoJson.features.filter(
              (item) =>
                item.properties.adcode == that.$store.state.login.userDistCode
            );
          }
          // if (that.geoJson.features.length === 0) return;
          that.getMapData(that.$store.state.login.userDistCode);
        });
      });
    },
    //点击下砖，区分三级
    getGeoJson2(adcode, isClick = "") {
      let that = this;
      console.log(adcode,'adcode点击下砖，区分三级');
      AMapUI.loadUI(["geo/DistrictExplorer"], (DistrictExplorer) => {
        var districtExplorer = new DistrictExplorer();
        districtExplorer.loadAreaNode(adcode, function (error, areaNode) {
          if (error) {
            console.error(error);
            return;
          }
          let Json = areaNode.getSubFeatures();
          that.geoJson.features = that.geoJson.features.filter(
            (item) => item.properties.adcode == adcode
          );
          if (that.geoJson.features.length === 0) return;
          that.getMapData(adcode);
        });
      });
    },

    getGeoJson(adcode, isClick = "") {
      let that = this;
      AMapUI.loadUI(["geo/DistrictExplorer"], (DistrictExplorer) => {
        var districtExplorer = new DistrictExplorer();
         console.log(adcode,'getGeoJson');
        districtExplorer.loadAreaNode(adcode, function (error, areaNode) {
          if (error) {
            console.error(error);
            return;
          }
          let Json = areaNode.getSubFeatures();
          if (Json.length > 0) {
            that.geoJson.features = Json;
          } else if (Json.length === 0) {
            getPreDistrict({
              districtId: that.$store.state.login.userDistCode,
            }).then((res) => {
              that.getGeoJsonPre(res.data.data.parentCode, adcode, isClick);
            });
          }
          that.getMapData(adcode);
        });
      });
    },
    //获取数据
    async getMapData(adcode) {
      //企业上图
      this.pointData = [];
      console.log('企业上图')
      let pointDataRes = await queryPageRiskPage({
        distCode: adcode,
        nowPage: 1,
        pageSize: 99999,
      });
      console.log( adcode,' this.currentAdcode',this.distCode)
      if (pointDataRes.data.status == "200") {
        if(adcode == this.distCode|| !this.distCode){
             pointDataRes.data.data.list.forEach((item) => {
              this.pointData.push({
                name: item.enterpName,
                value: [item.longitude, item.latitude, item.riskValue],
                obj: item,
              });
            });
        }
       
      }

      //区域风险等级
      queryRiskAreaByDistCode({
        distCode: adcode,
      }).then((res) => {
        if (res.data.status === 200) {
          this.geoJson.features.forEach((item) => {
            if (item.properties.adcode) {
              const obj = res.data.data.find(
                (v) => v.areaCode == item.properties.adcode
              );

              if (obj) {
                item.properties.obj = obj;
              }
            }
          });

          let mapData = this.geoJson.features.map((item) => {
            return {
              name: item.properties.name,
              value: item.properties.obj
                ? item.properties.obj.riskAreaValue
                : 0,
              cityCode: item.properties.adcode,
              restArg:item.properties.obj
            };
          });

          mapData = mapData.sort(function (a, b) {
            return b.value - a.value;
          });
          this.initEcharts(mapData, this.pointData);
        } else {
        }
      });

      // this.initEcharts(mapData);
      // debugger;
      // this.initEcharts(mapData, pointData);
      // console.log(pointData);
    },
    initEcharts(mapData, pointData) {
      var that = this;
      this.$nextTick(()=>{
      this.myChart = echarts.init(this.$refs.allMap);
      if (this.parentInfo.length === 1) {
        // echarts.registerMap("china", this.geoJson); //注册
        echarts.registerMap("map", this.geoJson); //注册
      } else {
        echarts.registerMap("map", this.geoJson); //注册
      }
      this.option = {
        timeline: {
          // data: this.timeTitle,
          show: false,
          axisType: "category",
          autoPlay: false,
          playInterval: 3000,
          left: "10%",
          right: "10%",
          bottom: "2%",
          width: "80%",
          label: {
            normal: {
              textStyle: {
                color: "rgb(179, 239, 255)",
              },
            },
            emphasis: {
              textStyle: {
                color: "#fff",
              },
            },
          },
          symbolSize: 10,
          lineStyle: {
            color: "#8df4f4",
          },
          checkpointStyle: {
            borderColor: "#8df4f4",
            color: "#53D9FF",
            borderWidth: 2,
          },
          controlStyle: {
            showNextBtn: true,
            showPrevBtn: true,
            normal: {
              color: "#53D9FF",
              borderColor: "#53D9FF",
            },
            emphasis: {
              color: "rgb(58,115,192)",
              borderColor: "rgb(58,115,192)",
            },
          },
        },
        baseOption: {
          animation: false,
          animationDuration: 900,
          animationEasing: "cubicInOut",
          animationDurationUpdate: 900,
          animationEasingUpdate: "cubicInOut",
          tooltip: {
            trigger: "axis",
            axisPointer: {
              type: "shadow",
            },
          },
          //   grid: {
          //     right: "2%",
          //     top: "12%",
          //     bottom: "8%",
          //     width: "20%",
          //   },

          geo: {
            map: this.parentInfo.length === 1 ? "map" : "map",
            zoom: 1.1,
            roam: true,
            silent: false, //鼠标移入区域变色 如果设置为true则无法高亮
            // center:
            //   this.parentInfo.length === 1
            //     ? ["118.83531246", "32.0267395887"]
            //     : false,
            // tooltip: {
            //     trigger: 'item',
            //     formatter: (p) => {
            //         let val = p.value[2];
            //         if (window.isNaN(val)) {
            //             val = 0;
            //         }
            //         let txtCon =
            //             "<div style='text-align:left'>" + p.name + ":<br />销售额：" + val.toFixed(
            //                 2) + '万</div>';
            //         return txtCon;
            //     }
            // },
            label: {
              normal: {
                show: false,
                color: "#000", //省份标签字体颜色
                formatter: (p) => {
                  return p.name;
                },
              },
              emphasis: {
                show: true,
                color: "#000",
              },
            },
            itemStyle: {
              normal: {
                areaColor: "rgba(217,217,217,0.1)",
                borderColor: "rgb(217,217,217)",
                borderWidth: 1.3,
                // shadowBlur: 5,
                // shadowColor: "rgb(217,217,217)",
                // shadowOffsetX: 3,
                // shadowOffsetY: 2,
              },
              emphasis: {
                areaColor: "rgba(217,217,217,0.1)",
                borderColor: "rgb(147,147,147)",
                // shadowBlur: 25,
              },
            },
          },
        },
        options: [],
      };
      this.timeTitle.forEach((item) => {
        // debugger
        // var xData = [],
        //   yData = [];
        // var min = mapData[item][mapData[item].length - 1].value;
        // var max = mapData[item][0].value;
        // if (mapData[item].length === 1) {
        //   min = 0;
        // }
        // mapData[item].forEach((c) => {
        //   xData.unshift(c.name);
        //   yData.unshift(c.value);
        // });
        this.option.options.push({
          title: [
            // {
            //   left: "center",
            //   top: 10,
            //   text:
            //     item +
            //     this.parentInfo[this.parentInfo.length - 1].cityName +
            //     "年" +
            //     "销售额统计图(可点击下钻到县)",
            //   textStyle: {
            //     color: "rgb(179, 239, 255)",
            //     fontSize: 16,
            //   },
            // },
            // {
            //   text: "销售总额：" + sum[item].toFixed(2) + "万",
            //   left: "center",
            //   top: "6.5%",
            //   textStyle: {
            //     color: "#FFAC50",
            //     fontSize: 26,
            //   },
            // },
          ],
          visualMap: {
            // min: min,
            // max: max,
            right: "3%",
            bottom: "2%",
            calculable: true,
            seriesIndex: [0],
            type: "piecewise", // 定义为分段型 visualMap
            inRange: {
              color: ["#326eff", "#ffd964", "#ffa656", "#b70b04"], //蓝、
            },
            textStyle: {
              color: "#000",
            },
            pieces: [
              //自定义『分段式视觉映射组件（visualMapPiecewise）』的每一段的范围，以及每一 段的文字，以及每一段的特别的样式
              { min: 200, label: "重大风险" }, // 不指定 max，表示 max 为无限大（Infinity）。

              { min: 100, max: 200, label: "较大风险" },
              { min: 20, max: 100, label: "一般风险" },
              //  {min: 10, max: 200, label: ‘10 到 200（自定义label）’},
              //  {value: 123, label: ‘123（自定义特殊颜色）’, color: ‘grey’}, // 表示 value 等于 123 的情况。
              { max: 20, label: "低风险" }, // 不指定 min，表示 min 为无限大（-Infinity）。
            ],
          },
          series: [
            {
              // name: item + "销售额度",
              // color: "rgba(69,114,167,1)",
              //   silent: false, //鼠标移入区域变色 如果设置为true则无法高亮
              type: "map",
              geoIndex: 0,
              map: "map",
              roam: true,
              zoom: 1.3,
               tooltip: {
                trigger: "item",
                formatter: function (params) {
                  // 格式化提示框的文字，下面使用模板字符串
                  // return `<span style="color:#aaaaaa">${params.data.name}</span><br />
                  //           <div style="font-size:14px;font-weight:700;">
                  //             风险等级：${params.data.obj.riskLevelName}
                  //           </div>
                  //           `;
                  return `<div class="dialoge">
                      <h3 style="color:white">区域风险统计</h3>
                                      <div class="dialogeList">
                                        <div class="item">
                        <span class="key">风险值</span>
                        <span class="value"
                              >${params.data.restArg.riskAreaValue}</span>
                      </div>
                      <div class="item">
                        <span class="key">平均值</span>
                        <span class="value"
                              >${params.data.restArg.riskAreaAverageValue}</span>
                      </div>
                      <div class="item">
                        <span class="key">最大值</span>
                        <span class="value"
                              >${params.data.restArg.riskAreaMaxValue}
                             </span>
                      </div>
                      <div class="item">
                        <span class="key">最大值来源</span>
                        <span class="value"
                              >${params.data.restArg.riskAreaMaxTargetName}
                           </span>
                      </div>
                      </div>
                      </div>`;
                },
              },
              // tooltip: {
              //   trigger: "item",
              //   formatter: (p) => {
              //     let val = p.value;
              //     if (p.name == "南海诸岛") return;
              //     if (window.isNaN(val)) {
              //       val = 0;
              //     }
              //     let txtCon =
              //       "<div style='text-align:left'>" +
              //       p.name +
              //       ":<br />销售额：" +
              //       val.toFixed(2) +
              //       "万</div>";
              //     return txtCon;
              //   },
              // },
              // label: {
              //   normal: {
              //     show: false,
              //   },
              //   emphasis: {
              //     show: false,
              //   },
              // },
              data: mapData,
            },
            {
              name: "散点",
              type: "effectScatter",
              coordinateSystem: "geo",
              rippleEffect: {
                brushType: "fill",
              },
              itemStyle: {
                normal: {
                  color: "#F4E925",
                  shadowBlur: 10,
                  shadowColor: "#333",
                },
              },
               tooltip: {
                trigger: "item",
                formatter: function (params) {
                  // 格式化提示框的文字，下面使用模板字符串
                  return `<span style="color:#aaaaaa">${params.data.name}</span><br />
                            <div style="font-size:14px;font-weight:700;">
                              风险等级：${params.data.obj.riskLevelName}                             
                            </div>
                            `;
                },
              },
             

              data: pointData,
              symbolSize: 8,
              // symbolSize: function (val) {
              //   let value = val[2];
              //   if (value == max) {
              //     return 27;
              //   }
              //   return 10;
              // },
              showEffectOn: "render", //加载完毕显示特效
            },
            // {
            //   type: "bar",
            //   barGap: "-100%",
            //   barCategoryGap: "55%",
            //   itemStyle: {
            //     normal: {
            //       barBorderRadius: 30,
            //       color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            //         {
            //           offset: 0,
            //           color: "rgb(57,89,255,1)",
            //         },
            //         {
            //           offset: 1,
            //           color: "rgb(46,200,207,1)",
            //         },
            //       ]),
            //     },
            //     emphasis: {
            //       show: false,
            //     },
            //   },
            //   data: yData,
            // },
          ],
        });
      });
      this.myChart.setOption(this.option, true);
      this.myChart.off("click");

      this.myChart.on("click", this.echartsMapClick);
      //设置鼠标移入指定省份颜色不变的效果
      this.myChart.off("mouseover");
      this.myChart.on("mouseover", this.mouseFn);
      })
    },

    mouseFn(params) {
      // params.data.cityCode
      //  this.myChart.dispatchAction({
      //    type: 'downplay'
      //   });
      // debugger
      // console.log(this)
      // this.myChart._model.option.geo[0].emphasis.itemStyle.areaColor=params.color;
      // this.myChart._model.option.geo[0].emphasis.itemStyle.borderColor=params.color;
      // this.myChart._model.option.geo[0].emphasis.itemStyle.color='#fff'
      // this.option.baseOption.geo.itemStyle.emphasis.areaColor=params.color
      // this.option.baseOption.geo.itemStyle.emphasis.borderColor=params.color
      // this.myChart.setOption(this.option, true);
    },

    //点击下钻
    echartsMapClick(params) {
      this.distCode = params.data.cityCode;
      this.$emit("distCodeFn", params.data.cityCode);
      // debugger;
      // if (!params.data) {
      //   return;
      // }
      if (
        this.parentInfo[this.parentInfo.length - 1].code == params.data.cityCode
      ) {
        return;
      }
      if (params.data.obj) {
        this.$router.push({
          path: "/gardenEnterpriseManagement/entManagement",
        });
        this.$store.commit("login/updataActiveName", "riskDynamics");
        this.$store.commit("controler/updateEntId", params.data.obj.enterpId);
      }
      let data = params.data;
      this.parentInfo.push({
        cityName: data.name,
        code: data.cityCode,
      });
      this.getGeoJson(data.cityCode, "click");
    },
    //选择切换市县
    chooseArea(val, index) {
      this.$emit("distCodeFn", val.code);
      this.distCode = val.code;
      // if()
      if (this.parentInfo.length === index + 1) {
        return;
      }
      this.parentInfo.splice(index + 1);
      this.getGeoJson(this.parentInfo[this.parentInfo.length - 1].code);
    },
  },
};
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  position: relative;
  // background: url("../assets/bg1.jpg") no-repeat;
  background-size: 100% 100%;
}

.mapChoose {
  position: absolute;
  left: 0;
  top: 10px;
  // color: #000;
  .title {
    padding: 5px;
    border-top: 1px solid #409eff;
    border-bottom: 1px solid #409eff;
    cursor: pointer;
  }

  .icon {
    font-family: "simsun";
    font-size: 25px;
    margin: 0 11px;
  }
}
</style>
