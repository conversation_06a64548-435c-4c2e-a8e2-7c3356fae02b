<template>
  <div>
    <!-- 111 -->
    <!-- <iframe
      class="iframeBox"
      src="https://jy.hbsis.gov.cn/#/web/home"
      style="border: 0"
      width="100%"
      height="100%"
    ></iframe> -->


      <!-- <iframe
      class="iframeBox"
      :src="'zhpt/whsystematism?ticket='+tokenLisk"
      style="border: 0"
      width="100%"
      height="100%"
    ></iframe> -->

<!-- http://hbwh.hbsis.gov.cn:9080 -->

     <iframe
      class="iframeBox"
      :src="'https://yyzc.hbsis.gov.cn:30001/whsystematism?ticket='+ tokenLisk"
      style="border: 0"
      width="100%"
      height="100%"
    ></iframe>



  </div>
</template>
<script>
import {
  getTokenGenerateLink, //第三方系统对接生成一次性token
  getTokenCheck, //第三方系统对接校验token返回用户数据
} from "@/api/companyParticularJob";
var dayjs = require("dayjs");

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import axios from "axios";
export default {
  components: {},
  data() {
    return {
      tokenLisk:'',
      iframUrl:'',
      localUrl:''
    };
  },

  filters: {},
  created() {    
    getTokenGenerateLink({ token: this.$store.state.login.token }).then(
      (res) => {
        if (res.data.status == 200) {   
          this.tokenLisk = res.data.data.accessToken; 
           console.log(this.tokenLisk,'aaaaaaabbbbbb')   
          // this.localUrl=url        
        }
      }
    );
  },

  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    // this.getData();
    // this.serchStatusList();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.iframeBox {
  height: calc(100vh - 80px);
  padding-top: 0;
  width: 100%;
  // background: #f3f6f8;
}
</style>

