<template>
  <div class="FillInformation" :key="fillInformationKey">
    <div class="div1" ref="div" v-loading="loading">
      <div class="table">
        <ul class="container">
          <li>
            <div class="l">企业名称</div>
            <div class="r">{{ enterprise.enterpName }}</div>
          </li>
          <li>
            <div class="l">企业简称</div>
            <div class="r">{{ enterprise.entShort }}</div>
          </li>
          <li class="">
            <div class="l">企业类型大类</div>
            <div class="r" v-if="enterprise.enterpriseType == '01'">生产</div>
            <!-- <div class="r"
                 v-else-if="enterprise.enterpriseType == '02'">
              经营
            </div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '03'">
              使用
            </div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '04'">
              第一类非药品易制毒
            </div> -->
            <div class="r">{{ enterprise.enterpriseTypeName }}</div>
          </li>
          <li class="">
            <div class="l">企业类型小类</div>
            <div class="r">
              {{ managementType(enterprise.managementType) }}
            </div>
          </li>
          <li>
            <div class="l">企业编码</div>
            <div class="r">{{ enterprise.enterpId }}</div>
          </li>

          <li>
            <div class="l">统一社会信用代码</div>
            <div class="r">{{ enterprise.entcreditCode }}</div>
          </li>
          <li>
            <div class="l">信用代码有效开始日期</div>
            <div class="r">{{ enterprise.entcreditcodEstartDATE }}</div>
          </li>
          <li>
            <div class="l">信用代码有效结束日期</div>
            <div class="r">{{ enterprise.entcreditcodeEndDATE }}</div>
          </li>
          <li>
            <div class="l">统一社会信用代码附件</div>
            <!-- <div class="r">{{ enterprise.attachmentEntcreditcode }}</div> -->
            <img
              :src="`data:image/png;base64,${enterprise.attachmentEntcreditcode}`"
              alt=""
            />
          </li>
          <!-- <li class="">
              <div class="l">是否获取到许可证</div>
              <div class="r">{{ enterprise.isLicense == 0 ? "否" : "是" }}</div>
            </li> -->
          <li>
            <div class="l">危险化学品安全生产许可证附件</div>
            <div class="r">
              <img
                :src="`data:image/png;base64,${enterprise.attachmentLicense}`"
                alt=""
              />
            </div>
          </li>
          <li v-if="enterprise.enterpriseType == '01'">
            <div class="l">安全生产许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li>
          <li v-if="enterprise.enterpriseType == '02'">
            <div class="l">危险化学品经营许可证</div>
            <div class="r">{{ enterprise.msdslicensenum }}</div>
          </li>
          <li v-if="enterprise.enterpriseType == '03'">
            <div class="l">危险化学品安全使用许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li>
          <li v-if="enterprise.enterpriseType == '04'">
            <div class="l">危险化学品安全使用许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li>
          <li>
            <div class="l">许可证有效开始日期</div>
            <div class="r">{{ enterprise.secLicenseNumStartDate }}</div>
          </li>
          <li>
            <div class="l">许可证有效结束日期</div>
            <div class="r">{{ enterprise.secLicenseNumEndDate }}</div>
          </li>
          <li>
            <div class="l">许可范围</div>
            <div class="r">
              {{ enterprise.scopeSafety }}
            </div>
          </li>
          <li>
            <div class="l">是否涉及重点监管危化品</div>
            <div class="r">
              {{ enterprise.isInvolvedChemicals == 1 ? "是" : "否" }}
            </div>
          </li>
          <li>
            <div class="l">是否涉及重点监管工艺</div>
            <div class="r">
              {{ enterprise.isInvolvedDangercraft == 1 ? "是" : "否" }}
            </div>
          </li>
          <li>
            <div class="l">涉是否构成重大危险源</div>
            <div class="r">
              {{ enterprise.isConstituteDanger == 1 ? "是" : "否" }}
            </div>
          </li>
          <li class="" v-if="enterprise.enterpriseType == '03'">
            <div class="l">是否涉及化学反应</div>
            <div class="r">
              {{ enterprise.isChemicalReaction == 1 ? "是" : "否" }}
            </div>
          </li>
          <!-- <li class="">
            <div class="l">生产场所地址</div>
            <div class="r">
              {{ enterprise.bornAddress }}
            </div>
          </li> -->
          <li class="">
            <div class="l">法人代表</div>
            <div class="r">{{ enterprise.legalrepper }}</div>
          </li>

          <li class="">
            <div class="l">法人代表手机</div>
            <div class="r">{{ enterprise.legalreppTel }}</div>
          </li>
          <li>
            <div class="l">所属行业门类</div>
            <div class="r">{{ enterprise.industryCategory }}</div>
          </li>
          <li>
            <div class="l">所属行业大类</div>
            <div class="r">{{ enterprise.industryClass }}</div>
          </li>
          <li>
            <div class="l">经济类型代码（大类）</div>
            <div class="r">{{ enterprise.economyTypeCode }}</div>
          </li>
          <li>
            <div class="l">经济类型代码（小类）</div>
            <div class="r">{{ enterprise.economyTypeCode2 }}</div>
          </li>
          <li class="">
            <div class="l">营业执照经营范围</div>
            <div class="r">{{ enterprise.businessLicense }}</div>
          </li>
          <li>
            <div class="l">工商注册地址</div>
            <div class="r">{{ enterprise.businessAddress }}</div>
          </li>
          <li>
            <div class="l">生产场所地址</div>
            <div class="r">{{ enterprise.bornAddress }}</div>
          </li>
          <li>
            <div class="l">企业经营状态</div>
            <div class="r">
              {{ enterprise.businessType == 0 ? "正常" : "长期停产" }}
            </div>
          </li>
          <li>
            <div class="l">注册日期</div>
            <div class="r">{{ enterprise.regDate }}</div>
          </li>
          <li>
            <div class="l">是否在化工园区</div>
            <div class="r">{{ enterprise.inPark == 1 ? "是" : "否" }}</div>
          </li>
          <li>
            <div class="l">所属化工园区区名称</div>
            <div class="r">{{ enterprise.parkName }}</div>
          </li>
          <!-- <li class="">
            <div class="l">职工总人数</div>
            <div class="r">{{ enterprise.bornAddress }}</div>
          </li> -->
          <li>
            <div class="l">企业负责人</div>
            <div class="r">{{ enterprise.respper }}</div>
          </li>
          <li>
            <div class="l">企业负责人手机</div>
            <div class="r">{{ enterprise.responsiblePhone }}</div>
          </li>
          <li>
            <div class="l">安全负责人</div>
            <div class="r">{{ enterprise.principal }}</div>
          </li>
          <li>
            <div class="l">安全负责人手机</div>
            <div class="r">{{ enterprise.safetyResponsiblePhone }}</div>
          </li>
          <li>
            <div class="l">安全值班电话</div>
            <div class="r">{{ enterprise.safeTel }}</div>
          </li>
          <li>
            <div class="l">职工总人数</div>
            <div class="r">{{ enterprise.employmentNum }}</div>
          </li>
          <li>
            <div class="l">占地面积(单位：平方米)</div>
            <div class="r">{{ enterprise.area }}</div>
          </li>

          <li class="">
            <div class="l">邮政编码</div>
            <div class="r">{{ enterprise.postCode }}</div>
          </li>
          <li class="">
            <div class="l">行政区划</div>
            <div class="r">{{ enterprise.districtName }}</div>
          </li>
          <li class="">
            <div class="l">行政区划编码</div>
            <div class="r">{{ enterprise.districtCode }}</div>
          </li>
          <li class="">
            <div class="l">企业规模</div>
            <div class="r" v-if="enterprise.scalecode == 1">大型</div>
            <div class="r" v-else-if="enterprise.scalecode == 2">中型</div>
            <div class="r" v-else-if="enterprise.scalecode == 3">小型</div>
            <div class="r" v-else-if="enterprise.scalecode == 4">微型</div>
            <div class="r" v-else></div>
          </li>
          <li class="">
            <div class="l">经度</div>
            <div class="r">{{ enterprise.longitude }}</div>
          </li>
          <li class="">
            <div class="l">纬度</div>
            <div class="r">{{ enterprise.latitude }}</div>
          </li>
          <li class="">
            <div class="l">企业网址</div>
            <div class="r">{{ enterprise.webSite }}</div>
          </li>
          <li class="">
            <div class="l">企业负责人电话</div>
            <div class="r">{{ enterprise.responsiblePhone }}</div>
          </li>
          <li class="">
            <div class="l">企业负责人专业</div>
            <div class="r">{{ enterprise.enterProPeo }}</div>
          </li>
          <li class="">
            <div class="l">主要负责人是否有化工大专以上学历</div>
            <div class="r">
              {{ enterprise.isPersonEdu == 1 ? "是" : "否" }}
            </div>
          </li>
          <li class="">
            <div class="l">实际控制人是否有化工大专以上学历</div>
            <div class="r">
              {{ enterprise.isControllerEdu == 1 ? "是" : "否" }}
            </div>
          </li>
          <li class="">
            <div class="l">安全负责人电话</div>
            <div class="r">{{ enterprise.safetyResponsiblePhone }}</div>
          </li>
          <li class="">
            <div class="l">安全负责人专业</div>
            <div class="r">{{ enterprise.secuOffiPro }}</div>
          </li>
          <li class="">
            <div class="l">专职安全管理人员数</div>
            <div class="r">{{ enterprise.safeManageNum }}</div>
          </li>
          <li class="">
            <div class="l">注册安全工程师配备数量</div>
            <div class="r">{{ enterprise.safeEngineerNum }}</div>
          </li>
          <li class="">
            <div class="l">涉及两重点一重大作业人员数</div>
            <div class="r">{{ enterprise.majorHazardNum }}</div>
          </li>
          <li class="">
            <div class="l">涉及两重点一重大作业有大专及以上学历人数</div>
            <!-- <div class="r">{{ enterprise.majorHazardNum }}</div> -->
            <div class="r">{{ enterprise.majorHazardEduNum }}</div>
          </li>
          <li class="">
            <div class="l">涉及爆炸性危化品作业人员数</div>
            <div class="r">{{ enterprise.explosiveOperationNum }}</div>
          </li>
          <li class="">
            <div class="l">涉及爆炸性危化品作业有大专及以上学历人数</div>
            <div class="r">{{ enterprise.explosiveOperationEduNum }}</div>
          </li>
          <li class="">
            <div class="l">特种作业人员数</div>
            <div class="r">{{ enterprise.peopleOperation }}</div>
          </li>
          <li class="">
            <div class="l">剧毒化学品作业人员数</div>
            <div class="r">{{ enterprise.peopleToxic }}</div>
          </li>
          <li class="">
            <div class="l">危险化学品作业人员数</div>
            <div class="r">{{ enterprise.peopleHazard }}</div>
          </li>

          <li class="">
            <div class="l">安全生产标准化等级</div>
            <div class="r">{{ enterprise.standardLevel }}</div>
          </li>
          <li class="">
            <div class="l">安全事故情况</div>
            <div class="r">{{ enterprise.acciSiuation }}</div>
          </li>
          <li class="">
            <div class="l">企业周边情况</div>
            <div class="r">{{ enterprise.surroundinfoCode }}</div>
          </li>
          <li class="">
            <div class="l">年度生产总值</div>
            <div class="r">{{ enterprise.grossProductNum }}</div>
          </li>
          <li class="" v-if="enterprise.enterpriseType == '03'">
            <div class="l"></div>
            <div class="r"></div>
          </li>
          <li class="" v-if="enterprise.enterpriseType == '03'">
            <div class="l"></div>
            <div class="r"></div>
          </li>
          <li class="lang bottom">
            <div class="l">企业简介</div>
            <div class="r">{{ enterprise.companyProfile }}</div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import { getInformationBasicInfo } from "@/api/entList";
export default {
  //import引入的组件
  name: "FillInformation",
  components: {},
  data() {
    return {
      enterprise: {},
      enterpriseId: {},
      loading: false,
      fillInformationKey: "",
    };
  },
  //方法集合
  methods: {
    managementType(type) {
      let val = "";
      switch (type) {
        case "10":
          val = "危险化学品生产企业";
          break;
        case "20":
          val = "危险化学品经营企业";
          break;
        case "21":
          val = "加油站";
          break;
        case "22":
          val = "仓储经营";
          break;
        case "23":
          val = "带储存经营（构成重大危险源）";
          break;
        case "24":
          val = "带储存经营（不构成重大危险源）";
          break;
        case "30":
          val = "危险化学品使用企业（使用许可）";
          break;
        case "40":
          val = "化工企业（不实施使用许可）";
          break;
        case "50":
          val = "医药企业";
          break;
        case "60":
          val = "气体充装企业";
          break;
        case "70":
          val = "油气储存企业";
          break;
        case "90":
          val = "其它企业";
          break;
        default:
          val = " ";
          break;
      }
      return val;
    },
    getData(id) {
      this.loading = true;
      this.$nextTick(() => {
        this.$refs.div.style.overflow = "hidden";
      });
      getInformationBasicInfo(id).then((res) => {
        this.fillInformationKey = new Date() + "key";
        this.enterprise = res.data.data.enterprise;
        this.danger = res.data.data.danger;
        this.hazarchem = res.data.data.hazarchem;
        this.regprocess = res.data.data.regprocess;
        this.$nextTick(() => {
          this.$refs.div.style.overflowY = "scroll";
          this.loading = false;
        });
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.FillInformation {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    height: 60vh;
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-right: 1px solid rgb(231, 231, 231);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 99.97%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          //   border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 16.65%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 82.5%;
            padding: 5px 10px;
            flex-wrap: wrap;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
