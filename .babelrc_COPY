{"presets": [["@babel/preset-env", {"modules": false, "targets": {"browsers": ["> 1%", "last 2 versions", "not ie <= 8"]}}]], "plugins": [["component", {"libraryName": "element-ui", "styleLibraryName": "theme-chalk"}], "transform-vue-jsx", ["@babel/plugin-transform-runtime", {"corejs": 2}], "@babel/plugin-syntax-dynamic-import", "@babel/plugin-syntax-import-meta", "@babel/plugin-proposal-class-properties", "@babel/plugin-proposal-json-strings", ["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-proposal-function-sent", "@babel/plugin-proposal-export-namespace-from", "@babel/plugin-proposal-numeric-separator", "@babel/plugin-proposal-throw-expressions", "@babel/plugin-proposal-nullish-coalescing-operator", "@babel/plugin-proposal-optional-chaining"]}