<template>
  <div class="specialWork">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 特殊作业分析
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <!-- 添加 tab 切换 -->
    <el-tabs v-model="activeTab" class="special-tabs">
      <!-- 特殊作业分析 tab -->
      <el-tab-pane label="特殊作业管理" name="analysis">
        <div>
          <div class="seach-part">
            <div class="l">
              <el-cascader
                v-show="
                  (user.user_type === 'gov' && user.distRole != '2') ||
                  user.user_type === 'park'
                "
                placeholder="请选择地市/区县"
                :options="district"
                v-model="districtVal"
                clearable
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                @change="handleChange"
                :show-all-levels="true"
                size="mini"
              ></el-cascader>

              <el-input
                v-show="user.user_type === 'gov' || user.user_type === 'park'"
                v-model.trim="enterpName"
                placeholder="请输入企业名称"
                class="input"
                clearable
                @clear="clearKey"
                style="width: 200px"
                size="mini"
              ></el-input>
              <el-select
                v-model="ReportType"
                placeholder="请选择作业类型"
                clearable
                size="mini"
              >
                <el-option
                  v-for="item in ReportTypeOpt"
                  :key="item.id"
                  :label="item.label"
                  :value="item.id"
                >
                </el-option>
              </el-select>
              <!-- datetimerange daterange-->
              <el-date-picker
                v-model="datePickerValue"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                unlink-panels
                size="mini"
                style="width: 370px"
                value-format="yyyy-MM-dd HH:mm:ss"
                :clearable="false"
                class="date-picker"
              ></el-date-picker>

              <el-button type="primary" size="mini" @click="getData()"
                >查询</el-button
              >
              <CA-button type="primary" size="mini" plain @click="exportExcel"
                >导出
              </CA-button>
            </div>
          </div>
          <div class="table-main">
            <div class="table-top">
              <!-- <h2>特殊作业管理</h2> -->
              <!-- <el-button type="primary"
                         size="mini"
                         @click="openDetails('新增特殊作业')"
                         v-if="user.user_type === 'ent'">新增
               </el-button> -->
            </div>
            <div v-loading="loading">
              <div class="table">
                <el-table
                  :data="tableData"
                  :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                  border
                  style="width: 100%"
                >
                  <!-- <el-table-column prop="i"
                                   type="selection"
                                   width="45"
                                   fixed="left"
                                   align="center">
                  </el-table-column> -->
                  <el-table-column
                    prop="i"
                    type="index"
                    label="序号"
                    width="60"
                    align="center"
                  >
                  </el-table-column>

                  <el-table-column
                    prop="jobName"
                    label="企业名称"
                    align="center"
                    min-width="300"
                    :show-overflow-tooltip="true"
                  >
                    <template slot-scope="scope">
                      <span
                        @click="goEnt(scope.row)"
                        type="text"
                        class="enterpName"
                      >
                        {{ scope.row.orgName }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="districtName"
                    label="行政区划"
                    align="center"
                    width="150"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="jobBigTypeName"
                    label="作业类型"
                    align="center"
                    width="150"
                    :show-overflow-tooltip="true"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="jobSmallTypeName"
                    label="作业类型小类"
                    align="center"
                    width="150"
                    :show-overflow-tooltip="true"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="jobStartTime"
                    label="作业开始时间"
                    align="center"
                    width="160"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="jobEndTime"
                    label="作业结束时间"
                    align="center"
                    width="160"
                  >
                  </el-table-column>
                  <el-table-column
                    prop="workContent"
                    label="作用内容"
                    align="center"
                    width="155"
                  >
                    <!-- <template slot-scope="{ row }">
                      <span v-if="row.contractor == 1">是</span>
                      <span v-else-if="row.contractor == 0">否</span>
                      <span v-else></span>
                    </template> -->
                  </el-table-column>
                  <el-table-column
                    prop="electronicTicketUrl"
                    label="电子作业票"
                    align="center"
                    width="110"
                    v-if="user.user_type === 'ent'"
                  >
                    <template slot-scope="{ row }">
                      <el-button
                        type="text"
                        v-if="
                          row.electronicTicketUrl &&
                          row.electronicTicketUrl.length > 0
                        "
                        @click="openImg(row)"
                        >查看附件</el-button
                      >
                    </template>
                  </el-table-column>
                  <el-table-column
                    prop="i"
                    label="操作"
                    align="center"
                    width="220"
                    fixed="right"
                  >
                    <template slot-scope="scope">
                      <el-button
                        type="text"
                        @click="openDetails('特殊作业详情', scope.row)"
                        >详情
                      </el-button>
                      <el-button
                        type="text"
                        @click="openDetails('编辑特殊作业', scope.row)"
                        v-if="user.user_type === 'ent'"
                        >编辑
                      </el-button>
                      <!-- <el-button type="text"
                                 @click="abnormalEquipmentDel(scope.row)"
                                 v-if="user.user_type === 'ent'">
                        删除
                      </el-button> -->
                    </template>
                  </el-table-column>
                </el-table>
              </div>
              <div class="pagination">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page.sync="currentPage"
                  background
                  layout="total, prev, pager, next"
                  :total="total"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 分析统计 tab -->
      <el-tab-pane label="分析统计" name="statistics">
        <div class="statistics-content">
          <!-- 工具栏 -->
          <div class="seach-part">
            <div class="l">
              <el-cascader
                v-show="
                  (user.user_type === 'gov' && user.distRole != '2') ||
                  user.user_type === 'park'
                "
                placeholder="请选择地市/区县"
                :options="district"
                v-model="statisticsDistrictVal"
                clearable
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                @change="handleStatisticsDistrictChange"
                :show-all-levels="true"
                size="mini"
              ></el-cascader>

              <el-date-picker
                v-model="statisticsDate"
                type="date"
                placeholder="选择日期"
                size="mini"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
              <el-button
                type="primary"
                size="mini"
                @click="handleStatisticsSearch"
                >查询</el-button
              >
              <CA-button
                type="primary"
                size="mini"
                plain
                @click="handleStatisticsExport"
                >导出</CA-button
              >
            </div>
          </div>

          <!-- 统计表格 -->
          <div class="table-main">
            <div class="table">
              <el-table
                :data="statisticsData"
                :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
                border
                style="width: 100%"
              >
                <el-table-column
                  v-for="column in statisticsColumns"
                  :key="column.prop"
                  v-bind="column"
                />
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="1000px"
      top="5vh"
      @close="closeDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog" v-loading="loading">
        <el-form ref="ruleForm" :model="form" size="medium" :rules="rules">
          <el-descriptions :column="2" border :labelStyle="labelStyle">
            <el-descriptions-item>
              <template slot="label">企业名称</template>
              <el-form-item>
                <el-input disabled v-model.trim="form.orgName"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">行政区划</template>
              <el-form-item>
                <el-input disabled v-model.trim="form.districtName"></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>作业类型大类
              </template>
              <el-form-item prop="jobBigType">
                <el-select
                  v-model="form.jobBigType"
                  placeholder="请选择作业类型大类"
                  clearable
                  style="width: 100%"
                  :disabled="disabled"
                  @change="filterJobBigType(form.jobBigType, true)"
                  :loading="remoteBig"
                >
                  <el-option
                    v-for="item in ReportTypeOpt"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>作业类型小类
              </template>
              <el-form-item prop="jobSmallType">
                <el-select
                  v-model="form.jobSmallType"
                  placeholder="请先选择作业类型大类/再选择作业类型小类"
                  clearable
                  :disabled="disabled || !form.jobBigType"
                  style="width: 100%"
                  :loading="remoteSmall"
                >
                  <el-option
                    v-for="item in jobSmallTypeOpt"
                    :key="item.id"
                    :label="item.label"
                    :value="item.id"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>作业开始时间
              </template>
              <el-form-item prop="jobStartTime">
                <el-date-picker
                  type="datetime"
                  placeholder="选择作业开始时间"
                  style="width: 100%"
                  :disabled="disabled"
                  v-model="form.jobStartTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  @change="handleChange1"
                  class="date-picker"
                  :picker-options="{
                    disabledDate(time) {
                      if (time) {
                        return (
                          time.getTime() > new Date(form.jobEndTime).getTime()
                        );
                      }
                    },
                    format: 'HH:mm:ss',
                    selectableRange: `00:00:00 - ${
                      form.jobEndTime
                        ? new Date(form.jobEndTime).Format('hh:mm:ss')
                        : '23:59:59'
                    }`,
                  }"
                >
                </el-date-picker>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>作业结束时间
              </template>
              <el-form-item prop="jobEndTime">
                <el-date-picker
                  type="datetime"
                  placeholder="请选择作业结束时间"
                  style="width: 100%"
                  :disabled="disabled"
                  v-model="form.jobEndTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  class="date-picker"
                  :picker-options="{
                    disabledDate(time) {
                      return (
                        time.getTime() < new Date(form.jobStartTime).getTime()
                      );
                    },
                    format: 'HH:mm:ss',
                    selectableRange: `${
                      form.jobStartTime
                        ? new Date(form.jobStartTime).Format('hh:mm:ss')
                        : '00:00:00'
                    } - 23:59:59`,
                  }"
                >
                </el-date-picker>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>是否重大危险源
              </template>
              <el-form-item prop="dangerFlag">
                <el-select
                  v-model="form.dangerFlag"
                  placeholder="请选择是否重大危险源"
                  :disabled="disabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dangerFlagOpt"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>作业地址/重大危险源
              </template>

              <div v-if="form.dangerFlag == 0">其它</div>

              <div v-else>
                <el-form-item prop="dangerId">
                  <el-select
                    v-model="form.dangerId"
                    :disabled="disabled"
                    clearable
                    placeholder="请选择重大危险源"
                    @change="changeDangerId"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in dangerList"
                      :key="item.dangerId"
                      :label="item.dangerName"
                      :value="item.dangerId"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>设备名称
              </template>
              <div v-if="form.dangerFlag == 0">其它</div>

              <div v-else>
                <el-form-item prop="deviceId">
                  <el-select
                    v-model="form.deviceId"
                    :disabled="disabled"
                    placeholder="请选择设备名称"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in options1"
                      :key="item.equipmentCode"
                      :label="item.equipmentName"
                      :value="item.equipmentCode"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-descriptions-item>

            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>是否承包商作业
              </template>
              <el-form-item prop="contractor">
                <el-select
                  v-model="form.contractor"
                  placeholder="请选择是否承包商作业"
                  clearable
                  :disabled="disabled"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in isYesOrNoOpt"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">作业单位</template>
              <el-form-item prop="jobWork">
                <el-input
                  v-model="form.jobWork"
                  :disabled="disabled"
                  placeholder="请输入作业单位"
                  maxlength="40"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>项目负责人
              </template>
              <el-form-item prop="jobLeader">
                <el-input
                  v-model.trim="form.jobLeader"
                  placeholder="请输入项目负责人"
                  clearable
                  maxlength="20"
                  show-word-limit
                  :disabled="disabled"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="redDot">*</span>负责人电话
              </template>
              <el-form-item prop="leaderInformation">
                <el-input
                  placeholder="请输入负责人电话"
                  :disabled="disabled"
                  v-model.trim="form.leaderInformation"
                  maxlength="11"
                  show-word-limit
                  oninput="value = value.replace(/[^0-9]/g,'')"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template slot="label">
                <span class="redDot">*</span>作业地点
              </template>
              <el-form-item prop="workAddress">
                <el-input
                  placeholder="请输入作业地点"
                  maxlength="50"
                  show-word-limit
                  :disabled="disabled"
                  v-model.trim="form.workAddress"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template slot="label"> 地图选点</template>
              <egisMap
                :islistener="false"
                :isdetail="disabled"
                ref="detailMap"
                :datas="form"
                style="height: 300px"
                @mapCallback="mapcallback"
              ></egisMap>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template slot="label">
                <span class="redDot">*</span>作业内容
              </template>
              <el-form-item prop="workContent">
                <el-input
                  type="textarea"
                  :rows="2"
                  maxlength="500"
                  show-word-limit
                  v-model.trim="form.workContent"
                  :disabled="disabled"
                ></el-input>
              </el-form-item>
            </el-descriptions-item>
            <el-descriptions-item :span="2">
              <template slot="label">
                <span class="redDot">*</span>附件
              </template>
              <el-form-item prop="electronicTicketUrl">
                <AttachmentUpload
                  :attachmentlist="form.electronicTicketUrl"
                  :limit="5"
                  type="all"
                  v-bind="{}"
                  :editabled="disabled"
                ></AttachmentUpload>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
      <!-- {{title}} =============={{title!='特殊作业详情'}} -->
      <div
        slot="footer"
        style="display: flex; justify-content: center"
        v-if="title != '特殊作业详情'"
      >
        <div></div>

        <el-button @click="closeDialog()">取 消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="submit()"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      title="查看附件"
      :visible.sync="dialogImg"
      width="900px"
      top="5vh"
      @close="dialogFileSrc = []"
      :close-on-click-modal="false"
    >
      <div style="">
        <AttachmentUpload
          :attachmentlist="dialogFileSrc"
          :limit="5"
          type="all"
          :editabled="true"
        ></AttachmentUpload>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from "vuex";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import {
  getCompanyParticularJobList,
  getCompanyParticularJob,
  getCompanyParticularJobAdd,
  getCompanyParticularJobDelete,
  getCompanyParticularJobUpdate,
  getCompanyParticularJobExport,
  getCompanyParticularJobType,
} from "@/api/companyParticularJob";
import {
  getSpecialWorkStatistics,
  exportSpecialWorkStatistics,
} from "@/api/workingAcc";
import downloadFuc from "../../../../api/download/download";
import { getfindByEnterpId, getEnterpriseId } from "@/api/user";
import { getCompanyParticularJobTypeByParentCode } from "@/api/companyParticularJob";
import { getEquipmentData } from "@/api/dailySafety";
import { getSelectData } from "@/api/entList";
// import { parseTime } form "../../../../utils";
export default {
  name: "specialWork",
  components: {
    AttachmentUpload,
  },
  data() {
    return {
      enterpid: "",
      districtVal: this.userDistCode || "",
      ReportType: "",
      declareReason: "",
      dangerList: [], //重大危险源下拉
      options1: [],
      jobName: "",
      jobType: "",
      title: "",
      dialogVisible: false,
      disabled: true,
      rules: {
        jobBigType: [
          { required: true, message: "请选择作业类型大类", trigger: "blur" },
        ],
        jobSmallType: [
          { required: true, message: "请选择作业类型小类", trigger: "blur" },
        ],
        jobStartTime: [
          { required: true, message: "请选择作业开始时间", trigger: "blur" },
        ],
        jobEndTime: [
          { required: true, message: "请选择作业结束时间", trigger: "blur" },
        ],
        //
        dangerFlag: [
          { required: true, message: "请选择重大危险源", trigger: "blur" },
        ],
        //
        dangerId: [
          {
            required: true,
            message: "请选择作业地址/重大危险源",
            trigger: "blur",
          },
        ],

        deviceId: [
          { required: true, message: "请选择设备名称", trigger: "blur" },
        ],

        contractor: [
          { required: true, message: "请选择是否承包商作业", trigger: "blur" },
        ],
        jobLeader: [
          { required: true, message: "请输入项目负责人", trigger: "blur" },
        ],
        leaderInformation: [
          { required: true, message: "负责人联系方式", trigger: "blur" },
        ],
        workAddress: [
          { required: true, message: "请输入作业地点", trigger: "blur" },
        ],
        workContent: [
          { required: true, message: "请输入作业内容", trigger: "blur" },
        ],
        electronicTicketUrl: [
          { required: true, message: "请上传相关附件", trigger: "blur" },
        ],
      },
      labelStyle: {
        textAlign: "center",
        backgroundColor: "rgb(242, 246, 255)",
      },
      ReportTypeOpt: [],
      jobSmallTypeOpt: {},
      isYesOrNoOpt: [
        {
          value: "0",
          label: "否",
        },
        {
          value: "1",
          label: "是",
        },
      ],
      dangerFlagOpt: [
        {
          value: "0",
          label: "否",
        },
        {
          value: "1",
          label: "是",
        },
      ],
      enterpName: "",
      datePickerValue: [
        new Date(new Date().getTime() - (720 * 60 * 60 * 1000 - 1)).Format(
          "yyyy-MM-dd 00:00:00"
        ),
        new Date().Format("yyyy-MM-dd 23:59:59"),
      ],
      state: "",
      startTime: "",
      endTime: "",
      loading: false,
      btnLoading: false,
      selection: [],
      tableData: [],
      distCode: this.userDistCode || "",
      currentPage: 1,
      size: 10,
      total: 0,
      form: {
        applicat: "",
        contractor: "",
        electronicTicketUrl: [
          //   {
          //     attachId: "",
          //     msg: "",
          //     name: "",
          //     status: "",
          //     url: "",
          //     urlOuterNet: "",
          //   },
        ],
        id: "",
        jobBigType: "420000",
        jobCertificateLevel: "",
        jobEndTime: "",
        jobLeader: "",
        jobName: "",
        jobPeople: "",
        jobPermitNumber: "",
        jobSmallType: "",
        jobStartTime: "",
        jobType: "",
        jobWork: "",
        latitude: "30.580942",
        leaderInformation: "",
        longitude: "114.311467",
        orgCode: "",
        safetyInformation: "",
        safetyPeople: "",
        updateBy: "",
        updateTime: "",
        workAddress: "",
        workContent: "",
        dangerFlag: "",
        dangerId: "",
        deviceId: "",
      },
      dialogFileSrc: "",
      dialogImg: false,
      administrativeList: [],
      remoteSmall: false,
      remoteBig: false,
      activeTab: "analysis",
      region: "",
      statisticsDate: "",
      statisticsColumns: [
        {
          type: "index",
          label: "序号",
          width: 60,
          align: "center",
        },
        {
          prop: "districtName",
          label: "行政区划",
          minWidth: 120,
        },
        {
          prop: "shouldPromiseNumber",
          label: "企业总数",
          minWidth: 100,
        },
        {
          prop: "firesNumber",
          label: "特殊动火作业",
          minWidth: 120,
        },
        {
          prop: "fire1Number",
          label: "一级动火作业",
          minWidth: 120,
        },
        {
          prop: "fire2Number",
          label: "二级动火作业",
          minWidth: 120,
        },
        {
          prop: "spaceworkNumber",
          label: "进入受限空间作业",
          minWidth: 150,
        },
        {
          prop: "blindplateNumber",
          label: "盲板抽堵作业",
          minWidth: 100,
        },
        {
          prop: "highworkNumber",
          label: "高处作业",
          minWidth: 100,
        },
        {
          prop: "liftingworkNumber",
          label: "吊装作业",
          minWidth: 100,
        },
        {
          prop: "electricityworkNumber",
          label: "临时用电作业",
          minWidth: 120,
        },
        {
          prop: "soilworkNumber",
          label: "动土作业",
          minWidth: 100,
        },
        {
          prop: "roadworkNumber",
          label: "断路作业",
          minWidth: 100,
        },
      ],
      statisticsData: [
        {
          districtName: "武汉市",
          shouldPromiseNumber: 301,
          firesNumber: 17,
          fire1Number: 120,
          fire2Number: 200,
          spaceworkNumber: 43,
          blindplateNumber: 24,
          highworkNumber: 24,
          liftingworkNumber: 229,
          electricityworkNumber: 51,
          soilworkNumber: 74,
          roadworkNumber: 100,
        },
      ],
      statisticsDistrictVal: this.userDistCode || "",
    };
  },
  methods: {
    //重大危险源下拉
    selectInit(enterpId) {
      getSelectData({
        enterpId: enterpId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.dangerList = res.data.data;
        }
      });
    },
    changeDangerId(val) {
      // debugger
      this.form.deviceId = "";
      if (val != "") {
        getEquipmentData({
          dangerid: val,
        }).then((res) => {
          // res.data.data.splice(0, 1);
          // var aa={
          //   equipmentCode:'-1',
          //   equipmentName:"其它"
          // }
          res.data.data.forEach((el) => {
            if (el.equipmentCode == "-1") {
              el.equipmentName = "其它";
            }
          });
          this.options1 = res.data.data;
        });
      }
    },
    handleChange1(val) {},
    getCompanyParticularJobTypeFun() {
      this.remoteBig = true;
      getCompanyParticularJobType().then((res) => {
        this.remoteBig = false;
        this.ReportTypeOpt = res.data.data.map((item, index) => {
          return { id: item.id, label: item.label };
        });
        // console.log(this.ReportTypeOpt);
      });
    },
    //打开文件
    openImg(img) {
      // img.electronicTicketUrl.forEach((item, index) => {
      //   if (!item.url.includes('http://***********:8090')) {
      //     item.url = 'http://***********:8090' + item.url
      //   }
      // })
      this.dialogFileSrc = img.electronicTicketUrl;
      this.dialogImg = true;
    },
    //根据code查找作业类型
    jobBigTypeFilter(value) {
      let res = this.ReportTypeOpt.find((item, index) => {
        return value === item.value;
      });
      return res.label;
    },
    abnormalEquipmentDel(row) {
      this.$confirm("确认删除？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          getCompanyParticularJobDelete({ id: row.id }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success(res.data.data);
              if (this.tableData.length === 1 && this.currentPage !== 1) {
                this.currentPage--;
              }
              this.getData();
            } else {
              this.$message.error(res.data.data);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },

    filterJobBigType(value, clock) {
      if (clock) {
        this.form.jobSmallType = "";
      }
      this.remoteSmall = true;
      getCompanyParticularJobTypeByParentCode({ parentCode: value }).then(
        (res) => {
          this.remoteSmall = false;
          this.jobSmallTypeOpt = res.data.data;
        }
      );
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        // console.log(this.administrativeList, data)
        const administrative = this.administrativeList.find((item, index) => {
          return item.label === data.addressComponent.county;
        });
        if (administrative) {
          this.form.amnstDvsn = administrative.id;
        } else {
          this.form.amnstDvsn = "";
        }
        // console.log(administrative);
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        // this.form.dtlLocaltion = data.formatted_address
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
    closeDialog() {
      this.form = {
        applicat: "",
        contractor: "",
        electronicTicketUrl: [
          // {
          //   attachId: "",
          //   msg: "",
          //   name: "",
          //   status: "",
          //   url: "",
          //   urlOuterNet: "",
          // },
        ],
        id: "",
        jobBigType: "420000",
        jobCertificateLevel: "",
        jobEndTime: "",
        jobLeader: "",
        jobName: this.enterData.enterpName,
        orgName: this.enterData.enterpName,
        jobPeople: "",
        jobPermitNumber: "",
        jobSmallType: "",
        jobStartTime: "",
        jobType: "",
        jobWork: "",
        latitude: "30.580942",
        leaderInformation: "",
        longitude: "114.311467",
        // orgCode: this.userDistCode,
        orgCode: this.user.org_code,
        safetyInformation: "",
        safetyPeople: "",
        workAddress: "",
        workContent: "",
      };
      this.dialogVisible = false;
    },

    submit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.form.jobType = this.form.jobBigType;
          this.btnLoading = true;
          if (this.title === "编辑特殊作业") {
            getCompanyParticularJobUpdate(this.form).then((res) => {
              this.btnLoading = false;
              if (res.data.status === 200) {
                this.$message.success(res.data.msg || "操作成功");
                this.dialogVisible = false;
                this.getData();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          } else if (this.title === "新增特殊作业") {
            getCompanyParticularJobAdd(this.form).then((res) => {
              this.btnLoading = false;
              if (res.data.status === 200) {
                this.$message.success(res.data.msg);
                this.dialogVisible = false;
                this.getData();
              } else {
                this.$message.error(res.data.msg);
              }
            });
          }
        } else {
          this.$scrollToError();
          return false;
        }
      });
    },
    getEntData() {
      this.loading = true;
      getEnterpriseId({ id: this.user.org_code }).then((res) => {
        this.loading = false;
        this.form.orgName = res.data.data.qymc;
        this.form.districtName = res.data.data.districtName;
        this.form.districtCode = res.data.data.districtCode;
      });
    },

    openDetails(title, row) {
      this.title = title;
      this.enterpid = row.enterpid;
      this.getCompanyParticularJobTypeFun();
      switch (title) {
        case "新增特殊作业":
          this.disabled = false;
          this.form.jobName = this.user.org_name;
          this.form.orgCode = this.user.org_code;
          this.form.district_code = this.userDistCode;
          this.getEntData();
          this.filterJobBigType("420000", false);
          break;
        case "特殊作业详情":
          this.disabled = true;
          this.loading = true;
          this.getCompanyParticularJobFun(row);
          this.selectInit(row.enterpid);
          this.changeDangerId(row.dangerId);
          break;
        case "编辑特殊作业":
          this.disabled = false;
          this.form.district_code = this.userDistCode;
          this.getCompanyParticularJobFun(row);
          this.selectInit(row.enterpid);
          this.changeDangerId(row.dangerId);
          break;
        default:
          break;
      }
      this.dialogVisible = true;
    },
    getCompanyParticularJobFun(row) {
      this.loading = true;
      getCompanyParticularJob({ id: row.id }).then((res) => {
        this.form = res.data.data;
        this.filterJobBigType(row.jobBigType, false);
        this.loading = false;
      });
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterpid);
    },

    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData();
    },
    clearKey() {
      this.enterpName = "";
    },
    // 导出
    exportExcel() {
      const dto = {
        orgName: this.jobName,
        jobType: this.ReportType,
        ids: this.selection,
        jobStartTime: this.datePickerValue[0],
        jobEndTime: this.datePickerValue[1],
        // orgCode: this.districtVal
      };

      if (this.user.user_type === "ent") {
        dto.orgCode = this.$store.state.login.user.org_code;
      } else {
        dto.orgName = this.enterpName;
        dto.districtCode = this.districtVal;
      }
      console.log(this.user.user_type, "this.user.user_type");
      getCompanyParticularJobExport(dto).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status === 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
          //获取今天的时间
          let day = new Date();
          day.setTime(day.getTime());
          let timestamp =
            day.getFullYear() +
            "-" +
            (day.getMonth() + 1) +
            "-" +
            day.getDate();
          const filename = "特殊作业管理列表" + timestamp + ".xls";
          //下载文件
          downloadFuc(response, filename);
        } else {
          this.$message.error("导出失败");
        }
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].id;
      }
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    getData() {
      this.loading = true;
      const dto = {
        jobName: this.jobName,
        jobType: this.ReportType,
        nowPage: this.currentPage,
        pageSize: 10,
        startTime: this.datePickerValue[0],
        endTime: this.datePickerValue[1],
      };
      if (this.user.user_type === "ent") {
        dto.orgCode = this.$store.state.login.enterData.enterpId;
      } else {
        dto.orgName = this.enterpName;
        dto.districtCode = this.districtVal;
      }
      getCompanyParticularJobList(dto).then((res) => {
        // console.log(res.data);
        this.tableData = res.data.data.list;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
    handleStatisticsSearch() {
      this.getStatisticsData();
    },

    handleStatisticsExport() {
      const dto = {
        distCode: this.statisticsDistrictVal,
        startDate: this.statisticsDate,
      };
      exportSpecialWorkStatistics(dto).then((response) => {
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "特殊作业管理列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },

    // 获取统计数据
    getStatisticsData() {
      this.loading = true;
      const params = {
        distCode: this.statisticsDistrictVal,
        startDate: this.statisticsDate,
      };
      getSpecialWorkStatistics(params).then((res) => {
        this.statisticsData = res.data.data;
        this.loading = false;
      });
    },
    handleStatisticsDistrictChange(value) {
      if (value) {
        this.statisticsDistrictVal = value;
      }
    },
    // 添加初始化方法
    initStatisticsData() {
      // 获取当前日期
      const today = new Date();
      this.statisticsDate = today.toISOString().split("T")[0];

      // 获取统计数据
      this.getStatisticsData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    this.getCompanyParticularJobTypeFun();
    // 初始化统计数据
    this.initStatisticsData();
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
      district: (state) => state.controler.district,
      enterData: (state) => state.login.enterData,
    }),
  },
  watch: {
    enterData: {
      handler(newVal, oldVal) {
        if (newVal) {
          this.form.jobName = newVal.enterpName;

          // this.form.orgCode = newVal.districtCode;
        }
      },
      deep: true,
      immediate: true,
    },
    "form.jobEndTime": {
      handler(newVal, oldVal) {
        if (newVal === null) {
          this.form.jobEndTime = "";
        }
      },
    },
    "form.jobStartTime": {
      handler(newVal, oldVal) {
        if (newVal === null) {
          this.form.jobStartTime = "";
        }
      },
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-descriptions .is-bordered .el-descriptions-item__cell {
  padding: 7px 5px;
}
/deep/ .el-form-item {
  margin-bottom: 10px;
  margin-top: 7px;
}

/deep/ .el-form-item__error {
  padding-top: 2px;
}
/deep/ .el-button--text {
  color: rgb(57, 119, 234);
}
/deep/ .enterpName span {
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.redDot {
  color: red;
}

.specialWork {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }

  .icon {
    color: #6f81b5;
    font-size: 15px;
  }

  .seach-part {
    width: 100%;
    font-weight: 600;
    padding-bottom: 20px;
    margin-bottom: 0px;
    margin-top: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .l {
      display: flex;
      justify-content: flex-start;

      > * {
        margin-right: 15px;
      }
    }
  }

  .table-main {
    background: #fff;

    .table-top {
      h2 {
        font-size: 18px;
        line-height: 45px;
        margin-bottom: 0;
        float: left;
      }

      button {
        float: right;
        margin-top: 10px;
      }
    }

    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}

/deep/ .l .el-input {
  width: 150px;
}

.dialog {
  height: 65vh;
  overflow-y: scroll;
}
/deep/ .el-image__preview {
  cursor: zoom-in;
}

.special-tabs {
  margin-top: 10px;

  .statistics-content {
    .seach-part {
      .l {
        display: flex;
        gap: 10px;
      }
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}

body .el-radio-group {
  margin-bottom: 15px;
}
body .el-picker-panel__footer .el-button--text.el-picker-panel__link-btn {
  display: none;
}
</style>
