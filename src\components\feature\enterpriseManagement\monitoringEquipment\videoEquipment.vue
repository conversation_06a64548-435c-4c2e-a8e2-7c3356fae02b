<template>
  <div class="device" id="videoEquipment">
    <div class="header">
      <div class="operation">
        <h2 class="title" alt="视频设备列表">视频设备列表</h2>
        <div class="inputBox">
          <!-- <el-autocomplete popper-class="my-autocomplete"
            v-model="formInline.orgName"
            :fetch-suggestions="querySearch"
            placeholder="请选择/输入承办单位"
            @clear="clearSensororgCode()"
            size="small"
            @select="handleSelect"
            style="width: 150px">
            <template slot-scope="{ item }">
              <div class="name">{{ item.enterpName }}</div>
            </template>
          </el-autocomplete> -->
          <el-select
            v-model="formInline.monitorEqpmtType"
            size="small"
            placeholder="请选择摄像头类型"
            clearable
            @clear="clearSensortypeCode($event)"
            style="width: 150px"
          >
            <el-option
              v-for="item in cameraTypeOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="formInline.deviceNo"
            placeholder="请输入设备编码"
            class="input"
            size="small"
            clearable
            @clear="clearDangerName(event)"
            style="width: 150px"
          ></el-input>
          <el-select
            v-model="formInline.enabled"
            size="small"
            placeholder="请选择设备状态"
            clearable
            @clear="clearEnabledCode($event)"
            style="width: 150px"
          >
            <el-option
              v-for="item in onlineStatusData"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-button type="primary" size="small" @click="search()"
            >查询</el-button
          >
        </div>
        <div class="btnBox">
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            size="mini"-->
          <!--            v-if="$store.state.login.user.user_type === 'gov'"-->
          <!--            @click="alarmRuleSettings('edit')"-->
          <!--            >报警规则设置</el-button-->
          <!--          >-->
          <!--          <el-button-->
          <!--            type="primary"-->
          <!--            size="mini"-->
          <!--            v-if="$store.state.login.user.user_type === 'gov'"-->
          <!--            @click="addEdit"-->
          <!--            >新增</el-button-->
          <!--          >-->
          <!--          <el-button type="primary" size="mini" @click="batchDelete"-->
          <!--            >批量删除</el-button-->
          <!--          >-->
          <CA-button
            type="primary"
            plain
            class="export"
            size="small"
            @click="exportExcel"
            >导出</CA-button
          >
        </div>
      </div>
    </div>
    <div class="table">
      <el-table
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        ref="multipleTable"
        height="530px"
        @selection-change="handleSelectionChange"
        @select="select"
        @select-all="select"
      >
        <el-table-column type="selection" width="40" align="center">
        </el-table-column>
        <el-table-column type="index" label="序号" width="55" align="center">
        </el-table-column>
        <el-table-column
          prop="deviceName"
          label="设备名称"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="deviceNo"
          label="设备编码"
          align="center"
          min-width="120"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="monitorEqpmtTypeName"
          label="摄像头类型"
          align="center"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column prop="onlineStatusStr" label="设备状态" align="center">
        </el-table-column>
        <el-table-column label="操作" align="center" min-width="120">
          <template slot-scope="scope">
            <span
              v-if="$store.state.login.user.user_type == 'ent'"
              @click="edit(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >编辑</span
            >
            <span
              @click="view(scope.row)"
              style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
              >详情</span
            >
            <!--            <span-->
            <!--              v-if="$store.state.login.user.user_type != 'gov'"-->
            <!--              @click="deleter(scope.row.id)"-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              >删除</span-->
            <!--            >-->
            <!--            <span-->
            <!--              v-if="$store.state.login.user.user_type == 'gov'"-->
            <!--              @click="alarmRuleSettings('see')"-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              >报警规则查看</span-->
            <!--            >-->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
        v-if="total != 0"
      >
      </el-pagination>
    </div>

    <!-- 新增编辑查看 -->
    <el-dialog
      :title="title"
      v-if="open"
      :visible.sync="open"
      width="1100px"
      :close-on-click-modal="false"
      :append-to-body="false"
    >
      <el-scrollbar>
        <div class="dialog_h">
          <el-form
            ref="form"
            :model="form"
            :rules="rules"
            label-width="150px"
            :hide-required-asterisk="disabled"
          >
            <div class="form_item">
              <h2 class="form_title">设备信息</h2>
              <div class="form_main">
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="设备名称">
                      <el-input
                        v-model.trim="form.deviceName"
                        disabled
                        maxlength="10"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="deviceNo" label="设备编号">
                      <el-input
                        v-model.trim="form.deviceNo"
                        disabled
                        maxlength="30"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="orgName" label="所属单位">
                      <el-input
                        v-model.trim="form.orgName"
                        disabled
                        maxlength="40"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item prop="monitorEqpmtType" label="摄像头类型">
                      <el-select
                        v-model="form.monitorEqpmtType"
                        disabled
                        clearable
                        placeholder="请选择监测设备类型"
                      >
                        <el-option
                          v-for="item in cameraTypeOptions"
                          :key="item.value"
                          :label="item.name"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <!-- <el-col :span="8">
                <el-form-item label="监控视频"
                              required>
                  <el-cascader placeholder="监控视频"
                               :show-all-levels="false"
                               v-model="redundantField1"
                               :props="optionProps"
                               :options="selectoptions"
                               @change="handleChange"
                               ref="myCascader"></el-cascader>
                </el-form-item>
              </el-col> -->
                  <el-col :span="8">
                    <el-form-item label="监控视频排序" prop="sort">
                      <el-input
                        v-model.trim="form.sort"
                        disabled
                        maxlength="4"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="关联重大危险源" prop="companyHazardId">
                      <el-input
                        v-model="form.companyHazardName"
                        placeholder="请选择关联的重大危险源"
                        :disabled="Isdisabled"
                        readonly
                        style="width: calc(100% - 80px)"
                      ></el-input>
                      <el-button
                        type="primary"
                        size="small"
                        style="margin-left: 10px"
                        @click="openHazardDialog"
                        :disabled="Isdisabled"
                        >关联</el-button
                      >
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item label="所在位置描述">
                      <el-input
                        v-model.trim="form.installLocation"
                        :disabled="Isdisabled"
                        maxlength="50"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item
                      prop="longitude"
                      label="经度"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.longitude"
                        maxlength="13"
                        disabled
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      prop="latitude"
                      label="纬度  "
                      style="width: 100%"
                    >
                      <el-input
                        v-model="form.latitude"
                        disabled
                        maxlength="13"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      prop="altitude"
                      label="高程"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.altitude"
                        :disabled="Isdisabled"
                        maxlength="13"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <div class="map_height">
                      <el-form-item label="地图定位">
                        <egisMap
                          :islistener="true"
                          :isdetail="disabled"
                          plotting-method="point"
                          :datas="form"
                          ref="detailMap"
                          style="height: 220px"
                          @mapCallback="mapcallback"
                        ></egisMap>
                        <!-- <EgisMap :isdetail="false" @mapCallBack="mapCallBackVal" :datas="form" :mapType="mapType"></EgisMap> -->
                      </el-form-item>
                    </div>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item prop="monitorType" label="监控类型">
                      <el-select
                        v-model="form.monitorType"
                        :disabled="Isdisabled"
                        clearable
                        placeholder="监控类型"
                        @change="monitorTypeChange(form.monitorType)"
                      >
                        <el-option
                          v-for="item in monitorTypeData"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="monitorItems" label="监控项">
                      <el-select
                        v-model="form.monitorItems"
                        multiple
                        clearable
                        placeholder="监控项"
                        :disabled="Isdisabled"
                      >
                        <el-option
                          v-for="item in monitorItemsData"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item prop="videoManufacturers" label="技术支持">
                      <el-select
                        v-model="form.videoManufacturers"
                        :disabled="Isdisabled"
                        clearable
                        placeholder="厂家名称"
                        @change="monitorTypeChange(form.videoManufacturers)"
                      >
                        <el-option
                          v-for="item in videoManufacturersData"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <!-- <el-col :span="8">
                <el-form-item prop="streamUrl"
                              label="视频流地址"
                              style="width: 100%">
                  <el-input v-model.trim="form.streamUrl"
                            maxlength="100"></el-input>
                </el-form-item>
              </el-col> -->
                  <el-col :span="8">
                    <el-form-item
                      label="设备负责人"
                      prop="respPersionId"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.respPersionId"
                        :disabled="Isdisabled"
                        maxlength="10"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item
                      label="联系电话"
                      prop="phone"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.phone"
                        :disabled="Isdisabled"
                        maxlength="11"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item
                      label="主要技术参数"
                      prop="mainTechnicalParameters"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.mainTechnicalParameters"
                        :disabled="Isdisabled"
                        maxlength="100"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item
                      label="监控区域"
                      prop="regionName"
                      style="width: 100%"
                    >
                      <el-input
                        v-model.trim="form.regionName"
                        :disabled="Isdisabled"
                        maxlength="15"
                      ></el-input>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </el-form>
        </div>
      </el-scrollbar>
      <div v-if="!disabled" slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          v-if="dialogType === 'edit'"
          @click="submitForm"
          >保 存</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 关联重大危险源弹窗 -->
    <el-dialog
      title="关联重大危险源"
      :visible.sync="hazardDialogVisible"
      width="800px"
      :close-on-click-modal="false"
      append-to-body
    >
      <div class="hazard-dialog">
        <div class="search-box">
          <el-input
            v-model="hazardSearchKeyword"
            placeholder="请输入危险源名称"
            clearable
            style="width: 250px; margin-right: 10px"
            @clear="handleHazardSearch"
          ></el-input>
          <el-button type="primary" size="small" @click="handleHazardSearch"
            >查询</el-button
          >
        </div>
        <el-table
          ref="hazardTable"
          :data="hazardList"
          border
          height="400px"
          v-loading="hazardLoading"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          style="width: 100%; margin-top: 15px"
          @selection-change="handleHazardSelectionChange"
          row-key="id"
        >
          <el-table-column
            type="selection"
            width="55"
            :reserve-selection="true"
          ></el-table-column>
          <el-table-column
            type="index"
            label="序号"
            width="60"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="label"
            label="危险源名称"
            min-width="300"
            show-overflow-tooltip
          ></el-table-column>
        </el-table>
        <div class="pagination" style="margin-top: 15px">
          <el-pagination
            @current-change="handleHazardPageChange"
            :current-page.sync="hazardCurrentPage"
            :page-size="hazardPageSize"
            layout="total, prev, pager, next"
            :total="hazardTotal"
            background
          ></el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="hazardDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmHazardSelection"
          >确 定</el-button
        >
      </span>
    </el-dialog>

    <!-- 报警规则设置 -->
    <el-dialog
      :title="flag === 'see' ? '报警规则查看' : '报警规则设置'"
      :visible.sync="visible"
      append-to-body
      :close-on-click-modal="false"
      width="60%"
    >
      <div class="flex-full" style="position: relative">
        <div :class="flag == 'see' ? 'mask' : ''"></div>
        <!-- 底部按钮 -->
        <el-form ref="ruleForm" :model="ruleForm" label-width="200px">
          <el-form-item label="报警短信推送人">
            <el-input v-model.trim="ruleForm.videoAlarmReceiverName">
              <el-button type="primary" slot="append">选择人员</el-button>
            </el-input>
          </el-form-item>
        </el-form>
        <span class="list-btn" style="text-align: center; display: inherit">
          <el-button
            type="primary"
            icon="el-icon-check"
            @click="submit"
            v-if="flag == 'edit'"
          >
            确认提交
          </el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getCamerainfoListData,
  addCamerainfoData,
  editCamerainfoData,
  detailCamerainfoData,
  deleteCamerainfoData,
  exportCamerainfoData,
} from "@/api/sensor";
import { getExpertTypeData } from "@/api/mergencyResources";
import { getSearchArr } from "@/api/entList.js";
import { getDangerList } from "@/api/workingAcc";
export default {
  //import引入的组件
  name: "videoEquipment",
  components: {},
  data() {
    var validatelongitude = (rule, value, callback) => {
      // 校验经度
      const reg =
        /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,9})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,9}|180)$/;
      if (value !== "" && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error("经度整数部分为0-180,小数部分为0到9位!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validatelatitude = (rule, value, callback) => {
      // 校验纬度
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,9}|90\.0{0,9}|[0-8]?\d{1}|90)$/;
      if (value !== "" && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error("纬度整数部分为0-90,小数部分为0到9位!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      selectedList: [],
      dialogType: "",
      Isdisabled: true,
      flag: "edit",
      orgData: [],
      ruleForm: {
        videoAlarmReceiverName: "",
        videoAlarmReceiver: "",
      },
      visible: false,
      // 关联重大危险源相关数据
      hazardDialogVisible: false,
      hazardList: [],
      allHazardList: [], // 存储所有危险源数据，用于前端过滤和分页
      hazardLoading: false,
      hazardSearchKeyword: "",
      hazardCurrentPage: 1,
      hazardPageSize: 10,
      hazardTotal: 0,
      selectedHazards: [], // 存储多选的危险源
      //监控项的禁用
      monitorItemsDisabled: false,
      //监控视频的显示与隐藏
      hazardId: "",
      cameraType: "",
      redundantField1: [],
      selectoptions: [],
      optionProps: {
        value: "id",
        label: "label",
        children: "children",
      },
      open: false,
      disabled: false,
      formInline: {
        offlineWhy: "",
        enabled: "",
        deviceNo: "",
        orgCode: "",
        orgName: "",
        monitorEqpmtType: "",
      },
      title: "新增储罐信息",
      currentPage: 1,
      size: 10,
      selection: [],
      total: 3,
      tableData: [],
      form: {
        deviceName: "",
        deviceNo: "",
        monitorEqpmtType: "",
        monitorObjectType: "",
        monitorObjectCode: "",
        monitorType: "",
        monitorItems: [],
        streamUrl: "",
        videoManufacturers: "",
        installLocation: "",
        phone: "",
        mainTechnicalParameters: "",
        orgCode: "",
        orgName: "",
        latitude: "",
        longitude: "",
        altitude: "",
        respPersionId: "",
        systemFlag: "",
        sort: "",
        regionName: "",
        regionId: "",
        dialogType: "",
      },
      rules: {
        regionName: [
          { required: true, message: "请输入监控区域", trigger: "blur" },
        ],
        deviceNo: [
          { required: true, message: "请输入设备编码", trigger: "blur" },
        ],
        deviceName: [
          { required: true, message: "请输入设备名称", trigger: "blur" },
        ],
        orgCode: [
          { required: true, message: "请输入所属企业", trigger: "blur" },
        ],
        monitorType: [
          {
            required: true,
            message: "请输入监控类型",
            trigger: ["blur"],
          },
        ],
        videoManufacturers: [
          {
            required: true,
            message: "请选择设备所属厂家",
            trigger: ["blur"],
          },
        ],
        // streamUrl: [{ required: true, message: '请输入视频流地址', trigger: ['change', 'blur'] }],
        // monitorItems: [{ required: true, message: '请输入正确数据', trigger: ['change', 'blur'] }],
        monitorEqpmtType: [
          {
            required: true,
            message: "请输入监测设备类型",
            trigger: ["blur"],
          },
        ],
        // monitorObjectType: [{ required: true, message: '请输入正确数据', trigger: ['change', 'blur'] }],
        streamUrl: [
          {
            message: '请输入以"http://"开头的正确的视频流',
            pattern: /(http):\/\/([\w.]+\/?)\S*/,
            trigger: ["blur"],
          },
        ],
        // longitude: [
        //   {
        //     required: true,
        //     message: "请输入整数部分为0-180,小数部分为0到9位的数",
        //     pattern:
        //       /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,9})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,9}|180)$/,
        //     trigger: ["change", "blur"],
        //   },
        // ],
        // latitude: [
        //   {
        //     required: true,
        //     message: "请输入整数部分为0-90,小数部分为0到9位的数",
        //     pattern:
        //       /^(\-|\+)?([0-8]?\d{1}\.\d{0,9}|90\.0{0,9}|[0-8]?\d{1}|90)$/,
        //     trigger: ["change", "blur"],
        //   },
        // ],
        altitude: [
          {
            required: true,
            pattern: /^\d{1,6}(\.\d{1,6})?$/,
            message: "请输入正确的高程",
            trigger: ["blur"],
          },
        ],
        respPersionId: [
          {
            required: true,
            message: "请输入设备负责人",
            trigger: ["blur"],
          },
        ],
        phone: [
          {
            required: true,
            pattern:
              /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/,
            message: "请输入正确手机号",
            trigger: "blur",
          },
        ],
        sort: [{ pattern: /^([0-9][0-9]*)$/, message: "请输入数字" }],
      },
      options: [], //企业下拉
      monitorObjectCodeData: [], // 监测对象名称下拉数据
      monitorTypeData: [
        //监控类型下拉数据
        { id: "1", name: "普通" },
        { id: "2", name: "智能分析" },
      ],
      monitorItemsData: [
        //监控项下拉数据
        { id: "1", name: "安全帽佩戴规范" },
        { id: "2", name: "在离岗和人数超员" },
      ],
      videoManufacturersData: [
        // 技术支持下拉
        { id: "device_1104", name: "大华视频" },
        { id: "device_1103", name: "海康视频" },
      ],
      cameraTypeOptions: [
        //监测类别数据
        // { id: '1', name: '固定摄像头' },
        // { id: '2', name: '小型移动指挥平台' },
        // { id: '3', name: '执法仪' }
      ],
      onlineStatusData: [
        { id: "0", name: "离线" },
        { id: "1", name: "在线" },
      ],
      monitorObjectTypeData: [
        // 监测对象类型下拉数据
        {
          name: "储罐",
          id: "G0",
        },
        {
          name: "仓库",
          id: "K0",
        },
        {
          name: "生产场所",
          id: "S0",
        },
        {
          name: "装置",
          id: "P0",
        },
        {
          name: "泄漏点",
          id: "Q0",
        },
        {
          name: "其他",
          id: "T0",
        },
      ],
      enterpriseId: "",
    };
  },
  watch: {
    enterpriseId: {
      handler(newVal, oldVal) {
        this.currentPage = 1;
      },
      deep: true,
      immediate: true,
    },
    // longitude:{
    //   handler(newVal, oldVal) {
    //     debugger
    //   },
    // }
  },
  filters: {
    numToFixed(val) {
      if (val) {
        return parseInt(val).toFixed(2);
      } else {
        return val;
      }
    },
  },
  //方法集合
  methods: {
    aaa(val) {},
    //请选择摄像头类型
    getPlanLevel() {
      getExpertTypeData({ dicCode: "ICC_CAMERA_DEVICE_TYPE" }).then((res) => {
        if (res.data.status == 200) {
          // debugger
          console.log(res.data.data, "ICC_CAMERA_DEVICE_TYPE");
          this.cameraTypeOptions = res.data.data;
        }
      });
    },
    //视频厂商字典项
    getJishu() {
      getExpertTypeData({ dicCode: "ICC_CAMERA_MANUFACTURER" }).then((res) => {
        if (res.data.status == 200) {
          // debugger
          console.log(res.data.data, "ICC_CAMERA_MANUFACTURER");
          this.cameraTypeOptions = res.data.data;
        }
      });
    },
    view(row) {
      this.reset();
      this.open = true;
      this.disabled = false;
      this.dialogType = "view";
      this.Isdisabled = true;
      this.title = "查看监控设备信息";
      this.form.orgCode = this.$store.state.login.user.org_code;
      this.form.orgName = this.$store.state.login.user.org_name;
      detailCamerainfoData({ id: row.id }).then((res) => {
        if (res.data.status == 200) {
          this.form = res.data.data;
          this.tableDatas = res.data.data.sensorMonItemInfoDTOS;

          // 初始化关联重大危险源信息
          if (
            this.form.companyHazardId &&
            this.form.companyHazardId.length > 0
          ) {
            // 支持危险源ID数组
            const hazardIds = Array.isArray(this.form.companyHazardId)
              ? this.form.companyHazardId
              : [this.form.companyHazardId]; // 确保是数组格式

            if (this.allHazardList.length > 0) {
              // 从危险源列表中查找对应的名称
              const hazardNames = hazardIds
                .map((id) => {
                  const hazard = this.allHazardList.find(
                    (item) => item.id === id
                  );
                  return hazard ? hazard.label : "";
                })
                .filter((name) => name); // 过滤掉空名称

              if (hazardNames.length > 0) {
                this.form.companyHazardName = hazardNames.join(",");
              } else {
                // 如果在列表中找不到，使用默认名称或已有名称
                this.form.companyHazardName =
                  this.form.companyHazardName || "已关联危险源";
              }
            } else {
              // 如果危险源列表为空，使用已有名称
              this.form.companyHazardName =
                this.form.companyHazardName || "已关联危险源";
            }
          } else {
            this.form.companyHazardName = "";
          }
        }
      });
    },
    handleChange() {},
    querySearch(queryString, cb) {
      let roleInfo =
        JSON.parse(sessionStorage.getItem("VueX_local")).root.login.user || {};
      if (queryString === "") {
        queryString = roleInfo.user_type === "ent" ? roleInfo.org_name : "";
      }
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.formInline.orgCode = item.enterpId;
      this.formInline.orgName = item.enterpName;
    },
    clearSensororgCode() {
      this.formInline.orgCode = "";
      this.formInline.orgName = "";
    },
    monitorTypeChange(val) {
      if (val == 1) {
        this.form.monitorItems = [];
        this.monitorItemsDisabled = true;
      }
      if (val == 2) {
        this.monitorItemsDisabled = false;
      }
    },
    getData(id) {
      this.enterpriseId = id;
      this.initHazardList(id);
      getCamerainfoListData({
        systemFlag: "", //系统标识
        // deviceMainType: 'device_11', //固定值
        deviceMainType: "", //固定值
        orgCode: id, //企业id
        offlineWhy: this.formInline.offlineWhy
          ? this.formInline.offlineWhy
          : "",
        enabled: this.formInline.enabled ? this.formInline.enabled : "",
        deviceNo: this.formInline.deviceNo ? this.formInline.deviceNo : "",
        monitorEqpmtType: this.formInline.monitorEqpmtType
          ? this.formInline.monitorEqpmtType
          : "",
        pageSize: this.size, //条数
        nowPage: this.currentPage, //页数
      }).then((res) => {
        if (res.data.status == 200) {
          this.total = res.data.data.total;
          this.tableData = res.data.data.list;
        }
      });
    },

    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    search() {
      this.currentPage = 1;
      this.getData(this.enterpriseId);
    },
    clearSensortypeCode(e) {
      this.formInline.monitorEqpmtType = "";
    },
    clearEnabledCode(e) {
      this.formInline.enabled = "";
    },
    clearDangerName(e) {
      this.formInline.deviceNo = "";
    },
    handleSelectionChange(val) {
      console.log(val);
      this.selectedList = val;
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      console.log(val, "我执行了首页表格的翻页");
      this.currentPage = val;
      this.getData(this.enterpriseId);
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId;
      }
    },
    // 导出
    exportExcel() {
      if (!this.selectedList.length) {
        return this.$message({
          message: "请选择要导出的数据",
          type: "warning",
        });
      }
      exportCamerainfoData({
        ids: this.selectedList.map((el) => el.id),
        systemFlag: "", //系统标识
        deviceMainType: "", //固定值
        // orgCode: this.formInline.orgCode ? this.formInline.orgCode : '', //企业id
        orgCode: this.enterpriseId,
        offlineWhy: this.formInline.offlineWhy
          ? this.formInline.offlineWhy
          : "",
        enabled: this.formInline.enabled ? this.formInline.enabled : "",
        deviceNo: this.formInline.deviceNo ? this.formInline.deviceNo : "",
        monitorEqpmtType: this.formInline.monitorEqpmtType
          ? this.formInline.monitorEqpmtType
          : "",
        pageSize: this.size, //条数
        nowPage: this.currentPage, //页数
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "视频监测" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    addEdit() {
      this.reset();
      this.open = true;
      this.form.orgCode = this.$store.state.login.user.org_code;
      this.form.orgName = this.$store.state.login.user.org_name;
    },
    edit(row) {
      this.reset();
      this.open = true;
      this.dialogType = "edit";
      this.Isdisabled = false;
      this.form.orgCode = this.$store.state.login.user.org_code;
      this.form.orgName = this.$store.state.login.user.org_name;
      this.disabled = false;
      this.title = "编辑监控设备信息";
      detailCamerainfoData({ id: row.id }).then((res) => {
        if (res.data.status == 200) {
          this.form = res.data.data;
          this.tableDatas = res.data.data.sensorMonItemInfoDTOS;
          // 初始化关联重大危险源信息
          if (
            this.form.companyHazardId &&
            this.form.companyHazardId.length > 0
          ) {
            // 支持危险源ID数组
            const hazardIds = Array.isArray(this.form.companyHazardId)
              ? this.form.companyHazardId
              : [this.form.companyHazardId]; // 确保是数组格式

            if (this.allHazardList.length > 0) {
              // 从危险源列表中查找对应的名称
              const hazardNames = hazardIds
                .map((id) => {
                  const hazard = this.allHazardList.find(
                    (item) => item.id === id
                  );
                  return hazard ? hazard.label : "";
                })
                .filter((name) => name); // 过滤掉空名称

              if (hazardNames.length > 0) {
                this.form.companyHazardName = hazardNames.join(",");
              } else {
                // 如果在列表中找不到，使用默认名称或已有名称
                this.form.companyHazardName =
                  this.form.companyHazardName || "已关联危险源";
              }
            } else {
              // 如果危险源列表为空，使用已有名称
              this.form.companyHazardName =
                this.form.companyHazardName || "已关联危险源";
            }
          } else {
            this.form.companyHazardName = "";
          }
        }
      });
    },

    // 打开关联重大危险源弹窗
    openHazardDialog() {
      this.hazardDialogVisible = true;
      this.hazardSearchKeyword = "";
      this.hazardCurrentPage = 1;
      // 清除之前的选中状态
      this.selectedHazards = [];
      this.getHazardList();
    },

    // 获取危险源列表
    getHazardList() {
      // this.hazardLoading = true;
      this.filterAndPaginateHazards();
    },

    // 前端过滤和分页处理
    filterAndPaginateHazards() {
      // 根据关键字过滤
      let filteredList = this.allHazardList;
      if (this.hazardSearchKeyword) {
        const keyword = this.hazardSearchKeyword.toLowerCase();
        filteredList = this.allHazardList.filter(
          (item) => item.label && item.label.toLowerCase().includes(keyword)
        );
      }

      // 计算总数
      this.hazardTotal = filteredList.length;

      // 分页处理
      const start = (this.hazardCurrentPage - 1) * this.hazardPageSize;
      const end = start + this.hazardPageSize;
      this.hazardList = filteredList.slice(start, end);

      // 在下一个事件循环中设置表格的选中状态
      this.$nextTick(() => {
        if (this.$refs.hazardTable) {
          // 先清除所有选中状态
          this.$refs.hazardTable.clearSelection();

          // 如果有选中的危险源，则设置选中状态
          if (this.selectedHazards.length > 0) {
            // 为当前页的每一行检查是否在选中列表中
            this.hazardList.forEach((row) => {
              if (
                this.selectedHazards.some((selected) => selected.id === row.id)
              ) {
                this.$refs.hazardTable.toggleRowSelection(row, true);
              }
            });
          }
        }
      });
    },

    // 处理危险源多选
    handleHazardSelectionChange(selection) {
      this.selectedHazards = selection;
    },

    // 确认选择危险源
    confirmHazardSelection() {
      if (this.selectedHazards.length === 0) {
        this.$message.warning("请至少选择一个危险源");
        return;
      }

      // 将选中的危险源ID作为数组，名称作为逗号分隔的字符串
      const ids = this.selectedHazards.map((item) => item.id); // 数组格式
      const names = this.selectedHazards.map((item) => item.label).join(",");

      this.form.companyHazardId = ids; // 数组格式
      this.form.companyHazardName = names;

      this.hazardDialogVisible = false;
      // this.$message.success("关联危险源成功");
    },

    // 危险源分页变化
    handleHazardPageChange(page) {
      this.hazardCurrentPage = page;
      this.filterAndPaginateHazards();
    },

    // 处理搜索输入变化
    handleHazardSearch() {
      this.hazardCurrentPage = 1; // 重置到第一页
      this.filterAndPaginateHazards();
    },

    // 初始化危险源列表数据
    initHazardList(enterpId) {
      getDangerList({
        enterpId,
      })
        .then((res) => {
          if (res.data.status === 200) {
            // 保存完整的危险源列表
            this.allHazardList = res.data.data || [];
          } else {
            this.allHazardList = [];
          }
        })
        .catch(() => {
          this.allHazardList = [];
        });
    },
    deleter(row) {
      const id = row;
      this.$confirm("是否确认删除该监控设备信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteCamerainfoData({
            ids: [id],
          }).then((res) => {
            if (res.data.status == 200) {
              this.$message.success("删除成功");
              this.getData(this.enterpriseId);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    batchDelete() {
      if (this.selection.length == 0) {
        this.$message.info("请勾选需要删除的监控设备信息");
      } else {
        this.$confirm("是否确认批量删除勾选的监控设备信息", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        })
          .then(() => {
            deleteCamerainfoData({
              ids: this.selection,
            }).then((res) => {
              if (res.data.status == 200) {
                this.$message.success("批量删除成功");
                this.getData(this.enterpriseId);
              } else {
                this.$message.error(res.data.msg);
              }
            });
          })
          .catch(() => {});
      }
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    reset() {
      this.form = {
        deviceName: "",
        deviceNo: "",
        monitorEqpmtType: "",
        monitorObjectType: "",
        monitorObjectCode: "",
        monitorType: "",
        monitorItems: [],
        streamUrl: "",
        videoManufacturers: "",
        installLocation: "",
        phone: "",
        mainTechnicalParameters: "",
        orgCode: "",
        orgName: "",
        latitude: "",
        longitude: "",
        altitude: "",
        respPersionId: "",
        systemFlag: "",
        sort: "",
        regionName: "",
        regionId: "",
        companyHazardId: [], // 关联重大危险源ID（数组格式）
        companyHazardName: "", // 关联重大危险源名称
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.id) {
            let params = {
              systemFlag: "SAFETY", //系统标识
              id: this.form.id,
              streamUrl: this.form.streamUrl ? this.form.streamUrl : "",
              deviceName: this.form.deviceName ? this.form.deviceName : "",
              deviceNo: this.form.deviceNo ? this.form.deviceNo : "",
              monitorEqpmtType: this.form.monitorEqpmtType
                ? this.form.monitorEqpmtType
                : "",
              monitorObjectType: this.form.monitorObjectType
                ? this.form.monitorObjectType
                : "",
              monitorObjectCode: this.form.monitorObjectCode
                ? this.form.monitorObjectCode
                : "",
              monitorType: this.form.monitorType ? this.form.monitorType : "",
              videoManufacturers: this.form.videoManufacturers
                ? this.form.videoManufacturers
                : "",
              monitorItems: this.form.monitorItems
                ? this.form.monitorItems
                : [],
              installLocation: this.form.installLocation
                ? this.form.installLocation
                : "",
              phone: this.form.phone ? this.form.phone : "",
              mainTechnicalParameters: this.form.mainTechnicalParameters
                ? this.form.mainTechnicalParameters
                : "",
              orgCode: this.form.orgCode ? this.form.orgCode : "",
              // latitude: this.form.latitude ? this.form.latitude.toFixed(9) : '',
              // longitude: this.form.longitude ? this.form.longitude.toFixed(9) : '',
              latitude: this.form.latitude ? this.form.latitude : "",
              longitude: this.form.longitude ? this.form.longitude : "",
              altitude: this.form.altitude ? this.form.altitude : "", // 高程 -- 王薛婷 2021-10-30
              respPersionId: this.form.respPersionId
                ? this.form.respPersionId
                : "",
              companyHazardId: this.form.companyHazardId,
              regionId: this.form.regionId ? this.form.regionId : "",
              redundantField1: this.redundantField ? this.redundantField : "",
              sort: this.form.sort ? this.form.sort : "",
              regionName: this.form.regionName ? this.form.regionName : "",
              cameraType: this.form.cameraType ? this.form.cameraType : "",
            };
            editCamerainfoData(params).then((response) => {
              if (response.data.status == 200) {
                this.open = false;
                this.$message({
                  type: "success",
                  message: "修改成功",
                });
                this.reset();
                this.getData(this.enterpriseId);
              }
            });
          } else {
            let redundantField1 = this.redundantField1.join();
            let params = {
              systemFlag: "SAFETY", //系统标识
              deviceMainType: "device_11",
              streamUrl: this.form.streamUrl ? this.form.streamUrl : "",
              deviceName: this.form.deviceName ? this.form.deviceName : "",
              deviceNo: this.form.deviceNo ? this.form.deviceNo : "",
              monitorEqpmtType: this.form.monitorEqpmtType
                ? this.form.monitorEqpmtType
                : "",
              monitorObjectType: this.form.monitorObjectType
                ? this.form.monitorObjectType
                : "",
              monitorObjectCode: this.form.monitorObjectCode
                ? this.form.monitorObjectCode
                : "",
              monitorType: this.form.monitorType ? this.form.monitorType : "",
              videoManufacturers: this.form.videoManufacturers
                ? this.form.videoManufacturers
                : "",
              monitorItems: this.form.monitorItems
                ? this.form.monitorItems
                : [],
              installLocation: this.form.installLocation
                ? this.form.installLocation
                : "",
              phone: this.form.phone ? this.form.phone : "",
              mainTechnicalParameters: this.form.mainTechnicalParameters
                ? this.form.mainTechnicalParameters
                : "",
              orgCode: this.form.orgCode ? this.form.orgCode : "",
              // latitude: this.form.latitude ? this.form.latitude.toFixed(9) : '',
              // longitude: this.form.longitude ? this.form.longitude.toFixed(9) : '',
              latitude: this.form.latitude ? this.form.latitude : "",
              longitude: this.form.longitude ? this.form.longitude : "",
              altitude: this.form.altitude ? this.form.altitude : "", // 高程
              respPersionId: this.form.respPersionId
                ? this.form.respPersionId
                : "",
              // companyHazardId: this.hazardId,
              companyHazardId: this.form.companyHazardId,
              regionId: this.form.regionId ? this.form.regionId : "",
              redundantField1: redundantField1 ? redundantField1 : "",
              sort: this.form.sort ? this.form.sort : "",
              regionName: this.form.regionName ? this.form.regionName : "",
              cameraType: this.cameraType ? this.cameraType : "",
            };
            addCamerainfoData(params).then((response) => {
              if (response.data.status == 200) {
                this.open = false;
                this.$message({
                  type: "success",
                  message: "新增成功",
                });
                this.reset();
                this.getData(this.enterpriseId);
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    alarmRuleSettings(flag) {
      this.flag = flag;
      this.visible = true;
    },
    submit() {},
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        this.form.altitude = data.location.altitude.toFixed(6) || 0;
        this.form.installLocation = data.formatted_address;
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）

  mounted() {
    this.getJishu();
    // 初始化危险源列表数据
    if (this.$store.state.login.user.user_type == "gov") {
      this.formInline.orgCode = "";
      this.formInline.orgName = "";
      this.form.orgCode = "";
      this.form.orgName = "";
    } else {
      this.formInline.orgCode = this.$store.state.login.user.org_code;
      this.formInline.orgName = this.$store.state.login.user.org_name;
      this.form.orgCode = this.$store.state.login.user.org_code;
      this.form.orgName = this.$store.state.login.user.org_name;
      this.initHazardList(this.$store.state.login.enterData.enterpId);
    }
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0;
}

.hazard-dialog {
  padding: 20px;

  .search-box {
    display: flex;
    margin-bottom: 15px;
  }
}
.dialog_h {
  height: 600px;
  padding: 30px 20px;
  overflow-y: auto;
}
/deep/ h2 {
  margin: 0;
}
.device {
  background-color: #fff;
  padding-bottom: 50px;
  .header {
    .title {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        // width: 1020px;
        width: 685px;
        float: left;
        // display: flex;
        // justify-content: space-between;
        .input {
          width: 180px;
        }
      }
      .btnBox {
        display: flex;
        & > * {
          margin-left: 10px;
        }
      }
    }
    .export {
      margin-right: 5px;
    }
  }
  .table {
    width: 100%;
  }
  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
