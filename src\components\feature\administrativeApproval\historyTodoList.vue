<template>
    <iframe class="iframe" src="https://zwfw.hbsis.gov.cn:8182/otherLogin?iframe=true&isOverdue=2&isOutSystem=1" frameborder="0"></iframe>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator'
export default {
//import引入的组件
name: "HistoryList",
components: {
},
};
</script>

<style lang="scss" scoped>
.iframe{
  width: 100%;
  height: 100vh;
}
</style>