<!-- 企业画像配置 -->
<template>
  <div class="portaitConfiguration">
    <div class="configuration-head">
      <!-- <div class="head-title">{{ planConfig.name }}</div> -->
      <div class="head-title" style="display: none;">风险评估总体设置方案</div>
      <div class="head-middle" style="display: none;">
        分值与评级：
        <span>0</span>
        <div class="progress-red"></div>
        <span>{{ planConfig.rankD||0 }}</span>
        <div class="progress-orange"></div>
        <span>{{ planConfig.rankC ||0 }}</span>
        <div class="progress-yellow"></div>
        <span>{{ planConfig.rankB ||0 }}</span>
        <div class="progress-green"></div>
        <span>{{ planConfig.rankA ||0 }}</span>
        <el-button type="primary" plain size="small" @click="showModal"
          >设置</el-button
        >
      </div>
      <div class="heade-right">
        <span
          ><img
            class="icon"
            src="../../../../../static/icon/tip-icon.png"
            alt=""
          />
          u1~u6总分值为{{planConfig.total||0 }}分、单个维度总权重配比均为100%，方可提交</span
        >
        <el-button type="primary" size="small" @click="handleSubmit"
          >保存</el-button
        >
        <el-button type="primary" size="small" @click="handlePubish"
          >提交配置方案</el-button
        >
      </div>
    </div>
    <!-- 指数配置 -->
    <div class="main-content">
      <div class="configuration-item" v-for="(item,index) in planConfig.mainDTOs">
        <div class="index-head bg-u1">
          <div class="left">
            <el-tooltip class="left" :content="`${item.levelName}(${item.levelCode})-u${index+1}`" placement="top-start" effect="dark">
                <span>{{ `${item.levelName}(${item.levelCode})-u${index+1}` }}</span>
            </el-tooltip>

          </div>
          <div class="right">{{ item.score || 0 }}分</div>
        </div>
        <div class="index-content">
          <indexCard
            v-for="sub in item.subDTOs"
            :key="sub.id"
            :item="sub"
            @edit="itemEdit(sub, item)"
            @delete="itemDelete(sub, item)"
            @start="itemStart(sub, item)"
          ></indexCard>
          <el-button
            type="primary"
            plain
            size="large"
            style="width: 100%"
            @click="showSecond(item)"
            >+</el-button
          >
        </div>
        <div class="index-head index-bottom">
          <div class="left">总权重配比</div>
          <div class="right">{{item.subDTOs?totalRate(item.subDTOs):0}}%</div>
        </div>
      </div>
    </div>
    <div class="configuration-head">
      <!-- <div class="head-title">{{ planConfig.name }}</div> -->
      <div class="head-title">风险评估总体设置方案</div>
      <div class="head-middle">
        分值与评级：
        <span>0</span>
        <div class="progress-red"></div>
        <span>{{ planConfig.rankD||0 }}</span>
        <div class="progress-orange"></div>
        <span>{{ planConfig.rankC ||0 }}</span>
        <div class="progress-yellow"></div>
        <span>{{ planConfig.rankB ||0 }}</span>
        <div class="progress-green"></div>
        <span>{{ planConfig.rankA ||0 }}</span>
        <el-button type="primary" plain size="small" @click="showModal"
          >设置</el-button
        >
      </div>
    </div>
    <!-- 分值与评级设置 -->
    <rateSetting
      v-if="firstVisible"
      :visible="firstVisible"
      :firstSetting="rateData"
      @close="closeBoolean"
      @submit="submitRate"
    ></rateSetting>
    <!-- 二级梯度配置 -->
    <secondSetting
      v-if="secondVisible"
      :visible="secondVisible"
      :subSetting="secondConfig"
      :mainSetting="mainSet"
      @close="closeSecond"
      @submit="handleSub"
    ></secondSetting>
  </div>
</template>
<script>
import indexCard from "./components/indexCard";
import rateSetting from "./components/rateSetting.vue";
import secondSetting from "./components/secondSetting.vue";
import { deepClone } from "@/utils/index";
import {
  getPortraitPlanByid,
  findByMain,
  deleteSubRule,
  updateSubRule,
  getPublishPlan,
  getPortraitPlanUpdate,
} from "@/api/enterprisePortrait";
export default {
  components: {
    indexCard,
    rateSetting,
    secondSetting,
  },
  props: {
    planId: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      firstVisible: false,
      secondVisible: false,
      planConfig: {
        name: "",
        mainDTOs: [],
      },
      // 一级配置
      mainSet: {},
      // 二级配置项
      secondConfig: {},
      // 分数配置项
      rateData: {},
    };
  },
  computed: {
    totalRate(){
      return (list)=>{
        let total = 0;
        list.forEach(item=>{
          total += item.weight
        })
        return total.toFixed(0)

      }
    }
  },
  created() {
    this.getPlanData();
  },
  methods: {
    // 编辑评分设置
    showModal() {
      this.firstVisible = true;
      this.rateData = deepClone(this.planConfig);
    },
    closeBoolean(val) {
      this.firstVisible = false;
      this.rateData = {};
    },
    // 新增二级规则
    showSecond(row) {
      this.secondVisible = true;
      this.mainSet = row;
    },
    // 关闭二级规则
    closeSecond() {
      this.secondVisible = false;
      this.mainSet = {};
      this.secondConfig = {};
    },
    // 提交二级规则
    async handleSub() {
      const item = this.planConfig.mainDTOs.find(
        (item) => item.id == this.mainSet.id
      );
      await this.getItemData(item.id, item);
      this.secondConfig = {};
      this.mainSet = {};
      this.secondVisible = false;
    },
    // 编辑二级规则
    itemEdit(type, row) {
      this.mainSet = deepClone(row);
      this.secondConfig = deepClone(type);
      this.secondVisible = true;
    },
    // 删除二级规则
    itemDelete(row, item) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(async () => {
          await deleteSubRule({ subId: row.id }).then((res) => {
            if (res.data.status == 200) {
              this.$message({
                type: "success",
                message: res.data.msg,
              });
              this.getItemData(item.id, item);
            }
          });
        })
        .catch(() => {});
    },
    itemStart(row) {
      row.switchFlag = row.switchFlag == "1" ? "0" : "1";
      this.$confirm(
        `${row.switchFlag == "0" ? "是否关闭" : "是否开启"}规则?`,
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      )
        .then(() => {
          updateSubRule(row).then((res) => {
            if (res.data.status == 200) {
              this.$message({
                type: "success",
                message: row.switchFlag == "1" ? "开启成功" : "关闭成功",
              });
              this.getItemData(item.id, item);
            }
          });
        })
        .catch(() => {});
    },
    // 获取方案数据
    getPlanData() {
      getPortraitPlanByid({ id: this.planId }).then((res) => {
        if (res.data.status == 200) {
          this.planConfig = res.data.data;
          this.planConfig.mainDTOs.forEach((item) => {
            this.getItemData(item.id, item);
          });
        }
      });
    },
    // 获取二级配置
    async getItemData(mainId, item) {
      await findByMain({ mainId: mainId }).then((res) => {
        if (res.data.status == 200) {
          item.subDTOs = res.data.data.subDTOs || [];
        }
      });
    },
    submitRate() {
      this.firstVisible = false;
      this.secondVisible = false;
      this.getPlanData();
    },
    handleSubmit() {
      const params = {
        ...this.planConfig,
        ruleMainAddDTOs: this.planConfig.mainDTOs,
      };
      delete params.mainDTOs;
      getPortraitPlanUpdate(params).then((res) => {
        if (res.data.status == 200) {
          this.getPlanData();
        }
      });
    },
    // 发布
    handlePubish() {
      this.$confirm("是否发布?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {
        await getPublishPlan({ id: this.planId }).then((res) => {
          if (res.data.status == 200) {
            this.$message({
              type: "success",
              message: res.data.msg,
            });
            this.$emit("submit");
          }else {
            this.$message({
              type: "error",
              message: res.data.msg,
            });
          }
        });
      });
    },
  },
};
</script>
<style scoped lang="scss">
.portaitConfiguration {
  height: 100%;
  width: 100%;

  .configuration-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 54px;

    .head-title {
      width: 180px;
      font-weight: 550;
    }

    .head-middle {
      flex: 1;
      display: flex;
      align-items: center;

      span {
        margin: 0 5px;
      }

      .progress-red {
        width: 129px;
        height: 5px;
        background: #f25151;
      }

      .progress-orange {
        width: 85px;
        height: 5px;
        background: #ffa337;
      }

      .progress-yellow {
        width: 52px;
        height: 5px;
        background: #f3e079;
      }

      .progress-green {
        width: 52px;
        height: 5px;
        background: #3dce70;
      }
    }

    .head-right {
      display: flex;
      align-items: center;

      .icon {
        width: 18px;
        height: 18px;
      }
    }
  }

  .main-content {
    display: flex;

    .configuration-item {
      flex: 1;
      min-width: 0;
      max-width: 300px;
      border: 0.1px solid #cbdcf5;
    }
  }

  // 标题栏样式
  .index-head {
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 20px 0 10px;
    font-family: PingFang SC;
    font-weight: 800;
    font-size: 18px;
    color: #333333;

    .left {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .right {
      width: auto;
      font-family: PingFang SC;
      font-weight: 800;
      font-size: 22px;
    }
  }

  .index-content {
    height: 560px;
    padding: 10px;
    background: #f2f7ff;
    box-sizing: border-box;
    overflow: auto;
  }

  .index-bottom {
    background: #f2f7ff;
    border: 1px solid #e7effb;

    .right {
      font-family: PingFang SC;
      font-weight: 800;
      font-size: 16px;
      color: #196dca;
    }
  }

  .bg-u1 {
    background: url("/static/img/portait/bg-man.png") no-repeat center center;
    background-size: 100% 100%;
  }

  .bg-u2 {
    background: url("/static/img/portait/bg-things.png") no-repeat center center;
    background-size: 100% 100%;
  }

  .bg-u3 {
    background: url("/static/img/portait/bg-management.png") no-repeat center
      center;
    background-size: 100% 100%;
  }

  .bg-u4 {
    background: url("/static/img/portait/bg-environment.png") no-repeat center
      center;
    background-size: 100% 100%;
  }

  .bg-u5 {
    background: url("/static/img/portait/bg-supervision.png") no-repeat center
      center;
    background-size: 100% 100%;
  }

  .bg-u6 {
    background: url("/static/img/portait/bg-performance.png") no-repeat center
      center;
    background-size: 100% 100%;
  }
}
</style>
