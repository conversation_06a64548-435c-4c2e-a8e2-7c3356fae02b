<template>
  <div class="riskEchart">
  
    <div class="riskEchartBox">
   
    </div>
  </div>
</template>
<script>
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  data() {
    return {        
    };
  },
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.riskEchart {

}
</style>