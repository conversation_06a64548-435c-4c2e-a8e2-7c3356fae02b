<template>
  <div class="emergency-events">
    <div class="header">
      <div class="title">
        <i class="icon el-icon-warning"></i>
        突发事件信息
      </div>
    </div>

    <div class="content">
      <div class="event-list">
        <div class="event-header">
          <span class="time-col">时间</span>
          <span class="level-col">事件类型</span>
          <span class="desc-col">事件标题</span>
          <span class="action-col">操作</span>
        </div>

        <div
          class="event-scroll-container"
          ref="scrollContainer"
          @mouseenter="pauseScroll"
          @mouseleave="resumeScroll"
        >
          <div
            class="event-scroll-content"
            ref="scrollContent"
            :style="{ transform: `translateY(${scrollOffset}px)` }"
          >
            <div
              class="event-item"
              v-for="(item, index) in displayEventList"
              :key="`${item.id || index}-${Math.floor(
                index / eventList.length
              )}`"
            >
              <span class="time-col">{{ item.time }}</span>
              <span class="level-col">
                <span :class="['level-tag']">
                  {{ item.eventTypeName }}
                </span>
              </span>
              <span class="desc-col" :title="item.description">
                {{ item.description }}
              </span>
              <span class="action-col">
                <el-button type="text" size="mini" @click="viewDetail(item)">
                  查看
                </el-button>
              </span>
            </div>
          </div>
        </div>

        <div v-if="loading" class="loading">
          <i class="el-icon-loading"></i>
          <span>加载中...</span>
        </div>

        <div v-else-if="eventList.length === 0" class="no-data">
          <div class="null"></div>
        </div>
      </div>

      <div class="action-buttons">
        <div class="button-item" @click="quickReport">
          <div class="button-content">
            <i class="el-icon-edit"></i>
            <span>快速上报</span>
          </div>
        </div>
        <div class="button-item" @click="indexDispatch">
          <div class="button-content">
            <i class="el-icon-data-line"></i>
            <span>指挥调度</span>
          </div>
        </div>
        <div class="button-item" @click="bigModel">
          <div class="button-content">
            <i class="el-icon-office-building"></i>
            <span>大模型</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getEventBase } from "@/api/accidentManagement";

export default {
  name: "EmergencyEvents",
  data() {
    return {
      eventList: [],
      loading: false,
      scrollOffset: 0,
      scrollTimer: null,
      isScrollPaused: false,
      itemHeight: 40, // 每个事件项的高度
      scrollSpeed: 1, // 滚动速度（像素/帧）
      scrollInterval: 50, // 滚动间隔（毫秒）
    };
  },
  computed: {
    // 获取用户的行政区划代码
    districtCode() {
      return this.$store.state.login.userDistCode || "";
    },
    username() {
      return this.$store.state.login.user.username || "";
    },
    // 创建循环显示的列表，复制数据以实现无缝滚动
    displayEventList() {
      if (this.eventList.length === 0) return [];
      // 复制两倍数据以实现无缝循环
      return [...this.eventList, ...this.eventList];
    },
  },
  methods: {
    // 获取事件数据
    async getEventData() {
      if (!this.districtCode) {
        console.warn("行政区划代码为空，无法获取事件数据");
        return;
      }

      this.loading = true;
      try {
        const response = await getEventBase({
          // districtCode: this.districtCode,
          specialCode: "hazardousChemical",
        });
        if (response && response.data.data.list) {
          const eventData = response.data.data.list;
          this.eventList = this.processEventData(eventData);

          // 重新启动滚动
          this.stopScroll();
          this.$nextTick(() => {
            this.startScroll();
          });
        } else {
          console.error("获取事件数据失败:", response);
          this.eventList = [];
        }
      } catch (error) {
        console.error("获取事件数据异常:", error);
        this.eventList = [];
      } finally {
        this.loading = false;
      }
    },

    // 处理事件数据，转换为组件需要的格式
    processEventData(eventData) {
      return eventData.map((item, index) => ({
        id: item.eventId || `event_${index}`,
        time: this.formatTime(item.occurTime || item.createTime),
        eventTypeName: item.eventTypeName,
        description:
          item.eventTitle ||
          item.infoDescription ||
          item.eventDetail ||
          "暂无描述",
        originalData: item, // 保存原始数据，用于详情查看
      }));
    },

    // 格式化时间
    formatTime(timeStr) {
      if (!timeStr) return "";
      try {
        const date = new Date(timeStr);
        return date
          .toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          })
          .replace(/\//g, "-");
      } catch (error) {
        return timeStr;
      }
    },

    viewDetail(item) {
      window.open(
        "http://************:8080/hubei-amap169/#/?phone=" +
          this.username +
          "&eventId=" +
          item.id
      );
    },
    quickReport() {
      this.$router.push({
        path: "/riskAssessment/accidentManagement",
        query: { tab: "事件信息" },
      });
    },
    indexDispatch() {
      window.open(
        "http://************:8080/hubei-amap169/#/?phone=" + this.username
      );
      // 实现指挥调度功能
    },
    bigModel() {
      window.top.postMessage({ action: "changeMenu" }, "*");
      // 实现大集团功能
    },
    // 开始滚动
    startScroll() {
      if (this.eventList.length <= 3) return; // 如果数据少于等于3条，不需要滚动

      this.scrollTimer = setInterval(() => {
        if (!this.isScrollPaused) {
          this.scrollOffset -= this.scrollSpeed;

          // 当滚动到一半时（即原始数据的长度），重置偏移量实现无缝循环
          const totalHeight = this.eventList.length * this.itemHeight;
          if (Math.abs(this.scrollOffset) >= totalHeight) {
            this.scrollOffset = 0;
          }
        }
      }, this.scrollInterval);
    },
    // 暂停滚动
    pauseScroll() {
      this.isScrollPaused = true;
    },
    // 恢复滚动
    resumeScroll() {
      this.isScrollPaused = false;
    },
    // 停止滚动
    stopScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
  },
  mounted() {
    // 获取突发事件数据
    this.getEventData();
  },
  beforeDestroy() {
    this.stopScroll();
  },
};
</script>

<style lang="scss" scoped>
.emergency-events {
  width: 100%;
  height: 100%;
  border: 1px solid #d8e0ee;
  box-shadow: 0px 0px 3px 0.01px #d8e0ee;
  background: #fff;

  .header {
    padding: 5px 10px 5px;
    border-bottom: 1px solid rgba(198, 207, 217, 0.33);

    .title {
      color: #3b4046;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;

      .icon {
        color: #6f81b5;
        font-size: 15px;
        margin-right: 8px;
      }
    }
  }

  .content {
    height: calc(100% - 60px);
    display: flex;
    flex-direction: column;
  }

  .event-list {
    flex: 1;
    padding: 5px 10px;

    .event-header {
      display: flex;
      align-items: center;
      padding: 5px 0;
      border-bottom: 1px solid #eee;
      font-weight: 600;
      color: #666;
      font-size: 14px;
    }

    .event-scroll-container {
      height: 70px; // 固定高度，显示约3条数据
      overflow: hidden;
      position: relative;
    }

    .event-scroll-content {
      transition: transform 0.1s linear;
    }

    .event-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f5f5f5;
      font-size: 14px;
      height: 40px; // 固定高度
      box-sizing: border-box;

      &:hover {
        background-color: #f8f9fa;
      }
    }

    .time-col {
      width: 140px;
      flex-shrink: 0;
    }

    .level-col {
      width: 160px;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;

      .level-tag {
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;

        &.general {
          background: #e6f7ff;
          color: #1890ff;
        }

        &.major {
          background: #fff7e6;
          color: #fa8c16;
        }

        &.serious {
          background: #fff2f0;
          color: #f5222d;
        }

        &.critical {
          background: #f6ffed;
          color: #52c41a;
        }
      }
    }

    .desc-col {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 10px;
    }

    .action-col {
      width: 60px;
      flex-shrink: 0;
      text-align: center;
    }

    .loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 120px;
      color: #666;

      i {
        font-size: 20px;
        margin-right: 8px;
      }
    }

    .no-data {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 120px;

      .null {
        width: 100px;
        height: 80px;
        background-image: url(../../../../../static/img/null.png);
        background-size: contain;
        background-repeat: no-repeat;
        background-position: center;
      }
    }
  }

  .action-buttons {
    display: flex;
    justify-content: space-around;
    padding: 5px 10px;
    border-top: 1px solid #eee;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

    .button-item {
      flex: 1;
      margin: 0 5px;
      cursor: pointer;

      .button-content {
        display: flex;
        // flex-direction: column;
        justify-content: center;
        align-items: center;
        border: 2px solid transparent;
        border-radius: 8px;
        padding: 8px 4px;
        text-align: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #fff;
        box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(
            90deg,
            transparent,
            rgba(255, 255, 255, 0.2),
            transparent
          );
          transition: left 0.5s;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(102, 126, 234, 0.5);
          background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);

          &::before {
            left: 100%;
          }
        }

        &:active {
          transform: translateY(0px);
          box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        i {
          display: block;
          font-size: 18px;
          margin-bottom: 4px;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        span {
          font-size: 12px;
          font-weight: 500;
          display: block;
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          letter-spacing: 0.3px;
        }
      }

      // 为不同按钮添加不同的渐变色
      &:nth-child(1) .button-content {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        box-shadow: 0 2px 8px rgba(255, 107, 107, 0.3);

        &:hover {
          background: linear-gradient(135deg, #ee5a24 0%, #ff6b6b 100%);
          box-shadow: 0 4px 12px rgba(255, 107, 107, 0.5);
        }
      }

      &:nth-child(2) .button-content {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        box-shadow: 0 2px 8px rgba(78, 205, 196, 0.3);

        &:hover {
          background: linear-gradient(135deg, #44a08d 0%, #4ecdc4 100%);
          box-shadow: 0 4px 12px rgba(78, 205, 196, 0.5);
        }
      }

      &:nth-child(3) .button-content {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
        box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);

        &:hover {
          background: linear-gradient(135deg, #fed6e3 0%, #a8edea 100%);
          box-shadow: 0 4px 12px rgba(168, 237, 234, 0.5);
        }

        i,
        span {
          text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }
      }
    }
  }
}
</style>
