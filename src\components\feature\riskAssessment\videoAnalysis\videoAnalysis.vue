<template>
  <div class="videoAnalysis">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" />
              智能识别报警
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div>
      <!-- {{districtCode}} -->
      <!-- v-if="isShowDist && showArea" -->
      <!-- {{$store.state.login.user.user_type +'------------------ '}}   -->

      <div class="seach-part">
        <div class="l">
          <el-cascader
            size="mini"
            v-if="$store.state.login.user.user_type != 'ent'"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            :show-all-levels="true"
          ></el-cascader>

          <el-select
            v-model="alarmId"
            size="mini"
            placeholder="请选择报警类型"
            clearable
          >
            <el-option
              v-for="item in options1"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <div
            style="width: 200px"
            v-if="$store.state.login.user.user_type != 'ent'"
          >
            <el-autocomplete
              popper-class="my-autocomplete"
              v-model="queryParams.orgName"
              :fetch-suggestions="querySearch"
              placeholder="请输入企业名称关键字"
              clearable
              @clear="clearSensororgCode()"
              @select="handleSelect"
              size="mini"
              style="width: 200"
            >
              <template slot-scope="{ item }">
                <div class="name">{{ item.enterpName }}</div>
              </template>
            </el-autocomplete>
          </div>

          <el-select
            v-model="feedbackStatus"
            size="mini"
            placeholder="请选择反馈状态"
            clearable
          >
            <el-option
              v-for="item in options2"
              :key="item.id"
              :label="item.label"
              :value="item.id"
            >
            </el-option>
          </el-select>
          <el-date-picker
            v-model="dateTime"
            size="mini"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
            @change="searchTime"
            unlink-panels
            clearable
          >
          </el-date-picker>
          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >

          <el-button type="" size="mini" @click="exportExcel()">导出</el-button>
        </div>
      </div>

      <div class="table-top">
        <h2>智能识别报警列表</h2>
      </div>

      <div class="table-main">
        <div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="enterpName"
                label="单位名称"
                min-width="180"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.enterpName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="行政区划"
                width="150"
                align="center"
                prop="districtName"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.districtName }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="视频点位名称"
                min-width="80"
                align="center"
                prop="channelName"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.channelName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="设备编码"
                min-width="80"
                align="center"
                prop="channelId"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.channelId }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="报警类型"
                min-width="80"
                align="center"
                prop="alarmType"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.alarmTypeName }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="报警时间"
                min-width="110"
                align="center"
                prop="applyTime"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.alarmTime | filterAlarmTime }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="反馈状态"
                min-width="80"
                align="center"
                prop="feedbackStatusName"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.feedbackStatusName }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                label="反馈时间"
                min-width="80"
                align="center"
                prop="feedbackTime"
              >
                <template slot-scope="{ row }">
                  <div>
                    {{ row.feedbackTime | filterFeedbackTime }}
                  </div>
                </template>
              </el-table-column>
              <!-- <el-table-column label="状态" min-width="110" align="center">
                <template slot-scope="{ row  }">
                  <div>
                    {{ row.endTime }}
                  </div>
                </template>
              </el-table-column> -->

              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="{ row }">
                  <div>
                    <el-button
                      type="text"
                      @click="showOpenFn(row)"
                      :disabled="row.fold == 0"
                      >展开</el-button
                    >

                    <el-button
                      type="text"
                      @click="clickDisposal(row)"
                      :disabled="row.feedbackStatus == 1"
                      v-if="$store.state.login.user.user_type == 'ent'"
                      >处置反馈</el-button
                    >
                    <el-button type="text" @click="WarnBool(row)"
                      >详情</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.pageSize"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- <alarmDeatil :detailAlarmDialog='detailAlarmDialog' @closeCenterDialog='closeCenterDialog'  ref="alarmDeatil"></alarmDeatil> -->

    <!-- 弹窗 -->
    <el-dialog
      title="报警详情"
      top="3vh"
      :visible.sync="detailAlarmDialog"
      width="1000px"
      @open="open()"
      :close-on-click-modal="false"
    >
      <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="监管通报" name="1"></el-tab-pane>
        <el-tab-pane label="企业反馈" name="0"></el-tab-pane>
      </el-tabs> -->
      <div class="warn">
        <!-- <div class="null" v-if="tableData == null"></div> -->
        <div
          class="warnStatus"
          :class="[detailData.feedbackStatus == 1 ? 'complete' : 'noComplete']"
        >
          <div class="start">
            <div class="step_line"><span>开始反馈</span></div>
          </div>
          <div class="end">
            反馈完成
            <p>{{ detailData.feedbackTime }} &nbsp;</p>
          </div>
        </div>
        <div class="showImage">
          <div
            v-if="dataList.length > 0"
            class="lunbo"
            @mouseenter="clear"
            @mouseleave="run"
          >
            <div class="img">
              <img
                :src="dataList[currentIndex]"
                alt=""
                @click.self="showBigImage(dataList[currentIndex])"
              />
            </div>

            <!-- <div class="dooted" v-if="this.dataList.length">
              <ul class="doo">
                <li
                  v-for="(item, index) in this.dataList"
                  :key="index"
                  :class="{ current: currentIndex == index }"
                  @click="gotoPage(index)"
                ></li>
              </ul>
            </div> -->
            <!-- <div class="right turn" @click="next()">
              <i class="el-icon-arrow-right"></i>
            </div>
            <div class="left turn" @click="up()">
              <i class="el-icon-arrow-left"></i>
            </div> -->

            <div class="botttomBox">
              <span
                class="el-icon-zoom-in"
                @click.self="showBigImage(dataList[currentIndex])"
              ></span>
              <!-- <span class="el-icon-download"></span> -->
            </div>
          </div>

          <div v-else>暂无图片！</div>
        </div>

        <div class="div1">
          <div class="table">
            <ul class="container">
              <li class="lang">
                <div class="l">企业名称</div>
                <div class="r">{{ detailData.enterpName || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">企业编码</div>
                <div class="r">{{ detailData.districtCode || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">报警时间</div>
                <div class="r">{{ detailData.alarmTime || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">报警类型</div>
                <div class="r">{{ detailData.alarmTypeName || "--" }}</div>
              </li>
              <li class="lang">
                <div class="l">点位名称</div>
                <div class="r">
                  {{ detailData.channelName || "--" }}
                </div>
              </li>
              <li class="lang">
                <div class="l">设备编码</div>
                <div class="r">
                  {{ detailData.channelId || "--" }}
                </div>
              </li>
              <!-- <li class="lang">
                <div class="l">电力户号</div>
                <div class="r">
                  {{ detailData }}
                </div>
              </li> -->
              <li class="lang bottom">
                <div class="l">原因说明</div>
                <div class="r rHeight">
                  {{ detailData.feedbackReason || "无" }}
                </div>
              </li>

              <li class="lang bottom">
                <div class="l">处置内容</div>
                <div class="r rHeight">
                  {{ detailData.feedbackHandle || "无" }}
                </div>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <!-- <div v-if='echartData.length==0' style="height:300px;text-align:center;line-height:300px">
        暂无用电量数据！
      </div> -->
    </el-dialog>

    <dialogEdit ref="dialogEdit"></dialogEdit>

    <bigImg
      :visible="photoVisible"
      :url="bigImgUrl"
      @closeClick="
        () => {
          photoVisible = false;
        }
      "
    ></bigImg>

    <!-- 展开弹窗 -->
    <div class="dialog">
      <el-dialog
        title="智能识别报警列表"
        :visible.sync="showOpen"
        width="1350px"
        top="10vh"
        @close="handleClose"
        :key="demoKey"
        v-dialogDrag
        :close-on-click-modal="false"
      >
        <el-scrollbar class="diagHeight">
          <el-table
            :data="subQuer.tableData.list"
            v-loading="subQuer.loading"
            style="width: 100%"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            ref="multipleTable"
          >
            <!-- <el-table-column type="selection" width="50" align="center">
              </el-table-column> -->
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>
            <el-table-column
              prop="enterpName"
              label="单位名称"
              min-width="180"
              align="center"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.enterpName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="行政区划"
              width="150"
              align="center"
              prop="districtName"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.districtName }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              label="视频点位名称"
              min-width="80"
              align="center"
              prop="channelName"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.channelName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="设备编码"
              min-width="80"
              align="center"
              prop="channelId"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.channelId }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="报警类型"
              min-width="80"
              align="center"
              prop="alarmType"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.alarmTypeName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="报警时间"
              min-width="110"
              align="center"
              prop="applyTime"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="{ row }">
                <div>
                  {{ row.alarmTime | filterAlarmTime }}
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" min-width="100" align="center">
              <template slot-scope="{ row }">
                <div>
                  <el-button type="text" @click="WarnBool(row)">详情</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination subpagination">
            <el-pagination
              @current-change="handleCurrentChange2"
              :current-page.sync="subQuer.currentPage"
              :page-size="subQuer.tableData.size"
              layout="total, prev, pager, next"
              background
              :total="subQuer.tableData.total"
            >
            </el-pagination>
          </div>
        </el-scrollbar>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  getCompanyProjectList, //列表查询
  videoAlarmType, //报警类型下拉列表
  videoAlarmFeedbackStatus, //反馈状态下拉列表
  videoAlarmFindPage,
  videoAlarmExport, //导出
  videoAlarmFindById,
  findSubPage,
} from "@/api/companyParticularJob";

import { getInformationBasicInfo } from "@/api/entList";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { getSearchArr } from "@/api/entList.js";
var dayjs = require("dayjs");

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import dialogEdit from "./dialogEdit.vue";
import bigImg from "./bigImg.vue";
export default {
  components: {
    dialogEdit,
    bigImg,
  },
  data() {
    return {
      showOpen: false,
      alarmId: "",
      alarmType: "",
      districtCode: this.$store.state.login.userDistCode, //行政区划代
      endTime: "",
      startTime: "",
      enterpId: "",
      dateTime: [], //查询时间
      feedbackStatus: "",
      detailData: {}, //详情数据
      photoVisible: false, //点击大图弹框
      bigImgUrl: "", //点击大图
      dataList: [
        // "../../../../../static/img/橙-较大风险.png",
        // "../../../../../static/img/红-重大风险.png",
        // "../../../../../static/img/黄-一般风险.png",
      ],
      currentIndex: 0, // 默认显示图片
      timer: null, // 定时器
      queryParams: {
        orgCode: "",
        orgName: "",
      },
      total: "",
      detailAlarmDialog: false,
      loading: false,
      tableData: [],
      options: [],
      options1: [],
      options2: [],
      // districtCode: this.$store.state.login.userDistCode, //行政区划代
      district: this.$store.state.controler.district,
      currentPage: 1,
      selection: [],
      demoKey: 0,
      subQuer: {
        currentPage: 1,
        pageSize: 5,
        tableData: [],
        total: 0,
        loading: false,
        alarmId: "",
      },
    };
  },

  filters: {
    filterFeedbackTime(val) {
      // debugger
      return val ? dayjs(val).format("YYYY-MM-DD HH:mm") : "";
    },
    filterAlarmTime(val) {
      return val ? dayjs(val).format("YYYY-MM-DD HH:mm") : "";
    },
  },

  methods: {
    showOpenFn(val) {
      this.showOpen = true;
      this.subQuer.alarmId = val.alarmId;
      this.getData2();
    },
    handleClose() {
      this.subQuer.tableData = [];
      this.subQuer.currentPage = 1;
      // 初始化弹窗位置
      this.demoKey =
        "demo-" + new Date() + ((Math.random() * 1000).toFixed(0) + "");
    },
    handleCurrentChange2(val) {
      this.subQuer.currentPage = val;
      this.getData2();
    },
    //查询列表
    getData2() {
      const dto = {
        alarmId: this.subQuer.alarmId,
        nowPage: this.subQuer.currentPage,
        pageSize: 10,
      };
      findSubPage(dto).then((res) => {
        this.subQuer.tableData = res.data.data;
        this.subQuer.total = res.data.data.total;
      });
    },
    showBigImage(e) {
      //点击图片函数，点击后，把photoVisible设置成true
      if (e != "") {
        this.photoVisible = true;
        this.bigImgUrl = e;
      }
    },
    //图片展示
    gotoPage(index) {
      this.currentIndex = index;
    },
    next() {
      if (this.currentIndex === this.dataList.length - 1) {
        this.currentIndex = 0;
      } else {
        this.currentIndex++;
      }
    },
    up() {
      if (this.currentIndex === 0) {
        this.currentIndex = this.dataList.length - 1;
      } else {
        this.currentIndex--;
      }
    },
    // 定时器
    run() {
      // this.timer = setInterval(() => {
      //   this.next()
      // }, 2000)
    },
    clear() {
      clearInterval(this.timer);
    },
    //图片展示 end

    //自动输入
    clearSensororgCode() {
      this.queryParams.orgCode = "";
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    //选择企业
    handleSelect(item) {
      this.queryParams.orgCode = item.enterpId;
      this.queryParams.orgName = item.enterpName;
    },
    //自动输入end

    //下拉
    serchStatusList() {
      //报警类型下拉列表
      videoAlarmType().then((res) => {
        if (res.data.status == 200) {
          this.options1 = res.data.data;
        }
      });
      //反馈状态下拉列表
      videoAlarmFeedbackStatus().then((res) => {
        if (res.data.status == 200) {
          this.options2 = res.data.data;
        }
      });
    },

    //弹奏执行echart
    open() {
      // const _self = this;
      // setTimeout(() => {
      //   _self.chartData();
      // }, 0);
    },
    closeCenterDialog() {
      this.detailAlarmDialog = false;
    },

    WarnBool(row) {
      // this.$refs.alarmDeatil.closeBoolean(true);
      this.detailAlarmDialog = true;
      this.detailFn(row);
    },
    detailFn(row) {
      this.dataList = [];
      videoAlarmFindById({ alarmId: row.alarmId }).then((res) => {
        if (res.data.status == 200) {
          this.detailData = res.data.data;
          //http://59.208.149.239:8090/ehl/group2/M00/00/75/CgACnGLnVz-AMNHeAAUtlxFr6x0181.jpg
          if (res.data.data.alarmPicUrl) {
            this.dataList.push(res.data.data.alarmPicUrl);
          }
          console.log(this.dataList.length, "图片没用图片");
        }
      });
    },
    //处置反馈
    clickDisposal(row) {
      this.$refs.dialogEdit.isDisposal(true);
      this.$refs.dialogEdit.detailFn(row);
    },
    searchTime(value) {},
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    //查询列表
    getData() {
      console.log(this.dateTime);
      this.loading = true;
      const dto = {
        alarmId: "",
        alarmType: this.alarmId, //告警类型
        districtCode: this.districtCode,
        startTime: this.dateTime.length > 0 ? this.dateTime[0] : "",
        endTime: this.dateTime.length > 0 ? this.dateTime[1] : "",
        enterpId: this.queryParams.orgCode, //企业id
        feedbackStatus: this.feedbackStatus, //反馈状态
        nowPage: this.currentPage,
        pageSize: 10,
      };
      videoAlarmFindPage(dto).then((res) => {
        //getCompanyParticularJobList   //getCompanyProjectList
        this.tableData = res.data.data;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
    // 导出
    exportExcel() {
      videoAlarmExport({
        alarmId: "",
        alarmType: this.alarmId, //告警类型
        districtCode: this.districtCode,
        startTime: this.dateTime.length > 0 ? this.dateTime[0] : "",
        endTime: this.dateTime.length > 0 ? this.dateTime[1] : "",
        enterpId: this.queryParams.orgCode, //企业id
        feedbackStatus: this.feedbackStatus, //反馈状态
        pageSize: 0,
        nowPage: 0,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "智能识别报警" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].companyCode;
      }
    },

    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
      this.$refs.DetailTable.getDistrict();
    },
    openDialoges(distCode, type, areaName) {
      this.$refs.DetailTablees.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTablees.getEntData(distCode, type, areaName);
      this.$refs.DetailTablees.getDistrict();
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleCurrentChange(val) {
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
    this.serchStatusList();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-scrollbar__wrap {
  overflow-x: auto;
}
/deep/ .diagHeight .el-table .el-table__cell {
  padding: 6px 0;
}
/deep/ .diagHeight .el-table .cell.el-tooltip > div {
  overflow: hidden;
  text-align: center;
  white-space: nowrap;
  text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
}
.subpagination {
  text-align: right;
  margin: 10px 0 0 0;
}
.diagHeight {
  height: 70vh;
}
.videoAnalysis {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
//弹框样式
.warn {
  .rHeight {
    height: 100px;
    overflow: auto;
  }
  overflow: auto;
  color: #000;
  .noComplete .start {
    color: #409eff;
  }
  .complete .end {
    color: #409eff;
  }
  .warnStatus {
    width: 80%;
    display: flex;
    margin: 0 auto;
    > div.active {
      color: #409eff;
    }
    > div {
      width: 50%;
      .step_line {
        width: 100%;
        position: relative;
      }
      span {
        display: inline-block;
        // background: #409eff;
      }
      span:after {
        content: "";
        position: absolute;
        right: 0;
        border-bottom: 1px solid #ccc;
        top: 9px;
        width: 85%;
      }
    }
  }
  ul {
    padding-inline-start: 0px;
  }
  .null {
    width: 467px;
    height: 285px;
    background-image: url(../../../../../static/img/null.png);
    background-size: contain;
    background-repeat: no-repeat;
    margin-left: 50%;
    transform: translateX(-50%);
    margin-top: 50px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 50%;
          display: flex;
          // border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 30%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 70%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}

//图片
.showImage {
  position: relative;
  height: 300px;
  line-height: 300px;
  text-align: center;
  width: 80%;
  margin: 0 auto 20px auto;
  ul li {
    float: left;
    width: 30px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    cursor: pointer;
    color: rgba(240, 238, 238, 0.8);
    font-size: 14px;
  }
  .img {
    height: 300px;
    width: 100%;
    border: 1px solid gray;
    img {
      height: 100%;
      width: 100%;
    }
  }
  .dooted {
    position: absolute;
    bottom: -10px;
    right: 0px;
  }
  .container {
  }
  .turn:hover .el-icon-arrow-right,
  .turn:hover .el-icon-arrow-left {
    color: #fff;
  }
  .turn:hover {
    background: #409eff;
  }
  .turn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    line-height: 40px;
    text-align: center;
    border: 2px solid #409eff;
    cursor: pointer;
  }
  .right {
    position: absolute;
    top: 100px;
    right: -60px;
  }
  .left {
    position: absolute;
    top: 100px;
    left: -60px;
  }
  .current {
    color: gray;
  }
  .el-icon-arrow-right,
  .el-icon-arrow-left {
    color: #409eff;
  }
  .botttomBox {
    position: absolute;
    left: 0;
    width: 100%;
    height: 30px;
    line-height: 30px;
    text-align: right;
    bottom: 0;
    padding: 0 0;
    background: rgba(0, 0, 0, 0.5);
    span {
      display: inline-block;
      height: 30px;
      vertical-align: top;
      line-height: 30px;
      width: 30px;
      text-align: center;
      cursor: pointer;
      color: #fff;
    }
    span:hover {
      color: #409eff;
    }
  }
}
</style>
