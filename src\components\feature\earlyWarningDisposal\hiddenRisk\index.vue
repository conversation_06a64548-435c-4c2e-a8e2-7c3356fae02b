<template>
  <div class="enterpriseManagement">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box">
                <a-icon type="home" theme="filled" class="icon" /> 安全风险预警
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <el-input
            v-model="queryParams.workOrderNo"
            size="mini"
            placeholder="请输入工单编号"
            style="width: 180px"
            clearable
          ></el-input>
          <el-select
            v-model="queryParams.eventLevel"
            size="mini"
            placeholder="事件等级"
            clearable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input
            v-model="queryParams.assignmentUnitCode"
            size="mini"
            placeholder="交办单位"
            style="width: 180px"
            clearable
          ></el-input>
          <el-input
            v-model="queryParams.disposeUnitCode"
            size="mini"
            placeholder="办理单位"
            style="width: 180px"
            clearable
          ></el-input>
          <!-- 交办时间 -->
          <el-date-picker
            v-model="queryParams.assignmentTime"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            placeholder="交办时间"
            @change="searchTime"
            clearable
          ></el-date-picker>
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="queryParams.districtCode"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
          ></el-cascader>
          <el-select
            v-model="queryParams.status"
            size="mini"
            placeholder="处置状态"
            clearable
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="queryParams.replyStatus"
            size="mini"
            placeholder="响应状态"
            clearable
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>安全风险预警列表</h2>
        </div>
        <div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              :default-sort="{ prop: 'date', order: 'descending' }"
            >
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="id"
                label="工单编号"
                min-width="150"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="eventDescription"
                label="事件描述"
                width="200"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="eventTypeName"
                label="事件来源"
                width="120"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="eventLevel"
                label="事件等级"
                min-width="110"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.eventLevel == 0">
                    <el-tag type="danger" effect="dark">不分级</el-tag></span
                  >
                  <span v-if="scope.row.eventLevel == 1"
                    ><el-tag type="danger" effect="dark">I级</el-tag></span
                  >
                  <span v-if="scope.row.eventLevel == 2"
                    ><el-tag type="danger" effect="dark">II级</el-tag></span
                  >
                  <span v-if="scope.row.eventLevel == 3"
                    ><el-tag type="danger" effect="dark">III级</el-tag></span
                  >
                  <span v-if="scope.row.eventLevel == 4"
                    ><el-tag type="danger" effect="dark">IV级</el-tag></span
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="assignmentUnitName"
                label="交办单位"
                min-width="150"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="assignmentTime"
                label="交办时间"
                min-width="150"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="disposeUnitName"
                label="事件处置单位"
                min-width="150"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="districtName"
                label="行政区划"
                width="120"
                align="center"
                :show-overflow-tooltip="true"
              >
              </el-table-column>
              <el-table-column
                prop="replyStatus"
                label="响应状态"
                width="90"
                align="center"
                :show-overflow-tooltip="true"
              >
                <!-- true-已响应 -->
                <template slot-scope="scope">
                  <span v-if="scope.row.replyStatus">
                    <el-tag effect="dark">已响应</el-tag></span
                  >
                  <span v-else
                    ><el-tag type="danger" effect="dark">未响应</el-tag></span
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="status"
                label="处置状态"
                width="90"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="scope">
                  <span v-if="scope.row.status == 0">
                    <el-tag type="danger" effect="dark">处置中</el-tag></span
                  >
                  <span v-if="scope.row.status == 1"
                    ><el-tag effect="dark">已处置</el-tag></span
                  >
                  <span v-if="scope.row.status == 2"
                    ><el-tag type="danger" effect="dark">未处置</el-tag></span
                  >
                  <span v-if="scope.row.status == 3"
                    ><el-tag type="danger" effect="dark">草稿</el-tag></span
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="操作"
                min-width="280"
                align="center"
                fixed="right"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <el-button type="text" @click="handleView(row)"
                      >查看</el-button
                    >
                    <el-button type="text" @click="relateVideo(row)"
                      >关联视频</el-button
                    >
                    <el-button
                      type="text"
                      :disabled="row.eventTypeName != '物联监测'"
                      @click="generateEvent(row)"
                      >生成事件信息</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.size"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- 生成事件信息弹窗 -->
    <el-dialog
      title="生成事件信息"
      :visible.sync="eventDialogVisible"
      top="5vh"
      width="1100px"
      @close="closeEventDialog"
      v-if="eventDialogVisible"
      :close-on-click-modal="false"
    >
      <div class="dialog">
        <el-form
          :model="eventForm"
          :rules="eventRules"
          ref="eventForm"
          label-width="150px"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="事件标题:" prop="title">
                <el-input
                  v-model.trim="eventForm.title"
                  maxlength="100"
                  placeholder="请输入事件标题"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <div>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item prop="address" label="事件地址:">
                  <el-input
                    v-model="eventForm.address"
                    placeholder="请输入事件地址"
                    maxlength="100"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事件类型:" prop="typeCode">
                <el-select
                  v-model="eventForm.typeCode"
                  placeholder="请输入事件类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.code"
                    :value="item.code"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事件等级:" prop="levelCode">
                <el-select
                  v-model="eventForm.levelCode"
                  placeholder="请输入事件等级"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in levelList"
                    :key="item.levelCode"
                    :value="item.levelCode"
                    :label="item.levelName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="死亡人数:" prop="deathNum">
                <el-input
                  v-model.trim="eventForm.deathNum"
                  maxlength="10"
                  class="accident-form-input"
                ></el-input
                >人
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="受伤人数:" prop="woundNum">
                <el-input
                  v-model.trim="eventForm.woundNum"
                  class="accident-form-input"
                  maxlength="10"
                ></el-input>
                人
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="事发时间:" prop="time">
                <el-date-picker
                  v-model="eventForm.time"
                  type="date"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择事发日期"
                  style="width: 100%"
                  :picker-options="pickerOptions"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="事发企业:" prop="companyName">
                <el-input
                  v-model.trim="eventForm.companyName"
                  style="width: 100%"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="关联重大危险源:" prop="dangerId">
                <el-select
                  v-model="eventForm.dangerId"
                  placeholder="请选择危险源"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dangerSourceList"
                    :key="item.dangerId"
                    :value="item.dangerId"
                    :label="item.dangerName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联危险化学品:" prop="chemicalsId">
                <el-select
                  v-model="eventForm.chemicalsId"
                  placeholder="请选择危险化学品"
                  style="width: 100%"
                  multiple
                  collapse-tags
                >
                  <el-option
                    v-for="item in hazarchemList"
                    :key="item.hazarchemId"
                    :value="item.hazarchemId"
                    :label="item.hazarchemName"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="经度:" prop="longitude">
                <el-input
                  v-model.trim="eventForm.longitude"
                  style="width: 240px"
                  placeholder="请输入经度"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纬度:" prop="latitude">
                <el-input
                  v-model="eventForm.latitude"
                  style="width: 240px"
                  placeholder="请输入纬度"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div class="map_height">
                <el-form-item label="地图定位">
                  <egisMap
                    :islistener="false"
                    ref="detailMap"
                    :datas="eventForm"
                    style="height: 200px"
                    @mapCallback="mapcallback"
                  ></egisMap>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="详情描述:" prop="description">
                <el-input
                  v-model.trim="eventForm.description"
                  type="textarea"
                  :rows="5"
                  placeholder="2000字以内"
                  maxlength="2000"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="上传附件:">
                <AttachmentUpload
                  :attachmentlist="eventForm.file"
                  :limit="1"
                  type="office"
                  v-bind="{}"
                >
                </AttachmentUpload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="closeEventDialog">取 消</el-button>
          <el-button type="primary" @click="saveEventData">提 交</el-button>
        </div>
      </div>
    </el-dialog>
    <!-- 查看关联视频弹窗 -->
    <el-dialog
      title="查看关联视频"
      :visible.sync="videoDialogVisible"
      width="70%"
      @close="closeVideoDialog"
      :close-on-click-modal="false"
      class="video-dialog"
    >
      <div class="video-container">
        <div class="video-list">
          <h3>{{ currentHazard && currentHazard.disposeUnitName }}</h3>
          <div class="video-list-content" v-loading="videoLoading">
            <ul>
              <li
                v-for="(item, index) in videoList"
                :key="index"
                :class="{
                  active: selectedVideo && selectedVideo.id === item.id,
                }"
                @click="selectVideo(item)"
              >
                <span>{{ item.deviceName || "视频" + (index + 1) }}</span>
              </li>
              <li
                v-if="videoList.length === 0 && !videoLoading"
                class="no-data"
              >
                暂无关联视频
              </li>
            </ul>
          </div>
        </div>
        <div class="video-player">
          <!-- 四路视频播放器 -->
          <div class="player-container">
            <multiVideoPlayer ref="multiVideoPlayer" />
          </div>
        </div>
      </div>
    </el-dialog>
    <workOrderDialog
      v-if="workOrderVisible"
      :ent-obj="workOrderInfo"
      :show="workOrderVisible"
      @closeBoolean="closeWorkOrder"
    ></workOrderDialog>
  </div>
</template>
<script>
import { getRiskWarningData } from "@/api/earlyWarningDisposal";
import { findCamerainfoList } from "@/api/sensor";
import {
  getAccidentTypeListData,
  addAccidentDuty,
} from "@/api/accidentManagement";
import { getEnt } from "@/api/dailySafety";
import { getVideoPreviewUrl } from "@/api/riskAssessment";
import { getSelectData, getHazarchemList } from "@/api/entList";
import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
import { getSearchArr } from "@/api/entList.js";
import workOrderDialog from "./workOrderDialog.vue";
import multiVideoPlayer from "@/components/feature/riskAssessment/videoOnlineMonitoring/multiVideoPlayer.vue";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {
    workOrderDialog,
    multiVideoPlayer,
  },
  data() {
    return {
      videoDialogVisible: false,
      currentHazard: null,
      videoLoading: false,
      videoList: [],
      selectedVideo: null,
      // 事件信息相关数据
      eventDialogVisible: false,
      typeList: [], // 事件类型列表
      levelList: [], // 事件等级列表
      dangerSourceList: [], // 危险源列表
      hazarchemList: [], // 危险化学品列表
      eventForm: {
        title: "", // 事件标题
        address: "", // 事件地址
        typeCode: "", // 事件类型
        levelCode: "", // 事件等级
        deathNum: "", // 死亡人数
        woundNum: "", // 受伤人数
        time: "", // 事发时间
        file: [],
        companyName: "", // 事发企业
        companyId: "", // 事发企业ID
        dangerId: "", // 关联危险源
        chemicalsId: [], // 关联危险化学品（多选）
        longitude: "", // 经度
        latitude: "", // 纬度
        description: "", // 详情描述
      },
      eventRules: {
        title: [{ required: true, message: "请输入事件标题", trigger: "blur" }],
        address: [
          { required: true, message: "请输入事件地址", trigger: "blur" },
        ],
        typeCode: [
          { required: true, message: "请选择事件类型", trigger: "change" },
        ],
        levelCode: [
          { required: true, message: "请选择事件等级", trigger: "change" },
        ],
        time: [
          { required: true, message: "请选择事发时间", trigger: "change" },
        ],
        description: [
          { required: true, message: "请填写事件描述", trigger: "blur" },
        ],
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      loading: false,
      tableData: {},
      queryParams: {
        workOrderNo: "", //工单编号
        eventLevel: "", //事件等级
        assignmentUnitCode: "", //交办单位编码
        disposeUnitCode: "", //事件处置单位编码
        assignmentTime: "", //交办时间
        startTime: "", //交办时间开始时间
        endTime: "", //交办时间结束时间
        districtCode: "", //行政区划编码
        status: "", //处置状态
        replyStatus: "", //响应状态
      },
      options: [
        {
          value: "0",
          label: "不分级",
        },
        {
          value: "1",
          label: "I级",
        },
        {
          value: "2",
          label: "II级",
        },
        {
          value: "3",
          label: "III级",
        },
        {
          value: "4",
          label: "IV级",
        },
      ],
      options1: [
        {
          value: "0",
          label: "处置中",
        },
        {
          value: "1",
          label: "已处置",
        },
        {
          value: "2",
          label: "未处置",
        },
        {
          value: "3",
          label: "草稿",
        },
      ],
      options2: [
        {
          value: false,
          label: "未响应",
        },
        {
          value: true,
          label: "已响应",
        },
      ],
      workOrderVisible: false,
      workOrderInfo: {},
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      district: this.$store.state.controler.district,
    };
  },
  methods: {
    // 查看关联视频
    relateVideo(row) {
      this.videoDialogVisible = true;
      this.currentHazard = row;
      const disposeUnitCode = row.disposeUnitCode.split("_")[0];
      this.getRelatedVideos(row.dangerId, disposeUnitCode);
    },
    // 获取关联视频列表
    getRelatedVideos(companyHazardId, orgCode) {
      this.videoLoading = true;
      this.videoList = [];
      this.selectedVideo = null;
      findCamerainfoList({
        companyHazardId: companyHazardId,
        orgCode: orgCode,
      })
        .then((res) => {
          this.videoLoading = false;
          if (res.data.status === 200) {
            this.videoList = res.data.data || [];
            // 如果有视频，默认选中第一个
            if (this.videoList.length > 0) {
              this.selectVideo(this.videoList[0]);
            }
          } else {
            this.$message.error(res.data.msg || "获取视频列表失败");
          }
        })
        .catch(() => {
          this.videoLoading = false;
          this.$message.error("获取视频列表失败");
        });
    },
    // 选择视频
    selectVideo(video) {
      this.selectedVideo = video;

      // 播放视频
      this.playVideo(video.id);
    },
    // 播放视频
    async playVideo(channelId) {
      try {
        // 使用新的视频预览接口
        const res = await getVideoPreviewUrl({
          channelId: channelId,
          schema: 5, // 使用默认的流媒体协议
          subType: 1, // 实时预览
        });

        if (res.status === 200 && res.data?.data?.url) {
          const videoSource = {
            id: channelId,
            name: `摄像头-${channelId}`,
            url: res.data.data.url,
            channelId: channelId,
            expireTime: res.data.data.expireTime,
            schema: res.data.data.schema,
            status: "online",
          };

          // 添加到四路视频播放器
          this.$nextTick(() => {
            if (
              this.$refs.multiVideoPlayer &&
              typeof this.$refs.multiVideoPlayer.addVideoToChannel ===
                "function"
            ) {
              const channelIndex =
                this.$refs.multiVideoPlayer.addVideoToChannel(videoSource);
              if (channelIndex >= 0) {
                console.log(`视频已添加到通道 ${channelIndex + 1}`);
              } else {
                console.error("添加视频到多路播放器失败");
              }
            } else {
              console.error("多路播放器组件未准备好");
            }
          });
        } else {
          this.$message.error(
            "获取视频流失败：" + (res.data?.msg || "未知错误")
          );
        }
      } catch (error) {
        console.error("播放视频失败", error);
        this.$message.error("播放视频失败：" + (error.message || "未知错误"));
      }
    },
    // 关闭视频弹窗
    closeVideoDialog() {
      // 关闭所有视频播放
      if (this.$refs.multiVideoPlayer) {
        this.$refs.multiVideoPlayer.closeAllVideos();
      }

      this.videoDialogVisible = false;
      this.videoList = [];
      this.selectedVideo = null;
    },
    // 生成事件信息
    generateEvent(row) {
      console.log("生成事件信息======================", row);
      // 初始化事件相关数据
      const enterpriseId = row.disposeUnitCode.split("_")[0]; // 将row.disposeUnitCode以_隔开,取第一个
      this.getEventRelatedData(enterpriseId);

      // 初始化表单
      this.resetEventForm();
      // 填充报警相关信息
      this.eventForm.address = row.enterpAddress || "";
      this.eventForm.companyName = row.disposeUnitName || "";
      this.eventForm.companyId = row.disposeUnitCode.split("_")[0] || "";
      this.eventForm.dangerId = row.dangerId || "";
      // 如果有经纬度信息
      if (row.latitude && row.longitude) {
        this.eventForm.longitude = row.longitude;
        this.eventForm.latitude = row.latitude;
      }
      this.eventDialogVisible = true;
    },
    // 重置事件表单
    resetEventForm() {
      this.eventForm = {
        title: "",
        address: "",
        typeCode: "",
        levelCode: "",
        deathNum: "",
        woundNum: "",
        time: "",
        companyName: "",
        companyId: "",
        dangerId: "",
        chemicalsId: [], // 初始化为空数组，支持多选
        longitude: "",
        latitude: "",
        description: "",
      };
    },
    // 地图定位
    mapcallback(data) {
      // 标点赋值
      this.eventForm.longitude = data.location.lon.toFixed(6);
      this.eventForm.latitude = data.location.lat.toFixed(6);
      this.eventForm.address = data.formatted_address;
      if (data.addressComponent.county_code != "") {
      }
      //districtName
      this.eventForm.districtCode = data.addressComponent.county_code.slice(
        3,
        data.addressComponent.county_code.length
      );
    },
    // 关闭事件弹窗
    closeEventDialog() {
      this.eventDialogVisible = false;
      this.resetEventForm();
    },

    // 保存事件数据
    saveEventData() {
      this.$refs.eventForm.validate((valid) => {
        if (valid) {
          // 构建保存数据
          const saveData = {
            ...this.eventForm,
            // 添加其他必要字段
            districtCode: this.$store.state.login.userDistCode,
          };
          addAccidentDuty(saveData)
            .then((res) => {
              if (res.data.status === 200) {
                this.$message.success("事件信息保存成功");
                this.closeEventDialog();
              } else {
                this.$message.error(res.data.msg || "保存失败");
              }
            })
            .catch(() => {
              this.$message.error("保存失败，请稍后重试");
            });
        } else {
          return false;
        }
      });
    },
    // 获取事件相关数据
    getEventRelatedData(enterpriseId) {
      // 获取事件类型和等级列表
      getAccidentTypeListData().then((res) => {
        if (res.data.status === 200) {
          this.typeList = res.data.data.typeCode || [];
          this.levelList = res.data.data.accidentLevel || [];
        }
      });

      getSelectData({
        enterpId: enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.dangerSourceList = res.data.data;
        }
      });

      // 获取危险化学品列表
      getHazarchemList(enterpriseId).then((res) => {
        if (res.data.code == 0) {
          this.hazarchemList = res.data.data;
        }
      });
    },
    querySearch(queryString, cb) {
      this.getSeachData(queryString || "", cb);
    },
    getSeachData(keyWord, cb) {
      getSearchArr(keyWord)
        .then((res) => {
          if (res.data.code == 0) {
            if (res.data.data.length > 0) {
              cb(res.data.data);
            } else {
              cb([]);
            }
          }
        })
        .catch((e) => {
          console.log(e, "请求错误");
        });
    },
    clearAssignmentCode() {
      this.queryParams.assignmentUnitCode = "";
    },
    clearDisposeCode() {
      this.queryParams.disposeUnitCode = "";
    },
    // 查看
    handleView(row) {
      this.workOrderVisible = true;
      this.workOrderInfo = row;
    },
    // 关闭弹窗
    closeWorkOrder() {
      this.workOrderVisible = false;
      this.workOrderInfo = {};
    },
    searchTime(value) {
      this.queryParams.assignmentTime = value;
      if (value) {
        this.queryParams.startTime = new Date(
          this.queryParams.assignmentTime[0]
        ).Format("yy-MM-dd hh:mm:ss");
        this.queryParams.endTime = new Date(
          new Date(this.queryParams.assignmentTime[1].getTime() + 86399900)
        ).Format("yy-MM-dd hh:mm:ss");
      } else {
        this.queryParams.startTime = "";
        this.queryParams.endTime = "";
      }
    },
    getData() {
      if (this.$store.state.login.user.user_type == "ent") {
        getEnt({}).then((res) => {
          if (res.data.code == 0) {
            this.getDataes([res.data.data.enterpId]);
          }
        });
      } else {
        this.getDataes(null);
      }
    },
    getDataes(id) {
      this.loading = true;
      const { assignmentTime, ...params } = this.queryParams;
      if (params.eventLevel) {
        params.eventLevel = [params.eventLevel];
      } else {
        params.eventLevel = [];
      }
      getRiskWarningData({
        ...params,
        pageSize: 10,
        nowPage: this.currentPage,
      }).then((res) => {
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
    handleCurrentChange(val) {
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
  computed: {
    ...mapStateLogin({
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      align-items: center;

      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
.dialog {
  height: 70vh;
  overflow-y: scroll;
  overflow-x: hidden;
  padding-right: 10px;

  .dialog-footer {
    display: flex;
    justify-content: center;

    & > * {
      margin: 0 10px;
    }
  }
}
.disposalSituation {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
