<template>
  <!-- <a-layout :class="['review', isMobile ? 'h5' : 'pc']"> -->
  <div>
    <a-layout class="review h5">
      <a-layout class="review-main">
        <div class="review-main-title">危化企业“三同时”复查</div>

        <div v-if="isShow">
          <div class="content">
            <div class="content-list">
              <p class="tipInfo">
                尊敬的{{
                  approvalReviewData.expertName
                }}专家您好，欢迎使用湖北省企业安全生产“三同时”专家审查意见在线复查功能
              </p>
              <div class="content-list-item">
                <div class="title">
                  <span><i class="baseinfo"></i>基本信息</span>
                </div>
                <div class="detail">
                  <!-- <table border="1" cellspacing="0" width="80%" height="80" v-if="!isMobile">
                <tr>
                  <th>企业名称</th>
                  <td colspan="3">常熟华虞环境科技有限公司</td>
                  <th>项目名称</th>
                  <td>xx公司xx装置项目</td>
                </tr>
                <tr>
                  <th>报告类型</th>
                  <td>设计报告</td>
                  <th>项目类型</th>
                  <td>新建</td>
                  <th>审批级别</th>
                  <td>省级（默认）</td>
                </tr>
              </table> -->
                  <ul class="list">
                    <li>
                      <span class="label">企业名称</span>
                      <span class="val">{{ infoDTO.enterpName }}</span>
                    </li>
                    <li>
                      <span class="label">项目名称</span>
                      <span class="val">{{ infoDTO.projectName }}</span>
                    </li>
                    <li>
                      <span class="label">报告类型</span>
                      <span class="val">{{ infoDTO.reportTypeName }}</span>
                    </li>
                    <li>
                      <span class="label">项目类型</span>
                      <span class="val">{{ infoDTO.projectTypeName }}</span>
                    </li>
                    <li>
                      <span class="label">审批级别</span>
                      <span class="val">{{
                        infoDTO.approvalLevel | filterapprovalStatusName
                      }}</span>
                    </li>
                    <!-- <li>
                  <span class="label">企业名称</span>
                  <span class="val">常熟华虞环境科技有限公司</span>
                </li> -->
                    <li class="file">
                      <span class="label">电子报告</span>
                      <div class="file-con">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.reportAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                        <!-- <AttachmentUpload
                        :attachmentlist="infoDTO.reportAttachList"
                        :limit="5"
                        type="pdf"
                        v-bind="{}"
                        :editabled="true"
                      ></AttachmentUpload> -->
                      </div>
                    </li>
                    <li class="file">
                      <span class="label">专家意见表</span>
                      <div class="file-con">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.commentAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                        <!-- <AttachmentUpload
                          :attachmentlist="infoDTO.commentAttachList"
                          :limit="5"
                          type="pdf"
                          v-bind="{}"
                          :editabled="true"
                        ></AttachmentUpload> -->
                      </div>
                    </li>
                    <li class="file">
                      <span class="label">修订对照表</span>
                      <div class="file-con">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.reviseAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                        <!-- <AttachmentUpload
                          :attachmentlist="infoDTO.reviseAttachList"
                          :limit="5"
                          type="pdf"
                          v-bind="{}"
                          :editabled="true"
                        ></AttachmentUpload> -->
                      </div>
                    </li>
                    <li class="file">
                      <span class="label">附件</span>
                      <div class="file-con" v-if="infoDTO.otherAttachList.length!=0">
                        <div
                          class="file-name"
                          v-for="(el, index) of infoDTO.otherAttachList"
                          :key="index"
                        >
                          <i></i>
                          <span @click="clickPdf(el)">{{ el.name }}</span>
                        </div>
                      </div>
                       <div v-else>暂无数据</div>
                      <!-- <div class="file-con">
                    <div class="file-name">
                      <i class="file"></i>
                      <span>相关附件2.pdf</span>
                    </div>
                    <div class="file-size">380.6KB</div>
                  </div> -->
                    </li>
                    <li class="file">
                      <span class="label">备注</span>
                      <div class="file-con">
                        {{ infoDTO.remark || "无" }}
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
              <div class="content-list-item" v-if="feedbackList.length > 0">
                <div class="title" @click="handleFole">
                  <span><i class="feedback"></i>专家组成员复查意见</span>
                  <div class="feedbackNum">
                    <!-- <span>已反馈2/12</span> -->
                    <i
                      :class="
                        !isFole ? 'el-icon-arrow-up' : 'el-icon-arrow-down'
                      "
                    ></i>
                  </div>
                </div>
                <div
                  class="detail"
                  :style="{ display: !isFole ? 'block' : 'none' }"
                >
                  <el-timeline>
                    <el-timeline-item
                      v-for="(item, index) in feedbackList"
                      :key="index"
                      color="#3470ff"
                      timestamp=""
                    >
                      <div class="feedCon">
                        <div class="info">
                          <div class="info-left">
                            <span class="name">{{ item.expertName }}</span>
                            <span class="line"></span>
                            <span class="tel">{{ item.expertTelephone }}</span>
                            <span
                              :class="[
                                'status',
                                `status_${item.expertApproval}`,
                              ]"
                            >
                              <i
                                class="el-icon-check"
                                v-if="item.status === '0'"
                              ></i>
                              <!-- {{ item.status | filterStatus }} -->
                              {{ item.expertApprovalName }}
                            </span>
                          </div>
                          <!-- <div class="info-date">{{ dayjs(item.updateTime).format('MM/DD HH:mm') }}</div> -->
                          <div class="info-date">
                            {{ item.updateTime | fiterUpdataTime }}
                          </div>
                        </div>




                          <div class="exprertWenj">
                          <div class="exprertWenjBox">
                            <span> 附件： </span>
                            <div class="experComment">
                              <AttachmentUpload
                              :attachmentlist="item.commentAttachments"
                              :limit="5"
                              type="pdf"
                              v-bind="{}"
                              :editabled="true"
                            ></AttachmentUpload>
                            </div>               
                          </div>
                        </div>





                         <div class="exprertWenj commentStly">
                        <div class="exprertWenjBox">
                          <span>意见：</span>
                          <div class="experComment">
                            {{ item.expertComment || "无意见" }}
                          </div>
                        </div>
                      </div>


                        <!-- <div class="mark">
                          {{ item.expertComment || "无意见" }}
                        </div> -->
                      </div>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </div>
              <div class="content-list-item">
                <div class="title">
                  <span><i class="idea"></i>专家复查意见</span>
                </div>
                <div class="detail">
                  <ul class="list">
                    <li>
                      <span class="label">专家姓名</span>
                      <span class="val">
                        {{ approvalReviewData.expertName }}</span
                      >
                    </li>
                    <li>
                      <span class="label">结论</span>
                      <div class="val">
                        <el-radio-group
                          v-model="reviewResult"
                          @change="handleCheck"
                        >
                          <el-radio label="0">通过</el-radio>
                          <el-radio label="1">不通过</el-radio>
                        </el-radio-group>
                      </div>
                    </li>

                      <li>
                      <span class="label">附件：</span>
                      <div class="val">
                        <div  class="uploadBox">
                          <AttachmentUpload
                          :attachmentlist="commentAttachments"
                          :limit="5"
                          type="office"
                          v-bind="{}"                       
                        ></AttachmentUpload>
                        </div>                      
                      </div>
                    </li>




                    <li class="file">
                      <span class="label">意见说明</span>
                      <div class="file-con">
                        <el-input
                          type="textarea"
                          :rows="5"
                          placeholder="请输入意见说明"
                          v-model.trim="checkMark"
                           maxlength="500"                 
                          show-word-limit
                        >
                        </el-input>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

             <div class="btnStyle">
              <el-button  @click="clickDraft">草稿</el-button>

              <el-button
             
                type="primary"
                @click="handleSign"               
                >签名提交</el-button
              >
            </div>
            <!-- <div class="content-btn" @click="handleSign">
              <div>
                <i class="sign"></i>
                <span>签名提交</span>
              </div>
            </div> -->



          </div>
        </div>

        <div v-else class="tipMore">
          <div>{{isShowTit || '复核已完成，请勿重复访问'}}</div>
        </div>
      </a-layout>
      <el-dialog
        custom-class="sign-dialog"
        :visible.sync="dialogVisible"
        width="90%"
        :close-on-click-modal="false"
      >
        <span slot="title" class="sign-header">
          <span>手写签名</span>
          <span class="tips">请在下方灰色区域手写签名</span>
        </span>
        <Sign @closeSign="handleClose"></Sign>
      </el-dialog>

      <!-- pdf显示 -->
      <el-dialog
        title="预览"
        :visible.sync="dialogVisible3"
        width="100%"
        height="80%"
        top="5vh"
        @close="closeDialog"
        class="pdh5Dialog"
        :close-on-click-modal="false"
        :destroy-on-close="true"
      >
        <div class="dialog dialogInfo" id="dialogpdfRef">
          <!-- <pdfSignatureView :linkattachId='linkattachId'></pdfSignatureView> -->
          <div v-loading="loading">
            <!-- <iframe
              id="pdfRef"
              ref="pdf"
              :src="this.pdfUrl"
              width="100%"
              frameborder="no"
              border="0"
              marginwidth="0"
              marginheight="0"
              style="height:calc(100vh - 100px)"
            ></iframe> -->
          </div>
        </div>
      </el-dialog>

      <!-- word 显示-->
      <el-dialog  
        v-if="previewShow"
        title="预览"
        height="80%"
        top="5vh"
        :visible.sync="previewShow"
        append-to-body
        width="100%"
        :close-on-click-modal="false"
      >
        
        <div
          style="height: calc(100vh - 100px); overflow: auto"
          class="wordReview"
        >
          <div v-loading="loading" ref="word"></div>
        </div>

        <span slot="footer" class="dialog-footer">
          <el-button type="primary" plain @click="previewShow = false"
            >关 闭</el-button
          >
        </span>
      </el-dialog>
    </a-layout>
  </div>
</template>

<script>
import { mapState } from "vuex";
import Sign from "./signH5.vue";
import { shortCodeRestore } from "@/api/mailList";
import axios from "axios";
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js";
const docx = require("docx-preview");
window.JSZip = require("jszip");

var dayjs = require("dayjs");
import {
  projectApprovalReview,
  projectApprovalDecide,
  projectEvaluateDraft, //草稿
} from "@/api/companyParticularJob";

export default {
  name: "reviewH5",
  components: {
    Sign,
    AttachmentUpload,
  },
  data() {
    return {
      commentAttachments:[],
      pdfh5: null,
      previewShow: false,
      loading: true,
      pdfUrl: "",
      dialogVisible3: false,
      isShow: true,
      isShowTit:'',
      isFole: false, // 是否折叠
      feedbackList: [
        {
          name: "王芳",
          tel: "15678768098",
          status: "1",
          time: "09-26/08:30",
          mark: "无审核意见",
        },
        {
          name: "王芳",
          tel: "15678768098",
          status: "0",
          time: "09-26/08:30",
          mark: "三同时修订对照表不符合相关条例规定，需按照规定重新填写上传。",
        },
      ],
      reviewResult: "", // 是否复查通过
      checkMark: "", // 审核说明
      dialogVisible: false, // 签名弹窗
      approvalId: "",
      expertTelephone: "",
      approvalReviewData: "",
      infoDTO: "",
    };
  },
  filters: {
    // filterStatus(val) {
    //   let str = "";
    //   switch (val) {
    //     case "1":
    //       str = "不通过";
    //       break;
    //     case "0":
    //       str = "通过";
    //       break;
    //     default:
    //       break;
    //   }
    //   return str;
    // },
    //filterapprovalStatusName
    filterapprovalStatusName(val) {
      let str = "";
      switch (val) {
        case "0":
          str = "省级";
          break;
        case "1":
          str = "市级";
          break;
        case "2":
          str = "区级";
          break;
        default:
          break;
      }
      return str;
    },
    //dayjs(item.updateTime).format('MM/DD HH:mm')
    fiterUpdataTime(val) {
      return dayjs(val).format("MM/DD HH:mm");
    },
  },
  computed: {
    isMobile() {
      let flag = navigator.userAgent.match(
        /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i
      );
      return flag;
    },
  },
  mounted() {
   
    var shortCode = this.getQueryVariable("code");
    shortCodeRestore({ shortCode: shortCode }).then((res) => {
      if (res.status == 200) {
        var dataUrl2 = res.data.data.split("?")[1];
        var vars = dataUrl2.split("&");
        for (var i = 0; i < vars.length; i++) {
          var pair = vars[i].split("=");
          if (pair[0] == "approvalId") {
            this.approvalId = pair[1];
          } else {
            this.expertTelephone = pair[1];
          }
        }

        projectApprovalReview({
          approvalId: this.approvalId,
          expertTelephone: this.expertTelephone,
        }).then((res) => {
          if (res.data.status == 200) {
            this.approvalReviewData = res.data.data;
            this.reviewResult=res.data.data.expertApproval || '';
            this.commentAttachments=res.data.data.commentAttachments;
             this.checkMark=res.data.data.expertComment || '';         
            this.feedbackList = res.data.data.approvalDTOs || []; //专家组成员复查意见
            this.infoDTO = res.data.data.infoDTO;
          } else {
             this.isShowTit=res.data.msg
            this.isShow = false;
          }
        });
      }
     
    });
  },
  created() {
    //   document.oncontextmenu=function(evt){
    // evt.preventDefault();
    // }
    // document.onselectstart=function(evt){
    //  evt.preventDefault();
    // };
  },
  methods: {
      //草稿
    clickDraft() {
      var param = {
        approvalId: this.approvalId,
        expertApproval: this.reviewResult,
        expertComment: this.checkMark,
        attachmentOutDTOs: [],
        commentAttachments: this.commentAttachments,
      };
      projectEvaluateDraft(param).then((res) => {       
        if (res.data.status === 200) {              
          this.$message.success(res.data.msg); //?？？？?????有问题
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    async clickPdf(val) {
      this.loading=true;
      if (/(.*)\.(docx)$/i.test(val.name)) {
        this.previewShow = true;
        axios({
          method: "post",
          url: "/gemp-file/api/attachment/download/v1",
          //文件流：gapi/gemp-file/api/attachment/download/v1    res:/gemp-file/api/attachment/findByFileId/v1
          // url:'http://ashuai.work:10000/getDoc',
          data: { fileId: val.attachId },
          responseType: "blob",
        }).then((res) => {
          this.loading=false
          // 对后端返回二进制流做处理
          const blob = new Blob([res.data]);
          docx.renderAsync(blob, this.$refs.word);
          // docx.renderAsync(res, this.$refs.word); // 渲染到页面
        });
      } else if (/(.*)\.(pdf)$/i.test(val.name)) {
        // this.dialogVisible3 = true;
        // this.linkattachId = val.attachId;
        // var axiosRes = await axios({
        //   method: "post",
        //   url: "/gemp-file/api/attachment/findByFileId/v1",
        //   data: { attachId: val.attachId },
        // });
        // this.pdfUrl =
        //   axiosRes.data.data.fileOuterPath + axiosRes.data.data.filePath;
        // this.loading = false;
        // console.log(this.pdfUrl);

        //----------->实例化      
        this.dialogVisible3 = true;
        //实现方式二
        axios({
          method: "post",
          url: "/gemp-file/api/attachment/download/v1",
          //文件流：gapi/gemp-file/api/attachment/download/v1    res:/gemp-file/api/attachment/findByFileId/v1
          // url:'http://ashuai.work:10000/getDoc',
          data: { fileId: val.attachId },
          responseType: "arraybuffer",
        }).then((res) => {
          this.loading=false
          this.pdfh5 = new Pdfh5("#dialogpdfRef", {
            data: res.data,
          });
        });

        // 实现方式一
        // var axiosRes = await axios({
        //   method: "post",
        //   url: "/gemp-file/api/attachment/findByFileId/v1",
        //   data: { attachId: val.attachId },
        // });
        // this.pdfUrl =
        //   axiosRes.data.data.fileOuterPath + axiosRes.data.data.filePath;
        // this.loading = false;
        // console.log(this.pdfUrl);

        // this.pdfh5 = new Pdfh5("#dialogpdfRef", {
        //   pdfurl: '../../../static/pdf/show.pdf',
        // });
      }else if((/(.*)\.(zip)$/i.test(val.name)) || /(.*)\.(rar)$/i.test(val.name)){       
        let par = {
          fileId: val.attachId
        }
        Attachmentdownload(par).then((res) => {         
          downloadFuc(res)
        }) 
      } else {
        this.$message.error("读取文件格式错误");
      }
    },
    closeDialog() {
      this.dialogVisible3 = false;
    },
    handleFole() {
      this.isFole = !Boolean(this.isFole);
    },
    handleCheck(val) {     
      this.reviewResult = val;
    },
    handleClose() {
      this.dialogVisible = false;
    },
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month =
        new Date(timeStamp).getMonth() + 1 < 10
          ? "0" + (new Date(timeStamp).getMonth() + 1)
          : new Date(timeStamp).getMonth() + 1;
      let date =
        new Date(timeStamp).getDate() < 10
          ? "0" + new Date(timeStamp).getDate()
          : new Date(timeStamp).getDate();
      let hh =
        new Date(timeStamp).getHours() < 10
          ? "0" + new Date(timeStamp).getHours()
          : new Date(timeStamp).getHours();
      let mm =
        new Date(timeStamp).getMinutes() < 10
          ? "0" + new Date(timeStamp).getMinutes()
          : new Date(timeStamp).getMinutes();
      let ss =
        new Date(timeStamp).getSeconds() < 10
          ? "0" + new Date(timeStamp).getSeconds()
          : new Date(timeStamp).getSeconds();
      let week = new Date(timeStamp).getDay();
      let weeks = ["日", "一", "二", "三", "四", "五", "六"];
      let getWeek = "星期" + weeks[week];
      return year + month + date;
    },
    //url处理
    getQueryVariable(variable) {
      var query = window.location.search.substring(1);
      var vars = query.split("&");
      for (var i = 0; i < vars.length; i++) {
        var pair = vars[i].split("=");
        if (pair[0] == variable) {
          return pair[1];
        }
      }
      return false;
    },
    handleSign() {    
      var isReviewResult=1;
      var isCheckMark=1
      if (this.reviewResult == "") {
        this.$message.info("请选择结论通过/不通过");
        isReviewResult= 0
      }else(
        isReviewResult= 1
      )
      if (this.checkMark == "") {
        this.$message.info("请填写意见说明");
        isCheckMark=0;
      }else{
        isCheckMark=1
      }
      
      if(isReviewResult==1 && isCheckMark==1){
         //路由方式传参签名
      var nextSubmit={
        infoId:this.infoDTO.infoId,
        expertName:this.approvalReviewData.expertName,
        approvalId:this.approvalId,
        expertApproval: this.reviewResult,
        expertComment: this.checkMark,
        commentAttachments:this.commentAttachments
      }
      // this.dialogVisible = true;  //弹框形式签名
      //路由方式-横屏方式签名 
       this.$router.push({
        path: "/signH5big",
        query:nextSubmit
      });
      }
     
    },
    //签名提交
    handleClose(imgUrl) {
      this.dialogVisible = false;     
      //base64图片附件上传 attachment/uploadBase64/v1
      var paramUploadBase64 = {
        file: imgUrl,
        module: "chemical",
        name: this.timeFormate(new Date()) + this.approvalReviewData.expertName, //当前日期+专家姓名 20220707AB
        path: "/uploadFile/chemical/projectApproval/" + this.infoDTO.infoId,
      };
      axios({
        method: "post",
        url: "/gemp-file/api/attachment/uploadBase64/v1",
        data: paramUploadBase64,
        // responseType: "arraybuffer",
      }).then((res) => {
        if (res && res.status == 200) {
          var attachmentOutDTOs = [res.data.data];
          var param = {
            approvalId: this.approvalId,
            expertApproval: this.reviewResult,
            expertComment: this.checkMark,
            attachmentOutDTOs: attachmentOutDTOs,
          };         
          projectApprovalDecide(param).then((res) => {
            if (res.data.status === 200) {            
              // location.reload();
              this.$router.go(-1)
              this.$message.success(res.data.msg); //?？？？?????有问题
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog {
    height: 100%;
}
/deep/ .el-dialog__body{
   height: 100%;
}
/deep/ .experComment .slot_img, 
/deep/ .experComment .slot_file{
  align-items: flex-start!important;
}
/deep/ .el-upload-list__item{
  margin:0!important;

}
/deep/ .el-upload-list--picture-card, .el-upload-list__item{
  display: block!important;
  margin: 0!important;
}
/deep/ .upload--picture-card{
  justify-content: center!important;
}
/deep/ .el-upload--picture-card{
  justify-content:center!important;
}
.btnStyle {
  display: flex;
  justify-content: center;
  margin-bottom:0.7em
}

.review {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  font-size: 10px;
  .exprertWenj.commentStly{
    padding:8px 0 0 0;
  }
  .tipInfo {
    margin: 0 0 0 0;
    font-size: 17px;
    text-align: center;
    padding: 0 10px 8px 10px;
  }
  /deep/.ant-layout-header {
    padding: 0;
  }
  .header {
    background: url(/static/img/assets/img/top-bg.png) no-repeat center center;
    background-size: 100% 100%;
    width: 100%;
    // height: 68px;
    // line-height: 68px;
    display: flex;
    justify-content: space-between;
    .mainPoint {
      font-size: 22px;
      color: white;
      margin-left: 20px;
      width: 40vw;
      height: 68px;
      .accAnalyse {
        // display: inline-block;
        background: url(/static/img/assets/img/nationalEmblem.png) no-repeat
          center center;
        background-size: 100% 100%;
        width: 41px;
        height: 41px;
        position: relative;
        top: 10px;
        float: left;
        margin-right: 10px;
      }
    }
  }
  &-main {
    flex: 1;
    &-title {
      width: 100%;
      color: #252525;
    }
    .content {
      &-list {
        &-item {
          .title {
          }
          .detail {
            table {
              th {
                background: #f7f8fa;
              }
            }
          }
        }
      }
      &-btn {
      }
    }
  }
  &.h5 {
    font-size: 15px;
    color: #252525;
    li {
      list-style: none;
    }
    i {
      display: inline-block;
      width: 1em;
      height: 1em;
      margin-right: 0.5em;
    }
    .review-main {
      background: #f4f7f8;
      &-title {
        height: 3em;
        line-height: 3em;
        margin-bottom: 0.5em;
        padding-left: 0.7em;
        background: #fff;
        font-size: 1.4em;
      }
      .content {
        background: #f4f7f8;
        &-list {
          &-item {
            margin-bottom: 0.5em;
            background: #fff;
            .title {
              display: flex;
              align-items: center;
              justify-content: space-between;
              height: 3em;
              line-height: 3em;
              padding-left: 1em;
              border-bottom: 0.05em solid #c9c9c9;
              i {
                &.baseinfo {
                  background: url(/static/img/assets/img/review/ico_info.png)
                    no-repeat center center / contain;
                }
                &.feedback {
                  background: url(/static/img/assets/img/review/ico_feedback.png)
                    no-repeat center center / contain;
                }
                &.idea {
                  background: url(/static/img/assets/img/review/ico_idea.png)
                    no-repeat center center / contain;
                }
              }
              .feedbackNum {
                display: flex;
                align-items: center;
                color: #707070;
                > span {
                  margin-right: 0.5em;
                }
              }
            }
            .detail {
              padding: 0 0.7em;
              > ul.list {
                padding: 0 0.7em;
                li {
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                  padding: 1em 0;
                  border-bottom: 0.05em solid #c9c9c9;
                  .label {
                    color: #707070;
                  }
                  &.file {
                    flex-direction: column;
                    align-items: flex-start;
                    .file-con {
                      width: 100%;
                      // display: flex;
                      // justify-content: space-between;
                      // align-items: center;
                      // padding-top: 0.8em;
                      // flex-wrap: wrap;
                      .file-name {
                        padding-top: 0.8em;
                        i {
                          margin-right: 0.2em;
                          background: url(/static/img/assets/img/review/ico_link.png)
                            no-repeat center center / contain;
                        }
                        span {
                          color: #3470ff;
                        }
                      }
                      .file-size {
                        color: #707070;
                      }
                    }
                  }
                  &:last-child {
                    border-bottom: none;
                  }
                }
              }
              /deep/.el-timeline {
                margin-top: 2em;
                .el-timeline-item {
                  padding-bottom: 0.5em;
                  .el-timeline-item__wrapper {
                    top: -1em;
                  }
                }
              }
              /deep/.el-radio-group {
                margin-bottom: 0;
                .el-radio__label {
                  font-size: 1em;
                  color: #252525;
                }
              }
              /deep/.el-textarea {
                textarea {
                  padding: 0;
                  resize: none;
                  border: none;
                }
              }
              .feedCon {
                padding: 0.5em;
                border-radius: 0.4em;
                background: #f8f9fb;
                .info {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  margin-bottom: 0.5em;
                  &-left {
                    display: flex;
                    align-items: center;
                    .line {
                      width: 0.05em;
                      height: 1.2em;
                      background: #707070;
                      display: inline-block;
                      margin: 0 0.5em;
                    }
                    .status {
                      margin-left: 1em;
                      margin-left: 1em;
                      padding: 0 0.5em;
                      border-radius: 0.3em;
                      background: #fef7e7;
                      color: #faad14;
                      i {
                        margin-right: 0.1em;
                      }
                      &.status_0 {
                        background: #d7eece;
                        color: #52c41a;
                      }
                      &.status_1 {
                        background: #edcecf;
                        color: #bf241f;
                      }
                    }
                  }
                  &-date {
                    color: #707070;
                  }
                }
                .mark {
                  color: #707070;
                }
              }
            }
          }
        }
        &-btn {
          background: #3470ff;
          margin-top: -1em;
          height: 3em;
          line-height: 3em;
          text-align: center;
          color: #fff;
          font-weight: 500;
        }
      }
    }
  }
  &.pc {
    .review-main {
      background: url(/static/img/assets/img/review/bg.png) no-repeat center top,
        #fafafa;
      background-size: contain;
      &-title {
        text-align: center;
        background: unset;
      }
    }
    .content {
      background: #fff;
    }
  }
}
.tipMore {
  text-align: center;
  height: calc(100vh - 3.5em);
  line-height: calc(100vh - 3.5em);
  font-size: 24px;
}
</style>