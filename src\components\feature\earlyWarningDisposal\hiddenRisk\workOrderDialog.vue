<template>
  <div>
    <el-dialog
      title="湖北省应急厅风险监测预警中心处置工单"
      :visible.sync="show"
      width="1300px"
      @close="closeBoolean()"
    >
      <div class="container">
        <div class="box">
          <el-form
            label-width="130px"
            class="form-box"
            :model="workOrderInfo"
            size="mini"
          >
            <el-row>
              <el-col :span="12">
                <el-form-item label="工单编号">
                  <el-input v-model="workOrderInfo.id" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="风险事件等级">
                  <el-input
                    v-model="workOrderInfo.eventLevel"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="风险事件详情">
                  <el-input
                    v-model="workOrderInfo.eventDescription"
                    type="textarea"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="风险事件研判">
                  <el-input
                    v-model="workOrderInfo.riskDecide"
                    type="textarea"
                    :rows="8"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="交办单位">
                  <el-input
                    v-model="workOrderInfo.assignmentUnitName"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="交办时间">
                  <el-input
                    v-model="workOrderInfo.assignmentTime"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件工单反馈期限">
                  <el-input
                    v-model="workOrderInfo.feedbackTime"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件处置单位">
                  <el-input
                    v-model="workOrderInfo.disposeUnitName"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="处置责任人">
                  <el-input
                    v-model="workOrderInfo.eventDisposeUser"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件处置期限">
                  <el-input
                    v-model="workOrderInfo.eventDisposeDeadline"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="事件处置结果">
                  <el-input
                    v-model="workOrderInfo.eventDisposeResult"
                    disabled
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { riskWarningDetail } from "@/api/earlyWarningDisposal";

export default {
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    entObj: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      workOrderInfo: {},
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    closeBoolean(val) {
      this.$emit("closeBoolean", val);
    },
    // 调用详情接口
    getData() {
      riskWarningDetail(this.entObj.id).then((res) => {
        if (res.data.status == 200) {
          this.workOrderInfo = res.data.data;
          console.log("工单详情=================", this.workOrderInfo);
          //根据eventLevel数字返回对应名称的函数
          this.workOrderInfo.eventLevel = this.getEventLevelName(
            this.workOrderInfo.eventLevel
          );
        }
      });
    },
    getEventLevelName(level) {
      switch (level) {
        case '0':
          return "不分级";
        case '1':
          return "I级";
        case '2':
          return "II级";
        case '3':
          return "III级";
        case '4':
          return "IV级";
        default:
          return "未知";
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  height: 100%;
  width: 100%;
}
.box {
  width: 100%;
  height: 100%;
}
</style>
