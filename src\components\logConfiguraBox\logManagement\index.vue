<template>
  <!-- <div class="logManagement">
    <div class="right" :style="width" ><Container></Container></div>
  </div> -->
  <div>
    <logInfo></logInfo>  
  </div>
</template>

<script>
// import Container from "./Container";
import logInfo from "./logInfo";
export default {
  //import引入的组件
  components: {
    // Container,
    logInfo
  },
  data() {
    return {
      width: "width:100%",
    };
  },
  //方法集合
  methods: {
    // parent(){
		// 		this.$refs.goodslist.getMenuList(this.$store.state.sa.SAMenuListData.systemCode)
		// 	}
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.menuManagement {
  display: flex;
  justify-content: flex-start;
  .side {
    margin-right: 15px;
  }

  .right {
    height: 89vh;
    background-color: #fff;
      overflow: auto;
  }
}
</style>