<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <!-- <meta name="referrer" content="no-referrer" /> -->

    <meta name="referrer" content="strict-origin-when-cross-origin" />
    <title>武汉市危险化学品安全生产综合业务系统</title>
    <link rel="stylesheet" href="/static/css/loaders.css" />
    <!-- <link rel="stylesheet" href="/static/WSPlayer/player.css">
  <script src="/static/WSPlayer/PlayerControl.js"></script> -->
    <link rel="stylesheet" href="/static/WSPlayer/player.css" />
    <script src="/static/WSPlayer/PlayerControl.js"></script>
    <script src="/static/WSPlayer/libdhplay.js"></script>

    <script src="/static/flv.min.js"></script>
    <!-- libdhplay -->
    <script src="/static/pdf/fabric.js"></script>
    <script src="/static/pdf/Sortable.min.js"></script>
    <script src="/static/pdf/pdf.js"></script>
    <script src="/static/pdf/pdf.worker.js"></script>

    <script src="/static/pdf/pdfh5/jquery-2.1.1.min.js"></script>
    <script src="/static/pdf/pdfh5/pdfh5.js"></script>
    <link rel="stylesheet" href="/static/pdf/pdfh5/pdfh5.css" />

    <link
      rel="stylesheet"
      type="text/css"
      href="/static/mars3d/Cesium/Widgets/widgets.css"
    />
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/mars3d/mars3d/mars3d.css"
    />
    <script type="text/javascript" src="/static/mars3d/turf.min.js"></script>
    <script
      type="text/javascript"
      src="/static/mars3d/Cesium/Cesium.js"
    ></script>
    <script
      type="text/javascript"
      src="/static/mars3d/mars3d/mars3d.js"
    ></script>
    <link
      rel="stylesheet"
      type="text/css"
      href="/static/mars3d/divGraphic.css"
    />

    <script src="https://webapi.amap.com/maps?v=1.3&key=cb67a1a636532950c27804c9ce4b6351&plugin=AMap.DistrictSearch"></script>
    <script src="https://webapi.amap.com/ui/1.0/main.js"></script>

    <!-- 地图插件用到的 -->
    <!-- <link href="http://************:9699/Examples/Build/Cesium/Widgets/widgets.css" rel="stylesheet"> -->

    <style>
      /* 侧导航hover效果底色 */
      .ant-menu-dark .ant-menu-sub {
        background: #313a59 !important;
      }

      .ant-menu-dark .ant-menu-submenu-selected {
        background: #424d70;
      }

      .ant-menu-inline .ant-menu-submenu {
        border-bottom: 1px #313a59 solid;
      }

      html body {
        margin: 0;
        padding: 0;
      }

      #loaderCss {
        margin: 0;
        padding: 0;
        position: fixed;
        width: 100%;
        top: 50%;
        /*偏移*/
        transform: translateY(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .loader-inner div {
        background-color: #409eff;
      }
      .el-tooltip__popper,
      .el-select-dropdown {
        max-width: 330px !important;
      }
    </style>
    <!-- <script src="http://************:9699/Examples/Build/Cesium/Cesium.js"></script> -->
  </head>

  <body>
    <div id="app">
      <div id="loaderCss">
        <!-- 首屏加载动画 -->
        <div class="loader-inner line-scale">
          <div></div>
          <div></div>
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
    <!-- built files will be auto injected -->
    <!-- 视频插件用到的 -->
    <!--  <script src="/static/jquery-1.12.4.min.js"></script>-->
    <script src="/static/jsencrypt.min.js"></script>
    <script src="/static/jsWebControl-1.0.0.min.js"></script>
    <script src="/static/h5player.min.js"></script>
    <!-- <script
      type="text/javascript"
      src="https://cdn.bootcss.com/vConsole/3.3.0/vconsole.min.js"
    ></script>
    <script>
      // 初始化
      var vConsole = new VConsole();
      console.log("Hello world");
    </script> -->
  </body>
</html>
