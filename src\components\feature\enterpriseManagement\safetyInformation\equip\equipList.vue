<template>
  <div class="safetyOrgList">
    <div class="header">
      <div class="title">特种设备作业人员</div>
      <div class="operation">
        <div class="inputBox">
          <el-input
            v-model.trim="params.personName"
            size="small"
            placeholder="请输入姓名"
            class="input"
            clearable
          ></el-input>
          <el-select
            v-model="params.specialEquipmentOperationPositions"
            size="small"
            placeholder="请选择特种设备作业岗位"
            :clearable="true"
          >
            <el-option
              v-for="item in isFullTimeData"
              :key="item"
              :label="item"
              :value="item"
            >
            </el-option>
          </el-select>
          <el-input
            v-model.trim="params.phone"
            size="small"
            placeholder="请输入手机号码"
            class="input"
            clearable
          ></el-input>
          <template v-if="isOpen">
            <span>
              <label>发证日期</label>
              <el-date-picker
                v-model="issuanceTimeRange"
                size="small"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="发证开始日期"
                end-placeholder="发证结束日期"
                @change="searchTime"
                unlink-panels
                style="width: 370px"
                :picker-options="pickerOptions"
              ></el-date-picker>
            </span>
            <span
              ><label>证书有效期</label>
              <el-date-picker
                v-model="validityPeriodRange"
                size="small"
                type="daterange"
                value-format="yyyy-MM-dd"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                unlink-panels
                style="width: 370px"
              ></el-date-picker> </span
          ></template>
          <el-button type="primary" size="small" @click="handleSearch()"
            >查询</el-button
          >
          <el-button type="primary" size="small" pain @click="handleReset"
            >重置</el-button
          >
          <el-button type="text" size="small" @click="isOpen = !isOpen"
            >{{ isOpen ? "收起" : "展开"
            }}<i :class="isOpen ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i
          ></el-button>
        </div>
      </div>
    </div>

    <el-table
      class="table"
      v-loading="loading"
      :data="tableData"
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      border
      height="100%"
      style="width: 100%"
    >
      <el-table-column type="index" label="序号" width="55" align="center">
      </el-table-column>
      <el-table-column
        prop="personName"
        label="姓名"
        width="100"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="specialEquipmentOperationPositions"
        label="特种设备作业岗位"
        width="160"
        align="center"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="operateCertificateName"
        label="操作证名称"
        align="center"
        width="140"
        :show-overflow-tooltip="true"
      >
      </el-table-column>
      <el-table-column
        prop="fullTimePersonnel"
        label="手机号码"
        align="center"
        width="120"
      >
      </el-table-column>
      <el-table-column prop="certificateType" label="证书名称" align="center">
      </el-table-column>
      <el-table-column prop="issuanceTimeStr" label="发证时间" align="center">
      </el-table-column>
      <el-table-column
        prop="validityPeriodStr"
        label="证书有效期"
        align="center"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        align="center"
        width="140"
      >
      </el-table-column>

      <el-table-column
        prop="address"
        label="操作"
        align="center"
        width="100"
        fixed="right"
      >
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        :current-page.sync="params.nowPage"
        :page-size="params.pageSize"
        layout="total, prev, pager, next"
        background
        :total="total"
      >
      </el-pagination>
    </div>
    <equipDialog
      v-if="detailVisible"
      :show="detailVisible"
      :entObj="safetyOrgInfo"
      @closeBoolean="closeBoolean"
      @getData="getList"
    ></equipDialog>
  </div>
</template>

<script>
import { getSafetOrgList, getSafetyInformation } from "@/api/safetyInfomation";
import equipDialog from "./equipDialog";
export default {
  //import引入的组件
  name: "rescueTeam",
  components: { equipDialog },
  props: {
    companyCode: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      pickerOptions: {
        disabledDate: (time) => {
          return (
            time.getTime() >
            new Date(new Date().Format("yyyy-MM-dd 23:59:59")).getTime()
          );
        },
      },
      isOpen: false,
      tableData: [],
      loading: false,
      total: 0,
      params: {
        nowPage: 1,
        pageSize: 10,
        personType: "equip",
        personName: "",
        phone: "",
        isFullTime: null,
        companyCode: this.companyCode,
        specialOperationPositions: "",
        specialEquipmentOperationPositions: "",
        issuanceTimeEndTime: null,
        issuanceTimeStartTime: null,
        validityPeriodEndTime: null,
        validityPeriodStartTime: null,
      },
      issuanceTimeRange: null,
      validityPeriodRange: null,
      isFullTimeData: [],
      detailVisible: false,
      safetyOrgInfo: {},
    };
  },
  created() {},
  //方法集合
  methods: {
    searchTime() {},
    handleSearch() {
      this.params.nowPage = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.params.nowPage = val;
      this.getList();
    },
    handleReset() {
      this.params = {
        nowPage: 1,
        pageSize: 10,
        personType: "equip",
        personName: "",
        phone: "",
        isFullTime: null,
        companyCode: null,
        specialOperationPositions: "",
        specialEquipmentOperationPositions: "",
        issuanceTimeEndTime: null,
        issuanceTimeStartTime: null,
        validityPeriodEndTime: null,
        validityPeriodStartTime: null,
      };
      this.issuanceTimeRange = null;
      this.validityPeriodRange = null;
      this.getList();
    },
    handleView(row) {
      this.safetyOrgInfo = row;
      this.detailVisible = true;
    },
    closeBoolean() {
      this.detailVisible = false;
      this.safetyOrgInfo = {};
    },
    //获取列表
    getList() {
      const params = this.params;
      if (this.issuanceTimeRange) {
        params.issuanceTimeStartTime = this.issuanceTimeRange[0] + " 00:00:00";
        params.issuanceTimeEndTime = this.issuanceTimeRange[1] + " 23:59:59";
      } else {
        params.issuanceTimeStartTime = null;
        params.issuanceTimeEndTime = null;
      }
      if (this.validityPeriodRange) {
        params.validityPeriodStartTime =
          this.validityPeriodRange[0] + " 00:00:00";
        params.validityPeriodEndTime =
          this.validityPeriodRange[1] + " 23:59:59";
      } else {
        params.validityPeriodStartTime = null;
        params.validityPeriodEndTime = null;
      }
      this.loading = true;
      getSafetOrgList(params)
        .then((res) => {
          if (res.data.status == 200) {
            this.tableData = res.data.data.list;
            this.total = Number(res.data.data.total);
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getDict() {
      getSafetyInformation("equip").then((res) => {
        if (res.status == 200) {
          this.isFullTimeData = res.data;
        }
      });
    },
  },
  mounted() {
    this.getDict();
    this.getList();
  },
};
</script>
<style lang="scss" scoped>
.safetyOrgList {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  .header {
    .title {
      // margin-left: 20px;
      font-size: 18px;
      font-family: Microsoft YaHei;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
    }
    .operation {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      .inputBox {
        flex: 1;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-wrap: wrap;
        .input {
          width: 200px;
        }
        > * {
          margin-right: 15px;
          margin-bottom: 15px;
        }
        label {
          margin-right: 3px;
        }
      }
    }
  }
  .table {
    width: 100%;
    // padding-left: 20px;
  }
  .pagination {
    margin-top: 10px;
    display: flex;
    justify-content: flex-end;
  }
  .dialog-footer {
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
  }
  .form_item {
    .form_title {
      font-size: 20px;
      color: #2f85de;
      margin-left: 20px;
    }
  }
}
</style>
