.marsBlackPanel {
    min-width: 90px;
    min-height: 35px;
    position: absolute;
    left: 16px;
    bottom: 31px;
    cursor: default;
    border-radius: 4px;
    opacity: 0.96;
    border: 1px solid #14171c;
    box-shadow: 0px 2px 21px 0px rgba(33, 34, 39, 0.55);
    border-radius: 4px;
    box-sizing: border-box;
    background: linear-gradient(0deg, #1e202a 0%, #0d1013 100%);
}

.marsBlackPanel::before {
    content: "";
    width: calc(100% + 22px);
    height: 39px;
    position: absolute;
    bottom: -39px;
    left: -22px;
    background: url("../../img/icon/popupLbl.png") 0px 0px no-repeat;
    background-position: 0px 0px;
}

.marsBlackPanel-text {
    width: 100%;
    height: 100%;
    min-height: 33px;
    text-align: center;
    padding: 10px 5px 0 5px;
    margin: 0;
    font-size: 14px;
    font-weight: 400;
    color: #ffffff;
    border: 1px solid #ffffff4f;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    white-space: nowrap;
}

.marsBlackPanel-highlight {
    border: 2px solid yellow;
}

/**************** 涓€涓笎鍙樼殑鏂囨湰闈㈡澘,涓棿绔栫洿杩炵嚎 ****************/
.marsBlueGradientPnl {
    text-align: center;
    padding: 5px 30px;
    margin: 0;
    color: #fff;
    background: linear-gradient(rgb(7 10 203 / 75%), rgb(16 238 220));
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
    max-height: 130px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    white-space: nowrap;
}

.marsBlueGradientPnl:after {
    content: "";
    position: absolute;
    bottom: -60px;
    left: calc(50% - 3px);
    display: block;
    width: 3px;
    height: 60px;
    border-right: 3px solid #2bcdbb;
}

.marsBlueGradientPnl-highlight {
    border: 2px solid yellow;
}

.marsBlueGradientPnl-highlight:after {
    border-right: 3px solid yellow;
}

/**************** 钃濊壊绉戞妧鎰熸枃鏈潰鏉� ****************/
.mars-spot {
    background-image: url("../../img/icon/lbl-circle.png");
    position: absolute;
    background-repeat: no-repeat;
    user-select: none;
    z-index: 100000;
    width: 70px;
    height: 44px;
    background-size: cover;
    bottom: -80px;
    left: -30px;
    cursor: default;
}

.mars-spot:hover {
    background-image: url("../../img/icon/lbl-circle2.png");
}

.mars-spot:hover .mars-spot-board {
    background-image: url("../../img/icon/lbl-extent2.png");
}

.mars-spot:hover .mars-spot-line {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot:hover .mars-spot-line:before {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot:hover .mars-spot-line:after {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-board {
    background-image: url("../../img/icon/lbl-extent.png");
    top: -165px;
    transform: translateX(-30%);
    width: 180px;
    height: 52px;
    padding: 15px 0;
    background-position: 50%;
    background-size: contain;
    cursor: pointer;
    position: absolute;
    background-repeat: no-repeat;
}

.mars-spot-board:hover {
    background-image: url("../../img/icon/lbl-extent2.png");

}

.mars-spot-board:hover+.mars-spot-line {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-board:hover+.mars-spot-line:before {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-board:hover+.mars-spot-line:after {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-line {
    position: absolute;
    top: -108px;
    left: 35px;
    width: 2px;
    height: 140px;
    background: linear-gradient(180deg, #38c9ff, transparent);
}

.mars-spot-line:hover {
    background: linear-gradient(180deg, #ff6b04, transparent);
}

.mars-spot-line:before {
    height: 40%;
    left: 6px;
    animation: rise 3s linear 2s infinite;
    -webkit-animation: rise 3s linear 2s infinite;
    content: "";
    display: block;
    width: 1px;
    background: linear-gradient(180deg, #38c9ff, transparent);
    position: absolute;
    cursor: pointer;
}

.mars-spot-line:after {
    content: "";
    display: block;
    width: 1px;
    background: linear-gradient(180deg, #38c9ff, transparent);
    position: absolute;
    cursor: pointer;
    height: 60%;
    left: -5px;
    animation: rise 3s linear infinite;
    -webkit-animation: rise 3s linear infinite;
}

.mars-spot-board h5 {
    width: 100%;
    text-align: center;
    line-height: 24px;
    color: #beedff;
    font-size: 16px;
    margin: 0;
}

/**************** 涓€涓畝娲佹枃鏈潰鏉�,涓棿绔栫洿杩炵嚎 ****************/
.marsGreenGradientPnl {
    width: 100px;
    text-align: center;
    background-image: linear-gradient(to right, #565d39, #00ffc3);
    position: absolute;
    left: -50px;
    bottom: 30px;
    cursor: default;
    padding: 5px;
    border: 1px solid #9c9944e8;
}

.marsGreenGradientPnl:hover {
    border: 1px solid rgb(9, 255, 0);
}

.marsGreenGradientPnl::before {
    position: absolute;
    content: "";
    left: 50%;
    bottom: -30px;
    height: 30px;
    border-left: 2px dashed #c5e22770;
}

.marsGreenGradientPnl-highlight {
    border: 2px solid yellow;
}

.marsGreenGradientPnl-highlight::before {
    border-left: 2px dashed yellow !important;
}

/********** 闈㈡澘锛氬€炬枩鎸囧悜宸︿笅瑙掔殑div1.png鍥剧墖闈㈡澘鏍峰紡 ***********/

.marsImgPanel1 {
    position: relative;
    width: 200px;
    height: 134px;
    background: rgba(42, 42, 42, 0.8);
    border-radius: 4px;

    background: url("../../img/icon/div1.png");
    background-size: 100%;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;

    -webkit-animation: mymove 5s infinite;
    animation: mymove 5s infinite;
    animation-duration: 5s;
    animation-timing-function: ease;
    animation-delay: 0s;
    animation-iteration-count: infinite;
    animation-direction: normal;
    animation-fill-mode: none;
    animation-play-state: running;
    animation-name: mymove;
}

.marsImgPanel1 .title {
    position: relative;
    top: 20px;
    left: 70px;
    font-size: 15px;
    text-align: left;
    color: rgba(255, 255, 255, 1);
    text-shadow: 0px 0px 9px rgba(0, 0, 0, 0.75);
}

/********** 闈㈡澘锛氬€炬枩鎸囧悜宸︿笅瑙掔殑div2.png鍥剧墖闈㈡澘鏍峰紡 ***********/
.marsImgPanel2 {
    position: relative;
    width: 200px;
    height: 157px;
    background: rgba(42, 42, 42, 0.8);
    border-radius: 4px;
    background: url("../../img/icon/div2.png");
    background-size: 100%;
    -webkit-background-size: cover;
    -moz-background-size: cover;
    -o-background-size: cover;

    -webkit-animation: mymove 5s infinite;
    animation: mymove 5s infinite;
    animation-duration: 5s;
    animation-timing-function: ease;
    animation-delay: 0s;
    animation-iteration-count: infinite;
    animation-direction: normal;
    animation-fill-mode: none;
    animation-play-state: running;
    animation-name: mymove;
}

.marsImgPanel2 .title {
    position: inherit;
    top: 22px;
    left: 70px;
    font-size: 14px;
    text-align: left;

    color: rgba(255, 255, 255, 1);
    text-shadow: 0px 0px 9px rgba(0, 0, 0, 0.75);
}

.marsImgPanel2 .content {
    position: inherit;
    font-size: 14px;
    top: 30px;
    left: 50px;
    width: 140px;
    height: auto;
    text-align: left;
    color: rgba(255, 255, 255, 1);
    overflow: hidden;
}

/********** 闈㈡澘锛氬€炬枩鎸囧悜宸︿笅瑙掔殑闈㈡澘鏍峰紡 ***********/

.marsTiltPanel-wrap {
    position: relative;
    padding: 30px;
    overflow: hidden;
}

.marsTiltPanel .area {
    position: relative;
    min-width: 180px;
}

.marsTiltPanel .b-t {
    position: absolute;
    top: 0;
    left: 44px;
    right: 0;
    height: 1px;
    z-index: 10;
}

.marsTiltPanel .b-r {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 44px;
    width: 1px;
    z-index: 10;
}

.marsTiltPanel .b-b {
    position: absolute;
    left: 0;
    right: 44px;
    bottom: 0;
    height: 1px;
    z-index: 10;
}

.marsTiltPanel .b-l {
    position: absolute;
    top: 44px;
    left: 0;
    bottom: 0;
    width: 1px;
    z-index: 10;
}

.marsTiltPanel .b-t-l {
    position: absolute;
    top: 0;
    left: 0;
    width: 1px;
    height: 62px;
    transform: rotate(45deg) translate(52px, -22px);
    z-index: 10;
}

.marsTiltPanel .b-b-r {
    position: absolute;
    bottom: 0;
    right: 0;
    width: 1px;
    height: 62px;
    transform: rotate(45deg) translate(-52px, 22px);
    z-index: 10;
}

.marsTiltPanel .label-wrap {
    padding-left: 12px;
    color: #fff;
    font-size: 16px;
    white-space: nowrap;
    overflow: hidden;
}

.marsTiltPanel .title {
    margin-top: 20px;
    padding: 0 12px 0 30px;
    height: 36px;
    line-height: 36px;
    position: relative;
}

.marsTiltPanel .title::before {
    content: "";
    position: absolute;
    bottom: -4px;
    left: 0;
    right: 0;
    z-index: 10;
    height: 2px;
}

.marsTiltPanel .label-content {
    padding: 15px 0;
}

.marsTiltPanel .data-li {
    padding: 4px 45px 4px 0;
}

.marsTiltPanel .data-label,
.data-value {
    display: inline-block;
}

.marsTiltPanel .data-value {
    font-size: 14px;
}

.marsTiltPanel .label-num {
    margin-right: 3px;
    color: #f09e28;
    font-weight: 600;
}

.marsTiltPanel .label-tag {
    display: inline-block;
    position: relative;
    margin-right: 6px;
    padding: 0 6px;
    font-weight: 600;
    cursor: pointer;
    background-color: #909399;
    border-radius: 4px;
}

.marsTiltPanel .label-tag::after {
    content: attr(alt);
    display: inline-block;
    position: absolute;
    bottom: -22px;
    right: -35px;
    z-index: -1;
    padding: 2px 4px;
    color: #fff;
    font-size: 14px;
    background-color: #333;
    border-radius: 3px;
    opacity: 0;
    transition: all 0.3s ease-in;
}

.marsTiltPanel .label-tag:hover::after {
    opacity: 1;
    z-index: 11;
}

.marsTiltPanel .data-value-status-0 {
    background-color: #f0285c;
}

.marsTiltPanel .data-value-status-1 {
    background-color: #35b15b;
}

.marsTiltPanel .data-value-status-2 {
    background-color: #f09e28;
}

.marsTiltPanel .arrow {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 45px;
    height: 2px;
    transform: rotate(-45deg) translate(5px, -15px);
}

/* 钃濊壊涓婚 */

.marsTiltPanel-theme-blue .b-t,
.marsTiltPanel-theme-blue .b-r,
.marsTiltPanel-theme-blue .b-b,
.marsTiltPanel-theme-blue .b-l,
.marsTiltPanel-theme-blue .b-t-l,
.marsTiltPanel-theme-blue .b-b-r {
    background-color: #29baf1;
    box-shadow: 0 0 10px 2px #29baf1;
}

.marsTiltPanel-theme-blue .area {
    background-image: linear-gradient(135deg, transparent 30px, #28bbf06c 30px, #28bbf06c 50%, transparent 50%),
        linear-gradient(-45deg, transparent 30px, #28bbf06c 30px, #28bbf06c 50.1%, transparent 50%);
}

.marsTiltPanel-theme-blue .title {
    background-image: linear-gradient(135deg, transparent 25px, #29baf1 25px);
}

.marsTiltPanel-theme-blue .arrow,
.marsTiltPanel-theme-blue .title::before {
    background-color: #28bbf0;
}

/* 缁胯壊涓婚 */

.marsTiltPanel-theme-green .b-t,
.marsTiltPanel-theme-green .b-r,
.marsTiltPanel-theme-green .b-b,
.marsTiltPanel-theme-green .b-l,
.marsTiltPanel-theme-green .b-t-l,
.marsTiltPanel-theme-green .b-b-r {
    background-color: #06e34a;
    box-shadow: 0 0 10px 2px #06e34a;
}

.marsTiltPanel-theme-green .area {
    background-image: linear-gradient(135deg, transparent 30px, #06e3486c 30px, #06e3486c 50%, transparent 50%),
        linear-gradient(-45deg, transparent 30px, #06e3486c 30px, #06e3486c 50.01%, transparent 50%);
}

.marsTiltPanel-theme-green .title {
    background-image: linear-gradient(135deg, transparent 25px, #06e34a 25px);
}

.marsTiltPanel-theme-green .arrow,
.marsTiltPanel-theme-green .title::before {
    background-color: #06e34a;
}

/* 绾㈣壊涓婚 */
.marsTiltPanel-theme-red .b-t,
.marsTiltPanel-theme-red .b-r,
.marsTiltPanel-theme-red .b-b,
.marsTiltPanel-theme-red .b-l,
.marsTiltPanel-theme-red .b-t-l,
.marsTiltPanel-theme-red .b-b-r {
    background-color: #e3064f;
    box-shadow: 0 0 10px 2px #e3064f;
}

.marsTiltPanel-theme-red .area {
    background-image: linear-gradient(135deg, transparent 30px, #e306506c 30px, #e306506c 50%, transparent 50%),
        linear-gradient(-45deg, transparent 30px, #e306506c 30px, #e306506c 50%, transparent 50%);
}

.marsTiltPanel-theme-red .title {
    background-image: linear-gradient(135deg, transparent 25px, #e3064f 25px);
}

.marsTiltPanel-theme-red .arrow,
.marsTiltPanel-theme-red .title::before {
    background-color: #e3064f;
}

/* 榛勮壊涓婚 */

.marsTiltPanel-theme-e9b709 .b-t,
.marsTiltPanel-theme-e9b709 .b-r,
.marsTiltPanel-theme-e9b709 .b-b,
.marsTiltPanel-theme-e9b709 .b-l,
.marsTiltPanel-theme-e9b709 .b-t-l,
.marsTiltPanel-theme-e9b709 .b-b-r {
    background-color: #e9b709;
    box-shadow: 0 0 10px 2px #e9b709;
}

.marsTiltPanel-theme-e9b709 .area {
    background-image: linear-gradient(135deg, transparent 30px, #e9b9096c 30px, #e9b9096c 50%, transparent 50%),
        linear-gradient(-45deg, transparent 30px, #e9b9096c 30px, #e9b9096c 50%, transparent 50%);
}

.marsTiltPanel-theme-e9b709 .title {
    background-image: linear-gradient(135deg, transparent 25px, #e9b709 25px);
}

.marsTiltPanel-theme-e9b709 .arrow,
.marsTiltPanel-theme-e9b709 .title::before {
    background-color: #e9b709;
}

/**************** 閮ㄥ垎鍏叡鏍峰紡锛屽閫変腑鏍峰紡銆佸姩鐢荤瓑 ****************/

.animation-spaceInDown {
    animation-duration: 1s;
    animation-fill-mode: both;
    animation-name: spaceInDown;
}

@keyframes spaceInDown {
    0% {
        opacity: 0;
        transform-origin: 0% 100%;
        transform: scale(0.2) translate(0, 200%);
    }

    100% {
        opacity: 1;
        transform-origin: 0% 100%;
        transform: scale(1) translate(0, 0);
    }
}

@keyframes rise {
    0% {
        opacity: 0.1;
        transform: translate(0, 100%);
    }

    5% {
        opacity: 0.3;
        transform: translate(0, 95%);
    }

    10% {
        opacity: 0.6;
        transform: translate(0, 90%);
    }

    15% {
        opacity: 1;
        transform: translate(0, 85%);
    }

    75% {
        opacity: 1;
        transform: translate(0, 25%);
    }

    80% {
        opacity: 0.7;
        transform: translate(0, 20%);
    }

    90% {
        opacity: 0.3;
        transform: translate(0, 10%);
    }

    95% {
        opacity: 0.2;
        transform: translate(0, 5%);
    }

    100% {
        opacity: 0.1;
        transform: translate(0, 0);
    }
}