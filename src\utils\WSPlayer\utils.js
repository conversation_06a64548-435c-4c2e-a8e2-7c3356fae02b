const userAgentKey = {
    Opera: "Opera",
    Chrome: "Chrome",
    Firefox: "Firefox",
    Edge: "Edge",
    IE: "IE",
    Safari: "Safari",
}

function getBrowserType() {
    const {userAgent} = navigator;
    // 判断是否为Edge浏览器
    if(userAgent.includes("Edge")) {
        return userAgentKey.Edge;
    }
    // 判断是否为Firefox浏览器
    if(userAgent.includes("Firefox")) {
        return userAgentKey.Firefox;
    }
    // 判断是否为Chrome浏览器
    if(userAgent.includes("Chrome")) {
        return userAgentKey.Chrome;
    }
    // 判断是否为Safari浏览器
    if(userAgent.includes("Safari")) {
        return userAgentKey.Safari;
    }
    // 判断是否为IE浏览器
    if(userAgent.includes("compatible")
        && userAgent.includes("MSIE")
        && userAgent.includes("Opera")
    ) {
        return userAgentKey.IE;
    }
    // 判断是否为Opera浏览器
    if(userAgent.includes("Opera")) {
        return userAgentKey.Opera;
    }
    return "";
}

function getBrowserVersion(browserType) {
    const {userAgent} = navigator;
    return userAgent.split(browserType)[1].split(".")[0].slice(1)
}

function checkBrowser() {
    const browserType = getBrowserType();
    const browserVersion = getBrowserVersion(browserType);
    let isVersionCompliance = false;
    let errorCode = 0;
    switch(browserType) {
        case userAgentKey.Chrome:
            isVersionCompliance = browserVersion >= 91;
            errorCode = 701;
            break;
        case userAgentKey.Firefox:
            isVersionCompliance = browserVersion >= 97;
            errorCode = 702;
            break;
        case userAgentKey.Edge:
            isVersionCompliance = browserVersion >= 91;
            errorCode = 703;
            break;
        default:
            isVersionCompliance = 0;
    }
    return {isVersionCompliance, browserType, errorCode};
}

export default {
    checkBrowser
}
