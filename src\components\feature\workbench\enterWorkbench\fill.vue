<template>
  <div class="safetyCommitmentFill">
    <el-dialog
      title="上报安全承诺"
      :visible.sync="show"
      @close="closeBoolean(false, '')"
      width="80%"
      top="5vh"
      :center="true"
      v-dialog-drag
      :close-on-click-modal="false"
    >
      <div class="fill">
        <!-- {{table}} -->
        <div>
          <div class="div1">
            <div class="title">
              <span>作业现状</span>
              <!-- <el-button type="primary" @click="openDialog" size="mini">历史承诺</el-button> -->
            </div>
            <div class="table">
              <el-form
                :model="table"
                ref="ruleForm"
                label-width="100px"
                class="demo-ruleForm"
              >
                <ul class="container">
                  <li>
                    <div class="l">
                      <span class="red">*</span>生产装置套数（套）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入生产装置套数"
                        class-p
                        oninput="value = value.replace(/[^0-9]/g,'')"
                        maxlength="8"
                        v-model="table.unitsNumber"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>运行套数（套）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入运行套数"
                        v-model="table.runNumber"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>停车套数（套）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入停车套数"
                        v-model="table.parkNumber"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>

                  <li>
                    <div class="l">
                      <span class="red">*</span>一级重大危险源（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入一级重大危险源数量"
                        v-model="table.dangerlevelone"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>

                  <li>
                    <div class="l">
                      <span class="red">*</span>二级重大危险源（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入二级重大危险源数量"
                        v-model="table.dangerleveltwe"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>

                  <li>
                    <div class="l">
                      <span class="red">*</span>三级重大危险源（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入三级重大危险源数量"
                        v-model="table.dangerlevelthree"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>四级重大危险源（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入四级重大危险源数量"
                        v-model="table.dangerleveltfour"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>试生产装置数（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入试生产装置数"
                        v-model="table.tryunitsNumber"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>重点监管危险工艺（种）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入重点监管危险工艺"
                        v-model="table.dangermsds"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>开车装置数（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入开车装置数"
                        v-model="table.workNumber"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>停车装置数（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入停车装置数"
                        v-model="table.notWorkNumber"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>是否有承包商作业
                    </div>
                    <div class="r radio">
                      <el-radio v-model="table.contractor" label="1"
                        >是</el-radio
                      >
                      <el-radio v-model="table.contractor" label="0"
                        >否</el-radio
                      >
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>是否处于试生产期
                    </div>
                    <div class="r radio">
                      <el-radio v-model="table.trialProduction" label="1"
                        >是</el-radio
                      >
                      <el-radio v-model="table.trialProduction" label="0"
                        >否</el-radio
                      >
                    </div>
                  </li>
                  <li>
                    <div class="l">
                      <span class="red">*</span>是否处于开停车状态
                    </div>
                    <div class="r radio">
                      <el-radio v-model="table.openParking" label="1"
                        >是</el-radio
                      >
                      <el-radio v-model="table.openParking" label="0"
                        >否</el-radio
                      >
                    </div>
                  </li>
                  <li class="">
                    <div class="l">
                      <span class="red">*</span>是否开展中（扩）
                    </div>
                    <div class="r radio">
                      <el-radio v-model="table.test" label="1">是</el-radio>
                      <el-radio v-model="table.test" label="0">否</el-radio>
                    </div>
                  </li>
                  <li class="">
                    <div class="l"><span class="red">*</span>有无重大隐患</div>
                    <div class="r radio">
                      <el-radio v-model="table.mhazards" label="1">有</el-radio>
                      <el-radio v-model="table.mhazards" label="0">无</el-radio>
                    </div>
                  </li>
                  <li class="bottom">
                    <div class="l">
                      <span class="red">*</span>检维修套数（处）
                    </div>
                    <div class="r">
                      <input
                        type="text"
                        placeholder="请输入检维修套数"
                        v-model="table.fixnum"
                        maxlength="8"
                        oninput="value = value.replace(/[^0-9]/g,'')"
                      />
                    </div>
                  </li>
                  <li class="bottom">
                    <div class="l"></div>
                    <div class="r"></div>
                  </li>
                </ul>
              </el-form>
            </div>
          </div>
          <div class="div2">
            <div class="title">风险作业</div>
            <div class="table">
              <ul class="container">
                <li>
                  <div class="l">
                    <span class="red">*</span>特级动火作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入特级动火作业数"
                      v-model="table.firesNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入特级动火作业数"
                      v-model.number="table.firesNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.firesNumber"
                      :max="1000"
                      @focus="
                        (currentValue, oldValue) => {
                          focusFiresNumber(currentValue, oldValue);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'firesNumber',
                            '01'
                          );
                        }
                      "
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.firesNumber);
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li>
                  <div class="l">
                    <span class="red">*</span>一级动火作业数（套）
                  </div>
                  <div class="r">
                    <!-- <input   
                      type="text"
                      placeholder="请输入一级动火作业数"
                      v-model="table.fire1Number"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入一级动火作业数"
                      v-model.number="table.fire1Number"
                      size="mini"
                      style="width: 100%"
                      :min="mix.fire1Number"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.fire1Number);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'fire1Number',
                            '02'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li>
                  <div class="l">
                    <span class="red">*</span> 二级动火作业数（处）
                  </div>
                  <div class="r">
                    <el-input-number
                      placeholder="请输入二级动火作业数"
                      v-model.number="table.fire2Number"
                      size="mini"
                      style="width: 100%"
                      :min="mix.fire2Number"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.fire2Number);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'fire1Number',
                            '03'
                          );
                        }
                      "
                    ></el-input-number>
                    <!-- <input
                      type="text"
                      placeholder="请输入二级动火作业数"
                      v-model="table.fire2Number"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                  </div>
                </li>
                <li>
                  <div class="l">
                    <span class="red">*</span
                    >受限空间作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入受限空间作业数"
                      v-model="table.spaceworkNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <!--  -->
                    <el-input-number
                      placeholder="请输入受限空间作业数"
                      v-model.number="table.spaceworkNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.spaceworkNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.spaceworkNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'spaceworkNumber',
                            '7'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li>
                  <div class="l">
                    <span class="red">*</span>盲板作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入盲板作业数"
                      v-model="table.blindplateNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入盲板作业数"
                      v-model.number="table.blindplateNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.blindplateNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.blindplateNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'blindplateNumber',
                            '5'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li>
                  <div class="l">
                    <span class="red">*</span>高处作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入高出作业数"
                      v-model="table.highworkNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="高处作业数（处）"
                      v-model.number="table.highworkNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.highworkNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.highworkNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'highworkNumber',
                            '2'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li class="">
                  <div class="l">
                    <span class="red">*</span>吊装作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入吊装作业数"
                      v-model="table.liftingworkNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入吊装作业数"
                      v-model.number="table.liftingworkNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.liftingworkNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.liftingworkNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'liftingworkNumber',
                            '6'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li class="">
                  <div class="l">
                    <span class="red">*</span>临时用电作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入临时用电作业数"
                      v-model="table.electricityworkNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入临时用电作业数"
                      v-model.number="table.electricityworkNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.electricityworkNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.electricityworkNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'electricityworkNumber',
                            '1'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li class="">
                  <div class="l">
                    <span class="red">*</span>动土作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入动土作业数"
                      v-model="table.soilworkNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入动土作业数"
                      v-model.number="table.soilworkNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.soilworkNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.soilworkNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'soilworkNumber',
                            '3'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>
                <li class="">
                  <div class="l">
                    <span class="red">*</span>断路作业数（处）
                  </div>
                  <div class="r">
                    <!-- <input
                      type="text"
                      placeholder="请输入断路作业数"
                      v-model="table.roadworkNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    /> -->
                    <el-input-number
                      placeholder="请输入断路作业数"
                      v-model.number="table.roadworkNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.roadworkNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.roadworkNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'roadworkNumber',
                            '4'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>

                <li class="">
                  <div class="l"><span class="red">*</span>倒灌作业</div>
                  <div class="r">
                    <el-input-number
                      placeholder="请输入倒灌作业"
                      v-model.number="table.pourOutNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.pourOutNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.pourOutNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'pourOutNumber',
                            'A'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>

                <li class="">
                  <div class="l"><span class="red">*</span>清灌作业</div>
                  <div class="r">
                    <el-input-number
                      placeholder="请输入清灌作业"
                      v-model.number="table.cleanTankNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.cleanTankNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.cleanTankNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'cleanTankNumber',
                            'B'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>

                <li class="bottom">
                  <div class="l"><span class="red">*</span>切水作业</div>
                  <div class="r">
                    <el-input-number
                      placeholder="请输入切水作业"
                      v-model.number="table.drainingNumber"
                      size="mini"
                      style="width: 100%"
                      :min="mix.drainingNumber"
                      :max="1000"
                      @input.native="
                        (e, oldVal) => {
                          inputFiresNumber(e, table.drainingNumber);
                        }
                      "
                      @change="
                        (currentValue, oldValue) => {
                          handleFire1Number(
                            currentValue,
                            oldValue,
                            'drainingNumber',
                            'C'
                          );
                        }
                      "
                    ></el-input-number>
                  </div>
                </li>

                <li class="bottom">
                  <div class="l">
                    <span class="red">*</span>检维修作业数（处）
                  </div>
                  <div class="r">
                    <input
                      type="text"
                      placeholder="请输入检维修作业数"
                      v-model="table.inspectionNumber"
                      maxlength="8"
                      oninput="value = value.replace(/[^0-9]/g,'')"
                    />
                  </div>
                </li>
                <li class="bottom">
                  <div class="l"></div>
                  <div class="r"></div>
                </li>
              </ul>
            </div>
          </div>

          <!-- {{StorageTankData}} -->

          <el-table
            :data="StorageTankData"
            style="width: 100%"
            :highlight-current-row="false"
            border
            class="tableA"
          >
            <el-table-column prop="storageTankNUM" label=" " width="50">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="jobType" label="作业类型" width="/">
              <template slot-scope="scope">
                <div :class="['color' + scope.row.jobType]">
                  {{ scope.row.jobType | jobTypefn }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="type" label="是否重大危险源" width="150">
              <template slot-scope="scope">
                <el-radio v-model="scope.row.dangerFlag" label="1">是</el-radio>
                <el-radio v-model="scope.row.dangerFlag" label="0">否</el-radio>
              </template>
            </el-table-column>
            <el-table-column
              prop="dangerId"
              label="作业地址/重大危险源"
              width="/"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.dangerFlag == 0">
                  其它
                  <!-- <el-input
                    type="text"
                    size="mini"
                    v-model="scope.row.dangerId"
                    placeholder="其它"
                    :disabled="scope.row.dangerFlag == 0"
                    v-model.trim="scope.row.dangerId"
                  /> -->
                </div>

                <div v-else>
                  <el-select
                    v-model="scope.row.dangerId"
                    size="small"
                    clearable
                    placeholder="请选择"
                    @change="changeDangerId"
                  >
                    <el-option
                      v-for="item in dangerList"
                      :key="item.dangerId"
                      :label="item.dangerName"
                      :value="item.dangerId"
                    >
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>

            <el-table-column prop="deviceId" label="设备名称" width="/">
              <template slot-scope="scope">
                <div v-if="scope.row.dangerFlag == 0">
                  其它
                  <!-- <el-input
                    type="text"
                    size="mini"
                    v-model="scope.row.dangerId"
                    placeholder="其它"
                    :disabled="scope.row.dangerFlag == 0"
                    v-model.trim="scope.row.dangerId"
                  /> -->
                </div>

                <div v-else>
                  <el-select
                    v-model="scope.row.deviceId"
                    size="mini"
                    placeholder="请选择设备名称"
                    clearable
                    @clear="clearSensortypeCode()"
                  >
                    <el-option
                      v-for="item in options1"
                      :key="item.equipmentCode"
                      :label="item.equipmentName"
                      :value="item.equipmentCode"
                    >
                    </el-option>
                  </el-select>
                </div>
              </template>
            </el-table-column>

            <!-- <el-table-column prop="isTest" label="是否承包商作业" width="120">
              <template slot-scope="scope">              
                <el-radio v-model="scope.row.isTest" label="1">是</el-radio>
                <el-radio v-model="scope.row.isTest" label="0">否</el-radio>
              </template>
            </el-table-column>
            <el-table-column label="施工单位" width="250">
              <template slot-scope="scope">
                <div v-if="scope.row.isTest == 1">
                  <el-select
                    v-model.trim="scope.row.shiGong"
                    filterable
                    size="mini"
                    placeholder="请输入施工单位名称搜索选择"
                    remote
                    value-key="id"
                    clearable
                    @change="
                      (item) => {
                        currentSel(item, scope.row);
                      }
                    "
                    reserve-keyword
                    :remote-method="Tolikesearch"
                  >
                    <el-option
                      v-for="item in safetyTrainingList"
                      :key="item.id"
                      :title="item"
                      :label="item.name"
                      :value="item"
                    >
                    </el-option>
                  </el-select>
                </div>
                <div v-else>
                  <el-input
                    type="text"
                    size="mini"
                    v-model.trim="scope.row.shiGong"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="项目负责人" width="">
              <template slot-scope="scope">
                <el-input
                  type="text"
                  size="mini"
                  :disabled="scope.row.isTest == 1"
                  v-model.trim="scope.row.shiGongPerson"
                />
              </template>
            </el-table-column>
            <el-table-column label="联系方式" width="">
              <template slot-scope="scope">
                <el-input
                  type="text"
                  size="mini"
                  v-model.trim="scope.row.shiGongPhone"
                  :disabled="scope.row.isTest == 1"
                />
              </template>
            </el-table-column> -->

            <el-table-column label="作业时间" width="420">
              <template slot-scope="scope">
                <el-date-picker
                  :clearable="false"
                  v-model="scope.row.datetimerangeValue"
                  type="datetimerange"
                  range-separator="至"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  start-placeholder="开始日期"
                  style="width: 380px"
                  :picker-options="{
                    disabledDate: (time) => {
                      return (
                        time.getTime() < Date.now() - 1 * 24 * 60 * 60 * 1000
                      );
                    },
                  }"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </template>
            </el-table-column>

            <el-table-column label="作业内容" width="">
              <template slot-scope="scope">
                <el-input
                  type="text"
                  size="mini"
                  v-model.trim="scope.row.workContent"
                />

                <div>
                  <!-- <el-select v-model="form.workType"
                                   placeholder="请选择参与作业类型">
                          <el-option v-for="dict in trainingFormOptions"
                                     :key="dict.id"
                                     :label="dict.label"
                                     :value="dict.id" />
                        </el-select> -->
                </div>
              </template>
            </el-table-column>

            <el-table-column label="操作" width="80">
              <template slot-scope="scope">
                <!-- <div
                  :disabled='scope.row.handleFlag==1'
                  class="deteleStyle"
                  @click="deteleFn(scope.$index, scope.row)"
                >
                  删除
                </div> -->
                <div v-if="scope.row.handleFlag != 1">
                  <button
                    class="deteleStyle"
                    @click="deteleFn(scope.$index, scope.row)"
                  >
                    删除
                  </button>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <br />
          <h2>进行中任务</h2>
          <el-table
            :data="StorageTankDataNo"
            style="width: 100%"
            :highlight-current-row="false"
            border
            class="tableA"
          >
            <el-table-column prop="storageTankNUM" label=" " width="50">
              <template slot-scope="scope">
                {{ scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column
              prop="jobType"
              label="作业类型"
              width="/"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <div :class="['color' + scope.row.jobType]">
                  {{ scope.row.jobType | jobTypefn }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="type"
              label="是否重大危险源"
              width="150"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <!-- <el-radio v-model="scope.row.dangerFlag" label="1">是</el-radio>
                <el-radio v-model="scope.row.dangerFlag" label="0">否</el-radio> -->
                {{ scope.row.dangerFlag == "1" ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column
              prop="dangerId"
              label="作业地址/重大危险源"
              width="/"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.dangerFlag == 0">其它</div>

                <div v-else>
                  {{ scope.row.dangerName }}
                </div>
              </template>
            </el-table-column>

            <el-table-column
              prop="deviceId"
              label="设备名称"
              width="/"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                <div v-if="scope.row.dangerFlag == 0">其它</div>
                <div v-else>
                  {{ scope.row.deviceName }}
                </div>
              </template>
            </el-table-column>

            <!-- <el-table-column prop="isTest" label="是否承包商作业" width="120">
              <template slot-scope="scope">              
                <el-radio v-model="scope.row.isTest" label="1">是</el-radio>
                <el-radio v-model="scope.row.isTest" label="0">否</el-radio>
              </template>
            </el-table-column>
            <el-table-column label="施工单位" width="250">
              <template slot-scope="scope">
                <div v-if="scope.row.isTest == 1">
                  <el-select
                    v-model.trim="scope.row.shiGong"
                    filterable
                    size="mini"
                    placeholder="请输入施工单位名称搜索选择"
                    remote
                    value-key="id"
                    clearable
                    @change="
                      (item) => {
                        currentSel(item, scope.row);
                      }
                    "
                    reserve-keyword
                    :remote-method="Tolikesearch"
                  >
                    <el-option
                      v-for="item in safetyTrainingList"
                      :key="item.id"
                      :title="item"
                      :label="item.name"
                      :value="item"
                    >
                    </el-option>
                  </el-select>
                </div>
                <div v-else>
                  <el-input
                    type="text"
                    size="mini"
                    v-model.trim="scope.row.shiGong"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column label="项目负责人" width="">
              <template slot-scope="scope">
                <el-input
                  type="text"
                  size="mini"
                  :disabled="scope.row.isTest == 1"
                  v-model.trim="scope.row.shiGongPerson"
                />
              </template>
            </el-table-column>
            <el-table-column label="联系方式" width="">
              <template slot-scope="scope">
                <el-input
                  type="text"
                  size="mini"
                  v-model.trim="scope.row.shiGongPhone"
                  :disabled="scope.row.isTest == 1"
                />
              </template>
            </el-table-column> -->

            <el-table-column
              label="作业时间"
              width="420"
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                {{ scope.row.jobStartTime + "-" + scope.row.jobEndTime }}
              </template>
            </el-table-column>

            <el-table-column
              label="作业内容"
              width=""
              :show-overflow-tooltip="true"
            >
              <template slot-scope="scope">
                {{ scope.row.workContent }}

                <div></div>
              </template>
            </el-table-column>
          </el-table>

          <div class="div3">
            <div class="title">企业承诺</div>
            <ul class="container">
              <li class="bottom">
                <div class="l"><span class="red">*</span>承诺人</div>
                <div class="r">{{ table.commitment }}</div>
              </li>
              <li class="bottom">
                <div class="l"><span class="red">*</span>承诺日期</div>
                <div class="r">{{ table.commiteDate }}</div>
              </li>
              <li class="bottom">
                <div class="l"></div>
                <div class="r"></div>
              </li>
            </ul>
            <div class="table">
              <textarea
                cols="5"
                placeholder="今日未上报"
                v-model="table.subject"
                maxlength="1000"
              ></textarea>
            </div>
            <span class="num">还可以输入{{ num }}个字</span>
          </div>
          <div class="foot">
            <el-button
              type="info"
              plain
              @click="!PromiseBtn ? saveTable(1) : null"
              :disabled="PromiseBtn"
              >保存草稿</el-button
            >
            <el-button
              type="primary"
              @click="!PromiseBtn ? saveTableDialog(0) : null"
              :disabled="PromiseBtn"
              >上报</el-button
            >
          </div>
        </div>
      </div>
      <div class="model" v-if="visible">
        <div class="box">
          <a-icon type="close" class="close" @click="handleOff()" />
          <div class="header"></div>
          <div class="content">
            <h1>未按时承诺</h1>
            <div>请于每日10:30之前上报安全承诺！</div>
          </div>
          <el-button
            type="primary"
            :disabled="s == 0 ? false : true || PromiseBtn"
            @click="!PromiseBtn ? handleOk(0) : null"
            class="el-button"
            size="large"
          >
            上报
            <span v-if="s == 0 ? false : true">{{ s }}s</span>
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getPromiseAdd, getSelectData } from "@/api/entList";
import { getContractorManagementListData } from "@/api/contractorManagement";
var dayjs = require("dayjs");
import {
  batchSave,
  particularJobPage,
  particularJobList,
} from "@/api/riskAssessment";
import { getEquipmentData } from "@/api/dailySafety";
export default {
  //import引入的组件
  components: {},
  data() {
    return {
      options1: [],
      safetyTrainingList: [], //施工单位下拉
      dangerList: [], //重大危险源下拉
      StorageTankData: [],
      StorageTankDataNo: [],
      visible: false,
      show: false,
      options: [
        {
          value: "1",
          label: "有",
        },
        {
          value: "0",
          label: "无",
        },
      ],
      options2: [
        {
          value: "1",
          label: "是",
        },
        {
          value: "0",
          label: "否",
        },
      ],
      value: "",
      table: {},
      loading: false,
      num: 1000,
      s: 5,
      timeout: "",
      PromiseBtn: false,
      saftyPromiseId: "",
      jobType7Mix: 0,
      jobTyp01Mix: 0,
      mix: {
        blindplateNumber: 0,
        cleanTankNumber: 0,
        drainingNumber: 0,
        electricityworkNumber: 0,
        fire1Number: 0,
        fire2Number: 0,
        firesNumber: 0,
        highworkNumber: 0,
        liftingworkNumber: 0,
        pourOutNumber: 0,
        roadworkNumber: 0,
        soilworkNumber: 0,
        spaceworkNumber: 0,
      },
    };
  },
  filters: {
    jobTypefn(val) {
      if (val == "01") {
        return "特级动火作业数";
      } else if (val == "02") {
        return "一级动火作业数";
      } else if (val == "03") {
        return "二级动火作业数";
      } else if (val == "7") {
        return "受限空间作业数";
      } else if (val == "5") {
        return "盲板作业数";
      } else if (val == "2") {
        return "高处作业数";
      } else if (val == "6") {
        return "吊装作业数";
      } else if (val == "1") {
        return "临时用电作业数";
      } else if (val == "3") {
        return "动土作业数";
      } else if (val == "4") {
        return "断路作业数";
      } else if (val == "A") {
        return "倒灌作业";
      } else if (val == "B") {
        return " 清灌作业";
      } else if (val == "C") {
        return "切水作业";
      }
    },
  },
  props: ["enterpriseId"],
  watch: {
    // "table.firesNumber": {
    //   handler(newVal, oldVal) {
    //     console.log(newVal,'firesNumber-newVal')
    //     for (var i = 0; i < newVal; i++) {
    //       this.addFn("firesNumber");
    //     }
    //   },
    // },
    // "table.fire1Number": {
    //   handler(newVal, oldVal) {
    //     console.log(newVal,'fire1Number-newVal')
    //     for (var i = 0; i < newVal; i++) {
    //       this.addFn("fire1Number");
    //     }
    //   },
    // },
    "table.subject": {
      handler(newVal, oldVal) {
        if (newVal) {
          this.num = 1000 - newVal.length;
        }
      },
    },
    immediate: true,
    deep: true,
  },
  //方法集合
  methods: {
    clearSensortypeCode() {},
    focusFiresNumber(currentValue, oldValue) {
      // debugger
    },
    inputFiresNumber(e, oldVal) {
      if (e.target.value == "") {
        e.target.value = 0;
      }
    },
    //加减事件
    handleFire1Number(currentValue, oldValue, type, typeCode) {
      if (currentValue > oldValue) {
        //新增
        var n = currentValue - oldValue;
        for (var i = 0; i < n; i++) {
          this.addFn(type, typeCode);
        }
      } else {
        //删除
        var n = oldValue - currentValue;
        var indexValue = 0;
        var itemValue = "";
        for (var i = 0; i < this.StorageTankData.length; i++) {
          if (this.StorageTankData[i].jobType == typeCode) {
            this.StorageTankData.splice(i, n);
            break;
          } else if (this.StorageTankData[i].jobType == typeCode) {
            this.StorageTankData.splice(i, n);
            break;
          }
        }

        // this.StorageTankData.splice(
        //   this.StorageTankData[this.StorageTankData.length - 1],
        //   n
        // );
      }
    },
    //新增作业list
    addFn(type, typeCode) {
      // item.cimRiskUnitId = this.parentData.cimRiskUnitId;
      // item.cimRiskUnitType = this.parentData.cimRiskUnitType;
      // item.enterpId = this.parentData.enterId; //enterpId
      // item.riskPointId = this.parentData.riskPointId;
      // var title = "";
      // var typeValue = "";
      // if (type == "fire1Number") {
      //   title = "一级动火作业数";
      // } else if (type == "firesNumber") {
      //   title = "特级动火作业数";
      // }
      let params = {
        // title: title, //作业类型
        dangerFlag: "1", //危险源标识0-否1-是
        workContent: "", //作业内容
        jobType: typeCode,
        type: type,
        dangerId: "", //危险源id
        deviceId: "", //设备设施id
        jobStartTime: "",
        jobEndTime: "",
        // datetimerangeValue:[]
        datetimerangeValue: [
          dayjs().format("YYYY-MM-DD") + " 00:00:00",
          dayjs().format("YYYY-MM-DD") + " 23:59:59",
        ],
      };
      if (this.StorageTankData.length > 0) {
        // var indexValue = 0;
        // for (var i = 0; i < this.StorageTankData.length; i++) {
        //   if (this.StorageTankData[i].jobType == typeCode) {
        //     this.StorageTankData.splice(i, 0, params);
        //     break;
        //   } else {
        //     this.StorageTankData.push(params);
        //     break;
        //   }
        // }
        var _index = "";
        var aFind = this.StorageTankData.some((el, index) => {
          _index = index;
          return el.jobType == typeCode;
        });
        if (aFind) {
          this.StorageTankData.splice(_index, 0, params);
        } else {
          this.StorageTankData.push(params);
        }
      } else {
        this.StorageTankData.push(params);
      }
    },
    //删除作业list
    deteleFn(val, row) {
      this.StorageTankData.splice(val, 1);
      if (row.jobType == "01") {
        if (this.table.firesNumber == 0) {
          this.table.firesNumber = 0;
        } else {
          this.table.firesNumber--;
        }
      } else if (row.jobType == "02") {
        //一级动火作业数（套）
        if (this.table.fire1Number == 0) {
          this.table.fire1Number = 0;
        } else {
          this.table.fire1Number--;
        }
      } else if (row.jobType == "03") {
        //二级动火作业数（处）
        if (this.table.fire2Number == 0) {
          this.table.fire2Number = 0;
        } else {
          this.table.fire2Number--;
        }
      } else if (row.jobType == "7") {
        //受限空间作业数（处）
        if (this.table.spaceworkNumber == 0) {
          this.table.spaceworkNumber = 0;
        } else {
          this.table.spaceworkNumber--;
        }
      } else if (row.jobType == "5") {
        //盲板作业数（处）
        if (this.table.blindplateNumber == 0) {
          this.table.blindplateNumber = 0;
        } else {
          this.table.blindplateNumber--;
        }
      } else if (row.jobType == "2") {
        //高处作业数（处）
        if (this.table.highworkNumber == 0) {
          this.table.highworkNumber = 0;
        } else {
          this.table.highworkNumber--;
        }
      } else if (row.jobType == "6") {
        //吊装作业数（处）
        if (this.table.liftingworkNumber == 0) {
          this.table.liftingworkNumber = 0;
        } else {
          this.table.liftingworkNumber--;
        }
      } else if (row.jobType == "1") {
        //临时用电作业数（处）
        if (this.table.electricityworkNumber == 0) {
          this.table.electricityworkNumber = 0;
        } else {
          this.table.electricityworkNumber--;
        }
      } else if (row.jobType == "3") {
        //动土作业数（处）
        if (this.table.soilworkNumber == 0) {
          this.table.soilworkNumber = 0;
        } else {
          this.table.soilworkNumber--;
        }
      } else if (row.jobType == "4") {
        //断路作业数（处）
        if (this.table.roadworkNumber == 0) {
          this.table.roadworkNumber = 0;
        } else {
          this.table.roadworkNumber--;
        }
      } else if (row.jobType == "A") {
        //倒灌作业
        if (this.table.pourOutNumber == 0) {
          this.table.pourOutNumber = 0;
        } else {
          this.table.pourOutNumber--;
        }
      } else if (row.jobType == "B") {
        // 清灌作业
        if (this.table.cleanTankNumber == 0) {
          this.table.cleanTankNumber = 0;
        } else {
          this.table.cleanTankNumber--;
        }
      } else if (row.jobType == "C") {
        //切水作业
        if (this.table.drainingNumber == 0) {
          this.table.drainingNumber = 0;
        } else {
          this.table.drainingNumber--;
        }
      }
    },
    //重大危险源下拉
    selectInit() {
      if(this.enterpriseId){
        getSelectData({
        enterpId: this.enterpriseId,
      }).then((res) => {
        if (res.data.code == 0) {
          this.dangerList = res.data.data;
        }
      });
      }
      
    },
    changeDangerId(val) {
      if(val!=''){
        getEquipmentData({
        dangerid: val,
      }).then((res) => {
        // res.data.data.splice(0, 1);
        // var aa={
        //   equipmentCode:'-1',
        //   equipmentName:"其它"
        // }
        res.data.data.forEach((el) => {
          if (el.equipmentCode == "-1") {
            el.equipmentName = "其它";
          }
        });
        this.options1 = res.data.data;
      });
      }
      
    },
    // selectVideoData(){
    //    getVideoData({
    //       enterpid: this.enterpid,
    //     }).then((res) => {
    //       arr = res.data.data;
    //       if (arr) {
    //         arr.forEach((item) => {
    //           item.equipmentName = item.name;
    //           item.equipmentCode = item.id;
    //         });
    //       }
    //       this.options1 = arr;
    //     });
    // },
    //施工单位
    Tolikesearch(query) {
      var orgCode = "";
      if (this.$store.state.login.user.user_type == "ent") {
        orgCode = this.$store.state.login.user.org_code;
      }
      var queryParams = {
        isBlacklist: "",
        keyword: query,
        nowPage: 1,
        orgCode: orgCode,
        orgName: "",
        pageSize: 500,
      };

      getContractorManagementListData(queryParams).then((response) => {
        this.safetyTrainingList = response.data.data.list;
      });
    },
    //施工单位赋值
    currentSel(item, row) {
      row.shiGongPerson = item.principal; //项目负责人
      row.shiGongPhone = item.principalContact; //联系方式
      row.workContent = item.legalPersonContact; //作业内容
    },
    handleOk(type) {
      this.saveTable(type);
      this.visible = false;
    },
    handleOff() {
      this.visible = false;
      this.s = 5;
      clearInterval(this.timeout);
    },
    /**  * type  * @param {Number} type [type=0上报，type=1时保存草稿] 保存草稿不用判定 */
    //判断超时上报逻辑
    saveTableDialog(type) {
      // if (this.objectValueAllExist(this.table)) {
        const time = new Date();
        const nowTime = Date.parse(new Date());
        const nowMonth = time.getMonth() * 1 + 1;
        const fillStopTime = Date.parse(
          time.getFullYear() +
            "-" +
            nowMonth +
            "-" +
            time.getDate() +
            " 10:30:59"
        );
        //当前时间晚于今天10点半
        if (nowTime > fillStopTime) {
          this.visible = true;
          if (this.s >= 1) {
            this.timeout = setInterval(() => {
              this.s = this.s - 1;
              if (this.s <= 0) {
                clearInterval(this.timeout);
              }
            }, 1000);
          } else {
            this.s = 0;
          }
        } else {
          this.saveTable(type);
        }
      // } else {
      //   this.$message.error("请填写完整");
      // }
    },
    /**  * object  * @param {Object} object   检查一个对象是否都有值的方法*/
    objectValueAllExist(object) {
      debugger
      var isExist = [];
      this.saftyPromiseId = object.id;
      delete object.id;
      Object.keys(object).forEach(function (item, index) {
        //判断当前对象里面的键值
        if (object[item] != null && object[item] !== "") {
          isExist[index] = true;
        } else {
          isExist[index] = false;
        }
      });
      //是否存在false
      if (isExist.includes(false)) {
        return false;
      }
      return true;
    },
    closeBoolean(val, table) {
      this.show = val;
      var that = this;
      this.StorageTankData = [];
      if (table == "") {
        this.table.unitsNumber = ""; //生产装置套数
        this.table.runNumber = ""; //运行套数
        this.table.parkNumber = ""; //停车套数
        this.table.contractor = ""; //是否有承包商作业
        this.table.trialProduction = ""; //是否处于试生产期
        this.table.openParking = ""; //是否处于开停车状态
        this.table.test = ""; //是否有承包商作业
        this.table.mhazards = ""; //
        this.table.firesNumber = ""; //特级动火作业数（处）
        this.table.fire1Number = "";
        this.table.fire2Number = ""; //二级动火作业数
        this.table.spaceworkNumber = "";
        this.table.blindplateNumber = "";
        this.table.highworkNumber = "";
        this.table.liftingworkNumber = "";
        this.table.electricityworkNumber = "";
        this.table.soilworkNumber = "";
        this.table.workNumber = "";
        this.table.notWorkNumber = ""; //停车装置数
        this.table.roadworkNumber = "";
        this.table.inspectionNumber = "";
        this.table.tryunitsNumber = ""; //试生产装置数（处）
        this.table.fixnum = ""; //检维修套数（处）
        this.table.dangermsds = ""; //重点监管危险工艺
        this.table.subject = "";
      } else {
        this.table = table;
        //初始化本机时间
        this.table.commiteDate = new Date(new Date()).Format("yy-MM-dd hh:mm");
        console.log(this.table, "this.table请求的数据");
        console.log(this.$store.state.login.enterData.enterpId,'获取安全承诺的企业id')
        particularJobList({
          enterpId: this.$store.state.login.enterData.enterpId,
          jobDate: dayjs().format("YYYY-MM-DD"),
          nowPage: 1,
          pageSize: 1000,
        }).then((res) => {
          if (res.data.status === 200) {
            // 作业需要这个接口的数据重新赋值
            var obj2 = res.data.data;
            Object.keys(obj2).forEach((key) => {
              this.table[key] = obj2[key];
            });
            var dataList = res.data.data.newDTOs || [];
            this.StorageTankDataNo = res.data.data.oldDTOs || [];
            //
            if (res.data.data.oldDTOs.length > 0) {
              //处理old数据
              res.data.data.oldDTOs.forEach((el) => {
                if (el.jobType == "C") {
                  el.jobTypeName = "drainingNumber";
                } else if (el.jobType == "B") {
                  el.jobTypeName = "cleanTankNumber";
                } else if (el.jobType == "A") {
                  el.jobTypeName = "pourOutNumber";
                } else if (el.jobType == "4") {
                  el.jobTypeName = "roadworkNumber";
                } else if (el.jobType == "3") {
                  el.jobTypeName = "soilworkNumber";
                } else if (el.jobType == "1") {
                  el.jobTypeName = "electricityworkNumber";
                } else if (el.jobType == "6") {
                  el.jobTypeName = "liftingworkNumber";
                } else if (el.jobType == "2") {
                  el.jobTypeName = "highworkNumber";
                } else if (el.jobType == "5") {
                  el.jobTypeName = "blindplateNumber";
                } else if (el.jobType == "7") {
                  el.jobTypeName = "spaceworkNumber";
                } else if (el.jobType == "03") {
                  el.jobTypeName = "fire2Number";
                } else if (el.jobType == "02") {
                  el.jobTypeName = "fire1Number";
                } else if (el.jobType == "01") {
                  el.jobTypeName = "firesNumber";
                }
              });
            }
              //设置input最小值min
            if (this.StorageTankDataNo.length > 0) {
              Object.keys(this.mix).forEach((key) => {
                this.mix[key] = res.data.data.oldDTOs.filter(
                  (item) => item.jobTypeName == key
                ).length;
              });
            }
            if (dataList.length > 0) {
              dataList.forEach((item, i) => {
                // if (item.handleFlag == 1) {
                //   //不能删除
                //   // this.jobTyp01Mix=that.table.firesNumber
                //   Object.keys(this.mix).forEach((key) => {
                //     this.mix[key] = this.table[key];
                //   });
                // } else {
                //   Object.keys(this.mix).forEach((key) => {
                //     this.mix[key] = 0;
                //   });
                // }
                this.StorageTankData.push(dataList[i]);
                if(item.dangerId){
                  if(item.dangerId!=''){
                    this.changeDangerId(item.dangerId);
                  }
                }
                

                dataList[i].datetimerangeValue = [
                  dataList[i].jobStartTime,
                  dataList[i].jobEndTime,
                ];
              });
              // debugger
              // let code01 = dataList.some((el) => el.jobType === "01"); //true
              // console.log(this.table,'this.table')
              // console.log(dataList,'dataList-newDTOs')
              // console.log(res.data.data.oldDTOs,'res.data.data.oldDTOs')
              // if (!code01) {
              //   if (this.table.fireNumber && this.table.fireNumber > 0) {
              //     for (var i = 0; i < this.table.fireNumber; i++) {
              //       this.addFn("fireNumber", "01");
              //     }
              //   }
              // }

              // let code02 = dataList.some((el) => el.jobType === "02"); //true
              // if (!code02) {
              //   if (this.table.fire1Number && this.table.fire1Number > 0) {
              //     for (var i = 0; i < this.table.fire1Number; i++) {
              //       this.addFn("fire1Number", "02");
              //     }
              //   }
              // }

              // let code03 = dataList.some((el) => el.jobType === "03"); //true
              // if (!code03) {
              //   if (this.table.fire2Number && this.table.fire2Number > 0) {
              //     for (var i = 0; i < this.table.fire2Number; i++) {
              //       this.addFn("fire2Number", "03");
              //     }
              //   }
              // }

              // let code7 = dataList.some((el) => el.jobType === "7"); //true
              // if (!code7) {
              //   if (
              //     this.table.spaceworkNumber &&
              //     this.table.spaceworkNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.spaceworkNumber; i++) {
              //       this.addFn("spaceworkNumber", "7");
              //     }
              //   }
              // }

              // let code5 = dataList.some((el) => el.jobType === "5"); //true
              // if (!code5) {
              //   if (
              //     this.table.blindplateNumber &&
              //     this.table.blindplateNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.blindplateNumber; i++) {
              //       this.addFn("blindplateNumber", "5");
              //     }
              //   }
              // }

              // let code2 = dataList.some((el) => el.jobType === "2"); //true
              // if (!code2) {
              //   if (
              //     this.table.highworkNumber &&
              //     this.table.highworkNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.highworkNumber; i++) {
              //       this.addFn("highworkNumber", "2");
              //     }
              //   }
              // }

              // let code6 = dataList.some((el) => el.jobType === "6"); //true
              // if (!code6) {
              //   if (
              //     this.table.liftingworkNumber &&
              //     this.table.liftingworkNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.liftingworkNumber; i++) {
              //       this.addFn("liftingworkNumber", "6");
              //     }
              //   }
              // }

              // let code1 = dataList.some((el) => el.jobType === "1"); //true
              // if (!code1) {
              //   if (
              //     this.table.electricityworkNumber &&
              //     this.table.electricityworkNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.electricityworkNumber; i++) {
              //       this.addFn("electricityworkNumber", "1");
              //     }
              //   }
              // }

              // let code3 = dataList.some((el) => el.jobType === "3"); //true
              // if (!code3) {
              //   if (
              //     this.table.soilworkNumber &&
              //     this.table.soilworkNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.soilworkNumber; i++) {
              //       this.addFn("soilworkNumber", "3");
              //     }
              //   }
              // }

              // let code4 = dataList.some((el) => el.jobType === "4"); //true
              // if (!code4) {
              //   if (
              //     this.table.roadworkNumber &&
              //     this.table.roadworkNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.roadworkNumber; i++) {
              //       this.addFn("roadworkNumber", "4");
              //     }
              //   }
              // }

              // let codeA = dataList.some((el) => el.jobType === "A"); //true
              // if (!codeA) {
              //   if (this.table.pourOutNumber && this.table.pourOutNumber > 0) {
              //     for (var i = 0; i < this.table.pourOutNumber; i++) {
              //       this.addFn("pourOutNumber", "A");
              //     }
              //   }
              // }

              // let codeB = dataList.some((el) => el.jobType === "B"); //true
              // if (!codeB) {
              //   if (
              //     this.table.cleanTankNumber &&
              //     this.table.cleanTankNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.cleanTankNumber; i++) {
              //       this.addFn("cleanTankNumber", "B");
              //     }
              //   }
              // }

              // let codeC = dataList.some((el) => el.jobType === "C"); //true
              // if (!codeC) {
              //   if (
              //     this.table.drainingNumber &&
              //     this.table.drainingNumber > 0
              //   ) {
              //     for (var i = 0; i < this.table.drainingNumber; i++) {
              //       this.addFn("drainingNumber", "C");
              //     }
              //   }
              // }
            }
          } else {
          }
        });

        // if (this.table.firesNumber && this.table.firesNumber > 0) {
        //   for (var i = 0; i < this.table.firesNumber; i++) {
        //     this.addFn("firesNumber", "01");
        //   }
        // }
        // if (this.table.fire1Number && this.table.fire1Number > 0) {
        //   for (var i = 0; i < this.table.fire1Number; i++) {
        //     this.addFn("fire1Number", "02");
        //   }
        // }
      }
    },
    saveTable(type) {

      //判断对象是否都有值的方法   //这种判断没有具体的提示
      if (!this.table.unitsNumber) {
        this.$message({ message: "生产装置套数不能为空", type: "error" });
        return;
      }
      if (!this.table.runNumber) {
        this.$message({ message: "运行套数不能为空", type: "error" });
        return;
      }
      if (!this.table.parkNumber) {
        this.$message({ message: "停车套数不能为空", type: "error" });
        return;
      }
      if (!this.table.dangerlevelone) {
        this.$message({ message: "一级重大危险源不能为空", type: "error" });
        return;
      }
      if (!this.table.dangerleveltwe) {
        this.$message({ message: "二级重大危险源不能为空", type: "error" });
        return;
      }
      if (!this.table.dangerlevelthree) {
        this.$message({ message: "三级重大危险源不能为空", type: "error" });
        return;
      }
      if (!this.table.dangerleveltfour) {
        this.$message({ message: "四级重大危险源不能为空", type: "error" });
        return;
      }
      if (!this.table.tryunitsNumber) {
        this.$message({ message: "试生产装置数不能为空", type: "error" });
        return;
      }
      if (!this.table.dangermsds) {
        this.$message({ message: "重点监管危险工艺不能为空", type: "error" });
        return;
      }
      if (!this.table.workNumber) {
        this.$message({ message: "开车装置数不能为空", type: "error" });
        return;
      }
      if (!this.table.notWorkNumber) {
        this.$message({ message: "停车装置数不能为空", type: "error" });
        return;
      }
      if (!this.table.trialProduction) {
        this.$message({ message: "是否处于试生产期不能为空", type: "error" });
        return;
      }
      if (!this.table.openParking) {
        this.$message({ message: "是否处于开停车状态不能为空", type: "error" });
        return;
      }
      if (!this.table.test) {
        this.$message({ message: "是否开展中不能为空", type: "error" });
        return;
      }
      if (!this.table.mhazards) {
        this.$message({ message: "有无重大隐患不能为空", type: "error" });
        return;
      }
      //fixnum
      if (!this.table.fixnum) {
        this.$message({ message: "检维修套数不能为空", type: "error" });
        return;
      }   
      //    if (!this.table.firesNumber) {
      //   this.$message({ message: "特级动火作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.fire1Number) {
      //   this.$message({ message: "一级动火作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.fire2Number) {
      //   this.$message({ message: " 二级动火作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.spaceworkNumber) {
      //   this.$message({ message: "受限空间作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.blindplateNumber) {
      //   this.$message({ message: "盲板作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.highworkNumber) {
      //   this.$message({ message: "高处作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.liftingworkNumber) {
      //   this.$message({ message: "吊装作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.electricityworkNumber) {
      //   this.$message({ message: "临时用电作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.soilworkNumber) {
      //   this.$message({ message: "动土作业数不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.roadworkNumber) {
      //   this.$message({ message: "断路作业数不能为空", type: "error" });
      //   return;
      // }

      //  if (!this.table.pourOutNumber) {
      //   this.$message({ message: "倒灌作业不能为空", type: "error" });
      //   return;
      // }
      // if (!this.table.cleanTankNumber) {
      //   this.$message({ message: "清灌作业不能为空", type: "error" });
      //   return;
      // }

      //   if (!this.table.drainingNumber) {
      //   this.$message({ message: "切水作业不能为空", type: "error" });
      //   return;
      // } 
      if (!this.table.inspectionNumber) {
        this.$message({ message: "检维修作业数不能为空", type: "error" });
        return;
      }

      if(!this.table.commitment){
        this.$message({ message: "承诺人不能为空", type: "error" });
        return;
      }
      //
      if(!this.table.commiteDate){
        this.$message({ message: "承诺日期不能为空", type: "error" });
        return;
      }
     
      // var a=this.StorageTankData.push(this.StorageTankDataNo)
      // console.log(a)
      // jobStartTime:"",
      // jobEndTime:"",
      // this.StorageTankData.forEach((el, i) => {
      //   if (el.dangerFlag == 1) {
      //     if (el.dangerName == "" || el.deviceName == "") {
      //       this.$message.error("第" + i + "行重大危险源和设备名称不能为空");
      //     }
      //   }
      // });
      var isOff = true;
      if (this.StorageTankData.length > 0) {
        this.StorageTankData.forEach((item, i) => {
          item.jobStartTime = item.datetimerangeValue[0];
          item.jobEndTime = item.datetimerangeValue[1];
        });
        for (var i = 0; i < this.StorageTankData.length; i++) {
          if (this.StorageTankData[i].dangerFlag == 1) {
            if (
              !this.StorageTankData[i].dangerId ||
              !this.StorageTankData[i].deviceId
            ) {
              isOff = false;
              this.$message.error(
                "第" + (i + 1) + "作业地址/重大危险源和设备名称不能为空"
              );
              return false;
            } else {
              isOff = true;
            }
          }
        }
      }

      if (isOff) {
        // if (this.StorageTankData.length > 0) {
        // }
        var aryContact = [...this.StorageTankData, ...this.StorageTankDataNo];
        batchSave(aryContact).then((res) => {
          if (res.data.status == 200) {
          } else {
            this.$message.error(res.data.msg);
          }
        });

        delete this.table.commiteDate;
        this.PromiseBtn = true;
        const THIS = this;
        getPromiseAdd({
          ...this.table,
          id: this.saftyPromiseId,
          commitmentstate: type,
          companyCode: this.enterpriseId,
        }).then((res) => {
          this.PromiseBtn = false;
          //判断上报情况
          if (res.data.code == 0) {
            this.$message({
              message: res.data.data,
              type: "success",
            });
          } else {
            this.$message.error(res.data.data);
          }
          this.show = false;
          this.$emit("save", this.enterpriseId);
        });
      }
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素
  created() {
    this.selectInit();
  },
  mounted() {
    // console.log(this.table.firesNumber, "初始化-特级动火作业数");
    //  this.$watch('table.firesNumber',function(newVal,oldVal){
    //    console.log(newVal,'table.firesNumber-newVal')
    //  })
    // const unwatch = this.$watch('table.firesNumber', function(newValue, oldValue){
    //     console.log(newValue);
    //     unwatch()
    //   });
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-table .el-table__header-wrapper tr {
  background-color: #fff;
  color: #000;
  font-weight: bold;
}
/deep/ .el-range-editor.el-input__inner {
  padding: 0 5px;
}
/deep/ .el-input__inner {
  height: 32px;
  line-height: 32px;
}
/deep/ .el-date-editor .el-range__icon {
  line-height: 0;
}
/deep/ .el-select {
  width: 90%;
}
/deep/ .tableA .el-radio {
  margin-right: 10px;
}
.deteleStyle {
  cursor: pointer;
  color: blue;
}
.color01 {
  color: rgb(111, 0, 255);
}
.color02 {
  color: #faad14;
}
.color03 {
  color: hsl(320, 100%, 50%);
}
.color7 {
  color: #ff0000;
}
.color5 {
  color: #88ff00;
}
.color2 {
  color: #0099ff;
}
.color6 {
  color: #fd3300;
}
.color1 {
  color: #7d960d;
}
.color3 {
  color: #025361;
}
.color4 {
  color: #5f0468;
}
.colorA {
  color: #684504;
}
.colorB {
  color: #087e25;
}
.colorC {
  color: #6d061c;
}
input::-webkit-input-placeholder {
  color: rgb(199, 199, 199);
}
input::-moz-input-placeholder {
  color: rgb(199, 199, 199);
}
input::-ms-input-placeholder {
  color: rgb(199, 199, 199);
}

textarea::-webkit-input-placeholder {
  color: rgb(199, 199, 199);
}
textarea::-moz-input-placeholder {
  color: rgb(199, 199, 199);
}
textarea::-ms-input-placeholder {
  color: rgb(199, 199, 199);
}
.safetyCommitmentFill {
  overflow: hidden;
  color: #000;
  .fill {
    height: auto;
    max-height: 80vh;
    overflow: auto;
  }
  .content {
    display: flex;
    align-items: center;
    font-size: 16px;
    .icon {
      color: #faad14;
      font-size: 22px;
      margin-right: 4px;
    }
  }
  .ent {
    height: 60vh;
    width: 100%;
    .header {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .container {
      height: 40vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      margin-top: 60px;
      .bg {
        background: url("../../../../../static/img/aqcnsb.png");
        background-size: 100% 100%;
        width: 547px;
        height: 253px;
        margin-bottom: 40px;
      }
    }
  }
  input {
    border: none;
    outline: none;
    width: 96%;
    height: 75%;
    // background-color: #f3f3f3;
  }
  ul {
    padding-inline-start: 0px;
    margin-bottom: 1em;
  }
  .div1 {
    margin-top: 20px;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }
    .pageHeader {
      margin: 20px 0;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
          .radio {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            padding-left: 20px;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
      }
    }
  }
  .div2 {
    margin-top: 20px;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          height: 40px;
          line-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
          }
        }
      }
    }
  }
  .div3 {
    margin-top: 20px;
    padding-bottom: 20px;
    width: 100%;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .container {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      text-align: center;
      .bottom {
        // border-top: 1px solid rgb(182, 182, 182);
        border-bottom: 1px solid rgb(231, 231, 231);
      }
      li {
        list-style-type: none;
        width: 33.3%;
        display: flex;
        border-top: 1px solid rgb(231, 231, 231);
        // border-right: 1px solid rgb(231, 231, 231);
        border-left: 1px solid rgb(231, 231, 231);
        overflow: hidden;
        height: 40px;
        line-height: 40px;
        .red {
          color: red;
        }
        .l {
          width: 50%;
          color: #60627a;
          background: rgb(242, 246, 255);
        }

        .r {
          width: 50%;
        }
      }
      li:nth-of-type(3n + 0) {
        list-style-type: none;
        width: 33.3%;
        display: flex;
        border-top: 1px solid rgb(231, 231, 231);
        border-right: 1px solid rgb(231, 231, 231);
        // border-left: 1px solid rgb(231, 231, 231);
        overflow: hidden;
        height: 40px;
        line-height: 40px;
        .red {
          color: red;
        }
        .l {
          width: 50%;
          color: #60627a;
          background: rgb(242, 246, 255);
        }

        .r {
          width: 50%;
        }
      }
    }
    .table {
      width: 100%;
      height: 139px;
      border: 1px solid rgb(231, 231, 231);
      display: flex;
      align-items: center;
      overflow: hidden;
      textarea {
        border: none;
        outline: none;
        height: 100px;
        width: 98%;
        margin-left: 1%;
        resize: none;
        // background-color: #f3f3f3;
      }
    }
    .num {
      float: right;
      margin: 10px 10px 0 0;
      font-size: 12px;
      color: #777;
    }
  }
  .foot {
    padding: 20px 10px;
    display: flex;
    justify-content: flex-end;
    margin-top: 15px;
  }
  .model {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100000;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    & > .box {
      width: 396px;
      height: 369px;
      background-color: #fff;
      position: relative;
      border-radius: 5px;
      .header {
        width: 396px;
        height: 165px;
        background-image: url("../../../../../static/img/anquanchengnuoBg.png");
      }
      .close {
        position: absolute;
        right: 10px;
        top: 10px;
        font-size: 18px;
      }
      .content {
        width: 100%;
        height: 75px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: center;
        margin-top: 30px;
      }
      .el-button {
        margin-top: 30px;
        margin-left: 50%;
        transform: translateX(-50%);
      }
    }
  }
}
</style>
