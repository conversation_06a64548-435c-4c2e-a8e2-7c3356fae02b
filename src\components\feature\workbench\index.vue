<template>
  <div class="workingaccount">
    <component
      :is="$route.name === 'enterWorkbench' ? 'enterWorkbench' : $route.name"
    ></component>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  //import引入的组件
  components: {
    enterWorkbench: () => import("./enterWorkbench"),
    superviseWorkbench: () => import("./superviseWorkbench"),
  },
  data() {
    return {};
  },
  //方法集合
  methods: {},
  computed: {
    ...mapStateLogin({
      user: (state) => state.user,
    }),
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},

  watch: {},
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
