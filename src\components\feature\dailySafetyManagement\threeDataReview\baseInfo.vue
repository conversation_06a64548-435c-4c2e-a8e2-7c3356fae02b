<template>
  <div class="infoTableOne">
    <!-- {{ baseInfoData }} -->
    <div class="btnBox">
      <!-- disabled='true' 禁止不能用 -->
      <el-button
        type="primary"
        size="mini"
        @click="passInfo"
        :disabled="
          baseInfoData.approvalStatus == '4' ||
          baseInfoData.approvalStatus == '5' ||
          baseInfoData.approvalStatus == '3' || 
          user.isAdmin != 1
        "
        >受理</el-button
      >

      <!-- 处理中只能驳回，不能点受理 -->
      <el-button
        type="primary"
        size="mini"
        @click="bacKInfoBtn"
        :disabled="baseInfoData.approvalStatus != '3' ||  user.isAdmin != 1"
        >驳回</el-button
      >
    </div>
    <div class="titStatus">
      <span>当前状态 ：</span>
      <!-- approvalStatus -->
      <div class="rejectBtn">
        {{ baseInfoData.approvalStatusName }}
      </div>
      <!-- <div class="processBtn" v-else-if="3">
        {{ baseInfoData.approvalStatusName }}
      </div>
      <div class="processBtn" v-else-if="1">
        {{ baseInfoData.approvalStatusName }}
      </div>
      <div class="processBtn" v-else-if="2">
        {{ baseInfoData.approvalStatusName }}
      </div>
      <div class="processBtn" v-else-if="5">
        {{ baseInfoData.approvalStatusName }}
      </div> -->
    </div>
    <div class="detailNew">
      <table>
        <tr>
          <th>企业名称</th>
          <td colspan="3">{{ baseInfoData.enterpName || "--" }}</td>
          <th>项目名称</th>
          <td colspan="2">{{ baseInfoData.projectName || "--" }}</td>
        </tr>
        <tr>
          <th>报告类型</th>
          <td>{{ baseInfoData.reportTypeName || "--" }}</td>
          <th>项目类型</th>
          <td>{{ baseInfoData.projectTypeName || "--" }}</td>
          <th>审批级别</th>
          <!-- <td>{{ baseInfoData.approvalLevel || "--" }}</td> -->
          <td>{{ baseInfoData.approvalLevel | filterapprovalStatusName }}</td>
        </tr>
      </table>
    </div>
    <!-- this.reportAttachList = this.baseInfoData.reportAttachList; //电子报告
    this.commentAttachList = this.baseInfoData.commentAttachList; //专家意见附件信息
    this.reviseAttachList = this.baseInfoData.reviseAttachList; //修订对照附件信息
    this.signAttachList = this.baseInfoData.signAttachList; //签字报告附件信息
    this.otherAttachList = this.baseInfoData.otherAttachList; //其他附件信息 -->
    <div class="itemBox">
      <div class="item">
        <div class="widthOnly">电子报告 ：</div>
        <AttachmentUpload
          :attachmentlist="baseInfoData.reportAttachList"
          :limit="5"
          type="pdf"
          v-bind="{}"
          :editabled="true"
        ></AttachmentUpload>
        <!-- <span><i class="file"></i>“三同时”资料复查电子报告名称.pdf</span> -->
      </div>
      <div class="item">
        <div class="widthOnly">专家意见表 ：</div>
        <!-- commentAttachList -->
        <AttachmentUpload
          :attachmentlist="baseInfoData.commentAttachList"
          :limit="5"
          type="pdf"
          v-bind="{}"
          :editabled="true"
        ></AttachmentUpload>
        <!-- <span><i class="file"></i>“三同时”资料复查电子报告名称.pdf</span> -->
      </div>
      <div class="item">
        <div class="widthOnly">修订对照表 ：</div>
        <AttachmentUpload
          :attachmentlist="baseInfoData.reviseAttachList"
          :limit="5"
          type="pdf"
          v-bind="{}"
          :editabled="true"
        ></AttachmentUpload>
        <!-- <span><i class="file"></i>“三同时”资料复查电子报告名称.pdf</span> -->
      </div>
      <!-- <div class="item">
        <div>签字报告附件 ：</div>
        <AttachmentUpload
          :attachmentlist="signAttachList"
          :limit="5"
          type="pdf"
          v-bind="{}"
          :editabled="true"
        ></AttachmentUpload>
      </div> -->

      <div class="item">
        <div>其它附件 ：</div>
        <AttachmentUpload
          :attachmentlist="baseInfoData.otherAttachList"
          :limit="5"
          type="pdf"
          v-bind="{}"
          :editabled="true"
        ></AttachmentUpload>
      </div>

      <div class="itemSub">
        <!-- <AttachmentUpload
                  :attachmentlist="signAttachListBack"
                  :limit="5"
                  type="pdf"
                  v-bind="{}"                 
                  :editabled="true"
                ></AttachmentUpload> -->
        <!-- <label>
          <span
            ><i class="file"></i>“三同时”资料复查电子报告名称.pdf</span
          ></label
        >
        <label>
          <span
            ><i class="file"></i>“三同时”资料复查电子报告名称.pdf</span
          ></label
        >
        <label>
          <span
            ><i class="file"></i>“三同时”资料复查电子报告名称.pdf</span
          ></label
        > -->
      </div>

      <div class="item">备注 ：{{ baseInfoData.remark || "无" }}</div>
      <div class="item" v-if="$route.query.approvalStatus == 4">
        驳回原因 ：{{ baseInfoData.rejectInfo || "无" }}
      </div>

      <div class="item" v-if="baseInfoData.approvalStatus == 5">
        <div>专家签字报告 ：</div>
        <AttachmentUpload
          :attachmentlist="baseInfoData.signAttachList"
          :limit="5"
          type="pdf"
          v-bind="{}"
          :editabled="true"
        ></AttachmentUpload>
      </div>
    </div>

    <!-- <div class="expertOpinion">
      <div class="titStatus">
        <span>专家复查意见</span>
      </div>
      <div class="formBox">
        <el-form ref="form" :model="form" label-width="100px">
          <div class="form_item">
            <div class="form_main">
              <el-row :gutter="24">
                <el-col :span="24">
                  <el-form-item label="专家姓名：" prop="name">
                    <el-input
                      v-model="form.name"
                      disabled
                      placeholder=""
                      style="width: 316px"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="结论：" prop="checkPage">
                    <el-radio-group v-model="form.checkPage">
                      <el-radio label="1">通过</el-radio>
                      <el-radio label="2">不通过</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>

                <el-col :span="24">
                  <el-form-item label="意见说明：" prop="checkPage">
                    <el-input
                      type="textarea"
                      placeholder="请输入意见说明"
                      v-model="form.Comments"
                      :rows="4"
                      maxlength="500"
                    >
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
    </div> -->

    <el-dialog
      title="驳回"
      :visible.sync="dialogVisible"
      width="500px"
      top="5vh"
      @close="closeDialog"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="dialog dialogInfo">
        <el-form ref="ruleForm" :model="form2" size="medium" :rules="rules2">
          <el-descriptions :column="2" border :labelStyle="labelStyle">
            <el-descriptions-item :span="2">
              <template slot="label"
                ><span class="redDot" style="color:red;">*</span> 驳回：
              </template>
              <el-form-item prop="rejectInfo">
                <el-input
                  type="textarea"
                  placeholder="请输入驳回原因"
                  v-model.trim="form2.rejectInfo"
                  :rows="4"
                  maxlength="500"
                  show-word-limit
                >
                </el-input>
              </el-form-item>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
      </div>
      <div slot="footer" style="display: flex; justify-content: center">
        <!-- 保存的相当于是个草稿 -->
        <el-button size="mini" type="primary" @click="backInfo()"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>


<script>
import AttachmentUpload from "@/components/common/packages/attachmentUploadStyle";
import { mapState } from "vuex";
import {
  getCompanyProjectFindById, //查询详情
  getCompanyProjectReject, //驳回
  projectEvaluatePass,
  projectEvaluateRecieve,
} from "@/api/companyParticularJob";
import controler from "@/store/modules/controler";
export default {
  name: "baseInfo",
  components: {
    AttachmentUpload,
  },
  // props: {
  //   baseInfoData: Object,
  // },
  data() {
    return {
      nowTime: "",
      baseInfoData: "",
      dialogVisible: false,
      reportAttachList: [], //电子报告
      commentAttachList: [], //专家意见附件信息
      reviseAttachList: [], //修订对照附件信息
      signAttachList: [], //签字报告附件信息
      otherAttachList: [], //其他附件信息  no
      messageTip:
        "请上传word、pdf等其他补充材料，最多上传5个，单个文件大小不超过30M",
      form: {
        signAttachList: [],
        infoId: "",
      },
      form2: {
        rejectInfo: "",
        infoId: "",
      },
      rules2: {
        rejectInfo: [
          { required: true, message: "驳回原因不能为空", trigger: "blur" },
        ],
      },
      labelStyle: {
        textAlign: "center",
        backgroundColor: "rgb(242, 246, 255)",
      },
    };
  },
  // watch:{
  //   baseInfoData:(newData){
  //     debugger
  //   },
  //   deep:true,
  //   immediate: true,
  // },

   computed: {
    ...mapState({
      user: (state) => state.login.user,
      userDistCode: (state) => state.login.userDistCode,
      district: (state) => state.controler.district,
      enterData: (state) => state.login.enterData,
    }),
  },

  filters: {
    filterStatus(val) {
      let str = "";
      switch (val) {
        case "0":
          str = "不通过";
          break;
        case "1":
          str = "通过";
          break;
        default:
          break;
      }
      return str;
    },
    //filterapprovalStatusName
    filterapprovalStatusName(val) {
      let str = "";
      switch (val) {
        case "0":
          str = "省级";
          break;
        case "1":
          str = "市级";
          break;
        case "2":
          str = "区级";
          break;
        default:
          break;
      }
      return str;
    },
  },
  methods: {
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month =
        new Date(timeStamp).getMonth() + 1 < 10
          ? "0" + (new Date(timeStamp).getMonth() + 1)
          : new Date(timeStamp).getMonth() + 1;
      let date =
        new Date(timeStamp).getDate() < 10
          ? "0" + new Date(timeStamp).getDate()
          : new Date(timeStamp).getDate();
      let hh =
        new Date(timeStamp).getHours() < 10
          ? "0" + new Date(timeStamp).getHours()
          : new Date(timeStamp).getHours();
      let mm =
        new Date(timeStamp).getMinutes() < 10
          ? "0" + new Date(timeStamp).getMinutes()
          : new Date(timeStamp).getMinutes();
      let ss =
        new Date(timeStamp).getSeconds() < 10
          ? "0" + new Date(timeStamp).getSeconds()
          : new Date(timeStamp).getSeconds();
      let week = new Date(timeStamp).getDay();
      let weeks = ["日", "一", "二", "三", "四", "五", "六"];
      let getWeek = "星期" + weeks[week];
      return year + month + date;
    },
    //查看详情
    getCompanyParticularJobFun(row) {     
      getCompanyProjectFindById({ infoId: this.$route.query.infoId }).then(
        (res) => {
          this.baseInfoData = res.data.data;        
        }
      );
    },
    closeDialog() {},
    //复核、受理
    passInfo() {
      // this.$store.state.controler
      // this.$store.commit('controler/updateTableBar','expertReview');
      // console.log(this.$store.state.controler.clickTableBar)
      //        this.$router.push({
      //         path: "/dailySafetyManagement/handleProcedures",
      //         query:{infoId:this.$route.query.infoId,approvalStatus:this.$route.query.approvalStatus,checkFlag:this.$route.query.checkFlag,query:1}
      //       });
      //  this.$refs[this.activeTabClass].getList();
      //   this.$refs[this.activeTabClass].reviewResultData()

      this.$confirm("确认受理？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.form.infoId = this.baseInfoData.infoId;
          projectEvaluateRecieve(this.form).then((res) => {
            if (res.data.status === 200) {
              this.$message.success(res.data.data);
              this.$emit("updateMsg", "expertReview");
              this.getCompanyParticularJobFun(); //需要监听状态
              // this.$router.push({
              //   path: `/dailySafetyManagement/threeDataReview`,
              // });
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
    },
    //驳回按钮
    bacKInfoBtn() {
      this.dialogVisible = true;
    },
    //驳回
    backInfo() {
      this.$refs["ruleForm"].validate((valid) => {      
       this.form2.infoId = this.baseInfoData.infoId;
        if (valid) {
          getCompanyProjectReject(this.form2).then((res) => {
            if (res.data.status === 200) {
              this.$message.success(res.data.data);
              this.$message.success(res.data.data);
              this.$router.push({
                path: `/dailySafetyManagement/threeDataReview`,
              });
              this.dialogVisible = false;
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
  },
  mounted() {
    this.$nextTick(() => {
      this.getCompanyParticularJobFun();
    });  
  },
  created() {    
    // console.log(this.$store,'这是this.$store')
    // console.log($store.state.login.user.isDanger,'这是this.$store.isDanger')  
    // this.form.infoId = this.baseInfoData.infoId;
    // this.form2.infoId = this.baseInfoData.infoId;
    // this.reportAttachList = this.baseInfoData.reportAttachList; //电子报告
    // this.commentAttachList = this.baseInfoData.commentAttachList; //专家意见附件信息
    // this.reviseAttachList = this.baseInfoData.reviseAttachList; //修订对照附件信息
    // this.signAttachList = this.baseInfoData.signAttachList; //签字报告附件信息
    // this.otherAttachList = this.baseInfoData.otherAttachList; //其他附件信息
  },
};
</script>

<style lang="scss" scoped>
.infoTableOne {
  height: 72vh;
  overflow: auto;
  padding: 0 10px;
  .btnBox {
    position: absolute;
    top: -34px;
    right: 20px;
  }
  .titStatus {
    display: flex;
    height: 50px;
    align-items: center;
    // margin-top: -15px;
  }
  .titStatus span {
    border-left: 4px solid #409eff;
    padding: 0 0 0 10px;
    color: #252525;
  }

  .rejectBtn {
    width: 74px;
    height: 30px;
    background: #f8e8e8;
    line-height: 30px;
    text-align: center;
    margin: 0 10px;
    color: #c43129;
    border-radius: 3px;
  }
  .processBtn {
    width: 74px;
    height: 30px;
    background: #eaf0ff;
    line-height: 30px;
    text-align: center;
    color: #3e76ff;
  }
  .itemBox {
    padding: 0 0 0 0;
    .item {
      margin: 20px 0 0 0;
      position: relative;
      padding: 0 0 0 15px;
      display: flex;
      align-items: center;
      div.widthOnly {
        width: 90px;
      }
      span {
        color: #3470ff;
        cursor: pointer;
        cursor: pointer;
        text-decoration: underline;
        padding: 0 0 0 0;
        i {
          //  background: url(/static/img/assets/img/review/ico_link.png) no-repeat center center / contain;
          background: url("../../../../../static/img/assets/img/review/ico_link.png")
            no-repeat center center / contain;
          width: 17px;
          height: 17px;
          display: inline-block;
        }
      }
    }
    .itemSub {
      margin: 15px 0 0 0;
      label {
        border-right: 1px solid #d9d9d9;
        margin: 0 20px 0 0;
        padding: 0 20px 0 0;
      }
      span {
        color: #3470ff;
        cursor: pointer;
        cursor: pointer;
        text-decoration: underline;
        padding: 0 0 0 0;
        i {
          //  background: url(/static/img/assets/img/review/ico_link.png) no-repeat center center / contain;
          background: url("../../../../../static/img/assets/img/review/ico_link.png")
            no-repeat center center / contain;
          width: 17px;
          height: 17px;
          display: inline-block;
        }
      }
    }
    .item:before {
      content: "";
      display: block;
      width: 6px;
      height: 6px;
      position: absolute;
      background: #409eff;
      left: 0;
      top: 0;
      border-radius: 50%;
      top: 50%;
      margin-top: -3px;
      left: 0;
    }
  }

  .formBox {
    padding: 0 0 0 20px;
  }
  .detail {
    width: 100%;
    border-bottom: 1px solid #d9d9d9;
    .deailItem {
      border: 1px solid red;
      border-bottom: 0;
      display: flex;
      > div {
        border-left: 1px solid red;
      }
      .name {
        width: 20%;
        background: #f7f8fa;
      }
      .con {
        width: 80%;
      }
      .name,
      .con {
        padding: 20px;
      }
    }
  }
  .detailNew table {
    width: 100%;
    font-size: 14px;
    color: #252525;
    border-width: 1px;
    border-color: #c9c9c9;
    border-collapse: collapse;
    th {
      background: #f7f8fa;
      border-width: 1px;
      padding: 8px;
      border-style: solid;
      border-color: #c9c9c9;
    }
    td {
      border-width: 1px;
      padding: 8px;
      border-style: solid;
      border-color: #c9c9c9;
    }
  }
}
/deep/ .el-form-item {
  margin-bottom: 15px;
}
/deep/ body .el-radio-group {
  margin-bottom: 0;
}
/deep/ .el-tabs__content {
  overflow: inherit;
}
</style>
