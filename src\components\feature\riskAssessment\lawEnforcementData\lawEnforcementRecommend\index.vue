<!-- 执法辅助推荐 -->
<template>
    <div class="law-recommend">
        <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px !important">
            <el-form-item prop="distCode">
                <el-select v-model="queryParams.distCode" size="small" placeholder="请选择行政区划"
                    style="min-width: 380px;width: auto;" multiple clearable>
                    <el-option v-for="item in district" :key="item.distCode" :label="item.distName"
                        :value="item.distCode">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="enterpriseType">
                <el-select v-model="queryParams.enterpriseType" size="small" placeholder="请选择企业类型" style="width: 380px"
                    multiple clearable>
                    <el-option v-for="item in entTypes" :key="item.id" :label="item.enterpriseType" :value="item.id">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop="processid">
                <el-select v-model="queryParams.processid" placeholder="请选择工艺" style="width: 380px" size="small"
                    multiple :clearable="true">
                    <el-option v-for="item in processIdsOption" :key="item.processid" :label="item.processname"
                        :value="item.processid">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item prop='dangerLevelCode'>
                <el-select v-model="queryParams.dangerLevelCode" placeholder="危险源等级" style="width: 280px" multiple
                    size="small" clearable>
                    <el-option v-for="item in dangerLevelOptions" :key="item.value" :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-select v-model="status" placeholder="请选择执法状态" style="width: 280px" size="small" @change="handleCheck"
                    :clearable="true">
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
                <span v-if="status == 4"> 近 <el-input v-model.trim="dayValue" type="number"
                        style="width: 80px"></el-input>天未检查</span>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" size="small" @click="handleRearch">查询</el-button>
                <el-button type="primary" size="small" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>
        <div class="table-main">
            <div class="table">
                <el-table :data="tableData" v-loading="loading"
                    :header-cell-style="{ background: '#F1F6FF', color: '#333' }" border style="width: 100%"
                    ref="multipleTable">
                    <el-table-column type="index" label="序号" width="50" align="center">
                    </el-table-column>
                    <el-table-column prop="companyName" label="企业名称" align="center" width="300">
                    </el-table-column>
                    <el-table-column prop="districtName" label="所在行政区划" align="center">
                       
                    </el-table-column>
                    <el-table-column prop="enterpriseType" label="企业类型" align="center">
                        <template slot-scope="scope">
                            <div>
                                {{ entTypeFn(scope.row.enterpriseType) }}
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column prop="companyCode" label="企业编码" align="center">
                    </el-table-column>
                    <el-table-column prop="respPer" label="企业负责人" align="center">
                    </el-table-column>
                    <el-table-column prop="respTel" label="负责人手机号" align="center">
                    </el-table-column>
                    <el-table-column prop="lawEnforcementCount" label="总执法次数" align="center">
                    </el-table-column>
                    <el-table-column prop="lawEnforcementDate" label="最近执法开始时间" align="center" width="180">    
                    </el-table-column>
                    <!-- <el-table-column label="操作" width="240" align="center">
                        <template slot-scope="scope">
                            <div>
                                <el-button type="text" @click="openDialog('read', scope.row)">查看</el-button>
                                <el-button type="text" @click="openDialog('edit', scope.row)"
                                    :disabled="scope.row.handleFlag == 0">编辑</el-button><el-button type="text"
                                    @click="deleteAccident(scope.row.id)"
                                    :disabled="scope.row.handleFlag == 0">删除</el-button>
                            </div>
                        </template>
                    </el-table-column> -->
                </el-table>
            </div>
            <div class="pagination">
                <el-pagination @current-change="handleCurrentChange" :current-page.sync="queryParams.page" background
                    layout="total, prev, pager, next" :total="total" v-if="total != 0">
                </el-pagination>
            </div>
        </div>
    </div>
</template>

<script>
import { mapGetters, createNamespacedHelpers } from "vuex";
const { mapGetters: mapGettersLogin } = createNamespacedHelpers("login");
import {
    getAccidentListData,
    getAccidentTypeListData,
} from "@/api/accidentManagement";

import { getSpotCheck } from "@/api/riskAssessment";
import {
    getSelectData,
    enterpriseType,
    getHazarchemList,
    getKnowledgeListData,
} from "@/api/entList";
import { parseTime } from "@/utils/index";
var dayjs = require("dayjs");

export default {
    name: "accidentCount",
    components: {
    },
    data() {
        return {

            loading: false, // 加载状态
            queryParams: {
                // 表格查询参数
                page: 1,
                pageSize: 10,
                dangerLevelCode: [], // 事故等级
                processid: [],  // 事故类型
                // districtCode: this.$store.state.login.userDistCode,
                distCode: [],
                startTime: "",
                endTime: "",
                enterpriseType: [],
            },
            tableData: [], // 表格数据
            total: 0,
            status: '', // 执法状态
            dayValue: "",
            statusOptions: [
                { label: "近一周未检查", value: "1" },
                { label: "近一月未检查", value: "2" },
                { label: "近三月未检查", value: "3" },
                { label: "其他", value: "4" },
            ],
            enListOption: [],
            dateValue: "", // 时间选择
            typeList: [], // 事故类型数据
            levelList: [], // 事故等级数据
            district: [], // 行政区划
            dangerSourceList: [], // 危险源数据
            hazarchemList: [], // 危化品数据
            processIdsOption: [], // 工序数据
            entTypes: [], // 企业类型
            typeOptions: [], // 事故类型
            // 危险源等级
            dangerLevelOptions: [

                { label: "一等", value: "1" },
                { label: "二等", value: "2" },
                { label: "三等", value: "3" },
                { label: "四等", value: "4" },
            ],
            // 工艺
            processOptions: [

                { label: "一阶", value: "1" },
                { label: "二阶", value: "2" },
                { label: "三阶", value: "3" },
                { label: "四阶", value: "4" },
            ],

        };
    },
    filters: {
        timeFn(val) {
            if (val) {
                return dayjs(val).format("YYYY-MM-DD HH:mm:ss");
            }
        },
    },

    methods: {
        entTypeFn(val) {
            if (val) {
                const item = this.entTypes.find(item => item.id == val);
                return item.enterpriseType;
            }
        },
        //获取企业类型
        async getEnterpriseType() {
            await enterpriseType({}).then((res) => {
                this.entTypes = res.data.data;
            });
        },
        // 获取工序列表np
        getProcessData() {
            getKnowledgeListData({
                processid: "",
                processname: "",
            }).then((res) => {
                if (res.data.status === 200) {
                    //processIdsOption
                    this.processIdsOption = res.data.data;
                }
            });

        },
        dateOptions(time) {
            return time.getTime() < Date.now() - 8.64e6;
        },
        handleCheck(val) {
            if (!val) {
                this.dayValue = "";
                this.queryParams.startTime = ""
                this.queryParams.endTime = ""
            }

        },
        handleRearch() {
            this.queryParams.page = 1;
            this.getAccidentList();
        },
        resetQuery() {
            this.$refs['queryForm'].resetFields();
            this.status = '';
            this.dayValue = "";
            this.handleRearch()
        },
        handleSelectionChange() { },

        // 分页查询
        handleCurrentChange(val) {
            this.queryParams.page = val;
            this.getAccidentList();
        },
        // 获取事故列表数据
        getAccidentList() {
            this.tableData = [];
            this.total = 0;
            if (this.status == '1') {
                this.queryParams.startTime = this.timeAgoFn(7)
                this.queryParams.endTime = this.timeAgoFn()
            } else if (this.status == '2') {
                this.queryParams.startTime = this.timeAgoFn(30)
                this.queryParams.endTime = this.timeAgoFn()
            } else if (this.status == '3') {
                this.queryParams.startTime = this.timeAgoFn(90)
                this.queryParams.endTime = this.timeAgoFn()
            } else if (this.status == '4') {
                this.queryParams.startTime = this.timeAgoFn(this.dayValue)
                this.queryParams.endTime = this.timeAgoFn()
            } else {
                this.queryParams.startTime = ""
                this.queryParams.endTime = ""
            }
            this.loading = true;
            let params = JSON.parse(JSON.stringify(this.queryParams));
            getSpotCheck(params).then((res) => {
                if (res.data.code == 0) {
                    this.tableData = res.data.data.records;
                    this.total = res.data.data.total;

                } else {
                    this.$message({
                        message: res.data.msg,
                        type: "warning",
                    });
                }
            }).finally(() => {
                this.loading = false;
            });
        },
        // 获取事故类型列表
        async getaccidentTypeList() {
            await getAccidentTypeListData().then((res) => {
                if (res.status === 200) {
                    this.typeList = res.data.data.typeCode;
                    this.levelList = res.data.data.accidentLevel;
                }
            });
        },
        // 时间改变执行方法
        searchTime(val) {
            if (val) {
                let date1 = new Date(val[0]);
                let dataTime1 = parseTime(date1, "{y}-{m}-{d}") + " 00:00:00";
                let date2 = new Date(val[1]);
                let dataTime2 = parseTime(date2, "{y}-{m}-{d}") + " 00:00:00";
                this.queryParams.startTime = dataTime1;
                this.queryParams.endTime = dataTime2;
            } else {
                this.dateValue = "";
                this.queryParams.startTime = "";
                this.queryParams.endTime = "";
            }
        },
        // 获取n天前日期时间
        timeAgoFn(val) {
            if (val) {
                return dayjs().subtract(val, 'day').format('YYYY-MM-DD HH:mm:ss')
            } else {
                return dayjs().format('YYYY-MM-DD HH:mm:ss')
            }
        },
    },
    async mounted() {
        const district = this.$store.state.controler.district;
        this.district = district.length > 0 ? district[0].children : [];
        this.getaccidentTypeList();
        await this.getEnterpriseType();
        this.getProcessData();
        this.getAccidentList();
    },
};
</script>

<style lang="scss" scoped>

.el-form-item{
    margin-bottom: 12px;
}

.table-main {
    background: #fff;

    .table-top {
        display: flex;
        justify-content: space-between;

        // padding: 10px 0;
        h2 {
            font-size: 18px;
            line-height: 32px;
            margin-bottom: 0;
        }
    }

    .pagination {
        margin-top: 30px;
        padding-bottom: 20px;
        display: flex;
        justify-content: flex-end;
    }
}
</style>
<style lang="scss">
.accident-form-input {
    // display: inline-block;
    width: 80px;
    margin-right: 10px;

    .el-input__inner {
        border: none;
        border-bottom: 1px solid #666;
        width: 80px;
        outline: none;
        height: 20px;
        border-radius: 0;
    }
}

.accident-form-address .el-form-item__content {
    margin-left: -20px !important;
}
</style>