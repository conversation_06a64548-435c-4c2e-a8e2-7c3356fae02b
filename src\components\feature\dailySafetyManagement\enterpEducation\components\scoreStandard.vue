<!-- 分值标准 -->
<template>
  <el-form :model="testInfo" ref="standardForm" size="small" :rules="rules" :inline="true">
    <div class="standard-container">
      <div class="label-box">分值标准：</div>
      <div class="standard-box">
        <div class="standard-item">
          <span class="text">不及格</span>
          <el-form-item prop="failNumMin">
            <el-input
              v-model.trim="testInfo.failNumMin"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
              
            />分
          </el-form-item>
          <span>--</span>
          <el-form-item prop="failNumMax">
            <el-input
              v-model.trim="testInfo.failNumMax"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
            />
            分
          </el-form-item>
        </div>
        <div class="standard-item">
          <span class="text">及格</span>
          <el-form-item prop="score">
            <el-input
              v-model.trim="testInfo.passNumMin"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              placeholder=""
              clearable
              size="small"
            />分
          </el-form-item>
          <span>--</span>
          <el-form-item prop="score">
            <el-input
              v-model.trim="testInfo.passNumMax"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
            />
            分
          </el-form-item>
        </div>
        <div class="standard-item">
          <span class="text">良好</span>
          <el-form-item prop="score">
            <el-input
              v-model.trim="testInfo.goodNumMin"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
              size="small"
            />分
          </el-form-item>
          <span>--</span>
          <el-form-item prop="score">
            <el-input
              v-model.trim="testInfo.goodNumMax"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
            />
            分
          </el-form-item>
        </div>
        <div class="standard-item">
          <span class="text">优秀</span>
          <el-form-item prop="score">
            <el-input
              v-model.trim="testInfo.excellentNumMin"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
              size="small"
            />分
          </el-form-item>
          <span>--</span>
          <el-form-item prop="score">
            <el-input
              v-model.trim="testInfo.excellentNumMax"
              style="width: 80px; margin-right: 5px"
              :disabled="disabled"
              clearable
              placeholder=""
            />
            分
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script>
export default {
  model: {
    prop: "testInfo",
    event: "change",
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
    testInfo: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    const numberValid = [
      {
        required: true,
        trigger: "blur",
        message: "请输入数字",
        pattern: /^[1-9]?\d$/,
      },
    ];
    return {
      timeOptions: [
        { value: "30", label: "30分钟" },
        { value: "45", label: "45分钟" },
        { value: "60", label: "60分钟" },
        { value: "90", label: "90分钟" },
      ],
      examinationTypes: [
        { label: "单选", value: "单选" },
        { label: "多选", value: "多选" },
      ],
      rules: {
        failNumMin: numberValid,
        failNumMax: numberValid,
      },
    };
  },
  methods: {
    updateForm() {
      return new Promise((resolve, reject) => {
        this.$refs.standardForm.validate((valid) => {
          if (valid) {
            this.$emit("change", this.testInfo);
            resolve(valid);
          } else {
            reject(false);
            return false;
          }
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.standard-container {
  height: 400px;
  overflow: auto;
  padding: 30px 20px 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  // justify-content: space-between;
  .label-box {
    width: 120px;
    line-height: 32px;
    margin-right: 10px;
  }

  .standard-item {
    width: 500px;
    line-height: 32px;
    font-weight: bold;
    .text {
      display: inline-block;
      width: 60px;
    }
    span {
      margin: 0 5px;
    }
  }
}
</style>
