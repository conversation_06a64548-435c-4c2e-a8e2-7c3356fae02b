<template>
  <div class="securityCommitments" v-loading="loading">
    <div class="header">
      <el-cascader
        v-show="this.$store.state.login.user.user_type == 'gov'"
        size="mini"
        placeholder="请选择地市/区县"
        :options="district"
        v-model="districtVal"
        :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
        clearable
        @change="handleChange"
        :show-all-levels="true"
        style="width: 190px"
      ></el-cascader>
      <el-input
        placeholder="请输入企业名称"
        style="width: 190px"
        size="mini"
        clearable
        v-model.trim="enterName"
      ></el-input>
      <el-select
        v-model="isCommit"
        placeholder="请选择是否承诺"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in isCommitOpt"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="isCommitAbnormal"
        placeholder="请选择是否承诺异常"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in isCommitAbnormalOpt"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getData()">查询</el-button>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      style="width: 100%"
      :height="500"
    >
      <el-table-column type="index" width="80" label="序号"></el-table-column>
      <el-table-column
        prop="distName"
        label="区划"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column prop="enterName" label="企业名称" show-overflow-tooltip>
              <template slot-scope="{row}">
        <span type="text" style="color: #3977ea; cursor: pointer;" @click="goEnt(row)">{{row.enterName}}</span>
      </template>
      </el-table-column>
      <el-table-column prop="ifCommit" label="承诺情况" width="110">
      </el-table-column>
      <el-table-column prop="ifCommitAbnormal" label="承诺是否异常" width="130">
      </el-table-column>
      <el-table-column
        prop="commitTime"
        label="承诺时间"
        width="180"
        show-overflow-tooltip
      >
      </el-table-column>
      <el-table-column
        prop="abnormalReason"
        label="承诺异常原因"
        show-overflow-tooltip
      >
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { postOnlinePatrolQuerySafetyCommitDetails } from "@/api/riskAssessment";
import {mapState } from "vuex"
export default {
  data() {
    return {
      tableData: [],
      district: this.$store.state.controler.district,
      currentPage: 1,
      total: 0,
      enterName: "",
      isCommit: "",
      isCommitAbnormal: "",
      isCommitOpt: [
        {
          value: "0",
          label: "已承诺",
        },
        {
          value: "1",
          label: "未承诺",
        },
      ],
      isCommitAbnormalOpt: [
        {
          value: "0",
          label: "是",
        },
        {
          value: "1",
          label: "否",
        },
      ],
      districtVal: this.districtProps,
      loading: true,
    };
  },
  props:["districtProps","isCommitProps","isCommitAbnormalProps"],
  watch: {
    districtProps: {
      handler(newVal, oldVal) {
        this.districtVal = newVal;
      },
            immediate:true,
      deep:true
    },
    isCommitProps:{
      handler(newVal,oldVal){
        this.isCommit = newVal;
      },
            immediate:true,
      deep:true
    },
    isCommitAbnormalProps:{
      handler(newVal,oldVal){
        this.isCommitAbnormal = newVal;
      },
      immediate:true,
      deep:true
    }
  },
  computed:{
    ...mapState({
      // userDistCode:'login/userDistCode'
      userDistCode:state=>state.login.userDistCode
    })
  },
  methods: {
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterPid);
    },
        closeTable(){
      this.tableData = [];
      this.currentPage = 1;
      this.total = 0;
            this.enterName= "";
      this.isCommit="";
      this.isCommitAbnormal="";
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.getData();
    },
    getData() {
      this.loading = true;
      postOnlinePatrolQuerySafetyCommitDetails({
        distParentCode:this.userDistCode,
        distCode: this.districtVal,
        current: this.currentPage,
        size: 10,
        // distCode:this.districtVal,
        enterName: this.enterName,
        isCommit: this.isCommit,
        isCommitAbnormal: this.isCommitAbnormal,
      }).then((res) => {
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.securityCommitments {
  .header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    & > * {
      margin-right: 20px;
    }
  }
  .pagination {
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>