<template>
  <div class="enterpriseManagement">
    <div>
      <div class="header">
        <div class="breadcrumb">
          <a-breadcrumb separator="–">
            <a-breadcrumb-item>
              <span class="icon-box">
                <a-icon type="home" theme="filled" class="icon" /> 安全风险预警
              </span>
            </a-breadcrumb-item>
          </a-breadcrumb>
        </div>
      </div>
      <div class="seach-part">
        <div class="l">
          <!-- {{district}} -->
          <el-cascader
            size="mini"
            placeholder="请选择行政区划"
            :options="district"
            v-model="districtVal"
            :props="{
              checkStrictly: true,
              value: 'distCode',
              label: 'distName',
              children: 'children',
              emitPath: false,
            }"
            clearable
            :show-all-levels="true"
            v-if="isShowDist && showArea"
          ></el-cascader>
          <el-select
            v-model="rank"
            size="mini"
            placeholder="请选择最高预警等级"
            clearable
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="warnStatus"
            size="mini"
            placeholder="请选择预警状态"
            clearable
          >
            <el-option
              v-for="item in options1"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-date-picker
            v-model="date"
            size="mini"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            clearable
          >
          </el-date-picker>
          <el-button type="primary" size="mini" @click="getData()"
            >查询</el-button
          >
          <CA-button type="primary" size="mini" plain @click="exportExcel()"
            >导出</CA-button
          >
        </div>
      </div>
      <div class="table-main">
        <div class="table-top">
          <h2>安全风险预警列表</h2>

          <el-button type="primary" size="mini" @click="clickPath"
            >查看分析报告</el-button
          >
        </div>
        <div>
          <div class="table">
            <el-table
              :data="tableData.list"
              v-loading="loading"
              style="width: 100%"
              :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
              border
              ref="multipleTable"
              @selection-change="handleSelectionChange"
              :default-sort="{ prop: 'date', order: 'descending' }"
              @select="select"
              @select-all="select"
            >
              <el-table-column type="selection" width="50" align="center">
              </el-table-column>
              <el-table-column
                type="index"
                label="序号"
                width="50"
                align="center"
              >
              </el-table-column>
              <el-table-column
                prop="companyName"
                label="单位名称"
                min-width="180"
                align="center"
                :show-overflow-tooltip="true"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span
                    @click="goEnt(row)"
                    style="color: #3977ea; cursor: pointer"
                  >
                    {{ row.companyName }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column
                :label="isShowDist == true ? '行政区划' : '归属园区'"
                width="120"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <span v-if="isShowDist == true">{{ row.distName }}</span>
                  <span v-else>{{ park.parkName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="最高预警等级" width="120" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.highestRankLevel }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="触发黄色预警时间"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.yellowWarningTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="触发橙色预警时间"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.orangeWarningTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column
                label="触发红色预警时间"
                min-width="110"
                align="center"
              >
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.redWarningTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="消警时间" min-width="110" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.endTime }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="消警状态" width="90" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.warnStatus }}
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="企业反馈状态" width="90" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    {{ row.isFeedBackCode == "0" ? "未反馈" : "已反馈" }}
                    <!-- {{row.isFeedBackCode}} -->
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="100" align="center">
                <template slot-scope="{ row, column, $index, store }">
                  <div>
                    <el-button
                      type="text"
                      v-if="row.isFeedBackCode == '0' ? true : false"
                      @click="disposalSituationBool(row)"
                      v-show="user.user_type == 'ent'"
                      >反馈</el-button
                    >
                    <el-button
                      v-if="
                        row.warnStatusCode == '0' &&
                        user.user_type == 'gov' &&
                        $store.state.login.user.isDanger == '1'
                      "
                      type="text"
                      @click="NotificationBool(row)"
                      >通报</el-button
                    ><el-button type="text" @click="WarnBool(row)"
                      >详情</el-button
                    >
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="pagination">
            <el-pagination
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="tableData.size"
              layout="total, prev, pager, next"
              background
              :total="tableData.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <Notification ref="Notification"></Notification>
    <Warn ref="Warn"></Warn>
    <div class="disposalSituation">
      <el-dialog
        title="处置情况报告"
        :visible.sync="disposalSituationShow"
        width="1000px"
        top="10vh"
        :destroy-on-close="true"
        v-dialogDrag
        :close-on-click-modal="false"
      >
        <div class="div1" v-loading="receivingUnit.length <= 0 ? true : false">
          <div class="table">
            <ul class="container">
              <li class="lang">
                <div class="l">报告标题</div>
                <div class="r">
                  {{ title }}
                </div>
              </li>
              <li class="lang">
                <div class="l">接收单位</div>
                <div class="r" ref="Unit">
                  <span v-for="(item, index) in receivingUnit" :key="index"
                    >{{ item }}；</span
                  >
                </div>
              </li>
              <li class="lang">
                <div class="l"><span style="color: red">*</span>原因说明</div>
                <div class="r">
                  <el-input
                    cols="5"
                    type="textarea"
                    resize="none"
                    class="textarea"
                    step
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    placeholder="请输入原因说明"
                    v-model.trim="reasonDescription"
                    maxlength="500"
                    show-word-limit
                    clearable
                  ></el-input>
                </div>
              </li>
              <li class="lang">
                <div class="l"><span style="color: red">*</span>处置措施</div>
                <div class="r">
                  <el-input
                    cols="5"
                    type="textarea"
                    resize="none"
                    class="textarea"
                    step
                    :autosize="{ minRows: 2, maxRows: 6 }"
                    placeholder="请输入处置措施"
                    v-model.trim="notificationContent"
                    maxlength="500"
                    show-word-limit
                    clearable
                  ></el-input>
                </div>
              </li>
              <li class="lang bottom">
                <div class="l">发送单位</div>
                <div class="r">{{ user.org_name }}</div>
              </li>
            </ul>
            <el-button
              class="submit"
              size="medium"
              type="primary"
              @click="addCimEarlyWarning()"
              :disabled="disabled"
              >提交</el-button
            >
          </div>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  postCimEarlyWarningPushList,
  cimEarlyWarningExportExcel,
  getSelCompany,
  postCimEarlyWarningFeedBackAdd,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";
import Notification from "./notification";
import Warn from "./warn";
import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {
    Notification,
    Warn,
  },
  data() {
    return {
      tableCheck: true,
      loading: false,
      areaName: "",
      tableData: {},
      options: [
        {
          value: "1",
          label: "红色预警",
        },
        {
          value: "2",
          label: "橙色预警",
        },
        {
          value: "3",
          label: "黄色预警",
        },
      ],
      options1: [
        {
          value: "1",
          label: "已消警",
        },
        {
          value: "0",
          label: "未消警",
        },
      ],
      value: "",
      warnStatus: "",
      rank: "",
      showBreak: false,
      distCode: this.$store.state.login.userDistCode,
      currentPage: 1,
      selection: [],
      district: this.$store.state.controler.district,
      districtVal: this.$store.state.login.userDistCode,
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (720 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      // date:"",
      startDate: "",
      endDate: "",
      showArea: true,
      disposalSituationShow: false,
      receivingUnit: [],
      reasonDescription: "",
      notificationContent: "",
      nowtime: new Date(new Date()).Format("yy年MM月dd日 hh时mm分"),
      park: "",
      listId: "",
      title: "风险预警信息核查、处置情况报告",
      disabled: false,
    };
  },
  methods: {
    clickPath() {
      this.$router.push({ path: "/riskAssessment/monitorWarnReport" });
    },
    disposalSituationBool(row) {
      this.disposalSituationShow = true;
      this.listId = row.id;
      getSelCompany(row.id)
        .then((res) => {
          this.receivingUnit = res.data.data.receivingUnit;
        })
        .catch((error) => {
          reject(error);
        });
    },
    addCimEarlyWarning() {
      if (!this.reasonDescription) {
        Message({
          message: "原因说明不能为空",
          type: "info",
        });
        return;
      } else if (!this.notificationContent) {
        Message({
          message: "处置措施不能为空",
          type: "info",
        });
        return;
      }
      this.disable = true;
      postCimEarlyWarningFeedBackAdd({
        warningId: this.listId,
        title: this.title,
        reasonDescription: this.reasonDescription,
        notificationContent: this.notificationContent,
        receivingUnit: this.receivingUnit.join(","),
      })
        .then((res) => {
          this.disable = false;
          this.disposalSituationShow = false;
          this.receivingUnit = [];
          this.reasonDescription = "";
          this.notificationContent = "";
          if (res.data.status == 200) {
            Message({
              message: "反馈成功",
              type: "success",
            });
            this.getData();
          } else {
            Message.error("反馈失败");
          }
        })
        .catch((error) => {
          this.disable = false;
          this.disposalSituationShow = false;
          Message.error("反馈失败");
        });
    },
    //填报信息
    NotificationBool(row) {
      this.$refs.Notification.closeBoolean(true);
      this.$refs.Notification.getData(row);
      this.$refs.Notification.preData = row;
    },
    WarnBool(row) {
      this.$refs.Warn.closeBoolean(true);
      console.log(row);
      this.$refs.Warn.getData(row.id, row.companyCode, row.companyName);
      this.$refs.Warn.preData = row;
    },
    searchTime(value) {
      if (value) {
        this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
        this.endDate = new Date(
          new Date(this.date[1].getTime() + 86399900)
        ).Format("yy-MM-dd hh:mm:ss");
      } else {
        this.startDate = "";
        this.endDate = "";
      }
    },
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.companyCode);
    },
    getData() {
      if (this.$store.state.login.user.user_type == "ent") {
        this.showArea = false;
        getEnt({}).then((res) => {
          if (res.data.code == 0) {
            // debugger;
            // this.enterpid= res.data.data.enterpId;
            this.getDataes([res.data.data.enterpId]);
          }
        });
      } else {
        this.getDataes(null);
      }
    },
    getDataes(id) {
      this.loading = true;
      postCimEarlyWarningPushList({
        // companyName: "string",
        companyCode: id ? id.toString() : "",
        distCode: this.districtVal,
        startDate: this.date
          ? new Date(this.date[0]).Format("yy-MM-dd") + " 00:00:00"
          : "",
        endDate: this.date
          ? new Date(this.date[1]).Format("yy-MM-dd") + " 23:59:59"
          : "",
        //
        // parkId: "string",
        rank: this.rank,
        warnStatus: this.warnStatus,
        pageSize: 10,
        nowPage: this.currentPage,
      }).then((res) => {
        this.tableData = res.data.data;
        this.loading = false;
      });
    },
    // 导出
    exportExcel() {
      console.log(32);
      cimEarlyWarningExportExcel({
        // companyName: "string",
        distCode: this.districtVal,
        startDate: this.startDate, // || new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss")
        endDate: this.endDate, // || new Date(this.date[1]).Format("yy-MM-dd hh:mm:ss")
        //
        // parkId: "string",
        idList: this.selection,
        rank: this.rank,
        warnStatus: this.warnStatus,
        size: 10,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "安全风险预警列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        // debugger
        this.selection[i] = selection[i].id;
      }
    },

    openDialog(distCode, type, areaName) {
      this.$refs.DetailTable.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTable.getEntData(distCode, type, areaName);
      this.$refs.DetailTable.getDistrict();
    },
    openDialoges(distCode, type, areaName) {
      this.$refs.DetailTablees.closeBoolean(true);
      distCode = this.isShowDist ? distCode : null;
      this.$refs.DetailTablees.getEntData(distCode, type, areaName);
      this.$refs.DetailTablees.getDistrict();
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleCurrentChange(val) {
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getData();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      padding: 10px 0;
      align-items: center;

      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
.disposalSituation {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
