<template>
  <div id="storageTank" class="storageTank">
    <div class="header">
      <div class="operation">
        <h2 style="margin-bottom: 0">储罐列表</h2>
        <div class="inputBox">
          <el-select v-model="searchParam.hazardId" style="margin-right: 10px"
            size="small"
            clearable
            placeholder="全部重大危险源">
            <el-option v-for="item in dangerIdIsNotNullListDataList"
              :key="item.dangerid"
              :label="item.dangername"
              :value="item.dangerid" />
            </el-option>
          </el-select>
          <el-input v-model.trim="searchParam.keyword" style="margin-right: 10px"
            placeholder="请输入储罐名称"
            class="input"
            size="small"
            clearable></el-input>
          <el-select v-model="searchParam.tankType" style="margin-right: 10px"
            size="small"
            clearable
            placeholder="请选择储罐类型">
            <el-option v-for="item in tankTypeData"
              :key="item.value"
              :label="item.name"
              :value="item.value">
            </el-option>
          </el-select>
          <el-button size="small"
            type="primary"
            @click="search()">查询</el-button>
        </div>
        <div class="btnBox">
          <!--          <el-button size="mini" type="primary" @click="addEdit"-->
          <!--            >新增</el-button-->
          <!--          >-->
          <!-- <el-button size="mini" v-if="$store.state.login.user.user_type != 'ent'" style="margin-right:10px;" type="primary">批量删除</el-button> -->
          <CA-button class="export"
            plain
            size="small"
            type="primary"
            @click="exportExcel">导出
          </CA-button>
        </div>
      </div>
    </div>
    <div class="table" v-loading="loading">
      <el-table ref="multipleTable"
        :data="tableData"
        :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
        border
        style="width: 100%"
        height="530px">
        <!-- <el-table-column
          align="center"
          fixed="left"
          type="selection"
          width="40"
        >
        </el-table-column> -->
        <el-table-column align="center"
          label="序号"
          type="index"
          width="55">
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
          align="center"
          label="所属重大危险源"
          width="150"
          prop="hazardIdName">
        </el-table-column>
        <el-table-column align="center120"
          label="储罐名称"
          prop="tankName"           
          width="150"                
          show-overflow-tooltip
         >
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
          align="center"
          label="储罐类型"
          width="120"
          prop="tankTypeName">
        </el-table-column>
        <el-table-column align="center"
          label="存储化学品名称"
          width="150"
          prop="msdstitle">
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true"
          align="center"
          label="最大贮存量(m³)"
          width="150"
          prop="maxStorage">
        </el-table-column>
        <el-table-column align="center"
          label="日常贮存量(m³)"
          width="150"
          prop="dailyStorage">
        </el-table-column>
        <el-table-column align="center"
          label="化学品状态"
          prop="chemicalStatusName">
        </el-table-column>
        <el-table-column align="center"
          label="最近检修日期"
          prop="updateTime"
          width="150">
          <template slot-scope="scope">
            <span style="color: rgb(57, 119, 234); cursor: pointer"
              @click="enterDetail(scope.row)">{{ scope.row.lastMaintenanceTime ? new Date(scope.row.lastMaintenanceTime).Format("yyyy-MM-dd") : "" }}
            </span>
          </template>
        </el-table-column>
        <el-table-column align="center"
          label="操作"
          width="160">
          <template slot-scope="scope">
            <span @click="view(scope.row)"
                  style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              ">详情</span>
            <span @click="edit(scope.row)"
                  style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  v-if="$store.state.login.user.user_type === 'ent'">编辑</span>
            <!--            <span-->
            <!--              @click="deleter(scope.row.id)"-->
            <!--              style="-->
            <!--                color: rgb(57, 119, 234);-->
            <!--                margin-right: 10px;-->
            <!--                cursor: pointer;-->
            <!--              "-->
            <!--              v-if="$store.state.login.user.user_type === 'ent'"-->
            <!--              >删除</span-->
            <!--            >-->
            <span style="
                color: rgb(57, 119, 234);
                margin-right: 10px;
                cursor: pointer;
              "
                  @click="enterInput(scope.row)"
                  v-if="$store.state.login.user.user_type === 'ent'">录入</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination v-if="total != 0"
                     :current-page.sync="currentPage"
                     :total="total"
                     :page-size='size'
                     background
                     layout="total, prev, pager, next"
                     @size-change="handleSizeChange"
                     @current-change="handleCurrentChange">
      </el-pagination>
    </div>

    <!-- 新增编辑查看 -->
    <div  v-if='open'>
    <el-dialog :title="title"
             :visible.sync="open"
               width="1100px"
               :close-on-click-modal="false"
               :append-to-body="false"
               :before-close="beforeClose"
               top="5vh">
      <div class="dialog"
           v-loading="fromLoading">
        <el-form ref="form"
                 :model="form"
                 :rules="rules"
                 label-width="150px">
          <div class="form_item">
            <h2 class="form_title">基本信息
              <el-tag  size="small" type="danger" v-show='form.lifeAlarm ==1' effect="dark">寿命告警</el-tag>
               <el-tag  size="small" v-show='form.lifeAlarm ==0'>寿命正常</el-tag>
                    <!-- <el-tooltip class="item" effect="dark" content="寿命告警标识" placement="top-start" >
                          <el-tag  size="small" type="danger" v-show='form.lifeAlarm ==1' effect="dark">告警</el-tag>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="寿命告警标识" placement="top-start">
                          <el-tag v-show='form.lifeAlarm ==0' size="small">未告警</el-tag>
                    </el-tooltip> -->
                
            </h2>
            <div class="form_main">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="所属重大危险源">
                    <el-select v-model="form.hazardId"
                               placeholder="请选择所属重大危险源"
                               disabled>
                      <el-option v-for="item in dangerIdIsNotNullListDataList"
                                 :key="item.dangerid"
                                 :label="item.dangername"
                                 :value="item.dangerid" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设备编码">
                    <el-input v-model.trim="form.tankNum"
                              disabled
                              placeholder="设备编码" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="储罐名称">
                    <el-input v-model.trim="form.tankName"
                              disabled
                              placeholder="储罐名称" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="储罐材质">
                    <el-input v-model.trim="form.tankMaterial"
                    maxlength="30"
                              :disabled="dialogType === 'add'"
                              placeholder="储罐材质" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="最大储存量">
                    <el-input v-model.trim="form.maxStorage"
                              disabled
                              placeholder="最大储存量">
                      <template slot="append">立方米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="储量">
                    <el-input v-model.trim="form.dailyStorage"
                     maxlength="10"
                              :disabled="dialogType === 'add'"
                              placeholder="储量">
                      <template slot="append">立方米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="是否为压力容器">
                    <el-select v-model="form.isPressure"
                               placeholder="是否为压力容器"
                               disabled>
                      <el-option label="是"
                                 value="1"></el-option>
                      <el-option label="否"
                                 value="0"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设计压力">
                    <el-input v-model.trim="form.designPressure"
                              placeholder="请输入设计压力"
                              disabled>
                      <template slot="append">Mpa</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="最高工作压力">
                    <el-input v-model.trim="form.maximumWorkingPressure"
                              placeholder="请输入最高工作压力"
                              disabled>
                      <template slot="append">Mpa</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="实际工作压力">
                    <el-input v-model.trim="form.actualPressure"
                              disabled
                              placeholder="请输入实际工作压力">
                      <template slot="append">Mpa</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <!-- <el-radio v-model="radio" label="1">备选项</el-radio>
  <el-radio v-model="radio" label="2">备选项</el-radio> -->
              <!-- <el-col :span="8">
                  <el-form-item label="寿命告警标识">
                    <el-radio v-model="form.lifeAlarm" label="0">不告警</el-radio>
                  <el-radio v-model="form.lifeAlarm" label="1">告警</el-radio>
                  </el-form-item>
                </el-col> -->

                <!-- 寿命告警标识 -->
                <el-col :span="8">
                  <el-form-item label="设计使用年限">
                    <el-input v-model.trim="form.designLife"
                    maxlength="4"
                              :disabled="dialogType === 'add'"
                              placeholder="请输入设计使用年限">
                      <template slot="append">年</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="实际使用年限">
                    <el-input v-model.trim="form.actualLife"
                              disabled
                              placeholder="请输入实际使用年限">
                      <template slot="append">年</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="罐类型">
                    <el-select v-model="form.tankType"
                               placeholder="请选择罐类型"
                               disabled>
                      <el-option v-for="item in tankTypeData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="压力类型">
                    <el-select v-model="form.pressuretype"
                               placeholder="请选择压力类型"
                               disabled>
                      <el-option v-for="item in pressuretypeData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="温度类型">
                    <el-select v-model="form.temperatureType"
                               placeholder="请选择温度类型"
                               disabled>
                      <el-option v-for="item in temperaturTypeData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="设计温度低限">
                    <el-input v-model.trim="form.designTemperatureLow"
                              disabled
                              placeholder="请输入设计温度低限" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="设计温度高限">
                    <el-input v-model.trim="form.designTemperatureHigh"
                              disabled
                              placeholder="请输入设计温度高限" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="液位最高值">
                    <el-input v-model.trim="form.maximumLiquidLevel"
                              disabled
                              placeholder="请输液位最高值" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="储罐体积">
                    <el-input v-model.trim="form.tankVolume"
                              :disabled="dialogType === 'add'"
                              placeholder="请输入储罐体积">
                      <template slot="append">立方米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="储罐半径">
                    <el-input v-model.trim="form.tankRadius"
                    maxlength="6"
                              :disabled="dialogType === 'add'"
                              placeholder="请输入储罐半径">
                      <template slot="append">米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="储罐高度">
                    <el-input v-model.trim="form.tankHeight"
                     maxlength="6"
                              :disabled="dialogType === 'add'"
                              placeholder="请输入储罐高度">
                      <template slot="append">米</template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item label="地址">
                    <el-input v-model.trim="form.address"
                    maxlength="45"
                              :disabled="dialogType === 'add'"
                              placeholder="请选择地址" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="经度">
                    <el-input v-model.trim="form.longitude"                    
                    maxlength="10"
                              disabled
                              placeholder="请输入经度" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="纬度">
                    <el-input v-model.trim="form.latitude"
                     maxlength="10"
                             disabled
                              placeholder="请输入纬度" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="高程">
                    <el-input v-model.trim="form.altitude"
                     maxlength="5"
                              :disabled="dialogType === 'add'"
                              placeholder="请输入高程">
                    <template slot="append">米</template>
                              </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="map_height">
                    <el-form-item label="地图定位">
                      <egisMap :islistener="false"
                               :isdetail="disabled"
                               ref="detailMap"
                               :datas="form"
                               style="height: 200px"
                               @mapCallback="mapcallback"></egisMap>
                    </el-form-item>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="form_item">
            <h2 class="form_title">存储化学品信息</h2>
            <div class="form_main">
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="存储介质">
                    <el-input v-model.trim="form.msdstitle"
                        placeholder="请输入CAS号"
                        disabled />
                    <!-- <CASelect v-model="form.msdsid"
                      :default-selected-str="options"
                      :clearable="true"
                      :multiple="true"
                      search-key="keyword"
                      @selectedChange="handleSelect"
                      disabled /> -->
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="CAS号">
                    <el-input v-model.trim="form.msdsid"
                              placeholder="请输入CAS号"
                              disabled />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="介质形态">
                    <!-- <el-input
                                    v-model="form.chemicalStatus"
                                    placeholder="请输入介质形态"
                                /> -->
                    <el-select v-model="form.chemicalStatus"
                               placeholder="请选择介质形态"
                               disabled>
                      <el-option v-for="item in dangerIdIsNotNullListData"
                                 :key="item.value"
                                 :label="item.name"
                                 :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="8">
                  <el-form-item label="是否重点监管">
                    <span>{{ form.supervisionFlag == 1 ? "是" : "否" }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8"> </el-col>
                <el-col :span="8"> </el-col>
              </el-row>
            </div>
          </div>
        </el-form>
      </div>
      <div v-if="dialogType === 'edit'"
           slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="submitForm"
                   :loading="btnLoading">保 存</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    </div>

    <!-- 录入 -->
    <el-dialog :append-to-body="true"
               :close-on-click-modal="false"
               :visible.sync="opens"
               v-if='opens'
               title="录入检维修记录"
               width="900px">
      <el-form ref="formData"
               :model="formData"
               label-width="150px">
        <div class="form_item">
          <div class="form_main">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item prop="dangerId"
                              label="所属重大危险源">
                  <el-select v-model="formData.hazardId"
                             placeholder="请选择所属重大危险源"
                             @change="changeDanger"
                             style="width: 270px"
                             :disabled="true">
                    <el-option v-for="item in dangerIdIsNotNullListDataList"
                               :key="item.dangerid"
                               :label="item.dangername"
                               :value="item.dangerid" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="危险源编码">
                  <el-input v-model.trim="formData.hazardId"
                            :disabled="true"
                            placeholder="请输入危险源编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设备名称"
                              prop="tankName">
                  <el-input :disabled="true"
                            placeholder="请选择设备名称"
                            v-model.trim="formData.tankName"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设备编码">
                  <el-input :disabled="true"
                            v-model.trim="formData.tankNum"
                            placeholder="请输入设备编码" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="上次检维修日期">
                  <el-input :disabled="true"
                            v-model.trim="formData.lastMaintenanceTime"
                            style="width: 270px" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="检维修日期"
                              prop="maintenanceTime"
                              :rules="{
                    required: true,
                    message: '检维修日期不能为空',
                    trigger: 'blur',
                  }">
                  <el-date-picker v-model="formData.maintenanceTime"
                                  type="date"
                                  placeholder="请输入检维修日期"
                                  style="width: 270px"
                                  value-format="yyyy-MM-dd">
                  </el-date-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作负责人"
                              prop="operator"
                              
                              :rules="{
                    required: true,
                    message: '操作负责人不能为空',
                    trigger: 'blur',
                  }">
                  <el-input maxlength="30" v-model.trim="formData.operator"
                            placeholder="请输入操作负责人" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系方式"
                              prop="contact"
                              :rules="[{
                    required: true,
                    message: '联系方式不能为空',
                    trigger: 'blur',
                  },
                   {
                    pattern: /^1(?:3\d|4[4-9]|5[0-35-9]|6[67]|7[013-8]|8\d|9\d)\d{8}$/,
                    message: '请输入有效的电话号码'
                   }
                  ]">
                  <el-input v-model.trim="formData.contact"
                            placeholder="请输入联系方式" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="更换备品清单"
                              prop="replacementContent"
                              :rules="{
                    required: true,
                    message: '更换备品清单不能为空',
                    trigger: 'blur',
                  }">
                  <el-input v-model.trim="formData.replacementContent"
                            :rows="4"
                            maxlength="500"
                            placeholder="请输入更换备品清单"
                            show-word-limit
                            type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="检维修内容"
                              prop="maintenanceContent"
                              :rules="{
                    required: true,
                    message: '检维修内容不能为空',
                    trigger: 'blur',
                  }">
                  <el-input v-model.trim="formData.maintenanceContent"
                            :rows="4"
                            maxlength="500"
                            placeholder="请输入检维修内容"
                            show-word-limit
                            type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-form>
      <div slot="footer"
           class="dialog-footer">
        <el-button type="primary"
                   @click="submit"
                   :loading="btnLoading">保 存</el-button>
        <el-button @click="cancels">取 消</el-button>
      </div>
    </el-dialog>
    <repairDialog ref="repairDialog"
                  :form="formDataRepair"
                  v-model="openes"
                  :viewForm="formList"
                  @handleCurrentChange="handleMaintenance"></repairDialog>
  </div>
</template>
<script>
import {
  getStorageTankListData,
  addStorageTankListData,
  editStorageTankListData,
  deleteStorageTankListData,
  datailStorageTankListData,
  getDangerIdIsNotNullListDatas,
  getDangerIdIsNotNullTypeData,
  getDangerIdIsNotNullListData,
  maintenanceSave,
  getmaintenanceSave,
  getStorageTankListAllData,
  exportStorageTankListData,
} from "@/api/equipmentAndFacilities";
import { parseTime } from "../../../../utils";
import { mapState } from "vuex";
import repairDialog from "./repairDialog";
import { getmaintenanceList } from "../../../../api/equipmentAndFacilities";

const mapConfig = require("@/assets/json/map.json");
export default {
  //import引入的组件s
  name: "storageTank",
  components: {
    repairDialog,
  },
  data() {
    var validatelongitude = (rule, value, callback) => {
      // 校验经度
      const reg =
        /^(\-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,9})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,9}|180)$/;
      if (value !== "" && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error("经度整数部分为0-180,小数部分为0到9位!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    var validatelatitude = (rule, value, callback) => {
      // 校验纬度
      const reg = /^(\-|\+)?([0-8]?\d{1}\.\d{0,9}|90\.0{0,9}|[0-8]?\d{1}|90)$/;
      if (value !== "" && value !== null && value !== undefined) {
        if (!reg.test(value)) {
          callback(new Error("纬度整数部分为0-90,小数部分为0到9位!"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    };
    return {
      fromLoading: false,
      loading: false,
      btnLoading: false,
      formDataRepair: {},
      formList: {},
      open: false,
      opens: false,
      openes: false,
      isDanger: true,
      dangerIdIsNotNullListDataList: [],
      title: "编辑储罐信息",
      dialogType: "",
      tankTypeData: [],
      pressuretypeData: [],
      temperaturTypeData: [],
      dangerIdIsNotNullListData: [],
      allShebeiNameData: [],
      currentPage: 1,
      size: 11,
      selection: [],
      total: 0,
      tableData: [],
      options: [],
      form: {
        tankArea: "",
        seq: "",
        tankName: "",
        tankMaterial: "",
        maxStorage: "",
        dailyStorage: "",
        isPressure: "",
        designPressure: "",
        maximumWorkingPressure: "",
        actualPressure: "",
        designLife: "",
        actualLife: "",
        tankType: "",
        pressuretype: "",
        baseChemicalName: "",
        temperaturType: "",
        designTemperatureLow: "",
        designTemperatureHigh: "",
        maximumLiquidLevel: "",
        tankVolume: "",
        tankRadius: "",
        tankHeight: "",
        address: "",
        longitude: mapConfig.map.defaultExtent.center[0],
        latitude: mapConfig.map.defaultExtent.center[1],
        altitude: "0",
        companyChemicalId: "",
        casCode: "",
        chemicalStatus: "",
        isImportant: "",
        type: 0,
        hazardId: "",
        // lifeAlarm:''
      },
      nowPage: 1,
      knowledgeNowPage: 1,
      maintenanceListProps: {},
      formData: {
        contact: "",
        facilityId: "",
        dangerId: "",
        upmaintenanceTime: "",
        maintenanceContent: "",
        maintenanceId: "",
        maintenanceTime: "",
        operator: "",
        replacementContent: "",
      },
      rules: {
        longitude: [
          {
            required: true,
            validator: validatelongitude,
            trigger: ["change", "blur"],
          },
        ],
        latitude: [
          {
            required: true,
            validator: validatelatitude,
            trigger: ["change", "blur"],
          },
        ],
        altitude: [
          {
            required: true,
            pattern: /^\d{1,6}(\.\d{1,6})?$/,
            message: "请输入正确的高程",
            trigger: ["change", "blur"],
          },
        ],
      },
      enterpriseId: "",
      disabled: false,
      searchParam: {
        // 搜索
        hazardId: "", // 所属危险源
        keyword: "", // 关键字
        tankType: "", // 罐类型
      },
    };
  },
  //方法集合
  methods: {
    beforeClose(){
      this.open = false;
    },
    // changeLongitude(val){
    //   debugger
    // },
    handleMaintenance(row) {
      this.getmaintenanceListFun(this.formList, row.currentPage);
    },
    changeDanger(event, item) {
      if (event) {
        this.isDanger = false;
        this.formData.dangerId = event;
        this.getAllName(event);
      } else {
        this.isDanger = true;
      }
    },
    getAllName(danderId) {
      getStorageTankListAllData({
        hazardId: danderId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.allShebeiNameData = res.data.data;
        }
      });
    },
    enterDetail(row) {
      // console.log(row);
      this.openes = true;
      this.formList = row;
      this.getmaintenanceListFun(row);
    },
    getmaintenanceSaveFun() {
      getmaintenanceSave({
        maintenanceId: row.id,
      }).then((res) => {
        if (res.data.status === 200) {
          this.formData = res.data.data;
        }
      });
    },
    getmaintenanceListFun(row, nowPage) {
      this.$nextTick(() => {
        this.$refs.repairDialog.loading = true;
      });
      getmaintenanceList({
        facilityId: row.id,
        maintenanceId: row.maintenanceId,
        nowPage: nowPage || 1,
        pageSize: 10,
      }).then((res) => {
        this.$nextTick(() => {
          this.$refs.repairDialog.loading = false;
          if (res.data.status === 200) {
            this.formDataRepair = res.data.data;
          }
        });
      });
    },
    getData(id) {
      this.enterpriseId = id;
      this.loading = true;
      var orgCode = "";
      getStorageTankListData({
        nowPage: this.currentPage,
        // orgCode: this.user.org_code,
        orgCode: id,
        pageSize: this.size,
        hazardId: this.searchParam.hazardId, // 所属危险源
        keyword: this.searchParam.keyword, // 关键字
        tankType: this.searchParam.tankType, // 罐类型
      }).then((res) => {
        this.loading = false;
        if (res.data.status === 200) {
          this.total = res.data.data.total;
          this.tableData = res.data.data.list;
        }
      });
    },
    //重大危险源
    getDangerIdIsNotNullListDataList(id) {
      getDangerIdIsNotNullListDatas({ enterpid: id }).then((res) => {
        if (res.data.status === 200) {
          this.dangerIdIsNotNullListDataList = res.data.data;
        }
      });
    },
    getDangerIdIsNotNullListDataType() {
      getDangerIdIsNotNullTypeData({
        dicCode: "TANK_TYPE",
      }).then((res) => {
        if (res.data.status === 200) {
          this.tankTypeData = res.data.data;
        }
      });
      getDangerIdIsNotNullTypeData({
        dicCode: "PRESSURE_TYPE",
      }).then((res) => {
        if (res.data.status === 200) {
          this.pressuretypeData = res.data.data;
        }
      });
      getDangerIdIsNotNullTypeData({
        dicCode: "TEMPERATURE_TYPE",
      }).then((res) => {
        if (res.data.status === 200) {
          this.temperaturTypeData = res.data.data;
        }
      });
      getDangerIdIsNotNullTypeData({
        dicCode: "CHEMICAL_STATUS",
      }).then((res) => {
        if (res.data.status === 200) {
          this.dangerIdIsNotNullListData = res.data.data;
        }
      });
    },
    //选择化学品
    handleSelect(res) {
      this.form.msdsid = res.msdsid;
      // this.form.baseChemicalName = res.msdstitle;
      this.form.casCode = res.casno;
      this.form.isImportant = res.supervisionflag;
    },
    clearSensororgCode(item) {
      this.form.baseChemicalId = "";
      this.form.baseChemicalName = "";
      this.form.casCode = "";
      this.form.isImportant = "";
    },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0]);
        let dataTime1 = parseTime(date1, "{y}-{m}-{d}");
        let date2 = new Date(value[1]);
        let dataTime2 = parseTime(date2, "{y}-{m}-{d}");
        this.starTime = dataTime1;
        this.endTime = dataTime2;
      } else {
        this.value1 = "";
        this.starTime = "";
        this.endTime = "";
      }
    },
    search() {
      this.currentPage = 1;
      this.getData(this.enterpriseId);
    },
    clearSensortypeCode(e) {
      this.sensortypeCode = "";
    },
    clearState(e) {
      this.state = "";
    },
    clearDangerName(e) {
      this.dangerName = "";
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.getData(this.enterpriseId);
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].warningId;
      }
    },
    reset() {
      this.form = {
        tankArea: "",
        seq: "",
        tankName: "",
        tankMaterial: "",
        maxStorage: "",
        dailyStorage: "",
        isPressure: "",
        designPressure: "",
        maximumWorkingPressure: "",
        actualPressure: "",
        designLife: "",
        actualLife: "",
        tankType: "",
        pressuretype: "",
        temperaturType: "",
        designTemperatureLow: "",
        designTemperatureHigh: "",
        maximumLiquidLevel: "",
        tankVolume: "",
        tankRadius: "",
        tankHeight: "",
        address: "",
        longitude: "114.311467",
        latitude: "30.580942",
        altitude: "0",
        companyChemicalId: "",
        casCode: "",
        chemicalStatus: "",
        isImportant: "",
        type: 0,
        hazardId: "",
        // lifeAlarm:''
      };
      if (this.$refs["form"]) {
        this.$refs["form"].resetFields();
      }
    },
    resetes() {
      this.formData = {
        contact: "",
        facilityId: "",
        maintenanceContent: "",
        maintenanceId: "",
        maintenanceTime: "",
        operator: "",
        replacementContent: "",
      };
      if (this.$refs["formData"]) {
        this.$refs["formData"].resetFields();
      }
    },
    addEdit() {
      this.reset();
      this.open = true;
      this.disabled = false;
    },
    view(row) {
      this.reset();
      this.dialogType = "add";
      this.open = true;
      this.disabled = true;
      this.title = "查看储罐信息";
      this.datailStorageTankListDataFun(row);
    },
    datailStorageTankListDataFun(row) {
      this.fromLoading = true;
      datailStorageTankListData({ id: row.id }).then((res) => {
        this.fromLoading = false;
        if (res.data.status === 200) {
          this.form = res.data.data;
          if (!this.form.longitude || !this.form.latitude) {
            this.form.longitude = mapConfig.map.defaultExtent.center[0];
            this.form.latitude = mapConfig.map.defaultExtent.center[1];
          }
        }
      });
    },
    edit(row) {
      this.reset();
      this.dialogType = "edit";
      this.open = true;
      this.disabled = false;
      this.title = "编辑储罐信息";
      //   this.form = row;
      this.datailStorageTankListDataFun(row);
    },
    enterInput(row) {
      this.resetes();
      this.formData = row;
      this.formData.lastMaintenanceTime = new Date(
        this.formData.lastMaintenanceTime
      ).Format("yyyy-MM-dd");
      this.formData.maintenanceId = row.id;
      this.opens = true;
    },
    deleter(row) {
      const id = row;
      this.$confirm("是否确认删除该储罐信息", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteStorageTankListData({
            ids: [id],
          }).then((res) => {
            if (res.data.status === 200) {
              this.$message.success("删除成功");
              this.getData(this.enterpriseId);
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {});
    },
    cancel() {
      this.open = false;
      this.reset();
    },
    cancels() {
      this.opens = false;
      this.resetes();
    },

    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          if (this.title === "编辑储罐信息") {
            editStorageTankListData(this.form).then((response) => {
              this.btnLoading = false;
              if (response.data.status === 200) {
                this.open = false;
                this.$message({
                  type: "success",
                  message: response.data.msg || "修改成功",
                });
                this.reset();
                this.getData(this.enterpriseId);
              } else {
                this.$message.error(response.data.msg || "修改失败");
              }
            });
          } else {
            // this.form.orgCode = this.enterpriseId;
            this.form.orgCode = this.$store.state.login.user.org_code;
            addStorageTankListData(this.form).then((response) => {
              this.btnLoading = false;
              if (response.data.status == 200) {
                this.open = false;
                this.$message({
                  type: "success",
                  message: response.data.msg || "新增成功",
                });
                this.reset();
                this.getData(this.enterpriseId);
              } else {
                this.$message.error(response.data.msg || "新增失败");
              }
            });
          }
        } else {
          return false;
        }
      });
    },
    submit() {
      this.$refs["formData"].validate((valid) => {
        if (valid) {
          this.btnLoading = true;
          const {
            contact,
            id,
            maintenanceContent,
            maintenanceId,
            maintenanceTime,
            operator,
            replacementContent,
          } = this.formData;
          maintenanceSave({
            contact,
            facilityId: id,
            maintenanceContent,
            maintenanceId,
            maintenanceTime,
            operator,
            replacementContent,
          }).then((response) => {
            this.btnLoading = false;
            if (response.data.status === 200) {
              this.$message({
                type: "success",
                message: "录入成功",
              });
              this.opens = false;
              this.resetes();
              this.getData(this.enterpriseId);
            } else {
              this.$message({
                type: "success",
                message: "录入失败",
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    mapcallback(data) {
      if (!this.disabled) {
        // 标点赋值
        this.form.longitude = data.location.lon.toFixed(6);
        this.form.latitude = data.location.lat.toFixed(6);
        this.form.address = data.formatted_address;
        this.form.altitude = data.location.altitude
          ? data.location.altitude.toFixed(6)
          : "0";
      } else {
        this.$message({
          message: "详情页面不可选点！",
          type: "warning",
        });
      }
    },
    exportExcel() {
      exportStorageTankListData({
        nowPage: this.currentPage,
        orgCode: this.enterpriseId,
        pageSize: this.size,
        hazardId: this.searchParam.hazardId, // 所属危险源
        keyword: this.searchParam.keyword, // 关键字
        tankType: this.searchParam.tankType, // 罐类型
      }).then((response) => {
        this.$message({
          message: "导出成功",
          type: "success",
        });
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "储罐" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  computed: {
    ...mapState({
      user: (state) => state.login.user,
    }),
  },
};
</script>
<style lang="scss" scoped>
.storageTank {
  background-color: #fff;
  padding-bottom: 50px;
  .dialog {
    height: 60vh;
    overflow-y: scroll;
    overflow-x: hidden;
    .dialog-footer {
      display: flex;
      justify-content: center;
      & > * {
        margin: 0 10px;
      }
    }
  }

  .header {
    .title {
      margin-left: 20px;
      font-weight: 600;
    }

    .operation {
      //   margin-left: 20px;
      margin-bottom: 20px;
      display: flex;
      justify-content: space-between;

      .inputBox {
        // width: 1020px;
        // display: flex;
        // justify-content: space-between;

        display: flex;
        align-items: center;
        .input {
          width: 160px;
        }
      }
      .btnBox {
        display: flex;
        & > * {
          margin-left: 10px;
        }
      }
    }

    .export {
      margin-right: 20px;
    }
  }

  .table {
    width: 100%;
  }

  .pagination {
    margin-top: 30px;
    padding-bottom: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
