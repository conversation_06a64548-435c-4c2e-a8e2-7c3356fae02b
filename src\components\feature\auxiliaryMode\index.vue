<template>
  <div class="auxiliaryMode">
    <component
      :is="$route.name == 'auxiliaryMode' ? 'lawEnforcementData' : $route.name"
    ></component>
  </div>
</template>

<script>
export default {
  //import引入的组件
  components: {
    lawEnforcementData: () => import("../riskAssessment/lawEnforcementData"),
    countReport: () => import("../workingAccount/smartReport/countReport"),
    accidentManagement: () =>
      import("../riskAssessment/accidentManagement/accidentManagement"),
    enterpEducation: () => import("../dailySafetyManagement/enterpEducation"),
  },
  data() {
    return {};
  },
  //方法集合
  methods: {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  watch: {},
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
