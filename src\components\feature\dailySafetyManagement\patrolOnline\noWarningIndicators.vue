<template>
  <div class="noWarningIndicators" v-loading="loading">
    <div class="header">
      <el-cascader
        v-show="this.$store.state.login.user.user_type == 'gov'"
        size="mini"
        placeholder="请选择地市/区县"
        :options="district"
        v-model="districtVal"
        :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
        clearable
        @change="handleChange"
        :show-all-levels="true"
        style="width: 190px"
      ></el-cascader>
      <el-input
        placeholder="请输入企业名称"
        style="width: 190px"
        size="mini"
        clearable
        v-model.trim="enterName"
      ></el-input>
      <el-select
        v-model="monitemkey"
        placeholder="请选择指标类型"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in options1"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-select
        v-model="sensortypeCode"
        placeholder="请选择报警类型"
        style="width: 190px"
        size="mini"
        clearable
      >
        <el-option
          v-for="(item, index) in options2"
          :key="index"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <el-button type="primary" size="mini" @click="getData()">查询</el-button>
    </div>
    <el-table
      :data="tableData"
      border
      :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
      style="width: 100%"
      :height="500"
    >
      <el-table-column type="index" width="80" label="序号"></el-table-column>
      <el-table-column prop="districtName" label="区划" width="150" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="enterpName" label="企业名称" min-width="200" show-overflow-tooltip>
              <template slot-scope="{row}">
        <span type="text" style="color: #3977ea; cursor: pointer;" @click="goEnt(row)">{{row.enterpName}}</span>
      </template>
      </el-table-column>
      <el-table-column prop="dangerName" label="重大危险源名称" width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="targetName" label="设备名称" width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="bitNo" label="指标位号" width="120" show-overflow-tooltip>
      </el-table-column>
      <el-table-column prop="monitemkey" label="指标类型" width="120"> </el-table-column>
      <el-table-column prop="warningType" label="报警类型" width="120"> </el-table-column>
      <el-table-column prop="monvalue" label="报警值" width="120" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="warningTime" label="报警时间" width="180"> </el-table-column>
      <el-table-column prop="warningDurationLong" label="报警时长" width="180">
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page.sync="currentPage"
        background
        layout="total, prev, pager, next"
        :total="total"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import { createNamespacedHelpers } from "vuex";
import { postOnlinePatrolQueryNoAlarmCancelDetails } from "@/api/riskAssessment";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
export default {
  data() {
    return {
      tableData: [],
      district: this.$store.state.controler.district,
      options1: [
        { label: "温度", value: "WD" },
        { label: "压力", value: "YL" },
        { label: "液位", value: "YW" },
        { label: "有毒气体", value: "YDQT" },
        { label: "可燃气体", value: "KRQT" },
      ],
      options2: [
        { label: "高高报", value: "4" },
        { label: "高报", value: "3" },
        { label: "低报", value: "2" },
        { label: "低低报", value: "1" },
      ],
      currentPage: 1,
      total: 0,
      enterName: "",
      monitemkey: "",
      sensortypeCode: "",
      districtVal:this.districtProps,
      loading:false
    };
  },
  props:['districtProps'],
      watch:{
    districtProps:{
      handler(newVal,oldVal){
        this.districtVal = newVal;
      }
    }
  },
  computed:{
        ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
    }),
  },
  methods: {
    goEnt(row) {
      this.$router.push({ name: "entManagement" });
      this.$store.commit("controler/updateEntId", row.enterPid);
    },
        closeTable(){
      this.tableData = [];
      this.currentPage = 1;
      this.total = 0;
    },
    handleChange(value) {
      if (value) {
        this.distCode = value;
      }
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.getData();
    },
    getData() {
      this.loading = true;
      postOnlinePatrolQueryNoAlarmCancelDetails({
        distParentCode:this.userDistCode,
        distCode: this.districtVal,
        current: this.currentPage,
        size: 10,
        // distCode: this.districtVal,
        enterName: this.enterName,
        monitemkey: this.monitemkey,
        sensortypeCode: this.sensortypeCode,
      }).then((res) => {
        this.tableData = res.data.data.records;
        this.total = res.data.data.total;
        this.loading = false;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.noWarningIndicators {
  .header {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    & > * {
      margin-right: 20px;
    }
  }
  .pagination {
    height: 60px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
  }
}
</style>