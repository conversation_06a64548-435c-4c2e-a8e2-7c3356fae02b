<template>
  <div class="spotCheckOnline">
    <div class="header">
      <el-cascader v-show="this.$store.state.login.user.user_type == 'gov'"
                   size="mini"
                   placeholder="请选择地市/区县"
                   :options="district"
                   v-model="distCode"
                   :props="{
          checkStrictly: true,
          value: 'distCode',
          label: 'distName',
          children: 'children',
          emitPath: false,
        }"
                   clearable
                   @change="handleChange"
                   ref="myCascader"
                   :show-all-levels="true"
                   style="width: 190px; margin-top: 10px"></el-cascader>
      <el-select v-model="item.value"
                 v-for="(item, index) in ruleForm.optionsList"
                 :key="index"
                 filterable
                 multiple
                 :placeholder="'请选择' + item.title"
                 size="mini"
                 style="width: 190px; margin-top: 10px"
                 collapse-tags
                 clearable>
        <el-option v-for="(item_options, index_options) in item.options"
                   :key="index_options"
                   :label="item_options.name"
                   :value="item_options.id">
        </el-option>
      </el-select>
      <el-input placeholder="请输入企业数量"
                v-model.trim="screenNum"
                style="width: 280px; margin-top: 10px"
                clearable
                size="mini">
        <template slot="prepend">筛选</template>
        <template slot="append">家企业</template>
      </el-input>
      <el-button type="primary"
                 style="margin-top: 10px"
                 size="mini"
                 @click="search">查询</el-button>
      <CA-button type="primary"
                 plain
                 @click="resetForm()"
                 style="margin-top: 10px"
                 size="mini">重置</CA-button>
      <el-button type="primary"
                 style="float:right;margin-top:10px"
                 @click="oneClick"
                 v-if="$store.state.login.user.isDanger == '1'"
                 size="mini">一键自动抽查</el-button>
    </div>
    <div class="container">
      <div class="title">企业列表</div>
      <div>
        <el-table :data="tableData"
                  v-loading="loading"
                  border
                  style="width: 100%"
                  :header-cell-style="{
            textAlign: 'center',
            color: 'rgb(51, 51, 51)',
            backgroundColor: 'rgb(242, 246, 255)',
          }">
          <el-table-column type="index"
                           label="序号"
                           width="60">
          </el-table-column>
          <el-table-column prop="distName"
                           label="行政区划"
                           width="180">
          </el-table-column>
          <el-table-column prop="companyName"
                           label="企业名称"
                           width="250"
                           :show-overflow-tooltip="true">
            <template slot-scope="{ row }">
              <span type="text"
                    style="color: #3977ea; cursor: pointer"
                    @click="goEnt(row)">{{ row.companyName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="dangerLevelCode"
                           label="危险源等级">
            <template slot-scope="scope">
              <span v-if="scope.row.dangerLevelCode == '1'">一级</span>
              <span v-else-if="scope.row.dangerLevelCode == '2'">二级</span>
              <span v-else-if="scope.row.dangerLevelCode == '3'">三级</span>
              <span v-else-if="scope.row.dangerLevelCode == '4'">四级</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="online"
                           label="系统在线">
            <template slot-scope="scope">
              <span v-if="scope.row.online == '0'">离线</span>
              <span v-else-if="scope.row.online == '1'">在线</span>
            </template>
          </el-table-column>
          <el-table-column prop="offlineReport"
                           label="离线是否报备">
          </el-table-column>
          <el-table-column prop="offlineReportReason"
                           label="离线原因">
          </el-table-column>
          <el-table-column prop="videoOnline"
                           label="视频在线">
            <template slot-scope="scope">
              <span v-if="scope.row.videoOnline == '0'">离线</span>
              <span v-else-if="scope.row.videoOnline == '1'">在线</span>
            </template>
          </el-table-column>
          <el-table-column prop="videoOfflineReport"
                           label="离线是否报备">
          </el-table-column>
          <el-table-column prop="warnRank"
                           label="预警等级">
            <template slot-scope="scope">
              <span v-if="scope.row.warnRank == '1'">红色预警</span>
              <span v-else-if="scope.row.warnRank == '2'">橙色预警</span>
              <span v-else-if="scope.row.warnRank == '3'">黄色预警</span>
              <span v-else>蓝色预警</span>
            </template>
          </el-table-column>
          <el-table-column prop="commitStatus"
                           label="是否安全承诺">
            <template slot-scope="scope">
              <span v-if="scope.row.commitStatus == '0'">否</span>
              <span v-else-if="scope.row.commitStatus == '1'">是</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarmCount"
                           label="未销警次数">
          </el-table-column>
          <el-table-column prop="isLastWeekHignWarnRank"
                           label="7日内是否出现过较大及以上安全风险等级">
            <!-- <template slot-scope="scope">
              <span v-if="scope.row.lastWeekWarnRank == '1'">重大风险</span>
              <span v-else-if="scope.row.lastWeekWarnRank == '2'"
                >较大风险</span
              >
              <span v-else-if="scope.row.lastWeekWarnRank == '3'"
                >一般风险</span
              >
              <span v-else-if="scope.row.lastWeekWarnRank == '4'">低风险</span>
            </template> -->
          </el-table-column>
          <el-table-column prop="address"
                           label="操作"
                           width="110px"
                           fixed="right"
                           v-if="$store.state.login.user.isDanger == '1'">
            <template slot-scope="scope">
              <el-button v-if="scope.row.disabled"
                         type="text"
                         disabled>加入抽查</el-button>
              <el-button v-else
                         type="text"
                         @click="addSpotCheck(scope.row)">加入抽查</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="pagination">
          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="currentPage"
                         background
                         layout="total, prev, pager, next"
                         :total="total"
                         v-if="total != 0">
          </el-pagination>
        </div>
      </div>
    </div>
    <!-- 成功加入巡查记录提示 -->
    <div class="model"
         v-if="visible">
      <div class="box">
        <a-icon type="close"
                class="close"
                @click="handleOff()" />
        <div class="content">已加入抽查记录</div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getIndustry,
  getScale,
  getHazarchem,
  getKnoRegprocess,
  getSpotCheck,
  addSpotCheck,
  oneClick
} from '@/api/dailySafety'
import {
  enterpriseType,
} from "@/api/entList";
import { createNamespacedHelpers, mapState } from 'vuex'
import { MessageBox } from 'element-ui'
const { mapState: mapStateLogin } = createNamespacedHelpers('login')
const { mapState: mapStateControler } = createNamespacedHelpers('controler')
export default {
  name: 'spotCheckOnline',
  data() {
    return {
      districtVal: this.$store.state.login.userDistCode || '',
      visible: false,
      currentPage: 1,
      size: 10,
      total: 0,
      loading: true,
      tableData: [],
      screenNum: '',
      ruleForm: {
        optionsList: [
          {
            options: [],
            value: [],
            title: '所属行业'
          },
          {
            options: [],
            value: [],
            title: '企业规模'
          },
          {
            options: [
              // {
              //   id: '01',
              //   name: '生产'
              // },
              // {
              //   id: '02',
              //   name: '经营'
              // },
              // {
              //   id: '03',
              //   name: '使用'
              // },
              // {
              //   id: '04',
              //   name: '第一类非药品易制毒'
              // }
            ],
            value: [],
            title: '企业类型大类'
          },
          {
            options: [
              {
                id: '1',
                name: '重大风险'
              },
              {
                id: '2',
                name: '较大风险'
              },
              {
                id: '3',
                name: '一般风险'
              },
              {
                id: '4',
                name: '低风险'
              }
            ],
            value: [],
            title: '企业风险等级'
          },
          {
            options: [],
            value: [],
            title: '重点监管危化品'
          },
          {
            options: [],
            value: [],
            title: '重点监管危化工艺'
          }
        ]
      },
      industryCategory: [],
      scaleCode: [],
      enterpriseType: [],
      riskRank: [],
      hazchemName: [],
      processid: [],
      distCode: this.$store.state.login.userDistCode || '',
      district: this.$store.state.controler.district,
      patrolData: []
    }
  },
  mounted() {},
  computed: {
    ...mapStateLogin({
      userDistCode: state => state.userDistCode,
      park: state => state.park,
      isShowDist: state => state.isShowDist
    }),
    ...mapState({
      user: state => state.login.user
    }),
    ...mapStateControler({
      vuexDistrict: state => state.district
    })
  },
  methods: {
    //一键抽查
    oneClick() {
      this.loading = true
      oneClick({
        distCode: [this.$store.state.login.userDistCode],
        distRole: this.$store.state.login.user.distRole
      })
        .then(res => {
          if (res.data.code == 0) {
            this.loading = false
            this.patrolData = res.data.data
            this.patrolData.forEach(item => {
              item.genAreaCode = this.$store.state.login.userDistCode
            })
            this.oneSpotCheck()
          }
        })
        .catch(e => {
          console.log(e, '请求错误')
        })
    },
    oneSpotCheck() {
      MessageBox.confirm('是否确定一键生成抽查?', '通知', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          addSpotCheck(this.patrolData).then(res => {
            if (res.data.code === 0) {
              this.$message.success('一键抽查成功')
              this.$store.commit(
                'controler/updateAutomaticSpotCheckData',
                res.data.data
              )
              this.$parent.$parent.$parent.fatherMethod()
              this.$parent.$parent.$parent.handleClick()
            } else {
              this.$message.error('一键抽查失败')
            }
          })
        })
        .catch(() => {})
    },
    goEnt(row) {
      this.$router.push({ name: 'entManagement' })
      this.$store.commit('controler/updateEntId', row.companyCode)
    },
    getData() {
      //   if (this.user.user_type === "gov") {
      this.getGetIndustry()
      this.getGetScale()
      this.getGetHazarchem()
      this.getGetKnoRegprocess()
      //   }
      this.getSpotCheckList()
      this.getEnterpriseType()
    },

     getEnterpriseType() {
      enterpriseType({}).then((res) => {
        var ary=[]
        res.data.data.forEach(el=>{
          ary.push({
            name:el.enterpriseType,
            id:el.id
          })
        })
        this.ruleForm.optionsList[2].options = ary
      });
    },

    //获取行业
    getGetIndustry() {
      getIndustry({}).then(res => {
        if (res.data.code == 0) {
          this.ruleForm.optionsList[0].options = res.data.data
        }
      })
    },
    //获取规模
    getGetScale() {
      getScale({}).then(res => {
        if (res.data.code == 0) {
          this.ruleForm.optionsList[1].options = res.data.data
        }
      })
    },
    //获取重点监管危化品
    getGetHazarchem() {
      getHazarchem({}).then(res => {
        if (res.data.code == 0) {
          this.ruleForm.optionsList[4].options = res.data.data
        }
      })
    },
    //获取重点监管危化工艺
    getGetKnoRegprocess() {
      getKnoRegprocess({}).then(res => {
        if (res.data.code == 0) {
          this.ruleForm.optionsList[5].options = res.data.data
        }
      })
    },
    //在线抽查列表
    getSpotCheckList() {
      this.loading = true
      getSpotCheck({
        current: this.currentPage,
        screenNum: this.screenNum,
        distCode: this.distCode.split(','),
        size: this.size,
        industryCategory: this.industryCategory,
        scaleCode: this.scaleCode,
        enterpriseType: this.enterpriseType,
        riskRank: this.riskRank,
        hazchemName: this.hazchemName,
        processid: this.processid
      }).then(res => {
        if (res.data.code == 0) {
          this.loading = false
          this.tableData = res.data.data.records
          this.total = res.data.data.total
        }
      })
    },
    search() {
      this.currentPage = 1
      this.industryCategory = this.ruleForm.optionsList[0].value
      this.scaleCode = this.ruleForm.optionsList[1].value
      this.enterpriseType = this.ruleForm.optionsList[2].value
      this.riskRank = this.ruleForm.optionsList[3].value
      this.hazchemName = this.ruleForm.optionsList[4].value
      this.processid = this.ruleForm.optionsList[5].value
      this.getSpotCheckList()
    },
    handleChange(value) {
      if (value) {
        this.distCode = value
      } else {
        this.distCode = this.$store.state.login.userDistCode
      }
    },
    resetForm() {
      for (let i = 0; i < this.ruleForm.optionsList.length; i++) {
        this.ruleForm.optionsList[i].value = []
      }
      this.screenNum = ''
      this.distCode = this.$store.state.login.userDistCode
    },
    //加入抽查
    addSpotCheck(value) {
      value.disabled = true
      if (value.warnRank == '--') {
        value.warnRank = ''
      }
      addSpotCheck([
        {
          companyCode: value.companyCode,
          online: value.online,
          videoOnline: value.videoOnline,
          warnRank: value.warnRank == '--' ? '' : value.warnRank,
          commitStatus: value.commitStatus,
          alarmCount: value.alarmCount,
          lastWeekWarnRank: value.lastWeekWarnRank,
          replyType: value.replyType,
          genAreaCode: this.$store.state.login.userDistCode
        }
      ]).then(res => {
        if (res.data.code == 0) {
          this.visible = true
          var that = this
          setTimeout(function() {
            that.handleOff()
          }, 3000)
        }
      })
    },
    handleOff() {
      this.visible = false
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getSpotCheckList()
    }
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal
      }
    }
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-select__tags-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
  display: block;
}
.spotCheckOnline {
  .header {
    margin-bottom: 20px;
    display: inline-table;
    & > * {
      margin: 0 20px 0 0;
    }
  }
  .container {
    .title {
      font-size: 18px;
      width: 100%;
      text-align: left;
      padding-bottom: 10px;
      font-weight: 900;
    }
  }
  .pagination {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
  }
  .model {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100000;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    & > .box {
      width: 524px;
      height: 415px;
      position: relative;
      border-radius: 5px;
      background-image: url('../../../../../static/img/SpotCheckDialogBg.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      .close {
        position: absolute;
        right: 30px;
        top: 10px;
        font-size: 18px;
      }
      .content {
        width: 100%;
        text-align: center;
        margin-top: 260px;
        color: #545c65;
        font-weight: 900;
        font-size: 16px;
      }
    }
  }
}
</style>
