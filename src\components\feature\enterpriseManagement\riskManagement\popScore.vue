<template>
  <!-- 重大   -->
  <el-dialog
    title="风险单元"
    :visible.sync="isScore"
    width="800px"
    class=""
    top="5vh"
    :close-on-click-modal="false"
  >
    <!-- text-align:center; -->
    <div class="ScoreBox">
      <!-- <img src="../../../../../static/img/riskManagementIcon/sgyh.png" /> -->
      <div class="hs_box">
        <h2>单元风险频率指标：{{scoreData.indexG}}</h2>
        <!-- <el-progress type="circle" :percentage="1.87"></el-progress> -->
      </div>

     
      <p style="color:red">初始安全生产标准化等级满分为100分</p>
      <div class="ScoreCon">
        安全生产标准自评分数：<b>{{scoreData.evaluationScore}}</b>
        <!-- &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        *安全生产标准评审分数：<el-input size="mini" v-model.trim="num2"></el-input> -->
      </div>
    </div>

    <!-- <div slot="footer" style="display: flex; justify-content: center">
      <el-button size="mini">取 消</el-button>

      <el-button size="mini" type="primary" @click="submit(2)">提 交</el-button>
    </div> -->
  </el-dialog>
</template>
<script>
export default {
  data() {
    return {
      isScore: false,
      num1: "",
      num2: "",
      scoreData:{}
    };
  },
};
</script>
<style scoped lang="scss">
.ScoreBox{
  text-align: center;
}
.hs_box {
  // display: flex;
  // align-items: center;
  // justify-content: center;
}
.ScoreCon{
  // display: flex;
  // align-items: center;
  text-align: center;
}
/deep/ .el-input{
  width:100px
}
</style>