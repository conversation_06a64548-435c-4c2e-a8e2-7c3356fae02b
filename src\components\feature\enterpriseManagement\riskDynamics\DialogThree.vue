<template>
  <div class="riskDy" v-if="dialogRisk">
    <el-dialog
      :title="title + '—' + parentData.riskPointName"
      :visible.sync="dialogRisk"
      width="1460px"
      top="10px"
      :close-on-click-modal="false"
    >
      <div class="riskDialogBox">
        <div class="riskDialogTop">
          <div class="dialogL">固有风险-五高风险因子计量</div>
          <div class="dialogR">风险动态指标调控</div>
        </div>

        <div class="dialogCon">
          <div class="dialogConL">
            <div class="item">
              <div class="itemTit">高风险设备设施HS</div>
              <ul>
                <!-- <li
                  v-for="(item, indexh1) of hsDataObj.deviceInherentDatas"
                  :key="indexh1"
                >
                  {{ item.factorName }}：{{ item.factorValue || "未涉及" }}
                </li> -->

                <div class="itemBox" v-if="hsDataObj.deviceInherentDatas">
                  <div :class="{ activeHeightHs: lookMoreHs }">
                    <div
                      class="itemCon"
                      v-for="(item, indexh1) of hsDataObj.deviceInherentDatas"
                      :key="indexh1"
                    >
                      {{ item.factorName }}：{{ item.factorValue || "未涉及" }}
                    </div>
                  </div>

                  <div
                    class="JournalismShow"
                    v-if="hsDataObj.deviceInherentDatas.length > 3"
                    @click="selectMoreHs"
                  >
                    <span v-if="lookMoreHs"
                      >展开<i class="el-icon-arrow-down"></i
                    ></span>
                    <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                  </div>
                </div>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">高风险物品M</div>
              <div class="conBox" v-if="mDataObj.materialDataList">
                <div v-if="mDataObj.materialDataList.length > 0">
                  <div class="headerTit">
                    <span class="title" style="text-align: left">物资名称</span>
                    <span class="storage">设计存储量</span>
                    <span>临界量</span>
                    <span>校正系数</span>
                  </div>
                  <div :class="{ activeHeightM: lookMoreM }">
                    <div
                      class="conList"
                      v-for="(item, mIndx) of mDataObj.materialDataList"
                      :key="mIndx"
                    >
                      <span class="title" style="text-align: left">
                        <el-tooltip :content="item.title" placement="top">
                          <el-button>{{ item.title }}</el-button>
                        </el-tooltip>
                      </span>
                      <span :class="['storage',item.storage > item.criticalValue?'red':'']">{{ item.storage }}吨</span>
                      <!-- 设计存储量大与临界值 -->
                      <span :class="[item.storage > item.criticalValue?'red':'']">{{ item.criticalValue }}吨</span>
                      <span>{{ item.correction }}</span>
                    </div>
                  </div>
                </div>
                <div class="activeHeightM" v-else>暂无数据！</div>

                <div
                  class="JournalismShow"
                  v-if="mDataObj.materialDataList.length > 4"
                  @click="selectMoreM"
                >
                  <span v-if="lookMoreM"
                    >展开<i class="el-icon-arrow-down"></i
                  ></span>
                  <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                </div>
              </div>
            </div>

            <div class="item">
              <div class="itemTit">高风险场所E</div>
              <ul>
                <li>发生装置区域：{{ eDataObj.indexE || "无" }}</li>
              </ul>
            </div>

            <div class="item" v-if="k1DataObj.processInherentLists">
              <div class="itemTit">高风险工艺K1</div>
              <div class="itemBox" :class="{ activeHeight2: lookMore2 }">
                <div
                  class="itemOne"
                  v-for="(el, k1) of k1DataObj.processInherentLists"
                  :key="k1"
                >
                  <h5>{{ el.superFactorName }}</h5>
                  <!-- :class="{ activeHeight: lookMore }" -->
                  <div>
                    <div
                      class="itemCon"
                      v-for="(item, k2) of el.processInherentDatas"
                      :key="k2"
                    >
                      {{ item.factorName }}：{{ item.factorValue || "不涉及" }}
                    </div>
                  </div>
                  <!-- <div
                    class="JournalismShow"
                    v-if="el.processInherentDatas.length > 4"
                    @click="selectMore"
                  >
                    <span v-if="lookMore"
                      >展开<i class="el-icon-arrow-down"></i
                    ></span>
                    <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                  </div> -->
                </div>
              </div>
              <div
                class="JournalismShow"
                v-if="k1DataObj.processInherentLists.length >= 1"
                @click="selectMore2"
              >
                <span v-if="lookMore2"
                  >展开<i class="el-icon-arrow-down"></i
                ></span>
                <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
              </div>
            </div>

            <div class="item">
              <div class="itemTit">高风险作业K2</div>
              <div class="k2Box" v-if="k2Data">
                <div v-if="k2Data.length > 0">
                  <div class="k2Ttit">
                    <span>作业大类</span><span class="num">数量</span
                    ><span>更新时间</span>
                  </div>
                  <div
                    class="k2ConList"
                    :class="{ activeHeightK2: lookMoreK2 }"
                  >
                    <div
                      class="itemDiv"
                      v-for="(item, k2Index) of k2Data"
                      :key="k2Index"
                    >
                      <span>{{ item.jobBigTypeName }}</span>
                      <span class="num">未绑定</span>
                      <span>{{ item.updateTime | updateTimeFn }}</span>
                    </div>
                  </div>

                  <div
                    class="JournalismShow"
                    v-if="k2Data.length >= 3"
                    @click="selectMoreK2"
                  >
                    <span v-if="lookMoreK2"
                      >展开<i class="el-icon-arrow-down"></i
                    ></span>
                    <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                  </div>
                </div>
                <div v-else>暂无数据！</div>
              </div>
            </div>
          </div>
          <div class="dialogConC">
            <div class="centerL">
              <div class="center1">
                高风险设备设施HS
                <div style="color: #fe9b45; font-weight: bold">
                  {{ hsDataObj.indexHs || "无" }}
                </div>
              </div>
              <div class="center2">
                高风险作业K2
                <div style="color: #5c8dfb; font-weight: bold">
                  {{ k2DataObj.indexK2 || "无" }}
                </div>
              </div>
              <div class="center3">
                <div style="color: #eec10e; font-weight: bold">
                  {{ mDataObj.indexM || "无" }}
                </div>
                高风险物品<br />（能量）M
              </div>
              <div class="center4">
                <div style="color: #00ffff; font-weight: bold">
                  {{ eDataObj.indexE || "无" }}
                </div>
                高风险场所E
              </div>
              <div class="center5">
                <div style="color: #5c8dfb; font-weight: bold">
                  {{ k1DataObj.indexK1 || "无" }}
                </div>
                高风险工艺K1
              </div>
              <!-- <div class="center6">
                <div>131.1</div>
                初始风险H
              </div> -->
              <!-- <div class="center7">指标管控率G<span>1.4</span></div> -->
              <p class="clickCen">
                <span>{{ parentData.riskPointName }}</span>
              </p>
              <div
                class="clickStyle clickStyle3"
                v-if="allRiskPoinData.length == 3"
              >
                <span
                  @click="clickRistPoin(0)"
                  :class="['H1', clickActive == 0 ? 'clickActive' : '']"
                  >H1</span
                >
                <span
                  @click="clickRistPoin(1)"
                  :class="['H2', clickActive == 1 ? 'clickActive' : '']"
                  >H2</span
                >
                <span
                  @click="clickRistPoin(2)"
                  :class="['H3', clickActive == 2 ? 'clickActive' : '']"
                  >H3</span
                >
              </div>

              <div
                class="clickStyle clickStyle2"
                v-if="allRiskPoinData.length == 2"
              >
                <span
                  @click="clickRistPoin(0)"
                  :class="['H1', clickActive == 0 ? 'clickActive' : '']"
                  >H1</span
                >
                <span
                  @click="clickRistPoin(1)"
                  :class="['H2', clickActive == 1 ? 'clickActive' : '']"
                  >H2</span
                >
              </div>

              <div
                class="clickStyle clickStyle1"
                v-if="allRiskPoinData.length == 1"
              >
                <span
                  @click="clickRistPoin(0)"
                  :class="['H1', clickActive == 0 ? 'clickActive' : '']"
                  >H1</span
                >
              </div>

              <!-- <div class="center71">
                固有风险H
                <b class="num">91.8</b>
              </div> -->
            </div>

            <div class="centerC">
              <p>初始风险 <b>RO</b></p>
              <div class="center10">
                <div class="num">{{ modelData.indexR0 }}</div>
                <span>R0</span>
              </div>
              <div class="center101">
                <div>管控频率</div>
                <b>{{ modelData.indexG }}</b>
              </div>

              <div class="center102">
                <div>安全标准化评分</div>
                <b>{{ modelData.evaluationScore }}</b>
              </div>
            </div>
            <div class="centerR">
              <p>动态调控</p>
              <div class="center12">
                <span class="num">{{enterSpecialData.length > 0 ?'有':'无'}}</span>
                特殊时期
              </div>
              <div class="center13">
                <!-- 数字不为0的时候红色显示 -->
                <div :class="['num',getIndex123!=0?'red':'']">{{getIndex123}}</div>
                <!-- 动态调控指标D -->
              </div>
              <div class="center14">
                <span class="num">{{naturalData.length > 0 ?'有':'无'}}</span>
                自然环境
              </div>
              <div class="center15">
                <span class="num">{{internetData.length > 0?'有':'无'}}</span>
                物联网
              </div>
            </div>

            <div class="k3">
              <p>k3</p>
              <div class="con">
                <p>高危风险监</p>
                <p>测监控特征</p>
              </div>
              <b>{{ k3DataObj.indexK3 }}</b>
            </div>
            <div class="k4">
              <p>k4</p>
              <div class="con">
                <p>事故隐患</p>
              </div>
              <b>{{ modelData.indexK4 }}</b>
            </div>

            <div class="major">
              <p>重大隐患</p>
              <div class="num">{{ modelData.majorDanger }}</div>
            </div>

            <div class="centerBottom">
              <div class="centerBottomItem2">
                <p>
                  {{ bigObj.riskLevelName
                  }}<span>{{ bigObj.riskUnitValue }}</span>
                </p>
                <div class="name">{{ bigObj.riskUnitName }}</div>
              </div>
            </div>
          </div>
          <div class="dialogConR">
            <div class="item">
              <div class="itemTit">高危风险监测监控特征（K3）</div>
              <div class="fen_dong">
                <div class="fen_tit">
                  <span>报警级别</span>
                  <span class="num">数量</span>
                  <span class="num">系数</span>
                  <span>更新时间</span>
                </div>
                <div class="feng_con">
                  <div class="feng_item">
                    <div>二级报警</div>
                    <div v-if='k3DataObj.alarmLevel2==0' :class="['level_num' ,'num',k3DataObj.alarmLevel2!=0?'red':'']">
                      {{ k3DataObj.alarmLevel2 }}
                    </div>

                     <div style="cursor: pointer;" @click="clickNewOpen" v-if='k3DataObj.alarmLevel2!=0' :class="['level_num' ,'num',k3DataObj.alarmLevel2!=0?'red':'']">
                      {{ k3DataObj.alarmLevel2 }}
                    </div>
                    <div class="xiShu num">0.3</div>
                    <div class="updataTime">
                      {{ k3DataObj.updateTime | updateTimeFilter }}
                    </div>
                  </div>
                </div>

                <div class="feng_con">
                  <div class="feng_item">
                    <div>三级报警</div>
                    <div  v-if='k3DataObj.alarmLevel3==0' :class="['level_num', 'num',k3DataObj.alarmLevel3!=0?'red':'']">
                      {{ k3DataObj.alarmLevel3 }}
                    </div>
                    <div style="cursor: pointer;" @click="clickNewOpen" v-if="k3DataObj.alarmLevel3!=0" :class="['level_num', 'num',k3DataObj.alarmLevel3!=0?'red':'']">
                      {{ k3DataObj.alarmLevel3 }}
                    </div>
                    <div class="xiShu num">0.6</div>
                    <div class="updataTime">
                      {{ k3DataObj.updateTime | updateTimeFilter }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="item">
              <div class="itemTit">事故隐患（K4）</div>
              <ul>
                <li>一般隐患：{{ modelData.generalDanger }}</li>
                <li>重大隐患：{{ modelData.majorDanger }}</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">特殊时期指数修正</div>

              <div v-if="enterSpecialData.length > 0" class="specialBox">
                <div :class="{ activeHeightSpecialData: lookMoreSpecialData }">
                  <div
                    v-for="(el, specialIndex) of enterSpecialData"
                    :key="specialIndex"
                    class="specialItem"
                  >
                    <div>活动名称：{{ el.periodName }}</div>
                    <div>开始时间：{{ el.startTime }}</div>
                    <div>结束时间：{{ el.endTime }}</div>
                    <div>活动描述：{{ el.periodNote }}</div>
                    <div>生效区域：{{ el.districtName }}</div>
                  </div>
                </div>
                <div
                  class="JournalismShow"
                  v-if="naturalData.length > 1"
                  @click="selectMoreSpecialData"
                >
                  <span v-if="lookMoreNaturalData"
                    >展开<i class="el-icon-arrow-down"></i
                  ></span>
                  <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                </div>
              </div>
              <div class="activeHeightNaturalData" v-else>暂无数据！</div>
            </div>
            <div class="item">
              <div class="itemTit">自然环境指数修正</div>
              <div v-if="naturalData.length > 0" class="specialBox">
                <div :class="{ activeHeightNaturalData: lookMoreNaturalData }">
                  <div
                    v-for="(el, specialIndex) of naturalData"
                    :key="specialIndex"
                    class="specialItem"
                  >
                    <div>活动名称：{{ el.disasterName }}</div>
                    <div>开始时间：{{ el.startTime }}</div>
                    <div>结束时间：{{ el.endTime }}</div>
                    <div>活动描述：{{ el.disasterInfo }}</div>
                    <div>生效区域：{{ el.districtName }}</div>
                  </div>
                </div>
                <div
                  class="JournalismShow"
                  v-if="naturalData.length > 1"
                  @click="selectMoreSpecialData"
                >
                  <span v-if="lookMoreNaturalData"
                    >展开<i class="el-icon-arrow-down"></i
                  ></span>
                  <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                </div>
              </div>
              <div class="activeHeightNaturalData" v-else>暂无数据！</div>
            </div>

            <div class="item">
              <div class="itemTit">物联网指数修正</div>
              <div v-if="internetData.length > 0" class="specialBox">
                <div
                  :class="{ activeHeightInternetData: lookMoreInternetData }"
                >
                  <div
                    v-for="(el, specialIndex) of internetData"
                    :key="specialIndex"
                    class="specialItem"
                  >
                    <div>活动名称：{{ el.accidentName }}</div>
                    <div>开始时间：{{ el.startTime }}</div>
                    <div>结束时间：{{ el.endTime }}</div>
                    <div>活动描述：{{ el.accidentInfo }}</div>
                    <div>生效区域：{{ el.districtName }}</div>
                  </div>
                </div>
                <div
                  class="JournalismShow"
                  v-if="internetData.length > 1"
                  @click="selectMoreInternetData"
                >
                  <span v-if="lookMoreInternetData"
                    >展开<i class="el-icon-arrow-down"></i
                  ></span>
                  <span v-else>收缩<i class="el-icon-arrow-up"></i></span>
                </div>
              </div>
              <div class="activeHeightInternetData" v-else>暂无数据！</div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
var dayjs = require("dayjs");
import {
  getFindDevice,
  getDeviceInherentIndex,
  updateDevice,
  getFindProcess,
  updateProcess,
  exposeIndexOption,
  exposeFind,
  exposeUpdate,
  specificChemicalsIndex,
  updateMaterial,
  findMaterial,
  particularFindJob,
  particularJobPage,
  queryRiskUnitInfo,
  alarmFind,
  findPeriodList,
} from "@/api/riskAssessment";
import {
  getAccidentRecordList,
  naturalDisasterPageList,
} from "@/api/accidentManagement";
export default {
  //import引入的组件
  name: "riskDynamics",
  components: {},
  data() {
    return {
      lookMoreSpecialData: true,
      lookMoreNaturalData: true,
      lookMoreInternetData: true,
      lookMore: true,
      lookMore2: true,
      lookMoreHs: true,
      lookMoreM: true,
      lookMoreK2: true,
      initLength: 4,
      loading: false,
      dialogRisk: false, //弹窗
      // risktopData: {},
      title: "",
      parentData: {},
      hsDataObj: {}, //(hs)初始数据
      k1DataObj: {}, //(K1)初始数据
      eDataObj: {}, //(E)初始数据
      mDataObj: {}, //(M)初始数据
      clickActive: "", //默认是点击的那个风险点
      allRiskPoinData: {}, //风险单位里面的所有风险点
      k2DataObj: {},
      k2Data: [],
      bigObj: {},
      modelData: {},
      k3DataObj: {},
      enterSpecialData: [], //特殊时期指数修正
      naturalData: [], //自然修正
      internetData: [], //物联网
      curDistrictCode:"",
      risktopData:{},
      getIndex123:0
    };
  },
  computed:{
   
  },
  watch: {
    clickActive: {
      handler(newVal, oldVal) {
        this.parentData = this.allRiskPoinData[newVal];
        this.getHS();
        this.getK1();
        this.getE();
        this.getM();
        this.getModel();
        this.getK3();
      },
      immediate: true, // 初始化数据触发watch
      deep: true, // 对 对象进行深度监听
    },
  },
  filters: {
    filterLevel(val) {
      var str = "";
      if (val == 1) {
        str = "较大风险";
      } else if (val == 2) {
        str = "一般风险";
      } else if (val == 3) {
        str = "重大风险";
      } else if (val == 4) {
        str = "低风险";
      }
      return str;
    },
    updateTimeFn(val) {
      return dayjs(val).format("YYYY-MM-DD");
    },
    updateTimeFilter(val) {
      return dayjs(val).format("YYYY-MM-DD");
    },
  },
  //方法集合
  methods: {
    clickNewOpen(){
      const routeData = this.$router.resolve({
           path: '/riskAssessment/iotMontoringAlarm',
           query:{
            state:1
           }
         });
         window.open(routeData.href, '_blank');
    },
    //activeHeightSpecialData
    selectMoreSpecialData(leg) {
      this.lookMoreSpecialData = !this.lookMoreSpecialData;
    },
    //naturalData
    selectMoreNaturalData(leg) {
      this.lookMoreNaturalData = !this.lookMoreNaturalData;
    },
    //InternetData
    selectMoreInternetData(leg) {
      this.lookMoreInternetData = !this.lookMoreInternetData;
    },
    selectMore(leg) {
      this.lookMore = !this.lookMore;
    },
    selectMore2(leg) {
      this.lookMore2 = !this.lookMore2;
    },
    selectMoreHs(leg) {
      this.lookMoreHs = !this.lookMoreHs;
    },
    selectMoreM(leg) {
      this.lookMoreM = !this.lookMoreM;
    },
    selectMoreK2(leg) {
      this.lookMoreK2 = !this.lookMoreK2;
    },
    clickRistPoin(index) {
      this.clickActive = index;
      this.parentData = this.allRiskPoinData[index];
    },
    isShow(val) {
      this.dialogRisk = true;
    },
    //自然
    getNaturalData() {
      // debugger
      var params = {
        districtCode: this.curDistrictCode,
        disasterLevel: "",
        disasterType: "",
        // draftFlag: "1",
        // endTime: dayjs().format("YYYY-MM-DD")+ " 23:59:59",
        id: "",
        keywords: "",
        nowPage:1,
        pageSize: 50,
        // startTime: dayjs().format("YYYY-MM-DD")+ " 00:00:00",
        draftFlag:1
      };
      naturalDisasterPageList(params).then((res) => {
        if (res.data.status === 200) {
          this.naturalData = res.data.data.list;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
    //物联网
    internetDataList() {
      var params = {
        districtCode: this.curDistrictCode,
        accidentLevel: "",
        accidentType: "",
        draftFlag: "",
        // endTime: dayjs().format("YYYY-MM-DD")+ " 23:59:59",
        id: "",
        keywords: "",
         nowPage:1,
        pageSize: 50,
        // startTime: dayjs().format("YYYY-MM-DD")+ " 00:00:00",
        draftFlag:1
      };
      getAccidentRecordList(params).then((res) => {
        if (res.data.status === 200) {
          this.internetData = res.data.data.list;
        } else {
          this.$message({
            message: res.data.msg,
            type: "warning",
          });
        }
      });
    },
    //特殊时期修正   需要判断节假日 节假日或重要活动其中有数据就是有
    enterSpecialFn() {
      var params = {
        districtCode: this.curDistrictCode,      
        endTime: dayjs().format("YYYY-MM-DD"),
        id: "",
        keywords: "",      
        nowPage:1,
        pageSize: 50,
        startTime: dayjs().format("YYYY-MM-DD"),
        draftFlag:1,
        periodType:""
      };

      findPeriodList(params).then((res) => {
        if (res.data.status === 200) {          
          this.enterSpecialData = res.data.data;
        } else {
        }
      });
    },
    getK3() {
      alarmFind({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k3DataObj = res.data.data;
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    getModel() {
      queryRiskUnitInfo({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        riskType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.modelData = res.data.data;
        } else {
        }
      });
    },
    //获取风险设备设施(hs)初始数据
    getHS() {
      getFindDevice({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.hsDataObj = res.data.data;
        } else {
        }
      });
    },
    //获取高风险工艺(K1)初始数据
    getK1() {
      getFindProcess({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k1DataObj = res.data.data;
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //高风险场所(E)初始数据
    getE() {
      exposeFind({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        dataId: this.parentData.dataId,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.eDataObj = res.data.data;
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //高风险场所(M)初始数据
    getM() {
      findMaterial({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.mDataObj = res.data.data;
          console.log(
            this.mDataObj,
            "高风险场所(E)初始数据高风险场所(E)初始数据高风险场所(E)初始数据高风险场所(E)初始数据"
          );
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
    //k2—高风险作业指数
    getK2() {
      var _this = this;
      particularFindJob({
        cimRiskUnitId: _this.parentData.cimRiskUnitId,
        cimRiskUnitType: _this.parentData.cimRiskUnitType,
        enterpId: _this.parentData.enterpId,
        riskPointId: _this.parentData.riskPointId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k2DataObj = res.data.data;
        } else {
          // this.$message.error(res.msg);
        }
      });
      particularJobPage({
        cimRiskUnitId: this.parentData.cimRiskUnitId,
        cimRiskUnitType: this.parentData.cimRiskUnitType,
        enterpId: this.parentData.enterpId,
        riskPointId: this.parentData.riskPointId,
        jobDate: dayjs().format("YYYY-MM-DD"),
        nowPage: 1,
        pageSize: 1000,
      }).then((res) => {
        if (res.data.status === 200) {
          this.k2Data = res.data.data.list || [];
        } else {
          // this.$message.error(res.msg);
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
.red{
  color:red
}
.conBox,
.specialBox,
.fen_dong {
  color: #b3b3b9;
}
/deep/ .activeHeightM .el-button {
  color: #b3b3b9 !important;
}
.specialBox {
  font-size: 12px;
  .specialItem {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    margin: 0 0 10px 0;
    padding: 0 0 10px 0;
  }
}
.fen_dong {
  font-size: 12px;
  .fen_tit {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    > span {
      width: 30%;
    }
    > span.num {
      width: 20%;
    }
  }
  .feng_item {
    display: flex;
    text-align: center;
    align-items: center;
    // margin: 10px 0;
    // > span {
    //   width: 40%;
    // }

    > div {
      width: 30%;
      box-sizing: content-box;
    }
    > div.num {
      width: 20%;
    }
  }
}
.k2Box {
  font-size: 12px;
  .k2Ttit {
    display: flex;
  }
  .k2ConList {
    .itemDiv {
      display: flex;
    }
  }
  span {
    width: 40%;
  }
  .num {
    width: 20%;
  }
}
/deep/ .conList .el-button {
  padding: 0 !important;
  background: transparent !important;
  border: 0;
  font-size: 12px;
}
.conBox {
  font-size: 12px;
  .headerTit {
    display: flex;
  }
  .conList {
    display: flex;
  }
  .title {
    width: 30%;
    overflow: hidden; //超出隐藏
    white-space: nowrap; //不折行
    text-overflow: ellipsis; //溢出显示省略号
  }
  span {
    width: 20%;
    text-align: center;
  }
  span.storage {
    width: 30%;
    text-align: center;
  }
}

@media screen and (min-width: 1599px) and (max-width: 1740px) {
  /deep/ .el-dialog__wrapper {
    transform: scale(1.1);
  }
  /deep/ .el-dialog {
    margin-top: 100px !important;
  }
}

@media screen and (min-width: 1740px) {
  /deep/ .el-dialog__wrapper {
    transform: scale(1.2);
  }
  /deep/ .el-dialog {
    margin-top: 100px !important;
  }
}

/deep/ .el-dialog__body {
  padding: 0;
}
::-webkit-scrollbar {
  width: 7px;
  height: 7px;
  background: rgba(0, 0, 0, 0.2);
}
::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background-color: rgba(120, 139, 199, 0.5);
}

.activeHeight {
  overflow: hidden;
  max-height: 70px;
  position: relative;
}
.activeHeightK2 {
  overflow: hidden;
  max-height: 50px;
  position: relative;
}
.activeHeightHs {
  overflow: hidden;
  max-height: 50px;
  position: relative;
}
.activeHeight2 {
  overflow: hidden;
  height: 75px;
  position: relative;
}
.activeHeightM {
  overflow: hidden;
  height: 55px;
  position: relative;
}
.activeHeightSpecialData {
  overflow: hidden;
  height: 75px;
  position: relative;
  font-size: 12px;
}
.activeHeightNaturalData {
  overflow: hidden;
  height: 75px;
  position: relative;
  font-size: 12px;
}
.activeHeightInternetData {
  overflow: hidden;
  height: 75px;
  position: relative;
  font-size: 12px;
}

.JournalismShow {
  text-align: right;
  color: #fff;
  font-size: 12px;
  // margin-top: -15px;
  z-index: 9999;
  position: relative;
  cursor: pointer;
  // position: absolute;
  // right:0;
  // bottom:0;
}
.itemBox {
  .itemOne {
    color: #b3b3b9;
    font-size: 12px;
    h5 {
      color: #fff;
      font-size: 13px;
      font-weight: blod;
      margin: 0;
    }
  }
}
.riskDialogBox {
  background: url("../../../../../static/img/riskDialog_bg.png") no-repeat
    center #0c1534;
  width: 100%;
  padding: 0 10px 0 20px;
  .riskDialogTop {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #fff;
    padding: 10px 0 10px 0;
    .dialogL {
      background: url("../../../../../static/img/riskTopL.png") no-repeat bottom
        left;
      padding: 0 0 8px;
      width: 48%;
    }
    .dialogR {
      background: url("../../../../../static/img/riskTopR.png") no-repeat bottom
        right;
      padding: 0 0 8px;
      width: 48%;
      text-align: right;
    }
  }
  .dialogCon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialogConL {
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitL.png") no-repeat
          bottom left;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #b3b3b9;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
    .dialogConC {
      background: url("../../../../../static/img/riskDialog.png") no-repeat
        center center;
      width: 960px;
      height: 535px;
      background-size: 100% 100%;
      position: relative;
    }
    .dialogConR {
      text-align: right;
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      padding: 0 10px 0 0;
      box-sizing: content-box;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitR.png") no-repeat
          bottom right;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #b3b3b9;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
  }
  .dialogConC {
  }
  .centerL {
    font-size: 12px;
    text-align: center;
    color: #fff;
    .center1 {
      //高风险设备设施
      position: absolute;
      top: 31px;
      left: 75px;
    }
    .center2 {
      // 高风险作业K2
      position: absolute;
      left: 218px;
      top: 31px;
    }
    .center3 {
      //高风险物品（能量）M
      position: absolute;
      left: 19px;
      top: 144px;
    }
    .center4 {
      //高风险场所E
      position: absolute;
      left: 96px;
      top: 239px;
    }
    .center5 {
      left: 212px;
      top: 239px;
      position: absolute;
    }
    .center6 {
      //初始风险H
      left: 284px;
      top: 147px;
      position: absolute;
    }
    .center7 {
      //指标管控率G
      position: absolute;
      left: 226px;
      top: 116px;
      transform: rotate(-56deg);
    }
    .center71 {
      //  固有风险H
      position: absolute;
      left: 167px;
      top: 184px;
      color: #03d2da;
      b {
        display: block;
        color: #fff;
      }
    }
    .clickCen {
      position: absolute;
      left: 113px;
      top: 89px;
      font-size: 16px;
      // width: 158px;
      line-height: 16px;
      padding: 3px 0;
      color: #000;
      width: 160px;
      span {
        background: #03d2da;
        display: inline-block;
      }
    }
    .clickStyle {
      position: absolute;
      width: 60px;
      background-size: 100% 100%;
      height: 60px;
      left: 161px;
      top: 119px;

      span.clickActive.H1,
      span.clickActive.H2,
      span.clickActive.H3 {
        color: red;
      }
    }
    .clickStyle1 {
      background: url("../../../../../static/img/img-只有一个数值.png")
        no-repeat //img-只有一个数值
        center;
      line-height: 60px;
      background-size: 100% 100%;
    }
    .clickStyle2 {
      background: url("../../../../../static/img/img-两个-常态.png") no-repeat
        center;
      background-size: 100% 100%;
      line-height: 60px;
      span {
        display: inline-block;
        width: 28px;
        cursor: pointer;
      }
    }
    .clickStyle3 {
      background: url("../../../../../static/img/正方体-3-常态.png") no-repeat
        center;
      background-size: 100% 100%;
      span.H1 {
        width: 50px;
        height: 24px;
        cursor: pointer;
        position: absolute;
        top: 2px;
        left: 7px;
        /* border: 1px solid red; */
        line-height: 24px;
      }
      span.H2 {
        width: 28px;
        height: 33px;
        line-height: 33px;
        cursor: pointer;
        position: absolute;
        top: 22px;
        left: 0;
      }
      span.H3 {
        width: 28px;
        height: 33px;
        cursor: pointer;
        position: absolute;
        top: 22px;
        left: 31px;
        line-height: 33px;
      }
    }
  }

  .centerC {
    font-size: 12px;
    text-align: center;
    color: #fff;
    position: absolute;
    width: 240px;
    /* border: 1px solid red; */
    left: 347px;
    top: 5px;
    height: 242px;
    > p {
      padding: 21px 0 0 0;
    }
  }
  .center10 {
    position: absolute;
    /* border: 1px solid red; */
    width: 69px;
    left: 83px;
    top: 92px;
    /* height: 64px; */
    text-align: center;
    font-weight: bold;
  }
  .center101 {
    position: absolute;
    left: 27px;
    top: 176px;
    font-weight: bold;
    > div {
      color: rgba(255, 255, 255, 0.6);
      font-weight: normal;
    }
  }
  .center102 {
    position: absolute;
    top: 176px;
    left: 116px;
    font-weight: bold;
    > div {
      color: rgba(255, 255, 255, 0.6);
      font-weight: normal;
    }
  }
  .centerR {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 300px;
    /* border: 1px solid red; */
    height: 123px;
    right: 18px;
    top: 133px;
    color: #33bbff;
    > p {
      position: absolute;
      left: 122px;
      bottom: 11px;
      margin: 0;
    }
    .center12 {
      //特殊时期
      position: absolute;
      left: 178px;
      top: 16px;
      span {
        font-weight: bold;
      }
    }
    .center13 {
      position: absolute;
      left: 32px;
      top: 25px;
      width: 61px;
      height: 46px;
      /* border: 1px solid red; */
      line-height: 46px;
      font-weight: bold;
      color: #fff;
    }
    .center14 {
      //自然环境
      position: absolute;
      left: 178px;
      top: 41px;
      span {
        font-weight: bold;
      }
    }
    .center15 {
      position: absolute;
      left: 178px;
      top: 65px;
      span {
        font-weight: bold;
      }
    }
  }
  .k4 {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 146px;
    height: 90px;
    // border: 1px solid red;
    height: 72px;
    right: 18px;
    top: 44px;
    color: #33bbff;
    > p {
      text-align: right;
      padding: 5px 10px 0 0;
      font-weight: bold;
      margin: 0;
      line-height: 12px;
    }
    .con {
      line-height: 15px;
    }
    .con > p {
      margin: 0;
      color: rgba(51, 187, 255, 0.6);
    }
  }
  .k3 {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 140px;
    height: 90px;
    /* border: 1px solid red; */
    height: 69px;
    right: 180px;
    top: 44px;
    color: #00ffff;
    > p {
      text-align: right;
      padding: 5px 10px 0 0;
      font-weight: bold;
      margin: 0;
      line-height: 12px;
    }
    .con {
      line-height: 15px;
    }
    .con > p {
      margin: 0;
      color: rgba(0, 255, 255, 0.6);
    }
  }
  .major {
    position: absolute;
    right: 87px;
    top: 375px;
    background: url("../../../../../static/img/重大隐患.png") no-repeat center;
    width: 150px;
    background-size: 100%;
    height: 123px;
    /* padding: 20px 0 0 0; */
    color: #ea6544;
    font-size: 12px;
    > p {
      text-align: center;
      margin-top: -10px;
      padding: 0 0 0 27px;
    }
    > div.num {
      width: 67px;
      margin: 0 0 0 59px;
      height: 61px;
      position: absolute;
      top: 30px;
      line-height: 61px;
      text-align: center;
      font-weight: bold;
    }
  }

  .centerBottomItem2 {
    position: absolute;
    bottom: 50px;
    left: 362px;
    text-align: center;
    color: #fff;
    width: 218px;
    .name {
      color: #ffdcc3;
    }
    > p {
      font-size: 22px;
      margin: 0px 0 5px 0;
      span {
        font-weight: bold;
        display: inline-block;
        margin-left: 5px;
      }
    }
  }
}
</style>