<template>
  <div class="report-pc" :class="{ 'form-readonly': !isEdit }">
    <!-- 表单填写 -->
    <div class="form-step">
      <div class="form-header">
        <h3>{{ gatherContentDTO.gatherName }}</h3>
        <el-button type="primary" @click="submitForm" v-if="isEdit"
          >保存</el-button
        >
      </div>

      <div class="form-content">
        <v-form-render
          :form-json="formJson"
          :form-data="formData"
          :option-data="optionData"
          ref="vFormRender"
        ></v-form-render>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getReportDesignById,
  addReportGatherContent,
  getReportByEnterpriseCode,
  updateReportGatherContent,
  getChemicalName,
} from "@/api/smartReport";

export default {
  data() {
    return {
      formJson: {},
      formData: {},
      optionData: {},
      isEdit: false,
      gatherContentDTO: {},
      currentDropdown: null, // 当前显示的下拉框
      mockList: [], // 模拟的列表数据
      debounceTimer: null, // 改名为 debounceTimer
      gatherName: "",
      delete: (widget) => {
        this.$confirm("确认删除该化学品吗？", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }).then(async () => {
          // 获取化学品列表
          const widgetList =
            this.formJson.widgetList[10].cols[5].widgetList[0].tabs[0]
              .widgetList;

          // 如果只有一种化学品，不允许删除
          if (widgetList.length <= 1) {
            this.$message.warning("至少需要保留一种化学品");
            return;
          }

          // 获取要删除的化学品对象
          const targetWidget =
            widget.$parent.$parent.$parent.$parent.$parent.$parent.$parent
              .widget;

          // 找到要删除的化学品在列表中的索引
          const index = widgetList.findIndex(
            (item) => item.id === targetWidget.id
          );

          if (index !== -1) {
            // 获取要删除的化学品序号
            let chemNumber = null;
            const match = targetWidget.options.label.match(/第(\d+)种/);
            if (match && match[1]) {
              chemNumber = parseInt(match[1]);
            }

            // 从列表中删除该化学品
            widgetList.splice(index, 1);

            // 如果找到了化学品序号，删除formData中对应的数据
            if (chemNumber !== null) {
              // 删除formData中与该化学品相关的所有字段
              const fieldsToDelete = [
                `productName${chemNumber}`,
                `operateType${chemNumber}`,
                `salesVolume${chemNumber}`,
                `storage${chemNumber}`,
                `storageNumber${chemNumber}`,
                `designNumber${chemNumber}`,
                `storeroomNumber${chemNumber}`,
                `roomDesign${chemNumber}`,
              ];

              // 从 formData 中删除这些字段
              fieldsToDelete.forEach((field) => {
                if (this.formData.hasOwnProperty(field)) {
                  delete this.formData[field];
                }
              });

              console.log(`已删除第${chemNumber}种化学品的数据`);
            }

            // 更新表单
            const temp = { ...(await this.$refs.vFormRender.getFormData()) };
            this.$refs.vFormRender.setFormJson(this.formJson);
            this.$refs.vFormRender.setFormData(temp);
            this.$message.success("已删除化学品");
          } else {
            this.$message.error("未找到要删除的化学品");
          }
        });
      },
    };
  },

  computed: {
    // 是否定制化
    isCustomized() {
      return this.gatherName.includes("危险化学品经营许可申请书");
    },
  },

  methods: {
    // 将节流改为防抖
    debounce(fn, delay = 300) {
      return (...args) => {
        if (this.debounceTimer) {
          clearTimeout(this.debounceTimer);
        }
        this.debounceTimer = setTimeout(() => {
          fn.apply(this, args);
          this.debounceTimer = null;
        }, delay);
      };
    },

    // 实际的回调处理函数
    async handleCallback(val, widget, name) {
      // 清除之前可能存在的下拉框
      this.removeDropdown();

      // 如果输入值为空，直接返回
      if (!val) {
        return;
      }

      const res = await getChemicalName({
        operateType: name,
        productName: val,
      });
      this.mockList = res.data.data;

      // 创建下拉框
      if (widget && widget.$el) {
        const inputEl = widget.$el;
        const rect = inputEl.getBoundingClientRect();

        // 更新下拉框位置的函数
        const updateDropdownPosition = () => {
          const newRect = inputEl.getBoundingClientRect();
          if (this.currentDropdown) {
            this.currentDropdown.style.left = `${newRect.left + 80}px`;
            this.currentDropdown.style.top = `${newRect.bottom}px`;
          }
        };

        // 创建下拉框元素
        const dropdown = document.createElement("div");
        dropdown.className = "dynamic-dropdown";
        dropdown.style.position = "fixed";
        // 设置初始位置
        dropdown.style.left = `${rect.left + 80}px`;
        dropdown.style.top = `${rect.bottom}px`;
        dropdown.style.width = `${rect.width - 80}px`;
        dropdown.style.zIndex = "1000";
        dropdown.style.backgroundColor = "#fff";
        dropdown.style.border = "1px solid #ccc";
        dropdown.style.boxShadow = "0 2px 4px rgba(0,0,0,0.1)";
        dropdown.style.maxHeight = "150px";
        dropdown.style.overflowY = "auto";

        // 添加滚动事件监听
        window.addEventListener("scroll", updateDropdownPosition);
        const formContent = document.querySelector(".form-content");
        if (formContent) {
          formContent.addEventListener("scroll", updateDropdownPosition);
        }

        // 创建选项列表
        if (this.mockList && this.mockList.length > 0) {
          const ul = document.createElement("ul");
          ul.style.listStyleType = "none";
          ul.style.padding = "0";
          ul.style.margin = "0";

          this.mockList.forEach((option) => {
            const li = document.createElement("li");
            li.textContent = option;
            li.style.padding = "8px";
            li.style.cursor = "pointer";

            // 鼠标悬停效果
            li.addEventListener("mouseover", () => {
              li.style.backgroundColor = "#f0f0f0";
            });
            li.addEventListener("mouseout", () => {
              li.style.backgroundColor = "";
            });

            // 点击选项时的处理
            li.addEventListener("click", () => {
              // 设置输入框的值
              if (widget.setValue) {
                widget.setValue(option);
              }
              // 移除下拉框
              this.removeDropdown();
            });

            ul.appendChild(li);
          });

          dropdown.appendChild(ul);
        }

        document.body.appendChild(dropdown);
        this.currentDropdown = dropdown;

        // 在组件销毁时移除事件监听
        this.$once("hook:beforeDestroy", () => {
          window.removeEventListener("scroll", updateDropdownPosition);
          if (formContent) {
            formContent.removeEventListener("scroll", updateDropdownPosition);
          }
        });
      }
    },

    // 将 callBack 改为使用防抖版本
    callBack(val, widget, name) {
      this.debounce(this.handleCallback)(val, widget, name);
    },

    // 新增化学品功能
    async sava() {
      // 获取化学品列表
      const widgetList =
        this.formJson.widgetList[10].cols[5].widgetList[0].tabs[0].widgetList;

      // 获取第一个化学品对象作为模板
      const firstChem = widgetList[0];

      // 查找当前化学品的序号
      let maxNum = 1;

      for (let i = 0; i < widgetList.length; i++) {
        const widget = widgetList[i];
        // 从标签中提取序号，如"第一种"中的"1"
        const match = widget.options.label.match(/第(\d+)种/);
        if (match && parseInt(match[1]) > maxNum) {
          maxNum = parseInt(match[1]);
        }
      }

      // 新的序号
      const newNum = maxNum + 1;

      // 创建新的化学品对象
      const newChem = this.createNewChem(firstChem, newNum);

      // 将新的化学品对象添加到列表中
      widgetList.push(newChem);

      // 更新表单
      const temp = { ...(await this.$refs.vFormRender.getFormData()) };
      this.$refs.vFormRender.setFormJson(this.formJson);
      this.$refs.vFormRender.setFormData(temp);
      this.$message.success(`已添加第${newNum}种化学品`);
    },

    // 创建新的化学品对象
    createNewChem(sourceChem, newNum) {
      // 深拷贝源对象
      const newChem = JSON.parse(JSON.stringify(sourceChem));

      // 更新label
      newChem.options.label = `第${newNum}种`;
      // 生成新的随机ID
      newChem.id = `card${this.generateRandomId()}`;

      // 更新widgetList中的每个组件
      for (let i = 0; i < newChem.widgetList.length; i++) {
        const widget = newChem.widgetList[i];

        // 更新name属性，将数字部分替换为新的序号
        if (widget.options && widget.options.name) {
          widget.options.name = widget.options.name.replace(/\d+$/, newNum);
        }

        // 生成新的随机ID
        widget.id = `${widget.type}${this.generateRandomId()}`;

        // 如果是容器类型，递归处理其子组件
        if (widget.type === "grid" && widget.cols) {
          for (let j = 0; j < widget.cols.length; j++) {
            const col = widget.cols[j];
            // 更新col的ID
            col.id = `grid-col-${this.generateRandomId()}`;
            if (col.options && col.options.name) {
              col.options.name = `gridCol${this.generateRandomId()}`;
            }

            // 处理col中的widgetList
            if (col.widgetList && col.widgetList.length > 0) {
              for (let k = 0; k < col.widgetList.length; k++) {
                const subWidget = col.widgetList[k];
                // 更新name属性
                if (subWidget.options && subWidget.options.name) {
                  subWidget.options.name = subWidget.options.name.replace(
                    /\d+$/,
                    newNum
                  );
                }
                // 生成新的随机ID
                subWidget.id = `${subWidget.type}${this.generateRandomId()}`;
              }
            }
          }
        }
      }

      return newChem;
    },

    // 生成5位随机数字ID
    generateRandomId() {
      return Math.floor(10000 + Math.random() * 90000);
    },

    // 初始化数据
    async initData(row) {
      if (row) {
        this.isEdit = row.isEdit;
        await this.getReportContent(row);
        await this.getReportDesignById(row);
        const res = await getChemicalName({
          enterpriseCode: row.enterpriseCode,
        });
        this.mockList = res.data.data;
      }
    },

    // 获取表单设计数据
    async getReportDesignById(row) {
      try {
        const res = await getReportDesignById({ id: row.gatherId });
        if (res.data.status === 200 && res.data.data.textContent) {
          this.$nextTick(async () => {
            this.formJson = JSON.parse(res.data.data.textContent);
            this.gatherName = res.data.data.gatherName;
            this.addUploadTokenToForm();

            if (this.isCustomized) {
              this.$refs.vFormRender.callback = this.callBack;
              this.$refs.vFormRender.sava = this.sava;
              this.$refs.vFormRender.delete = this.delete;
            }

            const temp = { ...(await this.$refs.vFormRender.getFormData()) };
            this.$refs.vFormRender.setFormJson(this.formJson);
            this.$refs.vFormRender.setFormData(temp);

            if (this.isEdit && this.isCustomized) {
              const contentJson = JSON.parse(this.gatherContentDTO.contentJson);
              // 在表单加载完成后调整化学品数量
              this.$nextTick(() => {
                // 确保表单已经加载
                if (this.formJson && this.formJson.widgetList) {
                  this.adjustChemicalItems(contentJson);
                }
              });
            }
          });
        }
      } catch (error) {
        console.error("获取表单失败:", error);
        this.$message.error("获取表单失败");
      }
    },

    // 处理文件上传下载配置
    addUploadTokenToForm() {
      const processWidget = (widget) => {
        // 处理当前组件
        if (widget.type === "picture-upload" || widget.type === "file-upload") {
          widget.options.uploadURL = "/gapi/gemp-file/api/attachment/upload/v1";
          widget.options.onBeforeUpload = `var currentWidget = this;
   var token = "${
     this.$store.state.login.user.token_type +
     " " +
     this.$store.state.login.user.access_token
   }";
   currentWidget.setUploadHeader('token', token);
   delete currentWidget.uploadData.key`;
          widget.options.onFileDownload = `
          let par = {
      fileId: file.attachId || file.response.attachId
    }
    this.$Attachmentdownload(par).then((res) => {
      console.log(res,'特殊作业管理下载')
      this.$downloadFuc(res)
    })`;
        }

        // 递归处理所有可能的子组件结构
        const processChildren = (widget) => {
          // 处理常规widgetList
          if (widget.widgetList) {
            widget.widgetList.forEach((child) => processWidget(child));
          }
          // 处理表格rows结构
          if (widget.rows) {
            widget.rows.forEach((row) => {
              if (row.cols) {
                row.cols.forEach((col) => {
                  if (col.widgetList) {
                    col.widgetList.forEach((child) => processWidget(child));
                  }
                });
              }
            });
          }
          if (widget.cols) {
            widget.cols.forEach((col) => {
              if (col.widgetList) {
                col.widgetList.forEach((child) => processWidget(child));
              }
            });
          }
        };

        processChildren(widget);
      };

      if (this.formJson?.widgetList) {
        this.formJson.widgetList.forEach((widget) => processWidget(widget));
      }
    },

    // 调整化学品项目数量
    async adjustChemicalItems(contentJson) {
      try {
        // 识别已有的化学品数量
        const productNameKeys = Object.keys(contentJson).filter(
          (key) => key.startsWith("productName") && /productName\d+$/.test(key)
        );

        if (productNameKeys.length <= 1) {
          return; // 只有一种或没有化学品，不需要调整
        }

        // 提取数字部分并排序
        const numbers = productNameKeys
          .map((key) => parseInt(key.replace("productName", ""), 10))
          .filter((num) => !isNaN(num))
          .sort((a, b) => a - b);

        if (numbers.length <= 1) {
          return;
        }

        // 获取化学品列表
        const widgetList =
          this.formJson.widgetList[10].cols[5].widgetList[0].tabs[0].widgetList;

        // 如果表单中的化学品数量小于数据中的化学品数量，需要添加更多
        for (let i = 1; i < numbers.length; i++) {
          if (i >= widgetList.length) {
            // 创建新的化学品并添加
            const newChem = this.createNewChem(widgetList[0], numbers[i]);
            widgetList.push(newChem);
          }
        }

        // 更新表单
        const temp = { ...(await this.$refs.vFormRender.getFormData()) };
        this.$refs.vFormRender.setFormJson(this.formJson);
        this.$refs.vFormRender.setFormData(temp);
      } catch (error) {
        console.error("调整化学品数量失败:", error);
      }
    },

    // 获取报表内容
    async getReportContent(row) {
      try {
        // 查询企业填报记录
        const reportRes = await getReportByEnterpriseCode({
          enterpId: row.enterpId,
          gatherId: row.gatherId,
        });
        if (reportRes.data.status === 200 && reportRes.data.data) {
          // 有填报记录，进入编辑模式
          this.gatherContentDTO = reportRes.data.data.gatherContentDTO;
          this.formData = {
            ...this.formData,
            ...JSON.parse(this.gatherContentDTO.contentJson),
          };
        } else {
          // 无填报记录，进入新增模式
          this.isEdit = false;
        }
      } catch (error) {
        console.error("获取报表内容失败:", error);
        this.$message.error("获取报表内容失败");
      }
    },

    // 提交表单
    async submitForm() {
      try {
        const contentJson = await this.$refs.vFormRender.getFormData();
        const params = {
          contentJson: JSON.stringify(contentJson),
          gatherId: this.gatherContentDTO.gatherId,
          director: contentJson.mainPeople || this.gatherContentDTO.director,
          phone: contentJson.mainPeoplePhone || this.gatherContentDTO.phone,
          enterpriseName:
            contentJson.enterpriseName || this.gatherContentDTO.enterpriseName,
          enterpriseAddress:
            contentJson.enterpriseAddress ||
            this.gatherContentDTO.enterpriseAddress,
          enterpId: this.gatherContentDTO.enterpId,
        };
        let res;
        if (this.isEdit) {
          // 编辑模式需要添加ID
          params.id = this.gatherContentDTO.id;
          res = await updateReportGatherContent(params);
        } else {
          res = await addReportGatherContent(params);
        }

        if (res.data.status === 200) {
          this.$message.success("保存成功");
          this.$emit("close");
        } else {
          this.$message.error(res.data.msg || "保存失败");
        }
      } catch (error) {
        console.error("保存失败:", error);
        this.$message.error(error);
      }
    },

    // 修改 removeDropdown 方法
    removeDropdown() {
      if (
        this.currentDropdown &&
        document.body.contains(this.currentDropdown)
      ) {
        // 移除所有相关的事件监听器
        window.removeEventListener("scroll", this.updateDropdownPosition);
        const formContent = document.querySelector(".form-content");
        if (formContent) {
          formContent.removeEventListener(
            "scroll",
            this.updateDropdownPosition
          );
        }

        document.body.removeChild(this.currentDropdown);
        this.currentDropdown = null;
      }
    },
  },

  // 组件销毁时清理
  beforeDestroy() {
    // 清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }
    this.removeDropdown();
  },
};
</script>

<style lang="scss" scoped>
.report-pc {
  height: calc(90vh - 108px); // 适应弹窗高度
  width: 100%;
  background-color: #fff;

  // 只读模式样式
  &.form-readonly {
    ::v-deep {
      // 禁用所有输入框
      .el-input__inner,
      .el-textarea__inner {
        background-color: #f5f7fa !important;
        border-color: #e4e7ed !important;
        color: #606266 !important;
        cursor: not-allowed !important;
      }

      // 禁用所有按钮和可点击元素
      .el-radio__input,
      .el-checkbox__input,
      .el-switch,
      .el-slider__button-wrapper {
        pointer-events: none !important;
      }

      // 禁用下拉框
      .el-select .el-input.is-disabled .el-input__inner {
        background-color: #f5f7fa !important;
        border-color: #e4e7ed !important;
      }

      // 禁用日期选择器
      .el-date-editor.is-disabled {
        background-color: #f5f7fa !important;
      }

      // 禁用文件上传
      .el-upload {
        pointer-events: none !important;
      }

      // 禁用所有可点击和可输入元素
      input,
      textarea,
      select,
      .el-input,
      .el-select,
      .el-date-editor,
      .el-cascader,
      .el-time-picker,
      .el-color-picker {
        pointer-events: none !important;
      }

      // 禁用单选框组和复选框组
      .el-radio-group,
      .el-checkbox-group {
        pointer-events: none !important;

        .el-radio,
        .el-checkbox {
          cursor: not-allowed !important;

          .el-radio__input,
          .el-checkbox__input {
            cursor: not-allowed !important;

            .el-radio__inner,
            .el-checkbox__inner {
              background-color: #f5f7fa !important;
              border-color: #e4e7ed !important;
              cursor: not-allowed !important;
            }
          }

          .el-radio__label,
          .el-checkbox__label {
            color: #909399 !important;
            cursor: not-allowed !important;
          }
        }

        // 保持选中状态的样式
        .el-radio.is-checked,
        .el-checkbox.is-checked {
          .el-radio__input.is-checked .el-radio__inner,
          .el-checkbox__input.is-checked .el-checkbox__inner {
            background-color: #409eff !important;
            border-color: #409eff !important;
          }

          .el-radio__label,
          .el-checkbox__label {
            color: #606266 !important;
          }
        }
      }
    }
  }

  .form-step {
    height: 100%;
    display: flex;
    flex-direction: column;

    .form-header {
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h3 {
        margin: 0;
        font-size: 16px;
      }
    }

    .form-content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
    }
  }
}
</style>
