<template>
  <div class="historySpotCheck">
    <div class="header">
       <el-input
            v-model.trim="keywords"
            size="mini"
            placeholder="关键字搜索"
            class="input"
            clearable
            style="width: 200px"
          ></el-input>
      <!-- <el-date-picker v-model="value1"
                      size="mini"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="searchTime"
                      unlink-panels
                      :picker-options="{
          disabledDate: (time) => {
            return (
              time.getTime() >
              new Date(new Date().Format('yyyy-MM-dd 23:59:59')).getTime()
            );
          },
        }"
                      style="width: 370px"></el-date-picker> -->
      <!-- <el-select
        v-model="feedbackStatus"
        placeholder="请选择状态"
        size="mini"
        style="width: 190px"
        clearable
        v-show="this.$store.state.login.user.user_type == 'gov'"
      >
        <el-option
          v-for="(item, index) in optionses"
          :key="index"
          :label="item.label"
          :value="item.value"
        >
        </el-option>
      </el-select> -->
      <el-button type="primary"
                 style="margin-top: 10px"
                 size="mini"
                 @click="search">查询</el-button>
    </div>
    <div class="container">
      <div class="title">现场核查与行政检查</div>
      <div>
          
        <div class="table">         
          <el-table
            :data="tableData"
             border
                  v-loading="loading"
                  style="width: 100%"
                  :header-cell-style="{
            textAlign: 'center',
            color: 'rgb(51, 51, 51)',
            backgroundColor: 'rgb(242, 246, 255)',
          }"
          >
            <el-table-column
              type="index"
              label="序号"
              width="50"
              align="center"
            >
            </el-table-column>


              <el-table-column
              prop="documentNum"
              label="文件编号"
              align="center"
            >
            </el-table-column>
               <el-table-column
              prop="enterpName"
              label="检查对象名称"
              align="center"
            >
            </el-table-column>
               <el-table-column
              prop="parentOrgName"
              label="检查机构"
              align="center"
            >
            </el-table-column>
               <el-table-column
              prop="orgName"
              label="检查部门"
              align="center"
              width="150"
            >
            </el-table-column>
               <el-table-column
              prop="startTime"
              label="检查开始时间"
              align="center"
              width="180"
            >
            </el-table-column>
               <el-table-column
              prop="dataSource"
              label="数据来源"
              align="center"
              width="150"
            >
            </el-table-column>

              <el-table-column
              prop="inspectPerson"
              label="检查员"
              align="center"
              width="150"
            >
            </el-table-column>

<!-- 
            <el-table-column label="操作" width="100" align="center">
                <template slot-scope="{ row }">
                  <div>                 
                    <el-button type="text" @click="clickBan(row)"
                      >办理</el-button
                    >
                  </div>
                </template>
              </el-table-column> -->



          </el-table>
        </div>
     
        <!-- <el-table :data="tableData"
                  border
                  v-loading="loading"
                  style="width: 100%"
                  :header-cell-style="{
            textAlign: 'center',
            color: 'rgb(51, 51, 51)',
            backgroundColor: 'rgb(242, 246, 255)',
          }"
                  @selection-change="handleSelectionChange">
          <el-table-column type="index"
                           label="序号"
                           width="60">
          </el-table-column>
          <el-table-column prop="distName"
                           label="行政区划"
                           width="180">
          </el-table-column>
          <el-table-column prop="companyName"
                           label="企业名称"
                           width="160"
                           :show-overflow-tooltip="true">
          </el-table-column>
          <el-table-column prop="dangerLevelCode"
                           label="危险源等级">
            <template slot-scope="scope">
              <span v-if="scope.row.dangerLevelCode == '1'">一级</span>
              <span v-else-if="scope.row.dangerLevelCode == '2'">二级</span>
              <span v-else-if="scope.row.dangerLevelCode == '3'">三级</span>
              <span v-else-if="scope.row.dangerLevelCode == '4'">四级</span>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column prop="online"
                           label="系统在线"
                           width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.online == '0'">离线</span>
              <span v-else-if="scope.row.online == '1'">在线</span>
            </template>
          </el-table-column>
          <el-table-column prop="videoOnline"
                           label="视频在线"
                           width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.videoOnline == '0'">离线</span>
              <span v-else-if="scope.row.videoOnline == '1'">在线</span>
            </template>
          </el-table-column>
          <el-table-column prop="warnRank"
                           label="预警等级"
                           width="80px">
            <template slot-scope="scope">
              <span v-if="scope.row.warnRank == '1'">红色预警</span>
              <span v-else-if="scope.row.warnRank == '2'">橙色预警</span>
              <span v-else-if="scope.row.warnRank == '3'">黄色预警</span>
              <span v-else>蓝色预警</span>
            </template>
          </el-table-column>
          <el-table-column prop="commitStatus"
                           label="是否安全承诺"
                           width="110px">
            <template slot-scope="scope">
              <span v-if="scope.row.commitStatus == '0'">否</span>
              <span v-else-if="scope.row.commitStatus == '1'">是</span>
            </template>
          </el-table-column>
          <el-table-column prop="alarmCount"
                           label="未消警次数"
                           width="100px">
          </el-table-column>
          <el-table-column prop="isLastWeekHignWarnRank"
                           label="7日内是否出现过较大及以上安全风险等级"
                           width="150px">
          
          </el-table-column>
          <el-table-column prop="patrolledTime"
                           label="抽查时间"
                           width="160px">
          </el-table-column>
          <el-table-column prop="patrolledBy"
                           label="抽查人"
                           width="90px">
          </el-table-column>
          <el-table-column prop="feedbackStatus"
                           label="状态"
                           width="150px">
            <template slot-scope="scope">
              <span v-if="scope.row.feedbackStatus == '2'">已反馈</span>
              <span v-else>未反馈</span>
            </template>
          </el-table-column>
          <el-table-column prop="feedbackTime"
                           label="反馈时间"
                           width="160px">
          </el-table-column>
          <el-table-column prop="address"
                           label="操作"
                           min-width="150px"
                           fixed="right">
            <template slot-scope="scope">
              <el-button type="text"
                         @click="addSpotCheckFeedback(scope.row)"
                         v-if="scope.row.feedbackStatus != '2'">反馈</el-button>
              <el-button type="text"
                         @click="addSpotCheckDetails(scope.row)"
                         v-if="scope.row.feedbackStatus == '2'">反馈详情</el-button>
            </template>
          </el-table-column>
        </el-table> -->
        <div class="pagination">
          <el-pagination @size-change="handleSizeChange"
                         @current-change="handleCurrentChange"
                         :current-page.sync="currentPage"
                         background
                         layout="total, prev, pager, next"
                         :total="total"
                         v-if="total != 0">
          </el-pagination>
        </div>
      </div>
    </div>
    <div class="">
      <el-dialog title="企业反馈详情"
                 :visible.sync="showSpotCheckDetails"
                 width="740px"
                 v-dialog-drag
                 :close-on-click-modal="false"
                 top="10vh">
                 <el-scrollbar style="height:600px">
        <el-descriptions :column="1"
                         border
                         :labelStyle="labelStyle">
          <el-descriptions-item>
            <template slot="label"> 企业名称 </template>
            {{ spotDetail.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查反馈类型 </template>
            {{ spotDetail.replyTypeContent }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 其他情况说明 </template>
            {{ spotDetail.otherContent }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 是否完成处置 </template>
            {{ spotDetail.isFinish == 1 ? "是" : "否" }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 计划完成日期 </template>
            {{ new Date(spotDetail.planDate).Format("yyyy-MM-dd") }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查人 </template>
            {{ spotDetail.patrolledBy }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查单位 </template>
            {{ spotDetail.patrolledUnit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 企业情况说明 </template>
            {{ spotDetail.situation }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 处置措施 </template>
            {{ spotDetail.measure }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 延期情况说明 </template>
            {{ spotDetail.delaySituation }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 反馈时间 </template>
            {{ spotDetail.feedbackTime }}
          </el-descriptions-item>
        </el-descriptions>
                 </el-scrollbar>
      </el-dialog>

      <el-dialog title="企业反馈"
                 :visible.sync="showSpotCheckFeedback"
                 width="740px"
                 top="10vh"
                 :close-on-click-modal="false"
                 v-dialog-drag>
        <el-descriptions :column="1"
                         border
                         :labelStyle="labelStyle"
                         style="height: 600px; overflow: auto">
          <el-descriptions-item>
            <template slot="label"> 企业名称 </template>
            {{ spotDetail.companyName }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查反馈类型 </template>
            <!-- <div>1.安全风险等级：于2021/10/15达到一般风险。</div>
            <div>2.安全承诺：未承诺。</div>
            <div>3.未销警数量：3次。</div>
            <div>
              4.实时监测异常情况：截止2021-10-15 10:55:32，实时监测全部离线。
            </div> -->
            {{ spotDetail.replyTypeContent }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 其他情况说明 </template>
            {{ spotDetail.otherContent }}
          </el-descriptions-item>

          <el-descriptions-item>
            <template slot="label">
              <span style="color: red">*</span> 是否完成处置
            </template>
            <el-select v-model="isFinish"
                       placeholder="请选择">
              <el-option v-for="item in options"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 计划完成日期 </template>
            <el-date-picker v-model="planDate"
                            type="date"
                            value-format="yyyy-MM-dd 00:00:00"
                            placeholder="选择日期时间">
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              情况说明
            </template>
            <el-input v-model.trim="situation"
                      type="textarea"
                      placeholder="最多可输入500字"
                      maxlength="500"
                      show-word-limit
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      resize="none">
            </el-input>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              <span style="color: red">*</span> 处置措施
            </template>
            <el-input v-model.trim="measure"
                      type="textarea"
                      placeholder="最多可输入500字"
                      maxlength="500"
                      show-word-limit
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      resize="none">
            </el-input>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              延期日期
            </template>
            <el-date-picker v-model="delayDate"
                            type="date"
                            placeholder="选择日期"
                            value-format="yyyy-MM-dd 00:00:00">
            </el-date-picker>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label">
              延期情况说明
            </template>
            <el-input v-model.trim="delaySituation"
                      type="textarea"
                      placeholder="最多可输入500字"
                      maxlength="500"
                      show-word-limit
                      :autosize="{ minRows: 4, maxRows: 8 }"
                      resize="none">
            </el-input>
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查人 </template>
            {{ spotDetail.patrolledBy }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查单位 </template>
            {{ spotDetail.patrolledUnit }}
          </el-descriptions-item>
          <el-descriptions-item>
            <template slot="label"> 抽查时间 </template>
            {{ spotDetail.patrolledTime }}
          </el-descriptions-item>
        </el-descriptions>
        <span slot="footer"
              class="dialog-footer">
          <!-- <el-button @click="showSpotCheckFeedback = false" size="mini">取 消</el-button>
            <el-button type="primary" @click="showSpotCheckFeedback = false" size="mini">提 交</el-button> -->
          <el-button @click="showSpotCheckFeedback = false"
                     size="mini">取 消</el-button>
          <el-button type="primary"
                     @click="feedBackSpotCheck"
                     size="mini">提 交</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { MessageBox } from 'element-ui'
import { Message } from 'element-ui'
import { parseTime } from '@/utils/index'
import { 
  spotInspectFindPage
} from "@/api/riskAssessment";
import {
  querySpotCheckHis,
  spotCheckHisDetail,
  addSpotCheckes
} from '@/api/dailySafety'
export default {
  name: 'otherInspections',
  data() {
    return {
      value1: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (720 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900
      ],
      keywords:"",
      delayDate: '',
      delaySituation: '',
      startTime: '',
      endTime: '',
      feedbackStatus: '',
      showSpotCheck: false,
      showSpotCheckDetails: false,
      showSpotCheckFeedback: false,
      spotCheckDetail: {},
      spotDetail: {},
      checkList: [],
      replyType: '',
      systemFlag: '',
      otherContent: '',
      currentPage: 1,
      situation: '', //反馈说明
      measure: '', //企业反馈处置措施
      size: 10,
      total: 0,
      loading: true,
      labelStyle: {
        textAlign: 'center',
        width: '200px',
        backgroundColor: 'rgb(242, 246, 255)'
      },
      multipleSelection: [],
      tableData: [],
      sceneData: [], //现场核查和行政检查
      options: [
        {
          value: '1',
          label: '是'
        },
        {
          value: '0',
          label: '否'
        }
      ],
      isFinish: '',
      planDate: ''
    }
  },
  mounted(){
    this.getQuerySpotCheckHis()
  },

  methods: {
    //获取历史抽查记录列表 - 企业
    getQuerySpotCheckHis() {
      this.loading = true
       var params={
         "keywords": this.keywords,
          "page": 1,
          "pageSize": this.size,
          "startTime": "",
          "creditCode":this.$store.state.login.user.user_type == 'ent'?this.$store.state.login.enterData.entcreditCode:""
      }
       spotInspectFindPage(params).then(res => {
        //  debugger
        if (res.data.code == 0) { 
          this.tableData = res.data.data.records;      
          this.total =  res.data.data.total;     
         }
        
      }).finally(() => {
        this.loading = false
      })
      // querySpotCheckHis({
      //   current: this.currentPage,
      //   startTime:
      //     this.startTime ||
      //     parseTime(
      //       new Date(new Date().toLocaleDateString()).getTime() -
      //         (720 * 60 * 60 * 1000 - 1),
      //       '{y}-{m}-{d} {h}:{i}:{s}'
      //     ),
      //   size: this.size,
      //   endTime:
      //     this.endTime ||
      //     parseTime(
      //       new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      //       '{y}-{m}-{d} {h}:{i}:{s}'
      //     ),
      //   companyCode: this.$store.state.login.enterData.enterpId
      //   // feedbackStatus:this.feedbackStatus,
      // }).then(res => {
      //   if (res.data.code == 0) {
      //     this.loading = false
      //     this.tableData = res.data.data.records
      //     this.total = res.data.data.total
      //   }
      // })
    },
    // add0(m){return m<10?'0'+m:m },
    // format(shijianchuo){
    //     var time = new Date(shijianchuo);
    //     var y = time.getFullYear();
    //     var m = time.getMonth()+1;
    //     var d = time.getDate();
    //     var h = time.getHours();
    //     var mm = time.getMinutes();
    //     var s = time.getSeconds();
    //     return y+'-'+this.add0(m)+'-'+this.add0(d)+' '+this.add0(h)+':'+this.add0(mm)+':'+this.add0(s);
    // },
    searchTime(value) {
      if (value) {
        let date1 = new Date(value[0])
        let dataTime1 = parseTime(date1, '{y}-{m}-{d} {h}:{i}:{s}')
        let date2 = new Date(value[1])
        let dataTime2 = parseTime(date2, '{y}-{m}-{d}') + ' 23:59:59'
        this.startTime = dataTime1
        this.endTime = dataTime2
      } else {
        this.value1 = ''
        this.startTime = ''
        this.endTime = ''
      }
    },
    search() {
      this.currentPage = 1
      this.getQuerySpotCheckHis()
    },
    addSpotCheck(value) {
      this.showSpotCheck = true
      this.spotCheckDetail = value
      if (this.spotCheckDetail.replyType) {
        this.checkList = this.spotCheckDetail.replyType.split(',')
      } else {
        this.checkList = []
      }
    },
    //反馈详情
    addSpotCheckDetails(value) {
      spotCheckHisDetail({
        id: value.id
      }).then(res => {
        if (res.data.code == 0) {
          this.showSpotCheckDetails = true
          this.spotDetail = res.data.data
        }
      })
    },
    //反馈
    addSpotCheckFeedback(value) {
      this.replyType = value.replyType
      this.systemFlag = value.systemFlag
      spotCheckHisDetail({
        id: value.id
      }).then(res => {
        if (res.data.code == 0) {
          this.showSpotCheckFeedback = true
          this.spotDetail = res.data.data
          this.spotDetail.id = value.id
        }
      })
    },
    //企业反馈
    feedBackSpotCheck() {
      //   if (!this.situation) {
      //     Message.error("反馈情况说明不能为空！");
      //     return;
      //   }
      if (!this.measure) {
        // Message.error("企业反馈处置措施不能为空！");
        this.$message({
          type: 'error',
          message: '企业反馈处置措施不能为空！'
        })
        return
      }
      if (!this.isFinish) {
        // Message.error("是否完成处置不能为空！");
        this.$message({
          type: 'error',
          message: '是否完成处置不能为空！'
        })
        return
      }
      addSpotCheckes({
        inspectId: this.spotDetail.id,
        situation: this.situation,
        measure: this.measure,
        planDate: this.planDate,
        delayDate: this.delayDate,
        delaySituation: this.delaySituation,
        isFinish: this.isFinish,
        replyType: this.replyType,
        systemFlag: this.systemFlag
      }).then(res => {
        if (res.data.code == 0) {
          Message.success(res.data.data)
          this.showSpotCheckFeedback = false
          this.situation = ''
          this.measure = ''
          this.planDate = ''
          this.isFinish = ''
          this.getQuerySpotCheckHis()
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`)
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.getQuerySpotCheckHis()
    }
  }
}
</script>

<style lang="scss" scoped>

/deep/ .el-scrollbar__wrap
{
  overflow-x: hidden;
}
/deep/ .el-select__tags-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 90px;
  display: block;
}
/deep/ .cell .el-button {
  padding: 0;
}
.historySpotCheck {
  .header {
    margin-bottom: 20px;
    & > * {
      margin-right: 20px;
    }
  }
  .container {
    .title {
      font-size: 18px;
      width: 100%;
      text-align: left;
      padding-bottom: 10px;
      font-weight: 900;
    }
  }
  .pagination {
    margin-top: 30px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
