<template>
  <div class="app-container">
    <div class="specification-from-box">
      <el-form
        :model="queryParams"
        ref="queryForm"
        :inline="true"
        v-show="showSearch"
        label-width="68px !important"
      >
        <el-form-item prop="announcementTitle">
          <el-input
            v-model.trim="queryParams.announcementTitle"
            placeholder="请输入公告标题"
            clearable
            maxlength="100"
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="instCode">
          <el-input
            v-model.trim="queryParams.issueUnitName"
            placeholder="请输入发布单位"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>

        <el-form-item prop="announcementStatus">
          <el-select
            v-model="queryParams.announcementStatus"
            placeholder="请选择公告状态"
            clearable
            size="small"
          >
            <el-option
              :label="item.label"
              :value="item.value"
              v-for="(item, index) in statusList"
              :key="index"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
            v-model="daterangeReleaseTime"
            size="small"
            style="width: 240px"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="searchTime"
            unlink-panels
            :clearable="false"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="handleQuery"
            >查询</el-button
          >
        </el-form-item>
        <el-form-item>
          <CA-button type="primary" size="small" plain @click="resetQuery"
            >重置</CA-button
          >
        </el-form-item>
      </el-form>
    </div>

    <div class="specification-table-head">
      <div class="left">公告列表</div>
      <div>
        <el-button
          type="primary"
          v-if="roleInfo.user_type !== 'ent'"
          plain
          icon="el-icon-plus"
          size="small"
          @click="handleAdd()"
          >添加公告</el-button
        >
        <el-button
          type="danger"
          v-if="roleInfo.user_type !== 'ent'"
          plain
          icon="el-icon-delete"
          size="small"
          :disabled="multiple"
          @click="handleDelete"
          >批量删除</el-button
        >
        <el-button
          type="success"
          v-if="roleInfo.user_type !== 'ent'"
          plain
          icon="el-icon-document"
          size="small"
          :disabled="multiple"
          @click="handlePublish"
          >批量发布</el-button
        >
      </div>
    </div>

    <el-table
      v-loading="loading"
      :data="noticeList"
      stripe
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" type="index" width="70" />
      <el-table-column label="公告标题" align="center" prop="announcementTitle"  :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <div class="title-hover">{{ scope.row.announcementTitle }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="发布时间"
        align="center"
        prop="releaseTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.releaseTime
                ? parseTime(scope.row.releaseTime, "{y}-{m}-{d}")
                : "--"
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="发布单位"
        align="center"
        prop="issueUnitName"
        width="200"
      >
        <template slot-scope="scope">
          <span
            >{{ scope.row.issueUnitName ? scope.row.issueUnitName : "--" }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="公告状态"
        align="center"
        prop="announcementStatus"
        width="120"
      >
        <template slot-scope="scope">
          <span>
            {{
              scope.row.announcementStatus == 0
                ? "待发布"
                : scope.row.announcementStatus == 1
                ? "有效"
                : "失效"
            }}
          </span>
        </template>
      </el-table-column>

      <el-table-column
        label="有效期限"
        align="center"
        prop="termOfValid"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.termOfValid, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>

      <el-table-column label="已阅/已发机构" align="center" width="120">
        <template slot-scope="{ row }">
          {{ row.noOfPeopleRead }}/{{ row.noOfSenders }}
        </template>
      </el-table-column>
      <el-table-column prop="updateTime" label="更新时间" align="center" width="180"/>
      <el-table-column label="操作" align="center" width="200">
        <template slot-scope="scope">
          <el-button type="text" @click="handleUpdate(scope.row, '查看')"
            >查看</el-button
          >
          <el-button
            v-if="scope.row.announcementStatus != 1"
            type="text"
            @click="handleUpdate(scope.row, '编辑')"
            >编辑</el-button
          >

          <!--          <el-button round size="mini" @click="updateStatusApi(scope.row)"-->
          <!--            >取消发布</el-button-->
          <!--          >-->

          <el-button
            v-if="scope.row.announcementStatus != 1"
            type="text"
            @click="handleDelete(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination">
      <el-pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.nowPage"
        :limit.sync="queryParams.pageSize"
        @current-change="handleGetList"
        background
        layout="total, prev, pager, next"
      ></el-pagination>
    </div>

    <!-- 添加或修改通知公告对话框 -->
    <el-dialog
      :title="title"
      :visible.sync="open"
      width="1000px"
      append-to-body
      @close="cancel"
      :close-on-click-modal="false"
      top="10vh"
    >
      <el-scrollbar style="height: 500px" v-loading="dialogLoading">
        <div style="">
          <el-form ref="form" :model="form" :rules="rules">
            <el-row>
              <el-col :span="8">
                <el-form-item label prop="reciveUnit">
                  <div class="leftBox" v-if="title == '查看通知公告'">
                    <div class="title">接收单位列表</div>
                    <!-- {{orgDTOsAry}} -->
                    <div class="viewCon" v-if="orgDTOsAry.length > 0">
                      <el-scrollbar style="height:500px">
                      <div
                        class="item"
                        v-for="(item, index) of orgDTOsAry"
                        :key="index"
                      >
                        {{ item.orgName }}
                      </div>
                      </el-scrollbar>
                    </div>
                    <div v-else>暂无接收单位</div>
                  </div>

                  <div class="leftBox" v-else>
                    <div class="title">接收单位列表</div>
                    <div class="con">
                      <el-input
                        placeholder="输入关键字进行过滤"
                        v-model.trim="filterText"
                      ></el-input>
                      <div
                        style="height: 350px; overflow: auto"
                        class="auto-scrollbar"
                      >
                        <el-tree
                          class="filter-tree"
                          :data="leftOrgData"
                          :props="defaultProps"
                          show-checkbox
                          node-key="id"
                          :check-strictly="true"
                          :default-checked-keys="checkedMans"
                          :filter-node-method="filterNode"
                          :default-expand-all="false"
                          ref="tree"
                        ></el-tree>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-col>

              <el-col :span="15" :offset="1">
                <el-form-item
                  label="公告标题"
                  prop="announcementTitle"
                  label-width="100px !important"
                >
                  <el-input
                    v-model.trim="form.announcementTitle"
                    placeholder="请输入公告标题"
                    :disabled="disabled"
                    maxlength="100"
                  />
                </el-form-item>
                <el-form-item
                  label="有效期限"
                  prop="termOfValid"
                  label-width="100px !important"
                >
                  <el-date-picker
                    clearable
                    size=""
                    v-model="form.termOfValid"
                    type="date"
                    :picker-options="pickerOptions"
                    value-format="yyyy-MM-dd"
                    placeholder="选择有效期限"
                    :disabled="disabled"
                  ></el-date-picker>
                </el-form-item>
                <el-form-item
                  label="公告内容"
                  label-width="100px !important"
                  prop="announcementContent"
                >
                  <div v-if="!disabled">
                    <CA-editor
                      v-model="form.announcementContent"
                      :min-height="192"
                    />
                  </div>

                  <div
                    v-else
                    class="announcementContent"
                    v-html="form.announcementContent"
                  ></div>
                </el-form-item>
                <el-form-item
                  label="附件"
                  prop="attachmentlist"
                  label-width="100px !important"
                >
                  <AttachmentUpload
                    :attachmentlist="form.attachmentList"
                    :limit="1"
                    type="exVideo"
                    v-bind="{}"
                    :editabled="disabled"
                  ></AttachmentUpload>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-scrollbar>

      <div slot="footer" class="dialog-footer" v-if="!disabled">
        <el-button
          type="primary"
          @click="submitForm('发布')"
          :loading="btnLoading"
          >发布公告</el-button
        >
        <el-button
          type="primary"
          @click="submitForm('草稿')"
          :loading="btnLoading"
          >保存草稿</el-button
        >
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { Loading } from "element-ui";
import AttachmentUpload from "@/components/common/packages/attachmentUpload";
import {
  addNotice,
  getNoticeList,
  getNotice,
  updateNotice,
  orgTreeUser,
  delNotice,
  batchUpdateGeNoticeNoticeByIds,
  noticeDeleteBatch,
} from "../../../../../api/informationRelease";
import { getOrglistTreeData } from "../../../../../api/mailList";
import { mapState } from "vuex";
import { getOrgTrees, getOrgTreesN } from "../../../../../api/user";
const checkTitleIsEmpty = (rule, value, callback) => {
  if (!value.trim()) {
    return callback(new Error("标题不能为空"));
  } else {
    callback();
  }
};

const checkConIsEmpty = (rule, value, callback) => {
  if (checkVal(value)) {
    return callback(new Error("内容不能为空"));
  } else {
    callback();
  }
};

var loadingInstance1;

/**
 * 判断editor富文本域是否为空
 * str返回的值为"" 代表输入框里面有值 成功
 * str返回！="" 代表里面有空格 回车 失败
 * */
function checkVal(str) {
  let num = 0,
    reg = /<p>(&nbsp;|&nbsp;\s+)+<\/p>|<p>(<br>)+<\/p>/g;
  while (num < str.length && str !== "") {
    num++;
    let k = str.match(reg);
    if (k) {
      str = str.replace(k[0], "");
    }
  }
  return str === "";
}

export default {
  name: "announcements",
  components: {
    AttachmentUpload,
  },
  data() {
    return {
      orgDTOsAry: [],
      disabled: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      //选中的item数组
      itemArray: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: false,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 通知公告表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 发布时间时间范围
      daterangeReleaseTime: [],
      userId: "",
      // 查询参数
      queryParams: {
        // announcementCode: "string",
        // announcementCodes: ["string"],
        announcementStatus: "",
        announcementTitle: "",
        endTime: "",
        issueUnitCode: "",
        issueUnitName: "",
        nowPage: 1,
        pageSize: 10,
        startTime: "",
      },
      // 表单参数
      form: {
        announcementCode: "",
        announcementContent: "",
        announcementStatus: "", //公告状态0：待发布；1：有效；2：失效；
        announcementTitle: "",
        attachmentList: [],
        issueUnitCode: "",
        orgDTOs: [],
        rcveUnit: "",
        termOfValid: "",
      },
      orgvoList: [],
      // 表单校验
      rules: {
        announcementTitle: [
          {
            required: true,
            message: "请输入公告标题",
            trigger: ["blur", "change"],
          },
          {
            validator: checkTitleIsEmpty,
            trigger: ["blur", "change"],
          },
        ],

        termOfValid: [
          {
            required: true,
            message: "请选择有效期限",
            trigger: ["blur", "change"],
          },
        ],

        announcementContent: [
          {
            required: true,
            message: "请输入公告内容",
            trigger: ["blur", "change"],
          },
          {
            validator: checkConIsEmpty,
            trigger: ["blur", "change"],
          },
        ],
      },
      //状态列表
      statusList: [
        { label: "待发布", value: "0" },
        { label: "有效", value: "1" }, //有效的时候不能编辑删除
        { label: "失效", value: "2" },
      ],

      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },

      filterText: "",
      defaultProps: {
        children: "children",
        label: "label",
        disabled: "virtualNode",
      },
      leftOrgData: [], //与会人员左边的组织树
      rightManList: [], //与会人员右边的列表
      rightTreeLoading: true,
      // 左边部分修改为人员列表，不要之前的机构列表
      isIndeterminate: true,
      checkAll: false, //是否全选
      checkedMans: [], //选中的人员列表
      manList: [], //接口返回的人员列表
      selectManList: [], //新增时左边的数据源

      // 选择的发布对象的集合
      publishObjArray: [],
      CheckedNodesArray: [],
      dialogLoading: false,
      btnLoading: false,
      roleInfo: {},
    };
  },

  watch: {
    // 新增时的人员筛选
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() {
    this.roleInfo =
      JSON.parse(sessionStorage.getItem("VueX_local")).root.login.user || {};
    this.daterangeReleaseTime = [
      new Date(new Date().getTime() - 2626560000).Format("yyyy-MM-dd 00:00:00"),
      new Date(new Date().getTime()).Format("yyyy-MM-dd 24:00:00"),
    ];
    this.queryParams.startTime = this.daterangeReleaseTime[0];
    this.queryParams.endTime = this.daterangeReleaseTime[1];
    this.getList();
  },
  methods: {
    handleGetList(val) {
      this.queryParams.nowPage = val;
      this.getList();
    },
    getList() {
      this.getData();
      this.orgTreeByorgcodeApi();
    },
    searchTime(value) {
      console.log(value);
      if (value) {
        this.queryParams.startTime = value[0] + " 00:00:00";
        this.queryParams.endTime = value[1] + " 24:00:00";
      }
    },
    // 发布对象的数据接口
    orgTreeByorgcodeApi() {
      // getOrgTrees()
      //   .then(res => {
      //     // console.log(res.data);
      //     this.leftOrgData = Object.freeze(res.data.data)
      //   })
      //   .catch(err => {
      //     console.log(err)
      //   })
      getOrgTreesN()
        .then((res) => {
          console.log(res.data, "机构数组");
          this.leftOrgData = Object.freeze(res.data.data);
        })
        .catch((err) => {
          console.log(err);
        });
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },

    /** 查询通知公告列表 */
    getData() {
      this.loading = true;
      getNoticeList(this.queryParams).then((response) => {
        this.loading = false;
        if (response.data.status === 200) {
          this.noticeList = response.data.data.list;
          this.total = response.data.data.total;
        } else {
          this.$message.error(response.data.msg);
        }
      });
    },

    // 取消按钮
    cancel() {
      this.$nextTick(() => {
        this.getList();
        this.$refs.tree.setCheckedKeys([], false);
      });
      this.reset();
      this.selectManList = this.manList;
      this.$set(this.checkedMans, "", []);
      this.filterText = "";
      this.open = false;
    },
    // 表单重置
    reset() {
      this.form = {
        announcementCode: "",
        announcementContent: "",
        announcementStatus: "", //公告状态0：待发布；1：有效；2：失效；
        announcementTitle: "",
        attachmentList: [],
        issueUnitCode: "",
        orgDTOs: [],
        rcveUnit: "",
        termOfValid: "",
      };
      this.publishObjArray = [];
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.nowPage = 1;
      this.queryParams.announcementTitle = this.queryParams.announcementTitle
        ? this.queryParams.announcementTitle.trim()
        : "";
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.announcementStatus=""
      this.queryParams.announcementTitle='';
      this.queryParams.issueUnitCode="";
      this.queryParams.issueUnitName=""
      this.resetForm("queryForm");
      this.daterangeReleaseTime = [
      new Date(new Date().getTime() - 2626560000).Format("yyyy-MM-dd 00:00:00"),
      new Date(new Date().getTime()).Format("yyyy-MM-dd 24:00:00"),
      ];
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.announcementCode);
      this.itemArray = selection;
      this.single = selection.length !== 1;
      // this.multiple = !selection.length;
    },

    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.disabled = false;
      this.title = "添加通知公告";

      //   this.$refs.tree.setCheckedKeys([]);
    },
    /** 修改，查看按钮操作 */
    handleUpdate(row, title) {
      if (title === "查看") {
        this.title = "查看通知公告";
        this.disabled = true;
      } else if (title === "编辑") {
        this.title = "编辑通知公告";
        this.disabled = false;
      }
      this.open = true;
      this.dialogLoading = true;
      const announcementCode = row.announcementCode || this.ids;
      getNotice({ announcementCode: announcementCode }).then((response) => {
        this.dialogLoading = false;
        this.form = response.data.data;
        //orgDTOsAry
        this.orgDTOsAry = response.data.data.orgDTOs;
        // if(orgDTOsLen.length > 0){
        //   orgDTOsLen.forEach(item=>{
        //     if(item.orgName){
        //       orgDTOsAry.push(item.orgName)
        //     }
        //   })
        // }

        if (this.form.termOfValid) {
          this.form.termOfValid = new Date(this.form.termOfValid).Format(
            "yyyy-MM-dd"
          );
        }
        if (this.form.releaseTime) {
          this.form.releaseTime = new Date(this.form.releaseTime).Format(
            "yyyy-MM-dd"
          );
        }
        // 处理接收列表
        if (response.data.data.rcveUnit) {
          let _array = response.data.data.rcveUnit.split(",");
          // let _nameArr = [];
          // console.log(_array);
          // _array.forEach((item) => {
          //   _nameArr.push(this.manList.find((v) => v.id === item).orgName);
          // });
          this.checkedMans = _array;
        }
      });
    },

    // 修改公告状态
    // updateStatusApi(row) {
    //   updateStatus({
    //     announcementCode: row.announcementCode,
    //     announcementStatus: "0",
    //   }).then((res) => {
    //     this.msgSuccess("取消成功");
    //     this.getList();
    //   });
    // },

    /** 提交按钮 */
    submitForm(type) {
      // 机构
      let manIdArray = [];
      this.$nextTick(() => {
        //获取OrgCode
        let manIdArray = this.$refs.tree.getCheckedKeys();
        //获取OrgName
        let manNodeArray = this.$refs.tree.getCheckedNodes();
        let orgDTOs = [];
        if (manIdArray.length > 0) {
          manIdArray = manIdArray.join(",");
          if (manNodeArray.length > 150) {
            this.$message.warning(
              "当前接收单位数" + manNodeArray.length + "个过多，不能超过150个！"
            );
            return false;
          }
          this.form.rcveUnit = manIdArray;
          manNodeArray.forEach((item, index) => {
            // orgDTOs[index] = {
            //   orgCode: item.id,
            //   orgName: item.qymc
            // }
            orgDTOs[index] = item.id;
          });
          this.form.orgDTOs = orgDTOs;
        } else {
          this.$message.warning("请选择接收人员");
          return false;
        }

        if (type === "草稿") {
          this.form.announcementStatus = "0";
        } else {
          this.form.announcementStatus = "1";
        }
        this.form.issueUnitCode = this.user.org_code;
        this.$refs["form"].validate((valid) => {
          if (valid) {
            this.btnLoading = true;
            if (this.form.announcementCode !== "") {
              updateNotice(this.form).then((response) => {
                this.btnLoading = false;
                if (response.data.status === 200) {
                  this.$message.success("保存成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.$message.error("保存失败");
                }
              });
            } else {
              addNotice(this.form).then((response) => {
                this.btnLoading = false;
                if (response.data.status === 200) {
                  this.$message.success("保存成功");
                  this.open = false;
                  this.getList();
                } else {
                  this.$message.error("保存失败");
                }
              });
            }
            this.selectManList = this.manList;
            this.filterText = "";
          }
        });
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if (!row.announcementCode && this.ids.length === 0) {
        this.$message.warning("请先选择至少一条数据~");
        return false;
      }
      this.$confirm("确认删除该数据?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          if (row.announcementCode) {
            return delNotice({ announcementCode: row.announcementCode });
          } else {
            return noticeDeleteBatch({ announcementCodes: this.ids });
          }
        })
        .then((res) => {
          if (res.data.status === 200) {
            this.$message.success("删除成功");
             if (this.noticeList.length === 1 && this.queryParams.nowPage !== 1) {
                this.queryParams.nowPage--;
              }
            this.getList();
          } else {
            this.$message.error("删除成功");
          }
        });
    },
    /** 导出按钮操作 */
    // handleExport() {
    //   const queryParams = this.queryParams;
    //   this.$confirm("是否确认导出所有通知公告数据项?", "警告", {
    //     confirmButtonText: "确定",
    //     cancelButtonText: "取消",
    //     type: "warning",
    //   })
    //     .then(function () {
    //       return exportNotice(queryParams);
    //     })
    //     .then((response) => {
    //       this.download(response.msg);
    //     });
    // },
    /** 批量发布按钮操作 */
    handlePublish() {
      let flag = true;
      if (this.itemArray.length === 0) {
        this.$message.warning("请先选择至少一条通知公告~");
        return false;
      }
      this.itemArray.forEach((item) => {
        if (item.announcementStatus != 0) {
          this.$alert('只能发布 "待发布" 状态的通知公告', "提示", {
            confirmButtonText: "我知道了",
            type: "warning",
            callback: (action) => {},
          });
          flag = false;
        }

        if (
          item.announcementStatus == 0 &&
          this.compareData(item.termOfValid + " 23:59:59") <= 0
        ) {
          this.$alert("待发布的通知公告的有效期必须在今天或者之后", "提示", {
            confirmButtonText: "我知道了",
            type: "warning",
            callback: (action) => {},
          });
          flag = false;
        }
      });

      if (flag) {
        this.ids = this.itemArray.map((item) => item.announcementCode);
        this.$confirm("是否确认发布选中的通知公告?", "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        }).then(() => {
          this.batchUpdateGeNoticeNoticeByIdsFun({
            announcementCodes: this.ids,
          });
        });
      }
    },

    // 批量发布
    batchUpdateGeNoticeNoticeByIdsFun(data) {
      this.multiple = true;
      batchUpdateGeNoticeNoticeByIds(data).then((res) => {
        this.multiple = false;
        if (res.data.status === 200) {
          this.getList();
          this.$message.success("发布成功");
        } else {
          this.$message.error("发布失败");
        }
      });
    },

    //比较两个时间的先后关系
    compareData(time) {
      let t1 = new Date(time).getTime();
      let t0 = new Date().getTime();
      return t1 - t0;
    },
  },
  computed: {
    ...mapState({
      user: (state) => state.login.user,
    }),
  },
};
</script>

<style lang="scss" scoped>
/deep/ .el-scrollbar__wrap{
   overflow-x: hidden;
}
/deep/ .el-form-item__content {
  line-height: 35px;
  position: relative;
  font-size: 12px;
  margin: 5px 0;
}
.el-form-item {
  margin-bottom: 0;
  margin-top: 10px;
}
.el-icon-document {
  padding-top: 0 !important;
}
.el-tree-node__content {
  height: 36px;
}
.specification-table-head {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  margin-top: 15px;
  .left {
    font-size: 18px;
    text-align: left;
    font-weight: 900;
  }
}
.firstLevel {
  padding: 10px 0 10px 0;
  font-weight: bold;
  font-size: 14px;
  color: #082754;
}
.dialog-footer {
  display: flex;
  justify-content: center;
}
.secLevel {
  padding: 10px 0 10px 0;
  font-size: 14px;
  color: #082754;
}

.leftBox {
  padding: 10px;
  box-shadow: 0 0 10px #eee;
  border: 1px solid #f5f5f5;
  border-radius: 2px;
  .title {
    border-bottom: 2px solid #46a0fb;
    text-align: left;
    height: 40px;
    line-height: 40px;
    color: #082754;
    font-size: 16px;
    margin: 0;
  }
  .con {
    padding: 10px;
    height: 400px;
  }
 }
/deep/ .el-link--inner {
  padding-top: 10px !important;
}
/deep/ .el-link--danger {
  white-space: nowrap;
}
.pagination {
  display: flex;
  padding: 20px 0;
  justify-content: flex-end;
}
.announcementContent {
  border: 1px solid #ccc;
  padding: 8px 8px 8px 20px;
  border-radius: 4px;
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}
// /deep/ .announcementContent ::marker{
//   content:" "
// }

</style>
