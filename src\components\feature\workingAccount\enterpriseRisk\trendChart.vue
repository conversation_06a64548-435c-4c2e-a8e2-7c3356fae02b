<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span @click="gotoRunningState"
              ><a-icon type="home" theme="filled" class="icon" /> 企业风险分析
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>趋势分析</a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="seach-part">
      <div class="l">
        <el-cascader
          size="mini"
          placeholder="请选择行政区划"
          :options="district"
          v-model="districtVal"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          style="width: 250px"
          :show-all-levels="true"
        ></el-cascader>
        <el-date-picker
          v-model="date"
          size="mini"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="getDate"
          unlink-panels
        >
        </el-date-picker>
        <el-button type="primary" size="mini" @click="getData">查询</el-button>
        <CA-button
          v-if="mode === '统计'"
          type="primary"
          size="mini"
          plain
          @click="exportExcel"
          >导出</CA-button
        >
      </div>
      <!-- <el-button type="primary" size="mini">趋势分析</el-button>      -->
    </div>
    <div class="table-main">
      <div class="table-top">
        <h2>企业风险趋势变化</h2>
        <!-- <el-radio-group size="small" v-model="mode">
          <el-radio-button label="图表"></el-radio-button>
          <el-radio-button label="统计"></el-radio-button>
        </el-radio-group> -->
        <CA-RadioGroup
          class="radio"
          v-model="mode"
          backgroundColor="#F1F6FF"
          border="1px solid rgba(57, 119, 234, 0.2)"
        >
          <CA-Radio
            :label="{
              src: '../../../static/img/tubiao_icon.png',
              style: 'width:15px;height:15px',
            }"
            :labelTwo="{
              src: '../../../static/img/tubiao_icon_hover.png',
              style: 'width:15px;height:15px',
            }"
            bgColorActive="#409eff"
            value="图表"
          ></CA-Radio>
          <CA-Radio
            :label="{
              src: '../../../static/img/liebiao_icon.png',
              style: 'width:15px;height:15px',
            }"
            :labelTwo="{
              src: '../../../static/img/liebiao_icon_hover.png',
              style: 'width:15px;height:15px',
            }"
            value="统计"
            bgColorActive="#409eff"
          ></CA-Radio>
        </CA-RadioGroup>
      </div>
      <div v-show="showtable">
        <div class="table" v-loading="loading">
          <el-table
            :data="tableData"
            style="width: 100%"
            ref="multipleTable"
            @selection-change="handleSelectionChange"
            :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
            border
            :default-sort="{ prop: 'date', order: 'descending' }"
            @select="select"
            @select-all="select"
          >
            <el-table-column type="selection" width="50" align="center">
            </el-table-column>
            <el-table-column
              label="日期"
              align="center"
              prop="riskDate"
              width="180"
            >
              <template slot-scope="{ row, column, $index, store }">
                <span>{{ "20" + row.riskDate }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="行政区划"
              width="180"
              prop="areaName"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="已接入企业数"
              prop="linkedNumber"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="重大风险企业数"
              prop="riskOne"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="较大风险企业数"
              prop="riskTwo"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="一般风险企业数"
              prop="riskThree"
              align="center"
            >
            </el-table-column>
            <el-table-column
              label="低风险企业数"
              prop="riskFour"
              align="center"
            >
            </el-table-column>
          </el-table>
        </div>
        <!-- <div class="pagination">
          <el-pagination
            @current-change="handleCurrentChange"
            :current-page.sync="currentPage"
            :page-size="tableData.size"
            layout="total, prev, pager, next"
            :total="tableData.total"
            background
          >
          </el-pagination>
        </div> -->
      </div>
      <div v-show="!showtable" v-loading="loading">
        <div id="myCharteds"></div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getCimRiskAnalysisCimRiskGroupDate,
  getCimRiskAnalysisExportCimRiskGroupDateToExcel,
} from "@/api/workingAcc";
import { createNamespacedHelpers } from "vuex";
import dayjs from 'dayjs';
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {},
  data() {
    return {
      options: [
        {
          value: "1",
          label: "一级重大危险源",
        },
        {
          value: "2",
          label: "二级重大危险源",
        },
        {
          value: "3",
          label: "三级重大危险源",
        },
        {
          value: "4",
          label: "四级重大危险源",
        },
      ],
      value1: "",
      value: "",
      tableData: [],
      EchartData: [],
      mode: "图表",
      modees: "企业运行趋势",
      showtable: false,
      currentPage: 1,
      widthBox: 1400,
      endDate: "",
      startDate: "",
      date: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (144 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900,
      ],
      majorHazardLevel: ["1", "2", "3", "4"],
      districtVal: this.$store.state.login.userDistCode,
      district: this.$store.state.controler.district,
      selection: [],
      loading: false,
      selection: [],
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
    }),
  ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  methods: {
    gotoRunningState() {
      let data = false;
      this.$emit("RunningState", data);
      // this.$router.go(0);
    },

    getData() {
      this.loading = true;
      //因为后端不传size就默认给10条，所以我根据时间天数算应该给
      var endDate = "";
      var startDate = "";
      if(this.date) {
        startDate = dayjs(this.date[0]).format("YYYY-MM-DD");
        endDate = dayjs(this.date[1]).format("YYYY-MM-DD");
      } else {
        startDate = dayjs().format("YYYY-MM-DD");
        endDate =  dayjs().format("YYYY-MM-DD");
      }
      var size = endDate - startDate + 1000;
      size = Math.floor((size / 1000 / 60 / 60 / 24) << 0);
      getCimRiskAnalysisCimRiskGroupDate({
        distCode: this.districtVal,
        startDate,
        endDate,
      }).then((res) => {
        // console.log(res);
        if (this.mode != "统计") {
          this.EchartData = res.data.data;
        } else {
          this.tableData = res.data.data;
        }
        this.loading = false;
        // this.getEchart();
      });
    },
    getDate() {
      this.startDate = new Date(this.date[0]).Format("yy-MM-dd hh:mm:ss");
      this.endDate = new Date(
        new Date(this.date[1].getTime() + 86399900)
      ).Format("yy-MM-dd hh:mm:ss");
    },
    // 导出
    exportExcel() {
      var endDate = "";
      var startDate = "";
      if(this.date) {
        startDate = dayjs(this.date[0]).format("YYYY-MM-DD");
        endDate = dayjs(this.date[1]).format("YYYY-MM-DD");
      } else {
        startDate = dayjs().format("YYYY-MM-DD");
        endDate =  dayjs().format("YYYY-MM-DD");
      }
      getCimRiskAnalysisExportCimRiskGroupDateToExcel({
        distCode: this.districtVal,
        dateList: this.selection.length <= 1 ? null : this.selection,
        startDate,
        endDate,
      }).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], {
          type: "application/vnd.ms-excel",
        });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "企业风险分析-趋势分析" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        this.selection[i] = selection[i].riskDate;
      }
    },
    handleSelectionChange(val) {
      // console.log(val);
    },
    handleClick() {
      console.log(123);
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.$setEchart("myCharteds", 250, 250);
  },
  watch: {
    mode: {
      handler(newValue, oldValue) {
        this.selection = [];
        if (newValue == "统计") {
          this.showtable = true;
          this.getData();
        } else {
          this.showtable = false;
          this.getData();
        }
      },
      deep: true,
      // immediate: true,
    },
    vuexDistrict:{
      handler(newVal, oldVal) {
        this.district = newVal;
      }
    },
    // modees(newValue, oldValue) {
    //   if (newValue == "企业运行趋势") {
    //     this.drawLine();
    //   } else {
    //     this.drawLinees();
    //   }
    // },
    EchartData: {
      handler(newVal, oldVal) {
        let myChart = this.$echarts.init(document.getElementById("myCharteds"));
        var option;
        var xAxisData = [];
        let seriesData = [
          {
            name: "重大风险企业数",
            type: "line",
            stack: "总量",
            data: [],
            itemStyle: {
              normal: {
                color: "rgb(233,52,36)", //折线点的颜色
                lineStyle: {
                  color: "rgb(233,52,36)", //折线的颜色
                },
              },
            },
          },
          {
            name: "较大风险企业数",
            type: "line",
            stack: "总量",
            data: [],
            itemStyle: {
              normal: {
                color: "rgb(248,206,119)", //折线点的颜色
                lineStyle: {
                  color: "rgb(248,206,119)", //折线的颜色
                },
              },
            },
          },
          {
            name: "一般风险企业数",
            type: "line",
            stack: "总量",
            data: [],
            itemStyle: {
              normal: {
                color: "rgb(171,240,75)", //折线点的颜色
                lineStyle: {
                  color: "rgb(171,240,75)", //折线的颜色
                },
              },
            },
          },
          {
            name: "低风险企业数",
            type: "line",
            stack: "总量",
            data: [],
            itemStyle: {
              normal: {
                color: "rgb(73,139,239)", //折线点的颜色
                lineStyle: {
                  color: "rgb(73,139,239)", //折线的颜色
                },
              },
            },
          },
        ];
        for (let i = 0; i < this.EchartData.length; i++) {
          xAxisData[i] = "20" + this.EchartData[i].riskDate;
          // seriesData[i].name = this.tableData[i].riskDate;
          if (this.EchartData[i].riskOne != 0) {
            seriesData[0].data[i] = 0;
          } else {
            seriesData[0].data[i] = this.EchartData[i].riskOne;
          }
          if (this.EchartData[i].riskTwo != 0) {
            seriesData[1].data[i] = this.EchartData[i].riskTwo;
          } else {
            seriesData[1].data[i] = this.EchartData[i].riskTwo;
          }
          if (this.EchartData[i].riskThree != 0) {
            seriesData[2].data[i] = this.EchartData[i].riskThree;
          } else {
            seriesData[2].data[i] = this.EchartData[i].riskThree;
          }
          if (this.EchartData[i].riskFour != 0) {
            seriesData[3].data[i] = this.EchartData[i].riskFour;
          } else {
            seriesData[3].data[i] = this.EchartData[i].riskFour;
          }
        }
        option = {
          tooltip: {
            trigger: "axis",
          },
          legend: {
            data: [
              "重大风险企业数",
              "较大风险企业数",
              "一般风险企业数",
              "低风险企业数",
            ],
          },
          grid: {
            left: "3%",
            right: "4%",
            bottom: "3%",
            containLabel: true,
          },
          toolbox: {
            feature: {
              saveAsImage: {},
            },
          },
          dataZoom: [
            {
              type: "slider", //数据滑块
              start: 0,
              minSpan: 8, //5min
              // minSpan:16,   //10min
              // minSpan:24,   //15min
              // minSpan:50,   //30min
              dataBackground: {
                lineStyle: {
                  // color:'#95BC2F'
                },
                areaStyle: {
                  // color:'#95BC2F',
                  opacity: 1,
                },
              },
              // fillerColor:'rgba(255,255,255,.6)'
            },
            {
              type: "inside", //使鼠标在图表中时滚轮可用
            },
          ],
          xAxis: {
            type: "category",
            boundaryGap: false,
            data: xAxisData,
          },
          yAxis: {
            type: "value",
          },
          series: seriesData,
        };

        option && myChart.setOption(option);
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 10px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
    }
  }
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 15px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      & > * {
        margin-right: 15px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      height: 48px;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
</style>
