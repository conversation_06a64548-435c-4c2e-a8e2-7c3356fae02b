// import request from '@/utils/request'
import axios from "axios";
import qs from "qs";

// 查询通知公告列表
export function listNotice(query) {
  return axios({
    url: "/notice/notice/list",
    method: "get",
    params: query,
  });
}

// 查询通知公告详细
export function getNotice(announcementCode) {
  return axios({
    url: "/notice/notice/" + announcementCode,
    method: "get",
  });
}

// 新增通知公告
export function addNotice(data) {
  return axios({
    url: "/notice/notice",
    method: "post",
    data: data,
  });
}

// 新增通知公告-追加人
export function addRelatedUser(data) {
  return axios({
    url: "/notice/notice/addRelatedUser",
    method: "post",
    data: data,
  });
}

// 编辑通知公告-追加人
export function updateRelatedUser(data) {
  return axios({
    url: "/notice/notice/updateRelatedUser",
    method: "post",
    data: data,
  });
}

// 修改通知公告
export function updateNotice(data) {
  return request({
    url: "/gemp-chemical/api/gemp/notice/notice/edit/v1",
    method: "post",
    data: data,
  });
}

// 修改公告状态
export function updateStatus(data) {
  return request({
    url: "/notice/notice/updateStatus",
    method: "post",
    data: data,
  });
}

// 删除通知公告
export function delNotice(announcementCode) {
  return request({
    url: "/notice/notice/" + announcementCode,
    method: "delete",
  });
}

// 导出通知公告
export function exportNotice(query) {
  return request({
    url: "/notice/notice/export",
    method: "get",
    params: query,
  });
}

// 根据附件ID查附件
// export function queryAttachmentInfoById(query) {
//   return request({
//     url: '/common/queryAttachmentInfoById',
//     headers:{
//       'Content-Type': 'application/x-www-form-urlencoded'
//     },
//     method: 'get',
//     params: query
//   })
// }

export function queryAttachmentInfoById(query) {
  return request({
    url: "/common/download/queryAttachmentInfoById",
    method: "get",
    params: query,
  });
}

// 批量发布公告
export function batchUpdateGeNoticeNoticeByIds(data) {
  return request({
    url: "/notice/notice/batchUpdateGeNoticeNoticeByIds",
    method: "post",
    data: data,
  });
}
// 批量发布公告
export function downloadUrl(query) {
  return request({
    url: "/download/v1",
    method: "get",
    params: query,
  });
}
