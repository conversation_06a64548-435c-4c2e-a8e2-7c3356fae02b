define(["./AttributeCompression-6e71d14f","./Matrix2-e6265fb0","./ComponentDatatype-a9820060","./createTaskProcessorWorker","./RuntimeError-ac440aa5","./defaultValue-69ee94f4","./WebGLConstants-f63312fc"],(function(e,a,t,r,n,o,i){"use strict";const s=32767,c=new a.Cartographic,u=new a.Cartesian3,p=new a.Rectangle,l=new a.Ellipsoid,f={min:void 0,max:void 0};return r((function(r,n){const o=new Uint16Array(r.positions);!function(e){e=new Float64Array(e);let t=0;f.min=e[t++],f.max=e[t++],a.Rectangle.unpack(e,t,p),t+=a.Rectangle.packedLength,a.Ellipsoid.unpack(e,t,l)}(r.packed<PERSON>uffer);const i=p,m=l,h=f.min,C=f.max,d=o.length/3,g=o.subarray(0,d),b=o.subarray(d,2*d),w=o.subarray(2*d,3*d);e.AttributeCompression.zigZagDeltaDecode(g,b,w);const k=new Float64Array(o.length);for(let e=0;e<d;++e){const r=g[e],n=b[e],o=w[e],p=t.CesiumMath.lerp(i.west,i.east,r/s),l=t.CesiumMath.lerp(i.south,i.north,n/s),f=t.CesiumMath.lerp(h,C,o/s),d=a.Cartographic.fromRadians(p,l,f,c),y=m.cartographicToCartesian(d,u);a.Cartesian3.pack(y,k,3*e)}return n.push(k.buffer),{positions:k.buffer}}))}));
