{"header": {"reportVersion": 1, "event": "Allocation failed - JavaScript heap out of memory", "trigger": "FatalE<PERSON>r", "filename": "report.20211102.144833.24684.0.001.json", "dumpEventTime": "2021-11-02T14:48:33Z", "dumpEventTimeStamp": "1635835713984", "processId": 24684, "cwd": "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront", "commandLine": ["D:\\Program Files\\nodejs\\node.exe", "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront\\node_modules\\_worker-farm@1.7.0@worker-farm\\lib\\child\\index.js", "D:\\Program Files\\nodejs\\node.exe", "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront\\node_modules\\_webpack-dev-server@4.4.0@webpack-dev-server\\bin\\webpack-dev-server.js", "serve", "--config", "build/webpack.dev.conf.js"], "nodejsVersion": "v12.13.0", "wordSize": 64, "arch": "x64", "platform": "win32", "componentVersions": {"node": "12.13.0", "v8": "7.7.299.13-node.12", "uv": "1.32.0", "zlib": "1.2.11", "brotli": "1.0.7", "ares": "1.15.0", "modules": "72", "nghttp2": "1.39.2", "napi": "5", "llhttp": "1.1.4", "http_parser": "2.8.0", "openssl": "1.1.1d", "cldr": "35.1", "icu": "64.2", "tz": "2019a", "unicode": "12.1"}, "release": {"name": "node", "lts": "Erbium", "headersUrl": "https://nodejs.org/download/release/v12.13.0/node-v12.13.0-headers.tar.gz", "sourceUrl": "https://nodejs.org/download/release/v12.13.0/node-v12.13.0.tar.gz", "libUrl": "https://nodejs.org/download/release/v12.13.0/win-x64/node.lib"}, "osName": "Windows_NT", "osRelease": "10.0.18363", "osVersion": "Windows 10 Home China", "osMachine": "x86_64", "cpus": [{"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 41902156, "nice": 0, "sys": 39106218, "idle": 912784468, "irq": 4584265}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 23709500, "nice": 0, "sys": 14655609, "idle": 955427015, "irq": 167093}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 76657828, "nice": 0, "sys": 43279546, "idle": 873854734, "irq": 385640}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 28795046, "nice": 0, "sys": 15018781, "idle": 949978281, "irq": 154437}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 64086312, "nice": 0, "sys": 36741140, "idle": 892964656, "irq": 363406}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 18594984, "nice": 0, "sys": 13779500, "idle": 961417625, "irq": 131546}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 47808125, "nice": 0, "sys": 29453437, "idle": 916530546, "irq": 257906}, {"model": "Intel(R) Core(TM) i7-8550U CPU @ 1.80GHz", "speed": 1992, "user": 29808828, "nice": 0, "sys": 30330703, "idle": 933652562, "irq": 153484}], "networkInterfaces": [{"name": "SV-Connection", "internal": false, "mac": "08:00:58:00:00:05", "address": "fe80::c58e:a9e6:3748:53f7", "netmask": "ffff:ffff:ffff:ffff::", "family": "IPv6", "scopeid": 25}, {"name": "SV-Connection", "internal": false, "mac": "08:00:58:00:00:05", "address": "***********", "netmask": "*************", "family": "IPv4"}, {"name": "WLAN", "internal": false, "mac": "74:e5:f9:eb:7a:26", "address": "*************", "netmask": "*************", "family": "IPv4"}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "::1", "netmask": "ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff", "family": "IPv6", "scopeid": 0}, {"name": "Loopback Pseudo-Interface 1", "internal": true, "mac": "00:00:00:00:00:00", "address": "127.0.0.1", "netmask": "*********", "family": "IPv4"}], "host": "DESKTOP-F9JJ7E6"}, "javascriptStack": {"message": "No stack.", "stack": ["Unavailable."]}, "nativeStack": [{"pc": "0x00007ff7dfcc1389", "symbol": ""}, {"pc": "0x00007ff7dfcc57cc", "symbol": ""}, {"pc": "0x00007ff7dfcc4778", "symbol": ""}, {"pc": "0x00007ff7dfdb332b", "symbol": ""}, {"pc": "0x00007ff7e05b6f4e", "symbol": ""}, {"pc": "0x00007ff7e059ef91", "symbol": ""}, {"pc": "0x00007ff7e046c85c", "symbol": ""}, {"pc": "0x00007ff7e042662b", "symbol": ""}, {"pc": "0x00007ff7e042645c", "symbol": ""}, {"pc": "0x00007ff7e0427551", "symbol": ""}, {"pc": "0x00007ff7e04258bd", "symbol": ""}, {"pc": "0x00007ff7e042c569", "symbol": ""}, {"pc": "0x00007ff7e042c82d", "symbol": ""}, {"pc": "0x00007ff7e042ccd6", "symbol": ""}, {"pc": "0x00007ff7e0458362", "symbol": ""}, {"pc": "0x00007ff7e04582ea", "symbol": ""}, {"pc": "0x00007ff7e042a687", "symbol": ""}, {"pc": "0x00007ff7e0479622", "symbol": ""}, {"pc": "0x00007ff7e0473f66", "symbol": ""}, {"pc": "0x00007ff7e0469fc3", "symbol": ""}, {"pc": "0x00007ff7e0468794", "symbol": ""}, {"pc": "0x00007ff7e0489a65", "symbol": ""}, {"pc": "0x00007ff7e01f6239", "symbol": ""}, {"pc": "0x00007ff7e09e2c6d", "symbol": ""}, {"pc": "0x00007ff7e09e3ba1", "symbol": ""}, {"pc": "0x0000020bfe94d4d9", "symbol": ""}], "javascriptHeap": {"totalMemory": 284602368, "totalCommittedMemory": 284602368, "usedMemory": 266686832, "availableMemory": 1904429112, "memoryLimit": 2197815296, "heapSpaces": {"read_only_space": {"memorySize": 262144, "committedMemory": 262144, "capacity": 261872, "used": 32296, "available": 229576}, "new_space": {"memorySize": 33554432, "committedMemory": 33554432, "capacity": 16759808, "used": 16727312, "available": 32496}, "old_space": {"memorySize": 176685056, "committedMemory": 176685056, "capacity": 176336368, "used": 176336368, "available": 0}, "code_space": {"memorySize": 425984, "committedMemory": 425984, "capacity": 234144, "used": 229760, "available": 4384}, "map_space": {"memorySize": 528384, "committedMemory": 528384, "capacity": 526928, "used": 274160, "available": 252768}, "large_object_space": {"memorySize": 73097216, "committedMemory": 73097216, "capacity": 73083384, "used": 73083384, "available": 0}, "code_large_object_space": {"memorySize": 49152, "committedMemory": 49152, "capacity": 3552, "used": 3552, "available": 0}, "new_large_object_space": {"memorySize": 0, "committedMemory": 0, "capacity": 16759808, "used": 0, "available": 16759808}}}, "resourceUsage": {"userCpuSeconds": 1.625, "kernelCpuSeconds": 0.437, "cpuConsumptionPercent": 206.2, "maxRss": 348078080, "pageFaults": {"IORequired": 169843, "IONotRequired": 0}, "fsActivity": {"reads": 24, "writes": 7}}, "libuv": [], "environmentVariables": {"ALLUSERSPROFILE": "C:\\ProgramData", "APPDATA": "C:\\Users\\<USER>\\AppData\\Roaming", "asl.log": "Destination=file", "CLASSPATH": ".;D:\\Java\\jdk1.8.0_261\\lib;D:\\Java\\jdk1.8.0_261\\lib\\tools.jar", "COLOR": "1", "CommonProgramFiles": "C:\\Program Files\\Common Files", "CommonProgramFiles(x86)": "C:\\Program Files (x86)\\Common Files", "CommonProgramW6432": "C:\\Program Files\\Common Files", "COMPUTERNAME": "DESKTOP-F9JJ7E6", "ComSpec": "C:\\WINDOWS\\system32\\cmd.exe", "dp0": "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront\\node_modules\\.bin\\", "DriverData": "C:\\Windows\\System32\\Drivers\\DriverData", "EDITOR": "notepad.exe", "HOME": "C:\\Users\\<USER>", "HOMEDRIVE": "C:", "HOMEPATH": "\\Users\\13031", "INIT_CWD": "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront", "JAVA_HOME": "D:\\Java\\jdk1.8.0_261", "lnkenv": "C:\\Program Files (x86)\\Internet Explorer\\IEXPLORE.EXE", "LOCALAPPDATA": "C:\\Users\\<USER>\\AppData\\Local", "LOGONSERVER": "\\\\DESKTOP-F9JJ7E6", "MAVEN_HOME": "D:\\chenan\\javasoft\\apache-maven-3.6.1", "NODE": "D:\\Program Files\\nodejs\\node.exe", "NODE_ENV": "development", "NODE_EXE": "D:\\Program Files\\nodejs\\\\node.exe", "NODE_PATH": "C:\\Program Files\\nodejs\\node_modules", "NPM_CLI_JS": "C:\\Program Files\\nodejs\\node_global\\node_modules\\npm\\bin\\npm-cli.js", "npm_command": "run-script", "npm_config_cache": "C:\\Program Files\\nodejs\\node_cache", "npm_config_disturl": "https://npm.taobao.org/dist", "npm_config_globalconfig": "C:\\Program Files\\nodejs\\node_global\\etc\\npmrc", "npm_config_global_prefix": "C:\\Program Files\\nodejs\\node_global", "npm_config_init_module": "C:\\Users\\<USER>\\.npm-init.js", "npm_config_local_prefix": "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront", "npm_config_metrics_registry": "https://registry.npmjs.org/", "npm_config_node_gyp": "C:\\Program Files\\nodejs\\node_global\\node_modules\\npm\\node_modules\\node-gyp\\bin\\node-gyp.js", "npm_config_noproxy": "", "npm_config_prefix": "C:\\Program Files\\nodejs\\node_global", "npm_config_userconfig": "C:\\Users\\<USER>\\.npmrc", "npm_config_user_agent": "npm/7.22.0 node/v12.13.0 win32 x64 workspaces/false", "npm_execpath": "C:\\Program Files\\nodejs\\node_global\\node_modules\\npm\\bin\\npm-cli.js", "npm_lifecycle_event": "dev", "npm_lifecycle_script": "cross-env process.env.NODE_ENV=development webpack-dev-server --config build/webpack.dev.conf.js", "npm_node_execpath": "D:\\Program Files\\nodejs\\node.exe", "npm_package_engines_node": ">= 6.0.0", "npm_package_engines_npm": ">= 3.0.0", "npm_package_json": "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront\\package.json", "npm_package_name": "hbsecondaryprojectfront", "npm_package_version": "1.0.0", "NPM_PREFIX_NPM_CLI_JS": "C:\\Program Files\\nodejs\\node_global\\node_modules\\npm\\bin\\npm-cli.js", "NUMBER_OF_PROCESSORS": "8", "OneDrive": "C:\\Users\\<USER>\\OneDrive", "OneDriveConsumer": "C:\\Users\\<USER>\\OneDrive", "OS": "Windows_NT", "Path": "D:\\chenan\\shengtingtwoqi\\hubeiweihuafront\\node_modules\\.bin;D:\\chenan\\shengtingtwoqi\\node_modules\\.bin;D:\\chenan\\node_modules\\.bin;D:\\node_modules\\.bin;C:\\Program Files\\nodejs\\node_global\\node_modules\\npm\\node_modules\\@npmcli\\run-script\\lib\\node-gyp-bin;C:\\Program Files (x86)\\Common Files\\Oracle\\Java\\javapath;D:\\Program Files\\nodejs\\;C:\\Program Files\\OpenVPN\\bin;C:\\windows\\system32;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;D:\\Java\\jdk1.8.0_261\\bin;D:\\Java\\jdk1.8.0_261\\jre\\bin;D:\\chenan\\javasoft\\apache-maven-3.6.1\\bin;C:\\Ruby25-x64\\bin;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Program Files\\Microsoft VS Code\\bin;C:\\Program Files\\nodejs\\node_global;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Fiddler;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps", "PATHEXT": ".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JSE;.WSF;.WSH;.MSC;.CPL", "PORT": "8082", "PROCESSOR_ARCHITECTURE": "AMD64", "PROCESSOR_IDENTIFIER": "Intel64 Family 6 Model 142 Stepping 10, GenuineIntel", "PROCESSOR_LEVEL": "6", "PROCESSOR_REVISION": "8e0a", "PROG27B48B2C053": "1", "ProgramData": "C:\\ProgramData", "ProgramFiles": "C:\\Program Files", "ProgramFiles(x86)": "C:\\Program Files (x86)", "ProgramW6432": "C:\\Program Files", "PROMPT": "$P$G", "PSModulePath": "C:\\Users\\<USER>\\Documents\\WindowsPowerShell\\Modules;C:\\Program Files\\WindowsPowerShell\\Modules;C:\\WINDOWS\\system32\\WindowsPowerShell\\v1.0\\Modules", "PUBLIC": "C:\\Users\\<USER>", "SystemDrive": "C:", "SystemRoot": "C:\\WINDOWS", "TEMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "TMP": "C:\\Users\\<USER>\\AppData\\Local\\Temp", "USERDOMAIN": "DESKTOP-F9JJ7E6", "USERDOMAIN_ROAMINGPROFILE": "DESKTOP-F9JJ7E6", "USERNAME": "13031", "USERPROFILE": "C:\\Users\\<USER>", "VBOX_MSI_INSTALL_PATH": "C:\\Program Files\\Oracle\\VirtualBox\\", "WEBPACK_SERVE": "true", "windir": "C:\\WINDOWS", "_prog": "node"}, "sharedObjects": ["D:\\Program Files\\nodejs\\node.exe", "C:\\WINDOWS\\SYSTEM32\\ntdll.dll", "C:\\WINDOWS\\System32\\KERNEL32.DLL", "C:\\WINDOWS\\System32\\KERNELBASE.dll", "C:\\WINDOWS\\System32\\WS2_32.dll", "C:\\WINDOWS\\System32\\RPCRT4.dll", "C:\\WINDOWS\\System32\\ADVAPI32.dll", "C:\\WINDOWS\\SYSTEM32\\dbghelp.dll", "C:\\WINDOWS\\System32\\msvcrt.dll", "C:\\WINDOWS\\System32\\ucrtbase.dll", "C:\\WINDOWS\\System32\\sechost.dll", "C:\\WINDOWS\\System32\\USER32.dll", "C:\\WINDOWS\\System32\\win32u.dll", "C:\\WINDOWS\\System32\\GDI32.dll", "C:\\WINDOWS\\System32\\gdi32full.dll", "C:\\WINDOWS\\System32\\msvcp_win.dll", "C:\\WINDOWS\\System32\\PSAPI.DLL", "C:\\WINDOWS\\System32\\CRYPT32.dll", "C:\\WINDOWS\\System32\\MSASN1.dll", "C:\\WINDOWS\\System32\\bcrypt.dll", "C:\\WINDOWS\\SYSTEM32\\IPHLPAPI.DLL", "C:\\WINDOWS\\SYSTEM32\\USERENV.dll", "C:\\WINDOWS\\System32\\profapi.dll", "C:\\WINDOWS\\SYSTEM32\\WINMM.dll", "C:\\WINDOWS\\SYSTEM32\\winmmbase.dll", "C:\\WINDOWS\\System32\\cfgmgr32.dll", "C:\\WINDOWS\\System32\\bcryptPrimitives.dll", "C:\\WINDOWS\\System32\\IMM32.DLL", "C:\\WINDOWS\\System32\\powrprof.dll", "C:\\WINDOWS\\System32\\UMPDC.dll", "C:\\WINDOWS\\system32\\uxtheme.dll", "C:\\WINDOWS\\System32\\combase.dll", "C:\\WINDOWS\\system32\\mswsock.dll", "C:\\WINDOWS\\System32\\kernel.appcore.dll", "C:\\WINDOWS\\System32\\NSI.dll", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc6.DLL", "C:\\WINDOWS\\SYSTEM32\\dhcpcsvc.DLL", "C:\\WINDOWS\\SYSTEM32\\DNSAPI.dll", "C:\\WINDOWS\\system32\\napinsp.dll", "C:\\WINDOWS\\system32\\pnrpnsp.dll", "C:\\WINDOWS\\System32\\winrnr.dll", "C:\\WINDOWS\\system32\\NLAapi.dll", "C:\\WINDOWS\\system32\\wshbth.dll"]}