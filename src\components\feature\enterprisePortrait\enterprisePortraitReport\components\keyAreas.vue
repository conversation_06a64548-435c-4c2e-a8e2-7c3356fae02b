<template>
  <div>
    重点行业领域：{{industryCategoryDTO.supervisionCorpStr}}
    <!-- <el-table :data="tableData" :header-cell-style="headerCellStyle" border
                style="width: 100%" ref="multipleTable">
                <el-table-column type="index" label="序号" width="55" align="center">
                </el-table-column>
                 <el-table-column prop="level" label="物(Things)" align="center"></el-table-column>
                <el-table-column prop="level" label="管(Management)" align="center"></el-table-column>
                <el-table-column prop="level" label="环(Environment)" align="center"></el-table-column>
                <el-table-column prop="level" label="监(Supervision)" align="center"></el-table-column>
                <el-table-column prop="level" label="绩(Performance)" align="center"></el-table-column>
               <el-table-column prop="level" label="指标总分" align="center"></el-table-column>
                <el-table-column prop="level" label="指标趋势" align="center"></el-table-column>
             
            </el-table> -->
  </div>
</template>

<script>
export default {
    props: {
      industryCategoryDTO: {
        type: Object,
        default: () => {}
      },
      tableData: {
        type: Array,
        default: () => []
      }
    },
  data() {
      return {
        headerCellStyle: { background: '#F1F6FF', color: '#333' },
      }
    },
  }
</script>

<style lang="scss" scoped>

</style>