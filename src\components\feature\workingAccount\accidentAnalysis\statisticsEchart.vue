<template>
  <div class="statisticsEchart">
    <div class="seach-part">
      <div class="l">
        <el-cascader
          size="mini"
          placeholder="请选择行政区划"
          :options="district"
          v-model="distCode"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          v-if="isShowDist"
        ></el-cascader>

        <!-- <div v-if="activeTabClass=='statisticsList'">
             <el-date-picker
          v-model="dateTime"
          size="mini"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          unlink-panels
          clearable
        >
        </el-date-picker>
        </div> -->

        <el-button type="primary" size="mini" @click="search">查询</el-button>
        <!-- <CA-button type="primary" size="mini" plain @click="exportExcel"
            >导出</CA-button
          > -->
      </div>
      <!-- <el-button type="primary" size="mini" @click="gotoTrendAnalysis"
          >趋势分析</el-button
        > -->
    </div>

    <div>
      <div class="dataOverview">
        <div class="echrtBox">
          <div class="dataOverviewL">
            <div class="tit">
              <span>近五年各类危化品事故趋势</span>
              <!-- <div class="titR">
                <span class="refresh">刷新</span>
                <span class="big">放大</span>
              </div> -->
            </div>
            <div
              class="echartsTopLeft"
              id="echartsTopLeft"
              style="width: 828px; height: 300px"
            ></div>
          </div>
          <div class="dataOverviewR">
            <div class="tit">
              <span>近五年各级别危化品事故趋势</span>
              <!-- <div class="titR">
                <span class="refresh">刷新</span>
                <span class="big">放大</span>
              </div> -->
            </div>
            <div
              class="echartsTopRight"
              id="echartsTopRight"
              style="width: 828px; height: 300px"
            ></div>
          </div>
        </div>

        <div class="echrtBox">
          <div class="dataOverviewL">
            <div class="tit">
              <span>危化品事故环比图</span>
              <!-- <div class="titR">
                <span class="refresh">刷新</span>
                <span class="big">放大</span>
              </div> -->
              <el-date-picker
                v-model="yearTime"
                value-format="yyyy"
                type="year"
                @change="yearTimeFn"
                size="mini"
                style="width: 105px"
                placeholder="选择年"
              >
              </el-date-picker>
            </div>
            <div
              class="echartsBottomLeft"
              id="echartsBottomLeft"
              style="width: 828px; height: 300px"
            ></div>
          </div>
          <div class="dataOverviewR">
            <div class="tit">
              <span>危化品事故同比图</span>
              <!-- <div class="titR">
                <span class="refresh">刷新</span>
                <span class="big">放大</span>
              </div> -->
            </div>
            <div
              class="echartsBottomRight"
              id="echartsBottomRight"
              style="width: 828px; height: 300px"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  eventStatisticsType,
  eventStatisticsLevel,
  eventStatisticsMothly,
  eventStatisticsGrowth,
} from "@/api/riskAssessment";
import { getDistrictUser } from "@/api/entList";
import { getEnt } from "@/api/dailySafety";

import { createNamespacedHelpers } from "vuex";
import { Message } from "element-ui";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
var dayjs = require("dayjs");
export default {
  components: {},
  data() {
    return {
      yearTime: "",
      dateTime: [],
      district: this.$store.state.controler.district,
      distCode: this.$store.state.login.userDistCode,
    };
  },
  methods: {
    search() {},
    yearTimeFn(val) {
      console.log(val);
      this.yearTime = val;
      this.initeventStatisticsMothly();
    },
    //左上-近五年各类危化品事故趋势
    echartsTopLeftFn(Arr) {
      var code01 = [];
      var code02 = [];
      var code03 = [];
      var code04 = [];
      var code05 = [];
      var code06 = [];
      var code07 = [];
      var xdata = [];
      var yData = [];
      Arr[0].list.forEach((item) => {
        //危化品火灾事故
        code01.push(item.NUM);
        xdata.push(item.YEAR);
      });
      Arr[1].list.forEach((item) => {
        //危化品爆炸事故
        code02.push(item.NUM);
      });
      Arr[2].list.forEach((item) => {
        //危化品中毒和窒息事故
        code03.push(item.NUM);
      });
      Arr[3].list.forEach((item) => {
        //危化品灼伤事故
        code04.push(item.NUM);
      });
      Arr[4].list.forEach((item) => {
        //危化品泄露事故
        code05.push(item.NUM);
      });
      Arr[5].list.forEach((item) => {
        //其他危化品事故
        code06.push(item.NUM);
      });
      Arr[6].list.forEach((item) => {
        //其他类型事故
        code07.push(item.NUM);
      });

      var chartDom = document.getElementById("echartsTopLeft");
      var myChart = this.$echarts.init(chartDom);
      var option;
      option = {
        // title: {
        //   text: "Stacked Line",
        // },
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: [
            "危化品火灾事故",
            "危化品爆炸事故",
            "危化品中毒和窒息事故",
            "危化品灼伤事故",
            "危化品泄露事故",
            "其他危化品事故",
            "其他类型事故",
          ],
        },
        grid: {
          top: "25%",
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {},
        //   },
        // },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: xdata,
        },
        yAxis: {
          type: "value",
          name: "突发事件(起)",
        },
        series: [
          {
            name: Arr[0].name,
            type: "line",
            // stack: "Total",
            data: code01,
          },
          {
            name: Arr[1].name,
            type: "line",
            // stack: "Total",
            data: code02,
          },
          {
            name: Arr[2].name,
            type: "line",
            // stack: "Total",
            data: code03,
          },
          {
            name: Arr[3].name,
            type: "line",
            // stack: "Total",
            data: code04,
          },
          {
            name: Arr[4].name,
            type: "line",
            // stack: "Total",
            data: code05,
          },
          {
            name: Arr[5].name,
            type: "line",
            // stack: "Total",
            data: code06,
          },
          {
            name: Arr[6].name,
            type: "line",
            // stack: "Total",
            data: code07,
          },
        ],
      };

      option && myChart.setOption(option);
    },

    //右上-近五年各级别危化品事故趋
    eventStatisticsLevelEchart(arr) {
      var xData = [];
      var code0 = [];
      var code1 = [];
      var code2 = [];
      var code3 = [];
      var code4 = [];
      arr[0].list.forEach((item) => {
        code0.push(item.NUM);
        xData.push(item.YEAR);
      });
      arr[1].list.forEach((item) => {
        code1.push(item.NUM);
      });
      arr[2].list.forEach((item) => {
        code2.push(item.NUM);
      });
      arr[3].list.forEach((item) => {
        code3.push(item.NUM);
      });
      var chartDom = document.getElementById("echartsTopRight");
      var myChart = this.$echarts.init(chartDom);
      var option;

      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {
          data: [arr[0].name, arr[1].name, arr[2].name, arr[3].name],
        },
        grid: {
          left: "3%",
          right: "12%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            name: "突发事件(起)",
            data: xData,
            axisTick: {
              alignWithLabel: true,
            },
          },
        ],
        yAxis: [
          {
            type: "value",
          },
        ],
        series: [
          {
            name: arr[0].name,
            type: "bar",
            barWidth: "20",
            data: code0,
          },
          {
            name: arr[1].name,
            type: "bar",
            barWidth: "20",
            data: code1,
          },
          {
            name: arr[2].name,
            type: "bar",
            barWidth: "20",
            data: code2,
          },
          {
            name: arr[3].name,
            type: "bar",
            barWidth: "20",
            data: code3,
          },
        ],
      };

      option && myChart.setOption(option);
    },

    //左下-危化事故环比图
    eventStatisticsMothlyFn(arr) {
      var xdata = [];
      var numData = [];
      var monthlyData = [];
      arr.forEach((item) => {
        xdata.push(item.month + "月");
        numData.push(item.num);
        monthlyData.push(item.monthly);
      });
      var chartDom = document.getElementById("echartsBottomLeft");
      var myChart = this.$echarts.init(chartDom);
      var option;
      option = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "cross",
            crossStyle: {
              color: "#999",
            },
          },
        },
        // toolbox: {
        //   feature: {
        //     dataView: { show: true, readOnly: false },
        //     magicType: { show: true, type: ["line", "bar"] },
        //     restore: { show: true },
        //     saveAsImage: { show: true },
        //   },
        // },
        legend: {
          data: ["危化品事故数量", "环比"],
        },
        xAxis: [
          {
            type: "category",
            // data: ["1月", "2月", "23月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"],
            data: xdata,
            axisPointer: {
              type: "shadow",
            },
          },
        ],
        yAxis: [
          {
            type: "value",
            name: "突发事件数量(起)",
            // min: 0,
            // max: 250,
            // interval: 50,
            axisLabel: {
              formatter: "{value}",
            },
          },
          {
            type: "value",
            name: "环比",
            // min: 0,
            // max: 25,
            // interval: 5,
            axisLabel: {
              formatter: "{value}",
            },
          },
        ],
        series: [
          {
            name: "危化品事故数量",
            type: "bar",
            // tooltip: {
            //   valueFormatter: function (value) {
            //     return value;
            //   },
            // },
            // data: [
            //   2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4,
            //   3.3,
            // ],
            data: numData,
          },

          {
            name: "环比",
            type: "line",
            yAxisIndex: 1,
            // tooltip: {
            //   valueFormatter: function (value) {
            //     return value;
            //   },
            // },
            // data: [
            //   2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2,
            // ],
            data: monthlyData,
          },
        ],
      };

      option && myChart.setOption(option);
    },

    //右下
    eventStatisticsGrowthFn(arr) {
      var code0 = []; //一季度
      var code1 = []; //二季度
      var code2 = []; //三季度
      var code3 = []; //四季度

      var chartDom = document.getElementById("echartsBottomRight");
      var myChart = this.$echarts.init(chartDom);
      var option;
      option = {
        // title: {
        //   text: "World Population",
        // },
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
        },
        legend: {},
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "value",
          boundaryGap: [0, 0.01],
        },
        yAxis: {
          type: "category",
          data: ["1季度", "2季度", "3季度", "4季度"],
        },
        series: [
          {
            name: arr[0].year,
            type: "bar",
            data: [arr[0].one, arr[0].two, arr[0].three, arr[0].four],
          },
          {
            name: arr[1].year,
            type: "bar",
            data: [arr[1].one, arr[1].two, arr[1].three, arr[1].four],
          },
          {
            name: arr[2].year,
            type: "bar",
            data: [arr[2].one, arr[3].two, arr[3].three, arr[3].four],
          },
          {
            name: arr[3].year,
            type: "bar",
            data: [arr[3].one, arr[3].two, arr[3].three, arr[3].four],
          },
          {
            name: arr[4].year,
            type: "bar",
            data: [arr[4].one, arr[4].two, arr[4].three, arr[4].four],
          },
        ],
      };

      option && myChart.setOption(option);
    },

    initData() {
      eventStatisticsType({ code: this.distCode }).then((res) => {
        if (res.data.status === 200) {
         

          this.echartsTopLeftFn(res.data.data);
        }
      });
      //右上-近五年各级别危化品事故趋
      eventStatisticsLevel({ code: this.distCode }).then((res) => {
        if (res.data.status === 200) {
          this.eventStatisticsLevelEchart(res.data.data);
        }
      });

      //右下
      eventStatisticsGrowth({
        code: this.distCode,
      }).then((res) => {
        if (res.data.status === 200) {
          this.eventStatisticsGrowthFn(res.data.data);
        }
      });
    },
    //左下-危化事故环比图
    initeventStatisticsMothly() {
      var param = {
        date: this.yearTime,
        code: this.distCode,
      };
      eventStatisticsMothly(param).then((res) => {
        if (res.data.status === 200) {
          this.eventStatisticsMothlyFn(res.data.data);
        }
      });
    },
    handleClickActiveTab() {},
  },
  created() {
    this.yearTime = dayjs().format("YYYY");
  },
  mounted() {
    // debugger
    // console.log(this.$store.state.controler.district)
    this.initData();
    this.initeventStatisticsMothly();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
    distCode: {
      handler(newVal, oldVal) {
        this.distCode = newVal;
        this.initData();
        this.initeventStatisticsMothly();
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.statisticsEchart {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 15px;
    margin-bottom: 15px;
    // margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      // display: flex;
      // justify-content: space-between;
      // padding: 10px 0;
      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
  .dataOverview {
    .tit {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: rgb(241, 246, 255);
      padding: 0 20px;
      span {
        // font-size: 20px;
        color: #000;
      }
      .titR {
        .refresh {
          margin: 0 10px 0 0;
        }
      }
    }
    .echrtBox {
      display: flex;
      justify-content: space-between;
      > div {
        width: 49%;
      }
      .dataOverviewL {
      }
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
