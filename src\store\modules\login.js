// import { stat } from "fs"

// initial state
const state = {
  // 视频监控模块数据
  user: {},
  // token: "bearer e7a41b76-673f-429e-a35d-adb9c333f540"
  token: "bearer 2d7505ca-6eb4-4721-aca8-586f7fb6726d",
  // token: "",
  enterData: {},
  userDistCode: "",
  isXiaZuan: true,
  park: {},
  isShowDist: true,
  activeName: "basicInformationTab",
  parkActiveName: "parkEnterList",
  isGmIframe: false,
};

// getters
const getters = {
  getUser: (state) => {
    return state.user;
  },
  getToken: (state) => {
    return state.token;
  },
  getUserDistCode: (state) => {
    return state.userDistCode;
  },
  getIsXiaZuan: (state) => {
    return state.isXiaZuan;
  },
  getPark: (state) => {
    return state.park;
  },
  getIsShowDist: (state) => {
    return state.isShowDist;
  },
  getIsGmIframe: (state) => {
    return state.isGmIframe;
  },
};

// actions
const actions = {
  setUser({ state, commit }, maptype) {
    commit("updataUser", maptype);
  },
  setUseres({ state, commit }, maptype) {
    commit("updataUseres", maptype);
  },
  setToekn({ state, commit }, maptype) {
    commit("updataToken", maptype);
  },
  setUserDistCode({ state, commit }, maptype) {
    commit("updataUserDistCode", maptype);
  },
  setIsXiaZuan({ state, commit }, maptype) {
    commit("updataIsXiaZuan", maptype);
  },
  setPark({ state, commit }, maptype) {
    commit("updataPark", maptype);
  },
  setIsShowDist({ state, commit }, maptype) {
    commit("updataIsShowDist", maptype);
  },
};

// mutations
const mutations = {
  updataActiveName(state, val) {
    state.activeName = val;
  },

  updataParkActiveName(state, val) {
    state.parkActiveName = val;
  },

  updataUser(state, val) {
    state.user = val;
  },
  updataUseres(state, val) {
    state.user = val;
  },
  updataToken(state, val) {
    state.token = val;
  },
  updataUserDistCode(state, val) {
    state.userDistCode = val;
  },
  updataIsXiaZuan(state, val) {
    state.isXiaZuan = val;
  },
  updataPark(state, val) {
    state.park = val;
  },
  updataIsShowDist(state, val) {
    state.isShowDist = val;
  },
  updataEnter(state, val) {
    state.enterData = val;
  },
  updataIsGmIframe(state, val) {
    state.isGmIframe = val;
  },
};

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations,
};
