<template>
  <el-dialog
    title="
重点监管危险化工工艺"
    :visible.sync="show"
    width="1350px"
    top="5vh"
    @close="closeBoolean(false)"
    :destroy-on-close="true"
    append-to-body
    :close-on-click-modal="false"
  >
    <div class="process" v-loading="loading">
      <div style="height: 70vh; overflow: auto">
        <div class="div1">
          <div class="table">
            <ul class="container">
              <li>
                <div class="l">企业名称</div>
                <div class="r">{{ regprocessDetails.enterpName }}</div>
              </li>
              <li>
                <div class="l">企业编码</div>
                <div class="r">{{ regprocessDetails.enterpId }}</div>
              </li>
              <li>
                <div class="l">* 统一社会信用代码</div>
                <div class="r">{{ regprocessDetails.entcreditCode }}</div>
              </li>
              <li>
                <div class="l">* 工艺名称</div>
                <div class="r">{{ regprocessDetails.processName }}</div>
              </li>
              <!-- <li>
              <div class="l">* 是否属于典型工艺</div>
              <div class="r" v-if="regprocessDetails.isTypicalProcess != null">{{regprocessDetails.isTypicalProcess==1?"是":"否"}}</div>
              <div class="r" v-else> </div>
            </li> -->

              <li>
                <div class="l">* 工艺投用日期</div>
                <div class="r">{{ regprocessDetails.investmentTime }}</div>
              </li>

              <li>
                <div class="l">工艺装置地址</div>
                <div class="r">{{ regprocessDetails.processAddress }}</div>
              </li>
              <li>
                <div class="l">* 是否配备化工自动化仪表控制系统</div>
                <div
                  class="r"
                  v-if="regprocessDetails.isChemicalSystem != null"
                >
                  {{ regprocessDetails.isChemicalSystem == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <!-- <li>
              <div class="l">BBBBASSS配备的控制系统类型</div>
              <div class="r" v-if="regprocessDetails.controlSystemType != null">{{regprocessDetails.controlSystemType == "01"?"集散控制系统(DCS)":"可编程逻辑器件系统(PLC)"}}</div>
              <div class="r" v-else> </div>
            </li> -->
              <li>
                <div class="l">* 是否配备独立安全仪表（SIS）</div>
                <div class="r" v-if="regprocessDetails.isSafeInstrument != null">
                  {{ regprocessDetails.isSafeInstrument == 1 ? "是" : "否" }}
                </div>
                <div class="r" v-else></div>
              </li>
              <li>
                <div class="l">工艺危险性特征</div>
                <div class="r">{{ regprocessDetails.hazardFeature }}</div>
                <!-- <div class="r" v-if=" regprocessDetails.isSafeInstrument != null">{{regprocessDetails.isSafeInstrument==1?"是":"否"}}</div>
              <div class="r" v-else></div> -->
              </li>
              <!-- <li>
              <div class="l">* 该危险工艺段所在车间现场作业人数(人)</div>
              <div class="r">{{regprocessDetails.processWorkerNum}}</div>
            </li> -->
              <li>
                <div class="l">* 装置名称</div>
                <div class="r">{{ regprocessDetails.deviceName }}</div>
              </li>
              <li style="width: 66.6%; border-right: 1px solid #e7e7e7">
                <div class="l">* 单元内主要装置、设施及生产(储存)规模</div>
                <div class="r">{{ regprocessDetails.equipmentScale }}</div>
              </li>
              <li class="lang bottom W100">
                <div class="l">工艺危险性描述</div>
                <div class="r">{{ regprocessDetails.hazardInfo }}</div>
              </li>
              <!-- <li>
              <div class="l">经度</div>
              <div class="r">{{regprocessDetails.longitude}}</div>
            </li> -->
              <!-- <li>
              <div class="l">纬度</div>
              <div class="r">{{regprocessDetails.latitude}}</div>
            </li> -->
              <li class="lang bottom W100">
                <!-- <div class="l">* 反应热效应</div>
              <div class="r" v-if="regprocessDetails.reactionType==1">放热反应</div>
              <div class="r" v-else-if="regprocessDetails.reactionType==2">吸热反应</div>
              <div class="r" v-else-if="regprocessDetails.reactionType==5">其他</div>
              <div class="r" v-else></div> -->
                <div class="l">* 反应类型</div>
                <div class="r">{{ regprocessDetails.reactionType }}</div>
              </li>
              <li class="lang bottom W100">
                <div class="l">* 工艺控制参数</div>
                <div class="r">
                  {{ regprocessDetails.controlParameter }}
                </div>
              </li>
              <li class="lang bottom W100">
                <div class="l">* 安全控制的基本要求及采用的控制方式</div>
                <div class="r">{{ regprocessDetails.controlWay }}</div>
              </li>
              <li class="lang bottom W100">
                <div class="l">近3年事故信息</div>
                <div class="r">{{ regprocessDetails.accidentInfo }}</div>
              </li>
            </ul>
          </div>
        </div>
        <div class="div2">
          <div class="title">危险化学品</div>
          <div class="table" v-if="hazarchemSaiSi.length > 0">
            <ul class="container header">
              <li>* 危险化学品名称</li>
              <li>化学品别名</li>
              <li>CAS号</li>
              <li>化学品属性</li>
              <li>* 设计储存量(吨/标方)</li>
              <li class="liLine">年产量/消耗量(吨/标方)</li>
            </ul>
            <ul
              class="container"
              v-for="(item, index) in hazarchemSaiSi"
              :key="index"
            >
              <li>{{ item.hazarchemName }}</li>
              <li>{{ item.chemicalAlias }}</li>
              <li>{{ item.casNo }}</li>
              <li>{{ item.hazarchemProperty }}</li>
              <li>{{ item.storageNum }}</li>
              <li class="liLine">{{ item.annualUsageMeal }}</li>
            </ul>
          </div>
          <div class="table" v-else>
            <ul class="container header">
              <li>* 危险化学品名称</li>
              <li>化学品别名</li>
              <li>CAS号</li>
              <li>化学品属性</li>
              <li>* 设计储存量(吨/标方)</li>
              <li class="liLine">年产量/消耗量(吨/标方)</li>
            </ul>
            <ul class="container">
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li></li>
              <li class="liLine back"></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { getInformationInfoRelation } from "@/api/entList";
export default {
  //import引入的组件
  name: "process",
  components: {},
  data() {
    return {
      show: false,
      regprocessDetails: {},
      hazarchemSaiSi: {},
      loading: false,
    };
  },
  //方法集合
  methods: {
    closeBoolean(val) {
      this.show = val;
      this.regprocessDetails = {};
      this.hazarchemSaiSi = {};
    },
    getData(id) {
      this.loading = true;
      getInformationInfoRelation(id).then((res) => {
        this.regprocessDetails = res.data.data.regprocessDetails;
        this.hazarchemSaiSi = res.data.data.hazarchemSaiSi;
        this.loading = false;
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
.W100 {
  width: 99.9% !important;
  border-bottom: 0 !important;
}
.W100:last-child {
  border-bottom: 1px solid #e7e7e7 !important;
}
.process .div1 .table .container .lang.W100 .l {
  width: 16.65% !important;
}
.process .div1 .table .container .lang.W100 .r {
  width: 82.5% !important;
  padding: 5px 10px !important;
}
.process {
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        li:nth-of-type(3n + 0) {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 99.97%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 16.65%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 82.5%;
            padding: 5px 10px;
            flex-wrap: wrap;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            background-color: rgb(242, 242, 242);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
  .div2 {
    margin-top: 20px;
    padding-bottom: 60px;
    ul {
      margin-block-end: 0;
    }
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .header {
        color: #60627a;
        background: rgb(242, 246, 255);
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 16.6%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          display: flex;
          justify-content: center;

          align-items: center;
        }
        .liLine {
          list-style-type: none;
          width: 17%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: center;
          text-align: center;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
      .container:nth-last-of-type(1) {
        border-bottom: 1px solid rgb(231, 231, 231);
      }
    }
  }
}
</style>
