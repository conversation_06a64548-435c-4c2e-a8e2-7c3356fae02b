<template>
  <div class="recruit-canvas">
    <div class="canvas-box" ref="canvasRef">
      <canvas
        ref="canvasMapRef"
        id="canvas-map"
        width="100"
        height="100"
      ></canvas>
    </div>

    <div class="btn-box flex-row">
      <div class="del-btn" @click="clearCanvasHandle">重写</div>
      <div class="sure-btn" type="primary" @click="makeCanvasHandle">提交</div>
      <!-- <span class="del-btn" @click="clearCanvasHandle">清除</span>
      <span class="sure-btn" @click="makeCanvasHandle">确认</span> -->
    </div>

    <!-- <img v-show="previewImage" class="preview-image" :src="previewImage" alt="生成预览"> -->
  </div>
</template>

<script>
import SignaturePad from "signature_pad";
import axios from "axios";
import { projectApprovalDecide } from "@/api/companyParticularJob";
import { rotateBase64Img } from "@/utils/common";

export default {
  name: "signNameCanvas",
  data() {
    return {
      canvasNode: null,
      previewImage: null,
      imgUrl: "",
    };
  },
  mounted() {
    this.initalHandle();
    window.addEventListener("resize", this.initalHandle, false);
  },
  methods: {
    initalHandle() {
      const _canvasBox = this.$refs.canvasRef;
      const _canvas = this.$refs.canvasMapRef;
      if (!_canvasBox || !_canvas) {
        return false;
      }

      _canvas.width = _canvasBox.clientWidth;
      _canvas.height = _canvasBox.clientHeight;

      this.clearCanvasHandle();
      this.canvasNode = new SignaturePad(_canvas, {
        minWidth: 2,
        maxWidth: 2,
        penColor: "rgb(0, 0, 0)",
      });
    },

    clearCanvasHandle() {
      if (this.canvasNode) {
        this.canvasNode.clear();
        this.previewImage = null;
      }
    },

    makeCanvasHandle() {
      const canvasNode = this.canvasNode;
      var cdata = this.previewImage;

      // 重新初始化画布
      if (!canvasNode) {
        this.initalHandle();
      }

      // 是否签字
      if (canvasNode.isEmpty()) {
        // this.$toast('您还没有签名')
        this.$message.info("您还没有签名");
        return false;
      }     
      // 图像旋转二次处理
      const _boxWidth = window.innerWidth;
      const _boxHeight = window.innerHeight;
      const _signImg = canvasNode.toDataURL();
      if (_boxWidth < _boxHeight) {
        rotateBase64Img(_signImg, -90, (imgUrlRes) => {
          this.previewImage = imgUrlRes;
          this.handleClose(this.previewImage);
        });
      } else {
        this.previewImage = _signImg;
        this.handleClose(this.previewImage);
      }
     
     

      // this.imgUrl = this.$refs.canvasMapRef.toDataURL();
      // console.log(imgUrl, "图片null");
      // console.log(this.$route.query, "query");
      // this.handleClose( this.imgUrl);
      // upload ajax
      // do something here
    },
    //签名提交
    handleClose(imgUrl) {
      var params = this.$route.query;
      this.dialogVisible = false;
      //base64图片附件上传 attachment/uploadBase64/v1
      var paramUploadBase64 = {
        file: imgUrl,
        module: "chemical",
        name: this.timeFormate(new Date()) + params.expertName, //当前日期+专家姓名 20220707AB
        path: "/uploadFile/chemical/projectApproval/" + params.infoId,
      };
      axios({
        method: "post",
        url: "/gemp-file/api/attachment/uploadBase64/v1",
        data: paramUploadBase64,
        // responseType: "arraybuffer",
      }).then((res) => {
        if (res && res.status == 200) {
          var attachmentOutDTOs = [res.data.data];
          var param = {
            approvalId: params.approvalId,
            expertApproval: params.expertApproval,
            expertComment: params.expertComment,
            attachmentOutDTOs: attachmentOutDTOs,
            commentAttachments:params.commentAttachments
          };
          console.log(param, "专家签名提交param");
          projectApprovalDecide(param).then((res) => {
            if (res.data.status === 200) {
              this.isclick = true;
              // location.reload();
              this.$router.go(-1);
              this.$message.success(res.data.msg); //?？？？?????有问题
            } else {
              this.$message.error(res.data.msg);
            }
          });
        }
      });
    },
    timeFormate(timeStamp) {
      let year = new Date(timeStamp).getFullYear();
      let month =
        new Date(timeStamp).getMonth() + 1 < 10
          ? "0" + (new Date(timeStamp).getMonth() + 1)
          : new Date(timeStamp).getMonth() + 1;
      let date =
        new Date(timeStamp).getDate() < 10
          ? "0" + new Date(timeStamp).getDate()
          : new Date(timeStamp).getDate();
      let hh =
        new Date(timeStamp).getHours() < 10
          ? "0" + new Date(timeStamp).getHours()
          : new Date(timeStamp).getHours();
      let mm =
        new Date(timeStamp).getMinutes() < 10
          ? "0" + new Date(timeStamp).getMinutes()
          : new Date(timeStamp).getMinutes();
      let ss =
        new Date(timeStamp).getSeconds() < 10
          ? "0" + new Date(timeStamp).getSeconds()
          : new Date(timeStamp).getSeconds();
      let week = new Date(timeStamp).getDay();
      let weeks = ["日", "一", "二", "三", "四", "五", "六"];
      let getWeek = "星期" + weeks[week];
      return year + month + date;
    },

    rotateBase64Img2(src, edg) {
      var canvas = document.createElement("canvas");
      var ctx = canvas.getContext("2d");
      var imgW; //图片宽度
      var imgH; //图片高度
      var size; //canvas初始大小
      if (edg % 90 != 0) {
        console.error("旋转角度必须是90的倍数!");
        throw "旋转角度必须是90的倍数!";
      }
      edg < 0 && (edg = (edg % 360) + 360);
      const quadrant = (edg / 90) % 4; //旋转象限
      const cutCoor = { sx: 0, sy: 0, ex: 0, ey: 0 }; //裁剪坐标
      var image = new Image();
      image.crossOrigin = "anonymous";
      image.src = src;
      image.onload = function () {
        imgW = image.width;
        imgH = image.height;
        size = imgW > imgH ? imgW : imgH;
        canvas.width = size * 2;
        canvas.height = size * 2;
        switch (quadrant) {
          case 0:
            cutCoor.sx = size;
            cutCoor.sy = size;
            cutCoor.ex = size + imgW;
            cutCoor.ey = size + imgH;
            break;
          case 1:
            cutCoor.sx = size - imgH;
            cutCoor.sy = size;
            cutCoor.ex = size;
            cutCoor.ey = size + imgW;
            break;
          case 2:
            cutCoor.sx = size - imgW;
            cutCoor.sy = size - imgH;
            cutCoor.ex = size;
            cutCoor.ey = size;
            break;
          case 3:
            cutCoor.sx = size;
            cutCoor.sy = size - imgW;
            cutCoor.ex = size + imgH;
            cutCoor.ey = size + imgW;
            break;
        }
        ctx.translate(size, size);
        ctx.rotate((edg * Math.PI) / 180);
        ctx.drawImage(image, 0, 0);
        var imgData = ctx.getImageData(
          cutCoor.sx,
          cutCoor.sy,
          cutCoor.ex,
          cutCoor.ey
        );
        if (quadrant % 2 == 0) {
          canvas.width = imgW;
          canvas.height = imgH;
        } else {
          canvas.width = imgH;
          canvas.height = imgW;
        }
        ctx.putImageData(imgData, 0, 0);
        // $("textarea", $tools).val(canvas.toDataURL());
        //callback(canvas.toDataURL())
       
      };
      return canvas.toDataURL()
    },
  },
  beforeDestroy() {
    window.removeEventListener("resize", this.initalHandle, false);
  },
};
</script>

<style lang='css' scoped>
.recruit-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.recruit-canvas .preview-image {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 999;
  transform: translate(-50%, -50%);
}

.recruit-canvas .canvas-box,
.recruit-canvas .btn-box {
  position: absolute;
  top: 50%;
  z-index: 100;
}

.recruit-canvas .btn-box {
  left: -22%;
  z-index: 1000;
  text-align: center;
  transform: rotate(90deg);
  -o-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  -moz-transform: rotate(90deg);
  -webkit-transform: rotate(90deg);
}

.recruit-canvas .btn-box .del-btn,
.recruit-canvas .btn-box .sure-btn {
  display: inline-block;
  width: 100px;
  height: 24px;
  margin: 0 10px;
  line-height: 24px;
  border-radius: 6px;
  background-color: #fff;
  cursor: pointer;
}

.recruit-canvas .btn-box .del-btn {
  background: #fff;
  border: 1px solid #dcdfe6;
}

.recruit-canvas .btn-box .sure-btn {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.recruit-canvas .canvas-box {
  left: 22%;
  height: 80vh;
  width: 70vw;
  overflow: hidden;
  border: 1px dashed #d4d4d4;
  transform: translateY(-50%);
  background-color: #fff;
}

.recruit-canvas .canvas-box #canvas-map {
  width: 100%;
  height: 100%;
}

@media screen and (orientation: portrait) {
  /*竖屏 css*/
}

@media screen and (orientation: landscape) {
  /*横屏 css*/
  .recruit-canvas .canvas-box {
    top: 20px;
    left: 10%;
    width: 80vw;
    height: 70vh;
    transform: translateY(0);
  }

  .recruit-canvas .btn-box {
    width: 60%;
    left: 20%;
    top: 86%;
    transform: rotate(0);
  }
}
</style>
