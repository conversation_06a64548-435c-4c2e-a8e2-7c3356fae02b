<template>
  <div class="safety-check">
    <!-- 头部面包屑 -->
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box" @click="goBack">
              <a-icon type="home" theme="filled" class="icon" />
              采集报表管理
            </span>
          </a-breadcrumb-item>
          <a-breadcrumb-item>
            <span>{{ reportName }}</span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 搜索工具栏 -->
      <div class="tool-bar">
        <div class="right">
          <el-form :inline="true" :model="searchForm">
            <el-form-item>
              <el-date-picker
                v-model="searchForm.timeRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                size="mini"
              >
              </el-date-picker>
            </el-form-item>
            <!-- <el-form-item v-if="isGov">
              <el-cascader
                v-model="searchForm.districtCode"
                :options="district"
                :props="{
                  checkStrictly: true,
                  value: 'distCode',
                  label: 'distName',
                  children: 'children',
                  emitPath: false,
                }"
                clearable
                placeholder="请选择地市/区县"
                size="mini"
                style="width: 190px"
              ></el-cascader>
            </el-form-item> -->
            <el-form-item>
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入企业名称关键字"
                size="mini"
                clearable
                style="width: 200px"
              ></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" size="mini" @click="handleSearch">
                检索
              </el-button>
              <el-button type="primary" size="mini" @click="handleShare">
                分享
              </el-button>
              <el-button type="primary" size="mini" @click="test">
                H5
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" border style="width: 100%" height="650px">
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column
          prop="enterpriseName"
          label="企业名称"
          min-width="200"
        />
        <el-table-column prop="director" label="主要负责人" width="120" />
        <el-table-column prop="phone" label="联系电话" width="120" />
        <el-table-column
          prop="enterpriseAddress"
          label="企业地址"
          min-width="200"
        />
        <el-table-column prop="reportDate" label="提交时间" width="160" />
        <el-table-column label="附件" min-width="150">
          <template slot-scope="scope">
            <template
              v-if="
                scope.row.reportAttachIdList &&
                scope.row.reportAttachIdList.length
              "
            >
              <div
                v-for="file in scope.row.reportAttachIdList"
                :key="file.attachId"
                class="file-item"
              >
                <el-link
                  type="primary"
                  :underline="false"
                  @click="handleDownload(file)"
                >
                  <i class="el-icon-document"></i>
                  {{ file.fileName }}
                </el-link>
              </div>
            </template>
            <span v-else>暂无附件</span>
          </template>
        </el-table-column>
        <!-- 状态 -->
        <el-table-column prop="isReturn" label="状态" width="120">
          <template slot-scope="scope">
            <span v-if="scope.row.isReturn == '1'">未提交</span>
            <span v-else>已提交</span>
          </template>
        </el-table-column>
        <!-- 新增操作列 -->
        <el-table-column label="操作" width="300" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="handleView(scope.row)">
              查看
            </el-button>
            <el-button type="text" size="mini" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="text"
              size="mini"
              @click="handleReturn(scope.row)"
              v-if="scope.row.isReturn == '0'"
            >
              退回
            </el-button>
            <el-button
              type="text"
              class="delete-btn"
              size="mini"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          :current-page="page.current"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="page.size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <qrCodeDialog
      v-if="showQrCode"
      :visible="showQrCode"
      :url="qrcodeDataURL"
      :reportId="reportId"
      @closeBoolean="showQrCode = false"
    />

    <!-- 表单弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="formDialogVisible"
      width="60%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
      class="form-dialog"
    >
      <ReportPC ref="reportPC" @close="closeFormDialog"></ReportPC>
    </el-dialog>
  </div>
</template>

<script>
import {
  getEnterpriseInfoList,
  returnReportGatherContent,
  deleteReportGatherContent,
} from "@/api/smartReport";
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js";
import qrCodeDialog from "./qrCodeDialog.vue";
import ReportPC from "./reportPC.vue";

export default {
  name: "GatherReportDetail",
  components: {
    qrCodeDialog,
    ReportPC,
  },
  data() {
    return {
      reportId: "",
      reportName: "",
      searchForm: {
        timeRange: [],
        // districtCode: "",
        keyword: "",
      },
      district: this.$store.state.controler.district,
      isGov: this.$store.state.login.user.user_type === "gov",
      tableData: [],
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      showQrCode: false,
      qrcodeDataURL: "",
      returnDialogVisible: false,
      returnForm: {
        reason: "",
        id: null,
      },
      returnRules: {
        reason: [
          { required: true, message: "请输入退回原因", trigger: "blur" },
        ],
      },
      formDialogVisible: false,
      dialogTitle: "",
    };
  },
  methods: {
    initData(data) {
      this.reportId = data.id;
      this.reportName = data.gatherName;
      this.getList();
    },

    // 获取列表数据
    async getList() {
      try {
        const [startTime, endTime] = this.searchForm.timeRange || [];
        const params = {
          gatherId: this.reportId,
          nowPage: this.page.current,
          pageSize: this.page.size,
          startTime,
          endTime,
          // keyword: this.searchForm.keyword,
        };

        const res = await getEnterpriseInfoList(params);
        if (res.data.status === 200) {
          const { list, total } = res.data.data;
          this.tableData = list;
          this.page.total = total;
        }
      } catch (error) {
        console.error("获取列表失败:", error);
        this.$message.error("获取列表失败");
      }
    },

    handleSearch() {
      this.page.current = 1;
      this.getList();
    },

    handleShare() {
      // if (process.env.NODE_ENV === "development") {
      //   this.$router.push({
      //     path: "/reportH5",
      //     query: { id: this.reportId },
      //   });
      // } else {
      this.showQrCode = true;
      this.qrcodeDataURL = `https://zhyj.yjj.wuhan.gov.cn:8993/reportH5?id=${this.reportId}`;
      // }
    },

    test() {
      this.$router.push({
        path: "/reportH5",
        query: { id: this.reportId },
      });
    },

    handleSizeChange(val) {
      this.page.size = val;
      this.getList();
    },

    handleCurrentChange(val) {
      this.page.current = val;
      this.getList();
    },

    goBack() {
      this.$emit("goBack");
    },

    // 处理文件下载
    handleDownload(file) {
      try {
        if (!file.url) {
          this.$message.warning("文件地址不存在");
          return;
        }

        Attachmentdownload({ fileId: file.attachId }).then((res) => {
          downloadFuc(res, file.name);
        });
      } catch (error) {
        console.error("下载失败:", error);
        this.$message.error("下载失败");
      }
    },

    // 查看详情
    handleView(row) {
      this.dialogTitle = "查看表单信息";
      this.formDialogVisible = true;
      row.isEdit = false;
      this.$nextTick(() => {
        this.$refs.reportPC.initData(row);
      });
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = "编辑表单信息";
      this.formDialogVisible = true;
      row.isEdit = true;
      this.$nextTick(() => {
        this.$refs.reportPC.initData(row);
      });
    },

    // 退回操作
    async handleReturn(row) {
      try {
        await this.$confirm("确认退回该记录?", "提示", {
          type: "warning",
        });
        // TODO: 调用退回接口
        const res = await returnReportGatherContent({ id: row.id });
        if (res.data.status === 200) {
          this.$message.success("退回成功");
          this.getList();
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("退回失败:", error);
          this.$message.error("退回失败");
        }
      }
    },

    // 删除
    async handleDelete(row) {
      try {
        await this.$confirm("确认删除该记录?", "提示", {
          type: "warning",
        });
        const res = await deleteReportGatherContent({ id: row.id });
        if (res.data.status === 200) {
          this.$message.success("删除成功");
          this.getList();
        }
      } catch (error) {
        if (error !== "cancel") {
          console.error("删除失败:", error);
          this.$message.error("删除失败");
        }
      }
    },

    // 关闭表单弹窗
    closeFormDialog() {
      this.formDialogVisible = false;
      this.getList(); // 刷新列表数据
    },
  },
};
</script>

<style lang="scss" scoped>
.safety-check {
  height: 100%;

  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;

      .icon-box {
        cursor: pointer;

        &:hover {
          color: #3977ea;

          .icon {
            color: #3977ea;
          }
        }
      }

      .icon {
        color: #6f81b5;
        font-size: 15px;
      }
    }
  }

  .content {
    padding: 20px;
    background-color: #fff;

    .tool-bar {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .pagination {
      margin-top: 20px;
      text-align: right;
    }
  }

  .file-item {
    margin: 5px 0;

    .el-link {
      display: inline-flex;
      align-items: center;

      i {
        margin-right: 5px;
      }
    }

    &:hover {
      color: #409eff;
    }
  }

  .delete-btn {
    color: #f56c6c;
  }

  // 表单弹窗样式
  ::v-deep .form-dialog {
    .el-dialog {
      margin-top: 5vh !important;

      .el-dialog__body {
        padding: 0;
        height: calc(90vh - 108px);
      }
    }
  }
}
</style>
