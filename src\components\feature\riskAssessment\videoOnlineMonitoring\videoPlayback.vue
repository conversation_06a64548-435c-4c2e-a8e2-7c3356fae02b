<template>
  <div class="enterpriseManagement">
    <div>
      <div class="videoInspection">
        <div class="video-left">
          <div class="tabTit">
            <span
              @click="clickTab(1)"
              :class="[tabTitActive == 1 ? 'active' : '']"
              >实时视频</span
            >
            <!-- 视频回放功能暂时屏蔽 -->
            <!-- <span
              @click="clickTab(2)"
              :class="[tabTitActive == 2 ? 'active' : '']"
              >视频回放</span
            > -->
          </div>

          <div v-if="tabTitActive == 1">
            <div class="videoLeft-top">
              <div class="list-search">
                <el-input
                  v-model.trim="enterpName"
                  size="mini"
                  placeholder="请输入企业名称"
                  class="input"
                  clearable
                  style="width: 260px; margin-left: 10px"
                ></el-input>
                <el-button type="primary" size="mini" @click="search"
                  >查询</el-button
                >
              </div>
              <!-- {{newAllData}} -->
              <div class="video-list" v-loading="loading">
                <a-directory-tree
                  multiple
                  default-expand-all
                  @select="onSelect"
                  @expand="onExpand"
                  style="padding: 0 10px"
                >
                  <a-tree-node
                    :key="item.id + ',' + item.type"
                    v-for="item in newAllData"
                    :title="item.name"
                  >
                    <a-tree-node
                      v-if="item.children.length > 0"
                      :key="subItem.id + ',' + subItem.type"
                      v-for="subItem in item.children"
                      :title="subItem.name"
                    >
                      <a-tree-node
                        v-if="subItem.children.length > 0"
                        :key="subItems.id + ',' + subItems.type"
                        v-for="subItems in subItem.children"
                        :title="subItems.name"
                      >
                        <a-tree-node
                          v-if="subItems.children.length > 0"
                          :key="subItemd.id + ',' + subItemd.type"
                          v-for="subItemd in subItems.children"
                          :title="subItemd.name"
                        >
                          <a-tree-node
                            v-if="subItemd.children.length > 0"
                            :key="subItemed.id + ',' + subItemed.type"
                            v-for="subItemed in subItemd.children"
                            :title="subItemed.name"
                          ></a-tree-node>
                        </a-tree-node>
                      </a-tree-node>
                    </a-tree-node>
                  </a-tree-node>
                </a-directory-tree>
              </div>
            </div>
          </div>

          <!-- 视频回放功能暂时屏蔽 -->
          <!-- <div v-if="tabTitActive == 2">
            视频回放相关内容已屏蔽
          </div> -->
        </div>
        <div class="video-right">
          <!-- 四路实时视频 -->
          <div class="video-content">
            <multiVideoPlayer ref="multiVideoPlayer" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getOnlineVideoData } from "@/api/riskAssessment";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
import multiVideoPlayer from "./multiVideoPlayer.vue";
import { getVideoPreviewUrl } from "@/api/riskAssessment";
export default {
  components: { multiVideoPlayer },
  data() {
    return {
      tabTitActive: "1", // 固定为实时视频
      newAllData: [],
      enterpName: "",
      loading: true,
    };
  },
  methods: {
    // 为了兼容父组件的调用，添加getList方法
    getList() {
      this.getOnlineVideoDataList();
    },
    clickTab(val) {
      this.tabTitActive = val;
      // 只保留实时视频功能
    },
    //视频运行列表
    getOnlineVideoDataList() {
      this.loading = true;
      getOnlineVideoData({
        enterpName: this.enterpName,
      }).then((res) => {
        if (res.data.code == 0) {
          this.loading = false;
          this.newAllData = res.data.data;
          this.newAllData2 = res.data.data;
        }
      });
    },

    goToRunning() {},
    async onSelect(selectedKeys) {
      if (selectedKeys[0].split(",")[1] == "3") {
        const cameraId = selectedKeys[0].split(",")[0];
        const cameraName = selectedKeys[0].split(",")[2] || "摄像头";

        console.log("选择摄像头:", { cameraId, cameraName });

        try {
          // 获取视频流URL
          const res = await getVideoPreviewUrl({
            channelId: cameraId,
            schema: 5, // 使用默认的流媒体协议
            subType: 1, // 实时预览
          });

          if (res.status === 200 && res.data?.data?.url) {
            const videoSource = {
              id: cameraId,
              name: cameraName,
              url: res.data.data.url,
              channelId: cameraId,
              expireTime: res.data.data.expireTime,
              schema: res.data.data.schema,
              status: "online",
            };

            // 添加到四路视频播放器
            this.$nextTick(() => {
              if (
                this.$refs.multiVideoPlayer &&
                typeof this.$refs.multiVideoPlayer.addVideoToChannel ===
                  "function"
              ) {
                const channelIndex =
                  this.$refs.multiVideoPlayer.addVideoToChannel(videoSource);
                if (channelIndex >= 0) {
                  console.log(`视频已添加到通道 ${channelIndex + 1}`);
                } else {
                  console.error("添加视频到多路播放器失败");
                }
              } else {
                console.error("多路播放器组件未准备好");
              }
            });
          } else {
            this.$message.error(
              "获取视频流失败：" + (res.data?.msg || "未知错误")
            );
          }
        } catch (error) {
          console.error("获取视频流失败:", error);
          this.$message.error(
            "获取视频流失败：" + (error.message || "未知错误")
          );
        }
      }
    },
    onExpand() {},
    search() {
      this.getOnlineVideoDataList();
    },
  },
  mounted() {
    this.getOnlineVideoDataList();
  },
  destroyed() {
    console.log("销毁视频组件~");
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
    }),
  },
};
</script>
<style lang="scss" scoped>
/deep/ .el-form-item {
  margin-top: 0;
  margin-bottom: 0;
}
.btnBox {
  display: flex;
  justify-content: center;
  margin-top: 3px;
}
.tabTit {
  padding: 0;
  position: relative;
  margin: 0 0 15px;
  border-bottom: 2px solid #e4e7ed;
  span {
    margin: 0 20px;
    height: 40px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 500;
    color: #303133;
    position: relative;
    cursor: pointer;
    padding: 0 20px;
  }
  span.active {
    background: #daefff;
    color: #409eff;
  }
  span.active:before {
    border-bottom: 2px solid #409eff;
    position: absolute;
    bottom: -2px;
    content: "";
    width: 100%;
    left: 0;
  }
  span:first-child {
    margin-left: 0;
  }
}
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
    .vedioBox {
      display: flex;
      margin-bottom: 10px;
      div:last-child {
        margin: 0 0 0 10px;
      }
    }
  }
  .videoInspection {
    // padding: 15px 0;
    overflow: hidden;
    .video-left {
      float: left;
      width: 345px;
      margin-right: 25px;
      margin-top: 15px;
      .videoLeft-top {
        background: #daefff;
        .list-search {
          padding-top: 15px;
        }
        .video-list {
          height: calc(100vh - 250px);
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
      .videoLeft-top2 {
        .list-search {
          padding-top: 15px;
          background: #daefff;
        }
        .video-list {
          background: #daefff;
          height: calc(100vh - 430px);
          padding: 15px 0;
          border-radius: 4px;
          overflow-y: scroll;
        }
      }
    }
    .video-right {
      float: left;
      position: relative;
      width: calc(100% - 370px);
      height: calc(100vh - 120px);
      margin-top: 15px;

      .video-content {
        width: 100%;
        height: 100%;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
      }
    }
  }
}
</style>
