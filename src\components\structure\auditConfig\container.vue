<template>
  <div class="container">
    <div class="header">
      <!-- <div class="title">菜单信息</div>
      <el-button type="primary" class="newMenu" @click="newMenu()" icon="plus"
        ><span>添加规则</span></el-button
      > -->
      <div class="search-box">
        <div class="select-maker" style="margin-right: 20px">
          <label class="label">配置key:</label>
          <el-input placeholder="配置key" clearable> </el-input>
        </div>
        <div class="select-maker">
          <label class="label">配置描述:</label>
          <el-input placeholder="配置描述" clearable> </el-input>
        </div>
        <div class="seach-btn">
          <el-button type="primary" icon="el-icon-search" @click="search"
            >查询</el-button
          >
          <el-button
            type="primary"
            icon="el-icon-circle-plus-outline"
            @click="newMenu()"
            >新建</el-button
          >
        </div>
      </div>
    </div>
    <div class="body" v-loading="loading">
      <el-table
        :data="tableData.records"
        style="width: 100%; color: rgb(101, 101, 101)"
      >
        <el-table-column type="index" label="编号" width="80">
        </el-table-column>
        <el-table-column prop="rulePwdCode" label="规则表达式" width="280">
        </el-table-column>
        <el-table-column prop="rulePwdName" label="规则描述" width="280">
        </el-table-column>
        <el-table-column prop="isSelected" label="状态" align="center">
          <template slot-scope="scope">
            <span>{{
              scope.row.isSelected == "true" ? "已启用" : "已停用"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="isSelected" label="启用/关闭" align="center">
          <template slot-scope="scope">
            <i
              :class="
                scope.row.isSelected == 'true'
                  ? 'el-icon-unlock'
                  : 'el-icon-lock'
              "
              style="font-size: 16px"
            >
              <!-- @click="setPwdRuleUpdateStatus(scope.row)" -->
            </i>
          </template>
        </el-table-column>
        <el-table-column align="right" label="操作">
          <template slot-scope="scope">
            <el-button
              size="small"
              type="primary"
              @click="handleEdit(scope.$index, scope.row)"
              ><i class="el-icon-edit"></i
            ></el-button>
            <el-button
              size="small"
              type="danger"
              @click="handleDelete(scope.$index, scope.row)"
              ><i class="el-icon-delete"></i
            ></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        @current-change="handleCurrentChange"
        @prev-click="handleCurrentChange"
        @next-click="handleCurrentChange"
        :current-page.sync="currentPage"
        :page-size="tableData.size"
        layout="prev, pager, next, jumper"
        :total="tableData.total"
      >
      </el-pagination>
    </div>
    <DialogTable
      ref="child"
      :dialogTableVisible="dialogTableVisible"
    ></DialogTable>
  </div>
</template>

<script>
import {
  getPwdRuleList,
  updatePwdRuleStatus,
  deleteByPwdRuleId,
} from "../../../api/user";
import DialogTable from "./table";
import { mapState, mapGetters } from "vuex";
import Bus from "../../../utils/bus";
export default {
  //import引入的组件
  components: {
    DialogTable,
  },
  data() {
    return {
      dialogTableVisible: true,
      currentPage: 1,
      tableData: {},
      loading: false,
    };
  },
  //方法集合
  methods: {
    //打开编辑弹框
    handleEdit(index, row) {
      this.dialogTableVisible = true;
      //打开表单填报
      this.$refs.child.parentMsg(this.dialogTableVisible);
      //数据给到编辑表单，关闭系统编码填报
      this.$refs.child.getData(row);
    },
    //删除
    handleDelete(index, row) {
      this.$confirm("确定要删除选择的数据吗？", "通知", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          deleteByPwdRuleId(row.ruleId)
            .then((res) => {
              this.$message({
                message: res.data.msg,
                type: "success",
              });
              if (this.tableData.total % this.tableData.size == 1) {
                this.currentPage = this.currentPage - 1;
              }
              this.getMenuData(this.currentPage, 1);
              this.$emit("pf");
            })
            .catch((e) => {
              console.log(e, "请求错误");
            });
        })
        .catch(() => {
          // this.$message({
          //   type: "info",
          //   message: "已取消删除",
          // });
        });
    },
    //新建菜单
    newMenu() {
      this.dialogTableVisible = true;
      this.$refs.child.parentMsg(this.dialogTableVisible);
      this.$refs.child.clearTable();
      //数据给到编辑表单,清空表单，并开放系统编码填报
      this.$refs.child.getData({});
    },
    //获取菜单信息列表
    getMenuData(pageNo) {
      // console.log(val);
      this.loading = true;
      getPwdRuleList({
        current: pageNo || 1,
        size: 8,
      })
        .then((res) => {
          // console.log(res.data.data);
          this.tableData = res.data.data;
          this.currentPage = res.data.data.current;
          this.loading = false;
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    fatherMethod() {
      this.getMenuData();
    },
    //切换分页数据
    handleCurrentChange(data) {
      this.getMenuData(data);
    },
    //修改密码管理启用状态
    setPwdRuleUpdateStatus(data) {
      let status = null;
      if (data.isSelected == "true") {
        status = "false";
      } else {
        status = "true";
      }
      updatePwdRuleStatus({ id: data.id, status: status })
        .then((res) => {
          this.$message({
            message: "操作成功",
            type: "success",
          });
          this.getMenuData();
        })
        .catch((e) => {
          this.loading = false;
          console.log(e, "请求错误");
        });
    },
    searchTime() {},
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getMenuData();
  },
};
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 15px;

  .header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    .newMenu {
      width: 100px;
      height: 38px;
      padding: 10px;
      color: #fff;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
    }
    .search-box {
      width: 100%;
      padding-bottom: 10px;
      background: #fff;
      margin-bottom: 20px;
      padding-top: 20px;
      position: relative;
      > div {
        display: inline-flex;
        .label {
          width: 100px;
          line-height: 40px;
        }
      }
      .seach-btn {
        position: absolute;
        right: 0;
      }
    }
  }
  .pagination {
    float: right;
    margin-top: 30px;
  }
}
</style>
