<template>
  <div class="riskDy">
    <el-dialog
      title="氯化工艺单元中毒事故风险点"
      :visible.sync="dialogRisk"
      width="1460px"
      top="10px"
      :close-on-click-modal="false"
    >
      <div class="riskDialogBox">
        <div class="riskDialogTop">
          <div class="dialogL">固有风险-五高风险因子计量</div>
          <div class="dialogR">风险动态指标调控</div>
        </div>

        <div class="dialogCon">
          <div class="dialogConL">
            <div class="item">
              <div class="itemTit">高风险设备设施-坝体H1</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">高风险设物-氨气</div>
              <ul>
                <li>危险指数特征值(M)：1.7</li>
                <li>存量：320标方</li>
                <li>年使用量：280标方</li>
                <li>物理特性：有毒，可燃，易爆</li>
              </ul>
            </div>

            <div class="item">
              <div class="itemTit">高风险场所：生产装置区域</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>

            <div class="item">
              <div class="itemTit">高风险工艺：光电及光气化工艺</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>

            <div class="item">
              <div class="itemTit">高风险作业：受限空间作业</div>
              <ul>
                <li>危险指数特征值(h1)：1.7</li>
                <li>是否配备ESD：是</li>
                <li>是否配备SIS：否</li>
                <li>设计剧吐化学品：氯</li>
              </ul>
            </div>
          </div>
          <div class="dialogConC">
            <div class="centerL">
              <div class="center1">
                高风险设备设施
                <div style="color: #fe9b45; font-weight: bold">1.2</div>
              </div>
              <div class="center2">
                高风险作业K2
                <div style="color: #5c8dfb; font-weight: bold">1.2</div>
              </div>
              <div class="center3">
                <div style="color: #eec10e; font-weight: bold">5.0</div>
                高风险物品<br />（能量）M
              </div>
              <div class="center4">
                <div style="color: #00ffff; font-weight: bold">9.0</div>
                高风险场所E
              </div>
              <div class="center5">
                <div style="color: #5c8dfb; font-weight: bold">1.0</div>
                高风险工艺K1
              </div>
              <!-- <div class="center6">
                <div>131.1</div>
                初始风险H
              </div> -->
              <!-- <div class="center7">指标管控率G<span>1.4</span></div> -->
              <p class="clickCen">仓库爆炸事故风险点</p>
              <div class="clickStyle">
                <b>91.8</b>
              </div>

              <div class="center71">
                固有风险H
                <!-- <b class="num">91.8</b> -->
              </div>
            </div>

            <div class="centerC">
              <p>初始风险 <b>G</b></p>
              <div class="center10">
                <div class="num">131.1</div>
                <span>G</span>
              </div>
              <div class="center101">
                <div>管控频率</div>
                <b>1.43</b>
              </div>

              <div class="center102">
                <div>安全标准化评分</div>
                <b>70</b>
              </div>
            </div>
            <div class="centerR">
              <p>动态调控</p>
              <div class="center12">
                <span class="num">1.0</span>
                特殊时期
              </div>
              <div class="center13">
                <div class="num">2.0</div>
                <!-- 动态调控指标D -->
              </div>
              <div class="center14">
                <span class="num">0.0</span>
                自然环境
              </div>
              <div class="center15">
                <span class="num">1.0</span>
                物联网
              </div>
            </div>

            <div class="k3">
              <p>k3</p>
              <div class="con">
                <p>高危风险监</p>
                <p>测监控特征</p>
              </div>
              <b>2.0</b>
            </div>
            <div class="k4">
              <p>k4</p>
              <div class="con">
                <p>事故隐患</p>
              </div>
              <b>7</b>
            </div>

            <div class="major">
              <p>重大隐患</p>
              <div class="num">1.0</div>
            </div>

            <div class="centerBottom">
              <div class="centerBottomItem2">
                <p>重大风险<span>131.8</span></p>
                <div class="name">氢化工艺单元</div>
              </div>
            </div>
          </div>
          <div class="dialogConR">
            <div class="item">
              <div class="itemTit">高风险监测特征指标修改系数（K4）</div>
              <ul>
                <li>风险修正系数：1.0</li>
                <li>压力报警：12</li>
                <li>液位报警：--</li>
                <li>有毒气体：--</li>
                <li>可燃气体：--</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">事故隐患直指标修正指数（K4）</div>
              <ul>
                <li>风险修正系数：1.0</li>
                <li>压力报警：12</li>
                <li>一般隐患：00</li>
                <li>重大隐患：00</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">特殊时期指数修正</div>
              <ul>
                <li>风险修正系数：1.0</li>
                <li>时间：2021年1月24日</li>
                <li>湖北省第十三届人民代表大会</li>
                <li>第五次会议</li>
              </ul>
            </div>
            <div class="item">
              <div class="itemTit">高风险监测特征指标修改系数（K4）</div>
              <ul>
                <li>高危风险物联网指数修正</li>
                <li>风险修正系数：1.0</li>
                <li>时间：2021年1月24日</li>
                <li>类型：事故</li>
                <li>湖北省第十三届人民代表大会</li>
                <li>第五次会议</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// const echarts = require('echarts');
import {
  queryRiskCompanyInfo, //
} from "@/api/riskAssessment";
export default {
  //import引入的组件
  name: "riskDynamics",
  components: {},
  data() {
    return {
      loading: false,
      dialogRisk: false, //弹窗
      risktopData: {},    
    };
  },
  filters: {
    filterLevel(val) {
      var str = "";
      if (val == 1) {
        str = "较大风险";
      } else if (val == 2) {
        str = "一般风险";
      } else if (val == 3) {
        str = "重大风险";
      } else if (val == 4) {
        str = "低风险";
      }
      return str;
    },
  },
  //方法集合
  methods: {
    isShow(val){
      this.dialogRisk=true
    },
    // clickItem() {
    //   this.dialogRisk = true;
    // },
    getData(enterpriseId) {
      this.loading = true;
      queryRiskCompanyInfo({
        enterId: enterpriseId,
      }).then((res) => {
        if (res.data.status === 200) {
          this.loading = false;
          this.riskData = res.data.data.cimRiskUnitDTOList;
          this.risktopData = res.data.data;
        } else {
          // this.$message.error(res.data.msg);
        }
      });
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
/deep/ .el-dialog__body {
  padding: 0;
}
.riskDialogBox {
  background: url("../../../../../static/img/riskDialog_bg.png") no-repeat
    center #0c1534;
  width: 100%;
  padding: 0 20px;
  .riskDialogTop {
    display: flex;
    justify-content: space-between;
    font-size: 16px;
    color: #fff;
    padding: 10px 0 10px 0;
    .dialogL {
      background: url("../../../../../static/img/riskTopL.png") no-repeat bottom
        left;
      padding: 0 0 8px;
      width: 48%;
    }
    .dialogR {
      background: url("../../../../../static/img/riskTopR.png") no-repeat bottom
        right;
      padding: 0 0 8px;
      width: 48%;
      text-align: right;
    }
  }
  .dialogCon {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .dialogConL {
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitL.png") no-repeat
          bottom left;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #808089;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
    .dialogConC {
      background: url("../../../../../static/img/riskDialog.png") no-repeat
        center center;
      width: 960px;
      height: 535px;
      background-size: 100% 100%;
      position: relative;
    }
    .dialogConR {
      text-align: right;
      width: 250px;
      overflow-y: auto;
      height: 584px;
      overflow-x: hidden;
      .item {
        margin-bottom: 10px;
      }
      .itemTit {
        color: #fff;
        background: url("../../../../../static/img/riskTitR.png") no-repeat
          bottom right;
        width: 100%;
        font-size: 14px;
        margin-bottom: 10px;
      }
      ul,
      li {
        list-style: none;
        color: #808089;
        margin: 0;
        padding: 0;
        font-size: 12px;
      }
    }
  }
  .dialogConC {
  }
  .centerL {
    font-size: 12px;
    text-align: center;
    color: #fff;
    .center1 {
      //高风险设备设施
      position: absolute;
      top: 31px;
      left: 75px;
    }
    .center2 {
      // 高风险作业K2
      position: absolute;
      left: 218px;
      top: 31px;
    }
    .center3 {
      //高风险物品（能量）M
      position: absolute;
      left: 19px;
      top: 144px;
    }
    .center4 {
      //高风险场所E
      position: absolute;
      left: 96px;
      top: 239px;
    }
    .center5 {
      left: 212px;
      top: 239px;
      position: absolute;
    }
    .center6 {
      //初始风险H
      left: 284px;
      top: 147px;
      position: absolute;
    }
    .center7 {
      //指标管控率G
      position: absolute;
      left: 226px;
      top: 116px;
      transform: rotate(-56deg);
    }
    .center71 {
      //  固有风险H
      position: absolute;
      left: 167px;
      top: 184px;
      color: #03d2da;
      b {
        display: block;
        color: #fff;
      }
    }
    .clickCen {
      background: #03d2da;
      position: absolute;
      left: 113px;
      top: 89px;
      font-size: 16px;
      width: 158px;
      line-height: 16px;
      padding: 3px 0;
      color: #000;
    }
    .clickStyle {
      background: url("../../../../../static/img/img-只有一个数值.png") no-repeat
        center;
      position: absolute;
      width: 60px;
      background-size: 100% 100%;
      height: 60px;
      left: 161px;
      top: 119px;
      line-height: 60px;
      font-size: 16px;
      span{
        // display: inline-block;
        // width: 28px;
        // cursor: pointer;
      }
    }
  }

  .centerC {
    font-size: 12px;
    text-align: center;
    color: #fff;
    position: absolute;
    width: 240px;
    /* border: 1px solid red; */
    left: 347px;
    top: 5px;
    height: 242px;
    > p {
      padding: 21px 0 0 0;
    }
  }
  .center10 {
    position: absolute;
    /* border: 1px solid red; */
    width: 69px;
    left: 83px;
    top: 92px;
    /* height: 64px; */
    text-align: center;
    font-weight: bold;
  }
  .center101 {
    position: absolute;
    left: 27px;
    top: 176px;
    font-weight: bold;
    > div {
      color: rgba(255, 255, 255, 0.6);
      font-weight: normal;
    }
  }
  .center102 {
    position: absolute;
    top: 176px;
    left: 116px;
    font-weight: bold;
    > div {
      color: rgba(255, 255, 255, 0.6);
      font-weight: normal;
    }
  }
  .centerR {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 300px;
    /* border: 1px solid red; */
    height: 123px;
    right: 18px;
    top: 133px;
    color: #33bbff;
    > p {
      position: absolute;
      left: 122px;
      bottom: 11px;
      margin: 0;
    }
    .center12 {
      //特殊时期
      position: absolute;
      left: 178px;
      top: 16px;
      span {
        font-weight: bold;
      }
    }
    .center13 {
      position: absolute;
      left: 32px;
      top: 25px;
      width: 61px;
      height: 46px;
      /* border: 1px solid red; */
      line-height: 46px;
      font-weight: bold;
      color: #fff;
    }
    .center14 {
      //自然环境
      position: absolute;
      left: 178px;
      top: 41px;
      span {
        font-weight: bold;
      }
    }
    .center15 {
      position: absolute;
      left: 178px;
      top: 65px;
      span {
        font-weight: bold;
      }
    }
  }
  .k4 {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 146px;
    height: 90px;
    // border: 1px solid red;
    height: 72px;
    right: 18px;
    top: 44px;
    color: #33bbff;
    > p {
      text-align: right;
      padding: 5px 10px 0 0;
      font-weight: bold;
      margin: 0;
      line-height: 12px;
    }
    .con {
      line-height: 15px;
    }
    .con > p {
      margin: 0;
      color: rgba(51, 187, 255, 0.6);
    }
  }
  .k3 {
    font-size: 12px;
    text-align: center;
    position: absolute;
    width: 140px;
    height: 90px;
    /* border: 1px solid red; */
    height: 69px;
    right: 180px;
    top: 44px;
    color: #00ffff;
    > p {
      text-align: right;
      padding: 5px 10px 0 0;
      font-weight: bold;
      margin: 0;
      line-height: 12px;
    }
    .con {
      line-height: 15px;
    }
    .con > p {
      margin: 0;
      color: rgba(0, 255, 255, 0.6);
    }
  }
  .major {
    position: absolute;
    right: 87px;
    top: 375px;
    background: url("../../../../../static/img/重大隐患.png") no-repeat center;
    width: 150px;
    background-size: 100%;
    height: 123px;
    /* padding: 20px 0 0 0; */
    color: #ea6544;
    font-size: 12px;
    > p {
      text-align: center;
      margin-top: -10px;
      padding: 0 0 0 27px;
    }
    > div.num {
      width: 67px;
      margin: 0 0 0 59px;
      height: 61px;
      position: absolute;
      top: 30px;
      line-height: 61px;
      text-align: center;
      font-weight: bold;
    }
  }

  .centerBottomItem2 {
    position: absolute;
    bottom: 50px;
    left: 362px;
    text-align: center;
    color: #fff;
    width: 218px;
    .name {
      color: #ffdcc3;
    }
    > p {
      font-size: 22px;
      margin: 0px 0 5px 0;
      span {
        font-weight: bold;
        display: inline-block;
        margin-left: 5px;
      }
    }
  }
}

</style>