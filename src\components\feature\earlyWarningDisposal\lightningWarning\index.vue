<template>
  <div class="enterpriseManagement">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 雷电预警
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="seach-part">
      <div class="l">
        <el-cascader
          size="mini"
          placeholder="请选择行政区划"
          :options="district"
          v-model="params.districtCode"
          :props="{
            checkStrictly: true,
            value: 'distCode',
            label: 'distName',
            children: 'children',
            emitPath: false,
          }"
          clearable
          :show-all-levels="true"
        ></el-cascader>
        <el-select
          v-model="params.levelCode"
          size="mini"
          placeholder="请选择最高预警等级"
          clearable
          multiple
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-input
          v-model.trim="params.companyName"
          v-if="this.$store.state.login.user.user_type != 'ent'"
          size="mini"
          placeholder="请输入企业名称"
          class="input"
          clearable
          style="width: 150px"
        ></el-input>
        <el-date-picker
          v-model="dateRange"
          size="mini"
          type="daterange"
          
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="searchTime"
          clearable
        >
        </el-date-picker>
        <el-button type="primary" size="mini" @click="getTableData"
          >查询</el-button
        >
        <CA-button type="primary" size="mini" plain @click="exportExcel"
          >导出</CA-button
        >
      </div>
    </div>
    <div class="table-main">
      <div class="table-top">
        <h2>雷电预警</h2>
        <!-- <el-button type="primary" size="mini" @click="clickPath"
            >查看分析报告</el-button
          > -->
      </div>
      <div class="table">
        <el-table
          :data="tableData"
          v-loading="loading"
          style="width: 100%"
          :header-cell-style="{ background: '#F1F6FF', color: '#333' }"
          border
          ref="multipleTable"
          @selection-change="handleSelectionChange"
          :default-sort="{ prop: 'date', order: 'descending' }"
          @select="select"
          @select-all="select"
        >
          <el-table-column type="selection" width="50" align="center">
          </el-table-column>
          <el-table-column type="index" label="序号" width="50" align="center">
          </el-table-column>
          <el-table-column
            prop="companyName"
            label="单位名称"
            align="center"
            :show-overflow-tooltip="true"
          >
          </el-table-column>
          <el-table-column
            :label="isShowDist == true ? '行政区划' : '归属园区'"
            width="100"
            align="center"
          >
            <template slot-scope="{ row, column, $index, store }">
              <span v-if="isShowDist == true">{{ row.districtName }}</span>
              <span v-else>{{ park.districtName }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="设备名称"
            prop="equipName"
            align="center"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="{ row, column, $index, store }">
              <span @click="goEnt(row)" style="color: #3977ea; cursor: pointer">
                {{ row.equipName }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            label="设备经度"
            width="100"
            prop="longitude"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="设备纬度"
            width="100"
            prop="latitude"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="预警时间开始"
            prop="beginTime"
            width="160"
            align="center"
          >
          </el-table-column>
          <el-table-column
            label="预警结束时间"
            prop="endTime"
            width="160"
            align="center"
          >
          </el-table-column>
          <el-table-column label="预警等级" width="100" align="center">
            <template slot-scope="{ row, column, $index, store }">
              <div>
                {{ row.levelCode|levelfilter }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center">
            <template slot-scope="{ row, column, $index, store }">
              <div>
                <el-button type="text" @click="handleView(row)">查看</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="pagination">
        <el-pagination
          @current-change="handleCurrentChange"
          :current-page.sync="params.page"
          :page-size="params.pageSize"
          layout="total, prev, pager, next"
          background
          :total="total"
        >
        </el-pagination>
      </div>
    </div>
    <!-- 设备详情 -->
    <enterpriseDialog
      v-if="detailVisible"
      :ent-obj="warningInfo"
      :show="detailVisible"
      @closeBoolean="closeBoolean"
    ></enterpriseDialog>
    <!-- 雷电预警信息 -->
    <warningDialog
      v-if="warningVisible"
      :ent-obj="warningInfo"
      :show="warningVisible"
      @closeBoolean="closeWaning"
    ></warningDialog>
  </div>
</template>
<script>
import { getLightWarningData,lightningWarningExport } from "@/api/earlyWarningDisposal";
import { getEnt } from "@/api/dailySafety";
import enterpriseDialog from "./enterpriseDialog.vue";
import warningDialog from "./warningDialog.vue";
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
export default {
  components: {
    enterpriseDialog,
    warningDialog,
  },
  data() {
    return {
      loading: false,
      tableData: [],
      total: 0,
      options: [
        {
          value: "1",
          label: "红色预警",
        },
        {
          value: "2",
          label: "橙色预警",
        },
        {
          value: "3",
          label: "黄色预警",
        },
      ],
      params: {
        page: 1,
        pageSize: 10,
        districtCode: this.$store.state.login.userDistCode,
        startTime: "",
        endTime: "",
        companyName: "",
        levelCode: [],
      },
      district: this.$store.state.controler.district,
      dateRange: [
        new Date(new Date().toLocaleDateString()).getTime() -
          (720 * 60 * 60 * 1000 - 1),
        new Date(new Date().toLocaleDateString()).getTime() + 86399900
      ],
      warningInfo: {},
      detailVisible: false,
      warningVisible: false,
    };
  },
  filters: {
    levelfilter(val) {
      if (val == 1) {
        return "红色预警";
      } else if (val == 2) {
        return "橙色预警";
      } else if (val == 3) {
        return "黄色预警";
      }
    },
  },
  methods: {
    clickPath() {
      this.$router.push({ path: "/riskAssessment/monitorWarnReport" });
    },
    // 设备信息
    goEnt(row) {
      this.warningInfo = row;
      this.detailVisible = true;
    },
    // 查看
    handleView(row) {
      this.warningVisible = true;
      this.warningInfo = row;
    },
    closeWaning() {
      this.warningVisible = false;
      this.warningInfo = {};
    },
    closeBoolean() {
      this.detailVisible = false;
      this.warningInfo = {};
    },
    searchTime(value) {
      this.dateRange = value;
      if (value) {
        this.params.startTime = new Date(this.dateRange[0]).Format(
          "yy-MM-dd hh:mm:ss"
        );
        this.params.endTime = new Date(
          new Date(this.dateRange[1].getTime() + 86399900)
        ).Format("yy-MM-dd hh:mm:ss");
      } else {
        this.params.startTime = "";
        this.params.endTime = "";
      }
    },
    getTableData() {
      const params = {
        ...this.params,
        districtCode:this.params.districtCode.split(',')
      };
      getLightWarningData(params)
        .then((res) => {
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getData() {
      if (this.$store.state.login.user.user_type == "ent") {
        this.showArea = false;
        getEnt({}).then((res) => {
          if (res.data.code == 0) {
            // debugger;
            // this.enterpid= res.data.data.enterpId;
            this.getDataes([res.data.data.enterpId]);
          }
        });
      } else {
        this.getDataes(null);
      }
    },
    async getDataes(id) {
      // return;
      // this.loading = true;
      // this.params.startTime = this.date
      //   ? new Date(this.date[0]).Format("yy-MM-dd") + " 00:00:00"
      //   : "";
      // this.params.endTime = this.date
      //   ? new Date(this.date[1]).Format("yy-MM-dd") + " 23:59:59"
      //   : "";

      await getLightWarningData(this.params)
        .then((res) => {
          this.tableData = res.data.data.records;
          this.total = res.data.data.total;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 导出
    exportExcel() {
      const params = {
        ...this.params,
        districtCode:this.params.districtCode.split(',')
      };
      lightningWarningExport(params).then((response) => {
        // 处理返回的文件流
        // console.log(response);
        if (response.status == 200) {
          this.$message({
            message: "导出成功",
            type: "success",
          });
        } else {
          this.$message.error("导出失败");
        }
        const blob = new Blob([response.data], { type: "application/xls" });
        //获取今天的时间
        let day = new Date();
        day.setTime(day.getTime());
        let timestamp =
          day.getFullYear() + "-" + (day.getMonth() + 1) + "-" + day.getDate();
        const filename = "雷电预警列表" + timestamp + ".xls";
        //下载文件
        const link = document.createElement("a");
        link.href = URL.createObjectURL(blob);
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        window.setTimeout(function () {
          URL.revokeObjectURL(blob);
          document.body.removeChild(link);
        }, 0);
      });
    },
    select(selection, row) {
      this.selection = [];
      for (let i = 0; i < selection.length; i++) {
        // debugger
        this.selection[i] = selection[i].id;
      }
    },
    handleSelectionChange(val) {
      console.log(val);
    },
    handleCurrentChange(val) {
      this.params.page = val;
      this.getData();
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.getTableData();
  },
  computed: {
    ...mapStateLogin({
      isXiaZuan: (state) => state.isXiaZuan,
      isShowDist: (state) => state.isShowDist,
      user: (state) => state.user,
    }),
    ...mapStateControler({
      vuexDistrict: (state) => state.district,
    }),
  },
  watch: {
    vuexDistrict: {
      handler(newVal, oldVal) {
        this.district = newVal;
      },
    },
  },
};
</script>
<style lang="scss" scoped>
.enterpriseManagement {
  .icon {
    color: #6f81b5;
    font-size: 15px;
  }
  .header {
    background-color: #fff;
    overflow: hidden;
    margin-bottom: 5px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .breadcrumb {
      margin-bottom: 10px;
      cursor: pointer;
      color: #4f5b69;
      //   border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    }
  }
  .seach-part {
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    margin-bottom: 10px;
    margin-top: 20px;
    border-bottom: 1px rgba(198, 207, 217, 0.33) solid;
    .l {
      display: flex;
      justify-content: flex-start;
      > * {
        margin-right: 20px;
      }
    }
  }
  .table-main {
    background: #fff;
    .table-top {
      display: flex;
      justify-content: space-between;
      // padding: 10px 0;
      margin-bottom: 10px;
      align-items: center;

      h2 {
        font-size: 18px;
        line-height: 32px;
        margin-bottom: 0;
      }
    }
    .pagination {
      margin-top: 30px;
      padding-bottom: 20px;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
<style>
body .el-table th.gutter {
  display: table-cell !important;
}
body .el-radio-group {
  margin-bottom: 15px;
}
</style>
<style lang="scss" scoped>
.disposalSituation {
  .textarea {
    width: 100%;
    resize: none;
  }
  .textLength {
    font-size: 12px;
    float: right;
    color: #777;
    margin-top: 3px;
  }
  overflow: auto;
  color: #000;
  ul {
    padding-inline-start: 0px;
  }
  .div1 {
    overflow: auto;
    .title {
      font-weight: 600;
      margin-bottom: 10px;
    }
    .table {
      .submit {
        float: right;
        margin-top: 15px;
        margin-left: 20px;
      }
      .container {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        text-align: center;
        .bottom {
          // border-top: 1px solid rgb(182, 182, 182);
          border-bottom: 1px solid rgb(231, 231, 231);
        }
        li {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          align-items: center;
          border-top: 1px solid rgb(231, 231, 231);
          // border-right: 1px solid rgb(231, 231, 231);
          border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;

          .red {
            color: red;
          }
          .l {
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            height: 100%;
            width: 50%;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 50%;
            padding: 5px 10px;
          }
        }
        .lang {
          list-style-type: none;
          width: 100%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          // border-left: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          text-align: left;
          .red {
            color: red;
          }
          .l {
            width: 20%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }

          .r {
            width: 80%;
            padding: 5px 10px;
            flex-wrap: wrap;
            text-align: left;
          }
        }
        .liLine {
          list-style-type: none;
          width: 33.3%;
          display: flex;
          border-top: 1px solid rgb(231, 231, 231);
          border-right: 1px solid rgb(231, 231, 231);
          overflow: hidden;
          min-height: 40px;
          .red {
            color: red;
          }
          .l {
            width: 50%;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 5px 10px;
            color: #60627a;
            background: rgb(242, 246, 255);
          }
          .r {
            padding: 5px 10px;
            width: 50%;
          }
        }
      }
    }
  }
}
</style>
