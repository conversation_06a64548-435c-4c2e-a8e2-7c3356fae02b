<template>
  <div class="entManagement">
    <component
      :is="$route.name === 'entManagement' ? 'entManagement' : $route.name"
    ></component>
  </div>
</template>

<script>
// import entManagement from "./entAndGov.vue";
// import mailList from "./mailList";
// import mergencyResources from "./mergencyResources";

export default {
  //import引入的组件
  name: "realtimeMonitor",
  components: {
    entManagement: () => import("./entAndGov.vue"),
    // mailList: () => import("./mailList"),
    // mergencyResources: () => import("./mergencyResources"),
  },
};
</script>
<style lang="scss">
.chart-icon {
  font-size: 12px;
  margin-right: 3px;
}
</style>
