import Vue from "vue";
import router from "./router/h5";
import store from "./store";

import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
Vue.use(ElementUI);
import VForm from "vform-builds"; //引入VForm库
import "vform-builds/dist/VFormDesigner.css"; //引入VForm样式
// 让VForm可以访问到全局函数
import downloadFuc, { Attachmentdownload } from "@/api/download/download.js"; // 假设这些函数位于此路径
Vue.prototype.$Attachmentdownload = Attachmentdownload;
Vue.prototype.$downloadFuc = downloadFuc;

// 初始化VForm时，告诉它如何找到这些函数
Vue.use(VForm, {
  globalFunctions: {
    Attachmentdownload: () => Vue.prototype.$Attachmentdownload,
    downloadFuc: () => Vue.prototype.$downloadFuc,
  },
});

Vue.config.productionTip = false;

// 创建轻量化的H5应用实例
new Vue({
  router,
  store,
  render: (h) => h("router-view"),
}).$mount("#app");
