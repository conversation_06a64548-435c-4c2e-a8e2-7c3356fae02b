<template>
  <div class="upload-file">
    <el-upload
      :action="uploadFileUrl"
      ref="upload"
      :before-upload="handleBeforeUpload"
      v-if="pageType != 'show' && !disabled"
      :file-list="fileList"
      :data="paramsData"
      :limit="1"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="false"
      :headers="headers"
      :disabled="disabled"
      class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button size="mini" type="primary" :disabled="disabled"
        >选取文件</el-button
      >
      <!-- 上传提示 -->
      <div v-if="showTip" slot="tip" class="el-upload__tip">
        请上传
        <template v-if="fileSize">
          大小不超过
          <b style="color: #f56c6c">{{ fileSize }}MB</b>
        </template>
        <template v-if="fileType">
          格式为
          <b style="color: #f56c6c">{{ fileType.join("/") }}</b>
        </template>
        的文件
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <transition-group
      class="upload-file-list el-upload-list el-upload-list--text"
      name="el-fade-in-linear"
      tag="ul"
    >
      <li
        v-for="(file, index) in list"
        :key="file.uid"
        class="el-upload-list__item ele-upload-list__item-content"
        :class="{ noBorder: pageType == 'show' }"
      >
        <!-- <el-link :href="file.url" :underline="false" target="_blank" class="nameBox">
        <span class="el-icon-document">{{ file.name }}</span>
        </el-link>-->

        <div class="nameBox" @click="downClick()">{{ file.name }}</div>
        <!-- </div> -->

        <div class="ele-upload-list__item-content-action">
          <el-link
            v-if="!disabled && pageType != 'show'"
            :underline="false"
            type="danger"
            @click="handleDelete(index)"
            >删除</el-link
          >

          <el-link
            v-if="pageType == 'show'"
            :underline="false"
            :href="file.url"
            target="_blank"
            >预览</el-link
          >
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script>
import { getToken } from "@/utils/auth";
// import { queryAttachmentInfoById, downloadUrl } from '@/api/notice/notice'
import downloadFuc from "@/api/download/download.js";
export default {
  name: "CAFileUpload",
  props: {
    // 值
    value: [String, Object, Array],
    // 大小限制(MB)
    fileSize: {
      type: Number,
      default: 5,
    },
    // 文件类型, 例如['png', 'jpg', 'jpeg']
    fileType: {
      type: Array,
      default: () => ["doc", "xls", "ppt", "txt", "pdf", "png", "jpg", "jpeg"],
    },
    // 是否显示提示
    isShowTip: {
      type: Boolean,
      default: true,
    },
    // type
    paramsData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    pageType: {
      // 组件用途类型， show: 只是展示
      type: String,
      default: "",
    },
  },
  data() {
    return {
      uploadFileUrl:
        "http://***********:8868/gapi/gemp-file/api/attachment/upload/v1", // 上传的图片服务器地址
      headers: {
        Authorization: store.state.login.token,
        // token:store.state.login.token
      },
      fileList: [],
      list: [],
    };
  },
  computed: {
    // 是否显示提示
    showTip() {
      return this.isShowTip && (this.fileType || this.fileSize);
    },
  },
  watch: {
    value() {
      if (this.value) {
        this.queryAttachmentInfoByIdFun(this.value);
      } else {
        this.fileList = [];
        this.list = [];
      }
    },
  },
  created() {
    this.queryAttachmentInfoByIdFun(this.value);
  },
  methods: {
    queryAttachmentInfoByIdFun(id) {
      if (!id) {
        this.fileList = [];
        this.list = [];
        return false;
      }
      queryAttachmentInfoById({ attachmentId: id })
        .then((res) => {
          let temp = 1;
          const _value = res.geAttachmentInfo.attachmentaddr;
          // 首先将值转为数组
          const list = Array.isArray(_value) ? _value : [_value];
          const _list = [];
          // 然后将数组转为对象数组
          list.forEach((item) => {
            const _item = {
              name: res.geAttachmentInfo.nameofattachment,
              url: item,
              uid: item.uid || new Date().getTime() + temp++,
            };
            _list.push(_item);
          });
          this.fileList = _list;
          this.list = _list;
          console.log("this.list==", this.list);
        })
        .catch((err) => {
          this.fileList = [];
          this.list = [];
        });
    },
    // downClick() {
    //   let id = this.value;
    //   // // const obj = {
    //   // //   id: valueId
    //   // // };
    //   debugger;
    //   downloadUrl({ id: id }).then(res => {
    //     if (res) {
    //       downloadFuc(res);
    //     }
    //   });
    // },

    downClick() {
      const id = this.value;
      window.location.href =
        process.env.VUE_APP_BASE_API + "/download/v1?id=" + id;
    },
    // exportListApi() {
    //   let id = this.value;
    //   // downloadUrl({ id: id }).then(res => {
    //   //   // this.downloadAtm(res);
    //   //   this.$message({
    //   //     type: "success",
    //   //     message: "下载成功!"
    //   //   });
    //   // });
    //   window.location.href = baseURL + "/download/v1?id=" + id;
    // },
    // downloadAtm(fileName) {
    //   window.location.href = baseURL + "/common/download?id=" + id;
    // },

    // 上传前校检格式和大小
    handleBeforeUpload(file) {
      // 校检文件类型
      if (this.fileType) {
        let fileExtension = "";
        if (file.name.lastIndexOf(".") > -1) {
          fileExtension = file.name.slice(file.name.lastIndexOf(".") + 1);
        }
        const isTypeOk = this.fileType.some((type) => {
          if (file.type.indexOf(type) > -1) return true;
          if (fileExtension && fileExtension.indexOf(type) > -1) return true;
          return false;
        });
        if (!isTypeOk) {
          this.$message.error(
            `文件格式不正确, 请上传${this.fileType.join("/")}格式文件!`
          );
          return false;
        }
      }
      // 校检文件大小
      if (this.fileSize) {
        const isLt = file.size / 1024 / 1024 < this.fileSize;
        if (!isLt) {
          this.$message.error(`上传文件大小不能超过 ${this.fileSize} MB!`);
          return false;
        }
      }
      return true;
    },
    // 文件个数超出
    handleExceed() {
      this.$message.error(`只允许上传单个文件`);
    },
    // 上传失败
    handleUploadError(err) {
      this.$message.error("上传失败, 请重试");
    },
    // 上传成功回调
    handleUploadSuccess(res, file) {
      this.$message.success("上传成功");
      this.$emit("input", res.attachmentId);
    },
    // 删除文件
    handleDelete(index) {
      this.fileList.splice(index, 1);
      this.$emit("input", "");
    },
    // 获取文件名称
    getFileName(name) {
      if (name.lastIndexOf("/") > -1) {
        return name.slice(name.lastIndexOf("/") + 1).toLowerCase();
      } else {
        return "";
      }
    },
  },
};
</script>

<style scoped lang="scss">
.nameBox {
  width: 90%;
  display: flex;
  justify-content: flex-start;
  cursor: pointer;
}
.upload-file-uploader {
  margin-bottom: 5px;
}
.upload-file-list .el-upload-list__item {
  border: 1px solid #e4e7ed;
  line-height: 2;
  margin-bottom: 10px;
  position: relative;
  padding: 0 10px;
}
.upload-file-list .ele-upload-list__item-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: inherit;
}
.noBorder {
  border: none !important;
}
.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
