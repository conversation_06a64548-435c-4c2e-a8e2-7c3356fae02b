<template>
  <div class="videoQualityMonitoringAlarm">
    <div class="header">
      <div class="breadcrumb">
        <a-breadcrumb separator="–">
          <a-breadcrumb-item>
            <span class="icon-box">
              <a-icon type="home" theme="filled" class="icon" /> 视频质量监测报警
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
      </div>
    </div>
    <div class="operation">
      <span class="label">区域名称：</span>
      <el-cascader size="small" placeholder="请选择行政区划" :options="district" v-model="searchParams.distCode"
        :props="distOptions" clearable :show-all-levels="true" style="width: 220px"></el-cascader>
    </div>
    <div class="table">
      <el-table :data="tableData" v-loading="loading" :header-cell-style="headerCellStyle" border style="width: 100%">
        <el-table-column label="序号" type="index" width="60"></el-table-column>
        <el-table-column label="行政区划" prop="districtName" width="180px"></el-table-column>
        <el-table-column label="视频问题企业数" prop="entNumber">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleSend(row.id)">{{ row.entNumber }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="问题监控数" prop="warnNumber"></el-table-column>
        <el-table-column label="信号丢失" prop="signalNumber" width="120px"></el-table-column>
        <el-table-column label="场景改变" prop="senceNumber" width="120px"></el-table-column>
        <el-table-column label="视频遮挡" prop="videoBlock" width="120px"></el-table-column>
        <el-table-column label="视频抖动" prop="videoShake" width="120px"></el-table-column>
        <el-table-column label="操作" width="160px">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleSend(row.id)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination :current-page.sync="searchParams.nowPage" :page-size="searchParams.pageSize" :total="total"
        background layout="total, prev, pager, next" @current-change="handleCurrentChange"></el-pagination>
    </div>
    <videoMonitoringDialog v-if="dialogVisible" :visible="dialogVisible" :title="title" :id="distId"
      @close="closeDialog" />
  </div>
</template>
<script>
import { createNamespacedHelpers } from "vuex";
const { mapState: mapStateLogin } = createNamespacedHelpers("login");
const { mapState: mapStateControler } = createNamespacedHelpers("controler");
import videoMonitoringDialog from "./videoMonitoringDialog.vue";
export default {
  name: "videoQualityMonitoringAlarm",
  components: { videoMonitoringDialog },
  data() {
    return {
      loading: false,
      headerCellStyle: {
        background: '#F1F6FF', color: '#333', textAlign: 'center',
        backgroundColor: 'rgb(242, 246, 255)'
      },
      distOptions: {
        checkStrictly: true,
        value: 'distCode',
        label: 'distName',
        children: 'children',
        emitPath: false,
      },
      searchParams: {
        distCode: this.$store.state.login.userDistCode,
        nowPage: 1,
        pageSize: 10,
      },
      tableData: [
        { id: 1, districtName: '武汉市', districtCode: '110000', entNumber: 20, warnNumber: 10, signalNumber: 5, senceNumber: 5, videoBlock: 5, videoShake: 5 }
      ],
      total: 0,
      dialogVisible: false,
      title: "",
      distId: "武汉市视频问题企业",
    };
  },
  computed: {
    ...mapStateLogin({
      userDistCode: (state) => state.userDistCode,
    }),
    ...mapStateControler({
      district: (state) => state.district,
    }),
  },
  methods: {
    handleCurrentChange(val) {
      this.searchParams.nowPage = val;
      this.getData();
    },
    getData() {
    },
    handleSend(id) {
      this.distId = id;
      this.dialogVisible = true;
      this.title = '武汉市视频问题企业';
    },
    closeDialog() {
      this.dialogVisible = false;
      this.title = "";
      this.distId = "";

    }
  }
};
</script>
<style lang="scss" scoped>
.header {
  background-color: #fff;
  overflow: hidden;
  margin-bottom: 5px;
  border-bottom: 1px rgba(198, 207, 217, 0.33) solid;

  .breadcrumb {
    margin-bottom: 10px;
    cursor: pointer;
    color: #4f5b69;
  }
}

.operation {
  display: flex;
  align-items: center;
  padding: 10px 0;
  margin: 10px 0;

  .label {
    margin-left: 20px;
  }

}

.pagination {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}
</style>
