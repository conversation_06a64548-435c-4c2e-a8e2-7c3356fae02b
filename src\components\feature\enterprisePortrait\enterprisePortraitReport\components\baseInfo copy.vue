<template>
  <div class="table">
    <ul class="container">
      <li>
        <div class="l">企业名称</div>
        <div class="r">{{ enterprise.enterpName }}</div>
      </li>
      <li class="">
        <div class="l">企业类型大类</div>
        <!-- <div class="r"
                 v-if="enterprise.enterpriseType == '01'">生产</div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '02'">
              经营
            </div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '03'">
              使用
            </div>
            <div class="r"
                 v-else-if="enterprise.enterpriseType == '04'">
              第一类非药品易制毒
            </div> -->
        <div class="r">{{ enterprise.enterpriseTypeName }}</div>
      </li>
      <li class="">
        <div class="l">企业类型小类</div>
        <!-- <div class="r">{{ managementType(enterprise.managementType) }}</div> -->
        <div class="r">{{ enterprise.managementType }}</div>
      </li>
      <li>
        <div class="l">企业编码</div>
        <div class="r">{{ enterprise.enterpId }}</div>
      </li>
      <li>
        <div class="l">统一社会信用代码</div>
        <div class="r">{{ enterprise.entcreditCode }}</div>
      </li>
      <li v-if="enterprise.enterpriseType == '01'">
        <div class="l">安全生产许可证</div>
        <div class="r">{{ enterprise.secLicenseNum }}</div>
      </li>
      <li v-if="enterprise.enterpriseType == '02'">
        <div class="l">危险化学品经营许可证</div>
        <div class="r">{{ enterprise.msdslicensenum }}</div>
      </li>
      <li v-if="enterprise.enterpriseType == '03'">
        <div class="l">危险化学品安全使用许可证</div>
        <div class="r">{{ enterprise.msdsseclicensenum }}</div>
      </li>
      <!-- <li v-if="enterprise.enterpriseType == '04'">
            <div class="l">危险化学品安全使用许可证</div>
            <div class="r">{{ enterprise.secLicenseNum }}</div>
          </li> -->
      <li v-if="enterprise.enterpriseType == null">
        <div class="l">许可证</div>
        <div class="r"></div>
      </li>
      <li>
        <div class="l">许可证有效开始日期</div>
        <div class="r">{{ enterprise.secLicenseNumStartDate }}</div>
      </li>
      <li>
        <div class="l">许可证有效结束日期</div>
        <div class="r">{{ enterprise.secLicenseNumEndDate }}</div>
      </li>
      <li>
        <div class="l">许可范围</div>
        <div class="r">
          <el-popover
            placement="left-start"
            width="250"
            trigger="hover"
            :content="enterprise.scopeSafety"
          >
            <div
              slot="reference"
              style="
                width: 90%;
                margin: 0 auto;
                cursor: default;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              "
            >
              {{ enterprise.scopeSafety }}
            </div>
          </el-popover>
        </div>
      </li>
      <li>
        <div class="l">所属行业门类</div>
        <div class="r">{{ enterprise.industryCategory }}</div>
      </li>
      <li>
        <div class="l">所属行业大类</div>
        <div class="r">{{ enterprise.industryClass }}</div>
      </li>
      <li>
        <div class="l">经济类型代码（大类）</div>
        <div class="r">{{ enterprise.economyTypeCode }}</div>
      </li>
      <li>
        <div class="l">经济类型代码（小类）</div>
        <div class="r">{{ enterprise.economyTypeCode2 }}</div>
      </li>
      <li class="">
        <div class="l">行政区划</div>
        <div class="r">{{ enterprise.districtName }}</div>
      </li>
      <li>
        <div class="l">工商注册地址</div>
        <div class="r">{{ enterprise.businessAddress }}</div>
      </li>
      <li>
        <div class="l">生产场所地址</div>
        <div class="r">{{ enterprise.bornAddress }}</div>
      </li>
      <li>
        <div class="l">职工总人数</div>
        <div class="r">{{ enterprise.employmentNum }}</div>
      </li>
      <li>
        <div class="l">占地面积(单位：平方米)</div>
        <div class="r">{{ enterprise.area }}</div>
      </li>
      <li>
        <div class="l">成立日期</div>
        <div class="r">{{ enterprise.regDate }}</div>
      </li>
      <li>
        <div class="l">是否在化工园区</div>
        <div class="r" v-if="enterprise.inPark != null">
          {{ enterprise.inPark == 1 ? "是" : "否" }}
        </div>
        <div class="r" v-else></div>
      </li>
      <li>
        <div class="l">所属化工园区区名称</div>
        <div class="r">{{ enterprise.parkName }}</div>
      </li>
      <li>
        <div class="l">企业经营状态</div>
        <div class="r" v-if="enterprise.businessType != null">
          {{ enterprise.businessType == 0 ? "正常" : "长期停产" }}
        </div>
        <div class="r" v-else></div>
      </li>
      <li>
        <div class="l">企业负责人</div>
        <div class="r">{{ enterprise.respper }}</div>
      </li>
      <li>
        <div class="l">企业负责人手机</div>
        <div class="r">{{ enterprise.respTel }}</div>
      </li>
      <li class="bottom">
        <div class="l">安全负责人</div>
        <div class="r">{{ enterprise.principal }}</div>
      </li>
      <li class="bottom">
        <div class="l">安全负责人手机</div>
        <div class="r">{{ enterprise.principalPhone }}</div>
      </li>
      <li class="bottom">
        <div class="l">安全值班电话</div>
        <div class="r">{{ enterprise.safeTel }}</div>
      </li>
    </ul>
    <div class="flow-box">
      <div class="step-box">
        <div class="step-item">
          <div class="tag-box">生成报告</div>
          <div class="time-box">2024-12-13 19:12:59</div>
          <div class="des">系统生成</div>
        </div>
        <div class="step-item">
          <div class="tag-box">报告下发</div>
          <div class="time-box">2024-12-13 19:12:59</div>
          <div class="des">下发操作：应急处王局长</div>
        </div>
        <div class="step-item">
          <div class="tag-box">报告查看</div>
          <div class="time-box">2024-12-13 19:12:59</div>
          <div class="des">查看:企业端李三金</div>
        </div>
      </div>
      <div class="report-btn">提醒查看</div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      enterprise: {
        enterpName: "武汉有机实业有限公司",
        entcreditCode: "914201001777012169",
        enterpId: "420110001",
        secLicenseNum: "(鄂)WH安许证[2022]延0922",
        secLicenseNumStartDate: "2022-10-27",
        secLicenseNumEndDate: "2025-10-26",
        scopeSafety: null,
        businessAddress: "武汉化学工业区化工二路1号",
        bornAddress: null,
        industryCategory: "制造业",
        industryClass: "化学原料和化学制品制造业",
        economyTypeCode: "国外投资",
        economyTypeCode2: "国外投资股份有限（公司）",
        businessType: "0",
        regDate: "1990-01-12",
        inPark: "1",
        parkName: "武汉化工园区",
        respper: "邹晓虹",
        respTel: null,
        principal: "范进锋",
        principalPhone: null,
        safeTel: "027-83429800",
        employmentNum: "545",
        area: 56895.67,
        entShort: null,
        attachmentEntcreditcode: null,
        entcreditcodEstartDATE: null,
        entcreditcodeEndDATE: null,
        attachmentLicense: null,
        isInvolvedChemicals: null,
        isInvolvedDangercraft: null,
        isConstituteDanger: null,
        legalrepper: "邹晓虹",
        legalreppTel: null,
        postCode: null,
        districtName: "武汉市青山区",
        districtCode: "420107",
        scalecode: "2",
        longitude: 114.545838,
        latitude: 30.616179,
        webSite: "http://www.chinaorganic.com/",
        responsiblePhone: "***********",
        enterProPeo: null,
        isPersonEdu: null,
        isControllerEdu: null,
        safetyResponsiblePhone: "***********",
        secuOffiPro: null,
        businessLicense: "苯甲腈10000吨/年、苯600吨/年、树脂30000吨/年**",
        safeManageNum: 0,
        safeEngineerNum: 0,
        majorHazardNum: 0,
        explosiveOperationNum: 0,
        explosiveOperationEduNum: 0,
        peopleOperation: 104,
        peopleToxic: 0,
        peopleHazard: 23,
        standardLevel: "2",
        acciSiuation: null,
        surroundinfoCode: null,
        grossProductNum: null,
        companyProfile: null,
        isLicense: null,
        isChemicalReaction: null,
        enterpriseType: "01",
        enterpriseTypeName: "生产",
        managementType: null,
        msdslicensenum: null,
        majorHazardEduNum: 0,
        msdsseclicensenum: null,
      },
    };
  },
};
</script>

<style lang="scss" scoped>
.title {
  width: 100%;
  font-weight: 600;
  margin-bottom: 10px;
  font-size: 18px;
  display: flex;
  justify-content: space-between;
}
.table {
  .container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    text-align: center;
    .bottom {
      border-bottom: 1px solid rgb(231, 231, 231);
    }
    li {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      // border-right: 1px solid rgb(231, 231, 231);
      border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 48%;
        padding: 1%;
        word-break: break-all;
      }
    }
    li:nth-of-type(3n + 0) {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      align-items: center;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        min-height: 40px;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        color: #60627a;
        background: rgb(242, 246, 255);
      }

      .r {
        width: 50%;
        word-break: break-all;
      }
    }
    .lang {
      list-style-type: none;
      width: 66.6%;
      display: flex;
      border-top: 1px solid #eaedf2;
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 24.9%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 5px 10px;
        background-color: rgb(242, 242, 242);
      }
      .r {
        width: 73.3%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: left;
        padding: 0px 10px;
        word-break: break-all;
      }
    }
    .liLine {
      list-style-type: none;
      width: 33.3%;
      display: flex;
      border-top: 1px solid rgb(231, 231, 231);
      border-right: 1px solid rgb(231, 231, 231);
      // border-left: 1px solid rgb(231, 231, 231);
      overflow: hidden;
      min-height: 40px;
      .red {
        color: red;
      }
      .l {
        width: 50%;
        background: rgb(242, 246, 255);
      }
      .r {
        width: 50%;
        word-break: break-all;
      }
    }
  }
}
.flow-box {
  padding-top: 10px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  padding-right: 30px;
  .step-box {
    flex: 1;
    display: flex;
    justify-content: center;
    .step-item {
      width: 240px;
      text-align: center;
      .tag-box {
        position: relative;
        width: 80px;
        height: 30px;
        line-height: 30px;
        border: #2674cd 1px solid;
        margin: 0 auto;
        border-radius: 5px;
        &::after {
          content: "";
          position: absolute;
          width: 160px;
          height: 1px;
          left: 80px;
          top: 50%;
          background: #000;
        }
      }
      &:last-child {
        .tag-box {
          &::after {
            content: "";
            position: absolute;
            width: 120px;
            height: 1px;
            left: 80px;
            top: 50%;
            background: #000;
            display: none;
          }
        }
      }
    }
  }
  .report-btn {
    padding: 10px 18px;
    background: #2674cd;
    border-radius: 3px;
    color: #fff;
  }
}
</style>
