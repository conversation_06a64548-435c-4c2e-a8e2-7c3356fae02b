<template>
  <div class="notification">
    <el-dialog
      :title="title"
      :visible.sync="showsImg"
      @close="closeBoolean()"
      top="10vh"
      :close-on-click-modal="false"
    >
      <div class="div1"           
      >
          <el-image
            :src="img"
            :preview-src-list="[img]"
            fit="scale-down"
            alt="点击图片查看大图"
           @error="errImg"
          @load="loadImg"
          :z-index="3000"

          >
            <div slot="error" class="image-slot">
              <a-empty description="图片加载失败" /></div
          ></el-image>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  //import引入的组件
  name: "chemicals",
  components: {},
  data() {
    return {
      showsImg: false,
      title: "",
      img: "",
      showTool:true
    };
  },
  //方法集合
  methods: {
    errImg(){
      // console.log('errImg')
      this.showTool = true;
    },
    loadImg(){
      // console.log('loadImg')
      this.showTool = false
    },
    closeBoolean(val) {
      this.showsImg = val;
    },
    // showItem(e){
    //   this.$refs.item.style.top=e.y+"px";
    //   this.$refs.item.style.left=e.x+20+"px";
    // },
    // enterItem(){
    //   this.$refs.item.style.display="block";
    // },
    // outItem(){
    //   this.$refs.item.style.display="none";
    // },
    getData(img, type) {
      this.title = "异常报备信息详情"
      // if (type == "0") {
      //   this.title = "异常设备附件查看";
      // } else if (type == "1") {
      //   this.title = "视频异常附件查看";
      // }
      this.img = img;
    },
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
};
</script>
<style lang="scss" scoped>
// /deep/ .el-dialog__body {
//   font-size: 12px;
// }
/deep/ .el-icon-circle-close{
  color: #fff;
  font-weight: 300;
}
/deep/ .el-image__preview{
  cursor: -moz-zoom-in;
    cursor: -webkit-zoom-in;
    cursor: zoom-in;
}
.div1 {
  max-height: 70vh;
  overflow: auto;
  display: flex;
  justify-content: center;
}
.item{
  color: #fff;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  display: none;
  color: #000;
}
</style>