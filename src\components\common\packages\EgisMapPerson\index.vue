<template>
  <div style="width: 100%; height: 100%">
    <!-- <iframe style="width: 100%;height:100%;" id="egisMapId" frameborder="0" scrolling="no">
    </iframe>-->
    <div :id="mapId" style="width: 100%; height: 100%; position: relative">
      <div class="searchBox">
        <!-- <el-input class="searchInput" @input="searchList()" v-model.trim="searchAddress" placeholder="请输入地址名称">
        </el-input>-->
        <ul v-show="haveResult" class="resultBox">
          <li
            v-for="(item, index) in resultArr"
            :key="index"
            class="resultLi"
            :title="item.name"
            @click="resultClick(item)"
          >
            {{ item.name }}
          </li>
          <!-- <li v-if="resultArr.length == 0">未搜索到相关结果</li> -->
        </ul>
      </div>
      <div
        v-if="plottingMethod != 'point' && !isdetail"
        style="
          width: 95px;
          height: 38px;
          background: rgb(255, 255, 255);
          position: absolute;
          display: flex;
          justify-content: center;
          align-items: center;
          top: 12px;
          left: 400px;
          z-index: 10;
          cursor: pointer;
          border-radius: 5px;
          color: gray;
          font-size: 18px;
          font-weight: bold;
        "
        @click="deletePlotting"
      >
        重新标绘
      </div>
      <div
        style="
          width: 46px;
          cursor: pointer;
          position: absolute;
          right: 5px;
          top: 10px;
          z-index: 10;
        "
        @click="handleFullScreen"
      >
        <img
          :src="fullScreenStatus ? minScreen : maxScreen"
          width="46"
          height="46"
          alt
        />
      </div>

      <div
        style="
          width: 46px;
          cursor: pointer;
          position: absolute;
          right: 5px;
          top: 60px;
          z-index: 10;
        "
        @click="toogleMapLayer"
      >
        <img
          :src="mapLayerStatus ? gisVector : gisImage"
          width="46"
          height="46"
          alt
        />
      </div>

      <div
        style="
          width: 46px;
          cursor: pointer;
          position: absolute;
          right: 5px;
          bottom: 60px;
          z-index: 10;
        "
        @click="handleScreenEvent('zoomIn')"
      >
        <img :src="gisScreenBig" width="46" height="46" alt />
      </div>

      <div
        style="
          width: 46px;
          cursor: pointer;
          position: absolute;
          right: 5px;
          bottom: 10px;
          z-index: 10;
        "
        @click="handleScreenEvent('zoomOut')"
      >
        <img :src="gisScreenSmall" width="46" height="46" alt />
      </div>
    </div>
  </div>
</template>
<script>
import axios from "axios";
const egis = null;
let mapConfig;
// if (window.sessionStorage.systemIdentification) {
//   mapConfig = require("../../assets/json/map-" +
//     JSON.parse(window.sessionStorage.systemIdentification) +
//     ".json");
// } else {
//   mapConfig = require("../../../../assets/json/map.json");
// }
mapConfig = require("../../../../assets/json/map.json");
// console.log(require("/static/img/assets/img/top-bg.png"));
const gisVector = require("/static/img/assets/img/mapIcon/gis-vector.png"); // 地图矢量
const gisImage = require("/static/img/assets/img/mapIcon/gis-image.png"); // 地图影像
const gisScreenBig = require("/static/img/assets/img/mapIcon/gis-screen-big.png"); // 地图放大
const gisScreenSmall = require("/static/img/assets/img/mapIcon/gis-screen-small.png"); // 地图缩小
const maxScreen = require("/static/img/assets/img/mapIcon/max-screen.png"); // 全屏
const minScreen = require("/static/img/assets/img/mapIcon/min-screen.png"); // 恢复全屏

export default {
  name: "EgisMap",
  props: {
    value: { type: [Number, Number] },
    datas: {},
    mapId: { type: String, default: "egisMapId" },
    mapType: { type: String, default: "" },
    plottingMethod: { type: String, default: "point" },
    plottingMethodAuto: { type: String, default: "" },
    isdetail: { type: Boolean },
  },
  computed: {
    shouldLoadMap() {
      // 如果是 reportH5 路由，不加载地图
      return this.$route.path !== "/reportH5";
    },
  },
  data() {
    return {
      toolTipWare: "",
      fullScreenStatus: false,
      searchVal: "",
      role: {},
      keyIndex: 1,
      theMap: "",
      tiandituvec: "",
      tianditucta: "",
      WRGSService: "",
      // 标绘图层
      canvasLayer: "",
      // 回显图层
      elementLayer: "",
      // 无命令工具
      noopTool: "",
      // 构造地图搜索服务对象
      WPSSService: "",
      // 当前点元素
      currentDiotElement: "",
      tiandituimg: "",
      tianditucia: "",
      // 放大 缩小 全屏
      fullExtentCommand: "",
      zoomInCommand: "",
      zoomOutCommand: "",
      commandNotify: "",
      commandManager: "",
      mapLayerStatus: true,
      haveResult: false,
      searchAddress: "",
      resultArr: [],
      x_PI: (3.14159265358979324 * 3000.0) / 180.0,
      PI: 3.1415926535897932384626,
      aaa: 6378245.0,
      ee: 0.00669342162296594323,
      plottingData: "",
      egis: egis,
      gisVector: gisVector,
      gisImage: gisImage,
      gisScreenBig: gisScreenBig,
      gisScreenSmall: gisScreenSmall,
      maxScreen: maxScreen,
      minScreen: minScreen,
    };
  },
  watch: {
    // datas(newval, oldval) {
    //   this.echoToMap(newval);
    // },
    // mapId(newval, oldval) {
    //   if (newval) {
    //     this.initMap();
    //   }
    // }
  },
  mounted() {
    // 如果是 reportH5 路由，不初始化地图
    if (!this.shouldLoadMap) {
      console.log("reportH5页面，跳过地图初始化");
      return;
    }

    window["EGIS_BASE_URL"] = mapConfig.staticUrl; // 静态资源地址
    this.initMap();
  },
  methods: {
    // 初始化地图
    initMap() {
      // 动态加载 emap-2d 模块
      if (!egis) {
        egis = require("emap-2d");
      }

      // 创建地图
      if (this.mapType === "3d") {
        this.map = new egis.carto.Globe(mapConfig["map" + this.mapType]);
        const envelope = new egis.sfs.Envelope({
          spatialReference: egis.sfs.EnumSpatialReference.EPSG4490, // 坐标系
          minx: mapConfig["map" + this.mapType].defaultExtent[0], // x最小值
          miny: mapConfig["map" + this.mapType].defaultExtent[1], // y最小值
          maxx: mapConfig["map" + this.mapType].defaultExtent[2], // x最大值
          maxy: mapConfig["map" + this.mapType].defaultExtent[3], // y最大值
        });
        this.map.camera.setView(envelope);
      } else {
        this.map = new egis.carto.Map(mapConfig.mapPerson);
      }
      // 初始化地图，传入要初始化的DOM对象的id
      this.map.init({ targetId: this.mapId });
      // 根据传进来的值加载图层
      const baseLayers = mapConfig["baseLayersPerson" + this.mapType];
      baseLayers.forEach((value) => {
        if (!value.visible) return;
        value.layers.forEach((item) => {
          if (item.clientId && item.clientSecret) {
            item.resthttp = new egis.core.RestHttp({
              client_id: item.clientId,
              client_secret: item.clientSecret,
            });
          }
          const layer =
            this.mapType === "3d"
              ? new egis.carto.TileLayer3D(item)
              : new egis.carto.TileLayer(item);
          this.map.addLayer(layer);
        });
      });
      // 解绑地图theMap的鼠标单击事件
      // theMap.un('click',mouseClickFunc);
      // 监听渲染完成事件
      this.map.on("postrender", this.mapRenderFinish);
      // 创建地图全图命令
      this.fullExtentCommand = new egis.interact.FullExtentCommand({
        id: "fullExtent", // 地图全图的id
      });
      // 创建地图放大命令
      this.zoomInCommand = new egis.interact.ZoomInCommand({
        id: "zoomIn",
      });
      // 创建地图缩小命令
      this.zoomOutCommand = new egis.interact.ZoomOutCommand({
        id: "zoomOut",
      });
      this.commandManager = new egis.gdm.CommandManager();
      // 创建命令通知实例，用来命令之间的相互通知，传入命令管理对象
      this.commandNotify = new egis.gdm.CommandNotify({
        manager: this.commandManager,
      });
      // 将命令放到命令管理中统一管理
      this.commandManager.add(this.fullExtentCommand);
      this.commandManager.add(this.zoomOutCommand);
      this.commandManager.add(this.zoomInCommand);
      // 命令初始化
      this.commandManager.onCreate({
        map: this.map,
        commandNotify: this.commandNotify,
      });

      // 监听全屏状态改变时的事件
      document.addEventListener("fullscreenchange", this.exitHandler);
      document.addEventListener("webkitfullscreenchange", this.exitHandler);
      document.addEventListener("mozfullscreenchange", this.exitHandler);
      document.addEventListener("MSFullscreenChange", this.exitHandler);
      // 创建一个canvas图层用于标绘，一个element图层用于回显（因为3D地图存在问题，无法用一个图层完成标绘回显）
      // const canvasLayerConfig = { id: "canvasLayer", name: "标绘图层" };
      // const elementLayerConfig = { id: "elementLayer", name: "回显图层" };
      // this.canvasLayer =
      //   this.mapType === "3d"
      //     ? new egis.carto.CanvasLayer3D(canvasLayerConfig)
      //     : new egis.carto.CanvasLayer(canvasLayerConfig);
      // this.elementLayer =
      //   this.mapType === "3d"
      //     ? new egis.carto.ElementLayer3D(elementLayerConfig)
      //     : new egis.carto.ElementLayer(elementLayerConfig);
      // if (!this.isdetail) {
      //   switch (this.plottingMethod) {
      //     case "point":
      //       // 监听地图theMap的鼠标单击事件
      //       this.map.on("click", this.mouseClickFunc);
      //       break;
      //     case "polyline":
      //       this.drawLine();
      //       this.commandNotify.activeCommand("polylinePlot"); // 激活线标绘命令
      //       break;
      //     case "polygon":
      //       this.drawPlane();
      //       this.commandNotify.activeCommand("polygonPlot"); // 激活线标绘命令
      //       break;
      //   }
      // }

      // 将元素图层添加到theMap地图上
      // this.map.addLayer(this.canvasLayer);
      // this.map.addLayer(this.elementLayer);

      // 创建无操作工具
      this.noopTool = new egis.interact.NoopTool();
      this.noopTool.onCreate({
        map: this.map,
      });
      if (this.datas) {
        this.echoToMap(this.datas);
      }
    },
    // 绑定线标绘方法
    mapCallBackFun(msgObj) {
      if (this.plottingMethod !== "point") {
        // 线面转成wkt格式
        msgObj = msgObj.elements[0].geometry.asWkt();
      }
      this.$emit("mapCallback", msgObj);
    },
    polylinePlot() {
      this.commandNotify.activeCommand("polylinePlot"); // 激活线标绘命令
    },
    // 绑定面标绘方法
    polygonPlot() {
      this.commandNotify.activeCommand("polygonPlot"); // 激活面标绘命令
    },

    // 线标绘
    drawLine() {
      // 构建一个空的线对象
      const gsLineSymbol = new egis.sfs.SimpleLineSymbol({
        color: new egis.sfs.Color({ a: 255, r: 255, g: 0, b: 0 }),
      });
      const path = new egis.sfs.Path({
        spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      });
      const polyline = new egis.sfs.Polyline({
        spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      });
      path.addPoint(
        new egis.sfs.Point({
          x: 116.339811227417,
          y: 39.87178623809814,
          spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
        })
      );
      polyline.addGeometry(path);
      const polylineEle = new egis.sfs.Element({
        geometry: polyline,
        symbol: gsLineSymbol,
      });
      const polylinePlot = new egis.plot.PlotTool({
        id: "polylinePlot", // 标绘的ID
        drawTemplate: polylineEle, // 标绘的模板
        algorithm: new egis.plot.PolylineAlgorithm(), // 标绘算法
        elements: [polylineEle], // 标绘符号element数组
        name: "线", // 标绘符号名称
        plotLayer: this.canvasLayer, // 标绘图层
      });
      this.commandManager.add(polylinePlot);
      // 命令构造器
      this.commandManager.onCreate({
        map: this.map,
        commandNotify: this.commandNotify,
      });
      const that = this;
      polylinePlot.onEndDraw = function (element) {
        that.plottingData = element;
        that.noopTool.onClick();
        that.mapCallBackFun(element);
        // this.map.pan(element.elements[0].geometry, 5);
      };
    },

    clickItem(el) {
      const center = new egis.sfs.Point({
        x: el.longitude,
        y: el.latitude,
        z: 0,
        spatialReference: 4490,
      });

      var contentTemplate = `<div class="mapbox" style="background: #fff;border:1px solid #ccc;border-radius: 3px;padding:5px;width:200px">
            <div style="background:red;padding:5px;text-align:center;color:#fff;width:100%">呼叫中</div>
            <div><span style="font-weight:bold;color:#000;font-size:16px;display:inline-block;margin:5px 10px 0 0;">${el.name}</span><span>15002742578</span></div>
            <div style="width:100%;border-bottom:1px solid #ccc;margin:5px 0 5px 0;"></div>
            <div><span>地址</span>：${el.adress}</div>
            <div><span>时间</span>：${el.time}</div>
            </div>`;
      //创建提示框

      this.locationT(el);

      var tooltip = new egis.widget.Tooltip({
        anchor: {
          $type: "Point,http://www.Gs.com",
          spatialReference: 4490,
          x: el.longitude,
          y: el.latitude,
          z: 0,
        }, //提示工具在地图上停靠的位置
        content: contentTemplate, //提示的内容
        //  content: `<div id="tooltip"></div>`, //提示的内容
        id: "ffaa",
        title: el.eventTitle,
        offset: [-90, -160], //位置偏移量 0：左右    1：上下的距离
      });

      this.toolTipWare.clear();
      this.toolTipWare.add(tooltip);
    },

    locationT(el) {
      this.map.zoomTo(18);
      this.map.setCenter({
        x: el.longitude,
        y: el.latitude,
      });
    },

    // 面标绘
    drawPlane() {
      // 创建简单面符号
      const simpleFillSymbol = new egis.sfs.SimpleFillSymbol({
        fillColor: new egis.sfs.Color({ a: 100, r: 255, g: 0, b: 0 }),
      });
      // 创建一个空的面元素对象
      const ring = new egis.sfs.Ring({
        spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      });
      const polygon = new egis.sfs.Polygon({
        spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      });
      ring.addPoint(
        new egis.sfs.Point({
          x: 116.339811227417,
          y: 39.87178623809814,
          spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
        })
      );
      polygon.addGeometry(ring);
      const polygonEle = new egis.sfs.Element({
        geometry: polygon,
        symbol: simpleFillSymbol,
      });
      const polygonPlot = new egis.plot.PlotTool({
        id: "polygonPlot", // 标绘的ID
        drawTemplate: polygonEle, // 标绘的模板
        algorithm: new egis.plot.PolygonAlgorithm(), // 标绘算法
        elements: [polygonEle], // 标绘符号element数组
        name: "面", // 标绘符号名称
        plotLayer: this.canvasLayer, // 标绘图层
      });
      this.commandManager.add(polygonPlot);
      // 命令构造器
      this.commandManager.onCreate({
        map: this.map,
        commandNotify: this.commandNotify,
      });
      const that = this;
      polygonPlot.onEndDraw = function (element) {
        that.plottingData = element;
        that.noopTool.onClick();
        that.mapCallBackFun(element);
        // this.map.pan(element.elements[0].geometry, 5);
      };
    },
    // 删除方法
    deletePlotting() {
      // this.canvasLayer.remove(this.plottingData);
      // this.elementLayer.remove(this.plottingData);
      // this.canvasLayer.clear();
      // this.elementLayer.clear();
      if (this.plottingMethod == "polyline") {
        this.drawLine();
        this.commandNotify.activeCommand("polylinePlot");
      } else if (this.plottingMethod == "polygon") {
        this.drawPlane();
        this.commandNotify.activeCommand("polygonPlot");
      }
    },

    // 搜索列表
    searchList() {
      const that = this;
      axios({
        method: "get",
        url: mapConfig.searchService + this.searchAddress,
      }).then(function (res) {
        if (!res.data.pois) {
          that.haveResult = false;
        } else {
          that.resultArr = res.data.pois;
          that.haveResult = true;
        }
      });
    },

    gcj02towgs84(gcj_lonlat) {
      const lng = +gcj_lonlat.split(",")[0];
      const lat = +gcj_lonlat.split(",")[1];
      if (this.out_of_china(lng, lat)) {
        // return [lng, lat];
        return lng + "," + lat;
      } else {
        let dlat = this.transformlat(lng - 105.0, lat - 35.0);
        let dlng = this.transformlng(lng - 105.0, lat - 35.0);
        const radlat = (lat / 180.0) * this.PI;
        let magic = Math.sin(radlat);
        magic = 1 - this.ee * magic * magic;
        const sqrtmagic = Math.sqrt(magic);
        dlat =
          (dlat * 180.0) /
          (((this.aaa * (1 - this.ee)) / (magic * sqrtmagic)) * this.PI);
        dlng =
          (dlng * 180.0) /
          ((this.aaa / sqrtmagic) * Math.cos(radlat) * this.PI);
        const mglat = lat + dlat;
        const mglng = lng + dlng;
        const wgslng = lng * 2 - mglng;
        const wgslat = lat * 2 - mglat;
        return wgslng + "," + wgslat;
      }
    },

    transformlat(ln, la) {
      const lat = +la;
      const lng = +ln;
      let ret =
        -100.0 +
        2.0 * lng +
        3.0 * lat +
        0.2 * lat * lat +
        0.1 * lng * lat +
        0.2 * Math.sqrt(Math.abs(lng));
      ret +=
        ((20.0 * Math.sin(6.0 * lng * this.PI) +
          20.0 * Math.sin(2.0 * lng * this.PI)) *
          2.0) /
        3.0;
      ret +=
        ((20.0 * Math.sin(lat * this.PI) +
          40.0 * Math.sin((lat / 3.0) * this.PI)) *
          2.0) /
        3.0;
      ret +=
        ((160.0 * Math.sin((lat / 12.0) * this.PI) +
          320 * Math.sin((lat * this.PI) / 30.0)) *
          2.0) /
        3.0;
      return ret;
    },

    transformlng(ln, la) {
      const lat = +la;
      const lng = +ln;
      let ret =
        300.0 +
        lng +
        2.0 * lat +
        0.1 * lng * lng +
        0.1 * lng * lat +
        0.1 * Math.sqrt(Math.abs(lng));
      ret +=
        ((20.0 * Math.sin(6.0 * lng * this.PI) +
          20.0 * Math.sin(2.0 * lng * this.PI)) *
          2.0) /
        3.0;
      ret +=
        ((20.0 * Math.sin(lng * this.PI) +
          40.0 * Math.sin((lng / 3.0) * this.PI)) *
          2.0) /
        3.0;
      ret +=
        ((150.0 * Math.sin((lng / 12.0) * this.PI) +
          300.0 * Math.sin((lng / 30.0) * this.PI)) *
          2.0) /
        3.0;
      return ret;
    },

    out_of_china(ln, la) {
      const lat = +la;
      const lng = +ln;
      // 纬度3.86~53.55,经度73.66~135.05
      return !(lng > 73.66 && lng < 135.05 && lat > 3.86 && lat < 53.55);
    },

    resultClick(item) {
      const location = this.gcj02towgs84(item.location);
      const point = new egis.sfs.Point({
        x: location.split(",")[0],
        y: location.split(",")[1],
        z: 1000,
        spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      });
      this.getWRGS(point);
      // this.map.setCenter(point);
      this.searchAddress = "";
      this.resultArr = [];
      this.haveResult = false;
    },

    getWRGS(point) {
      // 构造逆向地理编码输入参数对象
      /* const WRGSInput = new egis.ews.WRGSInput({
    location: point,
    ext_poi: true,  // 是否搜索返回附近poi数据，为true则返回
    ext_road: true  //是否返回附近道路数据，为true则返回
  });
  // 调用逆向地理编码服务的regeocode接口
  let promise = this.WRGSService.regeocode(WRGSInput);*/
      const that = this;
      const url =
        mapConfig.urlService +
        "postStr={'lon':" +
        point.x +
        ",'lat':" +
        point.y +
        ",'ver':1}";
      axios({
        method: "get",
        url: url,
      }).then(
        (result) => {
          // result为服务返回结果数据
          if (result.data.msg !== "ok") {
            this.$emit(
              "mapstatustips",
              "目前的选择不在中国境内,请重新在地图上选择！"
            );
            return;
          }
          // 将点添加到地图上
          this.addPointToMap(point);
          this.mapCallBackFun(result.data.result);
        },
        (err) => {
          this.$emit(
            "mapstatustips",
            "目前的选择不在中国境内,请重新在地图上选择！"
          );
          return;
        }
      );
    },
    echoToMap(data) {
      // this.elementLayer.clear();
      // this.canvasLayer.clear();
      if (this.plottingMethod == "point") {
        this.addPointToMap(data);
      } else if (this.plottingMethod == "polyline") {
        this.addPolylineToMap(data);
      } else if (this.plottingMethod == "polygon") {
        this.addPolygonToMap(data);
      }

      // 自定义方法 给线两端加点
      if (this.plottingMethodAuto == "point") {
        this.addPointToMap(data[0], "noClear", "start");
        this.addPointToMap(data[data.length - 1], "noClear", "end");
      }
    },

    // 将坐标点添加到地图上
    addPointToMap(data, type, potType) {
      //   if (this.currentDiotElement) {
      //     if (type != "noClear") {
      //       this.elementLayer.remove(this.currentDiotElement);
      //     }
      //   }
      //   // 创建点元素符号，source可以传入图标的base64位编码字符串，也可以传入图标的文件路径
      //   let picSource = "";
      //   if (potType == "start") {
      //     picSource =
      //       "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAABPlJREFUWEelVluMFEUUPberehNNfERBHtMgRgQNiiZABPEDwoeJYFSE9YMP/RCW6VmRAImKGjERjYov3OlhgUTkS8BnRP4MmqiIYlQMCWzAELeGh4AfSsTsVPc1XT099Mz09sxIJZNMV93HqXMfdQkdrOu38A1+xZ8HYAmYx4JoDBj/gnAIwAAR/cwXzu9Qq676s12z1I7geG9oWgBrGYDwl70YJ5j4fVhye3k5/dJKvCWAnFd5mkAvpRpiHjD7RJNSzs8xY1W5ILdngcgE4JT858G8rs4A0TpmPqBJ/HA6T3+EZ6NKfJ1kfwYRTW+UZ/Dasmu/PByIYQHkPL2YgJ0XFekYAl6heuWerBs5ffpeWLQR4BtrckEwU/V27U/TSwVQjfmBmgLROpUXL4TfuT6eRJbfz4HoKfdSFIKUlWSPgePUJaapx6gpOVMBOJ7uTyTcfuXKmc4bfBlsfafxJWgvfJ4b/mVBIwlw6zBYeE0tl3scT38HwOgQsGnQlflGrE0Aqjc8UhMUYqrqoV9T84H5MAt6jgIUEobnoMqY08+3wfcPmjPGhUD+M/5Ez5VnkyCaAIwrVlYz0YZqdteoj5WcdypzYgbU4/aXjTdyPM0xgPCsDjjzk6pgv5oJwPH0XgBzIgBYqPLy44ShaD86N845EIvKvXSuBrAJgH4QjI8iEvBN2ZV3ZwMoVo7EdS1ZjDleoFMRAL0EAd8Ey2JTaqYccZLA9wBY2MDEUuXKreHehCKP1uSfjM75iHLtm1sx8BeAKwA6plwxMSk8ztM7B13ZbWj2eW4YgpAxBiYQ0baarA6+SobH8fyjUVnSWeWKkS0A+GcAHgHmAVWwJ8fCuZLuJsYO0noiS3k0DAEBf7MBCyhXmqoI17iiv2awIKI8CtlLsKpcWZd3zVXg6a8JmB0qVkiMirtdrq+ylixaz4EYQZZ/NgxBxCqbvIgB5Dx/GYH7Y0dhl7TZPx1BoUPKFbdmMpAr6XeJ8Wg1aRaUXfl5+D/eJ4sKHHAxDkEYFgYWx0kJxhQQRsYAcp6eT8Bu4x7YFYYwOwTJ/p/ogI5X+R6wbICnALBjAGNLldkWUw8DYwiwwDgODjbHrbe+f/AryrWfygQwunRhgmT7JwBXG8EA88P+73j6PIG2BITDFD1Qo4FwFqDfwIECqCs2TMAZk6zmXYBhMFwWgum/u10/ZgIwSeT5bzJ4ZTVuphrCbNdadJ9aQWdyxaE7CNYMsugWBk8G4/IGoxGAWvab083KlT1JuWpYGrcA44CskIUqhouPUbN0+k5j6067/bAATNIVK88Q0YsJ8/shxNLwXcgCUe3/W+JHyCQz87Plgr0+Ta/FQKI/A2NBnaIpPz4oA7Ev7pKm21n+LICmNg8w2K3y8r7hQGcCyG0cup2k9QWAa9MN0LFoPzF81AueYx3MK6/oGnY2bGMm9JcTuNRu7JNyDMqXXbEpS7clgGpVbGPwI52AINB7g64wDe2SAThb+RoMBfsATpt+U+zTALqsWWkjWKNwWwwYFvr0/Wzhk1Y3MqUV4IHBXvlpW7LtCMUyuWJlAxGtztJh5tfLBXtNu3bbZiA26JT0t2DMSnVA2Kfy8q52nRu2OhEOZZtG9oSB4brdJSdhowGn6D8B4rfq9plWqoJ4u9MLdcxALRSe/gDAQ9XvD5UrF3Xq/H+FIHZSnXR2hN8VEg/Hk1OnIP4DS/I/Pw9xvz0AAAAASUVORK5CYII=";
      //   } else if (potType == "end") {
      //     picSource =
      //       "data:image/png;base64,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";
      //   } else {
      //     picSource =
      //       "data:image/png;base64,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";
      //   }
      //   const pointSymbol = new egis.sfs.PictureMarkerSymbol({
      //     source: picSource,
      //     width: 28,
      //     height: 36,
      //     offsetX: 14,
      //     offsetY: this.mapType == "3d" ? 23 : 31,
      //     opacity: 1,
      //   });
      //   // 创建点元素对象
      //   this.currentDiotElement = new egis.sfs.Element({
      //     geometry: new egis.sfs.Point({
      //       x: data.x || data.longitude,
      //       y: data.y || data.latitude,
      //       z: 0,
      //       spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      //     }),
      //     symbol: pointSymbol,
      //   });
      //   // 将点元素对象添加到元素图层上
      //   this.elementLayer.add(this.currentDiotElement);
      //   // 回显定位中心点, 更改Z数据避免缩放影响
      //   const setCenterData = new egis.sfs.Point({
      //     x: data.x || data.longitude,
      //     y: data.y || data.latitude,
      //     z: 1000,
      //     spatialReference: egis.sfs.EnumSpatialReference.EPSG4490,
      //   });
      //   this.map.setCenter(setCenterData);
      // },
      // // 添加线到地图上
      // addPolylineToMap(data) {
      //   let polyline;
      //   if (data.length < 1) {
      //     data = "linestring()";
      //   }
      //   if (data instanceof Array) {
      //     polyline = new egis.sfs.Polyline({});
      //     const path = new egis.sfs.Path({});
      //     data.forEach((ele) => {
      //       path.addPoint(
      //         new egis.sfs.Point({ x: ele.longitude, y: ele.latitude })
      //       );
      //     });
      //     polyline.addGeometry(path);
      //   } else {
      //     polyline = egis.sfs.GeometryFactory.createGeometryFromWkt(data, "4490");
      //   }
      //   // 创建线元素符号
      //   const simpleLineSymbol = new egis.sfs.SimpleLineSymbol({
      //     color: new egis.sfs.Color({ r: 255, g: 0, b: 0, a: 255 }),
      //     width: 3,
      //   });
      //   // 创建线元素对象
      //   const lineElement = new egis.sfs.Element({
      //     geometry: polyline,
      //     symbol: simpleLineSymbol,
      //   });
      //   // 将线元素对象添加到元素图层上
      //   this.elementLayer.add(lineElement);
      //   this.map.pan(polyline);
      // },
      // // 添加面到地图上
      // addPolygonToMap(data, obj) {
      //   const targetGon = egis.sfs.GeometryFactory.createGeometryFromWkt(
      //     data,
      //     "4490"
      //   );
      //   // 创建面要素对象渲染的符号
      //   const color = {
      //     r: 255,
      //     g: 0,
      //     b: 0,
      //     a: 100,
      //   };
      //   const gonSymbol = new egis.sfs.SimpleFillSymbol({
      //     fillColor: new egis.sfs.Color(color),
      //   });
      //   let id = "polygon";
      //   if (obj) {
      //     id = obj.id;
      //   }
      //   const gonStringEle = new egis.sfs.Element({
      //     id: id,
      //     geometry: targetGon,
      //     symbol: gonSymbol,
      //   });
      //   this.elementLayer.add(gonStringEle);
      //   this.plottingData = gonStringEle;
      //   this.noopTool.onClick();
      //   if (obj && obj.text) {
      //     const topoTextSymbol = new egis.sfs.TextSymbol({
      //       text: obj.text,
      //       fontFamilyName: "宋体",
      //       fontSize: 20,
      //       borderThickness: 1,
      //     });
      //     const topoTextSymbolElement = new egis.sfs.Element({
      //       geometry: targetGon,
      //       symbol: topoTextSymbol,
      //     });
      //     this.elementLayer.add(topoTextSymbolElement);
      //   }
      // this.map.pan(targetGon, 5);
    },

    // 鼠标单击事件回调函数
    mouseClickFunc(button, shift, screenx, screeny, mapx, mapy, handled) {
      // 如果是详情页，不响应鼠标的点击事件
      if (this.isdetail) {
        return;
      }
      // 根据屏幕的像素坐标获取经纬度坐标
      // 获取屏幕上指定像素位置对应地图上的地理坐标
      const pixel =
        this.mapType === "3d"
          ? new egis.sfs.Point({
              x: screenx,
              y: screeny,
              z: 0,
              spatialReference: 4490,
            })
          : [screenx, screeny];
      const coorArr = this.map.getCoordinateFromPixel(pixel);
      if (
        Number(coorArr[1]) < 3.86 ||
        Number(coorArr[1]) > 53.55 ||
        Number(coorArr[0]) < 73.66 ||
        Number(coorArr[0]) > 135.05
      ) {
        this.$emit(
          "mapstatustips",
          "目前的选择不在中国境内,请重新在地图上选择！"
        );
        return;
      }
      const point =
        this.mapType === "3d"
          ? coorArr
          : new egis.sfs.Point({
              x: coorArr[0],
              y: coorArr[1],
              z: 0,
              spatialReference: 4490,
            });
      this.getWRGS(point);
    },

    aroundSearch(dataList) {
      // const component: any = await this.getComponent();
      // this.map = component.map;
      let _this = this;
      // const container: any = (this as any).container;
      // const mapShare: any = container.resolve('mapShare');
      // this.map = mapShare.comFactory.options.map
      //创建一个信息管理类对象，绑定地图对象
      this.toolTipWare = new egis.widget.TooltipWare({
        map: this.map,
      });

      let layerToolTip = new egis.widget.TooltipWare({
        map: this.map,
      });

      // this.$store.commit('visualizeModule/updataToolTipWareVuex',layerToolTip)
      // console.log(component.map, "component");
      // component.addPoin();
      // console.log(componentBase);
      let baseStationElementLayer = this.map.findLayer("人员定位");

      if (!baseStationElementLayer) {
        // 将元素图层添加到egismap地图上
        baseStationElementLayer = new egis.carto.ElementLayer({
          id: "baseStationElementLayer",
          name: "人员定位",
          zIndex: 100,
        });
        this.map.addLayer(baseStationElementLayer);
        // 鼠标悬停事件(选用)
        baseStationElementLayer.on(
          "mouseover",
          (screenX, screenY, mapX, mapY, graphic) => {
            // var contentTemplate = '<div style="background: red;">' +
            // '<label style=" display: block; white-space: nowrap;line-height: 20px; min-width: 120px;">'
            // + graphic.element.tag.name + '</div>'
            var contentTemplate = `<div class="mapbox" style="background: #fff;border:1px solid #ccc;border-radius: 3px;padding:5px;width:200px">
            <div style="background:red;padding:5px;text-align:center;color:#fff;width:100%">呼叫中</div>
            <div><span style="font-weight:bold;color:#000;font-size:16px;display:inline-block;margin:5px 10px 0 0;">${graphic.element.tag.name}</span><span>15002742578</span></div>
            <div style="width:100%;border-bottom:1px solid #ccc;margin:5px 0 5px 0;"></div>
            <div><span>地址</span>：${graphic.element.tag.adress}</div>
            <div><span>时间</span>：${graphic.element.tag.time}</div>
            </div>`;
            // const url = require('@/assets/img/plan/iteml.png')
            // var contentTemplate = "111";
            //创建提示框
            var tooltip = new egis.widget.Tooltip({
              anchor: graphic.element.geometry, //提示工具在地图上停靠的位置
              content: contentTemplate, //提示的内容
              id: "ff",
              title: graphic.element.tag.eventTitle,
              offset: [-90, -160], //位置偏移量 0：左右    1：上下的距离
            });
            //创建一个信息管理类对象，绑定地图对象
            // var toolTipWare = new egis.widget.TooltipWare({
            //     map: this.map
            // });
            //将提示框加入到信息管理类对象中，显示提示信息
            this.toolTipWare.clear();
            this.toolTipWare.add(tooltip);
          }
        );
        baseStationElementLayer.on("mouseout", (event) => {
          this.toolTipWare.clear();
        });
      } else {
        baseStationElementLayer.clear();
      }

      // 创建点元素符号，source可以传入图标的base64位编码字符串，也可以传入图标的文件路径
      const pointSymbol = new egis.sfs.PictureMarkerSymbol({
        source:
          "data:image/png;base64,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",
        width: 40,
        height: 40,
        offsetX: 16,
        offsetY: 32,
        opacity: 1,
      });

      // 114.311467,
      // 30.580942

      // 创建点元素对象
      dataList.forEach((pois) => {
        // 构建egis点
        const point = new egis.sfs.Point({
          x: pois.longitude, // 根据实际调用参数取值
          y: pois.latitude,
          spatialReference: 4490,
        });
        baseStationElementLayer.add(
          new egis.sfs.Element({
            geometry: point,
            symbol: pointSymbol,
            tag: pois,
          })
        );
      });
    },

    // 通过点获取地址
    getAddressToDiot(pois) {
      // 构建egis点
      const point = new egis.sfs.Point({
        x: pois.data.location.x,
        y: pois.data.location.y,
        spatialReference: 4490,
      });
      const WRGSInput = new egis.ews.WRGSInput({
        location: point,
        ext_poi: true, // 是否搜索返回附近poi数据，为true则返回
        ext_road: true, // 是否返回附近道路数据，为true则返回
      });
      // 调用逆向地理编码服务的regeocode接口
      const promise = this.WRGSService.regeocode(WRGSInput);
      promise.then(
        (result) => {
          // result为服务返回结果数据
          // if (result.address_component.country !== "中国") {
          //   this.$emit("mapstatustips", '目前的选择不在中国境内,请重新在地图上选择！');
          //   return
          // }
          // 将点添加到地图上
          this.addPointToMap(point);
          // 将地图定位到坐标点位置
          // this.map.setCenter(point);
          this.mapCallBackFun(result);
        },
        (err) => {
          this.$emit(
            "mapstatustips",
            "目前的选择不在中国境内,请重新在地图上选择！"
          );
          return;
        }
      );
    },
    /* 通过回传点获取行政区划信息 */
    getDistrictCode(point) {
      return new Promise((resolve) => {
        // 构造逆向地理编码输入参数对象
        const WRGSInput = new egis.ews.WRGSInput({
          location: point,
          ext_poi: true, // 是否搜索返回附近poi数据，为true则返回
          ext_road: true, // 是否返回附近道路数据，为true则返回
        });
        // 调用逆向地理编码服务的regeocode接口
        const promise = this.WRGSService.regeocode(WRGSInput);
        promise.then((result) => {
          // result为服务返回结果数据
          resolve(result);
        });
      });
    },

    // 地图加载的监听事件
    mapRenderFinish() {
      const _role = sessionStorage.getItem("role");
      this.role = JSON.parse(_role);
      const mapInfo = {
        type: "geometry-location",
      };
      // 当前登录用户没有返回经纬度时，用地址查询经纬度
      // if (this.role['longitude'] && this.role['latitude']) {
      //   this.$set(mapInfo, 'data', { location: { x: this.role['longitude'], y: this.role['latitude'] } });
      //   this.setMapDiot(mapInfo);
      // }
      this.$emit("maprenderfinish"); // 发送地图渲染完成状态
      this.map.un("postrender", this.mapRenderFinish); // 解除监听
    },

    // 重新绘制地图高宽
    updateMapSize() {
      this.map.map.updateSize();
    },

    // 切换矢量地图和影像地图
    toogleMapLayer() {
      this.mapLayerStatus = !this.mapLayerStatus;
      let layer = this.map.findLayer("影像");
      layer.setVisible(this.mapLayerStatus);
      layer = this.map.findLayer("正摄");
      if (layer) layer.setVisible(this.mapLayerStatus);
    },

    // 判断浏览器是否处于全屏状态 （需要考虑兼容问题）
    checkFull() {
      // 火狐浏览器
      let isFull =
        document.mozFullScreen ||
        document.fullScreen ||
        // 谷歌浏览器及Webkit内核浏览器
        document.webkitIsFullScreen ||
        document.webkitRequestFullScreen ||
        document.mozRequestFullScreen ||
        document.msFullscreenEnabled;
      if (isFull === undefined) {
        isFull = false;
      }
      return isFull;
    },

    // 全屏状态的回调
    exitHandler(e) {
      if (!this.checkFull()) {
        this.fullScreenStatus = false;
      }
    },

    // 处理放大, 缩小, 全图
    handleScreenEvent(name) {
      switch (name) {
        case "fullExtent":
          // this.commandNotify.activeCommand('fullExtent');// 激活地图全图命令
          document.getElementById(this.mapId).webkitRequestFullScreen();
          break;
        case "zoomOut":
          this.commandNotify.activeCommand("zoomOut"); // 激活地图缩小命令
          break;
        case "zoomIn":
          this.commandNotify.activeCommand("zoomIn"); // 激活地图放大命令
          break;
        default:
          break;
      }
    },

    handleFullScreen() {
      const el = document.getElementById(this.mapId);
      // 判断是否已经是全屏
      // 如果是全屏，退出
      if (this.fullScreenStatus) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        setTimeout((_) => {
          this.map.map.handleResize_();
        }, 2000);
      } else {
        // 否则，进入全屏
        if (el.requestFullscreen) {
          el.requestFullscreen();
        } else if (el.webkitRequestFullScreen) {
          el.webkitRequestFullScreen();
        } else if (el.mozRequestFullScreen) {
          el.mozRequestFullScreen();
        } else if (el.msRequestFullscreen) {
          // IE11
          el.msRequestFullscreen();
        }
      }
      // 改变当前全屏状态
      this.fullScreenStatus = !this.fullScreenStatus;
    },
  },
};
</script>
<style lang="scss" scoped>
.searchBox {
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 10;
  .resultBox {
    width: 200px;
    background: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    padding: 5px;
    max-height: 230px;
    overflow-x: hidden;
    overflow-y: auto;
    margin-top: 3px;
    .resultLi {
      width: 100%;
      height: 30px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      cursor: pointer;
      line-height: 30px;
      padding-left: 5px;
      &:hover {
        background: rgb(230, 244, 255);
      }
    }
  }
}
</style>
